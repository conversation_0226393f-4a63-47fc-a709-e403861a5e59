/**
 * AI Admin Widget
 * Shows AI system status and management for admins
 */

import React, { useState } from 'react';
import {
  Bot,
  Activity,
  Zap,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Settings,
  BarChart3,
  Users,
  Clock,
  RefreshCw,
  ExternalLink,
  Network,
  DollarSign,
  Target
} from 'lucide-react';
import { Link } from 'react-router-dom';
import { useCentralizedAI } from '../../../../hooks/useCentralizedAI';
import { automaticAiUtils } from '../../../../services/automaticAiApi';



interface AIAdminWidgetProps {
  className?: string;
}

export const AIAdminWidget: React.FC<AIAdminWidgetProps> = ({ className = '' }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // Use real AI data from API
  const {
    workers,
    recentActions,
    stats,
    impactSummary,
    isLoading,
    error,
    isAIRunning,
    performanceStatus,
    refreshData
  } = useAutomaticAI();

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'excellent':
      case 'Excellent':
        return 'text-green-600 bg-green-100';
      case 'good':
      case 'Good':
        return 'text-blue-600 bg-blue-100';
      case 'warning':
      case 'Fair':
        return 'text-orange-600 bg-orange-100';
      case 'critical':
      case 'Poor':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 ${className}`}>
        <div className="p-6">
          <div className="flex items-center space-x-3 mb-4">
            <RefreshCw className="h-6 w-6 text-purple-500 animate-spin" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Loading AI System Data...</h3>
              <p className="text-sm text-gray-600 dark:text-gray-300">Fetching real-time AI system status</p>
            </div>
          </div>
          <div className="animate-pulse space-y-4">
            <div className="grid grid-cols-4 gap-4">
              {[1, 2, 3, 4].map(i => (
                <div key={i} className="bg-gray-200 dark:bg-gray-700 rounded h-16"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-red-200 dark:border-red-700 ${className}`}>
        <div className="p-6">
          <div className="flex items-center space-x-3 mb-4">
            <AlertTriangle className="h-6 w-6 text-red-500" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">AI System Error</h3>
              <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
            </div>
          </div>
          <button
            onClick={refreshData}
            className="bg-red-600 hover:bg-red-700 text-white text-sm font-medium py-2 px-4 rounded transition-colors"
          >
            Retry Loading AI System
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <Bot className="h-6 w-6 text-purple-500" />
              <div className={`px-2 py-1 rounded-full text-xs font-medium ${getHealthColor(performanceStatus?.overall_health || 'good')}`}>
                {(performanceStatus?.overall_health || 'GOOD').toUpperCase()}
              </div>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                🤖 AI System Management
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                {workers?.length || 0}/5 workers running • {
                  (workers?.length > 0 || isAIRunning) ? 'Online' : 'Offline'
                }
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Link
              to="/ai-admin/"
              className="flex items-center space-x-1 text-sm font-medium text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-200 transition-colors"
            >
              <span>AI Admin</span>
              <ExternalLink className="h-4 w-4" />
            </Link>
            <button className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
              <RefreshCw className="h-4 w-4 text-gray-500" />
            </button>
          </div>
        </div>
      </div>

      {/* System Overview */}
      <div className="p-6">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Activity className="h-5 w-5 text-blue-500" />
            </div>
            <div className="text-2xl font-bold text-gray-900 dark:text-white">{stats?.total_actions_today || 0}</div>
            <div className="text-xs text-gray-500">AI Recommendations</div>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Users className="h-5 w-5 text-green-500" />
            </div>
            <div className="text-2xl font-bold text-green-600">{impactSummary?.users_impacted || 0}</div>
            <div className="text-xs text-gray-500">Users Helped</div>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Zap className="h-5 w-5 text-yellow-500" />
            </div>
            <div className="text-2xl font-bold text-yellow-600">{stats?.ideas_enhanced || 0}</div>
            <div className="text-xs text-gray-500">Ideas Enhanced</div>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Clock className="h-5 w-5 text-purple-500" />
            </div>
            <div className="text-2xl font-bold text-purple-600">{performanceStatus?.avg_response_time || 'N/A'}</div>
            <div className="text-xs text-gray-500">Avg Response</div>
          </div>
        </div>

        {/* System Resources */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-6">
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-2">System Resources</h4>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-300">CPU Usage:</span>
                <span className="font-medium">{performanceStatus?.cpu_usage || 'N/A'}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-300">Memory Usage:</span>
                <span className="font-medium">{performanceStatus?.memory_usage || 'N/A'}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-300">Last Restart:</span>
                <span className="font-medium">{performanceStatus?.last_restart || 'N/A'}</span>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-2">Today's Impact</h4>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-300">Opportunities Found:</span>
                <span className="font-medium text-green-600">{stats?.opportunities_found || 0}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-300">Risks Identified:</span>
                <span className="font-medium text-red-600">{stats?.risks_identified || 0}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-300">System Uptime:</span>
                <span className="font-medium text-blue-600">{performanceStatus?.uptime || 'N/A'}</span>
              </div>
            </div>
          </div>
        </div>

        {/* AI Workers Status */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-sm font-semibold text-gray-900 dark:text-white">AI Workers Status</h4>
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-xs text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-200"
            >
              {isExpanded ? 'Show Less' : 'Show Details'}
            </button>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
            {workers && workers.length > 0 ? (
              workers.slice(0, isExpanded ? workers.length : 3).map((worker, index) => (
                <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <div className={`w-2 h-2 rounded-full ${
                        worker.status === 'active' ? 'bg-green-500' :
                        worker.status === 'working' ? 'bg-green-500 animate-pulse' :
                        'bg-gray-400'
                      }`} />
                      <span className="text-xs font-medium text-gray-900 dark:text-white">
                        {worker.name}
                      </span>
                    </div>
                    <span className="text-xs text-gray-500">{worker.load || 'N/A'}</span>
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-300">
                    {worker.actions_today || 0} recommendations today
                  </div>
                </div>
              ))
            ) : (
              <div className="col-span-full text-center py-4 text-gray-500 dark:text-gray-400">
                <p className="text-sm">No worker data available</p>
              </div>
            )}
          </div>
        </div>

        {/* Recent AI Actions */}
        {isExpanded && (
          <div className="mb-6">
            <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">Recent AI Actions</h4>
            <div className="space-y-2">
              {recentActions && recentActions.length > 0 ? (
                recentActions.slice(0, 3).map((action, index) => (
                  <div key={action.id || index} className="flex items-start space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <Activity className="h-4 w-4 text-blue-500 mt-0.5" />
                    <div className="flex-1">
                      <div className="text-sm text-gray-900 dark:text-white">{action.title}</div>
                      <div className="text-xs text-gray-600 dark:text-gray-300 mt-1">{action.description}</div>
                      <div className="text-xs text-gray-500 mt-1">{automaticAiUtils.timeAgo(action.timestamp)}</div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                  <p className="text-sm">No recent AI actions</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Autonomous Business Intelligence */}
        <div className="mb-6">
          <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">
            🤖 Autonomous Business Intelligence (Phase 4)
          </h4>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-4">
            <div className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg p-4 border border-blue-200 dark:border-blue-700">
              <div className="flex items-center space-x-3 mb-2">
                <Network className="h-5 w-5 text-blue-500" />
                <span className="text-sm font-medium text-gray-900 dark:text-white">Networking Agent</span>
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-300 mb-2">
                AI identifies and facilitates valuable business connections
              </div>
              <div className="flex items-center justify-between">
                <span className="text-lg font-bold text-blue-600">Active</span>
                <Link
                  to="/ai-admin/autonomous/networking/"
                  className="text-xs text-blue-600 hover:text-blue-800 font-medium"
                >
                  Monitor →
                </Link>
              </div>
            </div>

            <div className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg p-4 border border-green-200 dark:border-green-700">
              <div className="flex items-center space-x-3 mb-2">
                <DollarSign className="h-5 w-5 text-green-500" />
                <span className="text-sm font-medium text-gray-900 dark:text-white">Investment Matching</span>
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-300 mb-2">
                Smart AI connects ideas with potential investors
              </div>
              <div className="flex items-center justify-between">
                <span className="text-lg font-bold text-green-600">Active</span>
                <Link
                  to="/ai-admin/autonomous/investment/"
                  className="text-xs text-green-600 hover:text-green-800 font-medium"
                >
                  Monitor →
                </Link>
              </div>
            </div>

            <div className="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-lg p-4 border border-purple-200 dark:border-purple-700">
              <div className="flex items-center space-x-3 mb-2">
                <Target className="h-5 w-5 text-purple-500" />
                <span className="text-sm font-medium text-gray-900 dark:text-white">Business Optimization</span>
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-300 mb-2">
                Continuous AI optimization of business strategies
              </div>
              <div className="flex items-center justify-between">
                <span className="text-lg font-bold text-purple-600">Active</span>
                <Link
                  to="/ai-admin/autonomous/optimization/"
                  className="text-xs text-purple-600 hover:text-purple-800 font-medium"
                >
                  Monitor →
                </Link>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 border border-indigo-200 dark:border-indigo-700 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <h5 className="text-sm font-semibold text-gray-900 dark:text-white mb-1">
                  🚀 Autonomous Business Development
                </h5>
                <p className="text-xs text-gray-600 dark:text-gray-300">
                  AI system autonomously manages business development, networking, and investment matching.
                </p>
              </div>
              <div className="flex space-x-2">
                <Link
                  to="/ai-admin/autonomous/dashboard/"
                  className="bg-indigo-600 hover:bg-indigo-700 text-white text-xs font-medium py-2 px-3 rounded transition-colors"
                >
                  Autonomous Dashboard
                </Link>
                <Link
                  to="/ai-admin/autonomous/analytics/"
                  className="bg-purple-600 hover:bg-purple-700 text-white text-xs font-medium py-2 px-3 rounded transition-colors"
                >
                  Analytics
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Admin Actions */}
        <div className="bg-gradient-to-r from-purple-500/10 to-blue-500/10 border border-purple-200 dark:border-purple-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-1">
                🔧 AI System Management
              </h4>
              <p className="text-xs text-gray-600 dark:text-gray-300">
                Monitor, configure, and manage the AI Beyond Chat system.
              </p>
            </div>
            <div className="flex space-x-2">
              <Link
                to="/ai-admin/ai-dashboard/"
                className="bg-purple-600 hover:bg-purple-700 text-white text-xs font-medium py-2 px-3 rounded transition-colors"
              >
                AI Dashboard
              </Link>
              <Link
                to="/admin/ai_recommendations/airecommendation/"
                className="bg-gray-600 hover:bg-gray-700 text-white text-xs font-medium py-2 px-3 rounded transition-colors"
              >
                View Data
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AIAdminWidget;
