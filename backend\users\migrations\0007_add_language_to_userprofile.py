# Generated migration for adding language field to UserProfile

from django.db import migrations, models


def set_default_language(apps, schema_editor):
    """Set default language for existing user profiles"""
    UserProfile = apps.get_model('users', 'UserProfile')
    UserProfile.objects.filter(language__isnull=True).update(language='en')


def reverse_set_default_language(apps, schema_editor):
    """Reverse operation - no action needed"""
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0006_cleanup_orphaned_data'),
    ]

    operations = [
        migrations.AddField(
            model_name='userprofile',
            name='language',
            field=models.CharField(
                choices=[('en', 'English'), ('ar', 'Arabic')],
                default='en',
                help_text="User's preferred language for the interface",
                max_length=10,
                null=True,  # Allow null initially
                blank=True
            ),
        ),
        migrations.RunPython(
            set_default_language,
            reverse_set_default_language,
        ),
        migrations.AlterField(
            model_name='userprofile',
            name='language',
            field=models.CharField(
                choices=[('en', 'English'), ('ar', 'Arabic')],
                default='en',
                help_text="User's preferred language for the interface",
                max_length=10
            ),
        ),
    ]
