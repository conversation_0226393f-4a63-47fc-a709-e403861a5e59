// Dashboard stats types
export interface UserStats {
  total_users: number;
  active_users: number;
  new_users: number;
  user_growth_rate?: number;
  engagement_rate?: number;
  retention_rate?: number;
  posts_per_user?: number;
}

export interface EventStats {
  total_events: number;
  upcoming_events: number;
  new_events: number;
}

export interface ResourceStats {
  total_resources: number;
  resources_by_type: Record<string, number>;
  new_resources: number;
}

export interface PostStats {
  total_posts: number;
  popular_posts: PopularPost[];
  new_posts: number;
}

export interface PopularPost {
  id: number;
  title: string;
  like_count: number;
  author: string;
}

export interface DashboardStats {
  users: UserStats;
  events: EventStats;
  resources: ResourceStats;
  posts: PostStats;
}

// Recent activity types
export interface RecentActivity {
  type: 'user_joined' | 'event_created' | 'resource_shared' | 'post_created';
  user: string;
  timestamp: string;
  details?: string;
}

// Admin state type
export interface AdminState {
  dashboardStats: DashboardStats;
  recentActivity: RecentActivity[];
  users: any[];
  isLoading: boolean;
  error: string | null;
}
