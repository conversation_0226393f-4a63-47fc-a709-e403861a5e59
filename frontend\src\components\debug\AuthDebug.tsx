import React, { useState, useEffect } from 'react';
import { useAppSelector } from '../../store/hooks';
import { getAuthToken, getRefreshToken } from '../../services/api';
import { Shield, User, Key, Clock, CheckCircle, XCircle, AlertTriangle } from 'lucide-react';

const AuthDebug: React.FC = () => {
  const { user, isAuthenticated, loading } = useAppSelector(state => state.auth);
  const [tokenInfo, setTokenInfo] = useState<{
    hasAccessToken: boolean;
    hasRefreshToken: boolean;
    accessTokenExpiry: string | null;
    refreshTokenExpiry: string | null;
    isAccessTokenExpired: boolean;
    isRefreshTokenExpired: boolean;
  }>({
    hasAccessToken: false,
    hasRefreshToken: false,
    accessTokenExpiry: null,
    refreshTokenExpiry: null,
    isAccessTokenExpired: false,
    isRefreshTokenExpired: false
  });

  useEffect(() => {
    const checkTokens = () => {
      const accessToken = getAuthToken();
      const refreshToken = getRefreshToken();
      
      let accessTokenExpiry = null;
      let refreshTokenExpiry = null;
      let isAccessTokenExpired = false;
      let isRefreshTokenExpired = false;

      if (accessToken) {
        try {
          const payload = JSON.parse(atob(accessToken.split('.')[1]));
          accessTokenExpiry = new Date(payload.exp * 1000).toLocaleString();
          isAccessTokenExpired = payload.exp < Math.floor(Date.now() / 1000);
        } catch (e) {
          console.error('Error parsing access token:', e);
        }
      }

      if (refreshToken) {
        try {
          const payload = JSON.parse(atob(refreshToken.split('.')[1]));
          refreshTokenExpiry = new Date(payload.exp * 1000).toLocaleString();
          isRefreshTokenExpired = payload.exp < Math.floor(Date.now() / 1000);
        } catch (e) {
          console.error('Error parsing refresh token:', e);
        }
      }

      setTokenInfo({
        hasAccessToken: !!accessToken,
        hasRefreshToken: !!refreshToken,
        accessTokenExpiry,
        refreshTokenExpiry,
        isAccessTokenExpired,
        isRefreshTokenExpired
      });
    };

    checkTokens();
    const interval = setInterval(checkTokens, 5000); // Check every 5 seconds
    return () => clearInterval(interval);
  }, []);

  const getStatusIcon = (status: boolean) => {
    return status ? (
      <CheckCircle className="w-4 h-4 text-green-400" />
    ) : (
      <XCircle className="w-4 h-4 text-red-400" />
    );
  };

  const getExpiryIcon = (isExpired: boolean) => {
    return isExpired ? (
      <AlertTriangle className="w-4 h-4 text-yellow-400" />
    ) : (
      <CheckCircle className="w-4 h-4 text-green-400" />
    );
  };

  return (
    <div className="bg-gray-800 border border-gray-600 rounded-lg p-4 m-4 text-white">
      <div className="flex items-center gap-2 mb-4">
        <Shield className="w-5 h-5 text-blue-400" />
        <h3 className="text-lg font-semibold">Authentication Debug Info</h3>
      </div>

      <div className="space-y-3">
        {/* Redux Auth State */}
        <div className="border-b border-gray-600 pb-3">
          <h4 className="text-sm font-semibold text-gray-300 mb-2">Redux Auth State:</h4>
          
          <div className="flex items-center justify-between">
            <span className="text-gray-400 text-sm">Is Authenticated:</span>
            <div className="flex items-center gap-2">
              {getStatusIcon(isAuthenticated)}
              <span className={isAuthenticated ? 'text-green-400' : 'text-red-400'}>
                {isAuthenticated ? 'Yes' : 'No'}
              </span>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-gray-400 text-sm">Loading:</span>
            <div className="flex items-center gap-2">
              {getStatusIcon(!loading)}
              <span className={!loading ? 'text-green-400' : 'text-yellow-400'}>
                {loading ? 'Yes' : 'No'}
              </span>
            </div>
          </div>

          {user && (
            <div className="flex items-center justify-between">
              <span className="text-gray-400 text-sm">User ID:</span>
              <span className="text-white text-sm">{user.id}</span>
            </div>
          )}

          {user && (
            <div className="flex items-center justify-between">
              <span className="text-gray-400 text-sm">Username:</span>
              <span className="text-white text-sm">{user.username}</span>
            </div>
          )}
        </div>

        {/* Token Information */}
        <div className="border-b border-gray-600 pb-3">
          <h4 className="text-sm font-semibold text-gray-300 mb-2">Token Information:</h4>
          
          <div className="flex items-center justify-between">
            <span className="text-gray-400 text-sm">Access Token:</span>
            <div className="flex items-center gap-2">
              {getStatusIcon(tokenInfo.hasAccessToken)}
              <span className={tokenInfo.hasAccessToken ? 'text-green-400' : 'text-red-400'}>
                {tokenInfo.hasAccessToken ? 'Present' : 'Missing'}
              </span>
            </div>
          </div>

          {tokenInfo.hasAccessToken && (
            <div className="flex items-center justify-between">
              <span className="text-gray-400 text-sm">Access Token Expiry:</span>
              <div className="flex items-center gap-2">
                {getExpiryIcon(tokenInfo.isAccessTokenExpired)}
                <span className={tokenInfo.isAccessTokenExpired ? 'text-yellow-400' : 'text-green-400'}>
                  {tokenInfo.accessTokenExpiry}
                </span>
              </div>
            </div>
          )}

          <div className="flex items-center justify-between">
            <span className="text-gray-400 text-sm">Refresh Token:</span>
            <div className="flex items-center gap-2">
              {getStatusIcon(tokenInfo.hasRefreshToken)}
              <span className={tokenInfo.hasRefreshToken ? 'text-green-400' : 'text-red-400'}>
                {tokenInfo.hasRefreshToken ? 'Present' : 'Missing'}
              </span>
            </div>
          </div>

          {tokenInfo.hasRefreshToken && (
            <div className="flex items-center justify-between">
              <span className="text-gray-400 text-sm">Refresh Token Expiry:</span>
              <div className="flex items-center gap-2">
                {getExpiryIcon(tokenInfo.isRefreshTokenExpired)}
                <span className={tokenInfo.isRefreshTokenExpired ? 'text-yellow-400' : 'text-green-400'}>
                  {tokenInfo.refreshTokenExpiry}
                </span>
              </div>
            </div>
          )}
        </div>

        {/* Recommendations */}
        <div>
          <h4 className="text-sm font-semibold text-gray-300 mb-2">Recommendations:</h4>
          
          {!isAuthenticated && (
            <div className="text-sm text-yellow-300">
              • User is not authenticated. Please login to access admin features.
            </div>
          )}

          {!tokenInfo.hasAccessToken && (
            <div className="text-sm text-red-300">
              • No access token found. Please login again.
            </div>
          )}

          {tokenInfo.isAccessTokenExpired && (
            <div className="text-sm text-yellow-300">
              • Access token is expired. It should be automatically refreshed.
            </div>
          )}

          {tokenInfo.isRefreshTokenExpired && (
            <div className="text-sm text-red-300">
              • Refresh token is expired. Please login again.
            </div>
          )}

          {isAuthenticated && tokenInfo.hasAccessToken && !tokenInfo.isAccessTokenExpired && (
            <div className="text-sm text-green-300">
              • Authentication looks good! All tokens are valid.
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AuthDebug;
