import React, { useState, useEffect } from 'react';
import { Search, RefreshCw } from 'lucide-react';

import { Post } from '../../../../services/api';
import { useAppSelector } from '../../../../store/hooks';
import { AdvancedFilter, BulkActions } from '../../common';
import { getPostBulkActions } from '../../common/BulkActions';
import { PostSkeleton, SkeletonList } from '../../../ui/Skeleton';
import { <PERSON><PERSON>, Button } from '../../../ui';
import {
  PostItem,
  PostCreateModal,
  PostEditModal,
  PostDeleteModal,
  PostsHeader
} from './components';
import { usePostFilters } from './hooks';
import { usePostsInfinite, useCreatePost, useUpdatePost, useDeletePost } from '../../../../hooks/usePosts';

import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../hooks/useLanguage';

const PostsManagementInfiniteWithQuery: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const { user } = useAppSelector(state => state.auth);
  const [expandedPost, setExpandedPost] = useState<number | null>(null);
  const [selectedItems, setSelectedItems] = useState<number[]>([]);


  // Form state
  const [formData, setFormData] = useState<Partial<Post>>({});
  const [formError, setFormError] = useState<string | null>(null);
  const [formSuccess, setFormSuccess] = useState<string | null>(null);
  const [selectedPost, setSelectedPost] = useState<Post | null>(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  // Use React Query for infinite posts loading
  const {
    data,
    isLoading,
    isError,
    error,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch
  } = usePostsInfinite({
    page_size: 10,
    ordering: '-created_at'
  });

  // Extract posts from all pages
  const posts = data?.pages.flatMap(page => page.results) || [];

  // Use React Query mutations
  const createPostMutation = useCreatePost();
  const updatePostMutation = useUpdatePost(selectedPost?.id || 0);
  const deletePostMutation = useDeletePost(selectedPost?.id || 0);

  // Use custom hook for filtering
  const {
    searchTerm,
    setSearchTerm,
    activeFilters,
    handleFilterChange,
    filteredPosts,
    postFilterOptions
  } = usePostFilters(posts);

  // Effect to refresh posts when filters change
  useEffect(() => {
    if (activeFilters.length > 0) {
      refetch();
    }
  }, [activeFilters, refetch]);

  // Form handlers
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const openCreateModal = () => {
    setFormData({});
    setFormError(null);
    setFormSuccess(null);
    setIsCreateModalOpen(true);
  };

  const closeCreateModal = () => {
    setIsCreateModalOpen(false);
  };

  const handleEditPost = (post: Post) => {
    setSelectedPost(post);
    setFormData({
      title: post.title,
      content: post.content,
    });
    setFormError(null);
    setFormSuccess(null);
    setIsEditModalOpen(true);
  };

  const closeEditModal = () => {
    setIsEditModalOpen(false);
  };

  const handleDeletePost = (post: Post) => {
    setSelectedPost(post);
    setFormError(null);
    setIsDeleteModalOpen(true);
  };

  const closeDeleteModal = () => {
    setIsDeleteModalOpen(false);
  };

  // Create post
  const createPost = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);
    setFormSuccess(null);

    try {
      await createPostMutation.mutateAsync({
        ...formData,
        author_id: user?.id || 0
      });
      setFormSuccess(t("admin.post.created.successfully", "Post created successfully"));
      setFormData({});
      setTimeout(() => {
        closeCreateModal();
        setFormSuccess(null);
      }, 2000);
    } catch (err) {
      setFormError(err instanceof Error ? err.message : t("admin.failed.to.create", "Failed to create post"));
    }
  };

  // Update post
  const updatePost = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedPost) return;

    setFormError(null);
    setFormSuccess(null);

    try {
      await updatePostMutation.mutateAsync(formData);
      setFormSuccess(t("admin.post.updated.successfully", "Post updated successfully"));
      setTimeout(() => {
        closeEditModal();
        setFormSuccess(null);
      }, 2000);
    } catch (err) {
      setFormError(err instanceof Error ? err.message : t("admin.failed.to.update", "Failed to update post"));
    }
  };

  // Delete post
  const confirmDeletePost = async () => {
    if (!selectedPost) return;

    setFormError(null);

    try {
      await deletePostMutation.mutateAsync();
      closeDeleteModal();
    } catch (err) {
      setFormError(err instanceof Error ? err.message : t("admin.failed.to.delete", "Failed to delete post"));
    }
  };

  // Toggle expanded post
  const toggleExpandPost = (postId: number) => {
    setExpandedPost(expandedPost === postId ? null : postId);
  };

  // Toggle item selection
  const toggleItemSelection = (id: number) => {
    setSelectedItems(prev =>
      prev.includes(id)
        ? prev.filter(itemId => itemId !== id)
        : [...prev, id]
    );
  };

  // Deselect all items
  const handleDeselectAll = () => {
    setSelectedItems([]);
  };

  // Bulk action handlers
  const handleBulkApprove = async (_ids: number[]): Promise<void> => {
    try {
      // Implementation would go here
      // After successful operation:
      refetch();
      setSelectedItems([]);
      return Promise.resolve();
    } catch (error) {
      console.error('Error approving posts:', error);
      return Promise.reject(error);
    }
  };

  const handleBulkReject = async (_ids: number[]): Promise<void> => {
    try {
      // Implementation would go here
      // After successful operation:
      refetch();
      setSelectedItems([]);
      return Promise.resolve();
    } catch (error) {
      console.error('Error rejecting posts:', error);
      return Promise.reject(error);
    }
  };

  const handleBulkDelete = async (_ids: number[]): Promise<void> => {
    try {
      // Implementation would go here
      // After successful operation:
      refetch();
      setSelectedItems([]);
      return Promise.resolve();
    } catch (error) {
      console.error('Error deleting posts:', error);
      return Promise.reject(error);
    }
  };

  // Prevent form submission during loading
  const preventFormSubmission =
    createPostMutation.isPending ||
    updatePostMutation.isPending ||
    deletePostMutation.isPending;

  return (
    <DashboardLayout currentPage="posts">
      <PostsHeader
        onCreateClick={openCreateModal}
        preventFormSubmission={preventFormSubmission}
      />

      {/* Advanced Filter Component */}
      <AdvancedFilter
        filterOptions={postFilterOptions}
        onFilterChange={handleFilterChange}
        activeFilters={activeFilters}
      />

      {/* Search and Bulk Actions */}
      <div className={`mb-6 flex flex-col sm:flex-row justify-between items-start gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className="relative w-full sm:w-64">
          <input
            type="text"
            placeholder={t("admin.search.posts", "Search posts...")}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 bg-indigo-900/30 border border-indigo-800 rounded-lg text-white focus:outline-none focus:border-purple-500"
          />
          <Search className="absolute left-3 top-2.5 text-gray-400" size={18} />
        </div>

        {selectedItems.length > 0 && (
          <BulkActions
            selectedCount={selectedItems.length}
            onDeselectAll={handleDeselectAll}
            actions={getPostBulkActions(handleBulkApprove, handleBulkReject, handleBulkDelete)}
            selectedIds={selectedItems}
          />
        )}
      </div>

      <div className="bg-indigo-900/20 rounded-xl overflow-hidden shadow-lg">
        {isError && (
          <Alert variant="error" className="m-4">
            {error instanceof Error ? error.message : t("admin.an.error.occurred", "An error occurred while loading posts")}
            <Button onClick={() => refetch()} className="mt-2">
              Retry
            </Button>
          </Alert>
        )}

        {isLoading && posts.length === 0 ? (
          <SkeletonList count={5} SkeletonComponent={PostSkeleton} />
        ) : (
          <div className="divide-y divide-indigo-800/30">
            {filteredPosts.length === 0 ? (
              <div className="text-center py-10">
                <div className="text-gray-400 text-lg">{t("admin.no.posts.found", "No posts found matching your search.")}</div>
              </div>
            ) : (
              <>
                {filteredPosts.map((post, index) => (
                  <PostItem
                    key={post.id}
                    post={post}
                    isExpanded={expandedPost === post.id}
                    isSelected={selectedItems.includes(post.id)}
                    onToggleExpand={toggleExpandPost}
                    onToggleSelect={toggleItemSelection}
                    onEdit={handleEditPost}
                    onDelete={handleDeletePost}
                    ref={index === filteredPosts.length - 1 && hasNextPage ?
                      (node) => {
                        if (node) {
                          const observer = new IntersectionObserver(
                            (entries) => {
                              if (entries[0].isIntersecting && hasNextPage && !isFetchingNextPage) {
                                fetchNextPage();
                              }
                            },
                            { threshold: 0.5 }
                          );
                          observer.observe(node);
                          return () => observer.disconnect();
                        }
                      } : undefined
                    }
                  />
                ))}

                {/* Loading indicator at the bottom for infinite scroll */}
                {isFetchingNextPage && (
                  <div className={`flex justify-center py-6 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className="w-8 h-8 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
                  </div>
                )}

                {/* Load more button as fallback */}
                {hasNextPage && !isFetchingNextPage && (
                  <div className={`flex justify-center py-6 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <Button
                      onClick={() => fetchNextPage()}
                      leftIcon={<RefreshCw size={16} />}
                    >
                      Load More
                    </Button>
                  </div>
                )}
              </>
            )}
          </div>
        )}
      </div>

      {/* Modals */}
      <PostCreateModal
        isOpen={isCreateModalOpen}
        onClose={closeCreateModal}
        formData={formData}
        formError={formError}
        formSuccess={formSuccess}
        formSubmitting={createPostMutation.isPending}
        onInputChange={handleInputChange}
        onSubmit={createPost}
        preventFormSubmission={preventFormSubmission}
      />

      <PostEditModal
        isOpen={isEditModalOpen}
        onClose={closeEditModal}
        selectedPost={selectedPost}
        formData={formData}
        formError={formError}
        formSuccess={formSuccess}
        formSubmitting={updatePostMutation.isPending}
        onInputChange={handleInputChange}
        onSubmit={updatePost}
        preventFormSubmission={preventFormSubmission}
      />

      <PostDeleteModal
        isOpen={isDeleteModalOpen}
        onClose={closeDeleteModal}
        selectedPost={selectedPost}
        formError={formError}
        formSubmitting={deletePostMutation.isPending}
        onConfirmDelete={confirmDeletePost}
      />
    </DashboardLayout>
  );
};

export default PostsManagementInfiniteWithQuery;
