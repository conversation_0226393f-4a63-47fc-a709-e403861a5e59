import React from 'react';
import { Link } from 'react-router-dom';
import { ForumCategory } from '../../services/forumApi';
import { MessageSquare, Users, Clock, ArrowRight } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
interface ForumCategoriesProps {
  categories: ForumCategory[];
}

const ForumCategories: React.FC<ForumCategoriesProps> = ({ categories  }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  // Function to get icon component based on icon name
  const getIconComponent = (iconName: string) => {
    switch (iconName) {
      case 'message-square':
        return <MessageSquare size={24} className="text-purple-400" />;
      case 'users':
        return <Users size={24} className="text-blue-400" />;
      default:
        return <MessageSquare size={24} className="text-purple-400" />;
    }
  };

  if (categories.length === 0) {
    return (
      <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50 text-center">
        <MessageSquare size={48} className="text-gray-500 mx-auto mb-4" />
        <div className="text-gray-400 mb-2">t("common.no.forum.categories", "No forum categories available yet.")</div>
        <div className="text-gray-500 text-sm">
          Forum categories will appear here once they are created by administrators.
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {categories.map((category) => (
        <div
          key={category.id}
          className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50"
        >
          <div className={`flex items-start ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className={`mr-4 ${isRTL ? "space-x-reverse" : ""}`}>
              {category.icon ? getIconComponent(category.icon) : <MessageSquare size={24} className="text-purple-400" />}
            </div>
            <div className={`flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
              <Link to={`/forum/category/${category.slug}`} className="text-xl font-semibold hover:text-purple-300">
                {category.name}
              </Link>
              <div className="text-gray-400 mt-1">{category.description}</div>

              <div className={`flex flex-wrap gap-4 mt-4 text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
                <div className={`flex items-center text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <MessageSquare size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                  <span>{category.topic_count} Topics</span>
                </div>
                <div className={`flex items-center text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <Users size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                  <span>{category.thread_count} Threads</span>
                </div>
                <div className={`flex items-center text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <Clock size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                  <span>Last activity: {formatDistanceToNow(new Date(category.updated_at), { addSuffix: true })}</span>
                </div>
              </div>

              {category.topics && category.topics.length > 0 && (
                <div className="mt-4 pt-4 border-t border-indigo-800/50">
                  <h3 className="text-sm font-medium text-gray-300 mb-2">t("common.popular.topics", "Popular Topics")</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {category.topics.slice(0, 4).map((topic) => (
                      <Link
                        key={topic.id}
                        to={`/forum/topic/${topic.slug}`}
                        className={`flex items-center text-purple-400 hover:text-purple-300 ${isRTL ? "flex-row-reverse" : ""}`}
                      >
                        <ArrowRight size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                        <span>{topic.title}</span>
                      </Link>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ForumCategories;
