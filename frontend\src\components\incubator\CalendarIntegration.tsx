import React, { useState, useEffect } from 'react';
import { Calendar, Check, X, AlertCircle, Clock, Plus, RefreshCw, Trash2 } from 'lucide-react';
import { useAppDispatch } from '../../store/hooks';
import { MentorshipSession } from '../../services/incubatorApi';
import { mentorshipSessionsAPI } from '../../services/incubatorApi';

import { useTranslation } from 'react-i18next';
interface CalendarIntegrationProps {
  session: MentorshipSession;
  onSuccess?: () => void;
}

const CalendarIntegration: React.FC<CalendarIntegrationProps> = ({ session,
  onSuccess
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const dispatch = useAppDispatch();
  
  const [selectedProvider, setSelectedProvider] = useState<string>('google_calendar');
  const [isAdding, setIsAdding] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isRemoving, setIsRemoving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  
  const calendarProviders = [
    { id: 'google_calendar', name: t("common.google.calendar.icon", "Google Calendar"), icon: 'google") },
    { id: 'outlook', name: t("common.outlook.calendar.icon", "Outlook Calendar"), icon: 'outlook") },
  ];
  
  const handleAddToCalendar = async () => {
    setIsAdding(true);
    setError(null);
    
    try {
      const response = await mentorshipSessionsAPI.addToCalendar(session.id, {
        calendar_provider: selectedProvider
      });
      
      setSuccessMessage(t("common.session.added.to", "Session added to calendar successfully!"));
      
      // Call onSuccess callback if provided
      if (onSuccess) {
        setTimeout(() => {
          onSuccess();
        }, 1500);
      }
    } catch (err) {
      setError(t("common.failed.to.add", "Failed to add session to calendar. Please try again."));
      console.error('Error adding to calendar:', err);
    } finally {
      setIsAdding(false);
    }
  };
  
  const handleUpdateCalendar = async () => {
    setIsUpdating(true);
    setError(null);
    
    try {
      const response = await mentorshipSessionsAPI.updateCalendar(session.id);
      
      setSuccessMessage(t("common.calendar.event.updated", "Calendar event updated successfully!"));
      
      // Call onSuccess callback if provided
      if (onSuccess) {
        setTimeout(() => {
          onSuccess();
        }, 1500);
      }
    } catch (err) {
      setError(t("common.failed.to.update", "Failed to update calendar event. Please try again."));
      console.error('Error updating calendar:', err);
    } finally {
      setIsUpdating(false);
    }
  };
  
  const handleRemoveFromCalendar = async () => {
    setIsRemoving(true);
    setError(null);
    
    try {
      const response = await mentorshipSessionsAPI.removeFromCalendar(session.id);
      
      setSuccessMessage(t("common.session.removed.from", "Session removed from calendar successfully!"));
      
      // Call onSuccess callback if provided
      if (onSuccess) {
        setTimeout(() => {
          onSuccess();
        }, 1500);
      }
    } catch (err) {
      setError(t("common.failed.to.remove", "Failed to remove session from calendar. Please try again."));
      console.error('Error removing from calendar:', err);
    } finally {
      setIsRemoving(false);
    }
  };
  
  // Clear success message after 3 seconds
  useEffect(() => {
    if (successMessage) {
      const timer = setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
      
      return () => clearTimeout(timer);
    }
  }, [successMessage]);
  
  const isInCalendar = !!session.calendar_provider;
  
  return (
    <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
      <div className={`flex items-center justify-between mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
        <h2 className="text-xl font-semibold">t("common.calendar.integration", "Calendar Integration")</h2>
        <Calendar size={24} className="text-purple-400" />
      </div>
      
      {successMessage && (
        <div className={`bg-green-900/30 border border-green-800/50 rounded-lg p-4 mb-4 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
          <Check size={20} className={`text-green-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
          <div>{successMessage}</div>
        </div>
      )}
      
      {error && (
        <div className={`bg-red-900/30 border border-red-800/50 rounded-lg p-4 mb-4 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
          <AlertCircle size={20} className={`text-red-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
          <div>{error}</div>
        </div>
      )}
      
      <div className="mb-6">
        <div className={`flex items-center mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
          <Clock size={18} className={`text-purple-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
          <div className="text-sm text-gray-300">
            {new Date(session.scheduled_at).toLocaleString()} ({session.duration_minutes} minutes)
          </div>
        </div>
        
        <p className="text-sm text-gray-400 mb-4">
          Add this mentorship session to your calendar to receive reminders and manage your schedule.
        </div>
        
        {isInCalendar ? (
          <div className="bg-indigo-800/30 rounded-lg p-4 mb-4">
            <div className={`flex items-center justify-between mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
              <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <Calendar size={18} className={`text-green-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                <div className="text-sm">
                  This session is in your {session.calendar_provider === 'google_calendar' ? t("common.google.calendar.calendar", "Google Calendar' : 'calendar")}
                </div>
              </div>
            </div>
            
            <div className={`flex space-x-3 mt-4 ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                onClick={handleUpdateCalendar}
                disabled={isUpdating}
                className={`px-3 py-1.5 bg-indigo-700/50 hover:bg-indigo-700 rounded text-sm flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
              >
                {isUpdating ? (
                  <>
                    <div className={`w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin mr-1 ${isRTL ? "space-x-reverse" : ""}`}></div>
                    Updating...
                  </>
                ) : (
                  <>
                    <RefreshCw size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> Update
                  </>
                )}
              </button>
              
              <button
                onClick={handleRemoveFromCalendar}
                disabled={isRemoving}
                className={`px-3 py-1.5 bg-red-900/30 hover:bg-red-900/50 rounded text-sm flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
              >
                {isRemoving ? (
                  <>
                    <div className={`w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin mr-1 ${isRTL ? "space-x-reverse" : ""}`}></div>
                    Removing...
                  </>
                ) : (
                  <>
                    <Trash2 size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> Remove
                  </>
                )}
              </button>
            </div>
          </div>
        ) : (
          <>
            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">t("common.select.calendar.provider", "Select Calendar Provider")</label>
              <div className="grid grid-cols-2 gap-3">
                {calendarProviders.map((provider) => (
                  <button
                    key={provider.id}
                    type="button"
                    onClick={() => setSelectedProvider(provider.id)}
                    className={`p-3 rounded-lg border flex items-center justify-center ${
                      selectedProvider === provider.id
                        ? 'border-purple-500 bg-purple-900/20'
                        : 'border-indigo-800/50 hover:bg-indigo-800/20'}
                    }`}
                  >
                    <span className={`mr-2 ${provider.icon === 'google' ? 'text-red-400' : 'text-blue-400'}`}>
                      {provider.icon === 'google' ? t("common.g.o", "G' : 'O")}
                    </span>
                    {provider.name}
                  </button>
                ))}
              </div>
            </div>
            
            <button
              onClick={handleAddToCalendar}
              disabled={isAdding}
              className={`w-full py-2 bg-purple-600 hover:bg-purple-700 rounded-lg text-white flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}
            >
              {isAdding ? (
                <>
                  <div className={`w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 ${isRTL ? "space-x-reverse" : ""}`}></div>
                  Adding to Calendar...
                </>
              ) : (
                <>
                  <Plus size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> Add to Calendar
                </>
              )}
            </button>
          </>
        )}
      </div>
    </div>
  );
};

export default CalendarIntegration;
