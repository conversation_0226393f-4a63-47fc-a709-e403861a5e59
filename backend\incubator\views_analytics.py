from rest_framework import viewsets, permissions, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from django.db.models import Q
from django_filters.rest_framework import DjangoFilterBackend
from datetime import timedelta

from .models_base import BusinessIdea
from .models_milestone import BusinessAnalytics, BusinessMilestone, BusinessGoal
from .models_analytics import (
    PredictiveAnalytics, ComparativeAnalytics, 
    AnalyticsSnapshot, MetricDefinition
)
from .serializers_analytics import (
    PredictiveAnalyticsSerializer, ComparativeAnalyticsSerializer,
    AnalyticsSnapshotSerializer, MetricDefinitionSerializer,
    EnhancedBusinessAnalyticsSerializer, AnalyticsDashboardSerializer
)
from .analytics_service import PredictiveAnalyticsService
from api.permissions import IsAdminUser, IsOwnerOrAdmin

class EnhancedBusinessAnalyticsViewSet(viewsets.ModelViewSet):
    """Enhanced ViewSet for business analytics with predictive and comparative data"""
    queryset = BusinessAnalytics.objects.all().order_by('-last_calculated')
    serializer_class = EnhancedBusinessAnalyticsSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['business_idea']
    search_fields = ['business_idea__title']
    
    def get_permissions(self):
        """Only allow owners or admins to access analytics"""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsAdminUser()]
        return [permissions.IsAuthenticated(), IsOwnerOrAdmin()]
    
    def get_queryset(self):
        """Filter analytics based on user permissions"""
        user = self.request.user
        if not user.is_authenticated:
            return BusinessAnalytics.objects.none()
        
        if user.is_staff or user.is_superuser:
            return super().get_queryset()
        
        # Users can see analytics for their own business ideas or ideas they collaborate on
        return BusinessAnalytics.objects.filter(
            Q(business_idea__owner=user) | 
            Q(business_idea__collaborators=user)
        ).distinct()
    
    @action(detail=True, methods=['post'])
    def recalculate(self, request, pk=None):
        """Recalculate analytics for a business idea"""
        analytics = self.get_object()
        business_idea = analytics.business_idea
        
        # Recalculate basic analytics
        self._calculate_basic_analytics(analytics, business_idea)
        
        # Generate predictive analytics
        self._generate_predictive_analytics(business_idea)
        
        # Generate comparative analytics
        self._generate_comparative_analytics(business_idea)
        
        # Create analytics snapshot
        self._create_analytics_snapshot(analytics, business_idea)
        
        # Return updated analytics with dashboard data
        serializer = AnalyticsDashboardSerializer(analytics)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def dashboard(self, request):
        """Get analytics dashboard data for a business idea"""
        business_idea_id = request.query_params.get('business_idea')
        if not business_idea_id:
            return Response(
                {"error": "business_idea parameter is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            business_idea = BusinessIdea.objects.get(id=business_idea_id)
            
            # Check permissions
            if not (request.user.is_staff or request.user.is_superuser or 
                    business_idea.owner == request.user or 
                    business_idea.collaborators.filter(id=request.user.id).exists()):
                return Response(
                    {"error": "You do not have permission to view this business idea's analytics"},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            # Get or create analytics
            analytics, created = BusinessAnalytics.objects.get_or_create(business_idea=business_idea)
            
            # If analytics is older than 24 hours or just created, recalculate
            if created or (timezone.now() - analytics.last_calculated) > timedelta(hours=24):
                self._calculate_basic_analytics(analytics, business_idea)
            
            # Generate predictive analytics if needed
            try:
                predictive = PredictiveAnalytics.objects.get(business_idea=business_idea)
                if (timezone.now() - predictive.last_calculated) > timedelta(days=7):
                    self._generate_predictive_analytics(business_idea)
            except PredictiveAnalytics.DoesNotExist:
                self._generate_predictive_analytics(business_idea)
            
            # Generate comparative analytics if needed
            try:
                comparative = ComparativeAnalytics.objects.get(business_idea=business_idea)
                if (timezone.now() - comparative.last_calculated) > timedelta(days=7):
                    self._generate_comparative_analytics(business_idea)
            except ComparativeAnalytics.DoesNotExist:
                self._generate_comparative_analytics(business_idea)
            
            # Return dashboard data
            serializer = AnalyticsDashboardSerializer(analytics)
            return Response(serializer.data)
            
        except BusinessIdea.DoesNotExist:
            return Response(
                {"error": "Business idea not found"},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {"error": f"Error generating analytics dashboard: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def _calculate_basic_analytics(self, analytics, business_idea):
        """Calculate basic analytics metrics"""
        # Calculate progress rate (updates per month)
        thirty_days_ago = timezone.now() - timedelta(days=30)
        updates_count = business_idea.progress_updates.filter(created_at__gte=thirty_days_ago).count()
        analytics.progress_rate = updates_count
        
        # Calculate milestone completion rate
        total_milestones = business_idea.milestones.count()
        if total_milestones > 0:
            completed_milestones = business_idea.milestones.filter(status='completed').count()
            analytics.milestone_completion_rate = (completed_milestones / total_milestones) * 100
        else:
            analytics.milestone_completion_rate = 0
        
        # Calculate goal achievement rate
        total_goals = business_idea.goals.count()
        if total_goals > 0:
            achieved_goals = business_idea.goals.filter(status='achieved').count()
            analytics.goal_achievement_rate = (achieved_goals / total_goals) * 100
        else:
            analytics.goal_achievement_rate = 0
        
        # Calculate team size
        analytics.team_size = 1 + business_idea.collaborators.count()  # Owner + collaborators
        
        # Calculate mentor engagement
        from .models import MentorshipMatch, MentorshipSession
        
        matches = MentorshipMatch.objects.filter(business_idea=business_idea, status='active')
        if matches.exists():
            # Calculate engagement based on session frequency and feedback
            total_sessions = MentorshipSession.objects.filter(
                mentorship_match__in=matches,
                status__in=['completed', 'scheduled']
            ).count()
            
            # Calculate sessions per month per mentor
            months_active = max(1, (timezone.now() - business_idea.created_at).days / 30)
            mentor_count = matches.count()
            
            if mentor_count > 0:
                sessions_per_month_per_mentor = total_sessions / (months_active * mentor_count)
                
                # Score from 0-100 based on session frequency
                # 1 session per month = 25, 2 = 50, 3 = 75, 4+ = 100
                analytics.mentor_engagement = min(100, sessions_per_month_per_mentor * 25)
            else:
                analytics.mentor_engagement = 0
        else:
            analytics.mentor_engagement = 0
        
        # Calculate industry and stage percentiles
        # This would require more complex calculations comparing to other business ideas
        # For now, set to default values
        analytics.industry_percentile = 50.0
        analytics.stage_percentile = 50.0
        
        # Save updated analytics
        analytics.save()
        
        return analytics
    
    def _generate_predictive_analytics(self, business_idea):
        """Generate predictive analytics for a business idea"""
        # Get or create predictive analytics
        predictive, created = PredictiveAnalytics.objects.get_or_create(business_idea=business_idea)
        
        # Generate milestone predictions
        milestone_predictions = PredictiveAnalyticsService.predict_milestone_completion(business_idea.id)
        predictive.milestone_predictions = {'predictions': milestone_predictions}
        
        # Generate growth trajectory
        growth_predictions = PredictiveAnalyticsService.predict_growth_trajectory(business_idea.id)
        predictive.growth_predictions = growth_predictions
        
        # Set prediction confidence based on data quality
        predictive.prediction_confidence = growth_predictions.get('confidence', 'low')
        
        # Calculate success probability based on various factors
        try:
            analytics = BusinessAnalytics.objects.get(business_idea=business_idea)
            
            # Simple weighted formula for success probability
            milestone_weight = 0.3
            goal_weight = 0.2
            progress_weight = 0.2
            engagement_weight = 0.3
            
            success_probability = (
                analytics.milestone_completion_rate * milestone_weight +
                analytics.goal_achievement_rate * goal_weight +
                (analytics.progress_rate * 10) * progress_weight +  # Scale progress rate (0-10)
                analytics.mentor_engagement * engagement_weight
            )
            
            # Cap at 100
            predictive.success_probability = min(100, success_probability)
            
        except BusinessAnalytics.DoesNotExist:
            predictive.success_probability = 50.0  # Default value
        
        # Identify risk factors
        risk_factors = {
            'high_risks': [],
            'medium_risks': [],
            'low_risks': []
        }
        
        # Check for milestone delays
        delayed_milestones = BusinessMilestone.objects.filter(
            business_idea=business_idea,
            status='delayed'
        )
        
        if delayed_milestones.exists():
            risk_factors['high_risks'].append({
                'title': 'Delayed Milestones',
                'description': f'{delayed_milestones.count()} milestones are currently delayed',
                'mitigation': 'Review and adjust milestone timelines or allocate more resources'
            })
        
        # Check for low mentor engagement
        try:
            analytics = BusinessAnalytics.objects.get(business_idea=business_idea)
            if analytics.mentor_engagement < 25:
                risk_factors['medium_risks'].append({
                    'title': 'Low Mentor Engagement',
                    'description': 'Mentor engagement is below recommended levels',
                    'mitigation': 'Schedule regular sessions with mentors and actively seek guidance'
                })
        except BusinessAnalytics.DoesNotExist:
            pass
        
        # Check for infrequent progress updates
        thirty_days_ago = timezone.now() - timedelta(days=30)
        recent_updates = business_idea.progress_updates.filter(created_at__gte=thirty_days_ago).count()
        
        if recent_updates < 2:
            risk_factors['medium_risks'].append({
                'title': 'Infrequent Progress Updates',
                'description': 'Less than 2 progress updates in the last 30 days',
                'mitigation': 'Set a regular schedule for updating progress, at least weekly'
            })
        
        predictive.risk_factors = risk_factors
        
        # Identify opportunity areas
        opportunity_areas = {
            'key_opportunities': []
        }
        
        # Check for potential funding opportunities
        from .models import FundingOpportunity
        
        matching_opportunities = FundingOpportunity.objects.filter(
            Q(industry__icontains=business_idea.industry) |
            Q(eligibility_criteria__icontains=business_idea.current_stage)
        ).filter(application_deadline__gt=timezone.now())[:3]
        
        if matching_opportunities.exists():
            opportunity_areas['key_opportunities'].append({
                'title': 'Funding Opportunities',
                'description': f'{matching_opportunities.count()} funding opportunities match your business profile',
                'action': 'Review and apply for relevant funding opportunities'
            })
        
        # Check for potential mentor matches
        from .models import MentorProfile
        
        potential_mentors = MentorProfile.objects.filter(
            expertise__name__in=[tag.name for tag in business_idea.tags.all()]
        ).exclude(
            mentorship_matches__business_idea=business_idea
        )[:3]
        
        if potential_mentors.exists():
            opportunity_areas['key_opportunities'].append({
                'title': 'Potential Mentors',
                'description': f'{potential_mentors.count()} mentors with relevant expertise are available',
                'action': 'Reach out to these mentors for guidance in your specific domain'
            })
        
        predictive.opportunity_areas = opportunity_areas
        
        # Save updated predictive analytics
        predictive.save()
        
        return predictive
    
    def _generate_comparative_analytics(self, business_idea):
        """Generate comparative analytics for a business idea"""
        # Get or create comparative analytics
        comparative, created = ComparativeAnalytics.objects.get_or_create(business_idea=business_idea)
        
        # Get comparative data from service
        comparative_data = PredictiveAnalyticsService.get_comparative_analytics(business_idea.id)
        
        # Update comparative analytics
        comparative.similar_ideas = {'similar_ideas': comparative_data.get('similar_ideas', [])}
        comparative.industry_averages = {'averages': comparative_data.get('averages', {})}
        comparative.stage_averages = {'averages': comparative_data.get('averages', {})}  # Same as industry for now
        comparative.percentile_rankings = {'percentiles': comparative_data.get('percentiles', {})}
        
        # Identify competitive advantages and disadvantages
        try:
            analytics = BusinessAnalytics.objects.get(business_idea=business_idea)
            averages = comparative_data.get('averages', {})
            
            advantages = []
            disadvantages = []
            
            # Compare metrics to averages
            if analytics.progress_rate > averages.get('progress_rate', 0) * 1.2:  # 20% better
                advantages.append({
                    'area': 'Progress Rate',
                    'value': analytics.progress_rate,
                    'average': averages.get('progress_rate', 0),
                    'difference': f"{((analytics.progress_rate / max(0.1, averages.get('progress_rate', 0))) - 1) * 100:.0f}% higher",
                    'impact': 'Faster iteration and learning cycles'
                })
            elif analytics.progress_rate < averages.get('progress_rate', 0) * 0.8:  # 20% worse
                disadvantages.append({
                    'area': 'Progress Rate',
                    'value': analytics.progress_rate,
                    'average': averages.get('progress_rate', 0),
                    'difference': f"{(1 - (analytics.progress_rate / max(0.1, averages.get('progress_rate', 0)))) * 100:.0f}% lower",
                    'suggestion': 'Increase frequency of progress updates and iterations'
                })
            
            if analytics.milestone_completion_rate > averages.get('milestone_completion_rate', 0) * 1.2:
                advantages.append({
                    'area': 'Milestone Completion',
                    'value': analytics.milestone_completion_rate,
                    'average': averages.get('milestone_completion_rate', 0),
                    'difference': f"{((analytics.milestone_completion_rate / max(0.1, averages.get('milestone_completion_rate', 0))) - 1) * 100:.0f}% higher",
                    'impact': 'Better execution and project management'
                })
            elif analytics.milestone_completion_rate < averages.get('milestone_completion_rate', 0) * 0.8:
                disadvantages.append({
                    'area': 'Milestone Completion',
                    'value': analytics.milestone_completion_rate,
                    'average': averages.get('milestone_completion_rate', 0),
                    'difference': f"{(1 - (analytics.milestone_completion_rate / max(0.1, averages.get('milestone_completion_rate', 0)))) * 100:.0f}% lower",
                    'suggestion': 'Review milestone planning and execution strategy'
                })
            
            if analytics.mentor_engagement > averages.get('mentor_engagement', 0) * 1.2:
                advantages.append({
                    'area': 'Mentor Engagement',
                    'value': analytics.mentor_engagement,
                    'average': averages.get('mentor_engagement', 0),
                    'difference': f"{((analytics.mentor_engagement / max(0.1, averages.get('mentor_engagement', 0))) - 1) * 100:.0f}% higher",
                    'impact': 'Better guidance and access to expertise'
                })
            elif analytics.mentor_engagement < averages.get('mentor_engagement', 0) * 0.8:
                disadvantages.append({
                    'area': 'Mentor Engagement',
                    'value': analytics.mentor_engagement,
                    'average': averages.get('mentor_engagement', 0),
                    'difference': f"{(1 - (analytics.mentor_engagement / max(0.1, averages.get('mentor_engagement', 0)))) * 100:.0f}% lower",
                    'suggestion': 'Schedule more regular sessions with mentors'
                })
            
            comparative.competitive_advantages = {'key_advantages': advantages[:3]}
            comparative.competitive_disadvantages = {'key_disadvantages': disadvantages[:3]}
            
        except BusinessAnalytics.DoesNotExist:
            comparative.competitive_advantages = {'key_advantages': []}
            comparative.competitive_disadvantages = {'key_disadvantages': []}
        
        # Save updated comparative analytics
        comparative.save()
        
        return comparative
    
    def _create_analytics_snapshot(self, analytics, business_idea):
        """Create a snapshot of current analytics for historical tracking"""
        # Check if a snapshot already exists for today
        today = timezone.now().date()
        existing_snapshot = AnalyticsSnapshot.objects.filter(
            business_idea=business_idea,
            snapshot_date=today
        ).first()
        
        if existing_snapshot:
            # Update existing snapshot
            existing_snapshot.progress_rate = analytics.progress_rate
            existing_snapshot.milestone_completion_rate = analytics.milestone_completion_rate
            existing_snapshot.goal_achievement_rate = analytics.goal_achievement_rate
            existing_snapshot.team_size = analytics.team_size
            existing_snapshot.mentor_engagement = analytics.mentor_engagement
            existing_snapshot.industry_percentile = analytics.industry_percentile
            existing_snapshot.stage_percentile = analytics.stage_percentile
            
            # Get success probability from predictive analytics if available
            try:
                predictive = PredictiveAnalytics.objects.get(business_idea=business_idea)
                existing_snapshot.success_probability = predictive.success_probability
            except PredictiveAnalytics.DoesNotExist:
                existing_snapshot.success_probability = 50.0  # Default value
            
            existing_snapshot.save()
            return existing_snapshot
        else:
            # Create new snapshot
            snapshot = AnalyticsSnapshot(
                business_idea=business_idea,
                progress_rate=analytics.progress_rate,
                milestone_completion_rate=analytics.milestone_completion_rate,
                goal_achievement_rate=analytics.goal_achievement_rate,
                team_size=analytics.team_size,
                mentor_engagement=analytics.mentor_engagement,
                industry_percentile=analytics.industry_percentile,
                stage_percentile=analytics.stage_percentile
            )
            
            # Get success probability from predictive analytics if available
            try:
                predictive = PredictiveAnalytics.objects.get(business_idea=business_idea)
                snapshot.success_probability = predictive.success_probability
            except PredictiveAnalytics.DoesNotExist:
                snapshot.success_probability = 50.0  # Default value
            
            snapshot.save()
            return snapshot


class MetricDefinitionViewSet(viewsets.ModelViewSet):
    """ViewSet for metric definitions"""
    queryset = MetricDefinition.objects.all().order_by('category', 'display_order')
    serializer_class = MetricDefinitionSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['category']
    search_fields = ['display_name', 'description']
    
    def get_permissions(self):
        """Only admins can create, update or delete metric definitions"""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsAdminUser()]
        return [permissions.IsAuthenticated()]
