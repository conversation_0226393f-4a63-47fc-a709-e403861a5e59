import React, { useState, useEffect } from 'react';
import { 
  Heart, Cpu, HardDrive, Monitor, Network, Database, 
  CheckCircle, AlertTriangle, XCircle, RefreshCw, 
  Activity, Clock, TrendingUp, Server, Zap
} from 'lucide-react';
import { DashboardLayout } from '../dashboard';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';
import { getAuthToken } from '../../../services/api';

interface SystemHealthData {
  cpu: {
    usage_percent: number;
    count: number;
    status: 'healthy' | 'warning' | 'critical';
    temperature?: number;
    load_average?: number[];
  };
  memory: {
    total: number;
    available: number;
    percent: number;
    used: number;
    status: 'healthy' | 'warning' | 'critical';
    swap_total?: number;
    swap_used?: number;
  };
  disk: {
    total: number;
    used: number;
    free: number;
    percent: number;
    status: 'healthy' | 'warning' | 'critical';
    read_speed?: number;
    write_speed?: number;
  };
  network?: {
    bytes_sent: number;
    bytes_recv: number;
    packets_sent: number;
    packets_recv: number;
    errors_in?: number;
    errors_out?: number;
  };
  processes?: Array<{
    pid: number;
    name: string;
    cpu_percent: number;
    memory_percent: number;
    status: string;
  }>;
  overall_status: 'healthy' | 'warning' | 'critical';
  timestamp: string;
  uptime?: number;
  boot_time?: string;
}

const SystemHealthDetails: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  const [healthData, setHealthData] = useState<SystemHealthData | null>(null);
  const [loading, setLoading] = useState(true);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchHealthData = async () => {
    try {
      setLoading(true);
      
      const response = await fetch('/api/superadmin/system/system_health/', {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setHealthData(data);
        setLastUpdated(new Date());
      } else {
        console.error('Failed to fetch system health data:', response.status);
        setHealthData(null);
      }
    } catch (error) {
      console.error('Failed to fetch system health data:', error);
      setHealthData(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchHealthData();
  }, []);

  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      fetchHealthData();
    }, 15000); // Refresh every 15 seconds for health monitoring

    return () => clearInterval(interval);
  }, [autoRefresh]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="w-5 h-5 text-green-400" />;
      case 'warning': return <AlertTriangle className="w-5 h-5 text-yellow-400" />;
      case 'critical': return <XCircle className="w-5 h-5 text-red-400" />;
      default: return <Activity className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-400';
      case 'warning': return 'text-yellow-400';
      case 'critical': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getStatusBg = (status: string) => {
    switch (status) {
      case 'healthy': return 'bg-green-500/20 border-green-500/30';
      case 'warning': return 'bg-yellow-500/20 border-yellow-500/30';
      case 'critical': return 'bg-red-500/20 border-red-500/30';
      default: return 'bg-gray-500/20 border-gray-500/30';
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) return `${days}d ${hours}h ${minutes}m`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  return (
    <DashboardLayout currentPage="system-health">
      {/* Header */}
      <div className={`mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
        <div>
          <h1 className="text-2xl font-bold text-white flex items-center">
            <Heart className="w-8 h-8 mr-3 text-red-400" />
            {t('admin.system.health', 'System Health Details')}
          </h1>
          <div className="text-gray-400 mt-1">{t('admin.system.health.description', 'Comprehensive system health monitoring and diagnostics')}</div>
        </div>
        <div className={`mt-4 sm:mt-0 flex items-center space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
          <button
            onClick={fetchHealthData}
            disabled={loading}
            className="flex items-center space-x-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 rounded-lg text-sm font-medium transition-colors"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            <span>{t('system.refresh', 'Refresh')}</span>
          </button>
          <label className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="w-4 h-4 text-purple-600 bg-indigo-800 border-indigo-600 rounded focus:ring-purple-500"
            />
            <span className="text-gray-300 text-sm">{t('system.auto.refresh', 'Auto Refresh (15s)')}</span>
          </label>
          {lastUpdated && (
            <div className="text-xs text-gray-400">
              {t('system.last.updated', 'Last updated')}: {lastUpdated.toLocaleTimeString()}
            </div>
          )}
        </div>
      </div>

      {loading && !healthData ? (
        <div className="flex items-center justify-center py-12">
          <RefreshCw className="w-8 h-8 animate-spin text-blue-400" />
          <span className="ml-2 text-gray-400">Loading system health data...</span>
        </div>
      ) : healthData ? (
        <>
          {/* Overall System Status */}
          <div className={`mb-8 p-6 rounded-xl border-2 ${getStatusBg(healthData.overall_status)}`}>
            <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
              <div className="flex items-center space-x-4">
                {getStatusIcon(healthData.overall_status)}
                <div>
                  <h2 className="text-xl font-bold text-white">
                    {t('system.overall.status', 'Overall System Status')}: 
                    <span className={`ml-2 ${getStatusColor(healthData.overall_status)}`}>
                      {healthData.overall_status.toUpperCase()}
                    </span>
                  </h2>
                  <p className="text-gray-400">
                    {healthData.uptime && `${t('system.uptime', 'Uptime')}: ${formatUptime(healthData.uptime)}`}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm text-gray-400">{t('system.last.check', 'Last Check')}</div>
                <div className="text-white font-medium">
                  {new Date(healthData.timestamp).toLocaleString()}
                </div>
              </div>
            </div>
          </div>

          {/* System Components Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {/* CPU Health */}
            <div className={`p-6 rounded-xl border ${getStatusBg(healthData.cpu.status)} bg-indigo-900/30 backdrop-blur-sm`}>
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <Cpu className="w-6 h-6 text-blue-400" />
                  <h3 className="text-lg font-semibold text-white">{t('system.cpu', 'CPU')}</h3>
                </div>
                {getStatusIcon(healthData.cpu.status)}
              </div>
              
              <div className="space-y-3">
                <div>
                  <div className="text-2xl font-bold text-white">{healthData.cpu.usage_percent.toFixed(1)}%</div>
                  <div className="text-sm text-gray-400">{t('system.usage', 'Usage')}</div>
                </div>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-400">{t('system.cores', 'Cores')}: </span>
                    <span className="text-white">{healthData.cpu.count}</span>
                  </div>
                  {healthData.cpu.temperature && (
                    <div>
                      <span className="text-gray-400">{t('system.temp', 'Temp')}: </span>
                      <span className="text-white">{healthData.cpu.temperature}°C</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Memory Health */}
            <div className={`p-6 rounded-xl border ${getStatusBg(healthData.memory.status)} bg-indigo-900/30 backdrop-blur-sm`}>
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <Monitor className="w-6 h-6 text-green-400" />
                  <h3 className="text-lg font-semibold text-white">{t('system.memory', 'Memory')}</h3>
                </div>
                {getStatusIcon(healthData.memory.status)}
              </div>
              
              <div className="space-y-3">
                <div>
                  <div className="text-2xl font-bold text-white">{healthData.memory.percent.toFixed(1)}%</div>
                  <div className="text-sm text-gray-400">{t('system.used', 'Used')}</div>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">{t('system.total', 'Total')}:</span>
                    <span className="text-white">{formatBytes(healthData.memory.total)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">{t('system.available', 'Available')}:</span>
                    <span className="text-white">{formatBytes(healthData.memory.available)}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Disk Health */}
            <div className={`p-6 rounded-xl border ${getStatusBg(healthData.disk.status)} bg-indigo-900/30 backdrop-blur-sm`}>
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <HardDrive className="w-6 h-6 text-purple-400" />
                  <h3 className="text-lg font-semibold text-white">{t('system.disk', 'Disk')}</h3>
                </div>
                {getStatusIcon(healthData.disk.status)}
              </div>
              
              <div className="space-y-3">
                <div>
                  <div className="text-2xl font-bold text-white">{healthData.disk.percent.toFixed(1)}%</div>
                  <div className="text-sm text-gray-400">{t('system.used', 'Used')}</div>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">{t('system.total', 'Total')}:</span>
                    <span className="text-white">{formatBytes(healthData.disk.total)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">{t('system.free', 'Free')}:</span>
                    <span className="text-white">{formatBytes(healthData.disk.free)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Network Statistics */}
          {healthData.network && (
            <div className="bg-indigo-900/30 backdrop-blur-sm rounded-xl p-6 border border-indigo-800/50 mb-8">
              <h3 className="text-lg font-semibold text-white mb-6 flex items-center">
                <Network className="w-5 h-5 mr-2 text-blue-400" />
                {t('system.network.statistics', 'Network Statistics')}
              </h3>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                <div>
                  <div className="text-xl font-bold text-white">{formatBytes(healthData.network.bytes_sent)}</div>
                  <div className="text-sm text-gray-400">{t('system.bytes.sent', 'Bytes Sent')}</div>
                </div>
                <div>
                  <div className="text-xl font-bold text-white">{formatBytes(healthData.network.bytes_recv)}</div>
                  <div className="text-sm text-gray-400">{t('system.bytes.received', 'Bytes Received')}</div>
                </div>
                <div>
                  <div className="text-xl font-bold text-white">{healthData.network.packets_sent.toLocaleString()}</div>
                  <div className="text-sm text-gray-400">{t('system.packets.sent', 'Packets Sent')}</div>
                </div>
                <div>
                  <div className="text-xl font-bold text-white">{healthData.network.packets_recv.toLocaleString()}</div>
                  <div className="text-sm text-gray-400">{t('system.packets.received', 'Packets Received')}</div>
                </div>
              </div>
            </div>
          )}

          {/* Top Processes */}
          {healthData.processes && healthData.processes.length > 0 && (
            <div className="bg-indigo-900/30 backdrop-blur-sm rounded-xl p-6 border border-indigo-800/50">
              <h3 className="text-lg font-semibold text-white mb-6 flex items-center">
                <Activity className="w-5 h-5 mr-2 text-green-400" />
                {t('system.top.processes', 'Top Processes')}
              </h3>
              
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b border-gray-700">
                      <th className="text-left py-2 text-gray-400">{t('system.process.pid', 'PID')}</th>
                      <th className="text-left py-2 text-gray-400">{t('system.process.name', 'Name')}</th>
                      <th className="text-right py-2 text-gray-400">{t('system.process.cpu', 'CPU %')}</th>
                      <th className="text-right py-2 text-gray-400">{t('system.process.memory', 'Memory %')}</th>
                      <th className="text-center py-2 text-gray-400">{t('system.process.status', 'Status')}</th>
                    </tr>
                  </thead>
                  <tbody>
                    {healthData.processes.slice(0, 10).map((process) => (
                      <tr key={process.pid} className="border-b border-gray-800">
                        <td className="py-2 text-white">{process.pid}</td>
                        <td className="py-2 text-white font-mono text-xs">{process.name}</td>
                        <td className="py-2 text-right text-white">{process.cpu_percent.toFixed(1)}%</td>
                        <td className="py-2 text-right text-white">{process.memory_percent.toFixed(1)}%</td>
                        <td className="py-2 text-center">
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            process.status === 'running' ? 'bg-green-500/20 text-green-400' :
                            process.status === 'sleeping' ? 'bg-blue-500/20 text-blue-400' :
                            'bg-gray-500/20 text-gray-400'
                          }`}>
                            {process.status}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </>
      ) : (
        <div className="text-center py-12">
          <XCircle className="w-16 h-16 text-red-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-white mb-2">{t('system.health.unavailable', 'System Health Data Unavailable')}</h3>
          <p className="text-gray-400 mb-4">{t('system.health.error', 'Unable to fetch system health information')}</p>
          <button
            onClick={fetchHealthData}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-white font-medium transition-colors"
          >
            {t('system.retry', 'Retry')}
          </button>
        </div>
      )}
    </DashboardLayout>
  );
};

export default SystemHealthDetails;
