/**
 * Service worker registration utility
 */

// Check if the browser supports service workers
const isServiceWorkerSupported = 'serviceWorker' in navigator;

// Service worker configuration
const config = {
  swPath: '/service-worker.js',
  scope: '/',
  enableInDevelopment: false,
};

// Event callbacks
type ServiceWorkerCallbacks = {
  onSuccess?: (registration: ServiceWorkerRegistration) => void;
  onUpdate?: (registration: ServiceWorkerRegistration) => void;
  onError?: (error: Error) => void;
  onOffline?: () => void;
  onOnline?: () => void;
};

/**
 * Register the service worker
 */
export const register = (callbacks: ServiceWorkerCallbacks = {}) => {
  const { onSuccess, onUpdate, onError } = callbacks;
  
  // Only register in production or if explicitly enabled in development
  if (
    !isServiceWorkerSupported ||
    (process.env.NODE_ENV === 'development' && !config.enableInDevelopment)
  ) {
    return;
  }
  
  // Register when the window loads
  window.addEventListener('load', () => {
    const swUrl = config.swPath;
    
    registerServiceWorker(swUrl, { onSuccess, onUpdate, onError });
    
    // Set up online/offline event listeners
    setupOnlineOfflineListeners(callbacks);
  });
};

/**
 * Unregister the service worker
 */
export const unregister = async () => {
  if (!isServiceWorkerSupported) {
    return;
  }
  
  try {
    const registration = await navigator.serviceWorker.ready;
    await registration.unregister();
    console.log('Service worker unregistered successfully');
  } catch (error) {
    console.error('Error unregistering service worker:', error);
  }
};

/**
 * Check for service worker updates
 */
export const checkForUpdates = async () => {
  if (!isServiceWorkerSupported) {
    return false;
  }
  
  try {
    const registration = await navigator.serviceWorker.ready;
    await registration.update();
    return true;
  } catch (error) {
    console.error('Error checking for service worker updates:', error);
    return false;
  }
};

/**
 * Register the service worker with the given URL
 */
const registerServiceWorker = (
  swUrl: string,
  callbacks: Pick<ServiceWorkerCallbacks, 'onSuccess' | 'onUpdate' | 'onError'>
) => {
  navigator.serviceWorker
    .register(swUrl, { scope: config.scope })
    .then((registration) => {
      console.log('Service worker registered successfully');
      
      // Check for updates
      registration.onupdatefound = () => {
        const installingWorker = registration.installing;
        if (!installingWorker) return;
        
        installingWorker.onstatechange = () => {
          if (installingWorker.state === 'installed') {
            if (navigator.serviceWorker.controller) {
              // New content is available
              console.log('New content is available; please refresh');
              
              if (callbacks.onUpdate) {
                callbacks.onUpdate(registration);
              }
            } else {
              // Content is cached for offline use
              console.log('Content is cached for offline use');
              
              if (callbacks.onSuccess) {
                callbacks.onSuccess(registration);
              }
            }
          }
        };
      };
    })
    .catch((error) => {
      console.error('Error during service worker registration:', error);
      
      if (callbacks.onError) {
        callbacks.onError(error);
      }
    });
};

/**
 * Set up online/offline event listeners
 */
const setupOnlineOfflineListeners = (callbacks: ServiceWorkerCallbacks) => {
  const { onOffline, onOnline } = callbacks;
  
  // Handle offline events
  window.addEventListener('offline', () => {
    console.log('App is offline');
    
    if (onOffline) {
      onOffline();
    }
  });
  
  // Handle online events
  window.addEventListener('online', () => {
    console.log('App is back online');
    
    if (onOnline) {
      onOnline();
    }
    
    // Check for updates when coming back online
    checkForUpdates();
  });
};
