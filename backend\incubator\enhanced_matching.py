"""
Enhanced mentor matching algorithm with AI-powered recommendations.
"""
import os
import json
from django.db.models import Q, Count, Avg, F
from django.utils import timezone
from datetime import timedelta
import google.generativeai as genai
from dotenv import load_dotenv
from core.ai_config import get_gemini_config, generate_gemini_content

from .models import (
    BusinessIdea, MentorProfile, MentorExpertise, MentorshipMatch,
    MentorshipApplication, MentorshipSession, MentorshipFeedback
)
from .models_milestone import MentorRecommendation
from users.models import UserProfile

# Load environment variables
load_dotenv()

# Use centralized configuration
def get_configured_model():
    """Get configured Gemini model"""
    config = get_gemini_config()
    return config.model if config.is_available else None

class EnhancedMentorMatcher:
    """Enhanced mentor matching algorithm with AI-powered recommendations"""
    
    def __init__(self, business_idea_id):
        """Initialize with a business idea ID"""
        self.business_idea_id = business_idea_id
        self.business_idea = BusinessIdea.objects.get(id=business_idea_id)
        self.mentee = self.business_idea.owner
        self.mentee_profile = getattr(self.mentee, 'profile', None)
        
    def get_mentor_recommendations(self, limit=5):
        """
        Get mentor recommendations for a business idea
        
        Args:
            limit: Maximum number of recommendations to return
            
        Returns:
            list: List of MentorRecommendation objects
        """
        # Get all available mentors
        available_mentors = MentorProfile.objects.filter(
            is_accepting_mentees=True
        ).annotate(
            current_mentees=Count('mentorship_matches', filter=Q(mentorship_matches__status='active'))
        ).filter(
            current_mentees__lt=F('max_mentees')
        )
        
        # Get business idea details
        tags = self.business_idea.tags.all()
        industry = self.business_idea.industry
        current_stage = self.business_idea.current_stage
        problem_statement = self.business_idea.problem_statement
        solution_description = self.business_idea.solution_description
        target_audience = self.business_idea.target_audience
        
        # Combine all needs for text matching
        all_needs = f"{problem_statement} {solution_description} {target_audience}"
        
        # Get mentee preferences if available
        mentee_preferences = {}
        if hasattr(self.mentee, 'mentorship_applications'):
            # Get the most recent application
            recent_application = self.mentee.mentorship_applications.order_by('-created_at').first()
            if recent_application:
                mentee_preferences = {
                    'goals': recent_application.goals,
                    'specific_areas': recent_application.specific_areas,
                    'preferred_communication': recent_application.preferred_communication,
                    'preferred_expertise': recent_application.preferred_expertise
                }
        
        # Calculate match scores for each mentor
        recommendations = []
        
        for mentor in available_mentors:
            # Skip if already matched with this business idea
            if MentorshipMatch.objects.filter(
                mentor=mentor,
                business_idea=self.business_idea,
                status__in=['active', 'paused']
            ).exists():
                continue
                
            # Calculate base match score
            match_score = self._calculate_base_match_score(mentor, tags, all_needs, industry, current_stage)
            
            # Apply mentee preference boost
            match_score = self._apply_preference_boost(match_score, mentor, mentee_preferences)
            
            # Apply performance boost based on mentor ratings and success
            match_score = self._apply_performance_boost(match_score, mentor)
            
            # Apply personality compatibility boost if profiles exist
            match_score = self._apply_personality_compatibility(match_score, mentor)
            
            # Apply availability boost
            match_score = self._apply_availability_boost(match_score, mentor)
            
            # Cap score at 100
            match_score = min(match_score, 100)
            
            # Only include mentors with a minimum match score
            if match_score >= 40:  # Increased threshold for better matches
                # Create or update recommendation
                expertise_matches = self._get_expertise_matches(mentor, tags, all_needs)
                
                recommendation, created = MentorRecommendation.objects.update_or_create(
                    business_idea=self.business_idea,
                    mentor=mentor,
                    defaults={
                        'match_score': match_score,
                        'match_reason': self._generate_match_reason(mentor, match_score, expertise_matches),
                        'expertise_match': ", ".join(expertise_matches)
                    }
                )
                
                recommendations.append(recommendation)
        
        # Sort by match score and limit results
        recommendations.sort(key=lambda x: x.match_score, reverse=True)
        return recommendations[:limit]
    
    def _calculate_base_match_score(self, mentor, tags, all_needs, industry, current_stage):
        """Calculate base match score based on expertise match"""
        match_score = 0
        expertise_areas = mentor.expertise_areas.all()
        
        # Check expertise areas
        for expertise in expertise_areas:
            # Match based on tags
            for tag in tags:
                if tag.name.lower() in expertise.specific_expertise.lower():
                    match_score += 10
                
            # Match based on needs
            if all_needs and expertise.specific_expertise.lower() in all_needs.lower():
                match_score += 15
            
            # Match based on industry
            if industry and industry.lower() in expertise.specific_expertise.lower():
                match_score += 10
            
            # Match based on business stage
            if expertise.category == 'business_stage' and current_stage in expertise.specific_expertise.lower():
                match_score += 15
        
        return match_score
    
    def _apply_preference_boost(self, score, mentor, preferences):
        """Apply boost based on mentee preferences"""
        if not preferences:
            return score
            
        boost = 0
        
        # Check preferred expertise
        if preferences.get('preferred_expertise') and mentor.expertise_areas.filter(
            category=preferences.get('preferred_expertise')
        ).exists():
            boost += 15
            
        # Check communication preference
        mentor_user = mentor.user
        if hasattr(mentor_user, 'profile') and mentor_user.profile.preferred_communication == preferences.get('preferred_communication'):
            boost += 5
            
        return score + boost
    
    def _apply_performance_boost(self, score, mentor):
        """Apply boost based on mentor performance"""
        boost = 0
        
        # Get average rating
        avg_rating = MentorshipFeedback.objects.filter(
            session__mentorship_match__mentor=mentor,
            is_from_mentee=True
        ).aggregate(avg=Avg('rating'))['avg']
        
        if avg_rating:
            # Scale rating (1-5) to boost (0-15)
            rating_boost = (avg_rating - 1) * 3.75
            boost += rating_boost
            
        # Check successful completion rate
        total_matches = MentorshipMatch.objects.filter(mentor=mentor).count()
        completed_matches = MentorshipMatch.objects.filter(mentor=mentor, status='completed').count()
        
        if total_matches > 0:
            completion_rate = (completed_matches / total_matches) * 100
            # Scale completion rate (0-100) to boost (0-10)
            completion_boost = completion_rate * 0.1
            boost += completion_boost
            
        return score + boost
    
    def _apply_personality_compatibility(self, score, mentor):
        """Apply boost based on personality compatibility"""
        boost = 0
        
        # Check if both profiles exist
        mentor_profile = getattr(mentor.user, 'profile', None)
        
        if mentor_profile and self.mentee_profile:
            # Simple compatibility check based on learning style
            if mentor_profile.learning_style == self.mentee_profile.learning_style:
                boost += 5
                
            # Check communication style compatibility
            if mentor_profile.communication_style == self.mentee_profile.communication_style:
                boost += 5
                
        return score + boost
    
    def _apply_availability_boost(self, score, mentor):
        """Apply boost based on availability compatibility"""
        boost = 0
        
        # Check mentor availability level
        if mentor.availability == 'high':
            boost += 10
        elif mentor.availability == 'medium':
            boost += 5
            
        # Check recent session frequency
        recent_sessions = MentorshipSession.objects.filter(
            mentorship_match__mentor=mentor,
            scheduled_at__gte=timezone.now() - timedelta(days=30)
        ).count()
        
        # If mentor has fewer recent sessions, they're more available
        if recent_sessions < 5:
            boost += 5
            
        return score + boost
    
    def _get_expertise_matches(self, mentor, tags, all_needs):
        """Get list of expertise areas that match the business idea"""
        expertise_matches = []
        
        for expertise in mentor.expertise_areas.all():
            # Match based on tags
            for tag in tags:
                if tag.name.lower() in expertise.specific_expertise.lower() and expertise.specific_expertise not in expertise_matches:
                    expertise_matches.append(f"{expertise.get_category_display()}: {expertise.specific_expertise}")
            
            # Match based on needs
            if all_needs and expertise.specific_expertise.lower() in all_needs.lower() and expertise.specific_expertise not in expertise_matches:
                expertise_matches.append(f"{expertise.get_category_display()}: {expertise.specific_expertise}")
                
        return expertise_matches
    
    def _generate_match_reason(self, mentor, match_score, expertise_matches):
        """Generate a human-readable match reason"""
        reason = f"Based on expertise match and availability. Score: {match_score}/100. "
        
        if expertise_matches:
            reason += f"Matching expertise areas: {', '.join(expertise_matches[:3])}"
            if len(expertise_matches) > 3:
                reason += f" and {len(expertise_matches) - 3} more"
                
        return reason
    
    def generate_ai_match_explanation(self, mentor_id):
        """
        Generate an AI-powered explanation of why this mentor is a good match
        
        Args:
            mentor_id: ID of the mentor
            
        Returns:
            str: Detailed explanation of the match
        """
        try:
            mentor = MentorProfile.objects.get(id=mentor_id)
            
            # Get mentor details
            mentor_expertise = mentor.expertise_areas.all()
            expertise_areas = [f"{exp.get_category_display()}: {exp.specific_expertise}" for exp in mentor_expertise]
            
            # Get business idea details
            business_idea = self.business_idea
            
            # Create prompt for Gemini
            prompt = f"""
            I need to explain why a mentor is a good match for a specific business idea. Here are the details:
            
            Business Idea:
            - Title: {business_idea.title}
            - Description: {business_idea.description}
            - Problem Statement: {business_idea.problem_statement}
            - Solution: {business_idea.solution_description}
            - Target Audience: {business_idea.target_audience}
            - Current Stage: {business_idea.current_stage}
            
            Mentor:
            - Name: {mentor.user.get_full_name() or mentor.user.username}
            - Bio: {mentor.bio}
            - Company: {mentor.company or 'Not specified'}
            - Position: {mentor.position or 'Not specified'}
            - Years of Experience: {mentor.years_of_experience}
            - Expertise Areas: {', '.join(expertise_areas)}
            
            Please provide a detailed, personalized explanation (about 150 words) of why this mentor would be a good match for this business idea. Focus on how their specific expertise aligns with the business needs, and how their experience could help the business grow.
            """
            
            # Generate explanation using Gemini
            response = model.generate_content(prompt)
            explanation = response.text.strip()
            
            return explanation
            
        except Exception as e:
            return f"Could not generate AI explanation: {str(e)}"
