import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Server, Database, Shield, Settings, AlertTriangle,
  CheckCircle, XCircle, RefreshCw, Download, Upload,
  Terminal, Code, Globe, Lock, Key, Monitor,
  HardDrive, Cpu, Network, Activity, Zap,
  FileText, Archive, Clock, Users, Eye
} from 'lucide-react';
import DashboardLayout from '../dashboard/DashboardLayout';

interface SystemOperation {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  category: 'database' | 'server' | 'security' | 'backup' | 'monitoring';
  status: 'available' | 'running' | 'disabled';
  lastRun?: string;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
}

interface SystemConfig {
  debug_mode: boolean;
  maintenance_mode: boolean;
  registration_enabled: boolean;
  api_rate_limit: number;
  max_file_size: number;
  session_timeout: number;
  backup_frequency: string;
  ssl_enabled: boolean;
}

const SystemManagement: React.FC = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<string>('overview');
  const [systemConfig, setSystemConfig] = useState<SystemConfig | null>(null);
  const [operations, setOperations] = useState<SystemOperation[]>([]);
  const [loading, setLoading] = useState(false);
  const [confirmDialog, setConfirmDialog] = useState<{
    isOpen: boolean;
    operation: SystemOperation | null;
  }>({ isOpen: false, operation: null });

  useEffect(() => {
    initializeSystemOperations();
    fetchSystemConfig();
  }, []);

  const initializeSystemOperations = () => {
    const systemOperations: SystemOperation[] = [
      // Database Operations
      {
        id: 'db_backup',
        name: 'Database Backup',
        description: 'Create a full database backup',
        icon: <Database className="w-5 h-5" />,
        category: 'database',
        status: 'available',
        riskLevel: 'low'
      },
      {
        id: 'db_restore',
        name: 'Database Restore',
        description: 'Restore database from backup',
        icon: <Upload className="w-5 h-5" />,
        category: 'database',
        status: 'available',
        riskLevel: 'critical'
      },
      {
        id: 'db_optimize',
        name: 'Database Optimization',
        description: 'Optimize database performance',
        icon: <Zap className="w-5 h-5" />,
        category: 'database',
        status: 'available',
        riskLevel: 'medium'
      },
      {
        id: 'db_migrate',
        name: 'Run Migrations',
        description: 'Apply pending database migrations',
        icon: <RefreshCw className="w-5 h-5" />,
        category: 'database',
        status: 'available',
        riskLevel: 'high'
      },

      // Server Operations
      {
        id: 'server_restart',
        name: 'Server Restart',
        description: 'Restart the application server',
        icon: <Server className="w-5 h-5" />,
        category: 'server',
        status: 'available',
        riskLevel: 'critical'
      },
      {
        id: 'cache_clear',
        name: 'Clear Cache',
        description: 'Clear all application caches',
        icon: <RefreshCw className="w-5 h-5" />,
        category: 'server',
        status: 'available',
        riskLevel: 'low'
      },
      {
        id: 'logs_archive',
        name: 'Archive Logs',
        description: 'Archive old system logs',
        icon: <Archive className="w-5 h-5" />,
        category: 'server',
        status: 'available',
        riskLevel: 'low'
      },

      // Security Operations
      {
        id: 'security_scan',
        name: 'Security Scan',
        description: 'Run comprehensive security scan',
        icon: <Shield className="w-5 h-5" />,
        category: 'security',
        status: 'available',
        riskLevel: 'low'
      },
      {
        id: 'ssl_renew',
        name: 'Renew SSL Certificate',
        description: 'Renew SSL/TLS certificates',
        icon: <Lock className="w-5 h-5" />,
        category: 'security',
        status: 'available',
        riskLevel: 'medium'
      },
      {
        id: 'audit_export',
        name: 'Export Audit Logs',
        description: 'Export security audit logs',
        icon: <FileText className="w-5 h-5" />,
        category: 'security',
        status: 'available',
        riskLevel: 'low'
      },

      // Backup Operations
      {
        id: 'full_backup',
        name: 'Full System Backup',
        description: 'Complete system and data backup',
        icon: <Archive className="w-5 h-5" />,
        category: 'backup',
        status: 'available',
        riskLevel: 'low'
      },
      {
        id: 'config_backup',
        name: 'Configuration Backup',
        description: 'Backup system configuration',
        icon: <Settings className="w-5 h-5" />,
        category: 'backup',
        status: 'available',
        riskLevel: 'low'
      },

      // Monitoring Operations
      {
        id: 'health_check',
        name: 'System Health Check',
        description: 'Comprehensive system health analysis',
        icon: <Activity className="w-5 h-5" />,
        category: 'monitoring',
        status: 'available',
        riskLevel: 'low'
      },
      {
        id: 'performance_report',
        name: 'Performance Report',
        description: 'Generate detailed performance report',
        icon: <Monitor className="w-5 h-5" />,
        category: 'monitoring',
        status: 'available',
        riskLevel: 'low'
      }
    ];

    setOperations(systemOperations);
  };

  const fetchSystemConfig = async () => {
    try {
      const response = await fetch('/api/superadmin/system/configuration/', {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setSystemConfig(data.configuration || {});
      } else {
        console.error('Failed to fetch system configuration:', response.status);
        setSystemConfig({});
      }
    } catch (error) {
      console.error('Failed to fetch system configuration:', error);
      setSystemConfig({});
    }
  };

  const executeOperation = async (operation: SystemOperation) => {
    setLoading(true);
    try {
      // Execute real system operation via API
      const response = await fetch(`/api/superadmin/system/operations/${operation.id}/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();

        // Update operation status based on API response
        setOperations(prev => prev.map(op =>
          op.id === operation.id
            ? {
                ...op,
                status: result.status || 'available',
                lastRun: new Date().toISOString()
              }
            : op
        ));

        console.log(`Successfully executed operation: ${operation.name}`, result);
      } else {
        throw new Error(`Operation failed with status: ${response.status}`);
      }
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error(`Failed to execute operation: ${operation.name}`, error);
      }

      // Update operation status to show error
      setOperations(prev => prev.map(op =>
        op.id === operation.id
          ? { ...op, status: 'disabled' }
          : op
      ));
    } finally {
      setLoading(false);
      setConfirmDialog({ isOpen: false, operation: null });
    }
  };

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return 'text-green-400';
      case 'medium': return 'text-yellow-400';
      case 'high': return 'text-orange-400';
      case 'critical': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getRiskBadge = (riskLevel: string) => {
    const colors = {
      low: 'bg-green-900/20 text-green-300 border-green-500/20',
      medium: 'bg-yellow-900/20 text-yellow-300 border-yellow-500/20',
      high: 'bg-orange-900/20 text-orange-300 border-orange-500/20',
      critical: 'bg-red-900/20 text-red-300 border-red-500/20'
    };
    
    return (
      <span className={`px-2 py-1 text-xs rounded border ${colors[riskLevel as keyof typeof colors]}`}>
        {riskLevel.toUpperCase()}
      </span>
    );
  };

  const tabs = [
    { id: 'overview', name: 'System Overview', icon: <Monitor className="w-4 h-4" /> },
    { id: 'database', name: 'Database', icon: <Database className="w-4 h-4" /> },
    { id: 'server', name: 'Server', icon: <Server className="w-4 h-4" /> },
    { id: 'security', name: 'Security', icon: <Shield className="w-4 h-4" /> },
    { id: 'backup', name: 'Backup', icon: <Archive className="w-4 h-4" /> },
    { id: 'monitoring', name: 'Monitoring', icon: <Activity className="w-4 h-4" /> }
  ];

  const filteredOperations = activeTab === 'overview' 
    ? operations 
    : operations.filter(op => op.category === activeTab);

  return (
    <DashboardLayout currentPage="system-management">
      <div className="text-white">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-2">
            <Settings className="w-8 h-8 text-blue-400" />
            <h1 className="text-3xl font-bold text-white">
              {t('superAdmin.systemManagement.title', 'System Management')}
            </h1>
          </div>
          <p className="text-gray-400">
            {t('superAdmin.systemManagement.subtitle', 'Advanced system operations and configuration')}
          </p>
        </div>

      {/* Tabs */}
      <div className="flex flex-wrap gap-2 mb-8 border-b border-gray-700">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex items-center gap-2 px-4 py-2 rounded-t-lg transition-colors ${
              activeTab === tab.id
                ? 'bg-blue-600 text-white border-b-2 border-blue-400'
                : 'text-gray-400 hover:text-white hover:bg-gray-800'
            }`}
          >
            {tab.icon}
            {tab.name}
          </button>
        ))}
      </div>

      {/* System Configuration Overview */}
      {activeTab === 'overview' && systemConfig && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-400">Debug Mode</span>
              <div className={`flex items-center gap-1 ${systemConfig.debug_mode ? 'text-yellow-400' : 'text-green-400'}`}>
                {systemConfig.debug_mode ? <AlertTriangle className="w-4 h-4" /> : <CheckCircle className="w-4 h-4" />}
                <span className="text-sm">{systemConfig.debug_mode ? 'ON' : 'OFF'}</span>
              </div>
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-400">Maintenance</span>
              <div className={`flex items-center gap-1 ${systemConfig.maintenance_mode ? 'text-red-400' : 'text-green-400'}`}>
                {systemConfig.maintenance_mode ? <XCircle className="w-4 h-4" /> : <CheckCircle className="w-4 h-4" />}
                <span className="text-sm">{systemConfig.maintenance_mode ? 'ON' : 'OFF'}</span>
              </div>
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-400">SSL Status</span>
              <div className={`flex items-center gap-1 ${systemConfig.ssl_enabled ? 'text-green-400' : 'text-red-400'}`}>
                <Lock className="w-4 h-4" />
                <span className="text-sm">{systemConfig.ssl_enabled ? 'ENABLED' : 'DISABLED'}</span>
              </div>
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-400">Registration</span>
              <div className={`flex items-center gap-1 ${systemConfig.registration_enabled ? 'text-green-400' : 'text-red-400'}`}>
                <Users className="w-4 h-4" />
                <span className="text-sm">{systemConfig.registration_enabled ? 'OPEN' : 'CLOSED'}</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Operations Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredOperations.map((operation) => (
          <div key={operation.id} className="bg-gray-800 rounded-lg p-6 border border-gray-700 hover:border-gray-600 transition-colors">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gray-700 rounded-lg">
                  {operation.icon}
                </div>
                <div>
                  <h3 className="font-semibold text-white">{operation.name}</h3>
                  <p className="text-sm text-gray-400">{operation.description}</p>
                </div>
              </div>
              {getRiskBadge(operation.riskLevel)}
            </div>

            {operation.lastRun && (
              <div className="flex items-center gap-2 text-xs text-gray-500 mb-4">
                <Clock className="w-3 h-3" />
                Last run: {new Date(operation.lastRun).toLocaleString()}
              </div>
            )}

            <button
              onClick={() => setConfirmDialog({ isOpen: true, operation })}
              disabled={operation.status === 'running' || loading}
              className={`w-full py-2 px-4 rounded-lg font-medium transition-colors ${
                operation.status === 'running' || loading
                  ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                  : operation.riskLevel === 'critical'
                  ? 'bg-red-600 hover:bg-red-700 text-white'
                  : operation.riskLevel === 'high'
                  ? 'bg-orange-600 hover:bg-orange-700 text-white'
                  : 'bg-blue-600 hover:bg-blue-700 text-white'
              }`}
            >
              {operation.status === 'running' ? (
                <div className="flex items-center justify-center gap-2">
                  <RefreshCw className="w-4 h-4 animate-spin" />
                  Running...
                </div>
              ) : (
                'Execute'
              )}
            </button>
          </div>
        ))}
      </div>

      {/* Confirmation Dialog */}
      {confirmDialog.isOpen && confirmDialog.operation && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4 border border-gray-700">
            <div className="flex items-center gap-3 mb-4">
              <AlertTriangle className={`w-6 h-6 ${getRiskColor(confirmDialog.operation.riskLevel)}`} />
              <h3 className="text-lg font-semibold">Confirm Operation</h3>
            </div>
            
            <p className="text-gray-300 mb-4">
              Are you sure you want to execute <strong>{confirmDialog.operation.name}</strong>?
            </p>
            
            <div className="mb-4">
              <div className="flex items-center gap-2 mb-2">
                <span className="text-sm text-gray-400">Risk Level:</span>
                {getRiskBadge(confirmDialog.operation.riskLevel)}
              </div>
              <p className="text-sm text-gray-400">{confirmDialog.operation.description}</p>
            </div>

            <div className="flex gap-3">
              <button
                onClick={() => setConfirmDialog({ isOpen: false, operation: null })}
                className="flex-1 py-2 px-4 bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => executeOperation(confirmDialog.operation!)}
                disabled={loading}
                className={`flex-1 py-2 px-4 rounded-lg transition-colors ${
                  confirmDialog.operation.riskLevel === 'critical'
                    ? 'bg-red-600 hover:bg-red-700'
                    : 'bg-blue-600 hover:bg-blue-700'
                } disabled:opacity-50`}
              >
                {loading ? 'Executing...' : 'Execute'}
              </button>
            </div>
          </div>
        </div>
      )}
      </div>
    </DashboardLayout>
  );
};

export default SystemManagement;
