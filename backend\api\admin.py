from django.contrib import admin
from .models import Event, Resource, Post, Comment, MembershipApplication

class CommentInline(admin.TabularInline):
    model = Comment
    extra = 0

@admin.register(Event)
class EventAdmin(admin.ModelAdmin):
    list_display = ('title', 'date', 'location', 'is_virtual', 'organizer')
    list_filter = ('is_virtual', 'date')
    search_fields = ('title', 'description', 'location')
    date_hierarchy = 'date'

@admin.register(Resource)
class ResourceAdmin(admin.ModelAdmin):
    list_display = ('title', 'resource_type', 'author', 'created_at')
    list_filter = ('resource_type', 'created_at')
    search_fields = ('title', 'description')

@admin.register(Post)
class PostAdmin(admin.ModelAdmin):
    list_display = ('title', 'author', 'created_at', 'like_count')
    list_filter = ('created_at',)
    search_fields = ('title', 'content')
    inlines = [CommentInline]

@admin.register(Comment)
class CommentAdmin(admin.ModelAdmin):
    list_display = ('post', 'author', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('content',)

@admin.register(MembershipApplication)
class MembershipApplicationAdmin(admin.ModelAdmin):
    list_display = ('full_name', 'email', 'expertise_level', 'status', 'created_at')
    list_filter = ('status', 'expertise_level', 'created_at')
    search_fields = ('full_name', 'email', 'expertise_areas', 'background', 'motivation')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('Applicant Information', {
            'fields': ('user', 'full_name', 'email', 'phone', 'location')
        }),
        ('Expertise & Background', {
            'fields': ('expertise_areas', 'expertise_level', 'background', 'motivation')
        }),
        ('Profiles', {
            'fields': ('linkedin_profile', 'github_profile', 'portfolio_url')
        }),
        ('Review Status', {
            'fields': ('status', 'reviewed_by', 'reviewed_at', 'review_notes')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )
