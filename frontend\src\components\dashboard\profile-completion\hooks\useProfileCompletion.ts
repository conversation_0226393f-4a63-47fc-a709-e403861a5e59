import { useState, useEffect } from 'react';
import { useAppSelector } from '../../../../store/hooks';
import { authAPI, UserProfile } from '../../../../services/api';
import { useTranslation } from 'react-i18next';

interface ProfileChecklistItem {
  field: string;
  completed: boolean;
  importance: 'high' | 'medium' | 'low';
}

interface CompletionStatus {
  text: string;
  color: string;
  bgColor: string;
}

interface UseProfileCompletionResult {
  profile: UserProfile | null;
  loading: boolean;
  error: string | null;
  checklist: ProfileChecklistItem[];
  completedItems: number;
  completionPercentage: number;
  missingHighImportanceItems: string[];
  completionStatus: CompletionStatus;
}

export const useProfileCompletion = (): UseProfileCompletionResult => {
  const { user } = useAppSelector((state) => state.auth);
  const { t } = useAppSelector(state => state.language);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProfile = async () => {
      if (!user) return;

      setLoading(true);
      try {
        const profileData = await authAPI.getProfile();
        setProfile(profileData);
        setError(null);
      } catch (err) {
        console.error('Error fetching user profile:', err);
        setError('Failed to load profile data');
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, [user]);

  // Generate completion checklist
  const getCompletionChecklist = (): ProfileChecklistItem[] => {
    if (!user || !profile) return [];

    return [
      {
        field: 'Profile Picture',
        completed: !!profile.profile_image,
        importance: 'high',
      },
      {
        field: 'Bio',
        completed: !!profile.bio,
        importance: 'high',
      },
      {
        field: 'Location',
        completed: !!profile.location,
        importance: 'medium',
      },
      {
        field: 'Expertise',
        completed: !!profile.expertise,
        importance: 'high',
      },
      {
        field: 'First Name',
        completed: !!user.first_name,
        importance: 'medium',
      },
      {
        field: 'Last Name',
        completed: !!user.last_name,
        importance: 'medium',
      },
      {
        field: 'LinkedIn',
        completed: !!profile.linkedin,
        importance: 'medium',
      },
      {
        field: 'GitHub',
        completed: !!profile.github,
        importance: 'low',
      },
      {
        field: 'Website',
        completed: !!profile.website,
        importance: 'low',
      },
      {
        field: 'Twitter',
        completed: !!profile.twitter,
        importance: 'low',
      },
    ];
  };

  const checklist = getCompletionChecklist();
  const completedItems = checklist.filter(item => item.completed).length;
  const completionPercentage = profile?.completion_percentage ||
    (checklist.length > 0 ? Math.round((completedItems / checklist.length) * 100) : 0);

  // Get missing high importance items
  const missingHighImportanceItems = checklist
    .filter(item => !item.completed && item.importance === 'high')
    .map(item => item.field);

  // Get completion status text and color
  const getCompletionStatus = (): CompletionStatus => {
    if (completionPercentage >= 80) {
      return {
        text: 'complete',
        color: 'text-green-400',
        bgColor: 'bg-green-500',
      };
    } else if (completionPercentage >= 50) {
      return {
        text: 'almostthere',
        color: 'text-yellow-400',
        bgColor: 'bg-yellow-500',
      };
    } else if (completionPercentage >= 20) {
      return {
        text: 'makingprogress',
        color: 'text-orange-400',
        bgColor: 'bg-orange-500',
      };
    } else {
      return {
        text: 'juststarted',
        color: 'text-red-400',
        bgColor: 'bg-red-500',
      };
    }
  };

  const completionStatus = getCompletionStatus();

  return {
    profile,
    loading,
    error,
    checklist,
    completedItems,
    completionPercentage,
    missingHighImportanceItems,
    completionStatus
  };
};

export default useProfileCompletion;
