import React, { useState, useEffect } from 'react';
import { Server, Database, HardDrive, Cpu, Monitor, Network, AlertTriangle, CheckCircle, Activity, Zap, RefreshCw } from 'lucide-react';
import { DashboardLayout } from '../dashboard';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';
import { getAuthToken } from '../../../services/api';

interface SystemMetric {
  id: string;
  name: string;
  value: number;
  unit: string;
  status: 'healthy' | 'warning' | 'critical';
  threshold: { warning: number; critical: number };
  icon: React.ReactNode;
  color: string;
  history: number[];
}

interface ServiceStatus {
  name: string;
  status: 'online' | 'offline' | 'degraded';
  uptime: number;
  responseTime: number;
  lastCheck: string;
  endpoint: string;
  version?: string;
}

interface SystemAlert {
  id: string;
  type: 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: string;
  resolved: boolean;
  service: string;
}

const SystemMonitoring: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  // State management
  const [metrics, setMetrics] = useState<SystemMetric[]>([]);
  const [services, setServices] = useState<ServiceStatus[]>([]);
  const [alerts, setAlerts] = useState<SystemAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [authError, setAuthError] = useState<string | null>(null);

  // Fetch real system monitoring data
  const fetchSystemData = async () => {
    try {
      setLoading(true);

      // Check if user is authenticated
      const token = getAuthToken();
      if (!token) {
        console.error('No authentication token available');
        setAuthError('Please log in to view system monitoring data');
        setMetrics([]);
        setServices([]);
        setAlerts([]);
        setLoading(false);
        return;
      }

      // Clear any previous auth errors
      setAuthError(null);

      // Fetch real monitoring data from backend
      const response = await fetch('/api/superadmin/system/monitoring/', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();

        // Transform backend data to frontend format
        const transformedMetrics: SystemMetric[] = [
          {
            id: 'cpu',
            name: 'CPU Usage',
            value: data.system_metrics?.cpu_usage || 0,
            unit: '%',
            status: data.system_metrics?.cpu_usage > 90 ? 'critical' :
                   data.system_metrics?.cpu_usage > 70 ? 'warning' : 'healthy',
            threshold: { warning: 70, critical: 90 },
            icon: <Cpu size={20} />,
            color: data.system_metrics?.cpu_usage > 90 ? 'text-red-400' :
                   data.system_metrics?.cpu_usage > 70 ? 'text-yellow-400' : 'text-blue-400',
            history: data.historical_metrics?.cpu || [data.system_metrics?.cpu_usage || 0]
          },
          {
            id: 'memory',
            name: 'Memory Usage',
            value: data.system_metrics?.memory_usage || 0,
            unit: '%',
            status: data.system_metrics?.memory_usage > 90 ? 'critical' :
                   data.system_metrics?.memory_usage > 70 ? 'warning' : 'healthy',
            threshold: { warning: 70, critical: 90 },
            icon: <Monitor size={20} />,
            color: data.system_metrics?.memory_usage > 90 ? 'text-red-400' :
                   data.system_metrics?.memory_usage > 70 ? 'text-yellow-400' : 'text-green-400',
            history: data.historical_metrics?.memory || [data.system_metrics?.memory_usage || 0]
          },
          {
            id: 'disk',
            name: 'Disk Usage',
            value: data.system_metrics?.disk_usage || 0,
            unit: '%',
            status: data.system_metrics?.disk_usage > 95 ? 'critical' :
                   data.system_metrics?.disk_usage > 80 ? 'warning' : 'healthy',
            threshold: { warning: 80, critical: 95 },
            icon: <HardDrive size={20} />,
            color: data.system_metrics?.disk_usage > 95 ? 'text-red-400' :
                   data.system_metrics?.disk_usage > 80 ? 'text-yellow-400' : 'text-green-400',
            history: data.historical_metrics?.disk || [data.system_metrics?.disk_usage || 0]
          },
          {
            id: 'network',
            name: 'Network I/O',
            value: data.system_metrics?.network_io || 0,
            unit: 'MB/s',
            status: data.system_metrics?.network_io > 800 ? 'critical' :
                   data.system_metrics?.network_io > 500 ? 'warning' : 'healthy',
            threshold: { warning: 500, critical: 800 },
            icon: <Network size={20} />,
            color: data.system_metrics?.network_io > 800 ? 'text-red-400' :
                   data.system_metrics?.network_io > 500 ? 'text-yellow-400' : 'text-purple-400',
            history: data.historical_metrics?.network || [data.system_metrics?.network_io || 0]
          }
        ];

        setMetrics(transformedMetrics);
        setServices(data.services || []);
        setAlerts(data.recent_alerts || []);
      } else if (response.status === 401) {
        console.error('Authentication failed - user needs to log in');
        setAuthError('Authentication failed. Please log in again.');
        setMetrics([]);
        setServices([]);
        setAlerts([]);
      } else if (response.status === 403) {
        console.error('Permission denied - user needs super admin access');
        setAuthError('Access denied. Super admin privileges required.');
        setMetrics([]);
        setServices([]);
        setAlerts([]);
      } else {
        console.error('Failed to fetch monitoring data:', response.status);
        // Fallback to empty data instead of mock data
        setMetrics([]);
        setServices([]);
        setAlerts([]);
      }
    } catch (error) {
      console.error('Failed to fetch monitoring data:', error);
      // Fallback to empty data instead of mock data
      setMetrics([]);
      setServices([]);
      setAlerts([]);
    } finally {
      setLoading(false);
    }
  };

  // Initialize real system data
  useEffect(() => {
    fetchSystemData();

  }, []);

  // Auto-refresh real data
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      fetchSystemData();
    }, 30000); // Refresh every 30 seconds with real data

    return () => clearInterval(interval);
  }, [autoRefresh]);

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'online':
        return 'text-green-400';
      case 'warning':
      case 'degraded':
        return 'text-yellow-400';
      case 'critical':
      case 'offline':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };

  // Get status background
  const getStatusBg = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'online':
        return 'bg-green-500/20';
      case 'warning':
      case 'degraded':
        return 'bg-yellow-500/20';
      case 'critical':
      case 'offline':
        return 'bg-red-500/20';
      default:
        return 'bg-gray-500/20';
    }
  };

  // Get alert icon
  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'error':
        return <AlertTriangle size={16} className="text-red-400" />;
      case 'warning':
        return <AlertTriangle size={16} className="text-yellow-400" />;
      case 'info':
        return <CheckCircle size={16} className="text-blue-400" />;
      default:
        return <Activity size={16} className="text-gray-400" />;
    }
  };

  // Format uptime
  const formatUptime = (uptime: number) => {
    return `${uptime.toFixed(2)}%`;
  };

  // Get relative time
  const getRelativeTime = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffMs = now.getTime() - time.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 1) return t('system.just.now', 'Just now');
    if (diffMins < 60) return t('system.minutes.ago', '{{count}}m ago', { count: diffMins });
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return t('system.hours.ago', '{{count}}h ago', { count: diffHours });
    return time.toLocaleDateString();
  };

  return (
    <DashboardLayout currentPage="system">
      {/* Header */}
      <div className={`mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
        <div>
          <h1 className="text-2xl font-bold text-white">{t('admin.system.monitoring', 'System Monitoring')}</h1>
          <div className="text-gray-400 mt-1">{t('admin.system.monitoring.description', 'Real-time system health and performance monitoring')}</div>
        </div>
        <div className={`mt-4 sm:mt-0 flex items-center space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
          <button
            onClick={fetchSystemData}
            disabled={loading}
            className="flex items-center space-x-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 rounded-lg text-sm font-medium transition-colors"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            <span>{t('system.refresh', 'Refresh')}</span>
          </button>
          <label className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="w-4 h-4 text-purple-600 bg-indigo-800 border-indigo-600 rounded focus:ring-purple-500"
            />
            <span className="text-gray-300 text-sm">{t('system.auto.refresh', 'Auto Refresh (30s)')}</span>
          </label>
          <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
            <div className={`w-2 h-2 rounded-full ${loading ? 'bg-yellow-400 animate-pulse' : 'bg-green-400'}`}></div>
            <span className="text-sm text-gray-400">{loading ? t('system.loading', 'Loading...') : t('system.live', 'Live')}</span>
          </div>
        </div>
      </div>

      {/* Authentication Error */}
      {authError ? (
        <div className="text-center py-12">
          <AlertTriangle className="w-16 h-16 text-yellow-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-white mb-2">{t('system.auth.error', 'Authentication Required')}</h3>
          <p className="text-gray-400 mb-4">{authError}</p>
          <button
            onClick={() => window.location.href = '/login'}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-white font-medium transition-colors"
          >
            {t('system.login', 'Go to Login')}
          </button>
        </div>
      ) : loading && !metrics.length ? (
        <div className="flex items-center justify-center py-12">
          <RefreshCw className="w-8 h-8 animate-spin text-blue-400" />
          <span className="ml-2 text-gray-400">Loading system data...</span>
        </div>
      ) : (
        <>
          {/* System Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {metrics.map((metric) => (
          <div key={metric.id} className="bg-indigo-900/30 backdrop-blur-sm rounded-xl p-6 border border-indigo-800/50 hover:border-purple-500/50 transition-all duration-300">
            <div className={`flex items-center justify-between mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
              <div className={metric.color}>
                {metric.icon}
              </div>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusBg(metric.status)} ${getStatusColor(metric.status)}`}>
                {metric.status}
              </span>
            </div>
            <div className="mb-4">
              <h3 className="text-2xl font-bold text-white mb-1">
                {metric.value.toFixed(1)}{metric.unit}
              </h3>
              <p className="text-gray-400 text-sm">{metric.name}</p>
            </div>
            {/* Mini Chart */}
            <div className="h-8 flex items-end space-x-1">
              {metric.history.map((value, index) => (
                <div
                  key={index}
                  className={`flex-1 rounded-t ${
                    value >= metric.threshold.critical ? 'bg-red-500' :
                    value >= metric.threshold.warning ? 'bg-yellow-500' : 'bg-green-500'
                  } opacity-70`}
                  style={{ height: `${(value / 100) * 100}%` }}
                ></div>
              ))}
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Services Status */}
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-xl p-6 border border-indigo-800/50">
          <h3 className="text-lg font-semibold text-white mb-6">{t('system.services.status', 'Services Status')}</h3>
          
          <div className="space-y-4">
            {services.map((service, index) => (
              <div key={index} className="p-4 bg-indigo-800/30 rounded-lg">
                <div className={`flex items-center justify-between mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                    <div className={`w-3 h-3 rounded-full ${
                      service.status === 'online' ? 'bg-green-400' :
                      service.status === 'degraded' ? 'bg-yellow-400' : 'bg-red-400'
                    }`}></div>
                    <span className="text-white font-medium">{service.name}</span>
                    {service.version && (
                      <span className="text-gray-400 text-xs">v{service.version}</span>
                    )}
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusBg(service.status)} ${getStatusColor(service.status)}`}>
                    {service.status}
                  </span>
                </div>
                
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-gray-400">{t('system.uptime', 'Uptime')}: </span>
                    <span className="text-white">{formatUptime(service.uptime)}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">{t('system.response', 'Response')}: </span>
                    <span className="text-white">{Math.round(service.responseTime)}ms</span>
                  </div>
                  <div>
                    <span className="text-gray-400">{t('system.checked', 'Checked')}: </span>
                    <span className="text-white">{getRelativeTime(service.lastCheck)}</span>
                  </div>
                </div>
                
                <div className="mt-2">
                  <span className="text-gray-400 text-xs">{service.endpoint}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* System Alerts */}
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-xl p-6 border border-indigo-800/50">
          <div className={`flex items-center justify-between mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
            <h3 className="text-lg font-semibold text-white">{t('system.alerts', 'System Alerts')}</h3>
            <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
              <Zap size={16} className="text-yellow-400" />
              <span className="text-sm text-gray-400">
                {alerts.filter(a => !a.resolved).length} {t('system.active', 'active')}
              </span>
            </div>
          </div>
          
          <div className="space-y-3 max-h-80 overflow-y-auto">
            {alerts.map((alert) => (
              <div key={alert.id} className={`p-3 bg-indigo-800/30 rounded-lg border-l-4 ${
                alert.type === 'error' ? 'border-l-red-500' :
                alert.type === 'warning' ? 'border-l-yellow-500' : 'border-l-blue-500'
              } ${alert.resolved ? 'opacity-60' : ''}`}>
                <div className={`flex items-start space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                  <div className="flex-shrink-0 mt-1">
                    {getAlertIcon(alert.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className={`flex items-center justify-between mb-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <h4 className="text-white font-medium text-sm">{alert.title}</h4>
                      {alert.resolved && (
                        <CheckCircle size={14} className="text-green-400" />
                      )}
                    </div>
                    <p className="text-gray-300 text-sm mb-2">{alert.message}</p>
                    <div className={`flex items-center justify-between text-xs ${isRTL ? "flex-row-reverse" : ""}`}>
                      <span className="text-gray-400">{alert.service}</span>
                      <span className="text-gray-400">{getRelativeTime(alert.timestamp)}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
        </>
      )}
    </DashboardLayout>
  );
};

export default SystemMonitoring;
