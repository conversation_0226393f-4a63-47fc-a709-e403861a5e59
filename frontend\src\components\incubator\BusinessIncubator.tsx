import React, { useState, useEffect } from 'react';
import { Lightbulb, Users, BookOpen, ArrowRight, Plus, Search, DollarSign } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../store/hooks';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import {
  fetchBusinessIdeas,
  fetchIncubatorResources,
  fetchFundingOpportunities,
  fetchMentorProfiles
} from '../../store/incubatorSlice';
import {
  BusinessIdeaCard,
  IncubatorResourceCard,
  FundingOpportunityCard,
  MentorProfileCard,
  MentorshipApplicationForm
} from './';
import BusinessIdeaForm from './BusinessIdeaForm';
import Navbar from '../Navbar';
import Footer from '../Footer';

const BusinessIncubator: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const {
    businessIdeas,
    resources,
    fundingOpportunities,
    mentorProfiles,
    isLoading
  } = useAppSelector(state => state.incubator);
  const { isAuthenticated, user } = useAppSelector(state => state.auth);
  const { language } = useAppSelector(state => state.language);
  const [showIdeaForm, setShowIdeaForm] = useState(false);
  const [showMentorshipForm, setShowMentorshipForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState<'ideas' | 'resources' | 'funding' | 'mentorship'>('ideas');

  // Redirect authenticated users to their dashboard incubator
  useEffect(() => {
    if (isAuthenticated) {
      if (user?.is_admin) {
        navigate('/admin/incubator', { replace: true });
      } else {
        navigate('/dashboard');
      }
    }
  }, [isAuthenticated, user, navigate]);

  useEffect(() => {
    // Only fetch data if user is authenticated, otherwise show static content
    if (isAuthenticated) {
      dispatch(fetchBusinessIdeas());
      dispatch(fetchIncubatorResources());
      dispatch(fetchFundingOpportunities());
      dispatch(fetchMentorProfiles());
    }
  }, [dispatch, isAuthenticated]);

  const filteredIdeas = businessIdeas.filter(idea =>
    idea.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    idea.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredResources = resources.filter(resource =>
    resource.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    resource.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredFundingOpportunities = fundingOpportunities.filter(opportunity =>
    opportunity.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    opportunity.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    opportunity.funding_type.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredMentors = mentorProfiles.filter(mentor =>
    // Only show verified mentors who are accepting mentees
    mentor.is_verified && mentor.is_accepting_mentees &&
    (
      mentor.user.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      mentor.user.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      mentor.bio.toLowerCase().includes(searchTerm.toLowerCase()) ||
      mentor.company?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      mentor.position?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      // Search in expertise areas
      mentor.expertise_areas.some(expertise =>
        expertise.specific_expertise.toLowerCase().includes(searchTerm.toLowerCase()) ||
        expertise.category_display.toLowerCase().includes(searchTerm.toLowerCase())
      )
    )
  );

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  return (
    <div className="min-h-screen">
      <Navbar />
      <section id="business-incubator" className="py-20 bg-indigo-950">
        <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            {t('incubator.title')}
          </h2>
          <div className="text-gray-300 text-lg">
            {t('incubator.description')}
          </div>
        </div>

        {/* Tabs */}
        <div className={`flex justify-center mb-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`flex flex-wrap ${language === 'ar' ? 'space-x-reverse' : 'space-x-2'} bg-indigo-900/50 p-1 rounded-lg`}>
            <button
              onClick={() => setActiveTab('ideas')}
              className={`px-4 py-2 rounded-md ${
                activeTab === 'ideas'
                  ? 'bg-purple-600 text-white'
                  : 'text-gray-300 hover:text-white'}
              }`}
            >
              <Lightbulb size={18} className={`inline ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
              {t('incubator.tabs.ideas')}
            </button>
            <button
              onClick={() => setActiveTab('resources')}
              className={`px-4 py-2 rounded-md ${
                activeTab === 'resources'
                  ? 'bg-purple-600 text-white'
                  : 'text-gray-300 hover:text-white'}
              }`}
            >
              <BookOpen size={18} className={`inline ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
              {t('incubator.tabs.resources')}
            </button>
            <button
              onClick={() => setActiveTab('funding')}
              className={`px-4 py-2 rounded-md ${
                activeTab === 'funding'
                  ? 'bg-purple-600 text-white'
                  : 'text-gray-300 hover:text-white'}
              }`}
            >
              <DollarSign size={18} className={`inline ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
              {t('incubator.tabs.funding')}
            </button>
            <button
              onClick={() => setActiveTab('mentorship')}
              className={`px-4 py-2 rounded-md ${
                activeTab === 'mentorship'
                  ? 'bg-purple-600 text-white'
                  : 'text-gray-300 hover:text-white'}
              }`}
            >
              <Users size={18} className={`inline ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
              {t('incubator.tabs.mentorship')}
            </button>
          </div>
        </div>

        {/* Search and Add Button */}
        <div className={`flex flex-col md:flex-row justify-between items-center mb-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="relative w-full md:w-1/3 mb-4 md:mb-0">
            <input
              type="text"
              placeholder={t('incubator.search')}
              value={searchTerm}
              onChange={handleSearchChange}
              className={`w-full px-4 py-2 ${language === 'ar' ? 'pr-10' : 'pl-10'} bg-indigo-900/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white`}
              dir={language === 'ar' ? 'rtl' : 'ltr'}
            />
            <Search size={18} className={`absolute ${language === 'ar' ? 'right-3' : 'left-3'} top-2.5 text-gray-400`} />
          </div>

          {isAuthenticated && (
            <button
              onClick={() => setShowIdeaForm(true)}
              className={`px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
            >
              <Plus size={18} className={language === 'ar' ? 'ml-2' : 'mr-2'} />
              {t('incubator.submitBusinessIdea')}
            </button>
          )}
        </div>

        {/* Content based on active tab */}
        {activeTab === 'ideas' && (
          <div>
            {!isAuthenticated ? (
              // Static content for non-authenticated users
              <div className="text-center py-12">
                <Lightbulb size={64} className="mx-auto text-purple-400 mb-6" />
                <h3 className="text-2xl font-semibold mb-4">{t('incubator.businessIdeasTitle')}</h3>
                <div className="text-gray-300 mb-6 max-w-2xl mx-auto">
                  {t('incubator.businessIdeasDescription')}
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                  <div className="bg-indigo-900/50 p-6 rounded-lg">
                    <h4 className="font-semibold mb-2">{t('incubator.ideaValidation')}</h4>
                    <p className="text-gray-400 text-sm">{t('incubator.ideaValidationDesc')}</p>
                  </div>
                  <div className="bg-indigo-900/50 p-6 rounded-lg">
                    <h4 className="font-semibold mb-2">{t('incubator.businessPlanning')}</h4>
                    <p className="text-gray-400 text-sm">{t('incubator.businessPlanningDesc')}</p>
                  </div>
                  <div className="bg-indigo-900/50 p-6 rounded-lg">
                    <h4 className="font-semibold mb-2">{t('incubator.communityFeedback')}</h4>
                    <p className="text-gray-400 text-sm">{t('incubator.communityFeedbackDesc')}</p>
                  </div>
                </div>
                <button
                  onClick={() => navigate('/register')}
                  className="px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300"
                >
                  {t('incubator.joinToSubmitIdeas')}
                </button>
              </div>
            ) : isLoading ? (
              <div className={`flex justify-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
              </div>
            ) : filteredIdeas.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredIdeas.map(idea => (
                  <BusinessIdeaCard key={idea.id} idea={idea} />
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Lightbulb size={48} className="mx-auto text-gray-500 mb-4" />
                <h3 className="text-xl font-semibold mb-2">{t('incubator.noBusinessIdeasFound')}</h3>
                <div className="text-gray-400 mb-4">
                  {searchTerm ? t('incubator.tryDifferentSearch') : t('incubator.beFirstToSubmit')}
                </div>
                <button
                  onClick={() => setShowIdeaForm(true)}
                  className="px-4 py-2 bg-purple-600 rounded-lg hover:bg-purple-700 transition-colors"
                >
                  {t('incubator.submitAnIdea')}
                </button>
              </div>
            )}
          </div>
        )}

        {activeTab === 'resources' && (
          <div>
            {!isAuthenticated ? (
              // Static content for non-authenticated users
              <div className="text-center py-12">
                <BookOpen size={64} className="mx-auto text-purple-400 mb-6" />
                <h3 className="text-2xl font-semibold mb-4">{t('incubator.resourcesTitle')}</h3>
                <div className="text-gray-300 mb-6 max-w-2xl mx-auto">
                  {t('incubator.resourcesDescription')}
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                  <div className="bg-indigo-900/50 p-6 rounded-lg">
                    <h4 className="font-semibold mb-2">{t('incubator.learningMaterials')}</h4>
                    <p className="text-gray-400 text-sm">{t('incubator.learningMaterialsDesc')}</p>
                  </div>
                  <div className="bg-indigo-900/50 p-6 rounded-lg">
                    <h4 className="font-semibold mb-2">{t('incubator.businessTools')}</h4>
                    <p className="text-gray-400 text-sm">{t('incubator.businessToolsDesc')}</p>
                  </div>
                  <div className="bg-indigo-900/50 p-6 rounded-lg">
                    <h4 className="font-semibold mb-2">{t('incubator.expertGuidance')}</h4>
                    <p className="text-gray-400 text-sm">{t('incubator.expertGuidanceDesc')}</p>
                  </div>
                </div>
                <button
                  onClick={() => navigate('/register')}
                  className="px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300"
                >
                  {t('incubator.joinToAccessResources')}
                </button>
              </div>
            ) : isLoading ? (
              <div className={`flex justify-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
              </div>
            ) : filteredResources.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredResources.map(resource => (
                  <IncubatorResourceCard key={resource.id} resource={resource} />
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <BookOpen size={48} className="mx-auto text-gray-500 mb-4" />
                <h3 className="text-xl font-semibold mb-2">{t('incubator.noResourcesFound')}</h3>
                <div className="text-gray-400">
                  {searchTerm ? t('incubator.tryDifferentSearch') : t('incubator.resourcesAddedSoon')}
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'funding' && (
          <div>
            {isLoading ? (
              <div className={`flex justify-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
              </div>
            ) : filteredFundingOpportunities.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredFundingOpportunities.map(opportunity => (
                  <FundingOpportunityCard key={opportunity.id} opportunity={opportunity} />
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <DollarSign size={48} className="mx-auto text-gray-500 mb-4" />
                <h3 className="text-xl font-semibold mb-2">{t('incubator.noFundingOpportunities')}</h3>
                <div className="text-gray-400">
                  {searchTerm ? t('incubator.tryDifferentSearch') : t('incubator.fundingOpportunitiesAddedSoon')}
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'mentorship' && (
          <div>
            {showMentorshipForm ? (
              <div className="bg-indigo-900/50 rounded-lg p-6 mb-8">
                <h3 className="text-xl font-semibold mb-4">{t('incubator.applyForMentorship')}</h3>
                <MentorshipApplicationForm
                  onSubmitSuccess={() => setShowMentorshipForm(false)}
                  onCancel={() => setShowMentorshipForm(false)}
                />
              </div>
            ) : (
              <div className={`flex justify-end mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
                {isAuthenticated && (
                  <button
                    onClick={() => setShowMentorshipForm(true)}
                    className={`px-4 py-2 bg-purple-600 rounded-lg hover:bg-purple-700 transition-colors flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                  >
                    <Plus size={18} className={language === 'ar' ? 'ml-2' : 'mr-2'} />
                    {t('incubator.applyForMentorship')}
                  </button>
                )}
              </div>
            )}

            {isLoading ? (
              <div className={`flex justify-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
              </div>
            ) : filteredMentors.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredMentors.map(mentor => (
                  <MentorProfileCard key={mentor.id} mentor={mentor} />
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Users size={48} className="mx-auto text-gray-500 mb-4" />
                <h3 className="text-xl font-semibold mb-2">{t('incubator.noMentorsFound')}</h3>
                <div className="text-gray-400">
                  {searchTerm ? t('incubator.tryDifferentSearch') : t('incubator.mentorsAddedSoon')}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Business Idea Form Modal */}
        {showIdeaForm && (
          <div className={`fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className="bg-indigo-900 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className={`flex justify-between items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <h3 className="text-xl font-bold">{t('incubator.submitBusinessIdea')}</h3>
                  <button
                    onClick={() => setShowIdeaForm(false)}
                    className="text-gray-400 hover:text-white"
                  >
                    &times;
                  </button>
                </div>
                <BusinessIdeaForm
                  onSubmitSuccess={() => setShowIdeaForm(false)}
                  onCancel={() => setShowIdeaForm(false)}
                />
              </div>
            </div>
          </div>
        )}
        </div>
      </section>
      <Footer />
    </div>
  );
};

export default BusinessIncubator;
