/**
 * Enhanced SVG Search Components
 * Beautiful, animated SVG-based search interface components
 */

import React, { useState, useEffect } from 'react';
import { Search, Filter, X, ChevronDown, Sparkles, Zap } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface SearchResult {
  id: string;
  type: 'business' | 'forum' | 'resource' | 'user';
  title: string;
  description: string;
  score: number;
  metadata?: Record<string, any>;
}

interface SVGSearchBarProps {
  onSearch: (query: string, filters: SearchFilters) => void;
  placeholder?: string;
  className?: string;
  animated?: boolean;
}

interface SearchFilters {
  type?: string[];
  dateRange?: string;
  tags?: string[];
  author?: string;
}

// Animated SVG Search Icon Component
const AnimatedSearchIcon: React.FC<{ isActive: boolean; size?: number }> = ({
  isActive,
  size = 24
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    className={`transition-all duration-300 ${isActive ? 'text-purple-400' : 'text-gray-400'}`}
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
  >
    <circle
      cx="11"
      cy="11"
      r="8"
      className={`transition-all duration-300 ${isActive ? 'stroke-purple-400' : 'stroke-gray-400'}`}
    />
    <path
      d="m21 21-4.35-4.35"
      className={`transition-all duration-300 ${isActive ? 'stroke-purple-400' : 'stroke-gray-400'}`}
    />
    {isActive && (
      <circle cx="11" cy="11" r="3" fill="currentColor" className="animate-pulse" />
    )}
  </svg>
);

// SVG Filter Icon with Animation
const AnimatedFilterIcon: React.FC<{ isActive: boolean; size?: number }> = ({
  isActive,
  size = 20
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    className={`transition-all duration-300 ${isActive ? 'text-purple-400' : 'text-gray-400'}`}
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
  >
    <polygon
      points="22,3 2,3 10,12.46 10,19 14,21 14,12.46 22,3"
      className={`transition-all duration-300 ${isActive ? 'fill-purple-400/20' : ''}`}
    />
  </svg>
);

// Main SVG Search Bar Component
export const SVGSearchBar: React.FC<SVGSearchBarProps> = ({ onSearch,
  placeholder,
  className = '',
  animated = true
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [query, setQuery] = useState('');
  const [isActive, setIsActive] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<SearchFilters>({});

  const handleSearch = () => {
    onSearch(query, filters);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === t("common.enter", "Enter")) {
      handleSearch();
    }
  };

  return (
    <div className={`relative ${className}`}>
      {/* Main Search Container */}
      <div className={`
        relative bg-gradient-to-r from-indigo-900/30 to-purple-900/30
        backdrop-blur-sm rounded-xl border border-indigo-800/50
        transition-all duration-300 hover:border-purple-500/50}
        ${isActive ? 'ring-2 ring-purple-500/30 border-purple-500/50' : ''}
      `}>
        {/* Search Input */}
        <div className={`flex items-center p-4 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`${isRTL ? 'ml-3' : 'mr-3'}`}>
            <AnimatedSearchIcon isActive={isActive} />
          </div>

          <input
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onFocus={() => setIsActive(true)}
            onBlur={() => setIsActive(false)}
            onKeyPress={handleKeyPress}
            placeholder={placeholder || t('search.placeholder')}
            className={`
              flex-1 bg-transparent text-white placeholder-gray-400
              focus:outline-none text-lg}
              ${isRTL ? 'text-right' : 'text-left'}
            `}
            dir={isRTL ? 'rtl' : 'ltr'}
          />

          {/* Action Buttons */}
          <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
            {query && (
              <button
                onClick={() => setQuery('')}
                className="p-1 text-gray-400 hover:text-white transition-colors"
              >
                <X size={18} />
              </button>
            )}

            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`
                p-2 rounded-lg transition-all duration-300}
                ${showFilters ? 'bg-purple-500/20 text-purple-400' : 'text-gray-400 hover:text-white'}
              `}
            >
              <AnimatedFilterIcon isActive={showFilters} />
            </button>

            <button
              onClick={handleSearch}
              disabled={!query.trim()}
              className={`
                px-4 py-2 rounded-lg font-medium transition-all duration-300
                ${query.trim()
                  ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600'
                  : 'bg-gray-600 text-gray-400 cursor-not-allowed'}
                }
              `}
            >
              {t('search.search')}
            </button>
          </div>
        </div>

        {/* Animated Search Suggestions */}
        {animated && isActive && (
          <div className="absolute top-full left-0 right-0 mt-2 bg-indigo-900/90 backdrop-blur-sm rounded-lg border border-indigo-800/50 p-4 z-50">
            <div className="space-y-2">
              <div className="text-sm text-gray-400">{t('search.suggestions')}</div>
              <div className={`flex flex-wrap gap-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                {[t("common.ai.insights.business", "AI insights"), 'Business plans', 'Forum discussions', 'Resources'].map((suggestion) => (
                  <button
                    key={suggestion}
                    onClick={() => setQuery(suggestion)}
                    className="px-3 py-1 bg-purple-500/20 text-purple-300 rounded-full text-sm hover:bg-purple-500/30 transition-colors"
                  >
                    {suggestion}
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Advanced Filters Panel */}
      {showFilters && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-indigo-900/90 backdrop-blur-sm rounded-lg border border-indigo-800/50 p-6 z-40">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Content Type Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('search.contentType')}
              </label>
              <select
                value={filters.type?.[0] || ''}
                onChange={(e) => setFilters({...filters, type: e.target.value ? [e.target.value] : []})}
                className="w-full bg-indigo-800/50 border border-indigo-700 rounded-lg px-3 py-2 text-white"
              >
                <option value="">{t('search.allTypes')}</option>
                <option value="business">{t('search.businessIdeas')}</option>
                <option value="forum">{t('search.forumPosts')}</option>
                <option value="resource">{t('search.resources')}</option>
                <option value="user">{t('search.users')}</option>
              </select>
            </div>

            {/* Date Range Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('search.dateRange')}
              </label>
              <select
                value={filters.dateRange || ''}
                onChange={(e) => setFilters({...filters, dateRange: e.target.value})}
                className="w-full bg-indigo-800/50 border border-indigo-700 rounded-lg px-3 py-2 text-white"
              >
                <option value="">{t('search.anyTime')}</option>
                <option value="today">{t('search.today')}</option>
                <option value="week">{t('search.thisWeek')}</option>
                <option value="month">{t('search.thisMonth')}</option>
                <option value="year">{t('search.thisYear')}</option>
              </select>
            </div>

            {/* Author Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('search.author')}
              </label>
              <input
                type="text"
                value={filters.author || ''}
                onChange={(e) => setFilters({...filters, author: e.target.value})}
                placeholder={t('search.authorPlaceholder')}
                className="w-full bg-indigo-800/50 border border-indigo-700 rounded-lg px-3 py-2 text-white placeholder-gray-400"
              />
            </div>
          </div>

          {/* Filter Actions */}
          <div className={`flex justify-end space-x-3 mt-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            <button
              onClick={() => setFilters({})}
              className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
            >
              {t('search.clearFilters')}
            </button>
            <button
              onClick={() => setShowFilters(false)}
              className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors"
            >
              {t('search.applyFilters')}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

// SVG Search Results Component
export const SVGSearchResults: React.FC<{
  results: SearchResult[];
  isLoading: boolean;
  onResultClick: (result: SearchResult) => void;
}> = ({ results, isLoading, onResultClick }) => {
  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="bg-indigo-900/30 rounded-lg p-4 animate-pulse">
            <div className="h-4 bg-gray-600 rounded w-3/4 mb-2"></div>
            <div className="h-3 bg-gray-700 rounded w-1/2"></div>
          </div>
        ))}
      </div>
    );
  }

  if (results.length === 0) {
    return (
      <div className="text-center py-12">
        <svg
          width="64"
          height="64"
          viewBox="0 0 24 24"
          className="mx-auto mb-4 text-gray-400"
          fill="none"
          stroke="currentColor"
          strokeWidth="1"
        >
          <circle cx="11" cy="11" r="8" />
          <path d="m21 21-4.35-4.35" />
          <circle cx="11" cy="11" r="3" fill="currentColor" opacity="0.3" />
        </svg>
        <p className="text-gray-400">{t('search.noResults')}</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {results.map((result) => (
        <div
          key={result.id}
          onClick={() => onResultClick(result)}
          className={`
            bg-gradient-to-r from-indigo-900/30 to-purple-900/20
            backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50
            cursor-pointer transition-all duration-300
            hover:border-purple-500/50 hover:bg-purple-900/30}
            ${isRTL ? 'text-right' : 'text-left'}
          `}
        >
          <div className={`flex items-start justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className={`flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
              <div className={`flex items-center space-x-2 mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                <TypeIcon type={result.type} />
                <h3 className="font-semibold text-white">{result.title}</h3>
                <div className={`flex items-center space-x-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <Sparkles size={12} className="text-yellow-400" />
                  <span className="text-xs text-yellow-400">{Math.round(result.score * 100)}%</span>
                </div>
              </div>
              <p className="text-gray-300 text-sm line-clamp-2">{result.description}</p>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

// Type Icon Component
const TypeIcon: React.FC<{ type: string }> = ({ type }) => {
  const iconProps = { size: 16, className: "text-purple-400" };

  switch (type) {
    case 'business':
      return <Zap {...iconProps} />;
    case 'forum':
      return <svg {...iconProps} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
      </svg>;
    case 'resource':
      return <svg {...iconProps} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
        <polyline points="14,2 14,8 20,8" />
      </svg>;
    case 'user':
      return <svg {...iconProps} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
        <circle cx="12" cy="7" r="4" />
      </svg>;
    default:
      return <Search {...iconProps} />;
  }
};

export default SVGSearchBar;
