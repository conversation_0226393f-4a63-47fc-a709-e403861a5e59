import React, { useState, useEffect } from 'react';
import { Search, DollarSign, Calendar, FileText, CheckCircle, XCircle, Clock, AlertCircle } from 'lucide-react';
import { DashboardLayout } from '../dashboard';
import { FundingApplication, fundingApplicationsAPI } from '../../../services/incubatorApi';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';

const FundingApplicationsManagement: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  // State management
  const [applications, setApplications] = useState<FundingApplication[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch funding applications
  const fetchApplications = async () => {
    try {
      setLoading(true);
      const data = await fundingApplicationsAPI.getFundingApplications();
      setApplications(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error fetching funding applications:', error);
      setApplications([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchApplications();
  }, []);

  // Filter applications based on search
  const filteredApplications = applications.filter(application => {
    return application.business_idea.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      application.business_idea.user.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      application.business_idea.user.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      application.funding_opportunity.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      application.status.toLowerCase().includes(searchTerm.toLowerCase());
  });

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Get status color and icon
  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'pending':
        return {
          color: 'bg-yellow-500/20 text-yellow-300',
          icon: <Clock size={16} />,
          label: t('application.status.pending', 'Pending')
        };
      case 'under_review':
        return {
          color: 'bg-blue-500/20 text-blue-300',
          icon: <AlertCircle size={16} />,
          label: t('application.status.under_review', 'Under Review')
        };
      case 'approved':
        return {
          color: 'bg-green-500/20 text-green-300',
          icon: <CheckCircle size={16} />,
          label: t('application.status.approved', 'Approved')
        };
      case 'rejected':
        return {
          color: 'bg-red-500/20 text-red-300',
          icon: <XCircle size={16} />,
          label: t('application.status.rejected', 'Rejected')
        };
      case 'funded':
        return {
          color: 'bg-purple-500/20 text-purple-300',
          icon: <CheckCircle size={16} />,
          label: t('application.status.funded', 'Funded')
        };
      default:
        return {
          color: 'bg-gray-500/20 text-gray-300',
          icon: <Clock size={16} />,
          label: status
        };
    }
  };

  // Get funding type color
  const getFundingTypeColor = (type: string) => {
    switch (type) {
      case 'grant': return 'bg-green-500/20 text-green-300';
      case 'loan': return 'bg-blue-500/20 text-blue-300';
      case 'equity': return 'bg-purple-500/20 text-purple-300';
      case 'competition': return 'bg-orange-500/20 text-orange-300';
      default: return 'bg-gray-500/20 text-gray-300';
    }
  };

  return (
    <DashboardLayout currentPage="incubator">
      {/* Header */}
      <div className={`mb-8 ${isRTL ? "text-right" : ""}`}>
        <h1 className="text-2xl font-bold text-white">{t('admin.funding.applications.management', 'Funding Applications Management')}</h1>
        <div className="text-gray-400 mt-1">{t('admin.funding.applications.description', 'Review and manage funding applications')}</div>
      </div>

      {/* Search */}
      <div className="mb-6">
        <div className="relative">
          <Search size={20} className={`absolute top-3 text-gray-400 ${isRTL ? "right-3" : "left-3"}`} />
          <input
            type="text"
            placeholder={t('admin.search.funding.applications', 'Search funding applications...')}
            value={searchTerm}
            onChange={handleSearchChange}
            className={`w-full bg-indigo-900/50 border border-indigo-800 rounded-lg py-2.5 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 ${isRTL ? "pr-10 pl-4" : "pl-10 pr-4"}`}
          />
        </div>
      </div>

      {/* Applications List */}
      {loading ? (
        <div className="flex justify-center py-12">
          <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      ) : filteredApplications.length > 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredApplications.map((application) => {
            const statusDisplay = getStatusDisplay(application.status);
            
            return (
              <div key={application.id} className="bg-indigo-900/30 backdrop-blur-sm rounded-xl overflow-hidden border border-indigo-800/50 hover:border-purple-500/50 transition-all duration-300 hover:shadow-glow">
                <div className="p-6">
                  <div className={`flex justify-between items-start mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className="flex-1">
                      <div className={`flex items-center space-x-2 mb-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 ${statusDisplay.color} ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                          {statusDisplay.icon}
                          <span>{statusDisplay.label}</span>
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Business Idea */}
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-300 mb-2">{t('application.business.idea', 'Business Idea')}</h4>
                    <div className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                      <div className="w-10 h-10 rounded-full bg-purple-700 flex items-center justify-center text-white text-sm font-bold">
                        {application.business_idea.user.first_name.charAt(0)}{application.business_idea.user.last_name.charAt(0)}
                      </div>
                      <div>
                        <h3 className="text-white font-medium line-clamp-1">{application.business_idea.title}</h3>
                        <p className="text-gray-400 text-sm">
                          {application.business_idea.user.first_name} {application.business_idea.user.last_name}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Funding Opportunity */}
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-300 mb-2">{t('application.funding.opportunity', 'Funding Opportunity')}</h4>
                    <div className="space-y-2">
                      <h3 className="text-white font-medium line-clamp-1">{application.funding_opportunity.title}</h3>
                      <div className="flex flex-wrap gap-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getFundingTypeColor(application.funding_opportunity.funding_type)}`}>
                          {application.funding_opportunity.funding_type}
                        </span>
                        {application.funding_opportunity.amount_min && application.funding_opportunity.amount_max && (
                          <span className="px-2 py-1 bg-green-500/20 text-green-300 rounded-full text-xs font-medium">
                            ${application.funding_opportunity.amount_min.toLocaleString()} - ${application.funding_opportunity.amount_max.toLocaleString()}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Application Details */}
                  <div className="space-y-3">
                    {/* Requested Amount */}
                    <div className={`flex items-center text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
                      <DollarSign size={16} className={`text-green-400 ${isRTL ? "ml-2" : "mr-2"}`} />
                      <span className="text-gray-300">{t('application.requested', 'Requested')}: </span>
                      <span className="text-white font-medium ml-1">
                        ${application.requested_amount.toLocaleString()}
                      </span>
                    </div>

                    {/* Application Date */}
                    <div className={`flex items-center text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
                      <Calendar size={16} className={`text-blue-400 ${isRTL ? "ml-2" : "mr-2"}`} />
                      <span className="text-gray-300">{t('application.submitted', 'Submitted')}: </span>
                      <span className="text-white ml-1">{new Date(application.created_at).toLocaleDateString()}</span>
                    </div>

                    {/* Review Date */}
                    {application.reviewed_at && (
                      <div className={`flex items-center text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
                        <Calendar size={16} className={`text-purple-400 ${isRTL ? "ml-2" : "mr-2"}`} />
                        <span className="text-gray-300">{t('application.reviewed', 'Reviewed')}: </span>
                        <span className="text-white ml-1">{new Date(application.reviewed_at).toLocaleDateString()}</span>
                      </div>
                    )}

                    {/* Proposal */}
                    {application.proposal && (
                      <div>
                        <div className={`flex items-center space-x-2 text-sm mb-1 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                          <FileText size={16} className="text-cyan-400" />
                          <span className="text-gray-300">{t('application.proposal', 'Proposal')}</span>
                        </div>
                        <p className="text-gray-300 text-sm line-clamp-3">{application.proposal}</p>
                      </div>
                    )}

                    {/* Use of Funds */}
                    {application.use_of_funds && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-300 mb-1">{t('application.use.of.funds', 'Use of Funds')}</h4>
                        <p className="text-gray-300 text-sm line-clamp-2">{application.use_of_funds}</p>
                      </div>
                    )}

                    {/* Expected ROI */}
                    {application.expected_roi && (
                      <div className={`flex items-center text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
                        <span className="text-gray-300">{t('application.expected.roi', 'Expected ROI')}: </span>
                        <span className="text-white font-medium ml-1">{application.expected_roi}%</span>
                      </div>
                    )}

                    {/* Timeline */}
                    {application.timeline && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-300 mb-1">{t('application.timeline', 'Timeline')}</h4>
                        <p className="text-gray-300 text-sm line-clamp-2">{application.timeline}</p>
                      </div>
                    )}

                    {/* Reviewer Notes */}
                    {application.reviewer_notes && (
                      <div className="pt-3 border-t border-indigo-800/50">
                        <h4 className="text-sm font-medium text-gray-300 mb-1">{t('application.reviewer.notes', 'Reviewer Notes')}</h4>
                        <p className="text-gray-300 text-sm line-clamp-2">{application.reviewer_notes}</p>
                      </div>
                    )}

                    {/* Funded Amount */}
                    {application.funded_amount && (
                      <div className="pt-3 border-t border-indigo-800/50">
                        <div className={`flex items-center text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
                          <DollarSign size={16} className={`text-purple-400 ${isRTL ? "ml-2" : "mr-2"}`} />
                          <span className="text-gray-300">{t('application.funded.amount', 'Funded Amount')}: </span>
                          <span className="text-purple-300 font-medium ml-1">
                            ${application.funded_amount.toLocaleString()}
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        <div className="text-center py-12">
          <FileText size={48} className="mx-auto text-gray-600 mb-4" />
          <h3 className="text-lg font-medium text-gray-400 mb-2">
            {searchTerm 
              ? t('admin.no.funding.applications.found', 'No funding applications found')
              : t('admin.no.funding.applications', 'No funding applications yet')
            }
          </h3>
          <p className="text-gray-500 mb-4">
            {searchTerm
              ? t('admin.try.different.search', 'Try adjusting your search')
              : t('admin.no.funding.applications.description', 'Funding applications will appear here when entrepreneurs apply for funding')
            }
          </p>
        </div>
      )}
    </DashboardLayout>
  );
};

export default FundingApplicationsManagement;
