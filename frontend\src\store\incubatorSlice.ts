import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import {
  BusinessIdea, ProgressUpdate, IncubatorResource, MentorshipApplication,
  InvestorProfile, FundingOpportunity, FundingApplication, Investment,
  MentorProfile, MentorExpertise, MentorshipMatch, MentorshipSession, MentorshipFeedback,
  businessIdeasAPI, progressUpdatesAPI, incubatorResourcesAPI, mentorshipApplicationsAPI,
  investorProfilesAPI, fundingOpportunitiesAPI, fundingApplicationsAPI, investmentsAPI,
  mentorProfilesAPI, mentorExpertiseAPI, mentorshipMatchesAPI, mentorshipSessionsAPI, mentorshipFeedbackAPI
} from '../services/incubatorApi';

// Define the state interface
interface IncubatorState {
  businessIdeas: BusinessIdea[];
  progressUpdates: ProgressUpdate[];
  resources: IncubatorResource[];
  mentorshipApplications: MentorshipApplication[];
  investorProfiles: InvestorProfile[];
  fundingOpportunities: FundingOpportunity[];
  fundingApplications: FundingApplication[];
  investments: Investment[];
  mentorProfiles: MentorProfile[];
  mentorExpertise: MentorExpertise[];
  mentorshipMatches: MentorshipMatch[];
  mentorshipSessions: MentorshipSession[];
  mentorshipFeedback: MentorshipFeedback[];
  selectedBusinessIdea: BusinessIdea | null;
  selectedProgressUpdate: ProgressUpdate | null;
  selectedResource: IncubatorResource | null;
  selectedMentorshipApplication: MentorshipApplication | null;
  selectedInvestorProfile: InvestorProfile | null;
  selectedFundingOpportunity: FundingOpportunity | null;
  selectedFundingApplication: FundingApplication | null;
  selectedInvestment: Investment | null;
  selectedMentorProfile: MentorProfile | null;
  selectedMentorExpertise: MentorExpertise | null;
  selectedMentorshipMatch: MentorshipMatch | null;
  selectedMentorshipSession: MentorshipSession | null;
  selectedMentorshipFeedback: MentorshipFeedback | null;
  isLoading: boolean;
  error: string | null;
}

// Initial state
const initialState: IncubatorState = {
  businessIdeas: [],
  progressUpdates: [],
  resources: [],
  mentorshipApplications: [],
  investorProfiles: [],
  fundingOpportunities: [],
  fundingApplications: [],
  investments: [],
  mentorProfiles: [],
  mentorExpertise: [],
  mentorshipMatches: [],
  mentorshipSessions: [],
  mentorshipFeedback: [],
  selectedBusinessIdea: null,
  selectedProgressUpdate: null,
  selectedResource: null,
  selectedMentorshipApplication: null,
  selectedInvestorProfile: null,
  selectedFundingOpportunity: null,
  selectedFundingApplication: null,
  selectedInvestment: null,
  selectedMentorProfile: null,
  selectedMentorExpertise: null,
  selectedMentorshipMatch: null,
  selectedMentorshipSession: null,
  selectedMentorshipFeedback: null,
  isLoading: false,
  error: null,
};

// Async thunks for Business Ideas
export const fetchBusinessIdeas = createAsyncThunk(
  'incubator/fetchBusinessIdeas',
  async (_, { rejectWithValue }) => {
    try {
      const ideas = await businessIdeasAPI.getBusinessIdeas();
      return ideas;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to fetch business ideas');
    }
  }
);

export const fetchBusinessIdeaById = createAsyncThunk(
  'incubator/fetchBusinessIdeaById',
  async (id: number, { rejectWithValue }) => {
    try {
      const idea = await businessIdeasAPI.getBusinessIdea(id);
      return idea;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to fetch business idea');
    }
  }
);

export const createBusinessIdea = createAsyncThunk(
  'incubator/createBusinessIdea',
  async (ideaData: Partial<BusinessIdea> & { owner_id: number }, { rejectWithValue }) => {
    try {
      const idea = await businessIdeasAPI.createBusinessIdea(ideaData);
      return idea;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to create business idea');
    }
  }
);

export const updateBusinessIdea = createAsyncThunk(
  'incubator/updateBusinessIdea',
  async ({ id, ideaData }: { id: number, ideaData: Partial<BusinessIdea> }, { rejectWithValue }) => {
    try {
      const idea = await businessIdeasAPI.updateBusinessIdea(id, ideaData);
      return idea;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to update business idea');
    }
  }
);

export const deleteBusinessIdea = createAsyncThunk(
  'incubator/deleteBusinessIdea',
  async (id: number, { rejectWithValue }) => {
    try {
      await businessIdeasAPI.deleteBusinessIdea(id);
      return id;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to delete business idea');
    }
  }
);

// Async thunks for Progress Updates
export const fetchProgressUpdates = createAsyncThunk(
  'incubator/fetchProgressUpdates',
  async (businessIdeaId: number | undefined, { rejectWithValue }) => {
    try {
      const updates = await progressUpdatesAPI.getProgressUpdates(businessIdeaId);
      return updates;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to fetch progress updates');
    }
  }
);

export const createProgressUpdate = createAsyncThunk(
  'incubator/createProgressUpdate',
  async (updateData: Partial<ProgressUpdate> & { business_idea: number, created_by_id: number }, { rejectWithValue }) => {
    try {
      const update = await progressUpdatesAPI.createProgressUpdate(updateData);
      return update;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to create progress update');
    }
  }
);

// Async thunks for Incubator Resources
export const fetchIncubatorResources = createAsyncThunk(
  'incubator/fetchIncubatorResources',
  async (category: string | undefined, { rejectWithValue }) => {
    try {
      const resources = await incubatorResourcesAPI.getIncubatorResources(category);
      return resources;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to fetch incubator resources');
    }
  }
);

export const createIncubatorResource = createAsyncThunk(
  'incubator/createIncubatorResource',
  async (resourceData: Partial<IncubatorResource> & { author_id: number }, { rejectWithValue }) => {
    try {
      const resource = await incubatorResourcesAPI.createIncubatorResource(resourceData);
      return resource;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to create incubator resource');
    }
  }
);

// Async thunks for Mentorship Applications
export const fetchMentorshipApplications = createAsyncThunk(
  'incubator/fetchMentorshipApplications',
  async (businessIdeaId: number | undefined, { rejectWithValue }) => {
    try {
      const applications = await mentorshipApplicationsAPI.getMentorshipApplications(businessIdeaId);
      return applications;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to fetch mentorship applications');
    }
  }
);

export const createMentorshipApplication = createAsyncThunk(
  'incubator/createMentorshipApplication',
  async (applicationData: Partial<MentorshipApplication> & { business_idea: number, applicant_id: number }, { rejectWithValue }) => {
    try {
      const application = await mentorshipApplicationsAPI.createMentorshipApplication(applicationData);
      return application;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to create mentorship application');
    }
  }
);

// Async thunks for Investor Profiles
export const fetchInvestorProfiles = createAsyncThunk(
  'incubator/fetchInvestorProfiles',
  async (_, { rejectWithValue }) => {
    try {
      const profiles = await investorProfilesAPI.getInvestorProfiles();
      return profiles;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to fetch investor profiles');
    }
  }
);

export const fetchInvestorProfileById = createAsyncThunk(
  'incubator/fetchInvestorProfileById',
  async (id: number, { rejectWithValue }) => {
    try {
      const profile = await investorProfilesAPI.getInvestorProfile(id);
      return profile;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to fetch investor profile');
    }
  }
);

export const createInvestorProfile = createAsyncThunk(
  'incubator/createInvestorProfile',
  async (profileData: Partial<InvestorProfile> & { user_id: number }, { rejectWithValue }) => {
    try {
      const profile = await investorProfilesAPI.createInvestorProfile(profileData);
      return profile;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to create investor profile');
    }
  }
);

// Async thunks for Funding Opportunities
export const fetchFundingOpportunities = createAsyncThunk(
  'incubator/fetchFundingOpportunities',
  async (_, { rejectWithValue }) => {
    try {
      const opportunities = await fundingOpportunitiesAPI.getFundingOpportunities();
      return opportunities;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to fetch funding opportunities');
    }
  }
);

export const fetchFundingOpportunityById = createAsyncThunk(
  'incubator/fetchFundingOpportunityById',
  async (id: number, { rejectWithValue }) => {
    try {
      const opportunity = await fundingOpportunitiesAPI.getFundingOpportunity(id);
      return opportunity;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to fetch funding opportunity');
    }
  }
);

export const createFundingOpportunity = createAsyncThunk(
  'incubator/createFundingOpportunity',
  async (opportunityData: Partial<FundingOpportunity> & { provider_id: number }, { rejectWithValue }) => {
    try {
      const opportunity = await fundingOpportunitiesAPI.createFundingOpportunity(opportunityData);
      return opportunity;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to create funding opportunity');
    }
  }
);

// Async thunks for Funding Applications
export const fetchFundingApplications = createAsyncThunk(
  'incubator/fetchFundingApplications',
  async ({ businessIdeaId, fundingOpportunityId }: { businessIdeaId?: number, fundingOpportunityId?: number }, { rejectWithValue }) => {
    try {
      const applications = await fundingApplicationsAPI.getFundingApplications(businessIdeaId, fundingOpportunityId);
      return applications;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to fetch funding applications');
    }
  }
);

export const createFundingApplication = createAsyncThunk(
  'incubator/createFundingApplication',
  async (applicationData: Partial<FundingApplication> & { business_idea: number, funding_opportunity: number, applicant_id: number }, { rejectWithValue }) => {
    try {
      const application = await fundingApplicationsAPI.createFundingApplication(applicationData);
      return application;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to create funding application');
    }
  }
);

// Async thunks for Investments
export const fetchInvestments = createAsyncThunk(
  'incubator/fetchInvestments',
  async (businessIdeaId: number | undefined, { rejectWithValue }) => {
    try {
      const investments = await investmentsAPI.getInvestments(businessIdeaId);
      return investments;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to fetch investments');
    }
  }
);

export const createInvestment = createAsyncThunk(
  'incubator/createInvestment',
  async (investmentData: Partial<Investment> & { business_idea: number, investor_id: number }, { rejectWithValue }) => {
    try {
      const investment = await investmentsAPI.createInvestment(investmentData);
      return investment;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to create investment');
    }
  }
);

// Async thunks for Mentor Profiles
export const fetchMentorProfiles = createAsyncThunk(
  'incubator/fetchMentorProfiles',
  async (_, { rejectWithValue }) => {
    try {
      const profiles = await mentorProfilesAPI.getMentorProfiles();
      return profiles;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to fetch mentor profiles');
    }
  }
);

export const fetchMentorProfileById = createAsyncThunk(
  'incubator/fetchMentorProfileById',
  async (id: number, { rejectWithValue }) => {
    try {
      const profile = await mentorProfilesAPI.getMentorProfile(id);
      return profile;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to fetch mentor profile');
    }
  }
);

export const createMentorProfile = createAsyncThunk(
  'incubator/createMentorProfile',
  async (profileData: Partial<MentorProfile> & { user_id: number }, { rejectWithValue }) => {
    try {
      const profile = await mentorProfilesAPI.createMentorProfile(profileData);
      return profile;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to create mentor profile');
    }
  }
);

// Async thunks for Mentor Expertise
export const fetchMentorExpertise = createAsyncThunk(
  'incubator/fetchMentorExpertise',
  async (mentorId: number | undefined, { rejectWithValue }) => {
    try {
      const expertise = await mentorExpertiseAPI.getMentorExpertise(mentorId);
      return expertise;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to fetch mentor expertise');
    }
  }
);

export const createMentorExpertise = createAsyncThunk(
  'incubator/createMentorExpertise',
  async (expertiseData: Partial<MentorExpertise> & { mentor: number }, { rejectWithValue }) => {
    try {
      const expertise = await mentorExpertiseAPI.createMentorExpertise(expertiseData);
      return expertise;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to create mentor expertise');
    }
  }
);

// Async thunks for Mentorship Matches
export const fetchMentorshipMatches = createAsyncThunk(
  'incubator/fetchMentorshipMatches',
  async (_, { rejectWithValue }) => {
    try {
      const matches = await mentorshipMatchesAPI.getMentorshipMatches();
      return matches;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to fetch mentorship matches');
    }
  }
);

export const fetchMentorshipMatchById = createAsyncThunk(
  'incubator/fetchMentorshipMatchById',
  async (id: number, { rejectWithValue }) => {
    try {
      const match = await mentorshipMatchesAPI.getMentorshipMatch(id);
      return match;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to fetch mentorship match');
    }
  }
);

export const createMentorshipMatch = createAsyncThunk(
  'incubator/createMentorshipMatch',
  async (matchData: Partial<MentorshipMatch> & { mentor: number, business_idea: number, application_id?: number }, { rejectWithValue }) => {
    try {
      const match = await mentorshipMatchesAPI.createMentorshipMatch(matchData);
      return match;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to create mentorship match');
    }
  }
);

// Async thunks for Mentorship Sessions
export const fetchMentorshipSessions = createAsyncThunk(
  'incubator/fetchMentorshipSessions',
  async ({ matchId, status }: { matchId?: number, status?: string } = {}, { rejectWithValue }) => {
    try {
      const sessions = await mentorshipSessionsAPI.getMentorshipSessions(matchId, status);
      return sessions;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to fetch mentorship sessions');
    }
  }
);

export const fetchUpcomingSessions = createAsyncThunk(
  'incubator/fetchUpcomingSessions',
  async (_, { rejectWithValue }) => {
    try {
      const sessions = await mentorshipSessionsAPI.getUpcomingSessions();
      return sessions;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to fetch upcoming sessions');
    }
  }
);

export const fetchPastSessions = createAsyncThunk(
  'incubator/fetchPastSessions',
  async (_, { rejectWithValue }) => {
    try {
      const sessions = await mentorshipSessionsAPI.getPastSessions();
      return sessions;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to fetch past sessions');
    }
  }
);

export const fetchMentorshipSession = createAsyncThunk(
  'incubator/fetchMentorshipSession',
  async (id: number, { rejectWithValue }) => {
    try {
      const session = await mentorshipSessionsAPI.getMentorshipSession(id);
      return session;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to fetch mentorship session');
    }
  }
);

export const createMentorshipSession = createAsyncThunk(
  'incubator/createMentorshipSession',
  async (sessionData: Partial<MentorshipSession> & { mentorship_match: number }, { rejectWithValue }) => {
    try {
      const session = await mentorshipSessionsAPI.createMentorshipSession(sessionData);
      return session;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to create mentorship session');
    }
  }
);

export const updateMentorshipSession = createAsyncThunk(
  'incubator/updateMentorshipSession',
  async ({ id, action, data }: { id: number, action: string, data?: any }, { rejectWithValue }) => {
    try {
      let result;

      switch (action) {
        case 'start_session':
          result = await mentorshipSessionsAPI.startSession(id);
          break;
        case 'complete_session':
          result = await mentorshipSessionsAPI.completeSession(id);
          break;
        case 'cancel_session':
          result = await mentorshipSessionsAPI.cancelSession(id);
          break;
        case 'reschedule_session':
          if (!data?.scheduled_at) {
            throw new Error('scheduled_at is required for rescheduling');
          }
          result = await mentorshipSessionsAPI.rescheduleSession(id, data.scheduled_at);
          break;
        case 'create_meeting':
          if (!data?.video_provider) {
            throw new Error('video_provider is required for creating a meeting');
          }
          result = await mentorshipSessionsAPI.createMeeting(
            id,
            data.video_provider,
            data.meeting_link
          );
          break;
        default:
          // Regular update
          result = await mentorshipSessionsAPI.updateMentorshipSession(id, data);
      }

      return result.session || result;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to update mentorship session');
    }
  }
);

// Async thunks for Mentorship Feedback
export const fetchMentorshipFeedback = createAsyncThunk(
  'incubator/fetchMentorshipFeedback',
  async (sessionId: number | undefined, { rejectWithValue }) => {
    try {
      const feedback = await mentorshipFeedbackAPI.getMentorshipFeedback(sessionId);
      return feedback;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to fetch mentorship feedback');
    }
  }
);

export const fetchSessionFeedback = createAsyncThunk(
  'incubator/fetchSessionFeedback',
  async (sessionId: number, { rejectWithValue }) => {
    try {
      const feedback = await mentorshipFeedbackAPI.getSessionFeedback(sessionId);
      return feedback;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to fetch session feedback');
    }
  }
);

export const fetchMentorRatings = createAsyncThunk(
  'incubator/fetchMentorRatings',
  async (mentorId: number, { rejectWithValue }) => {
    try {
      const ratings = await mentorshipFeedbackAPI.getMentorRatings(mentorId);
      return ratings;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to fetch mentor ratings');
    }
  }
);

export const createMentorshipFeedback = createAsyncThunk(
  'incubator/createMentorshipFeedback',
  async (feedbackData: Partial<MentorshipFeedback> & { session: number, is_from_mentee: boolean }, { rejectWithValue }) => {
    try {
      const feedback = await mentorshipFeedbackAPI.createMentorshipFeedback(feedbackData);
      return feedback;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to create mentorship feedback');
    }
  }
);

export const updateMentorshipFeedback = createAsyncThunk(
  'incubator/updateMentorshipFeedback',
  async ({ id, feedbackData }: { id: number, feedbackData: Partial<MentorshipFeedback> }, { rejectWithValue }) => {
    try {
      const feedback = await mentorshipFeedbackAPI.updateMentorshipFeedback(id, feedbackData);
      return feedback;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to update mentorship feedback');
    }
  }
);

export const toggleFeedbackPrivacy = createAsyncThunk(
  'incubator/toggleFeedbackPrivacy',
  async (id: number, { rejectWithValue }) => {
    try {
      const feedback = await mentorshipFeedbackAPI.togglePrivacy(id);
      return feedback;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to toggle feedback privacy');
    }
  }
);

// Create the slice
const incubatorSlice = createSlice({
  name: 'incubator',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearSelectedBusinessIdea: (state) => {
      state.selectedBusinessIdea = null;
    },
    clearSelectedProgressUpdate: (state) => {
      state.selectedProgressUpdate = null;
    },
    clearSelectedResource: (state) => {
      state.selectedResource = null;
    },
    clearSelectedMentorshipApplication: (state) => {
      state.selectedMentorshipApplication = null;
    },
    clearSelectedInvestorProfile: (state) => {
      state.selectedInvestorProfile = null;
    },
    clearSelectedFundingOpportunity: (state) => {
      state.selectedFundingOpportunity = null;
    },
    clearSelectedFundingApplication: (state) => {
      state.selectedFundingApplication = null;
    },
    clearSelectedInvestment: (state) => {
      state.selectedInvestment = null;
    },
    clearSelectedMentorProfile: (state) => {
      state.selectedMentorProfile = null;
    },
    clearSelectedMentorExpertise: (state) => {
      state.selectedMentorExpertise = null;
    },
    clearSelectedMentorshipMatch: (state) => {
      state.selectedMentorshipMatch = null;
    },
    clearSelectedMentorshipSession: (state) => {
      state.selectedMentorshipSession = null;
    },
    clearSelectedMentorshipFeedback: (state) => {
      state.selectedMentorshipFeedback = null;
    },
  },
  extraReducers: (builder) => {
    // Business Ideas
    builder.addCase(fetchBusinessIdeas.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchBusinessIdeas.fulfilled, (state, action) => {
      state.isLoading = false;
      state.businessIdeas = action.payload;
    });
    builder.addCase(fetchBusinessIdeas.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    builder.addCase(fetchBusinessIdeaById.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchBusinessIdeaById.fulfilled, (state, action) => {
      state.isLoading = false;
      state.selectedBusinessIdea = action.payload;
    });
    builder.addCase(fetchBusinessIdeaById.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    builder.addCase(createBusinessIdea.fulfilled, (state, action) => {
      state.businessIdeas.push(action.payload);
    });

    builder.addCase(updateBusinessIdea.fulfilled, (state, action) => {
      const index = state.businessIdeas.findIndex(idea => idea.id === action.payload.id);
      if (index !== -1) {
        state.businessIdeas[index] = action.payload;
      }
      if (state.selectedBusinessIdea?.id === action.payload.id) {
        state.selectedBusinessIdea = action.payload;
      }
    });

    builder.addCase(deleteBusinessIdea.fulfilled, (state, action) => {
      state.businessIdeas = state.businessIdeas.filter(idea => idea.id !== action.payload);
      if (state.selectedBusinessIdea?.id === action.payload) {
        state.selectedBusinessIdea = null;
      }
    });

    // Progress Updates
    builder.addCase(fetchProgressUpdates.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchProgressUpdates.fulfilled, (state, action) => {
      state.isLoading = false;
      state.progressUpdates = action.payload;
    });
    builder.addCase(fetchProgressUpdates.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    builder.addCase(createProgressUpdate.fulfilled, (state, action) => {
      state.progressUpdates.push(action.payload);
    });

    // Incubator Resources
    builder.addCase(fetchIncubatorResources.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchIncubatorResources.fulfilled, (state, action) => {
      state.isLoading = false;
      state.resources = action.payload;
    });
    builder.addCase(fetchIncubatorResources.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    builder.addCase(createIncubatorResource.fulfilled, (state, action) => {
      state.resources.push(action.payload);
    });

    // Mentorship Applications
    builder.addCase(fetchMentorshipApplications.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchMentorshipApplications.fulfilled, (state, action) => {
      state.isLoading = false;
      state.mentorshipApplications = action.payload;
    });
    builder.addCase(fetchMentorshipApplications.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    builder.addCase(createMentorshipApplication.fulfilled, (state, action) => {
      state.mentorshipApplications.push(action.payload);
    });

    // Investor Profiles
    builder.addCase(fetchInvestorProfiles.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchInvestorProfiles.fulfilled, (state, action) => {
      state.isLoading = false;
      state.investorProfiles = action.payload;
    });
    builder.addCase(fetchInvestorProfiles.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    builder.addCase(fetchInvestorProfileById.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchInvestorProfileById.fulfilled, (state, action) => {
      state.isLoading = false;
      state.selectedInvestorProfile = action.payload;
    });
    builder.addCase(fetchInvestorProfileById.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    builder.addCase(createInvestorProfile.fulfilled, (state, action) => {
      state.investorProfiles.push(action.payload);
    });

    // Funding Opportunities
    builder.addCase(fetchFundingOpportunities.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchFundingOpportunities.fulfilled, (state, action) => {
      state.isLoading = false;
      state.fundingOpportunities = action.payload;
    });
    builder.addCase(fetchFundingOpportunities.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    builder.addCase(fetchFundingOpportunityById.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchFundingOpportunityById.fulfilled, (state, action) => {
      state.isLoading = false;
      state.selectedFundingOpportunity = action.payload;
    });
    builder.addCase(fetchFundingOpportunityById.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    builder.addCase(createFundingOpportunity.fulfilled, (state, action) => {
      state.fundingOpportunities.push(action.payload);
    });

    // Funding Applications
    builder.addCase(fetchFundingApplications.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchFundingApplications.fulfilled, (state, action) => {
      state.isLoading = false;
      state.fundingApplications = action.payload;
    });
    builder.addCase(fetchFundingApplications.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    builder.addCase(createFundingApplication.fulfilled, (state, action) => {
      state.fundingApplications.push(action.payload);
    });

    // Investments
    builder.addCase(fetchInvestments.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchInvestments.fulfilled, (state, action) => {
      state.isLoading = false;
      state.investments = action.payload;
    });
    builder.addCase(fetchInvestments.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    builder.addCase(createInvestment.fulfilled, (state, action) => {
      state.investments.push(action.payload);
    });

    // Mentor Profiles
    builder.addCase(fetchMentorProfiles.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchMentorProfiles.fulfilled, (state, action) => {
      state.isLoading = false;
      state.mentorProfiles = action.payload;
    });
    builder.addCase(fetchMentorProfiles.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    builder.addCase(fetchMentorProfileById.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchMentorProfileById.fulfilled, (state, action) => {
      state.isLoading = false;
      state.selectedMentorProfile = action.payload;
    });
    builder.addCase(fetchMentorProfileById.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    builder.addCase(createMentorProfile.fulfilled, (state, action) => {
      state.mentorProfiles.push(action.payload);
    });

    // Mentor Expertise
    builder.addCase(fetchMentorExpertise.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchMentorExpertise.fulfilled, (state, action) => {
      state.isLoading = false;
      state.mentorExpertise = action.payload;
    });
    builder.addCase(fetchMentorExpertise.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    builder.addCase(createMentorExpertise.fulfilled, (state, action) => {
      state.mentorExpertise.push(action.payload);
    });

    // Mentorship Matches
    builder.addCase(fetchMentorshipMatches.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchMentorshipMatches.fulfilled, (state, action) => {
      state.isLoading = false;
      state.mentorshipMatches = action.payload;
    });
    builder.addCase(fetchMentorshipMatches.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    builder.addCase(fetchMentorshipMatchById.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchMentorshipMatchById.fulfilled, (state, action) => {
      state.isLoading = false;
      state.selectedMentorshipMatch = action.payload;
    });
    builder.addCase(fetchMentorshipMatchById.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    builder.addCase(createMentorshipMatch.fulfilled, (state, action) => {
      state.mentorshipMatches.push(action.payload);
    });

    // Mentorship Sessions
    builder.addCase(fetchMentorshipSessions.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchMentorshipSessions.fulfilled, (state, action) => {
      state.isLoading = false;
      state.mentorshipSessions = action.payload;
    });
    builder.addCase(fetchMentorshipSessions.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    builder.addCase(fetchUpcomingSessions.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchUpcomingSessions.fulfilled, (state, action) => {
      state.isLoading = false;
      // Don't replace all sessions, just add these to the existing ones
      const existingIds = new Set(state.mentorshipSessions.map(s => s.id));
      const newSessions = action.payload.filter(s => !existingIds.has(s.id));
      state.mentorshipSessions = [...state.mentorshipSessions, ...newSessions];
    });
    builder.addCase(fetchUpcomingSessions.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    builder.addCase(fetchPastSessions.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchPastSessions.fulfilled, (state, action) => {
      state.isLoading = false;
      // Don't replace all sessions, just add these to the existing ones
      const existingIds = new Set(state.mentorshipSessions.map(s => s.id));
      const newSessions = action.payload.filter(s => !existingIds.has(s.id));
      state.mentorshipSessions = [...state.mentorshipSessions, ...newSessions];
    });
    builder.addCase(fetchPastSessions.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    builder.addCase(fetchMentorshipSession.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchMentorshipSession.fulfilled, (state, action) => {
      state.isLoading = false;
      state.selectedMentorshipSession = action.payload;

      // Also update the session in the sessions array if it exists
      const index = state.mentorshipSessions.findIndex(s => s.id === action.payload.id);
      if (index !== -1) {
        state.mentorshipSessions[index] = action.payload;
      } else {
        state.mentorshipSessions.push(action.payload);
      }
    });
    builder.addCase(fetchMentorshipSession.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    builder.addCase(createMentorshipSession.fulfilled, (state, action) => {
      state.mentorshipSessions.push(action.payload);
    });

    builder.addCase(updateMentorshipSession.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(updateMentorshipSession.fulfilled, (state, action) => {
      state.isLoading = false;

      // Update in sessions array
      const index = state.mentorshipSessions.findIndex(s => s.id === action.payload.id);
      if (index !== -1) {
        state.mentorshipSessions[index] = action.payload;
      }

      // Update selected session if it's the same one
      if (state.selectedMentorshipSession?.id === action.payload.id) {
        state.selectedMentorshipSession = action.payload;
      }
    });
    builder.addCase(updateMentorshipSession.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    // Mentorship Feedback
    builder.addCase(fetchMentorshipFeedback.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchMentorshipFeedback.fulfilled, (state, action) => {
      state.isLoading = false;
      state.mentorshipFeedback = action.payload;
    });
    builder.addCase(fetchMentorshipFeedback.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    builder.addCase(fetchSessionFeedback.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchSessionFeedback.fulfilled, (state, action) => {
      state.isLoading = false;

      // Don't replace all feedback, just add these to the existing ones
      const existingIds = new Set(state.mentorshipFeedback.map(f => f.id));
      const newFeedback = action.payload.filter(f => !existingIds.has(f.id));
      state.mentorshipFeedback = [...state.mentorshipFeedback, ...newFeedback];
    });
    builder.addCase(fetchSessionFeedback.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    builder.addCase(fetchMentorRatings.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchMentorRatings.fulfilled, (state, action) => {
      state.isLoading = false;

      // Add the recent feedback to our feedback array if not already there
      if (action.payload.recent_feedback && action.payload.recent_feedback.length > 0) {
        const existingIds = new Set(state.mentorshipFeedback.map(f => f.id));
        const newFeedback = action.payload.recent_feedback.filter(f => !existingIds.has(f.id));
        state.mentorshipFeedback = [...state.mentorshipFeedback, ...newFeedback];
      }
    });
    builder.addCase(fetchMentorRatings.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    builder.addCase(createMentorshipFeedback.fulfilled, (state, action) => {
      state.mentorshipFeedback.push(action.payload);

      // If this feedback is for the selected session, update the session's feedback array
      if (state.selectedMentorshipSession && action.payload.session === state.selectedMentorshipSession.id) {
        state.selectedMentorshipSession.feedback.push(action.payload);
      }
    });

    builder.addCase(updateMentorshipFeedback.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(updateMentorshipFeedback.fulfilled, (state, action) => {
      state.isLoading = false;

      // Update in feedback array
      const index = state.mentorshipFeedback.findIndex(f => f.id === action.payload.id);
      if (index !== -1) {
        state.mentorshipFeedback[index] = action.payload;
      }

      // Update in selected session's feedback array if applicable
      if (state.selectedMentorshipSession) {
        const feedbackIndex = state.selectedMentorshipSession.feedback.findIndex(f => f.id === action.payload.id);
        if (feedbackIndex !== -1) {
          state.selectedMentorshipSession.feedback[feedbackIndex] = action.payload;
        }
      }

      // Update selected feedback if it's the same one
      if (state.selectedMentorshipFeedback?.id === action.payload.id) {
        state.selectedMentorshipFeedback = action.payload;
      }
    });
    builder.addCase(updateMentorshipFeedback.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    builder.addCase(toggleFeedbackPrivacy.fulfilled, (state, action) => {
      // Update in feedback array
      const index = state.mentorshipFeedback.findIndex(f => f.id === action.payload.id);
      if (index !== -1) {
        state.mentorshipFeedback[index] = action.payload;
      }

      // Update in selected session's feedback array if applicable
      if (state.selectedMentorshipSession) {
        const feedbackIndex = state.selectedMentorshipSession.feedback.findIndex(f => f.id === action.payload.id);
        if (feedbackIndex !== -1) {
          state.selectedMentorshipSession.feedback[feedbackIndex] = action.payload;
        }
      }

      // Update selected feedback if it's the same one
      if (state.selectedMentorshipFeedback?.id === action.payload.id) {
        state.selectedMentorshipFeedback = action.payload;
      }
    });
  },
});

export const {
  clearError,
  clearSelectedBusinessIdea,
  clearSelectedProgressUpdate,
  clearSelectedResource,
  clearSelectedMentorshipApplication,
  clearSelectedInvestorProfile,
  clearSelectedFundingOpportunity,
  clearSelectedFundingApplication,
  clearSelectedInvestment,
  clearSelectedMentorProfile,
  clearSelectedMentorExpertise,
  clearSelectedMentorshipMatch,
  clearSelectedMentorshipSession,
  clearSelectedMentorshipFeedback
} = incubatorSlice.actions;

export default incubatorSlice.reducer;
