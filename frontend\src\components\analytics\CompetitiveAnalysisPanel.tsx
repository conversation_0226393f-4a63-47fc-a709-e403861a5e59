import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  BarChart2,
  RefreshCw,
  ChevronDown,
  ChevronUp,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Target,
  Users,
  Award,
  Shield,
  Zap
} from 'lucide-react';
import {
  CompetitiveAnalysis,
  MarketTrendsAnalysis,
  competitiveAnalysisAPI
} from '../../services/competitiveAnalysisApi';

interface CompetitiveAnalysisPanelProps {
  businessIdeaId: number;
  businessIdeaTitle?: string;
}

const CompetitiveAnalysisPanel: React.FC<CompetitiveAnalysisPanelProps> = ({
  businessIdeaId,
  businessIdeaTitle
}) => {
  const { t } = useTranslation();
  const [competitiveAnalysis, setCompetitiveAnalysis] = useState<CompetitiveAnalysis | null>(null);
  const [marketTrends, setMarketTrends] = useState<MarketTrendsAnalysis | null>(null);
  const [loading, setLoading] = useState(false);
  const [trendsLoading, setTrendsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [trendsError, setTrendsError] = useState<string | null>(null);
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    'Market Overview': true,
    'Competitor Analysis': false,
    'Competitive Advantages': true,
    'Competitive Disadvantages': true,
    'Strategic Recommendations': false
  });
  const [expandedTrendsSections, setExpandedTrendsSections] = useState<Record<string, boolean>>({
    'Current Market Trends': true,
    'Emerging Trends': true,
    'Trend Impact Analysis': false,
    'Strategic Recommendations': false
  });

  useEffect(() => {
    if (businessIdeaId) {
      fetchCompetitiveAnalysis();
    }
  }, [businessIdeaId]);

  const fetchCompetitiveAnalysis = async () => {
    setLoading(true);
    setError(null);

    try {
      const data = await competitiveAnalysisAPI.getCompetitiveAnalysis(businessIdeaId);
      setCompetitiveAnalysis(data);
    } catch (err) {
      console.error('Error fetching competitive analysis:', err);
      setError(t('incubator.analytics.competitive.failedToLoadCompetitive'));
    } finally {
      setLoading(false);
    }
  };

  const generateCompetitiveAnalysis = async () => {
    setLoading(true);
    setError(null);

    try {
      const data = await competitiveAnalysisAPI.generateCompetitiveAnalysis(businessIdeaId);
      setCompetitiveAnalysis(data);
    } catch (err) {
      console.error('Error generating competitive analysis:', err);
      setError(t('incubator.analytics.competitive.failedToGenerateCompetitive'));
    } finally {
      setLoading(false);
    }
  };

  const fetchMarketTrends = async () => {
    if (marketTrends) return; // Don't fetch if we already have data

    setTrendsLoading(true);
    setTrendsError(null);

    try {
      const data = await competitiveAnalysisAPI.getMarketTrendsAnalysis(businessIdeaId);
      setMarketTrends(data);
    } catch (err) {
      console.error('Error fetching market trends:', err);
      setTrendsError(t('incubator.analytics.competitive.failedToLoadTrends'));
    } finally {
      setTrendsLoading(false);
    }
  };

  const generateMarketTrends = async () => {
    setTrendsLoading(true);
    setTrendsError(null);

    try {
      const data = await competitiveAnalysisAPI.generateMarketTrendsAnalysis(businessIdeaId);
      setMarketTrends(data);
    } catch (err) {
      console.error('Error generating market trends:', err);
      setTrendsError(t('incubator.analytics.competitive.failedToGenerateTrends'));
    } finally {
      setTrendsLoading(false);
    }
  };

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const toggleTrendsSection = (section: string) => {
    setExpandedTrendsSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const renderAnalysisSection = (title: string, content: any, icon: React.ReactNode) => {
    if (!content) return null;

    return (
      <div className="mb-4 bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
        <div
          className="flex justify-between items-center p-3 bg-gray-700 cursor-pointer"
          onClick={() => toggleSection(title)}
        >
          <div className="flex items-center">
            {icon}
            <h3 className="font-medium ml-2">{title}</h3>
          </div>
          {expandedSections[title] ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
        </div>

        {expandedSections[title] && (
          <div className="p-4">
            {typeof content === 'object' ? (
              Object.entries(content).map(([key, value]) => (
                <div key={key} className="mb-3">
                  <h4 className="text-sm font-medium text-purple-300 mb-1">
                    {key.replace(/_/g, ' ')}
                  </h4>
                  <div className="text-sm text-gray-300">
                    {typeof value === 'string' ? (
                      <div>{value}</div>
                    ) : Array.isArray(value) ? (
                      <ul className="list-disc list-inside">
                        {value.map((item, i) => (
                          <li key={i} className="mb-1">{item}</li>
                        ))}
                      </ul>
                    ) : typeof value === 'object' ? (
                      Object.entries(value as object).map(([subKey, subValue]) => (
                        <div key={subKey} className="mb-2">
                          <h5 className="text-xs font-medium text-gray-400 mb-1">
                            {subKey.replace(/_/g, ' ')}
                          </h5>
                          {typeof subValue === 'string' ? (
                            <div>{subValue}</div>
                          ) : Array.isArray(subValue) ? (
                            <ul className="list-disc list-inside">
                              {subValue.map((item, i) => (
                                <li key={i} className="mb-1">{item}</li>
                              ))}
                            </ul>
                          ) : (
                            <p>{JSON.stringify(subValue)}</p>
                          )}
                        </div>
                      ))
                    ) : (
                      <p>{JSON.stringify(value)}</p>
                    )}
                  </div>
                </div>
              ))
            ) : (
              <div className="text-sm text-gray-300">{content}</div>
            )}
          </div>
        )}
      </div>
    );
  };

  const renderTrendsSection = (title: string, content: any, icon: React.ReactNode) => {
    if (!content) return null;

    return (
      <div className="mb-4 bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
        <div
          className="flex justify-between items-center p-3 bg-gray-700 cursor-pointer"
          onClick={() => toggleTrendsSection(title)}
        >
          <div className="flex items-center">
            {icon}
            <h3 className="font-medium ml-2">{title}</h3>
          </div>
          {expandedTrendsSections[title] ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
        </div>

        {expandedTrendsSections[title] && (
          <div className="p-4">
            {typeof content === 'object' ? (
              Object.entries(content).map(([key, value]) => (
                <div key={key} className="mb-3">
                  <h4 className="text-sm font-medium text-purple-300 mb-1">
                    {key.replace(/_/g, ' ')}
                  </h4>
                  <div className="text-sm text-gray-300">
                    {typeof value === 'string' ? (
                      <div>{value}</div>
                    ) : Array.isArray(value) ? (
                      <ul className="list-disc list-inside">
                        {value.map((item, i) => (
                          <li key={i} className="mb-1">{item}</li>
                        ))}
                      </ul>
                    ) : typeof value === 'object' ? (
                      Object.entries(value as object).map(([subKey, subValue]) => (
                        <div key={subKey} className="mb-2">
                          <h5 className="text-xs font-medium text-gray-400 mb-1">
                            {subKey.replace(/_/g, ' ')}
                          </h5>
                          {typeof subValue === 'string' ? (
                            <div>{subValue}</div>
                          ) : Array.isArray(subValue) ? (
                            <ul className="list-disc list-inside">
                              {subValue.map((item, i) => (
                                <li key={i} className="mb-1">{item}</li>
                              ))}
                            </ul>
                          ) : (
                            <p>{JSON.stringify(subValue)}</p>
                          )}
                        </div>
                      ))
                    ) : (
                      <p>{JSON.stringify(value)}</p>
                    )}
                  </div>
                </div>
              ))
            ) : (
              <div className="text-sm text-gray-300">{content}</div>
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="bg-gray-900 rounded-lg border border-gray-800 p-4">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold flex items-center">
          <BarChart2 size={20} className="mr-2 text-purple-400" />
          {t('incubator.analytics.competitive.title')}
        </h2>
        <button
          onClick={generateCompetitiveAnalysis}
          disabled={loading}
          className="px-3 py-1 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-800/50 disabled:cursor-not-allowed rounded-md text-white text-sm flex items-center"
        >
          {loading ? (
            <RefreshCw size={14} className="mr-1 animate-spin" />
          ) : (
            <RefreshCw size={14} className="mr-1" />
          )}
          {loading ? t('incubator.analytics.competitive.generating') : t('incubator.analytics.competitive.refreshAnalysis')}
        </button>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-900/50 border border-red-800 rounded-md text-red-200 text-sm">
          {error}
        </div>
      )}

      {competitiveAnalysis?.analysis && (
        <div className="mb-6">
          {renderAnalysisSection(t('incubator.analytics.competitive.marketOverview'), competitiveAnalysis.analysis['Market Overview'], <Target size={16} className="text-blue-400" />)}
          {renderAnalysisSection(t('incubator.analytics.competitive.competitorAnalysis'), competitiveAnalysis.analysis['Competitor Analysis'], <Users size={16} className="text-yellow-400" />)}
          {renderAnalysisSection(t('incubator.analytics.competitive.competitiveAdvantages'), competitiveAnalysis.analysis['Competitive Advantages'], <Award size={16} className="text-green-400" />)}
          {renderAnalysisSection(t('incubator.analytics.competitive.competitiveDisadvantages'), competitiveAnalysis.analysis['Competitive Disadvantages'], <AlertTriangle size={16} className="text-red-400" />)}
          {renderAnalysisSection(t('incubator.analytics.competitive.strategicRecommendations'), competitiveAnalysis.analysis['Strategic Recommendations'], <Zap size={16} className="text-purple-400" />)}
        </div>
      )}

      {!competitiveAnalysis?.analysis && !loading && (
        <div className="mb-6 p-4 bg-gray-800 rounded-lg border border-gray-700 text-center">
          <div className="text-gray-400 mb-2">{t('incubator.analytics.competitive.noAnalysisAvailable')}</div>
          <button
            onClick={generateCompetitiveAnalysis}
            className="px-3 py-1 bg-purple-600 hover:bg-purple-700 rounded-md text-white text-sm"
          >
            {t('incubator.analytics.competitive.generateAnalysis')}
          </button>
        </div>
      )}

      <div className="mt-8">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold flex items-center">
            <TrendingUp size={20} className="mr-2 text-indigo-400" />
            {t('incubator.analytics.competitive.marketTrendsAnalysis')}
          </h2>
          <button
            onClick={marketTrends ? generateMarketTrends : fetchMarketTrends}
            disabled={trendsLoading}
            className="px-3 py-1 bg-indigo-600 hover:bg-indigo-700 disabled:bg-indigo-800/50 disabled:cursor-not-allowed rounded-md text-white text-sm flex items-center"
          >
            {trendsLoading ? (
              <RefreshCw size={14} className="mr-1 animate-spin" />
            ) : (
              <RefreshCw size={14} className="mr-1" />
            )}
            {trendsLoading ? t('incubator.analytics.competitive.generating') : marketTrends ? t('incubator.analytics.competitive.refreshTrends') : t('incubator.analytics.competitive.loadTrends')}
          </button>
        </div>

        {trendsError && (
          <div className="mb-4 p-3 bg-red-900/50 border border-red-800 rounded-md text-red-200 text-sm">
            {trendsError}
          </div>
        )}

        {marketTrends?.market_trends && (
          <div>
            {renderTrendsSection(t('incubator.analytics.competitive.currentMarketTrends'), marketTrends.market_trends['Current Market Trends'], <TrendingUp size={16} className="text-blue-400" />)}
            {renderTrendsSection(t('incubator.analytics.competitive.emergingTrends'), marketTrends.market_trends['Emerging Trends'], <Zap size={16} className="text-yellow-400" />)}
            {renderTrendsSection(t('incubator.analytics.competitive.trendImpactAnalysis'), marketTrends.market_trends['Trend Impact Analysis'], <BarChart2 size={16} className="text-purple-400" />)}
            {renderTrendsSection(t('incubator.analytics.competitive.strategicRecommendations'), marketTrends.market_trends['Strategic Recommendations'], <Shield size={16} className="text-green-400" />)}
          </div>
        )}

        {!marketTrends && !trendsLoading && (
          <div className="p-4 bg-gray-800 rounded-lg border border-gray-700 text-center">
            <p className="text-gray-400 mb-2">{t('incubator.analytics.competitive.noTrendsAvailable')}</p>
            <button
              onClick={fetchMarketTrends}
              className="px-3 py-1 bg-indigo-600 hover:bg-indigo-700 rounded-md text-white text-sm"
            >
              {t('incubator.analytics.competitive.loadMarketTrends')}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default CompetitiveAnalysisPanel;
