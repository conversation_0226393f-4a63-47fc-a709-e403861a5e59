/**
 * Template Management Dashboard
 * Comprehensive dashboard for template management, analytics, and insights
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  LayoutDashboard,
  FileText,
  BarChart3,
  Users,
  Settings,
  Plus,
  Search,
  Filter,
  Download,
  RefreshCw,
  Bell,
  Sparkles,
  TrendingUp,
  Target,
  GitCompare
} from 'lucide-react';
import { RTLText, RTLFlex } from '../../components/common';
import { useLanguage } from '../../hooks/useLanguage';
// MainLayout removed - handled by routing system
import TemplateSelector from '../../components/templates/TemplateSelector';
import EnhancedTemplateAnalytics from '../../components/analytics/EnhancedTemplateAnalytics';
import TemplateRecommendationEngine from '../../components/templates/TemplateRecommendationEngine';
import TemplateComparisonTool from '../../components/templates/TemplateComparisonTool';
import TemplateUsagePatterns from '../../components/analytics/TemplateUsagePatterns';

interface DashboardStats {
  total_templates: number;
  active_templates: number;
  total_usage: number;
  completion_rate: number;
  user_satisfaction: number;
  growth_rate: number;
}

interface RecentActivity {
  id: string;
  type: 'template_created' | 'template_used' | 'template_completed' | 'template_rated';
  template_name: string;
  user_name: string;
  timestamp: string;
  details?: string;
}

const TemplateManagementDashboard: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [activeTab, setActiveTab] = useState<'overview' | 'templates' | 'analytics' | 'recommendations' | 'comparison' | 'patterns'>('overview');
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [notifications, setNotifications] = useState<string[]>([]);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      // Load real dashboard data from API
      const { templateAnalyticsAPI } = await import('../../services/templateAnalyticsApi');
      const dashboardData = await templateAnalyticsAPI.getDashboardData({
        timeRange: '30d'
      });

      setDashboardStats({
        total_templates: dashboardData.overview.total_templates,
        active_templates: dashboardData.overview.total_templates, // Assuming all templates are active
        total_usage: dashboardData.overview.total_usage,
        completion_rate: dashboardData.overview.completion_rate,
        user_satisfaction: dashboardData.overview.average_rating,
        growth_rate: dashboardData.overview.growth_rate
      });

      // Get recent activity from top templates data
      const recentActivityData: RecentActivity[] = dashboardData.top_templates.slice(0, 4).map((template, index) => ({
        id: template.id.toString(),
        type: index % 4 === 0 ? 'template_used' :
              index % 4 === 1 ? 'template_completed' :
              index % 4 === 2 ? 'template_rated' : 'template_created',
        template_name: template.name,
        user_name: t("common.user", "User"),
        timestamp: template.last_used || new Date().toISOString(),
        details: index % 4 === 0 ? t("dashboard.started.business.plan", "Started business plan") :
                index % 4 === 1 ? t("dashboard.completed.business.plan", "Completed business plan") :
                index % 4 === 2 ? `${t("dashboard.rated", "Rated")} ${template.average_rating} ${t("dashboard.stars", "stars")}` :
                t("dashboard.created.new.template", "Created new template")
      }));

      setRecentActivity(recentActivityData);

      // Generate notifications from real data
      const notificationsList = [];

      if (dashboardData.overview.growth_rate > 10) {
        notificationsList.push(t("dashboard.growth.notification", `Template usage increased by ${dashboardData.overview.growth_rate}%`));
      }

      if (dashboardData.overview.completion_rate > 70) {
        notificationsList.push(t("dashboard.completion.notification", `Template completion rate reached ${dashboardData.overview.completion_rate}%`));
      }

      if (dashboardData.overview.average_rating > 4) {
        notificationsList.push(t("dashboard.satisfaction.notification", `User satisfaction score reached ${dashboardData.overview.average_rating}/5`));
      }

      // Add trending template notification if available
      if (dashboardData.top_templates.length > 0) {
        const topTemplate = dashboardData.top_templates[0];
        notificationsList.push(t("dashboard.trending.notification", `Template '${topTemplate.name}' is trending`));
      }

      setNotifications(notificationsList);

    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'template_created': return <Plus size={16} className="text-green-400" />;
      case 'template_used': return <FileText size={16} className="text-blue-400" />;
      case 'template_completed': return <Target size={16} className="text-purple-400" />;
      case 'template_rated': return <TrendingUp size={16} className="text-yellow-400" />;
      default: return <FileText size={16} className="text-gray-400" />;
    }
  };

  const getActivityLabel = (type: string) => {
    switch (type) {
      case 'template_created': return t("dashboard.created", "Created");
      case 'template_used': return 'Used';
      case 'template_completed': return t("dashboard.completed", "Completed");
      case 'template_rated': return t("dashboard.rated.default.return", "Rated");
      default: return 'Activity';
    }
  };

  const tabs = [
    { id: 'overview', label: t("dashboard.overview", "Overview"), icon: LayoutDashboard },
    { id: 'templates', label: t("dashboard.templates", "Templates"), icon: FileText },
    { id: 'analytics', label: t("dashboard.analytics", "Analytics"), icon: BarChart3 },
    { id: 'recommendations', label: t("dashboard.ai.recommendations", "AI Recommendations"), icon: Sparkles },
    { id: 'comparison', label: t("dashboard.compare", "Compare"), icon: GitCompare },
    { id: 'patterns', label: t("dashboard.usage.patterns", "Usage Patterns"), icon: Users }
  ];

  if (loading) {
    return (
      <AuthenticatedLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">{t('Loading template dashboard...')}</p>
          </div>
        </div>
      </AuthenticatedLayout>
    );
  }

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center space-x-3 mb-2">
            <div className="p-2 bg-purple-100 rounded-lg">
              <FileText className="h-6 w-6 text-purple-600" />
            </div>
            <div>
              <RTLText as="h1" className="text-2xl font-bold text-white">
                Template Management
              </RTLText>
              <p className="text-gray-300">
                Manage templates, analyze performance, and optimize user experience
              </p>
            </div>
          </div>

          <RTLFlex className="items-center space-x-4 mt-4">
            {/* Notifications */}
            <div className="relative">
              <button className="p-2 bg-white/20 hover:bg-white/30 rounded-lg transition-colors relative">
                <Bell size={20} className="text-white" />
                {notifications.length > 0 && (
                  <span className={`absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    {notifications.length}
                  </span>
                )}
              </button>
            </div>

            {/* Quick Actions */}
            <button className={`px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <Plus size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
              New Template
            </button>
          </RTLFlex>
        </div>

        {/* Navigation Tabs */}
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 mb-6 border border-white/20">
          <div className={`flex space-x-4 overflow-x-auto ${isRTL ? "flex-row-reverse" : ""}`}>
            {tabs.map(tab => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors whitespace-nowrap ${
                    activeTab === tab.id
                      ? 'bg-purple-600 text-white'
                      : 'text-gray-300 hover:text-white hover:bg-white/10'}
                  }`}
                >
                  <Icon size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                  {tab.label}
                </button>
              );
            })}
          </div>
        </div>

        {/* Main Content */}
        <div>
          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <div className="space-y-8">
              {/* Stats Cards */}
              {dashboardStats && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
                  <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                    <div className="text-2xl font-bold text-blue-400 mb-1">
                      {dashboardStats.total_templates}
                    </div>
                    <div className="text-sm text-gray-400">{t("dashboard.total.templates", "Total Templates")}</div>
                  </div>

                  <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                    <div className="text-2xl font-bold text-green-400 mb-1">
                      {dashboardStats.active_templates}
                    </div>
                    <div className="text-sm text-gray-400">{t("dashboard.active.templates", "Active Templates")}</div>
                  </div>

                  <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                    <div className="text-2xl font-bold text-purple-400 mb-1">
                      {dashboardStats.total_usage.toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-400">{t("dashboard.total.usage", "Total Usage")}</div>
                  </div>

                  <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                    <div className="text-2xl font-bold text-yellow-400 mb-1">
                      {dashboardStats.completion_rate}%
                    </div>
                    <div className="text-sm text-gray-400">{t("dashboard.completion.rate", "Completion Rate")}</div>
                  </div>

                  <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                    <div className="text-2xl font-bold text-orange-400 mb-1">
                      {dashboardStats.user_satisfaction}/5
                    </div>
                    <div className="text-sm text-gray-400">{t("dashboard.user.satisfaction", "User Satisfaction")}</div>
                  </div>

                  <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                    <div className="text-2xl font-bold text-red-400 mb-1">
                      +{dashboardStats.growth_rate}%
                    </div>
                    <div className="text-sm text-gray-400">{t("dashboard.growth.rate", "Growth Rate")}</div>
                  </div>
                </div>
              )}

              {/* Recent Activity */}
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                <RTLText as="h3" className="text-lg font-semibold mb-4 text-white">
                  Recent Activity
                </RTLText>
                <div className="space-y-4">
                  {recentActivity.map(activity => (
                    <div key={activity.id} className={`flex items-center justify-between p-3 bg-white/5 rounded-lg ${isRTL ? "flex-row-reverse" : ""}`}>
                      <RTLFlex className="items-center">
                        {getActivityIcon(activity.type)}
                        <div className={`ml-3 ${isRTL ? "space-x-reverse" : ""}`}>
                          <div className="text-sm font-medium text-white">
                            {getActivityLabel(activity.type)} "{activity.template_name}"
                          </div>
                          <div className="text-xs text-gray-400">
                            by {activity.user_name} • {new Date(activity.timestamp).toLocaleString()}
                          </div>
                          {activity.details && (
                            <div className="text-xs text-gray-500 mt-1">{activity.details}</div>
                          )}
                        </div>
                      </RTLFlex>
                    </div>
                  ))}
                </div>
              </div>

            {/* Quick Recommendations */}
            <TemplateRecommendationEngine
              onSelectTemplate={(id) => console.log("Selected template:", id)}
              maxRecommendations={3}
            />
          </div>
        )}

        {/* Templates Tab */}
        {activeTab === 'templates' && (
          <TemplateSelector
            onSelectTemplate={(id) => console.log("Selected template:", id)}
            showCategories={true}
            showFilters={true}
            showActionButtons={true}
            onPreviewTemplate={(id) => console.log(t("dashboard.preview.template", "Preview template:"), id)}
            onUseTemplate={(id) => console.log(t("dashboard.use.template", "Use template:"), id)}
            onCreateTemplate={() => console.log(t("dashboard.create.new.template", "Create new template"))}
          />
        )}

        {/* Analytics Tab */}
        {activeTab === 'analytics' && (
          <EnhancedTemplateAnalytics
            showFilters={true}
            showExport={true}
          />
        )}

        {/* Recommendations Tab */}
        {activeTab === 'recommendations' && (
          <TemplateRecommendationEngine
            onSelectTemplate={(id) => console.log(t("dashboard.selected.template", "Selected template:"), id)}
            onViewAll={() => setActiveTab('templates')}
            maxRecommendations={9}
          />
        )}

        {/* Comparison Tab */}
        {activeTab === 'comparison' && (
          <TemplateComparisonTool
            templates={[]} // Would be populated with actual template data
            onSelectTemplate={(id) => console.log(t("dashboard.selected.template", "Selected template:"), id)}
            onPreviewTemplate={(id) => console.log(t("dashboard.preview.template", "Preview template:"), id)}
            maxComparisons={3}
          />
        )}

        {/* Usage Patterns Tab */}
        {activeTab === 'patterns' && (
          <TemplateUsagePatterns
            timeRange="30d"
            showExport={true}
          />
          )}
        </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TemplateManagementDashboard;
