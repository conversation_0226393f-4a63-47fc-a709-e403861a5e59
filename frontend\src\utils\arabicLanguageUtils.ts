/**
 * Arabic Language Utilities
 * Enhanced utilities for better Arabic language support
 */

// Arabic text processing utilities
export const arabicUtils = {
  // Check if text contains Arabic characters
  isArabic: (text: string): boolean => {
    const arabicRegex = /[\u0600-\u06FF\u0750-\u077F]/;
    return arabicRegex.test(text);
  },

  // Check if text is primarily Arabic (>50% Arabic characters)
  isPrimarilyArabic: (text: string): boolean => {
    if (!text) return false;
    
    const arabicChars = text.match(/[\u0600-\u06FF\u0750-\u077F]/g) || [];
    const totalChars = text.replace(/\s/g, '').length;
    
    return totalChars > 0 && (arabicChars.length / totalChars) > 0.5;
  },

  // Clean and normalize Arabic text
  normalizeArabic: (text: string): string => {
    return text
      // Normalize Arabic characters
      .replace(/ي/g, 'ی') // Normalize Yeh
      .replace(/ك/g, 'ک') // Normalize Kaf
      // Remove diacritics for search
      .replace(/[\u064B-\u0652]/g, '')
      // Normalize spaces
      .replace(/\s+/g, ' ')
      .trim();
  },

  // Format Arabic numbers
  formatArabicNumbers: (text: string, useArabicNumerals: boolean = true): string => {
    if (!useArabicNumerals) return text;
    
    const arabicNumerals = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    
    return text.replace(/[0-9]/g, (digit) => arabicNumerals[parseInt(digit)]);
  },

  // Convert Arabic numerals to English
  arabicToEnglishNumbers: (text: string): string => {
    const arabicNumerals = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    
    return text.replace(/[٠-٩]/g, (digit) => {
      return arabicNumerals.indexOf(digit).toString();
    });
  },

  // Get text direction based on content
  getTextDirection: (text: string): 'ltr' | 'rtl' => {
    return arabicUtils.isPrimarilyArabic(text) ? 'rtl' : 'ltr';
  },

  // Format mixed content (Arabic + English)
  formatMixedContent: (text: string): string => {
    // Add proper spacing around English words in Arabic text
    return text.replace(/([a-zA-Z]+)/g, ' $1 ').replace(/\s+/g, ' ').trim();
  },

  // Validate Arabic input
  validateArabicInput: (text: string): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];
    
    // Check for common issues
    if (text.includes('ي') && text.includes('ی')) {
      errors.push('Mixed Yeh characters detected');
    }
    
    if (text.includes('ك') && text.includes('ک')) {
      errors.push('Mixed Kaf characters detected');
    }
    
    // Check for proper RTL marks
    const hasRTLText = arabicUtils.isArabic(text);
    const hasLTRText = /[a-zA-Z]/.test(text);
    
    if (hasRTLText && hasLTRText && !text.includes('\u202E') && !text.includes('\u202D')) {
      errors.push('Mixed direction text without proper directional marks');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
};

// Arabic-specific React hooks
export const useArabicText = (text: string) => {
  const isArabic = arabicUtils.isArabic(text);
  const direction = arabicUtils.getTextDirection(text);
  const normalized = arabicUtils.normalizeArabic(text);
  
  return {
    isArabic,
    direction,
    normalized,
    className: `text-${direction}`,
    style: { direction }
  };
};

// Arabic form validation
export const arabicValidation = {
  // Validate Arabic name
  validateArabicName: (name: string): boolean => {
    const arabicNameRegex = /^[\u0600-\u06FF\u0750-\u077F\s]+$/;
    return arabicNameRegex.test(name.trim());
  },

  // Validate mixed Arabic/English text
  validateMixedText: (text: string): boolean => {
    // Allow Arabic, English, numbers, and common punctuation
    const mixedTextRegex = /^[\u0600-\u06FF\u0750-\u077F\u0660-\u0669a-zA-Z0-9\s.,!?؟،]+$/;
    return mixedTextRegex.test(text);
  },

  // Validate Arabic business description
  validateBusinessDescription: (description: string): { isValid: boolean; message: string } => {
    if (description.length < 10) {
      return { isValid: false, message: 'الوصف قصير جداً' };
    }
    
    if (description.length > 1000) {
      return { isValid: false, message: 'الوصف طويل جداً' };
    }
    
    if (!arabicValidation.validateMixedText(description)) {
      return { isValid: false, message: 'يحتوي الوصف على رموز غير مسموحة' };
    }
    
    return { isValid: true, message: '' };
  }
};

// Arabic content formatting
export const arabicFormatting = {
  // Format Arabic date
  formatArabicDate: (date: Date, useArabicNumerals: boolean = true): string => {
    const arabicMonths = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    
    const day = date.getDate();
    const month = arabicMonths[date.getMonth()];
    const year = date.getFullYear();
    
    let formatted = `${day} ${month} ${year}`;
    
    if (useArabicNumerals) {
      formatted = arabicUtils.formatArabicNumbers(formatted);
    }
    
    return formatted;
  },

  // Format Arabic currency
  formatArabicCurrency: (amount: number, currency: string = 'ريال'): string => {
    const formatted = new Intl.NumberFormat('ar-SA').format(amount);
    return `${formatted} ${currency}`;
  },

  // Format Arabic percentage
  formatArabicPercentage: (value: number): string => {
    const formatted = new Intl.NumberFormat('ar-SA', {
      style: 'percent',
      minimumFractionDigits: 1,
      maximumFractionDigits: 1
    }).format(value / 100);
    
    return formatted;
  }
};

// Arabic search utilities
export const arabicSearch = {
  // Prepare Arabic text for search
  prepareForSearch: (text: string): string => {
    return arabicUtils.normalizeArabic(text)
      .toLowerCase()
      // Remove common Arabic stop words
      .replace(/\b(في|من|إلى|على|عن|مع|هذا|هذه|ذلك|تلك|التي|الذي)\b/g, '')
      .replace(/\s+/g, ' ')
      .trim();
  },

  // Calculate Arabic text similarity
  calculateSimilarity: (text1: string, text2: string): number => {
    const prepared1 = arabicSearch.prepareForSearch(text1);
    const prepared2 = arabicSearch.prepareForSearch(text2);
    
    const words1 = prepared1.split(' ');
    const words2 = prepared2.split(' ');
    
    const commonWords = words1.filter(word => words2.includes(word));
    const totalWords = new Set([...words1, ...words2]).size;
    
    return totalWords > 0 ? commonWords.length / totalWords : 0;
  },

  // Extract Arabic keywords
  extractKeywords: (text: string, maxKeywords: number = 10): string[] => {
    const prepared = arabicSearch.prepareForSearch(text);
    const words = prepared.split(' ').filter(word => word.length > 2);
    
    // Count word frequency
    const frequency: { [key: string]: number } = {};
    words.forEach(word => {
      frequency[word] = (frequency[word] || 0) + 1;
    });
    
    // Sort by frequency and return top keywords
    return Object.entries(frequency)
      .sort(([, a], [, b]) => b - a)
      .slice(0, maxKeywords)
      .map(([word]) => word);
  }
};

// Export all utilities
export default {
  arabicUtils,
  useArabicText,
  arabicValidation,
  arabicFormatting,
  arabicSearch
};
