import React from 'react';
import { useLanguage } from '../../hooks/useLanguage';
import { useTranslation } from 'react-i18next';

interface LoadingFallbackProps {
  message?: string;
}

/**
 * Loading fallback component for React.Suspense
 * Used when lazy-loaded components are being loaded
 */
const LoadingFallback: React.FC<LoadingFallbackProps> = ({ message }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  return (
    <div className={`flex flex-col items-center justify-center  p-6 ${isRTL ? "flex-row-reverse" : ""}`}>
      <div className="w-12 h-12 rounded-full border-4 border-indigo-800 border-t-indigo-400 animate-spin mb-4" />
      <p className="text-indigo-300">
        {message || t('common.loading', 'Loading...')}
      </p>
    </div>
  );
};

export default LoadingFallback;
