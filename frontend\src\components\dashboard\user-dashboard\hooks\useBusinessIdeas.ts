import { useState, useEffect } from 'react';
import { useAppSelector } from '../../../../store/hooks';
import { BusinessIdea, businessIdeasAPI } from '../../../../services/incubatorApi';

interface UseBusinessIdeasResult {
  businessIdeas: BusinessIdea[];
  loading: boolean;
  error: string | null;
  stats: {
    totalIdeas: number;
    approvedIdeas: number;
    pendingIdeas: number;
    rejectedIdeas: number;
  };
  latestIdea: BusinessIdea | null;
}

export const useBusinessIdeas = (): UseBusinessIdeasResult => {
  const { user } = useAppSelector((state) => state.auth);
  const [businessIdeas, setBusinessIdeas] = useState<BusinessIdea[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBusinessIdeas = async () => {
      if (!user) return;

      setLoading(true);
      try {
        // Fetch user's business ideas
        const ideas = await businessIdeasAPI.getBusinessIdeas();
        const userIdeas = ideas.filter(idea => idea.owner.id === user.id);
        setBusinessIdeas(userIdeas);
        setError(null);
      } catch (err) {
        console.error('Error fetching business ideas:', err);
        setError('Failed to load business ideas. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchBusinessIdeas();
  }, [user]);

  // Calculate statistics
  const stats = {
    totalIdeas: businessIdeas.length,
    approvedIdeas: businessIdeas.filter(idea => idea.moderation_status === 'approved').length,
    pendingIdeas: businessIdeas.filter(idea => idea.moderation_status === 'pending').length,
    rejectedIdeas: businessIdeas.filter(idea => idea.moderation_status === 'rejected').length,
  };

  // Get the latest business idea
  const latestIdea = businessIdeas.length > 0
    ? businessIdeas.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())[0]
    : null;

  return {
    businessIdeas,
    loading,
    error,
    stats,
    latestIdea
  };
};

export default useBusinessIdeas;
