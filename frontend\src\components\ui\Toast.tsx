import React, { useState, useEffect, createContext, useContext } from 'react';
import { X, CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-react';
import { createPortal } from 'react-dom';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';



// Toast types
export type ToastType = 'success' | 'error' | 'info' | 'warning';

// Toast interface
export interface ToastMessage {
  id: string;
  type: ToastType;
  message: string;
  duration?: number;
}

// Context for toast state
interface ToastContextType {
  toasts: ToastMessage[];
  addToast: (toast: Omit<ToastMessage, 'id'>) => void;
  removeToast: (id: string) => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

// Toast provider component
export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [toasts, setToasts] = useState<ToastMessage[]>([]);

  const addToast = (toast: Omit<ToastMessage, 'id'>) => {
    const id = Math.random().toString(36).substring(2, 9);
    setToasts((prevToasts) => [...prevToasts, { ...toast, id }]);
  };

  const removeToast = (id: string) => {
    setToasts((prevToasts) => prevToasts.filter((toast) => toast.id !== id));
  };

  // Listen for toast events in the provider
  useEffect(() => {
    const handleToastEvent = (event: Event) => {
      const toastEvent = event as CustomEvent<ToastEvent>;
      addToast({
        type: toastEvent.detail.type,
        message: toastEvent.detail.message,
        duration: toastEvent.detail.duration,
      });
    };

    window.addEventListener('toast', handleToastEvent);
    return () => {
      window.removeEventListener('toast', handleToastEvent);
    };
  }, []);

  return (
    <ToastContext.Provider value={{ toasts, addToast, removeToast }}>
      {children}
      <ToastContainer />
    </ToastContext.Provider>
  );
};

// Hook to use toast
export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

// Toast container component
const ToastContainer: React.FC = () => {
  const { toasts, removeToast } = useToast();

  // Create portal for toast container
  return createPortal(
    <div className="fixed bottom-0 right-0 z-50 p-4 space-y-4 max-w-md w-full">
      {toasts.map((toast) => (
        <Toast key={toast.id} toast={toast} onClose={() => removeToast(toast.id)} />
      ))}
    </div>,
    document.body
  );
};

// Individual toast component
const Toast: React.FC<{ toast: ToastMessage; onClose: () => void }> = ({ toast, onClose }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  useEffect(() => {
    const timer = setTimeout(() => {
      onClose();
    }, toast.duration || 5000);

    return () => clearTimeout(timer);
  }, [toast, onClose]);

  // Get icon based on toast type
  const getIcon = () => {
    switch (toast.type) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      case 'info':
        return <Info className="w-5 h-5 text-blue-500" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      default:
        return null;
    }
  };

  // Get background color based on toast type for glass morphism
  const getBackgroundColorClass = () => {
    switch (toast.type) {
      case 'success':
        return 'border-green-500/30 bg-green-600/10';
      case 'error':
        return 'border-red-500/30 bg-red-600/10';
      case 'info':
        return 'border-blue-500/30 bg-blue-600/10';
      case 'warning':
        return 'border-yellow-500/30 bg-yellow-600/10';
      default:
        return 'border-glass-border bg-glass-bg';
    }
  };

  return (
    <div
      className={`flex items-start p-4 rounded-lg shadow-md border animate-slide-up glass-morphism ${getBackgroundColorClass()} ${isRTL ? "flex-row-reverse" : ""}`}
      role="alert"
    >
      <div className={`flex-shrink-0 mr-3 ${isRTL ? "flex-row-reverse" : ""}`}>{getIcon()}</div>
      <div className={`flex-1 mr-2 ${isRTL ? "flex-row-reverse" : ""}`}>
        <p className="text-sm font-medium text-glass-primary">
          {toast.message}
        </p>
      </div>
      <button
        onClick={onClose}
        className={`flex-shrink-0 text-glass-secondary hover:text-glass-primary focus:outline-none transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
        aria-label={t('common.close')}
      >
        <X className="w-5 h-5" />
      </button>
    </div>
  );
};

// Create a global event system for toasts
type ToastEvent = {
  type: ToastType;
  message: string;
  duration?: number;
};

// Create a custom event for toasts
const createToastEvent = (detail: ToastEvent) => {
  return new CustomEvent<ToastEvent>('toast', { detail });
};

// Utility for creating toasts that works outside of React components
export const toast = {
  success: (message: string, duration?: number) => {
    window.dispatchEvent(createToastEvent({ type: 'success', message, duration }));
  },
  error: (message: string, duration?: number) => {
    window.dispatchEvent(createToastEvent({ type: 'error', message, duration }));
  },
  info: (message: string, duration?: number) => {
    window.dispatchEvent(createToastEvent({ type: 'info', message, duration }));
  },
  warning: (message: string, duration?: number) => {
    window.dispatchEvent(createToastEvent({ type: 'warning', message, duration }));
  },
};

export default ToastProvider;
