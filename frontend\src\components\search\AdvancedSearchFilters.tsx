/**
 * Advanced Search Filters Component
 * Enhanced filtering capabilities for the search system
 */

import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { Calendar, Tag, User, Filter, X } from 'lucide-react';

interface AdvancedFilters {
  dateRange: {
    start?: string;
    end?: string;
  };
  tags: string[];
  authors: string[];
  contentTypes: string[];
  sortBy: 'relevance' | 'date' | 'popularity';
  sortOrder: 'asc' | 'desc';
}

interface AdvancedSearchFiltersProps {
  filters: AdvancedFilters;
  onFiltersChange: (filters: AdvancedFilters) => void;
  onClose: () => void;
}

export const AdvancedSearchFilters: React.FC<AdvancedSearchFiltersProps> = ({ filters,
  onFiltersChange,
  onClose
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [localFilters, setLocalFilters] = useState<AdvancedFilters>(filters);

  const updateFilters = (updates: Partial<AdvancedFilters>) => {
    const newFilters = { ...localFilters, ...updates };
    setLocalFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const addTag = (tag: string) => {
    if (tag && !localFilters.tags.includes(tag)) {
      updateFilters({
        tags: [...localFilters.tags, tag]
      });
    }
  };

  const removeTag = (tag: string) => {
    updateFilters({
      tags: localFilters.tags.filter(t => t !== tag)
    );
  };

  return (
    <div className="bg-indigo-900/90 backdrop-blur-sm rounded-lg border border-indigo-800/50 p-6">
      {/* Header */}
      <div className={`flex items-center justify-between mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
        <h3 className={`text-lg font-semibold text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
          <Filter className={`${isRTL ? 'ml-2' : 'mr-2'}`} size={20} />
          {t('search.advancedFilters')}
        </h3>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-white transition-colors"
        >
          <X size={20} />
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Date Range */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            <Calendar className={`inline ${isRTL ? 'ml-1' : 'mr-1'}`} size={16} />
            {t('search.dateRange')}
          </label>
          <div className="space-y-2">
            <input
              type="date"
              value={localFilters.dateRange.start || ''}
              onChange={(e) => updateFilters({
                dateRange: { ...localFilters.dateRange, start: e.target.value }
              })}
              className="w-full bg-indigo-800/50 border border-indigo-700 rounded-lg px-3 py-2 text-white"
              placeholder={t('search.startDate')}
            />
            <input
              type="date"
              value={localFilters.dateRange.end || ''}
              onChange={(e) => updateFilters({
                dateRange: { ...localFilters.dateRange, end: e.target.value }
              })}
              className="w-full bg-indigo-800/50 border border-indigo-700 rounded-lg px-3 py-2 text-white"
              placeholder={t('search.endDate')}
            />
          </div>
        </div>

        {/* Content Types */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            {t('search.contentType')}
          </label>
          <div className="space-y-2">
            {['business', 'forum', 'resource', 'user'].map(type => (
              <label key={type} className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <input
                  type="checkbox"
                  checked={localFilters.contentTypes.includes(type)}
                  onChange={(e) => {
                    const newTypes = e.target.checked
                      ? [...localFilters.contentTypes, type]
                      : localFilters.contentTypes.filter(t => t !== type);
                    updateFilters({ contentTypes: newTypes });
                  }}
                  className="rounded border-indigo-700 bg-indigo-800/50 text-purple-500"
                />
                <span className={`text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
                  {t(`search.${type}`)}
                </span>
              </label>
            ))}
          </div>
        </div>

        {/* Sort Options */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            {t('search.sortBy')}
          </label>
          <select
            value={localFilters.sortBy}
            onChange={(e) => updateFilters({ sortBy: e.target.value as any })}
            className="w-full bg-indigo-800/50 border border-indigo-700 rounded-lg px-3 py-2 text-white mb-2"
          >
            <option value="relevance">{t('search.relevance')}</option>
            <option value="date">{t('search.date')}</option>
            <option value="popularity">{t('search.popularity')}</option>
          </select>
          <select
            value={localFilters.sortOrder}
            onChange={(e) => updateFilters({ sortOrder: e.target.value as any })}
            className="w-full bg-indigo-800/50 border border-indigo-700 rounded-lg px-3 py-2 text-white"
          >
            <option value="desc">{t('search.descending')}</option>
            <option value="asc">{t('search.ascending')}</option>
          </select>
        </div>
      </div>

      {/* Tags Section */}
      <div className="mt-6">
        <label className="block text-sm font-medium text-gray-300 mb-2">
          <Tag className={`inline ${isRTL ? 'ml-1' : 'mr-1'}`} size={16} />
          {t('search.tags')}
        </label>
        <div className={`flex flex-wrap gap-2 mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
          {localFilters.tags.map(tag => (
            <span
              key={tag}
              className={`bg-purple-600/30 text-purple-200 px-2 py-1 rounded-full text-sm flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
            >
              {tag}
              <button
                onClick={() => removeTag(tag)}
                className={`text-purple-300 hover:text-white ${isRTL ? 'mr-1' : 'ml-1'}`}
              >
                <X size={12} />
              </button>
            </span>
          ))}
        </div>
        <input
          type="text"
          placeholder={t('search.addTag')}
          onKeyPress={(e) => {
            if (e.key === "Enter") {
              addTag(e.currentTarget.value);
              e.currentTarget.value = '';
            }
          }}
          className="w-full bg-indigo-800/50 border border-indigo-700 rounded-lg px-3 py-2 text-white"
        />
      </div>
    </div>
  );
};
