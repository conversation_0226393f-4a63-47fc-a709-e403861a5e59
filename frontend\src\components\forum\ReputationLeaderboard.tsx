import React from 'react';
import { Link } from 'react-router-dom';
import { UserReputation } from '../../services/forumApi';
import { Award, Medal, Trophy } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
interface ReputationLeaderboardProps {
  leaderboard: UserReputation[];
  loading: boolean;
}

const ReputationLeaderboard: React.FC<ReputationLeaderboardProps> = ({ leaderboard, loading  }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  // Function to get badge based on position
  const getPositionBadge = (position: number) => {
    switch (position) {
      case 0:
        return <Trophy size={16} className="text-yellow-400" />;
      case 1:
        return <Medal size={16} className="text-gray-300" />;
      case 2:
        return <Medal size={16} className="text-amber-600" />;
      default:
        return <span className="text-xs text-gray-400">#{position + 1}</span>;
    }
  };

  // Function to get level color
  const getLevelColor = (level: string) => {
    switch (level) {
      case 'Newcomer':
        return 'text-gray-400';
      case 'Contributor':
        return 'text-blue-400';
      case 'Active Member':
        return 'text-green-400';
      case 'Expert':
        return 'text-purple-400';
      case 'Mentor':
        return 'text-yellow-400';
      case 'Community Leader':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };

  if (loading && leaderboard.length === 0) {
    return (
      <div className={`flex justify-center items-center h-40 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  if (leaderboard.length === 0) {
    return (
      <div className="text-gray-400 text-center">t("common.no.reputation.data", "No reputation data available yet.")</div>
    );
  }

  return (
    <div className="space-y-3">
      {leaderboard.slice(0, 5).map((user, index) => (
        <div key={user.id} className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            {getPositionBadge(index)}
            <span className={`ml-2 ${isRTL ? "space-x-reverse" : ""}`}>{user.username}</span>
          </div>
          <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <span className={`text-sm ${getLevelColor(user.level)}`}>{user.level}</span>
            <span className={`ml-2 bg-indigo-800/50 px-2 py-0.5 rounded text-xs ${isRTL ? "space-x-reverse" : ""}`}>{user.points} pts</span>
          </div>
        </div>
      ))}
      
      <div className="pt-2 text-center">
        <Link 
          to="/forum/leaderboard" 
          className={`text-purple-400 hover:text-purple-300 text-sm flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}
        >
          <Award size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
          View Full Leaderboard
        </Link>
      </div>
    </div>
  );
};

export default ReputationLeaderboard;
