import React from 'react';
import { useLanguage } from '../../hooks/useLanguage';
import { useTranslation } from 'react-i18next';
interface RTLContainerProps {
  children: React.ReactNode;
  className?: string;
  as?: keyof JSX.IntrinsicElements;
  reverseInRTL?: boolean;
  textAlign?: 'start' | 'end' | 'center';
  padding?: {
    inline?: 1 | 2 | 3 | 4;
    block?: 1 | 2 | 3 | 4;
    start?: 1 | 2 | 3 | 4;
    end?: 1 | 2 | 3 | 4;
  };
  margin?: {
    inline?: 1 | 2 | 3 | 4;
    block?: 1 | 2 | 3 | 4;
    start?: 1 | 2 | 3 | 4;
    end?: 1 | 2 | 3 | 4;
  };
}

/**
 * RTL-aware container component that automatically adjusts layout based on language direction
 *
 * This component serves as a base for RTL-aware layouts and provides common RTL functionality.
 *
 * @example
 * ```tsx
 * <RTLContainer textAlign="start" padding={{ start: 2 }} margin={{ block: 3 }}>
 *   Content that respects RTL direction
 * </RTLContainer>
 * ```
 */
const RTLContainer: React.FC<RTLContainerProps> = ({ children,
  className = '',
  as: Component = 'div',
  reverseInRTL = false,
  textAlign = 'start',
  padding,
  margin,
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  // Helper functions for RTL classes
  const getTextAlignClass = (alignment: 'start' | 'end' | 'center') => {
    switch (alignment) {
      case 'start':
        return isRTL ? 'text-right' : 'text-left';
      case 'end':
        return isRTL ? 'text-left' : 'text-right';
      case 'center':
        return 'text-center';
      default:
        return 'text-left';
    }
  };

  const getPaddingClass = (direction: 'start' | 'end' | 'top' | 'bottom', size: number) => {
    switch (direction) {
      case 'start':
        return isRTL ? `pr-${size}` : `pl-${size}`;
      case 'end':
        return isRTL ? `pl-${size}` : `pr-${size}`;
      case 'top':
        return `pt-${size}`;
      case 'bottom':
        return `pb-${size}`;
      default:
        return '';
    }
  };

  const getMarginClass = (direction: 'start' | 'end' | 'top' | 'bottom', size: number) => {
    switch (direction) {
      case 'start':
        return isRTL ? `mr-${size}` : `ml-${size}`;
      case 'end':
        return isRTL ? `ml-${size}` : `mr-${size}`;
      case 'top':
        return `mt-${size}`;
      case 'bottom':
        return `mb-${size}`;
      default:
        return '';
    }
  };

  // Build class names
  const classes: string[] = [];

  // Add base class
  classes.push(className);

  // Add text alignment
  if (textAlign === 'center') {
    classes.push('text-center');
  } else {
    classes.push(getTextAlignClass(textAlign));
  }

  // Add padding classes
  if (padding) {
    if (padding.inline) {
      classes.push(`px-${padding.inline}`);
    }
    if (padding.block) {
      classes.push(`py-${padding.block}`);
    }
    if (padding.start) {
      classes.push(getPaddingClass('start', padding.start));
    }
    if (padding.end) {
      classes.push(getPaddingClass('end', padding.end));
    }
  }

  // Add margin classes
  if (margin) {
    if (margin.inline) {
      classes.push(`mx-${margin.inline}`);
    }
    if (margin.block) {
      classes.push(`my-${margin.block}`);
    }
    if (margin.start) {
      classes.push(getMarginClass('start', margin.start));
    }
    if (margin.end) {
      classes.push(getMarginClass('end', margin.end));
    }
  }

  // Add RTL-specific classes
  if (reverseInRTL && isRTL) {
    classes.push('rtl-reverse');
  }

  return (
    <Component className={classes.join(' ')}>
      {children}
    </Component>
  );
};

export default RTLContainer;
