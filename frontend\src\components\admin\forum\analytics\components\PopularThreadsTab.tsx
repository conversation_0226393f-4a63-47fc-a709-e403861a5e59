import React from 'react';
import {
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { useTranslation } from 'react-i18next';

interface PopularThreadsTabProps {
  data: any[];
  popularPeriod: string;
  onPopularPeriodChange: (period: string) => void;
}

const PopularThreadsTab: React.FC<PopularThreadsTabProps> = ({ data,
  popularPeriod,
  onPopularPeriodChange
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  if (!data || data.length === 0) return <div className="text-center py-8">t("admin.no.data.available", t("common.noDataAvailable", "No data available"))</div>;

  // Prepare chart data
  const chartData = data.map((thread) => ({
    name: thread.title.length > 30 ? thread.title.substring(0, 30) + '...' : thread.title,
    views: thread.views,
    posts: thread.post_count,
    likes: thread.like_count
  }));

  return (
    <div className="space-y-6">
      {/* Filter Controls */}
      <div className={`bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50 flex flex-wrap gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div>
          <label className="block text-sm text-gray-400 mb-1">t("admin.time.period", "Time Period")</label>
          <select
            value={popularPeriod}
            onChange={(e) => onPopularPeriodChange(e.target.value)}
            className="bg-indigo-900/50 border border-indigo-800 rounded-lg px-3 py-2 text-sm"
          >
            <option value="week">t("admin.this.week", "This Week")</option>
            <option value="month">t("admin.this.month", "This Month")</option>
            <option value="year">t("admin.this.year", "This Year")</option>
            <option value="all">t("admin.all.time", "All Time")</option>
          </select>
        </div>
      </div>

      {/* Popular Threads Table */}
      <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
        <h3 className="text-xl font-semibold mb-4">t("admin.popular.threads", "Popular Threads")</h3>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-indigo-800/50">
                <th className={`px-4 py-2  ${isRTL ? "text-right" : "text-left"}`}>t("admin.thread", "Thread")</th>
                <th className={`px-4 py-2  ${isRTL ? "text-right" : "text-left"}`}>t("admin.author", "Author")</th>
                <th className="px-4 py-2 text-right">t("admin.views", "Views")</th>
                <th className="px-4 py-2 text-right">t("admin.posts", "Posts")</th>
                <th className="px-4 py-2 text-right">t("admin.likes", "Likes")</th>
                <th className="px-4 py-2 text-right">t("admin.created", "Created")</th>
              </tr>
            </thead>
            <tbody>
              {data.map((thread) => (
                <tr key={thread.id} className="border-b border-indigo-800/30">
                  <td className={`px-4 py-3  font-medium ${isRTL ? "text-right" : "text-left"}`}>{thread.title}</td>
                  <td className={`px-4 py-3  ${isRTL ? "text-right" : "text-left"}`}>{thread.author}</td>
                  <td className="px-4 py-3 text-right">{thread.views}</td>
                  <td className="px-4 py-3 text-right">{thread.post_count}</td>
                  <td className="px-4 py-3 text-right">{thread.like_count}</td>
                  <td className="px-4 py-3 text-right">{new Date(thread.created_at).toLocaleDateString()}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Activity Distribution Chart */}
      <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
        <h3 className="text-xl font-semibold mb-4">t("admin.activity.distribution", "Activity Distribution")</h3>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <RechartsBarChart
              data={chartData}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#444" />
              <XAxis dataKey="name" stroke="#aaa" />
              <YAxis stroke="#aaa" />
              <Tooltip
                contentStyle={{ backgroundColor: '#1e1e3f', borderColor: '#444', color: '#fff' }}
              />
              <Legend />
              <Bar dataKey="views" name="Views" fill="#8884d8" />
              <Bar dataKey="posts" name="Posts" fill="#82ca9d" />
              <Bar dataKey="likes" name="Likes" fill="#ffc658" />
            </RechartsBarChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
};

export default PopularThreadsTab;
