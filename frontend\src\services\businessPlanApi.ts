import { apiRequest, PaginatedResponse } from './api';

// Types for Business Plan Templates
export interface BusinessPlanTemplate {
  id: number;
  name: string;
  description: string;
  industry: string;
  template_type: string;
  template_type_display: string;
  sections: {
    sections: Array<{
      key: string;
      title: string;
      description: string;
      guiding_questions: string[];
      is_required: boolean;
      order: number;
    }>;
  };
  is_active: boolean;
  is_system: boolean;
  allows_customization: boolean;
  customization_options: any;
  usage_count: number;
  rating: number;
  rating_count: number;
  tags: string[];
  difficulty_level: 'beginner' | 'intermediate' | 'advanced';
  estimated_time: number; // in hours
  created_at: string;
  updated_at: string;
  created_by?: {
    id: number;
    username: string;
    first_name: string;
    last_name: string;
  };
}

// Types for Business Plan Sections
export interface BusinessPlanSection {
  id: number;
  business_plan: number;
  title: string;
  key: string;
  content: string;
  order: number;
  is_required: boolean;
  is_completed: boolean;
  ai_suggestions: string | null;
}

// Types for Business Plans
export interface BusinessPlan {
  id: number;
  business_idea: number;
  business_idea_title: string;
  template: number;
  template_name: string;
  owner: number;
  owner_details: {
    id: number;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
  };
  title: string;
  status: 'draft' | 'in_progress' | 'completed' | 'archived';
  status_display: string;
  content: Record<string, any>;
  ai_feedback: {
    overall_feedback: {
      strengths: string[];
      areas_for_improvement: string[];
      suggestions: string[];
      completion_score: number;
    };
    section_feedback: Record<string, {
      strengths: string[];
      areas_for_improvement: string[];
      suggestions: string[];
      completion_score: number;
    }>;
  };
  completion_percentage: number;
  version: number;
  sections: BusinessPlanSection[];
  created_at: string;
  updated_at: string;
}

// Business Plan Templates API
export const businessPlanTemplatesAPI = {
  getTemplates: async (industry?: string) => {
    try {
      const endpoint = industry
        ? `/incubator/business-plan-templates/?industry=${industry}`
        : '/incubator/business-plan-templates/';

      const response = await apiRequest<PaginatedResponse<BusinessPlanTemplate>>(endpoint);

      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results;
      } else if (Array.isArray(response)) {
        return response;
      }

      console.error('API returned unexpected format for business plan templates:', response);
      return [];
    } catch (error) {
      console.error('Error fetching business plan templates:', error);
      return [];
    }
  },

  getTemplate: async (id: number) => {
    try {
      return await apiRequest<BusinessPlanTemplate>(`/incubator/business-plan-templates/${id}/`);
    } catch (error) {
      console.error(`Error fetching business plan template ${id}:`, error);
      throw error;
    }
  },

  createTemplate: async (data: Partial<BusinessPlanTemplate>) => {
    try {
      return await apiRequest<BusinessPlanTemplate>('/incubator/business-plan-templates/', 'POST', data);
    } catch (error) {
      console.error('Error creating business plan template:', error);
      throw error;
    }
  },

  generateTemplate: async (industry: string) => {
    try {
      return await apiRequest<BusinessPlanTemplate>(
        '/incubator/business-plan-templates/generate_template/',
        'POST',
        { industry }
      );
    } catch (error) {
      console.error('Error generating business plan template:', error);
      throw error;
    }
  },

  updateTemplate: async (id: number, data: Partial<BusinessPlanTemplate>) => {
    try {
      return await apiRequest<BusinessPlanTemplate>(`/incubator/business-plan-templates/${id}/`, 'PATCH', data);
    } catch (error) {
      console.error(`Error updating business plan template ${id}:`, error);
      throw error;
    }
  },

  deleteTemplate: async (id: number) => {
    try {
      await apiRequest(`/incubator/business-plan-templates/${id}/`, 'DELETE');
    } catch (error) {
      console.error(`Error deleting business plan template ${id}:`, error);
      throw error;
    }
  },

  // Enhanced template features
  getTemplateDetails: async (id: number) => {
    try {
      return await apiRequest<BusinessPlanTemplate>(`/incubator/business-plan-templates/${id}/`);
    } catch (error) {
      console.error(`Error fetching template details ${id}:`, error);
      throw error;
    }
  },

  getTemplateAnalytics: async (id: number) => {
    try {
      return await apiRequest<{
        usage_count: number;
        success_rate: number;
        average_completion_time: number;
        user_ratings: Array<{
          rating: number;
          review: string;
          user: string;
          created_at: string;
        }>;
        monthly_usage: Array<{
          month: string;
          count: number;
        }>;
      }>(`/incubator/business-plan-templates/${id}/analytics/`);
    } catch (error) {
      console.error(`Error fetching template analytics ${id}:`, error);
      throw error;
    }
  },

  rateTemplate: async (id: number, rating: number, review?: string) => {
    try {
      return await apiRequest(`/incubator/business-plan-templates/${id}/rate/`, 'POST', {
        rating,
        review
      });
    } catch (error) {
      console.error(`Error rating template ${id}:`, error);
      throw error;
    }
  },

  duplicateTemplate: async (id: number, name: string) => {
    try {
      return await apiRequest<BusinessPlanTemplate>(`/incubator/business-plan-templates/${id}/duplicate/`, 'POST', {
        name
      });
    } catch (error) {
      console.error(`Error duplicating template ${id}:`, error);
      throw error;
    }
  },

  exportTemplate: async (id: number, format: 'json' | 'pdf' | 'docx') => {
    try {
      const response = await apiRequest(`/incubator/business-plan-templates/${id}/export/`, 'POST', {
        format
      });
      return response;
    } catch (error) {
      console.error(`Error exporting template ${id}:`, error);
      throw error;
    }
  },

  importTemplate: async (file: File) => {
    try {
      const formData = new FormData();
      formData.append('file', file);

      return await apiRequest<BusinessPlanTemplate>('/incubator/business-plan-templates/import/', 'POST', formData);
    } catch (error) {
      console.error('Error importing template:', error);
      throw error;
    }
  },

  getTemplateCategories: async () => {
    try {
      return await apiRequest<Array<{
        name: string;
        count: number;
        description: string;
      }>>('/incubator/business-plan-templates/categories/');
    } catch (error) {
      console.error('Error fetching template categories:', error);
      throw error;
    }
  },

  searchTemplates: async (query: string, filters?: {
    industry?: string;
    template_type?: string;
    difficulty_level?: string;
    min_rating?: number;
    tags?: string[];
  }) => {
    try {
      const params = new URLSearchParams();
      params.append('search', query);

      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            if (Array.isArray(value)) {
              value.forEach(v => params.append(key, v));
            } else {
              params.append(key, value.toString());
            }
          }
        });
      }

      return await apiRequest<PaginatedResponse<BusinessPlanTemplate>>(
        `/incubator/business-plan-templates/search/?${params.toString()}`
      );
    } catch (error) {
      console.error('Error searching templates:', error);
      throw error;
    }
  }
};

// Business Plans API
export const businessPlansAPI = {
  getPlans: async (businessIdeaId?: number) => {
    try {
      const endpoint = businessIdeaId
        ? `/incubator/business-plans/?business_idea=${businessIdeaId}`
        : '/incubator/business-plans/';

      const response = await apiRequest<PaginatedResponse<BusinessPlan>>(endpoint);

      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results;
      } else if (Array.isArray(response)) {
        return response;
      }

      console.error('API returned unexpected format for business plans:', response);
      return [];
    } catch (error) {
      console.error('Error fetching business plans:', error);
      return [];
    }
  },

  getPlan: async (id: number) => {
    try {
      return await apiRequest<BusinessPlan>(`/incubator/business-plans/${id}/`);
    } catch (error) {
      console.error(`Error fetching business plan ${id}:`, error);
      throw error;
    }
  },

  createPlan: async (data: Partial<BusinessPlan>) => {
    try {
      return await apiRequest<BusinessPlan>('/incubator/business-plans/', 'POST', data);
    } catch (error) {
      console.error('Error creating business plan:', error);
      throw error;
    }
  },

  updatePlan: async (id: number, data: Partial<BusinessPlan>) => {
    try {
      return await apiRequest<BusinessPlan>(`/incubator/business-plans/${id}/`, 'PATCH', data);
    } catch (error) {
      console.error(`Error updating business plan ${id}:`, error);
      throw error;
    }
  },

  deletePlan: async (id: number) => {
    try {
      await apiRequest(`/incubator/business-plans/${id}/`, 'DELETE');
    } catch (error) {
      console.error(`Error deleting business plan ${id}:`, error);
      throw error;
    }
  },

  analyzePlan: async (id: number) => {
    try {
      return await apiRequest<BusinessPlan>(`/incubator/business-plans/${id}/analyze/`, 'POST');
    } catch (error) {
      console.error(`Error analyzing business plan ${id}:`, error);
      throw error;
    }
  },

  generateCompletePlan: async (businessIdeaId: number, title: string) => {
    try {
      return await apiRequest<BusinessPlan>(
        '/incubator/business-plans/generate_complete/',
        'POST',
        { business_idea_id: businessIdeaId, title }
      );
    } catch (error) {
      console.error('Error generating complete business plan:', error);
      throw error;
    }
  },

  generateMarketAnalysis: async (businessIdeaId: number) => {
    try {
      return await apiRequest<{
        business_idea_id: number;
        business_idea_title: string;
        market_analysis: string;
      }>(
        '/incubator/business-plans/generate_market_analysis/',
        'POST',
        { business_idea_id: businessIdeaId }
      );
    } catch (error) {
      console.error('Error generating market analysis:', error);
      throw error;
    }
  }
};

// Business Plan Sections API
export const businessPlanSectionsAPI = {
  getSections: async (businessPlanId: number) => {
    try {
      const response = await apiRequest<PaginatedResponse<BusinessPlanSection>>(
        `/incubator/business-plan-sections/?business_plan=${businessPlanId}`
      );

      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results;
      } else if (Array.isArray(response)) {
        return response;
      }

      console.error('API returned unexpected format for business plan sections:', response);
      return [];
    } catch (error) {
      console.error('Error fetching business plan sections:', error);
      return [];
    }
  },

  getSection: async (id: number) => {
    try {
      return await apiRequest<BusinessPlanSection>(`/incubator/business-plan-sections/${id}/`);
    } catch (error) {
      console.error(`Error fetching business plan section ${id}:`, error);
      throw error;
    }
  },

  updateSection: async (id: number, data: Partial<BusinessPlanSection>) => {
    try {
      return await apiRequest<BusinessPlanSection>(`/incubator/business-plan-sections/${id}/`, 'PATCH', data);
    } catch (error) {
      console.error(`Error updating business plan section ${id}:`, error);
      throw error;
    }
  },

  generateContent: async (id: number) => {
    try {
      return await apiRequest<BusinessPlanSection>(`/incubator/business-plan-sections/${id}/generate_content/`, 'POST');
    } catch (error) {
      console.error(`Error generating content for business plan section ${id}:`, error);
      throw error;
    }
  }
};
