import React from 'react';
import { useLanguage } from '../../hooks/useLanguage';
import { RTLFlexProps } from '../../types/rtl';


/**
 * RTL-aware flex container that automatically adjusts flex direction based on language direction
 *
 * This component handles flex direction based on the current language direction.
 *
 * @example
 * ```tsx
 * <RTLFlex direction="row" align="center" justify="between">
 *   <div>t("common.first.item", "First item")</div>
 *   <div>t("common.second.item", "Second item")</div>
 * </RTLFlex>
 * ```
 */
const RTLFlex: React.FC<RTLFlexProps> = ({ children,
  className = '',
  reverseInRTL = true,
  as: Component = 'div',
  direction = 'row',
  align = 'start',
  justify = 'start',
  wrap = false,
  gap = 0,
 }) => {
  const { isRTL } = useLanguage();

  // Helper function for flex direction
  const getFlexDirectionClass = (shouldReverse: boolean) => {
    if (shouldReverse && isRTL) {
      return 'flex-row-reverse';
    }
    return 'flex-row';
  };

  // Base flex classes
  const flexClasses = ['flex'];

  // Direction
  if (direction === 'row') {
    flexClasses.push(getFlexDirectionClass(!reverseInRTL));
  } else {
    flexClasses.push('flex-col');
  }

  // Alignment
  switch (align) {
    case 'start':
      flexClasses.push('items-start');
      break;
    case 'center':
      flexClasses.push('items-center');
      break;
    case 'end':
      flexClasses.push('items-end');
      break;
    case 'stretch':
      flexClasses.push('items-stretch');
      break;
    case 'baseline':
      flexClasses.push('items-baseline');
      break;
    default:
      flexClasses.push('items-start');
  }

  // Justification
  switch (justify) {
    case 'start':
      flexClasses.push('justify-start');
      break;
    case 'center':
      flexClasses.push('justify-center');
      break;
    case 'end':
      flexClasses.push('justify-end');
      break;
    case 'between':
      flexClasses.push('justify-between');
      break;
    case 'around':
      flexClasses.push('justify-around');
      break;
    case 'evenly':
      flexClasses.push('justify-evenly');
      break;
    default:
      flexClasses.push('justify-start');
  }

  // Wrap
  if (wrap) {
    flexClasses.push('flex-wrap');
  }

  // Gap
  if (gap > 0) {
    flexClasses.push(`gap-${gap}`);
  }

  return (
    <Component className={`${flexClasses.join(' ')} ${className}`}>
      {children}
    </Component>
  );
};

export default RTLFlex;
