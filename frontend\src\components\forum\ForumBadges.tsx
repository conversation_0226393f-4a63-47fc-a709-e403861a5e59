import React, { useState, useEffect } from 'react';
import {
  Award,
  Star,
  MessageSquare,
  ThumbsUp,
  Lightbulb,
  Clock,
  Zap,
  Heart,
  Target,
  TrendingUp,
  Users,
  Calendar,
  CheckCircle,
  BookOpen,
  Flame,
  Trophy,
  Medal,
  Gift,
  Sparkles
} from 'lucide-react';
import { userReputationAPI } from '../../services/forumApi';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../../store/hooks';



interface Badge {
  id: number;
  name: string;
  description: string;
  icon: React.ReactNode;
  category: 'participation' | 'achievement' | 'special';
  level: 'bronze' | 'silver' | 'gold' | 'platinum';
  earned: boolean;
  earned_at?: string;
  progress?: number;
  progress_max?: number;
  progress_text?: string;
}

const ForumBadges: React.FC<{ userId?: number }> = ({ userId  }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { language } = useAppSelector(state => state.language);
  const [badges, setBadges] = useState<Badge[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [activeCategory, setActiveCategory] = useState<'all' | 'participation' | 'achievement' | 'special'>('all');
  const [showEarned, setShowEarned] = useState<boolean>(true);
  const [showUnearned, setShowUnearned] = useState<boolean>(true);

  useEffect(() => {
    fetchBadges();
  }, [userId]);

  const fetchBadges = async () => {
    setLoading(true);
    try {
      const data = await userReputationAPI.getUserBadges(userId);

      // Map API data to Badge interface with icons
      const badgesWithIcons = data.map((badge: any) => ({
        ...badge,
        icon: getBadgeIcon(badge.name, badge.level)
      }));

      setBadges(badgesWithIcons);
    } catch (error) {
      console.error('Error fetching badges:', error);
      setError(t("forum.failed.to.load", "Failed to load badges. Please try again later."));
    } finally {
      setLoading(false);
    }
  };

  const getBadgeIcon = (name: string, level: string) => {
    // Map badge names to appropriate icons
    const iconMap: Record<string, React.ReactNode> = {
      [t('forum.badges.firstPost')]: <MessageSquare size={24} />,
      [t('forum.badges.prolificPoster')]: <MessageSquare size={24} />,
      [t('forum.badges.threadStarter')]: <MessageSquare size={24} />,
      [t('forum.badges.discussionStarter')]: <MessageSquare size={24} />,
      [t('forum.badges.firstLike')]: <ThumbsUp size={24} />,
      [t('forum.badges.wellLiked')]: <ThumbsUp size={24} />,
      [t('forum.badges.popularContributor')]: <ThumbsUp size={24} />,
      [t('forum.badges.firstSolution')]: <CheckCircle size={24} />,
      [t('forum.badges.problemSolver')]: <CheckCircle size={24} />,
      [t('forum.badges.guru')]: <Lightbulb size={24} />,
      [t('forum.badges.quickResponder')]: <Clock size={24} />,
      [t('forum.badges.dedicated')]: <Calendar size={24} />,
      [t('forum.badges.loyalMember')]: <Calendar size={24} />,
      [t('forum.badges.veteran')]: <Calendar size={24} />,
      [t('forum.badges.friendly')]: <Heart size={24} />,
      'Networker': <Users size={24} />,
      'Bookworm': <BookOpen size={24} />,
      'Hot Streak': <Flame size={24} />,
      'Rising Star': <TrendingUp size={24} />,
      'Achiever': <Target size={24} />,
      'Champion': <Trophy size={24} />,
      'Mentor': <Users size={24} />,
      'Contributor of the Month': <Medal size={24} />,
      'Special Recognition': <Gift size={24} />,
      'Staff Pick': <Star size={24} />,
      'Featured': <Sparkles size={24} />,

      // Also include English keys for backward compatibility
      'First Post': <MessageSquare size={24} />,
      'Prolific Poster': <MessageSquare size={24} />,
      'Thread Starter': <MessageSquare size={24} />,
      'Discussion Starter': <MessageSquare size={24} />,
      'First Like': <ThumbsUp size={24} />,
      'Well Liked': <ThumbsUp size={24} />,
      'Popular Contributor': <ThumbsUp size={24} />,
      'First Solution': <CheckCircle size={24} />,
      'Problem Solver': <CheckCircle size={24} />,
      'Guru': <Lightbulb size={24} />,
      'Quick Responder': <Clock size={24} />,
      'Dedicated': <Calendar size={24} />,
      'Loyal Member': <Calendar size={24} />,
      'Veteran': <Calendar size={24} />,
      'Friendly': <Heart size={24} />,
    };

    // Default to Award icon if no specific icon is found
    return iconMap[name] || <Award size={24} />;
  };

  const getBadgeColorClass = (level: string) => {
    switch (level) {
      case 'bronze':
        return 'bg-amber-700/30 border-amber-700/50';
      case 'silver':
        return 'bg-gray-400/30 border-gray-400/50';
      case 'gold':
        return 'bg-yellow-500/30 border-yellow-500/50';
      case 'platinum':
        return 'bg-cyan-400/30 border-cyan-400/50';
      default:
        return 'bg-indigo-900/30 border-indigo-800/50';
    }
  };

  const filteredBadges = badges.filter(badge => {
    // Filter by category
    if (activeCategory !== 'all' && badge.category !== activeCategory) {
      return false;
    }

    // Filter by earned status
    if (badge.earned && !showEarned) {
      return false;
    }

    if (!badge.earned && !showUnearned) {
      return false;
    }

    return true;
  });

  if (loading) {
    return (
      <ThemeWrapper
        className="backdrop-blur-sm rounded-lg p-6 border"
        darkClassName="bg-indigo-900/30 border-indigo-800/50"
        lightClassName="bg-white border-slate-200"
      >
        <div className={`flex justify-center items-center py-12 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
        </div>
      </ThemeWrapper>
    );
  }

  if (error) {
    return (
      <ThemeWrapper
        className="backdrop-blur-sm rounded-lg p-6 border"
        darkClassName="bg-red-900/30 border-red-800/50"
        lightClassName="bg-red-50 border-red-200"
      >
        <div className={`text-center ${getTextClass('primary')}`}>{error}</div>
      </ThemeWrapper>
    );
  }

  return (
    <div className="space-y-6">
      <ThemeWrapper
        className="backdrop-blur-sm rounded-lg p-6 border"
        darkClassName="bg-indigo-900/30 border-indigo-800/50"
        lightClassName="bg-white border-slate-200"
      >
        <div className={`flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
          <h2 className={`text-xl font-semibold ${getTextClass('primary')}`}>{t('forum.badgesAndAchievements')}</h2>

          <div className={`flex flex-wrap gap-2 ${isRTL ? "flex-row-reverse" : ""}`}>
            <ThemeWrapper
              className="px-3 py-1 rounded-lg text-sm"
              darkClassName={activeCategory === 'all' ? 'bg-purple-700/50 text-white' : 'bg-indigo-900/30 hover:bg-indigo-800/30 text-gray-300'}
              lightClassName={activeCategory === 'all' ? 'bg-purple-600 text-white' : 'bg-gray-200 hover:bg-gray-300 text-gray-700'}
            >
              <button onClick={() => setActiveCategory('all')} className="w-full h-full">
                {t('common.all')}
              </button>
            </ThemeWrapper>
            <ThemeWrapper
              className="px-3 py-1 rounded-lg text-sm"
              darkClassName={activeCategory === 'participation' ? 'bg-purple-700/50 text-white' : 'bg-indigo-900/30 hover:bg-indigo-800/30 text-gray-300'}
              lightClassName={activeCategory === 'participation' ? 'bg-purple-600 text-white' : 'bg-gray-200 hover:bg-gray-300 text-gray-700'}
            >
              <button onClick={() => setActiveCategory('participation')} className="w-full h-full">
                Participation
              </button>
            </ThemeWrapper>
            <ThemeWrapper
              className="px-3 py-1 rounded-lg text-sm"
              darkClassName={activeCategory === 'achievement' ? 'bg-purple-700/50 text-white' : 'bg-indigo-900/30 hover:bg-indigo-800/30 text-gray-300'}
              lightClassName={activeCategory === 'achievement' ? 'bg-purple-600 text-white' : 'bg-gray-200 hover:bg-gray-300 text-gray-700'}
            >
              <button onClick={() => setActiveCategory('achievement')} className="w-full h-full">
                Achievements
              </button>
            </ThemeWrapper>
            <ThemeWrapper
              className="px-3 py-1 rounded-lg text-sm"
              darkClassName={activeCategory === 'special' ? 'bg-purple-700/50 text-white' : 'bg-indigo-900/30 hover:bg-indigo-800/30 text-gray-300'}
              lightClassName={activeCategory === 'special' ? 'bg-purple-600 text-white' : 'bg-gray-200 hover:bg-gray-300 text-gray-700'}
            >
              <button onClick={() => setActiveCategory('special')} className="w-full h-full">
                Special
              </button>
            </ThemeWrapper>
          </div>

          <div className={`flex gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            <label className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <input
                type="checkbox"
                checked={showEarned}
                onChange={() => setShowEarned(!showEarned)}
                className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
              />
              <span className={`${language === 'ar' ? 'mr-2' : 'ml-2'} text-sm`}>t("common.earned", "Earned")</span>
            </label>
            <label className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <input
                type="checkbox"
                checked={showUnearned}
                onChange={() => setShowUnearned(!showUnearned)}
                className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
              />
              <span className={`${language === 'ar' ? 'mr-2' : 'ml-2'} text-sm`}>t("common.unearned", "Unearned")</span>
            </label>
          </div>
        </div>

        {filteredBadges.length === 0 ? (
          <div className="text-center py-8">
            <div className={getTextClass('secondary')}>t("common.no.badges.found", "No badges found matching the selected filters.")</div>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredBadges.map(badge => (
              <div
                key={badge.id}
                className={`glass-light rounded-lg p-4 border border-glass-border ${!badge.earned ? 'opacity-60' : ''} ${getBadgeColorClass(badge.level)}`}
              >
                <div className={`flex items-start gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className={`p-2 rounded-full glass-light border border-glass-border ${getBadgeColorClass(badge.level)}`}>
                    {badge.icon}
                  </div>

                  <div className={`flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className={`flex justify-between items-start ${isRTL ? "flex-row-reverse" : ""}`}>
                      <h3 className={`font-medium ${getTextClass('primary')}`}>{badge.name}</h3>
                      <span className="text-xs px-2 py-0.5 rounded capitalize glass-light text-glass-primary border border-glass-border">
                        {badge.level}
                      </span>
                    </div>

                    <p className="text-sm mt-1 text-glass-secondary">
                      {badge.description}
                    </p>

                    {badge.earned ? (
                      <p className="text-xs mt-2 text-glass-muted">
                        Earned on {new Date(badge.earned_at || '').toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                      </p>
                    ) : badge.progress !== undefined ? (
                      <div className="mt-2">
                        <div className={`flex justify-between text-xs mb-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                          <span className="text-glass-secondary">
                            {badge.progress_text || `Progress: ${badge.progress}/${badge.progress_max}`}
                          </span>
                          <span className="text-glass-secondary">
                            {Math.round((badge.progress / (badge.progress_max || 1)) * 100)}%
                          </span>
                        </div>
                        <div className="w-full rounded-full h-1.5 glass-light border border-glass-border">
                          <div
                            className="bg-purple-600 h-1.5 rounded-full"
                            style={{ width: `${(badge.progress / (badge.progress_max || 1)) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                    ) : (
                      <p className="text-xs mt-2 text-glass-muted">
                        Not yet earned
                      </p>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </ThemeWrapper>
    </div>
  );
};

export default ForumBadges;
