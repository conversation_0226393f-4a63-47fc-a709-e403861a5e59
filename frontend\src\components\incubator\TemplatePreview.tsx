import React, { useState, useEffect } from 'react';
import {
  X,
  Star,
  Clock,
  Users,
  FileText,
  Download,
  Copy,
  Heart,
  BarChart3,
  Tag,
  Calendar,
  User,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { BusinessPlanTemplate, businessPlanTemplatesAPI } from '../../services/businessPlanApi';
import { RTLText, RTLFlex, RTLIcon } from '../common';

interface TemplatePreviewProps {
  templateId: number;
  isOpen: boolean;
  onClose: () => void;
  onUseTemplate?: (templateId: number) => void;
  onDuplicate?: (templateId: number) => void;
}

export const TemplatePreview: React.FC<TemplatePreviewProps> = ({ templateId,
  isOpen,
  onClose,
  onUseTemplate,
  onDuplicate
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [template, setTemplate] = useState<BusinessPlanTemplate | null>(null);
  const [analytics, setAnalytics] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [userRating, setUserRating] = useState(0);
  const [userReview, setUserReview] = useState('');
  const [submittingRating, setSubmittingRating] = useState(false);

  useEffect(() => {
    if (isOpen && templateId) {
      fetchTemplateDetails();
    }
  }, [isOpen, templateId]);

  const fetchTemplateDetails = async () => {
    setLoading(true);
    setError(null);

    try {
      const [templateData, analyticsData] = await Promise.all([
        businessPlanTemplatesAPI.getTemplateDetails(templateId),
        businessPlanTemplatesAPI.getTemplateAnalytics(templateId).catch(() => null)
      ]);

      setTemplate(templateData);
      setAnalytics(analyticsData);
    } catch (err) {
      console.error('Error fetching template details:', err);
      setError(t("common.failed.to.load", "Failed to load template details"));
    } finally {
      setLoading(false);
    }
  };

  const handleRateTemplate = async () => {
    if (userRating === 0) return;

    setSubmittingRating(true);
    try {
      await businessPlanTemplatesAPI.rateTemplate(templateId, userRating, userReview);
      await fetchTemplateDetails(); // Refresh data
      setUserRating(0);
      setUserReview('');
    } catch (err) {
      console.error('Error rating template:', err);
    } finally {
      setSubmittingRating(false);
    }
  };

  const handleDuplicate = async () => {
    if (!template || !onDuplicate) return;

    try {
      const duplicatedTemplate = await businessPlanTemplatesAPI.duplicateTemplate(
        templateId,
        `${template.name} (Copy)`
      );
      onDuplicate(duplicatedTemplate.id);
    } catch (err) {
      console.error('Error duplicating template:', err);
    }
  };

  const getDifficultyColor = (level: string) => {
    switch (level) {
      case 'beginner': return 'text-green-400';
      case 'intermediate': return 'text-yellow-400';
      case 'advanced': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getDifficultyLabel = (level: string) => {
    switch (level) {
      case 'beginner': return t('templates.beginner');
      case 'intermediate': return t('templates.intermediate');
      case 'advanced': return t('templates.advanced');
      default: return level;
    }
  };

  if (!isOpen) return null;

  return (
    <div className={`fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4 ${isRTL ? "flex-row-reverse" : ""}`}>
      <div className="bg-gray-900 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className={`flex items-center justify-between p-6 border-b border-gray-700 ${isRTL ? "flex-row-reverse" : ""}`}>
          <RTLText as="h2" className="text-xl font-bold">
            {t('templates.templatePreview')}
          </RTLText>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        {/* Content */}
        <div className="overflow-y-auto max-h-[calc(90vh-80px)]">
          {loading ? (
            <div className={`flex items-center justify-center p-12 ${isRTL ? "flex-row-reverse" : ""}`}>
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
            </div>
          ) : error ? (
            <div className="p-6 text-center">
              <AlertCircle className="mx-auto mb-4 text-red-400" size={48} />
              <p className="text-red-400">{error}</p>
            </div>
          ) : template ? (
            <div className="p-6 space-y-6">
              {/* Template Header */}
              <div className="bg-gray-800/50 rounded-lg p-6">
                <RTLFlex className="items-start justify-between mb-4">
                  <div className={`flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <RTLText as="h3" className="text-2xl font-bold mb-2">
                      {template.name}
                    </RTLText>
                    <RTLText className="text-gray-300 mb-4">
                      {template.description}
                    </RTLText>

                    {/* Template Meta */}
                    <RTLFlex className="items-center gap-6 text-sm text-gray-400">
                      <RTLFlex className="items-center">
                        <Tag size={16} className={isRTL ? 'ml-1' : 'mr-1'} />
                        <span>{template.industry}</span>
                      </RTLFlex>

                      <RTLFlex className="items-center">
                        <FileText size={16} className={isRTL ? 'ml-1' : 'mr-1'} />
                        <span>{template.template_type_display}</span>
                      </RTLFlex>

                      <RTLFlex className="items-center">
                        <Clock size={16} className={isRTL ? 'ml-1' : 'mr-1'} />
                        <span>{template.estimated_time}h</span>
                      </RTLFlex>

                      <RTLFlex className="items-center">
                        <span className={getDifficultyColor(template.difficulty_level)}>
                          {getDifficultyLabel(template.difficulty_level)}
                        </span>
                      </RTLFlex>
                    </RTLFlex>
                  </div>

                  {/* Rating */}
                  <div className="text-center">
                    <RTLFlex className="items-center justify-center mb-1">
                      <Star className="text-yellow-400 fill-current" size={20} />
                      <span className={`text-lg font-bold ml-1 ${isRTL ? "space-x-reverse" : ""}`}>
                        {(template.rating || 0).toFixed(1)}
                      </span>
                    </RTLFlex>
                    <p className="text-xs text-gray-400">
                      {template.rating_count || 0} {t('templates.reviews')}
                    </p>
                  </div>
                </RTLFlex>

                {/* Tags */}
                {template.tags && template.tags.length > 0 && (
                  <div className={`flex flex-wrap gap-2 mt-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                    {template.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-purple-600/20 text-purple-300 rounded-full text-xs"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                )}
              </div>

              {/* Analytics */}
              {analytics && (
                <div className="bg-gray-800/50 rounded-lg p-6">
                  <RTLText as="h4" className="text-lg font-semibold mb-4">
                    {t('templates.analytics')}
                  </RTLText>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center">
                      <Users className="mx-auto mb-2 text-blue-400" size={24} />
                      <p className="text-2xl font-bold">{analytics.usage_count || 0}</p>
                      <p className="text-sm text-gray-400">{t('templates.timesUsed')}</p>
                    </div>

                    <div className="text-center">
                      <CheckCircle className="mx-auto mb-2 text-green-400" size={24} />
                      <p className="text-2xl font-bold">{analytics.success_rate || 0}%</p>
                      <p className="text-sm text-gray-400">{t('templates.successRate')}</p>
                    </div>

                    <div className="text-center">
                      <Clock className="mx-auto mb-2 text-purple-400" size={24} />
                      <p className="text-2xl font-bold">{analytics.average_completion_time || 0}h</p>
                      <p className="text-sm text-gray-400">{t('templates.avgCompletionTime')}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Template Sections */}
              <div className="bg-gray-800/50 rounded-lg p-6">
                <RTLText as="h4" className="text-lg font-semibold mb-4">
                  {t('templates.sections')} ({template.sections?.sections?.length || 0})
                </RTLText>

                <div className="space-y-3">
                  {template.sections?.sections?.map((section, index) => (
                    <div
                      key={index}
                      className="bg-gray-700/50 rounded-lg p-4 border border-gray-600"
                    >
                      <RTLFlex className="items-center justify-between mb-2">
                        <RTLText as="h5" className="font-medium">
                          {section.title}
                        </RTLText>
                        {section.is_required && (
                          <span className="text-xs bg-red-600/20 text-red-300 px-2 py-1 rounded">
                            {t('templates.required')}
                          </span>
                        )}
                      </RTLFlex>

                      <RTLText className="text-sm text-gray-300 mb-2">
                        {section.description}
                      </RTLText>

                      {section.guiding_questions && section.guiding_questions.length > 0 && (
                        <div className="text-xs text-gray-400">
                          <span className="font-medium">{t('templates.guidingQuestions')}:</span>
                          <ul className="list-disc list-inside mt-1 space-y-1">
                            {section.guiding_questions.slice(0, 3).map((question, qIndex) => (
                              <li key={qIndex}>{question}</li>
                            ))}
                            {section.guiding_questions.length > 3 && (
                              <li>... {t('templates.andMore', { count: section.guiding_questions.length - 3 })}</li>
                            )}
                          </ul>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* User Rating Section */}
              <div className="bg-gray-800/50 rounded-lg p-6">
                <RTLText as="h4" className="text-lg font-semibold mb-4">
                  {t('templates.rateThisTemplate')}
                </RTLText>

                <RTLFlex className="items-center gap-2 mb-4">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <button
                      key={star}
                      onClick={() => setUserRating(star)}
                      className={`transition-colors ${
                        star <= userRating ? 'text-yellow-400' : 'text-gray-600'}
                      }`}
                    >
                      <Star size={24} className={star <= userRating ? 'fill-current' : ''} />
                    </button>
                  ))}
                </RTLFlex>

                <textarea
                  value={userReview}
                  onChange={(e) => setUserReview(e.target.value)}
                  placeholder={t('templates.writeReview')}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg p-3 text-white placeholder-gray-400 resize-none"
                  rows={3}
                />

                <button
                  onClick={handleRateTemplate}
                  disabled={userRating === 0 || submittingRating}
                  className="mt-3 px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed rounded-lg text-white transition-colors"
                >
                  {submittingRating ? t('common.submitting') : t('templates.submitRating')}
                </button>
              </div>
            </div>
          ) : null}
        </div>

        {/* Footer Actions */}
        {template && (
          <div className="border-t border-gray-700 p-6">
            <RTLFlex className="items-center justify-between">
              <RTLFlex className="items-center gap-3">
                <button
                  onClick={() => onUseTemplate?.(templateId)}
                  className="px-6 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg text-white font-medium transition-colors"
                >
                  {t('templates.useTemplate')}
                </button>

                <button
                  onClick={handleDuplicate}
                  className={`px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg text-white transition-colors flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                >
                  <Copy size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
                  {t('templates.duplicate')}
                </button>
              </RTLFlex>

              <RTLFlex className="items-center gap-3 text-sm text-gray-400">
                <RTLFlex className="items-center">
                  <Calendar size={16} className={isRTL ? 'ml-1' : 'mr-1'} />
                  <span>{template.created_at ? new Date(template.created_at).toLocaleDateString() : t('common.unknown', 'Unknown')}</span>
                </RTLFlex>

                {template.created_by && (
                  <RTLFlex className="items-center">
                    <User size={16} className={isRTL ? 'ml-1' : 'mr-1'} />
                    <span>{template.created_by.first_name || ''} {template.created_by.last_name || ''}</span>
                  </RTLFlex>
                )}
              </RTLFlex>
            </RTLFlex>
          </div>
        )}
      </div>
    </div>
  );
};
