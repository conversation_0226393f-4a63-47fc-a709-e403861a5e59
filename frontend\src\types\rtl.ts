/**
 * RTL Types
 * 
 * This file contains type definitions for RTL-aware components.
 */

import { ReactNode } from 'react';

/**
 * Base props for all RTL-aware components
 */
export interface RTLBaseProps {
  /**
   * Additional class names to apply to the component
   */
  className?: string;
  
  /**
   * Whether to reverse the component in RTL mode
   */
  reverseInRTL?: boolean;
  
  /**
   * The HTML element to render the component as
   */
  as?: keyof JSX.IntrinsicElements;
}

/**
 * Props for RTL-aware text components
 */
export interface RTLTextProps extends RTLBaseProps {
  /**
   * The content to display
   */
  children: ReactNode;
  
  /**
   * The text alignment
   */
  align?: 'start' | 'end' | 'center';
}

/**
 * Props for RTL-aware icon components
 */
export interface RTLIconProps extends RTLBaseProps {
  /**
   * The icon component to render
   */
  icon: any;
  
  /**
   * The size of the icon
   */
  size?: number;
  
  /**
   * Whether to flip the icon in RTL mode
   */
  flipInRTL?: boolean;
  
  /**
   * The stroke width of the icon
   */
  strokeWidth?: number;
  
  /**
   * The color of the icon
   */
  color?: string;
}

/**
 * Props for RTL-aware flex components
 */
export interface RTLFlexProps extends RTLBaseProps {
  /**
   * The content to display
   */
  children: ReactNode;
  
  /**
   * The flex direction
   */
  direction?: 'row' | 'column';
  
  /**
   * The flex alignment
   */
  align?: 'start' | 'center' | 'end' | 'stretch' | 'baseline';
  
  /**
   * The flex justification
   */
  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';
  
  /**
   * Whether to wrap flex items
   */
  wrap?: boolean;
  
  /**
   * The gap between flex items
   */
  gap?: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 8 | 10 | 12;
}

/**
 * Props for RTL-aware input components
 */
export interface RTLInputProps extends RTLBaseProps {
  /**
   * The input label
   */
  label?: string;
  
  /**
   * The error message to display
   */
  error?: string;
  
  /**
   * The icon to display
   */
  icon?: any;
  
  /**
   * The position of the icon
   */
  iconPosition?: 'left' | 'right';
  
  /**
   * Additional class names for the container
   */
  containerClassName?: string;
  
  /**
   * Additional class names for the label
   */
  labelClassName?: string;
  
  /**
   * Additional class names for the input
   */
  inputClassName?: string;
  
  /**
   * Additional class names for the error message
   */
  errorClassName?: string;
  
  /**
   * Whether to show a password toggle button
   */
  showPasswordToggle?: boolean;
  
  /**
   * The input type
   */
  type?: string;
  
  /**
   * The input name
   */
  name?: string;
  
  /**
   * The input value
   */
  value?: string;
  
  /**
   * The input placeholder
   */
  placeholder?: string;
  
  /**
   * Whether the input is required
   */
  required?: boolean;
  
  /**
   * Whether the input is disabled
   */
  disabled?: boolean;
  
  /**
   * The input change handler
   */
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  
  /**
   * The input focus handler
   */
  onFocus?: (e: React.FocusEvent<HTMLInputElement>) => void;
  
  /**
   * The input blur handler
   */
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  
  /**
   * The text direction
   */
  dir?: 'ltr' | 'rtl' | 'auto';
}

/**
 * Props for RTL-aware data table components
 */
export interface RTLDataTableProps<T> {
  /**
   * The data to display
   */
  data: T[];
  
  /**
   * The columns to display
   */
  columns: {
    key: string;
    header: string;
    render?: (item: T) => React.ReactNode;
    sortable?: boolean;
  }[];
  
  /**
   * Function to extract a unique key from each item
   */
  keyExtractor: (item: T) => string;
  
  /**
   * Whether to show pagination
   */
  pagination?: boolean;
  
  /**
   * The number of items to show per page
   */
  itemsPerPage?: number;
  
  /**
   * Additional class names
   */
  className?: string;
  
  /**
   * Message to display when there is no data
   */
  emptyMessage?: string;
  
  /**
   * Whether the table is loading
   */
  loading?: boolean;
  
  /**
   * Function to call when a row is clicked
   */
  onRowClick?: (item: T) => void;
  
  /**
   * Whether the table is sortable
   */
  sortable?: boolean;
  
  /**
   * The initial sort key
   */
  initialSortKey?: string;
  
  /**
   * The initial sort direction
   */
  initialSortDirection?: 'asc' | 'desc';
}
