import React, { useState } from 'react';
import { Search, User, Briefcase, Award, Plus, Edit, Trash2, Eye, Filter, RefreshCw, Check, X } from 'lucide-react';
import { DashboardLayout } from '../dashboard';
import { AdvancedFilter, BulkActions } from '../common';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';
import {
  useMentorProfilesList,
  useCreateMentorProfile,
  useUpdateMentorProfile,
  useDeleteMentorProfile
} from '../../../hooks/useMentorship';
import { MentorProfile } from '../../../services/incubatorApi';
import { Alert, Button } from '../../ui';
import MentorProfileModal from './components/MentorProfileModal';
import MentorProfileDeleteModal from './components/MentorProfileDeleteModal';

interface FilterValue {
  is_verified?: boolean;
  is_accepting_mentees?: boolean;
  years_of_experience?: string;
  search?: string;
}

const MentorProfilesManagement: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  // State management
  const [selectedProfiles, setSelectedProfiles] = useState<number[]>([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedProfile, setSelectedProfile] = useState<MentorProfile | null>(null);
  const [filters, setFilters] = useState<FilterValue>({});
  const [searchTerm, setSearchTerm] = useState('');

  // API hooks
  const { data: profilesData, isLoading, error, refetch } = useMentorProfilesList(filters);
  const createProfile = useCreateMentorProfile();
  const updateProfile = useUpdateMentorProfile();
  const deleteProfile = useDeleteMentorProfile();

  const profiles = profilesData?.results || [];

  // Filter options
  const filterOptions = [
    {
      key: 'is_verified',
      label: t('admin.verified', 'Verified'),
      type: 'select' as const,
      options: [
        { value: true, label: t('common.verified', 'Verified') },
        { value: false, label: t('common.unverified', 'Unverified') }
      ]
    },
    {
      key: 'is_accepting_mentees',
      label: t('mentor.availability', 'Availability'),
      type: 'select' as const,
      options: [
        { value: true, label: t('common.available', 'Available') },
        { value: false, label: t('common.unavailable', 'Unavailable') }
      ]
    },
    {
      key: 'years_of_experience',
      label: t('mentor.experience', 'Experience'),
      type: 'select' as const,
      options: [
        { value: '0-3', label: t('mentor.experience.junior', 'Junior (0-3 years)') },
        { value: '3-7', label: t('mentor.experience.mid', 'Mid-level (3-7 years)') },
        { value: '7+', label: t('mentor.experience.senior', 'Senior (7+ years)') }
      ]
    },
    {
      key: 'search',
      label: t('common.search', 'Search'),
      type: 'text' as const,
      placeholder: t('mentor.searchPlaceholder', 'Search mentor profiles...')
    }
  ];

  // Bulk actions
  const bulkActions = [
    {
      id: 'verify',
      label: t('mentor.verifySelected', 'Verify Selected'),
      icon: Check,
      variant: 'primary' as const,
      action: async (ids: number[]) => {
        for (const id of ids) {
          await updateProfile.mutateAsync({ id, data: { is_verified: true } });
        }
        setSelectedProfiles([]);
      }
    },
    {
      id: 'unverify',
      label: t('mentor.unverifySelected', 'Unverify Selected'),
      icon: X,
      variant: 'secondary' as const,
      action: async (ids: number[]) => {
        for (const id of ids) {
          await updateProfile.mutateAsync({ id, data: { is_verified: false } });
        }
        setSelectedProfiles([]);
      }
    },
    {
      id: 'delete',
      label: t('common.delete', 'Delete'),
      icon: Trash2,
      variant: 'danger' as const,
      action: async (ids: number[]) => {
        for (const id of ids) {
          await deleteProfile.mutateAsync(id);
        }
        setSelectedProfiles([]);
      }
    }
  ];

  // Event handlers
  const handleCreateProfile = () => {
    setSelectedProfile(null);
    setShowCreateModal(true);
  };

  const handleEditProfile = (profile: MentorProfile) => {
    setSelectedProfile(profile);
    setShowEditModal(true);
  };

  const handleDeleteProfile = (profile: MentorProfile) => {
    setSelectedProfile(profile);
    setShowDeleteModal(true);
  };

  const handleFilterChange = (newFilters: FilterValue) => {
    setFilters(newFilters);
  };

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setFilters(prev => ({ ...prev, search: term }));
  };

  // Get experience level badge
  const getExperienceLevel = (years: number) => {
    if (years < 3) return { label: t('mentor.experience.junior', 'Junior'), color: 'bg-blue-500/20 text-blue-300' };
    if (years < 7) return { label: t('mentor.experience.mid', 'Mid-level'), color: 'bg-green-500/20 text-green-300' };
    return { label: t('mentor.experience.senior', 'Senior'), color: 'bg-purple-500/20 text-purple-300' };
  };

  if (error) {
    return (
      <DashboardLayout currentPage="incubator">
        <Alert variant="error">
          {t('common.error.loadFailed', 'Failed to load data. Please try again.')}
        </Alert>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout currentPage="incubator">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-white">
              {t('admin.mentor.profiles.management', 'Mentor Profiles Management')}
            </h1>
            <p className="text-gray-400 mt-1">
              {t('admin.mentor.profiles.description', 'View and manage mentor profiles')}
            </p>
          </div>
          <div className="flex gap-3">
            <Button
              onClick={() => refetch()}
              variant="secondary"
              size="sm"
              disabled={isLoading}
            >
              <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              {t('common.refresh', 'Refresh')}
            </Button>
            <Button onClick={handleCreateProfile} size="sm">
              <Plus className="w-4 h-4 mr-2" />
              {t('mentor.create', 'Create Profile')}
            </Button>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="bg-indigo-900/50 rounded-lg p-4">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder={t('mentor.searchPlaceholder', 'Search mentor profiles...')}
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                />
              </div>
            </div>
            <AdvancedFilter
              filters={filterOptions}
              values={filters}
              onChange={handleFilterChange}
            />
          </div>
        </div>

        {/* Bulk Actions */}
        {selectedProfiles.length > 0 && (
          <BulkActions
            selectedCount={selectedProfiles.length}
            actions={bulkActions}
            onClearSelection={() => setSelectedProfiles([])}
          />
        )}

        {/* Mentor Profiles Table */}
        <div className="bg-indigo-900/50 rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-indigo-800/50">
                <tr>
                  <th className="px-4 py-3 text-left">
                    <input
                      type="checkbox"
                      checked={selectedProfiles.length === profiles.length && profiles.length > 0}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedProfiles(profiles.map(p => p.id));
                        } else {
                          setSelectedProfiles([]);
                        }
                      }}
                      className="rounded border-gray-600 bg-indigo-800 text-purple-500 focus:ring-purple-500"
                    />
                  </th>
                  <th className="px-4 py-3 text-left text-gray-300 font-medium">
                    {t('common.name', 'Name')}
                  </th>
                  <th className="px-4 py-3 text-left text-gray-300 font-medium">
                    {t('mentor.company', 'Company')}
                  </th>
                  <th className="px-4 py-3 text-left text-gray-300 font-medium">
                    {t('mentor.experience', 'Experience')}
                  </th>
                  <th className="px-4 py-3 text-left text-gray-300 font-medium">
                    {t('admin.status', 'Status')}
                  </th>
                  <th className="px-4 py-3 text-left text-gray-300 font-medium">
                    {t('mentor.availability', 'Availability')}
                  </th>
                  <th className="px-4 py-3 text-right text-gray-300 font-medium">
                    {t('common.actions', 'Actions')}
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-indigo-800/50">
                {isLoading ? (
                  <tr>
                    <td colSpan={7} className="px-4 py-8 text-center text-gray-400">
                      {t('common.loading', 'Loading...')}
                    </td>
                  </tr>
                ) : profiles.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="px-4 py-8 text-center text-gray-400">
                      {t('mentor.noProfiles', 'No mentor profiles found')}
                    </td>
                  </tr>
                ) : (
                  profiles.map((profile) => {
                    const experienceLevel = getExperienceLevel(profile.years_of_experience);
                    return (
                      <tr key={profile.id} className="hover:bg-indigo-800/30">
                        <td className="px-4 py-3">
                          <input
                            type="checkbox"
                            checked={selectedProfiles.includes(profile.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedProfiles(prev => [...prev, profile.id]);
                              } else {
                                setSelectedProfiles(prev => prev.filter(id => id !== profile.id));
                              }
                            }}
                            className="rounded border-gray-600 bg-indigo-800 text-purple-500 focus:ring-purple-500"
                          />
                        </td>
                        <td className="px-4 py-3">
                          <div className="flex items-center gap-3">
                            <div className="w-10 h-10 rounded-full bg-purple-700 flex items-center justify-center text-white text-sm font-bold">
                              {profile.user.first_name.charAt(0)}{profile.user.last_name.charAt(0)}
                            </div>
                            <div>
                              <div className="text-white font-medium">
                                {profile.user.first_name} {profile.user.last_name}
                              </div>
                              <div className="text-gray-400 text-sm">{profile.position}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-4 py-3">
                          <div className="text-gray-300">{profile.company}</div>
                        </td>
                        <td className="px-4 py-3">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${experienceLevel.color}`}>
                            {experienceLevel.label}
                          </span>
                        </td>
                        <td className="px-4 py-3">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            profile.is_verified ? 'bg-green-500/20 text-green-300' : 'bg-yellow-500/20 text-yellow-300'
                          }`}>
                            {profile.is_verified ? t('common.verified', 'Verified') : t('common.pending', 'Pending')}
                          </span>
                        </td>
                        <td className="px-4 py-3">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            profile.is_accepting_mentees ? 'bg-blue-500/20 text-blue-300' : 'bg-gray-500/20 text-gray-300'
                          }`}>
                            {profile.is_accepting_mentees ? t('common.available', 'Available') : t('common.unavailable', 'Unavailable')}
                          </span>
                        </td>
                        <td className="px-4 py-3">
                          <div className="flex justify-end gap-2">
                            <Button
                              onClick={() => handleEditProfile(profile)}
                              variant="secondary"
                              size="sm"
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button
                              onClick={() => handleDeleteProfile(profile)}
                              variant="danger"
                              size="sm"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    );
                  })
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Modals */}
        <MentorProfileModal
          isOpen={showCreateModal || showEditModal}
          onClose={() => {
            setShowCreateModal(false);
            setShowEditModal(false);
            setSelectedProfile(null);
          }}
          profile={selectedProfile}
          isEditing={showEditModal}
        />

        <MentorProfileDeleteModal
          isOpen={showDeleteModal}
          onClose={() => {
            setShowDeleteModal(false);
            setSelectedProfile(null);
          }}
          profile={selectedProfile}
        />
      </div>
    </DashboardLayout>
  );
};

export default MentorProfilesManagement;
