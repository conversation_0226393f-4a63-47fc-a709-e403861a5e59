from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
import json


class SystemHealth(models.Model):
    """System health monitoring model"""
    timestamp = models.DateTimeField(auto_now_add=True)
    cpu_usage = models.FloatField()
    memory_usage = models.FloatField()
    disk_usage = models.FloatField()
    network_io = models.JSONField(default=dict, blank=True)
    status = models.CharField(max_length=20, choices=[
        ('healthy', 'Healthy'),
        ('warning', 'Warning'),
        ('critical', 'Critical'),
        ('offline', 'Offline')
    ], default='healthy')
    details = models.JSONField(default=dict, blank=True)

    class Meta:
        ordering = ['-timestamp']
        verbose_name = 'System Health Record'
        verbose_name_plural = 'System Health Records'

    def __str__(self):
        return f"System Health - {self.timestamp.strftime('%Y-%m-%d %H:%M')} - {self.status}"


class SuperAdminAuditLog(models.Model):
    """Audit log for super admin actions"""
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    action = models.CharField(max_length=100)
    resource = models.CharField(max_length=100, blank=True)
    resource_id = models.CharField(max_length=50, blank=True)
    details = models.JSONField(default=dict, blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)
    success = models.BooleanField(default=True)
    error_message = models.TextField(blank=True)

    class Meta:
        ordering = ['-timestamp']
        verbose_name = 'Super Admin Audit Log'
        verbose_name_plural = 'Super Admin Audit Logs'

    def __str__(self):
        return f"{self.user.username} - {self.action} - {self.timestamp.strftime('%Y-%m-%d %H:%M')}"


class SystemConfiguration(models.Model):
    """System-wide configuration settings"""
    key = models.CharField(max_length=100, unique=True)
    value = models.TextField()
    description = models.TextField(blank=True)
    category = models.CharField(max_length=50, choices=[
        ('system', 'System'),
        ('security', 'Security'),
        ('performance', 'Performance'),
        ('ai', 'AI Configuration'),
        ('backup', 'Backup'),
        ('monitoring', 'Monitoring')
    ], default='system')
    is_sensitive = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['category', 'key']
        verbose_name = 'System Configuration'
        verbose_name_plural = 'System Configurations'

    def __str__(self):
        return f"{self.category} - {self.key}"

    def get_display_value(self):
        """Return masked value for sensitive configurations"""
        if self.is_sensitive and self.value:
            return '*' * 8 + self.value[-4:] if len(self.value) > 4 else '*' * len(self.value)
        return self.value


class SystemAlert(models.Model):
    """System alerts and notifications"""
    title = models.CharField(max_length=200)
    message = models.TextField()
    alert_type = models.CharField(max_length=20, choices=[
        ('info', 'Information'),
        ('warning', 'Warning'),
        ('error', 'Error'),
        ('critical', 'Critical')
    ], default='info')
    category = models.CharField(max_length=50, choices=[
        ('system', 'System'),
        ('security', 'Security'),
        ('performance', 'Performance'),
        ('ai', 'AI System'),
        ('backup', 'Backup'),
        ('user', 'User Management')
    ], default='system')
    is_resolved = models.BooleanField(default=False)
    resolved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='resolved_alerts')
    resolved_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    metadata = models.JSONField(default=dict, blank=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'System Alert'
        verbose_name_plural = 'System Alerts'

    def __str__(self):
        return f"{self.alert_type.upper()} - {self.title}"

    def resolve(self, user):
        """Mark alert as resolved"""
        self.is_resolved = True
        self.resolved_by = user
        self.resolved_at = timezone.now()
        self.save()


class BackupRecord(models.Model):
    """Backup operation records"""
    backup_type = models.CharField(max_length=20, choices=[
        ('full', 'Full Backup'),
        ('incremental', 'Incremental'),
        ('database', 'Database Only'),
        ('files', 'Files Only')
    ], default='full')
    status = models.CharField(max_length=20, choices=[
        ('pending', 'Pending'),
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled')
    ], default='pending')
    file_path = models.CharField(max_length=500, blank=True)
    file_size = models.BigIntegerField(null=True, blank=True)  # Size in bytes
    started_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    error_message = models.TextField(blank=True)
    metadata = models.JSONField(default=dict, blank=True)

    class Meta:
        ordering = ['-started_at']
        verbose_name = 'Backup Record'
        verbose_name_plural = 'Backup Records'

    def __str__(self):
        return f"{self.backup_type} - {self.status} - {self.started_at.strftime('%Y-%m-%d %H:%M')}"

    @property
    def duration(self):
        """Calculate backup duration"""
        if self.completed_at and self.started_at:
            return self.completed_at - self.started_at
        return None

    @property
    def file_size_mb(self):
        """Return file size in MB"""
        if self.file_size:
            return round(self.file_size / (1024 * 1024), 2)
        return None


class PerformanceMetric(models.Model):
    """Performance metrics tracking"""
    metric_name = models.CharField(max_length=100)
    metric_value = models.FloatField()
    metric_unit = models.CharField(max_length=20, default='')
    category = models.CharField(max_length=50, choices=[
        ('cpu', 'CPU'),
        ('memory', 'Memory'),
        ('disk', 'Disk'),
        ('network', 'Network'),
        ('database', 'Database'),
        ('api', 'API Response'),
        ('ai', 'AI Performance')
    ], default='system')
    timestamp = models.DateTimeField(auto_now_add=True)
    metadata = models.JSONField(default=dict, blank=True)

    class Meta:
        ordering = ['-timestamp']
        verbose_name = 'Performance Metric'
        verbose_name_plural = 'Performance Metrics'
        indexes = [
            models.Index(fields=['metric_name', 'timestamp']),
            models.Index(fields=['category', 'timestamp']),
        ]

    def __str__(self):
        return f"{self.metric_name}: {self.metric_value} {self.metric_unit} - {self.timestamp.strftime('%Y-%m-%d %H:%M')}"


class SecurityEvent(models.Model):
    """Security events and incidents"""
    event_type = models.CharField(max_length=50, choices=[
        ('login_attempt', 'Login Attempt'),
        ('failed_login', 'Failed Login'),
        ('permission_denied', 'Permission Denied'),
        ('suspicious_activity', 'Suspicious Activity'),
        ('data_breach', 'Data Breach'),
        ('unauthorized_access', 'Unauthorized Access'),
        ('malware_detected', 'Malware Detected')
    ])
    severity = models.CharField(max_length=20, choices=[
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical')
    ], default='medium')
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    description = models.TextField()
    is_resolved = models.BooleanField(default=False)
    resolved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='resolved_security_events')
    resolved_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    metadata = models.JSONField(default=dict, blank=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Security Event'
        verbose_name_plural = 'Security Events'

    def __str__(self):
        return f"{self.event_type} - {self.severity} - {self.created_at.strftime('%Y-%m-%d %H:%M')}"
