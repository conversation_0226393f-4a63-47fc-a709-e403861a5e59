import React from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';

interface SunIconProps {
  size?: number;
  className?: string;
}

const SunIcon: React.FC<SunIconProps> = ({ size = 40, className = ''  }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  // Use fixed glass morphism colors instead of theme-based colors
  const colorPalette = {
    sun: '#FFD700', // Gold
    ray: '#FFD700', // Gold
    glow: '#FFFF0055', // Semi-transparent yellow
    stroke: '#FFA500' // Orange
  };

  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 100 100"
      className={className}
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* Sun circle */}
      <circle
        cx="50"
        cy="50"
        r="25"
        fill={colorPalette.sun}
        stroke={colorPalette.stroke}
        strokeWidth="2"
      />

      {/* Sun rays */}
      {[...Array(12)].map((_, i) => {
        const angle = (i * 30) * Math.PI / 180;
        const innerRadius = 25;
        const outerRadius = 40;
        const x1 = 50 + innerRadius * Math.cos(angle);
        const y1 = 50 + innerRadius * Math.sin(angle);
        const x2 = 50 + outerRadius * Math.cos(angle);
        const y2 = 50 + outerRadius * Math.sin(angle);

        return (
          <line
            key={i}
            x1={x1}
            y1={y1}
            x2={x2}
            y2={y2}
            stroke={colorPalette.ray}
            strokeWidth="4"
            strokeLinecap="round"
          />
        );
      })}

      {/* Glow effect */}
      <circle
        cx="50"
        cy="50"
        r="35"
        fill="none"
        stroke={colorPalette.glow}
        strokeWidth="10"
      />
    </svg>
  );
};

export default SunIcon;
