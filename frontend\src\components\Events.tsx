import React, { useState, useEffect } from 'react';
import { Calendar, MapPin, Clock, Users, ExternalLink, Plus, X } from 'lucide-react';
import { Event } from '../services/api';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { fetchEvents, attendEvent, unattendEvent, createEvent } from '../store/eventsSlice';
import { LazyImage, LazyComponent } from './ui';
import { useTranslation } from 'react-i18next';
import { RTLText, RTLFlex, RTLIcon } from './common';
interface EventCardProps {
  id: number;
  title: string;
  date: string;
  time: string;
  location: string;
  type: string;
  image: string;
  attendees: number;
  isAttending: boolean;
  isVirtual: boolean;
  virtualLink?: string;
  onAttend: (id: number) => void;
  onUnattend: (id: number) => void;
}

const EventCard: React.FC<EventCardProps> = ({ id,
  title,
  date,
  time,
  location,
  type,
  image,
  attendees,
  isAttending,
  isVirtual,
  virtualLink,
  onAttend,
  onUnattend
 }) => {
  const { t } = useTranslation();
  const { language } = useAppSelector(state => state.language);
  const isRTL = language === 'ar';
  const { isAuthenticated } = useAppSelector(state => state.auth);

  return (
    <div className="bg-indigo-900/20 rounded-xl overflow-hidden shadow-lg hover:shadow-purple-500/10 transition-all duration-300 group">
      <div className="h-48 overflow-hidden relative">
        <LazyImage
          src={image || 'https://images.pexels.com/photos/2599244/pexels-photo-2599244.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'}
          alt={title}
          className="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-500"
          placeholderHeight="100%"
          placeholderWidth="100%"
          placeholderClassName="bg-indigo-900/50"
          threshold={0.1}
        />
        <div className="absolute top-0 right-0 m-3">
          <span className="px-3 py-1 text-sm font-medium bg-purple-500/80 backdrop-blur-sm text-white rounded-full">
            {type}
          </span>
        </div>
        {isAttending && (
          <div className="absolute bottom-0 left-0 right-0 bg-green-500/80 text-white text-center py-1 text-sm font-medium">
            {t('home.events.youreAttending')}
          </div>
        )}
      </div>
      <div className="p-6">
        <RTLText as="h3" className="text-xl font-semibold mb-3">{title}</RTLText>
        <div className="space-y-2 mb-4">
          <RTLFlex className="items-center text-gray-300">
            <RTLIcon icon={Calendar} size={16} className="text-purple-400" />
            <RTLText as="span">{date}</RTLText>
          </RTLFlex>
          <RTLFlex className="items-center text-gray-300">
            <RTLIcon icon={Clock} size={16} className="text-purple-400" />
            <RTLText as="span">{time}</RTLText>
          </RTLFlex>
          <RTLFlex className="items-center text-gray-300">
            <RTLIcon icon={MapPin} size={16} className="text-purple-400" />
            <RTLText as="span">{location} {isVirtual && '(Virtual)'}</RTLText>
          </RTLFlex>
          <RTLFlex className="items-center text-gray-300">
            <RTLIcon icon={Users} size={16} className="text-purple-400" />
            <RTLText as="span">{attendees} {t('home.events.attendees')}</RTLText>
          </RTLFlex>
        </div>
        <div className={`flex space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
          {isVirtual && virtualLink && (
            <a
              href={virtualLink}
              target="_blank"
              rel="noopener noreferrer"
              className={`inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full text-sm font-medium hover:shadow-glow transition-all duration-300 ${isRTL ? "flex-row-reverse" : ""}`}
            >
              {t('home.events.join')} <RTLIcon icon={ExternalLink} size={14} />
            </a>
          )}

          {isAuthenticated && (
            isAttending ? (
              <button
                onClick={() => onUnattend(id)}
                className={`inline-flex items-center px-4 py-2 bg-gray-700 rounded-full text-sm font-medium hover:bg-gray-600 transition-all duration-300 ${isRTL ? "flex-row-reverse" : ""}`}
              >
                {t('home.events.cancelAttendance')}
              </button>
            ) : (
              <button
                onClick={() => onAttend(id)}
                className={`inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full text-sm font-medium hover:shadow-glow transition-all duration-300 ${isRTL ? "flex-row-reverse" : ""}`}
              >
                {t('home.events.attend')}
              </button>
            )
          )}
        </div>
      </div>
    </div>
  );
};

interface EventFormData {
  title: string;
  description: string;
  date: string;
  time: string;
  location: string;
  is_virtual: boolean;
  virtual_link: string;
}

const Events = () => {
  const { t } = useTranslation();
  const { language } = useAppSelector(state => state.language);
  const isRTL = language === 'ar';
  const dispatch = useAppDispatch();
  const { isAuthenticated, user } = useAppSelector(state => state.auth);
  const { events, isLoading: loading, error } = useAppSelector(state => state.events);
  const [showEventForm, setShowEventForm] = useState(false);
  const [eventFormData, setEventFormData] = useState<EventFormData>({
    title: '',
    description: '',
    date: '',
    time: '',
    location: '',
    is_virtual: false,
    virtual_link: '',
  });

  // Fetch events from API
  useEffect(() => {
    dispatch(fetchEvents());

    // Add console logging to debug events data
    console.log("Events state:", events);
  }, [dispatch]);

  // Ensure events is always an array
  const eventsArray = Array.isArray(events) ? events : [];

  // Empty data for fallback
  const mockEvents: any[] = [];

  // Function to load empty data
  const loadMockData = () => {
    return (
      <div className="text-center py-10">
        <div className="text-gray-400 text-lg">{t('home.events.noSampleEvents')}</div>
      </div>
    );
  };

  // Handle attend event
  const handleAttendEvent = async (eventId: number) => {
    if (!isAuthenticated) {
      alert('Please log in to attend events');
      return;
    }

    try {
      await dispatch(attendEvent(eventId));
    } catch (err) {
      console.error("Failed to attend event:", err);
      alert('Failed to attend event. Please try again.');
    }
  };

  // Handle unattend event
  const handleUnattendEvent = async (eventId: number) => {
    try {
      await dispatch(unattendEvent(eventId));
    } catch (err) {
      console.error("Failed to unattend event:", err);
      alert('Failed to cancel attendance. Please try again.');
    }
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checkbox = e.target as HTMLInputElement;
      setEventFormData(prev => ({
        ...prev,
        [name]: checkbox.checked
      }));
    } else {
      setEventFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // Handle form submission
  const handleSubmitEvent = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!isAuthenticated || !user) {
      alert('Please log in to create events');
      return;
    }

    try {
      // Format date and time for API
      const dateTime = new Date(`${eventFormData.date}T${eventFormData.time}`);

      const eventData = {
        title: eventFormData.title,
        description: eventFormData.description,
        date: dateTime.toISOString(),
        location: eventFormData.location,
        is_virtual: eventFormData.is_virtual,
        virtual_link: eventFormData.is_virtual ? eventFormData.virtual_link : null,
        organizer_id: user.id
      };

      await dispatch(createEvent(eventData));

      // Reset form and hide it
      setEventFormData({
        title: '',
        description: '',
        date: '',
        time: '',
        location: '',
        is_virtual: false,
        virtual_link: '',
      });
      setShowEventForm(false);
    } catch (err) {
      console.error("Failed to create event:", err);
      alert('Failed to create event. Please try again.');
    }
  };

  // Format event data for display
  const formatEventData = (event: Event) => {
    const eventDate = new Date(event.date);
    const formattedDate = eventDate.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
    const formattedTime = eventDate.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });

    // Determine event type based on description or title
    let eventType = t("common.event", "Event");
    if (event.description.toLowerCase().includes('workshop') || event.title.toLowerCase().includes('workshop')) {
      eventType = t("common.workshop", "Workshop");
    } else if (event.description.toLowerCase().includes('panel') || event.title.toLowerCase().includes('panel')) {
      eventType = t("common.panel", "Panel");
    } else if (event.description.toLowerCase().includes('symposium') || event.title.toLowerCase().includes('symposium')) {
      eventType = t("common.symposium", "Symposium");
    } else if (event.description.toLowerCase().includes('webinar') || event.title.toLowerCase().includes('webinar')) {
      eventType = t("common.webinar", "Webinar");
    }

    return {
      id: event.id,
      title: event.title,
      date: formattedDate,
      time: formattedTime,
      location: event.location,
      type: eventType,
      image: event.image || '',
      attendees: event.attendee_count,
      isAttending: event.is_attending,
      isVirtual: event.is_virtual,
      virtualLink: event.virtual_link || undefined
    };
  };

  return (
    <section id="events" className="py-20 bg-indigo-950/50">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-16">
          <RTLText as="h2" align="center" className="text-3xl md:text-4xl font-bold mb-4">
            {t('home.events.title')} <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-blue-400">{t('home.events.subtitle')}</span>
          </RTLText>
          <RTLText as="div" align="center" className="text-gray-300 text-lg">
            {t('home.events.description')}
          </RTLText>
        </div>

        {error && (
          <div className="bg-red-500/20 text-red-300 p-4 rounded-lg text-center mb-8">
            {error}
          </div>
        )}

        {loading ? (
          <div className={`flex flex-col justify-center items-center py-20 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className="w-12 h-12 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mb-6"></div>
            <RTLText as="div" align="center" className="text-gray-400 mb-4">{t('home.events.loadingEvents')}</RTLText>
            <button
              type="button"
              onClick={() => {}}
              className="px-4 py-2 bg-indigo-700 hover:bg-indigo-600 rounded-lg text-white"
            >
              {t('home.events.showSampleEventsInstead')}
            </button>
          </div>
        ) : (
          <>
            {eventsArray.length === 0 ? (
              <div className="text-center py-10">
                <RTLText as="div" align="center" className="text-gray-400 text-lg mb-6">{t('home.events.noUpcomingEvents')}</RTLText>
                {/* Show mock data button */}
                <button
                  type="button"
                  onClick={() => {}}
                  className="px-4 py-2 bg-indigo-700 hover:bg-indigo-600 rounded-lg text-white"
                >
                  {t('home.events.showSampleEvents')}
                </button>

                {/* Render mock events below */}
                <div className="mt-10">
                  {loadMockData()}
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {eventsArray.map((event, index) => {
                  const formattedEvent = formatEventData(event);
                  return (
                    <LazyComponent
                      key={event.id}
                      placeholderHeight="400px"
                      threshold={0.1}
                      rootMargin="100px"
                    >
                      <EventCard
                        {...formattedEvent}
                        onAttend={handleAttendEvent}
                        onUnattend={handleUnattendEvent}
                      />
                    </LazyComponent>
                  );
                })}
              </div>
            )}
          </>
        )}

        {isAuthenticated && (
          <div className="mt-16 text-center">
            {showEventForm ? (
              <div className="bg-gradient-to-r from-purple-900/20 to-blue-900/20 rounded-xl p-8 backdrop-blur-sm max-w-2xl mx-auto">
                <div className={`flex justify-between items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <h3 className="text-2xl font-semibold">{t('home.events.createNewEvent')}</h3>
                  <button
                    onClick={() => setShowEventForm(false)}
                    className="text-gray-400 hover:text-white"
                  >
                    <X size={24} />
                  </button>
                </div>

                <form onSubmit={handleSubmitEvent} className="space-y-4">
                  <div>
                    <label htmlFor="title" className="block text-sm font-medium text-gray-300 mb-1">
                      {t('home.events.eventTitle')}*
                    </label>
                    <input
                      id="title"
                      name="title"
                      type="text"
                      required
                      value={eventFormData.title}
                      onChange={handleInputChange}
                      className="w-full px-4 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                    />
                  </div>

                  <div>
                    <label htmlFor="description" className="block text-sm font-medium text-gray-300 mb-1">
                      {t('home.events.description')}*
                    </label>
                    <textarea
                      id="description"
                      name="description"
                      required
                      rows={4}
                      value={eventFormData.description}
                      onChange={handleInputChange}
                      className="w-full px-4 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                    ></textarea>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="date" className="block text-sm font-medium text-gray-300 mb-1">
                        {t('home.events.date')}*
                      </label>
                      <input
                        id="date"
                        name="date"
                        type="date"
                        required
                        value={eventFormData.date}
                        onChange={handleInputChange}
                        className="w-full px-4 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                      />
                    </div>
                    <div>
                      <label htmlFor="time" className="block text-sm font-medium text-gray-300 mb-1">
                        {t('home.events.time')}*
                      </label>
                      <input
                        id="time"
                        name="time"
                        type="time"
                        required
                        value={eventFormData.time}
                        onChange={handleInputChange}
                        className="w-full px-4 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="location" className="block text-sm font-medium text-gray-300 mb-1">
                      {t('home.events.location')}*
                    </label>
                    <input
                      id="location"
                      name="location"
                      type="text"
                      required
                      value={eventFormData.location}
                      onChange={handleInputChange}
                      className="w-full px-4 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                    />
                  </div>

                  <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    <input
                      id="is_virtual"
                      name="is_virtual"
                      type="checkbox"
                      checked={eventFormData.is_virtual}
                      onChange={handleInputChange}
                      className="w-4 h-4 text-purple-600 bg-indigo-950/50 border-indigo-800 rounded focus:ring-purple-500"
                    />
                    <label htmlFor="is_virtual" className={`ml-2 text-sm font-medium text-gray-300 ${isRTL ? "space-x-reverse" : ""}`}>
                      {t('home.events.virtualEvent')}
                    </label>
                  </div>

                  {eventFormData.is_virtual && (
                    <div>
                      <label htmlFor="virtual_link" className="block text-sm font-medium text-gray-300 mb-1">
                        {t('home.events.virtualMeetingLink')}
                      </label>
                      <input
                        id="virtual_link"
                        name="virtual_link"
                        type="url"
                        value={eventFormData.virtual_link}
                        onChange={handleInputChange}
                        className="w-full px-4 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                        placeholder="https://..."
                      />
                    </div>
                  )}

                  <div className="pt-4">
                    <button
                      type="submit"
                      className="w-full px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300"
                    >
                      {t('home.events.createNewEvent')}
                    </button>
                  </div>
                </form>
              </div>
            ) : (
              <div className="bg-gradient-to-r from-purple-900/20 to-blue-900/20 rounded-xl p-8 backdrop-blur-sm inline-block">
                <RTLText as="h3" align="center" className="text-2xl font-semibold mb-4">
                  {t('home.events.hostYourOwnEvent')}
                </RTLText>
                <RTLText as="p" align="center" className="text-gray-300 mb-6 max-w-2xl mx-auto">
                  {t('home.events.hostDescription')}
                </RTLText>
                <button
                  onClick={() => setShowEventForm(true)}
                  className={`px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full font-medium hover:shadow-glow transition-all duration-300 flex items-center justify-center mx-auto ${isRTL ? "flex-row-reverse" : ""}`}
                >
                  <RTLIcon icon={Plus} size={18} />
                  {t('home.events.createNewEvent')}
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </section>
  );
};

export default Events;