from rest_framework import serializers
from django.contrib.auth.models import User
from .models import (
    ForumCategory, ForumTopic, ForumThread, ForumPost,
    UserReputation, ReputationActivity, TopicSubscription, ThreadSubscription,
    ForumAttachment
)
from users.serializers import UserSerializer
from api.serializers import TagSerializer
from api.models import Tag
from .content_filter import ContentFlag

class UserReputationSerializer(serializers.ModelSerializer):
    username = serializers.CharField(source='user.username', read_only=True)

    class Meta:
        model = UserReputation
        fields = [
            'id', 'username', 'points', 'level', 'threads_created',
            'posts_created', 'solutions_provided', 'likes_received', 'likes_given'
        ]
        read_only_fields = ['id', 'points', 'level']

class ReputationActivitySerializer(serializers.ModelSerializer):
    username = serializers.CharField(source='user.username', read_only=True)

    class Meta:
        model = ReputationActivity
        fields = [
            'id', 'username', 'activity_type', 'points',
            'description', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class ContentFlagSerializer(serializers.ModelSerializer):
    reviewed_by_username = serializers.CharField(source='reviewed_by.username', read_only=True, allow_null=True)
    content_preview = serializers.SerializerMethodField()
    content_author = serializers.SerializerMethodField()

    class Meta:
        model = ContentFlag
        fields = [
            'id', 'content_type', 'content_id', 'reason', 'details',
            'severity', 'auto_flagged', 'reviewed', 'reviewed_by',
            'reviewed_by_username', 'reviewed_at', 'created_at',
            'content_preview', 'content_author'
        ]
        read_only_fields = ['id', 'created_at']

    def get_content_preview(self, obj):
        """Get a preview of the flagged content"""
        if obj.content_type == 'thread':
            try:
                thread = ForumThread.objects.get(id=obj.content_id)
                return {
                    'title': thread.title,
                    'content': thread.content[:200] + ('...' if len(thread.content) > 200 else ''),
                    'moderation_status': thread.moderation_status
                }
            except ForumThread.DoesNotExist:
                return {'error': 'Thread not found'}
        elif obj.content_type == 'post':
            try:
                post = ForumPost.objects.get(id=obj.content_id)
                return {
                    'content': post.content[:200] + ('...' if len(post.content) > 200 else ''),
                    'thread_title': post.thread.title,
                    'moderation_status': post.moderation_status
                }
            except ForumPost.DoesNotExist:
                return {'error': 'Post not found'}
        return None

    def get_content_author(self, obj):
        """Get the author of the flagged content"""
        if obj.content_type == 'thread':
            try:
                thread = ForumThread.objects.get(id=obj.content_id)
                return {
                    'id': thread.author.id,
                    'username': thread.author.username
                }
            except ForumThread.DoesNotExist:
                return None
        elif obj.content_type == 'post':
            try:
                post = ForumPost.objects.get(id=obj.content_id)
                return {
                    'id': post.author.id,
                    'username': post.author.username
                }
            except ForumPost.DoesNotExist:
                return None
        return None

class ForumAttachmentSerializer(serializers.ModelSerializer):
    uploaded_by_username = serializers.CharField(source='uploaded_by.username', read_only=True)
    file_url = serializers.SerializerMethodField()

    class Meta:
        model = ForumAttachment
        fields = [
            'id', 'file', 'file_url', 'file_type', 'file_size', 'file_name',
            'content_type', 'thread', 'post', 'uploaded_by', 'uploaded_by_username',
            'created_at'
        ]
        read_only_fields = ['id', 'file_size', 'file_type', 'created_at']
        extra_kwargs = {
            'uploaded_by': {'write_only': True},
            'thread': {'write_only': True},
            'post': {'write_only': True}
        }

    def get_file_url(self, obj):
        request = self.context.get('request')
        if request and obj.file:
            return request.build_absolute_uri(obj.file.url)
        return None


class ForumPostSerializer(serializers.ModelSerializer):
    author = UserSerializer(read_only=True)
    author_id = serializers.IntegerField(write_only=True)
    like_count = serializers.IntegerField(read_only=True)
    is_liked = serializers.SerializerMethodField()
    author_reputation = serializers.SerializerMethodField()
    attachments = ForumAttachmentSerializer(many=True, read_only=True)

    class Meta:
        model = ForumPost
        fields = [
            'id', 'thread', 'author', 'author_id', 'content',
            'like_count', 'is_liked', 'is_solution', 'author_reputation',
            'moderation_status', 'created_at', 'updated_at', 'attachments'
        ]
        read_only_fields = ['id', 'is_solution', 'moderation_status', 'created_at', 'updated_at']

    def get_is_liked(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return request.user in obj.likes.all()
        return False

    def get_author_reputation(self, obj):
        try:
            reputation = obj.author.reputation
            return {
                'level': reputation.level,
                'points': reputation.points
            }
        except UserReputation.DoesNotExist:
            return {
                'level': 'Newcomer',
                'points': 0
            }

class TopicSubscriptionSerializer(serializers.ModelSerializer):
    topic_title = serializers.CharField(source='topic.title', read_only=True)
    topic_description = serializers.CharField(source='topic.description', read_only=True)
    category_name = serializers.CharField(source='topic.category.name', read_only=True)

    class Meta:
        model = TopicSubscription
        fields = [
            'id', 'user', 'topic', 'topic_title', 'topic_description',
            'category_name', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']
        extra_kwargs = {
            'user': {'write_only': True},
            'topic': {'write_only': True}
        }


class ThreadSubscriptionSerializer(serializers.ModelSerializer):
    thread_title = serializers.CharField(source='thread.title', read_only=True)
    topic_title = serializers.CharField(source='thread.topic.title', read_only=True)
    category_name = serializers.CharField(source='thread.topic.category.name', read_only=True)

    class Meta:
        model = ThreadSubscription
        fields = [
            'id', 'user', 'thread', 'thread_title', 'topic_title',
            'category_name', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']
        extra_kwargs = {
            'user': {'write_only': True},
            'thread': {'write_only': True}
        }


class ForumThreadSerializer(serializers.ModelSerializer):
    author = UserSerializer(read_only=True)
    author_id = serializers.IntegerField(write_only=True)
    posts = ForumPostSerializer(many=True, read_only=True)
    post_count = serializers.IntegerField(read_only=True)
    tags = TagSerializer(many=True, read_only=True)
    tag_ids = serializers.PrimaryKeyRelatedField(
        many=True,
        write_only=True,
        queryset=Tag.objects.all(),
        required=False,
        source='tags'
    )
    author_reputation = serializers.SerializerMethodField()
    is_subscribed = serializers.SerializerMethodField()
    attachments = ForumAttachmentSerializer(many=True, read_only=True)

    class Meta:
        model = ForumThread
        fields = [
            'id', 'title', 'slug', 'topic', 'author', 'author_id',
            'content', 'tags', 'tag_ids', 'is_pinned', 'is_locked',
            'views', 'post_count', 'posts', 'author_reputation',
            'moderation_status', 'last_activity', 'created_at', 'updated_at',
            'is_subscribed', 'attachments'
        ]
        read_only_fields = [
            'id', 'slug', 'is_pinned', 'is_locked', 'views',
            'moderation_status', 'last_activity', 'created_at', 'updated_at'
        ]

    def get_author_reputation(self, obj):
        try:
            reputation = obj.author.reputation
            return {
                'level': reputation.level,
                'points': reputation.points
            }
        except UserReputation.DoesNotExist:
            return {
                'level': 'Newcomer',
                'points': 0
            }

    def get_is_subscribed(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return ThreadSubscription.objects.filter(
                user=request.user,
                thread=obj
            ).exists()
        return False

class ForumTopicSerializer(serializers.ModelSerializer):
    created_by = UserSerializer(read_only=True)
    created_by_id = serializers.IntegerField(write_only=True, required=False)
    thread_count = serializers.IntegerField(read_only=True)
    post_count = serializers.IntegerField(read_only=True)
    last_activity = serializers.DateTimeField(read_only=True)
    threads = ForumThreadSerializer(many=True, read_only=True)
    is_subscribed = serializers.SerializerMethodField()

    class Meta:
        model = ForumTopic
        fields = [
            'id', 'title', 'slug', 'description', 'category',
            'icon', 'image', 'is_pinned', 'is_locked', 'is_active',
            'created_by', 'created_by_id', 'thread_count', 'post_count',
            'last_activity', 'threads', 'created_at', 'updated_at',
            'is_subscribed'
        ]
        read_only_fields = [
            'id', 'slug', 'is_pinned', 'is_locked',
            'created_at', 'updated_at'
        ]

    def get_is_subscribed(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return TopicSubscription.objects.filter(
                user=request.user,
                topic=obj
            ).exists()
        return False

class ForumAttachmentSerializer(serializers.ModelSerializer):
    uploaded_by_username = serializers.CharField(source='uploaded_by.username', read_only=True)
    file_url = serializers.SerializerMethodField()

    class Meta:
        model = ForumAttachment
        fields = [
            'id', 'file', 'file_url', 'file_type', 'file_size', 'file_name',
            'content_type', 'thread', 'post', 'uploaded_by', 'uploaded_by_username',
            'created_at'
        ]
        read_only_fields = ['id', 'file_size', 'file_type', 'created_at']
        extra_kwargs = {
            'uploaded_by': {'write_only': True},
            'thread': {'write_only': True},
            'post': {'write_only': True}
        }

    def get_file_url(self, obj):
        request = self.context.get('request')
        if request and obj.file:
            return request.build_absolute_uri(obj.file.url)
        return None


class ForumCategorySerializer(serializers.ModelSerializer):
    topics = ForumTopicSerializer(many=True, read_only=True)
    topic_count = serializers.SerializerMethodField()
    thread_count = serializers.SerializerMethodField()
    post_count = serializers.SerializerMethodField()

    class Meta:
        model = ForumCategory
        fields = [
            'id', 'name', 'slug', 'description', 'icon',
            'order', 'is_active', 'topics', 'topic_count',
            'thread_count', 'post_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'slug', 'created_at', 'updated_at']

    def get_topic_count(self, obj):
        return obj.topics.count()

    def get_thread_count(self, obj):
        return sum(topic.thread_count for topic in obj.topics.all())

    def get_post_count(self, obj):
        return sum(topic.post_count for topic in obj.topics.all())
