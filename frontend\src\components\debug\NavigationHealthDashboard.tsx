import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useAppSelector } from '../../store/hooks';
import { 
  generateNavigationValidationReport, 
  getNavigationHealthScore,
  getNavigationRecommendations,
  NavigationValidationReport 
} from '../../utils/navigationValidationReport';
import { 
  Activity, 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  TrendingUp, 
  Users, 
  Shield,
  Navigation,
  BarChart3,
  RefreshCw
} from 'lucide-react';

/**
 * Navigation Health Dashboard - Development only
 * Shows overall navigation system health and recommendations
 */
const NavigationHealthDashboard: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [report, setReport] = useState<NavigationValidationReport | null>(null);
  const [healthScore, setHealthScore] = useState<number>(0);
  const [recommendations, setRecommendations] = useState<string[]>([]);
  
  const { user, isAuthenticated } = useAppSelector(state => state.auth);
  const location = useLocation();

  const generateReport = () => {
    const newReport = generateNavigationValidationReport();
    const score = getNavigationHealthScore(newReport);
    const recs = getNavigationRecommendations(
      location.pathname, 
      isAuthenticated, 
      user?.is_superuser ? 'super_admin' : user?.is_admin ? 'admin' : 'user'
    );

    setReport(newReport);
    setHealthScore(score);
    setRecommendations(recs);
  };

  useEffect(() => {
    if (isOpen && !report) {
      generateReport();
    }
  }, [isOpen]);

  const getHealthColor = (score: number): string => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    if (score >= 50) return 'text-orange-600';
    return 'text-red-600';
  };

  const getHealthStatus = (score: number): string => {
    if (score >= 90) return 'Excellent';
    if (score >= 70) return 'Good';
    if (score >= 50) return 'Fair';
    return 'Poor';
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return <XCircle className="w-4 h-4 text-red-600" />;
      case 'high': return <AlertTriangle className="w-4 h-4 text-orange-600" />;
      case 'medium': return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
      case 'low': return <CheckCircle className="w-4 h-4 text-blue-600" />;
      default: return <CheckCircle className="w-4 h-4 text-gray-600" />;
    }
  };

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <>
      {/* Floating Button */}
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-68 right-4 z-50 bg-emerald-600 hover:bg-emerald-700 text-white p-3 rounded-full shadow-lg transition-colors"
        title="Navigation Health Dashboard"
      >
        <Activity className="w-5 h-5" />
      </button>

      {/* Dashboard Panel */}
      {isOpen && (
        <div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-2xl max-w-5xl w-full max-h-[90vh] overflow-hidden">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Navigation Health Dashboard
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Overall navigation system health and recommendations
                </p>
              </div>
              <div className="flex items-center gap-3">
                <button
                  onClick={generateReport}
                  className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <RefreshCw className="w-4 h-4" />
                  Refresh
                </button>
                <button
                  onClick={() => setIsOpen(false)}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                >
                  ✕
                </button>
              </div>
            </div>

            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              {report ? (
                <div className="space-y-6">
                  {/* Health Score */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div className="md:col-span-1">
                      <div className="text-center p-6 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg">
                        <div className={`text-4xl font-bold ${getHealthColor(healthScore)}`}>
                          {healthScore}
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                          Health Score
                        </div>
                        <div className={`text-lg font-medium mt-2 ${getHealthColor(healthScore)}`}>
                          {getHealthStatus(healthScore)}
                        </div>
                      </div>
                    </div>

                    <div className="md:col-span-3 grid grid-cols-3 gap-4">
                      <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div className="text-2xl font-bold text-blue-600">{report.totalTests}</div>
                        <div className="text-sm text-gray-600 dark:text-gray-300">Total Tests</div>
                      </div>
                      <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div className="text-2xl font-bold text-green-600">
                          {report.criticalPaths.filter(p => p.testCoverage).length}
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-300">Covered Paths</div>
                      </div>
                      <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div className="text-2xl font-bold text-purple-600">
                          {Object.keys(report.testsByRole).length}
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-300">Tested Roles</div>
                      </div>
                    </div>
                  </div>

                  {/* Current State */}
                  <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
                      Current Navigation State
                    </h3>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <strong>Current Path:</strong> {location.pathname}
                      </div>
                      <div>
                        <strong>Authenticated:</strong> {isAuthenticated ? '✅ Yes' : '❌ No'}
                      </div>
                      <div>
                        <strong>User:</strong> {user?.username || 'None'}
                      </div>
                      <div>
                        <strong>Role:</strong> {user?.is_superuser ? 'Super Admin' : user?.is_admin ? 'Admin' : 'User'}
                      </div>
                    </div>
                  </div>

                  {/* Issues */}
                  {report.potentialIssues.length > 0 && (
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                        Potential Issues ({report.potentialIssues.length})
                      </h3>
                      <div className="space-y-3">
                        {report.potentialIssues.map((issue, index) => (
                          <div
                            key={index}
                            className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
                          >
                            <div className="flex items-start gap-3">
                              {getSeverityIcon(issue.severity)}
                              <div className="flex-1">
                                <div className="flex items-center gap-2 mb-2">
                                  <span className="font-medium text-gray-900 dark:text-white">
                                    {issue.description}
                                  </span>
                                  <span className={`px-2 py-1 text-xs rounded ${
                                    issue.severity === 'critical' ? 'bg-red-100 text-red-800' :
                                    issue.severity === 'high' ? 'bg-orange-100 text-orange-800' :
                                    issue.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                    'bg-blue-100 text-blue-800'
                                  }`}>
                                    {issue.severity.toUpperCase()}
                                  </span>
                                </div>
                                <div className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                                  <strong>Recommendation:</strong> {issue.recommendation}
                                </div>
                                {issue.affectedPaths.length > 0 && (
                                  <div className="text-sm text-gray-500">
                                    <strong>Affected paths:</strong> {issue.affectedPaths.join(', ')}
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Recommendations */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                      Current Recommendations
                    </h3>
                    <div className="space-y-2">
                      {recommendations.map((rec, index) => (
                        <div
                          key={index}
                          className="flex items-center gap-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg"
                        >
                          <CheckCircle className="w-4 h-4 text-green-600 flex-shrink-0" />
                          <span className="text-sm text-gray-700 dark:text-gray-300">{rec}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Test Coverage by Role */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                      Test Coverage by Role
                    </h3>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      {Object.entries(report.testsByRole).map(([role, count]) => (
                        <div
                          key={role}
                          className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg text-center"
                        >
                          <div className="text-lg font-bold text-blue-600">{count}</div>
                          <div className="text-sm text-gray-600 dark:text-gray-300">
                            {role.replace('_', ' ').toUpperCase()}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Overall Recommendations */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                      System Recommendations
                    </h3>
                    <div className="space-y-2">
                      {report.recommendations.map((rec, index) => (
                        <div
                          key={index}
                          className="flex items-center gap-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg"
                        >
                          <TrendingUp className="w-4 h-4 text-blue-600 flex-shrink-0" />
                          <span className="text-sm text-gray-700 dark:text-gray-300">{rec}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <BarChart3 className="w-16 h-16 mx-auto mb-4 text-gray-400" />
                    <p className="text-gray-500">Click "Refresh" to generate navigation health report</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default NavigationHealthDashboard;
