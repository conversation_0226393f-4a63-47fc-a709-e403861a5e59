import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Badge } from '../../components/ui/badge';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { FileText, Search, Filter, Download, Calendar, User, Shield, AlertTriangle, CheckCircle, X } from 'lucide-react';

interface AuditLog {
  id: string;
  timestamp: string;
  userId: string;
  userName: string;
  userRole: string;
  action: string;
  resource: string;
  resourceId?: string;
  ipAddress: string;
  userAgent: string;
  status: 'success' | 'failure' | 'warning';
  details: string;
  changes?: {
    field: string;
    oldValue: any;
    newValue: any;
  }[];
}

interface AuditStats {
  totalLogs: number;
  todayLogs: number;
  successfulActions: number;
  failedActions: number;
  uniqueUsers: number;
  criticalEvents: number;
}

const AuditLogsPage: React.FC = () => {
  const [logs, setLogs] = useState<AuditLog[]>([]);
  const [filteredLogs, setFilteredLogs] = useState<AuditLog[]>([]);
  const [stats, setStats] = useState<AuditStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [actionFilter, setActionFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('today');
  const [activeTab, setActiveTab] = useState('logs');

  // Mock data - replace with actual API call
  useEffect(() => {
    const mockStats: AuditStats = {
      totalLogs: 15847,
      todayLogs: 234,
      successfulActions: 14892,
      failedActions: 955,
      uniqueUsers: 1247,
      criticalEvents: 12
    };

    const mockLogs: AuditLog[] = [
      {
        id: '1',
        timestamp: '2024-01-16T14:30:00Z',
        userId: 'admin1',
        userName: 'Admin User',
        userRole: 'admin',
        action: 'user_suspended',
        resource: 'user',
        resourceId: 'user123',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        status: 'success',
        details: 'User Mike Wilson suspended for 7 days due to policy violations',
        changes: [
          { field: 'status', oldValue: 'active', newValue: 'suspended' },
          { field: 'suspension_end', oldValue: null, newValue: '2024-01-23T14:30:00Z' }
        ]
      },
      {
        id: '2',
        timestamp: '2024-01-16T14:15:00Z',
        userId: 'mod1',
        userName: 'Moderator John',
        userRole: 'moderator',
        action: 'content_removed',
        resource: 'post',
        resourceId: 'post456',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        status: 'success',
        details: 'Spam post removed from business ideas section'
      },
      {
        id: '3',
        timestamp: '2024-01-16T13:45:00Z',
        userId: 'user789',
        userName: 'Sarah Johnson',
        userRole: 'user',
        action: 'login_failed',
        resource: 'authentication',
        ipAddress: '************',
        userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15',
        status: 'failure',
        details: 'Failed login attempt - incorrect password'
      },
      {
        id: '4',
        timestamp: '2024-01-16T13:30:00Z',
        userId: 'admin1',
        userName: 'Admin User',
        userRole: 'admin',
        action: 'system_config_updated',
        resource: 'system_configuration',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        status: 'success',
        details: 'Updated email configuration settings',
        changes: [
          { field: 'smtp_host', oldValue: 'old.smtp.com', newValue: 'smtp.gmail.com' },
          { field: 'smtp_port', oldValue: 465, newValue: 587 }
        ]
      },
      {
        id: '5',
        timestamp: '2024-01-16T12:00:00Z',
        userId: 'system',
        userName: 'System',
        userRole: 'system',
        action: 'backup_completed',
        resource: 'database',
        ipAddress: '127.0.0.1',
        userAgent: 'System/1.0',
        status: 'success',
        details: 'Daily database backup completed successfully'
      }
    ];

    setTimeout(() => {
      setStats(mockStats);
      setLogs(mockLogs);
      setFilteredLogs(mockLogs);
      setLoading(false);
    }, 1000);
  }, []);

  // Filter logs based on search and filters
  useEffect(() => {
    let filtered = logs;

    if (searchTerm) {
      filtered = filtered.filter(log => 
        log.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.resource.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.details.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(log => log.status === statusFilter);
    }

    if (actionFilter !== 'all') {
      filtered = filtered.filter(log => log.action.includes(actionFilter));
    }

    if (dateFilter !== 'all') {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      
      filtered = filtered.filter(log => {
        const logDate = new Date(log.timestamp);
        switch (dateFilter) {
          case 'today':
            return logDate >= today;
          case 'week':
            const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            return logDate >= weekAgo;
          case 'month':
            const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
            return logDate >= monthAgo;
          default:
            return true;
        }
      });
    }

    setFilteredLogs(filtered);
  }, [logs, searchTerm, statusFilter, actionFilter, dateFilter]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'bg-green-100 text-green-800';
      case 'failure': return 'bg-red-100 text-red-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'failure': return <X className="w-4 h-4 text-red-600" />;
      case 'warning': return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
      default: return <Shield className="w-4 h-4 text-gray-600" />;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'bg-purple-100 text-purple-800';
      case 'moderator': return 'bg-blue-100 text-blue-800';
      case 'system': return 'bg-gray-100 text-gray-800';
      default: return 'bg-green-100 text-green-800';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const handleExport = () => {
    // Export logs to CSV
    const csvContent = filteredLogs.map(log => 
      `${log.timestamp},${log.userName},${log.action},${log.resource},${log.status},${log.details}`
    ).join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `audit_logs_${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Audit Logs</h1>
          <p className="text-gray-600 mt-1">Track all system activities and user actions</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleExport}>
            <Download className="w-4 h-4 mr-2" />
            Export Logs
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700">
            <Filter className="w-4 h-4 mr-2" />
            Advanced Filters
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Logs</p>
                  <p className="text-2xl font-bold">{stats.totalLogs.toLocaleString()}</p>
                  <p className="text-sm text-gray-600">{stats.todayLogs} today</p>
                </div>
                <FileText className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Success Rate</p>
                  <p className="text-2xl font-bold text-green-600">
                    {Math.round((stats.successfulActions / stats.totalLogs) * 100)}%
                  </p>
                  <p className="text-sm text-green-600">{stats.successfulActions.toLocaleString()} successful</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Critical Events</p>
                  <p className="text-2xl font-bold text-red-600">{stats.criticalEvents}</p>
                  <p className="text-sm text-gray-600">Require attention</p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Failed Actions</p>
                  <p className="text-2xl font-bold text-orange-600">{stats.failedActions}</p>
                  <p className="text-sm text-orange-600">Need investigation</p>
                </div>
                <X className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Users</p>
                  <p className="text-2xl font-bold text-purple-600">{stats.uniqueUsers}</p>
                  <p className="text-sm text-purple-600">Unique users</p>
                </div>
                <User className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Today's Activity</p>
                  <p className="text-2xl font-bold text-blue-600">{stats.todayLogs}</p>
                  <p className="text-sm text-blue-600">Actions logged</p>
                </div>
                <Calendar className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search logs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select 
              value={statusFilter} 
              onChange={(e) => setStatusFilter(e.target.value)}
              className="border rounded px-3 py-2"
            >
              <option value="all">All Status</option>
              <option value="success">Success</option>
              <option value="failure">Failure</option>
              <option value="warning">Warning</option>
            </select>
            <select 
              value={actionFilter} 
              onChange={(e) => setActionFilter(e.target.value)}
              className="border rounded px-3 py-2"
            >
              <option value="all">All Actions</option>
              <option value="login">Login/Logout</option>
              <option value="user">User Management</option>
              <option value="content">Content Management</option>
              <option value="system">System Changes</option>
            </select>
            <select 
              value={dateFilter} 
              onChange={(e) => setDateFilter(e.target.value)}
              className="border rounded px-3 py-2"
            >
              <option value="all">All Time</option>
              <option value="today">Today</option>
              <option value="week">Last Week</option>
              <option value="month">Last Month</option>
            </select>
            <Button variant="outline" onClick={() => {
              setSearchTerm('');
              setStatusFilter('all');
              setActionFilter('all');
              setDateFilter('today');
            }}>
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Audit Logs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="logs">Audit Logs ({filteredLogs.length})</TabsTrigger>
          <TabsTrigger value="summary">Summary</TabsTrigger>
        </TabsList>

        <TabsContent value="logs" className="space-y-4">
          <div className="space-y-4">
            {filteredLogs.map((log) => (
              <Card key={log.id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-4 flex-1">
                      {getStatusIcon(log.status)}
                      
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="font-semibold">{log.action.replace(/_/g, ' ').toUpperCase()}</h3>
                          <Badge className={getStatusColor(log.status)}>
                            {log.status}
                          </Badge>
                          <Badge className={getRoleColor(log.userRole)}>
                            {log.userRole}
                          </Badge>
                        </div>
                        
                        <p className="text-gray-700 mb-3">{log.details}</p>
                        
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600 mb-3">
                          <div>
                            <p className="font-medium">User:</p>
                            <p>{log.userName}</p>
                          </div>
                          <div>
                            <p className="font-medium">Resource:</p>
                            <p>{log.resource}</p>
                          </div>
                          <div>
                            <p className="font-medium">IP Address:</p>
                            <p>{log.ipAddress}</p>
                          </div>
                          <div>
                            <p className="font-medium">Timestamp:</p>
                            <p>{formatTimestamp(log.timestamp)}</p>
                          </div>
                        </div>

                        {log.changes && log.changes.length > 0 && (
                          <div className="bg-gray-50 p-3 rounded mb-3">
                            <p className="text-sm font-medium mb-2">Changes Made:</p>
                            <div className="space-y-1">
                              {log.changes.map((change, index) => (
                                <div key={index} className="text-sm">
                                  <span className="font-medium">{change.field}:</span>
                                  <span className="text-red-600 mx-2">{JSON.stringify(change.oldValue)}</span>
                                  →
                                  <span className="text-green-600 mx-2">{JSON.stringify(change.newValue)}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        <div className="text-xs text-gray-500">
                          User Agent: {log.userAgent.substring(0, 80)}...
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredLogs.length === 0 && (
            <Card>
              <CardContent className="p-8 text-center">
                <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 mb-4">No audit logs found matching your criteria.</p>
                <Button variant="outline" onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('all');
                  setActionFilter('all');
                  setDateFilter('all');
                }}>
                  Clear All Filters
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="summary" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Action Types</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {['login', 'user_management', 'content_moderation', 'system_config'].map((action, index) => {
                    const count = logs.filter(log => log.action.includes(action.split('_')[0])).length;
                    const percentage = (count / logs.length) * 100;
                    return (
                      <div key={index} className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm font-medium">{action.replace(/_/g, ' ')}</span>
                          <span className="text-sm text-gray-600">{count} ({percentage.toFixed(1)}%)</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full" 
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>User Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {['admin', 'moderator', 'user', 'system'].map((role, index) => {
                    const count = logs.filter(log => log.userRole === role).length;
                    const percentage = (count / logs.length) * 100;
                    return (
                      <div key={index} className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm font-medium">{role}</span>
                          <span className="text-sm text-gray-600">{count} ({percentage.toFixed(1)}%)</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-purple-600 h-2 rounded-full" 
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AuditLogsPage;
