/**
 * Performance Optimizer Component
 * Provides real-time performance monitoring and optimization suggestions
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Zap,
  TrendingUp,
  Clock,
  Database,
  Wifi,
  AlertTriangle,
  CheckCircle,
  Activity,
  Gauge
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { RTLText, RTLFlex } from '../common';

interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  memoryUsage: number;
  networkLatency: number;
  bundleSize: number;
  cacheHitRate: number;
}

interface OptimizationSuggestion {
  id: string;
  type: 'critical' | 'warning' | 'info';
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  effort: 'easy' | 'medium' | 'hard';
  action?: () => void;
}

export const PerformanceOptimizer: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    loadTime: 0,
    renderTime: 0,
    memoryUsage: 0,
    networkLatency: 0,
    bundleSize: 0,
    cacheHitRate: 0
  });

  const [suggestions, setSuggestions] = useState<OptimizationSuggestion[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [performanceScore, setPerformanceScore] = useState(0);

  // Performance monitoring
  const measurePerformance = useCallback(() => {
    // Measure page load time
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    const loadTime = navigation.loadEventEnd - navigation.loadEventStart;

    // Measure memory usage (if available)
    const memoryInfo = (performance as any).memory;
    const memoryUsage = memoryInfo ? memoryInfo.usedJSHeapSize / 1024 / 1024 : 0;

    // Simulate network latency measurement
    const networkLatency = Math.random() * 100 + 50;

    // Simulate bundle size (would be actual in production)
    const bundleSize = 2.5; // MB

    // Simulate cache hit rate
    const cacheHitRate = Math.random() * 40 + 60;

    // Measure render time using Performance Observer
    let renderTime = 0;
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          renderTime = entries.reduce((sum, entry) => sum + entry.duration, 0);
        });
        observer.observe({ entryTypes: ['measure'] });
      } catch (e) {
        renderTime = Math.random() * 50 + 10;
      }
    }

    const newMetrics: PerformanceMetrics = {
      loadTime: loadTime || Math.random() * 1000 + 500,
      renderTime: renderTime || Math.random() * 50 + 10,
      memoryUsage,
      networkLatency,
      bundleSize,
      cacheHitRate
    };

    setMetrics(newMetrics);
    generateSuggestions(newMetrics);
    calculatePerformanceScore(newMetrics);
  }, []);

  // Generate optimization suggestions based on metrics
  const generateSuggestions = (metrics: PerformanceMetrics) => {
    const newSuggestions: OptimizationSuggestion[] = [];

    if (metrics.loadTime > 3000) {
      newSuggestions.push({
        id: 'slow-load',
        type: 'critical',
        title: t("common.slow.page.load", "Slow Page Load Time"),
        description: `Page load time is ${(metrics.loadTime / 1000).toFixed(1)}s. Consider code splitting and lazy loading.`,
        impact: 'high',
        effort: 'medium'
      });
    }

    if (metrics.bundleSize > 3) {
      newSuggestions.push({
        id: 'large-bundle',
        type: 'warning',
        title: t("common.large.bundle.size", "Large Bundle Size"),
        description: `Bundle size is ${metrics.bundleSize.toFixed(1)}MB. Consider tree shaking and code splitting.`,
        impact: 'medium',
        effort: 'medium'
      });
    }

    if (metrics.memoryUsage > 50) {
      newSuggestions.push({
        id: 'high-memory',
        type: 'warning',
        title: t("common.high.memory.usage", "High Memory Usage"),
        description: `Memory usage is ${metrics.memoryUsage.toFixed(1)}MB. Check for memory leaks.`,
        impact: 'medium',
        effort: 'hard'
      });
    }

    if (metrics.cacheHitRate < 70) {
      newSuggestions.push({
        id: 'low-cache',
        type: 'info',
        title: t("common.low.cache.hit", "Low Cache Hit Rate"),
        description: `Cache hit rate is ${metrics.cacheHitRate.toFixed(1)}%. Improve caching strategy.`,
        impact: 'medium',
        effort: 'easy'
      });
    }

    if (metrics.networkLatency > 200) {
      newSuggestions.push({
        id: 'high-latency',
        type: 'warning',
        title: t("common.high.network.latency", "High Network Latency"),
        description: `Network latency is ${metrics.networkLatency.toFixed(0)}ms. Consider CDN optimization.`,
        impact: 'high',
        effort: 'easy'
      });
    }

    setSuggestions(newSuggestions);
  };

  // Calculate overall performance score
  const calculatePerformanceScore = (metrics: PerformanceMetrics) => {
    let score = 100;

    // Deduct points based on metrics
    if (metrics.loadTime > 3000) score -= 20;
    else if (metrics.loadTime > 2000) score -= 10;

    if (metrics.bundleSize > 3) score -= 15;
    else if (metrics.bundleSize > 2) score -= 8;

    if (metrics.memoryUsage > 50) score -= 15;
    else if (metrics.memoryUsage > 30) score -= 8;

    if (metrics.cacheHitRate < 70) score -= 10;
    else if (metrics.cacheHitRate < 80) score -= 5;

    if (metrics.networkLatency > 200) score -= 15;
    else if (metrics.networkLatency > 100) score -= 8;

    setPerformanceScore(Math.max(0, score));
  };

  // Auto-optimization actions
  const optimizeImages = () => {
    
    // In a real app, this would trigger image optimization
  };

  const enableServiceWorker = () => {
    
    // In a real app, this would register a service worker
  };

  const clearCache = () => {
    
    if ('caches' in window) {
      caches.keys().then(names => {
        names.forEach(name => caches.delete(name));
      });
    }
  };

  useEffect(() => {
    measurePerformance();

    if (isMonitoring) {
      const interval = setInterval(measurePerformance, 5000);
      return () => clearInterval(interval);
    }
  }, [isMonitoring, measurePerformance]);

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-400';
    if (score >= 70) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getScoreLabel = (score: number) => {
    if (score >= 90) return t("common.excellent", "Excellent");
    if (score >= 70) return 'Good';
    if (score >= 50) return t("common.needs.improvement.return", "Needs Improvement");
    return 'Poor';
  };

  const getSuggestionIcon = (type: OptimizationSuggestion['type']) => {
    switch (type) {
      case 'critical': return <AlertTriangle className="text-red-400" size={20} />;
      case 'warning': return <AlertTriangle className="text-yellow-400" size={20} />;
      case 'info': return <CheckCircle className="text-blue-400" size={20} />;
    }
  };

  return (
    <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6">
      {/* Header */}
      <RTLFlex className="items-center justify-between mb-6">
        <RTLFlex className="items-center">
          <Zap className="text-purple-400" size={24} />
          <RTLText className={`text-xl font-bold text-white ${isRTL ? 'mr-3' : 'ml-3'}`}>
            Performance Optimizer
          </RTLText>
        </RTLFlex>

        <button
          onClick={() => setIsMonitoring(!isMonitoring)}
          className={`px-4 py-2 rounded-lg font-medium transition-colors ${
            isMonitoring
              ? 'bg-red-600 hover:bg-red-700 text-white'
              : 'bg-green-600 hover:bg-green-700 text-white'}
          }`}
        >
          {isMonitoring ? t("common.stop.monitoring.start", "Stop Monitoring") : t("common.start.monitoring", "Start Monitoring")}
        </button>
      </RTLFlex>

      {/* Performance Score */}
      <div className="text-center mb-6">
        <div className={`text-4xl font-bold ${getScoreColor(performanceScore)} mb-2`}>
          {performanceScore}
        </div>
        <div className="text-gray-400">
          Performance Score - {getScoreLabel(performanceScore)}
        </div>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-gray-800/50 rounded-lg p-4">
          <RTLFlex className="items-center mb-2">
            <Clock className="text-blue-400" size={20} />
            <RTLText className={`font-medium text-white ${isRTL ? 'mr-2' : 'ml-2'}`}>
              Load Time
            </RTLText>
          </RTLFlex>
          <div className="text-2xl font-bold text-white">
            {(metrics.loadTime / 1000).toFixed(1)}s
          </div>
        </div>

        <div className="bg-gray-800/50 rounded-lg p-4">
          <RTLFlex className="items-center mb-2">
            <Activity className="text-green-400" size={20} />
            <RTLText className={`font-medium text-white ${isRTL ? 'mr-2' : 'ml-2'}`}>
              Render Time
            </RTLText>
          </RTLFlex>
          <div className="text-2xl font-bold text-white">
            {metrics.renderTime.toFixed(1)}ms
          </div>
        </div>

        <div className="bg-gray-800/50 rounded-lg p-4">
          <RTLFlex className="items-center mb-2">
            <Database className="text-purple-400" size={20} />
            <RTLText className={`font-medium text-white ${isRTL ? 'mr-2' : 'ml-2'}`}>
              Memory
            </RTLText>
          </RTLFlex>
          <div className="text-2xl font-bold text-white">
            {metrics.memoryUsage.toFixed(1)}MB
          </div>
        </div>

        <div className="bg-gray-800/50 rounded-lg p-4">
          <RTLFlex className="items-center mb-2">
            <Wifi className="text-yellow-400" size={20} />
            <RTLText className={`font-medium text-white ${isRTL ? 'mr-2' : 'ml-2'}`}>
              Latency
            </RTLText>
          </RTLFlex>
          <div className="text-2xl font-bold text-white">
            {metrics.networkLatency.toFixed(0)}ms
          </div>
        </div>

        <div className="bg-gray-800/50 rounded-lg p-4">
          <RTLFlex className="items-center mb-2">
            <Gauge className="text-orange-400" size={20} />
            <RTLText className={`font-medium text-white ${isRTL ? 'mr-2' : 'ml-2'}`}>
              Bundle Size
            </RTLText>
          </RTLFlex>
          <div className="text-2xl font-bold text-white">
            {metrics.bundleSize.toFixed(1)}MB
          </div>
        </div>

        <div className="bg-gray-800/50 rounded-lg p-4">
          <RTLFlex className="items-center mb-2">
            <TrendingUp className="text-cyan-400" size={20} />
            <RTLText className={`font-medium text-white ${isRTL ? 'mr-2' : 'ml-2'}`}>
              Cache Hit Rate
            </RTLText>
          </RTLFlex>
          <div className="text-2xl font-bold text-white">
            {metrics.cacheHitRate.toFixed(1)}%
          </div>
        </div>
      </div>

      {/* Optimization Suggestions */}
      {suggestions.length > 0 && (
        <div>
          <h3 className="text-lg font-semibold text-white mb-4">
            Optimization Suggestions
          </h3>
          <div className="space-y-3">
            {suggestions.map((suggestion) => (
              <div
                key={suggestion.id}
                className="bg-gray-800/50 rounded-lg p-4 border-l-4 border-l-purple-500"
              >
                <RTLFlex className="items-start justify-between">
                  <RTLFlex className="items-start">
                    {getSuggestionIcon(suggestion.type)}
                    <div className={isRTL ? 'mr-3' : 'ml-3'}>
                      <h4 className="font-medium text-white mb-1">
                        {suggestion.title}
                      </h4>
                      <p className="text-sm text-gray-400 mb-2">
                        {suggestion.description}
                      </p>
                      <RTLFlex className="items-center gap-4">
                        <span className={`text-xs px-2 py-1 rounded ${
                          suggestion.impact === 'high' ? 'bg-red-900/50 text-red-300' :
                          suggestion.impact === 'medium' ? 'bg-yellow-900/50 text-yellow-300' :
                          'bg-blue-900/50 text-blue-300'}
                        }`}>
                          {suggestion.impact} impact
                        </span>
                        <span className={`text-xs px-2 py-1 rounded ${
                          suggestion.effort === 'easy' ? 'bg-green-900/50 text-green-300' :
                          suggestion.effort === 'medium' ? 'bg-yellow-900/50 text-yellow-300' :
                          'bg-red-900/50 text-red-300'}
                        }`}>
                          {suggestion.effort} effort
                        </span>
                      </RTLFlex>
                    </div>
                  </RTLFlex>

                  {suggestion.action && (
                    <button
                      onClick={suggestion.action}
                      className="px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded transition-colors"
                    >
                      Fix
                    </button>
                  )}
                </RTLFlex>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="mt-6 pt-6 border-t border-gray-700/50">
        <h3 className="text-lg font-semibold text-white mb-4">{t("common.quick.actions", "Quick Actions")}</h3>
        <RTLFlex className={`gap-3 flex-wrap ${isRTL ? "flex-row-reverse" : ""}`}>
          <button
            onClick={optimizeImages}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            Optimize Images
          </button>
          <button
            onClick={enableServiceWorker}
            className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
          >
            Enable Service Worker
          </button>
          <button
            onClick={clearCache}
            className="px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-lg transition-colors"
          >
            Clear Cache
          </button>
          <button
            onClick={measurePerformance}
            className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
          >
            Refresh Metrics
          </button>
        </RTLFlex>
      </div>
    </div>
  );
};

export default PerformanceOptimizer;
