import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  ArcElement,
  RadialLinearScale,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  ArcElement,
  RadialLinearScale,
  Title,
  Tooltip,
  Legend
);

// Default chart options
export const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const,
      labels: {
        color: '#e2e8f0', // text-gray-200
      },
    },
    title: {
      display: true,
      color: '#e2e8f0', // text-gray-200
    },
  },
  scales: {
    x: {
      ticks: {
        color: '#94a3b8', // text-gray-400
      },
      grid: {
        color: 'rgba(71, 85, 105, 0.2)', // text-gray-600 with opacity
      },
    },
    y: {
      ticks: {
        color: '#94a3b8', // text-gray-400
      },
      grid: {
        color: 'rgba(71, 85, 105, 0.2)', // text-gray-600 with opacity
      },
      beginAtZero: true,
    },
  },
};

// Doughnut chart options
export const doughnutOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'right' as const,
      labels: {
        color: '#e2e8f0',
      },
    },
    title: {
      display: true,
      color: '#e2e8f0',
    },
  },
};

// Common chart colors
export const chartColors = {
  indigo: {
    primary: 'rgba(99, 102, 241, 1)', // indigo-500
    light: 'rgba(99, 102, 241, 0.5)',
  },
  purple: {
    primary: 'rgba(139, 92, 246, 1)', // purple-500
    light: 'rgba(139, 92, 246, 0.5)',
  },
  blue: {
    primary: 'rgba(59, 130, 246, 1)', // blue-500
    light: 'rgba(59, 130, 246, 0.5)',
  },
  sky: {
    primary: 'rgba(14, 165, 233, 1)', // sky-500
    light: 'rgba(14, 165, 233, 0.7)',
  },
  red: {
    primary: 'rgba(239, 68, 68, 1)', // red-500
    light: 'rgba(239, 68, 68, 0.7)',
  },
  orange: {
    primary: 'rgba(249, 115, 22, 1)', // orange-500
    light: 'rgba(249, 115, 22, 0.7)',
  },
  amber: {
    primary: 'rgba(245, 158, 11, 1)', // amber-500
    light: 'rgba(245, 158, 11, 0.7)',
  },
  emerald: {
    primary: 'rgba(16, 185, 129, 1)', // emerald-500
    light: 'rgba(16, 185, 129, 0.7)',
  },
  cyan: {
    primary: 'rgba(6, 182, 212, 1)', // cyan-500
    light: 'rgba(6, 182, 212, 0.7)',
  },
};
