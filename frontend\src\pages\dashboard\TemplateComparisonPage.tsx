import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
// MainLayout removed - handled by routing system
import { RTLText, RTLFlex } from '../../components/common';
import {
  ArrowLeft,
  Plus,
  X,
  Check,
  Star,
  Clock,
  Users,
  FileText,
  Zap,
  Crown,
  BarChart3,
  Target,
  DollarSign,
  TrendingUp,
  Shield,
  Globe,
  Smartphone
} from 'lucide-react';

interface Template {
  id: number;
  name: string;
  description: string;
  category: string;
  type: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime: number;
  sections: number;
  popularity: number;
  rating: number;
  price: number;
  isPremium: boolean;
  features: string[];
  pros: string[];
  cons: string[];
  bestFor: string[];
  author: string;
  lastUpdated: string;
  downloads: number;
  supportLevel: 'basic' | 'standard' | 'premium';
}

const TemplateComparisonPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const [selectedTemplates, setSelectedTemplates] = useState<Template[]>([]);
  const [availableTemplates, setAvailableTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);

  useEffect(() => {
    loadTemplates();

    // Load templates from URL params
    const templateIds = searchParams.get('templates')?.split(',').map(Number) || [];
    if (templateIds.length > 0) {
      loadSelectedTemplates(templateIds);
    }
  }, [searchParams]);

  const loadTemplates = async () => {
    try {
      const { businessPlanTemplatesAPI } = await import('../../services/templateCustomizationApi');
      const templatesData = await businessPlanTemplatesAPI.getTemplates();

      // Transform API data to match component interface
      const transformedTemplates: Template[] = templatesData.map(template => ({
        id: template.id,
        name: template.name,
        description: template.description,
        category: template.industry?.toLowerCase().replace(/\s+/g, '_') || 'general',
        type: template.template_type || 'standard',
        difficulty: template.difficulty_level || 'intermediate',
        estimatedTime: template.estimated_time || 8,
        sections: Object.keys(template.sections?.sections || {}).length || 0,
        popularity: Math.min(Math.round((template.usage_count || 0) / 100), 5),
        rating: template.rating || 0,
        price: template.price || 0, // Use real price from API
        isPremium: template.template_type ? ['saas', 'mobile_app', 'fintech', 'ai_startup', 'blockchain'].includes(template.template_type) : false,
        features: [], // This would come from template metadata
        pros: [], // This would come from template metadata
        cons: [], // This would come from template metadata
        bestFor: [], // This would come from template metadata
        author: template.author || template.created_by?.username || t("common.system", "System"),
        lastUpdated: template.updated_at || template.created_at || new Date().toISOString(),
        downloads: template.usage_count || 0,
        supportLevel: 'standard' // This would come from template metadata
      }));

      setAvailableTemplates(transformedTemplates);
    } catch (error) {
      console.error('Error loading templates:', error);
      setError('Failed to load templates. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const loadSelectedTemplates = (templateIds: number[]) => {
    const templates = availableTemplates.filter(t => templateIds.includes(t.id));
    setSelectedTemplates(templates);
  };

  const addTemplate = (template: Template) => {
    if (selectedTemplates.length < 3 && !selectedTemplates.find(t => t.id === template.id)) {
      const newSelected = [...selectedTemplates, template];
      setSelectedTemplates(newSelected);

      // Update URL
      const templateIds = newSelected.map(t => t.id).join(',');
      navigate(`/dashboard/templates/compare?templates=${templateIds}`, { replace: true });
    }
    setShowAddModal(false);
  };

  const removeTemplate = (templateId: number) => {
    const newSelected = selectedTemplates.filter(t => t.id !== templateId);
    setSelectedTemplates(newSelected);

    if (newSelected.length === 0) {
      navigate('/dashboard/templates/compare', { replace: true });
    } else {
      const templateIds = newSelected.map(t => t.id).join(',');
      navigate(`/dashboard/templates/compare?templates=${templateIds}`, { replace: true });
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'text-green-400 bg-green-900/30';
      case 'intermediate': return 'text-yellow-400 bg-yellow-900/30';
      case 'advanced': return 'text-red-400 bg-red-900/30';
      default: return 'text-gray-400 bg-gray-900/30';
    }
  };

  const getSupportIcon = (level: string) => {
    switch (level) {
      case 'basic': return <Shield size={16} className="text-gray-400" />;
      case 'standard': return <Shield size={16} className="text-blue-400" />;
      case 'premium': return <Shield size={16} className="text-purple-400" />;
      default: return <Shield size={16} className="text-gray-400" />;
    }
  };

  const handleUseTemplate = (template: Template) => {
    navigate(`/dashboard/business-plans/new?template=${template.id}`);
  };

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="space-y-6">
        {/* Header */}
        <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`flex items-center gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            <button
              onClick={() => navigate('/dashboard/templates')}
              className="p-2 bg-gray-800 hover:bg-gray-700 rounded-lg transition-colors"
            >
              <ArrowLeft size={20} />
            </button>

            <div>
              <RTLText as="h1" className="text-3xl font-bold text-white mb-2">
                {t('templates.comparison.title')}
              </RTLText>
              <RTLText className="text-gray-300">
                {t('templates.comparison.description')}
              </RTLText>
            </div>
          </div>

          {selectedTemplates.length < 3 && (
            <button
              onClick={() => setShowAddModal(true)}
              className={`flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
            >
              <Plus size={16} />
              {t('templates.comparison.addTemplate')}
            </button>
          )}
        </div>

        {/* Empty State */}
        {selectedTemplates.length === 0 && (
          <div className="text-center py-12">
            <BarChart3 size={64} className="mx-auto text-gray-500 mb-4" />
            <RTLText as="h3" className="text-xl font-semibold text-gray-300 mb-2">
              {t('templates.comparison.noTemplates')}
            </RTLText>
            <RTLText className="text-gray-400 mb-6">
              {t('templates.comparison.noTemplatesDescription')}
            </RTLText>
            <button
              onClick={() => setShowAddModal(true)}
              className="px-6 py-3 bg-purple-600 hover:bg-purple-700 rounded-lg text-white transition-colors"
            >
              {t('templates.comparison.addFirstTemplate')}
            </button>
          </div>
        )}

        {/* Comparison Table */}
        {selectedTemplates.length > 0 && (
          <div className="bg-gray-800/50 rounded-lg border border-gray-700 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-700">
                    <th className={` p-4 text-gray-300 font-medium w-48 ${isRTL ? "text-right" : "text-left"}`}>
                      {t('templates.comparison.feature')}
                    </th>
                    {selectedTemplates.map(template => (
                      <th key={template.id} className="text-center p-4 min-w-64">
                        <div className="space-y-2">
                          <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
                            <RTLText className="text-lg font-semibold text-white">
                              {template.name}
                            </RTLText>
                            <button
                              onClick={() => removeTemplate(template.id)}
                              className="p-1 text-gray-400 hover:text-red-400 transition-colors"
                            >
                              <X size={16} />
                            </button>
                          </div>

                          <div className={`flex items-center justify-center gap-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                            <div className={`flex items-center gap-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                              <Star size={14} className="text-yellow-400" fill="currentColor" />
                              <span className="text-sm text-gray-300">{template.rating}</span>
                            </div>
                            {template.isPremium && <Crown size={14} className="text-yellow-400" />}
                          </div>

                          <button
                            onClick={() => handleUseTemplate(template)}
                            className="w-full px-3 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white text-sm transition-colors"
                          >
                            {t('templates.use')}
                          </button>
                        </div>
                      </th>
                    ))}
                  </tr>
                </thead>

                <tbody>
                  {/* Basic Info */}
                  <tr className="border-b border-gray-700/50">
                    <td className="p-4 text-gray-300 font-medium">
                      {t('templates.comparison.category')}
                    </td>
                    {selectedTemplates.map(template => (
                      <td key={template.id} className="p-4 text-center text-gray-300">
                        {t(`templates.categories.${template.category}`)}
                      </td>
                    ))}
                  </tr>

                  <tr className="border-b border-gray-700/50">
                    <td className="p-4 text-gray-300 font-medium">
                      {t('templates.comparison.difficulty')}
                    </td>
                    {selectedTemplates.map(template => (
                      <td key={template.id} className="p-4 text-center">
                        <span className={`px-2 py-1 rounded-full text-xs ${getDifficultyColor(template.difficulty)}`}>
                          {t(`templates.difficulty.${template.difficulty}`)}
                        </span>
                      </td>
                    ))}
                  </tr>

                  <tr className="border-b border-gray-700/50">
                    <td className="p-4 text-gray-300 font-medium">
                      {t('templates.comparison.estimatedTime')}
                    </td>
                    {selectedTemplates.map(template => (
                      <td key={template.id} className="p-4 text-center text-gray-300">
                        <div className={`flex items-center justify-center gap-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                          <Clock size={14} />
                          {template.estimatedTime}h
                        </div>
                      </td>
                    ))}
                  </tr>

                  <tr className="border-b border-gray-700/50">
                    <td className="p-4 text-gray-300 font-medium">
                      {t('templates.comparison.sections')}
                    </td>
                    {selectedTemplates.map(template => (
                      <td key={template.id} className="p-4 text-center text-gray-300">
                        <div className={`flex items-center justify-center gap-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                          <FileText size={14} />
                          {template.sections}
                        </div>
                      </td>
                    ))}
                  </tr>

                  <tr className="border-b border-gray-700/50">
                    <td className="p-4 text-gray-300 font-medium">
                      {t('templates.comparison.price')}
                    </td>
                    {selectedTemplates.map(template => (
                      <td key={template.id} className="p-4 text-center">
                        <div className={`flex items-center justify-center gap-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                          <DollarSign size={14} className="text-green-400" />
                          <span className="text-green-400 font-semibold">
                            {template.price === 0 ? t('common.free') : `$${template.price}`}
                          </span>
                        </div>
                      </td>
                    ))}
                  </tr>

                  <tr className="border-b border-gray-700/50">
                    <td className="p-4 text-gray-300 font-medium">
                      {t('templates.comparison.support')}
                    </td>
                    {selectedTemplates.map(template => (
                      <td key={template.id} className="p-4 text-center">
                        <div className={`flex items-center justify-center gap-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                          {getSupportIcon(template.supportLevel)}
                          <span className="text-gray-300 capitalize">
                            {t(`templates.support.${template.supportLevel}`)}
                          </span>
                        </div>
                      </td>
                    ))}
                  </tr>

                  {/* Features */}
                  <tr className="border-b border-gray-700/50">
                    <td className="p-4 text-gray-300 font-medium">
                      {t('templates.comparison.keyFeatures')}
                    </td>
                    {selectedTemplates.map(template => (
                      <td key={template.id} className="p-4">
                        <ul className="space-y-1 text-sm text-gray-300">
                          {template.features.slice(0, 3).map((feature, index) => (
                            <li key={index} className={`flex items-center gap-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                              <Check size={12} className={`text-green-400 flex-shrink-0 ${isRTL ? "flex-row-reverse" : ""}`} />
                              {feature}
                            </li>
                          ))}
                          {template.features.length > 3 && (
                            <li className="text-gray-400 text-xs">
                              +{template.features.length - 3} {t('common.more')}
                            </li>
                          )}
                        </ul>
                      </td>
                    ))}
                  </tr>

                  {/* Best For */}
                  <tr className="border-b border-gray-700/50">
                    <td className="p-4 text-gray-300 font-medium">
                      {t('templates.comparison.bestFor')}
                    </td>
                    {selectedTemplates.map(template => (
                      <td key={template.id} className="p-4">
                        <div className={`flex flex-wrap gap-1 justify-center ${isRTL ? "flex-row-reverse" : ""}`}>
                          {template.bestFor.map((item, index) => (
                            <span
                              key={index}
                              className="px-2 py-1 bg-purple-900/30 text-purple-300 text-xs rounded-full"
                            >
                              {item}
                            </span>
                          ))}
                        </div>
                      </td>
                    ))}
                  </tr>

                  {/* Pros */}
                  <tr className="border-b border-gray-700/50">
                    <td className="p-4 text-gray-300 font-medium">
                      {t('templates.comparison.pros')}
                    </td>
                    {selectedTemplates.map(template => (
                      <td key={template.id} className="p-4">
                        <ul className="space-y-1 text-sm text-green-300">
                          {template.pros.map((pro, index) => (
                            <li key={index} className={`flex items-center gap-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                              <Check size={12} className={`text-green-400 flex-shrink-0 ${isRTL ? "flex-row-reverse" : ""}`} />
                              {pro}
                            </li>
                          ))}
                        </ul>
                      </td>
                    ))}
                  </tr>

                  {/* Cons */}
                  <tr>
                    <td className="p-4 text-gray-300 font-medium">
                      {t('templates.comparison.cons')}
                    </td>
                    {selectedTemplates.map(template => (
                      <td key={template.id} className="p-4">
                        <ul className="space-y-1 text-sm text-red-300">
                          {template.cons.map((con, index) => (
                            <li key={index} className={`flex items-center gap-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                              <X size={12} className={`text-red-400 flex-shrink-0 ${isRTL ? "flex-row-reverse" : ""}`} />
                              {con}
                            </li>
                          ))}
                        </ul>
                      </td>
                    ))}
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Add Template Modal */}
        {showAddModal && (
          <div className={`fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className="bg-gray-800 rounded-lg border border-gray-700 w-full max-w-4xl max-h-[80vh] overflow-hidden">
              <div className={`flex items-center justify-between p-6 border-b border-gray-700 ${isRTL ? "flex-row-reverse" : ""}`}>
                <RTLText as="h3" className="text-xl font-semibold text-white">
                  {t('templates.comparison.selectTemplate')}
                </RTLText>
                <button
                  onClick={() => setShowAddModal(false)}
                  className="p-2 text-gray-400 hover:text-white transition-colors"
                >
                  <X size={20} />
                </button>
              </div>

              <div className="p-6 overflow-y-auto max-h-[60vh]">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {availableTemplates
                    .filter(template => !selectedTemplates.find(t => t.id === template.id))
                    .map(template => (
                      <div
                        key={template.id}
                        className="bg-gray-700/50 rounded-lg p-4 border border-gray-600 hover:border-purple-500/50 transition-all cursor-pointer"
                        onClick={() => addTemplate(template)}
                      >
                        <div className={`flex items-center justify-between mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                          <RTLText as="h4" className="font-semibold text-white">
                            {template.name}
                          </RTLText>
                          {template.isPremium && <Crown size={16} className="text-yellow-400" />}
                        </div>

                        <RTLText className="text-gray-300 text-sm mb-3">
                          {template.description}
                        </RTLText>

                        <div className={`flex items-center justify-between text-xs ${isRTL ? "flex-row-reverse" : ""}`}>
                          <span className={`px-2 py-1 rounded-full ${getDifficultyColor(template.difficulty)}`}>
                            {t(`templates.difficulty.${template.difficulty}`)}
                          </span>
                          <div className={`flex items-center gap-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                            <Star size={12} className="text-yellow-400" fill="currentColor" />
                            <span className="text-gray-300">{template.rating}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
              </div>
        </div>
      </div>
    </div>
  );
};


