"""
Background tasks for the incubator app.
"""
import logging
from datetime import datetime, timedelta
from django.utils import timezone
from django.db.models import Q
from django.contrib.auth.models import User

from .models import MentorshipSession, MentorshipFeedback
from utils.email_service import (
    send_session_reminder,
    send_feedback_request,
    send_session_scheduled_notification,
    send_session_cancelled_notification,
    send_feedback_received_notification
)

logger = logging.getLogger(__name__)

def send_session_reminders():
    """
    Send reminders for upcoming mentorship sessions
    
    This function should be scheduled to run periodically (e.g., every hour)
    """
    now = timezone.now()
    
    # Find sessions that are scheduled to start in the next 24 hours
    # and haven't had reminders sent yet
    reminder_window_start = now
    reminder_window_end = now + timedelta(hours=24)
    
    upcoming_sessions = MentorshipSession.objects.filter(
        scheduled_at__gt=reminder_window_start,
        scheduled_at__lt=reminder_window_end,
        status__in=['scheduled', 'rescheduled'],
        reminder_sent=False
    )
    
    logger.info(f"Found {upcoming_sessions.count()} upcoming sessions that need reminders")
    
    for session in upcoming_sessions:
        # Get mentor and mentee emails
        mentor_email = session.mentorship_match.mentor.user.email
        mentee_email = session.mentorship_match.mentee.email
        
        # Send reminders
        mentor_reminder_sent = send_session_reminder(session, mentor_email)
        mentee_reminder_sent = send_session_reminder(session, mentee_email)
        
        if mentor_reminder_sent and mentee_reminder_sent:
            # Mark reminder as sent
            session.reminder_sent = True
            session.reminder_sent_at = now
            session.save(update_fields=['reminder_sent', 'reminder_sent_at'])
            logger.info(f"Sent reminders for session {session.id}: {session.title}")
        else:
            logger.error(f"Failed to send reminders for session {session.id}: {session.title}")


def send_feedback_requests():
    """
    Send feedback requests for recently completed mentorship sessions
    
    This function should be scheduled to run periodically (e.g., daily)
    """
    now = timezone.now()
    
    # Find sessions that were completed in the last 24 hours
    # and don't have feedback yet
    completed_window_start = now - timedelta(hours=24)
    completed_window_end = now
    
    completed_sessions = MentorshipSession.objects.filter(
        status='completed',
        ended_at__gt=completed_window_start,
        ended_at__lt=completed_window_end
    )
    
    logger.info(f"Found {completed_sessions.count()} recently completed sessions")
    
    for session in completed_sessions:
        # Check if feedback already exists
        has_mentor_feedback = MentorshipFeedback.objects.filter(
            session=session,
            is_from_mentee=False
        ).exists()
        
        has_mentee_feedback = MentorshipFeedback.objects.filter(
            session=session,
            is_from_mentee=True
        ).exists()
        
        # Get mentor and mentee emails
        mentor_email = session.mentorship_match.mentor.user.email
        mentee_email = session.mentorship_match.mentee.email
        
        # Send feedback requests if needed
        if not has_mentor_feedback:
            mentor_request_sent = send_feedback_request(session, mentor_email)
            if mentor_request_sent:
                logger.info(f"Sent feedback request to mentor for session {session.id}")
            else:
                logger.error(f"Failed to send feedback request to mentor for session {session.id}")
        
        if not has_mentee_feedback:
            mentee_request_sent = send_feedback_request(session, mentee_email)
            if mentee_request_sent:
                logger.info(f"Sent feedback request to mentee for session {session.id}")
            else:
                logger.error(f"Failed to send feedback request to mentee for session {session.id}")


def clean_up_old_sessions():
    """
    Clean up old sessions that were scheduled but never started
    
    This function should be scheduled to run periodically (e.g., daily)
    """
    now = timezone.now()
    
    # Find sessions that were scheduled to start more than 24 hours ago
    # but are still in 'scheduled' or 'rescheduled' status
    cutoff_time = now - timedelta(hours=24)
    
    old_sessions = MentorshipSession.objects.filter(
        scheduled_at__lt=cutoff_time,
        status__in=['scheduled', 'rescheduled']
    )
    
    logger.info(f"Found {old_sessions.count()} old sessions that need cleanup")
    
    for session in old_sessions:
        # Mark as cancelled
        session.status = 'cancelled'
        session.save(update_fields=['status'])
        
        # Get mentor and mentee emails
        mentor_email = session.mentorship_match.mentor.user.email
        mentee_email = session.mentorship_match.mentee.email
        
        # Send cancellation notifications
        cancellation_reason = "Session was automatically cancelled because it was not started as scheduled."
        
        mentor_notification_sent = send_session_cancelled_notification(
            session, mentor_email, cancellation_reason
        )
        
        mentee_notification_sent = send_session_cancelled_notification(
            session, mentee_email, cancellation_reason
        )
        
        if mentor_notification_sent and mentee_notification_sent:
            logger.info(f"Automatically cancelled session {session.id}: {session.title}")
        else:
            logger.error(f"Failed to send cancellation notifications for session {session.id}")


# Functions to be called from views

def notify_session_scheduled(session):
    """
    Send notifications when a session is scheduled
    
    Args:
        session: MentorshipSession instance
    """
    # Get mentor and mentee emails
    mentor_email = session.mentorship_match.mentor.user.email
    mentee_email = session.mentorship_match.mentee.email
    
    # Send notifications
    mentor_notification_sent = send_session_scheduled_notification(session, mentor_email)
    mentee_notification_sent = send_session_scheduled_notification(session, mentee_email)
    
    return mentor_notification_sent and mentee_notification_sent


def notify_session_cancelled(session, cancellation_reason=None):
    """
    Send notifications when a session is cancelled
    
    Args:
        session: MentorshipSession instance
        cancellation_reason: Reason for cancellation (optional)
    """
    # Get mentor and mentee emails
    mentor_email = session.mentorship_match.mentor.user.email
    mentee_email = session.mentorship_match.mentee.email
    
    # Send notifications
    mentor_notification_sent = send_session_cancelled_notification(
        session, mentor_email, cancellation_reason
    )
    
    mentee_notification_sent = send_session_cancelled_notification(
        session, mentee_email, cancellation_reason
    )
    
    return mentor_notification_sent and mentee_notification_sent


def notify_feedback_received(feedback):
    """
    Send notification when feedback is received
    
    Args:
        feedback: MentorshipFeedback instance
    """
    session = feedback.session
    
    # Determine recipient based on who provided the feedback
    if feedback.is_from_mentee:
        # Feedback is from mentee, notify mentor
        recipient_email = session.mentorship_match.mentor.user.email
    else:
        # Feedback is from mentor, notify mentee
        recipient_email = session.mentorship_match.mentee.email
    
    # Only send notification if feedback is not private or is from mentor
    if not feedback.is_private or not feedback.is_from_mentee:
        notification_sent = send_feedback_received_notification(feedback, recipient_email)
        return notification_sent
    
    return True  # No notification needed for private mentee feedback
