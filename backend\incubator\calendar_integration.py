"""
Calendar integration for mentorship sessions.
"""
import os
import json
import datetime
import logging
from django.conf import settings
from django.utils import timezone
from django.contrib.auth.models import User
from .models import MentorshipSession, MentorshipMatch

logger = logging.getLogger(__name__)

class CalendarIntegrationError(Exception):
    """Base exception for calendar integration errors"""
    pass

class CalendarProvider:
    """Base class for calendar providers"""
    
    def add_event(self, session_id, user_id):
        """Add a session to the user's calendar"""
        raise NotImplementedError("Subclasses must implement add_event")
    
    def update_event(self, session_id, user_id):
        """Update a session in the user's calendar"""
        raise NotImplementedError("Subclasses must implement update_event")
    
    def delete_event(self, session_id, user_id):
        """Delete a session from the user's calendar"""
        raise NotImplementedError("Subclasses must implement delete_event")
    
    def get_user_availability(self, user_id, start_date, end_date):
        """Get user's availability from their calendar"""
        raise NotImplementedError("Subclasses must implement get_user_availability")


class GoogleCalendarProvider(CalendarProvider):
    """Google Calendar integration"""
    
    def __init__(self):
        """Initialize with API credentials"""
        # This is a placeholder for Google Calendar integration
        # Actual implementation would require Google Calendar API and OAuth
        self.credentials_file = os.getenv('GOOGLE_CREDENTIALS_FILE')
        
    def _get_user_credentials(self, user_id):
        """Get OAuth credentials for the user"""
        # This is a placeholder
        # In a real implementation, this would retrieve the user's OAuth token
        return None
        
    def add_event(self, session_id, user_id):
        """
        Add a mentorship session to the user's Google Calendar
        
        Args:
            session_id: ID of the mentorship session
            user_id: ID of the user (mentor or mentee)
            
        Returns:
            dict: Event details including event_id and event_link
        """
        try:
            # Get session details
            session = MentorshipSession.objects.get(id=session_id)
            match = session.mentorship_match
            
            # Get user
            user = User.objects.get(id=user_id)
            
            # Determine if user is mentor or mentee
            is_mentor = hasattr(user, 'mentor_profile') and user.mentor_profile == match.mentor
            
            # Create event details
            event_details = {
                'summary': session.title,
                'description': session.description or f"Mentorship session for {match.business_idea.title}",
                'start': {
                    'dateTime': session.scheduled_at.isoformat(),
                    'timeZone': 'UTC',
                },
                'end': {
                    'dateTime': (session.scheduled_at + datetime.timedelta(minutes=session.duration_minutes)).isoformat(),
                    'timeZone': 'UTC',
                },
                'attendees': [
                    {'email': match.mentor.user.email},
                    {'email': match.mentee.email},
                ],
                'reminders': {
                    'useDefault': False,
                    'overrides': [
                        {'method': 'email', 'minutes': 24 * 60},
                        {'method': 'popup', 'minutes': 30},
                    ],
                },
            }
            
            # Add video conferencing details if available
            if session.video_provider and session.meeting_link:
                if session.video_provider == 'google_meet':
                    event_details['conferenceData'] = {
                        'createRequest': {
                            'requestId': f"yasmeen-{session.id}",
                            'conferenceSolutionKey': {
                                'type': 'hangoutsMeet'
                            }
                        }
                    }
                else:
                    # For non-Google Meet providers, add the link to the description
                    event_details['description'] += f"\n\nJoin the meeting: {session.meeting_link}"
                    if session.meeting_password:
                        event_details['description'] += f"\nPassword: {session.meeting_password}"
            
            # In a real implementation, this would call the Google Calendar API
            # For now, we'll just return a mock response
            event_id = f"event-{session.id}-{user.id}"
            event_link = f"https://calendar.google.com/calendar/event?eid={event_id}"
            
            # Store the event ID in the session
            if is_mentor:
                session.mentor_calendar_event_id = event_id
            else:
                session.mentee_calendar_event_id = event_id
            session.save(update_fields=['mentor_calendar_event_id', 'mentee_calendar_event_id'])
            
            return {
                'event_id': event_id,
                'event_link': event_link,
                'provider': 'google_calendar',
            }
            
        except MentorshipSession.DoesNotExist:
            raise CalendarIntegrationError(f"Session with ID {session_id} not found")
        except User.DoesNotExist:
            raise CalendarIntegrationError(f"User with ID {user_id} not found")
        except Exception as e:
            logger.error(f"Error adding event to Google Calendar: {str(e)}")
            raise CalendarIntegrationError(f"Error adding event to Google Calendar: {str(e)}")
    
    def update_event(self, session_id, user_id):
        """
        Update a mentorship session in the user's Google Calendar
        
        Args:
            session_id: ID of the mentorship session
            user_id: ID of the user (mentor or mentee)
            
        Returns:
            dict: Updated event details
        """
        try:
            # Get session details
            session = MentorshipSession.objects.get(id=session_id)
            user = User.objects.get(id=user_id)
            
            # Determine if user is mentor or mentee
            is_mentor = hasattr(user, 'mentor_profile') and user.mentor_profile == session.mentorship_match.mentor
            
            # Get the event ID
            event_id = session.mentor_calendar_event_id if is_mentor else session.mentee_calendar_event_id
            
            if not event_id:
                # If no event exists, create a new one
                return self.add_event(session_id, user_id)
            
            # In a real implementation, this would call the Google Calendar API
            # For now, we'll just return a mock response
            event_link = f"https://calendar.google.com/calendar/event?eid={event_id}"
            
            return {
                'event_id': event_id,
                'event_link': event_link,
                'provider': 'google_calendar',
                'updated': True
            }
            
        except MentorshipSession.DoesNotExist:
            raise CalendarIntegrationError(f"Session with ID {session_id} not found")
        except User.DoesNotExist:
            raise CalendarIntegrationError(f"User with ID {user_id} not found")
        except Exception as e:
            logger.error(f"Error updating event in Google Calendar: {str(e)}")
            raise CalendarIntegrationError(f"Error updating event in Google Calendar: {str(e)}")
    
    def delete_event(self, session_id, user_id):
        """
        Delete a mentorship session from the user's Google Calendar
        
        Args:
            session_id: ID of the mentorship session
            user_id: ID of the user (mentor or mentee)
            
        Returns:
            bool: True if successful
        """
        try:
            # Get session details
            session = MentorshipSession.objects.get(id=session_id)
            user = User.objects.get(id=user_id)
            
            # Determine if user is mentor or mentee
            is_mentor = hasattr(user, 'mentor_profile') and user.mentor_profile == session.mentorship_match.mentor
            
            # Get the event ID
            event_id = session.mentor_calendar_event_id if is_mentor else session.mentee_calendar_event_id
            
            if not event_id:
                # No event to delete
                return True
            
            # In a real implementation, this would call the Google Calendar API
            # For now, we'll just return success
            
            # Clear the event ID
            if is_mentor:
                session.mentor_calendar_event_id = None
            else:
                session.mentee_calendar_event_id = None
            session.save(update_fields=['mentor_calendar_event_id', 'mentee_calendar_event_id'])
            
            return True
            
        except MentorshipSession.DoesNotExist:
            raise CalendarIntegrationError(f"Session with ID {session_id} not found")
        except User.DoesNotExist:
            raise CalendarIntegrationError(f"User with ID {user_id} not found")
        except Exception as e:
            logger.error(f"Error deleting event from Google Calendar: {str(e)}")
            raise CalendarIntegrationError(f"Error deleting event from Google Calendar: {str(e)}")
    
    def get_user_availability(self, user_id, start_date, end_date):
        """
        Get user's availability from their Google Calendar
        
        Args:
            user_id: ID of the user
            start_date: Start date for availability check
            end_date: End date for availability check
            
        Returns:
            list: List of available time slots
        """
        try:
            # In a real implementation, this would call the Google Calendar API
            # For now, we'll just return mock data
            
            # Generate some available slots (9 AM to 5 PM, excluding lunch)
            available_slots = []
            current_date = start_date
            
            while current_date <= end_date:
                # Skip weekends
                if current_date.weekday() < 5:  # Monday to Friday
                    # Morning slots
                    for hour in range(9, 12):
                        available_slots.append({
                            'start': datetime.datetime.combine(current_date, datetime.time(hour, 0)),
                            'end': datetime.datetime.combine(current_date, datetime.time(hour + 1, 0)),
                        })
                    
                    # Afternoon slots
                    for hour in range(13, 17):
                        available_slots.append({
                            'start': datetime.datetime.combine(current_date, datetime.time(hour, 0)),
                            'end': datetime.datetime.combine(current_date, datetime.time(hour + 1, 0)),
                        })
                
                current_date += datetime.timedelta(days=1)
            
            return available_slots
            
        except User.DoesNotExist:
            raise CalendarIntegrationError(f"User with ID {user_id} not found")
        except Exception as e:
            logger.error(f"Error getting user availability from Google Calendar: {str(e)}")
            raise CalendarIntegrationError(f"Error getting user availability: {str(e)}")


# Factory function to get the appropriate calendar provider
def get_calendar_provider(provider_name):
    """
    Get calendar provider by name
    
    Args:
        provider_name: Name of the provider (google_calendar, outlook, etc.)
        
    Returns:
        CalendarProvider: Provider instance
    """
    providers = {
        'google_calendar': GoogleCalendarProvider(),
        # Add other providers as they are implemented
    }
    
    if provider_name not in providers:
        raise CalendarIntegrationError(f"Unsupported calendar provider: {provider_name}")
    
    return providers[provider_name]
