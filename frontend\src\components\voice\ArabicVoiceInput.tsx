/**
 * Arabic Voice Input Component
 * Advanced voice-to-text with Arabic language support
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Mic,
  MicOff,
  Volume2,
  VolumeX,
  Play,
  Pause,
  RotateCcw,
  Languages,
  Settings,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { RTLText, RTLFlex } from '../common';

interface VoiceInputProps {
  onTranscript: (text: string, language: string) => void;
  onError?: (error: string) => void;
  placeholder?: string;
  autoStart?: boolean;
  supportedLanguages?: string[];
  defaultLanguage?: string;
}

interface VoiceSettings {
  language: string;
  continuous: boolean;
  interimResults: boolean;
  maxAlternatives: number;
  noiseReduction: boolean;
  autoStop: boolean;
  autoStopDelay: number;
}

export const ArabicVoiceInput: React.FC<VoiceInputProps> = ({
  onTranscript,
  onError,
  placeholder = "Click the microphone to start speaking...",
  autoStart = false,
  supportedLanguages = ['ar-SA', 'en-US', 'ar-EG', 'ar-AE'],
  defaultLanguage = 'ar-SA'
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [interimTranscript, setInterimTranscript] = useState('');
  const [confidence, setConfidence] = useState(0);
  const [isSupported, setIsSupported] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [audioLevel, setAudioLevel] = useState(0);
  const [error, setError] = useState<string | null>(null);

  const [settings, setSettings] = useState<VoiceSettings>({
    language: defaultLanguage,
    continuous: true,
    interimResults: true,
    maxAlternatives: 3,
    noiseReduction: true,
    autoStop: true,
    autoStopDelay: 3000
  });

  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const microphoneRef = useRef<MediaStreamAudioSourceNode | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Language options with Arabic dialects
  const languageOptions = [
    { code: 'ar-SA', name: 'العربية (السعودية)', flag: '🇸🇦' },
    { code: 'ar-EG', name: 'العربية (مصر)', flag: '🇪🇬' },
    { code: 'ar-AE', name: 'العربية (الإمارات)', flag: '🇦🇪' },
    { code: 'ar-JO', name: 'العربية (الأردن)', flag: '🇯🇴' },
    { code: 'ar-KW', name: 'العربية (الكويت)', flag: '🇰🇼' },
    { code: 'ar-QA', name: 'العربية (قطر)', flag: '🇶🇦' },
    { code: 'en-US', name: 'English (US)', flag: '🇺🇸' },
    { code: 'en-GB', name: 'English (UK)', flag: '🇬🇧' }
  ];

  // Initialize speech recognition
  useEffect(() => {
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;

    if (SpeechRecognition) {
      setIsSupported(true);
      const recognition = new SpeechRecognition();

      recognition.continuous = settings.continuous;
      recognition.interimResults = settings.interimResults;
      recognition.lang = settings.language;
      recognition.maxAlternatives = settings.maxAlternatives;

      recognition.onstart = () => {
        setIsListening(true);
        setError(null);
        console.log("Voice recognition started");
      };

      recognition.onresult = (event) => {
        let finalTranscript = '';
        let interimTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const result = event.results[i];
          const transcriptText = result[0].transcript;

          if (result.isFinal) {
            finalTranscript += transcriptText;
            setConfidence(result[0].confidence);
          } else {
            interimTranscript += transcriptText;
          }
        }

        if (finalTranscript) {
          setTranscript(prev => prev + finalTranscript);
          onTranscript(finalTranscript, settings.language);

          // Auto-stop after delay if enabled
          if (settings.autoStop) {
            if (timeoutRef.current) clearTimeout(timeoutRef.current);
            timeoutRef.current = setTimeout(() => {
              stopListening();
            }, settings.autoStopDelay);
          }
        }

        setInterimTranscript(interimTranscript);
      };

      recognition.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        let errorMessage = 'Voice recognition error occurred';

        switch (event.error) {
          case 'no-speech':
            errorMessage = t("common.no.speech.detected", "No speech detected. Please try again.");
            break;
          case 'audio-capture':
            errorMessage = t("common.microphone.not.accessible", "Microphone not accessible. Please check permissions.");
            break;
          case 'not-allowed':
            errorMessage = t("common.microphone.permission.denied", "Microphone permission denied.");
            break;
          case 'network':
            errorMessage = 'Network error. Please check your connection.';
            break;
          case 'language-not-supported':
            errorMessage = t("common.selected.language.not", "Selected language not supported.");
            break;
        }

        setError(errorMessage);
        onError?.(errorMessage);
        setIsListening(false);
      };

      recognition.onend = () => {
        setIsListening(false);
        setInterimTranscript('');
        console.log('Voice recognition ended');
      };

      recognitionRef.current = recognition;
    } else {
      setIsSupported(false);
      setError(t("common.speech.recognition.not", "Speech recognition not supported in this browser"));
    }

    return () => {
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
    };
  }, [settings, onTranscript, onError]);

  // Initialize audio level monitoring
  const initializeAudioMonitoring = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      streamRef.current = stream;

      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const analyser = audioContext.createAnalyser();
      const microphone = audioContext.createMediaStreamSource(stream);

      analyser.fftSize = 256;
      microphone.connect(analyser);

      audioContextRef.current = audioContext;
      analyserRef.current = analyser;
      microphoneRef.current = microphone;

      // Monitor audio levels
      const dataArray = new Uint8Array(analyser.frequencyBinCount);
      const updateAudioLevel = () => {
        if (analyser && isListening) {
          analyser.getByteFrequencyData(dataArray);
          const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
          setAudioLevel(average / 255 * 100);
          requestAnimationFrame(updateAudioLevel);
        }
      };

      if (isListening) updateAudioLevel();

    } catch (error) {
      console.error('Error accessing microphone:', error);
      setError(t("common.could.not.access", "Could not access microphone"));
    }
  }, [isListening]);

  // Start listening
  const startListening = useCallback(async () => {
    if (!isSupported || !recognitionRef.current) {
      setError(t("common.speech.recognition.not", "Speech recognition not available"));
      return;
    }

    try {
      await initializeAudioMonitoring();
      recognitionRef.current.start();
    } catch (error) {
      console.error('Error starting recognition:', error);
      setError(t("common.failed.to.start", "Failed to start voice recognition"));
    }
  }, [isSupported, initializeAudioMonitoring]);

  // Stop listening
  const stopListening = useCallback(() => {
    if (recognitionRef.current && isListening) {
      recognitionRef.current.stop();
    }

    // Clean up audio monitoring
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }

    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }

    setAudioLevel(0);

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, [isListening]);

  // Toggle listening
  const toggleListening = () => {
    if (isListening) {
      stopListening();
    } else {
      startListening();
    }
  };

  // Clear transcript
  const clearTranscript = () => {
    setTranscript('');
    setInterimTranscript('');
    setConfidence(0);
    setError(null);
  };

  // Update settings
  const updateSettings = (newSettings: Partial<VoiceSettings>) => {
    setSettings(prev => ({ ...prev, ...newSettings }));

    // Update recognition settings if active
    if (recognitionRef.current) {
      recognitionRef.current.lang = newSettings.language || settings.language;
      recognitionRef.current.continuous = newSettings.continuous ?? settings.continuous;
      recognitionRef.current.interimResults = newSettings.interimResults ?? settings.interimResults;
      recognitionRef.current.maxAlternatives = newSettings.maxAlternatives || settings.maxAlternatives;
    }
  };

  // Auto-start if enabled
  useEffect(() => {
    if (autoStart && isSupported) {
      startListening();
    }
  }, [autoStart, isSupported, startListening]);

  if (!isSupported) {
    return (
      <div className="bg-red-900/20 border border-red-500/50 rounded-lg p-4">
        <RTLFlex className="items-center">
          <AlertCircle className="text-red-400" size={20} />
          <RTLText className={`text-red-300 ${isRTL ? 'mr-3' : 'ml-3'}`}>
            Voice recognition is not supported in this browser
          </RTLText>
        </RTLFlex>
      </div>
    );
  }

  return (
    <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6">
      {/* Header */}
      <RTLFlex className="items-center justify-between mb-4">
        <RTLFlex className="items-center">
          <Languages className="text-purple-400" size={24} />
          <RTLText className={`text-lg font-semibold text-white ${isRTL ? 'mr-3' : 'ml-3'}`}>
            Arabic Voice Input
          </RTLText>
        </RTLFlex>

        <button
          onClick={() => setShowSettings(!showSettings)}
          className="p-2 text-gray-400 hover:text-white transition-colors"
        >
          <Settings size={20} />
        </button>
      </RTLFlex>

      {/* Settings Panel */}
      {showSettings && (
        <div className="bg-gray-800/50 rounded-lg p-4 mb-4 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Language
            </label>
            <select
              value={settings.language}
              onChange={(e) => updateSettings({ language: e.target.value })}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-purple-500"
            >
              {languageOptions.map(lang => (
                <option key={lang.code} value={lang.code}>
                  {lang.flag} {lang.name}
                </option>
              ))}
            </select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <label className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <input
                type="checkbox"
                checked={settings.continuous}
                onChange={(e) => updateSettings({ continuous: e.target.checked })}
                className={`mr-2 rounded ${isRTL ? "space-x-reverse" : ""}`}
              />
              <span className="text-sm text-gray-300">t("common.continuous", "Continuous")</span>
            </label>

            <label className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <input
                type="checkbox"
                checked={settings.interimResults}
                onChange={(e) => updateSettings({ interimResults: e.target.checked })}
                className={`mr-2 rounded ${isRTL ? "space-x-reverse" : ""}`}
              />
              <span className="text-sm text-gray-300">t("common.interim.results", "Interim Results")</span>
            </label>

            <label className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <input
                type="checkbox"
                checked={settings.autoStop}
                onChange={(e) => updateSettings({ autoStop: e.target.checked })}
                className={`mr-2 rounded ${isRTL ? "space-x-reverse" : ""}`}
              />
              <span className="text-sm text-gray-300">t("common.auto.stop", "Auto Stop")</span>
            </label>
          </div>
        </div>
      )}

      {/* Main Controls */}
      <div className="text-center mb-6">
        <button
          onClick={toggleListening}
          disabled={!isSupported}
          className={`relative w-20 h-20 rounded-full transition-all duration-300 ${
            isListening
              ? 'bg-red-500 hover:bg-red-600 animate-pulse'
              : 'bg-purple-500 hover:bg-purple-600'}
          } ${!isSupported ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          {isListening ? (
            <MicOff className="text-white" size={32} />
          ) : (
            <Mic className="text-white" size={32} />
          )}

          {/* Audio level indicator */}
          {isListening && (
            <div
              className="absolute inset-0 rounded-full border-4 border-white/30"
              style={{
                transform: `scale(${1 + audioLevel / 100})`,
                transition: 'transform 0.1s ease'
              }}
            />
          )}
        </button>

        <div className="mt-4">
          <div className={`text-lg font-medium ${
            isListening ? 'text-red-400' : 'text-gray-400'}
          }`}>
            {isListening ? t("common.listening.click.to", "Listening...") : t("common.click.to.start", "Click to start")}
          </div>

          {confidence > 0 && (
            <div className="text-sm text-gray-500 mt-1">
              Confidence: {Math.round(confidence * 100)}%
            </div>
          )}
        </div>
      </div>

      {/* Audio Level Visualizer */}
      {isListening && (
        <div className="mb-4">
          <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
            <div
              className="h-full bg-gradient-to-r from-green-400 to-purple-500 transition-all duration-100"
              style={{ width: `${audioLevel}%` }}
            />
          </div>
        </div>
      )}

      {/* Transcript Display */}
      <div className="bg-gray-800/50 rounded-lg p-4 min-h-[120px] mb-4">
        <RTLFlex className="items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-300">{t("common.transcript", "Transcript")}</span>
          {transcript && (
            <button
              onClick={clearTranscript}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <RotateCcw size={16} />
            </button>
          )}
        </RTLFlex>

        <div className={`text-white ${isRTL ? 'text-right' : 'text-left'}`} dir={settings.language.startsWith('ar') ? 'rtl' : 'ltr'}>
          {transcript && (
            <div className="mb-2">
              {transcript}
              {confidence > 0 && (
                <CheckCircle className={`inline ml-2 text-green-400 ${isRTL ? "space-x-reverse" : ""}`} size={16} />
              )}
            </div>
          )}

          {interimTranscript && (
            <div className="text-gray-400 italic">
              {interimTranscript}
            </div>
          )}

          {!transcript && !interimTranscript && (
            <div className="text-gray-500 italic">
              {placeholder}
            </div>
          )}
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-900/20 border border-red-500/50 rounded-lg p-3 mb-4">
          <RTLFlex className="items-center">
            <AlertCircle className="text-red-400" size={16} />
            <span className={`text-red-300 text-sm ${isRTL ? 'mr-2' : 'ml-2'}`}>
              {error}
            </span>
          </RTLFlex>
        </div>
      )}

      {/* Action Buttons */}
      <RTLFlex className="gap-3">
        <button
          onClick={clearTranscript}
          disabled={!transcript}
          className={`flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-700 disabled:bg-gray-800 disabled:text-gray-500 text-white rounded-lg transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
        >
          Clear
        </button>

        <button
          onClick={() => onTranscript(transcript, settings.language)}
          disabled={!transcript}
          className={`flex-1 px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-800 disabled:text-gray-500 text-white rounded-lg transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
        >
          Use Text
        </button>
      </RTLFlex>
    </div>
  );
};

export default ArabicVoiceInput;
