import React, { useState, useEffect } from 'react';
import { 
  TrendingUp, TrendingDown, Activity, Clock, Cpu, HardDrive, 
  Network, Database, RefreshCw, AlertTriangle, CheckCircle,
  BarChart3, <PERSON>Chart, <PERSON><PERSON>hart, Monitor
} from 'lucide-react';
import { DashboardLayout } from '../dashboard';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';
import { getAuthToken } from '../../../services/api';

interface PerformanceMetric {
  id: string;
  name: string;
  current_value: number;
  unit: string;
  trend: 'up' | 'down' | 'stable';
  change_percent: number;
  status: 'good' | 'warning' | 'critical';
  historical_data: Array<{ timestamp: string; value: number }>;
}

interface SystemPerformance {
  cpu_metrics: {
    current: number;
    average_1h: number;
    peak_24h: number;
    cores: number;
  };
  memory_metrics: {
    current: number;
    available: number;
    total: number;
    swap_used: number;
  };
  disk_metrics: {
    io_read: number;
    io_write: number;
    usage_percent: number;
    free_space: number;
  };
  network_metrics: {
    bytes_sent: number;
    bytes_recv: number;
    packets_sent: number;
    packets_recv: number;
  };
}

const PerformanceMonitoring: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  const [performanceData, setPerformanceData] = useState<SystemPerformance | null>(null);
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([]);
  const [loading, setLoading] = useState(true);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [timeRange, setTimeRange] = useState('24h');

  const fetchPerformanceData = async () => {
    try {
      setLoading(true);
      
      const response = await fetch(`/api/superadmin/system/performance/?range=${timeRange}`, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setPerformanceData(data.current_metrics);
        
        // Transform historical data to metrics format
        const transformedMetrics: PerformanceMetric[] = [
          {
            id: 'cpu',
            name: 'CPU Usage',
            current_value: data.current_metrics?.cpu_usage || 0,
            unit: '%',
            trend: data.performance_summary?.cpu_trend || 'stable',
            change_percent: data.performance_summary?.cpu_change || 0,
            status: data.current_metrics?.cpu_usage > 80 ? 'critical' : 
                   data.current_metrics?.cpu_usage > 60 ? 'warning' : 'good',
            historical_data: data.historical_data?.cpu_usage || []
          },
          {
            id: 'memory',
            name: 'Memory Usage',
            current_value: data.current_metrics?.memory_usage || 0,
            unit: '%',
            trend: data.performance_summary?.memory_trend || 'stable',
            change_percent: data.performance_summary?.memory_change || 0,
            status: data.current_metrics?.memory_usage > 85 ? 'critical' : 
                   data.current_metrics?.memory_usage > 70 ? 'warning' : 'good',
            historical_data: data.historical_data?.memory_usage || []
          },
          {
            id: 'api_response',
            name: 'API Response Time',
            current_value: data.current_metrics?.api_response_time || 0,
            unit: 'ms',
            trend: data.performance_summary?.api_trend || 'stable',
            change_percent: data.performance_summary?.api_change || 0,
            status: data.current_metrics?.api_response_time > 1000 ? 'critical' : 
                   data.current_metrics?.api_response_time > 500 ? 'warning' : 'good',
            historical_data: data.historical_data?.api_response_time || []
          },
          {
            id: 'database',
            name: 'Database Performance',
            current_value: data.current_metrics?.database_response_time || 0,
            unit: 'ms',
            trend: data.performance_summary?.db_trend || 'stable',
            change_percent: data.performance_summary?.db_change || 0,
            status: data.current_metrics?.database_response_time > 100 ? 'critical' : 
                   data.current_metrics?.database_response_time > 50 ? 'warning' : 'good',
            historical_data: data.historical_data?.database_metrics || []
          }
        ];
        
        setMetrics(transformedMetrics);
      } else {
        console.error('Failed to fetch performance data:', response.status);
        setPerformanceData(null);
        setMetrics([]);
      }
    } catch (error) {
      console.error('Failed to fetch performance data:', error);
      setPerformanceData(null);
      setMetrics([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPerformanceData();
  }, [timeRange]);

  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      fetchPerformanceData();
    }, 30000);

    return () => clearInterval(interval);
  }, [autoRefresh, timeRange]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good': return 'text-green-400';
      case 'warning': return 'text-yellow-400';
      case 'critical': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getStatusBg = (status: string) => {
    switch (status) {
      case 'good': return 'bg-green-500/20';
      case 'warning': return 'bg-yellow-500/20';
      case 'critical': return 'bg-red-500/20';
      default: return 'bg-gray-500/20';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="w-4 h-4 text-red-400" />;
      case 'down': return <TrendingDown className="w-4 h-4 text-green-400" />;
      default: return <Activity className="w-4 h-4 text-gray-400" />;
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <DashboardLayout currentPage="performance">
      {/* Header */}
      <div className={`mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
        <div>
          <h1 className="text-2xl font-bold text-white">{t('admin.performance.monitoring', 'Performance Monitoring')}</h1>
          <div className="text-gray-400 mt-1">{t('admin.performance.description', 'Real-time system performance metrics and analysis')}</div>
        </div>
        <div className={`mt-4 sm:mt-0 flex items-center space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="bg-indigo-800 border border-indigo-600 text-white rounded-lg px-3 py-2 text-sm"
          >
            <option value="1h">Last Hour</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
          </select>
          <button
            onClick={fetchPerformanceData}
            disabled={loading}
            className="flex items-center space-x-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 rounded-lg text-sm font-medium transition-colors"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            <span>{t('system.refresh', 'Refresh')}</span>
          </button>
          <label className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="w-4 h-4 text-purple-600 bg-indigo-800 border-indigo-600 rounded focus:ring-purple-500"
            />
            <span className="text-gray-300 text-sm">{t('system.auto.refresh', 'Auto Refresh')}</span>
          </label>
        </div>
      </div>

      {loading && !performanceData ? (
        <div className="flex items-center justify-center py-12">
          <RefreshCw className="w-8 h-8 animate-spin text-blue-400" />
          <span className="ml-2 text-gray-400">Loading performance data...</span>
        </div>
      ) : (
        <>
          {/* Performance Metrics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {metrics.map((metric) => (
              <div key={metric.id} className="bg-indigo-900/30 backdrop-blur-sm rounded-xl p-6 border border-indigo-800/50 hover:border-purple-500/50 transition-all duration-300">
                <div className={`flex items-center justify-between mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className="flex items-center space-x-2">
                    {metric.id === 'cpu' && <Cpu className="w-5 h-5 text-blue-400" />}
                    {metric.id === 'memory' && <Monitor className="w-5 h-5 text-green-400" />}
                    {metric.id === 'api_response' && <Network className="w-5 h-5 text-purple-400" />}
                    {metric.id === 'database' && <Database className="w-5 h-5 text-orange-400" />}
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusBg(metric.status)} ${getStatusColor(metric.status)}`}>
                      {metric.status}
                    </span>
                  </div>
                  {getTrendIcon(metric.trend)}
                </div>
                <div className="mb-4">
                  <h3 className="text-2xl font-bold text-white mb-1">
                    {metric.current_value.toFixed(1)}{metric.unit}
                  </h3>
                  <p className="text-gray-400 text-sm">{metric.name}</p>
                </div>
                <div className={`flex items-center justify-between text-xs ${isRTL ? "flex-row-reverse" : ""}`}>
                  <span className={`${metric.change_percent >= 0 ? 'text-red-400' : 'text-green-400'}`}>
                    {metric.change_percent >= 0 ? '+' : ''}{metric.change_percent.toFixed(1)}%
                  </span>
                  <span className="text-gray-400">vs {timeRange}</span>
                </div>
              </div>
            ))}
          </div>

          {/* Detailed System Performance */}
          {performanceData && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* CPU & Memory Details */}
              <div className="bg-indigo-900/30 backdrop-blur-sm rounded-xl p-6 border border-indigo-800/50">
                <h3 className="text-lg font-semibold text-white mb-6 flex items-center">
                  <Cpu className="w-5 h-5 mr-2 text-blue-400" />
                  {t('performance.cpu.memory', 'CPU & Memory Details')}
                </h3>
                
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <span className="text-gray-400 text-sm">Current CPU</span>
                      <div className="text-white font-medium">{performanceData.cpu_metrics?.current?.toFixed(1)}%</div>
                    </div>
                    <div>
                      <span className="text-gray-400 text-sm">CPU Cores</span>
                      <div className="text-white font-medium">{performanceData.cpu_metrics?.cores || 'N/A'}</div>
                    </div>
                    <div>
                      <span className="text-gray-400 text-sm">Memory Used</span>
                      <div className="text-white font-medium">{performanceData.memory_metrics?.current?.toFixed(1)}%</div>
                    </div>
                    <div>
                      <span className="text-gray-400 text-sm">Available</span>
                      <div className="text-white font-medium">{formatBytes(performanceData.memory_metrics?.available || 0)}</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Network & Disk I/O */}
              <div className="bg-indigo-900/30 backdrop-blur-sm rounded-xl p-6 border border-indigo-800/50">
                <h3 className="text-lg font-semibold text-white mb-6 flex items-center">
                  <Network className="w-5 h-5 mr-2 text-purple-400" />
                  {t('performance.network.disk', 'Network & Disk I/O')}
                </h3>
                
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <span className="text-gray-400 text-sm">Bytes Sent</span>
                      <div className="text-white font-medium">{formatBytes(performanceData.network_metrics?.bytes_sent || 0)}</div>
                    </div>
                    <div>
                      <span className="text-gray-400 text-sm">Bytes Received</span>
                      <div className="text-white font-medium">{formatBytes(performanceData.network_metrics?.bytes_recv || 0)}</div>
                    </div>
                    <div>
                      <span className="text-gray-400 text-sm">Disk Read</span>
                      <div className="text-white font-medium">{formatBytes(performanceData.disk_metrics?.io_read || 0)}/s</div>
                    </div>
                    <div>
                      <span className="text-gray-400 text-sm">Disk Write</span>
                      <div className="text-white font-medium">{formatBytes(performanceData.disk_metrics?.io_write || 0)}/s</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </>
      )}
    </DashboardLayout>
  );
};

export default PerformanceMonitoring;
