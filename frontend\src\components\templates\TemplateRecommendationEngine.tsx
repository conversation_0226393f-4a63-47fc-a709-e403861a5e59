/**
 * Template Recommendation Engine
 * AI-powered template recommendations based on user profile and business needs
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Sparkles,
  TrendingUp,
  Users,
  Star,
  Clock,
  Target,
  Brain,
  Zap,
  CheckCircle,
  ArrowRight,
  Filter,
  RefreshCw,
  Monitor,
  Package,
  Megaphone,
  UserCheck,
  Dumbbell
} from 'lucide-react';
import { RTLText, RTLFlex } from '../common';
import { useLanguage } from '../../hooks/useLanguage';

interface TemplateRecommendation {
  id: string;
  name: string;
  description: string;
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime: number;
  popularity: number;
  matchScore: number;
  reasons: string[];
  icon: React.ComponentType<any>;
  isNew?: boolean;
  isPremium?: boolean;
}

interface UserProfile {
  experience_level: 'beginner' | 'intermediate' | 'expert';
  industry_preferences: string[];
  business_stage: 'idea' | 'startup' | 'growth' | 'established';
  time_availability: 'low' | 'medium' | 'high';
  previous_templates: string[];
  success_metrics: {
    completion_rate: number;
    satisfaction_score: number;
  };
}

interface TemplateRecommendationEngineProps {
  userProfile?: UserProfile;
  onSelectTemplate: (templateId: string) => void;
  onViewAll?: () => void;
  maxRecommendations?: number;
}

const TemplateRecommendationEngine: React.FC<TemplateRecommendationEngineProps> = ({ userProfile,
  onSelectTemplate,
  onViewAll,
  maxRecommendations = 6
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [recommendations, setRecommendations] = useState<TemplateRecommendation[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'trending' | 'personalized' | 'quick'>('personalized');

  useEffect(() => {
    generateRecommendations();
  }, [userProfile, selectedFilter]);

  const generateRecommendations = async () => {
    setLoading(true);
    try {
      // Fetch real templates and generate recommendations based on actual data
      const { businessPlanTemplatesAPI } = await import('../../services/templateCustomizationApi');
      const templates = await businessPlanTemplatesAPI.getTemplates();

      if (!Array.isArray(templates) || templates.length === 0) {
        setRecommendations([]);
        return;
      }

      // Generate real recommendations based on template data and user preferences
      const realRecommendations: TemplateRecommendation[] = templates
        .filter(template => template.name && template.description) // Only include complete templates
        .map(template => {
          // Calculate match score based on real template attributes
          let matchScore = 50; // Base score

          // Increase score for popular templates (use real usage_count)
          if (template.usage_count && template.usage_count > 100) matchScore += 20;

          // Increase score for highly rated templates (use real rating)
          if (template.rating && template.rating > 4) matchScore += 15;

          // Increase score for recently updated templates
          if (template.updated_at) {
            const daysSinceUpdate = (Date.now() - new Date(template.updated_at).getTime()) / (1000 * 60 * 60 * 24);
            if (daysSinceUpdate < 30) matchScore += 10;
          }

          // Generate reasons based on real template attributes
          const reasons: string[] = [];
          if (template.industry) {
            reasons.push(t("common.matches.industry", `Matches ${template.industry} industry`));
          }
          if (template.difficulty_level) {
            reasons.push(t("common.suitable.for.level", `Suitable for ${template.difficulty_level} level`));
          }
          if (template.usage_count && template.usage_count > 50) {
            reasons.push(t("common.popular.choice", "Popular choice among users"));
          }
          if (template.rating && template.rating > 4) {
            reasons.push(t("common.highly.rated", "Highly rated by users"));
          }

          return {
            id: template.id?.toString() || `template_${Date.now()}`,
            name: template.name,
            description: template.description,
            category: template.industry || 'General',
            difficulty: template.difficulty_level || 'intermediate',
            estimatedTime: template.estimated_time || 8,
            popularity: template.popularity_score || 0, // Use real popularity score from API
            matchScore: Math.min(matchScore, 100),
            reasons: reasons.length > 0 ? reasons : [t("common.recommended.template", "Recommended template")],
            icon: Brain, // Default icon, could be mapped based on template type
            isPremium: template.is_premium || false, // Use real is_premium from API
            isNew: template.is_new || false // Use real is_new from API
          };
        })
        .sort((a, b) => b.matchScore - a.matchScore); // Sort by match score

      // Filter based on selected filter using real data
      let filteredRecommendations = realRecommendations;

      if (selectedFilter === 'trending') {
        filteredRecommendations = realRecommendations.filter(r => r.isNew);
      } else if (selectedFilter === 'quick') {
        filteredRecommendations = realRecommendations.filter(r => r.estimatedTime <= 6);
      } else if (selectedFilter === 'personalized') {
        // Already sorted by match score, just take top results
        filteredRecommendations = realRecommendations;
      }

      setRecommendations(filteredRecommendations.slice(0, maxRecommendations));
    } catch (error) {
      console.error('Error generating recommendations:', error);
      // Set empty recommendations on error instead of mock data
      setRecommendations([]);
    } finally {
      setLoading(false);
    }
  };

  const refreshRecommendations = async () => {
    setRefreshing(true);
    await generateRecommendations();
    setRefreshing(false);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'text-green-400';
      case 'intermediate': return 'text-yellow-400';
      case 'advanced': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getMatchScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-400';
    if (score >= 80) return 'text-yellow-400';
    if (score >= 70) return 'text-orange-400';
    return 'text-red-400';
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        size={12}
        className={i < rating ? 'text-yellow-400 fill-current' : 'text-gray-600'}
      />
    ));
  };

  if (loading) {
    return (
      <div className="bg-gray-800/50 rounded-lg p-6">
        <div className={`flex items-center justify-center h-32 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-800/50 rounded-lg p-6">
      {/* Header */}
      <RTLFlex className="items-center justify-between mb-6">
        <div>
          <RTLFlex className="items-center mb-2">
            <Sparkles size={24} className={`text-purple-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
            <RTLText as="h3" className="text-xl font-bold">
              AI-Powered Recommendations
            </RTLText>
          </RTLFlex>
          <p className="text-gray-400 text-sm">
            Personalized template suggestions based on your profile and goals
          </p>
        </div>

        <RTLFlex className="items-center space-x-2">
          <button
            onClick={refreshRecommendations}
            disabled={refreshing}
            className="p-2 bg-gray-700 hover:bg-gray-600 rounded-md text-gray-300 hover:text-white transition-colors"
            title={t("common.refresh.recommendations", "Refresh recommendations")}
          >
            <RefreshCw size={16} className={refreshing ? 'animate-spin' : ''} />
          </button>

          {onViewAll && (
            <button
              onClick={onViewAll}
              className={`px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md text-sm transition-colors flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
            >
              View All
              <ArrowRight size={16} className={`ml-1 ${isRTL ? "space-x-reverse" : ""}`} />
            </button>
          )}
        </RTLFlex>
      </RTLFlex>

      {/* Filters */}
      <div className={`flex space-x-2 mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
        {[
          { id: 'personalized', label: t("common.for.you", "For You"), icon: Target },
          { id: 'trending', label: t("common.trending", "Trending"), icon: TrendingUp },
          { id: 'quick', label: t("common.quick.start", "Quick Start"), icon: Zap },
          { id: 'all', label: 'All', icon: Filter }
        ].map(filter => {
          const Icon = filter.icon;
          return (
            <button
              key={filter.id}
              onClick={() => setSelectedFilter(filter.id as any)}
              className={`flex items-center px-3 py-2 rounded-lg text-sm transition-colors ${
                selectedFilter === filter.id
                  ? 'bg-purple-600 text-white'
                  : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50'}
              }`}
            >
              <Icon size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
              {filter.label}
            </button>
          );
        })}
      </div>

      {/* Recommendations Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {recommendations.map((recommendation) => {
          const Icon = recommendation.icon;

          return (
            <div
              key={recommendation.id}
              onClick={() => onSelectTemplate(recommendation.id)}
              className="relative bg-gray-700/50 rounded-lg p-4 cursor-pointer transition-all hover:bg-gray-600/50 border border-gray-600 hover:border-purple-500"
            >
              {/* Badges */}
              <div className={`absolute top-3 right-3 flex gap-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                {recommendation.isNew && (
                  <span className="px-2 py-1 bg-green-600 text-white text-xs rounded-full">
                    New
                  </span>
                )}
                {recommendation.isPremium && (
                  <span className="px-2 py-1 bg-yellow-600 text-white text-xs rounded-full">
                    Pro
                  </span>
                )}
              </div>

              {/* Match Score */}
              <div className="absolute top-3 left-3">
                <div className={`text-xs font-bold ${getMatchScoreColor(recommendation.matchScore)}`}>
                  {recommendation.matchScore}% match
                </div>
              </div>

              {/* Icon and Title */}
              <div className="mt-6 mb-3">
                <div className="p-2 bg-purple-600/20 rounded-lg w-fit mb-3">
                  <Icon size={20} className="text-purple-400" />
                </div>
                <RTLText as="h4" className="font-semibold text-lg mb-1">
                  {recommendation.name}
                </RTLText>
                <div className={`flex items-center mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                  {renderStars(recommendation.popularity)}
                </div>
              </div>

              {/* Description */}
              <p className="text-gray-300 text-sm mb-3 line-clamp-2">
                {recommendation.description}
              </p>

              {/* Metadata */}
              <div className="space-y-2 text-xs text-gray-400 mb-3">
                <RTLFlex className="justify-between">
                  <span className={getDifficultyColor(recommendation.difficulty)}>
                    {recommendation.difficulty}
                  </span>
                  <span>{recommendation.category}</span>
                </RTLFlex>

                <RTLFlex className="items-center">
                  <Clock size={12} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                  <span>{recommendation.estimatedTime}h estimated</span>
                </RTLFlex>
              </div>

              {/* Reasons */}
              <div className="space-y-1">
                <div className="text-xs font-medium text-gray-300 mb-1">{t("common.why.this.template", "Why this template:")}</div>
                {recommendation.reasons.slice(0, 2).map((reason, index) => (
                  <div key={index} className={`flex items-start text-xs text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <CheckCircle size={12} className={`text-green-400 mr-1 mt-0.5 flex-shrink-0 ${isRTL ? "flex-row-reverse" : ""}`} />
                    <span>{reason}</span>
                  </div>
                ))}
              </div>
            </div>
          );
        })}
      </div>

      {/* No Recommendations */}
      {recommendations.length === 0 && (
        <div className="text-center py-8">
          <Sparkles size={48} className="text-gray-600 mx-auto mb-4" />
          <RTLText as="h4" className="text-lg font-semibold text-gray-400 mb-2">
            No recommendations available
          </RTLText>
          <p className="text-gray-500 text-sm">
            Try adjusting your filters or complete your profile for better recommendations.
          </p>
        </div>
      )}
    </div>
  );
};

export default TemplateRecommendationEngine;
