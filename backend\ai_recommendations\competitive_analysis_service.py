import os
import json
import google.generativeai as genai
from django.conf import settings
from dotenv import load_dotenv
from incubator.models import BusinessIdea
from incubator.models_analytics import ComparativeAnalytics
from django.utils import timezone
from django.db.models import Q
from core.ai_config import get_gemini_config, generate_gemini_content

# Load environment variables
load_dotenv()

# Use centralized configuration
def get_configured_model():
    """Get configured Gemini model"""
    config = get_gemini_config()
    return config.model if config.is_available else None

def generate_competitive_analysis(business_idea_id):
    """
    Generate a comprehensive competitive analysis for a business idea using Gemini AI
    
    Args:
        business_idea_id: ID of the business idea
        
    Returns:
        Dictionary containing the competitive analysis
    """
    try:
        # Get business idea
        business_idea = BusinessIdea.objects.get(id=business_idea_id)
        
        # Get similar business ideas for context
        similar_ideas = BusinessIdea.objects.filter(
            Q(current_stage=business_idea.current_stage) |
            Q(tags__in=business_idea.tags.all())
        ).exclude(id=business_idea.id).distinct()[:10]
        
        # Create context for AI
        similar_ideas_data = []
        for idea in similar_ideas:
            similar_ideas_data.append({
                'id': idea.id,
                'title': idea.title,
                'description': idea.description,
                'problem_statement': idea.problem_statement,
                'solution_description': idea.solution_description,
                'target_audience': idea.target_audience,
                'current_stage': idea.current_stage,
            })
        
        context = {
            'business_idea': {
                'title': business_idea.title,
                'description': business_idea.description,
                'problem_statement': business_idea.problem_statement,
                'solution_description': business_idea.solution_description,
                'target_audience': business_idea.target_audience,
                'market_opportunity': business_idea.market_opportunity,
                'business_model': business_idea.business_model,
                'current_stage': business_idea.current_stage,
            },
            'similar_ideas': similar_ideas_data
        }
        
        # Create the prompt for Gemini
        prompt = f"""
        You are an expert business analyst specializing in competitive analysis. Your task is to analyze the provided business idea 
        and generate a comprehensive competitive analysis report.
        
        Here's the data about the business idea and similar ideas in the platform:
        {json.dumps(context, indent=2)}
        
        Create a detailed competitive analysis with the following sections:
        
        1. Market Overview
           - Market size and growth potential
           - Key market trends
           - Market segments and target customers
           - Regulatory environment
        
        2. Competitor Analysis
           - Direct competitors (identify at least 3-5 potential competitors based on the business description)
           - Indirect competitors
           - Competitor strengths and weaknesses
           - Market positioning of competitors
        
        3. Competitive Advantages
           - Unique selling propositions of the business idea
           - Potential competitive advantages
           - Areas where the business idea outperforms competitors
        
        4. Competitive Disadvantages
           - Potential weaknesses compared to competitors
           - Areas for improvement
           - Potential barriers to entry
        
        5. Strategic Recommendations
           - Positioning strategy
           - Differentiation opportunities
           - Potential partnerships or alliances
           - Marketing and pricing strategies
        
        Format your response as a JSON object with these sections as keys, and detailed analysis as values.
        Each section should have subsections as appropriate.
        
        The analysis should be specific, data-driven where possible, and provide actionable insights for the business owner.
        """
        
        # Generate competitive analysis using Gemini
        response = model.generate_content(prompt)
        
        # Parse the response
        response_text = response.text
        
        # Extract JSON from the response
        if '```json' in response_text:
            json_start = response_text.find('```json') + 7
            json_end = response_text.find('```', json_start)
            json_str = response_text[json_start:json_end].strip()
        elif '{' in response_text and '}' in response_text:
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            json_str = response_text[json_start:json_end].strip()
        else:
            json_str = response_text
            
        analysis_data = json.loads(json_str)
        
        # Update or create ComparativeAnalytics for this business idea
        try:
            comparative = ComparativeAnalytics.objects.get(business_idea=business_idea)
        except ComparativeAnalytics.DoesNotExist:
            comparative = ComparativeAnalytics(business_idea=business_idea)
        
        # Update competitive advantages and disadvantages
        if 'Competitive Advantages' in analysis_data:
            comparative.competitive_advantages = {'advantages': analysis_data['Competitive Advantages']}
        
        if 'Competitive Disadvantages' in analysis_data:
            comparative.competitive_disadvantages = {'disadvantages': analysis_data['Competitive Disadvantages']}
        
        # Save the updated comparative analytics
        comparative.save()
        
        return analysis_data
        
    except Exception as e:
        print(f"Error generating competitive analysis: {e}")
        return None

def generate_market_trends_analysis(business_idea_id):
    """
    Generate an analysis of market trends relevant to a business idea
    
    Args:
        business_idea_id: ID of the business idea
        
    Returns:
        Dictionary containing the market trends analysis
    """
    try:
        # Get business idea
        business_idea = BusinessIdea.objects.get(id=business_idea_id)
        
        # Create context for AI
        context = {
            'business_idea': {
                'title': business_idea.title,
                'description': business_idea.description,
                'problem_statement': business_idea.problem_statement,
                'solution_description': business_idea.solution_description,
                'target_audience': business_idea.target_audience,
                'market_opportunity': business_idea.market_opportunity,
                'business_model': business_idea.business_model,
                'current_stage': business_idea.current_stage,
            }
        }
        
        # Create the prompt for Gemini
        prompt = f"""
        You are a market research expert specializing in trend analysis. Your task is to analyze the provided business idea 
        and identify current and emerging market trends that could impact its success.
        
        Here's the data about the business idea:
        {json.dumps(context, indent=2)}
        
        Create a detailed market trends analysis with the following sections:
        
        1. Current Market Trends
           - Major trends currently shaping the market
           - Consumer behavior trends
           - Technology trends affecting the industry
           - Economic factors influencing the market
        
        2. Emerging Trends
           - Upcoming trends that could impact the business
           - Early signals of market shifts
           - Potential disruptors
        
        3. Trend Impact Analysis
           - How identified trends could positively impact the business
           - How identified trends could negatively impact the business
           - Timeline for trend impact (short-term, medium-term, long-term)
        
        4. Strategic Recommendations
           - How to capitalize on positive trends
           - How to mitigate risks from negative trends
           - Suggested pivots or adaptations based on trends
        
        Format your response as a JSON object with these sections as keys, and detailed analysis as values.
        Each section should have subsections as appropriate.
        
        The analysis should be specific, data-driven where possible, and provide actionable insights for the business owner.
        """
        
        # Generate market trends analysis using Gemini
        response = model.generate_content(prompt)
        
        # Parse the response
        response_text = response.text
        
        # Extract JSON from the response
        if '```json' in response_text:
            json_start = response_text.find('```json') + 7
            json_end = response_text.find('```', json_start)
            json_str = response_text[json_start:json_end].strip()
        elif '{' in response_text and '}' in response_text:
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            json_str = response_text[json_start:json_end].strip()
        else:
            json_str = response_text
            
        analysis_data = json.loads(json_str)
        return analysis_data
        
    except Exception as e:
        print(f"Error generating market trends analysis: {e}")
        return None
