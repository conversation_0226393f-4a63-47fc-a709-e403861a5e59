import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../hooks/useLanguage';
// MainLayout removed - handled by routing system
import LanguageSettings from '../components/settings/LanguageSettings';
import { User, Bell, Shield, Palette, Globe } from 'lucide-react';

interface SettingsSection {
  id: string;
  title: string;
  icon: React.ReactNode;
  description: string;
}

const UserSettings: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [activeSection, setActiveSection] = useState('language');

  const sections: SettingsSection[] = [
    {
      id: 'language',
      title: t('settings.language.title', 'Language Settings'),
      icon: <Globe size={20} />,
      description: t('settings.language.description', 'Choose your preferred language')
    },
    {
      id: 'profile',
      title: t('settings.profile.title', 'Profile Settings'),
      icon: <User size={20} />,
      description: t('settings.profile.description', 'Manage your profile information')
    },
    {
      id: 'notifications',
      title: t('settings.notifications.title', 'Notifications'),
      icon: <Bell size={20} />,
      description: t('settings.notifications.description', 'Configure notification preferences')
    },
    {
      id: 'privacy',
      title: t('settings.privacy.title', 'Privacy & Security'),
      icon: <Shield size={20} />,
      description: t('settings.privacy.description', 'Manage privacy and security settings')
    },
    {
      id: 'appearance',
      title: t('settings.appearance.title', 'Appearance'),
      icon: <Palette size={20} />,
      description: t('settings.appearance.description', 'Customize the look and feel')
    }
  ];

  const renderContent = () => {
    switch (activeSection) {
      case 'language':
        return <LanguageSettings />;
      case 'profile':
        return (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              {t('settings.profile.title', 'Profile Settings')}
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              {t('settings.profile.comingSoon', 'Profile settings will be available soon.')}
            </p>
          </div>
        );
      case 'notifications':
        return (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              {t('settings.notifications.title', 'Notifications')}
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              {t('settings.notifications.comingSoon', 'Notification settings will be available soon.')}
            </p>
          </div>
        );
      case 'privacy':
        return (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              {t('settings.privacy.title', 'Privacy & Security')}
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              {t('settings.privacy.comingSoon', 'Privacy and security settings will be available soon.')}
            </p>
          </div>
        );
      case 'appearance':
        return (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              {t('settings.appearance.title', 'Appearance')}
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              {t('settings.appearance.comingSoon', 'Appearance settings will be available soon.')}
            </p>
          </div>
        );
      default:
        return <LanguageSettings />;
    }
  };

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            {t('settings.title', 'Settings')}
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            {t('settings.description', 'Manage your account settings and preferences')}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <nav className="space-y-2">
              {sections.map((section) => (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className={`
                    w-full flex items-center gap-3 px-4 py-3 rounded-lg text-left transition-colors
                    ${activeSection === section.id
                      ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-800'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50'
                    }
                    ${isRTL ? 'flex-row-reverse text-right' : ''}
                  `}
                >
                  <span className={`flex-shrink-0 ${activeSection === section.id ? 'text-blue-600 dark:text-blue-400' : 'text-gray-400'}`}>
                    {section.icon}
                  </span>
                  <div className={`flex-1 ${isRTL ? 'text-right' : 'text-left'}`}>
                    <div className="font-medium">{section.title}</div>
                    <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                      {section.description}
                    </div>
                  </div>
                </button>
              ))}
            </nav>
          </div>

          {/* Content */}
          <div className="lg:col-span-3">
            {renderContent()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserSettings;
