import React, { useState, useEffect } from 'react';
import { X, User, Briefcase, Award, Clock, MapPin } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../hooks/useLanguage';
import { useCreateMentorProfile, useUpdateMentorProfile } from '../../../../hooks/useMentorship';
import { MentorProfile } from '../../../../services/incubatorApi';
import { Button } from '../../../ui';
import { validateRequired, validatePositiveNumber } from '../../../../utils/localeValidation';

interface MentorProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
  profile: MentorProfile | null;
  isEditing: boolean;
}

interface FormData {
  user_id: number;
  company: string;
  position: string;
  years_of_experience: number;
  bio: string;
  linkedin_url: string;
  website_url: string;
  hourly_rate: number;
  is_accepting_mentees: boolean;
  max_mentees: number;
  availability: string;
  expertise_areas: string[];
}

const MentorProfileModal: React.FC<MentorProfileModalProps> = ({
  isOpen,
  onClose,
  profile,
  isEditing
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  const createProfile = useCreateMentorProfile();
  const updateProfile = useUpdateMentorProfile();

  const [formData, setFormData] = useState<FormData>({
    user_id: 0,
    company: '',
    position: '',
    years_of_experience: 0,
    bio: '',
    linkedin_url: '',
    website_url: '',
    hourly_rate: 0,
    is_accepting_mentees: true,
    max_mentees: 5,
    availability: 'weekdays',
    expertise_areas: []
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form data when profile changes
  useEffect(() => {
    if (isEditing && profile) {
      setFormData({
        user_id: profile.user.id,
        company: profile.company || '',
        position: profile.position || '',
        years_of_experience: profile.years_of_experience || 0,
        bio: profile.bio || '',
        linkedin_url: profile.linkedin_url || '',
        website_url: profile.website_url || '',
        hourly_rate: profile.hourly_rate || 0,
        is_accepting_mentees: profile.is_accepting_mentees || false,
        max_mentees: profile.max_mentees || 5,
        availability: profile.availability || 'weekdays',
        expertise_areas: profile.expertise_areas?.map(e => e.specific_expertise) || []
      });
    } else {
      // Reset form for create mode
      setFormData({
        user_id: 0,
        company: '',
        position: '',
        years_of_experience: 0,
        bio: '',
        linkedin_url: '',
        website_url: '',
        hourly_rate: 0,
        is_accepting_mentees: true,
        max_mentees: 5,
        availability: 'weekdays',
        expertise_areas: []
      });
    }
    setErrors({});
  }, [isEditing, profile]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked :
              type === 'number' ? Number(value) : value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleExpertiseChange = (expertise: string) => {
    setFormData(prev => ({
      ...prev,
      expertise_areas: prev.expertise_areas.includes(expertise)
        ? prev.expertise_areas.filter(e => e !== expertise)
        : [...prev.expertise_areas, expertise]
    }));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!validateRequired(formData.company)) {
      newErrors.company = t('validation.required', 'This field is required');
    }
    if (!validateRequired(formData.position)) {
      newErrors.position = t('validation.required', 'This field is required');
    }
    if (formData.years_of_experience < 0) {
      newErrors.years_of_experience = t('validation.positiveNumber', 'Must be a positive number');
    }
    if (!validateRequired(formData.bio)) {
      newErrors.bio = t('validation.required', 'This field is required');
    }
    if (!validateRequired(formData.expertise_areas)) {
      newErrors.expertise_areas = t('validation.selectAtLeastOne', 'Select at least one expertise area');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      if (isEditing && profile) {
        await updateProfile.mutateAsync({
          id: profile.id,
          data: formData
        });
      } else {
        await createProfile.mutateAsync(formData);
      }
      onClose();
    } catch (error) {
      console.error('Failed to save mentor profile:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const expertiseOptions = [
    'Technology', 'Marketing', 'Finance', 'Operations', 'Strategy',
    'Product Management', 'Sales', 'Human Resources', 'Legal',
    'Design', 'Engineering', 'Data Science', 'Business Development'
  ];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-indigo-900/90 rounded-xl shadow-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold text-white">
              {isEditing ? t('mentor.editProfile', 'Edit Mentor Profile') : t('mentor.createProfile', 'Create Mentor Profile')}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white"
            >
              <X size={20} />
            </button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-gray-300 mb-2">
                  {t('mentor.company', 'Company')} *
                </label>
                <input
                  type="text"
                  name="company"
                  value={formData.company}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                  placeholder={t('mentor.companyPlaceholder', 'Enter company name')}
                />
                {errors.company && <p className="text-red-400 text-sm mt-1">{errors.company}</p>}
              </div>

              <div>
                <label className="block text-gray-300 mb-2">
                  {t('mentor.position', 'Position')} *
                </label>
                <input
                  type="text"
                  name="position"
                  value={formData.position}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                  placeholder={t('mentor.positionPlaceholder', 'Enter position title')}
                />
                {errors.position && <p className="text-red-400 text-sm mt-1">{errors.position}</p>}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-gray-300 mb-2">
                  {t('mentor.yearsExperience', 'Years of Experience')} *
                </label>
                <input
                  type="number"
                  name="years_of_experience"
                  value={formData.years_of_experience}
                  onChange={handleInputChange}
                  min="0"
                  className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                />
                {errors.years_of_experience && <p className="text-red-400 text-sm mt-1">{errors.years_of_experience}</p>}
              </div>

              <div>
                <label className="block text-gray-300 mb-2">
                  {t('mentor.hourlyRate', 'Hourly Rate ($)')}
                </label>
                <input
                  type="number"
                  name="hourly_rate"
                  value={formData.hourly_rate}
                  onChange={handleInputChange}
                  min="0"
                  className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                />
              </div>
            </div>

            {/* Bio */}
            <div>
              <label className="block text-gray-300 mb-2">
                {t('mentor.bio', 'Bio')} *
              </label>
              <textarea
                name="bio"
                value={formData.bio}
                onChange={handleInputChange}
                rows={4}
                className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                placeholder={t('mentor.bioPlaceholder', 'Tell us about your experience and what you can offer as a mentor...')}
              />
              {errors.bio && <p className="text-red-400 text-sm mt-1">{errors.bio}</p>}
            </div>

            {/* Links */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-gray-300 mb-2">
                  {t('mentor.linkedinUrl', 'LinkedIn URL')}
                </label>
                <input
                  type="url"
                  name="linkedin_url"
                  value={formData.linkedin_url}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                  placeholder="https://linkedin.com/in/..."
                />
              </div>

              <div>
                <label className="block text-gray-300 mb-2">
                  {t('mentor.websiteUrl', 'Website URL')}
                </label>
                <input
                  type="url"
                  name="website_url"
                  value={formData.website_url}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                  placeholder="https://..."
                />
              </div>
            </div>

            {/* Availability Settings */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-gray-300 mb-2">
                  {t('mentor.availability', 'Availability')}
                </label>
                <select
                  name="availability"
                  value={formData.availability}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                >
                  <option value="weekdays">{t('mentor.weekdays', 'Weekdays')}</option>
                  <option value="weekends">{t('mentor.weekends', 'Weekends')}</option>
                  <option value="flexible">{t('mentor.flexible', 'Flexible')}</option>
                </select>
              </div>

              <div>
                <label className="block text-gray-300 mb-2">
                  {t('mentor.maxMentees', 'Max Mentees')}
                </label>
                <input
                  type="number"
                  name="max_mentees"
                  value={formData.max_mentees}
                  onChange={handleInputChange}
                  min="1"
                  max="20"
                  className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                />
              </div>

              <div className="flex items-center">
                <label className="flex items-center text-gray-300 mt-6">
                  <input
                    type="checkbox"
                    name="is_accepting_mentees"
                    checked={formData.is_accepting_mentees}
                    onChange={handleInputChange}
                    className="rounded border-gray-600 bg-indigo-800 text-purple-500 focus:ring-purple-500 mr-2"
                  />
                  {t('mentor.acceptingMentees', 'Accepting Mentees')}
                </label>
              </div>
            </div>

            {/* Expertise Areas */}
            <div>
              <label className="block text-gray-300 mb-2">
                {t('mentor.expertiseAreas', 'Expertise Areas')} *
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {expertiseOptions.map((expertise) => (
                  <label key={expertise} className="flex items-center text-gray-300">
                    <input
                      type="checkbox"
                      checked={formData.expertise_areas.includes(expertise)}
                      onChange={() => handleExpertiseChange(expertise)}
                      className="rounded border-gray-600 bg-indigo-800 text-purple-500 focus:ring-purple-500 mr-2"
                    />
                    {expertise}
                  </label>
                ))}
              </div>
              {errors.expertise_areas && <p className="text-red-400 text-sm mt-1">{errors.expertise_areas}</p>}
            </div>

            {/* Form Actions */}
            <div className="flex justify-end gap-3 pt-4 border-t border-indigo-800/50">
              <Button
                type="button"
                onClick={onClose}
                variant="secondary"
                disabled={isSubmitting}
              >
                {t('common.cancel', 'Cancel')}
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
              >
                {isSubmitting ? t('common.saving', 'Saving...') : 
                 isEditing ? t('common.update', 'Update') : t('common.create', 'Create')}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default MentorProfileModal;
