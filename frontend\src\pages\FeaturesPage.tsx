/**
 * Features Page - Detailed showcase of all AI capabilities
 */

import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import {
  Brain,
  TrendingUp,
  Eye,
  Mic,
  Target,
  Shield,
  DollarSign,
  BarChart3,
  MessageSquare,
  Users,
  Zap,
  CheckCircle,
  ArrowRight,
  Play,
  Star,
  Globe,
  Lightbulb,
  Activity,
  Award,
  Rocket
} from 'lucide-react';
import { useTranslation } from 'react-i18next';


interface Feature {
  id: string;
  icon: React.ComponentType<any>;
  title: string;
  description: string;
  benefits: string[];
  color: string;
  gradient: string;
  demo?: string;
}

export const FeaturesPage: React.FC = () => {
  const { t } = useTranslation();
  const [activeFeature, setActiveFeature] = useState('predictive');

  const features: Feature[] = [
    {
      id: 'predictive',
      icon: TrendingUp,
      title: t('Predictive Analytics'),
      description: t('Advanced machine learning models that predict business success, market trends, and investment opportunities with 94% accuracy.'),
      benefits: [
        t('Business success probability scoring'),
        t('Market trend forecasting'),
        t('Risk assessment and mitigation'),
        t('Investment readiness evaluation'),
        t('Competitive analysis'),
        t('Growth opportunity identification')
      ],
      color: 'text-blue-600',
      gradient: 'from-blue-500 to-cyan-500',
      demo: '/demo/predictive'
    },
    {
      id: 'vision',
      icon: Eye,
      title: t('Computer Vision'),
      description: t('AI-powered visual analysis that can read, understand, and analyze any business document, presentation, or image.'),
      benefits: [
        t('Document text extraction'),
        t('Pitch deck analysis and feedback'),
        t('Logo and brand assessment'),
        t('Chart data extraction'),
        t('Business plan review'),
        t('Visual content optimization')
      ],
      color: 'text-green-600',
      gradient: 'from-green-500 to-emerald-500',
      demo: '/demo/vision'
    },
    {
      id: 'voice',
      icon: Mic,
      title: t('Voice AI'),
      description: t('Natural language processing with Arabic and English support for voice commands, transcription, and synthesis.'),
      benefits: [
        t('Multi-language speech recognition'),
        t('Voice command navigation'),
        t('Meeting transcription'),
        t('Text-to-speech synthesis'),
        t('Sentiment analysis'),
        t('Voice-controlled workflows')
      ],
      color: 'text-purple-600',
      gradient: 'from-purple-500 to-pink-500',
      demo: '/demo/voice'
    },
    {
      id: 'intelligence',
      icon: Brain,
      title: t('Business Intelligence'),
      description: t('Autonomous AI advisor that provides real-time insights, recommendations, and strategic guidance for your business.'),
      benefits: [
        t('Automated business recommendations'),
        t('Market research and analysis'),
        t('Competitor intelligence'),
        t('Performance optimization'),
        t('Strategic planning assistance'),
        t('24/7 AI business advisor')
      ],
      color: 'text-orange-600',
      gradient: 'from-orange-500 to-red-500',
      demo: '/demo/intelligence'
    }
  ];

  const additionalCapabilities = [
    { icon: Target, title: t('Success Prediction'), description: t('87% accuracy in startup success forecasting') },
    { icon: Shield, title: t('Risk Management'), description: t('Comprehensive risk assessment and mitigation strategies') },
    { icon: DollarSign, title: t('Investment Matching'), description: t('AI-powered investor and funding recommendations') },
    { icon: BarChart3, title: t('Market Analysis'), description: t('Real-time market trends and opportunity identification') },
    { icon: MessageSquare, title: t('Smart Conversations'), description: t('Intelligent chat with context-aware responses') },
    { icon: Users, title: t('Mentor Matching'), description: t('AI-driven mentor and advisor recommendations') },
    { icon: Zap, title: t('Automation'), description: t('Automated workflows and business process optimization') },
    { icon: Globe, title: t('Global Insights'), description: t('International market analysis and expansion guidance') }
  ];

  const selectedFeature = features.find(f => f.id === activeFeature) || features[0];
  const SelectedIcon = selectedFeature.icon;

  return (
    <ThemeWrapper>
      <div className="features-page min-h-screen bg-gray-50 dark:bg-gray-900">
        
        {/* Header */}
        <section className="bg-gradient-to-r from-purple-600 to-blue-600 py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div className="flex justify-center mb-6">
              <Rocket className="w-16 h-16 text-white animate-bounce" />
            </div>
            
            <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
              {t('AI-Powered Features')}
            </h1>
            
            <p className="text-xl text-purple-100 mb-8 max-w-3xl mx-auto">
              {t('Discover the comprehensive suite of AI capabilities that will transform your business operations and decision-making process')}
            </p>
            
            <Link
              to="/register"
              className="inline-flex items-center space-x-2 px-8 py-4 bg-white text-purple-600 rounded-xl font-semibold text-lg hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg"
            >
              <Star className="w-5 h-5" />
              <span>{t('Start Free Trial')}</span>
              <ArrowRight className="w-5 h-5" />
            </Link>
          </div>
        </section>

        {/* Main Features */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                {t('Core AI Capabilities')}
              </h2>
              <p className="text-xl text-gray-600 dark:text-gray-300">
                {t('Four revolutionary AI technologies working together for your success')}
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Feature Navigation */}
              <div className="lg:col-span-1">
                <div className="space-y-4">
                  {features.map((feature) => {
                    const Icon = feature.icon;
                    const isActive = activeFeature === feature.id;
                    
                    return (
                      <button
                        key={feature.id}
                        onClick={() => setActiveFeature(feature.id)}
                        className={`w-full text-left p-6 rounded-xl transition-all duration-300 ${
                          isActive
                            ? 'bg-white dark:bg-gray-800 shadow-lg border-2 border-purple-200 dark:border-purple-700'
                            : 'bg-white dark:bg-gray-800 shadow hover:shadow-md border border-gray-200 dark:border-gray-700'
                        }`}
                      >
                        <div className="flex items-center space-x-4">
                          <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${feature.gradient} flex items-center justify-center`}>
                            <Icon className="w-6 h-6 text-white" />
                          </div>
                          <div className="flex-1">
                            <h3 className={`font-semibold ${isActive ? feature.color : 'text-gray-900 dark:text-white'}`}>
                              {feature.title}
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                              {feature.description.substring(0, 80)}...
                            </p>
                          </div>
                        </div>
                      </button>
                    );
                  })}
                </div>
              </div>

              {/* Feature Details */}
              <div className="lg:col-span-2">
                <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 border border-gray-200 dark:border-gray-700">
                  <div className="flex items-center space-x-4 mb-6">
                    <div className={`w-16 h-16 rounded-xl bg-gradient-to-r ${selectedFeature.gradient} flex items-center justify-center`}>
                      <SelectedIcon className="w-8 h-8 text-white" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                        {selectedFeature.title}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-300">
                        {selectedFeature.description}
                      </p>
                    </div>
                  </div>

                  <div className="mb-8">
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                      {t('Key Benefits')}
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {selectedFeature.benefits.map((benefit, index) => (
                        <div key={index} className="flex items-center space-x-3">
                          <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                          <span className="text-gray-700 dark:text-gray-300">{benefit}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-4">
                    <Link
                      to={selectedFeature.demo || '/demo'}
                      className="flex items-center justify-center space-x-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-300"
                    >
                      <Play className="w-4 h-4" />
                      <span>{t('Try Demo')}</span>
                    </Link>
                    
                    <Link
                      to="/register"
                      className="flex items-center justify-center space-x-2 px-6 py-3 border-2 border-purple-600 text-purple-600 dark:text-purple-400 rounded-lg hover:bg-purple-600 hover:text-white transition-all duration-300"
                    >
                      <Rocket className="w-4 h-4" />
                      <span>{t('Get Started')}</span>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Additional Capabilities */}
        <section className="py-20 bg-white dark:bg-gray-800">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                {t('Additional AI Capabilities')}
              </h2>
              <p className="text-xl text-gray-600 dark:text-gray-300">
                {t('Comprehensive tools for every aspect of your business')}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {additionalCapabilities.map((capability, index) => {
                const Icon = capability.icon;
                return (
                  <div
                    key={index}
                    className="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 hover:shadow-lg transition-all duration-300 group"
                  >
                    <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mb-4 group-hover:bg-purple-200 dark:group-hover:bg-purple-800 transition-colors">
                      <Icon className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                    </div>
                    <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                      {capability.title}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      {capability.description}
                    </p>
                  </div>
                );
              })}
            </div>
          </div>
        </section>

        {/* Performance Stats */}
        <section className="py-20 bg-gradient-to-r from-gray-900 to-purple-900">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-4xl font-bold text-white mb-4">
              {t('Proven AI Performance')}
            </h2>
            <p className="text-xl text-purple-100 mb-12">
              {t('Real metrics from real businesses')}
            </p>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {[
                { number: '94%', label: t('Prediction Accuracy'), icon: Target },
                { number: '87%', label: t('Success Rate'), icon: Award },
                { number: '3x', label: t('Faster Decisions'), icon: Zap },
                { number: '24/7', label: t('AI Availability'), icon: Activity }
              ].map((stat, index) => {
                const Icon = stat.icon;
                return (
                  <div key={index} className="text-center">
                    <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Icon className="w-8 h-8 text-white" />
                    </div>
                    <div className="text-4xl font-bold text-white mb-2">{stat.number}</div>
                    <div className="text-purple-100">{stat.label}</div>
                  </div>
                );
              })}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gray-50 dark:bg-gray-900">
          <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
            <div className="flex justify-center mb-8">
              <Lightbulb className="w-16 h-16 text-yellow-500 animate-pulse" />
            </div>
            
            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
              {t('Ready to Experience AI-Powered Business Growth?')}
            </h2>
            
            <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
              {t('Join thousands of Syrian entrepreneurs who are already using Yasmeen AI to transform their businesses')}
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/register"
                className="inline-flex items-center justify-center space-x-2 px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-xl font-semibold text-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                <Rocket className="w-5 h-5" />
                <span>{t('Start Free Trial')}</span>
                <ArrowRight className="w-5 h-5" />
              </Link>
              
              <Link
                to="/demo"
                className="inline-flex items-center justify-center space-x-2 px-8 py-4 border-2 border-purple-600 text-purple-600 dark:text-purple-400 rounded-xl font-semibold text-lg hover:bg-purple-600 hover:text-white transition-all duration-300"
              >
                <Play className="w-5 h-5" />
                <span>{t('Watch Demo')}</span>
              </Link>
            </div>
          </div>
        </section>
      </div>
    </ThemeWrapper>
  );
};
