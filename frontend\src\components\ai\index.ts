/**
 * AI Components Index
 * Exports all AI-related components for easy importing
 */

// ========================================
// ENHANCED AI FEATURES (NEW)
// ========================================
export { PredictiveAnalyticsDashboard } from './PredictiveAnalyticsDashboard';
export { ComputerVisionAnalyzer } from './ComputerVisionAnalyzer';
export { AdvancedVoiceAI } from './AdvancedVoiceAI';
export { EnhancedAIDashboard } from './EnhancedAIDashboard';

// ========================================
// EXISTING AI COMPONENTS
// ========================================
export { AIActivationPanel } from './AIActivationPanel';
export { AIActivityBanner } from './AIActivityBanner';
export { AIInsightsSummary } from './AIInsightsSummary';
export { AILoginPrompt } from './AILoginPrompt';
export { AIMarketResearchEngine } from './AIMarketResearchEngine';
export { AINotificationCenter } from './AINotificationCenter';
export { AINotificationWidget } from './AINotificationWidget';
export { AIOnboardingWizard } from './AIOnboardingWizard';
export { AIPerformanceAnalytics } from './AIPerformanceAnalytics';
export { AIStatusIndicator } from './AIStatusIndicator';
export { AISystemMonitor } from './AISystemMonitor';
export { AITutorialSystem } from './AITutorialSystem';
export { AutomatedBusinessPlanGenerator } from './AutomatedBusinessPlanGenerator';
export { AutomatedNetworkingEngine } from './AutomatedNetworkingEngine';

export { AutonomousBusinessAdvisor } from './AutonomousBusinessAdvisor';
export { AutonomousBusinessIntelligenceDashboard } from './AutonomousBusinessIntelligenceDashboard';
export { BusinessIdeaAIWidget } from './BusinessIdeaAIWidget';
export { BusinessIdeaGenerator } from './BusinessIdeaGenerator';
export { BusinessIntelligenceDashboard } from './BusinessIntelligenceDashboard';
export { BusinessIntelligenceWidget } from './BusinessIntelligenceWidget';
export { ComprehensiveAIStatus } from './ComprehensiveAIStatus';
export { ConsolidatedAIChat } from './ConsolidatedAIChat';
export { ConsolidatedAIStatus } from './ConsolidatedAIStatus';
export { ConsolidatedBusinessAnalysis } from './ConsolidatedBusinessAnalysis';
export { ContinuousOptimizationPanel } from './ContinuousOptimizationPanel';
export { IntelligentAIDashboard } from './IntelligentAIDashboard';
export { IntelligentAssistantInterface } from './IntelligentAssistantInterface';
export { IntelligentFormAssistant } from './IntelligentFormAssistant';
export { IntelligentResourceRecommendations } from './IntelligentResourceRecommendations';
export { InvestmentMatchingWidget } from './InvestmentMatchingWidget';
export { MarketTrendPrediction } from './MarketTrendPrediction';
export { MobileAIInterface } from './MobileAIInterface';
export { NetworkingAgent } from './NetworkingAgent';
export { PerformanceOptimizationEngine } from './PerformanceOptimizationEngine';
export { ProactiveAINotifications } from './ProactiveAINotifications';
export { RealTimeIdeaEnhancer } from './RealTimeIdeaEnhancer';
export { RiskAssessmentDashboard } from './RiskAssessmentDashboard';
// SimpleAIIndicator and SimpleAINotification removed - use AIStatusIndicator or ConsolidatedAIStatus
export { SmartFloatingAssistant } from './SmartFloatingAssistant';
export { SmartMentorshipMatcher } from './SmartMentorshipMatcher';
export { SuccessProbabilityEngine } from './SuccessProbabilityEngine';
export { UnifiedAIDashboard } from './UnifiedAIDashboard';
export { VoiceAIInterface } from './VoiceAIInterface';

// ========================================
// AI COMPONENT TYPES
// ========================================
export interface AIComponentProps {
  className?: string;
  onAnalysisComplete?: (results: any) => void;
  onError?: (error: string) => void;
}

export interface PredictiveAnalyticsResult {
  success_probability: number;
  confidence_score: number;
  risk_level: string;
  key_factors: string[];
  recommendations: string[];
  market_fit_score: number;
  financial_viability: number;
  competitive_advantage: number;
}

export interface ComputerVisionResult {
  document_type?: string;
  extracted_text?: string;
  text_confidence?: number;
  structure_analysis?: any;
  business_information?: any;
  key_insights?: string[];
  recommendations?: string[];
}

export interface VoiceAIResult {
  success: boolean;
  transcribed_text?: string;
  language?: string;
  confidence?: number;
  method?: string;
  audio_data?: string;
  format?: string;
  estimated_duration?: number;
}

// ========================================
// AI UTILITY FUNCTIONS
// ========================================
export const aiUtils = {
  /**
   * Check if AI features are available
   */
  isAIAvailable: async (): Promise<boolean> => {
    try {
      const response = await fetch('/api/ai/status/');
      return response.ok;
    } catch {
      return false;
    }
  },

  /**
   * Format AI confidence score
   */
  formatConfidence: (score: number): string => {
    return `${(score * 100).toFixed(1)}%`;
  },

  /**
   * Get risk level color
   */
  getRiskLevelColor: (level: string): string => {
    switch (level.toLowerCase()) {
      case 'low': return 'text-green-600';
      case 'medium': return 'text-yellow-600';
      case 'high': return 'text-red-600';
      default: return 'text-gray-600';
    }
  },

  /**
   * Format analysis timestamp
   */
  formatTimestamp: (timestamp: string): string => {
    return new Date(timestamp).toLocaleString();
  },

  /**
   * Validate business data for AI analysis
   */
  validateBusinessData: (data: any): boolean => {
    return !!(data && (data.title || data.description || data.industry));
  }
};

// ========================================
// AI CONSTANTS
// ========================================
export const AI_CONSTANTS = {
  // API Endpoints
  ENDPOINTS: {
    PREDICTIVE_ANALYTICS: '/ai/predictive-analytics/',
    COMPUTER_VISION: '/ai/computer-vision/',
    VOICE_AI: '/ai/voice-ai/',
    CHAT: '/ai/chat/',
    STATUS: '/ai/status/'
  },

  // Analysis Types
  ANALYSIS_TYPES: {
    SUCCESS_PREDICTION: 'success_prediction',
    MARKET_FORECAST: 'market_forecast',
    RISK_ASSESSMENT: 'risk_assessment',
    INVESTMENT_READINESS: 'investment_readiness',
    DOCUMENT_ANALYSIS: 'document_analysis',
    PITCH_DECK_ANALYSIS: 'pitch_deck_analysis',
    LOGO_ANALYSIS: 'logo_analysis',
    CHART_ANALYSIS: 'chart_analysis'
  },

  // Voice Actions
  VOICE_ACTIONS: {
    TRANSCRIBE: 'transcribe',
    SYNTHESIZE: 'synthesize',
    ANALYZE_SENTIMENT: 'analyze_sentiment',
    PROCESS_COMMAND: 'process_command',
    TRANSCRIBE_MEETING: 'transcribe_meeting'
  },

  // Supported Languages
  LANGUAGES: {
    AUTO: 'auto',
    ENGLISH: 'en',
    ARABIC: 'ar'
  },

  // Risk Levels
  RISK_LEVELS: {
    LOW: 'Low',
    MEDIUM: 'Medium',
    HIGH: 'High'
  },

  // Investment Readiness Levels
  INVESTMENT_LEVELS: {
    READY: 'Investment Ready',
    NEARLY_READY: 'Nearly Ready',
    NEEDS_DEVELOPMENT: 'Needs Development',
    EARLY_STAGE: 'Early Stage'
  }
};

// ========================================
// AI HOOKS (for future implementation)
// ========================================
export interface UseAIAnalysisOptions {
  autoRun?: boolean;
  onSuccess?: (result: any) => void;
  onError?: (error: string) => void;
}

// Note: These hooks would be implemented in separate files
// export { useAIAnalysis } from './hooks/useAIAnalysis';
// export { useVoiceAI } from './hooks/useVoiceAI';
// export { useComputerVision } from './hooks/useComputerVision';
// export { usePredictiveAnalytics } from './hooks/usePredictiveAnalytics';
