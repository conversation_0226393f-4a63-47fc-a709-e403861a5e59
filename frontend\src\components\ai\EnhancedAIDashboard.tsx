/**
 * Enhanced AI Dashboard
 * Comprehensive AI platform with all advanced features integrated
 */

import React, { useState, useEffect } from 'react';
import {
  Brain,
  TrendingUp,
  Eye,
  Mic,
  BarChart3,
  Zap,
  Target,
  Shield,
  DollarSign,
  Users,
  Settings,
  Activity,
  Star,
  Lightbulb,
  RefreshCw,
  ChevronRight
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { getAuthToken } from '../../services/api';

import { PredictiveAnalyticsDashboard } from './PredictiveAnalyticsDashboard';
import { ComputerVisionAnalyzer } from './ComputerVisionAnalyzer';
import { AdvancedVoiceAI } from './AdvancedVoiceAI';

interface EnhancedAIDashboardProps {
  className?: string;
}

interface AICapability {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  color: string;
  status: 'active' | 'inactive' | 'processing';
  usage: number;
  lastUsed?: string;
}

export const EnhancedAIDashboard: React.FC<EnhancedAIDashboardProps> = ({
  className = ''
}) => {
  const { t } = useTranslation();
  const [activeSection, setActiveSection] = useState<'overview' | 'predictive' | 'vision' | 'voice' | 'settings'>('overview');
  const [aiCapabilities, setAiCapabilities] = useState<AICapability[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [aiStatus, setAiStatus] = useState<'online' | 'offline' | 'limited'>('online');

  useEffect(() => {
    initializeAICapabilities();
    checkAIStatus();
  }, []);

  const initializeAICapabilities = () => {
    const capabilities: AICapability[] = [
      {
        id: 'predictive_analytics',
        name: 'Predictive Analytics',
        description: 'Business success prediction, market forecasting, and risk assessment',
        icon: TrendingUp,
        color: 'text-blue-600',
        status: 'active',
        usage: 85,
        lastUsed: '2 hours ago'
      },
      {
        id: 'computer_vision',
        name: 'Computer Vision',
        description: 'Document analysis, pitch deck review, and visual content processing',
        icon: Eye,
        color: 'text-green-600',
        status: 'active',
        usage: 72,
        lastUsed: '1 hour ago'
      },
      {
        id: 'voice_ai',
        name: 'Voice AI',
        description: 'Speech recognition, synthesis, and voice command processing',
        icon: Mic,
        color: 'text-purple-600',
        status: 'active',
        usage: 68,
        lastUsed: '30 minutes ago'
      },
      {
        id: 'business_intelligence',
        name: 'Business Intelligence',
        description: 'Automated insights, recommendations, and strategic analysis',
        icon: Brain,
        color: 'text-orange-600',
        status: 'active',
        usage: 91,
        lastUsed: '15 minutes ago'
      },
      {
        id: 'risk_assessment',
        name: 'Risk Assessment',
        description: 'Comprehensive risk analysis and mitigation strategies',
        icon: Shield,
        color: 'text-red-600',
        status: 'active',
        usage: 76,
        lastUsed: '45 minutes ago'
      },
      {
        id: 'investment_scoring',
        name: 'Investment Scoring',
        description: 'Investment readiness assessment and investor matching',
        icon: DollarSign,
        color: 'text-yellow-600',
        status: 'active',
        usage: 63,
        lastUsed: '1 hour ago'
      }
    ];

    setAiCapabilities(capabilities);
  };

  const checkAIStatus = async () => {
    try {
      const response = await fetch('/api/ai/status/', {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setAiStatus(data.status === 'available' ? 'online' : 'limited');
      } else {
        setAiStatus('offline');
      }
    } catch (error) {
      console.error('Error checking AI status:', error);
      setAiStatus('offline');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'text-green-600';
      case 'limited': return 'text-yellow-600';
      case 'offline': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online': return '🟢';
      case 'limited': return '🟡';
      case 'offline': return '🔴';
      default: return '⚪';
    }
  };

  const renderOverview = () => (
    <div className="space-y-6">
      {/* AI Status Header */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <Brain className="w-8 h-8 text-purple-600" />
            <div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                Yasmeen AI Platform
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Advanced AI capabilities for Syrian entrepreneurs
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <span className="text-2xl">{getStatusIcon(aiStatus)}</span>
            <div className="text-right">
              <div className={`font-semibold ${getStatusColor(aiStatus)}`}>
                {aiStatus.charAt(0).toUpperCase() + aiStatus.slice(1)}
              </div>
              <div className="text-xs text-gray-500">AI Status</div>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">6</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">AI Capabilities</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">94%</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Accuracy Rate</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">1.2K</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Analyses Today</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">24/7</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Availability</div>
          </div>
        </div>
      </div>

      {/* AI Capabilities Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {aiCapabilities.map((capability) => {
          const Icon = capability.icon;
          return (
            <div
              key={capability.id}
              className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow hover:shadow-lg transition-shadow cursor-pointer"
              onClick={() => {
                if (capability.id === 'predictive_analytics') setActiveSection('predictive');
                else if (capability.id === 'computer_vision') setActiveSection('vision');
                else if (capability.id === 'voice_ai') setActiveSection('voice');
              }}
            >
              <div className="flex items-center justify-between mb-4">
                <Icon className={`w-8 h-8 ${capability.color}`} />
                <div className={`w-3 h-3 rounded-full ${
                  capability.status === 'active' ? 'bg-green-500' :
                  capability.status === 'processing' ? 'bg-yellow-500' : 'bg-gray-400'
                }`}></div>
              </div>
              
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                {capability.name}
              </h4>
              
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                {capability.description}
              </p>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Usage</span>
                  <span className="font-medium">{capability.usage}%</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${capability.color.replace('text-', 'bg-')}`}
                    style={{ width: `${capability.usage}%` }}
                  ></div>
                </div>
                {capability.lastUsed && (
                  <div className="text-xs text-gray-500">
                    Last used: {capability.lastUsed}
                  </div>
                )}
              </div>
              
              <div className="mt-4 flex items-center text-sm text-blue-600 hover:text-blue-700">
                <span>Explore</span>
                <ChevronRight className="w-4 h-4 ml-1" />
              </div>
            </div>
          );
        })}
      </div>

      {/* Recent AI Activities */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Recent AI Activities
        </h3>
        
        <div className="space-y-3">
          {[
            { action: 'Business Success Prediction', time: '2 minutes ago', result: '87% success probability', icon: Target, color: 'text-green-600' },
            { action: 'Document Analysis', time: '15 minutes ago', result: 'Business plan analyzed', icon: Eye, color: 'text-blue-600' },
            { action: 'Voice Command', time: '30 minutes ago', result: 'Navigation to dashboard', icon: Mic, color: 'text-purple-600' },
            { action: 'Risk Assessment', time: '1 hour ago', result: 'Medium risk level identified', icon: Shield, color: 'text-orange-600' }
          ].map((activity, index) => {
            const Icon = activity.icon;
            return (
              <div key={index} className="flex items-center space-x-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <Icon className={`w-5 h-5 ${activity.color}`} />
                <div className="flex-1">
                  <div className="font-medium text-gray-900 dark:text-white">
                    {activity.action}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {activity.result}
                  </div>
                </div>
                <div className="text-xs text-gray-500">
                  {activity.time}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* AI Recommendations */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <Lightbulb className="w-5 h-5 text-yellow-500 mr-2" />
          AI Recommendations
        </h3>
        
        <div className="space-y-3">
          <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="font-medium text-blue-900 dark:text-blue-100">
              Optimize Your Business Plan
            </div>
            <div className="text-sm text-blue-700 dark:text-blue-300 mt-1">
              Use our Computer Vision analyzer to review your pitch deck and get improvement suggestions.
            </div>
          </div>
          
          <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
            <div className="font-medium text-green-900 dark:text-green-100">
              Voice Commands Available
            </div>
            <div className="text-sm text-green-700 dark:text-green-300 mt-1">
              Try saying "Show me my business ideas" or "Analyze market trends" for quick navigation.
            </div>
          </div>
          
          <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800">
            <div className="font-medium text-purple-900 dark:text-purple-100">
              Predictive Analytics Ready
            </div>
            <div className="text-sm text-purple-700 dark:text-purple-300 mt-1">
              Get AI-powered predictions for your business success and market opportunities.
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const navigationItems = [
    { id: 'overview', label: 'Overview', icon: Activity },
    { id: 'predictive', label: 'Predictive Analytics', icon: TrendingUp },
    { id: 'vision', label: 'Computer Vision', icon: Eye },
    { id: 'voice', label: 'Voice AI', icon: Mic },
    { id: 'settings', label: 'Settings', icon: Settings }
  ];

  return (
    <div className={`enhanced-ai-dashboard glass-light ${className}`}>
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Brain className="w-10 h-10 text-purple-600" />
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                  Enhanced AI Dashboard
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  Complete AI-powered business intelligence platform
                </p>
              </div>
            </div>
            
            <button
              onClick={checkAIStatus}
              disabled={isLoading}
              className="flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50"
            >
              <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              <span>Refresh Status</span>
            </button>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex space-x-1 mb-6 bg-gray-100 dark:bg-gray-800 rounded-lg p-1 overflow-x-auto">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            return (
              <button
                key={item.id}
                onClick={() => setActiveSection(item.id as any)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors whitespace-nowrap ${
                  activeSection === item.id
                    ? 'bg-white dark:bg-gray-700 shadow-sm text-purple-600'
                    : 'hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span className="text-sm font-medium">{item.label}</span>
              </button>
            );
          })}
        </div>

        {/* Content */}
        <div className="min-h-screen">
          {activeSection === 'overview' && renderOverview()}
          {activeSection === 'predictive' && <PredictiveAnalyticsDashboard />}
          {activeSection === 'vision' && <ComputerVisionAnalyzer />}
          {activeSection === 'voice' && <AdvancedVoiceAI />}
          {activeSection === 'settings' && (
            <div className="text-center py-12">
              <Settings className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                AI Settings
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Configure AI preferences and advanced options
              </p>
            </div>
          )}
        </div>
      </div>
  );
};
