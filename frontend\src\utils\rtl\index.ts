// RTL language detection and direction utilities
const RTL_LANGUAGES = ['ar', 'he', 'fa', 'ur', 'ku', 'ps', 'sd'];

/**
 * Check if a language is RTL (Right-to-Left)
 * @param language Language code (e.g., 'ar', 'en')
 * @returns true if the language is RTL
 */
export const isRTLLanguage = (language: string): boolean => {
  return RTL_LANGUAGES.includes(language.toLowerCase());
};

/**
 * Get text direction for a language
 * @param language Language code (e.g., 'ar', 'en')
 * @returns 'rtl' for RTL languages, 'ltr' for LTR languages
 */
export const getDirectionFromLanguage = (language: string): 'ltr' | 'rtl' => {
  return isRTLLanguage(language) ? 'rtl' : 'ltr';
};

/**
 * Hook to get current language direction
 * @returns Object with isRTL and other language utilities
 * @deprecated Use useLanguage hook instead
 */
export const useLanguageDirection = () => {
  // This is a fallback implementation
  const language = localStorage.getItem('language') || 'en';
  const direction = getDirectionFromLanguage(language);
  const isRTL = direction === 'rtl';

  return { isRTL, language, direction };
};

export default useLanguageDirection;
