# Generated migration for adding new template types

from django.db import migrations
from django.core.management import call_command


def create_new_templates(apps, schema_editor):
    """Create new template types in the database"""
    BusinessPlanTemplate = apps.get_model('incubator', 'BusinessPlanTemplate')

    # New template definitions
    new_templates = [
        {
            'name': 'Fitness & Wellness Business Plan',
            'description': 'Template for fitness centers, gyms, and wellness businesses',
            'template_type': 'fitness',
            'industry': 'services',
            'difficulty_level': 'intermediate',
            'estimated_completion_time': 8,
            'is_active': True,
            'is_premium': False,
            'sections_data': {
                'facility_concept': {
                    'title': 'Facility Concept & Services',
                    'description': 'Your fitness facility concept and service offerings',
                    'section_type': 'service_overview',
                    'order': 1,
                    'required': True
                },
                'member_acquisition': {
                    'title': 'Member Acquisition Strategy',
                    'description': 'How you\'ll attract and retain members',
                    'section_type': 'marketing_strategy',
                    'order': 2,
                    'required': True
                }
            }
        },
        {
            'name': 'Food Truck Business Plan',
            'description': 'Template for mobile food service businesses',
            'template_type': 'food_truck',
            'industry': 'hospitality',
            'difficulty_level': 'intermediate',
            'estimated_completion_time': 6,
            'is_active': True,
            'is_premium': False,
            'sections_data': {
                'concept_menu': {
                    'title': 'Food Concept & Menu',
                    'description': 'Your food concept and menu offerings',
                    'section_type': 'product_overview',
                    'order': 1,
                    'required': True
                },
                'location_strategy': {
                    'title': 'Location & Route Strategy',
                    'description': 'Your location strategy and route planning',
                    'section_type': 'location_analysis',
                    'order': 2,
                    'required': True
                }
            }
        },
        {
            'name': 'Beauty Salon Business Plan',
            'description': 'Template for beauty salons and spa businesses',
            'template_type': 'beauty_salon',
            'industry': 'services',
            'difficulty_level': 'beginner',
            'estimated_completion_time': 5,
            'is_active': True,
            'is_premium': False,
            'sections_data': {
                'service_menu': {
                    'title': 'Service Menu & Specializations',
                    'description': 'Your beauty services and specializations',
                    'section_type': 'service_catalog',
                    'order': 1,
                    'required': True
                }
            }
        },
        {
            'name': 'Pet Services Business Plan',
            'description': 'Template for pet care and veterinary businesses',
            'template_type': 'pet_services',
            'industry': 'services',
            'difficulty_level': 'intermediate',
            'estimated_completion_time': 7,
            'is_active': True,
            'is_premium': False,
            'sections_data': {
                'service_offerings': {
                    'title': 'Pet Service Offerings',
                    'description': 'Your pet care services and specializations',
                    'section_type': 'service_overview',
                    'order': 1,
                    'required': True
                }
            }
        },
        {
            'name': 'Automotive Business Plan',
            'description': 'Template for automotive repair and service businesses',
            'template_type': 'automotive',
            'industry': 'services',
            'difficulty_level': 'intermediate',
            'estimated_completion_time': 8,
            'is_active': True,
            'is_premium': False,
            'sections_data': {
                'service_capabilities': {
                    'title': 'Service Capabilities & Equipment',
                    'description': 'Your automotive services and equipment',
                    'section_type': 'technical_capabilities',
                    'order': 1,
                    'required': True
                }
            }
        },
        {
            'name': 'Photography Business Plan',
            'description': 'Template for photography and creative services',
            'template_type': 'photography',
            'industry': 'creative',
            'difficulty_level': 'beginner',
            'estimated_completion_time': 4,
            'is_active': True,
            'is_premium': False,
            'sections_data': {
                'portfolio_services': {
                    'title': 'Portfolio & Service Offerings',
                    'description': 'Your photography style and service packages',
                    'section_type': 'creative_portfolio',
                    'order': 1,
                    'required': True
                }
            }
        },
        {
            'name': 'Digital Marketing Agency Plan',
            'description': 'Template for digital marketing and advertising agencies',
            'template_type': 'digital_marketing',
            'industry': 'marketing',
            'difficulty_level': 'intermediate',
            'estimated_completion_time': 7,
            'is_active': True,
            'is_premium': False,
            'sections_data': {
                'service_portfolio': {
                    'title': 'Digital Marketing Services',
                    'description': 'Your digital marketing service portfolio',
                    'section_type': 'service_portfolio',
                    'order': 1,
                    'required': True
                }
            }
        },
        {
            'name': 'Dropshipping Business Plan',
            'description': 'Template for dropshipping e-commerce businesses',
            'template_type': 'dropshipping',
            'industry': 'retail',
            'difficulty_level': 'beginner',
            'estimated_completion_time': 5,
            'is_active': True,
            'is_premium': False,
            'sections_data': {
                'product_strategy': {
                    'title': 'Product Selection & Sourcing',
                    'description': 'Your product selection and supplier strategy',
                    'section_type': 'product_sourcing',
                    'order': 1,
                    'required': True
                }
            }
        },
        {
            'name': 'Coaching Business Plan',
            'description': 'Template for life, business, and specialty coaching services',
            'template_type': 'coaching',
            'industry': 'services',
            'difficulty_level': 'beginner',
            'estimated_completion_time': 5,
            'is_active': True,
            'is_premium': False,
            'sections_data': {
                'coaching_methodology': {
                    'title': 'Coaching Methodology & Approach',
                    'description': 'Your coaching methodology and service approach',
                    'section_type': 'methodology_framework',
                    'order': 1,
                    'required': True
                }
            }
        },
        {
            'name': 'Legal Services Business Plan',
            'description': 'Template for law firms and legal service providers',
            'template_type': 'legal_services',
            'industry': 'professional',
            'difficulty_level': 'advanced',
            'estimated_completion_time': 10,
            'is_active': True,
            'is_premium': True,
            'sections_data': {
                'practice_areas': {
                    'title': 'Practice Areas & Legal Expertise',
                    'description': 'Your legal practice areas and expertise',
                    'section_type': 'legal_framework',
                    'order': 1,
                    'required': True
                }
            }
        }
    ]

    # Create templates
    for template_data in new_templates:
        template, created = BusinessPlanTemplate.objects.get_or_create(
            template_type=template_data['template_type'],
            defaults=template_data
        )
        if created:
            print(f"Created template: {template.name}")
        else:
            print(f"Template already exists: {template.name}")


def reverse_create_templates(apps, schema_editor):
    """Remove the new templates"""
    BusinessPlanTemplate = apps.get_model('incubator', 'BusinessPlanTemplate')

    template_types_to_remove = [
        'fitness', 'food_truck', 'beauty_salon', 'pet_services', 'automotive',
        'photography', 'digital_marketing', 'dropshipping', 'coaching', 'legal_services'
    ]

    BusinessPlanTemplate.objects.filter(template_type__in=template_types_to_remove).delete()


class Migration(migrations.Migration):

    dependencies = [
        ('incubator', '0002_fundingopportunity_fundingapplication_investment_and_more'),
    ]

    operations = [
        migrations.RunPython(
            create_new_templates,
            reverse_create_templates,
        ),
    ]
