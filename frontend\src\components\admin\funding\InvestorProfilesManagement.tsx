import React, { useState } from 'react';
import { Search, Plus, Edit, Trash2, Eye, Filter, RefreshCw, DollarSign, TrendingUp, Users, CheckCircle, Building } from 'lucide-react';
import { DashboardLayout } from '../dashboard';
import { AdvancedFilter, BulkActions } from '../common';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';
import { 
  useInvestorProfilesList, 
  useCreateInvestorProfile, 
  useUpdateInvestorProfile, 
  useDeleteInvestorProfile 
} from '../../../hooks/useFunding';
import { InvestorProfile } from '../../../services/incubatorApi';
import { Alert, Button } from '../../ui';
import InvestorProfileModal from './components/InvestorProfileModal';
import InvestorProfileDeleteModal from './components/InvestorProfileDeleteModal';

interface FilterValue {
  is_verified?: boolean;
  investment_focus?: string;
  min_investment?: number;
  max_investment?: number;
  search?: string;
}

const InvestorProfilesManagement: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  // State management
  const [selectedProfiles, setSelectedProfiles] = useState<number[]>([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedProfile, setSelectedProfile] = useState<InvestorProfile | null>(null);
  const [filters, setFilters] = useState<FilterValue>({});
  const [searchTerm, setSearchTerm] = useState('');

  // API hooks
  const { data: profilesData, isLoading, error, refetch } = useInvestorProfilesList(filters);
  const createProfile = useCreateInvestorProfile();
  const updateProfile = useUpdateInvestorProfile();
  const deleteProfile = useDeleteInvestorProfile();

  const profiles = profilesData?.results || [];

  // Filter options
  const filterOptions = [
    {
      key: 'is_verified',
      label: t('admin.verified', 'Verified'),
      type: 'select' as const,
      options: [
        { value: true, label: t('common.verified', 'Verified') },
        { value: false, label: t('common.unverified', 'Unverified') }
      ]
    },
    {
      key: 'investment_focus',
      label: t('investor.focus', 'Investment Focus'),
      type: 'select' as const,
      options: [
        { value: 'technology', label: t('investor.focus.technology', 'Technology') },
        { value: 'healthcare', label: t('investor.focus.healthcare', 'Healthcare') },
        { value: 'fintech', label: t('investor.focus.fintech', 'FinTech') },
        { value: 'ecommerce', label: t('investor.focus.ecommerce', 'E-commerce') },
        { value: 'sustainability', label: t('investor.focus.sustainability', 'Sustainability') }
      ]
    },
    {
      key: 'search',
      label: t('common.search', 'Search'),
      type: 'text' as const,
      placeholder: t('investor.searchPlaceholder', 'Search investor profiles...')
    }
  ];

  // Bulk actions
  const bulkActions = [
    {
      id: 'verify',
      label: t('investor.verifySelected', 'Verify Selected'),
      icon: CheckCircle,
      variant: 'primary' as const,
      action: async (ids: number[]) => {
        for (const id of ids) {
          await updateProfile.mutateAsync({ id, data: { is_verified: true } });
        }
        setSelectedProfiles([]);
      }
    },
    {
      id: 'unverify',
      label: t('investor.unverifySelected', 'Unverify Selected'),
      icon: CheckCircle,
      variant: 'secondary' as const,
      action: async (ids: number[]) => {
        for (const id of ids) {
          await updateProfile.mutateAsync({ id, data: { is_verified: false } });
        }
        setSelectedProfiles([]);
      }
    },
    {
      id: 'delete',
      label: t('common.delete', 'Delete'),
      icon: Trash2,
      variant: 'danger' as const,
      action: async (ids: number[]) => {
        for (const id of ids) {
          await deleteProfile.mutateAsync(id);
        }
        setSelectedProfiles([]);
      }
    }
  ];

  // Event handlers
  const handleCreateProfile = () => {
    setSelectedProfile(null);
    setShowCreateModal(true);
  };

  const handleEditProfile = (profile: InvestorProfile) => {
    setSelectedProfile(profile);
    setShowEditModal(true);
  };

  const handleDeleteProfile = (profile: InvestorProfile) => {
    setSelectedProfile(profile);
    setShowDeleteModal(true);
  };

  const handleFilterChange = (newFilters: FilterValue) => {
    setFilters(newFilters);
  };

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setFilters(prev => ({ ...prev, search: term }));
  };

  const getInvestmentRangeBadge = (minInvestment: number, maxInvestment: number) => {
    const formatAmount = (amount: number) => {
      if (amount >= 1000000) return `$${(amount / 1000000).toFixed(1)}M`;
      if (amount >= 1000) return `$${(amount / 1000).toFixed(0)}K`;
      return `$${amount}`;
    };

    return (
      <span className="px-2 py-1 rounded-full text-xs font-medium bg-green-500/20 text-green-300">
        {formatAmount(minInvestment)} - {formatAmount(maxInvestment)}
      </span>
    );
  };

  if (error) {
    return (
      <DashboardLayout currentPage="funding">
        <Alert variant="error">
          {t('common.error.loadFailed', 'Failed to load data. Please try again.')}
        </Alert>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout currentPage="funding">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-white">
              {t('investor.management', 'Investor Profiles Management')}
            </h1>
            <p className="text-gray-400 mt-1">
              {t('investor.managementDescription', 'Manage investor profiles and investment preferences')}
            </p>
          </div>
          <div className="flex gap-3">
            <Button
              onClick={() => refetch()}
              variant="secondary"
              size="sm"
              disabled={isLoading}
            >
              <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              {t('common.refresh', 'Refresh')}
            </Button>
            <Button onClick={handleCreateProfile} size="sm">
              <Plus className="w-4 h-4 mr-2" />
              {t('investor.create', 'Create Profile')}
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-indigo-900/50 rounded-lg p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center">
                <Users className="w-5 h-5 text-blue-400" />
              </div>
              <div>
                <div className="text-gray-400 text-sm">{t('investor.totalInvestors', 'Total Investors')}</div>
                <div className="text-white font-bold">{profiles.length}</div>
              </div>
            </div>
          </div>
          <div className="bg-indigo-900/50 rounded-lg p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center">
                <CheckCircle className="w-5 h-5 text-green-400" />
              </div>
              <div>
                <div className="text-gray-400 text-sm">{t('investor.verified', 'Verified')}</div>
                <div className="text-white font-bold">{profiles.filter(p => p.is_verified).length}</div>
              </div>
            </div>
          </div>
          <div className="bg-indigo-900/50 rounded-lg p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-full bg-purple-500/20 flex items-center justify-center">
                <Building className="w-5 h-5 text-purple-400" />
              </div>
              <div>
                <div className="text-gray-400 text-sm">{t('investor.activeInvestments', 'Active Investments')}</div>
                <div className="text-white font-bold">24</div>
              </div>
            </div>
          </div>
          <div className="bg-indigo-900/50 rounded-lg p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-full bg-orange-500/20 flex items-center justify-center">
                <DollarSign className="w-5 h-5 text-orange-400" />
              </div>
              <div>
                <div className="text-gray-400 text-sm">{t('investor.totalInvested', 'Total Invested')}</div>
                <div className="text-white font-bold">$5.2M</div>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="bg-indigo-900/50 rounded-lg p-4">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder={t('investor.searchPlaceholder', 'Search investor profiles...')}
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                />
              </div>
            </div>
            <AdvancedFilter
              filters={filterOptions}
              values={filters}
              onChange={handleFilterChange}
            />
          </div>
        </div>

        {/* Bulk Actions */}
        {selectedProfiles.length > 0 && (
          <BulkActions
            selectedCount={selectedProfiles.length}
            actions={bulkActions}
            onClearSelection={() => setSelectedProfiles([])}
          />
        )}

        {/* Investor Profiles Table */}
        <div className="bg-indigo-900/50 rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-indigo-800/50">
                <tr>
                  <th className="px-4 py-3 text-left">
                    <input
                      type="checkbox"
                      checked={selectedProfiles.length === profiles.length && profiles.length > 0}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedProfiles(profiles.map(p => p.id));
                        } else {
                          setSelectedProfiles([]);
                        }
                      }}
                      className="rounded border-gray-600 bg-indigo-800 text-purple-500 focus:ring-purple-500"
                    />
                  </th>
                  <th className="px-4 py-3 text-left text-gray-300 font-medium">
                    {t('common.name', 'Name')}
                  </th>
                  <th className="px-4 py-3 text-left text-gray-300 font-medium">
                    {t('investor.organization', 'Organization')}
                  </th>
                  <th className="px-4 py-3 text-left text-gray-300 font-medium">
                    {t('investor.investmentRange', 'Investment Range')}
                  </th>
                  <th className="px-4 py-3 text-left text-gray-300 font-medium">
                    {t('investor.focus', 'Focus Areas')}
                  </th>
                  <th className="px-4 py-3 text-left text-gray-300 font-medium">
                    {t('admin.status', 'Status')}
                  </th>
                  <th className="px-4 py-3 text-right text-gray-300 font-medium">
                    {t('common.actions', 'Actions')}
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-indigo-800/50">
                {isLoading ? (
                  <tr>
                    <td colSpan={7} className="px-4 py-8 text-center text-gray-400">
                      {t('common.loading', 'Loading...')}
                    </td>
                  </tr>
                ) : profiles.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="px-4 py-8 text-center text-gray-400">
                      {t('investor.noProfiles', 'No investor profiles found')}
                    </td>
                  </tr>
                ) : (
                  profiles.map((profile) => (
                    <tr key={profile.id} className="hover:bg-indigo-800/30">
                      <td className="px-4 py-3">
                        <input
                          type="checkbox"
                          checked={selectedProfiles.includes(profile.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedProfiles(prev => [...prev, profile.id]);
                            } else {
                              setSelectedProfiles(prev => prev.filter(id => id !== profile.id));
                            }
                          }}
                          className="rounded border-gray-600 bg-indigo-800 text-purple-500 focus:ring-purple-500"
                        />
                      </td>
                      <td className="px-4 py-3">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 rounded-full bg-green-700 flex items-center justify-center text-white text-sm font-bold">
                            {profile.user.first_name.charAt(0)}{profile.user.last_name.charAt(0)}
                          </div>
                          <div>
                            <div className="text-white font-medium">
                              {profile.user.first_name} {profile.user.last_name}
                            </div>
                            <div className="text-gray-400 text-sm">{profile.title}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-3">
                        <div className="text-gray-300">{profile.organization}</div>
                      </td>
                      <td className="px-4 py-3">
                        {getInvestmentRangeBadge(profile.min_investment, profile.max_investment)}
                      </td>
                      <td className="px-4 py-3">
                        <div className="flex flex-wrap gap-1">
                          {profile.investment_focus?.slice(0, 2).map((focus, index) => (
                            <span key={index} className="px-2 py-1 rounded-full text-xs font-medium bg-blue-500/20 text-blue-300">
                              {focus}
                            </span>
                          ))}
                          {profile.investment_focus?.length > 2 && (
                            <span className="px-2 py-1 rounded-full text-xs font-medium bg-gray-500/20 text-gray-300">
                              +{profile.investment_focus.length - 2}
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="px-4 py-3">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          profile.is_verified ? 'bg-green-500/20 text-green-300' : 'bg-yellow-500/20 text-yellow-300'
                        }`}>
                          {profile.is_verified ? t('common.verified', 'Verified') : t('common.pending', 'Pending')}
                        </span>
                      </td>
                      <td className="px-4 py-3">
                        <div className="flex justify-end gap-2">
                          <Button
                            onClick={() => handleEditProfile(profile)}
                            variant="secondary"
                            size="sm"
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button
                            onClick={() => handleDeleteProfile(profile)}
                            variant="danger"
                            size="sm"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Modals */}
        <InvestorProfileModal
          isOpen={showCreateModal || showEditModal}
          onClose={() => {
            setShowCreateModal(false);
            setShowEditModal(false);
            setSelectedProfile(null);
          }}
          profile={selectedProfile}
          isEditing={showEditModal}
        />

        <InvestorProfileDeleteModal
          isOpen={showDeleteModal}
          onClose={() => {
            setShowDeleteModal(false);
            setSelectedProfile(null);
          }}
          profile={selectedProfile}
        />
      </div>
    </DashboardLayout>
  );
};

export default InvestorProfilesManagement;
