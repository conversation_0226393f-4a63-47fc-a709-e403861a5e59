import React from 'react';
import { Link, useParams } from 'react-router-dom';
import { Home, ChevronRight, Award } from 'lucide-react';
import ForumBadges from '../components/forum/ForumBadges';
import { useAppSelector } from '../store/hooks';
import { useTranslation } from 'react-i18next';
const ForumBadgesPage: React.FC = () => {
  const { t } = useTranslation();
  const { language } = useAppSelector(state => state.language);
  const { id } = useParams<{ id?: string }>();
  const { user: currentUser } = useAppSelector(state => state.auth);
  const userId = id ? parseInt(id) : undefined;

  const isOwnProfile = !id || (currentUser && userId === currentUser.id);
  const pageTitle = isOwnProfile ? t('forum.myBadges') : t('forum.userBadges');

  return (
    <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 text-white py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-6xl mx-auto">
        {/* Breadcrumb */}
        <nav className={`flex mb-6 text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
          <ol className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
            <li>
              <Link to="/" className="text-gray-400 hover:text-white">
                <Home size={16} />
              </Link>
            </li>
            <li className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <ChevronRight size={14} className="text-gray-500" />
              <Link to="/forum" className={`ml-2 text-gray-400 hover:text-white ${isRTL ? "space-x-reverse" : ""}`}>
                {t('forum.title')}
              </Link>
            </li>
            <li className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <ChevronRight size={14} className="text-gray-500" />
              <span className={`ml-2 text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <Award size={14} className={language === 'ar' ? 'ml-1' : 'mr-1'} />
                {t('forum.badges')}
              </span>
            </li>
          </ol>
        </nav>

        <h1 className="text-3xl font-bold mb-8">{pageTitle}</h1>

        <ForumBadges userId={userId} />
      </div>
    </div>
  );
};

export default ForumBadgesPage;
