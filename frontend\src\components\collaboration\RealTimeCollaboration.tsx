/**
 * Real-Time Collaboration System
 * Live collaboration features for business planning and team work
 */

import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Users, MessageCircle, Edit3, Share2, Video, Mic, MicOff,
  VideoOff, Monitor, Hand, Clock, CheckCircle, AlertCircle,
  UserPlus, Settings, Download, Upload
} from 'lucide-react';
import { useAppSelector } from '../../store/hooks';
import { useLanguage } from '../../hooks/useLanguage';

interface CollaborationUser {
  id: string;
  name: string;
  avatar?: string;
  status: 'online' | 'away' | 'busy' | 'offline';
  cursor?: { x: number; y: number };
  selection?: { start: number; end: number };
  role: 'owner' | 'editor' | 'viewer';
}

interface CollaborationSession {
  id: string;
  title: string;
  type: 'business_plan' | 'document' | 'presentation' | 'meeting';
  users: CollaborationUser[];
  isActive: boolean;
  startTime: Date;
  lastActivity: Date;
}

interface ChatMessage {
  id: string;
  userId: string;
  userName: string;
  message: string;
  timestamp: Date;
  type: 'text' | 'system' | 'file';
}

const RealTimeCollaboration: React.FC<{
  sessionId: string;
  documentId?: string;
  onUserJoin?: (user: CollaborationUser) => void;
  onUserLeave?: (userId: string) => void;
}> = ({ sessionId, documentId, onUserJoin, onUserLeave }) => {
  const { isRTL } = useLanguage();
  const { user } = useAppSelector(state => state.auth);

  const [session, setSession] = useState<CollaborationSession | null>(null);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isVideoCall, setIsVideoCall] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [showChat, setShowChat] = useState(true);
  const [showUsers, setShowUsers] = useState(true);

  const chatRef = useRef<HTMLDivElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    initializeSession();
    setupWebSocket();
    return () => {
      cleanupSession();
    };
  }, [sessionId]);

  useEffect(() => {
    if (chatRef.current) {
      chatRef.current.scrollTop = chatRef.current.scrollHeight;
    }
  }, [chatMessages]);

  const initializeSession = () => {
    const { t } = useTranslation();

    // Mock session data - replace with real API call when backend is ready
    const mockSession: CollaborationSession = {
      id: sessionId,
      title: t("common.business.plan.collaboration", "Business Plan Collaboration"),
      type: 'business_plan',
      users: [
        {
          id: user?.id?.toString() || '1',
          name: user?.username || t("common.current.user.status", "Current User"),
          status: 'online',
          role: 'owner'
        },
        {
          id: '2',
          name: 'Ahmed Hassan',
          status: 'online',
          role: 'editor'
        },
        {
          id: '3',
          name: 'Sara Mohamed',
          status: 'away',
          role: 'viewer'
        }
      ],
      isActive: true,
      startTime: new Date(Date.now() - 30 * 60 * 1000),
      lastActivity: new Date()
    };

    setSession(mockSession);

    // Mock chat messages
    setChatMessages([
      {
        id: '1',
        userId: '2',
        userName: t("common.ahmed.hassan.message", "Ahmed Hassan"),
        message: 'Welcome to the collaboration session!',
        timestamp: new Date(Date.now() - 15 * 60 * 1000),
        type: 'text'
      },
      {
        id: '2',
        userId: 'system',
        userName: t("common.system.message.sara", "System"),
        message: 'Sara Mohamed joined the session',
        timestamp: new Date(Date.now() - 10 * 60 * 1000),
        type: 'system'
      }
    ]);
  };

  const setupWebSocket = () => {
    // Mock WebSocket setup - in real app, this would be actual WebSocket connection
    console.log("Setting up WebSocket for session:", sessionId);
  };

  const cleanupSession = () => {
    // Cleanup WebSocket connections and resources
    console.log("Cleaning up collaboration session");
  };

  const sendChatMessage = () => {
    if (!newMessage.trim() || !user) return;

    const message: ChatMessage = {
      id: Date.now().toString(),
      userId: user.id?.toString() || '1',
      userName: user.username || 'User',
      message: newMessage.trim(),
      timestamp: new Date(),
      type: 'text'
    };

    setChatMessages(prev => [...prev, message]);
    setNewMessage('');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendChatMessage();
    }
  };

  const toggleVideoCall = () => {
    setIsVideoCall(!isVideoCall);
  };

  const toggleMute = () => {
    setIsMuted(!isMuted);
  };

  const toggleVideo = () => {
    setIsVideoEnabled(!isVideoEnabled);
  };

  const toggleScreenShare = () => {
    setIsScreenSharing(!isScreenSharing);
  };

  const inviteUser = () => {
    // Mock invite functionality
    const inviteLink = `${window.location.origin}/collaborate/${sessionId}`;
    navigator.clipboard.writeText(inviteLink);
    // Show toast notification
  };

  const UserAvatar: React.FC<{ user: CollaborationUser; size?: 'sm' | 'md' | 'lg' }> = ({
    user,
    size = 'md'
  }) => {
    const sizeClasses = {
      sm: 'w-8 h-8 text-xs',
      md: 'w-10 h-10 text-sm',
      lg: 'w-12 h-12 text-base'
    };

    const statusColors = {
      online: 'bg-green-500',
      away: 'bg-yellow-500',
      busy: 'bg-red-500',
      offline: 'bg-gray-500'
    };

    return (
      <div className="relative">
        <div className={`
          ${sizeClasses[size]} rounded-full bg-gradient-to-br from-purple-500 to-pink-500
          flex items-center justify-center text-white font-semibold
        `}>
          {user.avatar ? (
            <img src={user.avatar} alt={user.name} className="w-full h-full rounded-full object-cover" />
          ) : (
            user.name.charAt(0).toUpperCase()
          )}
        </div>
        <div className={`
          absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white
          ${statusColors[user.status]}
        `} />
      </div>
    );
  };

  if (!session) {
    return (
      <div className={`flex items-center justify-center h-64 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  return (
    <div className="bg-indigo-900/30 backdrop-blur-sm rounded-xl border border-indigo-800/50 overflow-hidden">
      {/* Header */}
      <div className="bg-indigo-800/50 p-4 border-b border-indigo-700/50">
        <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
              <Users className="text-purple-400" size={20} />
              <h3 className="text-white font-semibold">{session.title}</h3>
            </div>
            <div className={`flex items-center space-x-1 ${isRTL ? "flex-row-reverse" : ""}`}>
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-green-400 text-sm">{t('collaboration.live')}</span>
            </div>
          </div>

          <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
            {/* Video Call Controls */}
            <button
              onClick={toggleVideoCall}
              className={`p-2 rounded-lg transition-colors ${
                isVideoCall ? 'bg-green-500 text-white' : 'bg-gray-600 text-gray-300 hover:bg-gray-500'}
              }`}
            >
              <Video size={16} />
            </button>

            <button
              onClick={toggleMute}
              className={`p-2 rounded-lg transition-colors ${
                isMuted ? 'bg-red-500 text-white' : 'bg-gray-600 text-gray-300 hover:bg-gray-500'}
              }`}
            >
              {isMuted ? <MicOff size={16} /> : <Mic size={16} />}
            </button>

            <button
              onClick={toggleScreenShare}
              className={`p-2 rounded-lg transition-colors ${
                isScreenSharing ? 'bg-blue-500 text-white' : 'bg-gray-600 text-gray-300 hover:bg-gray-500'}
              }`}
            >
              <Monitor size={16} />
            </button>

            <button
              onClick={inviteUser}
              className="p-2 bg-purple-500 hover:bg-purple-600 text-white rounded-lg transition-colors"
            >
              <UserPlus size={16} />
            </button>
          </div>
        </div>
      </div>

      <div className={`flex h-96 ${isRTL ? "flex-row-reverse" : ""}`}>
        {/* Users Panel */}
        {showUsers && (
          <div className="w-64 bg-indigo-800/30 border-r border-indigo-700/50 p-4">
            <div className={`flex items-center justify-between mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
              <h4 className="text-white font-medium">{t('collaboration.participants')}</h4>
              <span className="text-gray-400 text-sm">{session.users.length}</span>
            </div>

            <div className="space-y-3">
              {session.users.map(collaborationUser => (
                <div key={collaborationUser.id} className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <UserAvatar user={collaborationUser} />
                  <div className={`flex-1 min-w-0 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className="text-white text-sm font-medium truncate">
                      {collaborationUser.name}
                      {collaborationUser.id === user?.id?.toString() && (
                        <span className={`text-purple-400 ml-1 ${isRTL ? "space-x-reverse" : ""}`}>({t('collaboration.you')})</span>
                      )}
                    </div>
                    <div className="text-gray-400 text-xs capitalize">{collaborationUser.role}</div>
                  </div>
                  <div className={`w-2 h-2 rounded-full ${
                    collaborationUser.status === 'online' ? 'bg-green-500' :
                    collaborationUser.status === 'away' ? 'bg-yellow-500' :
                    collaborationUser.status === 'busy' ? 'bg-red-500' : 'bg-gray-500'}
                  }`} />
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Main Content Area */}
        <div className={`flex-1 flex flex-col ${isRTL ? "flex-row-reverse" : ""}`}>
          {/* Video Call Area */}
          {isVideoCall && (
            <div className="bg-black/50 p-4 border-b border-indigo-700/50">
              <div className="grid grid-cols-2 gap-4">
                {session.users.slice(0, 4).map(collaborationUser => (
                  <div key={collaborationUser.id} className="relative bg-gray-800 rounded-lg aspect-video">
                    <video
                      ref={collaborationUser.id === user?.id?.toString() ? videoRef : undefined}
                      className="w-full h-full object-cover rounded-lg"
                      autoPlay
                      muted={collaborationUser.id === user?.id?.toString()}
                    />
                    <div className="absolute bottom-2 left-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                      {collaborationUser.name}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Chat Area */}
          {showChat && (
            <div className={`flex-1 flex flex-col ${isRTL ? "flex-row-reverse" : ""}`}>
              <div className={`flex-1 p-4 overflow-y-auto ${isRTL ? "flex-row-reverse" : ""}`} ref={chatRef}>
                <div className="space-y-3">
                  {chatMessages.map(message => (
                    <div key={message.id} className={`flex ${
                      message.userId === user?.id?.toString() ? 'justify-end' : 'justify-start'}
                    }`}>
                      {message.type === 'system' ? (
                        <div className="text-center text-gray-400 text-sm italic">
                          {message.message}
                        </div>
                      ) : (
                        <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                          message.userId === user?.id?.toString()
                            ? 'bg-purple-500 text-white'
                            : 'bg-gray-700 text-white'}
                        }`}>
                          {message.userId !== user?.id?.toString() && (
                            <div className="text-xs text-gray-300 mb-1">{message.userName}</div>
                          )}
                          <div className="text-sm">{message.message}</div>
                          <div className="text-xs text-gray-300 mt-1">
                            {message.timestamp.toLocaleTimeString()}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Chat Input */}
              <div className="p-4 border-t border-indigo-700/50">
                <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <input
                    type="text"
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder={t('collaboration.typeMessage')}
                    className={`flex-1 bg-indigo-800/50 border border-indigo-700 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 ${isRTL ? "flex-row-reverse" : ""}`}
                  />
                  <button
                    onClick={sendChatMessage}
                    disabled={!newMessage.trim()}
                    className="bg-purple-500 hover:bg-purple-600 disabled:bg-gray-600 text-white p-2 rounded-lg transition-colors"
                  >
                    <MessageCircle size={16} />
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Footer */}
      <div className="bg-indigo-800/50 p-3 border-t border-indigo-700/50">
        <div className={`flex items-center justify-between text-sm text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`flex items-center space-x-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            <span className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <Clock size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
              {t('collaboration.sessionStarted')}: {session.startTime.toLocaleTimeString()}
            </span>
            <span className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <CheckCircle size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
              {t('collaboration.autoSave')}: {t('collaboration.enabled')}
            </span>
          </div>

          <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
            <button
              onClick={() => setShowUsers(!showUsers)}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <Users size={14} />
            </button>
            <button
              onClick={() => setShowChat(!showChat)}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <MessageCircle size={14} />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RealTimeCollaboration;
