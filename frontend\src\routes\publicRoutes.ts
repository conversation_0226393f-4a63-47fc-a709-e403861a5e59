import { lazy } from 'react';
import { RouteConfig, createPublicRoute, createAuthRoute } from './routeConfig';

// Eagerly loaded public components
import LoginPage from '../pages/LoginPage';
import RegisterPage from '../pages/RegisterPage';

import { FeaturesPage } from '../pages/FeaturesPage';
// DemoPage removed - demo functionality integrated into main pages
import HomePage from '../pages/HomePage';
import LogoutPage from '../pages/LogoutPage';
// ✅ REMOVED: AdminLogin - now using unified login for all users

// Lazy loaded public components
const BusinessIncubator = lazy(() => import('../components/incubator/BusinessIncubator'));
const ReactQueryDemo = lazy(() => import('../components/examples/ReactQueryDemo'));
// ConsolidatedAIDemoPage removed - demo functionality integrated into main AI pages
const AdvancedSearchPage = lazy(() => import('../pages/dashboard/AdvancedSearchPage'));
const TemplateBrowserPage = lazy(() => import('../pages/TemplateBrowserPage'));
const TemplateTestPage = lazy(() => import('../pages/TemplateTestPage'));
const TemplateSystemStatusPage = lazy(() => import('../pages/TemplateSystemStatusPage'));
const NewFeaturesTestPage = lazy(() => import('../pages/NewFeaturesTestPage'));
const ComponentTestPage = lazy(() => import('../pages/ComponentTestPage'));

// Forum pages (public access)
const ForumPage = lazy(() => import('../pages/ForumPage'));
const ForumCategoryPage = lazy(() => import('../pages/ForumCategoryPage'));
const ForumTopicPage = lazy(() => import('../pages/ForumTopicPage'));
const ForumThreadPage = lazy(() => import('../pages/ForumThreadPage'));
const CreateThreadPage = lazy(() => import('../pages/CreateThreadPage'));
const CreateTopicPage = lazy(() => import('../pages/CreateTopicPage'));
const UserReputationPage = lazy(() => import('../pages/UserReputationPage'));
const ForumLeaderboardPage = lazy(() => import('../pages/ForumLeaderboardPage'));
const ForumSearchPage = lazy(() => import('../pages/ForumSearchPage'));
const ForumBadgesPage = lazy(() => import('../pages/ForumBadgesPage'));
const UserProfilePage = lazy(() => import('../pages/UserProfilePage'));
const AppDiagnosticsPage = lazy(() => import('../pages/AppDiagnosticsPage'));

/**
 * Public routes that don't require authentication
 */
export const publicRoutes: RouteConfig[] = [
  // Core public pages
  createPublicRoute('/', HomePage),
  createPublicRoute('/features', FeaturesPage),
  // Demo page removed - demo functionality integrated into main pages

  // Auth pages (no layout wrapper)
  createAuthRoute('/login', LoginPage),
  createAuthRoute('/register', RegisterPage),
  createAuthRoute('/logout', LogoutPage),
  // ✅ REMOVED: Separate admin login - now using unified /login for all users

  // Public features
  createPublicRoute('/incubator', BusinessIncubator, 'Loading business incubator...'),
  // AI demo pages removed - demo functionality integrated into main AI pages
  createPublicRoute('/react-query-demo', ReactQueryDemo, 'Loading React Query demo...'),

  // Public search and templates
  createPublicRoute('/search', AdvancedSearchPage, 'Loading search...'),
  createPublicRoute('/templates', TemplateBrowserPage, 'Loading template browser...'),
  createPublicRoute('/templates/test', TemplateTestPage, 'Loading template test...'),
  createPublicRoute('/templates/status', TemplateSystemStatusPage, 'Loading system status...'),

  // Test pages (public for development)
  createPublicRoute('/test/features', NewFeaturesTestPage, 'Loading feature tests...'),
  createPublicRoute('/test/components', ComponentTestPage, 'Loading component tests...'),

  // Forum routes (public access)
  createPublicRoute('/forum', ForumPage, 'Loading forum...'),
  createPublicRoute('/forum/category/:slug', ForumCategoryPage, 'Loading category...'),
  createPublicRoute('/forum/topic/:slug', ForumTopicPage, 'Loading topic...'),
  createPublicRoute('/forum/thread/:id', ForumThreadPage, 'Loading thread...'),
  createPublicRoute('/forum/thread/create', CreateThreadPage, 'Loading thread creation...'),
  createPublicRoute('/forum/topic/create', CreateTopicPage, 'Loading topic creation...'),
  createPublicRoute('/forum/reputation', UserReputationPage, 'Loading reputation...'),
  createPublicRoute('/forum/leaderboard', ForumLeaderboardPage, 'Loading leaderboard...'),
  createPublicRoute('/forum/search', ForumSearchPage, 'Loading search...'),
  createPublicRoute('/forum/badges', ForumBadgesPage, 'Loading badges...'),
  createPublicRoute('/forum/badges/:id', ForumBadgesPage, 'Loading badge details...'),

  // Public user profiles
  createPublicRoute('/user/:id', UserProfilePage, 'Loading user profile...'),

  // Testing and diagnostics
  createPublicRoute('/diagnostics', AppDiagnosticsPage, 'Loading app diagnostics...'),
];

export default publicRoutes;
