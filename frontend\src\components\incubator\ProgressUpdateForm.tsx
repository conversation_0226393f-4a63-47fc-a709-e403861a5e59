import React, { useState, useEffect } from 'react';
import { Save, X, TrendingUp, Plus } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { ProgressUpdate, BusinessIdea } from '../../services/incubatorApi';

interface ProgressUpdateFormProps {
  progressUpdate?: ProgressUpdate;
  businessIdea: BusinessIdea;
  onSave: (data: Partial<ProgressUpdate>) => Promise<boolean>;
  onCancel: () => void;
  isLoading?: boolean;
  mode: 'create' | 'edit';
}

const ProgressUpdateForm: React.FC<ProgressUpdateFormProps> = ({
  progressUpdate,
  businessIdea,
  onSave,
  onCancel,
  isLoading = false,
  mode
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [formData, setFormData] = useState({
    title: progressUpdate?.title || '',
    description: progressUpdate?.description || '',
    achievements: progressUpdate?.achievements || '',
    challenges: progressUpdate?.challenges || '',
    next_steps: progressUpdate?.next_steps || '',
    business_idea: businessIdea.id
  });

  const [formError, setFormError] = useState<string | null>(null);
  const [hasChanges, setHasChanges] = useState(mode === 'create');

  useEffect(() => {
    if (mode === 'edit' && progressUpdate) {
      // Check if form has changes
      const hasFormChanges = Object.keys(formData).some(key => {
        if (key === 'business_idea') return false;
        const formValue = formData[key as keyof typeof formData];
        const originalValue = progressUpdate[key as keyof ProgressUpdate] || '';
        return formValue !== originalValue;
      });
      setHasChanges(hasFormChanges);
    }
  }, [formData, progressUpdate, mode]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    setFormError(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);

    // Validate required fields
    if (!formData.title.trim()) {
      setFormError(t('incubator.progressUpdate.titleRequired'));
      return;
    }

    if (!formData.description.trim()) {
      setFormError(t('incubator.progressUpdate.descriptionRequired'));
      return;
    }

    if (!formData.achievements.trim()) {
      setFormError(t('incubator.progressUpdate.achievementsRequired'));
      return;
    }

    if (!formData.next_steps.trim()) {
      setFormError(t('incubator.progressUpdate.nextStepsRequired'));
      return;
    }

    const success = await onSave(formData);
    if (!success) {
      setFormError(mode === 'create' 
        ? t('incubator.progressUpdate.createError')
        : t('incubator.progressUpdate.updateError')
      );
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 rounded-xl border border-blue-500/30 max-w-3xl w-full max-h-[90vh] overflow-hidden shadow-2xl">
        {/* Header */}
        <div className={`flex items-center justify-between p-6 border-b border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            {mode === 'create' ? (
              <Plus size={24} className={`text-blue-400 ${isRTL ? 'ml-3' : 'mr-3'}`} />
            ) : (
              <TrendingUp size={24} className={`text-blue-400 ${isRTL ? 'ml-3' : 'mr-3'}`} />
            )}
            <h2 className="text-xl font-semibold text-white">
              {mode === 'create' 
                ? t('incubator.progressUpdate.createTitle')
                : t('incubator.progressUpdate.editTitle')
              }
            </h2>
          </div>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-white transition-colors"
            disabled={isLoading}
          >
            <X size={20} />
          </button>
        </div>

        {/* Business Idea Info */}
        <div className="px-6 py-4 bg-gray-800/50 border-b border-gray-700">
          <p className={`text-sm text-gray-400 ${isRTL ? 'text-right' : ''}`}>
            {t('incubator.progressUpdate.forBusinessIdea')}: 
            <span className="text-white font-medium ml-1">{businessIdea.title}</span>
          </p>
        </div>

        {/* Form */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Error Message */}
            {formError && (
              <div className="bg-red-900/30 border border-red-500/50 rounded-lg p-4">
                <p className="text-red-400 text-sm">{formError}</p>
              </div>
            )}

            {/* Title */}
            <div>
              <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                {t('incubator.progressUpdate.title')} *
              </label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-gray-400 ${isRTL ? 'text-right' : ''}`}
                placeholder={t('incubator.progressUpdate.titlePlaceholder')}
                required
                disabled={isLoading}
              />
            </div>

            {/* Description */}
            <div>
              <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                {t('incubator.progressUpdate.description')} *
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={4}
                className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-gray-400 resize-none ${isRTL ? 'text-right' : ''}`}
                placeholder={t('incubator.progressUpdate.descriptionPlaceholder')}
                required
                disabled={isLoading}
              />
            </div>

            {/* Achievements */}
            <div>
              <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                {t('incubator.progressUpdate.achievements')} *
              </label>
              <textarea
                name="achievements"
                value={formData.achievements}
                onChange={handleInputChange}
                rows={4}
                className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-gray-400 resize-none ${isRTL ? 'text-right' : ''}`}
                placeholder={t('incubator.progressUpdate.achievementsPlaceholder')}
                required
                disabled={isLoading}
              />
            </div>

            {/* Challenges */}
            <div>
              <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                {t('incubator.progressUpdate.challenges')}
              </label>
              <textarea
                name="challenges"
                value={formData.challenges}
                onChange={handleInputChange}
                rows={3}
                className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-gray-400 resize-none ${isRTL ? 'text-right' : ''}`}
                placeholder={t('incubator.progressUpdate.challengesPlaceholder')}
                disabled={isLoading}
              />
            </div>

            {/* Next Steps */}
            <div>
              <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                {t('incubator.progressUpdate.nextSteps')} *
              </label>
              <textarea
                name="next_steps"
                value={formData.next_steps}
                onChange={handleInputChange}
                rows={4}
                className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-gray-400 resize-none ${isRTL ? 'text-right' : ''}`}
                placeholder={t('incubator.progressUpdate.nextStepsPlaceholder')}
                required
                disabled={isLoading}
              />
            </div>
          </form>
        </div>

        {/* Footer */}
        <div className={`flex justify-end gap-4 p-6 border-t border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <button
            type="button"
            onClick={onCancel}
            className="px-6 py-2 border border-gray-600 rounded-lg hover:bg-gray-800 transition-colors text-white disabled:opacity-50"
            disabled={isLoading}
          >
            {t('common.cancel')}
          </button>
          <button
            onClick={handleSubmit}
            disabled={isLoading || (mode === 'edit' && !hasChanges)}
            className="px-6 py-2 bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors flex items-center text-white disabled:opacity-50"
          >
            {isLoading ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                {mode === 'create' ? t('common.creating') : t('common.saving')}
              </>
            ) : (
              <>
                <Save size={16} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                {mode === 'create' ? t('common.create') : t('common.saveChanges')}
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProgressUpdateForm;
