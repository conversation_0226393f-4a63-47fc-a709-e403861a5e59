import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';
import { FundingOpportunity } from '../../../services/incubatorApi';
import { Loader, Save, X, AlertCircle, DollarSign, Calendar } from 'lucide-react';

interface FundingOpportunityFormProps {
  initialData?: Partial<FundingOpportunity>;
  onSubmit: (data: Partial<FundingOpportunity>) => Promise<boolean>;
  onCancel: () => void;
  isSubmitting?: boolean;
  mode: 'create' | 'edit';
}

interface FormData {
  title: string;
  description: string;
  funding_type: 'grant' | 'loan' | 'investment' | 'competition' | 'accelerator';
  amount_min: number;
  amount_max: number;
  deadline: string;
  eligibility_criteria: string;
  application_process: string;
  contact_information: string;
  website_url: string;
  is_active: boolean;
}

interface FormErrors {
  [key: string]: string;
}

const FundingOpportunityForm: React.FC<FundingOpportunityFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isSubmitting = false,
  mode
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [formData, setFormData] = useState<FormData>({
    title: initialData?.title || '',
    description: initialData?.description || '',
    funding_type: initialData?.funding_type || 'grant',
    amount_min: initialData?.amount_min || 0,
    amount_max: initialData?.amount_max || 0,
    deadline: initialData?.deadline ? new Date(initialData.deadline).toISOString().split('T')[0] : '',
    eligibility_criteria: initialData?.eligibility_criteria || '',
    application_process: initialData?.application_process || '',
    contact_information: initialData?.contact_information || '',
    website_url: initialData?.website_url || '',
    is_active: initialData?.is_active ?? true
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  // Validation rules
  const validateField = (name: string, value: string | number | boolean): string => {
    switch (name) {
      case 'title':
        if (!value || (typeof value === 'string' && !value.trim())) {
          return t('validation.required', 'This field is required');
        }
        if (typeof value === 'string' && value.length < 5) {
          return t('validation.minLength', 'Must be at least 5 characters');
        }
        break;
      case 'description':
        if (!value || (typeof value === 'string' && !value.trim())) {
          return t('validation.required', 'This field is required');
        }
        if (typeof value === 'string' && value.length < 20) {
          return t('validation.minLength', 'Must be at least 20 characters');
        }
        break;
      case 'amount_min':
        if (typeof value === 'number' && value < 0) {
          return t('validation.positiveNumber', 'Must be a positive number');
        }
        break;
      case 'amount_max':
        if (typeof value === 'number' && value < 0) {
          return t('validation.positiveNumber', 'Must be a positive number');
        }
        if (typeof value === 'number' && formData.amount_min > 0 && value < formData.amount_min) {
          return t('validation.maxGreaterThanMin', 'Maximum amount must be greater than minimum');
        }
        break;
      case 'deadline':
        if (!value || (typeof value === 'string' && !value.trim())) {
          return t('validation.required', 'This field is required');
        }
        if (typeof value === 'string' && new Date(value) < new Date()) {
          return t('validation.futureDate', 'Deadline must be in the future');
        }
        break;
      case 'eligibility_criteria':
        if (!value || (typeof value === 'string' && !value.trim())) {
          return t('validation.required', 'This field is required');
        }
        break;
      case 'application_process':
        if (!value || (typeof value === 'string' && !value.trim())) {
          return t('validation.required', 'This field is required');
        }
        break;
      case 'website_url':
        if (value && typeof value === 'string' && value.trim()) {
          const urlPattern = /^https?:\/\/.+/;
          if (!urlPattern.test(value)) {
            return t('validation.invalidUrl', 'Must be a valid URL starting with http:// or https://');
          }
        }
        break;
    }
    return '';
  };

  // Validate all fields
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};
    
    Object.keys(formData).forEach(key => {
      if (key !== 'is_active' && key !== 'contact_information' && key !== 'website_url') {
        const error = validateField(key, formData[key as keyof FormData]);
        if (error) {
          newErrors[key] = error;
        }
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    let processedValue: string | number | boolean = value;
    
    if (type === 'number') {
      processedValue = parseFloat(value) || 0;
    } else if (type === 'checkbox') {
      processedValue = (e.target as HTMLInputElement).checked;
    }
    
    setFormData(prev => ({
      ...prev,
      [name]: processedValue
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // Handle field blur
  const handleBlur = (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    setTouched(prev => ({
      ...prev,
      [name]: true
    }));

    // Validate field on blur
    let processedValue: string | number | boolean = value;
    if (type === 'number') {
      processedValue = parseFloat(value) || 0;
    } else if (type === 'checkbox') {
      processedValue = (e.target as HTMLInputElement).checked;
    }
    
    const error = validateField(name, processedValue);
    setErrors(prev => ({
      ...prev,
      [name]: error
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Mark all fields as touched
    const allTouched = Object.keys(formData).reduce((acc, key) => {
      acc[key] = true;
      return acc;
    }, {} as Record<string, boolean>);
    setTouched(allTouched);

    // Validate form
    if (!validateForm()) {
      return;
    }

    // Submit form
    const success = await onSubmit(formData);
    if (success) {
      // Form will be closed by parent component
    }
  };

  const fundingTypeOptions = [
    { value: 'grant', label: t('funding.types.grant', 'Grant') },
    { value: 'loan', label: t('funding.types.loan', 'Loan') },
    { value: 'investment', label: t('funding.types.investment', 'Investment') },
    { value: 'competition', label: t('funding.types.competition', 'Competition') },
    { value: 'accelerator', label: t('funding.types.accelerator', 'Accelerator Program') }
  ];

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <div className="bg-gray-900 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className={`flex items-center justify-between p-6 border-b border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <h2 className="text-xl font-semibold text-white">
            {mode === 'create' 
              ? t('funding.createOpportunity', 'Create Funding Opportunity')
              : t('funding.editOpportunity', 'Edit Funding Opportunity')
            }
          </h2>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-white transition-colors"
            disabled={isSubmitting}
          >
            <X size={24} />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('funding.title', 'Title')} *
            </label>
            <input
              type="text"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              onBlur={handleBlur}
              className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white ${
                errors.title && touched.title ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder={t('funding.titlePlaceholder', 'Enter funding opportunity title')}
              disabled={isSubmitting}
            />
            {errors.title && touched.title && (
              <p className="mt-1 text-sm text-red-400 flex items-center">
                <AlertCircle size={16} className="mr-1" />
                {errors.title}
              </p>
            )}
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('funding.description', 'Description')} *
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              onBlur={handleBlur}
              rows={4}
              className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white resize-vertical ${
                errors.description && touched.description ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder={t('funding.descriptionPlaceholder', 'Describe the funding opportunity...')}
              disabled={isSubmitting}
            />
            {errors.description && touched.description && (
              <p className="mt-1 text-sm text-red-400 flex items-center">
                <AlertCircle size={16} className="mr-1" />
                {errors.description}
              </p>
            )}
          </div>

          {/* Funding Type and Deadline */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('funding.type', 'Funding Type')} *
              </label>
              <select
                name="funding_type"
                value={formData.funding_type}
                onChange={handleInputChange}
                onBlur={handleBlur}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                disabled={isSubmitting}
              >
                {fundingTypeOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('funding.deadline', 'Application Deadline')} *
              </label>
              <input
                type="date"
                name="deadline"
                value={formData.deadline}
                onChange={handleInputChange}
                onBlur={handleBlur}
                className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white ${
                  errors.deadline && touched.deadline ? 'border-red-500' : 'border-gray-600'
                }`}
                disabled={isSubmitting}
              />
              {errors.deadline && touched.deadline && (
                <p className="mt-1 text-sm text-red-400 flex items-center">
                  <AlertCircle size={16} className="mr-1" />
                  {errors.deadline}
                </p>
              )}
            </div>
          </div>

          {/* Amount Range */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('funding.amountMin', 'Minimum Amount')} ($)
              </label>
              <input
                type="number"
                name="amount_min"
                value={formData.amount_min}
                onChange={handleInputChange}
                onBlur={handleBlur}
                min="0"
                step="1000"
                className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white ${
                  errors.amount_min && touched.amount_min ? 'border-red-500' : 'border-gray-600'
                }`}
                placeholder="0"
                disabled={isSubmitting}
              />
              {errors.amount_min && touched.amount_min && (
                <p className="mt-1 text-sm text-red-400 flex items-center">
                  <AlertCircle size={16} className="mr-1" />
                  {errors.amount_min}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('funding.amountMax', 'Maximum Amount')} ($)
              </label>
              <input
                type="number"
                name="amount_max"
                value={formData.amount_max}
                onChange={handleInputChange}
                onBlur={handleBlur}
                min="0"
                step="1000"
                className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white ${
                  errors.amount_max && touched.amount_max ? 'border-red-500' : 'border-gray-600'
                }`}
                placeholder="0"
                disabled={isSubmitting}
              />
              {errors.amount_max && touched.amount_max && (
                <p className="mt-1 text-sm text-red-400 flex items-center">
                  <AlertCircle size={16} className="mr-1" />
                  {errors.amount_max}
                </p>
              )}
            </div>
          </div>

          {/* Eligibility Criteria */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('funding.eligibilityCriteria', 'Eligibility Criteria')} *
            </label>
            <textarea
              name="eligibility_criteria"
              value={formData.eligibility_criteria}
              onChange={handleInputChange}
              onBlur={handleBlur}
              rows={3}
              className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white resize-vertical ${
                errors.eligibility_criteria && touched.eligibility_criteria ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder={t('funding.eligibilityPlaceholder', 'Who is eligible for this funding?')}
              disabled={isSubmitting}
            />
            {errors.eligibility_criteria && touched.eligibility_criteria && (
              <p className="mt-1 text-sm text-red-400 flex items-center">
                <AlertCircle size={16} className="mr-1" />
                {errors.eligibility_criteria}
              </p>
            )}
          </div>

          {/* Application Process */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('funding.applicationProcess', 'Application Process')} *
            </label>
            <textarea
              name="application_process"
              value={formData.application_process}
              onChange={handleInputChange}
              onBlur={handleBlur}
              rows={3}
              className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white resize-vertical ${
                errors.application_process && touched.application_process ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder={t('funding.processPlaceholder', 'How to apply for this funding?')}
              disabled={isSubmitting}
            />
            {errors.application_process && touched.application_process && (
              <p className="mt-1 text-sm text-red-400 flex items-center">
                <AlertCircle size={16} className="mr-1" />
                {errors.application_process}
              </p>
            )}
          </div>

          {/* Contact Information and Website */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('funding.contactInformation', 'Contact Information')}
              </label>
              <textarea
                name="contact_information"
                value={formData.contact_information}
                onChange={handleInputChange}
                onBlur={handleBlur}
                rows={2}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white resize-vertical"
                placeholder={t('funding.contactPlaceholder', 'Contact details for inquiries')}
                disabled={isSubmitting}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('funding.websiteUrl', 'Website URL')}
              </label>
              <input
                type="url"
                name="website_url"
                value={formData.website_url}
                onChange={handleInputChange}
                onBlur={handleBlur}
                className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white ${
                  errors.website_url && touched.website_url ? 'border-red-500' : 'border-gray-600'
                }`}
                placeholder="https://example.com"
                disabled={isSubmitting}
              />
              {errors.website_url && touched.website_url && (
                <p className="mt-1 text-sm text-red-400 flex items-center">
                  <AlertCircle size={16} className="mr-1" />
                  {errors.website_url}
                </p>
              )}
            </div>
          </div>

          {/* Active Status */}
          <div>
            <label className="flex items-center gap-3">
              <input
                type="checkbox"
                name="is_active"
                checked={formData.is_active}
                onChange={handleInputChange}
                className="w-4 h-4 text-purple-600 bg-gray-800 border-gray-600 rounded focus:ring-purple-500 focus:ring-2"
                disabled={isSubmitting}
              />
              <span className="text-sm font-medium text-gray-300">
                {t('funding.isActive', 'Currently accepting applications')}
              </span>
            </label>
            <p className="mt-1 text-xs text-gray-400">
              {t('funding.activeHelp', 'Uncheck to hide this opportunity from public listings')}
            </p>
          </div>

          {/* Form Actions */}
          <div className={`flex gap-4 pt-4 border-t border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <button
              type="button"
              onClick={onCancel}
              className="px-6 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors"
              disabled={isSubmitting}
            >
              {t('common.cancel', 'Cancel')}
            </button>
            <button
              type="submit"
              className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader size={16} className="animate-spin" />
                  {t('common.saving', 'Saving...')}
                </>
              ) : (
                <>
                  <Save size={16} />
                  {mode === 'create'
                    ? t('common.create', 'Create')
                    : t('common.update', 'Update')
                  }
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default FundingOpportunityForm;
