/**
 * Consolidated AI Chat Component
 * Advanced chat interface using the new consolidated AI service
 */

import React, { useState, useRef, useEffect } from 'react';
import { Send, Bot, User, Sparkles, Globe, Brain, Zap } from 'lucide-react';
import { useCentralizedAI } from '../../hooks/useCentralizedAI';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { RTLText } from '../common';

interface ConsolidatedAIChatProps {
  businessIdeaId?: number;
  businessContext?: any;
  userId?: number;
  userName?: string;
  userRegion?: string;
  language?: string;
  className?: string;
}

interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  language?: string;
  workflowType?: string;
  enhanced?: boolean;
  hasCulturalContext?: boolean;
  hasMLInsights?: boolean;
}

export const ConsolidatedAIChat: React.FC<ConsolidatedAIChatProps> = ({
  businessIdeaId,
  businessContext,
  userId,
  userName,
  userRegion,
  language = 'auto',
  className = '',
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [currentLanguage, setCurrentLanguage] = useState(language);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Use centralized AI hook
  const {
    chat,
    chatAuto,
    chatArabic,
    isChatting,
    chatError,
    isAvailable,
    status,
  } = useCentralizedAI();

  // Mock availability for now
  const availability = {
    consolidatedAI: isAvailable,
    workflows: true,
    mlService: true,
    arabicProcessing: true,
  };

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Add welcome message
  useEffect(() => {
    if (messages.length === 0) {
      const welcomeMessage: ChatMessage = {
        id: 'welcome',
        role: 'assistant',
        content: t('ai.chat.welcomeMessage', 'Hello {{userName}}! I\'m Yasmeen, your AI assistant. How can I help you with your business ideas today?', { userName: userName || t('common.there', 'there') }),
        timestamp: new Date(),
        language: currentLanguage,
        enhanced: true,
      };
      setMessages([welcomeMessage]);
    }
  }, [currentLanguage, userName, messages.length, t]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isChatting) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: inputMessage,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');

    try {
      // Auto-detect language if set to auto
      const messageLanguage = currentLanguage === 'auto'
        ? 'en' // Default to English for now
        : currentLanguage;

      // Prepare chat history
      const chatHistory = messages.map(msg => ({
        role: msg.role,
        content: msg.content,
        timestamp: msg.timestamp.toISOString(),
      }));

      // Send message using appropriate service
      const response = currentLanguage === 'ar'
        ? await chatArabic(inputMessage)
        : messageLanguage === 'auto'
        ? await chatAuto(inputMessage)
        : await chat(inputMessage, messageLanguage);

      if (response) {

        const assistantMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: response.content || response.message || 'No response received',
          timestamp: new Date(),
          language: response.language || currentLanguage,
          workflowType: 'chat',
          enhanced: true,
          hasCulturalContext: currentLanguage === 'ar',
          hasMLInsights: true,
        };

        setMessages(prev => [...prev, assistantMessage]);
      }
    } catch (err) {
      console.error('Error sending message:', err);
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: t('ai.chat.errorMessage', 'Sorry, there was an error processing your message. Please try again.'),
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const getMessageIcon = (message: ChatMessage) => {
    if (message.role === 'user') {
      return <User className="w-5 h-5" />;
    }

    if (message.enhanced) {
      return <Sparkles className="w-5 h-5 text-purple-500" />;
    }

    return <Bot className="w-5 h-5" />;
  };

  const getMessageBadges = (message: ChatMessage) => {
    const badges = [];

    if (message.enhanced) {
      badges.push(
        <span key="enhanced" className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-500/20 text-purple-300 border border-purple-500/30">
          <Sparkles className="w-3 h-3 mr-1" />
          {t('admin.chat.enhanced', 'Enhanced')}
        </span>
      );
    }

    if (message.hasCulturalContext) {
      badges.push(
        <span key="cultural" className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-500/20 text-green-300 border border-green-500/30">
          <Globe className="w-3 h-3 mr-1" />
          {t('admin.chat.cultural', 'Cultural')}
        </span>
      );
    }

    if (message.hasMLInsights) {
      badges.push(
        <span key="ml" className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-500/20 text-blue-300 border border-blue-500/30">
          <Brain className="w-3 h-3 mr-1" />
          {t('admin.chat.mlInsights', 'ML Insights')}
        </span>
      );
    }

    if (message.workflowType && message.workflowType !== 'chat') {
      badges.push(
        <span key="workflow" className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-orange-500/20 text-orange-300 border border-orange-500/30">
          <Zap className="w-3 h-3 mr-1" />
          {message.workflowType}
        </span>
      );
    }

    return badges;
  };

  return (
    <div className={`flex flex-col h-full bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl shadow-xl ${className}`}>
      {/* Enhanced Header */}
      <div className="flex items-center justify-between p-6 border-b border-white/10">
        <div className={`flex items-center ${isRTL ? 'space-x-reverse space-x-4' : 'space-x-4'}`}>
          <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl shadow-lg">
            <Sparkles className="w-6 h-6 text-white" />
          </div>
          <div>
            <RTLText as="h3" className="font-bold text-white text-lg">
              {t('ai.chat.yasmeenTitle', 'Yasmeen AI Assistant')}
            </RTLText>
            <div className={`flex items-center ${isRTL ? 'space-x-reverse space-x-2' : 'space-x-2'} text-sm`}>
              <div className={`w-2 h-2 rounded-full animate-pulse ${availability.consolidatedAI ? 'bg-green-400' : 'bg-red-400'}`} />
              <RTLText as="span" className={`${availability.consolidatedAI ? 'text-green-400' : 'text-red-400'}`}>
                {availability.consolidatedAI
                  ? t('ai.chat.connected', 'Connected')
                  : t('ai.chat.disconnected', 'Disconnected')
                }
              </RTLText>
            </div>
          </div>
        </div>

        {/* Enhanced Language Toggle */}
        <div className="flex items-center space-x-3">
          <button
            onClick={() => setCurrentLanguage(currentLanguage === 'ar' ? 'en' : 'ar')}
            className="px-4 py-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-xl text-white text-sm font-medium transition-all duration-300 hover:scale-105"
          >
            {currentLanguage === 'ar' ? 'EN' : 'عربي'}
          </button>
        </div>
      </div>

      {/* Enhanced Messages */}
      <div className="flex-1 overflow-y-auto p-6 space-y-6" dir={isRTL ? 'rtl' : 'ltr'}>
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div className={`flex max-w-4xl ${message.role === 'user' ? (isRTL ? 'flex-row' : 'flex-row-reverse') : (isRTL ? 'flex-row-reverse' : 'flex-row')} ${isRTL ? 'space-x-reverse space-x-4' : 'space-x-4'}`}>
              <div className="flex-shrink-0">
                <div className={`flex items-center justify-center w-10 h-10 rounded-xl shadow-lg ${
                  message.role === 'user'
                    ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white'
                    : 'bg-gradient-to-r from-purple-500 to-pink-500 text-white'
                }`}>
                  {getMessageIcon(message)}
                </div>
              </div>
              <div className="flex-1">
                <div className={`p-4 rounded-2xl shadow-lg backdrop-blur-sm border transition-all duration-300 hover:shadow-xl ${
                  message.role === 'user'
                    ? 'bg-blue-500/20 border-blue-500/30 text-white'
                    : 'bg-white/10 border-white/20 text-white'
                }`}>
                  <RTLText as="p" className="whitespace-pre-wrap leading-relaxed">
                    {message.content}
                  </RTLText>
                </div>

                {/* Enhanced Message badges */}
                {message.role === 'assistant' && (
                  <div className={`flex flex-wrap gap-2 mt-3 ${isRTL ? 'justify-end' : 'justify-start'}`}>
                    {getMessageBadges(message)}
                  </div>
                )}

                <RTLText as="p" className="text-xs text-gray-400 mt-2">
                  {message.timestamp.toLocaleTimeString()}
                </RTLText>
              </div>
            </div>
          </div>
        ))}

        {isChatting && (
          <div className={`flex ${isRTL ? 'justify-end' : 'justify-start'}`}>
            <div className={`flex items-center ${isRTL ? 'space-x-reverse space-x-4' : 'space-x-4'}`}>
              <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl shadow-lg">
                <Bot className="w-5 h-5 text-white animate-pulse" />
              </div>
              <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-4 shadow-lg">
                <div className={`flex ${isRTL ? 'space-x-reverse space-x-2' : 'space-x-2'}`}>
                  <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce" />
                  <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                  <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                </div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Enhanced Input */}
      <div className="p-6 border-t border-white/10">
        {chatError && (
          <div className="mb-4 p-4 bg-red-500/20 border border-red-500/30 rounded-xl text-red-300 text-sm backdrop-blur-sm">
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <Bot size={16} className="mr-2 text-red-400" />
              <RTLText as="span">{chatError}</RTLText>
            </div>
          </div>
        )}

        <div className={`flex ${isRTL ? 'space-x-reverse space-x-4' : 'space-x-4'}`}>
          <textarea
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyDown={handleKeyPress}
            placeholder={t('ai.chat.messagePlaceholder', 'Type your message here...')}
            className="flex-1 p-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl resize-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300"
            rows={3}
            dir={isRTL ? 'rtl' : 'ltr'}
            disabled={isChatting}
          />
          <button
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || isChatting}
            className="px-6 py-4 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-2xl hover:from-purple-600 hover:to-pink-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl"
          >
            <Send className="w-5 h-5" />
          </button>
        </div>

        {/* Status indicators */}
        <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
          <div className="flex items-center space-x-4">
            {availability.workflows && (
              <span className="flex items-center">
                <Zap className="w-3 h-3 mr-1 text-orange-500" />
                {currentLanguage === 'ar' ? 'سير العمل المتقدم' : 'Advanced Workflows'}
              </span>
            )}
            {availability.mlService && (
              <span className="flex items-center">
                <Brain className="w-3 h-3 mr-1 text-blue-500" />
                {currentLanguage === 'ar' ? 'تعلم الآلة' : 'ML Insights'}
              </span>
            )}
            {availability.arabicProcessing && (
              <span className="flex items-center">
                <Globe className="w-3 h-3 mr-1 text-green-500" />
                {currentLanguage === 'ar' ? 'السياق الثقافي' : 'Cultural Context'}
              </span>
            )}
          </div>

          {inputMessage.length > 100 && (
            <span className="text-orange-500">
              {currentLanguage === 'ar' ? 'استعلام معقد - سيتم استخدام سير العمل المتقدم' : 'Complex query - Advanced workflow will be used'}
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

export default ConsolidatedAIChat;
