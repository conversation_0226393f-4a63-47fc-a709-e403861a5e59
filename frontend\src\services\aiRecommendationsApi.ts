import { apiRequest, PaginatedResponse } from './api';

// Types for AI Recommendations
export interface AIRecommendation {
  id: number;
  business_idea: number;
  business_idea_title: string;
  recommendation_type: 'milestone' | 'goal' | 'mentor' | 'resource' | 'strategy' | 'general';
  recommendation_type_display: string;
  title: string;
  description: string;
  reasoning: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  priority_display: string;
  action_items: string;
  expected_outcome: string;
  relevance_score: number;
  is_implemented: boolean;
  implementation_notes: string | null;
  implemented_by: number | null;
  implemented_by_details: {
    id: number;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
  } | null;
  implemented_at: string | null;
  feedback_count: number;
  helpful_count: number;
  created_at: string;
  updated_at: string;
}

export interface AIRecommendationFeedback {
  id: number;
  recommendation: number;
  user: number;
  user_details: {
    id: number;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
  };
  feedback_type: 'helpful' | 'not_helpful' | 'implemented' | 'not_applicable';
  feedback_type_display: string;
  comments: string | null;
  created_at: string;
}

// AI Recommendations API
export const aiRecommendationsAPI = {
  getRecommendations: async (businessIdeaId?: number) => {
    try {
      const endpoint = businessIdeaId
        ? `/ai/recommendations/?business_idea=${businessIdeaId}`
        : '/ai/recommendations/';

      const response = await apiRequest<PaginatedResponse<AIRecommendation>>(endpoint);

      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results;
      } else if (Array.isArray(response)) {
        return response;
      }

      console.error('API returned unexpected format for AI recommendations:', response);
      return [];
    } catch (error) {
      console.error('Error fetching AI recommendations:', error);
      return [];
    }
  },

  getRecommendation: (id: number) =>
    apiRequest<AIRecommendation>(`/ai/recommendations/${id}/`),

  generateRecommendations: (businessIdeaId: number) =>
    apiRequest<{ message: string, recommendations: AIRecommendation[] }>(
      '/ai/recommendations/generate_recommendations/',
      'POST',
      { business_idea_id: businessIdeaId }
    ),

  markImplemented: (id: number, implementationNotes: string = '') =>
    apiRequest<{ message: string, recommendation: AIRecommendation }>(
      `/ai/recommendations/${id}/mark_implemented/`,
      'POST',
      { implementation_notes: implementationNotes }
    ),

  // Feedback API
  getFeedback: async (recommendationId?: number) => {
    try {
      const endpoint = recommendationId
        ? `/ai/feedback/?recommendation=${recommendationId}`
        : '/ai/feedback/';

      const response = await apiRequest<PaginatedResponse<AIRecommendationFeedback>>(endpoint);

      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results;
      } else if (Array.isArray(response)) {
        return response;
      }

      console.error('API returned unexpected format for AI recommendation feedback:', response);
      return [];
    } catch (error) {
      console.error('Error fetching AI recommendation feedback:', error);
      return [];
    }
  },

  createFeedback: (feedbackData: Partial<AIRecommendationFeedback> & { recommendation: number }) =>
    apiRequest<AIRecommendationFeedback>('/ai/feedback/', 'POST', feedbackData),
};
