/**
 * AI Permissions API Service
 * Handles role-based AI access control and permissions
 */

import { apiClient } from './api';

export interface AICapability {
  id: string;
  name: string;
  description: string;
  features: string[];
  rate_limit: {
    requests: number;
    period: string;
  };
}

export interface AIRateLimits {
  chat: number;
  analysis: number;
  generation: number;
}

export interface UserAIAccess {
  user_id: number;
  username: string;
  roles: string[];
  capabilities: AICapability[];
  rate_limits: AIRateLimits;
  primary_role: string;
  has_advanced_features: boolean;
}

export interface AICapabilityCheck {
  capability_id: string;
  has_access: boolean;
  user_roles: string[];
}

/**
 * Get current user's AI access information
 */
export const getUserAIAccess = async (): Promise<UserAIAccess> => {
  try {
    const response = await apiClient.get('/api/ai/access/');
    
    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.error || 'Failed to get AI access information');
    }
  } catch (error: any) {
    console.error('Error getting user AI access:', error);
    throw new Error(error.response?.data?.error || 'Failed to get AI access information');
  }
};

/**
 * Check if user has access to a specific AI capability
 */
export const checkAICapabilityAccess = async (capabilityId: string): Promise<AICapabilityCheck> => {
  try {
    const response = await apiClient.post('/api/ai/check-capability/', {
      capability_id: capabilityId
    });
    
    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.error || 'Failed to check AI capability access');
    }
  } catch (error: any) {
    console.error('Error checking AI capability access:', error);
    throw new Error(error.response?.data?.error || 'Failed to check AI capability access');
  }
};

/**
 * Get all available AI capabilities (admin only)
 */
export const getAllAICapabilities = async (): Promise<Record<string, AICapability[]>> => {
  try {
    const response = await apiClient.get('/api/ai/capabilities/all/');
    
    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.error || 'Failed to get all AI capabilities');
    }
  } catch (error: any) {
    console.error('Error getting all AI capabilities:', error);
    throw new Error(error.response?.data?.error || 'Failed to get all AI capabilities');
  }
};

/**
 * Hook for managing AI permissions
 */
export const useAIPermissions = () => {
  const [aiAccess, setAIAccess] = React.useState<UserAIAccess | null>(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  const loadAIAccess = async () => {
    try {
      setLoading(true);
      setError(null);
      const access = await getUserAIAccess();
      setAIAccess(access);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const checkCapability = async (capabilityId: string): Promise<boolean> => {
    try {
      const result = await checkAICapabilityAccess(capabilityId);
      return result.has_access;
    } catch (err) {
      console.error('Error checking capability:', err);
      return false;
    }
  };

  const hasRole = (roleName: string): boolean => {
    return aiAccess?.roles.includes(roleName) || false;
  };

  const hasCapability = (capabilityId: string): boolean => {
    return aiAccess?.capabilities.some(cap => cap.id === capabilityId) || false;
  };

  const getRateLimit = (type: keyof AIRateLimits): number => {
    return aiAccess?.rate_limits[type] || 0;
  };

  React.useEffect(() => {
    loadAIAccess();
  }, []);

  return {
    aiAccess,
    loading,
    error,
    loadAIAccess,
    checkCapability,
    hasRole,
    hasCapability,
    getRateLimit
  };
};

/**
 * Rate limiting utilities
 */
export class AIRateLimiter {
  private static instance: AIRateLimiter;
  private requests: Map<string, { count: number; resetTime: number }> = new Map();

  static getInstance(): AIRateLimiter {
    if (!AIRateLimiter.instance) {
      AIRateLimiter.instance = new AIRateLimiter();
    }
    return AIRateLimiter.instance;
  }

  /**
   * Check if request is within rate limit
   */
  canMakeRequest(userId: number, capabilityId: string, limit: number, periodMs: number): boolean {
    const key = `${userId}-${capabilityId}`;
    const now = Date.now();
    const requestData = this.requests.get(key);

    if (!requestData || now > requestData.resetTime) {
      // Reset or initialize
      this.requests.set(key, { count: 1, resetTime: now + periodMs });
      return true;
    }

    if (requestData.count >= limit) {
      return false; // Rate limit exceeded
    }

    // Increment count
    requestData.count++;
    this.requests.set(key, requestData);
    return true;
  }

  /**
   * Get remaining requests for a capability
   */
  getRemainingRequests(userId: number, capabilityId: string, limit: number): number {
    const key = `${userId}-${capabilityId}`;
    const requestData = this.requests.get(key);
    
    if (!requestData || Date.now() > requestData.resetTime) {
      return limit;
    }

    return Math.max(0, limit - requestData.count);
  }

  /**
   * Get time until rate limit reset
   */
  getResetTime(userId: number, capabilityId: string): number {
    const key = `${userId}-${capabilityId}`;
    const requestData = this.requests.get(key);
    
    if (!requestData || Date.now() > requestData.resetTime) {
      return 0;
    }

    return requestData.resetTime - Date.now();
  }
}

/**
 * AI Permission utilities
 */
export const aiPermissionUtils = {
  /**
   * Get user-friendly role name
   */
  getRoleDisplayName: (role: string): string => {
    const roleNames: Record<string, string> = {
      'admin': 'Administrator',
      'moderator': 'Moderator',
      'mentor': 'Mentor',
      'investor': 'Investor',
      'user': 'User'
    };
    return roleNames[role] || role;
  },

  /**
   * Get role color for UI
   */
  getRoleColor: (role: string): string => {
    const roleColors: Record<string, string> = {
      'admin': 'text-yellow-400',
      'moderator': 'text-blue-400',
      'mentor': 'text-green-400',
      'investor': 'text-purple-400',
      'user': 'text-gray-400'
    };
    return roleColors[role] || 'text-gray-400';
  },

  /**
   * Get capability icon
   */
  getCapabilityIcon: (capabilityId: string): string => {
    const iconMap: Record<string, string> = {
      'basic_chat': 'MessageSquare',
      'basic_analysis': 'BarChart3',
      'advanced_chat': 'Brain',
      'mentor_analysis': 'TrendingUp',
      'mentee_support': 'Users',
      'investment_analysis': 'DollarSign',
      'market_intelligence': 'Search',
      'content_moderation': 'Shield',
      'community_insights': 'Users',
      'admin_analytics': 'BarChart',
      'ai_management': 'Settings'
    };
    return iconMap[capabilityId] || 'MessageSquare';
  },

  /**
   * Format rate limit period
   */
  formatRateLimitPeriod: (period: string): string => {
    const periodMap: Record<string, string> = {
      'hour': 'per hour',
      'day': 'per day',
      'month': 'per month'
    };
    return periodMap[period] || period;
  }
};

// Import React for the hook
import React from 'react';
