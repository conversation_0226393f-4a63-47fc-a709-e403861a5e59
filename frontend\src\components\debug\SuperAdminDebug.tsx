import React from 'react';
import { useAppSelector } from '../../store/hooks';
import { isSuperAdmin, getUserDashboardType, getHighestPermissionLevel } from '../../utils/roleBasedRouting';
import { Shield, User, Info, CheckCircle, XCircle } from 'lucide-react';

const SuperAdminDebug: React.FC = () => {
  const { user, isAuthenticated } = useAppSelector(state => state.auth);

  if (!isAuthenticated || !user) {
    return (
      <div className="bg-red-900/20 border border-red-500 rounded-lg p-4 m-4">
        <div className="flex items-center gap-2">
          <XCircle className="w-5 h-5 text-red-400" />
          <span className="text-red-300">Not authenticated</span>
        </div>
      </div>
    );
  }

  const isSuperAdminUser = isSuperAdmin(user);
  const dashboardType = getUserDashboardType(user);
  const permissionLevel = getHighestPermissionLevel(user);

  return (
    <div className="bg-gray-800 border border-gray-600 rounded-lg p-4 m-4 text-white">
      <div className="flex items-center gap-2 mb-4">
        <Shield className="w-5 h-5 text-blue-400" />
        <h3 className="text-lg font-semibold">Super Admin Debug Info</h3>
      </div>

      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-gray-300">Username:</span>
          <span className="text-white font-medium">{user.username}</span>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-gray-300">Email:</span>
          <span className="text-white font-medium">{user.email}</span>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-gray-300">Is Superuser:</span>
          <div className="flex items-center gap-2">
            {user.is_superuser ? (
              <CheckCircle className="w-4 h-4 text-green-400" />
            ) : (
              <XCircle className="w-4 h-4 text-red-400" />
            )}
            <span className={user.is_superuser ? 'text-green-400' : 'text-red-400'}>
              {user.is_superuser ? 'Yes' : 'No'}
            </span>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-gray-300">Is Admin:</span>
          <div className="flex items-center gap-2">
            {user.is_admin ? (
              <CheckCircle className="w-4 h-4 text-green-400" />
            ) : (
              <XCircle className="w-4 h-4 text-red-400" />
            )}
            <span className={user.is_admin ? 'text-green-400' : 'text-red-400'}>
              {user.is_admin ? 'Yes' : 'No'}
            </span>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-gray-300">Is Super Admin:</span>
          <div className="flex items-center gap-2">
            {isSuperAdminUser ? (
              <CheckCircle className="w-4 h-4 text-green-400" />
            ) : (
              <XCircle className="w-4 h-4 text-red-400" />
            )}
            <span className={isSuperAdminUser ? 'text-green-400' : 'text-red-400'}>
              {isSuperAdminUser ? 'Yes' : 'No'}
            </span>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-gray-300">Dashboard Type:</span>
          <span className="text-blue-400 font-medium">{dashboardType}</span>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-gray-300">Permission Level:</span>
          <span className="text-purple-400 font-medium">{permissionLevel}</span>
        </div>

        {user.profile && (
          <>
            <div className="border-t border-gray-600 pt-3 mt-3">
              <h4 className="text-sm font-semibold text-gray-300 mb-2">Profile Info:</h4>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-400 text-sm">Primary Role:</span>
                <span className="text-white text-sm">
                  {user.profile.primary_role?.name || 'None'}
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-gray-400 text-sm">Active Roles:</span>
                <span className="text-white text-sm">
                  {user.profile.active_roles?.length || 0} roles
                </span>
              </div>

              {user.profile.active_roles && user.profile.active_roles.length > 0 && (
                <div className="mt-2">
                  <span className="text-gray-400 text-sm">Roles:</span>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {user.profile.active_roles.map((role, index) => (
                      <span
                        key={index}
                        className="bg-blue-600/20 text-blue-300 px-2 py-1 rounded text-xs"
                      >
                        {role.name}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              <div className="flex items-center justify-between">
                <span className="text-gray-400 text-sm">Highest Permission:</span>
                <span className="text-white text-sm">
                  {user.profile.highest_permission_level || 'read'}
                </span>
              </div>
            </div>
          </>
        )}

        <div className="border-t border-gray-600 pt-3 mt-3">
          <div className="flex items-center gap-2">
            <Info className="w-4 h-4 text-blue-400" />
            <span className="text-sm text-gray-400">
              This debug panel shows your current authentication and role status.
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SuperAdminDebug;
