import React, { useState, useEffect } from 'react';
import {
  Search,
  Filter,
  Star,
  Clock,
  Users,
  FileText,
  Download,
  Upload,
  Grid,
  List,
  SortAsc,
  SortDesc,
  Tag,
  Eye,
  Copy,
  Heart,
  TrendingUp,
  Award
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { BusinessPlanTemplate, businessPlanTemplatesAPI } from '../../services/businessPlanApi';
import { RTLText, RTLFlex, RTLIcon } from '../common';
import { TemplatePreview } from './TemplatePreview';

interface EnhancedTemplateBrowserProps {
  onSelectTemplate?: (template: BusinessPlanTemplate) => void;
  onCreateNew?: () => void;
  showCreateButton?: boolean;
}

export const EnhancedTemplateBrowser: React.FC<EnhancedTemplateBrowserProps> = ({
  onSelectTemplate,
  onCreateNew,
  showCreateButton = true
}) => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';

  const [templates, setTemplates] = useState<BusinessPlanTemplate[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Search and filter state
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedType, setSelectedType] = useState('');
  const [selectedDifficulty, setSelectedDifficulty] = useState('');
  const [minRating, setMinRating] = useState(0);
  const [sortBy, setSortBy] = useState('rating');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Preview state
  const [previewTemplateId, setPreviewTemplateId] = useState<number | null>(null);
  const [showPreview, setShowPreview] = useState(false);

  useEffect(() => {
    fetchTemplates();
    fetchCategories();
  }, []);

  useEffect(() => {
    if (searchQuery || selectedCategory || selectedType || selectedDifficulty || minRating > 0) {
      handleSearch();
    } else {
      fetchTemplates();
    }
  }, [searchQuery, selectedCategory, selectedType, selectedDifficulty, minRating, sortBy, sortOrder]);

  const fetchTemplates = async () => {
    setLoading(true);
    setError(null);

    try {
      const data = await businessPlanTemplatesAPI.getTemplates();
      const sortedData = sortTemplates(data);
      setTemplates(sortedData);
    } catch (err) {
      console.error('Error fetching templates:', err);
      setError('Failed to load templates. Please try again later.');
      setTemplates([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const data = await businessPlanTemplatesAPI.getTemplateCategories();
      setCategories(data);
    } catch (err) {
      console.error('Error fetching categories:', err);
      setCategories([]);
    }
  };

  const handleSearch = async () => {
    if (!searchQuery && !selectedCategory && !selectedType && !selectedDifficulty && minRating === 0) {
      fetchTemplates();
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const filters: any = {};
      if (selectedCategory) filters.industry = selectedCategory;
      if (selectedType) filters.template_type = selectedType;
      if (selectedDifficulty) filters.difficulty_level = selectedDifficulty;
      if (minRating > 0) filters.min_rating = minRating;

      const response = await businessPlanTemplatesAPI.searchTemplates(searchQuery, filters);
      const data = Array.isArray(response) ? response : response.results || [];
      const sortedData = sortTemplates(data);
      setTemplates(sortedData);
    } catch (err) {
      console.error('Error searching templates:', err);
      setError('Failed to search templates');
    } finally {
      setLoading(false);
    }
  };

  const sortTemplates = (data: BusinessPlanTemplate[]) => {
    return [...data].sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'rating':
          aValue = a.rating || 0;
          bValue = b.rating || 0;
          break;
        case 'usage':
          aValue = a.usage_count || 0;
          bValue = b.usage_count || 0;
          break;
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'created':
          aValue = new Date(a.created_at);
          bValue = new Date(b.created_at);
          break;
        case 'difficulty':
          const difficultyOrder = { beginner: 1, intermediate: 2, advanced: 3 };
          aValue = difficultyOrder[a.difficulty_level as keyof typeof difficultyOrder] || 0;
          bValue = difficultyOrder[b.difficulty_level as keyof typeof difficultyOrder] || 0;
          break;
        default:
          return 0;
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
  };

  const handlePreviewTemplate = (templateId: number) => {
    setPreviewTemplateId(templateId);
    setShowPreview(true);
  };

  const handleUseTemplate = (templateId: number) => {
    const template = templates.find(t => t.id === templateId);
    if (template && onSelectTemplate) {
      onSelectTemplate(template);
    }
    setShowPreview(false);
  };

  const getDifficultyColor = (level: string) => {
    switch (level) {
      case 'beginner': return 'text-green-400 bg-green-400/10';
      case 'intermediate': return 'text-yellow-400 bg-yellow-400/10';
      case 'advanced': return 'text-red-400 bg-red-400/10';
      default: return 'text-gray-400 bg-gray-400/10';
    }
  };

  const renderTemplateCard = (template: BusinessPlanTemplate) => (
    <div
      key={template.id}
      className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-gray-700 hover:border-purple-500/50 transition-all duration-300"
    >
      {/* Header */}
      <RTLFlex className="items-start justify-between mb-4">
        <div className="flex-1">
          <RTLText as="h3" className="text-lg font-semibold mb-2">
            {template.name}
          </RTLText>
          <RTLText className="text-sm text-gray-300 mb-3 line-clamp-2">
            {template.description}
          </RTLText>
        </div>

        <RTLFlex className="items-center gap-1 ml-4">
          <Star className="text-yellow-400 fill-current" size={16} />
          <span className="text-sm font-medium">{(template.rating || 0).toFixed(1)}</span>
        </RTLFlex>
      </RTLFlex>

      {/* Meta Information */}
      <div className="space-y-2 mb-4">
        <RTLFlex className="items-center justify-between text-sm text-gray-400">
          <RTLFlex className="items-center">
            <Tag size={14} className={isRTL ? 'ml-1' : 'mr-1'} />
            <span>{template.industry}</span>
          </RTLFlex>
          <span className={`px-2 py-1 rounded-full text-xs ${getDifficultyColor(template.difficulty_level)}`}>
            {template.difficulty_level}
          </span>
        </RTLFlex>

        <RTLFlex className="items-center justify-between text-sm text-gray-400">
          <RTLFlex className="items-center">
            <Clock size={14} className={isRTL ? 'ml-1' : 'mr-1'} />
            <span>{template.estimated_time || 0}h</span>
          </RTLFlex>
          <RTLFlex className="items-center">
            <Users size={14} className={isRTL ? 'ml-1' : 'mr-1'} />
            <span>{template.usage_count || 0}</span>
          </RTLFlex>
        </RTLFlex>
      </div>

      {/* Tags */}
      {template.tags && template.tags.length > 0 && (
        <div className="flex flex-wrap gap-1 mb-4">
          {template.tags.slice(0, 3).map((tag, index) => (
            <span
              key={index}
              className="px-2 py-1 bg-purple-600/20 text-purple-300 rounded text-xs"
            >
              {tag}
            </span>
          ))}
          {template.tags.length > 3 && (
            <span className="px-2 py-1 bg-gray-600/20 text-gray-400 rounded text-xs">
              +{template.tags.length - 3}
            </span>
          )}
        </div>
      )}

      {/* Actions */}
      <RTLFlex className="items-center justify-between">
        <button
          onClick={() => handlePreviewTemplate(template.id)}
          className="px-3 py-1 bg-gray-700 hover:bg-gray-600 rounded text-sm text-white transition-colors flex items-center"
        >
          <Eye size={14} className={isRTL ? 'ml-1' : 'mr-1'} />
          {t('templates.preview')}
        </button>

        <button
          onClick={() => handleUseTemplate(template.id)}
          className="px-4 py-1 bg-purple-600 hover:bg-purple-700 rounded text-sm text-white transition-colors"
        >
          {t('templates.useTemplate')}
        </button>
      </RTLFlex>
    </div>
  );

  const renderTemplateList = (template: BusinessPlanTemplate) => (
    <div
      key={template.id}
      className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-4 border border-gray-700 hover:border-purple-500/50 transition-all duration-300"
    >
      <RTLFlex className="items-center justify-between">
        <RTLFlex className="items-center flex-1">
          <FileText size={20} className={`text-purple-400 ${isRTL ? 'ml-3' : 'mr-3'}`} />
          <div className="flex-1">
            <RTLText as="h3" className="font-semibold mb-1">
              {template.name}
            </RTLText>
            <RTLText className="text-sm text-gray-400">
              {template.industry} • {template.template_type_display}
            </RTLText>
          </div>
        </RTLFlex>

        <RTLFlex className="items-center gap-6 text-sm text-gray-400">
          <RTLFlex className="items-center">
            <Star className="text-yellow-400 fill-current" size={16} />
            <span className="ml-1">{(template.rating || 0).toFixed(1)}</span>
          </RTLFlex>

          <RTLFlex className="items-center">
            <Users size={16} className={isRTL ? 'ml-1' : 'mr-1'} />
            <span>{template.usage_count || 0}</span>
          </RTLFlex>

          <RTLFlex className="items-center">
            <Clock size={16} className={isRTL ? 'ml-1' : 'mr-1'} />
            <span>{template.estimated_time || 0}h</span>
          </RTLFlex>

          <RTLFlex className="items-center gap-2">
            <button
              onClick={() => handlePreviewTemplate(template.id)}
              className="px-3 py-1 bg-gray-700 hover:bg-gray-600 rounded text-sm text-white transition-colors"
            >
              {t('templates.preview')}
            </button>

            <button
              onClick={() => handleUseTemplate(template.id)}
              className="px-4 py-1 bg-purple-600 hover:bg-purple-700 rounded text-sm text-white transition-colors"
            >
              {t('templates.use')}
            </button>
          </RTLFlex>
        </RTLFlex>
      </RTLFlex>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <RTLFlex className="items-center justify-between">
        <div>
          <RTLText as="h2" className="text-2xl font-bold mb-2">
            {t('templates.templateBrowser')}
          </RTLText>
          <RTLText className="text-gray-400">
            {t('templates.browseDescription')}
          </RTLText>
        </div>

        {showCreateButton && (
          <button
            onClick={onCreateNew}
            className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg text-white transition-colors"
          >
            {t('templates.createNew')}
          </button>
        )}
      </RTLFlex>

      {/* Search and Filters */}
      <div className="bg-gray-800/50 rounded-lg p-6 space-y-4">
        {/* Search Bar */}
        <div className="relative">
          <Search className={`absolute top-3 ${isRTL ? 'right-3' : 'left-3'} text-gray-400`} size={20} />
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder={t('templates.searchPlaceholder')}
            className={`w-full bg-gray-700 border border-gray-600 rounded-lg ${isRTL ? 'pr-10 pl-4' : 'pl-10 pr-4'} py-3 text-white placeholder-gray-400 focus:border-purple-500 focus:outline-none`}
          />
        </div>

        {/* Filters */}
        <RTLFlex className="items-center gap-4 flex-wrap">
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:border-purple-500 focus:outline-none"
          >
            <option value="">{t('templates.allCategories')}</option>
            {categories.map((category) => (
              <option key={category.name} value={category.name}>
                {category.name} ({category.count})
              </option>
            ))}
          </select>

          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:border-purple-500 focus:outline-none"
          >
            <option value="">{t('templates.allTypes')}</option>
            <option value="standard">{t('templates.standard')}</option>
            <option value="lean">{t('templates.lean')}</option>
            <option value="detailed">{t('templates.detailed')}</option>
            <option value="investor">{t('templates.investor')}</option>
          </select>

          <select
            value={selectedDifficulty}
            onChange={(e) => setSelectedDifficulty(e.target.value)}
            className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:border-purple-500 focus:outline-none"
          >
            <option value="">{t('templates.allDifficulties')}</option>
            <option value="beginner">{t('templates.beginner')}</option>
            <option value="intermediate">{t('templates.intermediate')}</option>
            <option value="advanced">{t('templates.advanced')}</option>
          </select>

          <RTLFlex className="items-center gap-2">
            <span className="text-sm text-gray-400">{t('templates.minRating')}:</span>
            <select
              value={minRating}
              onChange={(e) => setMinRating(Number(e.target.value))}
              className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:border-purple-500 focus:outline-none"
            >
              <option value={0}>{t('templates.any')}</option>
              <option value={3}>3+ ⭐</option>
              <option value={4}>4+ ⭐</option>
              <option value={4.5}>4.5+ ⭐</option>
            </select>
          </RTLFlex>
        </RTLFlex>

        {/* Sort and View Options */}
        <RTLFlex className="items-center justify-between">
          <RTLFlex className="items-center gap-4">
            <RTLFlex className="items-center gap-2">
              <span className="text-sm text-gray-400">{t('templates.sortBy')}:</span>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:border-purple-500 focus:outline-none"
              >
                <option value="rating">{t('templates.rating')}</option>
                <option value="usage">{t('templates.popularity')}</option>
                <option value="name">{t('templates.name')}</option>
                <option value="created">{t('templates.newest')}</option>
                <option value="difficulty">{t('templates.difficulty')}</option>
              </select>
            </RTLFlex>

            <button
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              className="p-2 bg-gray-700 hover:bg-gray-600 rounded-lg text-white transition-colors"
            >
              {sortOrder === 'asc' ? <SortAsc size={16} /> : <SortDesc size={16} />}
            </button>
          </RTLFlex>

          <RTLFlex className="items-center gap-2">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-lg transition-colors ${
                viewMode === 'grid' ? 'bg-purple-600 text-white' : 'bg-gray-700 text-gray-400 hover:bg-gray-600'
              }`}
            >
              <Grid size={16} />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-lg transition-colors ${
                viewMode === 'list' ? 'bg-purple-600 text-white' : 'bg-gray-700 text-gray-400 hover:bg-gray-600'
              }`}
            >
              <List size={16} />
            </button>
          </RTLFlex>
        </RTLFlex>
      </div>

      {/* Results */}
      {loading ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
        </div>
      ) : error ? (
        <div className="text-center py-12">
          <p className="text-red-400">{error}</p>
        </div>
      ) : templates.length === 0 ? (
        <div className="text-center py-12">
          <FileText className="mx-auto mb-4 text-gray-400" size={48} />
          <p className="text-gray-400">{t('templates.noTemplatesFound')}</p>
        </div>
      ) : (
        <div className={viewMode === 'grid'
          ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
          : 'space-y-4'
        }>
          {templates.map(template =>
            viewMode === 'grid' ? renderTemplateCard(template) : renderTemplateList(template)
          )}
        </div>
      )}

      {/* Template Preview Modal */}
      {previewTemplateId && (
        <TemplatePreview
          templateId={previewTemplateId}
          isOpen={showPreview}
          onClose={() => setShowPreview(false)}
          onUseTemplate={handleUseTemplate}
          onDuplicate={(id) => {
            // Handle template duplication
            console.log('Duplicate template:', id);
            setShowPreview(false);
          }}
        />
      )}
    </div>
  );
};
