from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.contrib.auth.models import User
from django.db.models import Count, Avg, Q
from django.utils import timezone
from datetime import timedelta
from incubator.models import (
    MentorProfile, MentorshipMatch, MentorshipSession, 
    MentorshipFeedback, MentorshipApplication
)
from incubator.serializers import (
    MentorshipSessionSerializer, MentorshipFeedbackSerializer
)
from .models import UserProfile
from api.permissions import IsAdminUser


class MentorViewSet(viewsets.ViewSet):
    """
    ViewSet for mentor-specific functionality
    """
    permission_classes = [permissions.IsAuthenticated]

    def get_permissions(self):
        """
        Instantiates and returns the list of permissions that this view requires.
        """
        permission_classes = [permissions.IsAuthenticated]
        return [permission() for permission in permission_classes]

    @action(detail=False, methods=['get'])
    def dashboard_stats(self, request):
        """
        Get mentor dashboard statistics
        """
        # Check if user has mentor role
        user_profile = getattr(request.user, 'profile', None)
        if not user_profile or not user_profile.has_role('mentor'):
            if not (request.user.is_staff or request.user.is_superuser):
                return Response(
                    {'error': 'Mentor access required'}, 
                    status=status.HTTP_403_FORBIDDEN
                )

        try:
            mentor_profile = MentorProfile.objects.get(user=request.user)
        except MentorProfile.DoesNotExist:
            # Create mentor profile if it doesn't exist
            mentor_profile = MentorProfile.objects.create(
                user=request.user,
                expertise_areas="General Business",
                bio="Experienced mentor"
            )

        # Get mentorship statistics
        total_mentees = MentorshipMatch.objects.filter(
            mentor=mentor_profile,
            status='active'
        ).count()

        active_sessions = MentorshipSession.objects.filter(
            mentor=mentor_profile,
            status='scheduled'
        ).count()

        completed_sessions = MentorshipSession.objects.filter(
            mentor=mentor_profile,
            status='completed'
        ).count()

        # Calculate average rating
        avg_rating = MentorshipFeedback.objects.filter(
            session__mentor=mentor_profile
        ).aggregate(avg_rating=Avg('mentor_rating'))['avg_rating'] or 0

        # Calculate total hours
        total_hours = MentorshipSession.objects.filter(
            mentor=mentor_profile,
            status='completed'
        ).aggregate(
            total_duration=Count('duration')
        )['total_duration'] or 0

        # Get upcoming meetings
        upcoming_meetings = MentorshipSession.objects.filter(
            mentor=mentor_profile,
            status='scheduled',
            scheduled_time__gte=timezone.now()
        ).count()

        stats = {
            'totalMentees': total_mentees,
            'activeSessions': active_sessions,
            'completedSessions': completed_sessions,
            'averageRating': round(avg_rating, 1),
            'totalHours': total_hours,
            'upcomingMeetings': upcoming_meetings
        }

        return Response(stats)

    @action(detail=False, methods=['get'])
    def my_mentees(self, request):
        """
        Get mentor's mentees list
        """
        # Check mentor access
        user_profile = getattr(request.user, 'profile', None)
        if not user_profile or not user_profile.has_role('mentor'):
            if not (request.user.is_staff or request.user.is_superuser):
                return Response(
                    {'error': 'Mentor access required'}, 
                    status=status.HTTP_403_FORBIDDEN
                )

        try:
            mentor_profile = MentorProfile.objects.get(user=request.user)
        except MentorProfile.DoesNotExist:
            return Response([])

        # Get active mentorship matches
        matches = MentorshipMatch.objects.filter(
            mentor=mentor_profile,
            status='active'
        ).select_related('mentee', 'business_idea')

        mentees = []
        for match in matches:
            # Get last and next session
            last_session = MentorshipSession.objects.filter(
                mentor=mentor_profile,
                mentee=match.mentee,
                status='completed'
            ).order_by('-scheduled_time').first()

            next_session = MentorshipSession.objects.filter(
                mentor=mentor_profile,
                mentee=match.mentee,
                status='scheduled',
                scheduled_time__gte=timezone.now()
            ).order_by('scheduled_time').first()

            # Calculate progress (simplified)
            total_sessions = MentorshipSession.objects.filter(
                mentor=mentor_profile,
                mentee=match.mentee
            ).count()
            completed_sessions = MentorshipSession.objects.filter(
                mentor=mentor_profile,
                mentee=match.mentee,
                status='completed'
            ).count()
            
            progress = (completed_sessions / max(total_sessions, 1)) * 100

            mentees.append({
                'id': str(match.mentee.id),
                'name': f"{match.mentee.first_name} {match.mentee.last_name}",
                'businessIdea': match.business_idea.title if match.business_idea else "General Mentorship",
                'stage': match.business_idea.stage if match.business_idea else "Planning",
                'lastSession': last_session.scheduled_time.date().isoformat() if last_session else None,
                'nextSession': next_session.scheduled_time.date().isoformat() if next_session else None,
                'progress': round(progress)
            })

        return Response(mentees)

    @action(detail=False, methods=['get'])
    def upcoming_sessions(self, request):
        """
        Get upcoming mentorship sessions
        """
        # Check mentor access
        user_profile = getattr(request.user, 'profile', None)
        if not user_profile or not user_profile.has_role('mentor'):
            if not (request.user.is_staff or request.user.is_superuser):
                return Response(
                    {'error': 'Mentor access required'}, 
                    status=status.HTTP_403_FORBIDDEN
                )

        try:
            mentor_profile = MentorProfile.objects.get(user=request.user)
        except MentorProfile.DoesNotExist:
            return Response([])

        # Get upcoming sessions
        upcoming_sessions = MentorshipSession.objects.filter(
            mentor=mentor_profile,
            status='scheduled',
            scheduled_time__gte=timezone.now()
        ).select_related('mentee').order_by('scheduled_time')[:10]

        sessions = []
        for session in upcoming_sessions:
            sessions.append({
                'id': str(session.id),
                'menteeName': f"{session.mentee.first_name} {session.mentee.last_name}",
                'topic': session.topic or "General Mentorship Session",
                'scheduledTime': session.scheduled_time.isoformat(),
                'duration': session.duration or 60,
                'type': session.session_type or 'video'
            })

        return Response(sessions)

    @action(detail=False, methods=['get'])
    def session_history(self, request):
        """
        Get mentor's session history
        """
        # Check mentor access
        user_profile = getattr(request.user, 'profile', None)
        if not user_profile or not user_profile.has_role('mentor'):
            if not (request.user.is_staff or request.user.is_superuser):
                return Response(
                    {'error': 'Mentor access required'}, 
                    status=status.HTTP_403_FORBIDDEN
                )

        try:
            mentor_profile = MentorProfile.objects.get(user=request.user)
        except MentorProfile.DoesNotExist:
            return Response([])

        # Get completed sessions
        completed_sessions = MentorshipSession.objects.filter(
            mentor=mentor_profile,
            status='completed'
        ).select_related('mentee').order_by('-scheduled_time')[:20]

        sessions = []
        for session in completed_sessions:
            # Get feedback for this session
            feedback = MentorshipFeedback.objects.filter(session=session).first()
            
            sessions.append({
                'id': str(session.id),
                'menteeName': f"{session.mentee.first_name} {session.mentee.last_name}",
                'topic': session.topic or "General Mentorship Session",
                'scheduledTime': session.scheduled_time.isoformat(),
                'duration': session.duration or 60,
                'rating': feedback.mentor_rating if feedback else None,
                'feedback': feedback.mentor_feedback if feedback else None
            })

        return Response(sessions)

    @action(detail=False, methods=['get'])
    def analytics(self, request):
        """
        Get mentor analytics
        """
        # Check mentor access
        user_profile = getattr(request.user, 'profile', None)
        if not user_profile or not user_profile.has_role('mentor'):
            if not (request.user.is_staff or request.user.is_superuser):
                return Response(
                    {'error': 'Mentor access required'}, 
                    status=status.HTTP_403_FORBIDDEN
                )

        try:
            mentor_profile = MentorProfile.objects.get(user=request.user)
        except MentorProfile.DoesNotExist:
            return Response({})

        # Get analytics for the last 6 months
        six_months_ago = timezone.now() - timedelta(days=180)
        
        monthly_stats = []
        for i in range(6):
            month_start = timezone.now().replace(day=1) - timedelta(days=30*i)
            month_end = month_start + timedelta(days=30)
            
            sessions_count = MentorshipSession.objects.filter(
                mentor=mentor_profile,
                scheduled_time__gte=month_start,
                scheduled_time__lt=month_end,
                status='completed'
            ).count()
            
            avg_rating = MentorshipFeedback.objects.filter(
                session__mentor=mentor_profile,
                session__scheduled_time__gte=month_start,
                session__scheduled_time__lt=month_end
            ).aggregate(avg_rating=Avg('mentor_rating'))['avg_rating'] or 0
            
            monthly_stats.append({
                'month': month_start.strftime('%Y-%m'),
                'sessions': sessions_count,
                'averageRating': round(avg_rating, 1)
            })

        # Overall statistics
        total_sessions = MentorshipSession.objects.filter(
            mentor=mentor_profile,
            status='completed'
        ).count()
        
        total_mentees = MentorshipMatch.objects.filter(
            mentor=mentor_profile
        ).values('mentee').distinct().count()
        
        overall_rating = MentorshipFeedback.objects.filter(
            session__mentor=mentor_profile
        ).aggregate(avg_rating=Avg('mentor_rating'))['avg_rating'] or 0

        analytics_data = {
            'monthlyStats': monthly_stats,
            'totalSessions': total_sessions,
            'totalMentees': total_mentees,
            'overallRating': round(overall_rating, 1),
            'responseRate': 95,  # This could be calculated based on actual data
            'successRate': 85   # This could be calculated based on mentee outcomes
        }

        return Response(analytics_data)

    @action(detail=False, methods=['post'])
    def update_availability(self, request):
        """
        Update mentor availability
        """
        # Check mentor access
        user_profile = getattr(request.user, 'profile', None)
        if not user_profile or not user_profile.has_role('mentor'):
            if not (request.user.is_staff or request.user.is_superuser):
                return Response(
                    {'error': 'Mentor access required'}, 
                    status=status.HTTP_403_FORBIDDEN
                )

        try:
            mentor_profile = MentorProfile.objects.get(user=request.user)
            
            # Update availability (you might want to create a separate model for this)
            availability = request.data.get('availability', {})
            mentor_profile.availability = availability
            mentor_profile.save()
            
            return Response({'message': 'Availability updated successfully'})
        except MentorProfile.DoesNotExist:
            return Response(
                {'error': 'Mentor profile not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )
