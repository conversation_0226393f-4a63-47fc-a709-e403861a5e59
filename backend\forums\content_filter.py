import re
import json
import os
from django.conf import settings
from django.utils import timezone
from django.db import models

# Load profanity list
def load_profanity_list():
    """Load profanity words from a JSON file"""
    try:
        # Try to load from settings if defined
        profanity_file = getattr(settings, 'PROFANITY_LIST_FILE', None)
        
        if profanity_file and os.path.exists(profanity_file):
            with open(profanity_file, 'r') as f:
                return json.load(f)
        
        # Default profanity list (basic example)
        return [
            "badword1", "badword2", "offensive", "inappropriate", 
            "explicit", "vulgar", "obscene", "profane"
        ]
    except Exception as e:
        print(f"Error loading profanity list: {e}")
        return []

# Global profanity list
PROFANITY_LIST = load_profanity_list()

# Severity levels for content flags
SEVERITY_LEVELS = {
    'LOW': 1,      # Minor issues, probably fine
    'MEDIUM': 2,   # Concerning content, needs review
    'HIGH': 3      # Serious violation, auto-reject
}

class ContentFlag(models.Model):
    """Model to store content flags"""
    CONTENT_TYPES = (
        ('thread', 'Thread'),
        ('post', 'Post'),
    )
    
    REASONS = (
        ('profanity', 'Profanity'),
        ('hate_speech', 'Hate Speech'),
        ('spam', 'Spam'),
        ('inappropriate', 'Inappropriate Content'),
        ('other', 'Other'),
    )
    
    content_type = models.CharField(max_length=20, choices=CONTENT_TYPES)
    content_id = models.PositiveIntegerField()
    reason = models.CharField(max_length=20, choices=REASONS)
    details = models.TextField(blank=True)
    severity = models.PositiveSmallIntegerField(default=1)  # 1=low, 2=medium, 3=high
    auto_flagged = models.BooleanField(default=True)
    reviewed = models.BooleanField(default=False)
    reviewed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='reviewed_flags'
    )
    reviewed_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['content_type', 'content_id']),
        ]
    
    def __str__(self):
        return f"{self.content_type} #{self.content_id} - {self.reason} ({self.severity})"
    
    def mark_reviewed(self, user):
        """Mark the flag as reviewed"""
        self.reviewed = True
        self.reviewed_by = user
        self.reviewed_at = timezone.now()
        self.save()


def check_profanity(text):
    """
    Check text for profanity
    
    Args:
        text (str): Text to check
        
    Returns:
        tuple: (contains_profanity, found_words, severity)
    """
    if not text or not isinstance(text, str):
        return False, [], 0
    
    # Convert to lowercase for case-insensitive matching
    text_lower = text.lower()
    
    # Remove HTML tags if present
    text_lower = re.sub(r'<[^>]*>', ' ', text_lower)
    
    # Find all words
    words = re.findall(r'\b\w+\b', text_lower)
    
    # Check for profanity
    found_words = []
    for word in words:
        if word in PROFANITY_LIST:
            found_words.append(word)
    
    # Determine severity based on number of found words
    severity = 0
    if found_words:
        if len(found_words) >= 5:
            severity = SEVERITY_LEVELS['HIGH']
        elif len(found_words) >= 2:
            severity = SEVERITY_LEVELS['MEDIUM']
        else:
            severity = SEVERITY_LEVELS['LOW']
    
    return bool(found_words), found_words, severity


def check_spam(text):
    """
    Check text for spam indicators
    
    Args:
        text (str): Text to check
        
    Returns:
        tuple: (is_spam, reason, severity)
    """
    if not text or not isinstance(text, str):
        return False, "", 0
    
    # Check for excessive URLs (potential spam)
    url_count = len(re.findall(r'https?://(?:[-\w.]|(?:%[\da-fA-F]{2}))+', text))
    
    # Check for repeated text patterns
    repeated_phrases = re.findall(r'(.{10,}?)\1+', text)
    
    # Check for ALL CAPS text (shouting)
    words = re.findall(r'\b[A-Z]{4,}\b', text)
    caps_ratio = len(words) / max(len(re.findall(r'\b\w+\b', text)), 1)
    
    # Determine if it's spam and the severity
    is_spam = False
    reason = ""
    severity = 0
    
    if url_count > 5:
        is_spam = True
        reason = f"Excessive URLs ({url_count})"
        severity = SEVERITY_LEVELS['MEDIUM']
    
    if repeated_phrases:
        is_spam = True
        reason += f"{'; ' if reason else ''}Repeated text patterns"
        severity = max(severity, SEVERITY_LEVELS['MEDIUM'])
    
    if caps_ratio > 0.5 and len(words) > 3:
        is_spam = True
        reason += f"{'; ' if reason else ''}Excessive use of ALL CAPS"
        severity = max(severity, SEVERITY_LEVELS['LOW'])
    
    return is_spam, reason, severity


def analyze_content(content, content_type, content_id):
    """
    Analyze content for inappropriate material
    
    Args:
        content (str): Content to analyze
        content_type (str): Type of content ('thread' or 'post')
        content_id (int): ID of the content
        
    Returns:
        tuple: (is_flagged, flags)
    """
    flags = []
    
    # Check for profanity
    has_profanity, profane_words, profanity_severity = check_profanity(content)
    if has_profanity:
        flags.append({
            'content_type': content_type,
            'content_id': content_id,
            'reason': 'profanity',
            'details': f"Found profane words: {', '.join(profane_words)}",
            'severity': profanity_severity,
            'auto_flagged': True
        })
    
    # Check for spam
    is_spam, spam_reason, spam_severity = check_spam(content)
    if is_spam:
        flags.append({
            'content_type': content_type,
            'content_id': content_id,
            'reason': 'spam',
            'details': spam_reason,
            'severity': spam_severity,
            'auto_flagged': True
        })
    
    # Create ContentFlag objects for each flag
    flag_objects = []
    for flag_data in flags:
        flag = ContentFlag.objects.create(**flag_data)
        flag_objects.append(flag)
    
    return bool(flags), flag_objects


def should_auto_reject(flags):
    """
    Determine if content should be automatically rejected based on flags
    
    Args:
        flags (list): List of ContentFlag objects
        
    Returns:
        bool: True if content should be auto-rejected
    """
    if not flags:
        return False
    
    # Auto-reject if any flag has HIGH severity
    for flag in flags:
        if flag.severity >= SEVERITY_LEVELS['HIGH']:
            return True
    
    # Auto-reject if there are multiple MEDIUM severity flags
    medium_flags = [f for f in flags if f.severity >= SEVERITY_LEVELS['MEDIUM']]
    if len(medium_flags) >= 2:
        return True
    
    return False
