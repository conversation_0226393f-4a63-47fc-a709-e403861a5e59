import React from 'react';
import { Link } from 'react-router-dom';
import {
  UserPlus,
  Calendar,
  MessageSquare,
  Shield,
  Bot,
  BarChart3,
  Users,
  Lightbulb
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../hooks/useLanguage';
import { RTLText } from '../../../common';

interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  to: string;  // Changed from href to to for React Router
  color: string;
  count?: number;
}

interface QuickActionsPanelProps {
  pendingMemberships?: number;
  pendingPosts?: number;
  activeUsers?: number;
}

const QuickActionsPanel: React.FC<QuickActionsPanelProps> = ({ pendingMemberships = 0,
  pendingPosts = 0,
  activeUsers = 0
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const quickActions: QuickAction[] = [
    {
      id: 'membership',
      title: t('admin.membership'),
      description: t('admin.reviewApplications'),
      icon: <UserPlus size={20} />,
      to: '/admin/membership',
      color: 'bg-gradient-to-r from-purple-500 to-purple-600',
      count: pendingMemberships
    },
    {
      id: 'events',
      title: t('admin.events'),
      description: t('admin.manageEvents'),
      icon: <Calendar size={20} />,
      to: '/admin/events',
      color: 'bg-gradient-to-r from-blue-500 to-blue-600'
    },
    {
      id: 'posts',
      title: t('admin.posts'),
      description: t('admin.moderatePosts'),
      icon: <MessageSquare size={20} />,
      to: '/admin/posts',
      color: 'bg-gradient-to-r from-green-500 to-green-600',
      count: pendingPosts
    },
    {
      id: 'users',
      title: t('admin.users'),
      description: t('admin.manageUsers'),
      icon: <Users size={20} />,
      to: '/admin/users',
      color: 'bg-gradient-to-r from-orange-500 to-orange-600',
      count: activeUsers
    },
    {
      id: 'incubator',
      title: t('admin.incubator.title'),
      description: t('admin.incubator.description'),
      icon: <Lightbulb size={20} />,
      to: '/admin/incubator',
      color: 'bg-gradient-to-r from-yellow-500 to-yellow-600'
    },
    {
      id: 'chat',
      title: t('admin.yasmeenAI'),
      description: t('admin.chat.description'),
      icon: <Bot size={20} />,
      to: '/admin/chat',
      color: 'bg-gradient-to-r from-pink-500 to-pink-600'
    },
    {
      id: 'analytics',
      title: t('admin.analytics'),
      description: t('admin.viewAnalytics'),
      icon: <BarChart3 size={20} />,
      to: '/admin/analytics',
      color: 'bg-gradient-to-r from-indigo-500 to-indigo-600'
    },
    {
      id: 'moderation',
      title: t('admin.moderation'),
      description: t('admin.moderateContent'),
      icon: <Shield size={20} />,
      to: '/admin/moderation',
      color: 'bg-gradient-to-r from-red-500 to-red-600'
    }
  ];

  return (
    <div className="bg-black/25 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {quickActions.map((action) => (
          <Link
            key={action.id}
            to={action.to}
            className="group relative overflow-hidden bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-6 transition-all duration-300 hover:scale-105 hover:bg-white/15 hover:shadow-xl hover:shadow-purple-500/20 block"
          >
            <div className="relative z-10">
              <div className={`flex items-center justify-between mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className={`p-3 rounded-xl text-white ${action.color} group-hover:scale-110 transition-transform duration-300`}>
                  {action.icon}
                </div>
                {action.count !== undefined && action.count > 0 && (
                  <div className="relative">
                    <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full font-medium animate-pulse">
                      {action.count}
                    </span>
                    <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-400 rounded-full animate-ping"></div>
                  </div>
                )}
              </div>

              <RTLText as="h3" className="text-white font-semibold text-lg mb-2 group-hover:text-purple-200 transition-colors">
                {action.title}
              </RTLText>

              <RTLText as="p" className="text-gray-300 text-sm leading-relaxed">
                {action.description}
              </RTLText>

              {/* Action indicator */}
              <div className={`mt-4 flex items-center text-xs text-purple-300 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <span className="group-hover:text-purple-200 transition-colors">
                  {t('admin.clickToManage', 'Click to manage')}
                </span>
                <div className={`w-4 h-4 ${isRTL ? 'mr-2' : 'ml-2'} group-hover:translate-x-1 transition-transform`}>
                  <svg fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>

            {/* Animated border */}
            <div className="absolute inset-0 rounded-xl border-2 border-transparent group-hover:border-purple-400/50 transition-all duration-300"></div>

            {/* Glow effect */}
            <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-purple-600/0 via-purple-600/5 to-purple-600/0 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default QuickActionsPanel;
