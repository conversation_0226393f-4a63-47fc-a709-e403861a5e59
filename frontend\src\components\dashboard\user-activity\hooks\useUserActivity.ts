import { useState, useEffect } from 'react';
import { userAPI, UserActivity } from '../../../../services/api';

interface UseUserActivityResult {
  activity: UserActivity | null;
  loading: boolean;
  error: string | null;
  prepareActivityChartData: () => {
    labels: string[];
    datasets: {
      label: string;
      data: number[];
      backgroundColor: string;
      borderColor: string;
      borderWidth: number;
    }[];
  } | null;
}

export const useUserActivity = (): UseUserActivityResult => {
  const [activity, setActivity] = useState<UserActivity | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUserActivity = async () => {
      setLoading(true);
      try {
        const data = await userAPI.getUserActivity();
        setActivity(data);
        setError(null);
      } catch (err) {
        console.error('Error fetching user activity:', err);
        setError('Failed to load activity data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchUserActivity();
  }, []);

  // Prepare chart data
  const prepareActivityChartData = () => {
    if (!activity || !activity.daily_activity || activity.daily_activity.length === 0) {
      return null;
    }

    // Sort by date
    const sortedActivity = [...activity.daily_activity].sort(
      (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
    );

    // Get the last 7 days of activity
    const last7DaysActivity = sortedActivity.slice(-7);

    // Format dates and extract counts
    const formattedDates = last7DaysActivity.map(item => {
      const date = new Date(item.date);
      return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    });

    const postCounts = last7DaysActivity.map(item => item.post_count);
    const commentCounts = last7DaysActivity.map(item => item.comment_count);

    return {
      labels: formattedDates,
      datasets: [
        {
          label: 'Posts',
          data: postCounts,
          backgroundColor: 'rgba(99, 102, 241, 0.7)', // indigo
          borderColor: 'rgba(99, 102, 241, 1)',
          borderWidth: 1,
        },
        {
          label: 'Comments',
          data: commentCounts,
          backgroundColor: 'rgba(139, 92, 246, 0.7)', // purple
          borderColor: 'rgba(139, 92, 246, 1)',
          borderWidth: 1,
        },
      ],
    };
  };

  return {
    activity,
    loading,
    error,
    prepareActivityChartData
  };
};

export default useUserActivity;
