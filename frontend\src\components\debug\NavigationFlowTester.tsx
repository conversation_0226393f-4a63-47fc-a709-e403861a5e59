import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAppSelector } from '../../store/hooks';
import { 
  navigationTestScenarios, 
  getTestsForRole, 
  validateNavigationBehavior,
  NavigationTest,
  NavigationTestResult 
} from '../../utils/navigationTester';
import { UserRole } from '../../routes/routeConfig';
import { Navigation, Play, CheckCircle, XCircle, Clock, Users, BarChart3, RefreshCw } from 'lucide-react';

/**
 * Navigation Flow Tester - Development only
 * Comprehensive testing of user navigation flows
 */
const NavigationFlowTester: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [testResults, setTestResults] = useState<NavigationTestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [selectedRole, setSelectedRole] = useState<UserRole | 'unauthenticated' | 'all'>('all');
  const [currentTest, setCurrentTest] = useState<string | null>(null);
  
  const { user, isAuthenticated } = useAppSelector(state => state.auth);
  const navigate = useNavigate();
  const location = useLocation();

  const roles: (UserRole | 'unauthenticated' | 'all')[] = [
    'all', 'unauthenticated', 'user', 'admin', 'super_admin', 'mentor', 'investor', 'moderator'
  ];

  const runNavigationTest = async (test: NavigationTest): Promise<NavigationTestResult> => {
    const startTime = Date.now();
    setCurrentTest(test.id);

    try {
      // Simulate navigation test
      const actualPath = location.pathname;
      const validation = validateNavigationBehavior(test, actualPath, user);

      const result: NavigationTestResult = {
        testId: test.id,
        passed: validation.isValid,
        actualPath,
        expectedPath: test.expectedEndPath,
        error: validation.reason,
        steps: [],
        duration: Date.now() - startTime
      };

      return result;
    } catch (error) {
      return {
        testId: test.id,
        passed: false,
        actualPath: location.pathname,
        expectedPath: test.expectedEndPath,
        error: error instanceof Error ? error.message : 'Unknown error',
        steps: [],
        duration: Date.now() - startTime
      };
    }
  };

  const runSelectedTests = async () => {
    setIsRunning(true);
    setTestResults([]);

    try {
      const testsToRun = selectedRole === 'all' 
        ? navigationTestScenarios 
        : getTestsForRole(selectedRole);

      const results: NavigationTestResult[] = [];

      for (const test of testsToRun) {
        const result = await runNavigationTest(test);
        results.push(result);
        setTestResults([...results]); // Update UI progressively
        
        // Small delay between tests
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      setTestResults(results);
    } catch (error) {
      console.error('Navigation testing failed:', error);
    } finally {
      setIsRunning(false);
      setCurrentTest(null);
    }
  };

  const runSingleTest = async (test: NavigationTest) => {
    setIsRunning(true);
    try {
      const result = await runNavigationTest(test);
      setTestResults(prev => {
        const filtered = prev.filter(r => r.testId !== test.id);
        return [...filtered, result];
      });
    } finally {
      setIsRunning(false);
    }
  };

  const getTestStats = () => {
    const total = testResults.length;
    const passed = testResults.filter(r => r.passed).length;
    const failed = total - passed;
    const successRate = total > 0 ? Math.round((passed / total) * 100) : 0;

    return { total, passed, failed, successRate };
  };

  const getRoleColor = (role: string): string => {
    switch (role) {
      case 'super_admin': return 'text-red-600 bg-red-100';
      case 'admin': return 'text-blue-600 bg-blue-100';
      case 'moderator': return 'text-orange-600 bg-orange-100';
      case 'mentor': return 'text-green-600 bg-green-100';
      case 'investor': return 'text-purple-600 bg-purple-100';
      case 'user': return 'text-gray-600 bg-gray-100';
      case 'unauthenticated': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const stats = getTestStats();

  return (
    <>
      {/* Floating Button */}
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-56 right-4 z-50 bg-teal-600 hover:bg-teal-700 text-white p-3 rounded-full shadow-lg transition-colors"
        title="Navigation Flow Tester"
      >
        <Navigation className="w-5 h-5" />
      </button>

      {/* Test Panel */}
      {isOpen && (
        <div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Navigation Flow Tester
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Test user navigation flows and routing behavior
                </p>
              </div>
              <button
                onClick={() => setIsOpen(false)}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              >
                ✕
              </button>
            </div>

            <div className="flex h-[calc(90vh-120px)]">
              {/* Left Panel - Controls */}
              <div className="w-1/3 p-6 border-r border-gray-200 dark:border-gray-700 overflow-y-auto">
                {/* Current User Info */}
                <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    Current State
                  </h3>
                  <div className="text-sm space-y-1">
                    <div><strong>Path:</strong> {location.pathname}</div>
                    <div><strong>Authenticated:</strong> {isAuthenticated ? '✅ Yes' : '❌ No'}</div>
                    <div><strong>User:</strong> {user?.username || 'None'}</div>
                    <div><strong>Role:</strong> {user?.is_superuser ? 'Super Admin' : user?.is_admin ? 'Admin' : 'User'}</div>
                  </div>
                </div>

                {/* Test Controls */}
                <div className="mb-6">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                    Test Configuration
                  </h4>
                  
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Test Role:
                    </label>
                    <select
                      value={selectedRole}
                      onChange={(e) => setSelectedRole(e.target.value as any)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      {roles.map(role => (
                        <option key={role} value={role}>
                          {role === 'all' ? 'All Roles' : role.replace('_', ' ').toUpperCase()}
                        </option>
                      ))}
                    </select>
                  </div>

                  <button
                    onClick={runSelectedTests}
                    disabled={isRunning}
                    className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 transition-colors"
                  >
                    {isRunning ? (
                      <RefreshCw className="w-4 h-4 animate-spin" />
                    ) : (
                      <Play className="w-4 h-4" />
                    )}
                    {isRunning ? 'Running Tests...' : 'Run Navigation Tests'}
                  </button>
                </div>

                {/* Test Stats */}
                {testResults.length > 0 && (
                  <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                      Test Results Summary
                    </h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="text-center">
                        <div className="text-lg font-bold text-green-600">{stats.passed}</div>
                        <div className="text-gray-600 dark:text-gray-300">Passed</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-red-600">{stats.failed}</div>
                        <div className="text-gray-600 dark:text-gray-300">Failed</div>
                      </div>
                      <div className="text-center col-span-2">
                        <div className="text-lg font-bold text-blue-600">{stats.successRate}%</div>
                        <div className="text-gray-600 dark:text-gray-300">Success Rate</div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Current Test */}
                {currentTest && (
                  <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4 text-yellow-600 animate-pulse" />
                      <span className="text-sm text-yellow-800 dark:text-yellow-200">
                        Running: {currentTest}
                      </span>
                    </div>
                  </div>
                )}
              </div>

              {/* Right Panel - Test Results */}
              <div className="flex-1 p-6 overflow-y-auto">
                {testResults.length > 0 ? (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                      Navigation Test Results
                    </h3>
                    <div className="space-y-3">
                      {testResults.map((result, index) => {
                        const test = navigationTestScenarios.find(t => t.id === result.testId);
                        if (!test) return null;

                        return (
                          <div
                            key={index}
                            className={`p-4 border rounded-lg ${
                              result.passed 
                                ? 'border-green-200 bg-green-50 dark:bg-green-900/20' 
                                : 'border-red-200 bg-red-50 dark:bg-red-900/20'
                            }`}
                          >
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center gap-2">
                                {result.passed ? (
                                  <CheckCircle className="w-5 h-5 text-green-600" />
                                ) : (
                                  <XCircle className="w-5 h-5 text-red-600" />
                                )}
                                <span className="font-medium text-gray-900 dark:text-white">
                                  {test.name}
                                </span>
                                <span className={`px-2 py-1 text-xs rounded ${getRoleColor(test.userRole)}`}>
                                  {test.userRole.replace('_', ' ').toUpperCase()}
                                </span>
                              </div>
                              <span className="text-sm text-gray-500">
                                {result.duration}ms
                              </span>
                            </div>
                            
                            <div className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                              {test.description}
                            </div>
                            
                            <div className="text-sm space-y-1">
                              <div>
                                <strong>Expected:</strong> {result.expectedPath}
                              </div>
                              <div>
                                <strong>Actual:</strong> {result.actualPath}
                              </div>
                              {result.error && (
                                <div className="text-red-600">
                                  <strong>Error:</strong> {result.error}
                                </div>
                              )}
                            </div>

                            <div className="mt-2 flex gap-2">
                              <button
                                onClick={() => runSingleTest(test)}
                                disabled={isRunning}
                                className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
                              >
                                Re-run
                              </button>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-500 dark:text-gray-400">
                    <div className="text-center">
                      <Users className="w-16 h-16 mx-auto mb-4 opacity-50" />
                      <p>Select a role and run tests to see navigation flow results</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default NavigationFlowTester;
