from django.db import models
from django.contrib.auth.models import User
from .models_base import BusinessIdea, ProgressUpdate

class BusinessMilestone(models.Model):
    """Milestones for business ideas"""

    STATUS_CHOICES = (
        ('not_started', 'Not Started'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('delayed', 'Delayed'),
        ('cancelled', 'Cancelled'),
    )

    PRIORITY_CHOICES = (
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    )

    business_idea = models.ForeignKey(BusinessIdea, on_delete=models.CASCADE, related_name='milestones')
    title = models.CharField(max_length=200)
    description = models.TextField()
    due_date = models.DateField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='not_started')
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES, default='medium')

    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_milestones')
    assigned_to = models.ForeignKey(User, on_delete=models.CASCADE, related_name='assigned_milestones', null=True, blank=True)

    completion_date = models.DateField(null=True, blank=True)
    completion_notes = models.TextField(blank=True, null=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.title} - {self.business_idea.title}"

    class Meta:
        ordering = ['due_date', '-priority']


class BusinessGoal(models.Model):
    """Long-term goals for business ideas"""

    STATUS_CHOICES = (
        ('active', 'Active'),
        ('achieved', 'Achieved'),
        ('revised', 'Revised'),
        ('abandoned', 'Abandoned'),
    )

    TIMEFRAME_CHOICES = (
        ('short_term', 'Short Term (0-3 months)'),
        ('medium_term', 'Medium Term (3-12 months)'),
        ('long_term', 'Long Term (1+ years)'),
    )

    business_idea = models.ForeignKey(BusinessIdea, on_delete=models.CASCADE, related_name='goals')
    title = models.CharField(max_length=200)
    description = models.TextField()
    timeframe = models.CharField(max_length=20, choices=TIMEFRAME_CHOICES, default='medium_term')
    target_date = models.DateField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')

    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_goals')

    achievement_date = models.DateField(null=True, blank=True)
    achievement_notes = models.TextField(blank=True, null=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.title} - {self.business_idea.title}"

    class Meta:
        ordering = ['target_date', 'timeframe']


class MentorRecommendation(models.Model):
    """System-generated mentor recommendations based on business idea progress"""

    business_idea = models.ForeignKey(BusinessIdea, on_delete=models.CASCADE, related_name='mentor_recommendations')
    mentor = models.ForeignKey('MentorProfile', on_delete=models.CASCADE, related_name='recommendations')

    match_score = models.FloatField(help_text="Score from 0-100 indicating match quality")
    match_reason = models.TextField(help_text="Explanation of why this mentor is recommended")

    # Specific areas where the mentor can help
    expertise_match = models.TextField(help_text="Specific expertise areas that match the business idea needs")

    # Status of the recommendation
    is_applied = models.BooleanField(default=False, help_text="Whether the user has applied to this mentor")
    is_matched = models.BooleanField(default=False, help_text="Whether a mentorship match has been created")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Recommendation: {self.mentor.user.username} for {self.business_idea.title}"

    class Meta:
        ordering = ['-match_score']
        unique_together = ('business_idea', 'mentor')


class BusinessAnalytics(models.Model):
    """Analytics data for business ideas"""

    business_idea = models.OneToOneField(BusinessIdea, on_delete=models.CASCADE, related_name='analytics')

    # Progress metrics
    progress_rate = models.FloatField(default=0.0, help_text="Progress rate (updates per month)")
    milestone_completion_rate = models.FloatField(default=0.0, help_text="Percentage of milestones completed on time")
    goal_achievement_rate = models.FloatField(default=0.0, help_text="Percentage of goals achieved")

    # Engagement metrics
    team_size = models.IntegerField(default=1, help_text="Number of team members (owner + collaborators)")
    mentor_engagement = models.FloatField(default=0.0, help_text="Mentor engagement score (0-100)")

    # Comparative metrics
    industry_percentile = models.FloatField(default=0.0, help_text="Percentile rank within same industry")
    stage_percentile = models.FloatField(default=0.0, help_text="Percentile rank within same stage")

    # Last calculated
    last_calculated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Analytics for {self.business_idea.title}"
