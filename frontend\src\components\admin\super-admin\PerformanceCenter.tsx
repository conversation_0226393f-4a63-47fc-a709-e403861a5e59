import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Zap, Cpu, HardDrive, Activity, Network, Clock,
  TrendingUp, TrendingDown, RefreshCw, BarChart3,
  Monitor, Database, Globe, AlertTriangle, CheckCircle
} from 'lucide-react';
import AuthenticatedLayout from '../../layout/AuthenticatedLayout';
import { getAuthToken } from '../../../services/api';

interface PerformanceMetric {
  timestamp: string;
  metric_value: number;
}

interface CurrentMetrics {
  cpu_usage: number;
  memory_usage: number;
  disk_io: {
    read_bytes: number;
    write_bytes: number;
    read_count: number;
    write_count: number;
  } | null;
  network_io: {
    bytes_sent: number;
    bytes_recv: number;
    packets_sent: number;
    packets_recv: number;
  } | null;
}

interface PerformanceSummary {
  avg_cpu_1h: number;
  avg_memory_1h: number;
  avg_api_response_1h: number;
  peak_cpu_24h: number;
  peak_memory_24h: number;
}

const PerformanceCenter: React.FC = () => {
  const { t } = useTranslation();
  const [currentMetrics, setCurrentMetrics] = useState<CurrentMetrics | null>(null);
  const [performanceSummary, setPerformanceSummary] = useState<PerformanceSummary | null>(null);
  const [cpuData, setCpuData] = useState<PerformanceMetric[]>([]);
  const [memoryData, setMemoryData] = useState<PerformanceMetric[]>([]);
  const [apiData, setApiData] = useState<PerformanceMetric[]>([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'cpu' | 'memory' | 'network' | 'api'>('overview');
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    fetchPerformanceData();
    
    if (autoRefresh) {
      const interval = setInterval(fetchPerformanceData, 30000); // Refresh every 30 seconds
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  const fetchPerformanceData = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/superadmin/system/performance/', {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setCurrentMetrics(data.current_metrics);
        setPerformanceSummary(data.performance_summary);
        setCpuData(data.historical_data?.cpu_usage || []);
        setMemoryData(data.historical_data?.memory_usage || []);
        setApiData(data.historical_data?.api_response_time || []);
      } else {
        console.error('Failed to fetch performance data:', response.status);
        // Set empty data on error
        setCurrentMetrics(null);
        setPerformanceSummary(null);
        setCpuData([]);
        setMemoryData([]);
        setApiData([]);
      }
    } catch (error) {
      console.error('Failed to fetch performance data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStatusColor = (value: number, thresholds: { warning: number; critical: number }) => {
    if (value >= thresholds.critical) return 'text-red-400';
    if (value >= thresholds.warning) return 'text-yellow-400';
    return 'text-green-400';
  };

  const getStatusIcon = (value: number, thresholds: { warning: number; critical: number }) => {
    if (value >= thresholds.critical) return <AlertTriangle className="w-4 h-4" />;
    if (value >= thresholds.warning) return <AlertTriangle className="w-4 h-4" />;
    return <CheckCircle className="w-4 h-4" />;
  };

  const tabs = [
    { id: 'overview', name: 'Overview', icon: <Monitor className="w-4 h-4" /> },
    { id: 'cpu', name: 'CPU', icon: <Cpu className="w-4 h-4" /> },
    { id: 'memory', name: 'Memory', icon: <HardDrive className="w-4 h-4" /> },
    { id: 'network', name: 'Network', icon: <Network className="w-4 h-4" /> },
    { id: 'api', name: 'API Performance', icon: <Globe className="w-4 h-4" /> }
  ];

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Current Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <Cpu className="w-5 h-5 text-blue-400" />
              <span className="text-sm text-gray-400">CPU Usage</span>
            </div>
            {getStatusIcon(currentMetrics?.cpu_usage || 0, { warning: 70, critical: 90 })}
          </div>
          <div className={`text-2xl font-bold ${getStatusColor(currentMetrics?.cpu_usage || 0, { warning: 70, critical: 90 })}`}>
            {currentMetrics?.cpu_usage?.toFixed(1) || 0}%
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <HardDrive className="w-5 h-5 text-green-400" />
              <span className="text-sm text-gray-400">Memory Usage</span>
            </div>
            {getStatusIcon(currentMetrics?.memory_usage || 0, { warning: 80, critical: 95 })}
          </div>
          <div className={`text-2xl font-bold ${getStatusColor(currentMetrics?.memory_usage || 0, { warning: 80, critical: 95 })}`}>
            {currentMetrics?.memory_usage?.toFixed(1) || 0}%
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <div className="flex items-center gap-2 mb-2">
            <Network className="w-5 h-5 text-purple-400" />
            <span className="text-sm text-gray-400">Network I/O</span>
          </div>
          <div className="text-lg font-bold text-white">
            ↑ {formatBytes(currentMetrics?.network_io?.bytes_sent || 0)}
          </div>
          <div className="text-sm text-gray-400">
            ↓ {formatBytes(currentMetrics?.network_io?.bytes_recv || 0)}
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <div className="flex items-center gap-2 mb-2">
            <Activity className="w-5 h-5 text-orange-400" />
            <span className="text-sm text-gray-400">Disk I/O</span>
          </div>
          <div className="text-lg font-bold text-white">
            R: {formatBytes(currentMetrics?.disk_io?.read_bytes || 0)}
          </div>
          <div className="text-sm text-gray-400">
            W: {formatBytes(currentMetrics?.disk_io?.write_bytes || 0)}
          </div>
        </div>
      </div>

      {/* Performance Summary */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">Performance Summary</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-400">{performanceSummary?.avg_cpu_1h?.toFixed(1) || 0}%</div>
            <div className="text-sm text-gray-400">Avg CPU (1h)</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-400">{performanceSummary?.avg_memory_1h?.toFixed(1) || 0}%</div>
            <div className="text-sm text-gray-400">Avg Memory (1h)</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-400">{performanceSummary?.avg_api_response_1h?.toFixed(1) || 0}s</div>
            <div className="text-sm text-gray-400">Avg API Response (1h)</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-400">{performanceSummary?.peak_cpu_24h?.toFixed(1) || 0}%</div>
            <div className="text-sm text-gray-400">Peak CPU (24h)</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-400">{performanceSummary?.peak_memory_24h?.toFixed(1) || 0}%</div>
            <div className="text-sm text-gray-400">Peak Memory (24h)</div>
          </div>
        </div>
      </div>

      {/* Charts Placeholder */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">Performance Trends (Last 24 Hours)</h3>
        <div className="h-64 flex items-center justify-center border-2 border-dashed border-gray-600 rounded-lg">
          <div className="text-center">
            <BarChart3 className="w-12 h-12 text-gray-500 mx-auto mb-2" />
            <p className="text-gray-500">Performance charts would go here</p>
            <p className="text-sm text-gray-600">CPU: {cpuData.length} points, Memory: {memoryData.length} points, API: {apiData.length} points</p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderDetailedView = (title: string, data: PerformanceMetric[], unit: string, color: string) => (
    <div className="space-y-6">
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">{title} - Last 24 Hours</h3>
        <div className="h-64 flex items-center justify-center border-2 border-dashed border-gray-600 rounded-lg">
          <div className="text-center">
            <TrendingUp className={`w-12 h-12 ${color} mx-auto mb-2`} />
            <p className="text-gray-500">Detailed {title.toLowerCase()} chart would go here</p>
            <p className="text-sm text-gray-600">Showing {data.length} data points</p>
          </div>
        </div>
      </div>

      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">Recent Data Points</h3>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b border-gray-700">
                <th className="text-left py-2 text-gray-400">Time</th>
                <th className="text-right py-2 text-gray-400">Value</th>
              </tr>
            </thead>
            <tbody>
              {data.slice(-10).map((point, index) => (
                <tr key={index} className="border-b border-gray-800">
                  <td className="py-2 text-white">{new Date(point.timestamp).toLocaleTimeString()}</td>
                  <td className="py-2 text-right text-white">{point.metric_value.toFixed(2)} {unit}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  return (
    <AuthenticatedLayout>
      <div className="text-white">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-2">
            <Zap className="w-8 h-8 text-yellow-400" />
            <h1 className="text-3xl font-bold text-white">
              {t('superAdmin.performance.title', 'Performance Center')}
            </h1>
          </div>
          <p className="text-gray-400">
            {t('superAdmin.performance.subtitle', 'Monitor system performance and resource usage')}
          </p>
        </div>

        {/* Controls */}
        <div className="flex flex-wrap items-center justify-between gap-4 mb-8">
          <div className="flex items-center gap-4">
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
                className="rounded border-gray-600 bg-gray-700 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-300">Auto-refresh (30s)</span>
            </label>
          </div>

          <button
            onClick={fetchPerformanceData}
            disabled={loading}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors disabled:opacity-50"
          >
            {loading ? <RefreshCw className="w-4 h-4 animate-spin" /> : <RefreshCw className="w-4 h-4" />}
            Refresh
          </button>
        </div>

        {/* Tabs */}
        <div className="flex flex-wrap gap-2 mb-8 border-b border-gray-700">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center gap-2 px-4 py-2 rounded-t-lg transition-colors ${
                activeTab === tab.id
                  ? 'bg-yellow-600 text-white border-b-2 border-yellow-400'
                  : 'text-gray-400 hover:text-white hover:bg-gray-800'
              }`}
            >
              {tab.icon}
              {tab.name}
            </button>
          ))}
        </div>

        {/* Content */}
        {loading && activeTab === 'overview' ? (
          <div className="flex items-center justify-center py-12">
            <RefreshCw className="w-8 h-8 animate-spin text-yellow-400" />
            <span className="ml-2 text-gray-400">Loading performance data...</span>
          </div>
        ) : (
          <>
            {activeTab === 'overview' && renderOverview()}
            {activeTab === 'cpu' && renderDetailedView('CPU Usage', cpuData, '%', 'text-blue-400')}
            {activeTab === 'memory' && renderDetailedView('Memory Usage', memoryData, '%', 'text-green-400')}
            {activeTab === 'network' && renderDetailedView('Network Performance', [], 'MB/s', 'text-purple-400')}
            {activeTab === 'api' && renderDetailedView('API Response Time', apiData, 's', 'text-orange-400')}
          </>
        )}
      </div>
    </AuthenticatedLayout>
  );
};

export default PerformanceCenter;
