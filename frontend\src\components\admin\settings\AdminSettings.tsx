import React, { useState } from 'react';
import { DashboardLayout } from '../dashboard';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';
import {
  Settings,
  Save,
  RefreshCw,
  Globe,
  Mail,
  Shield,
  Database,
  Bell,
  Palette,
  Server,
  Key,
  Users,
  MessageSquare
} from 'lucide-react';

interface SettingsSection {
  id: string;
  title: string;
  icon: React.ReactNode;
  description: string;
}

const AdminSettings: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [activeSection, setActiveSection] = useState('general');
  const [isLoading, setIsLoading] = useState(false);
  const [settings, setSettings] = useState({
    // General Settings
    siteName: t("admin.business.incubator.platform", "Business Incubator Platform"),
    siteDescription: 'A platform for entrepreneurs and business development',
    adminEmail: '<EMAIL>',
    timezone: "UTC",
    language: 'en',

    // Security Settings
    sessionTimeout: 30,
    maxLoginAttempts: 5,
    passwordMinLength: 8,
    requireTwoFactor: false,

    // Email Settings
    smtpHost: 'smtp.example.com',
    smtpPort: 587,
    smtpUsername: '',
    smtpPassword: '',
    emailFromAddress: '<EMAIL>',

    // Notification Settings
    emailNotifications: true,
    pushNotifications: false,
    maintenanceMode: false,

    // API Settings
    apiRateLimit: 1000,
    apiTimeout: 30,
    enableApiLogging: true,

    // Database Settings
    backupFrequency: 'daily',
    retentionDays: 30,
    enableQueryLogging: false
  });

  const sections: SettingsSection[] = [
    {
      id: 'general',
      title: t('admin.generalSettings'),
      icon: <Settings size={20} />,
      description: t("admin.basic.site.configuration", "Basic site configuration and preferences")
    },
    {
      id: 'security',
      title: t('admin.securitySettings'),
      icon: <Shield size={20} />,
      description: t("admin.authentication.and.security", "Authentication and security policies")
    },
    {
      id: 'email',
      title: t('admin.emailSettings'),
      icon: <Mail size={20} />,
      description: t("admin.smtp.configuration.and", "SMTP configuration and email templates")
    },
    {
      id: 'notifications',
      title: t('admin.notificationSettings'),
      icon: <Bell size={20} />,
      description: t("admin.system.notifications.and", "System notifications and alerts")
    },
    {
      id: 'api',
      title: t('admin.apiSettings'),
      icon: <Server size={20} />,
      description: t("admin.api.configuration.and", "API configuration and rate limiting")
    },
    {
      id: 'database',
      title: t('admin.databaseSettings'),
      icon: <Database size={20} />,
      description: t("admin.database.backup.and", "Database backup and maintenance")
    }
  ];

  const handleSave = async () => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      // Handle save logic here
      console.log("Settings saved:", settings);
    } catch (error) {
      console.error('Error saving settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          {t('admin.siteName')}
        </label>
        <input
          type="text"
          value={settings.siteName}
          onChange={(e) => handleInputChange('siteName', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          {t('admin.siteDescription')}
        </label>
        <textarea
          value={settings.siteDescription}
          onChange={(e) => handleInputChange('siteDescription', e.target.value)}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('admin.adminEmail')}
          </label>
          <input
            type="email"
            value={settings.adminEmail}
            onChange={(e) => handleInputChange('adminEmail', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('admin.timezone')}
          </label>
          <select
            value={settings.timezone}
            onChange={(e) => handleInputChange('timezone', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="UTC">{t("admin.utc", "UTC")}</option>
            <option value="America/New_York">{t("admin.eastern.time", "Eastern Time")}</option>
            <option value="America/Los_Angeles">{t("admin.pacific.time", "Pacific Time")}</option>
            <option value="Europe/London">{t("admin.london", "London")}</option>
            <option value="Asia/Tokyo">{t("admin.tokyo", "Tokyo")}</option>
          </select>
        </div>
      </div>
    </div>
  );

  const renderSecuritySettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('admin.sessionTimeout')} (minutes)
          </label>
          <input
            type="number"
            value={settings.sessionTimeout}
            onChange={(e) => handleInputChange('sessionTimeout', parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('admin.maxLoginAttempts')}
          </label>
          <input
            type="number"
            value={settings.maxLoginAttempts}
            onChange={(e) => handleInputChange('maxLoginAttempts', parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>
      </div>

      <div>
        <label className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
          <input
            type="checkbox"
            checked={settings.requireTwoFactor}
            onChange={(e) => handleInputChange('requireTwoFactor', e.target.checked)}
            className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
          />
          <span className={`${isRTL ? 'mr-2' : 'ml-2'} text-sm text-gray-700 dark:text-gray-300`}>
            {t('admin.requireTwoFactor')}
          </span>
        </label>
      </div>
    </div>
  );

  const renderEmailSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('admin.smtpHost')}
          </label>
          <input
            type="text"
            value={settings.smtpHost}
            onChange={(e) => handleInputChange('smtpHost', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('admin.smtpPort')}
          </label>
          <input
            type="number"
            value={settings.smtpPort}
            onChange={(e) => handleInputChange('smtpPort', parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          {t('admin.emailFromAddress')}
        </label>
        <input
          type="email"
          value={settings.emailFromAddress}
          onChange={(e) => handleInputChange('emailFromAddress', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
        />
      </div>
    </div>
  );

  const renderNotificationSettings = () => (
    <div className="space-y-6">
      <div>
        <label className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
          <input
            type="checkbox"
            checked={settings.emailNotifications}
            onChange={(e) => handleInputChange('emailNotifications', e.target.checked)}
            className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
          />
          <span className={`${isRTL ? 'mr-2' : 'ml-2'} text-sm text-gray-700 dark:text-gray-300`}>
            {t('admin.emailNotifications')}
          </span>
        </label>
      </div>

      <div>
        <label className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
          <input
            type="checkbox"
            checked={settings.maintenanceMode}
            onChange={(e) => handleInputChange('maintenanceMode', e.target.checked)}
            className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
          />
          <span className={`${isRTL ? 'mr-2' : 'ml-2'} text-sm text-gray-700 dark:text-gray-300`}>
            {t('admin.maintenanceMode')}
          </span>
        </label>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeSection) {
      case 'general':
        return renderGeneralSettings();
      case 'security':
        return renderSecuritySettings();
      case 'email':
        return renderEmailSettings();
      case 'notifications':
        return renderNotificationSettings();
      default:
        return renderGeneralSettings();
    }
  };

  return (
    <DashboardLayout currentPage="settings">
      <div className="space-y-6">
        {/* Header */}
        <div className={`flex flex-col sm:flex-row sm:items-center sm:justify-between ${isRTL ? 'text-right' : 'text-left'}`}>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {t('admin.settings')}
            </h1>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {t('admin.settingsDescription')}
            </p>
          </div>
          <button
            onClick={handleSave}
            disabled={isLoading}
            className={`inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 disabled:opacity-50 ${isRTL ? "flex-row-reverse" : ""}`}
          >
            {isLoading ? (
              <RefreshCw size={16} className={`${isRTL ? 'ml-2' : 'mr-2'} animate-spin`} />
            ) : (
              <Save size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
            )}
            {t('common.save')}
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <nav className="space-y-1">
              {sections.map((section) => (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                    activeSection === section.id
                      ? 'bg-purple-100 text-purple-700 dark:bg-purple-900 dark:text-purple-200'
                      : 'text-gray-600 hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-gray-700'}
                  } ${isRTL ? 'text-right' : 'text-left'}`}
                >
                  <span className={isRTL ? 'ml-3' : 'mr-3'}>{section.icon}</span>
                  {section.title}
                </button>
              ))}
            </nav>
          </div>

          {/* Content */}
          <div className="lg:col-span-3">
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
              <div className="mb-6">
                <h2 className="text-lg font-medium text-gray-900 dark:text-white">
                  {sections.find(s => s.id === activeSection)?.title}
                </h2>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  {sections.find(s => s.id === activeSection)?.description}
                </p>
              </div>
              {renderContent()}
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default AdminSettings;
