/**
 * Template Download Utilities
 * Handles downloading templates in various formats
 */

import { BusinessPlanTemplate } from '../services/businessPlanApi';
import { businessPlanTemplatesAPI } from '../services/templateCustomizationApi';

export interface DownloadOptions {
  format: 'json' | 'pdf' | 'docx' | 'txt';
  includeInstructions?: boolean;
  includeExamples?: boolean;
}

/**
 * Download a template in the specified format
 */
export const downloadTemplate = async (
  templateId: string | number,
  options: DownloadOptions = { format: 'json' }
): Promise<void> => {
  try {
    const numericId = typeof templateId === 'string' ? parseInt(templateId) : templateId;
    
    if (isNaN(numericId)) {
      throw new Error('Invalid template ID');
    }

    // Get template data
    const template = await businessPlanTemplatesAPI.getTemplate(numericId);
    
    if (!template) {
      throw new Error('Template not found');
    }

    switch (options.format) {
      case 'json':
        downloadAsJSON(template, options);
        break;
      case 'pdf':
        await downloadAsPDF(template, options);
        break;
      case 'docx':
        await downloadAsDocx(template, options);
        break;
      case 'txt':
        downloadAsText(template, options);
        break;
      default:
        throw new Error(`Unsupported format: ${options.format}`);
    }
  } catch (error) {
    console.error('Error downloading template:', error);
    throw error;
  }
};

/**
 * Download template as JSON
 */
const downloadAsJSON = (template: BusinessPlanTemplate, options: DownloadOptions): void => {
  const templateData = {
    id: template.id,
    name: template.name,
    description: template.description,
    industry: template.industry,
    template_type: template.template_type,
    sections: template.sections,
    created_at: template.created_at,
    ...(options.includeInstructions && {
      instructions: generateInstructions(template)
    }),
    ...(options.includeExamples && {
      examples: generateExamples(template)
    })
  };

  const dataStr = JSON.stringify(templateData, null, 2);
  const dataBlob = new Blob([dataStr], { type: 'application/json' });
  const url = URL.createObjectURL(dataBlob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = `${sanitizeFilename(template.name)}.json`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

/**
 * Download template as PDF (placeholder - would need PDF generation library)
 */
const downloadAsPDF = async (template: BusinessPlanTemplate, options: DownloadOptions): Promise<void> => {
  // For now, create a simple text-based PDF content
  const content = generateTextContent(template, options);
  
  // In a real implementation, you would use a library like jsPDF or PDFKit
  // For now, we'll download as text with PDF extension
  const dataBlob = new Blob([content], { type: 'text/plain' });
  const url = URL.createObjectURL(dataBlob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = `${sanitizeFilename(template.name)}.pdf`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

/**
 * Download template as DOCX (placeholder - would need DOCX generation library)
 */
const downloadAsDocx = async (template: BusinessPlanTemplate, options: DownloadOptions): Promise<void> => {
  // For now, create a simple text-based content
  const content = generateTextContent(template, options);
  
  // In a real implementation, you would use a library like docx or officegen
  // For now, we'll download as text with DOCX extension
  const dataBlob = new Blob([content], { type: 'text/plain' });
  const url = URL.createObjectURL(dataBlob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = `${sanitizeFilename(template.name)}.docx`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

/**
 * Download template as plain text
 */
const downloadAsText = (template: BusinessPlanTemplate, options: DownloadOptions): void => {
  const content = generateTextContent(template, options);
  
  const dataBlob = new Blob([content], { type: 'text/plain' });
  const url = URL.createObjectURL(dataBlob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = `${sanitizeFilename(template.name)}.txt`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

/**
 * Generate text content for the template
 */
const generateTextContent = (template: BusinessPlanTemplate, options: DownloadOptions): string => {
  let content = `${template.name}\n`;
  content += `${'='.repeat(template.name.length)}\n\n`;
  content += `Description: ${template.description}\n`;
  content += `Industry: ${template.industry}\n`;
  content += `Type: ${template.template_type}\n\n`;

  if (template.sections && typeof template.sections === 'object') {
    content += 'SECTIONS:\n';
    content += '=========\n\n';

    Object.entries(template.sections).forEach(([key, section]: [string, any]) => {
      content += `${section.title || key}\n`;
      content += `${'-'.repeat((section.title || key).length)}\n`;
      
      if (section.description) {
        content += `Description: ${section.description}\n`;
      }
      
      if (section.guiding_questions && Array.isArray(section.guiding_questions)) {
        content += '\nGuiding Questions:\n';
        section.guiding_questions.forEach((question: string, index: number) => {
          content += `${index + 1}. ${question}\n`;
        });
      }
      
      if (options.includeInstructions && section.ai_prompt) {
        content += `\nAI Prompt: ${section.ai_prompt}\n`;
      }
      
      content += '\n';
    });
  }

  if (options.includeInstructions) {
    content += '\nINSTRUCTIONS:\n';
    content += '=============\n';
    content += generateInstructions(template);
  }

  if (options.includeExamples) {
    content += '\nEXAMPLES:\n';
    content += '=========\n';
    content += generateExamples(template);
  }

  return content;
};

/**
 * Generate instructions for using the template
 */
const generateInstructions = (template: BusinessPlanTemplate): string => {
  return `
1. Review each section of this ${template.template_type} business plan template
2. Fill in the content for each section based on your business
3. Use the guiding questions to help structure your responses
4. Customize the sections to fit your specific industry (${template.industry})
5. Review and refine your content before finalizing
6. Consider getting feedback from mentors or advisors

Tips:
- Be specific and detailed in your responses
- Use data and research to support your claims
- Keep your target audience in mind
- Update the plan regularly as your business evolves
`;
};

/**
 * Generate examples for the template
 */
const generateExamples = (template: BusinessPlanTemplate): string => {
  return `
This template is designed for ${template.industry} businesses.

Example use cases:
- Startups seeking funding
- Existing businesses planning expansion
- Entrepreneurs validating their business model
- Students working on business plan assignments

For best results:
- Adapt the language to your specific business
- Include relevant industry metrics and benchmarks
- Provide realistic financial projections
- Highlight your unique value proposition
`;
};

/**
 * Sanitize filename for download
 */
const sanitizeFilename = (filename: string): string => {
  return filename
    .replace(/[^a-z0-9]/gi, '_')
    .replace(/_+/g, '_')
    .replace(/^_|_$/g, '')
    .toLowerCase();
};

/**
 * Get available download formats
 */
export const getAvailableFormats = (): Array<{ value: string; label: string; description: string }> => {
  return [
    {
      value: 'json',
      label: 'JSON',
      description: 'Machine-readable format for developers'
    },
    {
      value: 'txt',
      label: 'Text',
      description: 'Plain text format for easy editing'
    },
    {
      value: 'pdf',
      label: 'PDF',
      description: 'Formatted document for printing'
    },
    {
      value: 'docx',
      label: 'Word Document',
      description: 'Microsoft Word compatible format'
    }
  ];
};
