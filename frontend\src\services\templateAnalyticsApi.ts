/**
 * Template Analytics API Service
 * Enhanced analytics service for template performance tracking
 */

import { apiRequest } from './api';

export interface TemplateAnalytics {
  id: string;
  name: string;
  category: string;
  usage_count: number;
  completion_rate: number;
  average_rating: number;
  total_ratings: number;
  success_rate: number;
  average_completion_time: number;
  user_satisfaction: number;
  conversion_rate: number;
  bounce_rate: number;
  retention_rate: number;
  created_at: string;
  last_used: string;
}

export interface TemplateUsageTrend {
  date: string;
  views: number;
  selections: number;
  completions: number;
  ratings: number;
  average_rating: number;
}

export interface CategoryAnalytics {
  category: string;
  template_count: number;
  total_usage: number;
  average_rating: number;
  completion_rate: number;
  growth_rate: number;
  market_share: number;
}

export interface UserSegmentAnalytics {
  segment: string;
  user_count: number;
  template_preferences: string[];
  completion_rate: number;
  satisfaction_score: number;
  retention_rate: number;
}

export interface TemplatePerformanceMetrics {
  template_id: string;
  performance_score: number;
  ranking: number;
  improvement_suggestions: string[];
  competitive_analysis: {
    similar_templates: string[];
    performance_comparison: number;
  };
  user_feedback: {
    positive_feedback: string[];
    negative_feedback: string[];
    improvement_requests: string[];
  };
}

export interface AnalyticsDashboardData {
  overview: {
    total_templates: number;
    total_usage: number;
    average_rating: number;
    completion_rate: number;
    growth_rate: number;
    active_users: number;
  };
  top_templates: TemplateAnalytics[];
  usage_trends: TemplateUsageTrend[];
  category_performance: CategoryAnalytics[];
  user_segments: UserSegmentAnalytics[];
  performance_metrics: TemplatePerformanceMetrics[];
  recommendations: {
    trending_templates: string[];
    underperforming_templates: string[];
    optimization_opportunities: string[];
  };
}

export interface AnalyticsFilters {
  timeRange: '7d' | '30d' | '90d' | '1y' | 'all';
  category?: string;
  templateId?: string;
  userSegment?: string;
  includeInactive?: boolean;
}

export const templateAnalyticsAPI = {
  /**
   * Get comprehensive analytics dashboard data
   */
  getDashboardData: async (filters: AnalyticsFilters): Promise<AnalyticsDashboardData> => {
    try {
      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, value.toString());
        }
      });

      return await apiRequest<AnalyticsDashboardData>(
        `/api/incubator/template-analytics/dashboard/?${params.toString()}`
      );
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      throw new Error('Failed to load analytics dashboard data. Please try again later.');
    }
  },

  /**
   * Get analytics for a specific template
   */
  getTemplateAnalytics: async (templateId: string, timeRange: string): Promise<TemplateAnalytics> => {
    try {
      return await apiRequest<TemplateAnalytics>(
        `/api/incubator/template-analytics/template/${templateId}/?timeRange=${timeRange}`
      );
    } catch (error) {
      console.error('Error fetching template analytics:', error);
      throw error;
    }
  },

  /**
   * Get usage trends for templates
   */
  getUsageTrends: async (filters: AnalyticsFilters): Promise<TemplateUsageTrend[]> => {
    try {
      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, value.toString());
        }
      });

      return await apiRequest<TemplateUsageTrend[]>(
        `/api/incubator/template-analytics/trends/?${params.toString()}`
      );
    } catch (error) {
      console.error('Error fetching usage trends:', error);
      return [];
    }
  },

  /**
   * Get category performance analytics
   */
  getCategoryAnalytics: async (timeRange: string): Promise<CategoryAnalytics[]> => {
    try {
      return await apiRequest<CategoryAnalytics[]>(
        `/api/incubator/template-analytics/categories/?timeRange=${timeRange}`
      );
    } catch (error) {
      console.error('Error fetching category analytics:', error);
      return [];
    }
  },

  /**
   * Get user segment analytics
   */
  getUserSegmentAnalytics: async (timeRange: string): Promise<UserSegmentAnalytics[]> => {
    try {
      return await apiRequest<UserSegmentAnalytics[]>(
        `/api/incubator/template-analytics/user-segments/?timeRange=${timeRange}`
      );
    } catch (error) {
      console.error('Error fetching user segment analytics:', error);
      return [];
    }
  },



  /**
   * Get template performance metrics
   */
  getPerformanceMetrics: async (templateId?: string): Promise<TemplatePerformanceMetrics[]> => {
    try {
      const url = templateId
        ? `/api/incubator/template-analytics/performance/${templateId}/`
        : '/api/incubator/template-analytics/performance/';

      return await apiRequest<TemplatePerformanceMetrics[]>(url);
    } catch (error) {
      console.error('Error fetching performance metrics:', error);
      return [];
    }
  },

  /**
   * Export analytics data
   */
  exportAnalytics: async (filters: AnalyticsFilters, format: 'csv' | 'xlsx' | 'pdf'): Promise<Blob> => {
    try {
      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, value.toString());
        }
      });
      params.append('format', format);

      const response = await fetch(
        `/api/incubator/template-analytics/export/?${params.toString()}`,
        {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error('Export failed');
      }

      return await response.blob();
    } catch (error) {
      console.error('Error exporting analytics:', error);
      throw error;
    }
  }
};


