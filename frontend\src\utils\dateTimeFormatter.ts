import { format, formatDistance, formatRelative, isValid } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';
import i18n from 'i18next';

// Map of language codes to date-fns locales
const LOCALE_MAP = {
  en: enUS,
  ar: ar,
};

/**
 * Get the appropriate date-fns locale for the current language
 * @returns date-fns locale object
 */
export const getLocale = () => {
  const language = i18n.language || 'en';
  return LOCALE_MAP[language as keyof typeof LOCALE_MAP] || enUS;
};

/**
 * Format a date according to the user's locale and preferences
 * @param date Date to format (string or Date object)
 * @param formatString Optional custom format string
 * @returns Formatted date string
 */
export const formatDate = (
  date: string | Date | number,
  formatString?: string
): string => {
  if (!date) return '';

  // Convert string to Date if needed
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  // Check if date is valid
  if (!isValid(dateObj)) {
    console.warn('Invalid date:', date);
    return String(date);
  }

  try {
    // Get current language and set format preferences
    const currentLang = i18n.language || 'en';
    const defaultDateFormat = currentLang === 'ar' ? 'dd/MM/yyyy' : 'MM/dd/yyyy';

    // Use provided format string or default format
    const dateFormat = formatString || defaultDateFormat;
    
    // Format the date using date-fns with the appropriate locale
    return format(dateObj, dateFormat, { locale: getLocale() });
  } catch (error) {
    console.error('Error formatting date:', error);
    return String(date);
  }
};

/**
 * Format a time according to the user's locale and preferences
 * @param date Date to format (string or Date object)
 * @param formatString Optional custom format string
 * @returns Formatted time string
 */
export const formatTime = (
  date: string | Date | number,
  formatString?: string
): string => {
  if (!date) return '';

  // Convert string to Date if needed
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  // Check if date is valid
  if (!isValid(dateObj)) {
    console.warn('Invalid date for time formatting:', date);
    return String(date);
  }

  try {
    // Get current language and set format preferences
    const currentLang = i18n.language || 'en';
    const defaultTimeFormat = currentLang === 'ar' ? 'HH:mm' : 'h:mm a';

    // Use provided format string or default format
    const timeFormat = formatString || defaultTimeFormat;
    
    // Format the time using date-fns with the appropriate locale
    return format(dateObj, timeFormat, { locale: getLocale() });
  } catch (error) {
    console.error('Error formatting time:', error);
    return String(date);
  }
};

/**
 * Format a date and time according to the user's locale and preferences
 * @param date Date to format (string or Date object)
 * @param formatString Optional custom format string
 * @returns Formatted date and time string
 */
export const formatDateTime = (
  date: string | Date | number,
  formatString?: string
): string => {
  if (!date) return '';

  // Convert string to Date if needed
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  // Check if date is valid
  if (!isValid(dateObj)) {
    console.warn('Invalid date for datetime formatting:', date);
    return String(date);
  }

  try {
    // Get current language and set format preferences
    const currentLang = i18n.language || 'en';
    const defaultDateFormat = currentLang === 'ar' ? 'dd/MM/yyyy' : 'MM/dd/yyyy';
    const defaultTimeFormat = currentLang === 'ar' ? 'HH:mm' : 'h:mm a';

    // Use provided format string or combine date and time formats
    const dateTimeFormat = formatString || `${defaultDateFormat} ${defaultTimeFormat}`;
    
    // Format the datetime using date-fns with the appropriate locale
    return format(dateObj, dateTimeFormat, { locale: getLocale() });
  } catch (error) {
    console.error('Error formatting datetime:', error);
    return String(date);
  }
};

/**
 * Format a relative time (e.g., "2 hours ago")
 * @param date Date to format (string or Date object)
 * @param baseDate Base date to compare against (defaults to now)
 * @returns Formatted relative time string
 */
export const formatRelativeTime = (
  date: string | Date | number,
  baseDate: Date = new Date()
): string => {
  if (!date) return '';

  // Convert string to Date if needed
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  // Check if date is valid
  if (!isValid(dateObj)) {
    console.warn('Invalid date for relative time formatting:', date);
    return String(date);
  }

  try {
    // Format the relative time using date-fns with the appropriate locale
    return formatDistance(dateObj, baseDate, {
      addSuffix: true,
      locale: getLocale(),
    });
  } catch (error) {
    console.error('Error formatting relative time:', error);
    return String(date);
  }
};

export default {
  formatDate,
  formatTime,
  formatDateTime,
  formatRelativeTime,
  getLocale,
};
