import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';
import { BusinessIdea } from '../../../services/incubatorApi';
import { User } from '../../../services/api';
import { Loader, Save, X, AlertCircle, CheckCircle } from 'lucide-react';

interface BusinessIdeaFormProps {
  initialData?: Partial<BusinessIdea>;
  onSubmit: (data: Partial<BusinessIdea>) => Promise<boolean>;
  onCancel: () => void;
  isSubmitting?: boolean;
  mode: 'create' | 'edit';
}

interface FormData {
  title: string;
  description: string;
  problem_statement: string;
  solution_description: string;
  target_audience: string;
  market_opportunity: string;
  business_model: string;
  current_stage: 'concept' | 'validation' | 'development' | 'scaling' | 'established';
}

interface FormErrors {
  [key: string]: string;
}

const BusinessIdeaForm: React.FC<BusinessIdeaFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isSubmitting = false,
  mode
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [formData, setFormData] = useState<FormData>({
    title: initialData?.title || '',
    description: initialData?.description || '',
    problem_statement: initialData?.problem_statement || '',
    solution_description: initialData?.solution_description || '',
    target_audience: initialData?.target_audience || '',
    market_opportunity: initialData?.market_opportunity || '',
    business_model: initialData?.business_model || '',
    current_stage: initialData?.current_stage || 'concept'
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  // Validation rules
  const validateField = (name: string, value: string): string => {
    switch (name) {
      case 'title':
        if (!value.trim()) return t('validation.required', 'This field is required');
        if (value.length < 3) return t('validation.minLength', 'Must be at least 3 characters');
        if (value.length > 200) return t('validation.maxLength', 'Must be less than 200 characters');
        break;
      case 'description':
        if (!value.trim()) return t('validation.required', 'This field is required');
        if (value.length < 10) return t('validation.minLength', 'Must be at least 10 characters');
        break;
      case 'problem_statement':
        if (!value.trim()) return t('validation.required', 'This field is required');
        if (value.length < 20) return t('validation.minLength', 'Must be at least 20 characters');
        break;
      case 'solution_description':
        if (!value.trim()) return t('validation.required', 'This field is required');
        if (value.length < 20) return t('validation.minLength', 'Must be at least 20 characters');
        break;
      case 'target_audience':
        if (!value.trim()) return t('validation.required', 'This field is required');
        if (value.length < 10) return t('validation.minLength', 'Must be at least 10 characters');
        break;
    }
    return '';
  };

  // Validate all fields
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};
    
    Object.keys(formData).forEach(key => {
      const error = validateField(key, formData[key as keyof FormData]);
      if (error) {
        newErrors[key] = error;
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // Handle field blur
  const handleBlur = (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    setTouched(prev => ({
      ...prev,
      [name]: true
    }));

    // Validate field on blur
    const error = validateField(name, value);
    setErrors(prev => ({
      ...prev,
      [name]: error
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Mark all fields as touched
    const allTouched = Object.keys(formData).reduce((acc, key) => {
      acc[key] = true;
      return acc;
    }, {} as Record<string, boolean>);
    setTouched(allTouched);

    // Validate form
    if (!validateForm()) {
      return;
    }

    // Submit form
    const success = await onSubmit(formData);
    if (success) {
      // Form will be closed by parent component
    }
  };

  const stageOptions = [
    { value: 'concept', label: t('incubator.stages.concept', 'Concept Stage') },
    { value: 'validation', label: t('incubator.stages.validation', 'Validation Stage') },
    { value: 'development', label: t('incubator.stages.development', 'Development Stage') },
    { value: 'scaling', label: t('incubator.stages.scaling', 'Scaling Stage') },
    { value: 'established', label: t('incubator.stages.established', 'Established Business') }
  ];

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <div className="bg-gray-900 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className={`flex items-center justify-between p-6 border-b border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <h2 className="text-xl font-semibold text-white">
            {mode === 'create' 
              ? t('incubator.createBusinessIdea', 'Create Business Idea')
              : t('incubator.editBusinessIdea', 'Edit Business Idea')
            }
          </h2>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-white transition-colors"
            disabled={isSubmitting}
          >
            <X size={24} />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('incubator.title', 'Title')} *
            </label>
            <input
              type="text"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              onBlur={handleBlur}
              className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white ${
                errors.title && touched.title ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder={t('incubator.titlePlaceholder', 'Enter your business idea title')}
              disabled={isSubmitting}
            />
            {errors.title && touched.title && (
              <p className="mt-1 text-sm text-red-400 flex items-center">
                <AlertCircle size={16} className="mr-1" />
                {errors.title}
              </p>
            )}
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('incubator.description', 'Description')} *
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              onBlur={handleBlur}
              rows={3}
              className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white resize-vertical ${
                errors.description && touched.description ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder={t('incubator.descriptionPlaceholder', 'Provide a brief description of your business idea')}
              disabled={isSubmitting}
            />
            {errors.description && touched.description && (
              <p className="mt-1 text-sm text-red-400 flex items-center">
                <AlertCircle size={16} className="mr-1" />
                {errors.description}
              </p>
            )}
          </div>

          {/* Problem Statement */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('incubator.problemStatement', 'Problem Statement')} *
            </label>
            <textarea
              name="problem_statement"
              value={formData.problem_statement}
              onChange={handleInputChange}
              onBlur={handleBlur}
              rows={3}
              className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white resize-vertical ${
                errors.problem_statement && touched.problem_statement ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder={t('incubator.problemPlaceholder', 'What problem does your business idea solve?')}
              disabled={isSubmitting}
            />
            {errors.problem_statement && touched.problem_statement && (
              <p className="mt-1 text-sm text-red-400 flex items-center">
                <AlertCircle size={16} className="mr-1" />
                {errors.problem_statement}
              </p>
            )}
          </div>

          {/* Solution Description */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('incubator.solutionDescription', 'Solution Description')} *
            </label>
            <textarea
              name="solution_description"
              value={formData.solution_description}
              onChange={handleInputChange}
              onBlur={handleBlur}
              rows={3}
              className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white resize-vertical ${
                errors.solution_description && touched.solution_description ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder={t('incubator.solutionPlaceholder', 'How does your idea solve the problem?')}
              disabled={isSubmitting}
            />
            {errors.solution_description && touched.solution_description && (
              <p className="mt-1 text-sm text-red-400 flex items-center">
                <AlertCircle size={16} className="mr-1" />
                {errors.solution_description}
              </p>
            )}
          </div>

          {/* Target Audience */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('incubator.targetAudience', 'Target Audience')} *
            </label>
            <textarea
              name="target_audience"
              value={formData.target_audience}
              onChange={handleInputChange}
              onBlur={handleBlur}
              rows={2}
              className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white resize-vertical ${
                errors.target_audience && touched.target_audience ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder={t('incubator.targetAudiencePlaceholder', 'Who is your target audience?')}
              disabled={isSubmitting}
            />
            {errors.target_audience && touched.target_audience && (
              <p className="mt-1 text-sm text-red-400 flex items-center">
                <AlertCircle size={16} className="mr-1" />
                {errors.target_audience}
              </p>
            )}
          </div>

          {/* Market Opportunity */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('incubator.marketOpportunity', 'Market Opportunity')}
            </label>
            <textarea
              name="market_opportunity"
              value={formData.market_opportunity}
              onChange={handleInputChange}
              onBlur={handleBlur}
              rows={2}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white resize-vertical"
              placeholder={t('incubator.marketOpportunityPlaceholder', 'What is the market opportunity?')}
              disabled={isSubmitting}
            />
          </div>

          {/* Business Model */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('incubator.businessModel', 'Business Model')}
            </label>
            <textarea
              name="business_model"
              value={formData.business_model}
              onChange={handleInputChange}
              onBlur={handleBlur}
              rows={2}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white resize-vertical"
              placeholder={t('incubator.businessModelPlaceholder', 'How will your business make money?')}
              disabled={isSubmitting}
            />
          </div>

          {/* Current Stage */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('incubator.currentStage', 'Current Stage')} *
            </label>
            <select
              name="current_stage"
              value={formData.current_stage}
              onChange={handleInputChange}
              onBlur={handleBlur}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
              disabled={isSubmitting}
            >
              {stageOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Form Actions */}
          <div className={`flex gap-4 pt-4 border-t border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <button
              type="button"
              onClick={onCancel}
              className="px-6 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors"
              disabled={isSubmitting}
            >
              {t('common.cancel', 'Cancel')}
            </button>
            <button
              type="submit"
              className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader size={16} className="animate-spin" />
                  {t('common.saving', 'Saving...')}
                </>
              ) : (
                <>
                  <Save size={16} />
                  {mode === 'create'
                    ? t('common.create', 'Create')
                    : t('common.update', 'Update')
                  }
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default BusinessIdeaForm;
