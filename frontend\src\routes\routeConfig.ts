import { ComponentType, ReactElement } from 'react';

// Define available roles in the system
export type UserRole =
  | 'super_admin'
  | 'admin'
  | 'moderator'
  | 'mentor'
  | 'investor'
  | 'user';

// Define permission levels
export type PermissionLevel = 'read' | 'write' | 'moderate' | 'admin' | 'super_admin';

// Route configuration interface
export interface RouteConfig {
  path: string;
  component: ComponentType<any>;
  exact?: boolean;
  roles?: UserRole[]; // Allowed roles for this route
  permissions?: PermissionLevel[]; // Required permission levels
  requireAuth?: boolean; // Whether authentication is required
  adminOnly?: boolean; // Shortcut for admin-only routes
  loadingMessage?: string; // Custom loading message
  redirectTo?: string; // Redirect path for unauthorized access
  children?: RouteConfig[]; // Nested routes
  layout?: 'public' | 'authenticated'; // Layout type for this route
}

// Route group configuration
export interface RouteGroup {
  name: string;
  basePath?: string;
  routes: RouteConfig[];
  defaultRole?: UserRole;
  defaultPermission?: PermissionLevel;
}

// Helper function to create route config
export const createRoute = (config: RouteConfig): RouteConfig => ({
  exact: true,
  requireAuth: true,
  loadingMessage: 'Loading...',
  ...config,
});

// Helper function to create admin route
export const createAdminRoute = (
  path: string,
  component: ComponentType<any>,
  loadingMessage?: string
): RouteConfig => createRoute({
  path,
  component,
  roles: ['admin', 'super_admin'], // Allow both admin and super_admin
  permissions: ['admin', 'super_admin'], // Allow both admin and super_admin permissions
  layout: 'authenticated',
  loadingMessage: loadingMessage || 'Loading admin panel...',
  redirectTo: '/dashboard'
});

// Helper function to create Super Admin only route
export const createSuperAdminRoute = (
  path: string,
  component: ComponentType<any>,
  loadingMessage?: string
): RouteConfig => createRoute({
  path,
  component,
  roles: ['super_admin'], // Only super_admin
  permissions: ['super_admin'], // Only super_admin permissions
  layout: 'authenticated',
  loadingMessage: loadingMessage || 'Loading Super Admin panel...',
  redirectTo: '/super_admin'
});

// Helper function to create user route
export const createUserRoute = (
  path: string,
  component: ComponentType<any>,
  loadingMessage?: string,
  allowedRoles?: UserRole[]
): RouteConfig => createRoute({
  path,
  component,
  roles: allowedRoles || ['user', 'admin', 'super_admin', 'mentor', 'investor', 'moderator'],
  permissions: ['read'],
  layout: 'authenticated',
  loadingMessage: loadingMessage || 'Loading...',
  redirectTo: '/login'
});

// Helper function to create public route
export const createPublicRoute = (
  path: string,
  component: ComponentType<any>,
  loadingMessage?: string
): RouteConfig => ({
  path,
  component,
  exact: true,
  requireAuth: false,
  layout: 'public',
  loadingMessage: loadingMessage || 'Loading...'
});

// Helper function to create auth route (login, register) - no layout wrapper
export const createAuthRoute = (
  path: string,
  component: ComponentType<any>,
  loadingMessage?: string
): RouteConfig => ({
  path,
  component,
  exact: true,
  requireAuth: false,
  layout: undefined, // No layout wrapper
  loadingMessage: loadingMessage || 'Loading...'
});

// Helper function to create role-specific route
export const createRoleRoute = (
  path: string,
  component: ComponentType<any>,
  roles: UserRole[],
  permissions?: PermissionLevel[],
  loadingMessage?: string
): RouteConfig => createRoute({
  path,
  component,
  roles,
  permissions: permissions || ['read'],
  layout: 'authenticated',
  loadingMessage: loadingMessage || 'Loading...',
  redirectTo: '/dashboard'
});

// Route validation helper
export const validateRouteAccess = (
  route: RouteConfig,
  userRoles: UserRole[],
  userPermissions: PermissionLevel[],
  isAuthenticated: boolean,
  isAdmin: boolean,
  isSuperAdmin?: boolean
): boolean => {
  const isDevelopment = process.env.NODE_ENV === 'development';

  // Check authentication requirement
  if (route.requireAuth && !isAuthenticated) {
    if (isDevelopment) {
      console.log('🚫 Route access denied - not authenticated:', route.path);
    }
    return false;
  }

  // Super Admin can access ALL routes
  if (isSuperAdmin) {
    if (isDevelopment) {
      console.log('✅ Route access granted - Super Admin bypass:', route.path);
    }
    return true;
  }

  // Check admin-only routes
  if (route.adminOnly && !isAdmin && !isSuperAdmin) {
    if (isDevelopment) {
      console.log('🚫 Route access denied - admin only:', route.path, { isAdmin, isSuperAdmin });
    }
    return false;
  }

  // Check role requirements
  if (route.roles && route.roles.length > 0) {
    const hasRequiredRole = route.roles.some(role =>
      userRoles.includes(role) ||
      (isAdmin && role === 'admin') ||
      (isSuperAdmin && (role === 'admin' || role === 'super_admin'))
    );
    if (!hasRequiredRole) {
      if (isDevelopment) {
        console.log('🚫 Route access denied - insufficient roles:', {
          path: route.path,
          requiredRoles: route.roles,
          userRoles,
          isAdmin,
          isSuperAdmin
        });
      }
      return false;
    }
  }

  // Check permission requirements
  if (route.permissions && route.permissions.length > 0) {
    const hasRequiredPermission = route.permissions.some(permission =>
      userPermissions.includes(permission) ||
      (isAdmin && permission === 'admin') ||
      (isSuperAdmin && (permission === 'admin' || permission === 'super_admin'))
    );
    if (!hasRequiredPermission) {
      if (isDevelopment) {
        console.log('🚫 Route access denied - insufficient permissions:', {
          path: route.path,
          requiredPermissions: route.permissions,
          userPermissions,
          isAdmin,
          isSuperAdmin
        });
      }
      return false;
    }
  }

  if (isDevelopment) {
    console.log('✅ Route access granted:', {
      path: route.path,
      userRoles,
      userPermissions,
      isAdmin,
      isSuperAdmin
    });
  }

  return true;
};

// Get redirect path for unauthorized access
export const getRedirectPath = (route: RouteConfig, isAuthenticated: boolean): string => {
  if (!isAuthenticated) {
    return '/login'; // ✅ UNIFIED: All users use the same login page
  }

  return route.redirectTo || '/dashboard';
};
