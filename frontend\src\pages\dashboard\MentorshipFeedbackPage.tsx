import React, { useState, useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { useAppDispatch, useAppSelector } from '../../store/hooks';
// MainLayout removed - handled by routing system
import { MentorshipFeedbackForm } from '../../components/incubator';
import { fetchMentorshipSession } from '../../store/incubatorSlice';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';

const MentorshipFeedbackPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const { sessionId } = useParams<{ sessionId: string }>();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  useEffect(() => {
    if (sessionId) {
      dispatch(fetchMentorshipSession(parseInt(sessionId)));
    }
  }, [dispatch, sessionId]);

  const handleFeedbackSuccess = () => {
    navigate(`/dashboard/mentorship/sessions/${sessionId}`);
  };

  return (
    <div className="p-6">
        {/* Back button */}
        <div className="mb-6">
          <Link
            to={`/dashboard/mentorship/sessions/${sessionId}`}
            className={`text-gray-400 hover:text-gray-300 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <ArrowLeft size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
            {t('mentorship.backToSessionDetails', 'Back to Session Details')}
          </Link>
        </div>

        <h1 className="text-2xl font-bold mb-6 text-white">{t('mentorship.provideSessionFeedback', 'Provide Session Feedback')}</h1>

        <MentorshipFeedbackForm
          sessionId={sessionId ? parseInt(sessionId) : undefined}
          onSuccess={handleFeedbackSuccess}
        />
      </div>
  );
};

export default MentorshipFeedbackPage;
