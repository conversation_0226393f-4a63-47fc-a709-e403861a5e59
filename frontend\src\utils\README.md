# Utilities

This directory contains utility functions and hooks that are used throughout the application.

## RTL Utilities

The `rtlUtils.ts` file contains utilities for working with Right-to-Left (RTL) layouts.

### useLanguageDirection

A hook that returns the current language direction ('ltr' or 'rtl') based on the current language.

```tsx
import { useLanguageDirection } from '../utils/rtlUtils';

const MyComponent = () => {
  const direction = useLanguageDirection();
  
  return <div dir={direction}>Content</div>;
};
```

### Logical CSS Properties

Functions for generating Tailwind classes with logical properties that automatically adjust based on the current language direction.

```tsx
import { getMarginClass, getPaddingClass, getBorderClass, getTextAlignClass } from '../utils/rtlUtils';

const MyComponent = () => {
  return (
    <div className={`
      ${getMarginClass('start', 4)}
      ${getPaddingClass('end', 2)}
      ${getBorderClass('start')}
      ${getTextAlignClass('start')}
    `}>
      Content
    </div>
  );
};
```

### getLogicalStyles

A function for generating CSS styles with logical properties that automatically adjust based on the current language direction.

```tsx
import { getLogicalStyles } from '../utils/rtlUtils';

const MyComponent = () => {
  const styles = getLogicalStyles({
    marginStart: '1rem',
    paddingEnd: '0.5rem',
    borderStart: '1px solid #ccc',
    textAlign: 'start',
  });
  
  return <div style={styles}>Content</div>;
};
```

## Theme Testing Utilities

The `themeTestUtils.ts` file contains utilities for testing components with theme support.

### testThemeCompliance

A function for testing if a component renders correctly in both light and dark themes.

```tsx
import { testThemeCompliance } from '../utils/themeTestUtils';

describe('MyComponent', () => {
  it('renders correctly in both themes', () => {
    const { lightRender, darkRender } = testThemeCompliance(MyComponent);
    
    // Test light theme
    expect(lightRender.getByText('Content')).toBeInTheDocument();
    
    // Test dark theme
    expect(darkRender.getByText('Content')).toBeInTheDocument();
  });
});
```

### createThemeTestComponent

A function for creating a test component with theme support.

```tsx
import { createThemeTestComponent } from '../utils/themeTestUtils';

describe('ThemeWrapper', () => {
  it('applies the correct classes', () => {
    const TestComponent = createThemeTestComponent(
      'base-class',
      'dark-class',
      'light-class'
    );
    
    const { getByTestId } = render(<TestComponent />);
    
    expect(getByTestId('theme-test-component')).toHaveClass('base-class');
  });
});
```

### checkThemeCompliance

A function for checking if a component has theme-related issues.

```tsx
import { checkThemeCompliance } from '../utils/themeTestUtils';

describe('MyComponent', () => {
  it('has no theme-related issues', () => {
    const compliance = checkThemeCompliance(MyComponent);
    
    expect(compliance.hasThemeWrapper).toBe(true);
    expect(compliance.hasDarkPrefix).toBe(false);
    expect(compliance.hasHardcodedColors).toBe(false);
    expect(compliance.hasTransitions).toBe(true);
  });
});
```

## Query Utilities

The `queryUtils.ts` file contains utilities for working with React Query.

### invalidateRelatedQueries

A function for invalidating related entity caches when an entity is modified.

```tsx
import { invalidateRelatedQueries } from '../utils/queryUtils';

const mutation = useMutation({
  mutationFn: updateUser,
  onSuccess: (data) => {
    // Invalidate related queries
    invalidateRelatedQueries(queryClient, 'users', data.id);
  },
});
```

### optimisticListUpdate

A function for optimistically updating a list query.

```tsx
import { optimisticListUpdate } from '../utils/queryUtils';

const mutation = useMutation({
  mutationFn: createPost,
  onMutate: async (newPost) => {
    // Cancel outgoing refetches
    await queryClient.cancelQueries({ queryKey: ['posts'] });
    
    // Snapshot the previous value
    const previousPosts = queryClient.getQueryData(['posts']);
    
    // Optimistically update the list
    optimisticListUpdate(
      queryClient,
      ['posts'],
      newPost,
      'add'
    );
    
    // Return a context object with the snapshot
    return { previousPosts };
  },
  onError: (err, newPost, context) => {
    // If the mutation fails, use the context to roll back
    queryClient.setQueryData(['posts'], context.previousPosts);
  },
});
```

### getEntityCacheConfig

A function for getting entity-specific cache configuration.

```tsx
import { getEntityCacheConfig } from '../utils/queryUtils';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: getEntityCacheConfig('posts').staleTime,
      gcTime: getEntityCacheConfig('posts').gcTime,
    },
  },
});
```
