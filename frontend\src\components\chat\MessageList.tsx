import React from 'react';
import { Sparkles } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { RTLText, RTLFlex } from '../common';
import { ChatMessage } from '../../services/chatApi';
interface MessageListProps {
  messages: ChatMessage[];
  isLoading: boolean;
  error: string | null;
}

const MessageList: React.FC<MessageListProps> = ({ messages, isLoading, error  }) => {
  const { t } = useTranslation();
  const { language, isRTL } = useLanguage();

  return (
    <div
      id="messages-container"
      className="h-[500px] overflow-y-auto p-4 space-y-4"
    >
      {messages.map((message, index) => (
        <div
          key={index}
          className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
        >
          <div
            className={`max-w-[80%] rounded-lg p-3 ${
              message.role === 'user'
                ? 'bg-purple-600 text-white'
                : 'bg-gray-800 text-gray-200'}
            }`}
          >
            <RTLFlex className="items-center mb-1">
              <span className="text-xs font-medium">
                {message.role === 'user' ? t('chat.you') : t('chat.title')}
              </span>
              {message.role === 'assistant' && (
                <Sparkles size={12} className={`${language === 'ar' ? 'mr-1' : 'ml-1'} text-purple-400`} />
              )}
            </RTLFlex>
            <RTLText as="div" className="whitespace-pre-wrap">{message.content}</RTLText>
            <RTLText as="p" className="text-xs opacity-70 mt-1">
              {new Date(message.timestamp).toLocaleTimeString(language === 'ar' ? 'ar-SA' : undefined)}
            </RTLText>
          </div>
        </div>
      ))}

      {isLoading && (
        <div className={`flex justify-start ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-[80%] rounded-lg p-3 bg-gray-800 text-gray-200">
            <RTLFlex className="items-center mb-1">
              <span className="text-xs font-medium">{t('chat.title')}</span>
              <Sparkles size={12} className={`${language === 'ar' ? 'mr-1' : 'ml-1'} text-purple-400`} />
            </RTLFlex>
            <div className={`flex space-x-1 ${isRTL ? "flex-row-reverse" : ""}`}>
              <div className="w-2 h-2 rounded-full bg-purple-400 animate-bounce" style={{ animationDelay: '0ms' }}></div>
              <div className="w-2 h-2 rounded-full bg-purple-400 animate-bounce" style={{ animationDelay: '150ms' }}></div>
              <div className="w-2 h-2 rounded-full bg-purple-400 animate-bounce" style={{ animationDelay: '300ms' }}></div>
            </div>
          </div>
        </div>
      )}

      {error && (
        <div className="bg-red-900/30 backdrop-blur-sm rounded-lg p-2 text-red-400 text-sm">
          {error}
        </div>
      )}
    </div>
  );
};

export default MessageList;
