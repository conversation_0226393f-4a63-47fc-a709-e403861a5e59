import React, { useState, useEffect } from 'react';
import { useAppSelector } from '../../../store/hooks';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';
import { ErrorDisplay, useSessionError } from '../../ui';
import {
  Database, Monitor, Shield, Users, Activity, AlertTriangle,
  Server, Lock, Eye, Bot, BarChart3, Settings
} from 'lucide-react';

/**
 * DEDICATED SUPER ADMIN DASHBOARD COMPONENT
 * This component is exclusively for super admin users only.
 * It provides system-wide control and monitoring capabilities.
 */
const SuperAdminDashboard: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { user } = useAppSelector(state => state.auth);
  const { error: accessError, clearError: clearAccessError } = useSessionError('accessError');
  const [systemStats, setSystemStats] = useState({
    totalUsers: 0,
    activeUsers: 0,
    systemHealth: 'healthy',
    pendingActions: 0,
    securityAlerts: 0,
    performanceScore: 95
  });

  useEffect(() => {
    // Fetch super admin specific data
    const fetchSystemData = async () => {
      try {
        // This would fetch real system data
        setSystemStats({
          totalUsers: 1247,
          activeUsers: 89,
          systemHealth: 'healthy',
          pendingActions: 3,
          securityAlerts: 1,
          performanceScore: 95
        });
      } catch (error) {
        console.error('Error fetching system data:', error);
      }
    };

    fetchSystemData();
  }, []);

  const quickActions = [
    {
      id: 'system-management',
      title: t('superAdmin.systemManagement', 'System Management'),
      description: t('superAdmin.systemManagementDesc', 'Manage system configuration and settings'),
      icon: <Database className="w-6 h-6" />,
      path: '/super_admin/system-management',
      color: 'from-red-500 to-red-600',
      riskLevel: 'critical'
    },
    {
      id: 'user-impersonation',
      title: t('superAdmin.userImpersonation', 'User Impersonation'),
      description: t('superAdmin.userImpersonationDesc', 'Impersonate users for support'),
      icon: <Eye className="w-6 h-6" />,
      path: '/super_admin/user-impersonation',
      color: 'from-orange-500 to-orange-600',
      riskLevel: 'high'
    },
    {
      id: 'ai-system',
      title: t('superAdmin.aiSystem', 'AI System Management'),
      description: t('superAdmin.aiSystemDesc', 'Manage AI capabilities and configuration'),
      icon: <Bot className="w-6 h-6" />,
      path: '/super_admin/ai-system-management',
      color: 'from-purple-500 to-purple-600',
      riskLevel: 'high'
    },
    {
      id: 'security-center',
      title: t('superAdmin.security', 'Security Center'),
      description: t('superAdmin.securityDesc', 'Monitor security and access controls'),
      icon: <Lock className="w-6 h-6" />,
      path: '/super_admin/security',
      color: 'from-blue-500 to-blue-600',
      riskLevel: 'critical'
    }
  ];

  const systemMetrics = [
    {
      title: t('superAdmin.totalUsers', 'Total Users'),
      value: systemStats.totalUsers.toLocaleString(),
      icon: <Users className="w-8 h-8" />,
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: t('superAdmin.activeUsers', 'Active Users'),
      value: systemStats.activeUsers.toString(),
      icon: <Activity className="w-8 h-8" />,
      color: 'from-green-500 to-green-600'
    },
    {
      title: t('superAdmin.systemHealth', 'System Health'),
      value: systemStats.systemHealth,
      icon: <Monitor className="w-8 h-8" />,
      color: 'from-emerald-500 to-emerald-600'
    },
    {
      title: t('superAdmin.securityAlerts', 'Security Alerts'),
      value: systemStats.securityAlerts.toString(),
      icon: <AlertTriangle className="w-8 h-8" />,
      color: systemStats.securityAlerts > 0 ? 'from-red-500 to-red-600' : 'from-gray-500 to-gray-600'
    }
  ];

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-900 via-red-900 to-red-800 ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
      <div className={`px-4 sm:px-6 lg:px-8 py-6 ${isRTL ? 'text-right' : 'text-left'}`}>
        {/* Welcome Section */}
        <div className="mb-8">
          <div className="glass-light border border-red-500/30 rounded-2xl p-6">
            <div className={`flex items-center gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className="p-3 rounded-xl bg-gradient-to-br from-red-500 to-red-600 shadow-lg">
                <Shield className="w-8 h-8 text-white" />
              </div>
              <div className={isRTL ? 'text-right' : 'text-left'}>
                <h1 className="text-2xl font-bold text-white">
                  {t('superAdmin.welcome', 'Super Admin Control Center')}
                </h1>
                <p className="text-red-200 mt-1">
                  {t('superAdmin.welcomeDesc', 'System-wide monitoring and control')} - {user?.username}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Access Error Display */}
        <ErrorDisplay
          error={accessError}
          type="info"
          onDismiss={clearAccessError}
          className="mb-6"
        />

        {/* System Metrics */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-white mb-6">{t('superAdmin.systemMetrics', 'System Metrics')}</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {systemMetrics.map((metric, index) => (
              <div key={index} className="glass-light border border-white/20 rounded-xl p-6">
                <div className={`flex items-center gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={`p-3 rounded-xl bg-gradient-to-br ${metric.color} shadow-lg`}>
                    {metric.icon}
                  </div>
                  <div className={isRTL ? 'text-right' : 'text-left'}>
                    <p className="text-white/70 text-sm">{metric.title}</p>
                    <p className="text-2xl font-bold text-white">{metric.value}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Critical Actions */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-white mb-6">{t('superAdmin.criticalActions', 'Critical System Actions')}</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {quickActions.map((action) => (
              <a
                key={action.id}
                href={action.path}
                className="glass-light border border-white/20 rounded-xl p-6 hover:bg-white/10 transition-all duration-300 group"
              >
                <div className={`flex items-center gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={`p-3 rounded-xl bg-gradient-to-br ${action.color} shadow-lg group-hover:scale-110 transition-transform`}>
                    {action.icon}
                  </div>
                  <div className={`flex-1 ${isRTL ? 'text-right' : 'text-left'}`}>
                    <h3 className="text-lg font-semibold text-white group-hover:text-red-200 transition-colors">
                      {action.title}
                    </h3>
                    <p className="text-white/70 text-sm mt-1">{action.description}</p>
                    <div className="mt-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        action.riskLevel === 'critical' ? 'bg-red-500/20 text-red-300' :
                        action.riskLevel === 'high' ? 'bg-orange-500/20 text-orange-300' :
                        'bg-yellow-500/20 text-yellow-300'
                      }`}>
                        {action.riskLevel?.toUpperCase()} RISK
                      </span>
                    </div>
                  </div>
                </div>
              </a>
            ))}
          </div>
        </div>

        {/* System Status */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-white mb-6">{t('superAdmin.systemStatus', 'System Status')}</h2>
          <div className="glass-light border border-white/20 rounded-xl p-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className={`text-center ${isRTL ? 'text-right' : 'text-left'}`}>
                <div className="p-4 rounded-xl bg-gradient-to-br from-green-500 to-green-600 inline-block mb-3">
                  <Server className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-white">System Health</h3>
                <p className="text-green-300 font-medium">Optimal</p>
              </div>
              <div className={`text-center ${isRTL ? 'text-right' : 'text-left'}`}>
                <div className="p-4 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 inline-block mb-3">
                  <BarChart3 className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-white">Performance</h3>
                <p className="text-blue-300 font-medium">{systemStats.performanceScore}%</p>
              </div>
              <div className={`text-center ${isRTL ? 'text-right' : 'text-left'}`}>
                <div className="p-4 rounded-xl bg-gradient-to-br from-purple-500 to-purple-600 inline-block mb-3">
                  <Settings className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-white">Pending Actions</h3>
                <p className="text-purple-300 font-medium">{systemStats.pendingActions}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SuperAdminDashboard;
