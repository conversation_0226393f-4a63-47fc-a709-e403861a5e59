"""
Advanced Analytics Engine for Yasmeen AI Platform
Provides comprehensive business intelligence and insights
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from django.db.models import Count, Avg, Sum, Q
from django.utils import timezone
from django.contrib.auth.models import User
import json
import logging

logger = logging.getLogger(__name__)

class BusinessIntelligenceEngine:
    """Advanced analytics for business insights and trends"""
    
    def __init__(self):
        self.cache_timeout = 3600  # 1 hour cache
    
    def get_platform_overview(self, date_range: int = 30) -> Dict[str, Any]:
        """Get comprehensive platform overview analytics"""
        try:
            end_date = timezone.now()
            start_date = end_date - timedelta(days=date_range)
            
            # Import models here to avoid circular imports
            from incubator.models import BusinessIdea, MentorshipMatch, Investment
            # from ai_recommendations.models import ChatMessage, AIRecommendation  # REMOVED - deprecated
            from forums.models import ForumThread, ForumPost
            
            # Core metrics
            total_users = User.objects.count()
            new_users = User.objects.filter(date_joined__gte=start_date).count()
            
            total_business_ideas = BusinessIdea.objects.count()
            new_business_ideas = BusinessIdea.objects.filter(created_at__gte=start_date).count()
            
            total_mentorship_matches = MentorshipMatch.objects.count()
            active_mentorships = MentorshipMatch.objects.filter(status='active').count()
            
            # AI Usage Analytics
            ai_messages = ChatMessage.objects.filter(created_at__gte=start_date).count()
            ai_recommendations = AIRecommendation.objects.filter(created_at__gte=start_date).count()
            
            # Forum Activity
            forum_threads = ForumThread.objects.filter(created_at__gte=start_date).count()
            forum_posts = ForumPost.objects.filter(created_at__gte=start_date).count()
            
            # Investment Analytics
            total_investments = Investment.objects.aggregate(
                count=Count('id'),
                total_amount=Sum('amount')
            )
            
            # Growth rates
            previous_period_start = start_date - timedelta(days=date_range)
            previous_users = User.objects.filter(
                date_joined__gte=previous_period_start,
                date_joined__lt=start_date
            ).count()
            
            user_growth_rate = ((new_users - previous_users) / max(previous_users, 1)) * 100 if previous_users > 0 else 0
            
            return {
                'overview': {
                    'total_users': total_users,
                    'new_users': new_users,
                    'user_growth_rate': round(user_growth_rate, 2),
                    'total_business_ideas': total_business_ideas,
                    'new_business_ideas': new_business_ideas,
                    'active_mentorships': active_mentorships,
                    'total_investments': total_investments['count'] or 0,
                    'total_investment_amount': float(total_investments['total_amount'] or 0)
                },
                'ai_usage': {
                    'messages_sent': ai_messages,
                    'recommendations_generated': ai_recommendations,
                    'avg_messages_per_user': round(ai_messages / max(new_users, 1), 2)
                },
                'community': {
                    'forum_threads': forum_threads,
                    'forum_posts': forum_posts,
                    'engagement_rate': round((forum_posts / max(forum_threads, 1)), 2)
                },
                'date_range': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'days': date_range
                }
            }
            
        except Exception as e:
            logger.error(f"Platform overview analytics failed: {e}")
            return {'error': str(e)}
    
    def get_business_success_analytics(self) -> Dict[str, Any]:
        """Analyze business idea success patterns"""
        try:
            from incubator.models import BusinessIdea, Investment, MentorshipMatch
            
            # Business stage distribution
            stage_distribution = BusinessIdea.objects.values('current_stage').annotate(
                count=Count('id')
            ).order_by('current_stage')
            
            # Success indicators
            funded_ideas = BusinessIdea.objects.filter(investments__isnull=False).distinct().count()
            mentored_ideas = BusinessIdea.objects.filter(mentorship_matches__isnull=False).distinct().count()
            
            # Category analysis
            category_performance = BusinessIdea.objects.values('category').annotate(
                count=Count('id'),
                funded_count=Count('investments', distinct=True),
                avg_funding=Avg('investments__amount')
            ).order_by('-count')
            
            # Time-based analysis
            monthly_creation = BusinessIdea.objects.extra(
                select={'month': "strftime('%%Y-%%m', created_at)"}
            ).values('month').annotate(count=Count('id')).order_by('month')
            
            return {
                'stage_distribution': list(stage_distribution),
                'success_metrics': {
                    'total_ideas': BusinessIdea.objects.count(),
                    'funded_ideas': funded_ideas,
                    'mentored_ideas': mentored_ideas,
                    'funding_rate': round((funded_ideas / max(BusinessIdea.objects.count(), 1)) * 100, 2),
                    'mentorship_rate': round((mentored_ideas / max(BusinessIdea.objects.count(), 1)) * 100, 2)
                },
                'category_performance': list(category_performance),
                'monthly_trends': list(monthly_creation)
            }
            
        except Exception as e:
            logger.error(f"Business success analytics failed: {e}")
            return {'error': str(e)}
    
    def get_user_engagement_analytics(self, date_range: int = 30) -> Dict[str, Any]:
        """Analyze user engagement patterns"""
        try:
            end_date = timezone.now()
            start_date = end_date - timedelta(days=date_range)
            
            # from ai_recommendations.models import ChatMessage  # REMOVED - deprecated
            from forums.models import ForumPost, ForumThread
            from incubator.models import ProgressUpdate
            
            # Active users
            active_users = User.objects.filter(
                Q(chat_messages__created_at__gte=start_date) |
                Q(forum_posts__created_at__gte=start_date) |
                Q(forum_threads__created_at__gte=start_date) |
                Q(progress_updates__created_at__gte=start_date)
            ).distinct().count()
            
            # Engagement by activity type
            chat_users = User.objects.filter(
                chat_messages__created_at__gte=start_date
            ).distinct().count()
            
            forum_users = User.objects.filter(
                Q(forum_posts__created_at__gte=start_date) |
                Q(forum_threads__created_at__gte=start_date)
            ).distinct().count()
            
            business_users = User.objects.filter(
                business_ideas__created_at__gte=start_date
            ).distinct().count()
            
            # Daily active users trend
            daily_active = []
            for i in range(date_range):
                day = start_date + timedelta(days=i)
                day_end = day + timedelta(days=1)
                
                dau = User.objects.filter(
                    Q(chat_messages__created_at__gte=day, chat_messages__created_at__lt=day_end) |
                    Q(forum_posts__created_at__gte=day, forum_posts__created_at__lt=day_end) |
                    Q(forum_threads__created_at__gte=day, forum_threads__created_at__lt=day_end)
                ).distinct().count()
                
                daily_active.append({
                    'date': day.strftime('%Y-%m-%d'),
                    'active_users': dau
                })
            
            # User retention analysis
            total_users = User.objects.count()
            retention_rate = (active_users / max(total_users, 1)) * 100
            
            return {
                'engagement_overview': {
                    'total_users': total_users,
                    'active_users': active_users,
                    'retention_rate': round(retention_rate, 2),
                    'engagement_rate': round((active_users / max(total_users, 1)) * 100, 2)
                },
                'activity_breakdown': {
                    'chat_users': chat_users,
                    'forum_users': forum_users,
                    'business_users': business_users
                },
                'daily_trends': daily_active,
                'date_range': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'days': date_range
                }
            }
            
        except Exception as e:
            logger.error(f"User engagement analytics failed: {e}")
            return {'error': str(e)}
    
    def get_ai_performance_analytics(self) -> Dict[str, Any]:
        """Analyze AI service performance and usage"""
        try:
            # from ai_recommendations.models import ChatMessage, AIRecommendation, UserChatAnalytics  # REMOVED - deprecated
            
            # Message volume and performance
            total_messages = ChatMessage.objects.count()
            avg_response_time = ChatMessage.objects.filter(
                processing_time__isnull=False
            ).aggregate(avg_time=Avg('processing_time'))['avg_time'] or 0
            
            # Language distribution
            language_stats = ChatMessage.objects.values('language').annotate(
                count=Count('id')
            ).order_by('-count')
            
            # Service usage
            service_stats = ChatMessage.objects.values('service_used').annotate(
                count=Count('id'),
                avg_response_time=Avg('processing_time')
            ).order_by('-count')
            
            # Recommendation analytics
            recommendation_stats = AIRecommendation.objects.values('recommendation_type').annotate(
                count=Count('id'),
                implemented_count=Count('id', filter=Q(is_implemented=True)),
                avg_relevance=Avg('relevance_score')
            ).order_by('-count')
            
            # User satisfaction (based on feedback)
            total_recommendations = AIRecommendation.objects.count()
            implemented_recommendations = AIRecommendation.objects.filter(is_implemented=True).count()
            implementation_rate = (implemented_recommendations / max(total_recommendations, 1)) * 100
            
            return {
                'performance_metrics': {
                    'total_messages': total_messages,
                    'avg_response_time': round(avg_response_time, 2),
                    'total_recommendations': total_recommendations,
                    'implementation_rate': round(implementation_rate, 2)
                },
                'language_distribution': list(language_stats),
                'service_performance': list(service_stats),
                'recommendation_analytics': list(recommendation_stats),
                'quality_metrics': {
                    'avg_relevance_score': round(
                        AIRecommendation.objects.aggregate(
                            avg_score=Avg('relevance_score')
                        )['avg_score'] or 0, 2
                    ),
                    'user_satisfaction': round(implementation_rate, 2)
                }
            }
            
        except Exception as e:
            logger.error(f"AI performance analytics failed: {e}")
            return {'error': str(e)}
    
    def generate_insights_report(self, date_range: int = 30) -> Dict[str, Any]:
        """Generate comprehensive insights report"""
        try:
            platform_overview = self.get_platform_overview(date_range)
            business_analytics = self.get_business_success_analytics()
            engagement_analytics = self.get_user_engagement_analytics(date_range)
            ai_analytics = self.get_ai_performance_analytics()
            
            # Generate key insights
            insights = []
            
            # Growth insights
            if platform_overview.get('overview', {}).get('user_growth_rate', 0) > 10:
                insights.append({
                    'type': 'growth',
                    'title': 'Strong User Growth',
                    'description': f"User growth rate of {platform_overview['overview']['user_growth_rate']}% indicates strong platform adoption.",
                    'priority': 'high'
                })
            
            # Engagement insights
            engagement_rate = engagement_analytics.get('engagement_overview', {}).get('engagement_rate', 0)
            if engagement_rate < 30:
                insights.append({
                    'type': 'engagement',
                    'title': 'Low User Engagement',
                    'description': f"Engagement rate of {engagement_rate}% suggests need for improved user experience.",
                    'priority': 'medium'
                })
            
            # AI performance insights
            implementation_rate = ai_analytics.get('performance_metrics', {}).get('implementation_rate', 0)
            if implementation_rate > 70:
                insights.append({
                    'type': 'ai_performance',
                    'title': 'High AI Recommendation Quality',
                    'description': f"Implementation rate of {implementation_rate}% shows users find AI recommendations valuable.",
                    'priority': 'positive'
                })
            
            return {
                'report_metadata': {
                    'generated_at': timezone.now().isoformat(),
                    'date_range': date_range,
                    'report_type': 'comprehensive'
                },
                'platform_overview': platform_overview,
                'business_analytics': business_analytics,
                'engagement_analytics': engagement_analytics,
                'ai_analytics': ai_analytics,
                'key_insights': insights
            }
            
        except Exception as e:
            logger.error(f"Insights report generation failed: {e}")
            return {'error': str(e)}

# Export the main class
__all__ = ['BusinessIntelligenceEngine']
