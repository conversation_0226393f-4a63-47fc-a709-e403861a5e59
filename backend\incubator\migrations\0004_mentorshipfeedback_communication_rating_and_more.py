# Generated by Django 5.1.7 on 2025-05-15 17:28

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('incubator', '0003_mentorshipapplication_preferred_communication_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='mentorshipfeedback',
            name='communication_rating',
            field=models.PositiveSmallIntegerField(blank=True, choices=[(1, '1 - Poor'), (2, '2 - Below Average'), (3, '3 - Average'), (4, '4 - Good'), (5, '5 - Excellent')], help_text='Rating for communication skills', null=True),
        ),
        migrations.AddField(
            model_name='mentorshipfeedback',
            name='follow_up_needed',
            field=models.BooleanField(default=False, help_text='Is follow-up needed?'),
        ),
        migrations.AddField(
            model_name='mentorshipfeedback',
            name='follow_up_notes',
            field=models.TextField(blank=True, help_text='Notes for follow-up', null=True),
        ),
        migrations.AddField(
            model_name='mentorshipfeedback',
            name='goals_achieved',
            field=models.BooleanField(default=False, help_text='Were the session goals achieved?'),
        ),
        migrations.AddField(
            model_name='mentorshipfeedback',
            name='helpfulness_rating',
            field=models.PositiveSmallIntegerField(blank=True, choices=[(1, '1 - Poor'), (2, '2 - Below Average'), (3, '3 - Average'), (4, '4 - Good'), (5, '5 - Excellent')], help_text='Rating for helpfulness', null=True),
        ),
        migrations.AddField(
            model_name='mentorshipfeedback',
            name='highlights',
            field=models.TextField(blank=True, help_text='What went particularly well in this session', null=True),
        ),
        migrations.AddField(
            model_name='mentorshipfeedback',
            name='knowledge_rating',
            field=models.PositiveSmallIntegerField(blank=True, choices=[(1, '1 - Poor'), (2, '2 - Below Average'), (3, '3 - Average'), (4, '4 - Good'), (5, '5 - Excellent')], help_text='Rating for knowledge and expertise', null=True),
        ),
        migrations.AddField(
            model_name='mentorshipfeedback',
            name='preparation_rating',
            field=models.PositiveSmallIntegerField(blank=True, choices=[(1, '1 - Poor'), (2, '2 - Below Average'), (3, '3 - Average'), (4, '4 - Good'), (5, '5 - Excellent')], help_text='Rating for preparation and organization', null=True),
        ),
        migrations.AddField(
            model_name='mentorshipfeedback',
            name='responsiveness_rating',
            field=models.PositiveSmallIntegerField(blank=True, choices=[(1, '1 - Poor'), (2, '2 - Below Average'), (3, '3 - Average'), (4, '4 - Good'), (5, '5 - Excellent')], help_text='Rating for responsiveness', null=True),
        ),
        migrations.AddField(
            model_name='mentorshipfeedback',
            name='share_with_mentor',
            field=models.BooleanField(default=True, help_text='Share this feedback with the mentor'),
        ),
        migrations.AddField(
            model_name='mentorshipfeedback',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='mentorshipsession',
            name='ended_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='mentorshipsession',
            name='meeting_id',
            field=models.CharField(blank=True, help_text='Meeting ID for video conferencing', max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='mentorshipsession',
            name='meeting_link',
            field=models.URLField(blank=True, help_text='Direct link to join the meeting', null=True),
        ),
        migrations.AddField(
            model_name='mentorshipsession',
            name='meeting_password',
            field=models.CharField(blank=True, help_text='Password for video conferencing', max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='mentorshipsession',
            name='reminder_sent',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='mentorshipsession',
            name='reminder_sent_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='mentorshipsession',
            name='session_type',
            field=models.CharField(choices=[('video', 'Video Call'), ('phone', 'Phone Call'), ('in_person', 'In Person'), ('chat', 'Chat/Messaging')], default='video', max_length=20),
        ),
        migrations.AddField(
            model_name='mentorshipsession',
            name='started_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='mentorshipsession',
            name='video_provider',
            field=models.CharField(blank=True, choices=[('zoom', 'Zoom'), ('google_meet', 'Google Meet'), ('microsoft_teams', 'Microsoft Teams'), ('jitsi', 'Jitsi Meet'), ('custom', 'Custom Link')], max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='mentorshipsession',
            name='status',
            field=models.CharField(choices=[('scheduled', 'Scheduled'), ('completed', 'Completed'), ('cancelled', 'Cancelled'), ('rescheduled', 'Rescheduled'), ('in_progress', 'In Progress')], default='scheduled', max_length=20),
        ),
        migrations.CreateModel(
            name='BusinessAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('progress_rate', models.FloatField(default=0.0, help_text='Progress rate (updates per month)')),
                ('milestone_completion_rate', models.FloatField(default=0.0, help_text='Percentage of milestones completed on time')),
                ('goal_achievement_rate', models.FloatField(default=0.0, help_text='Percentage of goals achieved')),
                ('team_size', models.IntegerField(default=1, help_text='Number of team members (owner + collaborators)')),
                ('mentor_engagement', models.FloatField(default=0.0, help_text='Mentor engagement score (0-100)')),
                ('industry_percentile', models.FloatField(default=0.0, help_text='Percentile rank within same industry')),
                ('stage_percentile', models.FloatField(default=0.0, help_text='Percentile rank within same stage')),
                ('last_calculated', models.DateTimeField(auto_now=True)),
                ('business_idea', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='analytics', to='incubator.businessidea')),
            ],
        ),
        migrations.CreateModel(
            name='BusinessGoal',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('timeframe', models.CharField(choices=[('short_term', 'Short Term (0-3 months)'), ('medium_term', 'Medium Term (3-12 months)'), ('long_term', 'Long Term (1+ years)')], default='medium_term', max_length=20)),
                ('target_date', models.DateField(blank=True, null=True)),
                ('status', models.CharField(choices=[('active', 'Active'), ('achieved', 'Achieved'), ('revised', 'Revised'), ('abandoned', 'Abandoned')], default='active', max_length=20)),
                ('achievement_date', models.DateField(blank=True, null=True)),
                ('achievement_notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('business_idea', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='goals', to='incubator.businessidea')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_goals', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['target_date', 'timeframe'],
            },
        ),
        migrations.CreateModel(
            name='BusinessMilestone',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('due_date', models.DateField()),
                ('status', models.CharField(choices=[('not_started', 'Not Started'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('delayed', 'Delayed'), ('cancelled', 'Cancelled')], default='not_started', max_length=20)),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('critical', 'Critical')], default='medium', max_length=20)),
                ('completion_date', models.DateField(blank=True, null=True)),
                ('completion_notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='assigned_milestones', to=settings.AUTH_USER_MODEL)),
                ('business_idea', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='milestones', to='incubator.businessidea')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_milestones', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['due_date', '-priority'],
            },
        ),
        migrations.CreateModel(
            name='MentorRecommendation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('match_score', models.FloatField(help_text='Score from 0-100 indicating match quality')),
                ('match_reason', models.TextField(help_text='Explanation of why this mentor is recommended')),
                ('expertise_match', models.TextField(help_text='Specific expertise areas that match the business idea needs')),
                ('is_applied', models.BooleanField(default=False, help_text='Whether the user has applied to this mentor')),
                ('is_matched', models.BooleanField(default=False, help_text='Whether a mentorship match has been created')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('business_idea', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='mentor_recommendations', to='incubator.businessidea')),
                ('mentor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='recommendations', to='incubator.mentorprofile')),
            ],
            options={
                'ordering': ['-match_score'],
                'unique_together': {('business_idea', 'mentor')},
            },
        ),
    ]
