/**
 * Role-based API service
 * Provides role-based functionality and permissions
 */

import { api, getAuthToken } from './api';

export interface UserRole {
  id: number;
  name: string;
  permissions: string[];
}

export interface Permission {
  id: number;
  name: string;
  codename: string;
  content_type: string;
}

class RoleBasedApiService {
  /**
   * Get current user's roles
   */
  async getUserRoles(): Promise<string[]> {
    try {
      const token = getAuthToken();
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await api.get('/users/roles/');
      return response.data.roles || [];
    } catch (error) {
      console.error('Failed to get user roles:', error);
      throw error;
    }
  }

  /**
   * Get current user's permissions
   */
  async getUserPermissions(): Promise<string[]> {
    try {
      const token = getAuthToken();
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await api.get('/users/permissions/');
      return response.data.permissions || [];
    } catch (error) {
      console.error('Failed to get user permissions:', error);
      throw error;
    }
  }

  /**
   * Check if user has specific permission
   */
  async hasPermission(permission: string): Promise<boolean> {
    try {
      const permissions = await this.getUserPermissions();
      return permissions.includes(permission);
    } catch (error) {
      console.error('Failed to check permission:', error);
      return false;
    }
  }

  /**
   * Check if user has specific role
   */
  async hasRole(role: string): Promise<boolean> {
    try {
      const roles = await this.getUserRoles();
      return roles.includes(role);
    } catch (error) {
      console.error('Failed to check role:', error);
      return false;
    }
  }

  /**
   * Get all available roles (admin only)
   */
  async getAllRoles(): Promise<UserRole[]> {
    try {
      const response = await api.get('/admin/roles/');
      return response.data.roles || [];
    } catch (error) {
      console.error('Failed to get all roles:', error);
      throw error;
    }
  }

  /**
   * Get all available permissions (admin only)
   */
  async getAllPermissions(): Promise<Permission[]> {
    try {
      const response = await api.get('/admin/permissions/');
      return response.data.permissions || [];
    } catch (error) {
      console.error('Failed to get all permissions:', error);
      throw error;
    }
  }

  /**
   * Assign role to user (admin only)
   */
  async assignRole(userId: number, roleId: number): Promise<void> {
    try {
      await api.post('/admin/users/assign-role/', {
        user_id: userId,
        role_id: roleId
      });
    } catch (error) {
      console.error('Failed to assign role:', error);
      throw error;
    }
  }

  /**
   * Remove role from user (admin only)
   */
  async removeRole(userId: number, roleId: number): Promise<void> {
    try {
      await api.post('/admin/users/remove-role/', {
        user_id: userId,
        role_id: roleId
      });
    } catch (error) {
      console.error('Failed to remove role:', error);
      throw error;
    }
  }

  /**
   * Create new role (super admin only)
   */
  async createRole(name: string, permissions: number[]): Promise<UserRole> {
    try {
      const response = await api.post('/admin/roles/', {
        name,
        permissions
      });
      return response.data;
    } catch (error) {
      console.error('Failed to create role:', error);
      throw error;
    }
  }

  /**
   * Update role (super admin only)
   */
  async updateRole(roleId: number, name: string, permissions: number[]): Promise<UserRole> {
    try {
      const response = await api.put(`/admin/roles/${roleId}/`, {
        name,
        permissions
      });
      return response.data;
    } catch (error) {
      console.error('Failed to update role:', error);
      throw error;
    }
  }

  /**
   * Delete role (super admin only)
   */
  async deleteRole(roleId: number): Promise<void> {
    try {
      await api.delete(`/admin/roles/${roleId}/`);
    } catch (error) {
      console.error('Failed to delete role:', error);
      throw error;
    }
  }

  /**
   * Get user's role hierarchy
   */
  async getUserRoleHierarchy(): Promise<{ role: string; level: number; permissions: string[] }[]> {
    try {
      const response = await api.get('/users/role-hierarchy/');
      return response.data.hierarchy || [];
    } catch (error) {
      console.error('Failed to get role hierarchy:', error);
      throw error;
    }
  }

  /**
   * Check if user can access specific resource
   */
  async canAccessResource(resource: string, action: string = 'read'): Promise<boolean> {
    try {
      const response = await api.post('/users/check-access/', {
        resource,
        action
      });
      return response.data.can_access || false;
    } catch (error) {
      console.error('Failed to check resource access:', error);
      return false;
    }
  }
}

// Export singleton instance
export const roleBasedApi = new RoleBasedApiService();
export default roleBasedApi;
