import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../../store/hooks';
import { RTLFlex, RTLIcon, RTLText, LocalizedDate } from '.';
import { Calendar, Gift, Flag, BookOpen } from 'lucide-react';
import { getUpcomingHolidays, Holiday } from '../../utils/culturalHolidays';


interface UpcomingHolidaysProps {
  count?: number;
  className?: string;
  showTitle?: boolean;
}

/**
 * Component to display upcoming holidays based on the current locale
 */
const UpcomingHolidays: React.FC<UpcomingHolidaysProps> = ({ count = 5,
  className = '',
  showTitle = true,
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { language } = useAppSelector(state => state.language);
  const { getMarginClass } = useLanguageDirection();

  const [holidays, setHolidays] = useState<Holiday[]>([]);

  // Update holidays when language changes
  useEffect(() => {
    setHolidays(getUpcomingHolidays(count, language));
  }, [language, count]);

  // Get icon for holiday type
  const getHolidayIcon = (type: 'public' | 'religious' | 'cultural') => {
    switch (type) {
      case 'public':
        return (
          <ThemeWrapper
            as={Flag}
            size={16}
            darkClassName="text-blue-400"
            lightClassName="text-blue-600"
          />
        );
      case 'religious':
        return (
          <ThemeWrapper
            as={BookOpen}
            size={16}
            darkClassName="text-purple-400"
            lightClassName="text-purple-600"
          />
        );
      case 'cultural':
        return (
          <ThemeWrapper
            as={Gift}
            size={16}
            darkClassName="text-pink-400"
            lightClassName="text-pink-600"
          />
        );
      default:
        return (
          <ThemeWrapper
            as={Calendar}
            size={16}
            darkClassName="text-gray-400"
            lightClassName="text-gray-600"
          />
        );
    }
  };

  // Get color class for holiday type
  const getHolidayColorClass = (type: 'public' | 'religious' | 'cultural') => {
    switch (type) {
      case 'public':
        return {
          dark: 'bg-blue-900/30 border-blue-800/50',
          light: 'bg-blue-100 border-blue-200'
        };
      case 'religious':
        return {
          dark: 'bg-purple-900/30 border-purple-800/50',
          light: 'bg-purple-100 border-purple-200'
        };
      case 'cultural':
        return {
          dark: 'bg-pink-900/30 border-pink-800/50',
          light: 'bg-pink-100 border-pink-200'
        };
      default:
        return {
          dark: 'bg-gray-900/30 border-gray-800/50',
          light: 'bg-gray-100 border-gray-200'
        };
    }
  };

  // Get translated holiday type
  const getHolidayTypeText = (type: 'public' | 'religious' | 'cultural') => {
    switch (type) {
      case 'public':
        return t('holidays.types.public');
      case 'religious':
        return t('holidays.types.religious');
      case 'cultural':
        return t('holidays.types.cultural');
      default:
        return type;
    }
  };

  const { theme } = useAppSelector(state => state.theme);

  return (
    <ThemeWrapper className={className}>
      {showTitle && (
        <RTLFlex as="h3" className="text-lg font-semibold mb-4" align="center">
          <RTLIcon icon={Calendar} size={20} className={getMarginClass('right', 2)} />
          <span>{t('holidays.upcoming')}</span>
        </RTLFlex>
      )}

      {holidays.length === 0 ? (
        <ThemeWrapper
          as={RTLText}
          darkClassName="text-gray-400"
          lightClassName="text-gray-900"
        >
          {t('holidays.none')}
        </ThemeWrapper>
      ) : (
        <div className="space-y-3">
          {holidays.map((holiday, index) => {
            const colorClasses = getHolidayColorClass(holiday.type);

            return (
              <ThemeWrapper
                key={index}
                className="p-3 rounded-lg border"
                darkClassName={colorClasses.dark}
                lightClassName={colorClasses.light}
              >
                <RTLFlex justify="between" align="center">
                  <RTLFlex align="center">
                    <RTLIcon
                      icon={() => getHolidayIcon(holiday.type)}
                      size={18}
                      className={getMarginClass('right', 2)}
                    />
                    <ThemeWrapper
                      as="span"
                      className="font-medium"
                      darkClassName="text-white"
                      lightClassName="text-gray-800"
                    >
                      {holiday.name}
                    </ThemeWrapper>
                  </RTLFlex>
                  <ThemeWrapper
                    as={LocalizedDate}
                    date={holiday.date}
                    format="date"
                    className="text-sm"
                    darkClassName="text-gray-400"
                    lightClassName="text-gray-900"
                  />
                </RTLFlex>
                <ThemeWrapper
                  className="mt-1 text-xs"
                  darkClassName="text-gray-400"
                  lightClassName="text-gray-900"
                >
                  {getHolidayTypeText(holiday.type)}
                </ThemeWrapper>
              </ThemeWrapper>
            );
          })}
        </div>
      )}
    </ThemeWrapper>
  );
};

export default UpcomingHolidays;
