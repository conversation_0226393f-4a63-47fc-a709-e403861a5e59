import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  Users,
  Calendar,
  Clock,
  Video,
  MessageSquare,
  Star,
  ArrowRight,
  Plus,
  CheckCircle,
  AlertCircle,
  RefreshCw
} from 'lucide-react';
import { useAppDispatch, useAppSelector } from '../../store/hooks';
// MainLayout removed - handled by routing system
import {
  fetchMentorshipMatches,
  fetchUpcomingSessions,
  fetchPastSessions
} from '../../store/incubatorSlice';
import { MentorshipMatch, MentorshipSession } from '../../services/incubatorApi';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';

const MentorshipDashboardPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { user } = useAppSelector(state => state.auth);
  const {
    mentorshipMatches,
    mentorshipSessions,
    isLoading,
    error
  } = useAppSelector(state => state.incubator);

  const [upcomingSessions, setUpcomingSessions] = useState<MentorshipSession[]>([]);
  const [pastSessions, setPastSessions] = useState<MentorshipSession[]>([]);
  const [activeMatches, setActiveMatches] = useState<MentorshipMatch[]>([]);

  useEffect(() => {
    // Fetch mentorship matches and sessions
    dispatch(fetchMentorshipMatches());
    dispatch(fetchUpcomingSessions());
    dispatch(fetchPastSessions());
  }, [dispatch]);

  useEffect(() => {
    // Filter active matches
    if (mentorshipMatches.length > 0) {
      setActiveMatches(mentorshipMatches.filter(match => match.status === 'active'));
    }

    // Filter upcoming and past sessions
    if (mentorshipSessions.length > 0) {
      const now = new Date();

      const upcoming = mentorshipSessions.filter(session =>
        new Date(session.scheduled_at) > now &&
        ['scheduled', 'rescheduled'].includes(session.status)
      ).sort((a, b) => new Date(a.scheduled_at).getTime() - new Date(b.scheduled_at).getTime());

      const past = mentorshipSessions.filter(session =>
        new Date(session.scheduled_at) < now ||
        ['completed', 'cancelled'].includes(session.status)
      ).sort((a, b) => new Date(b.scheduled_at).getTime() - new Date(a.scheduled_at).getTime());

      setUpcomingSessions(upcoming);
      setPastSessions(past);
    }
  }, [mentorshipMatches, mentorshipSessions]);

  const refreshData = () => {
    dispatch(fetchMentorshipMatches());
    dispatch(fetchUpcomingSessions());
    dispatch(fetchPastSessions());
  };

  const formatSessionTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString(undefined, {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getSessionStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'bg-blue-900/50 text-blue-200';
      case 'completed':
        return 'bg-green-900/50 text-green-200';
      case 'cancelled':
        return 'bg-red-900/50 text-red-200';
      case 'rescheduled':
        return 'bg-yellow-900/50 text-yellow-200';
      case 'in_progress':
        return 'bg-purple-900/50 text-purple-200';
      default:
        return 'bg-gray-900/50 text-gray-200';
    }
  };

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
      <div className="max-w-7xl mx-auto p-6">
        {/* Header */}
        <div className={`flex justify-between items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div>
            <h1 className="text-2xl font-bold text-white">{t("mentorship.dashboard.title")}</h1>
            <div className="text-gray-300 mt-1">
              {t("mentorship.dashboard.subtitle")}
            </div>
          </div>
          <div className={`flex space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
            <button
              onClick={refreshData}
              className="p-2 bg-white/20 hover:bg-white/30 rounded-md"
              title={t("mentorship.dashboard.refreshData")}
            >
              <RefreshCw size={18} />
            </button>
            <Link
              to="/dashboard/mentorship/apply"
              className={`px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
            >
              <Users size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
              {t("mentorship.dashboard.findMentor")}
            </Link>
          </div>
        </div>

        {isLoading && (
          <div className={`flex justify-center py-12 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
          </div>
        )}

        {error && (
          <div className={`bg-red-900/30 text-red-200 p-4 rounded-lg mb-6 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <AlertCircle size={20} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
            <span>{t("mentorship.errors.loadingSessions")}: {error}</span>
          </div>
        )}

        {!isLoading && !error && (
          <>
            {/* Upcoming Sessions */}
            <div className="mb-8">
              <div className={`flex justify-between items-center mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                <h2 className={`text-xl font-semibold flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                  <Calendar size={20} className={`mr-2 text-purple-400 ${isRTL ? "space-x-reverse" : ""}`} />
                  {t("mentorship.dashboard.upcomingSessions")}
                </h2>
                <Link
                  to="/dashboard/mentorship/sessions/schedule"
                  className={`text-purple-400 hover:text-purple-300 flex items-center text-sm ${isRTL ? "flex-row-reverse" : ""}`}
                >
                  <Plus size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                  {t("mentorship.dashboard.scheduleNewSession")}
                </Link>
              </div>

              {upcomingSessions.length === 0 ? (
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 text-center">
                  <Clock size={40} className="mx-auto mb-3 text-gray-500" />
                  <h3 className="text-lg font-medium mb-2">{t("mentorship.dashboard.noUpcomingSessions")}</h3>
                  <div className="text-gray-400 mb-4">
                    {t("mentorship.dashboard.noUpcomingSessionsDesc")}
                  </div>
                  <Link
                    to="/dashboard/mentorship/sessions/schedule"
                    className={`px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white inline-flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                  >
                    <Calendar size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                    {t("mentorship.dashboard.scheduleSession")}
                  </Link>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {upcomingSessions.slice(0, 4).map(session => (
                    <div
                      key={session.id}
                      className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20"
                    >
                      <div className={`flex justify-between items-start mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <h3 className="font-medium">{session.title}</h3>
                        <span className={`px-2 py-0.5 rounded-full text-xs ${getSessionStatusColor(session.status)}`}>
                          {session.status_display}
                        </span>
                      </div>

                      <div className="text-gray-400 text-sm mb-3">
                        <div className={`flex items-center mb-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                          <Calendar size={14} className={`mr-1 text-gray-500 ${isRTL ? "space-x-reverse" : ""}`} />
                          {formatSessionTime(session.scheduled_at)}
                        </div>
                        <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                          <Clock size={14} className={`mr-1 text-gray-500 ${isRTL ? "space-x-reverse" : ""}`} />
                          {session.duration_minutes} {t("mentorship.dashboard.minutes")}
                        </div>
                      </div>

                      <div className={`flex justify-between items-center mt-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                          {session.session_type === 'video' && (
                            <Video size={16} className={`mr-1 text-blue-400 ${isRTL ? "space-x-reverse" : ""}`} />
                          )}
                          <span className="text-sm">{session.session_type_display}</span>
                        </div>

                        <Link
                          to={`/dashboard/mentorship/sessions/${session.id}`}
                          className={`text-purple-400 hover:text-purple-300 text-sm flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                        >
                          {t("mentorship.dashboard.viewDetails")} <ArrowRight size={14} className={`ml-1 ${isRTL ? "space-x-reverse" : ""}`} />
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {upcomingSessions.length > 4 && (
                <div className="mt-4 text-center">
                  <Link
                    to="/dashboard/mentorship/sessions"
                    className={`text-purple-400 hover:text-purple-300 inline-flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                  >
                    {t("mentorship.dashboard.viewAllSessions")} <ArrowRight size={16} className={`ml-1 ${isRTL ? "space-x-reverse" : ""}`} />
                  </Link>
                </div>
              )}
            </div>

            {/* Active Mentorships */}
            <div className="mb-8">
              <h2 className={`text-xl font-semibold mb-4 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <Users size={20} className={`mr-2 text-blue-400 ${isRTL ? "space-x-reverse" : ""}`} />
                {t("mentorship.dashboard.activeMentorships")}
              </h2>

              {activeMatches.length === 0 ? (
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 text-center">
                  <Users size={40} className="mx-auto mb-3 text-gray-500" />
                  <h3 className="text-lg font-medium mb-2">{t("mentorship.dashboard.noActiveMentorships")}</h3>
                  <div className="text-gray-400 mb-4">
                    {t("mentorship.dashboard.noActiveMentorshipsDesc")}
                  </div>
                  <Link
                    to="/dashboard/mentorship/apply"
                    className={`px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white inline-flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                  >
                    <Users size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                    {t("mentorship.dashboard.findMentor")}
                  </Link>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {activeMatches.map(match => (
                    <div
                      key={match.id}
                      className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20"
                    >
                      <div className={`flex items-center mb-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <div className={`w-12 h-12 rounded-full bg-indigo-800 flex items-center justify-center mr-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                          <Users size={24} className="text-blue-400" />
                        </div>
                        <div>
                          <h3 className="font-medium">
                            {match.mentor.user.first_name} {match.mentor.user.last_name}
                          </h3>
                          <div className="text-gray-400 text-sm">
                            {match.mentor.title}
                          </div>
                        </div>
                      </div>

                      <div className="text-gray-400 text-sm mb-3">
                        <div>{t("mentorship.dashboard.businessIdea")}: {match.business_idea_title}</div>
                        <p>{t("mentorship.dashboard.started")}: {new Date(match.created_at).toLocaleDateString()}</p>
                      </div>

                      <div className={`flex justify-between items-center mt-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <Link
                          to={`/dashboard/mentorship/sessions/schedule?match=${match.id}`}
                          className={`text-blue-400 hover:text-blue-300 text-sm flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                        >
                          <Calendar size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                          {t("mentorship.dashboard.scheduleSession")}
                        </Link>

                        <Link
                          to={`/dashboard/mentorship/matches/${match.id}`}
                          className={`text-purple-400 hover:text-purple-300 text-sm flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                        >
                          {t("mentorship.dashboard.viewDetails")} <ArrowRight size={14} className={`ml-1 ${isRTL ? "space-x-reverse" : ""}`} />
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Recent Feedback */}
            <div className="mb-8">
              <h2 className={`text-xl font-semibold mb-4 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <MessageSquare size={20} className={`mr-2 text-green-400 ${isRTL ? "space-x-reverse" : ""}`} />
                {t("mentorship.dashboard.recentFeedback")}
              </h2>

              {pastSessions.length === 0 ? (
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 text-center">
                  <MessageSquare size={40} className="mx-auto mb-3 text-gray-500" />
                  <h3 className="text-lg font-medium mb-2">{t("mentorship.dashboard.noSessionHistory")}</h3>
                  <div className="text-gray-400">
                    {t("mentorship.dashboard.noSessionHistoryDesc")}
                  </div>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {pastSessions.slice(0, 4).map(session => (
                    <div
                      key={session.id}
                      className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20"
                    >
                      <div className={`flex justify-between items-start mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <h3 className="font-medium">{session.title}</h3>
                        <span className={`px-2 py-0.5 rounded-full text-xs ${getSessionStatusColor(session.status)}`}>
                          {session.status_display}
                        </span>
                      </div>

                      <div className="text-gray-400 text-sm mb-3">
                        <div>{formatSessionTime(session.scheduled_at)}</div>
                      </div>

                      <div className={`flex justify-between items-center mt-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                        {session.feedback && session.feedback.length > 0 ? (
                          <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                            <Star size={16} className={`text-yellow-400 mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                            <span>{session.average_rating?.toFixed(1) || 'N/A'} {t("mentorship.dashboard.rating")}</span>
                          </div>
                        ) : (
                          <Link
                            to={`/dashboard/mentorship/sessions/${session.id}/feedback`}
                            className={`text-green-400 hover:text-green-300 text-sm flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                          >
                            <MessageSquare size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                            {t("mentorship.dashboard.provideFeedback")}
                          </Link>
                        )}

                        <Link
                          to={`/dashboard/mentorship/sessions/${session.id}`}
                          className={`text-purple-400 hover:text-purple-300 text-sm flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                        >
                          {t("mentorship.dashboard.viewDetails")} <ArrowRight size={14} className={`ml-1 ${isRTL ? "space-x-reverse" : ""}`} />
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {pastSessions.length > 4 && (
                <div className="mt-4 text-center">
                  <Link
                    to="/dashboard/mentorship/sessions?tab=past"
                    className={`text-purple-400 hover:text-purple-300 inline-flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                  >
                    {t("mentorship.dashboard.viewSessionHistory")} <ArrowRight size={16} className={`ml-1 ${isRTL ? "space-x-reverse" : ""}`} />
                  </Link>
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default MentorshipDashboardPage;
