"""
Enhanced Centralized AI Service
Single source of truth for all AI operations with caching, rate limiting, and proper error handling
"""

import logging
import json
import hashlib
from typing import Dict, Any, Optional
from datetime import datetime
from django.core.cache import cache
from django.conf import settings

from .ai_config import get_ai_config, AIPrompts, AIFallbacks

logger = logging.getLogger(__name__)


class AIResponse:
    """Standardized AI response object"""
    def __init__(self, success: bool, data: Any = None, error: str = None,
                 service: str = 'unknown', fallback_used: bool = False,
                 cache_hit: bool = False, user_id: Optional[int] = None):
        self.success = success
        self.data = data
        self.error = error
        self.service = service
        self.fallback_used = fallback_used
        self.cache_hit = cache_hit
        self.user_id = user_id
        self.timestamp = datetime.now().isoformat()

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses"""
        result = {
            'success': self.success,
            'service': self.service,
            'timestamp': self.timestamp,
            'fallback_used': self.fallback_used,
            'cache_hit': self.cache_hit
        }

        if self.success and self.data:
            if isinstance(self.data, str):
                result['message'] = self.data
            else:
                result.update(self.data)

        if not self.success and self.error:
            result['error'] = self.error

        if self.user_id:
            result['user_id'] = self.user_id

        return result


class AICache:
    """AI response caching system"""

    @staticmethod
    def _generate_cache_key(operation: str, data: Dict[str, Any]) -> str:
        """Generate cache key for AI operations"""
        # Create a hash of the operation and data
        content = f"{operation}:{json.dumps(data, sort_keys=True)}"
        return f"ai_cache:{hashlib.md5(content.encode()).hexdigest()}"

    @staticmethod
    def get(operation: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Get cached AI response"""
        try:
            cache_key = AICache._generate_cache_key(operation, data)
            return cache.get(cache_key)
        except Exception as e:
            logger.warning(f"Cache get error: {e}")
            return None

    @staticmethod
    def set(operation: str, data: Dict[str, Any], response: Dict[str, Any], timeout: int = 3600):
        """Cache AI response"""
        try:
            cache_key = AICache._generate_cache_key(operation, data)
            cache.set(cache_key, response, timeout)
        except Exception as e:
            logger.warning(f"Cache set error: {e}")


class AIRateLimiter:
    """Rate limiting for AI operations"""

    @staticmethod
    def check_rate_limit(user_id: Optional[int], operation: str) -> bool:
        """Check if user has exceeded rate limit"""
        if not user_id:
            return True  # Allow anonymous requests for now

        try:
            cache_key = f"ai_rate_limit:{user_id}:{operation}"
            current_count = cache.get(cache_key, 0)

            # Different limits for different operations
            limits = {
                'chat': 50,  # 50 chat messages per hour
                'business_analysis': 20,  # 20 analyses per hour
                'text_analysis': 30,  # 30 text analyses per hour
            }

            limit = limits.get(operation, 10)  # Default limit

            if current_count >= limit:
                return False

            # Increment counter
            cache.set(cache_key, current_count + 1, 3600)  # 1 hour timeout
            return True

        except Exception as e:
            logger.warning(f"Rate limit check error: {e}")
            return True  # Allow on error


class AIService:
    """
    Enhanced centralized AI service with caching, rate limiting, and proper error handling
    All AI functionality across the app uses this service
    """

    def __init__(self):
        self.config = get_ai_config()
        self.cache = AICache()
        self.rate_limiter = AIRateLimiter()

    def chat(self, message: str, language: str = 'en', user_id: Optional[int] = None,
             context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Universal chat function used across the entire application
        Enhanced with caching and rate limiting
        """
        try:
            # Check rate limit
            if not self.rate_limiter.check_rate_limit(user_id, 'chat'):
                return AIResponse(
                    success=False,
                    error="Rate limit exceeded. Please try again later.",
                    service='rate_limiter',
                    user_id=user_id
                ).to_dict()

            # Check cache first
            cache_data = {'message': message, 'language': language}
            cached_response = self.cache.get('chat', cache_data)
            if cached_response:
                cached_response['cache_hit'] = True
                return cached_response

            # Prepare prompt using centralized prompts
            prompt = AIPrompts.prepare_chat_prompt(message, language, context)

            # Try AI service first
            if self.config.is_available:
                ai_response = self.config.generate_content(prompt)
                if ai_response:
                    response_data = {
                        'message': ai_response.strip(),
                        'language': language
                    }

                    result = AIResponse(
                        success=True,
                        data=response_data,
                        service='gemini',
                        user_id=user_id
                    ).to_dict()

                    # Cache the response
                    self.cache.set('chat', cache_data, result)

                    return result

            # Use fallback if AI not available
            fallback_message = AIFallbacks.get_chat_fallback(language)
            return AIResponse(
                success=True,
                data={'message': fallback_message, 'language': language},
                service='fallback',
                fallback_used=True,
                user_id=user_id
            ).to_dict()

        except Exception as e:
            logger.error(f"Chat error: {e}")
            return AIResponse(
                success=False,
                error=str(e),
                service='error',
                user_id=user_id
            ).to_dict()
    
    def analyze_business(self, business_idea: str, language: str = 'en', user_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Universal business analysis function used across the entire application
        Enhanced with caching and rate limiting
        """
        try:
            # Check rate limit
            if not self.rate_limiter.check_rate_limit(user_id, 'business_analysis'):
                return AIResponse(
                    success=False,
                    error="Rate limit exceeded. Please try again later.",
                    service='rate_limiter',
                    user_id=user_id
                ).to_dict()

            # Check cache first
            cache_data = {'business_idea': business_idea, 'language': language}
            cached_response = self.cache.get('business_analysis', cache_data)
            if cached_response:
                cached_response['cache_hit'] = True
                return cached_response

            # Prepare prompt using centralized prompts
            prompt = AIPrompts.prepare_business_analysis_prompt(business_idea, language)

            # Try AI service first
            if self.config.is_available:
                ai_response = self.config.generate_content(prompt)
                if ai_response:
                    response_data = {
                        'analysis': ai_response.strip(),
                        'language': language,
                        'business_idea': business_idea[:100] + '...' if len(business_idea) > 100 else business_idea
                    }

                    result = AIResponse(
                        success=True,
                        data=response_data,
                        service='gemini',
                        user_id=user_id
                    ).to_dict()

                    # Cache the response (longer timeout for business analysis)
                    self.cache.set('business_analysis', cache_data, result, timeout=7200)  # 2 hours

                    return result

            # Use fallback if AI not available
            fallback_analysis = AIFallbacks.get_business_analysis_fallback(business_idea, language)
            return AIResponse(
                success=True,
                data={
                    'analysis': fallback_analysis,
                    'language': language,
                    'business_idea': business_idea[:100] + '...' if len(business_idea) > 100 else business_idea
                },
                service='fallback',
                fallback_used=True,
                user_id=user_id
            ).to_dict()

        except Exception as e:
            logger.error(f"Business analysis error: {e}")
            return AIResponse(
                success=False,
                error=str(e),
                service='error',
                user_id=user_id
            ).to_dict()
    
    def analyze_text(self, text: str, language: str = 'en', user_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Universal text analysis function used across the entire application
        Enhanced with caching and rate limiting
        """
        try:
            # Check rate limit
            if not self.rate_limiter.check_rate_limit(user_id, 'text_analysis'):
                return AIResponse(
                    success=False,
                    error="Rate limit exceeded. Please try again later.",
                    service='rate_limiter',
                    user_id=user_id
                ).to_dict()

            # Check cache first
            cache_data = {'text': text, 'language': language}
            cached_response = self.cache.get('text_analysis', cache_data)
            if cached_response:
                cached_response['cache_hit'] = True
                return cached_response

            # Prepare prompt using centralized prompts
            prompt = AIPrompts.prepare_text_analysis_prompt(text, language)

            # Try AI service first
            if self.config.is_available:
                ai_response = self.config.generate_content(prompt)
                if ai_response:
                    response_data = {
                        'analysis': ai_response.strip(),
                        'language': language,
                        'text_length': len(text)
                    }

                    result = AIResponse(
                        success=True,
                        data=response_data,
                        service='gemini',
                        user_id=user_id
                    ).to_dict()

                    # Cache the response
                    self.cache.set('text_analysis', cache_data, result)

                    return result

            # Use fallback if AI not available
            fallback_message = f"Text analysis is temporarily unavailable. Text length: {len(text)} characters."
            if language == 'ar':
                fallback_message = f"تحليل النص غير متاح مؤقتاً. طول النص: {len(text)} حرف."

            return AIResponse(
                success=True,
                data={
                    'analysis': fallback_message,
                    'language': language,
                    'text_length': len(text)
                },
                service='fallback',
                fallback_used=True,
                user_id=user_id
            ).to_dict()

        except Exception as e:
            logger.error(f"Text analysis error: {e}")
            return AIResponse(
                success=False,
                error=str(e),
                service='error',
                user_id=user_id
            ).to_dict()
    
    def get_status(self) -> Dict[str, Any]:
        """
        Get comprehensive AI service status with enhanced metrics
        """
        config_status = self.config.get_status()

        return {
            **config_status,
            'centralized_service': True,
            'version': '2.0.0',  # Enhanced version
            'endpoints': {
                'chat': 'Universal chat with caching and rate limiting',
                'business_analysis': 'Enhanced business analysis with intelligent caching',
                'text_analysis': 'Smart text analysis with context awareness',
                'status': 'Comprehensive service status and health metrics',
                'intelligent_features': 'Advanced AI capabilities'
            },
            'features': {
                'chat': True,
                'business_analysis': True,
                'text_analysis': True,
                'multilingual': True,
                'fallbacks': True,
                'centralized_config': True,
                'caching': True,
                'rate_limiting': True,
                'error_handling': True,
                'context_awareness': True,
                'intelligent_responses': True
            },
            'performance': {
                'caching_enabled': True,
                'rate_limiting_enabled': True,
                'fallback_system': True,
                'error_recovery': True
            },
            'timestamp': datetime.now().isoformat()
        }

    def generate_intelligent_content(self, content_type: str, context: Dict[str, Any],
                                   language: str = 'en', user_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Generate intelligent content based on type and context
        New advanced AI feature for business intelligence
        """
        try:
            # Check rate limit
            if not self.rate_limiter.check_rate_limit(user_id, 'intelligent_content'):
                return AIResponse(
                    success=False,
                    error="Rate limit exceeded. Please try again later.",
                    service='rate_limiter',
                    user_id=user_id
                ).to_dict()

            # Check cache first
            cache_data = {'content_type': content_type, 'context': context, 'language': language}
            cached_response = self.cache.get('intelligent_content', cache_data)
            if cached_response:
                cached_response['cache_hit'] = True
                return cached_response

            # Generate content based on type
            if content_type == 'business_plan_section':
                prompt = self._build_business_plan_prompt(context, language)
            elif content_type == 'market_analysis':
                prompt = self._build_market_analysis_prompt(context, language)
            elif content_type == 'financial_projection':
                prompt = self._build_financial_projection_prompt(context, language)
            elif content_type == 'risk_assessment':
                prompt = self._build_risk_assessment_prompt(context, language)
            else:
                prompt = f"Generate {content_type} content based on: {context}"

            # Try AI service first
            if self.config.is_available:
                ai_response = self.config.generate_content(prompt)
                if ai_response:
                    response_data = {
                        'content': ai_response.strip(),
                        'content_type': content_type,
                        'language': language,
                        'context_used': context
                    }

                    result = AIResponse(
                        success=True,
                        data=response_data,
                        service='gemini',
                        user_id=user_id
                    ).to_dict()

                    # Cache the response (longer timeout for intelligent content)
                    self.cache.set('intelligent_content', cache_data, result, timeout=10800)  # 3 hours

                    return result

            # Fallback for intelligent content
            fallback_content = f"Intelligent {content_type} generation is temporarily unavailable."
            if language == 'ar':
                fallback_content = f"توليد {content_type} الذكي غير متاح مؤقتاً."

            return AIResponse(
                success=True,
                data={
                    'content': fallback_content,
                    'content_type': content_type,
                    'language': language
                },
                service='fallback',
                fallback_used=True,
                user_id=user_id
            ).to_dict()

        except Exception as e:
            logger.error(f"Intelligent content generation error: {e}")
            return AIResponse(
                success=False,
                error=str(e),
                service='error',
                user_id=user_id
            ).to_dict()
    
    def is_available(self) -> bool:
        """Check if AI service is available"""
        return self.config.is_available

    def reload_config(self):
        """Reload AI configuration"""
        self.config.reload_config()

    # Helper methods for intelligent content generation
    def _build_business_plan_prompt(self, context: Dict[str, Any], language: str) -> str:
        """Build prompt for business plan section generation"""
        business_idea = context.get('business_idea', '')
        section_type = context.get('section_type', '')
        industry = context.get('industry', '')

        if language == 'ar':
            return f"""
            اكتب قسم {section_type} لخطة عمل لفكرة العمل التالية:
            فكرة العمل: {business_idea}
            الصناعة: {industry}

            يجب أن يكون المحتوى مفصلاً ومهنياً ومناسباً للصناعة المحددة.
            """
        else:
            return f"""
            Write a {section_type} section for a business plan for the following business idea:
            Business Idea: {business_idea}
            Industry: {industry}

            The content should be detailed, professional, and industry-appropriate.
            """

    def _build_market_analysis_prompt(self, context: Dict[str, Any], language: str) -> str:
        """Build prompt for market analysis generation"""
        business_idea = context.get('business_idea', '')
        target_market = context.get('target_market', '')

        if language == 'ar':
            return f"""
            قم بإجراء تحليل سوق شامل لفكرة العمل التالية:
            فكرة العمل: {business_idea}
            السوق المستهدف: {target_market}

            يجب أن يشمل التحليل: حجم السوق، المنافسين، الاتجاهات، والفرص.
            """
        else:
            return f"""
            Conduct a comprehensive market analysis for the following business idea:
            Business Idea: {business_idea}
            Target Market: {target_market}

            The analysis should include: market size, competitors, trends, and opportunities.
            """

    def _build_financial_projection_prompt(self, context: Dict[str, Any], language: str) -> str:
        """Build prompt for financial projection generation"""
        business_idea = context.get('business_idea', '')
        revenue_model = context.get('revenue_model', '')

        if language == 'ar':
            return f"""
            أنشئ توقعات مالية لمدة 3 سنوات لفكرة العمل التالية:
            فكرة العمل: {business_idea}
            نموذج الإيرادات: {revenue_model}

            يجب أن تشمل: الإيرادات، المصروفات، الربح، والتدفق النقدي.
            """
        else:
            return f"""
            Create 3-year financial projections for the following business idea:
            Business Idea: {business_idea}
            Revenue Model: {revenue_model}

            Include: revenue, expenses, profit, and cash flow projections.
            """

    def _build_risk_assessment_prompt(self, context: Dict[str, Any], language: str) -> str:
        """Build prompt for risk assessment generation"""
        business_idea = context.get('business_idea', '')
        industry = context.get('industry', '')

        if language == 'ar':
            return f"""
            قم بتقييم المخاطر لفكرة العمل التالية:
            فكرة العمل: {business_idea}
            الصناعة: {industry}

            حدد المخاطر المحتملة واستراتيجيات التخفيف من حدتها.
            """
        else:
            return f"""
            Conduct a risk assessment for the following business idea:
            Business Idea: {business_idea}
            Industry: {industry}

            Identify potential risks and mitigation strategies.
            """


# Global AI service instance - used throughout the entire application
_ai_service = None

def get_ai_service() -> AIService:
    """
    Get the global AI service instance
    This is the ONLY AI service used across the entire application
    """
    global _ai_service
    if _ai_service is None:
        _ai_service = AIService()
    return _ai_service


# Universal AI functions - used everywhere in the application
def ai_chat(message: str, language: str = 'en', user_id: Optional[int] = None,
            context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """Universal chat function - used across all apps"""
    return get_ai_service().chat(message, language, user_id, context)

def ai_analyze_business(business_idea: str, language: str = 'en', user_id: Optional[int] = None) -> Dict[str, Any]:
    """Universal business analysis function - used across all apps"""
    return get_ai_service().analyze_business(business_idea, language, user_id)

def ai_analyze_text(text: str, language: str = 'en', user_id: Optional[int] = None) -> Dict[str, Any]:
    """Universal text analysis function - used across all apps"""
    return get_ai_service().analyze_text(text, language, user_id)

def ai_generate_intelligent_content(content_type: str, context: Dict[str, Any],
                                  language: str = 'en', user_id: Optional[int] = None) -> Dict[str, Any]:
    """Universal intelligent content generation - used across all apps"""
    return get_ai_service().generate_intelligent_content(content_type, context, language, user_id)

def ai_get_status() -> Dict[str, Any]:
    """Universal status function - used across all apps"""
    return get_ai_service().get_status()

def ai_is_available() -> bool:
    """Universal availability check - used across all apps"""
    return get_ai_service().is_available()

def ai_reload_config():
    """Universal config reload - used across all apps"""
    return get_ai_service().reload_config()


# Language detection utility
def detect_language(text: str) -> str:
    """
    Simple language detection
    Used across the application for automatic language detection
    """
    if not text or len(text.strip()) == 0:
        return 'en'
    
    # Count Arabic characters
    arabic_chars = sum(1 for char in text if '\u0600' <= char <= '\u06FF')
    total_chars = len([char for char in text if char.isalnum()])
    
    if total_chars == 0:
        return 'en'
    
    # If more than 20% Arabic characters, consider it Arabic
    arabic_percentage = arabic_chars / total_chars
    return 'ar' if arabic_percentage > 0.2 else 'en'


# Auto-detect language and process
def ai_chat_auto(message: str, user_id: Optional[int] = None) -> Dict[str, Any]:
    """Chat with automatic language detection"""
    language = detect_language(message)
    return ai_chat(message, language, user_id)

def ai_analyze_business_auto(business_idea: str, user_id: Optional[int] = None) -> Dict[str, Any]:
    """Business analysis with automatic language detection"""
    language = detect_language(business_idea)
    return ai_analyze_business(business_idea, language, user_id)

def ai_analyze_text_auto(text: str, user_id: Optional[int] = None) -> Dict[str, Any]:
    """Text analysis with automatic language detection"""
    language = detect_language(text)
    return ai_analyze_text(text, language, user_id)


# Business Plan specific functions
def generate_business_plan_template(business_idea: str, language: str = 'en', user_id: Optional[int] = None) -> Dict[str, Any]:
    """Generate a business plan template for a given business idea"""
    prompt = f"""Generate a comprehensive business plan template for the following business idea: {business_idea}

    Please provide a structured business plan with the following sections:
    1. Executive Summary
    2. Company Description
    3. Market Analysis
    4. Organization & Management
    5. Service or Product Line
    6. Marketing & Sales
    7. Funding Request
    8. Financial Projections
    9. Appendix

    For each section, provide detailed guidance and examples specific to this business idea."""

    return ai_chat(prompt, language, user_id)


def generate_section_content(section_name: str, business_idea: str, context: Dict[str, Any] = None,
                           language: str = 'en', user_id: Optional[int] = None) -> Dict[str, Any]:
    """Generate content for a specific business plan section"""
    context_str = ""
    if context:
        context_str = f"\nAdditional context: {json.dumps(context, indent=2)}"

    prompt = f"""Generate detailed content for the "{section_name}" section of a business plan for: {business_idea}

    {context_str}

    Please provide comprehensive, professional content that would be suitable for investors and stakeholders."""

    return ai_chat(prompt, language, user_id)


def analyze_business_plan(business_plan_content: str, language: str = 'en', user_id: Optional[int] = None) -> Dict[str, Any]:
    """Analyze a business plan and provide feedback and suggestions"""
    prompt = f"""Analyze the following business plan and provide detailed feedback:

    {business_plan_content}

    Please provide:
    1. Strengths of the plan
    2. Areas for improvement
    3. Missing elements
    4. Market viability assessment
    5. Financial projections review
    6. Overall recommendations

    Be constructive and specific in your feedback."""

    return ai_chat(prompt, language, user_id)


def generate_complete_business_plan(business_idea: str, language: str = 'en', user_id: Optional[int] = None) -> Dict[str, Any]:
    """Generate a complete business plan for a given business idea"""
    prompt = f"""Generate a complete, comprehensive business plan for: {business_idea}

    Please provide a full business plan with all sections filled out with detailed, realistic content:

    1. Executive Summary (2-3 paragraphs)
    2. Company Description (detailed background and mission)
    3. Market Analysis (target market, competition, market size)
    4. Organization & Management (team structure, key personnel)
    5. Service or Product Line (detailed description, features, benefits)
    6. Marketing & Sales Strategy (pricing, promotion, distribution)
    7. Funding Request (if applicable, amount and use of funds)
    8. Financial Projections (revenue, expenses, profit projections)
    9. Risk Analysis (potential risks and mitigation strategies)
    10. Implementation Timeline (key milestones and deadlines)

    Make it professional, detailed, and investor-ready."""

    return ai_chat(prompt, language, user_id)


def generate_market_analysis(business_idea: str, target_market: str = None, language: str = 'en', user_id: Optional[int] = None) -> Dict[str, Any]:
    """Generate detailed market analysis for a business idea"""
    market_context = f" focusing on {target_market}" if target_market else ""

    prompt = f"""Generate a comprehensive market analysis for: {business_idea}{market_context}

    Please provide detailed analysis including:

    1. Market Size and Growth Potential
       - Total Addressable Market (TAM)
       - Serviceable Addressable Market (SAM)
       - Market growth trends and projections

    2. Target Market Segmentation
       - Primary target demographics
       - Customer personas and behavior
       - Market needs and pain points

    3. Competitive Analysis
       - Direct and indirect competitors
       - Competitive advantages and disadvantages
       - Market positioning opportunities

    4. Market Trends and Opportunities
       - Industry trends affecting the market
       - Emerging opportunities
       - Potential threats and challenges

    5. Market Entry Strategy
       - Recommended approach to enter the market
       - Barriers to entry
       - Success factors

    Provide specific data, statistics, and actionable insights where possible."""

    return ai_chat(prompt, language, user_id)
