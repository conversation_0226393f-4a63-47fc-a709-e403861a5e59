import React from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../hooks/useLanguage';
import { RTLText } from '../../../common';
import { User } from '../../../../store/authSlice';

interface WelcomeSectionProps {
  user: User | null;
}

const WelcomeSection: React.FC<WelcomeSectionProps> = ({ user }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  return (
    <div className="mb-8">
      <RTLText as="h1" className="text-2xl font-bold">
        {String(t('dashboard.welcome', 'Welcome'))}, {user?.username || String(t('common.user', 'User'))}!
      </RTLText>
      <RTLText as="p" className="text-gray-400 mt-1">
        {String(t('dashboard.overview', 'Dashboard Overview'))}
      </RTLText>
    </div>
  );
};

export default WelcomeSection;
