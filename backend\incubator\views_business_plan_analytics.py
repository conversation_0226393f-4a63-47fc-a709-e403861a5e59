"""
Business Plan Analytics Views
API endpoints for business plan analytics including time tracking, collaboration stats, and success metrics
"""

from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.utils import timezone
from django.db import models
from datetime import timedel<PERSON>
from .business_plan_analytics_service import BusinessPlanAnalyticsService
from .models_business_plan import BusinessPlan
from .models_business_plan_analytics import (
    BusinessPlanSession, BusinessPlanAnalytics, TemplateSuccessMetrics
)


class BusinessPlanAnalyticsViewSet(viewsets.ViewSet):
    """ViewSet for business plan analytics"""
    permission_classes = [IsAuthenticated]
    
    @action(detail=False, methods=['get'])
    def time_analytics(self, request):
        """Get time spent analytics"""
        business_plan_id = request.query_params.get('business_plan_id')
        user_id = request.query_params.get('user_id')
        date_range = int(request.query_params.get('date_range', 30))
        
        # Check permissions
        if business_plan_id:
            try:
                business_plan = BusinessPlan.objects.get(id=business_plan_id)
                if business_plan.owner != request.user and not request.user.is_staff:
                    return Response(
                        {'error': 'Permission denied'}, 
                        status=status.HTTP_403_FORBIDDEN
                    )
            except BusinessPlan.DoesNotExist:
                return Response(
                    {'error': 'Business plan not found'}, 
                    status=status.HTTP_404_NOT_FOUND
                )
        
        if user_id and int(user_id) != request.user.id and not request.user.is_staff:
            return Response(
                {'error': 'Permission denied'}, 
                status=status.HTTP_403_FORBIDDEN
            )
        
        analytics = BusinessPlanAnalyticsService.get_time_analytics(
            business_plan_id=business_plan_id,
            user_id=user_id,
            date_range=date_range
        )
        
        return Response(analytics)
    
    @action(detail=False, methods=['get'])
    def template_usage(self, request):
        """Get most used templates analytics"""
        date_range = int(request.query_params.get('date_range', 30))
        
        analytics = BusinessPlanAnalyticsService.get_template_usage_analytics(
            date_range=date_range
        )
        
        return Response(analytics)
    
    @action(detail=False, methods=['get'])
    def collaboration_stats(self, request):
        """Get collaboration statistics"""
        business_plan_id = request.query_params.get('business_plan_id')
        date_range = int(request.query_params.get('date_range', 30))
        
        # Check permissions for specific business plan
        if business_plan_id:
            try:
                business_plan = BusinessPlan.objects.get(id=business_plan_id)
                if business_plan.owner != request.user and not request.user.is_staff:
                    return Response(
                        {'error': 'Permission denied'}, 
                        status=status.HTTP_403_FORBIDDEN
                    )
            except BusinessPlan.DoesNotExist:
                return Response(
                    {'error': 'Business plan not found'}, 
                    status=status.HTTP_404_NOT_FOUND
                )
        
        analytics = BusinessPlanAnalyticsService.get_collaboration_analytics(
            business_plan_id=business_plan_id,
            date_range=date_range
        )
        
        return Response(analytics)
    
    @action(detail=False, methods=['get'])
    def export_stats(self, request):
        """Get export/download statistics"""
        business_plan_id = request.query_params.get('business_plan_id')
        date_range = int(request.query_params.get('date_range', 30))
        
        # Check permissions for specific business plan
        if business_plan_id:
            try:
                business_plan = BusinessPlan.objects.get(id=business_plan_id)
                if business_plan.owner != request.user and not request.user.is_staff:
                    return Response(
                        {'error': 'Permission denied'}, 
                        status=status.HTTP_403_FORBIDDEN
                    )
            except BusinessPlan.DoesNotExist:
                return Response(
                    {'error': 'Business plan not found'}, 
                    status=status.HTTP_404_NOT_FOUND
                )
        
        analytics = BusinessPlanAnalyticsService.get_export_analytics(
            business_plan_id=business_plan_id,
            date_range=date_range
        )
        
        return Response(analytics)
    
    @action(detail=False, methods=['get'])
    def success_metrics(self, request):
        """Get success rate metrics"""
        date_range = int(request.query_params.get('date_range', 30))
        
        analytics = BusinessPlanAnalyticsService.get_success_rate_metrics(
            date_range=date_range
        )
        
        return Response(analytics)
    
    @action(detail=False, methods=['get'])
    def dashboard_overview(self, request):
        """Get comprehensive dashboard overview"""
        date_range = int(request.query_params.get('date_range', 30))
        user_id = request.user.id
        
        # Get all analytics for the user
        time_analytics = BusinessPlanAnalyticsService.get_time_analytics(
            user_id=user_id,
            date_range=date_range
        )
        
        collaboration_analytics = BusinessPlanAnalyticsService.get_collaboration_analytics(
            date_range=date_range
        )
        
        export_analytics = BusinessPlanAnalyticsService.get_export_analytics(
            date_range=date_range
        )
        
        success_metrics = BusinessPlanAnalyticsService.get_success_rate_metrics(
            date_range=date_range
        )
        
        template_usage = BusinessPlanAnalyticsService.get_template_usage_analytics(
            date_range=date_range
        )
        
        # Get user's business plans
        user_plans = BusinessPlan.objects.filter(owner=request.user)
        
        # Calculate user-specific metrics
        user_metrics = {
            'total_plans': user_plans.count(),
            'completed_plans': user_plans.filter(status='completed').count(),
            'draft_plans': user_plans.filter(status='draft').count(),
            'in_progress_plans': user_plans.filter(status='in_progress').count(),
            'average_completion': user_plans.aggregate(
                avg=models.Avg('completion_percentage')
            )['avg'] or 0,
        }
        
        return Response({
            'time_analytics': time_analytics,
            'collaboration_analytics': collaboration_analytics,
            'export_analytics': export_analytics,
            'success_metrics': success_metrics,
            'template_usage': template_usage[:5],  # Top 5 templates
            'user_metrics': user_metrics,
        })
    
    @action(detail=False, methods=['post'])
    def start_session(self, request):
        """Start a new work session"""
        business_plan_id = request.data.get('business_plan_id')
        
        if not business_plan_id:
            return Response(
                {'error': 'business_plan_id is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            business_plan = BusinessPlan.objects.get(id=business_plan_id)
            if business_plan.owner != request.user and not request.user.is_staff:
                return Response(
                    {'error': 'Permission denied'}, 
                    status=status.HTTP_403_FORBIDDEN
                )
        except BusinessPlan.DoesNotExist:
            return Response(
                {'error': 'Business plan not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        session = BusinessPlanAnalyticsService.start_session(
            business_plan_id=business_plan_id,
            user=request.user,
            request=request
        )
        
        return Response({
            'session_id': session.session_id,
            'start_time': session.start_time,
        })
    
    @action(detail=False, methods=['post'])
    def end_session(self, request):
        """End a work session"""
        session_id = request.data.get('session_id')
        
        if not session_id:
            return Response(
                {'error': 'session_id is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        session = BusinessPlanAnalyticsService.end_session(session_id)
        
        if not session:
            return Response(
                {'error': 'Session not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Check permissions
        if session.user != request.user and not request.user.is_staff:
            return Response(
                {'error': 'Permission denied'}, 
                status=status.HTTP_403_FORBIDDEN
            )
        
        return Response({
            'session_id': session.session_id,
            'duration_seconds': session.get_duration().total_seconds(),
            'end_time': session.end_time,
        })
    
    @action(detail=False, methods=['post'])
    def track_collaboration(self, request):
        """Track collaboration activity"""
        business_plan_id = request.data.get('business_plan_id')
        action_type = request.data.get('action_type')
        
        if not business_plan_id or not action_type:
            return Response(
                {'error': 'business_plan_id and action_type are required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            business_plan = BusinessPlan.objects.get(id=business_plan_id)
            if business_plan.owner != request.user and not request.user.is_staff:
                return Response(
                    {'error': 'Permission denied'}, 
                    status=status.HTTP_403_FORBIDDEN
                )
        except BusinessPlan.DoesNotExist:
            return Response(
                {'error': 'Business plan not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        collaboration = BusinessPlanAnalyticsService.track_collaboration(
            business_plan_id=business_plan_id,
            user=request.user,
            action_type=action_type,
            section_id=request.data.get('section_id'),
            section_title=request.data.get('section_title', ''),
            content=request.data.get('content', ''),
            metadata=request.data.get('metadata', {}),
            ip_address=BusinessPlanAnalyticsService._get_client_ip(request),
        )
        
        return Response({
            'collaboration_id': collaboration.id,
            'timestamp': collaboration.created_at,
        })
    
    @action(detail=False, methods=['post'])
    def track_export(self, request):
        """Track export/download activity"""
        business_plan_id = request.data.get('business_plan_id')
        export_format = request.data.get('export_format')
        export_type = request.data.get('export_type')
        
        if not all([business_plan_id, export_format, export_type]):
            return Response(
                {'error': 'business_plan_id, export_format, and export_type are required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            business_plan = BusinessPlan.objects.get(id=business_plan_id)
            if business_plan.owner != request.user and not request.user.is_staff:
                return Response(
                    {'error': 'Permission denied'}, 
                    status=status.HTTP_403_FORBIDDEN
                )
        except BusinessPlan.DoesNotExist:
            return Response(
                {'error': 'Business plan not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        export = BusinessPlanAnalyticsService.track_export(
            business_plan_id=business_plan_id,
            user=request.user,
            export_format=export_format,
            export_type=export_type,
            sections_included=request.data.get('sections_included', []),
            file_size=request.data.get('file_size'),
            file_path=request.data.get('file_path', ''),
            ip_address=BusinessPlanAnalyticsService._get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            export_successful=request.data.get('export_successful', True),
            error_message=request.data.get('error_message', ''),
        )
        
        return Response({
            'export_id': export.id,
            'timestamp': export.created_at,
        })
