import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';
import { visualizer } from 'rollup-plugin-visualizer';
import viteCompression from 'vite-plugin-compression';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // Load env variables based on mode
  const env = loadEnv(mode, process.cwd(), '');
  const isAnalyze = mode === 'analyze';

  return {
    plugins: [
      react(),
      // Add compression plugin for production builds
      viteCompression({
        algorithm: 'gzip',
        ext: '.gz',
        threshold: 10240, // Only compress files larger than 10KB
        deleteOriginFile: false,
      }),
      // Add brotli compression for production builds
      viteCompression({
        algorithm: 'brotliCompress',
        ext: '.br',
        threshold: 10240,
        deleteOriginFile: false,
      }),
      // Add bundle analyzer in analyze mode
      isAnalyze && visualizer({
        open: true,
        filename: 'dist/stats.html',
        gzipSize: true,
        brotliSize: true,
      }),
    ].filter(Boolean),

    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
      },
    },

    build: {
      rollupOptions: {
        output: {
          manualChunks: (id) => {
            // Dynamic chunking based on dependencies
            if (id.includes('node_modules')) {
              // Core React dependencies
              if (id.includes('react/') || id.includes('react-dom/') || id.includes('react-router-dom/')) {
                return 'vendor-react';
              }

              // Redux dependencies
              if (id.includes('@reduxjs/toolkit') || id.includes('react-redux')) {
                return 'vendor-redux';
              }

              // i18n dependencies
              if (id.includes('i18next') || id.includes('react-i18next')) {
                return 'vendor-i18n';
              }

              // Chart dependencies
              if (id.includes('chart.js') || id.includes('react-chartjs-2') || id.includes('recharts')) {
                return 'vendor-charts';
              }

              // UI dependencies
              if (id.includes('lucide-react')) {
                return 'vendor-ui';
              }

              // Utility dependencies
              if (id.includes('axios') || id.includes('date-fns')) {
                return 'vendor-utils';
              }

              // PDF dependencies
              if (id.includes('jspdf') || id.includes('jspdf-autotable')) {
                return 'vendor-pdf';
              }

              // Other dependencies
              return 'vendor-other';
            }

            // Split app code by feature
            if (id.includes('/components/admin/')) {
              return 'feature-admin';
            }

            if (id.includes('/components/dashboard/')) {
              return 'feature-dashboard';
            }

            if (id.includes('/components/incubator/')) {
              return 'feature-incubator';
            }

            if (id.includes('/components/forum/')) {
              return 'feature-forum';
            }

            if (id.includes('/components/ui/')) {
              return 'feature-ui';
            }
          },
          // Customize chunk filenames
          chunkFileNames: 'assets/js/[name]-[hash].js',
          entryFileNames: 'assets/js/[name]-[hash].js',
          assetFileNames: ({ name }) => {
            // Group assets by type
            if (/\.(png|jpe?g|gif|svg|webp)$/.test(name ?? '')) {
              return 'assets/images/[name]-[hash][extname]';
            }

            if (/\.(woff2?|eot|ttf|otf)$/.test(name ?? '')) {
              return 'assets/fonts/[name]-[hash][extname]';
            }

            if (/\.css$/.test(name ?? '')) {
              return 'assets/css/[name]-[hash][extname]';
            }

            return 'assets/[ext]/[name]-[hash][extname]';
          },
        },
      },
      // Enable source maps for production
      sourcemap: process.env.NODE_ENV !== 'production',
      // Minify output
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: process.env.NODE_ENV === 'production',
          drop_debugger: process.env.NODE_ENV === 'production',
        },
      },
      // Optimize CSS
      cssCodeSplit: true,
      // Target modern browsers
      target: 'es2020',
      // Increase chunk size warning limit
      chunkSizeWarningLimit: 1000,
    },
    optimizeDeps: {
      // Exclude dependencies that cause issues with optimization
      exclude: ['lucide-react'],
      // Include dependencies that should be pre-bundled
      include: ['react', 'react-dom', 'react-router-dom'],
    },

    // Development server configuration
    server: {
      port: 3000,
      host: true,
      proxy: {
        // Proxy API requests to Django backend
        '/api': {
          target: 'http://localhost:8000',
          changeOrigin: true,
          secure: false,
          configure: (proxy, _options) => {
            proxy.on('error', (err, _req, _res) => {
                          });
            proxy.on('proxyReq', (proxyReq, req, _res) => {
                          });
            proxy.on('proxyRes', (proxyRes, req, _res) => {
                          });
          },
        },
      },
    },
  };
});
