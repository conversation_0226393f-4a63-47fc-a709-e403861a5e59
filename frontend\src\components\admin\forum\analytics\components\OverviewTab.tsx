import React from 'react';
import { MessageSquare, Users } from 'lucide-react';
import {
  <PERSON><PERSON><PERSON> as Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>ltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import StatsCard from './StatsCard';
import { useTranslation } from 'react-i18next';

interface OverviewTabProps {
  data: any;
}

const OverviewTab: React.FC<OverviewTabProps> = ({ data  }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  if (!data) return <div className="text-center py-8">t("admin.no.data.available", t("common.noDataAvailable", "No data available"))</div>;

  // Prepare data for charts
  const categoryData = data.active_categories.map((cat: any) => ({
    name: cat.name,
    threads: cat.thread_count,
    posts: cat.post_count,
    value: cat.total_activity
  }));

  const topicData = data.active_topics.map((topic: any) => ({
    name: topic.title,
    threads: topic.thread_count,
    posts: topic.post_count,
    value: topic.total_activity
  }));

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <StatsCard
          title={t("admin.total.threads", "Total Threads")}
          value={data.total_stats.threads}
          icon={<MessageSquare size={20} className="text-purple-400" />}
          change={data.weekly_stats.thread_growth}
          changeLabel="this week"
        />

        <StatsCard
          title={t("admin.total.posts", "Total Posts")}
          value={data.total_stats.posts}
          icon={<MessageSquare size={20} className="text-purple-400" />}
          change={data.weekly_stats.post_growth}
          changeLabel="this week"
        />

        <StatsCard
          title={t("admin.active.users", "Active Users")}
          value={data.weekly_stats.active_users}
          icon={<Users size={20} className="text-purple-400" />}
          subtitle={`Out of ${data.total_stats.users} total users`}
        />
      </div>

      {/* Active Categories Chart */}
      <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
        <h3 className="text-xl font-semibold mb-4">t("admin.most.active.categories", "Most Active Categories")</h3>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <RechartsBarChart
              data={categoryData}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#444" />
              <XAxis dataKey="name" stroke="#aaa" />
              <YAxis stroke="#aaa" />
              <Tooltip
                contentStyle={{ backgroundColor: '#1e1e3f', borderColor: '#444', color: '#fff' }}
              />
              <Legend />
              <Bar dataKey="threads" name="Threads" fill="#8884d8" />
              <Bar dataKey="posts" name="Posts" fill="#82ca9d" />
            </RechartsBarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Active Topics Chart */}
      <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
        <h3 className="text-xl font-semibold mb-4">t("admin.most.active.topics", "Most Active Topics")</h3>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <RechartsBarChart
              data={topicData}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              layout="vertical"
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#444" />
              <XAxis type="number" stroke="#aaa" />
              <YAxis dataKey="name" type="category" stroke="#aaa" width={150} />
              <Tooltip
                contentStyle={{ backgroundColor: '#1e1e3f', borderColor: '#444', color: '#fff' }}
              />
              <Legend />
              <Bar dataKey="threads" name="Threads" fill="#8884d8" />
              <Bar dataKey="posts" name="Posts" fill="#82ca9d" />
            </RechartsBarChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
};

export default OverviewTab;
