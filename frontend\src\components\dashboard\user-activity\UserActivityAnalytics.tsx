import React from 'react';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../../../store/hooks';
import { useLanguage } from '../../../hooks/useLanguage';
import { FileText, MessageSquare, Calendar, ThumbsUp, Activity, Award } from 'lucide-react';
import { useUserActivity } from './hooks';
import { ActivityStatCard, ActivityChart, RecentActivityList } from './components';
const UserActivityAnalytics: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL, language } = useLanguage();
  const { activity, loading, error, prepareActivityChartData } = useUserActivity();

  if (loading) {
    return (
      <div className={`flex justify-center py-12 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-900/30 backdrop-blur-sm rounded-lg p-4 border border-red-800/50">
        <div className="text-red-400">{error}</div>
      </div>
    );
  }

  if (!activity) {
    return (
      <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50">
        <div className="text-gray-400">{t('dashboard.userActivity.noData')}</div>
      </div>
    );
  }

  const chartData = prepareActivityChartData();

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-bold">{t('dashboard.userActivity.title')}</h2>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <ActivityStatCard
          title={t('dashboard.userActivity.postsCreated')}
          value={activity.post_count}
          icon={<FileText size={24} className="text-indigo-400" />}
          color="indigo"
        />
        <ActivityStatCard
          title={t('dashboard.userActivity.commentsMade')}
          value={activity.comment_count}
          icon={<MessageSquare size={24} className="text-purple-400" />}
          color="purple"
        />
        <ActivityStatCard
          title={t('dashboard.userActivity.eventsOrganized')}
          value={activity.event_count}
          icon={<Calendar size={24} className="text-blue-400" />}
          color="blue"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <ActivityStatCard
          title={t('dashboard.userActivity.likesReceived')}
          value={activity.likes_received}
          icon={<ThumbsUp size={24} className="text-green-400" />}
          color="green"
        />
        <ActivityStatCard
          title={t('dashboard.userActivity.engagementScore')}
          value={Math.round(activity.engagement_score)}
          icon={<Activity size={24} className="text-yellow-400" />}
          color="yellow"
        />
        <div className={`bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className="text-gray-400 text-sm">{t('dashboard.userActivity.activityRank')}</div>
            <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <Award size={24} className={`text-amber-400 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
              <span className="text-2xl font-bold">{activity.rank}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Activity Chart */}
      {chartData && <ActivityChart chartData={chartData} />}

      {/* Recent Activity */}
      <RecentActivityList activity={activity} />
    </div>
  );
};

export default UserActivityAnalytics;
