from django.db import models
from django.contrib.auth.models import User
from django.utils.text import slugify
from django.utils import timezone
from api.models import Tag
from api.storage import OptimizedImageStorage
from .content_filter import ContentFlag

class ForumCategory(models.Model):
    """Categories for organizing forum topics"""
    name = models.CharField(max_length=100)
    slug = models.SlugField(max_length=120, unique=True)
    description = models.TextField(blank=True)
    icon = models.CharField(max_length=50, blank=True, help_text="Icon name from Lucide icons")
    order = models.IntegerField(default=0)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name_plural = "Forum Categories"
        ordering = ['order', 'name']


class ForumTopic(models.Model):
    """Topics within forum categories"""
    title = models.CharField(max_length=200)
    slug = models.SlugField(max_length=220, unique=True)
    description = models.TextField(blank=True)
    category = models.ForeignKey(ForumCategory, on_delete=models.CASCADE, related_name='topics')
    icon = models.CharField(max_length=50, blank=True, help_text="Icon name from Lucide icons")
    image = models.ImageField(upload_to='forum_images/', storage=OptimizedImageStorage(), blank=True, null=True)
    is_pinned = models.BooleanField(default=False)
    is_locked = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_topics')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.title)
        super().save(*args, **kwargs)

    def __str__(self):
        return self.title

    @property
    def thread_count(self):
        return self.threads.count()

    @property
    def post_count(self):
        return sum(thread.post_count for thread in self.threads.all())

    @property
    def last_activity(self):
        threads = self.threads.order_by('-last_activity')
        return threads.first().last_activity if threads.exists() else self.updated_at

    class Meta:
        ordering = ['-is_pinned', '-updated_at']


class ForumThread(models.Model):
    """Discussion threads within topics"""
    MODERATION_STATUS = (
        ('approved', 'Approved'),
        ('pending', 'Pending Review'),
        ('rejected', 'Rejected'),
    )

    title = models.CharField(max_length=200)
    slug = models.SlugField(max_length=220)
    topic = models.ForeignKey(ForumTopic, on_delete=models.CASCADE, related_name='threads')
    author = models.ForeignKey(User, on_delete=models.CASCADE, related_name='forum_threads')
    content = models.TextField()
    tags = models.ManyToManyField(Tag, related_name='forum_threads', blank=True)
    is_pinned = models.BooleanField(default=False)
    is_locked = models.BooleanField(default=False)
    views = models.PositiveIntegerField(default=0)
    moderation_status = models.CharField(max_length=20, choices=MODERATION_STATUS, default='pending')
    moderation_comment = models.TextField(blank=True, null=True)
    moderated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='moderated_threads')
    moderated_at = models.DateTimeField(null=True, blank=True)
    last_activity = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.title)
        super().save(*args, **kwargs)

    def __str__(self):
        return self.title

    @property
    def post_count(self):
        return self.posts.count()

    class Meta:
        ordering = ['-is_pinned', '-last_activity']
        unique_together = ['slug', 'topic']


class ForumPost(models.Model):
    """Posts within forum threads"""
    MODERATION_STATUS = (
        ('approved', 'Approved'),
        ('pending', 'Pending Review'),
        ('rejected', 'Rejected'),
    )

    thread = models.ForeignKey(ForumThread, on_delete=models.CASCADE, related_name='posts')
    author = models.ForeignKey(User, on_delete=models.CASCADE, related_name='forum_posts')
    content = models.TextField()
    likes = models.ManyToManyField(User, related_name='liked_forum_posts', blank=True)
    is_solution = models.BooleanField(default=False)
    moderation_status = models.CharField(max_length=20, choices=MODERATION_STATUS, default='pending')
    moderation_comment = models.TextField(blank=True, null=True)
    moderated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='moderated_forum_posts')
    moderated_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Post by {self.author.username} in {self.thread.title}"

    @property
    def like_count(self):
        return self.likes.count()

    def save(self, *args, **kwargs):
        is_new = self.pk is None
        super().save(*args, **kwargs)

        # Update thread's last_activity timestamp
        if is_new:
            self.thread.last_activity = self.created_at
            self.thread.save(update_fields=['last_activity'])

    class Meta:
        ordering = ['created_at']


class UserReputation(models.Model):
    """User reputation system for the forum"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='reputation')
    points = models.IntegerField(default=0)
    level = models.CharField(max_length=50, default='Newcomer')
    threads_created = models.PositiveIntegerField(default=0)
    posts_created = models.PositiveIntegerField(default=0)
    solutions_provided = models.PositiveIntegerField(default=0)
    likes_received = models.PositiveIntegerField(default=0)
    likes_given = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username}'s reputation ({self.points} points)"

    def update_level(self):
        """Update user level based on points"""
        if self.points >= 1000:
            self.level = 'Expert'
        elif self.points >= 500:
            self.level = 'Advanced'
        elif self.points >= 100:
            self.level = 'Intermediate'
        elif self.points >= 25:
            self.level = 'Beginner'
        else:
            self.level = 'Newcomer'
        self.save()

    def calculate_level(self):
        """Calculate user level based on points"""
        if self.points < 10:
            return 'Newcomer'
        elif self.points < 50:
            return 'Contributor'
        elif self.points < 200:
            return 'Active Member'
        elif self.points < 500:
            return 'Expert'
        elif self.points < 1000:
            return 'Mentor'
        else:
            return 'Community Leader'

    def update_level(self):
        """Update the user's level based on current points"""
        new_level = self.calculate_level()
        if new_level != self.level:
            self.level = new_level
            self.save(update_fields=['level'])


class ReputationActivity(models.Model):
    """Record of activities that affect user reputation"""
    ACTIVITY_TYPES = (
        ('thread_created', 'Created Thread'),
        ('post_created', 'Created Post'),
        ('solution_provided', 'Provided Solution'),
        ('like_received', 'Received Like'),
        ('like_given', 'Gave Like'),
        ('moderation_action', 'Moderation Action'),
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='reputation_activities')
    activity_type = models.CharField(max_length=50, choices=ACTIVITY_TYPES)
    points = models.IntegerField()
    thread = models.ForeignKey(ForumThread, on_delete=models.SET_NULL, null=True, blank=True)
    post = models.ForeignKey(ForumPost, on_delete=models.SET_NULL, null=True, blank=True)
    description = models.CharField(max_length=255)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user.username}: {self.activity_type} ({self.points} points)"

    class Meta:
        ordering = ['-created_at']
        verbose_name_plural = "Reputation Activities"


class TopicSubscription(models.Model):
    """User subscriptions to forum topics"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='topic_subscriptions')
    topic = models.ForeignKey(ForumTopic, on_delete=models.CASCADE, related_name='subscriptions')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['user', 'topic']
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} subscribed to {self.topic.title}"


class ThreadSubscription(models.Model):
    """User subscriptions to forum threads"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='thread_subscriptions')
    thread = models.ForeignKey(ForumThread, on_delete=models.CASCADE, related_name='subscriptions')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['user', 'thread']
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} subscribed to {self.thread.title}"


def forum_attachment_path(instance, filename):
    """Generate path for forum attachments"""
    # Get the file extension
    ext = filename.split('.')[-1]
    # Generate a unique filename
    unique_filename = f"{timezone.now().strftime('%Y%m%d%H%M%S')}_{filename}"
    # Return the path
    if hasattr(instance, 'post') and instance.post:
        return f"forum/attachments/posts/{instance.post.thread.id}/{unique_filename}"
    else:  # Thread attachment
        return f"forum/attachments/threads/{instance.thread.id}/{unique_filename}"


class ForumAttachment(models.Model):
    """Attachments for forum threads and posts"""
    ATTACHMENT_TYPES = (
        ('image', 'Image'),
        ('video', 'Video'),
        ('document', 'Document'),
        ('other', 'Other'),
    )

    file = models.FileField(upload_to=forum_attachment_path)
    file_type = models.CharField(max_length=20, choices=ATTACHMENT_TYPES)
    file_size = models.PositiveIntegerField()  # Size in bytes
    file_name = models.CharField(max_length=255)
    content_type = models.CharField(max_length=100)
    thread = models.ForeignKey(
        ForumThread,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='attachments'
    )
    post = models.ForeignKey(
        ForumPost,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='attachments'
    )
    uploaded_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return self.file_name

    def save(self, *args, **kwargs):
        # Set file size if not already set
        if not self.file_size and self.file:
            self.file_size = self.file.size

        # Set file name if not already set
        if not self.file_name and self.file:
            self.file_name = self.file.name.split('/')[-1]

        # Determine file type based on extension
        if not self.file_type and self.file:
            ext = self.file.name.split('.')[-1].lower()
            if ext in ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg']:
                self.file_type = 'image'
            elif ext in ['mp4', 'webm', 'ogg', 'mov', 'avi']:
                self.file_type = 'video'
            elif ext in ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt']:
                self.file_type = 'document'
            else:
                self.file_type = 'other'

        super().save(*args, **kwargs)
