import React from 'react';
import InvestorDashboard from '../../components/dashboard/investor-dashboard/InvestorDashboard';

/**
 * DEDICATED INVESTOR DASHBOARD PAGE
 * 
 * This page is specifically for investor users only.
 * It provides investment management and opportunity tracking tools.
 * 
 * Key Features:
 * - Investment portfolio management
 * - Opportunity discovery and evaluation
 * - Due diligence tools and workflows
 * - Financial analytics and reporting
 * - Startup tracking and monitoring
 * - Investment performance metrics
 * - Deal flow management
 * - Risk assessment tools
 */
const InvestorDashboardPage: React.FC = () => {
  return <InvestorDashboard />;
};

export default InvestorDashboardPage;
