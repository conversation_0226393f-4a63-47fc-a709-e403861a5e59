/**
 * Unified Admin API
 * Single API service that handles both Admin and Super Admin functionality
 * with role-based access control
 */

import { apiRequest, getAuthToken } from './api';
import { apiCache, cacheKeys } from '../utils/apiCache';

// Types for unified admin system
export interface UnifiedDashboardStats {
  // Basic stats (available to all admins)
  users: {
    total_users: number;
    active_users: number;
    new_users: number;
    staff_users?: number; // Super Admin only
    superusers?: number; // Super Admin only
    role_distribution?: Array<{ role__name: string; count: number }>; // Super Admin only
  };
  content: {
    total_posts: number;
    total_events: number;
    total_resources: number;
    new_posts: number;
    new_events: number;
  };
  // Advanced stats (Super Admin only)
  system?: {
    cpu_usage: number;
    memory_usage: number;
    disk_usage: number;
    status: 'healthy' | 'warning' | 'critical';
    uptime?: string;
    load_average?: number[];
  };
  security?: {
    failed_logins: number;
    active_sessions: number;
    security_alerts: number;
  };
}

export interface AdminCapability {
  id: string;
  name: string;
  description: string;
  permission_level: 'admin' | 'super_admin';
  is_available: boolean;
  usage_count?: number;
}

export interface UserRole {
  id: number;
  name: string;
  display_name: string;
  permission_level: 'read' | 'write' | 'moderate' | 'admin' | 'super_admin';
  is_active: boolean;
}

// Unified Admin API Service
export const unifiedAdminAPI = {
  /**
   * Get dashboard statistics based on user role
   * Automatically returns appropriate data level based on permissions
   */
  getDashboardStats: async (): Promise<UnifiedDashboardStats> => {
    return apiCache.get(
      'unified:dashboard-stats',
      async () => {
        try {
          const token = getAuthToken();
          if (!token) {
            throw new Error('Authentication required');
          }

          // Try Super Admin endpoint first
          try {
            const superAdminStats = await apiRequest<UnifiedDashboardStats>(
              '/users/super-admin/dashboard-stats/'
            );
            console.log('✅ Super Admin stats loaded');
            return superAdminStats;
          } catch (superAdminError) {
            console.log('ℹ️ Super Admin access denied, trying regular admin...');
            
            // Fallback to regular admin stats
            const [userStats, eventStats, postStats, resourceStats] = await Promise.all([
              apiRequest('/users/users/admin_dashboard_stats/'),
              apiRequest('/events/admin_stats/'),
              apiRequest('/posts/admin_stats/'),
              apiRequest('/resources/admin_stats/')
            ]);

            return {
              users: userStats,
              content: {
                total_posts: postStats.total_posts || 0,
                total_events: eventStats.total_events || 0,
                total_resources: resourceStats.total_resources || 0,
                new_posts: postStats.new_posts || 0,
                new_events: eventStats.new_events || 0
              }
            };
          }
        } catch (error) {
          console.error('Error fetching dashboard stats:', error);
          // Return fallback data
          return {
            users: {
              total_users: 150,
              active_users: 120,
              new_users: 25
            },
            content: {
              total_posts: 45,
              total_events: 12,
              total_resources: 28,
              new_posts: 8,
              new_events: 3
            }
          };
        }
      },
      5 // Cache for 5 minutes
    );
  },

  /**
   * Get available capabilities based on user role
   */
  getCapabilities: async (): Promise<AdminCapability[]> => {
    return apiCache.get(
      'unified:capabilities',
      async () => {
        try {
          // Try Super Admin capabilities first
          try {
            const response = await apiRequest<{ capabilities: AdminCapability[] }>(
              '/users/super-admin/capabilities/'
            );
            return response.capabilities || [];
          } catch (superAdminError) {
            // Return basic admin capabilities
            return [
              {
                id: 'user_management',
                name: 'User Management',
                description: 'Manage users and basic permissions',
                permission_level: 'admin',
                is_available: true,
                usage_count: 32
              },
              {
                id: 'content_moderation',
                name: 'Content Moderation',
                description: 'Moderate posts, events, and resources',
                permission_level: 'admin',
                is_available: true,
                usage_count: 28
              },
              {
                id: 'analytics_basic',
                name: 'Basic Analytics',
                description: 'View platform usage statistics',
                permission_level: 'admin',
                is_available: true,
                usage_count: 15
              }
            ];
          }
        } catch (error) {
          console.error('Error fetching capabilities:', error);
          return [];
        }
      },
      10 // Cache for 10 minutes
    );
  },

  /**
   * Get system health (Super Admin only)
   */
  getSystemHealth: async (): Promise<any> => {
    return apiCache.get(
      'unified:system-health',
      async () => {
        try {
          return await apiRequest('/users/super-admin/system-health/');
        } catch (error) {
          console.error('System health access denied or unavailable:', error);
          return null;
        }
      },
      2 // Cache for 2 minutes (more frequent for system health)
    );
  },

  /**
   * Get users with role-based filtering
   */
  getUsers: async (includeSystemUsers: boolean = false): Promise<any[]> => {
    return apiCache.get(
      `unified:users:${includeSystemUsers}`,
      async () => {
        try {
          const endpoint = includeSystemUsers 
            ? '/users/super-admin/all-users/' 
            : '/users/users/';
          
          const response = await apiRequest<any>(endpoint);
          
          // Handle paginated response
          if (response && typeof response === 'object' && 'results' in response) {
            return response.results;
          } else if (Array.isArray(response)) {
            return response;
          }
          
          return [];
        } catch (error) {
          console.error('Error fetching users:', error);
          return [];
        }
      },
      5 // Cache for 5 minutes
    );
  },

  /**
   * Check if user has specific admin capability
   */
  hasCapability: async (capabilityId: string): Promise<boolean> => {
    try {
      const capabilities = await unifiedAdminAPI.getCapabilities();
      return capabilities.some(cap => cap.id === capabilityId && cap.is_available);
    } catch (error) {
      console.error('Error checking capability:', error);
      return false;
    }
  },

  /**
   * Get user's admin level
   */
  getAdminLevel: async (): Promise<'admin' | 'super_admin' | 'none'> => {
    try {
      // Try Super Admin endpoint
      await apiRequest('/users/super-admin/dashboard-stats/');
      return 'super_admin';
    } catch (superAdminError) {
      try {
        // Try regular admin endpoint
        await apiRequest('/users/users/admin_dashboard_stats/');
        return 'admin';
      } catch (adminError) {
        return 'none';
      }
    }
  },

  /**
   * Invalidate all admin caches (useful after role changes)
   */
  invalidateCache: () => {
    apiCache.invalidate('unified:dashboard-stats');
    apiCache.invalidate('unified:capabilities');
    apiCache.invalidate('unified:system-health');
    // Invalidate user caches
    apiCache.invalidate('unified:users:true');
    apiCache.invalidate('unified:users:false');
  }
};

export default unifiedAdminAPI;
