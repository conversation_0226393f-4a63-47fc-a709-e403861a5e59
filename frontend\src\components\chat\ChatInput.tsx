import React from 'react';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../../store/hooks';
import { RTLText } from '../common';
interface ChatInputProps {
  inputMessage: string;
  setInputMessage: (message: string) => void;
  handleSendMessage: () => void;
  handleKeyPress: (e: React.KeyboardEvent) => void;
  isLoading: boolean;
}

const ChatInput: React.FC<ChatInputProps> = ({ inputMessage,
  setInputMessage,
  handleSendMessage,
  handleKeyPress,
  isLoading
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { language } = useAppSelector(state => state.language);

  return (
    <div className="p-4 border-t border-gray-800">
      <div className={`flex ${isRTL ? "flex-row-reverse" : ""}`}>
        <textarea
          className={`flex-1 bg-gray-800 border border-gray-700 rounded-l-md p-3 text-white resize-none ${isRTL ? "flex-row-reverse" : ""}`}
          placeholder={t('chat.placeholder')}
          rows={2}
          value={inputMessage}
          onChange={(e) => setInputMessage(e.target.value)}
          onKeyDown={handleKeyPress}
          disabled={isLoading}
          dir={language === 'ar' ? 'rtl' : 'ltr'}
        />
        <button
          onClick={handleSendMessage}
          disabled={!inputMessage.trim() || isLoading}
          className={`px-4 bg-purple-600 hover:bg-purple-700 rounded-r-md text-white disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}
        >
          {isLoading ? (
            <div className="animate-spin h-5 w-5 border-2 border-white border-t-transparent rounded-full"></div>
          ) : (
            t('chat.send')
          )}
        </button>
      </div>
      <RTLText as="p" align="center" className="text-xs text-gray-500 mt-2">
        {t('chat.poweredBy')}
      </RTLText>
    </div>
  );
};

export default ChatInput;
