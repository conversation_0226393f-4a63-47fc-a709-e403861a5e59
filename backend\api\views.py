from rest_framework import viewsets, permissions, status, filters
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.db import models
from datetime import timedelta
from django.contrib.auth.models import User
from django_filters.rest_framework import DjangoFilterBackend
from .models import Event, Resource, Post, Comment, Tag, MembershipApplication
from .serializers import EventSerializer, ResourceSerializer, PostSerializer, CommentSerializer, TagSerializer, MembershipApplicationSerializer
from .permissions import IsAdminUser

class EventViewSet(viewsets.ModelViewSet):
    queryset = Event.objects.all().order_by('-date')
    serializer_class = EventSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['location', 'is_virtual', 'moderation_status']
    search_fields = ['title', 'description', 'location']
    ordering_fields = ['date', 'created_at', 'title']

    def get_queryset(self):
        # For regular users, only show approved events or their own events
        if self.request.user.is_authenticated and not (self.request.user.is_staff or self.request.user.is_superuser):
            return Event.objects.filter(
                models.Q(moderation_status='approved') |
                models.Q(organizer=self.request.user)
            ).order_by('-date')
        return super().get_queryset()

    def get_permissions(self):
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.AllowAny]
        elif self.action in ['destroy', 'update', 'partial_update']:
            # Users can edit/delete their own events, admins can edit/delete any event
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [permissions.IsAuthenticated]
        return [permission() for permission in permission_classes]

    def perform_update(self, serializer):
        # Check if user is the organizer or admin
        event = self.get_object()
        if not (self.request.user.is_staff or self.request.user.is_superuser or
                event.organizer == self.request.user):
            raise permissions.PermissionDenied("You can only edit your own events")
        serializer.save()

    def perform_destroy(self, instance):
        # Check if user is the organizer or admin
        if not (self.request.user.is_staff or self.request.user.is_superuser or
                instance.organizer == self.request.user):
            raise permissions.PermissionDenied("You can only delete your own events")
        instance.delete()

    @action(detail=False, methods=['get'], permission_classes=[IsAdminUser])
    def admin_stats(self, request):
        """Get statistics for the admin dashboard"""
        total_events = Event.objects.count()
        upcoming_events = Event.objects.filter(date__gt=timezone.now()).count()
        past_events = Event.objects.filter(date__lte=timezone.now()).count()

        # Get new events in the last 30 days
        thirty_days_ago = timezone.now() - timedelta(days=30)
        new_events = Event.objects.filter(created_at__gte=thirty_days_ago).count()

        # Get events by month (for the last 6 months)
        events_by_month = {}
        for i in range(6):
            month_start = timezone.now().replace(day=1) - timedelta(days=30*i)
            month_name = month_start.strftime('%B %Y')
            month_end = (month_start.replace(day=28) + timedelta(days=4)).replace(day=1) - timedelta(days=1)
            count = Event.objects.filter(date__gte=month_start, date__lte=month_end).count()
            events_by_month[month_name] = count

        # Get events by location
        from django.db.models import Count
        location_data = Event.objects.values('location').annotate(count=Count('id')).order_by('-count')[:5]
        events_by_location = {item['location']: item['count'] for item in location_data}

        # Get events with most attendees
        popular_events = Event.objects.annotate(attendee_count=Count('attendees')).order_by('-attendee_count')[:5]
        popular_events_data = [{
            'id': event.id,
            'title': event.title,
            'date': event.date,
            'location': event.location,
            'attendee_count': event.attendees.count(),
            'organizer': event.organizer.username
        } for event in popular_events]

        return Response({
            'total_events': total_events,
            'upcoming_events': upcoming_events,
            'past_events': past_events,
            'new_events': new_events,
            'events_by_month': events_by_month,
            'events_by_location': events_by_location,
            'popular_events': popular_events_data,
        })

    def perform_create(self, serializer):
        # If user is admin, auto-approve the event
        if self.request.user.is_staff or self.request.user.is_superuser:
            serializer.save(
                organizer_id=self.request.user.id,
                moderation_status='approved',
                moderated_by=self.request.user,
                moderated_at=timezone.now()
            )
        else:
            serializer.save(organizer_id=self.request.user.id)

    @action(detail=True, methods=['post'], permission_classes=[IsAdminUser])
    def moderate(self, request, pk=None):
        """Moderate an event (approve or reject)"""
        event = self.get_object()
        status_value = request.data.get('status')
        comment = request.data.get('comment', '')

        if status_value not in ['approved', 'rejected']:
            return Response(
                {"error": "Status must be 'approved' or 'rejected'"},
                status=status.HTTP_400_BAD_REQUEST
            )

        event.moderation_status = status_value
        event.moderation_comment = comment
        event.moderated_by = request.user
        event.moderated_at = timezone.now()
        event.save()

        return Response({
            "message": f"Event has been {status_value}",
            "event_id": event.id,
            "event_title": event.title,
            "moderation_status": event.moderation_status
        })

    @action(detail=True, methods=['post'])
    def attend(self, request, pk=None):
        event = self.get_object()
        user = request.user

        if event.attendees.filter(id=user.id).exists():
            return Response(
                {"message": "You are already attending this event"},
                status=status.HTTP_400_BAD_REQUEST
            )

        event.attendees.add(user)
        return Response({"message": "You are now attending this event"})

    @action(detail=True, methods=['post'])
    def unattend(self, request, pk=None):
        event = self.get_object()
        user = request.user

        if not event.attendees.filter(id=user.id).exists():
            return Response(
                {"message": "You are not attending this event"},
                status=status.HTTP_400_BAD_REQUEST
            )

        event.attendees.remove(user)
        return Response({"message": "You are no longer attending this event"})

class ResourceViewSet(viewsets.ModelViewSet):
    queryset = Resource.objects.all().order_by('-created_at')
    serializer_class = ResourceSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['resource_type']
    search_fields = ['title', 'description']
    ordering_fields = ['created_at', 'title']

    def get_permissions(self):
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.AllowAny]
        elif self.action in ['destroy', 'update', 'partial_update']:
            # Users can edit/delete their own resources, admins can edit/delete any resource
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [permissions.IsAuthenticated]
        return [permission() for permission in permission_classes]

    def perform_update(self, serializer):
        # Check if user is the author or admin
        resource = self.get_object()
        if not (self.request.user.is_staff or self.request.user.is_superuser or
                resource.author == self.request.user):
            raise permissions.PermissionDenied("You can only edit your own resources")
        serializer.save()

    def perform_destroy(self, instance):
        # Check if user is the author or admin
        if not (self.request.user.is_staff or self.request.user.is_superuser or
                instance.author == self.request.user):
            raise permissions.PermissionDenied("You can only delete your own resources")
        instance.delete()

    @action(detail=False, methods=['get'], permission_classes=[IsAdminUser])
    def admin_stats(self, request):
        """Get statistics for the admin dashboard"""
        total_resources = Resource.objects.count()

        # Get resources by type
        resource_types = Resource.objects.values('resource_type').distinct()
        resources_by_type = {}
        for resource_type in resource_types:
            type_name = resource_type['resource_type']
            count = Resource.objects.filter(resource_type=type_name).count()
            resources_by_type[type_name] = count

        # Get new resources in the last 30 days
        thirty_days_ago = timezone.now() - timedelta(days=30)
        new_resources = Resource.objects.filter(created_at__gte=thirty_days_ago).count()

        return Response({
            'total_resources': total_resources,
            'resources_by_type': resources_by_type,
            'new_resources': new_resources,
        })

    def perform_create(self, serializer):
        serializer.save(author_id=self.request.user.id)

class PostViewSet(viewsets.ModelViewSet):
    queryset = Post.objects.all().order_by('-created_at')
    serializer_class = PostSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['moderation_status']
    search_fields = ['title', 'content']
    ordering_fields = ['created_at', 'title']

    def get_queryset(self):
        # For regular users, only show approved posts or their own posts
        if self.request.user.is_authenticated and not (self.request.user.is_staff or self.request.user.is_superuser):
            return Post.objects.filter(
                models.Q(moderation_status='approved') |
                models.Q(author=self.request.user)
            ).order_by('-created_at')
        return super().get_queryset()

    def get_permissions(self):
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.AllowAny]
        elif self.action in ['destroy', 'update', 'partial_update']:
            # Users can edit/delete their own posts, admins can edit/delete any post
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [permissions.IsAuthenticated]
        return [permission() for permission in permission_classes]

    def perform_update(self, serializer):
        # Check if user is the author or admin
        post = self.get_object()
        if not (self.request.user.is_staff or self.request.user.is_superuser or
                post.author == self.request.user):
            raise permissions.PermissionDenied("You can only edit your own posts")
        serializer.save()

    def perform_destroy(self, instance):
        # Check if user is the author or admin
        if not (self.request.user.is_staff or self.request.user.is_superuser or
                instance.author == self.request.user):
            raise permissions.PermissionDenied("You can only delete your own posts")
        instance.delete()

    @action(detail=False, methods=['get'], permission_classes=[IsAdminUser])
    def admin_stats(self, request):
        """Get statistics for the admin dashboard"""
        total_posts = Post.objects.count()

        # Get posts with most likes
        # Use values() to avoid the property setter issue
        popular_posts = Post.objects.values('id', 'title', 'author__username').annotate(
            like_count=models.Count('likes')
        ).order_by('-like_count')[:5]

        popular_posts_data = [{
            'id': post['id'],
            'title': post['title'],
            'like_count': post['like_count'],
            'author': post['author__username']
        } for post in popular_posts]

        # Get new posts in the last 30 days
        thirty_days_ago = timezone.now() - timedelta(days=30)
        new_posts = Post.objects.filter(created_at__gte=thirty_days_ago).count()

        return Response({
            'total_posts': total_posts,
            'popular_posts': popular_posts_data,
            'new_posts': new_posts,
        })

    def perform_create(self, serializer):
        # If user is admin, auto-approve the post
        if self.request.user.is_staff or self.request.user.is_superuser:
            serializer.save(
                author_id=self.request.user.id,
                moderation_status='approved',
                moderated_by=self.request.user,
                moderated_at=timezone.now()
            )
        else:
            serializer.save(author_id=self.request.user.id)

    @action(detail=True, methods=['post'], permission_classes=[IsAdminUser])
    def moderate(self, request, pk=None):
        """Moderate a post (approve or reject)"""
        post = self.get_object()
        status_value = request.data.get('status')
        comment = request.data.get('comment', '')

        if status_value not in ['approved', 'rejected']:
            return Response(
                {"error": "Status must be 'approved' or 'rejected'"},
                status=status.HTTP_400_BAD_REQUEST
            )

        post.moderation_status = status_value
        post.moderation_comment = comment
        post.moderated_by = request.user
        post.moderated_at = timezone.now()
        post.save()

        return Response({
            "message": f"Post has been {status_value}",
            "post_id": post.id,
            "post_title": post.title,
            "moderation_status": post.moderation_status
        })

    @action(detail=True, methods=['post'])
    def like(self, request, pk=None):
        post = self.get_object()
        user = request.user

        if post.likes.filter(id=user.id).exists():
            return Response(
                {"message": "You already liked this post"},
                status=status.HTTP_400_BAD_REQUEST
            )

        post.likes.add(user)
        return Response({"message": "Post liked successfully"})

    @action(detail=True, methods=['post'])
    def unlike(self, request, pk=None):
        post = self.get_object()
        user = request.user

        if not post.likes.filter(id=user.id).exists():
            return Response(
                {"message": "You haven't liked this post"},
                status=status.HTTP_400_BAD_REQUEST
            )

        post.likes.remove(user)
        return Response({"message": "Post unliked successfully"})

class TagViewSet(viewsets.ModelViewSet):
    queryset = Tag.objects.all().order_by('name')
    serializer_class = TagSerializer
    filter_backends = [filters.SearchFilter]
    search_fields = ['name', 'description']

    def get_permissions(self):
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.AllowAny]
        else:
            # Only admin users can create, update, or delete tags
            permission_classes = [IsAdminUser]
        return [permission() for permission in permission_classes]

class CommentViewSet(viewsets.ModelViewSet):
    queryset = Comment.objects.all().order_by('-created_at')
    serializer_class = CommentSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['post', 'moderation_status']
    search_fields = ['content']
    ordering_fields = ['created_at']

    def get_queryset(self):
        # For regular users, only show approved comments or their own comments
        if self.request.user.is_authenticated and not (self.request.user.is_staff or self.request.user.is_superuser):
            return Comment.objects.filter(
                models.Q(moderation_status='approved') |
                models.Q(author=self.request.user)
            ).order_by('-created_at')
        return super().get_queryset()

    def get_permissions(self):
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.IsAuthenticated]
        elif self.action in ['destroy', 'update', 'partial_update']:
            # Users can edit/delete their own comments, admins can edit/delete any comment
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [permissions.IsAuthenticated]
        return [permission() for permission in permission_classes]

    def perform_update(self, serializer):
        # Check if user is the author or admin
        comment = self.get_object()
        if not (self.request.user.is_staff or self.request.user.is_superuser or
                comment.author == self.request.user):
            raise permissions.PermissionDenied("You can only edit your own comments")
        serializer.save()

    def perform_destroy(self, instance):
        # Check if user is the author or admin
        if not (self.request.user.is_staff or self.request.user.is_superuser or
                instance.author == self.request.user):
            raise permissions.PermissionDenied("You can only delete your own comments")
        instance.delete()

    def perform_create(self, serializer):
        # If user is admin, auto-approve the comment
        if self.request.user.is_staff or self.request.user.is_superuser:
            serializer.save(
                author_id=self.request.user.id,
                moderation_status='approved',
                moderated_by=self.request.user,
                moderated_at=timezone.now()
            )
        else:
            serializer.save(author_id=self.request.user.id)

    @action(detail=True, methods=['post'], permission_classes=[IsAdminUser])
    def moderate(self, request, pk=None):
        """Moderate a comment (approve or reject)"""
        comment = self.get_object()
        status_value = request.data.get('status')
        mod_comment = request.data.get('comment', '')

        if status_value not in ['approved', 'rejected']:
            return Response(
                {"error": "Status must be 'approved' or 'rejected'"},
                status=status.HTTP_400_BAD_REQUEST
            )

        comment.moderation_status = status_value
        comment.moderation_comment = mod_comment
        comment.moderated_by = request.user
        comment.moderated_at = timezone.now()
        comment.save()

        return Response({
            "message": f"Comment has been {status_value}",
            "comment_id": comment.id,
            "comment_content": comment.content[:50] + ('...' if len(comment.content) > 50 else ''),
            "moderation_status": comment.moderation_status
        })

@api_view(['GET'])
@permission_classes([IsAdminUser])
def recent_activity(request):
    """Get recent activity for the admin dashboard"""
    # Get recent user registrations
    recent_users = User.objects.order_by('-date_joined')[:10]
    user_activities = [{
        'type': 'user_joined',
        'user': user.username,
        'timestamp': user.date_joined
    } for user in recent_users]

    # Get recent events
    recent_events = Event.objects.order_by('-created_at')[:10]
    event_activities = [{
        'type': 'event_created',
        'user': event.organizer.username,
        'timestamp': event.created_at
    } for event in recent_events]

    # Get recent resources
    recent_resources = Resource.objects.order_by('-created_at')[:10]
    resource_activities = [{
        'type': 'resource_shared',
        'user': resource.author.username,
        'timestamp': resource.created_at
    } for resource in recent_resources]

    # Get recent posts
    recent_posts = Post.objects.order_by('-created_at')[:10]
    post_activities = [{
        'type': 'post_created',
        'user': post.author.username,
        'timestamp': post.created_at
    } for post in recent_posts]

    # Combine all activities and sort by timestamp (most recent first)
    all_activities = user_activities + event_activities + resource_activities + post_activities
    all_activities.sort(key=lambda x: x['timestamp'], reverse=True)

    # Return the 10 most recent activities
    return Response(all_activities[:10])

class MembershipApplicationViewSet(viewsets.ModelViewSet):
    queryset = MembershipApplication.objects.all().order_by('-created_at')
    serializer_class = MembershipApplicationSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['status', 'expertise_level']
    search_fields = ['full_name', 'email', 'expertise_areas', 'background', 'motivation']

    def get_permissions(self):
        if self.action in ['create', 'list', 'retrieve']:
            # Anyone can apply, but only authenticated users can view applications
            permission_classes = [permissions.AllowAny if self.action == 'create' else permissions.IsAuthenticated]
        elif self.action in ['update', 'partial_update', 'destroy']:
            # Only admin users can update or delete applications
            permission_classes = [IsAdminUser]
        else:
            permission_classes = [permissions.IsAuthenticated]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        # Regular users can only see their own applications
        if self.request.user.is_authenticated and not (self.request.user.is_staff or self.request.user.is_superuser):
            return MembershipApplication.objects.filter(
                models.Q(user=self.request.user) |
                models.Q(email=self.request.user.email)
            ).order_by('-created_at')
        return super().get_queryset()

    def perform_create(self, serializer):
        # Link to user if authenticated
        if self.request.user.is_authenticated:
            serializer.save(user=self.request.user)
        else:
            serializer.save()

    @action(detail=True, methods=['post'], permission_classes=[IsAdminUser])
    def review(self, request, pk=None):
        """Review a membership application (approve or reject)"""
        application = self.get_object()
        status_value = request.data.get('status')
        notes = request.data.get('notes', '')

        if status_value not in ['approved', 'rejected']:
            return Response(
                {"error": "Status must be 'approved' or 'rejected'"},
                status=status.HTTP_400_BAD_REQUEST
            )

        application.status = status_value
        application.review_notes = notes
        application.reviewed_by = request.user
        application.reviewed_at = timezone.now()
        application.save()

        return Response({
            "message": f"Application has been {status_value}",
            "application_id": application.id,
            "applicant_name": application.full_name,
            "status": application.status
        })