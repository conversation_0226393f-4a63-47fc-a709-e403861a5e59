from django.core.management.base import BaseCommand
from dotenv import load_dotenv
import os
import google.generativeai as genai


class Command(BaseCommand):
    help = 'Reload environment variables and reconfigure Gemini API'

    def handle(self, *args, **options):
        self.stdout.write('Reloading environment variables...')
        
        # Reload .env file
        load_dotenv(override=True)
        
        # Get the new API key
        new_api_key = os.getenv('GEMINI_API_KEY')
        
        if new_api_key and new_api_key != 'YOUR_NEW_API_KEY_HERE':
            # Reconfigure Gemini API
            genai.configure(api_key=new_api_key)
            
            # Update the module-level variable
            from ai_recommendations import gemini_chat_service
            gemini_chat_service.GEMINI_API_KEY = new_api_key
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'✅ Successfully reloaded API key: {new_api_key[:10]}...'
                )
            )
            
            # Test the API
            try:
                model = genai.GenerativeModel('gemini-1.5-pro')
                response = model.generate_content("Test message")
                self.stdout.write(
                    self.style.SUCCESS('✅ Gemini API test successful!')
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'❌ Gemini API test failed: {str(e)}')
                )
        else:
            self.stdout.write(
                self.style.WARNING('⚠️ No valid API key found in .env file')
            )
