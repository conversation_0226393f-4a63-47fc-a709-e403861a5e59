import { api<PERSON>e<PERSON>, PaginatedResponse, User, Tag } from './api';

// Forum Types
export interface ForumCategory {
  id: number;
  name: string;
  slug: string;
  description: string;
  icon: string;
  order: number;
  is_active: boolean;
  topics: ForumTopic[];
  topic_count: number;
  thread_count: number;
  post_count: number;
  created_at: string;
  updated_at: string;
}

export interface ForumTopic {
  id: number;
  title: string;
  slug: string;
  description: string;
  category: number;
  icon: string;
  image: string | null;
  is_pinned: boolean;
  is_locked: boolean;
  is_active: boolean;
  created_by: User;
  thread_count: number;
  post_count: number;
  last_activity: string;
  threads: ForumThread[];
  created_at: string;
  updated_at: string;
}

export interface ForumThread {
  id: number;
  title: string;
  slug: string;
  topic: number;
  author: User;
  content: string;
  tags: Tag[];
  is_pinned: boolean;
  is_locked: boolean;
  views: number;
  post_count: number;
  posts: ForumPost[];
  author_reputation: {
    level: string;
    points: number;
  };
  moderation_status: 'approved' | 'pending' | 'rejected';
  moderation_comment?: string;
  last_activity: string;
  created_at: string;
  updated_at: string;
}

export interface ForumPost {
  id: number;
  thread: number;
  author: User;
  content: string;
  like_count: number;
  is_liked: boolean;
  is_solution: boolean;
  author_reputation: {
    level: string;
    points: number;
  };
  moderation_status: 'approved' | 'pending' | 'rejected';
  created_at: string;
  updated_at: string;
}

export interface UserReputation {
  id: number;
  username: string;
  points: number;
  level: string;
  threads_created: number;
  posts_created: number;
  solutions_provided: number;
  likes_received: number;
  likes_given: number;
}

export interface ReputationActivity {
  id: number;
  username: string;
  activity_type: string;
  points: number;
  description: string;
  created_at: string;
}

// Forum Categories API
export const forumCategoriesAPI = {
  getCategories: async () => {
    try {
      const response = await apiRequest<PaginatedResponse<ForumCategory>>('/forums/categories/');

      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results;
      } else if (Array.isArray(response)) {
        return response;
      }

      console.error('API returned unexpected format for forum categories:', response);
      return [];
    } catch (error) {
      console.error('Error fetching forum categories:', error);
      return [];
    }
  },

  getCategory: (slug: string) =>
    apiRequest<ForumCategory>(`/forums/categories/${slug}/`),

  createCategory: (categoryData: Partial<ForumCategory>) =>
    apiRequest<ForumCategory>('/forums/categories/', 'POST', categoryData),

  updateCategory: (slug: string, categoryData: Partial<ForumCategory>) =>
    apiRequest<ForumCategory>(`/forums/categories/${slug}/`, 'PUT', categoryData),

  deleteCategory: (slug: string) =>
    apiRequest<void>(`/forums/categories/${slug}/`, 'DELETE'),
};

// Forum Topics API
export const forumTopicsAPI = {
  getTopics: async (categorySlug?: string) => {
    try {
      const endpoint = categorySlug
        ? `/forums/topics/?category__slug=${categorySlug}`
        : '/forums/topics/';

      const response = await apiRequest<PaginatedResponse<ForumTopic>>(endpoint);

      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results;
      } else if (Array.isArray(response)) {
        return response;
      }

      console.error('API returned unexpected format for forum topics:', response);
      return [];
    } catch (error) {
      console.error('Error fetching forum topics:', error);
      return [];
    }
  },

  getTopic: (slug: string) =>
    apiRequest<ForumTopic>(`/forums/topics/${slug}/`),

  createTopic: (topicData: Partial<ForumTopic> & { created_by_id: number }) =>
    apiRequest<ForumTopic>('/forums/topics/', 'POST', topicData),

  updateTopic: (slug: string, topicData: Partial<ForumTopic>) =>
    apiRequest<ForumTopic>(`/forums/topics/${slug}/`, 'PUT', topicData),

  deleteTopic: (slug: string) =>
    apiRequest<void>(`/forums/topics/${slug}/`, 'DELETE'),

  pinTopic: (slug: string) =>
    apiRequest<{ message: string, is_pinned: boolean }>(`/forums/topics/${slug}/pin/`, 'POST'),

  lockTopic: (slug: string) =>
    apiRequest<{ message: string, is_locked: boolean }>(`/forums/topics/${slug}/lock/`, 'POST'),
};

// Forum Threads API
export const forumThreadsAPI = {
  getThreads: async (topicId?: number) => {
    try {
      const endpoint = topicId
        ? `/forums/threads/?topic=${topicId}`
        : '/forums/threads/';

      const response = await apiRequest<PaginatedResponse<ForumThread>>(endpoint);

      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results;
      } else if (Array.isArray(response)) {
        return response;
      }

      console.error('API returned unexpected format for forum threads:', response);
      return [];
    } catch (error) {
      console.error('Error fetching forum threads:', error);
      return [];
    }
  },

  getThread: (id: number) =>
    apiRequest<ForumThread>(`/forums/threads/${id}/`),

  createThread: (threadData: Partial<ForumThread> & { author_id: number, topic: number }) =>
    apiRequest<ForumThread>('/forums/threads/', 'POST', threadData),

  updateThread: (id: number, threadData: Partial<ForumThread>) =>
    apiRequest<ForumThread>(`/forums/threads/${id}/`, 'PUT', threadData),

  deleteThread: (id: number) =>
    apiRequest<void>(`/forums/threads/${id}/`, 'DELETE'),

  pinThread: (id: number) =>
    apiRequest<{ message: string, is_pinned: boolean }>(`/forums/threads/${id}/pin/`, 'POST'),

  lockThread: (id: number) =>
    apiRequest<{ message: string, is_locked: boolean }>(`/forums/threads/${id}/lock/`, 'POST'),

  moderateThread: (id: number, status: 'approved' | 'rejected', comment: string = '') =>
    apiRequest<{ message: string, thread_id: number, thread_title: string, moderation_status: string }>(
      `/forums/threads/${id}/moderate/`,
      'POST',
      { status, comment }
    ),

  bulkModerateThreads: (threadIds: number[], status: 'approved' | 'rejected', comment: string = '') =>
    apiRequest<{ message: string, thread_count: number }>(
      `/forums/threads/bulk_moderate/`,
      'POST',
      { thread_ids: threadIds, status, comment }
    ),
};

// Forum Posts API
export const forumPostsAPI = {
  getPosts: async (threadId?: number) => {
    try {
      const endpoint = threadId
        ? `/forums/posts/?thread=${threadId}`
        : '/forums/posts/';

      const response = await apiRequest<PaginatedResponse<ForumPost>>(endpoint);

      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results;
      } else if (Array.isArray(response)) {
        return response;
      }

      console.error('API returned unexpected format for forum posts:', response);
      return [];
    } catch (error) {
      console.error('Error fetching forum posts:', error);
      return [];
    }
  },

  getPost: (id: number) =>
    apiRequest<ForumPost>(`/forums/posts/${id}/`),

  createPost: (postData: { thread: number, author_id: number, content: string }) =>
    apiRequest<ForumPost>('/forums/posts/', 'POST', postData),

  updatePost: (id: number, postData: Partial<ForumPost>) =>
    apiRequest<ForumPost>(`/forums/posts/${id}/`, 'PUT', postData),

  deletePost: (id: number) =>
    apiRequest<void>(`/forums/posts/${id}/`, 'DELETE'),

  likePost: (id: number) =>
    apiRequest<{ message: string }>(`/forums/posts/${id}/like/`, 'POST'),

  unlikePost: (id: number) =>
    apiRequest<{ message: string }>(`/forums/posts/${id}/unlike/`, 'POST'),

  markAsSolution: (id: number) =>
    apiRequest<{ message: string, post_id: number }>(`/forums/posts/${id}/mark_as_solution/`, 'POST'),

  moderatePost: (id: number, status: 'approved' | 'rejected', comment: string = '') =>
    apiRequest<{ message: string, post_id: number, moderation_status: string }>(
      `/forums/posts/${id}/moderate/`,
      'POST',
      { status, comment }
    ),

  bulkModeratePosts: (postIds: number[], status: 'approved' | 'rejected', comment: string = '') =>
    apiRequest<{ message: string, post_count: number }>(
      `/forums/posts/bulk_moderate/`,
      'POST',
      { post_ids: postIds, status, comment }
    ),
};

// User Reputation API
export const userReputationAPI = {
  getLeaderboard: () =>
    apiRequest<UserReputation[]>('/forums/reputation/leaderboard/'),

  getMyReputation: () =>
    apiRequest<UserReputation>('/forums/reputation/my_reputation/'),

  getReputationActivities: () =>
    apiRequest<PaginatedResponse<ReputationActivity>>('/forums/reputation-activities/'),

  getMyActivities: () =>
    apiRequest<PaginatedResponse<ReputationActivity>>('/forums/reputation-activities/my_activities/'),

  getUserBadges: (userId?: number) => {
    const url = userId ? `/forums/badges/${userId}/` : '/forums/badges/';
    return apiRequest<any>(url, 'GET');
  },

  getAvailableBadges: () => {
    return apiRequest<any>('/forums/badges/available/', 'GET');
  }
};

// Forum Analytics API
export const forumAnalyticsAPI = {
  getForumAnalytics: (action: string, params: Record<string, any> = {}) => {
    const queryParams = new URLSearchParams({ action, ...params });
    return apiRequest<any>(`/forums/analytics/?${queryParams.toString()}`, 'GET');
  }
};

// Forum Search API
export const forumSearchAPI = {
  searchForum: (params: {
    q: string;
    type?: 'all' | 'threads' | 'posts';
    author?: string;
    tag?: string;
    date?: string;
    attachments?: string;
    attachment_type?: string;
    sort?: string;
    page?: number;
    page_size?: number;
  }) => {
    const queryParams = new URLSearchParams();

    // Add all non-empty params to query string
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    return apiRequest<{
      count: number;
      next: string | null;
      previous: string | null;
      results: any[];
    }>(`/forums/search/?${queryParams.toString()}`, 'GET');
  }
};

// Combined Forum API for easier imports
export const forumApi = {
  // Categories
  getCategories: forumCategoriesAPI.getCategories,
  getCategory: forumCategoriesAPI.getCategory,
  createCategory: forumCategoriesAPI.createCategory,
  updateCategory: forumCategoriesAPI.updateCategory,
  deleteCategory: forumCategoriesAPI.deleteCategory,

  // Topics
  getTopics: forumTopicsAPI.getTopics,
  getTopic: forumTopicsAPI.getTopic,
  createTopic: forumTopicsAPI.createTopic,
  updateTopic: forumTopicsAPI.updateTopic,
  deleteTopic: forumTopicsAPI.deleteTopic,
  pinTopic: forumTopicsAPI.pinTopic,
  lockTopic: forumTopicsAPI.lockTopic,

  // Threads
  getThreads: forumThreadsAPI.getThreads,
  getThread: forumThreadsAPI.getThread,
  createThread: forumThreadsAPI.createThread,
  updateThread: forumThreadsAPI.updateThread,
  deleteThread: forumThreadsAPI.deleteThread,
  pinThread: forumThreadsAPI.pinThread,
  lockThread: forumThreadsAPI.lockThread,
  moderateThread: forumThreadsAPI.moderateThread,
  bulkModerateThreads: forumThreadsAPI.bulkModerateThreads,

  // Posts
  getPosts: forumPostsAPI.getPosts,
  getPost: forumPostsAPI.getPost,
  createPost: forumPostsAPI.createPost,
  updatePost: forumPostsAPI.updatePost,
  deletePost: forumPostsAPI.deletePost,
  likePost: forumPostsAPI.likePost,
  unlikePost: forumPostsAPI.unlikePost,
  markAsSolution: forumPostsAPI.markAsSolution,
  moderatePost: forumPostsAPI.moderatePost,
  bulkModeratePosts: forumPostsAPI.bulkModeratePosts,

  // Reputation
  getLeaderboard: userReputationAPI.getLeaderboard,
  getMyReputation: userReputationAPI.getMyReputation,
  getReputationActivities: userReputationAPI.getReputationActivities,
  getMyActivities: userReputationAPI.getMyActivities,
  getUserBadges: userReputationAPI.getUserBadges,
  getAvailableBadges: userReputationAPI.getAvailableBadges,

  // Analytics
  getForumAnalytics: forumAnalyticsAPI.getForumAnalytics,

  // Search
  searchForum: forumSearchAPI.searchForum
};
