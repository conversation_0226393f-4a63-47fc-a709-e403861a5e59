import { useEffect } from 'react';
import { useAppDispatch } from '../store/hooks';
import { getCurrentUser } from '../store/authSlice';
import { getAuthToken, getRefreshToken } from '../services/api';

const AuthInitializer = () => {
  const dispatch = useAppDispatch();

  useEffect(() => {
    // Only try to get the current user if we have auth tokens
    const initAuth = async () => {
      if (process.env.NODE_ENV === 'development') {
        console.log("Initializing auth state...");
      }

      // Check if we have stored auth tokens
      const accessToken = getAuthToken();
      const refreshToken = getRefreshToken();

      if (!accessToken) {
        if (process.env.NODE_ENV === 'development') {
          console.log("No access token found, skipping auth initialization");
        }
        return;
      }

      try {
        if (process.env.NODE_ENV === 'development') {
          console.log("Access token found, fetching current user...");
        }
        const resultAction = await dispatch(getCurrentUser());
        if (getCurrentUser.fulfilled.match(resultAction)) {
          if (process.env.NODE_ENV === 'development') {
            console.log("Auth state initialized successfully:", resultAction.payload);
          }
        } else {
          if (process.env.NODE_ENV === 'development') {
            console.warn('Auth state initialization failed (token may be expired):', resultAction.error);
          }
          // Don't log this as an error since it's expected when tokens are expired
        }
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.warn('Failed to initialize auth state (this is normal if not logged in):', error);
        }
      }
    };

    initAuth();
  }, [dispatch]);

  // This component doesn't render anything
  return null;
};

export default AuthInitializer;
