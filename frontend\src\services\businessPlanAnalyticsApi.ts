/**
 * Business Plan Analytics API Service
 * Handles analytics tracking and data retrieval for business plans
 */

import { apiRequest } from './api';

// Types for analytics data
export interface TimeAnalytics {
  total_time_seconds: number;
  total_sessions: number;
  average_session_duration: number;
  daily_breakdown: Array<{
    day: string;
    total_sessions: number;
    avg_duration: number;
  }>;
  most_active_day?: {
    day: string;
    total_sessions: number;
  };
}

export interface TemplateUsageAnalytics {
  template__id: number;
  template__name: string;
  template__industry: string;
  usage_count: number;
  completion_rate: number;
  avg_time_to_complete: number;
  completion_success_rate: number;
  total_plans: number;
  completed_plans: number;
}

export interface CollaborationAnalytics {
  total_collaborations: number;
  unique_collaborators: number;
  action_breakdown: Array<{
    action_type: string;
    count: number;
  }>;
  user_activity: Array<{
    user__username: string;
    user__first_name: string;
    user__last_name: string;
    total_actions: number;
    comments: number;
    edits: number;
    reviews: number;
  }>;
  daily_activity: Array<{
    day: string;
    total_actions: number;
    unique_users: number;
  }>;
}

export interface ExportAnalytics {
  total_exports: number;
  successful_exports: number;
  success_rate: number;
  format_breakdown: Array<{
    export_format: string;
    count: number;
  }>;
  type_breakdown: Array<{
    export_type: string;
    count: number;
  }>;
  daily_exports: Array<{
    day: string;
    total_exports: number;
    unique_users: number;
  }>;
}

export interface SuccessMetrics {
  total_plans: number;
  completed_plans: number;
  published_plans: number;
  completion_rate: number;
  publish_rate: number;
  average_completion_percentage: number;
  average_time_to_complete_hours: number;
}

export interface DashboardOverview {
  time_analytics: TimeAnalytics;
  collaboration_analytics: CollaborationAnalytics;
  export_analytics: ExportAnalytics;
  success_metrics: SuccessMetrics;
  template_usage: TemplateUsageAnalytics[];
  user_metrics: {
    total_plans: number;
    completed_plans: number;
    draft_plans: number;
    in_progress_plans: number;
    average_completion: number;
  };
}

export interface SessionResponse {
  session_id: string;
  start_time: string;
  duration_seconds?: number;
  end_time?: string;
}

export interface CollaborationResponse {
  collaboration_id: number;
  timestamp: string;
}

export interface ExportResponse {
  export_id: number;
  timestamp: string;
}

// Business Plan Analytics API
export const businessPlanAnalyticsAPI = {
  /**
   * Get time spent analytics
   */
  async getTimeAnalytics(params: {
    business_plan_id?: number;
    user_id?: number;
    date_range?: number;
  } = {}): Promise<TimeAnalytics> {
    const queryParams = new URLSearchParams();
    if (params.business_plan_id) queryParams.append('business_plan_id', params.business_plan_id.toString());
    if (params.user_id) queryParams.append('user_id', params.user_id.toString());
    if (params.date_range) queryParams.append('date_range', params.date_range.toString());

    return await apiRequest<TimeAnalytics>(
      `/incubator/business-plan-analytics/time_analytics/?${queryParams.toString()}`
    );
  },

  /**
   * Get template usage analytics
   */
  async getTemplateUsageAnalytics(dateRange: number = 30): Promise<TemplateUsageAnalytics[]> {
    return await apiRequest<TemplateUsageAnalytics[]>(
      `/incubator/business-plan-analytics/template_usage/?date_range=${dateRange}`
    );
  },

  /**
   * Get collaboration statistics
   */
  async getCollaborationStats(params: {
    business_plan_id?: number;
    date_range?: number;
  } = {}): Promise<CollaborationAnalytics> {
    const queryParams = new URLSearchParams();
    if (params.business_plan_id) queryParams.append('business_plan_id', params.business_plan_id.toString());
    if (params.date_range) queryParams.append('date_range', params.date_range.toString());

    return await apiRequest<CollaborationAnalytics>(
      `/incubator/business-plan-analytics/collaboration_stats/?${queryParams.toString()}`
    );
  },

  /**
   * Get export/download statistics
   */
  async getExportStats(params: {
    business_plan_id?: number;
    date_range?: number;
  } = {}): Promise<ExportAnalytics> {
    const queryParams = new URLSearchParams();
    if (params.business_plan_id) queryParams.append('business_plan_id', params.business_plan_id.toString());
    if (params.date_range) queryParams.append('date_range', params.date_range.toString());

    return await apiRequest<ExportAnalytics>(
      `/incubator/business-plan-analytics/export_stats/?${queryParams.toString()}`
    );
  },

  /**
   * Get success rate metrics
   */
  async getSuccessMetrics(dateRange: number = 30): Promise<SuccessMetrics> {
    return await apiRequest<SuccessMetrics>(
      `/incubator/business-plan-analytics/success_metrics/?date_range=${dateRange}`
    );
  },

  /**
   * Get comprehensive dashboard overview
   */
  async getDashboardOverview(dateRange: number = 30): Promise<DashboardOverview> {
    return await apiRequest<DashboardOverview>(
      `/incubator/business-plan-analytics/dashboard_overview/?date_range=${dateRange}`
    );
  },

  /**
   * Start a new work session
   */
  async startSession(businessPlanId: number): Promise<SessionResponse> {
    return await apiRequest<SessionResponse>(
      '/incubator/business-plan-analytics/start_session/',
      'POST',
      { business_plan_id: businessPlanId }
    );
  },

  /**
   * End a work session
   */
  async endSession(sessionId: string): Promise<SessionResponse> {
    return await apiRequest<SessionResponse>(
      '/incubator/business-plan-analytics/end_session/',
      'POST',
      { session_id: sessionId }
    );
  },

  /**
   * Track collaboration activity
   */
  async trackCollaboration(data: {
    business_plan_id: number;
    action_type: string;
    section_id?: number;
    section_title?: string;
    content?: string;
    metadata?: Record<string, any>;
  }): Promise<CollaborationResponse> {
    return await apiRequest<CollaborationResponse>(
      '/incubator/business-plan-analytics/track_collaboration/',
      'POST',
      data
    );
  },

  /**
   * Track export/download activity
   */
  async trackExport(data: {
    business_plan_id: number;
    export_format: string;
    export_type: string;
    sections_included?: number[];
    file_size?: number;
    file_path?: string;
    export_successful?: boolean;
    error_message?: string;
  }): Promise<ExportResponse> {
    return await apiRequest<ExportResponse>(
      '/incubator/business-plan-analytics/track_export/',
      'POST',
      data
    );
  },
};

// Session management utility
export class BusinessPlanSessionManager {
  private static instance: BusinessPlanSessionManager;
  private currentSession: string | null = null;
  private businessPlanId: number | null = null;
  private startTime: Date | null = null;

  static getInstance(): BusinessPlanSessionManager {
    if (!BusinessPlanSessionManager.instance) {
      BusinessPlanSessionManager.instance = new BusinessPlanSessionManager();
    }
    return BusinessPlanSessionManager.instance;
  }

  async startSession(businessPlanId: number): Promise<string> {
    try {
      // End current session if exists
      if (this.currentSession) {
        await this.endSession();
      }

      const response = await businessPlanAnalyticsAPI.startSession(businessPlanId);
      this.currentSession = response.session_id;
      this.businessPlanId = businessPlanId;
      this.startTime = new Date();

      return response.session_id;
    } catch (error) {
      console.error('Failed to start session:', error);
      throw error;
    }
  }

  async endSession(): Promise<void> {
    if (!this.currentSession) return;

    try {
      await businessPlanAnalyticsAPI.endSession(this.currentSession);
    } catch (error) {
      console.error('Failed to end session:', error);
    } finally {
      this.currentSession = null;
      this.businessPlanId = null;
      this.startTime = null;
    }
  }

  getCurrentSession(): string | null {
    return this.currentSession;
  }

  getBusinessPlanId(): number | null {
    return this.businessPlanId;
  }

  getSessionDuration(): number {
    if (!this.startTime) return 0;
    return Date.now() - this.startTime.getTime();
  }

  async trackCollaboration(actionType: string, options: {
    sectionId?: number;
    sectionTitle?: string;
    content?: string;
    metadata?: Record<string, any>;
  } = {}): Promise<void> {
    if (!this.businessPlanId) return;

    try {
      await businessPlanAnalyticsAPI.trackCollaboration({
        business_plan_id: this.businessPlanId,
        action_type: actionType,
        section_id: options.sectionId,
        section_title: options.sectionTitle,
        content: options.content,
        metadata: options.metadata,
      });
    } catch (error) {
      console.error('Failed to track collaboration:', error);
    }
  }

  async trackExport(exportFormat: string, exportType: string, options: {
    sectionsIncluded?: number[];
    fileSize?: number;
    filePath?: string;
    exportSuccessful?: boolean;
    errorMessage?: string;
  } = {}): Promise<void> {
    if (!this.businessPlanId) return;

    try {
      await businessPlanAnalyticsAPI.trackExport({
        business_plan_id: this.businessPlanId,
        export_format: exportFormat,
        export_type: exportType,
        sections_included: options.sectionsIncluded,
        file_size: options.fileSize,
        file_path: options.filePath,
        export_successful: options.exportSuccessful,
        error_message: options.errorMessage,
      });
    } catch (error) {
      console.error('Failed to track export:', error);
    }
  }
}
