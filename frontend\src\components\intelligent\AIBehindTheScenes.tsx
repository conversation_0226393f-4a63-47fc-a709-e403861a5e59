import { useTranslation } from 'react-i18next';
/**
 * AI Behind the Scenes Demo
 * Shows how AI works invisibly to enhance user experience
 * No chat interface - just intelligent automation
 */

import React, { useState, useEffect } from 'react';
import {
  Brain,
  Zap,
  Eye,
  Target,
  TrendingUp,
  Users,
  Lightbulb,
  CheckCircle,
  Clock,
  ArrowRight,
  Sparkles,
  BarChart3,
  RefreshCw,
  Bell,
  FileText,
  DollarSign,
  Network,
} from 'lucide-react';

interface AIBehindTheScenesProps {
  language?: string;
  className?: string;
}

export const AIBehindTheScenes: React.FC<AIBehindTheScenesProps> = ({ language = 'en',
  className = '',
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [activeDemo, setActiveDemo] = useState<string>('smart-forms');
  const [aiActivity, setAiActivity] = useState<any[]>([]);
  const [isAiWorking, setIsAiWorking] = useState(false);

  useEffect(() => {
    // Simulate AI working in background
    const interval = setInterval(() => {
      addAiActivity();
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  const addAiActivity = () => {
    const activities = [
      {
        type: 'enhancement',
        icon: <Sparkles className="w-4 h-4 text-yellow-500" />,
        message: language === 'ar' ? 'تم تحسين فكرة العمل تلقائياً' : t("ai.business.idea.autoenhanced", "Business idea auto-enhanced"),
        time: new Date().toLocaleTimeString(),
      },
      {
        type: 'opportunity',
        icon: <DollarSign className="w-4 h-4 text-green-500" />,
        message: language === 'ar' ? 'تم العثور على 3 فرص تمويل مطابقة' : 'Found 3 matching funding opportunities',
        time: new Date().toLocaleTimeString(),
      },
      {
        type: 'analysis',
        icon: <BarChart3 className="w-4 h-4 text-blue-500" />,
        message: language === 'ar' ? 'تم تحديث نقاط صحة الأعمال' : t("ai.business.health.score", "Business health score updated"),
        time: new Date().toLocaleTimeString(),
      },
      {
        type: 'networking',
        icon: <Network className="w-4 h-4 text-purple-500" />,
        message: language === 'ar' ? 'تم العثور على موجه متوافق بنسبة 94%' : 'Found 94% compatible mentor',
        time: new Date().toLocaleTimeString(),
      },
    ];

    const randomActivity = activities[Math.floor(Math.random() * activities.length)];
    setAiActivity(prev => [randomActivity, ...prev.slice(0, 4)]);
  };

  const demos = [
    {
      id: 'smart-forms',
      title: language === 'ar' ? 'النماذج الذكية' : t("ai.smart.forms", "Smart Forms"),
      description: language === 'ar' 
        ? 'الذكاء الاصطناعي يملأ النماذج تلقائياً ويقترح تحسينات'
        : t("ai.ai.automatically.fills", "AI automatically fills forms and suggests improvements"),
      icon: <FileText className="w-6 h-6" />,
    },
    {
      id: 'auto-content',
      title: language === 'ar' ? 'إنشاء المحتوى التلقائي' : t("ai.auto.content.generation", "Auto Content Generation"),
      description: language === 'ar'
        ? 'إنشاء خطط الأعمال والعروض التقديمية تلقائياً'
        : t("ai.automatically.generate.business", "Automatically generate business plans and presentations"),
      icon: <Brain className="w-6 h-6" />,
    },
    {
      id: 'opportunity-detection',
      title: language === 'ar' ? 'كشف الفرص' : t("ai.opportunity.detection", "Opportunity Detection"),
      description: language === 'ar'
        ? 'الذكاء الاصطناعي يجد الفرص والشراكات المناسبة'
        : t("ai.ai.finds.relevant", "AI finds relevant opportunities and partnerships"),
      icon: <Eye className="w-6 h-6" />,
    },
    {
      id: 'progress-tracking',
      title: language === 'ar' ? 'تتبع التقدم' : t("ai.progress.tracking", "Progress Tracking"),
      description: language === 'ar'
        ? 'مراقبة تلقائية للتقدم مع اقتراحات للتحسين'
        : t("ai.automatic.progress.monitoring", "Automatic progress monitoring with improvement suggestions"),
      icon: <TrendingUp className="w-6 h-6" />,
    },
  ];

  const renderSmartFormsDemo = () => (
    <div className="bg-white rounded-lg p-6 border border-gray-200">
      <h4 className="font-semibold text-gray-900 mb-4">
        {language === 'ar' ? 'مثال: إنشاء فكرة عمل' : t("common.example.creating.business", "Example: Creating Business Idea")}
      </h4>
      
      <div className="space-y-4">
        {/* User Input */}
        <div className="border rounded-lg p-4 bg-gray-50">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {language === 'ar' ? 'عنوان الفكرة' : t("common.idea.title", "Idea Title")}
          </label>
          <input 
            type="text" 
            value="AI-Powered Fitness App"
            className="w-full p-2 border rounded-md"
            readOnly
          />
        </div>

        {/* AI Auto-Enhancement */}
        <div className="border-l-4 border-blue-500 pl-4 bg-blue-50 p-4 rounded-r-lg">
          <div className={`flex items-center mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
            <Sparkles className={`w-5 h-5 text-blue-500 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
            <span className="font-medium text-blue-900">
              {language === 'ar' ? 'الذكاء الاصطناعي يعمل...' : t("common.ai.working", "AI Working...")}
            </span>
          </div>
          
          <div className="space-y-2 text-sm">
            <div className={`flex items-center text-green-700 ${isRTL ? "flex-row-reverse" : ""}`}>
              <CheckCircle className={`w-4 h-4 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
              <span>{language === 'ar' ? 'تم إنشاء بيان المشكلة' : t("common.problem.statement.generated", "Problem statement generated")}</span>
            </div>
            <div className={`flex items-center text-green-700 ${isRTL ? "flex-row-reverse" : ""}`}>
              <CheckCircle className={`w-4 h-4 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
              <span>{language === 'ar' ? 'تم تحديد الجمهور المستهدف' : t("common.target.audience.identified", "Target audience identified")}</span>
            </div>
            <div className={`flex items-center text-green-700 ${isRTL ? "flex-row-reverse" : ""}`}>
              <CheckCircle className={`w-4 h-4 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
              <span>{language === 'ar' ? 'تم تحليل السوق' : t("common.market.analysis.completed", "Market analysis completed")}</span>
            </div>
          </div>
        </div>

        {/* Generated Content Preview */}
        <div className="bg-gradient-to-r from-green-50 to-blue-50 p-4 rounded-lg">
          <h5 className="font-medium text-gray-900 mb-2">
            {language === 'ar' ? 'المحتوى المُولد تلقائياً:' : t("common.autogenerated.content", "Auto-Generated Content:")}
          </h5>
          <div className="text-sm text-gray-700 space-y-1">
            <p><strong>{language === 'ar' ? 'المشكلة:' : t("common.problem", "Problem:")}</strong> Many people struggle to maintain consistent fitness routines...</p>
            <p><strong>{language === 'ar' ? 'الحل:' : t("common.solution", "Solution:")}</strong> AI-powered personalized fitness recommendations...</p>
            <p><strong>{language === 'ar' ? 'السوق:' : t("common.market", "Market:")}</strong> $15.2B fitness app market growing at 14% annually...</p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderAutoContentDemo = () => (
    <div className="bg-white rounded-lg p-6 border border-gray-200">
      <h4 className="font-semibold text-gray-900 mb-4">
        {language === 'ar' ? 'إنشاء المستندات التلقائي' : t("common.automatic.document.generation", "Automatic Document Generation")}
      </h4>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {[
          {
            title: language === 'ar' ? 'خطة العمل' : t("ai.business.plan", "Business Plan"),
            status: language === 'ar' ? 'مكتملة 85%' : '85% Complete',
            icon: <FileText className="w-5 h-5 text-blue-500" />,
            color: 'blue',
          },
          {
            title: language === 'ar' ? 'عرض المستثمرين' : t("ai.investor.pitch", "Investor Pitch"),
            status: language === 'ar' ? 'جاهز للمراجعة' : t("ai.ready.for.review", "Ready for Review"),
            icon: <Target className="w-5 h-5 text-green-500" />,
            color: 'green',
          },
          {
            title: language === 'ar' ? 'المحتوى التسويقي' : t("ai.marketing.copy", "Marketing Copy"),
            status: language === 'ar' ? 'تم الإنشاء' : t("ai.generated", "Generated"),
            icon: <Lightbulb className="w-5 h-5 text-yellow-500" />,
            color: 'yellow',
          },
          {
            title: language === 'ar' ? 'النموذج المالي' : t("ai.financial.model", "Financial Model"),
            status: language === 'ar' ? 'جاري الإنشاء...' : {t("ai.generating", t("common.generating", "Generating..."))},
            icon: <BarChart3 className="w-5 h-5 text-purple-500" />,
            color: 'purple',
          },
        ].map((doc, index) => (
          <div key={index} className={`p-4 rounded-lg border-l-4 border-${doc.color}-500 bg-${doc.color}-50`}>
            <div className={`flex items-center justify-between mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
              <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                {doc.icon}
                <span className={`font-medium text-gray-900 ml-2 ${isRTL ? "space-x-reverse" : ""}`}>{doc.title}</span>
              </div>
            </div>
            <div className="text-sm text-gray-600">{doc.status}</div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderOpportunityDetectionDemo = () => (
    <div className="bg-white rounded-lg p-6 border border-gray-200">
      <h4 className="font-semibold text-gray-900 mb-4">
        {language === 'ar' ? 'الفرص المكتشفة تلقائياً' : t("common.automatically.detected.opportunities", "Automatically Detected Opportunities")}
      </h4>
      
      <div className="space-y-3">
        {[
          {
            type: 'funding',
            title: language === 'ar' ? 'فرصة تمويل' : t("ai.funding.opportunity", "Funding Opportunity"),
            description: language === 'ar' ? 'صندوق TechStars يبحث عن تطبيقات اللياقة' : t("ai.techstars.fund.seeking", "TechStars fund seeking fitness apps"),
            match: '94%',
            icon: <DollarSign className="w-5 h-5 text-green-500" />,
          },
          {
            type: 'mentor',
            title: language === 'ar' ? 'موجه مناسب' : t("ai.suitable.mentor", "Suitable Mentor"),
            description: language === 'ar' ? 'سارة أحمد - خبيرة تطبيقات الصحة' : t("ai.sarah.ahmed.health", "Sarah Ahmed - Health App Expert"),
            match: '89%',
            icon: <Users className="w-5 h-5 text-blue-500" />,
          },
          {
            type: 'partnership',
            title: language === 'ar' ? 'شراكة محتملة' : t("ai.potential.partnership", "Potential Partnership"),
            description: language === 'ar' ? 'شركة FitGear تبحث عن تكامل تطبيقات' : t("ai.fitgear.looking.for", "FitGear looking for app integrations"),
            match: '76%',
            icon: <Network className="w-5 h-5 text-purple-500" />,
          },
        ].map((opportunity, index) => (
          <div key={index} className={`flex items-center justify-between p-4 bg-gray-50 rounded-lg ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              {opportunity.icon}
              <div className={`ml-3 ${isRTL ? "space-x-reverse" : ""}`}>
                <div className="font-medium text-gray-900">{opportunity.title}</div>
                <div className="text-sm text-gray-600">{opportunity.description}</div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-lg font-bold text-green-600">{opportunity.match}</div>
              <div className="text-xs text-gray-500">
                {language === 'ar' ? 'توافق' : t("common.match", "Match")}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderProgressTrackingDemo = () => (
    <div className="bg-white rounded-lg p-6 border border-gray-200">
      <h4 className="font-semibold text-gray-900 mb-4">
        {language === 'ar' ? 'تتبع التقدم التلقائي' : t("common.automatic.progress.tracking", "Automatic Progress Tracking")}
      </h4>
      
      <div className="space-y-4">
        {/* Progress Bar */}
        <div className="bg-gray-200 rounded-full h-3 overflow-hidden">
          <div className="bg-gradient-to-r from-blue-500 to-green-500 h-full rounded-full transition-all duration-1000" style={{ width: '73%' }}></div>
        </div>
        
        <div className={`flex justify-between text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
          <span className="text-gray-600">{language === 'ar' ? 'التقدم الإجمالي' : t("common.overall.progress", "Overall Progress")}</span>
          <span className="font-bold text-green-600">73%</span>
        </div>

        {/* Milestones */}
        <div className="space-y-2">
          {[
            { task: language === 'ar' ? 'تعريف المشكلة' : t("ai.problem.definition", "Problem Definition"), completed: true },
            { task: language === 'ar' ? 'بحث السوق' : t("ai.market.research", "Market Research"), completed: true },
            { task: language === 'ar' ? 'النموذج الأولي' : t("ai.prototype", "Prototype"), completed: false, current: true },
            { task: language === 'ar' ? 'اختبار المستخدمين' : t("ai.user.testing", "User Testing"), completed: false },
          ].map((milestone, index) => (
            <div key={index} className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              {milestone.completed ? (
                <CheckCircle className={`w-5 h-5 text-green-500 mr-3 ${isRTL ? "space-x-reverse" : ""}`} />
              ) : milestone.current ? (
                <Clock className={`w-5 h-5 text-blue-500 mr-3 ${isRTL ? "space-x-reverse" : ""}`} />
              ) : (
                <div className={`w-5 h-5 border-2 border-gray-300 rounded-full mr-3 ${isRTL ? "space-x-reverse" : ""}`}></div>
              )}
              <span className={`${milestone.completed ? 'text-green-700' : milestone.current ? 'text-blue-700 font-medium' : 'text-gray-500'}`}>
                {milestone.task}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderDemoContent = () => {
    switch (activeDemo) {
      case 'smart-forms':
        return renderSmartFormsDemo();
      case 'auto-content':
        return renderAutoContentDemo();
      case 'opportunity-detection':
        return renderOpportunityDetectionDemo();
      case 'progress-tracking':
        return renderProgressTrackingDemo();
      default:
        return renderSmartFormsDemo();
    }
  };

  return (
    <section className={`py-16 bg-gray-50 ${className}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className={`flex items-center justify-center mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            <Brain className={`w-10 h-10 text-blue-500 mr-3 ${isRTL ? "space-x-reverse" : ""}`} />
            <Zap className="w-8 h-8 text-yellow-500" />
          </div>
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            {language === 'ar' ? 'الذكاء الاصطناعي خلف الكواليس' : t("common.ai.behind.the", "AI Behind the Scenes")}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {language === 'ar'
              ? 'لا دردشة، لا أسئلة - فقط ذكاء اصطناعي يعمل بصمت لتحسين تجربتك وتسريع نجاحك'
              : t("common.no.chat.no", "No chat, no questions - just AI working silently to enhance your experience and accelerate your success")
            }
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Demo Selector */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-lg p-6 sticky top-6">
              <h3 className="font-semibold text-gray-900 mb-4">
                {language === 'ar' ? 'ميزات الذكاء الاصطناعي' : t("common.ai.features", "AI Features")}
              </h3>
              
              <div className="space-y-2">
                {demos.map((demo) => (
                  <button
                    key={demo.id}
                    onClick={() => setActiveDemo(demo.id)}
                    className={`w-full text-left p-3 rounded-lg transition-colors ${
                      activeDemo === demo.id
                        ? 'bg-blue-100 border-blue-300 text-blue-900'
                        : 'hover:bg-gray-50 border-gray-200 text-gray-700'}
                    } border`}
                  >
                    <div className={`flex items-center mb-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                      {demo.icon}
                      <span className={`font-medium ml-2 ${isRTL ? "space-x-reverse" : ""}`}>{demo.title}</span>
                    </div>
                    <p className="text-sm text-gray-600">{demo.description}</p>
                  </button>
                ))}
              </div>

              {/* AI Activity Feed */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <h4 className={`font-medium text-gray-900 mb-3 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                  <Bell className={`w-4 h-4 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                  {language === 'ar' ? 'نشاط الذكاء الاصطناعي' : t("common.ai.activity", "AI Activity")}
                </h4>
                <div className="space-y-2 max-h-48 overflow-y-auto">
                  {aiActivity.map((activity, index) => (
                    <div key={index} className={`flex items-start text-xs ${isRTL ? "flex-row-reverse" : ""}`}>
                      {activity.icon}
                      <div className={`ml-2 flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <div className="text-gray-700">{activity.message}</div>
                        <div className="text-gray-500">{activity.time}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Demo Content */}
          <div className="lg:col-span-2">
            {renderDemoContent()}
          </div>
        </div>

        {/* Key Benefits */}
        <div className="mt-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl p-8 text-white">
          <h3 className="text-2xl font-bold text-center mb-8">
            {language === 'ar' ? 'لماذا الذكاء الاصطناعي خلف الكواليس؟' : t("common.why.ai.behind", "Why AI Behind the Scenes?")}
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[
              {
                icon: <Zap className="w-8 h-8" />,
                title: language === 'ar' ? 'تلقائي بالكامل' : t("ai.fully.automatic", "Fully Automatic"),
                description: language === 'ar' ? 'لا حاجة لطرح أسئلة - الذكاء الاصطناعي يعرف ما تحتاجه' : t("ai.no.need.to", "No need to ask questions - AI knows what you need"),
              },
              {
                icon: <Eye className="w-8 h-8" />,
                title: language === 'ar' ? 'دائماً يراقب' : t("ai.always.watching", "Always Watching"),
                description: language === 'ar' ? 'يراقب تقدمك ويجد الفرص 24/7' : 'Monitors your progress and finds opportunities 24/7',
              },
              {
                icon: <Target className="w-8 h-8" />,
                title: language === 'ar' ? 'شخصي ودقيق' : 'Personal & Precise',
                description: language === 'ar' ? 'مخصص لاحتياجاتك وأهدافك المحددة' : t("ai.tailored.to.your", "Tailored to your specific needs and goals"),
              },
            ].map((benefit, index) => (
              <div key={index} className="text-center">
                <div className={`flex justify-center mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>{benefit.icon}</div>
                <h4 className="font-semibold mb-2">{benefit.title}</h4>
                <p className="text-blue-100">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default AIBehindTheScenes;
