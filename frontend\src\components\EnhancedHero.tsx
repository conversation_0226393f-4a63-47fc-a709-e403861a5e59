import React, { useEffect, useRef, useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Rocket,
  Users,
  TrendingUp,
  Brain,
  Zap,
  Play,
  ChevronDown,
  Star,
  Globe,
  Target,
  Award,
  BookOpen,
  MessageSquare,
  Sun,
  Moon
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useAppSelector } from '../store/hooks';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../hooks/useLanguage';
import { RTLIcon, RTLText, RTLFlex } from './common';


const EnhancedHero = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const navigate = useNavigate();

  const { t } = useTranslation();
  const { language, isRTL } = useLanguage();

  const [currentFeature, setCurrentFeature] = useState(0);
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);

  // Enhanced features showcase
  const features = [
    {
      icon: Brain,
      title: t('hero.features.aiMentorship'),
      description: t('hero.features.aiMentorshipDesc'),
      color: 'from-purple-500 to-pink-500'
    },
    {
      icon: Target,
      title: t('hero.features.expertNetwork'),
      description: t('hero.features.expertNetworkDesc'),
      color: 'from-blue-500 to-cyan-500'
    },
    {
      icon: MessageSquare,
      title: t('hero.features.smartResources'),
      description: t('hero.features.smartResourcesDesc'),
      color: 'from-green-500 to-emerald-500'
    },
    {
      icon: TrendingUp,
      title: t('hero.features.fundingSupport'),
      description: t('hero.features.fundingSupportDesc'),
      color: 'from-orange-500 to-red-500'
    }
  ];

  // Stats data
  const stats = [
    { icon: Brain, value: '500+', label: t('hero.stats.entrepreneurs') },
    { icon: Zap, value: '150+', label: t('hero.stats.ideasLaunched') },
    { icon: Award, value: '85%', label: t('hero.stats.successRate') },
    { icon: Globe, value: '50+', label: t('hero.stats.mentors') }
  ];

  // Navigation handlers
  const handleJoinCommunity = () => {
    navigate('/register');
  };

  const handleExploreResources = () => {
    navigate('/incubator');
  };

  const handleWatchDemo = () => {
    setIsVideoPlaying(true);
  };

  const scrollToFeatures = () => {
    document.getElementById('features')?.scrollIntoView({ behavior: 'smooth' });
  };



  // Auto-rotate features
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentFeature((prev) => (prev + 1) % features.length);
    }, 4000);

    return () => clearInterval(interval);
  }, [features.length]);

  // Enhanced canvas animation
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const isLightMode = false; // Always use dark mode for glass morphism

    const setCanvasDimensions = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    setCanvasDimensions();
    window.addEventListener('resize', setCanvasDimensions);

    // AI Space-themed particles system
    const particles: Array<{
      x: number;
      y: number;
      vx: number;
      vy: number;
      radius: number;
      opacity: number;
      color: string;
      type: 'star' | 'dot' | 'sparkle' | 'neural' | 'data';
      pulseSpeed: number;
      connections?: Array<{ x: number; y: number; opacity: number }>;
    }> = [];

    // Create AI space particles
    for (let i = 0; i < 200; i++) {
      const particleType = Math.random();
      let type: 'star' | 'dot' | 'sparkle' | 'neural' | 'data';
      let color: string;

      if (particleType > 0.8) {
        type = 'neural';
        color = isLightMode ? '#8B5CF6' : '#A855F7'; // Purple for neural nodes
      } else if (particleType > 0.6) {
        type = 'data';
        color = isLightMode ? '#06B6D4' : '#22D3EE'; // Cyan for data points
      } else if (particleType > 0.4) {
        type = 'sparkle';
        color = isLightMode ? '#F59E0B' : '#FCD34D'; // Gold for sparkles
      } else if (particleType > 0.2) {
        type = 'star';
        color = isLightMode ? '#6366F1' : '#818CF8'; // Indigo for stars
      } else {
        type = 'dot';
        color = isLightMode ? '#EC4899' : '#F472B6'; // Pink for dots
      }

      particles.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        vx: (Math.random() - 0.5) * 0.3,
        vy: (Math.random() - 0.5) * 0.3,
        radius: type === 'neural' ? Math.random() * 3 + 2 : Math.random() * 2 + 0.5,
        opacity: Math.random() * 0.8 + 0.2,
        color,
        type,
        pulseSpeed: Math.random() * 0.02 + 0.01,
        connections: type === 'neural' ? [] : undefined
      });
    }

    // AI-themed floating shapes
    const shapes: Array<{
      x: number;
      y: number;
      rotation: number;
      rotationSpeed: number;
      size: number;
      opacity: number;
      type: 'triangle' | 'square' | 'circle' | 'hexagon' | 'diamond';
      pulsePhase: number;
    }> = [];

    for (let i = 0; i < 25; i++) {
      shapes.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        rotation: 0,
        rotationSpeed: (Math.random() - 0.5) * 0.02,
        size: Math.random() * 40 + 15,
        opacity: Math.random() * 0.2 + 0.05,
        type: ['triangle', 'square', 'circle', 'hexagon', 'diamond'][Math.floor(Math.random() * 5)] as any,
        pulsePhase: Math.random() * Math.PI * 2
      });
    }

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Draw neural network connections first (behind particles)
      if (!isLightMode) {
        particles.forEach((particle, i) => {
          if (particle.type === 'neural') {
            particles.forEach((otherParticle, j) => {
              if (i !== j && (otherParticle.type === 'neural' || otherParticle.type === 'data')) {
                const dx = particle.x - otherParticle.x;
                const dy = particle.y - otherParticle.y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                if (distance < 150) {
                  const opacity = (1 - distance / 150) * 0.15;
                  ctx.save();
                  ctx.globalAlpha = opacity;
                  ctx.strokeStyle = particle.type === 'neural' ? '#8B5CF6' : '#06B6D4';
                  ctx.lineWidth = 1;
                  ctx.beginPath();
                  ctx.moveTo(particle.x, particle.y);
                  ctx.lineTo(otherParticle.x, otherParticle.y);
                  ctx.stroke();
                  ctx.restore();
                }
              }
            });
          }
        });
      }

      // Draw particles with smooth movement
      particles.forEach(particle => {
        // Update particle position
        particle.x += particle.vx;
        particle.y += particle.vy;

        // Boundary check
        if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
        if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;

        // Draw based on type with AI theme
        ctx.save();

        // Add pulsing effect for neural and data particles
        const pulseMultiplier = particle.type === 'neural' || particle.type === 'data'
          ? 1 + Math.sin(Date.now() * particle.pulseSpeed) * 0.3
          : 1;

        ctx.globalAlpha = particle.opacity * pulseMultiplier;
        ctx.fillStyle = particle.color;
        ctx.strokeStyle = particle.color;

        if (particle.type === 'sparkle') {
          // Draw AI sparkle with cross pattern
          ctx.translate(particle.x, particle.y);
          ctx.rotate(Date.now() * 0.001);
          ctx.lineWidth = 2;
          ctx.beginPath();
          for (let i = 0; i < 4; i++) {
            ctx.rotate(Math.PI / 2);
            ctx.moveTo(0, 0);
            ctx.lineTo(particle.radius * 3, 0);
          }
          ctx.stroke();
        } else if (particle.type === 'star') {
          // Draw star
          ctx.translate(particle.x, particle.y);
          ctx.beginPath();
          for (let i = 0; i < 5; i++) {
            ctx.lineTo(Math.cos(i * 2 * Math.PI / 5) * particle.radius,
                      Math.sin(i * 2 * Math.PI / 5) * particle.radius);
          }
          ctx.closePath();
          ctx.fill();
        } else if (particle.type === 'neural') {
          // Draw neural node with glow
          const gradient = ctx.createRadialGradient(particle.x, particle.y, 0, particle.x, particle.y, particle.radius * 2);
          gradient.addColorStop(0, particle.color);
          gradient.addColorStop(1, 'transparent');
          ctx.fillStyle = gradient;
          ctx.beginPath();
          ctx.arc(particle.x, particle.y, particle.radius * 2, 0, Math.PI * 2);
          ctx.fill();

          // Inner core
          ctx.fillStyle = particle.color;
          ctx.beginPath();
          ctx.arc(particle.x, particle.y, particle.radius * 0.5, 0, Math.PI * 2);
          ctx.fill();
        } else if (particle.type === 'data') {
          // Draw data point as square with rotation
          ctx.translate(particle.x, particle.y);
          ctx.rotate(Date.now() * 0.002);
          ctx.fillRect(-particle.radius, -particle.radius, particle.radius * 2, particle.radius * 2);
        } else {
          // Draw dot
          ctx.beginPath();
          ctx.arc(particle.x, particle.y, particle.radius, 0, Math.PI * 2);
          ctx.fill();
        }

        ctx.restore();
      });

      // Draw AI-themed floating shapes
      shapes.forEach(shape => {
        shape.rotation += shape.rotationSpeed;
        shape.pulsePhase += 0.02;

        ctx.save();

        // Add pulsing effect
        const pulseScale = 1 + Math.sin(shape.pulsePhase) * 0.1;
        const pulseOpacity = shape.opacity * (1 + Math.sin(shape.pulsePhase * 2) * 0.3);

        ctx.globalAlpha = pulseOpacity;
        ctx.translate(shape.x, shape.y);
        ctx.rotate(shape.rotation);
        ctx.scale(pulseScale, pulseScale);

        // AI-themed gradient colors
        const gradient = ctx.createLinearGradient(-shape.size/2, -shape.size/2, shape.size/2, shape.size/2);
        if (isLightMode) {
          gradient.addColorStop(0, 'rgba(139, 92, 246, 0.4)'); // Purple
          gradient.addColorStop(0.5, 'rgba(6, 182, 212, 0.3)'); // Cyan
          gradient.addColorStop(1, 'rgba(236, 72, 153, 0.2)'); // Pink
        } else {
          gradient.addColorStop(0, 'rgba(168, 85, 247, 0.5)'); // Brighter purple
          gradient.addColorStop(0.5, 'rgba(34, 211, 238, 0.4)'); // Brighter cyan
          gradient.addColorStop(1, 'rgba(244, 114, 182, 0.3)'); // Brighter pink
        }

        ctx.fillStyle = gradient;
        ctx.strokeStyle = isLightMode ? 'rgba(139, 92, 246, 0.6)' : 'rgba(168, 85, 247, 0.8)';
        ctx.lineWidth = 2;

        if (shape.type === 'triangle') {
          ctx.beginPath();
          ctx.moveTo(0, -shape.size/2);
          ctx.lineTo(-shape.size/2, shape.size/2);
          ctx.lineTo(shape.size/2, shape.size/2);
          ctx.closePath();
        } else if (shape.type === 'square') {
          ctx.beginPath();
          ctx.rect(-shape.size/2, -shape.size/2, shape.size, shape.size);
        } else if (shape.type === 'hexagon') {
          ctx.beginPath();
          for (let i = 0; i < 6; i++) {
            const angle = (i * Math.PI) / 3;
            const x = Math.cos(angle) * shape.size/2;
            const y = Math.sin(angle) * shape.size/2;
            if (i === 0) ctx.moveTo(x, y);
            else ctx.lineTo(x, y);
          }
          ctx.closePath();
        } else if (shape.type === 'diamond') {
          ctx.beginPath();
          ctx.moveTo(0, -shape.size/2);
          ctx.lineTo(shape.size/2, 0);
          ctx.lineTo(0, shape.size/2);
          ctx.lineTo(-shape.size/2, 0);
          ctx.closePath();
        } else {
          ctx.beginPath();
          ctx.arc(0, 0, shape.size/2, 0, Math.PI * 2);
        }

        ctx.fill();
        ctx.stroke();
        ctx.restore();
      });

      requestAnimationFrame(animate);
    };

    animate();

    return () => {
      window.removeEventListener('resize', setCanvasDimensions);
    };
  }, []);

  return (
    <div className={`relative min-h-screen flex items-center justify-center overflow-hidden ${isRTL ? "flex-row-reverse" : ""}`} style={{ transform: 'none' }}>
      {/* Enhanced Canvas Background */}
      <canvas
        ref={canvasRef}
        className="absolute top-0 left-0 w-full h-full pointer-events-none"
        style={{
          background: 'transparent',
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          zIndex: 1,
          transform: 'none',
          willChange: 'auto'
        }}
      />

      {/* AI Space-themed Gradient Overlays */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-900/30 via-indigo-900/20 to-cyan-900/25 pointer-events-none" style={{ zIndex: 2 }}></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-pink-900/15 via-transparent to-blue-900/20 pointer-events-none" style={{ zIndex: 2 }}></div>
      <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-purple-950/10 to-transparent pointer-events-none" style={{ zIndex: 2 }}></div>
      <div className="absolute inset-0 bg-gradient-radial from-transparent via-purple-500/5 to-transparent pointer-events-none" style={{ zIndex: 2 }}></div>

      {/* Floating AI Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none" style={{ zIndex: 3 }}>
        {/* Floating AI Icons */}
        <div className="absolute top-20 left-10 animate-pulse">
          <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg opacity-20 animate-bounce" style={{ animationDelay: '0s' }}></div>
        </div>
        <div className="absolute top-40 right-20 animate-pulse">
          <div className="w-6 h-6 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full opacity-25 animate-bounce" style={{ animationDelay: '1s' }}></div>
        </div>
        <div className="absolute bottom-32 left-20 animate-pulse">
          <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg opacity-15 animate-bounce" style={{ animationDelay: '2s' }}></div>
        </div>
        <div className="absolute bottom-20 right-10 animate-pulse">
          <div className="w-7 h-7 bg-gradient-to-r from-pink-500 to-red-500 rounded-full opacity-20 animate-bounce" style={{ animationDelay: '1.5s' }}></div>
        </div>

        {/* Data Stream Lines */}
        <div className="absolute top-0 left-1/4 w-px h-full bg-gradient-to-b from-transparent via-purple-500/20 to-transparent animate-pulse"></div>
        <div className="absolute top-0 right-1/3 w-px h-full bg-gradient-to-b from-transparent via-cyan-500/15 to-transparent animate-pulse" style={{ animationDelay: '0.5s' }}></div>
        <div className="absolute top-0 left-3/4 w-px h-full bg-gradient-to-b from-transparent via-pink-500/10 to-transparent animate-pulse" style={{ animationDelay: '1s' }}></div>
      </div>



      {/* Main Content */}
      <div className="relative px-4 py-20 container mx-auto" style={{ zIndex: 10 }}>
        <div className="max-w-6xl mx-auto">
          {/* Hero Text */}
          <div className="text-center mb-16">
            <div className="mb-6">
              <div className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium mb-6 glass-light text-glass-primary border border-glass-border ${isRTL ? "flex-row-reverse" : ""}`}>
                <Sparkles className={`w-4 h-4 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                Enhanced AI • LangGraph + Gemini + Custom ML
              </div>
            </div>

            <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400 animate-gradient-x">
                {t('hero.title')}
              </span>
            </h1>

            <h2 className="text-2xl md:text-4xl font-semibold mb-8 max-w-4xl mx-auto text-glass-primary">
              {t('hero.subtitle')}
            </h2>

            <RTLText
              as="p"
              className="text-xl md:text-2xl mb-12 max-w-3xl mx-auto leading-relaxed text-glass-secondary"
            >
              {t('hero.description')}
            </RTLText>

            {/* CTA Buttons */}
            <RTLFlex className={`flex-col sm:flex-row items-center justify-center gap-6 mb-16 ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                onClick={handleJoinCommunity}
                className="group relative px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-full font-semibold text-lg transform hover:scale-105 transition-all duration-300 hover:shadow-2xl hover:shadow-purple-500/25"
              >
                <span className={`flex items-center gap-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                  {t('hero.startJourney')}
                  <RTLIcon
                    icon={ArrowRight}
                    size={20}
                    flipInRTL={true}
                    className="group-hover:translate-x-1 transition-transform duration-300"
                  />
                </span>
              </button>

              <button
                onClick={handleWatchDemo}
                className={`group flex items-center gap-3 px-8 py-4 bg-transparent border-2 border-purple-500 text-purple-500 rounded-full font-semibold text-lg hover:bg-purple-500 hover:text-white transition-all duration-300 ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <Play className="w-5 h-5 group-hover:scale-110 transition-transform duration-300" />
                {t('hero.watchDemo')}
              </button>
            </RTLFlex>
          </div>

          {/* Stats Section */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
            {stats.map((stat, index) => (
              <div
                key={index}
                className="text-center p-6 rounded-2xl backdrop-blur-sm transition-all duration-300 hover:scale-105 glass-light border"
              >
                <stat.icon className="w-8 h-8 mx-auto mb-3 text-purple-500" />
                <div className="text-3xl font-bold mb-2 text-glass-primary">
                  {stat.value}
                </div>
                <div className="text-sm text-glass-secondary">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>

          {/* Features Showcase */}
          <div className="text-center">
            <div className="inline-block p-8 rounded-3xl backdrop-blur-sm transition-all duration-500 glass-light border">
              <div className={`flex items-center justify-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
                {React.createElement(features[currentFeature].icon, {
                  className: "w-12 h-12 text-purple-500"
                })}
              </div>

              <h3 className="text-2xl font-bold mb-4 text-glass-primary">
                {features[currentFeature].title}
              </h3>

              <p className="text-lg max-w-md mx-auto text-glass-secondary">
                {features[currentFeature].description}
              </p>

              {/* Feature indicators */}
              <div className={`flex justify-center gap-2 mt-6 ${isRTL ? "flex-row-reverse" : ""}`}>
                {features.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentFeature(index)}
                    className={`w-3 h-3 rounded-full transition-all duration-300 ${
                      index === currentFeature
                        ? 'bg-purple-500 scale-125'
                        : 'bg-gray-400 hover:bg-gray-300'}
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Scroll indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
          <button
            onClick={scrollToFeatures}
            className="animate-bounce p-2 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 transition-all duration-300"
          >
            <ChevronDown className="w-6 h-6 text-white" />
          </button>
        </div>
      </div>

      {/* Video Modal */}
      {isVideoPlaying && (
        <div className={`fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="relative max-w-4xl w-full aspect-video bg-black rounded-2xl overflow-hidden">
            <button
              onClick={() => setIsVideoPlaying(false)}
              className="absolute top-4 right-4 z-10 p-2 bg-black/50 text-white rounded-full hover:bg-black/70 transition-colors duration-300"
            >
              ✕
            </button>
            <div className={`w-full h-full flex items-center justify-center text-white ${isRTL ? "flex-row-reverse" : ""}`}>
              <div className="text-center">
                <Play className="w-16 h-16 mx-auto mb-4 opacity-50" />
                <p className="text-xl">{t('hero.demoPlaceholder')}</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedHero;
