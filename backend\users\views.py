from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.contrib.auth.models import User
from django.contrib.auth import authenticate, login, logout
from django.db.models import Count, Q
from django.db import transaction
from django.utils import timezone
from rest_framework_simplejwt.tokens import RefreshToken
from .models import UserProfile
from .serializers import UserSerializer, UserProfileSerializer, UserRegistrationSerializer, AdminUserCreationSerializer
from forums.models import ForumThread, ForumPost, ReputationActivity, UserReputation
from api.permissions import IsAdminUser

class UserViewSet(viewsets.ModelViewSet):
    queryset = User.objects.all()
    serializer_class = UserSerializer

    def get_permissions(self):
        if self.action == 'create' or self.action == 'login':
            permission_classes = [permissions.AllowAny]
        elif self.action == 'list' or self.action == 'retrieve' or self.action == 'destroy':
            # Only admin users can list all users, get user details, or delete users
            permission_classes = [IsAdminUser]
        else:
            permission_classes = [permissions.IsAuthenticated]
        return [permission() for permission in permission_classes]

    @action(detail=False, methods=['get'], permission_classes=[IsAdminUser])
    def admin_dashboard_stats(self, request):
        """Get enhanced statistics for the admin dashboard"""
        from django.utils import timezone
        from datetime import timedelta
        from django.db.models import Count, Sum, Avg, F, Q
        from api.models import Post, Comment, Event, Resource

        # Basic user statistics
        total_users = User.objects.count()
        active_users = User.objects.filter(is_active=True).count()
        staff_users = User.objects.filter(is_staff=True).count()
        superusers = User.objects.filter(is_superuser=True).count()

        # Time periods for trend analysis
        now = timezone.now()
        thirty_days_ago = now - timedelta(days=30)
        sixty_days_ago = now - timedelta(days=60)
        ninety_days_ago = now - timedelta(days=90)

        # User growth metrics
        new_users_last_30_days = User.objects.filter(date_joined__gte=thirty_days_ago).count()
        new_users_30_60_days = User.objects.filter(date_joined__gte=sixty_days_ago, date_joined__lt=thirty_days_ago).count()

        # Calculate growth rate
        growth_rate = 0
        if new_users_30_60_days > 0:
            growth_rate = ((new_users_last_30_days - new_users_30_60_days) / new_users_30_60_days) * 100

        # Get users by month (for the last 6 months)
        users_by_month = {}
        for i in range(6):
            month_start = now.replace(day=1) - timedelta(days=30*i)
            month_name = month_start.strftime('%B %Y')
            month_end = (month_start.replace(day=28) + timedelta(days=4)).replace(day=1) - timedelta(days=1)
            count = User.objects.filter(date_joined__gte=month_start, date_joined__lte=month_end).count()
            users_by_month[month_name] = count

        # User demographics
        location_data = UserProfile.objects.exclude(location='').values('location').annotate(count=Count('id')).order_by('-count')[:5]
        users_by_location = {item['location']: item['count'] for item in location_data}

        expertise_data = UserProfile.objects.exclude(expertise='').values('expertise').annotate(count=Count('id')).order_by('-count')[:5]
        users_by_expertise = {item['expertise']: item['count'] for item in expertise_data}

        # User engagement metrics
        total_posts = Post.objects.count()
        total_comments = Comment.objects.count()
        total_event_attendances = Event.objects.aggregate(total_attendees=Count('attendees'))['total_attendees']

        # Calculate engagement rate (posts + comments + event attendances per user)
        engagement_rate = 0
        if total_users > 0:
            engagement_rate = (total_posts + total_comments + total_event_attendances) / total_users

        # Posts per user ratio
        posts_per_user = 0
        if total_users > 0:
            posts_per_user = total_posts / total_users

        # Comments per post ratio
        comments_per_post = 0
        if total_posts > 0:
            comments_per_post = total_comments / total_posts

        # Daily active users (users who created content in the last day)
        yesterday = now - timedelta(days=1)
        daily_active_users = User.objects.filter(
            Q(posts__created_at__gte=yesterday) |
            Q(comments__created_at__gte=yesterday) |
            Q(attending_events__date__gte=yesterday)
        ).distinct().count()

        # Weekly active users
        week_ago = now - timedelta(days=7)
        weekly_active_users = User.objects.filter(
            Q(posts__created_at__gte=week_ago) |
            Q(comments__created_at__gte=week_ago) |
            Q(attending_events__date__gte=week_ago)
        ).distinct().count()

        # Monthly active users
        monthly_active_users = User.objects.filter(
            Q(posts__created_at__gte=thirty_days_ago) |
            Q(comments__created_at__gte=thirty_days_ago) |
            Q(attending_events__date__gte=thirty_days_ago)
        ).distinct().count()

        # User retention (users active in both the last 30 days and the previous 30 days)
        users_active_last_30 = User.objects.filter(
            Q(posts__created_at__gte=thirty_days_ago) |
            Q(comments__created_at__gte=thirty_days_ago) |
            Q(attending_events__date__gte=thirty_days_ago)
        ).distinct()

        users_active_30_60 = User.objects.filter(
            Q(posts__created_at__gte=sixty_days_ago, posts__created_at__lt=thirty_days_ago) |
            Q(comments__created_at__gte=sixty_days_ago, comments__created_at__lt=thirty_days_ago) |
            Q(attending_events__date__gte=sixty_days_ago, attending_events__date__lt=thirty_days_ago)
        ).distinct()

        retained_users = users_active_last_30.filter(id__in=users_active_30_60.values_list('id', flat=True)).count()
        retention_rate = 0
        if users_active_30_60.count() > 0:
            retention_rate = (retained_users / users_active_30_60.count()) * 100

        # Get most active users with detailed metrics
        active_users_data = []
        for user in User.objects.all()[:20]:  # Increased limit for better analysis
            post_count = Post.objects.filter(author=user).count()
            comment_count = Comment.objects.filter(author=user).count()
            event_count = Event.objects.filter(attendees=user).count()
            resource_count = Resource.objects.filter(author=user).count()

            # Calculate likes received on posts
            likes_received = sum(post.likes.count() for post in Post.objects.filter(author=user))

            # Calculate profile completion percentage
            profile = user.profile
            total_fields = 8  # bio, location, expertise, profile_image, website, github, linkedin, twitter
            filled_fields = 0
            if profile.bio: filled_fields += 1
            if profile.location: filled_fields += 1
            if profile.expertise: filled_fields += 1
            if profile.profile_image: filled_fields += 1
            if profile.website: filled_fields += 1
            if profile.github: filled_fields += 1
            if profile.linkedin: filled_fields += 1
            if profile.twitter: filled_fields += 1

            profile_completion = (filled_fields / total_fields) * 100

            # Calculate activity score with weighted metrics
            activity_score = (post_count * 3) + (comment_count * 1) + (event_count * 2) + (resource_count * 3) + (likes_received * 0.5)

            if activity_score > 0:
                active_users_data.append({
                    'id': user.id,
                    'username': user.username,
                    'post_count': post_count,
                    'comment_count': comment_count,
                    'event_count': event_count,
                    'resource_count': resource_count,
                    'likes_received': likes_received,
                    'profile_completion': profile_completion,
                    'activity_score': activity_score,
                    'last_active': user.last_login or user.date_joined
                })

        # Sort by activity score and get top 10
        active_users_data.sort(key=lambda x: x['activity_score'], reverse=True)
        active_users_data = active_users_data[:10]

        # Content engagement over time (last 6 months)
        content_engagement_by_month = {}
        for i in range(6):
            month_start = now.replace(day=1) - timedelta(days=30*i)
            month_name = month_start.strftime('%B %Y')
            month_end = (month_start.replace(day=28) + timedelta(days=4)).replace(day=1) - timedelta(days=1)

            posts_count = Post.objects.filter(created_at__gte=month_start, created_at__lte=month_end).count()
            comments_count = Comment.objects.filter(created_at__gte=month_start, created_at__lte=month_end).count()

            content_engagement_by_month[month_name] = {
                'posts': posts_count,
                'comments': comments_count,
                'total': posts_count + comments_count
            }

        return Response({
            # Basic user stats
            'total_users': total_users,
            'active_users': active_users,
            'staff_users': staff_users,
            'superusers': superusers,
            'new_users': new_users_last_30_days,

            # Growth metrics
            'user_growth_rate': growth_rate,
            'users_by_month': users_by_month,

            # Demographics
            'users_by_location': users_by_location,
            'users_by_expertise': users_by_expertise,

            # Engagement metrics
            'engagement_rate': engagement_rate,
            'posts_per_user': posts_per_user,
            'comments_per_post': comments_per_post,
            'daily_active_users': daily_active_users,
            'weekly_active_users': weekly_active_users,
            'monthly_active_users': monthly_active_users,
            'retention_rate': retention_rate,

            # Detailed user activity
            'most_active_users': active_users_data,

            # Content engagement over time
            'content_engagement_by_month': content_engagement_by_month,
        })

    def get_serializer_class(self):
        if self.action == 'create':
            # Use AdminUserCreationSerializer if user is admin, otherwise use regular registration
            if self.request.user.is_authenticated and (self.request.user.is_staff or self.request.user.is_superuser):
                return AdminUserCreationSerializer
            return UserRegistrationSerializer
        return UserSerializer

    def create(self, request, *args, **kwargs):
        """Override create to handle user registration"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # Create the user
        user = serializer.save()

        # For admin user creation, just return the user data
        if self.request.user.is_authenticated and (self.request.user.is_staff or self.request.user.is_superuser):
            user_serializer = UserSerializer(user)
            return Response(user_serializer.data, status=status.HTTP_201_CREATED)

        # For regular registration, return user data with tokens
        refresh = RefreshToken.for_user(user)
        access_token = refresh.access_token

        user_serializer = UserSerializer(user)
        response_data = {
            'user': user_serializer.data,
            'access': str(access_token),
            'refresh': str(refresh),
        }
        return Response(response_data, status=status.HTTP_201_CREATED)

    def destroy(self, request, *args, **kwargs):
        """
        Custom destroy method to handle foreign key constraints safely.
        Actually delete the user after cleaning up related data.
        """
        user = self.get_object()

        # Prevent deletion of superusers and staff users for safety
        if user.is_superuser:
            return Response(
                {"error": "Cannot delete superuser accounts"},
                status=status.HTTP_400_BAD_REQUEST
            )

        if user.is_staff and User.objects.filter(is_staff=True).count() <= 1:
            return Response(
                {"error": "Cannot delete the last staff user"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            with transaction.atomic():
                # Clean up related data before deletion
                from api.models import Post, Comment, Event

                # Update posts to remove author reference
                Post.objects.filter(author=user).update(author=None)

                # Update comments to remove author reference
                Comment.objects.filter(author=user).update(author=None)

                # Remove user from event attendees
                for event in Event.objects.filter(attendees=user):
                    event.attendees.remove(user)

                # Transfer event ownership to admin or set to None
                admin_user = User.objects.filter(is_staff=True, is_active=True).exclude(id=user.id).first()
                if admin_user:
                    Event.objects.filter(organizer=user).update(organizer=admin_user)
                else:
                    Event.objects.filter(organizer=user).update(organizer=None)

                # Delete user profile if exists
                if hasattr(user, 'profile'):
                    user.profile.delete()

                # Actually delete the user
                user_id = user.id
                user.delete()

                return Response(
                    {"message": f"User {user_id} has been deleted successfully"},
                    status=status.HTTP_204_NO_CONTENT
                )

        except Exception as e:
            return Response(
                {"error": f"Failed to delete user: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    def login(self, request):
        username = request.data.get('username')
        password = request.data.get('password')

        user = authenticate(username=username, password=password)

        if user:
            login(request, user)
            serializer = self.get_serializer(user)
            return Response(serializer.data)

        return Response(
            {"error": "Invalid credentials"},
            status=status.HTTP_400_BAD_REQUEST
        )

    @action(detail=False, methods=['post'])
    def logout(self, request):
        logout(request)
        return Response({"message": "Successfully logged out"})

    @action(detail=False, methods=['get'])
    def me(self, request):
        serializer = self.get_serializer(request.user)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def user_activity(self, request):
        """Get the current user's activity statistics"""
        if not request.user.is_authenticated:
            return Response({"detail": "Authentication credentials were not provided."}, status=status.HTTP_401_UNAUTHORIZED)

        user = request.user
        from django.utils import timezone
        from datetime import timedelta
        from django.db.models import Count, Q
        from api.models import Post, Comment, Event, Resource

        now = timezone.now()
        thirty_days_ago = now - timedelta(days=30)

        # Count user's content
        post_count = Post.objects.filter(author=user).count()
        comment_count = Comment.objects.filter(author=user).count()
        event_count = Event.objects.filter(organizer=user).count()
        resource_count = Resource.objects.filter(author=user).count()

        # Count likes received
        likes_received = sum(post.likes.count() for post in Post.objects.filter(author=user))

        # Get recent activity
        recent_posts = Post.objects.filter(author=user).order_by('-created_at')[:5]
        recent_comments = Comment.objects.filter(author=user).order_by('-created_at')[:5]
        recent_events = Event.objects.filter(organizer=user).order_by('-created_at')[:5]

        # Get activity over time (last 30 days)
        posts_by_day = {}
        comments_by_day = {}

        for i in range(30):
            day = (now - timedelta(days=i)).date()
            day_str = day.strftime('%Y-%m-%d')

            posts_by_day[day_str] = Post.objects.filter(
                author=user,
                created_at__date=day
            ).count()

            comments_by_day[day_str] = Comment.objects.filter(
                author=user,
                created_at__date=day
            ).count()

        # Calculate engagement score
        engagement_score = (post_count * 3) + (comment_count * 1) + (event_count * 2) + (resource_count * 3) + (likes_received * 0.5)

        return Response({
            'post_count': post_count,
            'comment_count': comment_count,
            'event_count': event_count,
            'resource_count': resource_count,
            'likes_received': likes_received,
            'engagement_score': engagement_score,
            'posts_by_day': posts_by_day,
            'comments_by_day': comments_by_day,
            'recent_posts': [{'id': post.id, 'title': post.title, 'created_at': post.created_at} for post in recent_posts],
            'recent_comments': [{'id': comment.id, 'content': comment.content[:50], 'created_at': comment.created_at} for comment in recent_comments],
            'recent_events': [{'id': event.id, 'title': event.title, 'date': event.date} for event in recent_events],
        })

    @action(detail=False, methods=['get'])
    def forum_activity(self, request):
        """Get the current user's forum activity"""
        if not request.user.is_authenticated:
            return Response({"detail": "Authentication credentials were not provided."}, status=status.HTTP_401_UNAUTHORIZED)

        user = request.user
        from django.utils import timezone
        from datetime import timedelta

        now = timezone.now()
        thirty_days_ago = now - timedelta(days=30)

        # Get thread and post counts
        thread_count = ForumThread.objects.filter(author=user).count()
        post_count = ForumPost.objects.filter(author=user).count()

        # Get recent threads and posts
        recent_threads = ForumThread.objects.filter(author=user).order_by('-created_at')[:5]
        recent_posts = ForumPost.objects.filter(author=user).order_by('-created_at')[:5]

        # Get like counts
        likes_received = ForumPost.objects.filter(author=user).aggregate(
            total_likes=Count('likes')
        )['total_likes'] or 0

        # Get solution count
        solution_count = ForumPost.objects.filter(author=user, is_solution=True).count()

        # Get reputation data
        try:
            reputation = UserReputation.objects.get(user=user)
            reputation_data = {
                'points': reputation.points,
                'level': reputation.level,
                'threads_created': reputation.threads_created,
                'posts_created': reputation.posts_created,
                'solutions_provided': reputation.solutions_provided,
                'likes_received': reputation.likes_received,
                'likes_given': reputation.likes_given
            }
        except UserReputation.DoesNotExist:
            reputation_data = {
                'points': 0,
                'level': 'Newcomer',
                'threads_created': 0,
                'posts_created': 0,
                'solutions_provided': 0,
                'likes_received': 0,
                'likes_given': 0
            }

        # Get recent reputation activities
        recent_activities = ReputationActivity.objects.filter(user=user).order_by('-created_at')[:10]

        # Get activity over time (last 30 days)
        threads_by_day = {}
        posts_by_day = {}

        for i in range(30):
            day = (now - timedelta(days=i)).date()
            day_str = day.strftime('%Y-%m-%d')

            threads_by_day[day_str] = ForumThread.objects.filter(
                author=user,
                created_at__date=day
            ).count()

            posts_by_day[day_str] = ForumPost.objects.filter(
                author=user,
                created_at__date=day
            ).count()

        return Response({
            'thread_count': thread_count,
            'post_count': post_count,
            'likes_received': likes_received,
            'solution_count': solution_count,
            'reputation': reputation_data,
            'recent_threads': [
                {
                    'id': thread.id,
                    'title': thread.title,
                    'created_at': thread.created_at,
                    'topic': thread.topic.title,
                    'post_count': thread.forum_posts.count()
                } for thread in recent_threads
            ],
            'recent_posts': [
                {
                    'id': post.id,
                    'content': post.content[:100] + ('...' if len(post.content) > 100 else ''),
                    'created_at': post.created_at,
                    'thread_title': post.thread.title,
                    'like_count': post.likes.count(),
                    'is_solution': post.is_solution
                } for post in recent_posts
            ],
            'recent_activities': [
                {
                    'id': activity.id,
                    'activity_type': activity.activity_type,
                    'points': activity.points,
                    'description': activity.description,
                    'created_at': activity.created_at
                } for activity in recent_activities
            ],
            'threads_by_day': threads_by_day,
            'posts_by_day': posts_by_day
        })

    @action(detail=False, methods=['get'])
    def content_recommendations(self, request):
        """Get personalized content recommendations for the current user"""
        if not request.user.is_authenticated:
            return Response({"detail": "Authentication credentials were not provided."}, status=status.HTTP_401_UNAUTHORIZED)

        user = request.user
        from api.models import Post, Event, Resource, Tag

        # Get user's interests based on their profile and activity
        user_tags = set()

        # Tags from user's posts
        post_tags = Tag.objects.filter(posts__author=user).distinct()
        user_tags.update(post_tag.id for post_tag in post_tags)

        # Tags from posts the user has liked
        liked_post_tags = Tag.objects.filter(posts__likes=user).distinct()
        user_tags.update(liked_post_tag.id for liked_post_tag in liked_post_tags)

        # Get user's expertise from profile
        user_expertise = user.profile.expertise if hasattr(user, 'profile') and user.profile.expertise else ""

        # Recommended posts based on tags and not authored by the user
        recommended_posts = Post.objects.filter(
            tags__id__in=user_tags,
            moderation_status='approved'
        ).exclude(
            author=user
        ).distinct().order_by('-created_at')[:5]

        # Recommended events based on tags and not organized by the user
        from django.utils import timezone
        recommended_events = Event.objects.filter(
            tags__id__in=user_tags,
            moderation_status='approved',
            date__gte=timezone.now()
        ).exclude(
            organizer=user
        ).distinct().order_by('date')[:5]

        # Recommended resources based on tags and not authored by the user
        recommended_resources = Resource.objects.filter(
            tags__id__in=user_tags,
            moderation_status='approved'
        ).exclude(
            author=user
        ).distinct().order_by('-created_at')[:5]

        return Response({
            'recommended_posts': [{'id': post.id, 'title': post.title, 'author': post.author.username} for post in recommended_posts],
            'recommended_events': [{'id': event.id, 'title': event.title, 'date': event.date, 'organizer': event.organizer.username} for event in recommended_events],
            'recommended_resources': [{'id': resource.id, 'title': resource.title, 'resource_type': resource.resource_type, 'author': resource.author.username} for resource in recommended_resources],
        })

class UserProfileViewSet(viewsets.ModelViewSet):
    queryset = UserProfile.objects.all()
    serializer_class = UserProfileSerializer
    permission_classes = [permissions.IsAuthenticated]

    @action(detail=False, methods=['get'])
    def my_profile(self, request):
        profile = request.user.profile
        serializer = self.get_serializer(profile)
        return Response(serializer.data)

    @action(detail=False, methods=['put', 'patch'])
    def update_my_profile(self, request):
        profile = request.user.profile
        serializer = self.get_serializer(profile, data=request.data, partial=True)

        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
