/**
 * Standard paginated response from the API
 */
export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

/**
 * Standard error response from the API
 */
export interface ApiErrorResponse {
  message: string;
  detail?: string;
  errors?: Record<string, string[]>;
  code?: string;
}

/**
 * Standard success response for operations that return no data
 */
export interface SuccessResponse {
  success: boolean;
  message?: string;
}

/**
 * Query parameters for paginated requests
 */
export interface PaginationParams {
  page?: number;
  page_size?: number;
  offset?: number;
  limit?: number;
}

/**
 * Query parameters for filtered requests
 */
export interface FilterParams {
  search?: string;
  ordering?: string;
  [key: string]: any;
}

/**
 * Combined query parameters
 */
export type QueryParams = PaginationParams & FilterParams;
