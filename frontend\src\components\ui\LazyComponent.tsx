import React, { useState, useEffect, useRef } from 'react';


interface LazyComponentProps {
  children: React.ReactNode;
  placeholder?: React.ReactNode;
  threshold?: number;
  rootMargin?: string;
  className?: string;
  placeholderHeight?: string | number;
  placeholderWidth?: string | number;
}

/**
 * LazyComponent wrapper that renders children only when they enter the viewport
 */
const LazyComponent: React.FC<LazyComponentProps> = ({ children,
  placeholder,
  threshold = 0.1,
  rootMargin = '200px',
  className = '',
  placeholderHeight = '200px',
  placeholderWidth = '100%',
 }) => {
  const [isInView, setIsInView] = useState(false);
  const componentRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Set up intersection observer to detect when component enters viewport
  useEffect(() => {
    if (!componentRef.current) return;

    observerRef.current = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting) {
          setIsInView(true);
          // Once the component is in view, we don't need to observe it anymore
          if (observerRef.current && componentRef.current) {
            observerRef.current.unobserve(componentRef.current);
          }
        }
      },
      {
        threshold,
        rootMargin,
      }
    );

    observerRef.current.observe(componentRef.current);

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [threshold, rootMargin]);

  return (
    <div ref={componentRef} className={className}>
      {isInView ? (
        children
      ) : (
        placeholder || (
          <div
            className="rounded bg-gray-300 animate-pulse"
            style={{
              height: placeholderHeight,
              width: placeholderWidth,
            }}
          />
        )
      )}
    </div>
  );
};

LazyComponent.displayName = "LazyComponent";

export default LazyComponent;
