/**
 * Unified Notifications Section
 * Placeholder for notifications section that adapts to different user roles
 */

import React from 'react';
import { useTranslation } from 'react-i18next';
import { RTLText } from '../../../common';
import { DashboardRole } from '../../../../types/dashboard';
import { useDashboardTheme } from '../../../../contexts/DashboardContext';
import { Bell } from 'lucide-react';

interface UnifiedNotificationsSectionProps {
  role: DashboardRole;
  onUpdate?: (sectionId: string, data: any) => void;
  className?: string;
}

const UnifiedNotificationsSection: React.FC<UnifiedNotificationsSectionProps> = ({
  role,
  onUpdate,
  className = '',
}) => {
  const { t } = useTranslation();
  const { getCardClasses, getTextClasses } = useDashboardTheme();

  return (
    <div className={className}>
      <div className="mb-6">
        <RTLText as="h2" className={`text-xl font-semibold ${getTextClasses('primary')}`}>
          {t('dashboard.notifications', 'Notifications')}
        </RTLText>
      </div>
      
      <div className={getCardClasses()}>
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <Bell className={`w-12 h-12 ${getTextClasses('secondary')} mx-auto mb-4`} />
            <RTLText as="p" className={`${getTextClasses('secondary')}`}>
              {t('dashboard.notifications.placeholder', 'Notifications section for {{role}} coming soon', { role })}
            </RTLText>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UnifiedNotificationsSection;
