import React from 'react';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../../../../store/hooks';
import { useLanguage } from '../../../../hooks/useLanguage';
import { CheckCircle } from 'lucide-react';

interface CompletionStatusProps {
  completedItems: number;
  totalItems: number;
}

const CompletionStatus: React.FC<CompletionStatusProps> = ({ completedItems,
  totalItems
 }) => {
  const { t } = useTranslation();
  const { isRTL, language } = useLanguage();

  return (
    <div className="mb-4">
      <p className={`text-sm text-green-400 flex items-center mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
        <CheckCircle size={14} className={`${language === 'ar' ? 'ml-1' : 'mr-1'}`} />
        {completedItems} {t('common.of')} {totalItems} {t('common.items')}
      </p>
    </div>
  );
};

export default CompletionStatus;
