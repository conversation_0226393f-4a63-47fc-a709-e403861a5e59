import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  TrendingUp,
  ChevronDown,
  ChevronUp,
  Clock,
  AlertTriangle,
  Lightbulb,
  Sparkles
} from 'lucide-react';
import { PredictiveAnalytics } from '../../services/enhancedAnalyticsApi';
import { formatPercentage } from '../../utils/exportUtils';
import {
  LineChart, Line, XAxis, YAxis, CartesianGrid,
  <PERSON>lt<PERSON>, Legend, ResponsiveContainer
} from 'recharts';

interface PredictiveAnalyticsSectionProps {
  predictiveAnalytics: PredictiveAnalytics | null;
  expanded: boolean;
  onToggle: () => void;
}

const PredictiveAnalyticsSection: React.FC<PredictiveAnalyticsSectionProps> = ({
  predictiveAnalytics,
  expanded,
  onToggle
}) => {
  const { t } = useTranslation();

  if (!predictiveAnalytics) {
    return null;
  }

  // Prepare growth prediction data for chart
  const growthPredictionData = predictiveAnalytics.growth_predictions?.months?.map((month, index) => ({
    month,
    progressRate: predictiveAnalytics.growth_predictions.progress_rate[index],
    milestoneCompletion: predictiveAnalytics.growth_predictions.milestone_completion[index],
    goalAchievement: predictiveAnalytics.growth_predictions.goal_achievement[index]
  })) || [];

  // Prepare milestone predictions data
  const milestonePredictions = predictiveAnalytics.milestone_predictions?.predictions
    ? Object.values(predictiveAnalytics.milestone_predictions.predictions)
    : [];

  // Sort milestone predictions by days remaining
  const sortedMilestonePredictions = [...milestonePredictions].sort((a, b) => a.days_remaining - b.days_remaining);

  return (
    <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
      <div
        className="flex items-center justify-between cursor-pointer"
        onClick={onToggle}
      >
        <h2 className="text-lg font-bold flex items-center">
          <Sparkles size={18} className="text-yellow-400 mr-2" />
          {t('incubator.analytics.predictive.title')}
        </h2>

        <div className="flex items-center">
          <span className="text-sm text-gray-400 mr-2">
            {t(`incubator.analytics.predictive.${predictiveAnalytics.prediction_confidence}Confidence`)} {t('incubator.analytics.predictive.confidence')}
          </span>
          {expanded ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
        </div>
      </div>

      {expanded && (
        <div className="mt-4 space-y-6">
          {/* Success Probability */}
          <div className="bg-indigo-950/50 rounded-lg p-4 border border-indigo-800/50">
            <h3 className="text-md font-medium mb-3">{t('incubator.analytics.predictive.successProbability')}</h3>

            <div className="flex items-center">
              <div className="relative w-full h-6 bg-indigo-950 rounded-full overflow-hidden">
                <div
                  className={`absolute top-0 left-0 h-full rounded-full ${
                    predictiveAnalytics.success_probability >= 70 ? 'bg-green-500' :
                    predictiveAnalytics.success_probability >= 50 ? 'bg-blue-500' :
                    predictiveAnalytics.success_probability >= 30 ? 'bg-yellow-500' :
                    'bg-red-500'
                  }`}
                  style={{ width: `${predictiveAnalytics.success_probability}%` }}
                ></div>
                <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center">
                  <span className="text-sm font-medium text-white">
                    {formatPercentage(predictiveAnalytics.success_probability)}
                  </span>
                </div>
              </div>
            </div>

            <div className="mt-2 text-sm text-gray-400">
              <div>
                {t('incubator.analytics.predictive.successProbabilityDescription', {
                  percentage: formatPercentage(predictiveAnalytics.success_probability)
                })}
              </div>
            </div>
          </div>

          {/* Growth Projections */}
          <div className="bg-indigo-950/50 rounded-lg p-4 border border-indigo-800/50">
            <h3 className="text-md font-medium mb-3">{t('incubator.analytics.predictive.growthProjections')}</h3>

            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={growthPredictionData}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#334155" />
                  <XAxis
                    dataKey="month"
                    stroke="#94a3b8"
                    tick={{ fill: '#94a3b8' }}
                  />
                  <YAxis
                    stroke="#94a3b8"
                    tick={{ fill: '#94a3b8' }}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: '#1e293b',
                      borderColor: '#475569',
                      color: '#f8fafc'
                    }}
                    labelStyle={{ color: '#f8fafc' }}
                  />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="progressRate"
                    name={t('incubator.analytics.predictive.progressRate')}
                    stroke="#8b5cf6"
                    activeDot={{ r: 8 }}
                  />
                  <Line
                    type="monotone"
                    dataKey="milestoneCompletion"
                    name={t('incubator.analytics.predictive.milestoneCompletion')}
                    stroke="#ec4899"
                  />
                  <Line
                    type="monotone"
                    dataKey="goalAchievement"
                    name={t('incubator.analytics.predictive.goalAchievement')}
                    stroke="#3b82f6"
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>

            <div className="mt-2 text-sm text-gray-400">
              <div>
                {t('incubator.analytics.predictive.growthProjectionsDescription')}
              </div>
            </div>
          </div>

          {/* Milestone Predictions */}
          <div className="bg-indigo-950/50 rounded-lg p-4 border border-indigo-800/50">
            <h3 className="text-md font-medium mb-3">{t('incubator.analytics.predictive.milestonePredictions')}</h3>

            {sortedMilestonePredictions.length > 0 ? (
              <div className="space-y-3">
                {sortedMilestonePredictions.map((milestone) => (
                  <div
                    key={milestone.milestone_id}
                    className="bg-indigo-900/30 rounded-lg p-3 border border-indigo-800/30"
                  >
                    <div className="flex justify-between items-center mb-1">
                      <h4 className="font-medium">{milestone.title}</h4>
                      <div className="flex items-center">
                        <Clock size={14} className="text-gray-400 mr-1" />
                        <span className={`text-sm ${
                          milestone.days_remaining < 0 ? 'text-red-400' :
                          milestone.days_remaining < 7 ? 'text-yellow-400' :
                          'text-gray-400'
                        }`}>
                          {milestone.days_remaining < 0
                            ? `${Math.abs(milestone.days_remaining)} ${t('incubator.analytics.predictive.daysOverdue')}`
                            : `${milestone.days_remaining} ${t('incubator.analytics.predictive.daysRemaining')}`}
                        </span>
                      </div>
                    </div>

                    <div className="flex justify-between items-center text-sm">
                      <span className="text-gray-400">
                        {t('incubator.analytics.predictive.predictedCompletion')} {new Date(milestone.predicted_completion_date).toLocaleDateString()}
                      </span>
                      <span className={`${
                        milestone.confidence === 'high' ? 'text-green-400' :
                        milestone.confidence === 'medium' ? 'text-yellow-400' :
                        'text-gray-400'
                      }`}>
                        {t(`incubator.analytics.predictive.${milestone.confidence}Confidence`)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 text-gray-400">
                <div>{t('incubator.analytics.predictive.noMilestonePredictions')}</div>
                <p className="text-sm mt-1">{t('incubator.analytics.predictive.createMilestones')}</p>
              </div>
            )}
          </div>

          {/* Risk Factors */}
          {predictiveAnalytics.risk_factors?.high_risks?.length > 0 && (
            <div className="bg-indigo-950/50 rounded-lg p-4 border border-indigo-800/50">
              <h3 className="text-md font-medium mb-3 flex items-center">
                <AlertTriangle size={16} className="text-red-400 mr-2" />
                {t('incubator.analytics.predictive.keyRiskFactors')}
              </h3>

              <div className="space-y-3">
                {predictiveAnalytics.risk_factors.high_risks.map((risk, index) => (
                  <div key={index} className="bg-red-900/20 rounded-lg p-3 border border-red-800/30">
                    <h4 className="font-medium mb-1">{risk.title}</h4>
                    <div className="text-sm text-gray-400 mb-2">{risk.description}</div>
                    <div className="flex items-start">
                      <span className="text-xs bg-red-900/50 text-red-300 px-2 py-1 rounded-md">{t('incubator.analytics.predictive.mitigation')}</span>
                      <div className="text-sm text-gray-400 ml-2">{risk.mitigation}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Opportunity Areas */}
          {predictiveAnalytics.opportunity_areas?.key_opportunities?.length > 0 && (
            <div className="bg-indigo-950/50 rounded-lg p-4 border border-indigo-800/50">
              <h3 className="text-md font-medium mb-3 flex items-center">
                <Lightbulb size={16} className="text-yellow-400 mr-2" />
                {t('incubator.analytics.predictive.keyOpportunities')}
              </h3>

              <div className="space-y-3">
                {predictiveAnalytics.opportunity_areas.key_opportunities.map((opportunity, index) => (
                  <div key={index} className="bg-yellow-900/20 rounded-lg p-3 border border-yellow-800/30">
                    <h4 className="font-medium mb-1">{opportunity.title}</h4>
                    <div className="text-sm text-gray-400 mb-2">{opportunity.description}</div>
                    <div className="flex items-start">
                      <span className="text-xs bg-yellow-900/50 text-yellow-300 px-2 py-1 rounded-md">{t('incubator.analytics.predictive.action')}</span>
                      <p className="text-sm text-gray-400 ml-2">{opportunity.action}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default PredictiveAnalyticsSection;
