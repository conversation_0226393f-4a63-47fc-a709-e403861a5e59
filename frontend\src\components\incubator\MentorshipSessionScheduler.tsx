import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { Calendar, Clock, Video, Users, Link, Check, X, AlertCircle } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { useAppDispatch, useAppSelector } from '../../store/hooks';
import {
  createMentorshipSession,
  fetchMentorshipMatches
} from '../../store/incubatorSlice';
import { MentorshipMatch } from '../../services/incubatorApi';

interface MentorshipSessionSchedulerProps {
  mentorshipMatchId?: number;
  onSuccess?: () => void;
  onCancel?: () => void;
}

const MentorshipSessionScheduler: React.FC<MentorshipSessionSchedulerProps> = ({ mentorshipMatchId,
  onSuccess,
  onCancel
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { mentorshipMatches, isLoading, error } = useAppSelector(state => state.incubator);
  const [selectedMatch, setSelectedMatch] = useState<MentorshipMatch | null>(null);

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    scheduled_at: '',
    scheduled_time: '10:00',
    duration_minutes: 60,
    session_type: 'video',
    video_provider: 'jitsi',
    location: '',
    meeting_link: '',
  });

  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const hasFetchedMatches = useRef(false);

  // Fetch mentorship matches if needed
  useEffect(() => {
    if (!mentorshipMatches.length && !isLoading && !hasFetchedMatches.current) {
      hasFetchedMatches.current = true;
      dispatch(fetchMentorshipMatches());
    }
  }, [dispatch]); // Remove mentorshipMatches.length and isLoading from dependencies to prevent infinite loop

  // Set selected match if mentorshipMatchId is provided
  useEffect(() => {
    if (mentorshipMatchId && mentorshipMatches.length) {
      const match = mentorshipMatches.find(m => m.id === mentorshipMatchId);
      if (match) {
        setSelectedMatch(match);
      }
    }
  }, [mentorshipMatchId, mentorshipMatches]);

  // Set default date to tomorrow at 10:00 AM
  useEffect(() => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const formattedDate = tomorrow.toISOString().split('T')[0];

    setFormData(prev => ({
      ...prev,
      scheduled_at: formattedDate
    }));
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error for this field
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleMatchChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const matchId = parseInt(e.target.value);
    const match = mentorshipMatches.find(m => m.id === matchId) || null;
    setSelectedMatch(match);
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!selectedMatch) {
      errors.mentorship_match = t('mentorship.sessionScheduler.validation.mentorshipRequired');
    }

    if (!formData.title.trim()) {
      errors.title = t('mentorship.sessionScheduler.validation.titleRequired');
    }

    if (!formData.scheduled_at) {
      errors.scheduled_at = t('mentorship.sessionScheduler.validation.dateRequired');
    }

    if (!formData.scheduled_time) {
      errors.scheduled_time = t('mentorship.sessionScheduler.validation.timeRequired');
    }

    if (formData.session_type === 'video' && !formData.video_provider) {
      errors.video_provider = t('mentorship.sessionScheduler.validation.videoProviderRequired');
    }

    if (formData.session_type === 'video' && formData.video_provider === 'custom' && !formData.meeting_link) {
      errors.meeting_link = t('mentorship.sessionScheduler.validation.meetingLinkRequired');
    }

    if (formData.session_type === 'in_person' && !formData.location) {
      errors.location = t('mentorship.sessionScheduler.validation.locationRequired');
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // Combine date and time
    const dateTime = new Date(`${formData.scheduled_at}T${formData.scheduled_time}`);

    try {
      await dispatch(createMentorshipSession({
        mentorship_match: selectedMatch!.id,
        title: formData.title,
        description: formData.description,
        scheduled_at: dateTime.toISOString(),
        duration_minutes: formData.duration_minutes,
        session_type: formData.session_type,
        location: formData.location,
        video_provider: formData.session_type === 'video' ? formData.video_provider : null,
        meeting_link: formData.session_type === 'video' && formData.video_provider === 'custom' ? formData.meeting_link : null
      })).unwrap();

      setSuccessMessage(t('mentorship.sessionScheduler.success'));

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (err) {
      console.error('Error scheduling session:', err);
      setFormErrors({
        submit: t('mentorship.sessionScheduler.validation.submitError')
      });
    }
  };

  return (
    <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
      <h2 className="text-xl font-bold mb-4">{t('mentorship.sessionScheduler.title')}</h2>

      {successMessage && (
        <div className={`mb-4 p-3 bg-green-900/50 text-green-200 rounded-md flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
          <Check size={18} className={isRTL ? 'ml-2' : 'mr-2'} />
          {successMessage}
        </div>
      )}

      {formErrors.submit && (
        <div className={`mb-4 p-3 bg-red-900/50 text-red-200 rounded-md flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
          <AlertCircle size={18} className={isRTL ? 'ml-2' : 'mr-2'} />
          {formErrors.submit}
        </div>
      )}

      <form onSubmit={handleSubmit}>
        {/* Mentorship Match Selection */}
        {!mentorshipMatchId && (
          <div className="mb-4">
            <label className={`block text-sm font-medium mb-1 ${isRTL ? 'text-right' : 'text-left'}`}>
              {t('mentorship.sessionScheduler.selectMentorship')}
            </label>
            <select
              className={`w-full bg-indigo-950 border ${formErrors.mentorship_match ? 'border-red-500' : 'border-indigo-700'} rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-purple-500 ${isRTL ? 'text-right' : 'text-left'}`}
              value={selectedMatch?.id || ''}
              onChange={handleMatchChange}
              dir={isRTL ? 'rtl' : 'ltr'}
            >
              <option value="">{t('mentorship.sessionScheduler.selectMentorshipPlaceholder')}</option>
              {mentorshipMatches.map(match => (
                <option key={match.id} value={match.id}>
                  {match.mentor.user.first_name} {match.mentor.user.last_name} - {match.business_idea_title}
                </option>
              ))}
            </select>
            {formErrors.mentorship_match && (
              <div className={`text-red-500 text-xs mt-1 ${isRTL ? 'text-right' : 'text-left'}`}>
                {formErrors.mentorship_match}
              </div>
            )}
          </div>
        )}

        {/* Session Title */}
        <div className="mb-4">
          <label className={`block text-sm font-medium mb-1 ${isRTL ? 'text-right' : 'text-left'}`}>
            {t('mentorship.sessionScheduler.sessionTitle')}
          </label>
          <input
            type="text"
            name="title"
            value={formData.title}
            onChange={handleInputChange}
            placeholder={t('mentorship.sessionScheduler.sessionTitlePlaceholder')}
            className={`w-full bg-indigo-950 border ${formErrors.title ? 'border-red-500' : 'border-indigo-700'} rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-purple-500 ${isRTL ? 'text-right' : 'text-left'}`}
            dir={isRTL ? 'rtl' : 'ltr'}
          />
          {formErrors.title && (
            <div className={`text-red-500 text-xs mt-1 ${isRTL ? 'text-right' : 'text-left'}`}>
              {formErrors.title}
            </div>
          )}
        </div>

        {/* Session Description */}
        <div className="mb-4">
          <label className={`block text-sm font-medium mb-1 ${isRTL ? 'text-right' : 'text-left'}`}>
            {t('mentorship.sessionScheduler.description')}
          </label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            placeholder={t('mentorship.sessionScheduler.descriptionPlaceholder')}
            rows={3}
            className={`w-full bg-indigo-950 border border-indigo-700 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-purple-500 ${isRTL ? 'text-right' : 'text-left'}`}
            dir={isRTL ? 'rtl' : 'ltr'}
          />
        </div>

        {/* Date and Time */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className={`block text-sm font-medium mb-1 ${isRTL ? 'text-right' : 'text-left'}`}>
              {t('mentorship.sessionScheduler.date')}
            </label>
            <div className="relative">
              <div className={`absolute inset-y-0 ${isRTL ? 'right-0 pr-3' : 'left-0 pl-3'} flex items-center pointer-events-none`}>
                <Calendar size={16} className="text-gray-400" />
              </div>
              <input
                type="date"
                name="scheduled_at"
                value={formData.scheduled_at}
                onChange={handleInputChange}
                className={`w-full bg-indigo-950 border ${formErrors.scheduled_at ? 'border-red-500' : 'border-indigo-700'} rounded-md p-2 ${isRTL ? 'pr-10 text-right' : 'pl-10 text-left'} focus:outline-none focus:ring-2 focus:ring-purple-500`}
                dir={isRTL ? 'rtl' : 'ltr'}
              />
            </div>
            {formErrors.scheduled_at && (
              <div className={`text-red-500 text-xs mt-1 ${isRTL ? 'text-right' : 'text-left'}`}>
                {formErrors.scheduled_at}
              </div>
            )}
          </div>

          <div>
            <label className={`block text-sm font-medium mb-1 ${isRTL ? 'text-right' : 'text-left'}`}>
              {t('mentorship.sessionScheduler.time')}
            </label>
            <div className="relative">
              <div className={`absolute inset-y-0 ${isRTL ? 'right-0 pr-3' : 'left-0 pl-3'} flex items-center pointer-events-none`}>
                <Clock size={16} className="text-gray-400" />
              </div>
              <input
                type="time"
                name="scheduled_time"
                value={formData.scheduled_time}
                onChange={handleInputChange}
                className={`w-full bg-indigo-950 border ${formErrors.scheduled_time ? 'border-red-500' : 'border-indigo-700'} rounded-md p-2 ${isRTL ? 'pr-10 text-right' : 'pl-10 text-left'} focus:outline-none focus:ring-2 focus:ring-purple-500`}
                dir={isRTL ? 'rtl' : 'ltr'}
              />
            </div>
            {formErrors.scheduled_time && (
              <div className={`text-red-500 text-xs mt-1 ${isRTL ? 'text-right' : 'text-left'}`}>
                {formErrors.scheduled_time}
              </div>
            )}
          </div>
        </div>

        {/* Duration */}
        <div className="mb-4">
          <label className={`block text-sm font-medium mb-1 ${isRTL ? 'text-right' : 'text-left'}`}>
            {t('mentorship.sessionScheduler.duration')}
          </label>
          <select
            name="duration_minutes"
            value={formData.duration_minutes}
            onChange={handleInputChange}
            className={`w-full bg-indigo-950 border border-indigo-700 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-purple-500 ${isRTL ? 'text-right' : 'text-left'}`}
            dir={isRTL ? 'rtl' : 'ltr'}
          >
            <option value={15}>{t('mentorship.sessionScheduler.durations.15')}</option>
            <option value={30}>{t('mentorship.sessionScheduler.durations.30')}</option>
            <option value={45}>{t('mentorship.sessionScheduler.durations.45')}</option>
            <option value={60}>{t('mentorship.sessionScheduler.durations.60')}</option>
            <option value={90}>{t('mentorship.sessionScheduler.durations.90')}</option>
            <option value={120}>{t('mentorship.sessionScheduler.durations.120')}</option>
          </select>
        </div>

        {/* Session Type */}
        <div className="mb-4">
          <label className={`block text-sm font-medium mb-1 ${isRTL ? 'text-right' : 'text-left'}`}>
            {t('mentorship.sessionScheduler.sessionType')}
          </label>
          <select
            name="session_type"
            value={formData.session_type}
            onChange={handleInputChange}
            className={`w-full bg-indigo-950 border border-indigo-700 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-purple-500 ${isRTL ? 'text-right' : 'text-left'}`}
            dir={isRTL ? 'rtl' : 'ltr'}
          >
            <option value="video">{t('mentorship.sessionScheduler.sessionTypes.video')}</option>
            <option value="phone">{t('mentorship.sessionScheduler.sessionTypes.phone')}</option>
            <option value="in_person">{t('mentorship.sessionScheduler.sessionTypes.inPerson')}</option>
            <option value="chat">{t('mentorship.sessionScheduler.sessionTypes.chat')}</option>
          </select>
        </div>

        {/* Video Provider (if video call) */}
        {formData.session_type === 'video' && (
          <div className="mb-4">
            <label className={`block text-sm font-medium mb-1 ${isRTL ? 'text-right' : 'text-left'}`}>
              {t('mentorship.sessionScheduler.videoProvider')}
            </label>
            <select
              name="video_provider"
              value={formData.video_provider}
              onChange={handleInputChange}
              className={`w-full bg-indigo-950 border ${formErrors.video_provider ? 'border-red-500' : 'border-indigo-700'} rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-purple-500 ${isRTL ? 'text-right' : 'text-left'}`}
              dir={isRTL ? 'rtl' : 'ltr'}
            >
              <option value="jitsi">{t('mentorship.sessionScheduler.videoProviders.jitsi')}</option>
              <option value="custom">{t('mentorship.sessionScheduler.videoProviders.custom')}</option>
            </select>
            {formErrors.video_provider && (
              <div className={`text-red-500 text-xs mt-1 ${isRTL ? 'text-right' : 'text-left'}`}>
                {formErrors.video_provider}
              </div>
            )}
          </div>
        )}

        {/* Custom Meeting Link (if custom provider) */}
        {formData.session_type === 'video' && formData.video_provider === 'custom' && (
          <div className="mb-4">
            <label className={`block text-sm font-medium mb-1 ${isRTL ? 'text-right' : 'text-left'}`}>
              {t('mentorship.sessionScheduler.meetingLink')}
            </label>
            <div className="relative">
              <div className={`absolute inset-y-0 ${isRTL ? 'right-0 pr-3' : 'left-0 pl-3'} flex items-center pointer-events-none`}>
                <Link size={16} className="text-gray-400" />
              </div>
              <input
                type="url"
                name="meeting_link"
                value={formData.meeting_link}
                onChange={handleInputChange}
                placeholder={t('mentorship.sessionScheduler.meetingLinkPlaceholder')}
                className={`w-full bg-indigo-950 border ${formErrors.meeting_link ? 'border-red-500' : 'border-indigo-700'} rounded-md p-2 ${isRTL ? 'pr-10 text-right' : 'pl-10 text-left'} focus:outline-none focus:ring-2 focus:ring-purple-500`}
                dir={isRTL ? 'rtl' : 'ltr'}
              />
            </div>
            {formErrors.meeting_link && (
              <div className={`text-red-500 text-xs mt-1 ${isRTL ? 'text-right' : 'text-left'}`}>
                {formErrors.meeting_link}
              </div>
            )}
          </div>
        )}

        {/* Location (if in-person) */}
        {formData.session_type === 'in_person' && (
          <div className="mb-4">
            <label className={`block text-sm font-medium mb-1 ${isRTL ? 'text-right' : 'text-left'}`}>
              {t('mentorship.sessionScheduler.location')}
            </label>
            <input
              type="text"
              name="location"
              value={formData.location}
              onChange={handleInputChange}
              placeholder={t('mentorship.sessionScheduler.locationPlaceholder')}
              className={`w-full bg-indigo-950 border ${formErrors.location ? 'border-red-500' : 'border-indigo-700'} rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-purple-500 ${isRTL ? 'text-right' : 'text-left'}`}
              dir={isRTL ? 'rtl' : 'ltr'}
            />
            {formErrors.location && (
              <div className={`text-red-500 text-xs mt-1 ${isRTL ? 'text-right' : 'text-left'}`}>
                {formErrors.location}
              </div>
            )}
          </div>
        )}

        {/* Submit Buttons */}
        <div className={`flex ${isRTL ? 'justify-start space-x-reverse space-x-3' : 'justify-end space-x-3'} mt-6`}>
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-md text-white"
            >
              {t('mentorship.sessionScheduler.cancel')}
            </button>
          )}

          <button
            type="submit"
            disabled={isLoading}
            className={`px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}
          >
            {isLoading ? (
              <>
                <div className={`w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin ${isRTL ? 'ml-2' : 'mr-2'}`}></div>
                {t('mentorship.sessionScheduler.scheduling')}
              </>
            ) : (
              <>
                <Calendar size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
                {t('mentorship.sessionScheduler.scheduleSession')}
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default MentorshipSessionScheduler;
