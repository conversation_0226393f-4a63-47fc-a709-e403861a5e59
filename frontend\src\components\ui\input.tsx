import React from 'react';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  className?: string;
  variant?: 'default' | 'light';
}

export const Input: React.FC<InputProps> = ({ className = '', variant = 'default', ...props }) => {
  const getVariantClasses = () => {
    switch (variant) {
      case 'light':
        return 'glass-light';
      default:
        return 'theme-bg-input';
    }
  };

  return (
    <input
      className={`
        flex h-10 w-full rounded-md border border-glass-border
        ${getVariantClasses()} px-3 py-2 text-sm
        text-glass-primary
        placeholder:text-glass-muted
        focus:outline-none focus:ring-2 focus:ring-glass-border focus:border-glass-border
        disabled:cursor-not-allowed disabled:opacity-50
        transition-all duration-300
        ${className}
      `}
      {...props}
    />
  );
};
