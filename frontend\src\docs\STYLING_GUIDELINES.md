# Glass Morphism Design System - Styling Guidelines

## Overview

This application uses a **Glass Morphism Design System** for consistent, modern UI styling. This document provides comprehensive guidelines for maintaining styling consistency across all components.

## 🎨 Design System Principles

### Core Philosophy
- **Glass Morphism**: Semi-transparent backgrounds with backdrop blur effects
- **Consistency**: All components should use the established design tokens
- **Accessibility**: Proper contrast ratios and focus indicators
- **RTL Support**: Full right-to-left language support

## 🔧 Available Design Tokens

### Glass Morphism Classes

#### Background Classes
```css
.glass-morphism     /* Primary glass effect - main cards, modals */
.glass-light        /* Lighter glass effect - secondary elements */
.glass-heavy        /* Heavier glass effect - emphasis elements */
```

#### Color Classes
```css
.text-glass-primary    /* Primary text color: #ffffff */
.text-glass-secondary  /* Secondary text color: rgba(255, 255, 255, 0.7) */
.text-glass-muted      /* Muted text color: rgba(255, 255, 255, 0.5) */
.text-glass-accent     /* Accent text color: rgba(255, 255, 255, 0.9) */
```

#### Border Classes
```css
.border-glass-border   /* Standard border: rgba(255, 255, 255, 0.2) */
```

#### Background Utilities
```css
.theme-bg-primary      /* Primary background */
.theme-bg-secondary    /* Secondary background */
.theme-bg-card         /* Card background */
.theme-bg-feature      /* Feature background */
.theme-bg-input        /* Input background */
```

### Component-Specific Classes

#### Cards
```css
.card                  /* Standard card styling */
.feature-card          /* Feature section cards */
.testimonial-card      /* Testimonial cards */
.contact-card          /* Contact form cards */
.gradient-card         /* Cards with gradient effects */
```

#### Buttons
```css
.btn-primary           /* Primary button styling */
```

#### Forms
```css
.input                 /* Standard input styling */
```

#### Navigation
```css
.navbar                /* Navigation bar styling */
```

#### Content
```css
.section-primary       /* Primary section styling */
.section-secondary     /* Secondary section styling */
```

## 🚫 What NOT to Use

### Deprecated Classes
❌ **DO NOT USE** these legacy classes:
```css
/* Legacy background colors */
.bg-white
.bg-gray-800
.bg-black
.bg-blue-100
.bg-gray-100

/* Legacy text colors */
.text-black
.text-gray-900
.text-gray-800
.text-blue-800

/* Legacy border colors */
.border-gray-300
.border-gray-600
.border-gray-200

/* Legacy dark mode classes */
.dark:bg-gray-800
.dark:text-white
.dark:border-gray-600
```

## ✅ Migration Examples

### Before (Legacy)
```tsx
// ❌ DON'T DO THIS
<div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
  <h3 className="text-gray-900 dark:text-white">Title</h3>
  <p className="text-gray-600 dark:text-gray-300">Description</p>
</div>
```

### After (Glass Morphism)
```tsx
// ✅ DO THIS
<div className="glass-morphism border border-glass-border">
  <h3 className="text-glass-primary">Title</h3>
  <p className="text-glass-secondary">Description</p>
</div>
```

## 🎯 Component Guidelines

### Cards
```tsx
// Standard card
<Card variant="default" className="p-6">
  <CardHeader>
    <CardTitle>Card Title</CardTitle>
  </CardHeader>
  <CardContent>
    Card content here
  </CardContent>
</Card>

// Light variant card
<Card variant="light" className="p-4">
  Content
</Card>
```

### Buttons
```tsx
// Use the Button component with variants
<Button variant="primary" size="md">
  Primary Action
</Button>

<Button variant="secondary" size="sm">
  Secondary Action
</Button>

<Button variant="outline" size="lg">
  Outline Button
</Button>
```

### Form Elements
```tsx
// Input fields
<Input 
  variant="default" 
  placeholder="Enter text..."
  className="mb-4"
/>

// Textarea
<Textarea 
  variant="light"
  placeholder="Enter description..."
  rows={4}
/>

// Select dropdown
<Select value={value} onValueChange={setValue}>
  <SelectTrigger>
    <SelectValue placeholder="Select option..." />
  </SelectTrigger>
  <SelectContent>
    <SelectItem value="option1">Option 1</SelectItem>
    <SelectItem value="option2">Option 2</SelectItem>
  </SelectContent>
</Select>
```

### Badges
```tsx
// Status badges
<Badge variant="default">Default</Badge>
<Badge variant="success">Success</Badge>
<Badge variant="warning">Warning</Badge>
<Badge variant="destructive">Error</Badge>
```

## 🌍 RTL Support

Always use RTL-aware components and utilities:

```tsx
import { RTLText, RTLFlex, RTLIcon } from '../components/rtl';

// RTL-aware text
<RTLText as="h1" className="text-glass-primary">
  Title
</RTLText>

// RTL-aware flex container
<RTLFlex className="items-center gap-4">
  <RTLIcon icon={IconComponent} />
  <span>Content</span>
</RTLFlex>
```

## 🎨 Role-Based Styling

For dashboard components, use role-specific accent colors while maintaining glass morphism base:

```tsx
const getRoleAccent = (role: string) => {
  switch (role) {
    case 'super_admin': return 'border-red-500/30';
    case 'admin': return 'border-purple-500/30';
    case 'moderator': return 'border-blue-500/30';
    case 'mentor': return 'border-blue-400/30';
    case 'investor': return 'border-green-500/30';
    default: return 'border-glass-border';
  }
};

// Usage
<div className={`glass-morphism border ${getRoleAccent(userRole)}`}>
  Content
</div>
```

## 🔍 Validation Checklist

Before submitting code, ensure:

- [ ] No hardcoded `bg-white`, `bg-gray-*`, `text-black` classes
- [ ] No `dark:` prefixed classes (use glass morphism instead)
- [ ] All text uses `text-glass-*` classes
- [ ] All borders use `border-glass-border`
- [ ] All backgrounds use glass morphism classes
- [ ] RTL support is implemented where needed
- [ ] Component variants are used consistently

## 🛠 Utility Functions

Use these utility functions for consistent styling:

```tsx
import { 
  getButtonClass, 
  getCardClass, 
  getBackgroundClass,
  getTextClass 
} from '../utils/themeUtils';

// Get button classes
const buttonClasses = getButtonClass('primary', 'md', false);

// Get card classes
const cardClasses = getCardClass();

// Get background classes
const bgClasses = getBackgroundClass('light');

// Get text classes
const textClasses = getTextClass('primary');
```

## 📝 Common Patterns

### Loading States
```tsx
<div className="glass-light animate-pulse">
  <div className="h-4 bg-glass-border rounded mb-2"></div>
  <div className="h-4 bg-glass-border rounded w-3/4"></div>
</div>
```

### Error States
```tsx
<Alert variant="error">
  <AlertCircle className="h-4 w-4" />
  <AlertTitle>Error</AlertTitle>
  <AlertDescription>
    Something went wrong. Please try again.
  </AlertDescription>
</Alert>
```

### Success States
```tsx
<Alert variant="success">
  <CheckCircle className="h-4 w-4" />
  <AlertTitle>Success</AlertTitle>
  <AlertDescription>
    Operation completed successfully.
  </AlertDescription>
</Alert>
```

## 🔄 Migration Strategy

1. **Identify Legacy Components**: Search for hardcoded colors
2. **Update Gradually**: Migrate one component at a time
3. **Test Thoroughly**: Ensure visual consistency
4. **Document Changes**: Update component documentation
5. **Review**: Have team members review changes

## 📚 Resources

- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Glass Morphism Design Trends](https://uxdesign.cc/glassmorphism-in-user-interfaces-1f39bb1308c9)
- [Accessibility Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)

---

**Remember**: Consistency is key. When in doubt, refer to existing glass morphism components and follow the established patterns.
