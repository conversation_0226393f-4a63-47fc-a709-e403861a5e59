import React, { useState, useEffect } from 'react';
import { useAppSelector } from '../../store/hooks';
import { Shield, CheckCircle, XCircle, AlertTriangle, Play, RotateCcw, Eye, EyeOff } from 'lucide-react';
import { validateRoleBasedAccessControl, getRoleAccessHealthScore } from '../../utils/enhancedRoleValidator';
import { User } from '../../services/api';

interface TestResult {
  scenario: any;
  results: Record<string, any>;
  passed: boolean;
  issues: string[];
}

/**
 * Role-Based Access Control Tester Component
 * Comprehensive testing interface for role-based access control
 */
const RoleAccessControlTester: React.FC = () => {
  const { user, isAuthenticated } = useAppSelector(state => state.auth);
  const [isVisible, setIsVisible] = useState(false);
  const [isRunning, setIsRunning] = useState(false);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [selectedUser, setSelectedUser] = useState<string>('current');
  const [healthScore, setHealthScore] = useState<any>(null);

  // Update health score when component mounts
  useEffect(() => {
    const score = getRoleAccessHealthScore();
    setHealthScore(score);
  }, []);

  const runTests = async () => {
    setIsRunning(true);
    try {
      const report = validateRoleBasedAccessControl();
      setTestResults([{
        scenario: { path: 'System Validation', description: 'Overall system validation' },
        results: report.userTypeAnalysis,
        passed: report.summary.errorCount === 0,
        issues: report.issues.filter(i => i.type === 'error').map(i => i.message)
      }]);
    } catch (error) {
      console.error('Error running role access tests:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const getUserType = (user: User | null): string => {
    if (!user) return 'unauthenticated';
    if (user.is_superuser) return 'super_admin';
    if (user.is_admin) return 'admin';
    return user.profile?.primary_role?.name || 'user';
  };

  const getStatusIcon = (passed: boolean) => {
    return passed ? (
      <CheckCircle className="w-4 h-4 text-green-500" />
    ) : (
      <XCircle className="w-4 h-4 text-red-500" />
    );
  };

  const getAccessIcon = (hasAccess: boolean) => {
    return hasAccess ? (
      <CheckCircle className="w-3 h-3 text-green-500" />
    ) : (
      <XCircle className="w-3 h-3 text-red-500" />
    );
  };

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-20 right-4 z-40 bg-purple-600 hover:bg-purple-700 text-white p-3 rounded-full shadow-lg transition-colors"
        title="Show Role Access Control Tester"
      >
        <Shield className="w-5 h-5" />
      </button>
    );
  }

  const currentUserType = getUserType(user);
  const passedTests = testResults.filter(result => result.passed).length;
  const totalTests = testResults.length;

  return (
    <div className="fixed bottom-4 right-4 z-40 bg-white dark:bg-gray-800 rounded-lg shadow-2xl border border-gray-200 dark:border-gray-700 w-96 max-h-[80vh] overflow-hidden">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-purple-50 dark:bg-purple-900/20">
        <div className="flex items-center gap-2">
          <Shield className="w-5 h-5 text-purple-600" />
          <h3 className="font-semibold text-gray-900 dark:text-white">
            Role Access Control Tester
          </h3>
        </div>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        >
          <EyeOff className="w-4 h-4" />
        </button>
      </div>

      <div className="p-4 space-y-4 max-h-[calc(80vh-80px)] overflow-y-auto">
        {/* Current User Info */}
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
          <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">Current User</h4>
          {isAuthenticated && user ? (
            <div className="space-y-1 text-sm">
              <div className="text-blue-800 dark:text-blue-200">
                <strong>Type:</strong> {currentUserType}
              </div>
              <div className="text-blue-800 dark:text-blue-200">
                <strong>Username:</strong> {user.username}
              </div>
              <div className="text-blue-800 dark:text-blue-200">
                <strong>Admin:</strong> {user.is_admin ? 'Yes' : 'No'}
              </div>
              <div className="text-blue-800 dark:text-blue-200">
                <strong>Super Admin:</strong> {user.is_superuser ? 'Yes' : 'No'}
              </div>
            </div>
          ) : (
            <div className="text-blue-800 dark:text-blue-200 text-sm">Not authenticated</div>
          )}
        </div>

        {/* Test User Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Test User:
          </label>
          <select
            value={selectedUser}
            onChange={(e) => setSelectedUser(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
          >
            <option value="current">Current User</option>
            <option value="unauthenticated">Unauthenticated</option>
            <option value="user">Regular User</option>
            <option value="admin">Admin</option>
            <option value="super_admin">Super Admin</option>
            <option value="mentor">Mentor</option>
            <option value="investor">Investor</option>
            <option value="moderator">Moderator</option>
          </select>
        </div>

        {/* Health Score */}
        {healthScore && (
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">System Health</h4>
            <div className="space-y-1 text-sm">
              <div className="text-gray-700 dark:text-gray-300">
                <strong>Score:</strong> {healthScore.score}/100 (Grade: {healthScore.grade})
              </div>
              <div className="text-gray-700 dark:text-gray-300">
                <strong>Issues:</strong> {healthScore.issues}
              </div>
              <div className={`text-sm ${healthScore.score >= 80 ? 'text-green-600' : healthScore.score >= 60 ? 'text-yellow-600' : 'text-red-600'}`}>
                {healthScore.score >= 80 ? '✅ Good' : healthScore.score >= 60 ? '⚠️ Needs Attention' : '❌ Critical Issues'}
              </div>
            </div>
          </div>
        )}

        {/* Test Controls */}
        <div className="flex gap-2">
          <button
            onClick={runTests}
            disabled={isRunning}
            className="flex-1 flex items-center justify-center gap-2 px-3 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 text-sm"
          >
            {isRunning ? (
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
            ) : (
              <Play className="w-4 h-4" />
            )}
            {isRunning ? 'Running...' : 'Run Tests'}
          </button>
          
          <button
            onClick={() => setTestResults([])}
            className="px-3 py-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md text-sm"
          >
            <RotateCcw className="w-4 h-4" />
          </button>
        </div>

        {/* Test Results */}
        {testResults.length > 0 && (
          <div>
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-medium text-gray-900 dark:text-white">Test Results</h4>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {passedTests}/{totalTests} passed
              </div>
            </div>

            <div className="space-y-2 max-h-64 overflow-y-auto">
              {testResults.map((result, index) => (
                <div
                  key={index}
                  className={`p-3 rounded-lg border ${
                    result.passed
                      ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
                      : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
                  }`}
                >
                  <div className="flex items-center gap-2 mb-2">
                    {getStatusIcon(result.passed)}
                    <span className="font-medium text-sm text-gray-900 dark:text-white">
                      {result.scenario.path}
                    </span>
                  </div>
                  
                  <div className="text-xs text-gray-600 dark:text-gray-400 mb-2">
                    {result.scenario.description}
                  </div>

                  {!result.passed && result.issues.length > 0 && (
                    <div className="space-y-1">
                      {result.issues.map((issue, issueIndex) => (
                        <div key={issueIndex} className="text-xs text-red-700 dark:text-red-300 flex items-start gap-1">
                          <AlertTriangle className="w-3 h-3 mt-0.5 flex-shrink-0" />
                          <span>{issue}</span>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Access Results by User Type */}
                  <div className="mt-2 grid grid-cols-2 gap-1 text-xs">
                    {Object.entries(result.results).map(([userType, accessResult]) => (
                      <div key={userType} className="flex items-center gap-1">
                        {getAccessIcon(accessResult.hasAccess)}
                        <span className="text-gray-600 dark:text-gray-400 capitalize">
                          {userType.replace('_', ' ')}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-3">
          <h4 className="font-medium text-green-900 dark:text-green-100 mb-2">
            Quick Actions
          </h4>
          <div className="space-y-2 text-sm">
            <button
              onClick={() => {
                const report = validateRoleBasedAccessControl();
                console.log('🔍 Role Access Control Report:', report);
              }}
              className="w-full text-left px-2 py-1 text-green-800 dark:text-green-200 hover:bg-green-100 dark:hover:bg-green-800 rounded"
            >
              📊 Log Full Report to Console
            </button>
            <button
              onClick={() => {
                const score = getRoleAccessHealthScore();
                console.log('🏥 Health Score:', score);
              }}
              className="w-full text-left px-2 py-1 text-green-800 dark:text-green-200 hover:bg-green-100 dark:hover:bg-green-800 rounded"
            >
              🏥 Log Health Score to Console
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RoleAccessControlTester;
