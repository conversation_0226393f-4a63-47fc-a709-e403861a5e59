/**
 * Template Analytics Dashboard
 * Comprehensive analytics and insights for template performance
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  BarChart3,
  TrendingUp,
  Users,
  Clock,
  Star,
  Target,
  Award,
  Eye,
  Download,
  ThumbsUp,
  Activity,
  Pie<PERSON>hart,
  Line<PERSON>hart,
  Calendar,
  Filter,
  RefreshCw
} from 'lucide-react';
import { RTLText, RTLFlex } from '../common';

interface AnalyticsData {
  overview: {
    totalTemplates: number;
    totalUsage: number;
    averageRating: number;
    completionRate: number;
  };
  topTemplates: Array<{
    id: string;
    name: string;
    usage: number;
    rating: number;
    completionRate: number;
    category: string;
  }>;
  usageTrends: Array<{
    date: string;
    views: number;
    selections: number;
    completions: number;
  }>;
  categoryPerformance: Array<{
    category: string;
    templates: number;
    usage: number;
    avgRating: number;
  }>;
  userInsights: {
    newUsers: number;
    returningUsers: number;
    averageSessionTime: string;
    bounceRate: number;
  };
}

interface TemplateAnalyticsDashboardProps {
  timeRange?: string;
  templateId?: string;
}

const TemplateAnalyticsDashboard: React.FC<TemplateAnalyticsDashboardProps> = ({ timeRange = '30d',
  templateId
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedTimeRange, setSelectedTimeRange] = useState(timeRange);
  const [selectedMetric, setSelectedMetric] = useState('usage');
  const [refreshing, setRefreshing] = useState(false);

  // Load analytics data from API
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadAnalyticsData();
  }, [selectedTimeRange, templateId]);

  const loadAnalyticsData = async () => {
    setLoading(true);
    try {
      // Load real analytics data from API
      const { templateAnalyticsAPI } = await import('../../services/templateAnalyticsApi');
      const analyticsData = await templateAnalyticsAPI.getDashboardData(selectedTimeRange, templateId);
      setAnalyticsData(analyticsData);
    } catch (error) {
      console.error('Error loading analytics data:', error);
      setError('Failed to load analytics data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const refreshData = async () => {
    setRefreshing(true);
    await loadAnalyticsData();
    setRefreshing(false);
  };

  const timeRangeOptions = [
    { value: '7d', label: t('analytics.timeRange.7days') },
    { value: '30d', label: t('analytics.timeRange.30days') },
    { value: '90d', label: t('analytics.timeRange.90days') },
    { value: '1y', label: t('analytics.timeRange.1year') }
  ];

  const metricOptions = [
    { value: 'usage', label: t('analytics.metrics.usage'), icon: Eye },
    { value: 'rating', label: t('analytics.metrics.rating'), icon: Star },
    { value: 'completion', label: t('analytics.metrics.completion'), icon: Target },
    { value: 'trends', label: t('analytics.metrics.trends'), icon: TrendingUp }
  ];

  if (loading) {
    return (
      <div className={`flex items-center justify-center h-64 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="text-center py-12">
        <BarChart3 size={48} className="text-gray-600 mx-auto mb-4" />
        <RTLText as="h3" className="text-xl font-semibold text-gray-400 mb-2">
          {t('analytics.noData')}
        </RTLText>
        <p className="text-gray-500">{t('analytics.noDataDescription')}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <RTLFlex className="items-center justify-between">
        <RTLText as="h2" className="text-2xl font-bold">
          {t('analytics.title')}
        </RTLText>

        <RTLFlex className="items-center space-x-4">
          {/* Time Range Selector */}
          <select
            value={selectedTimeRange}
            onChange={(e) => setSelectedTimeRange(e.target.value)}
            className="px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white focus:ring-2 focus:ring-purple-500"
          >
            {timeRangeOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>

          {/* Refresh Button */}
          <button
            onClick={refreshData}
            disabled={refreshing}
            className="p-2 bg-gray-800 hover:bg-gray-700 rounded-md text-gray-300 hover:text-white transition-colors"
          >
            <RefreshCw size={16} className={refreshing ? 'animate-spin' : ''} />
          </button>
        </RTLFlex>
      </RTLFlex>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-gray-800/50 rounded-lg p-6">
          <RTLFlex className="items-center justify-between mb-2">
            <div className="p-2 bg-blue-600/20 rounded-lg">
              <BarChart3 size={20} className="text-blue-400" />
            </div>
            <span className="text-2xl font-bold text-blue-400">
              {analyticsData.overview.totalTemplates}
            </span>
          </RTLFlex>
          <RTLText className="text-sm text-gray-400">
            {t('analytics.overview.totalTemplates')}
          </RTLText>
        </div>

        <div className="bg-gray-800/50 rounded-lg p-6">
          <RTLFlex className="items-center justify-between mb-2">
            <div className="p-2 bg-green-600/20 rounded-lg">
              <Users size={20} className="text-green-400" />
            </div>
            <span className="text-2xl font-bold text-green-400">
              {analyticsData.overview.totalUsage.toLocaleString()}
            </span>
          </RTLFlex>
          <RTLText className="text-sm text-gray-400">
            {t('analytics.overview.totalUsage')}
          </RTLText>
        </div>

        <div className="bg-gray-800/50 rounded-lg p-6">
          <RTLFlex className="items-center justify-between mb-2">
            <div className="p-2 bg-yellow-600/20 rounded-lg">
              <Star size={20} className="text-yellow-400" />
            </div>
            <span className="text-2xl font-bold text-yellow-400">
              {analyticsData.overview.averageRating.toFixed(1)}
            </span>
          </RTLFlex>
          <RTLText className="text-sm text-gray-400">
            {t('analytics.overview.averageRating')}
          </RTLText>
        </div>

        <div className="bg-gray-800/50 rounded-lg p-6">
          <RTLFlex className="items-center justify-between mb-2">
            <div className="p-2 bg-purple-600/20 rounded-lg">
              <Target size={20} className="text-purple-400" />
            </div>
            <span className="text-2xl font-bold text-purple-400">
              {analyticsData.overview.completionRate.toFixed(1)}%
            </span>
          </RTLFlex>
          <RTLText className="text-sm text-gray-400">
            {t('analytics.overview.completionRate')}
          </RTLText>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Templates */}
        <div className="bg-gray-800/50 rounded-lg p-6">
          <RTLText as="h3" className="text-lg font-semibold mb-4">
            {t('analytics.topTemplates')}
          </RTLText>
          <div className="space-y-4">
            {analyticsData.topTemplates.map((template, index) => (
              <div key={template.id} className={`flex items-center justify-between p-3 bg-gray-700/50 rounded-lg ${isRTL ? "flex-row-reverse" : ""}`}>
                <RTLFlex className="items-center">
                  <div className={`w-8 h-8 bg-purple-600/20 rounded-lg flex items-center justify-center mr-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <span className="text-sm font-bold text-purple-400">#{index + 1}</span>
                  </div>
                  <div>
                    <RTLText className="font-medium">{template.name}</RTLText>
                    <div className="text-sm text-gray-400">{template.category}</div>
                  </div>
                </RTLFlex>
                <div className="text-right">
                  <div className="text-sm font-medium">{template.usage} {t('analytics.uses')}</div>
                  <RTLFlex className="items-center text-xs text-gray-400">
                    <Star size={12} className={`text-yellow-400 mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                    {template.rating.toFixed(1)}
                    <span className="mx-2">•</span>
                    {template.completionRate.toFixed(1)}% {t('analytics.completed')}
                  </RTLFlex>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Category Performance */}
        <div className="bg-gray-800/50 rounded-lg p-6">
          <RTLText as="h3" className="text-lg font-semibold mb-4">
            {t('analytics.categoryPerformance')}
          </RTLText>
          <div className="space-y-4">
            {analyticsData.categoryPerformance.map((category) => (
              <div key={category.category} className="space-y-2">
                <RTLFlex className="items-center justify-between">
                  <RTLText className="font-medium">{category.category}</RTLText>
                  <div className="text-sm text-gray-400">
                    {category.usage} {t('analytics.uses')}
                  </div>
                </RTLFlex>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div
                    className="bg-purple-600 h-2 rounded-full"
                    style={{ width: `${(category.usage / Math.max(...analyticsData.categoryPerformance.map(c => c.usage))) * 100}%` }}
                  />
                </div>
                <RTLFlex className="items-center justify-between text-xs text-gray-400">
                  <span>{category.templates} {t('analytics.templates')}</span>
                  <RTLFlex className="items-center">
                    <Star size={12} className={`text-yellow-400 mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                    {category.avgRating.toFixed(1)}
                  </RTLFlex>
                </RTLFlex>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Usage Trends */}
      <div className="bg-gray-800/50 rounded-lg p-6">
        <RTLText as="h3" className="text-lg font-semibold mb-4">
          {t('analytics.usageTrends')}
        </RTLText>

        {/* Metric Selector */}
        <div className={`flex space-x-2 mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
          {metricOptions.map(option => {
            const Icon = option.icon;
            return (
              <button
                key={option.value}
                onClick={() => setSelectedMetric(option.value)}
                className={`flex items-center px-3 py-2 rounded-lg text-sm transition-colors ${
                  selectedMetric === option.value
                    ? 'bg-purple-600 text-white'
                    : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50'}
                }`}
              >
                <Icon size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                {option.label}
              </button>
            );
          })}
        </div>

        {/* Simple Chart Visualization */}
        <div className={`h-64 flex items-end space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
          {analyticsData.usageTrends.map((data, index) => {
            const value = selectedMetric === 'usage' ? data.views :
                         selectedMetric === 'rating' ? data.selections :
                         data.completions;
            const maxValue = Math.max(...analyticsData.usageTrends.map(d =>
              selectedMetric === 'usage' ? d.views :
              selectedMetric === 'rating' ? d.selections :
              d.completions
            ));
            const height = (value / maxValue) * 100;

            return (
              <div key={index} className={`flex-1 flex flex-col items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <div
                  className="w-full bg-purple-600 rounded-t transition-all duration-300 hover:bg-purple-500"
                  style={{ height: `${height}%` }}
                  title={`${new Date(data.date).toLocaleDateString()}: ${value}`}
                />
                <div className="text-xs text-gray-400 mt-2">
                  {new Date(data.date).getDate()}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* User Insights */}
      <div className="bg-gray-800/50 rounded-lg p-6">
        <RTLText as="h3" className="text-lg font-semibold mb-4">
          {t('analytics.userInsights')}
        </RTLText>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-400 mb-1">
              {analyticsData.userInsights.newUsers.toLocaleString()}
            </div>
            <div className="text-sm text-gray-400">{t('analytics.newUsers')}</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-400 mb-1">
              {analyticsData.userInsights.returningUsers.toLocaleString()}
            </div>
            <div className="text-sm text-gray-400">{t('analytics.returningUsers')}</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-400 mb-1">
              {analyticsData.userInsights.averageSessionTime}
            </div>
            <div className="text-sm text-gray-400">{t('analytics.avgSessionTime')}</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-400 mb-1">
              {analyticsData.userInsights.bounceRate.toFixed(1)}%
            </div>
            <div className="text-sm text-gray-400">{t('analytics.bounceRate')}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TemplateAnalyticsDashboard;
