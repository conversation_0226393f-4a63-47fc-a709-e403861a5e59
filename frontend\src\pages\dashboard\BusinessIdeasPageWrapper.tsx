import React, { Suspense, useState, useEffect } from 'react';
import { LoadingFallback } from '../../components/ui';
import BusinessIncubator from '../../components/incubator/BusinessIncubator';

// Try to load the BusinessIdeasPage, fallback to BusinessIncubator if it fails
const BusinessIdeasPageLazy = React.lazy(async () => {
  try {
    const module = await import('./BusinessIdeasPage');
    return module;
  } catch (error) {
    console.warn('Failed to load BusinessIdeasPage, using BusinessIncubator fallback:', error);
    // Return a wrapper around BusinessIncubator that matches the expected interface
    return {
      default: () => <BusinessIncubator />
    };
  }
});

/**
 * Wrapper component for BusinessIdeasPage that handles loading errors gracefully
 */
const BusinessIdeasPageWrapper: React.FC = () => {
  const [hasError, setHasError] = useState(false);

  // Error boundary for the lazy component
  const handleError = (error: Error) => {
    console.error('BusinessIdeasPage failed to render:', error);
    setHasError(true);
  };

  if (hasError) {
    return <BusinessIncubator />;
  }

  return (
    <Suspense fallback={<LoadingFallback message="Loading Business Ideas..." />}>
      <ErrorBoundary onError={handleError}>
        <BusinessIdeasPageLazy />
      </ErrorBoundary>
    </Suspense>
  );
};

// Simple error boundary component
class ErrorBoundary extends React.Component<
  { children: React.ReactNode; onError: (error: Error) => void },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode; onError: (error: Error) => void }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.props.onError(error);
  }

  render() {
    if (this.state.hasError) {
      return <BusinessIncubator />;
    }

    return this.props.children;
  }
}

export default BusinessIdeasPageWrapper;
