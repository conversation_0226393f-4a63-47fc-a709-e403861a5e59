import React from 'react';
import { LucideIcon } from 'lucide-react';


import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../hooks/useLanguage';

interface ActivityStatCardProps {
  title: string;
  value: number;
  icon: React.ReactNode;
  color: string;
}

const ActivityStatCard: React.FC<ActivityStatCardProps> = ({ title,
  value,
  icon,
  color
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const getDarkBgColor = () => {
    switch (color) {
      case 'indigo':
        return 'bg-indigo-900/30 border-indigo-800/50';
      case 'purple':
        return 'bg-purple-900/30 border-purple-800/50';
      case 'blue':
        return 'bg-blue-900/30 border-blue-800/50';
      case 'green':
        return 'bg-green-900/30 border-green-800/50';
      case 'yellow':
        return 'bg-yellow-900/30 border-yellow-800/50';
      default:
        return 'bg-indigo-900/30 border-indigo-800/50';
    }
  };

  const getLightBgColor = () => {
    switch (color) {
      case 'indigo':
        return 'bg-indigo-100 border-indigo-200';
      case 'purple':
        return 'bg-purple-100 border-purple-200';
      case 'blue':
        return 'bg-blue-100 border-blue-200';
      case 'green':
        return 'bg-green-100 border-green-200';
      case 'yellow':
        return 'bg-yellow-100 border-yellow-200';
      default:
        return 'bg-indigo-100 border-indigo-200';
    }
  };

  const getIconBgColor = () => {
    switch (color) {
      case 'indigo':
        return 'bg-indigo-800/50';
      case 'purple':
        return 'bg-purple-800/50';
      case 'blue':
        return 'bg-blue-800/50';
      case 'green':
        return 'bg-green-800/50';
      case 'yellow':
        return 'bg-yellow-800/50';
      default:
        return 'bg-indigo-800/50';
    }
  };

  const getLightIconBgColor = () => {
    switch (color) {
      case 'indigo':
        return 'bg-indigo-200';
      case 'purple':
        return 'bg-purple-200';
      case 'blue':
        return 'bg-blue-200';
      case 'green':
        return 'bg-green-200';
      case 'yellow':
        return 'bg-yellow-200';
      default:
        return 'bg-indigo-200';
    }
  };

  return (
    <div className="glass-light rounded-lg p-4 border-indigo-500/30">
      <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
        <div>
          <div className="text-sm text-glass-secondary">
            {title}
          </div>
          <h3 className="text-2xl font-bold text-glass-primary">{value}</h3>
        </div>
        <div className="p-3 rounded-full glass-light border-indigo-500/20">
          {icon}
        </div>
      </div>
    </div>
  );
};

export default ActivityStatCard;
