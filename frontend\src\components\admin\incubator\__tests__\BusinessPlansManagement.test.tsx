import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter } from 'react-router-dom';
import { I18nextProvider } from 'react-i18next';
import i18n from '../../../../i18n/config';
import BusinessPlansManagement from '../BusinessPlansManagement';

// Mock the hooks
jest.mock('../../../../hooks/useBusinessPlans', () => ({
  useBusinessPlansList: jest.fn(),
  useCreateBusinessPlan: jest.fn(),
  useUpdateBusinessPlan: jest.fn(),
  useDeleteBusinessPlan: jest.fn(),
  useGenerateBusinessPlanFeedback: jest.fn(),
  useDuplicateBusinessPlan: jest.fn(),
}));

// Mock the dashboard layout
jest.mock('../../dashboard', () => ({
  DashboardLayout: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="dashboard-layout">{children}</div>
  ),
}));

// Mock the common components
jest.mock('../../common', () => ({
  AdvancedFilter: ({ onChange }: { onChange: (filters: any) => void }) => (
    <div data-testid="advanced-filter">
      <button onClick={() => onChange({ status: 'draft' })}>Filter</button>
    </div>
  ),
  BulkActions: ({ selectedCount, onClearSelection }: { selectedCount: number; onClearSelection: () => void }) => (
    <div data-testid="bulk-actions">
      <span>{selectedCount} selected</span>
      <button onClick={onClearSelection}>Clear</button>
    </div>
  ),
}));

// Mock the modals
jest.mock('../components/BusinessPlanModal', () => {
  return function BusinessPlanModal({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) {
    return isOpen ? (
      <div data-testid="business-plan-modal">
        <button onClick={onClose}>Close Modal</button>
      </div>
    ) : null;
  };
});

jest.mock('../components/BusinessPlanDeleteModal', () => {
  return function BusinessPlanDeleteModal({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) {
    return isOpen ? (
      <div data-testid="business-plan-delete-modal">
        <button onClick={onClose}>Close Delete Modal</button>
      </div>
    ) : null;
  };
});

// Mock data
const mockBusinessPlan = {
  id: 1,
  title: 'Test Business Plan',
  business_idea: 1,
  business_idea_title: 'Test Business Idea',
  status: 'draft',
  content: {
    executive_summary: 'Test summary',
    market_analysis: 'Test analysis',
    competitive_analysis: '',
    marketing_strategy: '',
    operations_plan: '',
    management_team: '',
    financial_projections: '',
    funding_request: '',
    risk_analysis: ''
  },
  owner_details: {
    first_name: 'John',
    last_name: 'Doe'
  },
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
};

const mockBusinessPlansData = {
  results: [mockBusinessPlan],
  count: 1,
  next: null,
  previous: null
};

// Test wrapper
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <I18nextProvider i18n={i18n}>
          {children}
        </I18nextProvider>
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('BusinessPlansManagement', () => {
  const mockHooks = {
    useBusinessPlansList: require('../../../../hooks/useBusinessPlans').useBusinessPlansList,
    useCreateBusinessPlan: require('../../../../hooks/useBusinessPlans').useCreateBusinessPlan,
    useUpdateBusinessPlan: require('../../../../hooks/useBusinessPlans').useUpdateBusinessPlan,
    useDeleteBusinessPlan: require('../../../../hooks/useBusinessPlans').useDeleteBusinessPlan,
    useGenerateBusinessPlanFeedback: require('../../../../hooks/useBusinessPlans').useGenerateBusinessPlanFeedback,
    useDuplicateBusinessPlan: require('../../../../hooks/useBusinessPlans').useDuplicateBusinessPlan,
  };

  beforeEach(() => {
    // Reset all mocks
    Object.values(mockHooks).forEach(mock => mock.mockReset());

    // Default mock implementations
    mockHooks.useBusinessPlansList.mockReturnValue({
      data: mockBusinessPlansData,
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });

    mockHooks.useCreateBusinessPlan.mockReturnValue({
      mutateAsync: jest.fn(),
      isPending: false,
    });

    mockHooks.useUpdateBusinessPlan.mockReturnValue({
      mutateAsync: jest.fn(),
      isPending: false,
    });

    mockHooks.useDeleteBusinessPlan.mockReturnValue({
      mutateAsync: jest.fn(),
      isPending: false,
    });

    mockHooks.useGenerateBusinessPlanFeedback.mockReturnValue({
      mutateAsync: jest.fn(),
      isPending: false,
    });

    mockHooks.useDuplicateBusinessPlan.mockReturnValue({
      mutateAsync: jest.fn(),
      isPending: false,
    });
  });

  it('renders business plans management page', () => {
    render(<BusinessPlansManagement />, { wrapper: createWrapper() });

    expect(screen.getByText('Business Plans Management')).toBeInTheDocument();
    expect(screen.getByText('View and manage business plans')).toBeInTheDocument();
    expect(screen.getByText('Create Plan')).toBeInTheDocument();
  });

  it('displays business plans in table', () => {
    render(<BusinessPlansManagement />, { wrapper: createWrapper() });

    expect(screen.getByText('Test Business Plan')).toBeInTheDocument();
    expect(screen.getByText('Test Business Idea')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
  });

  it('shows loading state', () => {
    mockHooks.useBusinessPlansList.mockReturnValue({
      data: null,
      isLoading: true,
      error: null,
      refetch: jest.fn(),
    });

    render(<BusinessPlansManagement />, { wrapper: createWrapper() });

    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('shows error state', () => {
    mockHooks.useBusinessPlansList.mockReturnValue({
      data: null,
      isLoading: false,
      error: new Error('Failed to load'),
      refetch: jest.fn(),
    });

    render(<BusinessPlansManagement />, { wrapper: createWrapper() });

    expect(screen.getByText('Failed to load data. Please try again.')).toBeInTheDocument();
  });

  it('shows empty state when no plans exist', () => {
    mockHooks.useBusinessPlansList.mockReturnValue({
      data: { results: [], count: 0, next: null, previous: null },
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });

    render(<BusinessPlansManagement />, { wrapper: createWrapper() });

    expect(screen.getByText('No business plans found')).toBeInTheDocument();
  });

  it('opens create modal when create button is clicked', () => {
    render(<BusinessPlansManagement />, { wrapper: createWrapper() });

    const createButton = screen.getByText('Create Plan');
    fireEvent.click(createButton);

    expect(screen.getByTestId('business-plan-modal')).toBeInTheDocument();
  });

  it('opens edit modal when edit button is clicked', () => {
    render(<BusinessPlansManagement />, { wrapper: createWrapper() });

    const editButtons = screen.getAllByRole('button');
    const editButton = editButtons.find(button => 
      button.querySelector('svg') && button.getAttribute('class')?.includes('secondary')
    );
    
    if (editButton) {
      fireEvent.click(editButton);
      expect(screen.getByTestId('business-plan-modal')).toBeInTheDocument();
    }
  });

  it('opens delete modal when delete button is clicked', () => {
    render(<BusinessPlansManagement />, { wrapper: createWrapper() });

    const deleteButtons = screen.getAllByRole('button');
    const deleteButton = deleteButtons.find(button => 
      button.querySelector('svg') && button.getAttribute('class')?.includes('danger')
    );
    
    if (deleteButton) {
      fireEvent.click(deleteButton);
      expect(screen.getByTestId('business-plan-delete-modal')).toBeInTheDocument();
    }
  });

  it('handles search input', () => {
    render(<BusinessPlansManagement />, { wrapper: createWrapper() });

    const searchInput = screen.getByPlaceholderText('Search business plans...');
    fireEvent.change(searchInput, { target: { value: 'test search' } });

    expect(searchInput).toHaveValue('test search');
  });

  it('handles filter changes', () => {
    render(<BusinessPlansManagement />, { wrapper: createWrapper() });

    const filterButton = screen.getByText('Filter');
    fireEvent.click(filterButton);

    // The filter change should be handled by the component
    expect(screen.getByTestId('advanced-filter')).toBeInTheDocument();
  });

  it('handles row selection', () => {
    render(<BusinessPlansManagement />, { wrapper: createWrapper() });

    const checkboxes = screen.getAllByRole('checkbox');
    const rowCheckbox = checkboxes[1]; // First checkbox is select all, second is first row

    fireEvent.click(rowCheckbox);

    expect(screen.getByTestId('bulk-actions')).toBeInTheDocument();
    expect(screen.getByText('1 selected')).toBeInTheDocument();
  });

  it('handles select all functionality', () => {
    render(<BusinessPlansManagement />, { wrapper: createWrapper() });

    const checkboxes = screen.getAllByRole('checkbox');
    const selectAllCheckbox = checkboxes[0];

    fireEvent.click(selectAllCheckbox);

    expect(screen.getByTestId('bulk-actions')).toBeInTheDocument();
    expect(screen.getByText('1 selected')).toBeInTheDocument();
  });

  it('clears selection when clear button is clicked', () => {
    render(<BusinessPlansManagement />, { wrapper: createWrapper() });

    // Select a row first
    const checkboxes = screen.getAllByRole('checkbox');
    const rowCheckbox = checkboxes[1];
    fireEvent.click(rowCheckbox);

    // Clear selection
    const clearButton = screen.getByText('Clear');
    fireEvent.click(clearButton);

    expect(screen.queryByTestId('bulk-actions')).not.toBeInTheDocument();
  });

  it('calls refetch when refresh button is clicked', () => {
    const mockRefetch = jest.fn();
    mockHooks.useBusinessPlansList.mockReturnValue({
      data: mockBusinessPlansData,
      isLoading: false,
      error: null,
      refetch: mockRefetch,
    });

    render(<BusinessPlansManagement />, { wrapper: createWrapper() });

    const refreshButton = screen.getByText('Refresh');
    fireEvent.click(refreshButton);

    expect(mockRefetch).toHaveBeenCalled();
  });

  it('calculates completion percentage correctly', () => {
    const planWithContent = {
      ...mockBusinessPlan,
      content: {
        executive_summary: 'Summary',
        market_analysis: 'Analysis',
        competitive_analysis: 'Competitive',
        marketing_strategy: '',
        operations_plan: '',
        management_team: '',
        financial_projections: '',
        funding_request: '',
        risk_analysis: ''
      }
    };

    mockHooks.useBusinessPlansList.mockReturnValue({
      data: { results: [planWithContent], count: 1, next: null, previous: null },
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });

    render(<BusinessPlansManagement />, { wrapper: createWrapper() });

    // Should show 33% completion (3 out of 9 sections completed)
    expect(screen.getByText('33%')).toBeInTheDocument();
  });

  it('displays correct status badges', () => {
    const plans = [
      { ...mockBusinessPlan, id: 1, status: 'draft' },
      { ...mockBusinessPlan, id: 2, status: 'in_progress' },
      { ...mockBusinessPlan, id: 3, status: 'completed' },
      { ...mockBusinessPlan, id: 4, status: 'archived' }
    ];

    mockHooks.useBusinessPlansList.mockReturnValue({
      data: { results: plans, count: 4, next: null, previous: null },
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });

    render(<BusinessPlansManagement />, { wrapper: createWrapper() });

    expect(screen.getAllByText('Draft')).toHaveLength(1);
    expect(screen.getAllByText('In Progress')).toHaveLength(1);
    expect(screen.getAllByText('Completed')).toHaveLength(1);
    expect(screen.getAllByText('Archived')).toHaveLength(1);
  });
});
