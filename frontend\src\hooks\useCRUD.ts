import { useState, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

interface CRUDOperations<T> {
  create: (data: Partial<T>) => Promise<T>;
  read: (id?: number) => Promise<T | T[]>;
  update: (id: number, data: Partial<T>) => Promise<T>;
  delete: (id: number) => Promise<void>;
  bulkDelete?: (ids: number[]) => Promise<void>;
  bulkUpdate?: (ids: number[], data: Partial<T>) => Promise<T[]>;
  search?: (query: string, filters?: Record<string, any>) => Promise<T[]>;
  duplicate?: (id: number) => Promise<T>;
  archive?: (id: number) => Promise<T>;
  restore?: (id: number) => Promise<T>;
  export?: (ids?: number[]) => Promise<Blob>;
  import?: (file: File) => Promise<T[]>;
}

interface UseCRUDOptions<T> {
  onSuccess?: (operation: 'create' | 'update' | 'delete' | 'bulkDelete' | 'bulkUpdate', data?: any) => void;
  onError?: (operation: 'create' | 'read' | 'update' | 'delete' | 'bulkDelete' | 'bulkUpdate' | 'search', error: any) => void;
  enableOptimisticUpdates?: boolean;
  enableCache?: boolean;
  cacheTimeout?: number;
  retryAttempts?: number;
  retryDelay?: number;
  validation?: (data: Partial<T>) => string[] | null;
}

interface PaginationOptions {
  page: number;
  pageSize: number;
  totalItems: number;
  totalPages: number;
}

interface FilterOptions {
  searchQuery: string;
  filters: Record<string, any>;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

interface UseCRUDReturn<T> {
  // State
  data: T[];
  selectedItem: T | null;
  selectedItems: T[];
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  error: string | null;

  // Operations
  createItem: (data: Partial<T>) => Promise<boolean>;
  readItems: () => Promise<void>;
  readItem: (id: number) => Promise<void>;
  updateItem: (id: number, data: Partial<T>) => Promise<boolean>;
  deleteItem: (id: number) => Promise<boolean>;
  bulkDeleteItems: (ids: number[]) => Promise<boolean>;
  bulkUpdateItems: (ids: number[], data: Partial<T>) => Promise<boolean>;
  searchItems: (query: string, filters?: Record<string, any>) => Promise<void>;
  duplicateItem: (id: number) => Promise<boolean>;
  archiveItem: (id: number) => Promise<boolean>;
  restoreItem: (id: number) => Promise<boolean>;
  exportItems: (ids?: number[]) => Promise<boolean>;
  importItems: (file: File) => Promise<boolean>;

  // UI State Management
  setSelectedItem: (item: T | null) => void;
  setSelectedItems: (items: T[]) => void;
  toggleItemSelection: (item: T) => void;
  selectAllItems: () => void;
  clearSelection: () => void;
  clearError: () => void;
  setData: (data: T[]) => void;

  // Pagination
  pagination: PaginationOptions;
  setPagination: (options: Partial<PaginationOptions>) => void;
  goToPage: (page: number) => void;
  changePageSize: (pageSize: number) => void;

  // Filtering and Sorting
  filters: FilterOptions;
  setFilters: (filters: Partial<FilterOptions>) => void;
  clearFilters: () => void;

  // Computed values
  filteredData: T[];
  paginatedData: T[];
  hasSelection: boolean;
  isAllSelected: boolean;

  // Validation
  validateItem: (data: Partial<T>) => string[] | null;
}

export function useCRUD<T extends { id: number }>(
  operations: CRUDOperations<T>,
  options: UseCRUDOptions<T> = {}
): UseCRUDReturn<T> {
  const { t } = useTranslation();

  // State
  const [data, setData] = useState<T[]>([]);
  const [selectedItem, setSelectedItem] = useState<T | null>(null);
  const [selectedItems, setSelectedItems] = useState<T[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Pagination state
  const [pagination, setPaginationState] = useState<PaginationOptions>({
    page: 1,
    pageSize: 10,
    totalItems: 0,
    totalPages: 0
  });

  // Filter state
  const [filters, setFiltersState] = useState<FilterOptions>({
    searchQuery: '',
    filters: {},
    sortBy: '',
    sortOrder: 'asc'
  });

  const {
    onSuccess,
    onError,
    enableOptimisticUpdates = false,
    enableCache = false,
    cacheTimeout = 5 * 60 * 1000, // 5 minutes
    retryAttempts = 3,
    retryDelay = 1000,
    validation
  } = options;

  // Computed values
  const filteredData = useMemo(() => {
    let result = [...data];

    // Apply search filter
    if (filters.searchQuery) {
      const query = filters.searchQuery.toLowerCase();
      result = result.filter(item =>
        Object.values(item).some(value =>
          String(value).toLowerCase().includes(query)
        )
      );
    }

    // Apply custom filters
    Object.entries(filters.filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        result = result.filter(item => {
          const itemValue = (item as any)[key];
          if (Array.isArray(value)) {
            return value.includes(itemValue);
          }
          return itemValue === value;
        });
      }
    });

    // Apply sorting
    if (filters.sortBy) {
      result.sort((a, b) => {
        const aValue = (a as any)[filters.sortBy];
        const bValue = (b as any)[filters.sortBy];

        if (aValue < bValue) return filters.sortOrder === 'asc' ? -1 : 1;
        if (aValue > bValue) return filters.sortOrder === 'asc' ? 1 : -1;
        return 0;
      });
    }

    return result;
  }, [data, filters]);

  const paginatedData = useMemo(() => {
    const startIndex = (pagination.page - 1) * pagination.pageSize;
    const endIndex = startIndex + pagination.pageSize;
    return filteredData.slice(startIndex, endIndex);
  }, [filteredData, pagination.page, pagination.pageSize]);

  const hasSelection = selectedItems.length > 0;
  const isAllSelected = selectedItems.length === filteredData.length && filteredData.length > 0;

  // Utility functions
  const handleError = useCallback((operation: 'create' | 'read' | 'update' | 'delete' | 'bulkDelete' | 'bulkUpdate' | 'search', err: any) => {
    const errorMessage = err instanceof Error ? err.message : t('crud.messages.error.network');
    setError(errorMessage);
    onError?.(operation, err);
  }, [onError, t]);

  const retryOperation = useCallback(async <R>(operation: () => Promise<R>): Promise<R> => {
    let lastError: any;

    for (let attempt = 0; attempt <= retryAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        if (attempt < retryAttempts) {
          await new Promise(resolve => setTimeout(resolve, retryDelay * Math.pow(2, attempt)));
        }
      }
    }

    throw lastError;
  }, [retryAttempts, retryDelay]);

  const validateItem = useCallback((data: Partial<T>): string[] | null => {
    if (validation) {
      return validation(data);
    }
    return null;
  }, [validation]);

  // CRUD Operations
  const createItem = useCallback(async (itemData: Partial<T>): Promise<boolean> => {
    // Validate data
    const validationErrors = validateItem(itemData);
    if (validationErrors && validationErrors.length > 0) {
      setError(validationErrors.join(', '));
      return false;
    }

    try {
      setIsCreating(true);
      setError(null);

      const newItem = await retryOperation(() => operations.create(itemData));

      if (enableOptimisticUpdates) {
        setData(prev => [newItem, ...prev]);
      } else {
        setData(prev => [newItem, ...prev]);
      }

      // Update pagination
      setPaginationState(prev => ({
        ...prev,
        totalItems: prev.totalItems + 1,
        totalPages: Math.ceil((prev.totalItems + 1) / prev.pageSize)
      }));

      onSuccess?.('create', newItem);
      return true;
    } catch (err) {
      handleError('create', err);
      return false;
    } finally {
      setIsCreating(false);
    }
  }, [operations, onSuccess, handleError, retryOperation, enableOptimisticUpdates, validateItem]);

  const readItems = useCallback(async (): Promise<void> => {
    try {
      setIsLoading(true);
      setError(null);

      const result = await retryOperation(() => operations.read());
      const items = Array.isArray(result) ? result : [result];
      setData(items);

      // Update pagination
      setPaginationState(prev => ({
        ...prev,
        totalItems: items.length,
        totalPages: Math.ceil(items.length / prev.pageSize)
      }));
    } catch (err) {
      handleError('read', err);
    } finally {
      setIsLoading(false);
    }
  }, [operations, handleError, retryOperation]);

  const readItem = useCallback(async (id: number): Promise<void> => {
    try {
      setIsLoading(true);
      setError(null);

      const item = await retryOperation(() => operations.read(id)) as T;
      setSelectedItem(item);
    } catch (err) {
      handleError('read', err);
    } finally {
      setIsLoading(false);
    }
  }, [operations, handleError, retryOperation]);

  const updateItem = useCallback(async (id: number, itemData: Partial<T>): Promise<boolean> => {
    // Validate data
    const validationErrors = validateItem(itemData);
    if (validationErrors && validationErrors.length > 0) {
      setError(validationErrors.join(', '));
      return false;
    }

    try {
      setIsUpdating(true);
      setError(null);

      const updatedItem = await retryOperation(() => operations.update(id, itemData));
      setData(prev => prev.map(item => item.id === id ? updatedItem : item));

      if (selectedItem?.id === id) {
        setSelectedItem(updatedItem);
      }

      onSuccess?.('update', updatedItem);
      return true;
    } catch (err) {
      handleError('update', err);
      return false;
    } finally {
      setIsUpdating(false);
    }
  }, [operations, selectedItem, onSuccess, handleError, retryOperation, validateItem]);

  const deleteItem = useCallback(async (id: number): Promise<boolean> => {
    try {
      setIsDeleting(true);
      setError(null);

      await retryOperation(() => operations.delete(id));
      setData(prev => prev.filter(item => item.id !== id));

      if (selectedItem?.id === id) {
        setSelectedItem(null);
      }

      // Remove from selection if selected
      setSelectedItems(prev => prev.filter(item => item.id !== id));

      // Update pagination
      setPaginationState(prev => ({
        ...prev,
        totalItems: Math.max(0, prev.totalItems - 1),
        totalPages: Math.ceil(Math.max(0, prev.totalItems - 1) / prev.pageSize)
      }));

      onSuccess?.('delete', { id });
      return true;
    } catch (err) {
      handleError('delete', err);
      return false;
    } finally {
      setIsDeleting(false);
    }
  }, [operations, selectedItem, onSuccess, handleError, retryOperation]);

  const bulkDeleteItems = useCallback(async (ids: number[]): Promise<boolean> => {
    if (!operations.bulkDelete) {
      // Fallback to individual deletes
      try {
        setIsDeleting(true);
        setError(null);

        for (const id of ids) {
          await retryOperation(() => operations.delete(id));
        }

        setData(prev => prev.filter(item => !ids.includes(item.id)));
        setSelectedItems(prev => prev.filter(item => !ids.includes(item.id)));

        if (selectedItem && ids.includes(selectedItem.id)) {
          setSelectedItem(null);
        }

        // Update pagination
        setPaginationState(prev => ({
          ...prev,
          totalItems: Math.max(0, prev.totalItems - ids.length),
          totalPages: Math.ceil(Math.max(0, prev.totalItems - ids.length) / prev.pageSize)
        }));

        onSuccess?.('bulkDelete', { ids });
        return true;
      } catch (err) {
        handleError('bulkDelete', err);
        return false;
      } finally {
        setIsDeleting(false);
      }
    }

    try {
      setIsDeleting(true);
      setError(null);

      await retryOperation(() => operations.bulkDelete!(ids));
      setData(prev => prev.filter(item => !ids.includes(item.id)));
      setSelectedItems(prev => prev.filter(item => !ids.includes(item.id)));

      if (selectedItem && ids.includes(selectedItem.id)) {
        setSelectedItem(null);
      }

      // Update pagination
      setPaginationState(prev => ({
        ...prev,
        totalItems: Math.max(0, prev.totalItems - ids.length),
        totalPages: Math.ceil(Math.max(0, prev.totalItems - ids.length) / prev.pageSize)
      }));

      onSuccess?.('bulkDelete', { ids });
      return true;
    } catch (err) {
      handleError('bulkDelete', err);
      return false;
    } finally {
      setIsDeleting(false);
    }
  }, [operations, selectedItem, onSuccess, handleError, retryOperation]);

  const bulkUpdateItems = useCallback(async (ids: number[], itemData: Partial<T>): Promise<boolean> => {
    // Validate data
    const validationErrors = validateItem(itemData);
    if (validationErrors && validationErrors.length > 0) {
      setError(validationErrors.join(', '));
      return false;
    }

    if (!operations.bulkUpdate) {
      // Fallback to individual updates
      try {
        setIsUpdating(true);
        setError(null);

        const updatedItems: T[] = [];
        for (const id of ids) {
          const updatedItem = await retryOperation(() => operations.update(id, itemData));
          updatedItems.push(updatedItem);
        }

        setData(prev => prev.map(item => {
          const updatedItem = updatedItems.find(updated => updated.id === item.id);
          return updatedItem || item;
        }));

        onSuccess?.('bulkUpdate', { ids, data: itemData });
        return true;
      } catch (err) {
        handleError('bulkUpdate', err);
        return false;
      } finally {
        setIsUpdating(false);
      }
    }

    try {
      setIsUpdating(true);
      setError(null);

      const updatedItems = await retryOperation(() => operations.bulkUpdate!(ids, itemData));
      setData(prev => prev.map(item => {
        const updatedItem = updatedItems.find(updated => updated.id === item.id);
        return updatedItem || item;
      }));

      onSuccess?.('bulkUpdate', { ids, data: itemData });
      return true;
    } catch (err) {
      handleError('bulkUpdate', err);
      return false;
    } finally {
      setIsUpdating(false);
    }
  }, [operations, onSuccess, handleError, retryOperation, validateItem]);

  const searchItems = useCallback(async (query: string, searchFilters?: Record<string, any>): Promise<void> => {
    if (!operations.search) {
      // Fallback to client-side search
      setFiltersState(prev => ({
        ...prev,
        searchQuery: query,
        filters: { ...prev.filters, ...searchFilters }
      }));
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const results = await retryOperation(() => operations.search!(query, searchFilters));
      setData(results);

      // Update pagination
      setPaginationState(prev => ({
        ...prev,
        totalItems: results.length,
        totalPages: Math.ceil(results.length / prev.pageSize),
        page: 1 // Reset to first page
      }));
    } catch (err) {
      handleError('search', err);
    } finally {
      setIsLoading(false);
    }
  }, [operations, handleError, retryOperation]);

  // UI State Management
  const toggleItemSelection = useCallback((item: T) => {
    setSelectedItems(prev => {
      const isSelected = prev.some(selected => selected.id === item.id);
      if (isSelected) {
        return prev.filter(selected => selected.id !== item.id);
      } else {
        return [...prev, item];
      }
    });
  }, []);

  const selectAllItems = useCallback(() => {
    setSelectedItems(filteredData);
  }, [filteredData]);

  const clearSelection = useCallback(() => {
    setSelectedItems([]);
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Pagination functions
  const setPagination = useCallback((options: Partial<PaginationOptions>) => {
    setPaginationState(prev => ({ ...prev, ...options }));
  }, []);

  const goToPage = useCallback((page: number) => {
    setPaginationState(prev => ({ ...prev, page }));
  }, []);

  const changePageSize = useCallback((pageSize: number) => {
    setPaginationState(prev => ({
      ...prev,
      pageSize,
      page: 1, // Reset to first page
      totalPages: Math.ceil(prev.totalItems / pageSize)
    }));
  }, []);

  // Filter functions
  const setFilters = useCallback((newFilters: Partial<FilterOptions>) => {
    setFiltersState(prev => ({ ...prev, ...newFilters }));
    // Reset to first page when filters change
    setPaginationState(prev => ({ ...prev, page: 1 }));
  }, []);

  const clearFilters = useCallback(() => {
    setFiltersState({
      searchQuery: '',
      filters: {},
      sortBy: '',
      sortOrder: 'asc'
    });
    setPaginationState(prev => ({ ...prev, page: 1 }));
  }, []);

  // Additional CRUD Operations
  const duplicateItem = useCallback(async (id: number): Promise<boolean> => {
    if (!operations.duplicate) {
      setError(t('crud.messages.error.notSupported'));
      return false;
    }

    try {
      setIsCreating(true);
      setError(null);

      const duplicatedItem = await retryOperation(() => operations.duplicate!(id));
      setData(prev => [duplicatedItem, ...prev]);

      // Update pagination
      setPaginationState(prev => ({
        ...prev,
        totalItems: prev.totalItems + 1,
        totalPages: Math.ceil((prev.totalItems + 1) / prev.pageSize)
      }));

      onSuccess?.('create', duplicatedItem);
      return true;
    } catch (err) {
      handleError('create', err);
      return false;
    } finally {
      setIsCreating(false);
    }
  }, [operations, onSuccess, handleError, retryOperation, t]);

  const archiveItem = useCallback(async (id: number): Promise<boolean> => {
    if (!operations.archive) {
      setError(t('crud.messages.error.notSupported'));
      return false;
    }

    try {
      setIsUpdating(true);
      setError(null);

      const archivedItem = await retryOperation(() => operations.archive!(id));
      setData(prev => prev.map(item => item.id === id ? archivedItem : item));

      if (selectedItem?.id === id) {
        setSelectedItem(archivedItem);
      }

      onSuccess?.('update', archivedItem);
      return true;
    } catch (err) {
      handleError('update', err);
      return false;
    } finally {
      setIsUpdating(false);
    }
  }, [operations, selectedItem, onSuccess, handleError, retryOperation, t]);

  const restoreItem = useCallback(async (id: number): Promise<boolean> => {
    if (!operations.restore) {
      setError(t('crud.messages.error.notSupported'));
      return false;
    }

    try {
      setIsUpdating(true);
      setError(null);

      const restoredItem = await retryOperation(() => operations.restore!(id));
      setData(prev => prev.map(item => item.id === id ? restoredItem : item));

      if (selectedItem?.id === id) {
        setSelectedItem(restoredItem);
      }

      onSuccess?.('update', restoredItem);
      return true;
    } catch (err) {
      handleError('update', err);
      return false;
    } finally {
      setIsUpdating(false);
    }
  }, [operations, selectedItem, onSuccess, handleError, retryOperation, t]);

  const exportItems = useCallback(async (ids?: number[]): Promise<boolean> => {
    if (!operations.export) {
      setError(t('crud.messages.error.notSupported'));
      return false;
    }

    try {
      setIsLoading(true);
      setError(null);

      const blob = await retryOperation(() => operations.export!(ids));

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `export-${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      return true;
    } catch (err) {
      handleError('read', err);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [operations, handleError, retryOperation, t]);

  const importItems = useCallback(async (file: File): Promise<boolean> => {
    if (!operations.import) {
      setError(t('crud.messages.error.notSupported'));
      return false;
    }

    try {
      setIsLoading(true);
      setError(null);

      const importedItems = await retryOperation(() => operations.import!(file));
      setData(prev => [...importedItems, ...prev]);

      // Update pagination
      setPaginationState(prev => ({
        ...prev,
        totalItems: prev.totalItems + importedItems.length,
        totalPages: Math.ceil((prev.totalItems + importedItems.length) / prev.pageSize)
      }));

      return true;
    } catch (err) {
      handleError('create', err);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [operations, handleError, retryOperation, t]);

  return {
    // State
    data,
    selectedItem,
    selectedItems,
    isLoading,
    isCreating,
    isUpdating,
    isDeleting,
    error,

    // Operations
    createItem,
    readItems,
    readItem,
    updateItem,
    deleteItem,
    bulkDeleteItems,
    bulkUpdateItems,
    searchItems,
    duplicateItem,
    archiveItem,
    restoreItem,
    exportItems,
    importItems,

    // UI State Management
    setSelectedItem,
    setSelectedItems,
    toggleItemSelection,
    selectAllItems,
    clearSelection,
    clearError,
    setData,

    // Pagination
    pagination,
    setPagination,
    goToPage,
    changePageSize,

    // Filtering and Sorting
    filters,
    setFilters,
    clearFilters,

    // Computed values
    filteredData,
    paginatedData,
    hasSelection,
    isAllSelected,

    // Validation
    validateItem
  };
}

export default useCRUD;
