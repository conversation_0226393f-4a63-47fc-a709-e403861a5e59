import React from 'react';

interface BadgeProps {
  children: React.ReactNode;
  variant?: 'default' | 'secondary' | 'destructive' | 'outline' | 'success' | 'warning';
  className?: string;
}

export const Badge: React.FC<BadgeProps> = ({
  children,
  variant = 'default',
  className = ''
}) => {
  const baseClasses = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-all duration-300';

  const variantClasses = {
    default: 'glass-light text-glass-primary border border-glass-border',
    secondary: 'glass-morphism text-glass-secondary border border-glass-border',
    destructive: 'glass-morphism text-white bg-red-600/20 hover:bg-red-600/30 border border-red-500/30',
    outline: 'border border-glass-border text-glass-primary hover:bg-glass-hover',
    success: 'glass-morphism text-white bg-green-600/20 hover:bg-green-600/30 border border-green-500/30',
    warning: 'glass-morphism text-white bg-amber-600/20 hover:bg-amber-600/30 border border-amber-500/30'
  };

  return (
    <span className={`${baseClasses} ${variantClasses[variant]} ${className}`}>
      {children}
    </span>
  );
};
