"""
Voice AI Engine
Advanced voice processing, speech recognition, and voice synthesis capabilities
"""

import logging
import io
import base64
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from django.core.files.uploadedfile import InMemoryUploadedFile

# Voice AI imports with fallbacks
try:
    import speech_recognition as sr
    SPEECH_RECOGNITION_AVAILABLE = True
except ImportError:
    SPEECH_RECOGNITION_AVAILABLE = False

try:
    from gtts import gTTS
    GTTS_AVAILABLE = True
except ImportError:
    GTTS_AVAILABLE = False

try:
    from pydub import AudioSegment
    PYDUB_AVAILABLE = True
except ImportError:
    PYDUB_AVAILABLE = False

try:
    import whisper
    WHISPER_AVAILABLE = True
except ImportError:
    WHISPER_AVAILABLE = False

logger = logging.getLogger(__name__)


class VoiceAIEngine:
    """
    Advanced voice AI engine for speech recognition, synthesis, and analysis
    """
    
    _instance = None
    
    def __init__(self):
        self.is_initialized = False
        self.voice_available = SPEECH_RECOGNITION_AVAILABLE or WHISPER_AVAILABLE
        self.recognizer = None
        self.whisper_model = None
        
        if self.voice_available:
            self._initialize_voice_engine()
    
    @classmethod
    def get_instance(cls):
        """Singleton pattern"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def _initialize_voice_engine(self):
        """Initialize voice AI engine"""
        try:
            # Initialize speech recognition
            if SPEECH_RECOGNITION_AVAILABLE:
                self.recognizer = sr.Recognizer()
                self.recognizer.energy_threshold = 300
                self.recognizer.dynamic_energy_threshold = True
            
            # Initialize Whisper model (more accurate)
            if WHISPER_AVAILABLE:
                self.whisper_model = whisper.load_model("base")
            
            self.is_initialized = True
            logger.info("✅ Voice AI Engine initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Voice AI Engine: {e}")
            self.is_initialized = False
    
    def transcribe_audio(self, audio_data: bytes, language: str = 'auto') -> Dict[str, Any]:
        """
        Transcribe audio to text with language detection
        """
        if not self.voice_available:
            return self._fallback_transcription()
        
        try:
            # Convert audio data to appropriate format
            audio_file = self._prepare_audio_data(audio_data)
            
            # Try Whisper first (more accurate)
            if WHISPER_AVAILABLE and self.whisper_model:
                result = self._transcribe_with_whisper(audio_file, language)
                if result['success']:
                    return result
            
            # Fallback to speech_recognition
            if SPEECH_RECOGNITION_AVAILABLE and self.recognizer:
                result = self._transcribe_with_sr(audio_file, language)
                if result['success']:
                    return result
            
            return self._fallback_transcription()
            
        except Exception as e:
            logger.error(f"Error in audio transcription: {e}")
            return self._fallback_transcription()
    
    def synthesize_speech(self, text: str, language: str = 'en', voice_style: str = 'neutral') -> Dict[str, Any]:
        """
        Convert text to speech
        """
        if not GTTS_AVAILABLE:
            return self._fallback_synthesis()
        
        try:
            # Determine language code for gTTS
            lang_code = self._get_gtts_language_code(language)
            
            # Generate speech
            tts = gTTS(text=text, lang=lang_code, slow=False)
            
            # Save to bytes buffer
            audio_buffer = io.BytesIO()
            tts.write_to_fp(audio_buffer)
            audio_buffer.seek(0)
            
            # Encode to base64 for transmission
            audio_base64 = base64.b64encode(audio_buffer.getvalue()).decode('utf-8')
            
            return {
                'success': True,
                'audio_data': audio_base64,
                'format': 'mp3',
                'language': language,
                'text_length': len(text),
                'estimated_duration': len(text) * 0.1,  # Rough estimate
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error in speech synthesis: {e}")
            return self._fallback_synthesis()
    
    def analyze_voice_sentiment(self, transcribed_text: str) -> Dict[str, Any]:
        """
        Analyze sentiment and emotion from transcribed voice
        """
        try:
            # Simple sentiment analysis based on keywords
            sentiment_score = self._calculate_sentiment_score(transcribed_text)
            emotion = self._detect_emotion(transcribed_text)
            confidence = self._calculate_confidence(transcribed_text)
            
            return {
                'sentiment': {
                    'score': sentiment_score,
                    'label': self._get_sentiment_label(sentiment_score),
                    'confidence': confidence
                },
                'emotion': emotion,
                'key_phrases': self._extract_key_phrases(transcribed_text),
                'speaking_patterns': self._analyze_speaking_patterns(transcribed_text),
                'recommendations': self._generate_voice_recommendations(sentiment_score, emotion),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error in voice sentiment analysis: {e}")
            return self._fallback_sentiment_analysis()
    
    def process_voice_command(self, transcribed_text: str) -> Dict[str, Any]:
        """
        Process voice commands for platform navigation
        """
        try:
            # Extract command intent
            intent = self._extract_command_intent(transcribed_text)
            
            # Extract parameters
            parameters = self._extract_command_parameters(transcribed_text, intent)
            
            # Generate response
            response = self._generate_command_response(intent, parameters)
            
            return {
                'intent': intent,
                'parameters': parameters,
                'response': response,
                'confidence': self._calculate_command_confidence(transcribed_text, intent),
                'executable': intent != 'unknown',
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error in voice command processing: {e}")
            return self._fallback_command_processing()
    
    def transcribe_meeting(self, audio_data: bytes, participants: List[str] = None) -> Dict[str, Any]:
        """
        Transcribe business meeting with speaker identification
        """
        try:
            # Basic transcription
            transcription_result = self.transcribe_audio(audio_data)
            
            if not transcription_result['success']:
                return transcription_result
            
            full_text = transcription_result['transcribed_text']
            
            # Analyze meeting content
            meeting_analysis = self._analyze_meeting_content(full_text)
            
            # Extract action items
            action_items = self._extract_action_items(full_text)
            
            # Generate meeting summary
            summary = self._generate_meeting_summary(full_text, meeting_analysis)
            
            return {
                'success': True,
                'transcription': full_text,
                'participants': participants or ['Speaker 1'],  # Simplified
                'meeting_analysis': meeting_analysis,
                'action_items': action_items,
                'summary': summary,
                'key_decisions': self._extract_key_decisions(full_text),
                'next_steps': self._extract_next_steps(full_text),
                'duration_estimate': transcription_result.get('duration', 0),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error in meeting transcription: {e}")
            return self._fallback_meeting_transcription()
    
    # Helper methods
    def _prepare_audio_data(self, audio_data: bytes) -> Any:
        """Prepare audio data for processing"""
        if PYDUB_AVAILABLE:
            try:
                # Try to load with pydub for format conversion
                audio = AudioSegment.from_file(io.BytesIO(audio_data))
                # Convert to wav for better compatibility
                wav_buffer = io.BytesIO()
                audio.export(wav_buffer, format="wav")
                wav_buffer.seek(0)
                return wav_buffer
            except:
                pass
        
        # Return raw data if pydub not available
        return io.BytesIO(audio_data)
    
    def _transcribe_with_whisper(self, audio_file: Any, language: str) -> Dict[str, Any]:
        """Transcribe using Whisper model"""
        try:
            # Save audio to temporary file for Whisper
            import tempfile
            import os
            
            with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as temp_file:
                temp_file.write(audio_file.read())
                temp_file_path = temp_file.name
            
            try:
                # Transcribe with Whisper
                result = self.whisper_model.transcribe(temp_file_path)
                
                return {
                    'success': True,
                    'transcribed_text': result['text'],
                    'language': result.get('language', language),
                    'confidence': 0.9,  # Whisper is generally high confidence
                    'method': 'whisper',
                    'timestamp': datetime.now().isoformat()
                }
            finally:
                # Clean up temporary file
                os.unlink(temp_file_path)
                
        except Exception as e:
            logger.error(f"Whisper transcription error: {e}")
            return {'success': False, 'error': str(e)}
    
    def _transcribe_with_sr(self, audio_file: Any, language: str) -> Dict[str, Any]:
        """Transcribe using speech_recognition library"""
        try:
            with sr.AudioFile(audio_file) as source:
                audio = self.recognizer.record(source)
            
            # Try Google Speech Recognition
            text = self.recognizer.recognize_google(audio, language=language if language != 'auto' else None)
            
            return {
                'success': True,
                'transcribed_text': text,
                'language': language,
                'confidence': 0.8,  # Estimated confidence
                'method': 'google_sr',
                'timestamp': datetime.now().isoformat()
            }
            
        except sr.UnknownValueError:
            return {'success': False, 'error': 'Could not understand audio'}
        except sr.RequestError as e:
            return {'success': False, 'error': f'Speech recognition service error: {e}'}
        except Exception as e:
            logger.error(f"Speech recognition error: {e}")
            return {'success': False, 'error': str(e)}

    def _get_gtts_language_code(self, language: str) -> str:
        """Get gTTS language code"""
        language_map = {
            'en': 'en',
            'ar': 'ar',
            'auto': 'en'
        }
        return language_map.get(language, 'en')

    def _calculate_sentiment_score(self, text: str) -> float:
        """Calculate sentiment score from text"""
        positive_words = ['good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic', 'love', 'like']
        negative_words = ['bad', 'terrible', 'awful', 'hate', 'dislike', 'horrible', 'worst', 'disappointing']

        words = text.lower().split()
        positive_count = sum(1 for word in words if word in positive_words)
        negative_count = sum(1 for word in words if word in negative_words)

        if positive_count + negative_count == 0:
            return 0.5  # Neutral

        return positive_count / (positive_count + negative_count)

    def _get_sentiment_label(self, score: float) -> str:
        """Get sentiment label from score"""
        if score > 0.6:
            return 'positive'
        elif score < 0.4:
            return 'negative'
        else:
            return 'neutral'

    def _detect_emotion(self, text: str) -> Dict[str, Any]:
        """Detect emotion from text"""
        # Simplified emotion detection
        emotions = {
            'joy': ['happy', 'excited', 'thrilled', 'delighted'],
            'anger': ['angry', 'frustrated', 'annoyed', 'furious'],
            'sadness': ['sad', 'disappointed', 'upset', 'depressed'],
            'fear': ['worried', 'anxious', 'scared', 'nervous'],
            'confidence': ['confident', 'sure', 'certain', 'determined']
        }

        words = text.lower().split()
        emotion_scores = {}

        for emotion, keywords in emotions.items():
            score = sum(1 for word in words if word in keywords)
            emotion_scores[emotion] = score

        dominant_emotion = max(emotion_scores, key=emotion_scores.get) if any(emotion_scores.values()) else 'neutral'

        return {
            'dominant': dominant_emotion,
            'scores': emotion_scores,
            'confidence': min(1.0, max(emotion_scores.values()) / len(words)) if words else 0.0
        }

    def _calculate_confidence(self, text: str) -> float:
        """Calculate confidence in analysis"""
        return min(1.0, len(text) / 50)  # Simple confidence based on text length

    def _extract_key_phrases(self, text: str) -> List[str]:
        """Extract key phrases from text"""
        # Simple key phrase extraction
        sentences = text.split('.')
        key_phrases = []

        for sentence in sentences[:3]:  # Take first 3 sentences
            sentence = sentence.strip()
            if len(sentence) > 10:
                key_phrases.append(sentence)

        return key_phrases

    def _analyze_speaking_patterns(self, text: str) -> Dict[str, Any]:
        """Analyze speaking patterns"""
        words = text.split()
        sentences = text.split('.')

        return {
            'word_count': len(words),
            'sentence_count': len(sentences),
            'avg_words_per_sentence': len(words) / max(len(sentences), 1),
            'complexity': 'simple' if len(words) / max(len(sentences), 1) < 10 else 'complex'
        }

    def _generate_voice_recommendations(self, sentiment_score: float, emotion: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on voice analysis"""
        recommendations = []

        if sentiment_score < 0.4:
            recommendations.append("Consider addressing concerns or negative feedback")

        if emotion['dominant'] == 'anger':
            recommendations.append("Take time to address frustrations before proceeding")
        elif emotion['dominant'] == 'confidence':
            recommendations.append("Great confidence detected - leverage this energy")

        return recommendations or ["Continue with current approach"]

    def _extract_command_intent(self, text: str) -> str:
        """Extract command intent from text"""
        text_lower = text.lower()

        # Navigation commands
        if any(word in text_lower for word in ['go to', 'navigate', 'open', 'show']):
            if 'dashboard' in text_lower:
                return 'navigate_dashboard'
            elif 'business' in text_lower and 'idea' in text_lower:
                return 'navigate_business_ideas'
            elif 'profile' in text_lower:
                return 'navigate_profile'
            else:
                return 'navigate_general'

        # Action commands
        elif any(word in text_lower for word in ['create', 'add', 'new']):
            if 'business' in text_lower:
                return 'create_business_idea'
            elif 'plan' in text_lower:
                return 'create_business_plan'
            else:
                return 'create_general'

        # Search commands
        elif any(word in text_lower for word in ['search', 'find', 'look for']):
            return 'search'

        # Help commands
        elif any(word in text_lower for word in ['help', 'assist', 'guide']):
            return 'help'

        return 'unknown'

    def _extract_command_parameters(self, text: str, intent: str) -> Dict[str, Any]:
        """Extract parameters from command text"""
        parameters = {}

        if intent == 'search':
            # Extract search query
            search_triggers = ['search for', 'find', 'look for']
            for trigger in search_triggers:
                if trigger in text.lower():
                    query = text.lower().split(trigger, 1)[1].strip()
                    parameters['query'] = query
                    break

        return parameters

    def _generate_command_response(self, intent: str, parameters: Dict[str, Any]) -> str:
        """Generate response for voice command"""
        responses = {
            'navigate_dashboard': "Navigating to your dashboard",
            'navigate_business_ideas': "Opening your business ideas",
            'navigate_profile': "Going to your profile",
            'create_business_idea': "Starting new business idea creation",
            'create_business_plan': "Opening business plan generator",
            'search': f"Searching for: {parameters.get('query', 'your request')}",
            'help': "How can I help you today?",
            'unknown': "I didn't understand that command. Please try again."
        }

        return responses.get(intent, "Command processed")

    def _calculate_command_confidence(self, text: str, intent: str) -> float:
        """Calculate confidence in command recognition"""
        if intent == 'unknown':
            return 0.2
        elif intent in ['navigate_dashboard', 'help']:
            return 0.9
        else:
            return 0.7

    # Meeting analysis methods
    def _analyze_meeting_content(self, text: str) -> Dict[str, Any]:
        """Analyze meeting content"""
        return {
            'topics_discussed': self._extract_topics(text),
            'sentiment_analysis': self._calculate_sentiment_score(text),
            'participation_level': 'active',  # Simplified
            'meeting_type': self._classify_meeting_type(text)
        }

    def _extract_action_items(self, text: str) -> List[Dict[str, Any]]:
        """Extract action items from meeting text"""
        action_keywords = ['action item', 'todo', 'follow up', 'next step', 'assign', 'responsible']
        sentences = text.split('.')

        action_items = []
        for sentence in sentences:
            sentence_lower = sentence.lower()
            if any(keyword in sentence_lower for keyword in action_keywords):
                action_items.append({
                    'description': sentence.strip(),
                    'priority': 'medium',
                    'assignee': 'TBD',
                    'due_date': None
                })

        return action_items[:5]  # Return up to 5 action items

    def _generate_meeting_summary(self, text: str, analysis: Dict[str, Any]) -> str:
        """Generate meeting summary"""
        word_count = len(text.split())
        topics = analysis.get('topics_discussed', [])

        summary = f"Meeting summary ({word_count} words transcribed):\n"

        if topics:
            summary += f"Main topics: {', '.join(topics[:3])}\n"

        summary += f"Overall sentiment: {self._get_sentiment_label(analysis.get('sentiment_analysis', 0.5))}"

        return summary

    def _extract_key_decisions(self, text: str) -> List[str]:
        """Extract key decisions from meeting"""
        decision_keywords = ['decided', 'agreed', 'concluded', 'resolved', 'determined']
        sentences = text.split('.')

        decisions = []
        for sentence in sentences:
            sentence_lower = sentence.lower()
            if any(keyword in sentence_lower for keyword in decision_keywords):
                decisions.append(sentence.strip())

        return decisions[:3]  # Return up to 3 key decisions

    def _extract_next_steps(self, text: str) -> List[str]:
        """Extract next steps from meeting"""
        next_step_keywords = ['next step', 'moving forward', 'going forward', 'next meeting', 'follow up']
        sentences = text.split('.')

        next_steps = []
        for sentence in sentences:
            sentence_lower = sentence.lower()
            if any(keyword in sentence_lower for keyword in next_step_keywords):
                next_steps.append(sentence.strip())

        return next_steps[:3]  # Return up to 3 next steps

    def _extract_topics(self, text: str) -> List[str]:
        """Extract main topics from text"""
        # Simple topic extraction based on frequent words
        words = text.lower().split()
        word_freq = {}

        # Filter out common words
        stop_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'a', 'an'}

        for word in words:
            if word not in stop_words and len(word) > 3:
                word_freq[word] = word_freq.get(word, 0) + 1

        # Get top topics
        sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        return [word for word, freq in sorted_words[:5]]

    def _classify_meeting_type(self, text: str) -> str:
        """Classify meeting type based on content"""
        text_lower = text.lower()

        if any(word in text_lower for word in ['budget', 'financial', 'revenue', 'cost']):
            return 'financial'
        elif any(word in text_lower for word in ['strategy', 'planning', 'roadmap', 'goals']):
            return 'strategic'
        elif any(word in text_lower for word in ['team', 'hiring', 'performance', 'review']):
            return 'hr'
        elif any(word in text_lower for word in ['product', 'development', 'feature', 'technical']):
            return 'product'
        else:
            return 'general'

    # Fallback methods when voice AI is not available
    def _fallback_transcription(self) -> Dict[str, Any]:
        """Fallback transcription when voice AI is not available"""
        return {
            'success': False,
            'transcribed_text': '',
            'language': 'unknown',
            'confidence': 0.0,
            'method': 'none',
            'error': 'Voice AI libraries not available',
            'timestamp': datetime.now().isoformat(),
            'note': 'Install speech recognition libraries for voice transcription'
        }

    def _fallback_synthesis(self) -> Dict[str, Any]:
        """Fallback synthesis when voice AI is not available"""
        return {
            'success': False,
            'audio_data': '',
            'format': 'none',
            'language': 'unknown',
            'text_length': 0,
            'estimated_duration': 0,
            'error': 'Voice synthesis not available',
            'timestamp': datetime.now().isoformat(),
            'note': 'Install text-to-speech libraries for voice synthesis'
        }

    def _fallback_sentiment_analysis(self) -> Dict[str, Any]:
        """Fallback sentiment analysis when voice AI is not available"""
        return {
            'sentiment': {
                'score': 0.5,
                'label': 'neutral',
                'confidence': 0.0
            },
            'emotion': {
                'dominant': 'neutral',
                'scores': {},
                'confidence': 0.0
            },
            'key_phrases': [],
            'speaking_patterns': {
                'word_count': 0,
                'sentence_count': 0,
                'avg_words_per_sentence': 0,
                'complexity': 'unknown'
            },
            'recommendations': ['Voice AI analysis not available'],
            'timestamp': datetime.now().isoformat(),
            'note': 'Voice AI libraries not available'
        }

    def _fallback_command_processing(self) -> Dict[str, Any]:
        """Fallback command processing when voice AI is not available"""
        return {
            'intent': 'unknown',
            'parameters': {},
            'response': 'Voice command processing not available',
            'confidence': 0.0,
            'executable': False,
            'timestamp': datetime.now().isoformat(),
            'note': 'Voice AI libraries not available'
        }

    def _fallback_meeting_transcription(self) -> Dict[str, Any]:
        """Fallback meeting transcription when voice AI is not available"""
        return {
            'success': False,
            'transcription': '',
            'participants': [],
            'meeting_analysis': {
                'topics_discussed': [],
                'sentiment_analysis': 0.5,
                'participation_level': 'unknown',
                'meeting_type': 'unknown'
            },
            'action_items': [],
            'summary': 'Meeting transcription not available',
            'key_decisions': [],
            'next_steps': [],
            'duration_estimate': 0,
            'error': 'Voice AI libraries not available',
            'timestamp': datetime.now().isoformat(),
            'note': 'Install voice AI libraries for meeting transcription'
        }
