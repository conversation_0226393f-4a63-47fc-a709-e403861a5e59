from django.db import models
from django.contrib.auth.models import User
from django.utils.text import slugify
from django.core.validators import MinValueValidator, MaxValueValidator
from api.storage import OptimizedImageStorage
from api.models import Tag

class BusinessIdea(models.Model):
    """Business ideas submitted by users for the incubator program"""

    MODERATION_STATUS = (
        ('approved', 'Approved'),
        ('pending', 'Pending Review'),
        ('rejected', 'Rejected'),
    )

    STAGE_CHOICES = (
        ('concept', 'Concept Stage'),
        ('validation', 'Validation Stage'),
        ('development', 'Development Stage'),
        ('scaling', 'Scaling Stage'),
        ('established', 'Established Business'),
    )

    title = models.CharField(max_length=200)
    slug = models.SlugField(max_length=250, unique=True, blank=True)
    description = models.TextField()
    problem_statement = models.TextField(help_text="What problem does your business idea solve?")
    solution_description = models.TextField(help_text="How does your idea solve the problem?")
    target_audience = models.TextField(help_text="Who is your target audience?")
    market_opportunity = models.TextField(help_text="What is the market opportunity?", blank=True, null=True)
    business_model = models.TextField(help_text="How will your business make money?", blank=True, null=True)
    current_stage = models.CharField(max_length=20, choices=STAGE_CHOICES, default='concept')
    image = models.ImageField(upload_to='business_idea_images/', storage=OptimizedImageStorage(), blank=True, null=True)

    owner = models.ForeignKey(User, on_delete=models.CASCADE, related_name='business_ideas')
    collaborators = models.ManyToManyField(User, related_name='collaborated_ideas', blank=True)
    tags = models.ManyToManyField(Tag, related_name='business_ideas', blank=True)

    moderation_status = models.CharField(max_length=20, choices=MODERATION_STATUS, default='pending')
    moderation_comment = models.TextField(blank=True, null=True)
    moderated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='moderated_ideas')
    moderated_at = models.DateTimeField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.title)
        super().save(*args, **kwargs)

    def __str__(self):
        return self.title

    class Meta:
        ordering = ['-created_at']


class ProgressUpdate(models.Model):
    """Progress updates for business ideas"""

    business_idea = models.ForeignKey(BusinessIdea, on_delete=models.CASCADE, related_name='progress_updates')
    title = models.CharField(max_length=200)
    description = models.TextField()
    achievements = models.TextField(help_text="What milestones have you achieved?")
    challenges = models.TextField(help_text="What challenges are you facing?", blank=True, null=True)
    next_steps = models.TextField(help_text="What are your next steps?")

    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='progress_updates')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.business_idea.title} - {self.title}"

    class Meta:
        ordering = ['-created_at']
