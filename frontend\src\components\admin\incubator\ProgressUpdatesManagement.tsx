import React, { useState, useEffect } from 'react';
import { Search, TrendingUp, Calendar, Target, CheckCircle, AlertTriangle, BarChart3 } from 'lucide-react';
import { DashboardLayout } from '../dashboard';
import { ProgressUpdate, progressUpdatesAPI } from '../../../services/incubatorApi';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';

const ProgressUpdatesManagement: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  // State management
  const [updates, setUpdates] = useState<ProgressUpdate[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch progress updates
  const fetchUpdates = async () => {
    try {
      setLoading(true);
      const data = await progressUpdatesAPI.getProgressUpdates();
      setUpdates(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error fetching progress updates:', error);
      setUpdates([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUpdates();
  }, []);

  // Filter updates based on search
  const filteredUpdates = updates.filter(update => {
    return update.business_idea.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      update.business_idea.user.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      update.business_idea.user.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (update.title && update.title.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (update.description && update.description.toLowerCase().includes(searchTerm.toLowerCase()));
  });

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Get progress color based on percentage
  const getProgressColor = (progress: number) => {
    if (progress >= 80) return 'bg-green-500';
    if (progress >= 60) return 'bg-blue-500';
    if (progress >= 40) return 'bg-yellow-500';
    if (progress >= 20) return 'bg-orange-500';
    return 'bg-red-500';
  };

  // Get update type icon and color
  const getUpdateTypeDisplay = (type: string) => {
    switch (type) {
      case 'milestone':
        return {
          icon: <Target size={16} />,
          color: 'bg-green-500/20 text-green-300',
          label: t('update.type.milestone', 'Milestone')
        };
      case 'progress':
        return {
          icon: <TrendingUp size={16} />,
          color: 'bg-blue-500/20 text-blue-300',
          label: t('update.type.progress', 'Progress')
        };
      case 'challenge':
        return {
          icon: <AlertTriangle size={16} />,
          color: 'bg-red-500/20 text-red-300',
          label: t('update.type.challenge', 'Challenge')
        };
      case 'achievement':
        return {
          icon: <CheckCircle size={16} />,
          color: 'bg-purple-500/20 text-purple-300',
          label: t('update.type.achievement', 'Achievement')
        };
      default:
        return {
          icon: <BarChart3 size={16} />,
          color: 'bg-gray-500/20 text-gray-300',
          label: type
        };
    }
  };

  return (
    <DashboardLayout currentPage="incubator">
      {/* Header */}
      <div className={`mb-8 ${isRTL ? "text-right" : ""}`}>
        <h1 className="text-2xl font-bold text-white">{t('admin.progress.updates.management', 'Progress Updates Management')}</h1>
        <div className="text-gray-400 mt-1">{t('admin.progress.updates.description', 'Track business idea progress and milestones')}</div>
      </div>

      {/* Search */}
      <div className="mb-6">
        <div className="relative">
          <Search size={20} className={`absolute top-3 text-gray-400 ${isRTL ? "right-3" : "left-3"}`} />
          <input
            type="text"
            placeholder={t('admin.search.updates', 'Search progress updates...')}
            value={searchTerm}
            onChange={handleSearchChange}
            className={`w-full bg-indigo-900/50 border border-indigo-800 rounded-lg py-2.5 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 ${isRTL ? "pr-10 pl-4" : "pl-10 pr-4"}`}
          />
        </div>
      </div>

      {/* Updates List */}
      {loading ? (
        <div className="flex justify-center py-12">
          <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      ) : filteredUpdates.length > 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredUpdates.map((update) => {
            const typeDisplay = getUpdateTypeDisplay(update.update_type);
            const progressColor = getProgressColor(update.progress_percentage);
            
            return (
              <div key={update.id} className="bg-indigo-900/30 backdrop-blur-sm rounded-xl overflow-hidden border border-indigo-800/50 hover:border-purple-500/50 transition-all duration-300 hover:shadow-glow">
                <div className="p-6">
                  <div className={`flex justify-between items-start mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className="flex-1">
                      <div className={`flex items-center space-x-2 mb-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 ${typeDisplay.color} ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                          {typeDisplay.icon}
                          <span>{typeDisplay.label}</span>
                        </span>
                      </div>
                      
                      {update.title && (
                        <h3 className="text-lg font-semibold text-white mb-2 line-clamp-2">{update.title}</h3>
                      )}
                    </div>
                  </div>

                  {/* Business Idea Info */}
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-300 mb-2">{t('update.business.idea', 'Business Idea')}</h4>
                    <div className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                      <div className="w-10 h-10 rounded-full bg-purple-700 flex items-center justify-center text-white text-sm font-bold">
                        {update.business_idea.user.first_name.charAt(0)}{update.business_idea.user.last_name.charAt(0)}
                      </div>
                      <div>
                        <p className="text-white font-medium line-clamp-1">{update.business_idea.title}</p>
                        <p className="text-gray-400 text-sm">
                          {update.business_idea.user.first_name} {update.business_idea.user.last_name}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Progress Bar */}
                  <div className="mb-4">
                    <div className={`flex justify-between items-center mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <span className="text-sm font-medium text-gray-300">{t('update.progress', 'Progress')}</span>
                      <span className="text-sm font-medium text-white">{update.progress_percentage}%</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full transition-all duration-300 ${progressColor}`}
                        style={{ width: `${update.progress_percentage}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* Update Details */}
                  <div className="space-y-3">
                    {update.description && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-300 mb-1">{t('update.description', 'Description')}</h4>
                        <p className="text-gray-300 text-sm line-clamp-3">{update.description}</p>
                      </div>
                    )}

                    {update.achievements && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-300 mb-1">{t('update.achievements', 'Achievements')}</h4>
                        <p className="text-gray-300 text-sm line-clamp-2">{update.achievements}</p>
                      </div>
                    )}

                    {update.challenges && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-300 mb-1">{t('update.challenges', 'Challenges')}</h4>
                        <p className="text-gray-300 text-sm line-clamp-2">{update.challenges}</p>
                      </div>
                    )}

                    {update.next_steps && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-300 mb-1">{t('update.next.steps', 'Next Steps')}</h4>
                        <p className="text-gray-300 text-sm line-clamp-2">{update.next_steps}</p>
                      </div>
                    )}

                    {/* Metrics */}
                    {(update.revenue || update.users_count || update.team_size) && (
                      <div className="grid grid-cols-3 gap-3 pt-3 border-t border-indigo-800/50">
                        {update.revenue && (
                          <div className="text-center">
                            <p className="text-xs text-gray-400">{t('update.revenue', 'Revenue')}</p>
                            <p className="text-white font-medium">${update.revenue.toLocaleString()}</p>
                          </div>
                        )}
                        {update.users_count && (
                          <div className="text-center">
                            <p className="text-xs text-gray-400">{t('update.users', 'Users')}</p>
                            <p className="text-white font-medium">{update.users_count.toLocaleString()}</p>
                          </div>
                        )}
                        {update.team_size && (
                          <div className="text-center">
                            <p className="text-xs text-gray-400">{t('update.team', 'Team')}</p>
                            <p className="text-white font-medium">{update.team_size}</p>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Date */}
                    <div className="pt-3 border-t border-indigo-800/50">
                      <div className={`flex items-center space-x-2 text-sm ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                        <Calendar size={16} className="text-blue-400" />
                        <span className="text-gray-300">{t('update.date', 'Updated')}</span>
                        <span className="text-white">{new Date(update.created_at).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        <div className="text-center py-12">
          <TrendingUp size={48} className="mx-auto text-gray-600 mb-4" />
          <h3 className="text-lg font-medium text-gray-400 mb-2">
            {searchTerm 
              ? t('admin.no.updates.found', 'No progress updates found')
              : t('admin.no.updates', 'No progress updates yet')
            }
          </h3>
          <p className="text-gray-500 mb-4">
            {searchTerm
              ? t('admin.try.different.search', 'Try adjusting your search')
              : t('admin.no.updates.description', 'Progress updates will appear here when entrepreneurs share their progress')
            }
          </p>
        </div>
      )}
    </DashboardLayout>
  );
};

export default ProgressUpdatesManagement;
