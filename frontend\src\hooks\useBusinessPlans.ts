import { useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import {
  useApiList,
  useApiQuery,
  useApiCreate,
  useApiUpdate,
  useApiDelete,
  useApiInfinite,
  usePrefetchApiQuery,
  usePrefetchApiList
} from './useApi';
import { BusinessPlan } from '../services/businessPlanApi';
import { QueryParams } from '../types/api';

// Query keys for business plans
export const BusinessPlanKeys = {
  all: ['business-plans'] as const,
  lists: () => [...BusinessPlanKeys.all, 'list'] as const,
  list: (params?: QueryParams) => [...BusinessPlanKeys.lists(), params] as const,
  details: () => [...BusinessPlanKeys.all, 'detail'] as const,
  detail: (id: number) => [...BusinessPlanKeys.details(), id] as const,
};

/**
 * Hook for fetching business plans list
 */
export function useBusinessPlansList(params?: QueryParams) {
  return useApiList<BusinessPlan>(
    BusinessPlanKeys.list(params),
    '/incubator/business-plans/',
    params
  );
}

/**
 * Hook for infinite scrolling business plans
 */
export function useBusinessPlansInfinite(params?: QueryParams) {
  return useApiInfinite<BusinessPlan>(
    BusinessPlanKeys.list(params),
    '/incubator/business-plans/',
    params
  );
}

/**
 * Hook for fetching a single business plan
 */
export function useBusinessPlan(id: number) {
  return useApiQuery<BusinessPlan>(
    BusinessPlanKeys.detail(id),
    `/incubator/business-plans/${id}/`
  );
}

/**
 * Hook for prefetching a business plan
 */
export function usePrefetchBusinessPlan(id: number) {
  return usePrefetchApiQuery<BusinessPlan>(
    BusinessPlanKeys.detail(id),
    `/incubator/business-plans/${id}/`
  );
}

/**
 * Hook for prefetching business plans list
 */
export function usePrefetchBusinessPlansList(params?: QueryParams) {
  return usePrefetchApiList<BusinessPlan>(
    BusinessPlanKeys.list(params),
    '/incubator/business-plans/',
    params
  );
}

/**
 * Hook for creating a business plan
 */
export function useCreateBusinessPlan() {
  const queryClient = useQueryClient();

  return useApiCreate<BusinessPlan, Partial<BusinessPlan>>(
    '/incubator/business-plans/',
    {
      onSuccess: (newPlan) => {
        // Invalidate business plans list queries to refetch data
        queryClient.invalidateQueries({ queryKey: BusinessPlanKeys.lists() });

        // Add the new plan to the cache
        queryClient.setQueryData(BusinessPlanKeys.detail(newPlan.id), newPlan);
      }
    }
  );
}

/**
 * Hook for updating a business plan
 */
export function useUpdateBusinessPlan() {
  const queryClient = useQueryClient();

  return useApiUpdate<BusinessPlan, Partial<BusinessPlan>>(
    (id: number) => `/incubator/business-plans/${id}/`,
    {
      onSuccess: (updatedPlan) => {
        // Update the specific plan in cache
        queryClient.setQueryData(BusinessPlanKeys.detail(updatedPlan.id), updatedPlan);
        
        // Invalidate lists to ensure consistency
        queryClient.invalidateQueries({ queryKey: BusinessPlanKeys.lists() });
      }
    }
  );
}

/**
 * Hook for deleting a business plan
 */
export function useDeleteBusinessPlan() {
  const queryClient = useQueryClient();

  return useApiDelete(
    (id: number) => `/incubator/business-plans/${id}/`,
    {
      onSuccess: (_, id) => {
        // Remove from cache
        queryClient.removeQueries({ queryKey: BusinessPlanKeys.detail(id) });
        
        // Invalidate lists to refetch data
        queryClient.invalidateQueries({ queryKey: BusinessPlanKeys.lists() });
      }
    }
  );
}

/**
 * Hook for generating AI feedback for a business plan
 */
export function useGenerateBusinessPlanFeedback() {
  const queryClient = useQueryClient();

  return useApiCreate<BusinessPlan, { business_plan_id: number }>(
    '/incubator/business-plans/generate-feedback/',
    {
      onSuccess: (updatedPlan) => {
        // Update the specific plan in cache with new AI feedback
        queryClient.setQueryData(BusinessPlanKeys.detail(updatedPlan.id), updatedPlan);
      }
    }
  );
}

/**
 * Hook for duplicating a business plan
 */
export function useDuplicateBusinessPlan() {
  const queryClient = useQueryClient();

  return useApiCreate<BusinessPlan, { business_plan_id: number }>(
    '/incubator/business-plans/duplicate/',
    {
      onSuccess: (newPlan) => {
        // Invalidate business plans list queries to refetch data
        queryClient.invalidateQueries({ queryKey: BusinessPlanKeys.lists() });

        // Add the new plan to the cache
        queryClient.setQueryData(BusinessPlanKeys.detail(newPlan.id), newPlan);
      }
    }
  );
}
