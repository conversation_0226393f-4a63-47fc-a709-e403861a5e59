import React from 'react';
import { Users, Calendar, BookOpen, MessageSquare } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../hooks/useLanguage';
import StatCard from './StatCard';
import { DashboardStats } from '../../../../types/admin';
interface StatsSectionProps {
  stats: DashboardStats;
}

/**
 * StatsSection component displays a grid of stat cards with key metrics
 */
const StatsSection: React.FC<StatsSectionProps> = ({ stats  }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <StatCard
        title={t('admin.totalUsers')}
        count={stats.users?.total_users || 0}
        icon={<Users size={24} className="text-white" />}
        change={stats.users?.new_users || 0}
        color="bg-blue-600/30"
      />
      <StatCard
        title={t('admin.activeEvents')}
        count={stats.events?.total_events || 0}
        icon={<Calendar size={24} className="text-white" />}
        change={stats.events?.upcoming_events || 0}
        color="bg-purple-600/30"
      />
      <StatCard
        title={t('admin.resources')}
        count={stats.resources?.total_resources || 0}
        icon={<BookOpen size={24} className="text-white" />}
        change={stats.resources?.new_resources || 0}
        color="bg-green-600/30"
      />
      <StatCard
        title={t('admin.communityPosts')}
        count={stats.posts?.total_posts || 0}
        icon={<MessageSquare size={24} className="text-white" />}
        change={stats.posts?.new_posts || 0}
        color="bg-orange-600/30"
      />
    </div>
  );
};

export default StatsSection;
