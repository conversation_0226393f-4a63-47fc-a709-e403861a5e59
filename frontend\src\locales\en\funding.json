{"funding": {"title": "Funding Opportunities", "description": "Discover funding opportunities for your business", "myApplications": "My Applications", "totalOpportunities": "Total Opportunities", "activeOpportunities": "Active Opportunities", "searchPlaceholder": "Search funding opportunities...", "allTypes": "All Types", "grant": "<PERSON>", "investment": "Investment", "loan": "Loan", "competition": "Competition", "allStages": "All Stages", "seedStage": "Seed Stage", "earlyStage": "Early Stage", "growthStage": "Growth Stage", "noOpportunities": "No funding opportunities found", "checkBackLater": "Check back later for new opportunities", "types": {"grant": "<PERSON>", "investment": "Investment", "loan": "Loan", "competition": "Competition", "accelerator": "Accelerator", "incubator": "Incubator", "crowdfunding": "Crowdfunding", "angel": "Angel Investment", "venture": "Venture Capital", "government": "Government Funding"}, "stages": {"idea": "Idea Stage", "seed": "Seed Stage", "early": "Early Stage", "growth": "Growth Stage", "expansion": "Expansion Stage", "mature": "Mature Stage"}, "status": {"open": "Open", "closed": "Closed", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "inReview": "In Review"}, "filters": {"type": "Funding Type", "stage": "Business Stage", "amount": "Funding Amount", "deadline": "Application Deadline", "location": "Location", "industry": "Industry"}, "details": {"amount": "Funding Amount", "deadline": "Application Deadline", "requirements": "Requirements", "description": "Description", "eligibility": "Eligibility Criteria", "applicationProcess": "Application Process", "contactInfo": "Contact Information", "website": "Website", "documents": "Required Documents"}, "actions": {"apply": "Apply Now", "viewDetails": "View Details", "saveForLater": "Save for Later", "share": "Share", "downloadInfo": "Download Info", "contactProvider": "Contact Provider"}, "application": {"submit": "Submit Application", "draft": "Save as Draft", "review": "Review Application", "edit": "Edit Application", "withdraw": "Withdraw Application", "status": "Application Status", "submitted": "Application Submitted", "inProgress": "Application in Progress"}, "messages": {"applicationSubmitted": "Application submitted successfully", "applicationSaved": "Application saved as draft", "applicationWithdrawn": "Application withdrawn", "opportunitySaved": "Opportunity saved for later", "errorSubmitting": "Error submitting application", "errorLoading": "Error loading funding opportunities"}}}