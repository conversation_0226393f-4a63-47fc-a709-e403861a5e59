import React, { useState } from 'react';
import { Link, LinkProps, useNavigate } from 'react-router-dom';

import { useTranslation } from 'react-i18next';
interface PreloadLinkProps extends LinkProps {
  preloadComponent?: React.LazyExoticComponent<any>;
  preloadTimeout?: number;
  children: React.ReactNode;
}

/**
 * A Link component that preloads the target route's component when hovered
 */
const PreloadLink: React.FC<PreloadLinkProps> = ({ to,
  preloadComponent,
  preloadTimeout = 200,
  children,
  ...props
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [isPreloaded, setIsPreloaded] = useState(false);
  const navigate = useNavigate();
  let timer: NodeJS.Timeout | null = null;

  const handleMouseEnter = () => {
    if (isPreloaded || !preloadComponent) return;

    // Set a timeout to avoid preloading if the user just moves the mouse over the link briefly
    timer = setTimeout(() => {
      // Preload the component
      preloadComponent.preload();
      setIsPreloaded(true);
      console.log(`Preloaded route: ${to}`);
    }, preloadTimeout);
  };

  const handleMouseLeave = () => {
    // Clear the timeout if the user moves the mouse away before the timeout
    if (timer) {
      clearTimeout(timer);
      timer = null;
    }
  };

  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    if (props.onClick) {
      props.onClick(e);
    }
    
    // If the link is not a standard navigation (e.g., has onClick that prevents default),
    // don't interfere with it
    if (e.defaultPrevented) return;
    
    e.preventDefault();
    
    // If the component is not yet preloaded, preload it immediately
    if (!isPreloaded && preloadComponent) {
      preloadComponent.preload().then(() => {
        navigate(to.toString());
      });
    } else {
      navigate(to.toString());
    }
  };

  return (
    <Link
      to={to}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={handleClick}
      {...props}
    >
      {children}
    </Link>
  );
};

export default PreloadLink;
