import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Brain, Zap, Settings, BarChart3, Key, Shield,
  Activity, TrendingUp, AlertTriangle, CheckCircle,
  Cpu, Database, Globe, Lock, RefreshCw, Eye,
  DollarSign, Clock, Users, MessageSquare, Check
} from 'lucide-react';
import { getAuthToken } from '../../../services/api';
import AuthenticatedLayout from '../../layout/AuthenticatedLayout';

interface AIModel {
  id: string;
  name: string;
  provider: string;
  status: 'active' | 'inactive' | 'error';
  usage_count: number;
  cost_per_request: number;
  response_time_avg: number;
  success_rate: number;
  last_used: string;
}

interface AIUsageStats {
  total_requests: number;
  total_cost: number;
  avg_response_time: number;
  success_rate: number;
  top_users: Array<{ username: string; requests: number; cost: number }>;
  daily_usage: Array<{ date: string; requests: number; cost: number }>;
}

interface AIConfiguration {
  api_key: string;
  default_model: string;
  max_tokens: number;
  temperature: number;
  rate_limit_per_user: number;
  rate_limit_per_hour: number;
  enable_content_filter: boolean;
  enable_usage_tracking: boolean;
}

interface AIConfigItem {
  key: string;
  value: string;
  config_type: string;
  description: string;
  is_sensitive: boolean;
  updated_at: string;
}

const AISystemManagement: React.FC = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<string>('overview');
  const [aiModels, setAiModels] = useState<AIModel[]>([]);
  const [usageStats, setUsageStats] = useState<AIUsageStats | null>(null);
  const [configuration, setConfiguration] = useState<AIConfiguration | null>(null);
  const [configItems, setConfigItems] = useState<AIConfigItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);
  const [testResult, setTestResult] = useState<{success: boolean; message: string} | null>(null);

  useEffect(() => {
    fetchAIData();
  }, []);

  const fetchAIData = async () => {
    try {
      setLoading(true);

      const response = await fetch('/api/superadmin/system/ai_monitoring/', {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setAiModels(data.ai_models || []);
        setUsageStats(data.usage_stats || null);
      } else {
        console.error('Failed to fetch AI data:', response.status);
        setAiModels([]);
        setUsageStats(null);
      }


      // Fetch AI configuration from Super Admin API
      const configResponse = await fetch('/api/users/super-admin/ai_configuration/', {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      if (configResponse.ok) {
        const configData = await configResponse.json();
        setConfigItems(configData.configurations || []);

        // Convert to configuration object
        const config: AIConfiguration = {
          api_key: configData.configurations.find((c: AIConfigItem) => c.key === 'api_key')?.value || '',
          default_model: configData.configurations.find((c: AIConfigItem) => c.key === 'default_model')?.value || 'gemini-2.0-flash',
          max_tokens: parseInt(configData.configurations.find((c: AIConfigItem) => c.key === 'max_tokens')?.value || '4000'),
          temperature: parseFloat(configData.configurations.find((c: AIConfigItem) => c.key === 'temperature')?.value || '0.7'),
          rate_limit_per_user: 100,
          rate_limit_per_hour: 1000,
          enable_content_filter: true,
          enable_usage_tracking: true
        };
        setConfiguration(config);
      }

    } catch (error) {
      console.error('Failed to fetch AI data:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateConfiguration = async (newConfig: Partial<AIConfiguration>) => {
    if (!configuration) return;

    try {
      setSaving(true);

      // Prepare configuration updates
      const configUpdates: Record<string, any> = {};

      if (newConfig.api_key !== undefined) {
        configUpdates.api_key = newConfig.api_key;
      }
      if (newConfig.default_model !== undefined) {
        configUpdates.default_model = newConfig.default_model;
      }
      if (newConfig.max_tokens !== undefined) {
        configUpdates.max_tokens = newConfig.max_tokens;
      }
      if (newConfig.temperature !== undefined) {
        configUpdates.temperature = newConfig.temperature;
      }

      // Send update to Super Admin API
      const response = await fetch('/api/users/super-admin/update-ai-config/', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ configurations: configUpdates })
      });

      if (response.ok) {
        const result = await response.json();
        setConfiguration({ ...configuration, ...newConfig });
        setTestResult({ success: true, message: 'Configuration updated successfully!' });

        // Refresh the configuration data
        fetchAIData();
      } else {
        throw new Error('Failed to update configuration');
      }

    } catch (error) {
      console.error('Failed to update configuration:', error);
      setTestResult({ success: false, message: 'Failed to update configuration' });
    } finally {
      setSaving(false);
    }
  };

  const testConnection = async () => {
    try {
      setTesting(true);
      setTestResult(null);

      const response = await fetch('/api/users/super-admin/test-ai-connection/', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();

      if (result.success) {
        setTestResult({ success: true, message: 'AI connection test successful!' });
      } else {
        setTestResult({ success: false, message: result.error || 'Connection test failed' });
      }

    } catch (error) {
      console.error('Connection test failed:', error);
      setTestResult({ success: false, message: 'Connection test failed' });
    } finally {
      setTesting(false);
    }
  };

  const toggleModelStatus = async (modelId: string) => {
    setAiModels(prev => prev.map(model => 
      model.id === modelId 
        ? { ...model, status: model.status === 'active' ? 'inactive' : 'active' }
        : model
    ));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-400';
      case 'inactive': return 'text-gray-400';
      case 'error': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="w-4 h-4" />;
      case 'inactive': return <AlertTriangle className="w-4 h-4" />;
      case 'error': return <AlertTriangle className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  const tabs = [
    { id: 'overview', name: 'Overview', icon: <BarChart3 className="w-4 h-4" /> },
    { id: 'models', name: 'AI Models', icon: <Brain className="w-4 h-4" /> },
    { id: 'usage', name: 'Usage Analytics', icon: <TrendingUp className="w-4 h-4" /> },
    { id: 'configuration', name: 'Configuration', icon: <Settings className="w-4 h-4" /> },
    { id: 'security', name: 'Security', icon: <Shield className="w-4 h-4" /> }
  ];

  return (
    <AuthenticatedLayout>
      <div className="text-white">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-2">
            <Brain className="w-8 h-8 text-purple-400" />
            <h1 className="text-3xl font-bold text-white">
              {t('superAdmin.aiSystem.title', 'AI System Management')}
            </h1>
          </div>
          <p className="text-gray-400">
            {t('superAdmin.aiSystem.subtitle', 'Manage AI models, usage, and configuration')}
          </p>
        </div>

      {/* Tabs */}
      <div className="flex flex-wrap gap-2 mb-8 border-b border-gray-700">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex items-center gap-2 px-4 py-2 rounded-t-lg transition-colors ${
              activeTab === tab.id
                ? 'bg-purple-600 text-white border-b-2 border-purple-400'
                : 'text-gray-400 hover:text-white hover:bg-gray-800'
            }`}
          >
            {tab.icon}
            {tab.name}
          </button>
        ))}
      </div>

      {/* Overview Tab */}
      {activeTab === 'overview' && usageStats && (
        <div className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <div className="flex items-center gap-3 mb-2">
                <MessageSquare className="w-5 h-5 text-blue-400" />
                <span className="text-sm text-gray-400">Total Requests</span>
              </div>
              <div className="text-2xl font-bold text-blue-400">
                {usageStats.total_requests.toLocaleString()}
              </div>
            </div>

            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <div className="flex items-center gap-3 mb-2">
                <DollarSign className="w-5 h-5 text-green-400" />
                <span className="text-sm text-gray-400">Total Cost</span>
              </div>
              <div className="text-2xl font-bold text-green-400">
                ${usageStats.total_cost.toFixed(2)}
              </div>
            </div>

            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <div className="flex items-center gap-3 mb-2">
                <Clock className="w-5 h-5 text-yellow-400" />
                <span className="text-sm text-gray-400">Avg Response Time</span>
              </div>
              <div className="text-2xl font-bold text-yellow-400">
                {usageStats.avg_response_time}s
              </div>
            </div>

            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <div className="flex items-center gap-3 mb-2">
                <CheckCircle className="w-5 h-5 text-purple-400" />
                <span className="text-sm text-gray-400">Success Rate</span>
              </div>
              <div className="text-2xl font-bold text-purple-400">
                {usageStats.success_rate}%
              </div>
            </div>
          </div>

          {/* Top Users */}
          <div className="bg-gray-800 rounded-lg border border-gray-700">
            <div className="p-4 border-b border-gray-700">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Users className="w-5 h-5" />
                Top AI Users
              </h3>
            </div>
            <div className="p-4">
              <div className="space-y-3">
                {usageStats.top_users.map((user, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center text-sm">
                        {index + 1}
                      </div>
                      <span className="font-medium">{user.username}</span>
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-gray-300">{user.requests} requests</div>
                      <div className="text-xs text-gray-500">${user.cost.toFixed(2)}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* AI Models Tab */}
      {activeTab === 'models' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {aiModels.map((model) => (
              <div key={model.id} className="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-semibold">{model.name}</h3>
                    <p className="text-sm text-gray-400">{model.provider}</p>
                  </div>
                  <div className={`flex items-center gap-1 ${getStatusColor(model.status)}`}>
                    {getStatusIcon(model.status)}
                    <span className="text-sm capitalize">{model.status}</span>
                  </div>
                </div>

                <div className="space-y-3 mb-4">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Usage Count:</span>
                    <span>{model.usage_count.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Cost per Request:</span>
                    <span>${model.cost_per_request.toFixed(3)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Avg Response Time:</span>
                    <span>{model.response_time_avg}s</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Success Rate:</span>
                    <span>{model.success_rate}%</span>
                  </div>
                </div>

                <button
                  onClick={() => toggleModelStatus(model.id)}
                  className={`w-full py-2 px-4 rounded-lg font-medium transition-colors ${
                    model.status === 'active'
                      ? 'bg-red-600 hover:bg-red-700 text-white'
                      : 'bg-green-600 hover:bg-green-700 text-white'
                  }`}
                >
                  {model.status === 'active' ? 'Deactivate' : 'Activate'}
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Configuration Tab */}
      {activeTab === 'configuration' && configuration && (
        <div className="space-y-6">
          {/* Test Result */}
          {testResult && (
            <div className={`p-4 rounded-lg border ${
              testResult.success
                ? 'bg-green-900/20 border-green-500/20 text-green-300'
                : 'bg-red-900/20 border-red-500/20 text-red-300'
            }`}>
              <div className="flex items-center gap-2">
                {testResult.success ? <CheckCircle className="w-5 h-5" /> : <AlertTriangle className="w-5 h-5" />}
                <span>{testResult.message}</span>
              </div>
            </div>
          )}

          <div className="bg-gray-800 rounded-lg border border-gray-700">
            <div className="p-4 border-b border-gray-700">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <Key className="w-5 h-5" />
                  Gemini AI Configuration
                </h3>
                <button
                  onClick={testConnection}
                  disabled={testing || saving}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors disabled:opacity-50"
                >
                  {testing ? <RefreshCw className="w-4 h-4 animate-spin" /> : <Eye className="w-4 h-4" />}
                  {testing ? 'Testing...' : 'Test Connection'}
                </button>
              </div>
            </div>
            <div className="p-4 space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Gemini API Key</label>
                <input
                  type="password"
                  value={configuration.api_key}
                  onChange={(e) => updateConfiguration({ api_key: e.target.value })}
                  placeholder="Enter your Gemini API key"
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:border-blue-500 focus:outline-none"
                />
                <p className="text-xs text-gray-400 mt-1">
                  Get your API key from <a href="https://makersuite.google.com/app/apikey" target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:underline">Google AI Studio</a>
                </p>
              </div>
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg border border-gray-700">
            <div className="p-4 border-b border-gray-700">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Settings className="w-5 h-5" />
                Model Configuration
              </h3>
            </div>
            <div className="p-4 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Default Model</label>
                  <select
                    value={configuration.default_model}
                    onChange={(e) => updateConfiguration({ default_model: e.target.value })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:border-blue-500 focus:outline-none"
                  >
                    <option value="gemini-2.0-flash">Gemini 2.0 Flash</option>
                    <option value="gemini-1.5-pro">Gemini 1.5 Pro</option>
                    <option value="gemini-1.5-flash">Gemini 1.5 Flash</option>
                    <option value="gemini-pro-vision">Gemini Pro Vision</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Max Tokens</label>
                  <input
                    type="number"
                    min="1"
                    max="8192"
                    value={configuration.max_tokens}
                    onChange={(e) => updateConfiguration({ max_tokens: parseInt(e.target.value) })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:border-blue-500 focus:outline-none"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Temperature</label>
                  <input
                    type="number"
                    step="0.1"
                    min="0"
                    max="2"
                    value={configuration.temperature}
                    onChange={(e) => updateConfiguration({ temperature: parseFloat(e.target.value) })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:border-blue-500 focus:outline-none"
                  />
                  <p className="text-xs text-gray-400 mt-1">
                    Controls randomness: 0 = focused, 1 = balanced, 2 = creative
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Rate Limit (per user/hour)</label>
                  <input
                    type="number"
                    min="1"
                    value={configuration.rate_limit_per_user}
                    onChange={(e) => updateConfiguration({ rate_limit_per_user: parseInt(e.target.value) })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:border-blue-500 focus:outline-none"
                  />
                </div>
              </div>

              <div className="space-y-3">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={configuration.enable_content_filter}
                    onChange={(e) => updateConfiguration({ enable_content_filter: e.target.checked })}
                    className="rounded"
                  />
                  <span>Enable Content Filter</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={configuration.enable_usage_tracking}
                    onChange={(e) => updateConfiguration({ enable_usage_tracking: e.target.checked })}
                    className="rounded"
                  />
                  <span>Enable Usage Tracking</span>
                </label>
              </div>

              <div className="flex gap-3 pt-4">
                <button
                  onClick={() => updateConfiguration(configuration)}
                  disabled={saving}
                  className="flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg transition-colors disabled:opacity-50"
                >
                  {saving ? <RefreshCw className="w-4 h-4 animate-spin" /> : <Check className="w-4 h-4" />}
                  {saving ? 'Saving...' : 'Save Configuration'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      </div>
    </AuthenticatedLayout>
  );
};

export default AISystemManagement;
