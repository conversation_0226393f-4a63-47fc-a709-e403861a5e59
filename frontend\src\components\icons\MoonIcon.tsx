import React from 'react';
import { useLanguage } from '../../hooks/useLanguage';
import { useTranslation } from 'react-i18next';

interface MoonIconProps {
  size?: number;
  className?: string;
}

const MoonIcon: React.FC<MoonIconProps> = ({ size = 40, className = ''  }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  // Use consistent glass morphism colors
  const moonColor = '#E1E1E1';
  const moonStroke = '#CCCCCC';
  const craterColor = '#D0D0D0';
  const glowColor = '#FFFFFF33';

  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 100 100"
      className={className}
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* Moon crescent */}
      <path
        d="M50 15
           A35 35 0 1 0 50 85
           A25 25 0 1 1 50 15"
        fill={moonColor}
        stroke={moonStroke}
        strokeWidth="1"
      />

      {/* Moon craters */}
      <circle cx="35" cy="40" r="5" fill={craterColor} />
      <circle cx="60" cy="55" r="7" fill={craterColor} />
      <circle cx="45" cy="70" r="4" fill={craterColor} />

      {/* Glow effect */}
      <circle
        cx="50"
        cy="50"
        r="40"
        fill="none"
        stroke={glowColor}
        strokeWidth="8"
      />
    </svg>
  );
};

export default MoonIcon;
