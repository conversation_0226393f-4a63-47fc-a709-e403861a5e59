import { useState, useCallback, useEffect } from 'react';
import { Post, postsAPI } from '../../../../../services/api';
import { useAppSelector } from '../../../../../store/hooks';
import { FilterValue } from '../../../common/AdvancedFilter';
import useInfiniteScroll from '../../../../../hooks/useInfiniteScroll';

interface PostFormData {
  title: string;
  content: string;
}

export const usePostsManagement = () => {
  const { user } = useAppSelector(state => state.auth);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPost, setSelectedPost] = useState<Post | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [expandedPost, setExpandedPost] = useState<number | null>(null);
  const [formError, setFormError] = useState<string | null>(null);
  const [formSuccess, setFormSuccess] = useState<string | null>(null);
  const [formSubmitting, setFormSubmitting] = useState(false);
  const [allPosts, setAllPosts] = useState<Post[]>([]);

  // Filtering state
  const [activeFilters, setActiveFilters] = useState<FilterValue[]>([]);

  // Bulk actions state
  const [selectedItems, setSelectedItems] = useState<number[]>([]);

  // Form data for create/edit
  const [formData, setFormData] = useState<PostFormData>({
    title: '',
    content: ''
  });

  // Fetch posts with pagination
  const fetchPaginatedPosts = useCallback(async (page: number, limit: number) => {
    try {
      // In a real implementation, you would call an API that supports pagination
      // For now, we'll simulate pagination by slicing the allPosts array
      const start = (page - 1) * limit;
      const end = start + limit;

      // If this is the first page, fetch all posts first
      if (page === 1 && allPosts.length === 0) {
        const posts = await postsAPI.getPosts();
        setAllPosts(posts);
        return posts.slice(0, limit);
      }

      return allPosts.slice(start, end);
    } catch (error) {
      console.error('Error fetching paginated posts:', error);
      throw error;
    }
  }, [allPosts]);

  // Use the infinite scroll hook
  const {
    data: posts,
    loading,
    error,
    hasMore,
    lastElementRef,
    refresh: refreshPosts,
    loadMore
  } = useInfiniteScroll<Post>({
    fetchData: fetchPaginatedPosts,
    limit: 10,
    enabled: !!user?.id,
  });

  // Effect to refresh posts when filters change
  useEffect(() => {
    if (activeFilters.length > 0) {
      refreshPosts();
    }
  }, [activeFilters, refreshPosts]);

  // Filter posts based on search term and active filters
  const filteredPosts = posts.filter(post => {
    // Search term filter
    const matchesSearch =
      post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.author.username.toLowerCase().includes(searchTerm.toLowerCase());

    if (!matchesSearch) return false;

    // Apply advanced filters
    for (const filter of activeFilters) {
      switch (filter.field) {
        case 'title':
          if (!post.title.toLowerCase().includes(String(filter.value).toLowerCase())) return false;
          break;
        case 'content':
          if (!post.content.toLowerCase().includes(String(filter.value).toLowerCase())) return false;
          break;
        case 'author':
          if (!post.author.username.toLowerCase().includes(String(filter.value).toLowerCase())) return false;
          break;
        case 'moderation_status':
          if (post.moderation_status !== filter.value) return false;
          break;
        case 'created_at':
          if (filter.value instanceof Date) {
            const postDate = new Date(post.created_at);
            const filterDate = filter.value;
            if (postDate.getFullYear() !== filterDate.getFullYear() ||
                postDate.getMonth() !== filterDate.getMonth() ||
                postDate.getDate() !== filterDate.getDate()) {
              return false;
            }
          }
          break;
      }
    }

    return true;
  });

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | string, editorName?: string) => {
    if (typeof e === 'string' && editorName) {
      // Handle rich text editor input
      setFormData(prev => ({ ...prev, [editorName]: e }));
    } else if (typeof e !== 'string') {
      // Handle regular input
      const { name, value } = e.target;
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      title: '',
      content: ''
    });
    setFormError(null);
    setFormSuccess(null);
  };

  // Open create modal
  const openCreateModal = () => {
    resetForm();
    setIsCreateModalOpen(true);
  };

  // Handle edit post
  const handleEditPost = (post: Post) => {
    setSelectedPost(post);
    setFormData({
      title: post.title,
      content: post.content
    });
    setIsEditModalOpen(true);
  };

  // Handle delete post
  const handleDeletePost = (post: Post) => {
    setSelectedPost(post);
    setIsDeleteModalOpen(true);
  };

  // Create new post
  const createPost = async () => {
    setFormSubmitting(true);
    setFormError(null);
    setFormSuccess(null);

    try {
      // Validate form
      if (!formData.title || !formData.content) {
        setFormError(t("admin.please.fill.in", "Please fill in all required fields"));
        setFormSubmitting(false);
        return;
      }

      // Create post data
      const postData = {
        title: formData.title,
        content: formData.content,
        author_id: user?.id || 0
      };

      // Call API to create post
      const newPost = await postsAPI.createPost(postData);

      // Update local state
      setAllPosts([newPost, ...allPosts]);
      refreshPosts();

      // Show success message
      setFormSuccess(t("admin.post.created.successfully", "Post created successfully!"));

      // Close modal after a delay
      setTimeout(() => {
        setIsCreateModalOpen(false);
        resetForm();
      }, 1500);
    } catch (error) {
      console.error('Error creating post:', error);
      setFormError(t("admin.failed.to.create", "Failed to create post. Please try again."));
    } finally {
      setFormSubmitting(false);
    }
  };

  // Update post
  const updatePost = async () => {
    if (!selectedPost) return;

    setFormSubmitting(true);
    setFormError(null);
    setFormSuccess(null);

    try {
      // Validate form
      if (!formData.title || !formData.content) {
        setFormError(t("admin.please.fill.in", "Please fill in all required fields"));
        setFormSubmitting(false);
        return;
      }

      // Create post data
      const postData = {
        title: formData.title,
        content: formData.content
      };

      // Call API to update post
      const updatedPost = await postsAPI.updatePost(selectedPost.id, postData);

      // Update local state
      setAllPosts(allPosts.map(post => post.id === updatedPost.id ? updatedPost : post));
      refreshPosts();

      // Show success message
      setFormSuccess(t("admin.post.updated.successfully", "Post updated successfully!"));

      // Close modal after a delay
      setTimeout(() => {
        setIsEditModalOpen(false);
        setSelectedPost(null);
        resetForm();
      }, 1500);
    } catch (error) {
      console.error('Error updating post:', error);
      setFormError(t("admin.failed.to.update", "Failed to update post. Please try again."));
    } finally {
      setFormSubmitting(false);
    }
  };

  return {
    posts,
    filteredPosts,
    loading,
    error,
    hasMore,
    lastElementRef,
    refreshPosts,
    loadMore,
    searchTerm,
    setSearchTerm,
    selectedPost,
    setSelectedPost,
    isDeleteModalOpen,
    setIsDeleteModalOpen,
    isCreateModalOpen,
    setIsCreateModalOpen,
    isEditModalOpen,
    setIsEditModalOpen,
    expandedPost,
    setExpandedPost,
    formError,
    setFormError,
    formSuccess,
    setFormSuccess,
    formSubmitting,
    setFormSubmitting,
    allPosts,
    setAllPosts,
    activeFilters,
    setActiveFilters,
    selectedItems,
    setSelectedItems,
    formData,
    setFormData,
    formatDate,
    handleInputChange,
    resetForm,
    openCreateModal,
    handleEditPost,
    handleDeletePost,
    createPost,
    updatePost
  };
};

export default usePostsManagement;
