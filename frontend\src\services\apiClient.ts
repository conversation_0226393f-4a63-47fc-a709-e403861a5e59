import {
  getAuthToken,
  refreshToken,
  getCsrfToken,
  clearAuthTokens,
  ApiError,
  extractErrorMessage
} from './api';
import { PaginatedResponse, QueryParams, SuccessResponse } from '../types/api';

// API base URL - dynamically set based on environment
const API_URL = import.meta.env.VITE_API_URL ||
  (window.location.hostname === 'localhost'
    ? 'http://localhost:8000/api'
    : `${window.location.origin}/api`);

// Request abort controller map for cancellation support
const abortControllers = new Map<string, AbortController>();

// Generate a unique request ID for tracking
const getRequestId = (endpoint: string, method: string, params?: QueryParams): string => {
  return `${method}:${endpoint}:${params ? JSON.stringify(params) : ''}`;
};

/**
 * Standardized API client for making requests
 */
export class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string = API_URL) {
    this.baseUrl = baseUrl;
  }

  /**
   * Make a GET request to fetch a single item
   */
  async get<T>(endpoint: string, params?: QueryParams, signal?: AbortSignal): Promise<T> {
    return this.request<T>({
      endpoint,
      method: 'GET',
      params,
      signal
    });
  }

  /**
   * Make a GET request to fetch a paginated list
   */
  async getList<T>(endpoint: string, params?: QueryParams, signal?: AbortSignal): Promise<PaginatedResponse<T>> {
    return this.request<PaginatedResponse<T>>({
      endpoint,
      method: 'GET',
      params,
      signal
    });
  }

  /**
   * Make a POST request to create a new item
   */
  async post<T>(endpoint: string, data: any, signal?: AbortSignal): Promise<T> {
    return this.request<T>({
      endpoint,
      method: 'POST',
      data,
      signal
    });
  }

  /**
   * Make a PUT request to update an item
   */
  async put<T>(endpoint: string, data: any, signal?: AbortSignal): Promise<T> {
    return this.request<T>({
      endpoint,
      method: 'PUT',
      data,
      signal
    });
  }

  /**
   * Make a PATCH request to partially update an item
   */
  async patch<T>(endpoint: string, data: any, signal?: AbortSignal): Promise<T> {
    return this.request<T>({
      endpoint,
      method: 'PATCH',
      data,
      signal
    });
  }

  /**
   * Make a DELETE request to delete an item
   */
  async delete<T>(endpoint: string, signal?: AbortSignal): Promise<T> {
    return this.request<T>({
      endpoint,
      method: 'DELETE',
      signal
    });
  }

  /**
   * Upload a file with multipart/form-data
   */
  async upload<T>(endpoint: string, formData: FormData, signal?: AbortSignal): Promise<T> {
    const requestId = getRequestId(endpoint, 'POST', {});

    // Cancel any existing request with the same ID
    if (abortControllers.has(requestId)) {
      abortControllers.get(requestId)?.abort();
      abortControllers.delete(requestId);
    }

    // Create a new abort controller for this request
    const controller = new AbortController();
    abortControllers.set(requestId, controller);

    // Combine the abort signal with any provided signal
    const abortSignal = signal
      ? this.combineAbortSignals(controller.signal, signal)
      : controller.signal;

    const url = `${this.baseUrl}${endpoint}`;
    const headers: HeadersInit = {};

    // Add JWT token if available
    const token = getAuthToken();
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: formData,
        credentials: 'include',
        signal: abortSignal
      });

      // Clean up the abort controller
      abortControllers.delete(requestId);

      if (!response.ok) {
        let errorData;
        try {
          errorData = await response.json();
        } catch (e) {
          errorData = {
            message: `File upload failed with status ${response.status}`
          };
        }

        const errorMessage = extractErrorMessage(errorData);
        throw new ApiError(errorMessage, response.status, errorData);
      }

      return await response.json();
    } catch (error) {
      // Clean up the abort controller
      abortControllers.delete(requestId);

      if (error instanceof ApiError) {
        throw error;
      }

      console.error('File upload error:', error);
      throw new ApiError(
        error instanceof Error ? error.message : 'Unknown error occurred during file upload',
        0,
        { originalError: error }
      );
    }
  }

  /**
   * Make a request to the API with support for cancellation and better error handling
   */
  private async request<T>({
    endpoint,
    method = 'GET',
    data,
    params,
    withCredentials = true,
    retryWithRefresh = true,
    signal
  }: {
    endpoint: string;
    method?: string;
    data?: any;
    params?: QueryParams;
    withCredentials?: boolean;
    retryWithRefresh?: boolean;
    signal?: AbortSignal;
  }): Promise<T> {
    // Generate a request ID for this request
    const requestId = getRequestId(endpoint, method, params);

    // Cancel any existing request with the same ID
    if (abortControllers.has(requestId)) {
      abortControllers.get(requestId)?.abort();
      abortControllers.delete(requestId);
    }

    // Create a new abort controller for this request
    const controller = new AbortController();
    abortControllers.set(requestId, controller);

    // Combine the abort signal with any provided signal
    const abortSignal = signal
      ? this.combineAbortSignals(controller.signal, signal)
      : controller.signal;

    // Build URL with query parameters
    let url = `${this.baseUrl}${endpoint}`;
    if (params && Object.keys(params).length > 0) {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value));
        }
      });
      url += `?${searchParams.toString()}`;
    }

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    // Add JWT token if available
    const token = getAuthToken();
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
    // Fallback to CSRF for non-GET requests if no JWT token
    else if (method !== 'GET') {
      const csrfToken = getCsrfToken();
      if (csrfToken) {
        headers['X-CSRFToken'] = csrfToken;
      }
    }

    const options: RequestInit = {
      method,
      headers,
      credentials: withCredentials ? 'include' : 'omit',
      body: data ? JSON.stringify(data) : undefined,
      signal: abortSignal,
    };

    try {
      const startTime = Date.now();
      const response = await fetch(url, options);
      const endTime = Date.now();

      // Log request timing in development
      if (process.env.NODE_ENV === 'development') {
        console.debug(`API ${method} ${endpoint} completed in ${endTime - startTime}ms`);
      }

      // Clean up the abort controller
      abortControllers.delete(requestId);

      // Handle token expiration
      if (response.status === 401 && retryWithRefresh && token) {
        const refreshed = await refreshToken();
        if (refreshed) {
          // Retry the request with the new token
          return this.request<T>({
            endpoint,
            method,
            data,
            params,
            withCredentials,
            retryWithRefresh: false,
            signal
          });
        }
      }

      if (!response.ok) {
        // Try to parse error response
        let errorData;
        try {
          errorData = await response.json();
        } catch (e) {
          errorData = {
            message: `API request failed with status ${response.status}`
          };
        }

        // Extract error message
        const errorMessage = extractErrorMessage(errorData);

        // Throw custom error with status and data
        throw new ApiError(
          errorMessage,
          response.status,
          errorData
        );
      }

      // For successful responses with no content
      if (response.status === 204) {
        return {} as T;
      }

      // Parse JSON response
      try {
        const jsonData = await response.json();
        return jsonData;
      } catch (e) {
        console.error('Error parsing JSON response:', e);
        throw new ApiError(
          'Invalid JSON response from server',
          response.status,
          { originalError: e }
        );
      }
    } catch (error) {
      // Clean up the abort controller
      abortControllers.delete(requestId);

      // Handle aborted requests
      if (error instanceof DOMException && error.name === 'AbortError') {
        throw new ApiError('Request was cancelled', 0, { aborted: true });
      }

      // Re-throw ApiErrors
      if (error instanceof ApiError) {
        throw error;
      }

      // Log and wrap other errors
      console.error('API request error:', error);
      throw new ApiError(
        error instanceof Error ? error.message : 'Unknown error occurred',
        0,
        { originalError: error }
      );
    }
  }

  /**
   * Combine multiple abort signals into one
   */
  private combineAbortSignals(...signals: AbortSignal[]): AbortSignal {
    const controller = new AbortController();

    for (const signal of signals) {
      if (signal.aborted) {
        controller.abort();
        break;
      }

      signal.addEventListener('abort', () => controller.abort(), { once: true });
    }

    return controller.signal;
  }

  /**
   * Cancel all pending requests
   */
  public cancelAllRequests(): void {
    abortControllers.forEach(controller => controller.abort());
    abortControllers.clear();
  }
}

// Create and export a default instance
export const apiClient = new ApiClient();

// Also export as default for convenience
export default apiClient;
