import React, { useState, useEffect } from 'react';
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Users,
  Clock,
  Star,
  Target,
  Award,
  Calendar,
  Filter,
  Download,
  RefreshCw,
  ArrowUp,
  ArrowDown,
  Minus
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { RTLText, RTLFlex } from '../common';

interface PerformanceMetric {
  name: string;
  value: number;
  change: number;
  trend: 'up' | 'down' | 'stable';
  format: 'number' | 'percentage' | 'currency' | 'time';
}

interface TemplatePerformance {
  template_id: number;
  template_name: string;
  metrics: {
    usage_count: PerformanceMetric;
    completion_rate: PerformanceMetric;
    average_rating: PerformanceMetric;
    success_rate: PerformanceMetric;
    revenue: PerformanceMetric;
    time_to_complete: PerformanceMetric;
  };
  monthly_data: Array<{
    month: string;
    usage: number;
    completions: number;
    revenue: number;
  }>;
  user_segments: Array<{
    segment: string;
    count: number;
    completion_rate: number;
  }>;
  top_sections: Array<{
    section: string;
    completion_rate: number;
    avg_time: number;
  }>;
}

interface TemplatePerformanceAnalyticsProps {
  templateId?: number;
  timeRange?: '7d' | '30d' | '90d' | '1y';
  onExportData?: (data: any) => void;
}

export const TemplatePerformanceAnalytics: React.FC<TemplatePerformanceAnalyticsProps> = ({ templateId,
  timeRange = '30d',
  onExportData
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [performance, setPerformance] = useState<TemplatePerformance | null>(null);
  const [loading, setLoading] = useState(false);
  const [selectedTimeRange, setSelectedTimeRange] = useState(timeRange);
  const [selectedSegment, setSelectedSegment] = useState('all');

  useEffect(() => {
    if (templateId) {
      fetchPerformanceData();
    }
  }, [templateId, selectedTimeRange]);

  const fetchPerformanceData = async () => {
    setLoading(true);
    try {
      if (!templateId) {
        setPerformance(null);
        return;
      }

      // Fetch real performance data from API
      const { templateAnalyticsAPI } = await import('../../services/templateAnalyticsApi');
      const analyticsData = await templateAnalyticsAPI.getTemplateAnalytics(templateId);

      if (analyticsData) {
        // Transform real analytics data to performance format
        const realPerformance: TemplatePerformance = {
          template_id: templateId,
          template_name: analyticsData.name || t("common.template", "Template"),
          metrics: {
            usage_count: {
              value: analyticsData.usage_count || 0,
              change: 0, // Calculate from historical data if available
              format: 'number'
            },
            completion_rate: {
              value: analyticsData.completion_rate || 0,
              change: 0,
              format: 'percentage'
            },
            average_rating: {
              value: analyticsData.average_rating || 0,
              change: 0,
              format: 'number'
            },
            success_rate: {
              value: analyticsData.success_rate || 0,
              change: 0,
              format: 'percentage'
            }
          },
          monthly_data: [], // Would need historical data from API
          user_segments: [], // Would need segment data from API
          top_sections: [] // Would need section analytics from API
        };

        setPerformance(realPerformance);
      } else {
        setPerformance(null);
      }
    } catch (error) {
      console.error('Error fetching performance data:', error);
      setPerformance(null);
    } finally {
      setLoading(false);
    }
  };

  const formatMetricValue = (metric: PerformanceMetric) => {
    switch (metric.format) {
      case 'percentage':
        return `${metric.value}%`;
      case 'currency':
        return `$${metric.value.toLocaleString()}`;
      case 'time':
        return `${metric.value}h`;
      default:
        return metric.value.toLocaleString();
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <ArrowUp className="text-green-400" size={16} />;
      case 'down': return <ArrowDown className="text-red-400" size={16} />;
      case 'stable': return <Minus className="text-gray-400" size={16} />;
      default: return null;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'up': return 'text-green-400';
      case 'down': return 'text-red-400';
      case 'stable': return 'text-gray-400';
      default: return 'text-gray-400';
    }
  };

  if (loading) {
    return (
      <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
        <RTLFlex className="items-center justify-center py-8">
          <RefreshCw className={`animate-spin text-purple-500 mr-3 ${isRTL ? "space-x-reverse" : ""}`} size={24} />
          <RTLText>{t('analytics.loadingPerformanceData')}</RTLText>
        </RTLFlex>
      </div>
    );
  }

  if (!performance) {
    return (
      <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
        <RTLText className="text-gray-400 text-center">
          {t('analytics.noPerformanceData')}
        </RTLText>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-900/50 to-purple-900/50 rounded-lg p-6 border border-blue-500/30">
        <RTLFlex className="items-center justify-between mb-4">
          <RTLFlex className="items-center">
            <BarChart3 className={`text-blue-400 mr-3 ${isRTL ? "space-x-reverse" : ""}`} size={24} />
            <div>
              <RTLText as="h3" className="text-xl font-bold">
                {t('analytics.performanceAnalytics')}
              </RTLText>
              <RTLText className="text-gray-300">
                {performance.template_name}
              </RTLText>
            </div>
          </RTLFlex>

          <RTLFlex className="items-center gap-3">
            <select
              value={selectedTimeRange}
              onChange={(e) => setSelectedTimeRange(e.target.value as any)}
              className="bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-white focus:border-blue-500 focus:outline-none"
            >
              <option value="7d">{t('analytics.last7Days')}</option>
              <option value="30d">{t('analytics.last30Days')}</option>
              <option value="90d">{t('analytics.last90Days')}</option>
              <option value="1y">{t('analytics.lastYear')}</option>
            </select>

            {onExportData && (
              <button
                onClick={() => onExportData(performance)}
                className={`px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-white transition-colors flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <Download size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
                {t('analytics.export')}
              </button>
            )}
          </RTLFlex>
        </RTLFlex>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Object.entries(performance.metrics).map(([key, metric]) => (
          <div
            key={key}
            className="bg-gray-800/50 rounded-lg p-6 border border-gray-700"
          >
            <RTLFlex className="items-center justify-between mb-2">
              <RTLText className="text-sm text-gray-400">
                {metric.name}
              </RTLText>
              {getTrendIcon(metric.trend)}
            </RTLFlex>

            <RTLText className="text-2xl font-bold mb-1">
              {formatMetricValue(metric)}
            </RTLText>

            <RTLFlex className="items-center">
              <span className={`text-sm ${getTrendColor(metric.trend)}`}>
                {metric.change > 0 ? '+' : ''}{metric.change}%
              </span>
              <RTLText className={`text-xs text-gray-400 ml-2 ${isRTL ? "space-x-reverse" : ""}`}>
                {t('analytics.vsLastPeriod')}
              </RTLText>
            </RTLFlex>
          </div>
        ))}
      </div>

      {/* User Segments */}
      <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
        <RTLText as="h4" className="text-lg font-semibold mb-4">
          {t('analytics.userSegments')}
        </RTLText>

        <div className="space-y-3">
          {performance.user_segments.map((segment, index) => (
            <div
              key={index}
              className="bg-gray-700/50 rounded-lg p-4 border border-gray-600"
            >
              <RTLFlex className="items-center justify-between mb-2">
                <RTLText className="font-medium">
                  {segment.segment}
                </RTLText>
                <RTLText className="text-sm text-gray-400">
                  {segment.count} {t('analytics.users')}
                </RTLText>
              </RTLFlex>

              <div className="w-full bg-gray-600 rounded-full h-2 mb-2">
                <div
                  className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${segment.completion_rate}%` }}
                ></div>
              </div>

              <RTLText className="text-sm text-gray-400">
                {segment.completion_rate}% {t('analytics.completionRate')}
              </RTLText>
            </div>
          ))}
        </div>
      </div>

      {/* Section Performance */}
      <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
        <RTLText as="h4" className="text-lg font-semibold mb-4">
          {t('analytics.sectionPerformance')}
        </RTLText>

        <div className="space-y-3">
          {performance.top_sections.map((section, index) => (
            <div
              key={index}
              className="bg-gray-700/50 rounded-lg p-4 border border-gray-600"
            >
              <RTLFlex className="items-center justify-between mb-2">
                <RTLText className="font-medium">
                  {section.section}
                </RTLText>
                <RTLFlex className="items-center gap-4 text-sm text-gray-400">
                  <RTLFlex className="items-center">
                    <Clock size={14} className={isRTL ? 'ml-1' : 'mr-1'} />
                    <span>{section.avg_time}h</span>
                  </RTLFlex>
                  <span>{section.completion_rate}%</span>
                </RTLFlex>
              </RTLFlex>

              <div className="w-full bg-gray-600 rounded-full h-2">
                <div
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${section.completion_rate}%` }}
                ></div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Monthly Trends Chart Placeholder */}
      <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
        <RTLText as="h4" className="text-lg font-semibold mb-4">
          {t('analytics.monthlyTrends')}
        </RTLText>

        <div className={`h-64 bg-gray-700/30 rounded-lg flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="text-center">
            <BarChart3 className="mx-auto mb-4 text-gray-400" size={48} />
            <RTLText className="text-gray-400">
              {t('analytics.chartPlaceholder')}
            </RTLText>
            <RTLText className="text-sm text-gray-500 mt-2">
              {t('analytics.integrateChartLibrary')}
            </RTLText>
          </div>
        </div>
      </div>
    </div>
  );
};
