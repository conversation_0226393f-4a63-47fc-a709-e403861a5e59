/**
 * AI Beyond Chat Dashboard
 * Shows real automatic AI workers in action - connected to backend!
 */

import React from 'react';
import {
  Target,
  Sparkles,
  Bot,
  AlertTriangle
} from 'lucide-react';
import { EnhancedAIDashboard } from '../../components/ai/EnhancedAIDashboard';
// MainLayout removed - handled by routing system

import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';

const AIBeyondChatPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="p-6 space-y-6">
        {/* Page Header */}
        <div className="mb-8">
          <div className={`flex items-center space-x-3 mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className={`h-12 w-12 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <Bot className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-4xl font-bold text-white">
                🤖 {t('ai.beyondChat.title', 'AI Beyond Chat')}
              </h1>
              <p className="text-lg text-gray-300">
                {t('ai.beyondChat.subtitle', 'Watch 5 AI workers automatically enhance your business ideas 24/7')}
              </p>
            </div>
          </div>

          {/* Key Benefits */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-purple-500/30">
              <div className={`flex items-center space-x-2 mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                <Sparkles className="h-6 w-6 text-purple-400" />
                <h3 className="font-semibold text-white">{t("dashboard.automatic.enhancement", "Automatic Enhancement")}</h3>
              </div>
              <p className="text-sm text-gray-300">
                AI automatically improves your business ideas without you asking
              </p>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-green-500/30">
              <div className={`flex items-center space-x-2 mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                <Target className="h-6 w-6 text-green-400" />
                <h3 className="font-semibold text-white">{t("dashboard.proactive.opportunities", "Proactive Opportunities")}</h3>
              </div>
              <p className="text-sm text-gray-300">
                AI finds market opportunities and trends you might miss
              </p>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-blue-500/30">
              <div className={`flex items-center space-x-2 mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                <AlertTriangle className="h-6 w-6 text-red-400" />
                <h3 className="font-semibold text-white">{t("dashboard.risk.prevention", "Risk Prevention")}</h3>
              </div>
              <p className="text-sm text-gray-300">
                AI identifies and prevents problems before they happen
              </p>
            </div>
          </div>

          {/* How It's Different */}
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <h2 className="text-xl font-bold text-white mb-3">
              🚀 How This is Different from Chat
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-red-400 mb-2">❌ Traditional Chat AI:</h3>
                <ul className="space-y-1 text-sm text-gray-300">
                  <li>• You have to ask questions</li>
                  <li>• Reactive responses only</li>
                  <li>• Manual work required</li>
                  <li>• Limited to conversation</li>
                </ul>
              </div>
              <div>
                <h3 className="font-semibold text-green-400 mb-2">✅ AI Beyond Chat:</h3>
                <ul className="space-y-1 text-sm text-gray-300">
                  <li>• AI works automatically 24/7</li>
                  <li>• Proactive recommendations</li>
                  <li>• No manual input needed</li>
                  <li>• Takes real actions for you</li>
                </ul>
              </div>
            </div>
          </div>
          </div>

          {/* Main Dashboard */}
          <EnhancedAIDashboard />

        {/* Footer Info */}
        <div className="mt-12 text-center">
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <h3 className="text-lg font-semibold text-white mb-2">
              🎯 Experience AI as Your Business Partner
            </h3>
            <p className="text-gray-300 max-w-2xl mx-auto">
              This is what AI should be - not just a chatbot you talk to, but an intelligent system
              that actively works to make your business ideas more successful. Watch as 5 different
              AI workers continuously enhance, analyze, and improve your business concepts automatically.
            </p>
            <div className={`mt-4 flex items-center justify-center space-x-6 text-sm text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
              <span>🤖 5 AI Workers</span>
              <span>⚡ 24/7 Operation</span>
              <span>🎯 Proactive Intelligence</span>
              <span>🚀 Automatic Enhancement</span>
            </div>
          </div>
        </div>
      </div>
              </div>
        </div>
      </div>
    </div>
  );
};

export default AIBeyondChatPage;
