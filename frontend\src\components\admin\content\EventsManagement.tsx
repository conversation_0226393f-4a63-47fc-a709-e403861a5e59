import React, { useState, useEffect } from 'react';
import { Search, Edit, Trash2, Plus, Calendar, MapPin, Users, ExternalLink, X, Check, Link } from 'lucide-react';
import { DashboardLayout } from '../dashboard';
import { Event, eventsAPI } from '../../../services/api';
import { useAppSelector } from '../../../store/hooks';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';
interface EventFormData {
  title: string;
  description: string;
  date: string;
  time: string;
  location: string;
  is_virtual: boolean;
  virtual_link: string;
}

const EventsManagement: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const { user } = useAppSelector(state => state.auth);
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [formSuccess, setFormSuccess] = useState<string | null>(null);
  const [formSubmitting, setFormSubmitting] = useState(false);

  // Form data for create/edit
  const [formData, setFormData] = useState<EventFormData>({
    title: '',
    description: '',
    date: '',
    time: '',
    location: '',
    is_virtual: false,
    virtual_link: ''
  });

  // Fetch real event data
  const fetchEvents = async () => {
    setLoading(true);
    try {
      // Fetch events from the API
      const events = await eventsAPI.getEvents();
      setEvents(events);
    } catch (error) {
      console.error('Error fetching events:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchEvents();
  }, []);

  // Filter events based on search term
  const filteredEvents = events.filter(event =>
    event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    event.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    event.location.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Format time for display
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  // Format date for input field
  const formatDateForInput = (dateString: string) => {
    const date = new Date(dateString);
    return date.toISOString().split('T')[0];
  };

  // Format time for input field
  const formatTimeForInput = (dateString: string) => {
    const date = new Date(dateString);
    return date.toTimeString().split(' ')[0].substring(0, 5);
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle checkbox change
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({ ...prev, [name]: checked }));
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      date: '',
      time: '',
      location: '',
      is_virtual: false,
      virtual_link: ''
    });
    setFormError(null);
    setFormSuccess(null);
  };

  // Open create modal
  const openCreateModal = () => {
    resetForm();
    setIsCreateModalOpen(true);
  };

  // Handle edit event
  const handleEditEvent = (event: Event) => {
    setSelectedEvent(event);
    setFormData({
      title: event.title,
      description: event.description,
      date: formatDateForInput(event.date),
      time: formatTimeForInput(event.date),
      location: event.location,
      is_virtual: event.is_virtual,
      virtual_link: event.virtual_link || ''
    });
    setIsEditModalOpen(true);
  };

  // Handle delete event
  const handleDeleteEvent = (event: Event) => {
    setSelectedEvent(event);
    setIsDeleteModalOpen(true);
  };

  // Create new event
  const createEvent = async () => {
    setFormSubmitting(true);
    setFormError(null);
    setFormSuccess(null);

    try {
      // Validate form
      if (!formData.title || !formData.description || !formData.date || !formData.time || !formData.location) {
        setFormError(t("admin.please.fill.in", "Please fill in all required fields"));
        setFormSubmitting(false);
        return;
      }

      // Combine date and time
      const dateTime = new Date(`${formData.date}T${formData.time}`);

      // Create event data
      const eventData = {
        title: formData.title,
        description: formData.description,
        date: dateTime.toISOString(),
        location: formData.location,
        is_virtual: formData.is_virtual,
        virtual_link: formData.is_virtual ? formData.virtual_link : null,
        organizer_id: user?.id || 0
      };

      // Call API to create event
      const newEvent = await eventsAPI.createEvent(eventData);

      // Update local state
      setEvents([newEvent, ...events]);

      // Show success message
      setFormSuccess(t("admin.event.created.successfully", "Event created successfully!"));

      // Close modal after a delay
      setTimeout(() => {
        setIsCreateModalOpen(false);
        resetForm();
      }, 1500);
    } catch (error) {
      console.error('Error creating event:', error);
      setFormError(t("admin.failed.to.create", "Failed to create event. Please try again."));
    } finally {
      setFormSubmitting(false);
    }
  };

  // Update event
  const updateEvent = async () => {
    if (!selectedEvent) return;

    setFormSubmitting(true);
    setFormError(null);
    setFormSuccess(null);

    try {
      // Validate form
      if (!formData.title || !formData.description || !formData.date || !formData.time || !formData.location) {
        setFormError(t("admin.please.fill.in", "Please fill in all required fields"));
        setFormSubmitting(false);
        return;
      }

      // Combine date and time
      const dateTime = new Date(`${formData.date}T${formData.time}`);

      // Create event data
      const eventData = {
        title: formData.title,
        description: formData.description,
        date: dateTime.toISOString(),
        location: formData.location,
        is_virtual: formData.is_virtual,
        virtual_link: formData.is_virtual ? formData.virtual_link : null
      };

      // Call API to update event
      const updatedEvent = await eventsAPI.updateEvent(selectedEvent.id, eventData);

      // Update local state
      setEvents(events.map(event => event.id === updatedEvent.id ? updatedEvent : event));

      // Show success message
      setFormSuccess(t("admin.event.updated.successfully", "Event updated successfully!"));

      // Close modal after a delay
      setTimeout(() => {
        setIsEditModalOpen(false);
        setSelectedEvent(null);
        resetForm();
      }, 1500);
    } catch (error) {
      console.error('Error updating event:', error);
      setFormError(t("admin.failed.to.update", "Failed to update event. Please try again."));
    } finally {
      setFormSubmitting(false);
    }
  };

  // Confirm delete event
  const confirmDeleteEvent = async () => {
    if (selectedEvent) {
      setFormSubmitting(true);
      try {
        // Call the API to delete the event
        await eventsAPI.deleteEvent(selectedEvent.id);
        // Update the local state
        setEvents(events.filter(event => event.id !== selectedEvent.id));
        setIsDeleteModalOpen(false);
        setSelectedEvent(null);
      } catch (error) {
        console.error('Error deleting event:', error);
        // Show error message to user
        alert('Failed to delete event. Please try again.');
      } finally {
        setFormSubmitting(false);
      }
    }
  };

  return (
    <DashboardLayout currentPage="events">
      <div className={`mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
        <div>
          <h1 className="text-2xl font-bold">{t("admin.events.management", "Events Management")}</h1>
          <div className="text-gray-400 mt-1">{t("admin.manage.community.events", "Manage community events and workshops")}</div>
        </div>
        <button
          onClick={openCreateModal}
          className={`mt-4 sm:mt-0 px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300 flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}
        >
          <Plus size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
          Create New Event
        </button>
      </div>

      <div className="bg-indigo-900/20 rounded-xl overflow-hidden backdrop-blur-sm shadow-lg">
        <div className="p-4 border-b border-indigo-800/50">
          <div className="relative">
            <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder={t("admin.search.events", "Search events...")}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
            />
          </div>
        </div>

        {loading ? (
          <div className={`flex justify-center items-center py-20 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className="w-12 h-12 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
            {filteredEvents.length === 0 ? (
              <div className="col-span-full text-center py-10">
                <div className="text-gray-400 text-lg">{t("admin.no.events.found", "No events found matching your search.")}</div>
              </div>
            ) : (
              filteredEvents.map((event) => (
                <div key={event.id} className="bg-indigo-900/30 rounded-xl overflow-hidden shadow-lg hover:shadow-purple-500/10 transition-all duration-300 group">
                  <div className="h-40 overflow-hidden relative bg-gradient-to-r from-purple-900/50 to-blue-900/50">
                    {event.image ? (
                      <img
                        src={event.image}
                        alt={event.title}
                        className="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-500"
                      />
                    ) : (
                      <div className={`w-full h-full flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}>
                        <Calendar size={48} className="text-purple-400/50" />
                      </div>
                    )}
                    <div className={`absolute top-0 right-0 p-2 flex space-x-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <button
                        onClick={() => handleEditEvent(event)}
                        className="p-1.5 bg-indigo-800/80 rounded-full text-white hover:bg-indigo-700/80"
                      >
                        <Edit size={16} />
                      </button>
                      <button
                        onClick={() => handleDeleteEvent(event)}
                        className="p-1.5 bg-red-800/80 rounded-full text-white hover:bg-red-700/80"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                    {event.is_virtual && (
                      <div className="absolute bottom-0 left-0 right-0 bg-blue-500/80 text-white text-center py-1 text-sm font-medium">
                        Virtual Event
                      </div>
                    )}
                  </div>
                  <div className="p-4">
                    <h3 className="text-lg font-semibold mb-2">{event.title}</h3>
                    <div className="space-y-2 mb-3">
                      <div className={`flex items-center text-gray-300 text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
                        <Calendar size={14} className={`mr-2 text-purple-400 ${isRTL ? "space-x-reverse" : ""}`} />
                        <span>{formatDate(event.date)} • {formatTime(event.date)}</span>
                      </div>
                      <div className={`flex items-center text-gray-300 text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
                        <MapPin size={14} className={`mr-2 text-purple-400 ${isRTL ? "space-x-reverse" : ""}`} />
                        <span>{event.location}</span>
                      </div>
                      <div className={`flex items-center text-gray-300 text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
                        <Users size={14} className={`mr-2 text-purple-400 ${isRTL ? "space-x-reverse" : ""}`} />
                        <span>{event.attendee_count} Attendees</span>
                      </div>
                    </div>
                    {event.is_virtual && event.virtual_link && (
                      <a
                        href={event.virtual_link}
                        target="_blank"
                        rel="noopener noreferrer"
                        className={`inline-flex items-center text-sm text-purple-400 hover:text-purple-300 ${isRTL ? "flex-row-reverse" : ""}`}
                      >
                        <ExternalLink size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                        Virtual Link
                      </a>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        )}
      </div>

      {/* Create Event Modal */}
      {isCreateModalOpen && (
        <div className={`fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70 backdrop-blur-sm overflow-y-auto ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="bg-gradient-to-b from-indigo-900/90 to-purple-900/90 rounded-xl p-6 max-w-2xl w-full my-8">
            <div className={`flex justify-between items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
              <h3 className="text-xl font-semibold">{t("admin.create.new.event", "Create New Event")}</h3>
              <button
                onClick={() => setIsCreateModalOpen(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <X size={24} />
              </button>
            </div>

            {formError && (
              <div className="mb-4 p-3 bg-red-900/50 border border-red-500 rounded-lg text-red-200">
                {formError}
              </div>
            )}

            {formSuccess && (
              <div className="mb-4 p-3 bg-green-900/50 border border-green-500 rounded-lg text-green-200">
                {formSuccess}
              </div>
            )}

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Event Title *</label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  placeholder={t("admin.enter.event.title", "Enter event title")}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Description *</label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white h-32"
                  placeholder={t("admin.enter.event.description", "Enter event description")}
                  required
                ></textarea>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Date *</label>
                  <input
                    type="date"
                    name="date"
                    value={formData.date}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Time *</label>
                  <input
                    type="time"
                    name="time"
                    value={formData.time}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Location *</label>
                <input
                  type="text"
                  name="location"
                  value={formData.location}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  placeholder={t("admin.enter.event.location", "Enter event location")}
                  required
                />
              </div>

              <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <input
                  type="checkbox"
                  id="is_virtual"
                  name="is_virtual"
                  checked={formData.is_virtual}
                  onChange={handleCheckboxChange}
                  className="w-4 h-4 bg-indigo-950 border-indigo-800 rounded focus:ring-purple-500 text-purple-600"
                />
                <label htmlFor="is_virtual" className={`ml-2 text-sm font-medium ${isRTL ? "space-x-reverse" : ""}`}>
                  This is a virtual event
                </label>
              </div>

              {formData.is_virtual && (
                <div>
                  <label className="block text-sm font-medium mb-1">{t("admin.virtual.link", "Virtual Link")}</label>
                  <input
                    type="url"
                    name="virtual_link"
                    value={formData.virtual_link}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                    placeholder={t("admin.enter.virtual.meeting", "Enter virtual meeting link")}
                  />
                </div>
              )}

              <div>
                <label className="block text-sm font-medium mb-1">{t("admin.image.upload", "Image Upload")}</label>
                <div className="text-sm text-gray-400 mb-2">
                  Image upload is not available in this version. Please contact the administrator to add images to events.
                </div>
              </div>

              <div className={`flex justify-end space-x-3 mt-6 ${isRTL ? "flex-row-reverse" : ""}`}>
                <button
                  type="button"
                  onClick={() => setIsCreateModalOpen(false)}
                  className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg"
                  disabled={formSubmitting}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={createEvent}
                  className={`px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300 flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}
                  disabled={formSubmitting}
                >
                  {formSubmitting ? (
                    <>
                      <div className={`w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 ${isRTL ? "space-x-reverse" : ""}`}></div>
                      Creating...
                    </>
                  ) : (
                    <>
                      <Plus size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                      Create Event
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit Event Modal */}
      {isEditModalOpen && selectedEvent && (
        <div className={`fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70 backdrop-blur-sm overflow-y-auto ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="bg-gradient-to-b from-indigo-900/90 to-purple-900/90 rounded-xl p-6 max-w-2xl w-full my-8">
            <div className={`flex justify-between items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
              <h3 className="text-xl font-semibold">{t("admin.edit.event", "Edit Event")}</h3>
              <button
                onClick={() => setIsEditModalOpen(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <X size={24} />
              </button>
            </div>

            {formError && (
              <div className="mb-4 p-3 bg-red-900/50 border border-red-500 rounded-lg text-red-200">
                {formError}
              </div>
            )}

            {formSuccess && (
              <div className="mb-4 p-3 bg-green-900/50 border border-green-500 rounded-lg text-green-200">
                {formSuccess}
              </div>
            )}

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Event Title *</label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  placeholder={t("admin.enter.event.title", "Enter event title")}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Description *</label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white h-32"
                  placeholder={t("admin.enter.event.description", "Enter event description")}
                  required
                ></textarea>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Date *</label>
                  <input
                    type="date"
                    name="date"
                    value={formData.date}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Time *</label>
                  <input
                    type="time"
                    name="time"
                    value={formData.time}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Location *</label>
                <input
                  type="text"
                  name="location"
                  value={formData.location}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  placeholder={t("admin.enter.event.location", "Enter event location")}
                  required
                />
              </div>

              <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <input
                  type="checkbox"
                  id="edit_is_virtual"
                  name="is_virtual"
                  checked={formData.is_virtual}
                  onChange={handleCheckboxChange}
                  className="w-4 h-4 bg-indigo-950 border-indigo-800 rounded focus:ring-purple-500 text-purple-600"
                />
                <label htmlFor="edit_is_virtual" className={`ml-2 text-sm font-medium ${isRTL ? "space-x-reverse" : ""}`}>
                  This is a virtual event
                </label>
              </div>

              {formData.is_virtual && (
                <div>
                  <label className="block text-sm font-medium mb-1">{t("admin.virtual.link", "Virtual Link")}</label>
                  <input
                    type="url"
                    name="virtual_link"
                    value={formData.virtual_link}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                    placeholder={t("admin.enter.virtual.meeting", "Enter virtual meeting link")}
                  />
                </div>
              )}

              <div>
                <label className="block text-sm font-medium mb-1">{t("admin.image.upload", "Image Upload")}</label>
                <div className="text-sm text-gray-400 mb-2">
                  Image upload is not available in this version. Please contact the administrator to add images to events.
                </div>
              </div>

              <div className={`flex justify-end space-x-3 mt-6 ${isRTL ? "flex-row-reverse" : ""}`}>
                <button
                  type="button"
                  onClick={() => setIsEditModalOpen(false)}
                  className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg"
                  disabled={formSubmitting}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={updateEvent}
                  className={`px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300 flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}
                  disabled={formSubmitting}
                >
                  {formSubmitting ? (
                    <>
                      <div className={`w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 ${isRTL ? "space-x-reverse" : ""}`}></div>
                      Updating...
                    </>
                  ) : (
                    <>
                      <Check size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                      Update Event
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {isDeleteModalOpen && selectedEvent && (
        <div className={`fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70 backdrop-blur-sm ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="bg-gradient-to-b from-indigo-900/90 to-purple-900/90 rounded-xl p-6 max-w-md w-full">
            <h3 className="text-xl font-semibold mb-4">{t("admin.confirm.deletion", "Confirm Deletion")}</h3>
            <div className="text-gray-300 mb-6">
              Are you sure you want to delete the event <span className="font-medium">{selectedEvent.title}</span>? This action cannot be undone.
            </div>
            <div className={`flex justify-end space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                onClick={() => setIsDeleteModalOpen(false)}
                className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg"
                disabled={formSubmitting}
              >
                Cancel
              </button>
              <button
                onClick={confirmDeleteEvent}
                className={`px-4 py-2 bg-red-600 hover:bg-red-500 rounded-lg flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                disabled={formSubmitting}
              >
                {formSubmitting ? (
                  <>
                    <div className={`w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 ${isRTL ? "space-x-reverse" : ""}`}></div>
                    Deleting...
                  </>
                ) : (
                  <>
                    <Trash2 size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                    Delete
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </DashboardLayout>
  );
};

export default EventsManagement;
