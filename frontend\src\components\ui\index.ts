// Export all UI components
import React from 'react';

export { Skeleton, PostSkeleton, EventSkeleton, ResourceSkeleton, SkeletonList } from './Skeleton.tsx';
export { default as LoadingFallback } from './LoadingFallback.tsx';
export { default as LazyImage } from './LazyImage.tsx';
export { default as LazyComponent } from './LazyComponent.tsx';
export { default as PreloadLink } from './PreloadLink.tsx';
export { default as PrefetchLink } from './PrefetchLink.tsx';
export { default as Button } from './Button.tsx';

// Temporary compatibility stub for ThemeWrapper during transition
// This prevents import errors while we migrate away from ThemeWrapper
export const ThemeWrapper: React.FC<any> = ({
  children,
  className = '',
  as: Component = 'div',
  darkClassName,
  lightClassName,
  ...props
}) => {
  // Use glass morphism styling instead of theme-based styling
  const finalClassName = `${className} glass-light`.trim();
  return React.createElement(Component, { className: finalClassName, ...props }, children);
};
export { default as Alert } from './Alert.tsx';
export { default as Loader } from './LoadingFallback.tsx';
export { default as ErrorDisplay, useSessionError } from './ErrorDisplay';

// New UI components for dashboard and admin pages
export { Card, CardContent, CardHeader, CardTitle } from './card';
export { Badge } from './badge';
export { Input } from './input';
export { Textarea } from './textarea';
export { Tabs, TabsContent, TabsList, TabsTrigger } from './tabs';
export { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './select';




