# Internationalization (i18n) and Arabic Language Support

This document describes the internationalization system and Arabic language support implemented in the Yasmeen AI application.

## Overview

The application supports multiple languages with full Arabic language support, including:
- ✅ **Bidirectional text support (RTL/LTR)**
- ✅ **Complete Arabic translations**
- ✅ **User language preferences**
- ✅ **Persistent language settings**
- ✅ **Cultural adaptations**

## Supported Languages

| Language | Code | Direction | Status |
|----------|------|-----------|--------|
| English  | `en` | LTR       | ✅ Complete |
| Arabic   | `ar` | RTL       | ✅ Complete |

## Architecture

### Frontend (React + i18next)

```
frontend/src/
├── i18n.ts                     # i18next configuration
├── locales/
│   ├── en/
│   │   └── translation.json    # English translations
│   ├── ar/
│   │   └── translation.json    # Arabic translations
│   └── README.md              # This file
├── components/
│   ├── common/
│   │   └── LanguageSwitcher.tsx # Language switching component
│   ├── settings/
│   │   └── LanguageSettings.tsx # User language preferences
│   └── rtl/                    # RTL-aware components
├── hooks/
│   └── useLanguage.ts          # Language state hook
├── store/
│   └── languageSlice.ts        # Redux language state
└── utils/
    ├── translationManager.ts   # Translation utilities
    └── languageTest.ts         # Testing utilities
```

### Backend (Django)

```
backend/
├── users/
│   ├── models.py              # User profile with language preference
│   ├── language_views.py      # Language API endpoints
│   └── migrations/
│       └── 0002_add_language_to_userprofile.py
├── utils/
│   └── language_utils.py      # Server-side language utilities
├── middleware/
│   └── language_middleware.py # Language detection middleware
└── locale/
    └── ar/
        └── LC_MESSAGES/
            └── django.po      # Django translations
```

## Features

### 1. Language Switching

Users can switch languages through:
- **Navigation bar language selector**
- **User settings page**
- **Automatic detection** based on browser preferences

### 2. User Preferences

Language preferences are:
- **Saved to user profile** in the database
- **Persisted across sessions**
- **Synchronized between frontend and backend**

### 3. RTL Support

The application automatically:
- **Switches text direction** (RTL for Arabic, LTR for English)
- **Adjusts layout components** using logical CSS properties
- **Handles icon flipping** where appropriate
- **Maintains proper spacing** and alignment

### 4. Cultural Adaptations

Arabic language support includes:
- **Proper date formatting** (DD/MM/YYYY)
- **Number formatting** with Arabic-Indic digits option
- **Currency display** with appropriate symbols
- **Cultural context** in translations

## Usage

### Basic Translation

```tsx
import { useTranslation } from 'react-i18next';

function MyComponent() {
  const { t } = useTranslation();
  
  return (
    <div>
      <h1>{t('welcome.title')}</h1>
      <p>{t('welcome.description')}</p>
    </div>
  );
}
```

### Language-Aware Components

```tsx
import { useLanguage } from '../hooks/useLanguage';

function MyComponent() {
  const { language, isRTL } = useLanguage();
  
  return (
    <div className={`text-${isRTL ? 'right' : 'left'}`}>
      <p>Current language: {language}</p>
    </div>
  );
}
```

### Changing Language

```tsx
import { changeLanguage } from '../utils/translationManager';

// Change to Arabic
await changeLanguage('ar');

// Change to English
await changeLanguage('en');
```

## Translation Keys Structure

Translation keys follow a hierarchical structure:

```json
{
  "app": {
    "name": "Application name",
    "tagline": "Application tagline"
  },
  "nav": {
    "home": "Home",
    "settings": "Settings"
  },
  "settings": {
    "language": {
      "title": "Language Settings",
      "description": "Choose your preferred language"
    }
  }
}
```

## Adding New Translations

### 1. Add to English file (`en/translation.json`)

```json
{
  "newFeature": {
    "title": "New Feature",
    "description": "This is a new feature"
  }
}
```

### 2. Add to Arabic file (`ar/translation.json`)

```json
{
  "newFeature": {
    "title": "ميزة جديدة",
    "description": "هذه ميزة جديدة"
  }
}
```

### 3. Use in components

```tsx
const { t } = useTranslation();
return <h1>{t('newFeature.title')}</h1>;
```

## Testing

### Manual Testing

1. **Switch languages** using the language selector
2. **Check RTL layout** when Arabic is selected
3. **Verify translations** are displayed correctly
4. **Test persistence** by refreshing the page

### Automated Testing

```typescript
import { logLanguageTestResults } from '../utils/languageTest';

// Run all language tests
await logLanguageTestResults();
```

## Best Practices

### 1. Translation Keys

- Use **descriptive, hierarchical keys**
- Avoid **hardcoded text** in components
- Provide **fallback text** for missing translations

### 2. RTL Support

- Use **logical CSS properties** (`margin-inline-start` instead of `margin-left`)
- Test **layout in both directions**
- Consider **icon directionality**

### 3. Cultural Sensitivity

- Use **appropriate terminology** for the target culture
- Consider **cultural context** in translations
- Respect **local conventions** for dates, numbers, etc.

## Troubleshooting

### Common Issues

1. **Missing translations**: Check console for missing key warnings
2. **RTL layout issues**: Verify logical CSS properties are used
3. **Language not persisting**: Check localStorage and user profile settings
4. **Icons not flipping**: Ensure RTL-aware icon components are used

### Debug Tools

```typescript
// Check current language state
console.log('Current language:', i18n.language);
console.log('Available languages:', Object.keys(i18n.store.data));

// Test translation key
console.log('Translation:', i18n.t('your.key.here'));

// Run language tests
import { runAllLanguageTests } from '../utils/languageTest';
const results = await runAllLanguageTests();
console.log('Test results:', results);
```

## Contributing

When adding new features:

1. **Add translation keys** for all user-facing text
2. **Test in both languages** (English and Arabic)
3. **Verify RTL layout** works correctly
4. **Update this documentation** if needed

## API Endpoints

### Language Preferences

- `GET /api/users/language/` - Get user's language preference
- `POST /api/users/language/` - Set user's language preference

### Request/Response Format

```json
// POST /api/users/language/
{
  "language": "ar"
}

// Response
{
  "message": "Language set to ar",
  "language": "ar"
}
```
