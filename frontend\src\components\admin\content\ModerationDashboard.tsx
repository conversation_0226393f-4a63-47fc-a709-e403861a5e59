import React, { useState, useEffect } from 'react';
import { Check, X, AlertTriangle, Search, Edit, Trash2, Refresh<PERSON>w, CheckSquare, Save, Eye } from 'lucide-react';
// DashboardLayout removed - handled by routing system with AuthenticatedLayout
import { eventsAPI, postsAPI, commentsAPI } from '../../../services/api';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';

// Define content types for moderation
type ContentType = 'events' | 'posts' | 'comments';
type ModerationStatus = 'pending' | 'approved' | 'rejected';

// Define a generic content item interface
interface ContentItem {
  id: number;
  title?: string;
  content?: string;
  moderation_status: ModerationStatus;
  moderation_comment?: string;
  created_at: string;
  updated_at?: string;
  author: {
    username: string;
    id: number;
  };
  selected?: boolean; // For batch operations
}

const ModerationDashboard: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [contentType, setContentType] = useState<ContentType>('events');
  const [items, setItems] = useState<ContentItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState<ModerationStatus | 'all'>('pending');
  const [selectedItem, setSelectedItem] = useState<ContentItem | null>(null);
  const [moderationComment, setModerationComment] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isBatchModalOpen, setIsBatchModalOpen] = useState(false);
  const [editFormData, setEditFormData] = useState<{title?: string; content?: string}>({});
  const [batchAction, setBatchAction] = useState<'approved' | 'rejected' | 'delete'>('approved');
  const [batchComment, setBatchComment] = useState('');
  const [selectAll, setSelectAll] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Fetch content based on type
  useEffect(() => {
    const fetchContent = async () => {
      setLoading(true);
      try {
        let fetchedItems: ContentItem[] = [];

        switch (contentType) {
          case 'events':
            const events = await eventsAPI.getEvents();
            fetchedItems = events.map(event => ({
              id: event.id,
              title: event.title,
              content: event.description,
              moderation_status: event.moderation_status as ModerationStatus || 'pending',
              moderation_comment: event.moderation_comment,
              created_at: event.created_at,
              updated_at: event.updated_at,
              author: {
                username: event.organizer.username,
                id: event.organizer.id
              },
              selected: false
            }));
            break;
          case 'posts':
            const posts = await postsAPI.getPosts();
            fetchedItems = posts.map(post => ({
              id: post.id,
              title: post.title,
              content: post.content,
              moderation_status: post.moderation_status as ModerationStatus || 'pending',
              moderation_comment: post.moderation_comment,
              created_at: post.created_at,
              updated_at: post.updated_at,
              author: {
                username: post.author.username,
                id: post.author.id
              },
              selected: false
            }));
            break;
          case 'comments':
            try {
              const comments = await commentsAPI.getComments();
              fetchedItems = comments.map(comment => ({
                id: comment.id,
                content: comment.content,
                moderation_status: comment.moderation_status as ModerationStatus || 'pending',
                moderation_comment: comment.moderation_comment,
                created_at: comment.created_at,
                updated_at: comment.updated_at,
                author: {
                  username: comment.author.username,
                  id: comment.author.id
                },
                selected: false
              }));
            } catch (error) {
              console.error('Error fetching comments:', error);
              fetchedItems = [];
            }
            break;
        }

        setItems(fetchedItems);
        // Reset selection when changing content type
        setSelectAll(false);
      } catch (error) {
        console.error(`Error fetching ${contentType}:`, error);
      } finally {
        setLoading(false);
      }
    };

    fetchContent();
  }, [contentType, refreshTrigger]);

  // Filter items based on search term and status filter
  const filteredItems = items.filter(item => {
    const matchesSearch =
      (item.title?.toLowerCase().includes(searchTerm.toLowerCase()) || false) ||
      (item.content?.toLowerCase().includes(searchTerm.toLowerCase()) || false) ||
      item.author.username.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesFilter = filter === 'all' || item.moderation_status === filter;

    return matchesSearch && matchesFilter;
  });

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Toggle select all items
  const toggleSelectAll = () => {
    const newSelectAll = !selectAll;
    setSelectAll(newSelectAll);
    setItems(items.map(item => ({ ...item, selected: newSelectAll })));
  };

  // Toggle select single item
  const toggleSelectItem = (id: number) => {
    setItems(items.map(item =>
      item.id === id ? { ...item, selected: !item.selected } : item
    ));

    // Update selectAll state based on whether all items are now selected
    const allSelected = items.every(item =>
      item.id === id ? !item.selected : item.selected
    );
    setSelectAll(allSelected);
  };

  // Get selected items
  const getSelectedItems = () => {
    return items.filter(item => item.selected);
  };

  // Handle single item moderation
  const handleModerate = async (status: 'approved' | 'rejected') => {
    if (!selectedItem) return;

    try {
      switch (contentType) {
        case 'events':
          await eventsAPI.moderateEvent(selectedItem.id, status, moderationComment);
          break;
        case 'posts':
          await postsAPI.moderatePost(selectedItem.id, status, moderationComment);
          break;
        case 'comments':
          await commentsAPI.moderateComment(selectedItem.id, status, moderationComment);
          break;
      }

      // Update the item in the local state
      setItems(items.map(item =>
        item.id === selectedItem.id
          ? { ...item, moderation_status: status as ModerationStatus, moderation_comment: moderationComment }
          : item
      ));

      setIsModalOpen(false);
      setSelectedItem(null);
      setModerationComment('');
    } catch (error) {
      console.error(`Error moderating ${contentType}:`, error);
    }
  };

  // Handle batch moderation
  const handleBatchModerate = async () => {
    const selectedItems = getSelectedItems();
    if (selectedItems.length === 0) return;

    try {
      // Process based on action type
      if (batchAction === 'delete') {
        // Handle batch delete
        for (const item of selectedItems) {
          switch (contentType) {
            case 'events':
              await eventsAPI.deleteEvent(item.id);
              break;
            case 'posts':
              await postsAPI.deletePost(item.id);
              break;
            case 'comments':
              await commentsAPI.deleteComment(item.id);
              break;
          }
        }
      } else {
        // Handle batch approve/reject
        for (const item of selectedItems) {
          switch (contentType) {
            case 'events':
              await eventsAPI.moderateEvent(item.id, batchAction as 'approved' | 'rejected', batchComment);
              break;
            case 'posts':
              await postsAPI.moderatePost(item.id, batchAction as 'approved' | 'rejected', batchComment);
              break;
            case 'comments':
              await commentsAPI.moderateComment(item.id, batchAction as 'approved' | 'rejected', batchComment);
              break;
          }
        }
      }

      // Refresh content after batch operation
      setRefreshTrigger(prev => prev + 1);
      setIsBatchModalOpen(false);
      setBatchComment('');
    } catch (error) {
      console.error(`Error performing batch operation:`, error);
    }
  };

  // Handle edit content
  const handleEditContent = async () => {
    if (!selectedItem) return;

    try {
      switch (contentType) {
        case 'events':
          await eventsAPI.updateEvent(selectedItem.id, {
            title: editFormData.title,
            description: editFormData.content
          });
          break;
        case 'posts':
          await postsAPI.updatePost(selectedItem.id, {
            title: editFormData.title,
            content: editFormData.content
          });
          break;
        case 'comments':
          await commentsAPI.updateComment(selectedItem.id, {
            content: editFormData.content || ''
          });
          break;
      }

      // Refresh content after edit
      setRefreshTrigger(prev => prev + 1);
      setIsEditModalOpen(false);
      setSelectedItem(null);
      setEditFormData({});
    } catch (error) {
      console.error(`Error editing ${contentType}:`, error);
    }
  };

  // Handle delete content
  const handleDeleteContent = async () => {
    if (!selectedItem) return;

    try {
      switch (contentType) {
        case 'events':
          await eventsAPI.deleteEvent(selectedItem.id);
          break;
        case 'posts':
          await postsAPI.deletePost(selectedItem.id);
          break;
        case 'comments':
          await commentsAPI.deleteComment(selectedItem.id);
          break;
      }

      // Refresh content after delete
      setRefreshTrigger(prev => prev + 1);
      setIsDeleteModalOpen(false);
      setSelectedItem(null);
    } catch (error) {
      console.error(`Error deleting ${contentType}:`, error);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900">
      <div className="px-4 sm:px-6 lg:px-8 py-6">
        <div className="max-w-7xl mx-auto">
      <div className={`mb-8 flex justify-between items-center ${isRTL ? "flex-row-reverse" : ""}`}>
        <div>
          <h1 className="text-2xl font-bold">{t("admin.content.moderation", "Content Moderation")}</h1>
          <div className="text-gray-400 mt-1">{t("admin.review.and.moderate", "Review and moderate user-generated content")}</div>
        </div>
        <div className={`flex space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
          <button
            onClick={() => setRefreshTrigger(prev => prev + 1)}
            className={`px-3 py-2 bg-indigo-800/80 rounded-lg text-white hover:bg-indigo-700/80 text-sm flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <RefreshCw size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
            Refresh
          </button>
          {getSelectedItems().length > 0 && (
            <button
              onClick={() => setIsBatchModalOpen(true)}
              className={`px-3 py-2 bg-purple-700/80 rounded-lg text-white hover:bg-purple-600/80 text-sm flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
            >
              <CheckSquare size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
              Batch Actions ({getSelectedItems().length})
            </button>
          )}
        </div>
      </div>

      <div className="bg-indigo-900/20 rounded-xl overflow-hidden backdrop-blur-sm shadow-lg">
        <div className="p-4 border-b border-indigo-800/50">
          <div className={`flex flex-col md:flex-row md:items-center md:space-x-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className={`relative flex-1 mb-4 md:mb-0 ${isRTL ? "flex-row-reverse" : ""}`}>
              <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder={t("admin.search.content", "Search content...")}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
              />
            </div>
            <div className={`flex space-x-2 mb-4 md:mb-0 ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                onClick={() => setContentType('events')}
                className={`px-3 py-1.5 rounded-lg text-sm ${
                  contentType === 'events' ? 'bg-purple-600/30 text-white' : 'bg-indigo-900/30 text-gray-300 hover:bg-indigo-800/30'}
                }`}
              >
                Events
              </button>
              <button
                onClick={() => setContentType('posts')}
                className={`px-3 py-1.5 rounded-lg text-sm ${
                  contentType === 'posts' ? 'bg-purple-600/30 text-white' : 'bg-indigo-900/30 text-gray-300 hover:bg-indigo-800/30'}
                }`}
              >
                Posts
              </button>
              <button
                onClick={() => setContentType('comments')}
                className={`px-3 py-1.5 rounded-lg text-sm ${
                  contentType === 'comments' ? 'bg-purple-600/30 text-white' : 'bg-indigo-900/30 text-gray-300 hover:bg-indigo-800/30'}
                }`}
              >
                Comments
              </button>
            </div>
            <div className={`flex space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                onClick={() => setFilter('all')}
                className={`px-3 py-1.5 rounded-lg text-sm ${
                  filter === 'all' ? 'bg-gray-600/30 text-white' : 'bg-indigo-900/30 text-gray-300 hover:bg-indigo-800/30'}
                }`}
              >
                All
              </button>
              <button
                onClick={() => setFilter('pending')}
                className={`px-3 py-1.5 rounded-lg text-sm ${
                  filter === 'pending' ? 'bg-yellow-600/30 text-white' : 'bg-indigo-900/30 text-gray-300 hover:bg-indigo-800/30'}
                }`}
              >
                Pending
              </button>
              <button
                onClick={() => setFilter('approved')}
                className={`px-3 py-1.5 rounded-lg text-sm ${
                  filter === 'approved' ? 'bg-green-600/30 text-white' : 'bg-indigo-900/30 text-gray-300 hover:bg-indigo-800/30'}
                }`}
              >
                Approved
              </button>
              <button
                onClick={() => setFilter('rejected')}
                className={`px-3 py-1.5 rounded-lg text-sm ${
                  filter === 'rejected' ? 'bg-red-600/30 text-white' : 'bg-indigo-900/30 text-gray-300 hover:bg-indigo-800/30'}
                }`}
              >
                Rejected
              </button>
            </div>
          </div>
        </div>

        {loading ? (
          <div className={`flex justify-center items-center py-20 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className="w-12 h-12 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
          </div>
        ) : (
          <>
            {/* Table Header with Select All */}
            <div className={`p-4 border-b border-indigo-800/50 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <input
                  type="checkbox"
                  checked={selectAll}
                  onChange={toggleSelectAll}
                  className={`w-4 h-4 mr-3 accent-purple-600 ${isRTL ? "space-x-reverse" : ""}`}
                />
                <span className="text-sm font-medium text-gray-300">{t("admin.select.all", "Select All")}</span>
              </div>
              <div className="ml-auto text-sm text-gray-400">
                {filteredItems.length} {contentType} found
              </div>
            </div>

            <div className="divide-y divide-indigo-800/30">
              {filteredItems.length === 0 ? (
                <div className="text-center py-10">
                  <div className="text-gray-400 text-lg">No {contentType} found matching your criteria.</div>
                </div>
              ) : (
                filteredItems.map((item) => (
                  <div key={item.id} className="p-6 hover:bg-indigo-900/10">
                    <div className={`flex justify-between items-start ${isRTL ? "flex-row-reverse" : ""}`}>
                      <div className={`flex items-start ${isRTL ? "flex-row-reverse" : ""}`}>
                        <input
                          type="checkbox"
                          checked={item.selected || false}
                          onChange={() => toggleSelectItem(item.id)}
                          className={`mt-1.5 mr-4 w-4 h-4 accent-purple-600 ${isRTL ? "space-x-reverse" : ""}`}
                        />
                        <div>
                          <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                            <h3 className="text-lg font-semibold">{item.title || `Comment #${item.id}`}</h3>
                            <span className={`ml-3 px-2 py-1 text-xs rounded-full ${
                              item.moderation_status === 'approved' ? 'bg-green-600/20 text-green-400' :
                              item.moderation_status === 'rejected' ? 'bg-red-600/20 text-red-400' :
                              'bg-yellow-600/20 text-yellow-400'}
                            }`}>
                              {item.moderation_status.charAt(0).toUpperCase() + item.moderation_status.slice(1)}
                            </span>
                          </div>
                          <div className={`flex items-center text-sm text-gray-400 mt-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                            <span>By {item.author.username}</span>
                            <span className="mx-2">•</span>
                            <span>{formatDate(item.created_at)}</span>
                            {item.moderation_comment && (
                              <>
                                <span className="mx-2">•</span>
                                <span className="italic">"{item.moderation_comment}"</span>
                              </>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className={`flex space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <button
                          onClick={() => {
                            setSelectedItem(item);
                            setIsViewModalOpen(true);
                          }}
                          className="p-1.5 bg-indigo-900/80 rounded-lg text-white hover:bg-indigo-800/80 text-sm"
                          title={t("admin.view.details", "View Details")}
                        >
                          <Eye size={16} />
                        </button>
                        <button
                          onClick={() => {
                            setSelectedItem(item);
                            setEditFormData({
                              title: item.title,
                              content: item.content
                            });
                            setIsEditModalOpen(true);
                          }}
                          className="p-1.5 bg-blue-900/80 rounded-lg text-white hover:bg-blue-800/80 text-sm"
                          title={t("admin.edit.content", "Edit Content")}
                        >
                          <Edit size={16} />
                        </button>
                        <button
                          onClick={() => {
                            setSelectedItem(item);
                            setModerationComment('');
                            setIsModalOpen(true);
                          }}
                          className="p-1.5 bg-purple-900/80 rounded-lg text-white hover:bg-purple-800/80 text-sm"
                          title={t("admin.moderate", "Moderate")}
                        >
                          <Check size={16} />
                        </button>
                        <button
                          onClick={() => {
                            setSelectedItem(item);
                            setIsDeleteModalOpen(true);
                          }}
                          className="p-1.5 bg-red-900/80 rounded-lg text-white hover:bg-red-800/80 text-sm"
                          title={t("common.delete", "Delete")}
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </div>

                    <div className={`mt-4 ml-8 ${isRTL ? "space-x-reverse" : ""}`}>
                      <div className="text-gray-300 line-clamp-2">
                        {item.content || t("admin.no.content.available", "No content available")}
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </>
        )}
      </div>

      {/* Moderation Modal */}
      {isModalOpen && selectedItem && (
        <div className={`fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70 backdrop-blur-sm ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="bg-gradient-to-b from-indigo-900/90 to-purple-900/90 rounded-xl p-6 max-w-2xl w-full">
            <h3 className="text-xl font-semibold mb-4">{t("admin.moderate.content", "Moderate Content")}</h3>

            <div className="mb-6">
              <h4 className="font-medium mb-2">{selectedItem.title || `Comment #${selectedItem.id}`}</h4>
              <div className="text-gray-300 mb-4">{selectedItem.content}</div>

              <div className="bg-indigo-950/50 p-4 rounded-lg mb-4">
                <label className="block text-sm font-medium mb-2">{t("admin.moderation.comment", "Moderation Comment")}</label>
                <textarea
                  value={moderationComment}
                  onChange={(e) => setModerationComment(e.target.value)}
                  className="w-full px-4 py-2 bg-indigo-900/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  rows={3}
                  placeholder={t("admin.add.a.comment", "Add a comment explaining your moderation decision...")}
                ></textarea>
              </div>
            </div>

            <div className={`flex justify-end space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                onClick={() => setIsModalOpen(false)}
                className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg"
              >
                Cancel
              </button>
              <button
                onClick={() => handleModerate('rejected')}
                className={`px-4 py-2 bg-red-600 hover:bg-red-500 rounded-lg flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <X size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                Reject
              </button>
              <button
                onClick={() => handleModerate('approved')}
                className={`px-4 py-2 bg-green-600 hover:bg-green-500 rounded-lg flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <Check size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                Approve
              </button>
            </div>
          </div>
        </div>
      )}

      {/* View Details Modal */}
      {isViewModalOpen && selectedItem && (
        <div className={`fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70 backdrop-blur-sm ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="bg-gradient-to-b from-indigo-900/90 to-purple-900/90 rounded-xl p-6 max-w-2xl w-full">
            <h3 className="text-xl font-semibold mb-4">{t("admin.content.details", "Content Details")}</h3>

            <div className="mb-6 space-y-4">
              {selectedItem.title && (
                <div>
                  <h4 className="text-sm font-medium text-gray-400">{t("common.title", "Title")}</h4>
                  <div className="text-white text-lg">{selectedItem.title}</div>
                </div>
              )}

              <div>
                <h4 className="text-sm font-medium text-gray-400">{t("admin.content", "Content")}</h4>
                <div className="bg-indigo-950/50 p-4 rounded-lg mt-2 max-h-60 overflow-y-auto">
                  <div className="text-white whitespace-pre-wrap">{selectedItem.content}</div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-400">{t("admin.author", "Author")}</h4>
                  <div className="text-white">{selectedItem.author.username}</div>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-gray-400">{t("common.status", "Status")}</h4>
                  <div className={`${
                    selectedItem.moderation_status === 'approved' ? 'text-green-400' :
                    selectedItem.moderation_status === 'rejected' ? 'text-red-400' :
                    'text-yellow-400'}
                  }`}>
                    {selectedItem.moderation_status.charAt(0).toUpperCase() + selectedItem.moderation_status.slice(1)}
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-gray-400">{t("admin.created", "Created")}</h4>
                  <div className="text-white">{formatDate(selectedItem.created_at)}</div>
                </div>

                {selectedItem.updated_at && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-400">{t("admin.last.updated", "Last Updated")}</h4>
                    <div className="text-white">{formatDate(selectedItem.updated_at)}</div>
                  </div>
                )}
              </div>

              {selectedItem.moderation_comment && (
                <div>
                  <h4 className="text-sm font-medium text-gray-400">{t("admin.moderation.comment", "Moderation Comment")}</h4>
                  <div className="text-white italic">"{selectedItem.moderation_comment}"</div>
                </div>
              )}
            </div>

            <div className={`flex justify-end space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                onClick={() => setIsViewModalOpen(false)}
                className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Modal */}
      {isEditModalOpen && selectedItem && (
        <div className={`fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70 backdrop-blur-sm ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="bg-gradient-to-b from-indigo-900/90 to-purple-900/90 rounded-xl p-6 max-w-2xl w-full">
            <h3 className="text-xl font-semibold mb-4">{t("admin.edit.content", "Edit Content")}</h3>

            <div className="mb-6 space-y-4">
              {contentType !== 'comments' && (
                <div>
                  <label className="block text-sm font-medium mb-2">{t("common.title", "Title")}</label>
                  <input
                    type="text"
                    value={editFormData.title || ''}
                    onChange={(e) => setEditFormData({...editFormData, title: e.target.value})}
                    className="w-full px-4 py-2 bg-indigo-900/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                    placeholder={t("admin.enter.title", "Enter title...")}
                  />
                </div>
              )}

              <div>
                <label className="block text-sm font-medium mb-2">{t("admin.content", "Content")}</label>
                <textarea
                  value={editFormData.content || ''}
                  onChange={(e) => setEditFormData({...editFormData, content: e.target.value})}
                  className="w-full px-4 py-2 bg-indigo-900/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  rows={6}
                  placeholder={t("admin.enter.content", "Enter content...")}
                ></textarea>
              </div>
            </div>

            <div className={`flex justify-end space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                onClick={() => setIsEditModalOpen(false)}
                className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg"
              >
                Cancel
              </button>
              <button
                onClick={handleEditContent}
                className={`px-4 py-2 bg-blue-600 hover:bg-blue-500 rounded-lg flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <Save size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                Save Changes
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {isDeleteModalOpen && selectedItem && (
        <div className={`fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70 backdrop-blur-sm ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="bg-gradient-to-b from-indigo-900/90 to-purple-900/90 rounded-xl p-6 max-w-md w-full">
            <div className={`flex items-center mb-4 text-red-500 ${isRTL ? "flex-row-reverse" : ""}`}>
              <AlertTriangle size={24} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
              <h3 className="text-xl font-semibold">{t("admin.confirm.deletion", "Confirm Deletion")}</h3>
            </div>

            <div className="text-gray-300 mb-6">
              {t('moderation.confirmDeletion', { type: contentType.slice(0, -1) })}
            </div>

            <div className={`flex justify-end space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                onClick={() => setIsDeleteModalOpen(false)}
                className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg"
              >
                {t('common.cancel')}
              </button>
              <button
                onClick={handleDeleteContent}
                className={`px-4 py-2 bg-red-600 hover:bg-red-500 rounded-lg flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <Trash2 size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                {t('common.delete')}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Batch Actions Modal */}
      {isBatchModalOpen && (
        <div className={`fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70 backdrop-blur-sm ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="bg-gradient-to-b from-indigo-900/90 to-purple-900/90 rounded-xl p-6 max-w-md w-full">
            <h3 className="text-xl font-semibold mb-4">{t("admin.batch.actions", "Batch Actions")}</h3>

            <div className="mb-6">
              <div className="text-gray-300 mb-4">
                {t('moderation.applyActionTo', { count: getSelectedItems().length, type: contentType })}
              </div>

              <div className="space-y-3">
                <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <input
                    type="radio"
                    id="approve"
                    name="batchAction"
                    checked={batchAction === 'approved'}
                    onChange={() => setBatchAction('approved')}
                    className="accent-green-500"
                  />
                  <label htmlFor="approve" className={`text-green-400 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    <Check size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> {t('moderation.approve')}
                  </label>
                </div>

                <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <input
                    type="radio"
                    id="reject"
                    name="batchAction"
                    checked={batchAction === 'rejected'}
                    onChange={() => setBatchAction('rejected')}
                    className="accent-red-500"
                  />
                  <label htmlFor="reject" className={`text-red-400 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    <X size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> {t('moderation.reject')}
                  </label>
                </div>

                <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <input
                    type="radio"
                    id="delete"
                    name="batchAction"
                    checked={batchAction === 'delete'}
                    onChange={() => setBatchAction('delete')}
                    className="accent-red-500"
                  />
                  <label htmlFor="delete" className={`text-red-400 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    <Trash2 size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> {t('common.delete')}
                  </label>
                </div>
              </div>

              {batchAction !== 'delete' && (
                <div className="mt-4">
                  <label className="block text-sm font-medium mb-2">{t("admin.moderation.comment", "Moderation Comment")}</label>
                  <textarea
                    value={batchComment}
                    onChange={(e) => setBatchComment(e.target.value)}
                    className="w-full px-4 py-2 bg-indigo-900/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                    rows={3}
                    placeholder={t("admin.add.a.comment", "Add a comment explaining your moderation decision...")}
                  ></textarea>
                </div>
              )}
            </div>

            <div className={`flex justify-end space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                onClick={() => setIsBatchModalOpen(false)}
                className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg"
              >
                {t('common.cancel')}
              </button>
              <button
                onClick={handleBatchModerate}
                className={`px-4 py-2 bg-purple-600 hover:bg-purple-500 rounded-lg flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <CheckSquare size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                {t('moderation.applyToItems', { count: getSelectedItems().length })}
              </button>
            </div>
          </div>
        </div>
      )}
        </div>
      </div>
    </div>
  );
};

export default ModerationDashboard;
