/**
 * Enhanced Template Analytics Dashboard
 * Advanced analytics with real-time insights and recommendations
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import {
  BarChart3,
  TrendingUp,
  Users,
  Star,
  Target,
  Award,
  Eye,
  Download,
  RefreshCw,
  Filter,
  Calendar,
  PieChart,
  LineChart,
  Activity,
  Zap,
  AlertTriangle,
  CheckCircle,
  ArrowUp,
  ArrowDown,
  Minus
} from 'lucide-react';
import { RTLText, RTLFlex } from '../common';
import {
  templateAnalyticsAPI,
  AnalyticsDashboardData,
  AnalyticsFilters
} from '../../services/templateAnalyticsApi';

interface EnhancedTemplateAnalyticsProps {
  templateId?: string;
  showFilters?: boolean;
  showExport?: boolean;
}

const EnhancedTemplateAnalytics: React.FC<EnhancedTemplateAnalyticsProps> = ({ templateId,
  showFilters = true,
  showExport = true
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [analyticsData, setAnalyticsData] = useState<AnalyticsDashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [filters, setFilters] = useState<AnalyticsFilters>({
    timeRange: '30d',
    templateId,
    includeInactive: false
  });
  const [selectedView, setSelectedView] = useState<'overview' | 'trends' | 'performance' | 'insights'>('overview');

  useEffect(() => {
    loadAnalyticsData();
  }, [filters]);

  const loadAnalyticsData = async () => {
    setLoading(true);
    try {
      const data = await templateAnalyticsAPI.getDashboardData(filters);
      setAnalyticsData(data);
    } catch (error) {
      console.error('Error loading analytics data:', error);
    } finally {
      setLoading(false);
    }
  };

  const refreshData = async () => {
    setRefreshing(true);
    await loadAnalyticsData();
    setRefreshing(false);
  };

  const exportData = async (format: 'csv' | 'xlsx' | 'pdf') => {
    try {
      const blob = await templateAnalyticsAPI.exportAnalytics(filters, format);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `template-analytics-${new Date().toISOString().split('T')[0]}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error exporting data:', error);
    }
  };

  const getChangeIndicator = (value: number) => {
    if (value > 0) return <ArrowUp size={16} className="text-green-400" />;
    if (value < 0) return <ArrowDown size={16} className="text-red-400" />;
    return <Minus size={16} className="text-gray-400" />;
  };

  const getChangeColor = (value: number) => {
    if (value > 0) return 'text-green-400';
    if (value < 0) return 'text-red-400';
    return 'text-gray-400';
  };

  if (loading) {
    return (
      <div className={`flex items-center justify-center h-64 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="text-center py-12">
        <BarChart3 size={48} className="text-gray-600 mx-auto mb-4" />
        <RTLText as="h3" className="text-xl font-semibold text-gray-400 mb-2">
          No Analytics Data Available
        </RTLText>
        <p className="text-gray-500">t("common.analytics.data.will", "Analytics data will appear here once templates are used.")</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <RTLFlex className="items-center justify-between">
        <div>
          <RTLText as="h2" className="text-2xl font-bold">
            Template Analytics Dashboard
          </RTLText>
          <p className="text-gray-400 mt-1">
            Comprehensive insights into template performance and usage
          </p>
        </div>

        <RTLFlex className="items-center space-x-4">
          {/* View Selector */}
          <div className={`flex bg-gray-800 rounded-lg p-1 ${isRTL ? "flex-row-reverse" : ""}`}>
            {[
              { id: 'overview', label: t("common.overview", "Overview"), icon: BarChart3 },
              { id: 'trends', label: t("common.trends", "Trends"), icon: TrendingUp },
              { id: 'performance', label: t("common.performance", "Performance"), icon: Target },
              { id: 'insights', label: t("common.insights", "Insights"), icon: Zap }
            ].map(view => {
              const Icon = view.icon;
              return (
                <button
                  key={view.id}
                  onClick={() => setSelectedView(view.id as any)}
                  className={`flex items-center px-3 py-2 rounded-md text-sm transition-colors ${
                    selectedView === view.id
                      ? 'bg-purple-600 text-white'
                      : 'text-gray-300 hover:text-white'}
                  }`}
                >
                  <Icon size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                  {view.label}
                </button>
              );
            })}
          </div>

          {/* Export Button */}
          {showExport && (
            <div className="relative group">
              <button className="p-2 bg-gray-800 hover:bg-gray-700 rounded-md text-gray-300 hover:text-white transition-colors">
                <Download size={16} />
              </button>
              <div className="absolute right-0 top-full mt-2 bg-gray-800 rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all z-10">
                <div className="p-2 space-y-1">
                  <button
                    onClick={() => exportData('csv')}
                    className={`block w-full  px-3 py-2 text-sm text-gray-300 hover:bg-gray-700 rounded ${isRTL ? "text-right" : "text-left"}`}
                  >
                    Export CSV
                  </button>
                  <button
                    onClick={() => exportData('xlsx')}
                    className={`block w-full  px-3 py-2 text-sm text-gray-300 hover:bg-gray-700 rounded ${isRTL ? "text-right" : "text-left"}`}
                  >
                    Export Excel
                  </button>
                  <button
                    onClick={() => exportData('pdf')}
                    className={`block w-full  px-3 py-2 text-sm text-gray-300 hover:bg-gray-700 rounded ${isRTL ? "text-right" : "text-left"}`}
                  >
                    Export PDF
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Refresh Button */}
          <button
            onClick={refreshData}
            disabled={refreshing}
            className="p-2 bg-gray-800 hover:bg-gray-700 rounded-md text-gray-300 hover:text-white transition-colors"
          >
            <RefreshCw size={16} className={refreshing ? 'animate-spin' : ''} />
          </button>
        </RTLFlex>
      </RTLFlex>

      {/* Filters */}
      {showFilters && (
        <div className="bg-gray-800/50 rounded-lg p-4">
          <RTLFlex className="items-center space-x-4">
            <div>
              <label className="block text-sm font-medium mb-1">t("common.time.range", "Time Range")</label>
              <select
                value={filters.timeRange}
                onChange={(e) => setFilters({ ...filters, timeRange: e.target.value as any })}
                className="px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-purple-500"
              >
                <option value="7d">Last 7 days</option>
                <option value="30d">Last 30 days</option>
                <option value="90d">Last 90 days</option>
                <option value="1y">t("common.last.year", "Last year")</option>
                <option value="all">t("common.all.time", "All time")</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">t("common.category", t("common.category", "Category"))</label>
              <select
                value={filters.category || ''}
                onChange={(e) => setFilters({ ...filters, category: e.target.value || undefined })}
                className="px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-purple-500"
              >
                <option value="">t("common.all.categories", "All Categories")</option>
                <option value="technology">t("common.technology", "Technology")</option>
                <option value="services">t("common.services", "Services")</option>
                <option value="retail">t("common.retail", "Retail")</option>
                <option value="hospitality">t("common.hospitality", "Hospitality")</option>
                <option value="healthcare">t("common.healthcare", "Healthcare")</option>
              </select>
            </div>

            <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <input
                type="checkbox"
                id="includeInactive"
                checked={filters.includeInactive}
                onChange={(e) => setFilters({ ...filters, includeInactive: e.target.checked })}
                className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`}
              />
              <label htmlFor="includeInactive" className="text-sm">t("common.include.inactive.templates", "Include inactive templates")</label>
            </div>
          </RTLFlex>
        </div>
      )}

      {/* Overview Cards */}
      {selectedView === 'overview' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-gray-800/50 rounded-lg p-6">
            <RTLFlex className="items-center justify-between mb-2">
              <div className="p-2 bg-blue-600/20 rounded-lg">
                <BarChart3 size={20} className="text-blue-400" />
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-blue-400">
                  {analyticsData.overview.total_templates}
                </div>
                <RTLFlex className="items-center text-sm">
                  {getChangeIndicator(5.2)}
                  <span className={getChangeColor(5.2)}>+5.2%</span>
                </RTLFlex>
              </div>
            </RTLFlex>
            <RTLText className="text-sm text-gray-400">t("common.total.templates", "Total Templates")</RTLText>
          </div>

          <div className="bg-gray-800/50 rounded-lg p-6">
            <RTLFlex className="items-center justify-between mb-2">
              <div className="p-2 bg-green-600/20 rounded-lg">
                <Users size={20} className="text-green-400" />
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-green-400">
                  {analyticsData.overview.total_usage.toLocaleString()}
                </div>
                <RTLFlex className="items-center text-sm">
                  {getChangeIndicator(analyticsData.overview.growth_rate)}
                  <span className={getChangeColor(analyticsData.overview.growth_rate)}>
                    +{analyticsData.overview.growth_rate}%
                  </span>
                </RTLFlex>
              </div>
            </RTLFlex>
            <RTLText className="text-sm text-gray-400">t("common.total.usage", "Total Usage")</RTLText>
          </div>

          <div className="bg-gray-800/50 rounded-lg p-6">
            <RTLFlex className="items-center justify-between mb-2">
              <div className="p-2 bg-yellow-600/20 rounded-lg">
                <Star size={20} className="text-yellow-400" />
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-yellow-400">
                  {analyticsData.overview.average_rating.toFixed(1)}
                </div>
                <RTLFlex className="items-center text-sm">
                  {getChangeIndicator(2.1)}
                  <span className={getChangeColor(2.1)}>+2.1%</span>
                </RTLFlex>
              </div>
            </RTLFlex>
            <RTLText className="text-sm text-gray-400">t("common.average.rating", "Average Rating")</RTLText>
          </div>

          <div className="bg-gray-800/50 rounded-lg p-6">
            <RTLFlex className="items-center justify-between mb-2">
              <div className="p-2 bg-purple-600/20 rounded-lg">
                <Target size={20} className="text-purple-400" />
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-purple-400">
                  {analyticsData.overview.completion_rate.toFixed(1)}%
                </div>
                <RTLFlex className="items-center text-sm">
                  {getChangeIndicator(3.7)}
                  <span className={getChangeColor(3.7)}>+3.7%</span>
                </RTLFlex>
              </div>
            </RTLFlex>
            <RTLText className="text-sm text-gray-400">t("common.completion.rate", "Completion Rate")</RTLText>
          </div>
        </div>
      )}

      {/* Trends View */}
      {selectedView === 'trends' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Usage Trends Chart */}
          <div className="bg-gray-800/50 rounded-lg p-6">
            <RTLText as="h3" className="text-lg font-semibold mb-4">
              Usage Trends
            </RTLText>
            <div className={`h-64 flex items-end space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
              {analyticsData.usage_trends.map((data, index) => {
                const maxValue = Math.max(...analyticsData.usage_trends.map(d => d.views));
                const height = (data.views / maxValue) * 100;

                return (
                  <div key={index} className={`flex-1 flex flex-col items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div
                      className="w-full bg-purple-600 rounded-t transition-all duration-300 hover:bg-purple-500"
                      style={{ height: `${height}%` }}
                      title={`${new Date(data.date).toLocaleDateString()}: ${data.views} views`}
                    />
                    <div className="text-xs text-gray-400 mt-2">
                      {new Date(data.date).getDate()}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Category Performance */}
          <div className="bg-gray-800/50 rounded-lg p-6">
            <RTLText as="h3" className="text-lg font-semibold mb-4">
              Category Performance
            </RTLText>
            <div className="space-y-4">
              {analyticsData.category_performance.map((category) => (
                <div key={category.category} className="space-y-2">
                  <RTLFlex className="items-center justify-between">
                    <RTLText className="font-medium">{category.category}</RTLText>
                    <div className="text-sm text-gray-400">
                      {category.total_usage} uses
                    </div>
                  </RTLFlex>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-purple-600 to-blue-600 h-2 rounded-full"
                      style={{ width: `${category.market_share}%` }}
                    />
                  </div>
                  <RTLFlex className="items-center justify-between text-xs text-gray-400">
                    <span>{category.template_count} templates</span>
                    <RTLFlex className="items-center">
                      <Star size={12} className={`text-yellow-400 mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                      {category.average_rating.toFixed(1)}
                      <span className={`ml-2 ${isRTL ? "space-x-reverse" : ""}`}>
                        {getChangeIndicator(category.growth_rate)}
                        <span className={getChangeColor(category.growth_rate)}>
                          {category.growth_rate > 0 ? '+' : ''}{category.growth_rate.toFixed(1)}%
                        </span>
                      </span>
                    </RTLFlex>
                  </RTLFlex>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Performance View */}
      {selectedView === 'performance' && (
        <div className="space-y-6">
          {/* Top Performing Templates */}
          <div className="bg-gray-800/50 rounded-lg p-6">
            <RTLText as="h3" className="text-lg font-semibold mb-4">
              Top Performing Templates
            </RTLText>
            <div className="space-y-4">
              {analyticsData.top_templates.slice(0, 5).map((template, index) => (
                <div key={template.id} className={`flex items-center justify-between p-4 bg-gray-700/50 rounded-lg ${isRTL ? "flex-row-reverse" : ""}`}>
                  <RTLFlex className="items-center">
                    <div className={`w-8 h-8 bg-purple-600/20 rounded-lg flex items-center justify-center mr-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <span className="text-sm font-bold text-purple-400">#{index + 1}</span>
                    </div>
                    <div>
                      <RTLText className="font-medium">{template.name}</RTLText>
                      <div className="text-sm text-gray-400">{template.category}</div>
                    </div>
                  </RTLFlex>
                  <div className="text-right">
                    <div className="text-sm font-medium">{template.usage_count} uses</div>
                    <RTLFlex className="items-center text-xs text-gray-400">
                      <Star size={12} className={`text-yellow-400 mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                      {template.average_rating.toFixed(1)}
                      <span className="mx-2">•</span>
                      {template.completion_rate.toFixed(1)}% completed
                    </RTLFlex>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* User Segments */}
          <div className="bg-gray-800/50 rounded-lg p-6">
            <RTLText as="h3" className="text-lg font-semibold mb-4">
              User Segment Analysis
            </RTLText>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {analyticsData.user_segments.map((segment) => (
                <div key={segment.segment} className="bg-gray-700/50 rounded-lg p-4">
                  <RTLText className="font-medium mb-2">{segment.segment}</RTLText>
                  <div className="space-y-2 text-sm">
                    <RTLFlex className="justify-between">
                      <span className="text-gray-400">t("common.users", "Users:")</span>
                      <span>{segment.user_count.toLocaleString()}</span>
                    </RTLFlex>
                    <RTLFlex className="justify-between">
                      <span className="text-gray-400">t("common.completion.rate", "Completion Rate:")</span>
                      <span>{segment.completion_rate.toFixed(1)}%</span>
                    </RTLFlex>
                    <RTLFlex className="justify-between">
                      <span className="text-gray-400">t("common.satisfaction", "Satisfaction:")</span>
                      <RTLFlex className="items-center">
                        <Star size={12} className={`text-yellow-400 mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                        {segment.satisfaction_score.toFixed(1)}
                      </RTLFlex>
                    </RTLFlex>
                    <RTLFlex className="justify-between">
                      <span className="text-gray-400">t("common.retention", "Retention:")</span>
                      <span>{segment.retention_rate.toFixed(1)}%</span>
                    </RTLFlex>
                  </div>
                  <div className="mt-3">
                    <div className="text-xs text-gray-400 mb-1">t("common.top.templates", "Top Templates:")</div>
                    <div className={`flex flex-wrap gap-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                      {segment.template_preferences.slice(0, 3).map((template) => (
                        <span key={template} className="px-2 py-1 bg-purple-600/20 text-purple-400 text-xs rounded">
                          {template}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Insights View */}
      {selectedView === 'insights' && (
        <div className="space-y-6">
          {/* Recommendations */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-gray-800/50 rounded-lg p-6">
              <RTLFlex className="items-center mb-4">
                <TrendingUp size={20} className={`text-green-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                <RTLText as="h3" className="text-lg font-semibold">t("common.trending.templates", "Trending Templates")</RTLText>
              </RTLFlex>
              <div className="space-y-2">
                {analyticsData.recommendations.trending_templates.map((template) => (
                  <div key={template} className={`flex items-center p-2 bg-green-600/10 rounded ${isRTL ? "flex-row-reverse" : ""}`}>
                    <CheckCircle size={16} className={`text-green-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                    <span className="text-sm">{template}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-gray-800/50 rounded-lg p-6">
              <RTLFlex className="items-center mb-4">
                <AlertTriangle size={20} className={`text-yellow-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                <RTLText as="h3" className="text-lg font-semibold">t("common.needs.attention", "Needs Attention")</RTLText>
              </RTLFlex>
              <div className="space-y-2">
                {analyticsData.recommendations.underperforming_templates.map((template) => (
                  <div key={template} className={`flex items-center p-2 bg-yellow-600/10 rounded ${isRTL ? "flex-row-reverse" : ""}`}>
                    <AlertTriangle size={16} className={`text-yellow-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                    <span className="text-sm">{template}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-gray-800/50 rounded-lg p-6">
              <RTLFlex className="items-center mb-4">
                <Zap size={20} className={`text-purple-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                <RTLText as="h3" className="text-lg font-semibold">t("common.optimization", "Optimization")</RTLText>
              </RTLFlex>
              <div className="space-y-2">
                {analyticsData.recommendations.optimization_opportunities.map((opportunity, index) => (
                  <div key={index} className={`flex items-start p-2 bg-purple-600/10 rounded ${isRTL ? "flex-row-reverse" : ""}`}>
                    <Zap size={16} className={`text-purple-400 mr-2 mt-0.5 flex-shrink-0 ${isRTL ? "flex-row-reverse" : ""}`} />
                    <span className="text-sm">{opportunity}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedTemplateAnalytics;
