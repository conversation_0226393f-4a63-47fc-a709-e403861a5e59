from django.core.management.base import BaseCommand
from django.db import transaction
from users.models import UserRole


class Command(BaseCommand):
    help = 'Synchronize all required roles with the database'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force-update',
            action='store_true',
            help='Force update existing roles with new values',
        )
        parser.add_argument(
            '--verify-only',
            action='store_true',
            help='Only verify roles without making changes',
        )

    def handle(self, *args, **options):
        self.stdout.write('🔄 Starting role synchronization...')
        
        if options['verify_only']:
            self.verify_roles()
            return
        
        # Define all required roles with exact names and properties
        required_roles = [
            {
                'name': 'super_admin',
                'display_name': 'Super Administrator',
                'description': 'Complete system control with highest level access to all platform features, system management, user impersonation, AI system control, and advanced analytics.',
                'permission_level': 'super_admin',
                'is_active': True,
                'requires_approval': False
            },
            {
                'name': 'admin',
                'display_name': 'Administrator',
                'description': 'Full system administrator with all permissions except super admin functions',
                'permission_level': 'admin',
                'is_active': True,
                'requires_approval': False
            },
            {
                'name': 'moderator',
                'display_name': 'Moderator',
                'description': 'Content moderator with moderation permissions',
                'permission_level': 'moderate',
                'is_active': True,
                'requires_approval': True
            },
            {
                'name': 'mentor',
                'display_name': 'Mentor',
                'description': 'Business mentor providing guidance to entrepreneurs',
                'permission_level': 'write',
                'is_active': True,
                'requires_approval': True
            },
            {
                'name': 'investor',
                'display_name': 'Investor',
                'description': 'Investor with access to investment opportunities and analytics',
                'permission_level': 'write',
                'is_active': True,
                'requires_approval': True
            },
            {
                'name': 'user',
                'display_name': 'Regular User',
                'description': 'Standard user with basic permissions',
                'permission_level': 'write',
                'is_active': True,
                'requires_approval': False
            }
        ]
        
        created_count = 0
        updated_count = 0
        
        with transaction.atomic():
            for role_data in required_roles:
                role, created = UserRole.objects.get_or_create(
                    name=role_data['name'],
                    defaults=role_data
                )
                
                if created:
                    created_count += 1
                    self.stdout.write(
                        self.style.SUCCESS(f'✅ Created role: {role.name} ({role.display_name})')
                    )
                elif options['force_update']:
                    # Update existing role to ensure consistency
                    updated = False
                    for field, value in role_data.items():
                        if field != 'name' and getattr(role, field) != value:
                            setattr(role, field, value)
                            updated = True
                    
                    if updated:
                        role.save()
                        updated_count += 1
                        self.stdout.write(
                            self.style.WARNING(f'🔄 Updated role: {role.name} ({role.display_name})')
                        )
                    else:
                        self.stdout.write(f'✓ Role unchanged: {role.name} ({role.display_name})')
                else:
                    self.stdout.write(f'✓ Role exists: {role.name} ({role.display_name})')
        
        self.stdout.write(
            self.style.SUCCESS(
                f'\n📊 Role synchronization complete! Created: {created_count}, Updated: {updated_count}'
            )
        )
        
        # Verify after sync
        self.verify_roles()

    def verify_roles(self):
        """Verify all roles exist and have correct properties"""
        self.stdout.write('\n🔍 Verifying role consistency...')
        
        expected_roles = ['super_admin', 'admin', 'moderator', 'mentor', 'investor', 'user']
        existing_roles = list(UserRole.objects.values_list('name', flat=True))
        
        self.stdout.write(f'Expected roles: {expected_roles}')
        self.stdout.write(f'Existing roles: {existing_roles}')
        
        missing_roles = set(expected_roles) - set(existing_roles)
        extra_roles = set(existing_roles) - set(expected_roles)
        
        if missing_roles:
            self.stdout.write(
                self.style.ERROR(f'❌ Missing roles: {missing_roles}')
            )
        
        if extra_roles:
            self.stdout.write(
                self.style.WARNING(f'⚠️  Extra roles found: {extra_roles}')
            )
        
        # Display all roles
        self.stdout.write('\n📋 Current roles in database:')
        for role in UserRole.objects.all().order_by('name'):
            status = "✅" if role.is_active else "❌"
            approval = "Auto" if not role.requires_approval else "Manual"
            self.stdout.write(
                f'  {status} {role.name}: {role.display_name} '
                f'({role.permission_level}) - Approval: {approval}'
            )
        
        if not missing_roles:
            self.stdout.write(self.style.SUCCESS('\n✅ All required roles are present!'))
        
        return len(missing_roles) == 0
