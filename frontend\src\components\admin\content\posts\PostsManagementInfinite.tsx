import React, { useState, useEffect, useCallback } from 'react';
import { Search, RefreshCw } from 'lucide-react';
import { DashboardLayout } from '../../dashboard';
import { Post, postsAPI } from '../../../../services/api';
import { useAppSelector } from '../../../../store/hooks';
import { AdvancedFilter, BulkActions } from '../../common';
import { getPostBulkActions } from '../../common/BulkActions';
import useInfiniteScroll from '../../../../hooks/useInfiniteScroll';
import { PostSkeleton, SkeletonList } from '../../../ui/Skeleton';
import {
  PostItem,
  PostCreateModal,
  PostEditModal,
  PostDeleteModal,
  PostsHeader
} from './components';
import { usePostActions, usePostFilters } from './hooks';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../hooks/useLanguage';

const PostsManagementInfinite: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const { user } = useAppSelector(state => state.auth);
  const [allPosts, setAllPosts] = useState<Post[]>([]);
  const [expandedPost, setExpandedPost] = useState<number | null>(null);
  const [selectedItems, setSelectedItems] = useState<number[]>([]);

  // Fetch posts with pagination
  const fetchPaginatedPosts = useCallback(async (page: number, limit: number) => {
    try {
      // In a real implementation, you would call an API that supports pagination
      // For now, we'll simulate pagination by slicing the allPosts array
      const start = (page - 1) * limit;
      const end = start + limit;

      // If this is the first page, fetch all posts first
      if (page === 1 && allPosts.length === 0) {
        const posts = await postsAPI.getPosts();
        setAllPosts(posts);
        return posts.slice(0, limit);
      }

      return allPosts.slice(start, end);
    } catch (error) {
      console.error('Error fetching paginated posts:', error);
      throw error;
    }
  }, [allPosts]);

  // Use the infinite scroll hook
  const {
    data: posts,
    loading,
    error,
    hasMore,
    lastElementRef,
    refresh: refreshPosts,
    loadMore
  } = useInfiniteScroll<Post>({
    fetchData: fetchPaginatedPosts,
    limit: 10,
    enabled: !!user?.id,
  });

  // Use custom hooks for post actions and filtering
  const {
    formData,
    formError,
    formSuccess,
    formSubmitting,
    selectedPost,
    isCreateModalOpen,
    isEditModalOpen,
    isDeleteModalOpen,
    handleInputChange,
    openCreateModal,
    closeCreateModal,
    handleEditPost,
    closeEditModal,
    handleDeletePost,
    closeDeleteModal,
    createPost,
    updatePost,
    confirmDeletePost,
    preventFormSubmission
  } = usePostActions(refreshPosts, allPosts, setAllPosts);

  const {
    searchTerm,
    setSearchTerm,
    activeFilters,
    handleFilterChange,
    filteredPosts,
    postFilterOptions
  } = usePostFilters(posts);

  // Effect to refresh posts when filters change
  useEffect(() => {
    if (activeFilters.length > 0) {
      refreshPosts();
    }
  }, [activeFilters, refreshPosts]);

  // Toggle expanded post
  const toggleExpandPost = (postId: number) => {
    setExpandedPost(expandedPost === postId ? null : postId);
  };

  // Toggle item selection
  const toggleItemSelection = (id: number) => {
    setSelectedItems(prev =>
      prev.includes(id)
        ? prev.filter(itemId => itemId !== id)
        : [...prev, id]
    );
  };

  // Deselect all items
  const handleDeselectAll = () => {
    setSelectedItems([]);
  };

  // Bulk action handlers
  const handleBulkApprove = async (ids: number[]): Promise<void> => {
    try {
      // Process posts in batches of 5 for better performance
      const batchSize = 5;
      const batches = [];

      for (let i = 0; i < ids.length; i += batchSize) {
        batches.push(ids.slice(i, i + batchSize));
      }

      // Process each batch sequentially
      for (const batch of batches) {
        await Promise.all(
          batch.map(id => postsAPI.moderatePost(id, 'approved', 'Bulk approved by admin'))
        );
      }

      // Refresh posts after bulk action
      refreshPosts();
      setSelectedItems([]);
      return Promise.resolve();
    } catch (error) {
      console.error('Error approving posts:', error);
      return Promise.reject(error);
    }
  };

  const handleBulkReject = async (ids: number[]): Promise<void> => {
    try {
      // Process posts in batches of 5 for better performance
      const batchSize = 5;
      const batches = [];

      for (let i = 0; i < ids.length; i += batchSize) {
        batches.push(ids.slice(i, i + batchSize));
      }

      // Process each batch sequentially
      for (const batch of batches) {
        await Promise.all(
          batch.map(id => postsAPI.moderatePost(id, 'rejected', 'Bulk rejected by admin'))
        );
      }

      // Refresh posts after bulk action
      refreshPosts();
      setSelectedItems([]);
      return Promise.resolve();
    } catch (error) {
      console.error('Error rejecting posts:', error);
      return Promise.reject(error);
    }
  };

  const handleBulkDelete = async (ids: number[]): Promise<void> => {
    try {
      // Process posts in batches of 5 for better performance
      const batchSize = 5;
      const batches = [];

      for (let i = 0; i < ids.length; i += batchSize) {
        batches.push(ids.slice(i, i + batchSize));
      }

      // Process each batch sequentially
      for (const batch of batches) {
        await Promise.all(
          batch.map(id => postsAPI.deletePost(id))
        );
      }

      // Update local state
      setAllPosts(allPosts.filter(post => !ids.includes(post.id)));
      refreshPosts();
      setSelectedItems([]);
      return Promise.resolve();
    } catch (error) {
      console.error('Error deleting posts:', error);
      return Promise.reject(error);
    }
  };

  // Load empty data as fallback
  const loadMockData = () => {
    const mockPosts: Post[] = [];

    setAllPosts(mockPosts);
    refreshPosts();
  };

  return (
    <DashboardLayout currentPage="posts">
      <PostsHeader
        onCreateClick={openCreateModal}
        preventFormSubmission={preventFormSubmission}
      />

      {/* Advanced Filter Component */}
      <AdvancedFilter
        filterOptions={postFilterOptions}
        onFilterChange={handleFilterChange}
        activeFilters={activeFilters}
      />

      {/* Search and Bulk Actions */}
      <div className={`mb-6 flex flex-col sm:flex-row justify-between items-start gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className="relative w-full sm:w-64">
          <input
            type="text"
            placeholder={t("admin.search.posts", "Search posts...")}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 bg-indigo-900/30 border border-indigo-800 rounded-lg text-white focus:outline-none focus:border-purple-500"
          />
          <Search className="absolute left-3 top-2.5 text-gray-400" size={18} />
        </div>

        {selectedItems.length > 0 && (
          <BulkActions
            selectedCount={selectedItems.length}
            onDeselectAll={handleDeselectAll}
            actions={getPostBulkActions(handleBulkApprove, handleBulkReject, handleBulkDelete)}
            selectedIds={selectedItems}
          />
        )}
      </div>

      <div className="bg-indigo-900/20 rounded-xl overflow-hidden shadow-lg">
        {error && (
          <div className="p-4 bg-red-900/50 border border-red-800 text-white rounded-lg mb-4">
            <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <span className={`mr-2 text-red-400 ${isRTL ? "space-x-reverse" : ""}`}>{t("admin.error", "Error:")}</span>
              {error}
            </div>
            <button
              onClick={loadMockData}
              className="mt-2 px-4 py-2 bg-indigo-700 hover:bg-indigo-600 rounded-lg text-white"
            >
              Show Sample Data Instead
            </button>
          </div>
        )}

        {loading && posts.length === 0 ? (
          <SkeletonList count={5} SkeletonComponent={PostSkeleton} />
        ) : (
          <div className="divide-y divide-indigo-800/30">
            {filteredPosts.length === 0 ? (
              <div className="text-center py-10">
                <div className="text-gray-400 text-lg">{t("admin.no.posts.found", "No posts found matching your search.")}</div>
              </div>
            ) : (
              <>
                {filteredPosts.map((post, index) => (
                  <PostItem
                    key={post.id}
                    post={post}
                    isExpanded={expandedPost === post.id}
                    isSelected={selectedItems.includes(post.id)}
                    onToggleExpand={toggleExpandPost}
                    onToggleSelect={toggleItemSelection}
                    onEdit={handleEditPost}
                    onDelete={handleDeletePost}
                    ref={index === filteredPosts.length - 1 ? lastElementRef : undefined}
                  />
                ))}

                {/* Loading indicator at the bottom for infinite scroll */}
                {loading && posts.length > 0 && (
                  <div className={`flex justify-center py-6 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className="w-8 h-8 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
                  </div>
                )}

                {/* Load more button as fallback */}
                {hasMore && !loading && (
                  <div className={`flex justify-center py-6 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <button
                      onClick={loadMore}
                      className={`px-4 py-2 bg-indigo-700 hover:bg-indigo-600 rounded-lg text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                    >
                      <RefreshCw size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                      Load More
                    </button>
                  </div>
                )}
              </>
            )}
          </div>
        )}
      </div>

      {/* Modals */}
      <PostCreateModal
        isOpen={isCreateModalOpen}
        onClose={closeCreateModal}
        formData={formData}
        formError={formError}
        formSuccess={formSuccess}
        formSubmitting={formSubmitting}
        onInputChange={handleInputChange}
        onSubmit={createPost}
        preventFormSubmission={preventFormSubmission}
      />

      <PostEditModal
        isOpen={isEditModalOpen}
        onClose={closeEditModal}
        selectedPost={selectedPost}
        formData={formData}
        formError={formError}
        formSuccess={formSuccess}
        formSubmitting={formSubmitting}
        onInputChange={handleInputChange}
        onSubmit={updatePost}
        preventFormSubmission={preventFormSubmission}
      />

      <PostDeleteModal
        isOpen={isDeleteModalOpen}
        onClose={closeDeleteModal}
        selectedPost={selectedPost}
        formError={formError}
        formSubmitting={formSubmitting}
        onConfirmDelete={confirmDeletePost}
      />
    </DashboardLayout>
  );
};

export default PostsManagementInfinite;
