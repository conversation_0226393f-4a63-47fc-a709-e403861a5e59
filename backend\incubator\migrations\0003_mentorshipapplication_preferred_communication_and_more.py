# Generated by Django 5.1.7 on 2025-05-15 13:35

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('incubator', '0002_fundingopportunity_fundingapplication_investment_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='mentorshipapplication',
            name='preferred_communication',
            field=models.CharField(choices=[('video', 'Video Call'), ('phone', 'Phone Call'), ('email', 'Email'), ('chat', 'Chat/Messaging'), ('in_person', 'In Person')], default='video', max_length=20),
        ),
        migrations.AddField(
            model_name='mentorshipapplication',
            name='preferred_expertise',
            field=models.CharField(blank=True, choices=[('business_strategy', 'Business Strategy'), ('marketing', 'Marketing & Sales'), ('finance', 'Finance & Accounting'), ('operations', 'Operations & Logistics'), ('technology', 'Technology & Development'), ('product', 'Product Management'), ('legal', 'Legal & Compliance'), ('hr', 'Human Resources'), ('fundraising', 'Fundraising & Investment'), ('international', 'International Business'), ('ecommerce', 'E-Commerce'), ('social_impact', 'Social Impact'), ('other', 'Other')], max_length=50, null=True),
        ),
        migrations.CreateModel(
            name='MentorProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bio', models.TextField(help_text='Professional background and experience')),
                ('company', models.CharField(blank=True, max_length=200, null=True)),
                ('position', models.CharField(blank=True, max_length=200, null=True)),
                ('years_of_experience', models.PositiveIntegerField(default=0)),
                ('linkedin_profile', models.URLField(blank=True, null=True)),
                ('website', models.URLField(blank=True, null=True)),
                ('availability', models.CharField(choices=[('high', 'High - 5+ hours per week'), ('medium', 'Medium - 2-5 hours per week'), ('low', 'Low - 1-2 hours per week'), ('limited', 'Limited - Less than 1 hour per week')], default='medium', max_length=20)),
                ('max_mentees', models.PositiveIntegerField(default=3, help_text='Maximum number of mentees willing to take on')),
                ('is_accepting_mentees', models.BooleanField(default=True)),
                ('is_verified', models.BooleanField(default=False, help_text='Whether the mentor has been verified by administrators')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='mentor_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='mentorshipapplication',
            name='preferred_mentor',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='preferred_applications', to='incubator.mentorprofile'),
        ),
        migrations.CreateModel(
            name='MentorshipMatch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('active', 'Active'), ('completed', 'Completed'), ('paused', 'Paused'), ('terminated', 'Terminated')], default='active', max_length=20)),
                ('start_date', models.DateField(auto_now_add=True)),
                ('end_date', models.DateField(blank=True, null=True)),
                ('goals', models.TextField()),
                ('focus_areas', models.TextField()),
                ('mentee_notes', models.TextField(blank=True, help_text='Private notes for the mentee', null=True)),
                ('mentor_notes', models.TextField(blank=True, help_text='Private notes for the mentor', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('application', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='resulting_match', to='incubator.mentorshipapplication')),
                ('business_idea', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='mentorship_matches', to='incubator.businessidea')),
                ('mentee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='mentorship_matches', to=settings.AUTH_USER_MODEL)),
                ('mentor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='mentorship_matches', to='incubator.mentorprofile')),
            ],
            options={
                'verbose_name_plural': 'Mentorship Matches',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='MentorshipSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True, null=True)),
                ('scheduled_at', models.DateTimeField()),
                ('duration_minutes', models.PositiveIntegerField(default=60)),
                ('location', models.CharField(blank=True, help_text='Physical location or virtual meeting link', max_length=200, null=True)),
                ('status', models.CharField(choices=[('scheduled', 'Scheduled'), ('completed', 'Completed'), ('cancelled', 'Cancelled'), ('rescheduled', 'Rescheduled')], default='scheduled', max_length=20)),
                ('mentor_notes', models.TextField(blank=True, null=True)),
                ('mentee_notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('mentorship_match', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sessions', to='incubator.mentorshipmatch')),
            ],
            options={
                'ordering': ['-scheduled_at'],
            },
        ),
        migrations.CreateModel(
            name='MentorshipFeedback',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_from_mentee', models.BooleanField(default=True)),
                ('rating', models.PositiveSmallIntegerField(choices=[(1, '1 - Poor'), (2, '2 - Below Average'), (3, '3 - Average'), (4, '4 - Good'), (5, '5 - Excellent')])),
                ('comments', models.TextField()),
                ('areas_of_improvement', models.TextField(blank=True, null=True)),
                ('is_private', models.BooleanField(default=False, help_text='If true, feedback is only visible to administrators')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('provided_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='provided_feedback', to=settings.AUTH_USER_MODEL)),
                ('session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='feedback', to='incubator.mentorshipsession')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='MentorExpertise',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('category', models.CharField(choices=[('business_strategy', 'Business Strategy'), ('marketing', 'Marketing & Sales'), ('finance', 'Finance & Accounting'), ('operations', 'Operations & Logistics'), ('technology', 'Technology & Development'), ('product', 'Product Management'), ('legal', 'Legal & Compliance'), ('hr', 'Human Resources'), ('fundraising', 'Fundraising & Investment'), ('international', 'International Business'), ('ecommerce', 'E-Commerce'), ('social_impact', 'Social Impact'), ('other', 'Other')], max_length=50)),
                ('specific_expertise', models.CharField(help_text='Specific expertise within this category', max_length=200)),
                ('level', models.CharField(choices=[('beginner', 'Beginner'), ('intermediate', 'Intermediate'), ('advanced', 'Advanced'), ('expert', 'Expert')], default='intermediate', max_length=20)),
                ('years_experience', models.PositiveIntegerField(default=0)),
                ('mentor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='expertise_areas', to='incubator.mentorprofile')),
            ],
            options={
                'ordering': ['category', '-level'],
                'unique_together': {('mentor', 'category', 'specific_expertise')},
            },
        ),
    ]
