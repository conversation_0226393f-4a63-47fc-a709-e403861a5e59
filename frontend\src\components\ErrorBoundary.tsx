import React, { Component, ErrorInfo, ReactNode } from 'react';
import { withTranslation, WithTranslation } from 'react-i18next';

interface Props extends WithTranslation {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log the error details in development only
    if (import.meta.env.DEV) {
      console.error('ErrorBoundary caught an error:', error, errorInfo);

      // Check if this is the specific current_role error
      if (error.message.includes('current_role')) {
        console.error('CURRENT_ROLE ERROR DETECTED:', {
          message: error.message,
          stack: error.stack,
          componentStack: errorInfo.componentStack
        });
      }
    }

    this.setState({
      error,
      errorInfo
    });
  }

  render() {
    const { t } = this.props;

    if (this.state.hasError) {
      // Fallback UI
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
          <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <div className="text-center">
              <div className="text-red-500 text-6xl mb-4">⚠️</div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                {t('errors.somethingWentWrong')}
              </h1>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                {t('errors.errorOccurred')}
              </p>

              {process.env.NODE_ENV === 'development' && this.state.error && (
                <div className="text-left bg-gray-100 dark:bg-gray-700 p-4 rounded-lg mb-4">
                  <h3 className="font-semibold text-red-600 dark:text-red-400 mb-2">
                    {t('errors.errorDetails')}
                  </h3>
                  <p className="text-sm text-gray-800 dark:text-gray-200 font-mono">
                    {this.state.error.message}
                  </p>
                  {this.state.error.stack && (
                    <details className="mt-2">
                      <summary className="cursor-pointer text-sm text-gray-600 dark:text-gray-400">
                        {t('errors.stackTrace')}
                      </summary>
                      <pre className="text-xs text-gray-700 dark:text-gray-300 mt-2 overflow-auto">
                        {this.state.error.stack}
                      </pre>
                    </details>
                  )}
                </div>
              )}

              <button
                onClick={() => window.location.reload()}
                className="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition-colors"
              >
                {t('errors.reloadPage')}
              </button>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default withTranslation()(ErrorBoundary);
