import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Zap, Clock, Play, Pause, Settings, Plus, Edit,
  Trash2, CheckCircle, XCircle, AlertTriangle,
  Calendar, RefreshCw, Download, Filter, Search,
  Activity, BarChart3, Users, Database, Mail
} from 'lucide-react';

import { superAdminApi } from '../../../services/superAdminApi';

interface AutomatedTask {
  id: string;
  name: string;
  description: string;
  type: 'backup' | 'cleanup' | 'email' | 'report' | 'maintenance' | 'security';
  status: 'active' | 'paused' | 'error' | 'completed';
  schedule: string;
  last_run: string;
  next_run: string;
  success_rate: number;
  execution_count: number;
  created_at: string;
  enabled: boolean;
}

interface WorkflowStep {
  id: string;
  name: string;
  type: 'condition' | 'action' | 'delay' | 'notification';
  config: any;
  order: number;
}

interface Workflow {
  id: string;
  name: string;
  description: string;
  trigger: string;
  steps: WorkflowStep[];
  status: 'active' | 'draft' | 'paused';
  executions: number;
  success_rate: number;
  created_at: string;
}

const AutomationCenter: React.FC = () => {
  const { t } = useTranslation();
  const [tasks, setTasks] = useState<AutomatedTask[]>([]);
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'tasks' | 'workflows' | 'schedules'>('tasks');
  const [showCreateModal, setShowCreateModal] = useState(false);

  useEffect(() => {
    fetchAutomationData();
  }, []);

  const fetchAutomationData = async () => {
    try {
      setLoading(true);

      // Mock data - replace with real API calls
      setTasks([
        {
          id: '1',
          name: 'Daily Database Backup',
          description: 'Automated daily backup of all databases',
          type: 'backup',
          status: 'active',
          schedule: '0 2 * * *', // Daily at 2 AM
          last_run: new Date(Date.now() - 86400000).toISOString(),
          next_run: new Date(Date.now() + 86400000).toISOString(),
          success_rate: 98.5,
          execution_count: 365,
          created_at: new Date(Date.now() - 86400000 * 365).toISOString(),
          enabled: true
        },
        {
          id: '2',
          name: 'Weekly System Cleanup',
          description: 'Clean temporary files and optimize system performance',
          type: 'cleanup',
          status: 'active',
          schedule: '0 3 * * 0', // Weekly on Sunday at 3 AM
          last_run: new Date(Date.now() - 86400000 * 7).toISOString(),
          next_run: new Date(Date.now() + 86400000).toISOString(),
          success_rate: 95.2,
          execution_count: 52,
          created_at: new Date(Date.now() - 86400000 * 365).toISOString(),
          enabled: true
        },
        {
          id: '3',
          name: 'Monthly Analytics Report',
          description: 'Generate and send monthly analytics reports',
          type: 'report',
          status: 'active',
          schedule: '0 9 1 * *', // Monthly on 1st at 9 AM
          last_run: new Date(Date.now() - 86400000 * 30).toISOString(),
          next_run: new Date(Date.now() + 86400000 * 2).toISOString(),
          success_rate: 100,
          execution_count: 12,
          created_at: new Date(Date.now() - 86400000 * 365).toISOString(),
          enabled: true
        },
        {
          id: '4',
          name: 'Security Scan',
          description: 'Automated security vulnerability scanning',
          type: 'security',
          status: 'error',
          schedule: '0 1 * * *', // Daily at 1 AM
          last_run: new Date(Date.now() - 86400000).toISOString(),
          next_run: new Date(Date.now() + 86400000).toISOString(),
          success_rate: 87.3,
          execution_count: 365,
          created_at: new Date(Date.now() - 86400000 * 365).toISOString(),
          enabled: false
        }
      ]);

      setWorkflows([
        {
          id: '1',
          name: 'New User Onboarding',
          description: 'Automated workflow for new user registration and setup',
          trigger: 'user_registration',
          steps: [
            { id: '1', name: 'Send Welcome Email', type: 'action', config: {}, order: 1 },
            { id: '2', name: 'Wait 1 Hour', type: 'delay', config: { duration: 3600 }, order: 2 },
            { id: '3', name: 'Send Setup Guide', type: 'action', config: {}, order: 3 },
            { id: '4', name: 'Check Profile Completion', type: 'condition', config: {}, order: 4 }
          ],
          status: 'active',
          executions: 1247,
          success_rate: 94.2,
          created_at: new Date(Date.now() - 86400000 * 180).toISOString()
        },
        {
          id: '2',
          name: 'Payment Failed Recovery',
          description: 'Handle failed payment attempts and retry logic',
          trigger: 'payment_failed',
          steps: [
            { id: '1', name: 'Send Payment Failed Notification', type: 'notification', config: {}, order: 1 },
            { id: '2', name: 'Wait 24 Hours', type: 'delay', config: { duration: 86400 }, order: 2 },
            { id: '3', name: 'Retry Payment', type: 'action', config: {}, order: 3 },
            { id: '4', name: 'Check Payment Success', type: 'condition', config: {}, order: 4 }
          ],
          status: 'active',
          executions: 89,
          success_rate: 76.4,
          created_at: new Date(Date.now() - 86400000 * 90).toISOString()
        }
      ]);

    } catch (error) {
      console.error('Error fetching automation data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="w-5 h-5 text-green-400" />;
      case 'paused': return <Pause className="w-5 h-5 text-yellow-400" />;
      case 'error': return <XCircle className="w-5 h-5 text-red-400" />;
      case 'completed': return <CheckCircle className="w-5 h-5 text-blue-400" />;
      default: return <AlertTriangle className="w-5 h-5 text-gray-400" />;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'backup': return <Database className="w-5 h-5 text-blue-400" />;
      case 'cleanup': return <RefreshCw className="w-5 h-5 text-green-400" />;
      case 'email': return <Mail className="w-5 h-5 text-purple-400" />;
      case 'report': return <BarChart3 className="w-5 h-5 text-orange-400" />;
      case 'maintenance': return <Settings className="w-5 h-5 text-gray-400" />;
      case 'security': return <AlertTriangle className="w-5 h-5 text-red-400" />;
      default: return <Zap className="w-5 h-5 text-yellow-400" />;
    }
  };

  const formatSchedule = (schedule: string) => {
    // Convert cron expression to human readable
    const scheduleMap: { [key: string]: string } = {
      '0 2 * * *': 'Daily at 2:00 AM',
      '0 3 * * 0': 'Weekly on Sunday at 3:00 AM',
      '0 9 1 * *': 'Monthly on 1st at 9:00 AM',
      '0 1 * * *': 'Daily at 1:00 AM'
    };
    return scheduleMap[schedule] || schedule;
  };

  const toggleTask = async (taskId: string) => {
    setTasks(prev => prev.map(task => 
      task.id === taskId ? { ...task, enabled: !task.enabled } : task
    ));
  };

  const executeTask = async (taskId: string) => {
    // Mock execution
    console.log(`Executing task: ${taskId}`);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white">
              {t('superAdmin.automationCenter.title', 'Automation Center')}
            </h1>
            <p className="text-gray-300 mt-2">
              {t('superAdmin.automationCenter.subtitle', 'Manage automated tasks and workflows')}
            </p>
          </div>
          
          <div className="flex items-center gap-4">
            <button
              onClick={fetchAutomationData}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
            >
              <RefreshCw className="w-4 h-4" />
              Refresh
            </button>
            
            <button
              onClick={() => setShowCreateModal(true)}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
            >
              <Plus className="w-4 h-4" />
              Create Task
            </button>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-1 bg-gray-800/50 p-1 rounded-lg">
          {[
            { id: 'tasks', name: 'Automated Tasks', icon: Zap },
            { id: 'workflows', name: 'Workflows', icon: Activity },
            { id: 'schedules', name: 'Schedules', icon: Calendar }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center gap-2 px-4 py-2 rounded-md transition-colors ${
                activeTab === tab.id
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-300 hover:text-white hover:bg-gray-700'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              {tab.name}
            </button>
          ))}
        </div>

        {/* Tasks Tab */}
        {activeTab === 'tasks' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
                <div className="flex items-center gap-2 mb-2">
                  <Zap className="w-5 h-5 text-yellow-400" />
                  <span className="text-sm text-gray-400">Total Tasks</span>
                </div>
                <div className="text-2xl font-bold text-white">{tasks.length}</div>
              </div>

              <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span className="text-sm text-gray-400">Active Tasks</span>
                </div>
                <div className="text-2xl font-bold text-white">
                  {tasks.filter(t => t.status === 'active').length}
                </div>
              </div>

              <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
                <div className="flex items-center gap-2 mb-2">
                  <XCircle className="w-5 h-5 text-red-400" />
                  <span className="text-sm text-gray-400">Failed Tasks</span>
                </div>
                <div className="text-2xl font-bold text-white">
                  {tasks.filter(t => t.status === 'error').length}
                </div>
              </div>

              <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
                <div className="flex items-center gap-2 mb-2">
                  <BarChart3 className="w-5 h-5 text-blue-400" />
                  <span className="text-sm text-gray-400">Avg Success Rate</span>
                </div>
                <div className="text-2xl font-bold text-white">
                  {(tasks.reduce((acc, t) => acc + t.success_rate, 0) / tasks.length).toFixed(1)}%
                </div>
              </div>
            </div>

            {/* Tasks List */}
            <div className="space-y-4">
              {tasks.map((task) => (
                <div key={task.id} className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      {getTypeIcon(task.type)}
                      <div>
                        <h3 className="font-semibold text-white">{task.name}</h3>
                        <p className="text-sm text-gray-400">{task.description}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      {getStatusIcon(task.status)}
                      <span className={`px-3 py-1 rounded text-sm font-medium ${
                        task.status === 'active' ? 'bg-green-900/20 text-green-300' :
                        task.status === 'error' ? 'bg-red-900/20 text-red-300' :
                        task.status === 'paused' ? 'bg-yellow-900/20 text-yellow-300' :
                        'bg-gray-900/20 text-gray-300'
                      }`}>
                        {task.status.toUpperCase()}
                      </span>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                    <div>
                      <span className="text-sm text-gray-400">Schedule:</span>
                      <div className="text-white">{formatSchedule(task.schedule)}</div>
                    </div>
                    <div>
                      <span className="text-sm text-gray-400">Success Rate:</span>
                      <div className="text-green-400">{task.success_rate}%</div>
                    </div>
                    <div>
                      <span className="text-sm text-gray-400">Executions:</span>
                      <div className="text-blue-400">{task.execution_count}</div>
                    </div>
                    <div>
                      <span className="text-sm text-gray-400">Next Run:</span>
                      <div className="text-yellow-400">{new Date(task.next_run).toLocaleString()}</div>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <button
                      onClick={() => toggleTask(task.id)}
                      className={`px-3 py-1 rounded text-sm transition-colors ${
                        task.enabled 
                          ? 'bg-yellow-600 hover:bg-yellow-700 text-white' 
                          : 'bg-green-600 hover:bg-green-700 text-white'
                      }`}
                    >
                      {task.enabled ? 'Pause' : 'Enable'}
                    </button>
                    <button
                      onClick={() => executeTask(task.id)}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition-colors"
                    >
                      Run Now
                    </button>
                    <button className="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-sm transition-colors">
                      <Edit className="w-4 h-4" />
                    </button>
                    <button className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm transition-colors">
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Workflows Tab */}
        {activeTab === 'workflows' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {workflows.map((workflow) => (
                <div key={workflow.id} className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="font-semibold text-white">{workflow.name}</h3>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      workflow.status === 'active' ? 'bg-green-900/20 text-green-300' :
                      workflow.status === 'draft' ? 'bg-gray-900/20 text-gray-300' :
                      'bg-yellow-900/20 text-yellow-300'
                    }`}>
                      {workflow.status.toUpperCase()}
                    </span>
                  </div>
                  
                  <p className="text-sm text-gray-400 mb-4">{workflow.description}</p>
                  
                  <div className="space-y-2 mb-4">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Trigger:</span>
                      <span className="text-blue-400">{workflow.trigger}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Steps:</span>
                      <span className="text-white">{workflow.steps.length}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Executions:</span>
                      <span className="text-green-400">{workflow.executions}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Success Rate:</span>
                      <span className="text-green-400">{workflow.success_rate}%</span>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <button className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-3 rounded text-sm transition-colors">
                      Edit
                    </button>
                    <button className="bg-gray-600 hover:bg-gray-700 text-white py-2 px-3 rounded text-sm transition-colors">
                      <Settings className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
    </div>
  );
};

export default AutomationCenter;
