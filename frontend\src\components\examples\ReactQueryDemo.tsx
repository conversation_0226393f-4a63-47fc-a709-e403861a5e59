import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  usePostsList,
  usePost,
  useCreatePost,
  useUpdatePost,
  useDeletePost,
  usePrefetchPost
} from '../../hooks/usePosts';
import {
  useUsersList,
  useUser,
  useCurrentUser
} from '../../hooks/useUsers';
import { Post, User } from '../../services/api';
import { toast } from '../ui/Toast';
import { Loader, RefreshCw, Plus, Edit, Trash, Eye, ArrowRight } from 'lucide-react';

/**
 * Component that demonstrates React Query usage
 */
const ReactQueryDemo: React.FC = () => {
  const { t } = useTranslation();
  const [selectedPostId, setSelectedPostId] = useState<number | null>(null);

  // Get current user for creating posts
  const {
    data: currentUser,
    isLoading: isLoadingCurrentUser
  } = useCurrentUser();

  // Fetch posts with pagination
  const {
    data: postsData,
    isLoading: isLoadingPosts,
    isError: isPostsError,
    error: postsError,
    refetch: refetchPosts
  } = usePostsList({ page: 1, page_size: 5 });

  // Fetch users for the author dropdown
  const {
    data: usersData,
    isLoading: isLoadingUsers
  } = useUsersList({ page: 1, page_size: 10 });

  // Get the selected post details
  const {
    data: selectedPost,
    isLoading: isLoadingSelectedPost
  } = usePost(selectedPostId || 0);

  // Mutations
  const createPost = useCreatePost();
  const updatePost = useUpdatePost();
  const deletePost = useDeletePost();

  // Prefetch post data on hover
  const prefetchPost = usePrefetchPost(selectedPostId || 0);

  // Form state
  const [formData, setFormData] = useState<Partial<Post>>({
    title: '',
    content: '',
  });

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle form submission for creating a new post
  const handleCreatePost = (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentUser) {
      toast.error(t('errors.notLoggedIn'));
      return;
    }

    createPost.mutate({
      ...formData,
      author_id: currentUser.id
    }, {
      onSuccess: () => {
        toast.success(t('posts.createSuccess'));
        setFormData({ title: '', content: '' });
        refetchPosts();
      },
      onError: (error) => {
        toast.error(t('posts.createError'));
        console.error('Error creating post:', error);
      }
    });
  };

  // Handle updating a post
  const handleUpdatePost = (post: Post) => {
    updatePost.mutate({
      id: post.id,
      data: {
        title: `${post.title} (Updated)`,
        content: `${post.content}\n\nUpdated at ${new Date().toLocaleString()}`
      }
    }, {
      onSuccess: () => {
        toast.success(t('posts.updateSuccess'));
        refetchPosts();
      },
      onError: (error) => {
        toast.error(t('posts.updateError'));
        console.error('Error updating post:', error);
      }
    });
  };

  // Handle deleting a post
  const handleDeletePost = (postId: number) => {
    if (window.confirm(t('posts.confirmDelete'))) {
      deletePost.mutate(postId, {
        onSuccess: () => {
          toast.success(t('posts.deleteSuccess'));
          if (selectedPostId === postId) {
            setSelectedPostId(null);
          }
          refetchPosts();
        },
        onError: (error) => {
          toast.error(t('posts.deleteError'));
          console.error('Error deleting post:', error);
        }
      });
    }
  };

  // Handle viewing a post
  const handleViewPost = (postId: number) => {
    setSelectedPostId(postId);
  };

  // Handle prefetching post data on hover
  const handlePostHover = (postId: number) => {
    prefetchPost();
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">{t('demo.reactQueryTitle')}</h1>

      {/* Create Post Form */}
      <div className="card p-4 mb-8">
        <h2 className="text-xl font-semibold mb-4">{t('posts.createNew')}</h2>
        <form onSubmit={handleCreatePost} className="space-y-4">
          <div>
            <label htmlFor="title" className="block mb-1">{t('posts.title')}</label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              className="input w-full"
              required
            />
          </div>
          <div>
            <label htmlFor="content" className="block mb-1">{t('posts.content')}</label>
            <textarea
              id="content"
              name="content"
              value={formData.content}
              onChange={handleInputChange}
              className="input w-full h-24"
              required
            />
          </div>
          <button
            type="submit"
            className={`btn-primary flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
            disabled={createPost.isPending || isLoadingCurrentUser}
          >
            {createPost.isPending ? (
              <Loader className={`w-4 h-4 mr-2 animate-spin ${isRTL ? "space-x-reverse" : ""}`} />
            ) : (
              <Plus className={`w-4 h-4 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
            )}
            {t('posts.create')}
          </button>
        </form>
      </div>

      {/* Posts List */}
      <div className="card p-4 mb-8">
        <div className={`flex justify-between items-center mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
          <h2 className="text-xl font-semibold">{t('posts.list')}</h2>
          <button
            onClick={() => refetchPosts()}
            className="p-2 rounded-full hover:bg-indigo-800/30"
            title={t('common.refresh')}
          >
            <RefreshCw className={`w-5 h-5 ${isLoadingPosts ? 'animate-spin' : ''}`} />
          </button>
        </div>

        {isLoadingPosts ? (
          <div className={`flex justify-center py-8 ${isRTL ? "flex-row-reverse" : ""}`}>
            <Loader className="w-8 h-8 animate-spin" />
          </div>
        ) : isPostsError ? (
          <div className="text-red-500 p-4 rounded-lg bg-red-500/10">
            {postsError?.message || t('errors.unknown')}
          </div>
        ) : postsData?.results?.length === 0 ? (
          <div className="text-center py-8 text-gray-400">
            {t('posts.noPosts')}
          </div>
        ) : (
          <div className="space-y-4">
            {postsData?.results.map(post => (
              <div
                key={post.id}
                className="p-4 rounded-lg bg-indigo-800/20 hover:bg-indigo-800/30 transition-colors"
                onMouseEnter={() => handlePostHover(post.id)}
              >
                <div className={`flex justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
                  <h3 className="font-medium">{post.title}</h3>
                  <div className={`flex space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <button
                      onClick={() => handleViewPost(post.id)}
                      className="p-1 rounded hover:bg-indigo-700/30"
                      title={t('common.view')}
                    >
                      <Eye className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => handleUpdatePost(post)}
                      className="p-1 rounded hover:bg-indigo-700/30"
                      title={t('common.edit')}
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => handleDeletePost(post.id)}
                      className="p-1 rounded hover:bg-red-700/30"
                      title={t('common.delete')}
                    >
                      <Trash className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                <div className="text-sm text-gray-300 mt-2 line-clamp-2">{post.content}</div>
                <div className="text-xs text-gray-400 mt-2">
                  {post.author?.username || t('common.unknown')} • {new Date(post.created_at).toLocaleDateString()}
                </div>
              </div>
            ))}

            {/* Pagination info */}
            <div className={`flex justify-between text-sm text-gray-400 pt-2 ${isRTL ? "flex-row-reverse" : ""}`}>
              <div>
                {t('pagination.showing')} {postsData?.results.length} {t('pagination.of')} {postsData?.count}
              </div>
              <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                {postsData?.next && (
                  <button className={`flex items-center hover:text-white ${isRTL ? "flex-row-reverse" : ""}`}>
                    {t('pagination.next')} <ArrowRight className={`w-4 h-4 ml-1 ${isRTL ? "space-x-reverse" : ""}`} />
                  </button>
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Selected Post Details */}
      {selectedPostId && (
        <div className="card p-4">
          <h2 className="text-xl font-semibold mb-4">{t('posts.details')}</h2>

          {isLoadingSelectedPost ? (
            <div className={`flex justify-center py-8 ${isRTL ? "flex-row-reverse" : ""}`}>
              <Loader className="w-8 h-8 animate-spin" />
            </div>
          ) : selectedPost ? (
            <div>
              <h3 className="text-lg font-medium mb-2">{selectedPost.title}</h3>
              <div className="mb-4 whitespace-pre-line">{selectedPost.content}</div>
              <div className="text-sm text-gray-400">
                {t('posts.author')}: {selectedPost.author?.username || t('common.unknown')}
              </div>
              <div className="text-sm text-gray-400">
                {t('posts.created')}: {new Date(selectedPost.created_at).toLocaleString()}
              </div>
              {selectedPost.updated_at !== selectedPost.created_at && (
                <div className="text-sm text-gray-400">
                  {t('posts.updated')}: {new Date(selectedPost.updated_at).toLocaleString()}
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-400">
              {t('posts.notFound')}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ReactQueryDemo;
