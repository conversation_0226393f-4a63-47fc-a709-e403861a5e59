import React, { useState, useEffect } from 'react';
import { businessPlanAnalyticsAPI, BusinessPlanSessionManager } from '../../services/businessPlanAnalyticsApi';
import { useTranslation } from 'react-i18next';

const AnalyticsTestComponent: React.FC = () => {
  const { t } = useTranslation();
  const [sessionManager] = useState(() => BusinessPlanSessionManager.getInstance());
  const [currentSession, setCurrentSession] = useState<string | null>(null);
  const [testResults, setTestResults] = useState<string[]>([]);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testSessionManagement = async () => {
    try {
      addResult('Testing session management...');
      
      // Start a session (using business plan ID 1 for testing)
      const sessionId = await sessionManager.startSession(1);
      setCurrentSession(sessionId);
      addResult(`✅ Session started: ${sessionId}`);
      
      // Wait a bit
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Track some collaboration
      await sessionManager.trackCollaboration('edit', {
        sectionId: 1,
        sectionTitle: 'Executive Summary',
        content: 'Test edit'
      });
      addResult('✅ Collaboration tracked');
      
      // Track export
      await sessionManager.trackExport('pdf', 'full', {
        sectionsIncluded: [1, 2, 3],
        fileSize: 1024000,
        exportSuccessful: true
      });
      addResult('✅ Export tracked');
      
      // End session
      await sessionManager.endSession();
      setCurrentSession(null);
      addResult('✅ Session ended');
      
    } catch (error) {
      addResult(`❌ Error: ${error}`);
    }
  };

  const testAnalyticsRetrieval = async () => {
    try {
      addResult('Testing analytics retrieval...');
      
      // Get time analytics
      const timeAnalytics = await businessPlanAnalyticsAPI.getTimeAnalytics();
      addResult(`✅ Time analytics: ${timeAnalytics.total_sessions} sessions`);
      
      // Get template usage
      const templateUsage = await businessPlanAnalyticsAPI.getTemplateUsageAnalytics();
      addResult(`✅ Template usage: ${templateUsage.length} templates`);
      
      // Get collaboration stats
      const collaborationStats = await businessPlanAnalyticsAPI.getCollaborationStats();
      addResult(`✅ Collaboration stats: ${collaborationStats.total_collaborations} collaborations`);
      
      // Get export stats
      const exportStats = await businessPlanAnalyticsAPI.getExportStats();
      addResult(`✅ Export stats: ${exportStats.total_exports} exports`);
      
      // Get success metrics
      const successMetrics = await businessPlanAnalyticsAPI.getSuccessMetrics();
      addResult(`✅ Success metrics: ${successMetrics.completion_rate}% completion rate`);
      
    } catch (error) {
      addResult(`❌ Error: ${error}`);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
      <h3 className="text-lg font-semibold text-white mb-4">
        Analytics System Test
      </h3>
      
      <div className="space-y-4 mb-6">
        <div className="flex space-x-4">
          <button
            onClick={testSessionManagement}
            disabled={!!currentSession}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 rounded-lg text-white font-medium transition-colors"
          >
            Test Session Management
          </button>
          
          <button
            onClick={testAnalyticsRetrieval}
            className="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg text-white font-medium transition-colors"
          >
            Test Analytics Retrieval
          </button>
          
          <button
            onClick={clearResults}
            className="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg text-white font-medium transition-colors"
          >
            Clear Results
          </button>
        </div>
        
        {currentSession && (
          <div className="text-yellow-400 text-sm">
            Active session: {currentSession}
          </div>
        )}
      </div>
      
      <div className="bg-black/20 rounded-lg p-4 max-h-96 overflow-y-auto">
        <h4 className="text-white font-medium mb-2">Test Results:</h4>
        {testResults.length === 0 ? (
          <p className="text-gray-400 text-sm">No tests run yet</p>
        ) : (
          <div className="space-y-1">
            {testResults.map((result, index) => (
              <div key={index} className="text-sm font-mono text-gray-300">
                {result}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default AnalyticsTestComponent;
