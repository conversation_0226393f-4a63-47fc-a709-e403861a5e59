import React from 'react';
import { useLanguage } from '../../hooks/useLanguage';

import { useTranslation } from 'react-i18next';
interface RTLTableProps {
  children: React.ReactNode;
  className?: string;
  /**
   * Whether to add a wrapper div with overflow-x-auto
   * @default true
   */
  withWrapper?: boolean;
  /**
   * Whether to add zebra striping to the table
   * @default false
   */
  striped?: boolean;
  /**
   * Whether to add hover effect to the table rows
   * @default false
   */
  hover?: boolean;
  /**
   * Whether to add borders to the table
   * @default true
   */
  bordered?: boolean;
  /**
   * Whether to make the table compact
   * @default false
   */
  compact?: boolean;
}

/**
 * RTL-aware table component that automatically sets the correct direction
 * based on the current language
 */
const RTLTable: React.FC<RTLTableProps> = ({ children,
  className = '',
  withWrapper = true,
  striped = false,
  hover = false,
  bordered = true,
  compact = false,
 }) => {
  const { t } = useTranslation();
  const { isRTL, direction } = useLanguage();
  
  // Generate table classes
  const tableClasses = [
    'w-full',
    bordered ? 'border-collapse' : '',
    striped ? 'table-striped' : '',
    hover ? 'table-hover' : '',
    compact ? 'table-compact' : '',
    className,
  ].filter(Boolean).join(' ');
  
  // Table component with theme support
  const TableComponent = () => (
    <table
      className={`${tableClasses} glass-light border text-glass-primary`}
      dir={direction}
    >
      {children}
    </table>
  );
  
  // If no wrapper is needed, return just the table
  if (!withWrapper) {
    return <TableComponent />;
  }
  
  // Return table with wrapper
  return (
    <div className="overflow-x-auto rounded transition-all duration-300 glass-light border">
      <TableComponent />
    </div>
  );
};

/**
 * RTL-aware table header component
 */
export const RTLTableHead: React.FC<{ children: React.ReactNode; className?: string }> = ({
  children,
  className = '',
}) => {
  return (
    <thead className={`${className} glass-light`}>
      {children}
    </thead>
  );
};

/**
 * RTL-aware table body component
 */
export const RTLTableBody: React.FC<{ children: React.ReactNode; className?: string }> = ({
  children,
  className = '',
}) => {
  return (
    <tbody className={className}>
      {children}
    </tbody>
  );
};

/**
 * RTL-aware table row component
 */
export const RTLTableRow: React.FC<{
  children: React.ReactNode;
  className?: string;
  isHeader?: boolean;
  isSelected?: boolean;
  onClick?: () => void;
}> = ({
  children,
  className = '',
  isHeader = false,
  isSelected = false,
  onClick,
}) => {
  return (
    <tr
      className={`transition-colors duration-200 ${isSelected ? 'selected glass-accent' : ''} ${onClick ? 'cursor-pointer' : ''} ${className}`}
      onClick={onClick}
    >
      {children}
    </tr>
  );
};

/**
 * RTL-aware table cell component
 */
export const RTLTableCell: React.FC<{
  children: React.ReactNode;
  className?: string;
  isHeader?: boolean;
  align?: 'start' | 'center' | 'end';
  colSpan?: number;
  rowSpan?: number;
}> = ({
  children,
  className = '',
  isHeader = false,
  align = 'start',
  colSpan,
  rowSpan,
}) => {
  const alignClass = align === 'start' ? 'text-start' : align === 'end' ? 'text-end' : 'text-center';
  
  const Component = isHeader ? 'th' : 'td';
  
  return (
    <Component
      className={`p-3 ${alignClass} ${className} ${isHeader ? 'glass-light text-glass-primary' : 'border-glass-border'}`}
      colSpan={colSpan}
      rowSpan={rowSpan}
    >
      {children}
    </Component>
  );
};

export { RTLTableHead, RTLTableBody, RTLTableRow, RTLTableCell };
export default RTLTable;
