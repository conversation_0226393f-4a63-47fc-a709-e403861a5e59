# Generated by Django 5.1.7 on 2025-05-28 12:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('incubator', '0008_mentorshipsession_calendar_provider_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='businessplantemplate',
            name='template_type',
            field=models.CharField(choices=[('standard', 'Standard'), ('lean', 'Lean Canvas'), ('detailed', 'Detailed'), ('investor', 'Investor-Ready'), ('startup', 'Startup'), ('nonprofit', 'Non-Profit'), ('service', 'Service Business'), ('product', 'Product Business'), ('ecommerce', 'E-commerce'), ('saas', 'SaaS/Software'), ('restaurant', 'Restaurant/Food Service'), ('retail', 'Retail'), ('consulting', 'Consulting'), ('franchise', 'Franchise'), ('manufacturing', 'Manufacturing'), ('healthcare', 'Healthcare'), ('education', 'Education'), ('real_estate', 'Real Estate'), ('fintech', 'FinTech'), ('marketplace', 'Marketplace'), ('subscription', 'Subscription Business'), ('mobile_app', 'Mobile App'), ('social_impact', 'Social Impact'), ('green_business', 'Green/Sustainable Business'), ('ai_startup', 'AI/Tech Startup'), ('blockchain', 'Blockchain/Crypto'), ('gaming', 'Gaming/Entertainment'), ('international', 'International Expansion'), ('acquisition', 'Business Acquisition'), ('pivot', 'Business Pivot'), ('custom', 'Custom')], default='standard', max_length=20),
        ),
        migrations.AlterField(
            model_name='templatesectiondefinition',
            name='section_type',
            field=models.CharField(choices=[('text', 'Text'), ('financial', 'Financial'), ('chart', 'Chart/Graph'), ('table', 'Table'), ('image', 'Image'), ('swot', 'SWOT Analysis'), ('timeline', 'Timeline'), ('checklist', 'Checklist'), ('canvas', 'Business Model Canvas'), ('persona', 'Customer Persona'), ('journey_map', 'Customer Journey Map'), ('competitive_matrix', 'Competitive Analysis Matrix'), ('risk_matrix', 'Risk Assessment Matrix'), ('financial_forecast', 'Financial Forecast'), ('revenue_model', 'Revenue Model'), ('pricing_strategy', 'Pricing Strategy'), ('marketing_mix', 'Marketing Mix (4Ps)'), ('value_proposition', 'Value Proposition Canvas'), ('lean_canvas', 'Lean Canvas'), ('pitch_deck', 'Pitch Deck Slide'), ('milestone_tracker', 'Milestone Tracker'), ('team_structure', 'Team Structure'), ('technology_stack', 'Technology Stack'), ('supply_chain', 'Supply Chain Diagram'), ('process_flow', 'Process Flow'), ('market_sizing', 'Market Sizing'), ('user_story', 'User Stories'), ('feature_roadmap', 'Feature Roadmap'), ('kpi_dashboard', 'KPI Dashboard'), ('custom', 'Custom')], default='text', max_length=20),
        ),
    ]
