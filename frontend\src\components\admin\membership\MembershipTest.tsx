import React, { useEffect, useState } from 'react';
import { membershipAPI, MembershipApplication } from '../../../services/api';
import { useTranslation } from 'react-i18next';

const MembershipTest: React.FC = () => {
  const { t } = useTranslation();
  const [applications, setApplications] = useState<MembershipApplication[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchApplications = async () => {
      setLoading(true);
      try {
        console.log('Fetching membership applications...');
        const data = await membershipAPI.getApplications();
        console.log('Received applications:', data);
        setApplications(data || []);
      } catch (err) {
        console.error('Error fetching applications:', err);
        setError(t('admin.failedToLoadApplications'));
      } finally {
        setLoading(false);
      }
    };

    fetchApplications();
  }, []);

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">{t('admin.membershipApplicationsTest')}</h1>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {loading ? (
        <div>{t('admin.loadingApplications')}</div>
      ) : (
        <div>
          <h2 className="text-xl mb-2">{t('admin.applicationsCount', { count: applications.length })}</h2>
          {applications.length === 0 ? (
            <div>{t('admin.noApplicationsFound')}</div>
          ) : (
            <ul className="space-y-4">
              {applications.map(app => (
                <li key={app.id} className="border p-4 rounded">
                  <div className="font-bold">{app.full_name}</div>
                  <div>{app.email}</div>
                  <div>{t('admin.status')}: {app.status}</div>
                  <div>{t('admin.created')}: {new Date(app.created_at).toLocaleString()}</div>
                </li>
              ))}
            </ul>
          )}
        </div>
      )}
    </div>
  );
};

export default MembershipTest;
