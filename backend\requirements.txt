# Core Django dependencies
asgiref==3.8.1
certifi==2025.4.26
charset-normalizer==3.4.2
Django==5.2.1
django-cors-headers==4.7.0
django-filter==24.1
djangorestframework==3.16.0
djangorestframework-simplejwt==5.3.1

# Google AI and Gemini
google-api-core>=2.19.2
google-auth>=2.28.2
google-generativeai>=0.8.3

# LangChain and LangGraph for advanced AI workflows
langchain>=0.3.7
langchain-core>=0.3.15
langchain-community>=0.3.7
langchain-google-genai>=2.0.5
langgraph>=0.2.45
langsmith>=0.1.147

# Machine Learning and Data Science
scikit-learn==1.5.2
pandas==2.2.3
numpy==2.1.3
scipy==1.14.1
joblib==1.4.2

# Advanced AI and NLP
transformers==4.46.3
torch>=2.6.0
sentence-transformers==3.3.1
openai==1.57.0
anthropic==0.39.0

# Data processing and utilities
idna==3.10
Jinja2==3.1.6
MarkupSafe==3.0.2
pillow==11.2.1
proto-plus==1.23.0
protobuf==4.25.3
pyasn1==0.6.0
pyasn1-modules==0.4.0
PyJWT==2.8.0
python-dotenv==1.0.1
requests==2.32.3
rsa==4.9
sqlparse==0.5.3
tqdm==4.66.4
typing_extensions==4.11.0
tzdata==2025.2
uritemplate==4.1.1
urllib3==2.4.0

# Additional ML and AI utilities
redis==5.2.0
celery==5.4.0
faiss-cpu==1.9.0
chromadb==0.5.23
tiktoken==0.8.0

# Language processing and detection
langdetect==1.0.9
polyglot==16.7.4
arabic-reshaper==3.0.0
python-bidi==0.4.2

# Additional utilities for enhanced functionality
beautifulsoup4==4.12.3
lxml==5.1.0
markdown==3.6
bleach==6.1.0
python-magic==0.4.27

# Performance and caching
django-redis==5.4.0
django-extensions==3.2.3
django-debug-toolbar==4.4.6
django-ratelimit==4.1.0

# File handling and storage
django-storages==1.14.4
boto3==1.35.67
botocore==1.35.67

# API documentation
drf-spectacular==0.27.2
drf-spectacular-sidecar==2024.7.1

# Enhanced AI and ML capabilities
# Computer Vision
opencv-python==4.10.0.84
Pillow==11.2.1
pytesseract==0.3.13
easyocr==1.7.1

# Voice AI and Audio Processing
speechrecognition==3.12.0
pydub==0.25.1
gtts==2.5.4
whisper==1.1.10

# Advanced NLP and Text Processing
spacy==3.8.2
textblob==0.18.0.post0
nltk==3.9.1
gensim==4.3.3

# Predictive Analytics and ML
xgboost==2.1.3
lightgbm==4.5.0
catboost==1.2.7
prophet==1.1.6
statsmodels==0.14.4

# Time Series and Forecasting
plotly==5.24.1
seaborn==0.13.2
matplotlib==3.9.2

# Vector Database and Embeddings
pinecone-client==5.0.1
weaviate-client==4.9.3
qdrant-client==1.12.1

# Advanced Data Processing
polars==1.16.0
dask==2024.12.1
modin==0.33.1

# Financial and Business Analytics
yfinance==0.2.48
fredapi==0.5.2
alpha-vantage==3.0.0

# Web Scraping and Data Collection
scrapy==2.12.0
selenium==4.27.1
requests-html==0.10.0

# Document Processing
pypdf2==3.0.1
python-docx==1.1.2
openpyxl==3.1.5

# Workflow and Task Management
dramatiq==1.17.0
rq==2.0.0
flower==2.0.1

# Real-time Features
channels==4.2.0
channels-redis==4.2.0
websockets==14.1

# Advanced Security
cryptography==44.0.0
pyjwt==2.8.0
passlib==1.7.4

# Monitoring and Logging
sentry-sdk==2.19.2
prometheus-client==0.21.1
structlog==24.4.0

# API Integrations
stripe==11.2.0
twilio==10.3.1
sendgrid==6.12.0

# Development and Testing
pytest==8.3.4
pytest-django==4.9.0
factory-boy==3.3.1
coverage==7.6.9
