from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import SearchViewSet
from .universal_search import universal_search, search_suggestions

router = DefaultRouter()
router.register(r'documents', SearchViewSet)

urlpatterns = [
    path('', include(router.urls)),
    path('universal/', universal_search, name='universal_search'),
    path('suggestions/', search_suggestions, name='search_suggestions'),
]
