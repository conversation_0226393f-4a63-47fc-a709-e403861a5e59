/**
 * AI Context Provider
 * Manages intelligent contextual assistance across the application
 */

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useLocation } from 'react-router-dom';
import { useAppSelector } from '../store/hooks';

interface AIContextData {
  currentPage: string;
  currentContext: any;
  businessIdeaId?: number;
  userId?: number;
  userPreferences: AIPreferences;
  isAIEnabled: boolean;
  contextualHelp: ContextualHelp[];
  smartSuggestions: SmartSuggestion[];
}

interface AIPreferences {
  enableFloatingAssistant: boolean;
  enableRealTimeEnhancements: boolean;
  enableProactiveNotifications: boolean;
  enableFormAssistance: boolean;
  assistantPosition: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  notificationFrequency: 'high' | 'medium' | 'low';
  autoApplySuggestions: boolean;
  preferredLanguage: string;
}

interface ContextualHelp {
  id: string;
  page: string;
  element?: string;
  title: string;
  description: string;
  type: 'tip' | 'warning' | 'info' | 'enhancement';
  priority: number;
  conditions?: Record<string, any>;
}

interface SmartSuggestion {
  id: string;
  type: 'navigation' | 'action' | 'content' | 'optimization';
  title: string;
  description: string;
  action: () => void;
  confidence: number;
  context: string[];
}

interface AIContextProviderProps {
  children: React.ReactNode;
}

const AIContext = createContext<{
  aiData: AIContextData;
  updateContext: (updates: Partial<AIContextData>) => void;
  updatePreferences: (preferences: Partial<AIPreferences>) => void;
  addContextualHelp: (help: ContextualHelp) => void;
  addSmartSuggestion: (suggestion: SmartSuggestion) => void;
  clearContextualHelp: () => void;
  getPageContext: () => any;
  isAIRelevantPage: () => boolean;
} | null>(null);

export const useAIContext = () => {
  const context = useContext(AIContext);
  if (!context) {
    throw new Error('useAIContext must be used within an AIContextProvider');
  }
  return context;
};

export const AIContextProvider: React.FC<AIContextProviderProps> = ({ children }) => {
  const location = useLocation();
  const { user } = useAppSelector((state) => state.auth);

  const [aiData, setAIData] = useState<AIContextData>({
    currentPage: '',
    currentContext: {},
    userId: user?.id,
    userPreferences: {
      enableFloatingAssistant: true,
      enableRealTimeEnhancements: true,
      enableProactiveNotifications: true,
      enableFormAssistance: true,
      assistantPosition: 'bottom-right',
      notificationFrequency: 'medium',
      autoApplySuggestions: false,
      preferredLanguage: 'en'
    },
    isAIEnabled: true,
    contextualHelp: [],
    smartSuggestions: []
  });

  // Update current page context when location changes
  useEffect(() => {
    const currentPage = location.pathname;
    const pageContext = analyzePageContext(currentPage);

    setAIData(prev => ({
      ...prev,
      currentPage,
      currentContext: pageContext,
      businessIdeaId: extractBusinessIdeaId(currentPage)
    }));

    // Generate contextual help for the new page
    generateContextualHelp(currentPage, pageContext);
  }, [location.pathname]);

  // Load user preferences from localStorage
  useEffect(() => {
    const savedPreferences = localStorage.getItem('aiPreferences');
    if (savedPreferences) {
      try {
        const preferences = JSON.parse(savedPreferences);
        setAIData(prev => ({
          ...prev,
          userPreferences: { ...prev.userPreferences, ...preferences }
        }));
      } catch (error) {
        console.error('Error loading AI preferences:', error);
      }
    }
  }, []);

  const analyzePageContext = (pathname: string): any => {
    const context: any = {
      pageType: 'unknown',
      features: [],
      aiRelevance: 'low'
    };

    if (pathname.includes('/dashboard')) {
      context.pageType = 'dashboard';
      context.features = ['overview', 'navigation', 'quick-actions'];
      context.aiRelevance = 'high';
    } else if (pathname.includes('/business-ideas')) {
      context.pageType = 'business-ideas';
      context.features = ['creation', 'editing', 'analysis'];
      context.aiRelevance = 'very-high';
    } else if (pathname.includes('/mentorship')) {
      context.pageType = 'mentorship';
      context.features = ['matching', 'communication', 'scheduling'];
      context.aiRelevance = 'high';
    } else if (pathname.includes('/funding')) {
      context.pageType = 'funding';
      context.features = ['applications', 'tracking', 'preparation'];
      context.aiRelevance = 'high';
    } else if (pathname.includes('/profile')) {
      context.pageType = 'profile';
      context.features = ['editing', 'completion', 'optimization'];
      context.aiRelevance = 'medium';
    }

    return context;
  };

  const extractBusinessIdeaId = (pathname: string): number | undefined => {
    const match = pathname.match(/\/business-ideas\/(\d+)/);
    return match ? parseInt(match[1], 10) : undefined;
  };

  const generateContextualHelp = useCallback((page: string, context: any) => {
    const helpItems: ContextualHelp[] = [];

    if (page.includes('/business-ideas/create')) {
      helpItems.push({
        id: 'create-idea-help',
        page,
        title: 'AI-Powered Idea Creation',
        description: 'Use our AI assistant to enhance your business idea as you type. Get real-time suggestions for market fit and improvements.',
        type: 'tip',
        priority: 1
      });
    }

    if (page.includes('/dashboard') && context.pageType === 'dashboard') {
      helpItems.push({
        id: 'dashboard-ai-help',
        page,
        title: 'Your AI Assistant is Active',
        description: 'Your AI is continuously working to enhance your business ideas. Check the AI dashboard for recent improvements.',
        type: 'info',
        priority: 2
      });
    }

    if (page.includes('/mentorship')) {
      helpItems.push({
        id: 'mentorship-ai-help',
        page,
        title: 'Smart Mentor Matching',
        description: 'AI can analyze your business needs and match you with the most suitable mentors based on expertise and experience.',
        type: 'enhancement',
        priority: 1
      });
    }

    setAIData(prev => ({
      ...prev,
      contextualHelp: helpItems
    }));
  }, []);

  const updateContext = useCallback((updates: Partial<AIContextData>) => {
    setAIData(prev => ({ ...prev, ...updates }));
  }, []);

  const updatePreferences = useCallback((preferences: Partial<AIPreferences>) => {
    setAIData(prev => {
      const newPreferences = { ...prev.userPreferences, ...preferences };

      // Save to localStorage
      localStorage.setItem('aiPreferences', JSON.stringify(newPreferences));

      return {
        ...prev,
        userPreferences: newPreferences
      };
    });
  }, []);

  const addContextualHelp = useCallback((help: ContextualHelp) => {
    setAIData(prev => ({
      ...prev,
      contextualHelp: [...prev.contextualHelp, help]
    }));
  }, []);

  const addSmartSuggestion = useCallback((suggestion: SmartSuggestion) => {
    setAIData(prev => ({
      ...prev,
      smartSuggestions: [...prev.smartSuggestions, suggestion]
    }));
  }, []);

  const clearContextualHelp = useCallback(() => {
    setAIData(prev => ({
      ...prev,
      contextualHelp: []
    }));
  }, []);

  const getPageContext = useCallback(() => {
    return {
      page: aiData.currentPage,
      context: aiData.currentContext,
      businessIdeaId: aiData.businessIdeaId,
      userId: aiData.userId
    };
  }, [aiData]);

  const isAIRelevantPage = useCallback(() => {
    const relevantPages = [
      '/dashboard',
      '/business-ideas',
      '/mentorship',
      '/funding',
      '/profile',
      '/ai'
    ];

    return relevantPages.some(page => aiData.currentPage.includes(page)) ||
           aiData.currentContext.aiRelevance === 'high' ||
           aiData.currentContext.aiRelevance === 'very-high';
  }, [aiData.currentPage, aiData.currentContext]);

  const contextValue = {
    aiData,
    updateContext,
    updatePreferences,
    addContextualHelp,
    addSmartSuggestion,
    clearContextualHelp,
    getPageContext,
    isAIRelevantPage
  };

  return (
    <AIContext.Provider value={contextValue}>
      {children}
    </AIContext.Provider>
  );
};
