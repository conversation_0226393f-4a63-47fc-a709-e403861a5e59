import React, { useState, useEffect } from 'react';
import {
  Zap,
  Play,
  Pause,
  Setting<PERSON>,
  Clock,
  Mail,
  Bell,
  FileText,
  Users,
  CheckCircle,
  AlertTriangle,
  Plus,
  Edit,
  Trash2,
  ArrowRight,
  Calendar,
  Target,
  Workflow,
  Bot,
  Send
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { RTLText, RTLFlex } from '../common';

interface AutomationRule {
  id: number;
  name: string;
  description: string;
  trigger: {
    type: 'template_used' | 'section_completed' | 'time_based' | 'rating_received' | 'completion_stalled';
    conditions: any;
  };
  actions: Array<{
    type: 'send_email' | 'send_notification' | 'create_task' | 'update_status' | 'generate_report';
    config: any;
  }>;
  is_active: boolean;
  created_at: string;
  last_triggered: string | null;
  trigger_count: number;
}

interface WorkflowTemplate {
  id: number;
  name: string;
  description: string;
  steps: Array<{
    id: string;
    name: string;
    type: 'manual' | 'automated' | 'approval';
    estimated_time: number;
    dependencies: string[];
    assignee_role?: string;
  }>;
  is_active: boolean;
  usage_count: number;
}

interface TemplateAutomationProps {
  templateId: number;
  onRuleCreated?: (rule: AutomationRule) => void;
  onWorkflowCreated?: (workflow: WorkflowTemplate) => void;
}

export const TemplateAutomation: React.FC<TemplateAutomationProps> = ({ templateId,
  onRuleCreated,
  onWorkflowCreated
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [activeTab, setActiveTab] = useState<'rules' | 'workflows' | 'analytics'>('rules');
  const [automationRules, setAutomationRules] = useState<AutomationRule[]>([]);
  const [workflows, setWorkflows] = useState<WorkflowTemplate[]>([]);
  const [loading, setLoading] = useState(false);
  const [showCreateRule, setShowCreateRule] = useState(false);
  const [showCreateWorkflow, setShowCreateWorkflow] = useState(false);

  useEffect(() => {
    fetchAutomationData();
  }, [templateId]);

  const fetchAutomationData = async () => {
    setLoading(true);
    try {
      if (!templateId) {
        setAutomationRules([]);
        setWorkflows([]);
        return;
      }

      // Fetch real automation data from API
      const { customTemplatesAPI } = await import('../../services/templateCustomizationApi');

      // Get automation rules for template
      const rulesData = await customTemplatesAPI.getAutomationRules?.(templateId) || [];

      // Get workflow templates
      const workflowsData = await customTemplatesAPI.getWorkflowTemplates?.(templateId) || [];

      setAutomationRules(rulesData);
      setWorkflows(workflowsData);
    } catch (error) {
      console.error('Error fetching automation data:', error);
      // Set empty arrays on error
      setAutomationRules([]);
      setWorkflows([]);
    } finally {
      setLoading(false);
    }
  };

  const toggleRuleStatus = async (ruleId: number) => {
    try {
      // Make real API call to toggle rule status
      const { customTemplatesAPI } = await import('../../services/templateCustomizationApi');
      await customTemplatesAPI.toggleAutomationRule?.(ruleId);

      // Update local state after successful API call
      setAutomationRules(prev =>
        prev.map(rule =>
          rule.id === ruleId
            ? { ...rule, is_active: !rule.is_active }
            : rule
        )
      );
    } catch (error) {
      console.error('Error toggling rule status:', error);
    }
  };

  const deleteRule = async (ruleId: number) => {
    try {
      // Mock API call - replace with actual implementation
      setAutomationRules(prev => prev.filter(rule => rule.id !== ruleId));
    } catch (error) {
      console.error('Error deleting rule:', error);
    }
  };

  const getTriggerIcon = (triggerType: string) => {
    switch (triggerType) {
      case 'template_used': return <Play className="text-green-400" size={16} />;
      case 'section_completed': return <CheckCircle className="text-blue-400" size={16} />;
      case 'time_based': return <Clock className="text-yellow-400" size={16} />;
      case 'rating_received': return <Target className="text-purple-400" size={16} />;
      case 'completion_stalled': return <AlertTriangle className="text-red-400" size={16} />;
      default: return <Zap className="text-gray-400" size={16} />;
    }
  };

  const getActionIcon = (actionType: string) => {
    switch (actionType) {
      case 'send_email': return <Mail className="text-blue-400" size={14} />;
      case 'send_notification': return <Bell className="text-yellow-400" size={14} />;
      case 'create_task': return <FileText className="text-green-400" size={14} />;
      case 'update_status': return <Settings className="text-purple-400" size={14} />;
      case 'generate_report': return <FileText className="text-orange-400" size={14} />;
      default: return <Zap className="text-gray-400" size={14} />;
    }
  };

  const getStepIcon = (stepType: string) => {
    switch (stepType) {
      case 'manual': return <Users className="text-blue-400" size={16} />;
      case 'automated': return <Bot className="text-green-400" size={16} />;
      case 'approval': return <CheckCircle className="text-yellow-400" size={16} />;
      default: return <Settings className="text-gray-400" size={16} />;
    }
  };

  const renderAutomationRules = () => (
    <div className="space-y-4">
      {/* Header */}
      <RTLFlex className="items-center justify-between">
        <RTLText as="h4" className="font-semibold">
          {t('automation.automationRules')} ({automationRules.length})
        </RTLText>

        <button
          onClick={() => setShowCreateRule(true)}
          className={`px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg text-white transition-colors flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
        >
          <Plus size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
          {t('automation.createRule')}
        </button>
      </RTLFlex>

      {/* Rules List */}
      <div className="space-y-3">
        {automationRules.map((rule) => (
          <div
            key={rule.id}
            className="bg-gray-700/50 rounded-lg p-4 border border-gray-600"
          >
            <RTLFlex className="items-start justify-between mb-3">
              <div className={`flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                <RTLFlex className="items-center mb-2">
                  {getTriggerIcon(rule.trigger.type)}
                  <RTLText className={`font-medium ml-2 ${isRTL ? "space-x-reverse" : ""}`}>
                    {rule.name}
                  </RTLText>
                  <span className={`px-2 py-1 rounded-full text-xs ml-3 ${
                    rule.is_active
                      ? 'bg-green-600/20 text-green-300'
                      : 'bg-gray-600/20 text-gray-400'}
                  }`}>
                    {rule.is_active ? t('automation.active') : t('automation.inactive')}
                  </span>
                </RTLFlex>

                <RTLText className="text-sm text-gray-300 mb-2">
                  {rule.description}
                </RTLText>

                <RTLFlex className="items-center gap-4 text-xs text-gray-400">
                  <span>{t('automation.triggered')} {rule.trigger_count} {t('automation.times')}</span>
                  {rule.last_triggered && (
                    <span>
                      {t('automation.lastTriggered')}: {new Date(rule.last_triggered).toLocaleDateString()}
                    </span>
                  )}
                </RTLFlex>
              </div>

              <RTLFlex className="items-center gap-2">
                <button
                  onClick={() => toggleRuleStatus(rule.id)}
                  className={`p-2 rounded-lg transition-colors ${
                    rule.is_active
                      ? 'text-yellow-400 hover:bg-yellow-400/20'
                      : 'text-green-400 hover:bg-green-400/20'}
                  }`}
                >
                  {rule.is_active ? <Pause size={16} /> : <Play size={16} />}
                </button>

                <button className="p-2 text-blue-400 hover:bg-blue-400/20 rounded-lg transition-colors">
                  <Edit size={16} />
                </button>

                <button
                  onClick={() => deleteRule(rule.id)}
                  className="p-2 text-red-400 hover:bg-red-400/20 rounded-lg transition-colors"
                >
                  <Trash2 size={16} />
                </button>
              </RTLFlex>
            </RTLFlex>

            {/* Actions */}
            <div className={`flex flex-wrap gap-2 ${isRTL ? "flex-row-reverse" : ""}`}>
              {rule.actions.map((action, index) => (
                <RTLFlex
                  key={index}
                  className="items-center px-2 py-1 bg-gray-600/50 rounded text-xs"
                >
                  {getActionIcon(action.type)}
                  <span className={`ml-1 ${isRTL ? "space-x-reverse" : ""}`}>{t(`automation.${action.type}`)}</span>
                </RTLFlex>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderWorkflows = () => (
    <div className="space-y-4">
      {/* Header */}
      <RTLFlex className="items-center justify-between">
        <RTLText as="h4" className="font-semibold">
          {t('automation.workflows')} ({workflows.length})
        </RTLText>

        <button
          onClick={() => setShowCreateWorkflow(true)}
          className={`px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-white transition-colors flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
        >
          <Plus size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
          {t('automation.createWorkflow')}
        </button>
      </RTLFlex>

      {/* Workflows List */}
      <div className="space-y-4">
        {workflows.map((workflow) => (
          <div
            key={workflow.id}
            className="bg-gray-700/50 rounded-lg p-4 border border-gray-600"
          >
            <RTLFlex className="items-start justify-between mb-4">
              <div>
                <RTLFlex className="items-center mb-2">
                  <Workflow className="text-blue-400" size={16} />
                  <RTLText className={`font-medium ml-2 ${isRTL ? "space-x-reverse" : ""}`}>
                    {workflow.name}
                  </RTLText>
                  <span className={`px-2 py-1 rounded-full text-xs ml-3 ${
                    workflow.is_active
                      ? 'bg-green-600/20 text-green-300'
                      : 'bg-gray-600/20 text-gray-400'}
                  }`}>
                    {workflow.is_active ? t('automation.active') : t('automation.inactive')}
                  </span>
                </RTLFlex>

                <RTLText className="text-sm text-gray-300 mb-2">
                  {workflow.description}
                </RTLText>

                <RTLText className="text-xs text-gray-400">
                  {t('automation.used')} {workflow.usage_count} {t('automation.times')}
                </RTLText>
              </div>

              <RTLFlex className="items-center gap-2">
                <button className="p-2 text-blue-400 hover:bg-blue-400/20 rounded-lg transition-colors">
                  <Edit size={16} />
                </button>
                <button className="p-2 text-red-400 hover:bg-red-400/20 rounded-lg transition-colors">
                  <Trash2 size={16} />
                </button>
              </RTLFlex>
            </RTLFlex>

            {/* Workflow Steps */}
            <div className="space-y-2">
              <RTLText className="text-sm font-medium text-gray-300">
                {t('automation.workflowSteps')}:
              </RTLText>

              <div className={`flex items-center gap-2 overflow-x-auto pb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                {workflow.steps.map((step, index) => (
                  <React.Fragment key={step.id}>
                    <div className={`flex-shrink-0 bg-gray-600/50 rounded-lg p-3 min-w-[120px] ${isRTL ? "flex-row-reverse" : ""}`}>
                      <RTLFlex className="items-center mb-1">
                        {getStepIcon(step.type)}
                        <RTLText className={`text-xs font-medium ml-1 ${isRTL ? "space-x-reverse" : ""}`}>
                          {step.name}
                        </RTLText>
                      </RTLFlex>

                      <RTLText className="text-xs text-gray-400">
                        {step.estimated_time}h
                      </RTLText>

                      {step.assignee_role && (
                        <RTLText className="text-xs text-purple-300">
                          {step.assignee_role}
                        </RTLText>
                      )}
                    </div>

                    {index < workflow.steps.length - 1 && (
                      <ArrowRight className={`text-gray-400 flex-shrink-0 ${isRTL ? "flex-row-reverse" : ""}`} size={16} />
                    )}
                  </React.Fragment>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderAnalytics = () => (
    <div className="space-y-4">
      <RTLText as="h4" className="font-semibold">
        {t('automation.automationAnalytics')}
      </RTLText>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-gray-700/50 rounded-lg p-4 border border-gray-600 text-center">
          <Zap className="mx-auto mb-2 text-purple-400" size={24} />
          <RTLText className="text-2xl font-bold">
            {automationRules.filter(r => r.is_active).length}
          </RTLText>
          <RTLText className="text-sm text-gray-400">
            {t('automation.activeRules')}
          </RTLText>
        </div>

        <div className="bg-gray-700/50 rounded-lg p-4 border border-gray-600 text-center">
          <Send className="mx-auto mb-2 text-blue-400" size={24} />
          <RTLText className="text-2xl font-bold">
            {automationRules.reduce((sum, rule) => sum + rule.trigger_count, 0)}
          </RTLText>
          <RTLText className="text-sm text-gray-400">
            {t('automation.totalTriggers')}
          </RTLText>
        </div>

        <div className="bg-gray-700/50 rounded-lg p-4 border border-gray-600 text-center">
          <Workflow className="mx-auto mb-2 text-green-400" size={24} />
          <RTLText className="text-2xl font-bold">
            {workflows.reduce((sum, workflow) => sum + workflow.usage_count, 0)}
          </RTLText>
          <RTLText className="text-sm text-gray-400">
            {t('automation.workflowExecutions')}
          </RTLText>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-900/50 to-blue-900/50 rounded-lg p-6 border border-purple-500/30">
        <RTLFlex className="items-center mb-4">
          <Zap className={`text-purple-400 mr-3 ${isRTL ? "space-x-reverse" : ""}`} size={24} />
          <RTLText as="h3" className="text-xl font-bold">
            {t('automation.templateAutomation')}
          </RTLText>
        </RTLFlex>

        <RTLText className="text-gray-300">
          {t('automation.automationDescription')}
        </RTLText>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-700">
        <nav className={`flex space-x-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          {[
            { key: 'rules', label: t('automation.rules'), icon: Zap },
            { key: 'workflows', label: t('automation.workflows'), icon: Workflow },
            { key: 'analytics', label: t('automation.analytics'), icon: BarChart3 }
          ].map(({ key, label, icon: Icon }) => (
            <button
              key={key}
              onClick={() => setActiveTab(key as any)}
              className={`py-4 px-2 border-b-2 font-medium text-sm transition-colors flex items-center ${
                activeTab === key
                  ? 'border-purple-500 text-purple-400'
                  : 'border-transparent text-gray-400 hover:text-gray-300'}
              }`}
            >
              <Icon size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
              {label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div>
        {activeTab === 'rules' && renderAutomationRules()}
        {activeTab === 'workflows' && renderWorkflows()}
        {activeTab === 'analytics' && renderAnalytics()}
      </div>
    </div>
  );
};
