import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  DollarSign, TrendingUp, TrendingDown, CreditCard, Users,
  Calendar, BarChart3, PieChart, ArrowUp, ArrowDown,
  RefreshCw, Download, Filter, Eye, AlertCircle,
  CheckCircle, XCircle, Clock, Target, Zap
} from 'lucide-react';

import { superAdminApi } from '../../../services/superAdminApi';

interface RevenueMetrics {
  total_revenue: number;
  monthly_revenue: number;
  daily_revenue: number;
  revenue_growth: number;
  mrr: number; // Monthly Recurring Revenue
  arr: number; // Annual Recurring Revenue
  average_order_value: number;
  customer_lifetime_value: number;
  churn_rate: number;
  conversion_rate: number;
}

interface PaymentAnalytics {
  total_transactions: number;
  successful_payments: number;
  failed_payments: number;
  refunds: number;
  chargebacks: number;
  success_rate: number;
  average_transaction_value: number;
  payment_methods: Array<{
    method: string;
    count: number;
    amount: number;
    success_rate: number;
  }>;
}

interface SubscriptionMetrics {
  total_subscribers: number;
  new_subscribers: number;
  cancelled_subscribers: number;
  active_subscriptions: number;
  subscription_plans: Array<{
    plan_name: string;
    subscribers: number;
    revenue: number;
    growth_rate: number;
  }>;
}

interface RevenueByPeriod {
  period: string;
  revenue: number;
  transactions: number;
  growth_rate: number;
}

const RevenueAnalytics: React.FC = () => {
  const { t } = useTranslation();
  const [metrics, setMetrics] = useState<RevenueMetrics | null>(null);
  const [payments, setPayments] = useState<PaymentAnalytics | null>(null);
  const [subscriptions, setSubscriptions] = useState<SubscriptionMetrics | null>(null);
  const [revenueHistory, setRevenueHistory] = useState<RevenueByPeriod[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'payments' | 'subscriptions' | 'trends'>('overview');
  const [dateRange, setDateRange] = useState('30d');

  useEffect(() => {
    fetchRevenueData();
  }, [dateRange]);

  const fetchRevenueData = async () => {
    try {
      setLoading(true);

      // Mock data - replace with real API calls
      setMetrics({
        total_revenue: 2847563.45,
        monthly_revenue: 234567.89,
        daily_revenue: 7823.45,
        revenue_growth: 15.7,
        mrr: 234567.89,
        arr: 2814814.68,
        average_order_value: 89.50,
        customer_lifetime_value: 1247.80,
        churn_rate: 3.2,
        conversion_rate: 8.7
      });

      setPayments({
        total_transactions: 45623,
        successful_payments: 43891,
        failed_payments: 1732,
        refunds: 234,
        chargebacks: 12,
        success_rate: 96.2,
        average_transaction_value: 89.50,
        payment_methods: [
          { method: 'Credit Card', count: 32456, amount: 2456789.12, success_rate: 97.1 },
          { method: 'PayPal', count: 8934, amount: 567890.34, success_rate: 95.8 },
          { method: 'Bank Transfer', count: 3421, amount: 234567.89, success_rate: 98.9 },
          { method: 'Digital Wallet', count: 812, amount: 67890.12, success_rate: 94.3 }
        ]
      });

      setSubscriptions({
        total_subscribers: 12847,
        new_subscribers: 456,
        cancelled_subscribers: 89,
        active_subscriptions: 12758,
        subscription_plans: [
          { plan_name: 'Basic', subscribers: 6789, revenue: 67890.00, growth_rate: 12.3 },
          { plan_name: 'Pro', subscribers: 4523, revenue: 135690.00, growth_rate: 18.7 },
          { plan_name: 'Enterprise', subscribers: 1446, revenue: 289400.00, growth_rate: 25.1 }
        ]
      });

      // Generate revenue history
      const history: RevenueByPeriod[] = [];
      for (let i = 29; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        history.push({
          period: date.toISOString().split('T')[0],
          revenue: Math.random() * 10000 + 5000,
          transactions: Math.floor(Math.random() * 200) + 100,
          growth_rate: (Math.random() - 0.5) * 20
        });
      }
      setRevenueHistory(history);

    } catch (error) {
      console.error('Error fetching revenue data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const getGrowthIcon = (rate: number) => {
    return rate > 0 ? 
      <ArrowUp className="w-4 h-4 text-green-400" /> : 
      <ArrowDown className="w-4 h-4 text-red-400" />;
  };

  const getGrowthColor = (rate: number) => {
    return rate > 0 ? 'text-green-400' : 'text-red-400';
  };

  const getPaymentStatusIcon = (type: string) => {
    switch (type) {
      case 'successful': return <CheckCircle className="w-5 h-5 text-green-400" />;
      case 'failed': return <XCircle className="w-5 h-5 text-red-400" />;
      case 'pending': return <Clock className="w-5 h-5 text-yellow-400" />;
      default: return <AlertCircle className="w-5 h-5 text-gray-400" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white">
              {t('superAdmin.revenueAnalytics.title', 'Revenue Analytics')}
            </h1>
            <p className="text-gray-300 mt-2">
              {t('superAdmin.revenueAnalytics.subtitle', 'Financial performance and revenue insights')}
            </p>
          </div>
          
          <div className="flex items-center gap-4">
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white"
            >
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
              <option value="1y">Last year</option>
            </select>
            
            <button
              onClick={fetchRevenueData}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
            >
              <RefreshCw className="w-4 h-4" />
              Refresh
            </button>
            
            <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
              <Download className="w-4 h-4" />
              Export
            </button>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-1 bg-gray-800/50 p-1 rounded-lg">
          {[
            { id: 'overview', name: 'Revenue Overview', icon: DollarSign },
            { id: 'payments', name: 'Payment Analytics', icon: CreditCard },
            { id: 'subscriptions', name: 'Subscriptions', icon: Users },
            { id: 'trends', name: 'Revenue Trends', icon: TrendingUp }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center gap-2 px-4 py-2 rounded-md transition-colors ${
                activeTab === tab.id
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-300 hover:text-white hover:bg-gray-700'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              {tab.name}
            </button>
          ))}
        </div>

        {/* Overview Tab */}
        {activeTab === 'overview' && metrics && (
          <div className="space-y-6">
            {/* Key Revenue Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <DollarSign className="w-5 h-5 text-green-400" />
                    <span className="text-sm text-gray-400">Total Revenue</span>
                  </div>
                </div>
                <div className="text-2xl font-bold text-white mb-1">
                  {formatCurrency(metrics.total_revenue)}
                </div>
                <div className="flex items-center gap-1 text-sm">
                  {getGrowthIcon(metrics.revenue_growth)}
                  <span className={getGrowthColor(metrics.revenue_growth)}>
                    {metrics.revenue_growth > 0 ? '+' : ''}{formatPercentage(metrics.revenue_growth)}
                  </span>
                </div>
              </div>

              <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Calendar className="w-5 h-5 text-blue-400" />
                    <span className="text-sm text-gray-400">Monthly Revenue</span>
                  </div>
                </div>
                <div className="text-2xl font-bold text-white mb-1">
                  {formatCurrency(metrics.monthly_revenue)}
                </div>
                <div className="text-sm text-gray-400">
                  MRR: {formatCurrency(metrics.mrr)}
                </div>
              </div>

              <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Target className="w-5 h-5 text-purple-400" />
                    <span className="text-sm text-gray-400">Avg Order Value</span>
                  </div>
                </div>
                <div className="text-2xl font-bold text-white mb-1">
                  {formatCurrency(metrics.average_order_value)}
                </div>
                <div className="text-sm text-gray-400">
                  CLV: {formatCurrency(metrics.customer_lifetime_value)}
                </div>
              </div>

              <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Zap className="w-5 h-5 text-yellow-400" />
                    <span className="text-sm text-gray-400">Conversion Rate</span>
                  </div>
                </div>
                <div className="text-2xl font-bold text-white mb-1">
                  {formatPercentage(metrics.conversion_rate)}
                </div>
                <div className="text-sm text-gray-400">
                  Churn: {formatPercentage(metrics.churn_rate)}
                </div>
              </div>
            </div>

            {/* Revenue Breakdown */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
                <h3 className="text-lg font-semibold text-white mb-4">Revenue Breakdown</h3>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Annual Recurring Revenue (ARR)</span>
                    <span className="text-green-400 font-bold">{formatCurrency(metrics.arr)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Monthly Recurring Revenue (MRR)</span>
                    <span className="text-blue-400 font-bold">{formatCurrency(metrics.mrr)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Daily Revenue</span>
                    <span className="text-purple-400 font-bold">{formatCurrency(metrics.daily_revenue)}</span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
                <h3 className="text-lg font-semibold text-white mb-4">Performance Metrics</h3>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Customer Lifetime Value</span>
                    <span className="text-green-400 font-bold">{formatCurrency(metrics.customer_lifetime_value)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Average Order Value</span>
                    <span className="text-blue-400 font-bold">{formatCurrency(metrics.average_order_value)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Revenue Growth Rate</span>
                    <span className={`font-bold ${getGrowthColor(metrics.revenue_growth)}`}>
                      {metrics.revenue_growth > 0 ? '+' : ''}{formatPercentage(metrics.revenue_growth)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Payment Analytics Tab */}
        {activeTab === 'payments' && payments && (
          <div className="space-y-6">
            {/* Payment Overview */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
                <div className="flex items-center gap-2 mb-2">
                  <CreditCard className="w-5 h-5 text-blue-400" />
                  <span className="text-sm text-gray-400">Total Transactions</span>
                </div>
                <div className="text-2xl font-bold text-white">
                  {payments.total_transactions.toLocaleString()}
                </div>
              </div>

              <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span className="text-sm text-gray-400">Successful</span>
                </div>
                <div className="text-2xl font-bold text-green-400">
                  {payments.successful_payments.toLocaleString()}
                </div>
                <div className="text-sm text-gray-400">
                  {formatPercentage(payments.success_rate)} success rate
                </div>
              </div>

              <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
                <div className="flex items-center gap-2 mb-2">
                  <XCircle className="w-5 h-5 text-red-400" />
                  <span className="text-sm text-gray-400">Failed</span>
                </div>
                <div className="text-2xl font-bold text-red-400">
                  {payments.failed_payments.toLocaleString()}
                </div>
                <div className="text-sm text-gray-400">
                  {payments.refunds} refunds, {payments.chargebacks} chargebacks
                </div>
              </div>

              <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
                <div className="flex items-center gap-2 mb-2">
                  <DollarSign className="w-5 h-5 text-purple-400" />
                  <span className="text-sm text-gray-400">Avg Transaction</span>
                </div>
                <div className="text-2xl font-bold text-white">
                  {formatCurrency(payments.average_transaction_value)}
                </div>
              </div>
            </div>

            {/* Payment Methods */}
            <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
              <h3 className="text-lg font-semibold text-white mb-4">Payment Methods</h3>
              <div className="space-y-4">
                {payments.payment_methods.map((method, index) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-gray-700/50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <CreditCard className="w-5 h-5 text-blue-400" />
                      <div>
                        <div className="font-medium text-white">{method.method}</div>
                        <div className="text-sm text-gray-400">{method.count.toLocaleString()} transactions</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-green-400">{formatCurrency(method.amount)}</div>
                      <div className="text-sm text-gray-400">{formatPercentage(method.success_rate)} success</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
    </div>
  );
};

export default RevenueAnalytics;
