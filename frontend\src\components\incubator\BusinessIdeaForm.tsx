import React, { useState } from 'react';
import { useAppDispatch, useAppSelector } from '../../store/hooks';
import { createBusinessIdea } from '../../store/incubatorSlice';
import { useTranslation } from 'react-i18next';

interface BusinessIdeaFormProps {
  onSubmitSuccess: () => void;
  onCancel: () => void;
}

const BusinessIdeaForm: React.FC<BusinessIdeaFormProps> = ({ onSubmitSuccess, onCancel }) => {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector(state => state.auth);
  const { isLoading, error } = useAppSelector(state => state.incubator);
  const { t } = useTranslation();

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    problem_statement: '',
    solution_description: '',
    target_audience: '',
    market_opportunity: '',
    business_model: '',
    current_stage: 'concept',
  });

  const [formError, setFormError] = useState<string | null>(null);
  const [formSuccess, setFormSuccess] = useState<string | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);

    // Validate form
    if (!formData.title.trim()) {
      setFormError(t('incubator.businessIdea.titleRequired'));
      return;
    }

    if (!formData.problem_statement.trim()) {
      setFormError(t('incubator.businessIdea.problemRequired'));
      return;
    }

    if (!formData.solution_description.trim()) {
      setFormError(t('incubator.businessIdea.solutionRequired'));
      return;
    }

    if (!formData.target_audience.trim()) {
      setFormError(t('incubator.businessIdea.audienceRequired'));
      return;
    }

    if (!user) {
      setFormError(t('incubator.businessIdea.loginRequired'));
      return;
    }

    try {
      // Dispatch action to create business idea
      await dispatch(createBusinessIdea({
        ...formData,
        owner_id: user.id
      })).unwrap();

      setFormSuccess(t('incubator.businessIdea.submitSuccess'));

      // Reset form
      setFormData({
        title: '',
        description: '',
        problem_statement: '',
        solution_description: '',
        target_audience: '',
        market_opportunity: '',
        business_model: '',
        current_stage: 'concept',
      });

      // Close modal after success
      setTimeout(() => {
        onSubmitSuccess();
      }, 1500);
    } catch (err) {
      setFormError(err instanceof Error ? err.message : t('incubator.businessIdea.submitError'));
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {formError && (
        <div className="bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded-md">
          {formError}
        </div>
      )}

      {formSuccess && (
        <div className="bg-green-900/50 border border-green-500 text-green-200 px-4 py-3 rounded-md">
          {formSuccess}
        </div>
      )}

      <div>
        <label htmlFor="title" className="block text-sm font-medium mb-1">
          {t('incubator.businessIdea.title')} <span className="text-red-400">*</span>
        </label>
        <input
          type="text"
          id="title"
          name="title"
          value={formData.title}
          onChange={handleInputChange}
          className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
          placeholder={t('incubator.businessIdea.titlePlaceholder')}
          required
        />
      </div>

      <div>
        <label htmlFor="description" className="block text-sm font-medium mb-1">
          {t('incubator.businessIdea.description')}
        </label>
        <textarea
          id="description"
          name="description"
          value={formData.description}
          onChange={handleInputChange}
          rows={3}
          className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
          placeholder={t('incubator.businessIdea.descriptionPlaceholder')}
        />
      </div>

      <div>
        <label htmlFor="problem_statement" className="block text-sm font-medium mb-1">
          {t('incubator.businessIdea.problemStatement')} <span className="text-red-400">*</span>
        </label>
        <textarea
          id="problem_statement"
          name="problem_statement"
          value={formData.problem_statement}
          onChange={handleInputChange}
          rows={3}
          className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
          placeholder={t('incubator.businessIdea.problemPlaceholder')}
          required
        />
      </div>

      <div>
        <label htmlFor="solution_description" className="block text-sm font-medium mb-1">
          {t('incubator.businessIdea.solutionDescription')} <span className="text-red-400">*</span>
        </label>
        <textarea
          id="solution_description"
          name="solution_description"
          value={formData.solution_description}
          onChange={handleInputChange}
          rows={3}
          className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
          placeholder={t('incubator.businessIdea.solutionPlaceholder')}
          required
        />
      </div>

      <div>
        <label htmlFor="target_audience" className="block text-sm font-medium mb-1">
          {t('incubator.businessIdea.targetAudience')} <span className="text-red-400">*</span>
        </label>
        <textarea
          id="target_audience"
          name="target_audience"
          value={formData.target_audience}
          onChange={handleInputChange}
          rows={2}
          className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
          placeholder={t('incubator.businessIdea.audiencePlaceholder')}
          required
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label htmlFor="market_opportunity" className="block text-sm font-medium mb-1">
            {t('incubator.businessIdea.marketOpportunity')}
          </label>
          <textarea
            id="market_opportunity"
            name="market_opportunity"
            value={formData.market_opportunity}
            onChange={handleInputChange}
            rows={2}
            className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
            placeholder={t('incubator.businessIdea.marketPlaceholder')}
          />
        </div>

        <div>
          <label htmlFor="business_model" className="block text-sm font-medium mb-1">
            {t('incubator.businessIdea.businessModel')}
          </label>
          <textarea
            id="business_model"
            name="business_model"
            value={formData.business_model}
            onChange={handleInputChange}
            rows={2}
            className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
            placeholder={t('incubator.businessIdea.modelPlaceholder')}
          />
        </div>
      </div>

      <div>
        <label htmlFor="current_stage" className="block text-sm font-medium mb-1">
          {t('incubator.businessIdea.currentStage')}
        </label>
        <select
          id="current_stage"
          name="current_stage"
          value={formData.current_stage}
          onChange={handleInputChange}
          className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
        >
          <option value="concept">{t('incubator.businessIdea.stages.concept')}</option>
          <option value="validation">{t('incubator.businessIdea.stages.validation')}</option>
          <option value="development">{t('incubator.businessIdea.stages.development')}</option>
          <option value="scaling">{t('incubator.businessIdea.stages.scaling')}</option>
          <option value="established">{t('incubator.businessIdea.stages.established')}</option>
        </select>
      </div>

      <div className="flex justify-end space-x-4 pt-4">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-600 rounded-lg hover:bg-gray-800 transition-colors"
        >
          {t('common.cancel')}
        </button>
        <button
          type="submit"
          disabled={isLoading}
          className="px-4 py-2 bg-purple-600 rounded-lg hover:bg-purple-700 transition-colors flex items-center"
        >
          {isLoading ? (
            <>
              <span className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></span>
              {t('incubator.businessIdea.submitting')}
            </>
          ) : (
            t('incubator.businessIdea.submitIdea')
          )}
        </button>
      </div>
    </form>
  );
};

export default BusinessIdeaForm;
