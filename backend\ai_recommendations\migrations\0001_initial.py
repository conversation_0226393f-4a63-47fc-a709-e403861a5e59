# Generated by Django 5.1.7 on 2025-06-10 10:05

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("incubator", "0010_alter_businessplantemplate_template_type_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="AIRecommendation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "recommendation_type",
                    models.Char<PERSON>ield(
                        choices=[
                            ("milestone", "Milestone"),
                            ("goal", "Goal"),
                            ("mentor", "Mentor"),
                            ("resource", "Resource"),
                            ("strategy", "Strategy"),
                            ("general", "General"),
                        ],
                        default="general",
                        max_length=20,
                    ),
                ),
                ("title", models.Char<PERSON>ield(max_length=200)),
                ("description", models.TextField()),
                (
                    "reasoning",
                    models.TextField(
                        help_text="AI's reasoning for this recommendation"
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                            ("critical", "Critical"),
                        ],
                        default="medium",
                        max_length=20,
                    ),
                ),
                (
                    "action_items",
                    models.TextField(help_text="Specific action items to implement"),
                ),
                (
                    "expected_outcome",
                    models.TextField(
                        help_text="Expected outcome of implementing this recommendation"
                    ),
                ),
                (
                    "relevance_score",
                    models.FloatField(
                        default=0.0, help_text="Relevance score from 0-100"
                    ),
                ),
                ("is_implemented", models.BooleanField(default=False)),
                ("implementation_notes", models.TextField(blank=True, null=True)),
                ("implemented_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "business_idea",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="ai_recommendations",
                        to="incubator.businessidea",
                    ),
                ),
                (
                    "implemented_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="implemented_recommendations",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="AIRecommendationFeedback",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "feedback_type",
                    models.CharField(
                        choices=[
                            ("helpful", "Helpful"),
                            ("not_helpful", "Not Helpful"),
                            ("implemented", "Implemented"),
                            ("not_applicable", "Not Applicable"),
                        ],
                        max_length=20,
                    ),
                ),
                ("comments", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "recommendation",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="feedback",
                        to="ai_recommendations.airecommendation",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.AddIndex(
            model_name="airecommendation",
            index=models.Index(
                fields=["business_idea", "recommendation_type"],
                name="ai_recommen_busines_4e197c_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="airecommendation",
            index=models.Index(
                fields=["priority", "relevance_score"],
                name="ai_recommen_priorit_0ba0d8_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="airecommendation",
            index=models.Index(
                fields=["is_implemented"], name="ai_recommen_is_impl_aadd20_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="airecommendationfeedback",
            unique_together={("recommendation", "user")},
        ),
    ]
