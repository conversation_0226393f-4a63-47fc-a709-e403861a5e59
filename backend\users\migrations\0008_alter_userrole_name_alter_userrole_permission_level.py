# Generated by Django 5.1.7 on 2025-06-12 06:47

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0007_add_language_to_userprofile"),
    ]

    operations = [
        migrations.AlterField(
            model_name="userrole",
            name="name",
            field=models.CharField(
                choices=[
                    ("super_admin", "Super Administrator"),
                    ("admin", "Administrator"),
                    ("moderator", "Moderator"),
                    ("mentor", "Mentor"),
                    ("investor", "Investor"),
                    ("user", "Regular User"),
                ],
                max_length=50,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="userrole",
            name="permission_level",
            field=models.CharField(
                choices=[
                    ("read", "Read Only"),
                    ("write", "Read & Write"),
                    ("moderate", "Moderate Content"),
                    ("admin", "Full Admin Access"),
                    ("super_admin", "Super Admin System Control"),
                ],
                default="read",
                max_length=20,
            ),
        ),
    ]
