import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import {
  ForumCategory, ForumTopic, ForumThread, ForumPost, UserReputation, ReputationActivity,
  forumCategoriesAPI, forumTopicsAPI, forumThreadsAPI, forumPostsAPI, userReputationAPI
} from '../services/forumApi';

// Define the state interface
interface ForumState {
  categories: ForumCategory[];
  topics: ForumTopic[];
  threads: ForumThread[];
  posts: ForumPost[];
  currentCategory: ForumCategory | null;
  currentTopic: ForumTopic | null;
  currentThread: ForumThread | null;
  userReputation: UserReputation | null;
  reputationLeaderboard: UserReputation[];
  reputationActivities: ReputationActivity[];
  loading: boolean;
  error: string | null;
}

// Initial state
const initialState: ForumState = {
  categories: [],
  topics: [],
  threads: [],
  posts: [],
  currentCategory: null,
  currentTopic: null,
  currentThread: null,
  userReputation: null,
  reputationLeaderboard: [],
  reputationActivities: [],
  loading: false,
  error: null,
};

// Async thunks
export const fetchCategories = createAsyncThunk(
  'forum/fetchCategories',
  async (_, { rejectWithValue }) => {
    try {
      const categories = await forumCategoriesAPI.getCategories();
      console.log('Fetched categories:', categories);
      return categories;
    } catch (error: any) {
      console.error('Error fetching categories:', error);
      return rejectWithValue(error.message || 'Failed to fetch forum categories');
    }
  }
);

export const fetchCategory = createAsyncThunk(
  'forum/fetchCategory',
  async (slug: string) => {
    return await forumCategoriesAPI.getCategory(slug);
  }
);

export const fetchTopics = createAsyncThunk(
  'forum/fetchTopics',
  async (categorySlug?: string) => {
    return await forumTopicsAPI.getTopics(categorySlug);
  }
);

export const fetchTopic = createAsyncThunk(
  'forum/fetchTopic',
  async (slug: string) => {
    return await forumTopicsAPI.getTopic(slug);
  }
);

export const fetchThreads = createAsyncThunk(
  'forum/fetchThreads',
  async (topicId?: number) => {
    return await forumThreadsAPI.getThreads(topicId);
  }
);

export const fetchThread = createAsyncThunk(
  'forum/fetchThread',
  async (id: number) => {
    return await forumThreadsAPI.getThread(id);
  }
);

export const fetchPosts = createAsyncThunk(
  'forum/fetchPosts',
  async (threadId?: number) => {
    return await forumPostsAPI.getPosts(threadId);
  }
);

export const createThread = createAsyncThunk(
  'forum/createThread',
  async (threadData: Partial<ForumThread> & { author_id: number, topic: number }) => {
    return await forumThreadsAPI.createThread(threadData);
  }
);

export const createPost = createAsyncThunk(
  'forum/createPost',
  async (postData: { thread: number, author_id: number, content: string }) => {
    return await forumPostsAPI.createPost(postData);
  }
);

export const likePost = createAsyncThunk(
  'forum/likePost',
  async (id: number, { dispatch }) => {
    const result = await forumPostsAPI.likePost(id);
    // Refresh the thread to get updated post data
    dispatch(fetchThread((await forumPostsAPI.getPost(id)).thread));
    return result;
  }
);

export const unlikePost = createAsyncThunk(
  'forum/unlikePost',
  async (id: number, { dispatch }) => {
    const result = await forumPostsAPI.unlikePost(id);
    // Refresh the thread to get updated post data
    dispatch(fetchThread((await forumPostsAPI.getPost(id)).thread));
    return result;
  }
);

export const markAsSolution = createAsyncThunk(
  'forum/markAsSolution',
  async (id: number, { dispatch }) => {
    const result = await forumPostsAPI.markAsSolution(id);
    // Refresh the thread to get updated post data
    dispatch(fetchThread((await forumPostsAPI.getPost(id)).thread));
    return result;
  }
);

export const fetchUserReputation = createAsyncThunk(
  'forum/fetchUserReputation',
  async () => {
    return await userReputationAPI.getMyReputation();
  }
);

export const fetchReputationLeaderboard = createAsyncThunk(
  'forum/fetchReputationLeaderboard',
  async (_, { rejectWithValue }) => {
    try {
      const leaderboard = await userReputationAPI.getLeaderboard();
      console.log('Fetched leaderboard:', leaderboard);
      return leaderboard;
    } catch (error: any) {
      console.error('Error fetching leaderboard:', error);
      return rejectWithValue(error.message || 'Failed to fetch reputation leaderboard');
    }
  }
);

export const fetchReputationActivities = createAsyncThunk(
  'forum/fetchReputationActivities',
  async () => {
    const response = await userReputationAPI.getMyActivities();
    return response.results || [];
  }
);

// Create the slice
const forumSlice = createSlice({
  name: 'forum',
  initialState,
  reducers: {
    clearCurrentCategory: (state) => {
      state.currentCategory = null;
    },
    clearCurrentTopic: (state) => {
      state.currentTopic = null;
    },
    clearCurrentThread: (state) => {
      state.currentThread = null;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch categories
      .addCase(fetchCategories.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCategories.fulfilled, (state, action: PayloadAction<ForumCategory[]>) => {
        state.loading = false;
        state.categories = action.payload;
      })
      .addCase(fetchCategories.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch categories';
      })

      // Fetch single category
      .addCase(fetchCategory.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCategory.fulfilled, (state, action: PayloadAction<ForumCategory>) => {
        state.loading = false;
        state.currentCategory = action.payload;
      })
      .addCase(fetchCategory.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch category';
      })

      // Fetch topics
      .addCase(fetchTopics.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTopics.fulfilled, (state, action: PayloadAction<ForumTopic[]>) => {
        state.loading = false;
        state.topics = action.payload;
      })
      .addCase(fetchTopics.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch topics';
      })

      // Fetch single topic
      .addCase(fetchTopic.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTopic.fulfilled, (state, action: PayloadAction<ForumTopic>) => {
        state.loading = false;
        state.currentTopic = action.payload;
      })
      .addCase(fetchTopic.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch topic';
      })

      // Fetch threads
      .addCase(fetchThreads.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchThreads.fulfilled, (state, action: PayloadAction<ForumThread[]>) => {
        state.loading = false;
        state.threads = action.payload;
      })
      .addCase(fetchThreads.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch threads';
      })

      // Fetch single thread
      .addCase(fetchThread.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchThread.fulfilled, (state, action: PayloadAction<ForumThread>) => {
        state.loading = false;
        state.currentThread = action.payload;
      })
      .addCase(fetchThread.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch thread';
      })

      // Fetch posts
      .addCase(fetchPosts.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPosts.fulfilled, (state, action: PayloadAction<ForumPost[]>) => {
        state.loading = false;
        state.posts = action.payload;
      })
      .addCase(fetchPosts.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch posts';
      })

      // Create thread
      .addCase(createThread.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createThread.fulfilled, (state, action: PayloadAction<ForumThread>) => {
        state.loading = false;
        state.threads = [action.payload, ...state.threads];
        state.currentThread = action.payload;
      })
      .addCase(createThread.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to create thread';
      })

      // Create post
      .addCase(createPost.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createPost.fulfilled, (state, action: PayloadAction<ForumPost>) => {
        state.loading = false;
        state.posts = [...state.posts, action.payload];
        if (state.currentThread) {
          state.currentThread.posts = [...(state.currentThread.posts || []), action.payload];
          state.currentThread.post_count += 1;
        }
      })
      .addCase(createPost.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to create post';
      })

      // Fetch user reputation
      .addCase(fetchUserReputation.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUserReputation.fulfilled, (state, action: PayloadAction<UserReputation>) => {
        state.loading = false;
        state.userReputation = action.payload;
      })
      .addCase(fetchUserReputation.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch user reputation';
      })

      // Fetch reputation leaderboard
      .addCase(fetchReputationLeaderboard.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchReputationLeaderboard.fulfilled, (state, action: PayloadAction<UserReputation[]>) => {
        state.loading = false;
        state.reputationLeaderboard = action.payload;
      })
      .addCase(fetchReputationLeaderboard.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch reputation leaderboard';
      })

      // Fetch reputation activities
      .addCase(fetchReputationActivities.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchReputationActivities.fulfilled, (state, action: PayloadAction<ReputationActivity[]>) => {
        state.loading = false;
        state.reputationActivities = action.payload;
      })
      .addCase(fetchReputationActivities.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch reputation activities';
      });
  },
});

export const { clearCurrentCategory, clearCurrentTopic, clearCurrentThread, clearError } = forumSlice.actions;

export default forumSlice.reducer;
