from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, permissions
from django.utils import timezone
from django.db.models import Q

from incubator.models import BusinessIdea
from incubator.models_analytics import ComparativeAnalytics
from .competitive_analysis_service import generate_competitive_analysis, generate_market_trends_analysis
from api.permissions import IsAdminUser, IsOwnerOrAdmin

class CompetitiveAnalysisView(APIView):
    """View for generating and retrieving competitive analysis"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request, business_idea_id):
        """Get competitive analysis for a business idea"""
        try:
            business_idea = BusinessIdea.objects.get(id=business_idea_id)
            
            # Check permissions
            if not (request.user.is_staff or request.user.is_superuser or 
                    business_idea.owner == request.user or 
                    business_idea.collaborators.filter(id=request.user.id).exists()):
                return Response(
                    {"error": "You do not have permission to view this business idea's analysis"},
                    status=status.HTTP_403_FORBIDDEN
                )
                
            # Get or create comparative analytics
            try:
                comparative = ComparativeAnalytics.objects.get(business_idea=business_idea)
                
                # Check if competitive advantages and disadvantages exist
                has_competitive_data = (
                    comparative.competitive_advantages and 
                    comparative.competitive_disadvantages
                )
                
                if has_competitive_data:
                    return Response({
                        'business_idea_id': business_idea.id,
                        'business_idea_title': business_idea.title,
                        'competitive_advantages': comparative.competitive_advantages,
                        'competitive_disadvantages': comparative.competitive_disadvantages,
                        'last_updated': comparative.last_calculated
                    })
                else:
                    # Generate competitive analysis if it doesn't exist
                    analysis = generate_competitive_analysis(business_idea_id)
                    
                    if not analysis:
                        return Response(
                            {"error": "Failed to generate competitive analysis. Please try again later."},
                            status=status.HTTP_500_INTERNAL_SERVER_ERROR
                        )
                    
                    return Response({
                        'business_idea_id': business_idea.id,
                        'business_idea_title': business_idea.title,
                        'analysis': analysis,
                        'competitive_advantages': comparative.competitive_advantages,
                        'competitive_disadvantages': comparative.competitive_disadvantages,
                        'last_updated': comparative.last_calculated
                    })
                    
            except ComparativeAnalytics.DoesNotExist:
                # Generate competitive analysis
                analysis = generate_competitive_analysis(business_idea_id)
                
                if not analysis:
                    return Response(
                        {"error": "Failed to generate competitive analysis. Please try again later."},
                        status=status.HTTP_500_INTERNAL_SERVER_ERROR
                    )
                
                # Get the newly created comparative analytics
                comparative = ComparativeAnalytics.objects.get(business_idea=business_idea)
                
                return Response({
                    'business_idea_id': business_idea.id,
                    'business_idea_title': business_idea.title,
                    'analysis': analysis,
                    'competitive_advantages': comparative.competitive_advantages,
                    'competitive_disadvantages': comparative.competitive_disadvantages,
                    'last_updated': comparative.last_calculated
                })
                
        except BusinessIdea.DoesNotExist:
            return Response(
                {"error": "Business idea not found"},
                status=status.HTTP_404_NOT_FOUND
            )
            
    def post(self, request, business_idea_id):
        """Generate a new competitive analysis for a business idea"""
        try:
            business_idea = BusinessIdea.objects.get(id=business_idea_id)
            
            # Check permissions
            if not (request.user.is_staff or request.user.is_superuser or 
                    business_idea.owner == request.user or 
                    business_idea.collaborators.filter(id=request.user.id).exists()):
                return Response(
                    {"error": "You do not have permission to generate analysis for this business idea"},
                    status=status.HTTP_403_FORBIDDEN
                )
                
            # Generate competitive analysis
            analysis = generate_competitive_analysis(business_idea_id)
            
            if not analysis:
                return Response(
                    {"error": "Failed to generate competitive analysis. Please try again later."},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
                
            # Get the updated comparative analytics
            comparative = ComparativeAnalytics.objects.get(business_idea=business_idea)
            
            return Response({
                'business_idea_id': business_idea.id,
                'business_idea_title': business_idea.title,
                'analysis': analysis,
                'competitive_advantages': comparative.competitive_advantages,
                'competitive_disadvantages': comparative.competitive_disadvantages,
                'last_updated': comparative.last_calculated
            })
            
        except BusinessIdea.DoesNotExist:
            return Response(
                {"error": "Business idea not found"},
                status=status.HTTP_404_NOT_FOUND
            )

class MarketTrendsAnalysisView(APIView):
    """View for generating and retrieving market trends analysis"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request, business_idea_id):
        """Get market trends analysis for a business idea"""
        try:
            business_idea = BusinessIdea.objects.get(id=business_idea_id)
            
            # Check permissions
            if not (request.user.is_staff or request.user.is_superuser or 
                    business_idea.owner == request.user or 
                    business_idea.collaborators.filter(id=request.user.id).exists()):
                return Response(
                    {"error": "You do not have permission to view this business idea's analysis"},
                    status=status.HTTP_403_FORBIDDEN
                )
                
            # Generate market trends analysis (always generate fresh data)
            analysis = generate_market_trends_analysis(business_idea_id)
            
            if not analysis:
                return Response(
                    {"error": "Failed to generate market trends analysis. Please try again later."},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
                
            return Response({
                'business_idea_id': business_idea.id,
                'business_idea_title': business_idea.title,
                'market_trends': analysis
            })
                
        except BusinessIdea.DoesNotExist:
            return Response(
                {"error": "Business idea not found"},
                status=status.HTTP_404_NOT_FOUND
            )
            
    def post(self, request, business_idea_id):
        """Generate a new market trends analysis for a business idea"""
        try:
            business_idea = BusinessIdea.objects.get(id=business_idea_id)
            
            # Check permissions
            if not (request.user.is_staff or request.user.is_superuser or 
                    business_idea.owner == request.user or 
                    business_idea.collaborators.filter(id=request.user.id).exists()):
                return Response(
                    {"error": "You do not have permission to generate analysis for this business idea"},
                    status=status.HTTP_403_FORBIDDEN
                )
                
            # Generate market trends analysis
            analysis = generate_market_trends_analysis(business_idea_id)
            
            if not analysis:
                return Response(
                    {"error": "Failed to generate market trends analysis. Please try again later."},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
                
            return Response({
                'business_idea_id': business_idea.id,
                'business_idea_title': business_idea.title,
                'market_trends': analysis
            })
            
        except BusinessIdea.DoesNotExist:
            return Response(
                {"error": "Business idea not found"},
                status=status.HTTP_404_NOT_FOUND
            )
