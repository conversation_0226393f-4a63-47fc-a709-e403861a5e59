import React, { useState } from 'react';
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';

import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';

interface TablePaginationProps {
  totalItems: number;
  itemsPerPage: number;
  currentPage: number;
  onPageChange: (page: number) => void;
  onItemsPerPageChange?: (itemsPerPage: number) => void;
}

const TablePagination: React.FC<TablePaginationProps> = ({ totalItems,
  itemsPerPage,
  currentPage,
  onPageChange,
  onItemsPerPageChange,
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [jumpToPage, setJumpToPage] = useState<string>('');
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  // Generate page numbers to display
  const getPageNumbers = () => {
    const pageNumbers = [];
    const maxPagesToShow = 5;

    if (totalPages <= maxPagesToShow) {
      // If we have fewer pages than our max, show all pages
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      // Always include first page
      pageNumbers.push(1);

      // Calculate start and end of page range
      let startPage = Math.max(2, currentPage - 1);
      let endPage = Math.min(totalPages - 1, currentPage + 1);

      // Adjust if we're near the beginning
      if (currentPage <= 3) {
        endPage = Math.min(totalPages - 1, 4);
      }

      // Adjust if we're near the end
      if (currentPage >= totalPages - 2) {
        startPage = Math.max(2, totalPages - 3);
      }

      // Add ellipsis after first page if needed
      if (startPage > 2) {
        pageNumbers.push('...');
      }

      // Add middle pages
      for (let i = startPage; i <= endPage; i++) {
        pageNumbers.push(i);
      }

      // Add ellipsis before last page if needed
      if (endPage < totalPages - 1) {
        pageNumbers.push('...');
      }

      // Always include last page
      pageNumbers.push(totalPages);
    }

    return pageNumbers;
  };

  const handleJumpToPage = (e: React.FormEvent) => {
    e.preventDefault();
    const pageNum = parseInt(jumpToPage);
    if (!isNaN(pageNum) && pageNum >= 1 && pageNum <= totalPages) {
      onPageChange(pageNum);
      setJumpToPage('');
    }
  };

  const pageSizeOptions = [10, 25, 50, 100];

  return (
    <div className={`flex flex-col sm:flex-row items-center justify-between gap-4 mt-4 text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
      <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
        <span className="text-gray-400">{t("admin.items.per.page", "Items per page:")}</span>
        <select
          className="bg-indigo-900/30 border border-indigo-800 rounded px-2 py-1 text-white"
          value={itemsPerPage}
          onChange={(e) => onItemsPerPageChange && onItemsPerPageChange(Number(e.target.value))}
        >
          {pageSizeOptions.map(size => (
            <option key={size} value={size}>{size}</option>
          ))}
        </select>
      </div>

      <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
        <span className={`text-gray-400 mr-4 ${isRTL ? "space-x-reverse" : ""}`}>
          {totalItems === 0
            ? t("admin.no.items", "No items")
            : t("admin.showing.items", "Showing {{start}} to {{end}} of {{total}} items", {
                start: (currentPage - 1) * itemsPerPage + 1,
                end: Math.min(currentPage * itemsPerPage, totalItems),
                total: totalItems
              })
          }
        </span>

        <div className={`flex items-center space-x-1 ${isRTL ? "flex-row-reverse" : ""}`}>
          <button
            onClick={() => onPageChange(1)}
            disabled={currentPage === 1}
            className="p-1 rounded hover:bg-indigo-800/50 disabled:opacity-50 disabled:cursor-not-allowed"
            aria-label={t("admin.first.page", "First page")}
          >
            <ChevronsLeft size={18} />
          </button>

          <button
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="p-1 rounded hover:bg-indigo-800/50 disabled:opacity-50 disabled:cursor-not-allowed"
            aria-label={t("admin.previous.page", "Previous page")}
          >
            <ChevronLeft size={18} />
          </button>

          {getPageNumbers().map((page, index) => (
            typeof page === 'number' ? (
              <button
                key={index}
                onClick={() => onPageChange(page)}
                className={`w-8 h-8 rounded ${
                  currentPage === page
                    ? 'bg-indigo-600 text-white'
                    : 'hover:bg-indigo-800/50'}
                }`}
              >
                {page}
              </button>
            ) : (
              <span key={index} className="px-1">
                {page}
              </span>
            )
          ))}

          <button
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage === totalPages || totalPages === 0}
            className="p-1 rounded hover:bg-indigo-800/50 disabled:opacity-50 disabled:cursor-not-allowed"
            aria-label={t("admin.next.page", "Next page")}
          >
            <ChevronRight size={18} />
          </button>

          <button
            onClick={() => onPageChange(totalPages)}
            disabled={currentPage === totalPages || totalPages === 0}
            className="p-1 rounded hover:bg-indigo-800/50 disabled:opacity-50 disabled:cursor-not-allowed"
            aria-label={t("admin.last.page", "Last page")}
          >
            <ChevronsRight size={18} />
          </button>
        </div>
      </div>

      <form onSubmit={handleJumpToPage} className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
        <span className="text-gray-400">{t("admin.go.to", "Go to:")}</span>
        <input
          type="text"
          value={jumpToPage}
          onChange={(e) => setJumpToPage(e.target.value)}
          className="bg-indigo-900/30 border border-indigo-800 rounded px-2 py-1 w-16 text-white"
          placeholder={t("admin.page", "Page")}
        />
        <button
          type="submit"
          className="px-2 py-1 bg-indigo-600 hover:bg-indigo-700 rounded text-white"
        >
          {t("admin.go", "Go")}
        </button>
      </form>
    </div>
  );
};

export default TablePagination;
