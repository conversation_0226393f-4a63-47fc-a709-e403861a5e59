import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import {
  RTLInput,
  RTLFlex,
  RTLIcon,
  RTLText
} from '.';
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Clock,
  DollarSign,
  Home,
  Check,
  AlertCircle
} from 'lucide-react';
import {
  validatePhoneNumber,
  validatePostalCode,
  validateDate,
  validateTime,
  validateCurrency,
  validateName,
  validateAddress,
  validateEmail,
  getValidationPatterns
} from '../../utils/localeValidation';

interface LocaleValidationFormProps {
  className?: string;
}

const LocaleValidationForm: React.FC<LocaleValidationFormProps> = ({ className = ''  }) => {
  const { t } = useTranslation();
  const { language, isRTL } = useLanguage();

  // Helper function for margin classes
  const getMarginClass = (direction: 'right' | 'left', size: number) => {
    if (direction === 'right') {
      return isRTL ? `ml-${size}` : `mr-${size}`;
    } else {
      return isRTL ? `mr-${size}` : `ml-${size}`;
    }
  };

  // Form values
  const [formValues, setFormValues] = useState({
    name: '',
    email: '',
    phone: '',
    postalCode: '',
    date: '',
    time: '',
    currency: '',
    address: '',
  });

  // Form errors
  const [formErrors, setFormErrors] = useState({
    name: '',
    email: '',
    phone: '',
    postalCode: '',
    date: '',
    time: '',
    currency: '',
    address: '',
  });

  // Validation patterns
  const [patterns, setPatterns] = useState(getValidationPatterns());

  // Update patterns when language changes
  useEffect(() => {
    setPatterns(getValidationPatterns(language));

    // Clear form values and errors when language changes
    setFormValues({
      name: '',
      email: '',
      phone: '',
      postalCode: '',
      date: '',
      time: '',
      currency: '',
      address: '',
    });

    setFormErrors({
      name: '',
      email: '',
      phone: '',
      postalCode: '',
      date: '',
      time: '',
      currency: '',
      address: '',
    });
  }, [language]);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    setFormValues(prev => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when user types
    if (formErrors[name as keyof typeof formErrors]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: '',
      }));
    }
  };

  // Validate a field
  const validateField = (name: string, value: string): string => {
    if (!value.trim()) {
      return t('validation.required', { field: t(`fields.${name}`) });
    }

    let isValid = false;

    switch (name) {
      case 'name':
        isValid = validateName(value, language);
        break;
      case 'email':
        isValid = validateEmail(value);
        break;
      case 'phone':
        isValid = validatePhoneNumber(value, language);
        break;
      case 'postalCode':
        isValid = validatePostalCode(value, language);
        break;
      case 'date':
        isValid = validateDate(value, language);
        break;
      case 'time':
        isValid = validateTime(value, language);
        break;
      case 'currency':
        isValid = validateCurrency(value, language);
        break;
      case 'address':
        isValid = validateAddress(value, language);
        break;
      default:
        isValid = true;
    }

    return isValid ? '' : t('validation.pattern', { field: t(`fields.${name}`) });
  };

  // Handle field blur (validate on blur)
  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    const error = validateField(name, value);

    setFormErrors(prev => ({
      ...prev,
      [name]: error,
    }));
  };

  // Handle form submit
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate all fields
    const errors = {
      name: validateField('name', formValues.name),
      email: validateField('email', formValues.email),
      phone: validateField('phone', formValues.phone),
      postalCode: validateField('postalCode', formValues.postalCode),
      date: validateField('date', formValues.date),
      time: validateField('time', formValues.time),
      currency: validateField('currency', formValues.currency),
      address: validateField('address', formValues.address),
    };

    setFormErrors(errors);

    // Check if there are any errors
    const hasErrors = Object.values(errors).some(error => error !== '');

    if (!hasErrors) {
      // Form is valid, you can submit it
      alert(t('validation.formValid'));
    }
  };

  // Get placeholder text based on current language - using generic placeholders
  const getPlaceholder = (field: string): string => {
    if (language === 'en') {
      switch (field) {
        case 'name': return t("common.placeholder.name", "Enter your name");
        case 'email': return t("common.placeholder.email", "Enter your email");
        case 'phone': return t("common.placeholder.phone", "Enter phone number");
        case 'postalCode': return t("common.placeholder.postalCode", "Enter postal code");
        case 'date': return t("common.placeholder.date", "Select date");
        case 'time': return t("common.placeholder.time", "Select time");
        case 'currency': return t("common.placeholder.currency", "Enter amount");
        case 'address': return t("common.placeholder.address", "Enter address");
        default: return '';
      }
    } else if (language === 'ar') {
      switch (field) {
        case 'name': return 'محمد أحمد';
        case 'email': return '<EMAIL>';
        case 'phone': return '0512345678';
        case 'postalCode': return '12345';
        case 'date': return 'DD/MM/YYYY';
        case 'time': return t("common.hhmm", "HH:MM");
        case 'currency': return '123.45';
        case 'address': return 'شارع الرئيسي، المدينة';
        default: return '';
      }
    }

    return '';
  };

  return (
    <div className={`${className}`}>
      <RTLFlex as="h2" className="text-xl font-semibold mb-4" align="center">
        <RTLIcon icon={Check} size={20} className={getMarginClass('right', 2)} />
        <span>{t('validation.title')}</span>
      </RTLFlex>

      <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50 mb-6">
        <RTLText className="mb-4">
          {t('validation.description')}
        </RTLText>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div className="bg-indigo-800/30 p-3 rounded-lg">
            <h3 className="font-medium mb-2">{t('validation.currentPatterns')}</h3>
            <div className="text-sm space-y-1">
              <p><strong>{t('fields.name')}:</strong> <code>{patterns.name.toString()}</code></p>
              <p><strong>{t('fields.phone')}:</strong> <code>{patterns.phoneNumber.toString()}</code></p>
              <p><strong>{t('fields.postalCode')}:</strong> <code>{patterns.postalCode.toString()}</code></p>
              <p><strong>{t('fields.date')}:</strong> <code>{patterns.date.toString()}</code></p>
              <p><strong>{t('fields.time')}:</strong> <code>{patterns.time.toString()}</code></p>
              <p><strong>{t('fields.currency')}:</strong> <code>{patterns.currency.toString()}</code></p>
            </div>
          </div>

          <div className="bg-indigo-800/30 p-3 rounded-lg">
            <h3 className="font-medium mb-2">{t('validation.instructions')}</h3>
            <ul className="text-sm list-disc list-inside space-y-1">
              <li>{t('validation.instructionLanguage')}</li>
              <li>{t('validation.instructionFormat')}</li>
              <li>{t('validation.instructionValidation')}</li>
              <li>{t('validation.instructionSubmit')}</li>
            </ul>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <RTLInput
            name="name"
            label={t('fields.name')}
            placeholder={getPlaceholder('name')}
            value={formValues.name}
            onChange={handleInputChange}
            onBlur={handleBlur}
            error={formErrors.name}
            icon={<User size={18} />}
          />

          <RTLInput
            name="email"
            label={t('fields.email')}
            placeholder={getPlaceholder('email')}
            value={formValues.email}
            onChange={handleInputChange}
            onBlur={handleBlur}
            error={formErrors.email}
            icon={<Mail size={18} />}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <RTLInput
            name="phone"
            label={t('fields.phone')}
            placeholder={getPlaceholder('phone')}
            value={formValues.phone}
            onChange={handleInputChange}
            onBlur={handleBlur}
            error={formErrors.phone}
            icon={<Phone size={18} />}
          />

          <RTLInput
            name="postalCode"
            label={t('fields.postalCode')}
            placeholder={getPlaceholder('postalCode')}
            value={formValues.postalCode}
            onChange={handleInputChange}
            onBlur={handleBlur}
            error={formErrors.postalCode}
            icon={<MapPin size={18} />}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <RTLInput
            name="date"
            label={t('fields.date')}
            placeholder={getPlaceholder('date')}
            value={formValues.date}
            onChange={handleInputChange}
            onBlur={handleBlur}
            error={formErrors.date}
            icon={<Calendar size={18} />}
          />

          <RTLInput
            name="time"
            label={t('fields.time')}
            placeholder={getPlaceholder('time')}
            value={formValues.time}
            onChange={handleInputChange}
            onBlur={handleBlur}
            error={formErrors.time}
            icon={<Clock size={18} />}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <RTLInput
            name="currency"
            label={t('fields.currency')}
            placeholder={getPlaceholder('currency')}
            value={formValues.currency}
            onChange={handleInputChange}
            onBlur={handleBlur}
            error={formErrors.currency}
            icon={<DollarSign size={18} />}
          />

          <RTLInput
            name="address"
            label={t('fields.address')}
            placeholder={getPlaceholder('address')}
            value={formValues.address}
            onChange={handleInputChange}
            onBlur={handleBlur}
            error={formErrors.address}
            icon={<Home size={18} />}
          />
        </div>

        <div className={`flex justify-end ${isRTL ? "flex-row-reverse" : ""}`}>
          <button
            type="submit"
            className="bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
          >
            {t('validation.submit')}
          </button>
        </div>
      </form>
    </div>
  );
};

export default LocaleValidationForm;
