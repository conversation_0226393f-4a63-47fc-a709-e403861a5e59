import { useState, useEffect } from 'react';
import { Event, eventsAPI } from '../../../../services/api';

/**
 * Custom hook to fetch upcoming events
 * @param limit Maximum number of events to fetch
 * @returns Object containing upcoming events and loading state
 */
const useUpcomingEvents = (limit: number = 3) => {
  const [upcomingEvents, setUpcomingEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUpcomingEvents = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const events = await eventsAPI.getEvents();
        
        // Filter for upcoming events, sort by date, and limit to specified count
        const sortedEvents = events
          .filter(event => new Date(event.date) > new Date())
          .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
          .slice(0, limit);
          
        setUpcomingEvents(sortedEvents);
      } catch (err) {
        console.error('Error fetching upcoming events:', err);
        setError('Failed to fetch upcoming events');
      } finally {
        setLoading(false);
      }
    };

    fetchUpcomingEvents();
  }, [limit]);

  return { upcomingEvents, loading, error };
};

export default useUpcomingEvents;
