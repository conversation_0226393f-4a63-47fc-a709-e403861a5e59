import React from 'react';
import {
  Line<PERSON>hart as RechartsLineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { useTranslation } from 'react-i18next';

interface ActivityTabProps {
  data: any;
  period: string;
  timeRange: number;
  onPeriodChange: (period: string) => void;
  onTimeRangeChange: (range: number) => void;
}

const ActivityTab: React.FC<ActivityTabProps> = ({ data,
  period,
  timeRange,
  onPeriodChange,
  onTimeRangeChange
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  if (!data) return <div className="text-center py-8">t("admin.no.data.available", t("common.noDataAvailable", "No data available"))</div>;

  return (
    <div className="space-y-6">
      {/* Filter Controls */}
      <div className={`bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50 flex flex-wrap gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div>
          <label className="block text-sm text-gray-400 mb-1">t("admin.time.period", "Time Period")</label>
          <select
            value={period}
            onChange={(e) => onPeriodChange(e.target.value)}
            className="bg-indigo-900/50 border border-indigo-800 rounded-lg px-3 py-2 text-sm"
          >
            <option value="day">t("admin.daily", "Daily")</option>
            <option value="week">t("admin.weekly", "Weekly")</option>
            <option value="month">t("admin.monthly", "Monthly")</option>
          </select>
        </div>
        <div>
          <label className="block text-sm text-gray-400 mb-1">t("admin.time.range", "Time Range")</label>
          <select
            value={timeRange}
            onChange={(e) => onTimeRangeChange(parseInt(e.target.value))}
            className="bg-indigo-900/50 border border-indigo-800 rounded-lg px-3 py-2 text-sm"
          >
            <option value="7">Last 7 Days</option>
            <option value="30">Last 30 Days</option>
            <option value="90">Last 90 Days</option>
            <option value="365">t("admin.last.year", "Last Year")</option>
          </select>
        </div>
      </div>

      {/* Thread Activity Chart */}
      <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
        <h3 className="text-xl font-semibold mb-4">t("admin.thread.activity", "Thread Activity")</h3>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <RechartsLineChart
              data={data.thread_activity}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#444" />
              <XAxis dataKey="period" stroke="#aaa" />
              <YAxis stroke="#aaa" />
              <Tooltip
                contentStyle={{ backgroundColor: '#1e1e3f', borderColor: '#444', color: '#fff' }}
              />
              <Legend />
              <Line type="monotone" dataKey="count" name="Threads" stroke="#8884d8" activeDot={{ r: 8 }} />
            </RechartsLineChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Post Activity Chart */}
      <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
        <h3 className="text-xl font-semibold mb-4">t("admin.post.activity", "Post Activity")</h3>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <RechartsLineChart
              data={data.post_activity}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#444" />
              <XAxis dataKey="period" stroke="#aaa" />
              <YAxis stroke="#aaa" />
              <Tooltip
                contentStyle={{ backgroundColor: '#1e1e3f', borderColor: '#444', color: '#fff' }}
              />
              <Legend />
              <Line type="monotone" dataKey="count" name="Posts" stroke="#82ca9d" activeDot={{ r: 8 }} />
            </RechartsLineChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* User Activity Chart */}
      <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
        <h3 className="text-xl font-semibold mb-4">t("admin.user.activity", "User Activity")</h3>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <RechartsLineChart
              data={data.user_activity}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#444" />
              <XAxis dataKey="period" stroke="#aaa" />
              <YAxis stroke="#aaa" />
              <Tooltip
                contentStyle={{ backgroundColor: '#1e1e3f', borderColor: '#444', color: '#fff' }}
              />
              <Legend />
              <Line type="monotone" dataKey="count" name="Active Users" stroke="#ffc658" activeDot={{ r: 8 }} />
            </RechartsLineChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
};

export default ActivityTab;
