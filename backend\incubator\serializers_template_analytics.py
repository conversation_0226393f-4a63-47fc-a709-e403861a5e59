"""
Template Analytics Serializers
Serializers for template analytics data
"""

from rest_framework import serializers
from .models_template_analytics import (
    TemplateUsageAnalytics, TemplatePerformanceMetrics,
    TemplateSectionAnalytics, UserTemplateInteraction
)
from .models_business_plan import BusinessPlanTemplate


class TemplateAnalyticsSerializer(serializers.ModelSerializer):
    """Serializer for template analytics data"""
    
    template_name = serializers.CharField(source='template.name', read_only=True)
    template_type = serializers.CharField(source='template.template_type', read_only=True)
    user_name = serializers.Char<PERSON>ield(source='user.username', read_only=True)
    
    class Meta:
        model = TemplateUsageAnalytics
        fields = [
            'id', 'template', 'template_name', 'template_type', 'user', 'user_name',
            'viewed_at', 'selected_at', 'completed_at', 'completion_time',
            'rating', 'feedback_text', 'business_plan_published',
            'funding_received', 'business_launched', 'user_agent', 'ip_address'
        ]
        read_only_fields = ['id', 'viewed_at']


class TemplatePerformanceSerializer(serializers.ModelSerializer):
    """Serializer for template performance metrics"""
    
    template_name = serializers.CharField(source='template.name', read_only=True)
    template_type = serializers.CharField(source='template.template_type', read_only=True)
    
    class Meta:
        model = TemplatePerformanceMetrics
        fields = [
            'id', 'template', 'template_name', 'template_type',
            'total_views', 'total_selections', 'total_completions', 'unique_users',
            'selection_rate', 'completion_rate', 'average_completion_time',
            'average_rating', 'total_ratings', 'net_promoter_score',
            'business_plans_published', 'funding_success_rate', 'business_launch_rate',
            'usage_trends', 'performance_trends', 'last_updated'
        ]
        read_only_fields = ['id', 'last_updated']


class TemplateSectionAnalyticsSerializer(serializers.ModelSerializer):
    """Serializer for template section analytics"""
    
    template_name = serializers.CharField(source='template.name', read_only=True)
    
    class Meta:
        model = TemplateSectionAnalytics
        fields = [
            'id', 'template', 'template_name', 'section_key', 'section_title',
            'total_views', 'total_completions', 'average_time_spent', 'completion_rate',
            'ai_assistance_used', 'content_regenerated', 'section_customized',
            'average_content_length', 'user_satisfaction_score', 'last_updated'
        ]
        read_only_fields = ['id', 'last_updated']


class UserTemplateInteractionSerializer(serializers.ModelSerializer):
    """Serializer for user template interactions"""
    
    template_name = serializers.CharField(source='template.name', read_only=True)
    user_name = serializers.CharField(source='user.username', read_only=True)
    
    class Meta:
        model = UserTemplateInteraction
        fields = [
            'id', 'user', 'user_name', 'template', 'template_name',
            'interaction_type', 'interaction_data', 'session_duration',
            'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class AnalyticsDashboardSerializer(serializers.Serializer):
    """Serializer for analytics dashboard data"""
    
    # Overview metrics
    total_templates = serializers.IntegerField()
    total_usage = serializers.IntegerField()
    average_rating = serializers.FloatField()
    completion_rate = serializers.FloatField()
    growth_rate = serializers.FloatField()
    active_users = serializers.IntegerField()
    
    # Top templates
    top_templates = serializers.ListField(
        child=serializers.DictField()
    )
    
    # Usage trends
    usage_trends = serializers.ListField(
        child=serializers.DictField()
    )
    
    # Category performance
    category_performance = serializers.ListField(
        child=serializers.DictField()
    )
    
    # User segments
    user_segments = serializers.ListField(
        child=serializers.DictField()
    )
    
    # Performance metrics
    performance_metrics = serializers.ListField(
        child=serializers.DictField()
    )
    
    # Recommendations
    recommendations = serializers.DictField()


class TemplateUsageTrendSerializer(serializers.Serializer):
    """Serializer for template usage trends"""
    
    date = serializers.DateField()
    views = serializers.IntegerField()
    selections = serializers.IntegerField()
    completions = serializers.IntegerField()
    ratings = serializers.IntegerField()
    average_rating = serializers.FloatField()


class CategoryAnalyticsSerializer(serializers.Serializer):
    """Serializer for category analytics"""
    
    category = serializers.CharField()
    template_count = serializers.IntegerField()
    total_usage = serializers.IntegerField()
    average_rating = serializers.FloatField()
    completion_rate = serializers.FloatField()
    growth_rate = serializers.FloatField()
    market_share = serializers.FloatField()


class UserSegmentAnalyticsSerializer(serializers.Serializer):
    """Serializer for user segment analytics"""
    
    segment = serializers.CharField()
    user_count = serializers.IntegerField()
    template_preferences = serializers.ListField(
        child=serializers.CharField()
    )
    completion_rate = serializers.FloatField()
    satisfaction_score = serializers.FloatField()
    retention_rate = serializers.FloatField()


class TemplateRecommendationSerializer(serializers.Serializer):
    """Serializer for template recommendations"""
    
    trending_templates = serializers.ListField(
        child=serializers.CharField()
    )
    underperforming_templates = serializers.ListField(
        child=serializers.CharField()
    )
    optimization_opportunities = serializers.ListField(
        child=serializers.CharField()
    )


class TemplateComparisonSerializer(serializers.Serializer):
    """Serializer for template comparison data"""
    
    template_id = serializers.CharField()
    template_name = serializers.CharField()
    category = serializers.CharField()
    usage_count = serializers.IntegerField()
    completion_rate = serializers.FloatField()
    average_rating = serializers.FloatField()
    user_satisfaction = serializers.FloatField()
    performance_score = serializers.FloatField()
    ranking = serializers.IntegerField()


class AnalyticsExportSerializer(serializers.Serializer):
    """Serializer for analytics export requests"""
    
    time_range = serializers.ChoiceField(
        choices=['7d', '30d', '90d', '1y', 'all'],
        default='30d'
    )
    category = serializers.CharField(required=False)
    template_id = serializers.CharField(required=False)
    format = serializers.ChoiceField(
        choices=['csv', 'xlsx', 'pdf'],
        default='csv'
    )
    include_inactive = serializers.BooleanField(default=False)
    
    def validate(self, data):
        """Validate export request data"""
        if data.get('template_id') and data.get('category'):
            raise serializers.ValidationError(
                "Cannot specify both template_id and category filters"
            )
        return data


class TemplateInsightSerializer(serializers.Serializer):
    """Serializer for template insights and recommendations"""
    
    template_id = serializers.CharField()
    template_name = serializers.CharField()
    insight_type = serializers.ChoiceField(
        choices=['performance', 'optimization', 'trend', 'user_feedback']
    )
    insight_title = serializers.CharField()
    insight_description = serializers.CharField()
    priority = serializers.ChoiceField(
        choices=['low', 'medium', 'high', 'critical']
    )
    action_items = serializers.ListField(
        child=serializers.CharField()
    )
    impact_score = serializers.FloatField()
    confidence_score = serializers.FloatField()
    created_at = serializers.DateTimeField()


class TemplateHealthScoreSerializer(serializers.Serializer):
    """Serializer for template health scores"""
    
    template_id = serializers.CharField()
    template_name = serializers.CharField()
    overall_score = serializers.FloatField()
    usage_score = serializers.FloatField()
    satisfaction_score = serializers.FloatField()
    completion_score = serializers.FloatField()
    retention_score = serializers.FloatField()
    trend_score = serializers.FloatField()
    health_status = serializers.ChoiceField(
        choices=['excellent', 'good', 'fair', 'poor', 'critical']
    )
    last_updated = serializers.DateTimeField()
