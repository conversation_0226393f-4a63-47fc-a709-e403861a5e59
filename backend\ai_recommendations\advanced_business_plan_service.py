import os
import json
import google.generativeai as genai
from django.conf import settings
from dotenv import load_dotenv
from incubator.models import BusinessIdea
from incubator.models_business_plan import BusinessPlan, BusinessPlanTemplate, BusinessPlanSection
from incubator.models_analytics import ComparativeAnalytics, PredictiveAnalytics
from django.utils import timezone
from core.ai_config import get_gemini_config, generate_gemini_content

# Load environment variables
load_dotenv()

# Use centralized configuration
def get_configured_model():
    """Get configured Gemini model"""
    config = get_gemini_config()
    return config.model if config.is_available else None

def generate_complete_business_plan(business_idea_id):
    """
    Generate a complete business plan for a business idea using Gemini AI
    
    Args:
        business_idea_id: ID of the business idea
        
    Returns:
        Dictionary containing the complete business plan
    """
    try:
        # Get business idea
        business_idea = BusinessIdea.objects.get(id=business_idea_id)
        
        # Get analytics data if available
        comparative_data = {}
        predictive_data = {}
        
        try:
            comparative = ComparativeAnalytics.objects.get(business_idea=business_idea)
            comparative_data = {
                'similar_ideas': comparative.similar_ideas,
                'industry_averages': comparative.industry_averages,
                'competitive_advantages': comparative.competitive_advantages,
                'competitive_disadvantages': comparative.competitive_disadvantages
            }
        except ComparativeAnalytics.DoesNotExist:
            pass
            
        try:
            predictive = PredictiveAnalytics.objects.get(business_idea=business_idea)
            predictive_data = {
                'growth_predictions': predictive.growth_predictions,
                'success_probability': predictive.success_probability,
                'risk_factors': predictive.risk_factors,
                'opportunity_areas': predictive.opportunity_areas
            }
        except PredictiveAnalytics.DoesNotExist:
            pass
        
        # Create context for AI
        context = {
            'business_idea': {
                'title': business_idea.title,
                'description': business_idea.description,
                'problem_statement': business_idea.problem_statement,
                'solution_description': business_idea.solution_description,
                'target_audience': business_idea.target_audience,
                'market_opportunity': business_idea.market_opportunity,
                'business_model': business_idea.business_model,
                'current_stage': business_idea.current_stage,
            },
            'comparative_data': comparative_data,
            'predictive_data': predictive_data
        }
        
        # Create the prompt for Gemini
        prompt = f"""
        You are an expert business consultant and will generate a complete, professional business plan for the following business idea:
        
        {json.dumps(context, indent=2)}
        
        Create a comprehensive business plan with the following sections:
        
        1. Executive Summary
        2. Company Description
        3. Market Analysis (including competitive analysis)
        4. Organization & Management
        5. Service/Product Line
        6. Marketing & Sales Strategy
        7. Financial Projections
        8. Funding Requirements
        9. Risk Analysis
        10. Implementation Timeline
        
        For each section, provide detailed, specific content tailored to this business idea. Use the comparative and predictive data provided to inform your analysis.
        
        Format your response as a JSON object with each section as a key, and the content as the value. Include subsections where appropriate.
        
        The business plan should be professional, comprehensive, and ready for presentation to potential investors or stakeholders.
        """
        
        # Generate business plan using Gemini
        response = model.generate_content(prompt)
        
        # Parse the response
        response_text = response.text
        
        # Extract JSON from the response
        if '```json' in response_text:
            json_start = response_text.find('```json') + 7
            json_end = response_text.find('```', json_start)
            json_str = response_text[json_start:json_end].strip()
        elif '{' in response_text and '}' in response_text:
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            json_str = response_text[json_start:json_end].strip()
        else:
            json_str = response_text
            
        business_plan_data = json.loads(json_str)
        return business_plan_data
        
    except Exception as e:
        print(f"Error generating complete business plan: {e}")
        return None

def generate_market_analysis(business_idea_id):
    """
    Generate a detailed market analysis for a business idea using Gemini AI
    
    Args:
        business_idea_id: ID of the business idea
        
    Returns:
        String containing the market analysis
    """
    try:
        # Get business idea
        business_idea = BusinessIdea.objects.get(id=business_idea_id)
        
        # Create the prompt for Gemini
        prompt = f"""
        You are a market research expert. Create a comprehensive market analysis for the following business idea:
        
        Business Name: {business_idea.title}
        Description: {business_idea.description}
        Problem Statement: {business_idea.problem_statement}
        Solution: {business_idea.solution_description}
        Target Audience: {business_idea.target_audience}
        
        Your market analysis should include:
        
        1. Industry Overview
           - Current size and growth trends
           - Key players and market share
           - Regulatory environment
        
        2. Target Market Analysis
           - Detailed customer segments
           - Market size for each segment
           - Customer needs and pain points
        
        3. Competitive Analysis
           - Direct competitors (3-5 examples)
           - Indirect competitors
           - Competitive advantages and disadvantages
           - Barriers to entry
        
        4. SWOT Analysis
           - Strengths of the business idea
           - Weaknesses to address
           - Opportunities in the market
           - Threats to be aware of
        
        5. Market Entry Strategy
           - Recommended positioning
           - Pricing strategy
           - Distribution channels
        
        Be specific, data-driven where possible, and provide actionable insights. Format your response in a professional business report style.
        """
        
        # Generate market analysis using Gemini
        response = model.generate_content(prompt)
        
        # Return the generated content
        return response.text
        
    except Exception as e:
        print(f"Error generating market analysis: {e}")
        return None
