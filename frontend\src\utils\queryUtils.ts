import { QueryClient } from '@tanstack/react-query';

// Define entity relationships for cache invalidation
const entityRelationships: Record<string, string[]> = {
  'users': ['posts', 'comments', 'profile', 'business-ideas', 'mentorship-matches'],
  'posts': ['comments', 'likes', 'tags'],
  'comments': ['posts'],
  'profile': ['users'],
  'events': ['attendees'],
  'resources': ['tags'],
  'business-ideas': ['progress-updates', 'mentorship-matches', 'business-milestones', 'business-goals'],
  'progress-updates': ['business-ideas'],
  'mentorship-matches': ['mentorship-sessions', 'mentorship-feedback', 'mentor-profiles', 'business-ideas'],
  'mentorship-sessions': ['mentorship-matches', 'mentorship-feedback'],
  'mentorship-feedback': ['mentorship-matches', 'mentorship-sessions'],
  'mentor-profiles': ['mentor-expertise', 'mentorship-matches'],
  'mentor-expertise': ['mentor-profiles'],
  'business-milestones': ['business-ideas', 'business-goals'],
  'business-goals': ['business-ideas', 'business-milestones'],
  'forum-threads': ['forum-posts', 'forum-topics'],
  'forum-posts': ['forum-threads'],
  'forum-topics': ['forum-threads', 'forum-categories'],
  'forum-categories': ['forum-topics'],
};

/**
 * Invalidate related entity caches when an entity is modified
 * @param queryClient The QueryClient instance
 * @param entity The entity type that was modified
 * @param id Optional ID of the specific entity that was modified
 */
export const invalidateRelatedQueries = (
  queryClient: QueryClient,
  entity: string,
  id?: number | string
) => {
  // Invalidate the entity itself
  if (id) {
    queryClient.invalidateQueries({ queryKey: [entity, id] });
  } else {
    queryClient.invalidateQueries({ queryKey: [entity] });
  }
  
  // Invalidate related entities
  const relatedEntities = entityRelationships[entity] || [];
  
  for (const relatedEntity of relatedEntities) {
    queryClient.invalidateQueries({ queryKey: [relatedEntity] });
  }
};

/**
 * Optimistic update helper for list queries
 * @param queryClient The QueryClient instance
 * @param queryKey The query key for the list
 * @param newItem The new item to add to the list
 * @param type The type of update (add, update, remove)
 * @param id Optional ID for update/remove operations
 */
export const optimisticListUpdate = <T extends { id: number | string }>(
  queryClient: QueryClient,
  queryKey: unknown[],
  newItem: T,
  type: 'add' | 'update' | 'remove',
  id?: number | string
) => {
  queryClient.setQueryData(queryKey, (old: any) => {
    // Handle paginated responses
    if (old?.results && Array.isArray(old.results)) {
      let newResults;
      
      switch (type) {
        case 'add':
          newResults = [newItem, ...old.results];
          break;
        case 'update':
          newResults = old.results.map((item: T) => 
            item.id === newItem.id ? newItem : item
          );
          break;
        case 'remove':
          newResults = old.results.filter((item: T) => item.id !== id);
          break;
      }
      
      return {
        ...old,
        results: newResults,
        count: type === 'add' 
          ? (old.count || 0) + 1 
          : type === 'remove' 
            ? (old.count || 0) - 1 
            : old.count,
      };
    }
    
    // Handle regular arrays
    if (Array.isArray(old)) {
      switch (type) {
        case 'add':
          return [newItem, ...old];
        case 'update':
          return old.map((item: T) => 
            item.id === newItem.id ? newItem : item
          );
        case 'remove':
          return old.filter((item: T) => item.id !== id);
      }
    }
    
    return old;
  });
};

/**
 * Get entity-specific cache configuration
 * @param entity The entity type
 * @returns Cache configuration for the entity
 */
export const getEntityCacheConfig = (entity: string) => {
  // Define entity-specific cache configurations
  const entityCacheConfigs: Record<string, { staleTime: number, gcTime: number }> = {
    // Dynamic entities (frequent updates)
    'posts': {
      staleTime: 30 * 1000, // 30 seconds
      gcTime: 5 * 60 * 1000, // 5 minutes
    },
    'comments': {
      staleTime: 30 * 1000,
      gcTime: 5 * 60 * 1000,
    },
    'forum-posts': {
      staleTime: 30 * 1000,
      gcTime: 5 * 60 * 1000,
    },
    
    // Standard entities (occasional updates)
    'users': {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
    },
    'events': {
      staleTime: 5 * 60 * 1000,
      gcTime: 10 * 60 * 1000,
    },
    'business-ideas': {
      staleTime: 5 * 60 * 1000,
      gcTime: 10 * 60 * 1000,
    },
    
    // Static entities (rare updates)
    'tags': {
      staleTime: 30 * 60 * 1000, // 30 minutes
      gcTime: 60 * 60 * 1000, // 1 hour
    },
    'forum-categories': {
      staleTime: 30 * 60 * 1000,
      gcTime: 60 * 60 * 1000,
    },
  };
  
  // Default configuration
  const defaultConfig = {
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  };
  
  return entityCacheConfigs[entity] || defaultConfig;
};
