from rest_framework import serializers
from django.contrib.auth.models import User
from .models import (
    BusinessIdea, ProgressUpdate, IncubatorResource, MentorshipApplication,
    InvestorProfile, FundingOpportunity, FundingApplication, Investment,
    MentorProfile, MentorExpertise, MentorshipMatch, MentorshipSession, MentorshipFeedback
)
from users.serializers import UserSerializer
from api.serializers import TagSerializer

class BusinessIdeaSerializer(serializers.ModelSerializer):
    owner = UserSerializer(read_only=True)
    owner_id = serializers.IntegerField(write_only=True)
    collaborators = UserSerializer(many=True, read_only=True)
    collaborator_ids = serializers.PrimaryKeyRelatedField(
        many=True,
        write_only=True,
        queryset=User.objects.all(),
        required=False,
        source='collaborators'
    )
    tags = TagSerializer(many=True, read_only=True)
    tag_ids = serializers.PrimaryKeyRelatedField(
        many=True,
        write_only=True,
        queryset=User.objects.all(),
        required=False,
        source='tags'
    )
    progress_count = serializers.SerializerMethodField()

    class Meta:
        model = BusinessIdea
        fields = [
            'id', 'title', 'slug', 'description', 'problem_statement',
            'solution_description', 'target_audience', 'market_opportunity',
            'business_model', 'current_stage', 'image', 'owner', 'owner_id',
            'collaborators', 'collaborator_ids', 'tags', 'tag_ids',
            'moderation_status', 'moderation_comment', 'progress_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'slug', 'created_at', 'updated_at', 'moderation_status', 'moderation_comment']

    def get_progress_count(self, obj):
        return obj.progress_updates.count()


class ProgressUpdateSerializer(serializers.ModelSerializer):
    created_by = UserSerializer(read_only=True)
    created_by_id = serializers.IntegerField(write_only=True)
    business_idea_title = serializers.SerializerMethodField()

    class Meta:
        model = ProgressUpdate
        fields = [
            'id', 'business_idea', 'business_idea_title', 'title', 'description',
            'achievements', 'challenges', 'next_steps', 'created_by',
            'created_by_id', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def get_business_idea_title(self, obj):
        return obj.business_idea.title


class IncubatorResourceSerializer(serializers.ModelSerializer):
    author = UserSerializer(read_only=True)
    author_id = serializers.IntegerField(write_only=True)
    tags = TagSerializer(many=True, read_only=True)
    tag_ids = serializers.PrimaryKeyRelatedField(
        many=True,
        write_only=True,
        queryset=User.objects.all(),
        required=False,
        source='tags'
    )

    class Meta:
        model = IncubatorResource
        fields = [
            'id', 'title', 'description', 'resource_type', 'category',
            'url', 'image', 'file', 'author', 'author_id', 'tags', 'tag_ids',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class MentorExpertiseSerializer(serializers.ModelSerializer):
    category_display = serializers.SerializerMethodField()
    level_display = serializers.SerializerMethodField()

    class Meta:
        model = MentorExpertise
        fields = [
            'id', 'mentor', 'category', 'category_display', 'specific_expertise',
            'level', 'level_display', 'years_experience'
        ]

    def get_category_display(self, obj):
        return obj.get_category_display()

    def get_level_display(self, obj):
        return obj.get_level_display()


class MentorProfileSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    user_id = serializers.IntegerField(write_only=True)
    expertise_areas = MentorExpertiseSerializer(many=True, read_only=True)
    availability_display = serializers.SerializerMethodField()
    active_mentorships_count = serializers.SerializerMethodField()

    class Meta:
        model = MentorProfile
        fields = [
            'id', 'user', 'user_id', 'bio', 'company', 'position', 'years_of_experience',
            'linkedin_profile', 'website', 'availability', 'availability_display',
            'max_mentees', 'is_accepting_mentees', 'is_verified', 'expertise_areas',
            'active_mentorships_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'is_verified', 'created_at', 'updated_at']

    def get_availability_display(self, obj):
        return obj.get_availability_display()

    def get_active_mentorships_count(self, obj):
        return obj.mentorship_matches.filter(status='active').count()


class MentorshipApplicationSerializer(serializers.ModelSerializer):
    applicant = UserSerializer(read_only=True)
    applicant_id = serializers.IntegerField(write_only=True)
    business_idea_title = serializers.SerializerMethodField()
    preferred_mentor = MentorProfileSerializer(read_only=True)
    preferred_mentor_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    preferred_communication_display = serializers.SerializerMethodField()
    preferred_expertise_display = serializers.SerializerMethodField()

    class Meta:
        model = MentorshipApplication
        fields = [
            'id', 'business_idea', 'business_idea_title', 'applicant', 'applicant_id',
            'preferred_mentor', 'preferred_mentor_id', 'goals', 'specific_areas',
            'commitment', 'preferred_communication', 'preferred_communication_display',
            'preferred_expertise', 'preferred_expertise_display', 'status', 'admin_notes',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'status', 'admin_notes', 'created_at', 'updated_at']

    def get_business_idea_title(self, obj):
        return obj.business_idea.title

    def get_preferred_communication_display(self, obj):
        return obj.get_preferred_communication_display() if obj.preferred_communication else None

    def get_preferred_expertise_display(self, obj):
        return obj.get_preferred_expertise_display() if obj.preferred_expertise else None


class MentorshipFeedbackSerializer(serializers.ModelSerializer):
    provided_by = UserSerializer(read_only=True)
    provided_by_id = serializers.IntegerField(write_only=True)
    rating_display = serializers.SerializerMethodField()
    knowledge_rating_display = serializers.SerializerMethodField()
    communication_rating_display = serializers.SerializerMethodField()
    helpfulness_rating_display = serializers.SerializerMethodField()
    preparation_rating_display = serializers.SerializerMethodField()
    responsiveness_rating_display = serializers.SerializerMethodField()

    class Meta:
        model = MentorshipFeedback
        fields = [
            'id', 'session', 'provided_by', 'provided_by_id', 'is_from_mentee',
            # Overall rating
            'rating', 'rating_display',
            # Detailed ratings
            'knowledge_rating', 'knowledge_rating_display',
            'communication_rating', 'communication_rating_display',
            'helpfulness_rating', 'helpfulness_rating_display',
            'preparation_rating', 'preparation_rating_display',
            'responsiveness_rating', 'responsiveness_rating_display',
            # Feedback text
            'comments', 'areas_of_improvement', 'highlights',
            # Session outcomes
            'goals_achieved', 'follow_up_needed', 'follow_up_notes',
            # Privacy settings
            'is_private', 'share_with_mentor',
            # Timestamps
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def get_rating_display(self, obj):
        return obj.get_rating_display()

    def get_knowledge_rating_display(self, obj):
        return obj.get_knowledge_rating_display() if obj.knowledge_rating else None

    def get_communication_rating_display(self, obj):
        return obj.get_communication_rating_display() if obj.communication_rating else None

    def get_helpfulness_rating_display(self, obj):
        return obj.get_helpfulness_rating_display() if obj.helpfulness_rating else None

    def get_preparation_rating_display(self, obj):
        return obj.get_preparation_rating_display() if obj.preparation_rating else None

    def get_responsiveness_rating_display(self, obj):
        return obj.get_responsiveness_rating_display() if obj.responsiveness_rating else None


class MentorshipSessionSerializer(serializers.ModelSerializer):
    feedback = MentorshipFeedbackSerializer(many=True, read_only=True)
    status_display = serializers.SerializerMethodField()
    session_type_display = serializers.SerializerMethodField()
    video_provider_display = serializers.SerializerMethodField()
    average_rating = serializers.SerializerMethodField()
    feedback_count = serializers.SerializerMethodField()

    class Meta:
        model = MentorshipSession
        fields = [
            'id', 'mentorship_match', 'title', 'description',
            # Scheduling fields
            'scheduled_at', 'duration_minutes', 'session_type', 'session_type_display', 'location',
            # Video conferencing fields
            'video_provider', 'video_provider_display', 'meeting_id', 'meeting_password', 'meeting_link',
            # Reminders
            'reminder_sent', 'reminder_sent_at',
            # Status
            'status', 'status_display',
            # Notes
            'mentor_notes', 'mentee_notes',
            # Feedback
            'feedback', 'average_rating', 'feedback_count',
            # Timestamps
            'created_at', 'updated_at', 'started_at', 'ended_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'reminder_sent', 'reminder_sent_at']

    def get_status_display(self, obj):
        return obj.get_status_display()

    def get_session_type_display(self, obj):
        return obj.get_session_type_display()

    def get_video_provider_display(self, obj):
        return obj.get_video_provider_display() if obj.video_provider else None

    def get_average_rating(self, obj):
        ratings = [feedback.rating for feedback in obj.feedback.all() if feedback.rating]
        return sum(ratings) / len(ratings) if ratings else None

    def get_feedback_count(self, obj):
        return obj.feedback.count()


class MentorshipMatchSerializer(serializers.ModelSerializer):
    mentor = MentorProfileSerializer(read_only=True)
    mentee = UserSerializer(read_only=True)
    business_idea = serializers.PrimaryKeyRelatedField(queryset=BusinessIdea.objects.all())
    business_idea_title = serializers.SerializerMethodField()
    application = MentorshipApplicationSerializer(read_only=True)
    application_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    status_display = serializers.SerializerMethodField()
    sessions = MentorshipSessionSerializer(many=True, read_only=True)
    sessions_count = serializers.SerializerMethodField()

    class Meta:
        model = MentorshipMatch
        fields = [
            'id', 'mentor', 'mentee', 'business_idea', 'business_idea_title',
            'application', 'application_id', 'status', 'status_display',
            'start_date', 'end_date', 'goals', 'focus_areas', 'mentee_notes',
            'mentor_notes', 'sessions', 'sessions_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'start_date', 'created_at', 'updated_at']

    def get_business_idea_title(self, obj):
        return obj.business_idea.title

    def get_status_display(self, obj):
        return obj.get_status_display()

    def get_sessions_count(self, obj):
        return obj.sessions.count()


class InvestorProfileSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    user_id = serializers.IntegerField(write_only=True)

    class Meta:
        model = InvestorProfile
        fields = [
            'id', 'user', 'user_id', 'investor_type', 'company_name', 'bio',
            'investment_focus', 'investment_range_min', 'investment_range_max',
            'linkedin_profile', 'website', 'is_verified', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'is_verified', 'created_at', 'updated_at']


class FundingOpportunitySerializer(serializers.ModelSerializer):
    provider = UserSerializer(read_only=True)
    provider_id = serializers.IntegerField(write_only=True)
    application_count = serializers.SerializerMethodField()

    class Meta:
        model = FundingOpportunity
        fields = [
            'id', 'title', 'description', 'funding_type', 'amount',
            'provider', 'provider_id', 'eligibility_criteria', 'application_process',
            'application_deadline', 'status', 'application_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def get_application_count(self, obj):
        return obj.applications.count()


class FundingApplicationSerializer(serializers.ModelSerializer):
    applicant = UserSerializer(read_only=True)
    applicant_id = serializers.IntegerField(write_only=True)
    business_idea_title = serializers.SerializerMethodField()
    funding_opportunity_title = serializers.SerializerMethodField()

    class Meta:
        model = FundingApplication
        fields = [
            'id', 'business_idea', 'business_idea_title', 'funding_opportunity',
            'funding_opportunity_title', 'applicant', 'applicant_id', 'pitch',
            'requested_amount', 'use_of_funds', 'status', 'reviewer_notes',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'status', 'reviewer_notes', 'created_at', 'updated_at']

    def get_business_idea_title(self, obj):
        return obj.business_idea.title

    def get_funding_opportunity_title(self, obj):
        return obj.funding_opportunity.title


class InvestmentSerializer(serializers.ModelSerializer):
    investor = UserSerializer(read_only=True)
    investor_id = serializers.IntegerField(write_only=True)
    business_idea_title = serializers.SerializerMethodField()

    class Meta:
        model = Investment
        fields = [
            'id', 'business_idea', 'business_idea_title', 'investor', 'investor_id',
            'investment_type', 'amount', 'equity_percentage', 'terms', 'status',
            'created_at', 'updated_at', 'completed_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'completed_at']

    def get_business_idea_title(self, obj):
        return obj.business_idea.title
