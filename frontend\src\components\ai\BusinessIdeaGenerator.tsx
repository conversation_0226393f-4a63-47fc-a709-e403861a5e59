/**
 * AI-Powered Business Idea Generator
 * Generates innovative business ideas based on user preferences and market trends
 */

import React, { useState, useEffect } from 'react';
import {
  Lightbulb,
  Brain,
  Sparkles,
  RefreshCw,
  Star,
  ArrowRight,
  Settings
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { RTLText, RTLFlex } from '../common';

interface BusinessIdea {
  id: string;
  title: string;
  description: string;
  industry: string;
  targetMarket: string;
  revenueModel: string;
  startupCost: 'low' | 'medium' | 'high';
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  marketPotential: number; // 1-5 stars
  innovationScore: number; // 1-5 stars
  timeToMarket: string;
  keyFeatures: string[];
  competitiveAdvantage: string;
  risks: string[];
  opportunities: string[];
  nextSteps: string[];
  aiConfidence: number; // 1-100
  trending: boolean;
  tags: string[];
}

interface GenerationPreferences {
  industries: string[];
  budget: 'low' | 'medium' | 'high' | 'any';
  experience: 'beginner' | 'intermediate' | 'advanced' | 'any';
  timeCommitment: 'part-time' | 'full-time' | 'any';
  riskTolerance: 'low' | 'medium' | 'high';
  location: string;
  interests: string[];
  skills: string[];
  marketFocus: 'local' | 'national' | 'international';
}

export const BusinessIdeaGenerator: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [ideas, setIdeas] = useState<BusinessIdea[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [showPreferences, setShowPreferences] = useState(false);
  const [selectedIdea, setSelectedIdea] = useState<BusinessIdea | null>(null);
  const [generationCount, setGenerationCount] = useState(0);

  const [preferences, setPreferences] = useState<GenerationPreferences>({
    industries: [],
    budget: 'any',
    experience: 'any',
    timeCommitment: 'any',
    riskTolerance: 'medium',
    location: '',
    interests: [],
    skills: [],
    marketFocus: 'national'
  });

  // Industry options
  const industries = [
    'Technology', 'Healthcare', 'Education', 'Finance', 'E-commerce',
    'Food & Beverage', 'Entertainment', 'Real Estate', 'Transportation', 'Sustainability', 'Fashion', 'Sports & Fitness', 'Travel', 'Agriculture'
  ];



  // Generate mock business ideas (in real app, this would call AI service)
  const generateBusinessIdeas = async () => {
    setIsGenerating(true);

    // Simulate AI generation delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    try {
      // Generate real business ideas using AI API
      const response = await aiApi.generateBusinessIdeas({
        industry: criteria.industry,
        target_market: criteria.targetMarket,
        budget_range: criteria.budgetRange,
        experience_level: criteria.experienceLevel,
        interests: criteria.interests,
        market_trends: true,
        innovation_focus: true
      });

      const generatedIdeas: BusinessIdea[] = response.data.ideas.map((idea: any) => ({
        id: idea.id,
        title: idea.title,
        description: idea.description,
        industry: idea.industry,
        targetMarket: idea.target_market,
        revenueModel: idea.revenue_model,
        startupCost: idea.startup_cost,
        difficulty: idea.difficulty,
        marketPotential: idea.market_potential_score,
        innovationScore: idea.innovation_score,
        timeToMarket: idea.time_to_market,
        keyFeatures: idea.key_features || [],
        competitiveAdvantage: idea.competitive_advantage || '',
        risks: idea.risks || [],
        opportunities: idea.opportunities || [],
        nextSteps: idea.next_steps || [],
        aiConfidence: idea.ai_confidence || 0,
        trending: idea.trending || false,
        tags: idea.tags || []
      }));

      setIdeas(generatedIdeas);
      setGenerationCount(prev => prev + 1);
      setIsGenerating(false);
    } catch (error) {
      console.error('Failed to generate business ideas:', error);
      setIdeas([]);
      setIsGenerating(false);
    }

  };

  // Filter ideas based on preferences
  const filteredIdeas = ideas.filter(idea => {
    if (preferences.industries.length > 0) {
      const matchesIndustry = preferences.industries.some(industry =>
        idea.industry.toLowerCase().includes(industry.toLowerCase())
      );
      if (!matchesIndustry) return false;
    }

    if (preferences.budget !== 'any' && idea.startupCost !== preferences.budget) {
      return false;
    }

    if (preferences.experience !== 'any' && idea.difficulty !== preferences.experience) {
      return false;
    }

    return true;
  });

  const getStartupCostColor = (cost: string) => {
    switch (cost) {
      case 'low': return 'text-green-400';
      case 'medium': return 'text-yellow-400';
      case 'high': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'text-green-400';
      case 'intermediate': return 'text-yellow-400';
      case 'advanced': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        size={16}
        className={i < rating ? 'text-yellow-400 fill-current' : 'text-gray-600'}
      />
    ));
  };

  useEffect(() => {
    // Generate initial ideas on component mount
    generateBusinessIdeas();
  }, []);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <RTLFlex className="items-center justify-center mb-4">
          <Lightbulb className="text-yellow-400" size={32} />
          <RTLText className={`text-2xl font-bold text-white ${isRTL ? 'mr-3' : 'ml-3'}`}>
            {t('businessIdeas.title')}
          </RTLText>
        </RTLFlex>
        <p className="text-gray-300 max-w-2xl mx-auto">
          {t('businessIdeas.description')}
        </p>
      </div>

      {/* Controls */}
      <RTLFlex className="items-center justify-between">
        <RTLFlex className="items-center gap-4">
          <button
            onClick={generateBusinessIdeas}
            disabled={isGenerating}
            className={`flex items-center px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 disabled:opacity-50 text-white rounded-lg font-medium transition-all ${isRTL ? "flex-row-reverse" : ""}`}
          >
            {isGenerating ? (
              <RefreshCw className="animate-spin" size={20} />
            ) : (
              <Sparkles size={20} />
            )}
            <span className={isRTL ? 'mr-2' : 'ml-2'}>
              {isGenerating ? t('businessIdeas.generatingIdeas') : t('businessIdeas.generateNewIdeas')}
            </span>
          </button>

          <button
            onClick={() => setShowPreferences(!showPreferences)}
            className={`flex items-center px-4 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <Settings size={20} />
            <span className={isRTL ? 'mr-2' : 'ml-2'}>{t('businessIdeas.preferences')}</span>
          </button>
        </RTLFlex>

        <div className="text-gray-400 text-sm">
          {filteredIdeas.length} {t('businessIdeas.ideasGenerated')} • {t('businessIdeas.round')} {generationCount}
        </div>
      </RTLFlex>

      {/* Preferences Panel */}
      {showPreferences && (
        <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6">
          <h3 className="text-lg font-semibold text-white mb-4">{t('businessIdeas.generationPreferences')}</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Industries */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('businessIdeas.preferredIndustries')}
              </label>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {industries.map(industry => (
                  <label key={industry} className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    <input
                      type="checkbox"
                      checked={preferences.industries.includes(industry)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setPreferences(prev => ({
                            ...prev,
                            industries: [...prev.industries, industry]
                          }));
                        } else {
                          setPreferences(prev => ({
                            ...prev,
                            industries: prev.industries.filter(i => i !== industry)
                          }));
                        }
                      }}
                      className={`mr-2 rounded ${isRTL ? "space-x-reverse" : ""}`}
                    />
                    <span className="text-sm text-gray-300">{industry}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Budget */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('businessIdeas.startupBudget')}
              </label>
              <select
                value={preferences.budget}
                onChange={(e) => setPreferences(prev => ({ ...prev, budget: e.target.value as any }))}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-purple-500"
              >
                <option value="any">{t('businessIdeas.anyBudget')}</option>
                <option value="low">{t('businessIdeas.lowBudget')}</option>
                <option value="medium">{t('businessIdeas.mediumBudget')}</option>
                <option value="high">{t('businessIdeas.highBudget')}</option>
              </select>
            </div>

            {/* Experience */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('businessIdeas.experienceLevel')}
              </label>
              <select
                value={preferences.experience}
                onChange={(e) => setPreferences(prev => ({ ...prev, experience: e.target.value as any }))}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-purple-500"
              >
                <option value="any">{t('businessIdeas.anyLevel')}</option>
                <option value="beginner">{t('businessIdeas.beginner')}</option>
                <option value="intermediate">{t('businessIdeas.intermediate')}</option>
                <option value="advanced">{t('businessIdeas.advanced')}</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Ideas Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredIdeas.map((idea) => (
          <div
            key={idea.id}
            className="bg-gray-900/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6 hover:border-purple-500/50 transition-all cursor-pointer"
            onClick={() => setSelectedIdea(idea)}
          >
            {/* Header */}
            <RTLFlex className="items-start justify-between mb-4">
              <div className={`flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                <RTLFlex className="items-center mb-2">
                  <h3 className="text-lg font-semibold text-white">{idea.title}</h3>
                  {idea.trending && (
                    <span className={`px-2 py-1 bg-red-500/20 text-red-400 text-xs rounded-full ml-2 ${isRTL ? "space-x-reverse" : ""}`}>
                      {t('businessIdeas.trending')}
                    </span>
                  )}
                </RTLFlex>
                <p className="text-gray-300 text-sm mb-3">{idea.description}</p>
              </div>
            </RTLFlex>

            {/* Metrics */}
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <div className="text-xs text-gray-500 mb-1">{t('businessIdeas.marketPotential')}</div>
                <RTLFlex className="items-center">
                  {renderStars(idea.marketPotential)}
                </RTLFlex>
              </div>
              <div>
                <div className="text-xs text-gray-500 mb-1">{t('businessIdeas.innovationScore')}</div>
                <RTLFlex className="items-center">
                  {renderStars(idea.innovationScore)}
                </RTLFlex>
              </div>
            </div>

            {/* Details */}
            <div className="space-y-2 mb-4">
              <RTLFlex className="items-center justify-between text-sm">
                <span className="text-gray-400">{t('businessIdeas.startupCost')}</span>
                <span className={`font-medium ${getStartupCostColor(idea.startupCost)}`}>
                  {idea.startupCost.charAt(0).toUpperCase() + idea.startupCost.slice(1)}
                </span>
              </RTLFlex>
              <RTLFlex className="items-center justify-between text-sm">
                <span className="text-gray-400">{t('businessIdeas.difficulty')}</span>
                <span className={`font-medium ${getDifficultyColor(idea.difficulty)}`}>
                  {idea.difficulty.charAt(0).toUpperCase() + idea.difficulty.slice(1)}
                </span>
              </RTLFlex>
              <RTLFlex className="items-center justify-between text-sm">
                <span className="text-gray-400">{t('businessIdeas.timeToMarket')}</span>
                <span className="text-white font-medium">{idea.timeToMarket}</span>
              </RTLFlex>
            </div>

            {/* Tags */}
            <RTLFlex className={`items-center gap-2 mb-4 flex-wrap ${isRTL ? "flex-row-reverse" : ""}`}>
              {idea.tags.slice(0, 3).map(tag => (
                <span
                  key={tag}
                  className="px-2 py-1 bg-purple-500/20 text-purple-300 text-xs rounded-full"
                >
                  {tag}
                </span>
              ))}
            </RTLFlex>

            {/* AI Confidence */}
            <RTLFlex className="items-center justify-between">
              <RTLFlex className="items-center text-sm text-gray-400">
                <Brain size={16} />
                <span className={isRTL ? 'mr-2' : 'ml-2'}>
                  {t('businessIdeas.aiConfidence')} {idea.aiConfidence}%
                </span>
              </RTLFlex>
              <ArrowRight className="text-purple-400" size={16} />
            </RTLFlex>
          </div>
        ))}
      </div>

      {/* Detailed View Modal */}
      {selectedIdea && (
        <div className={`fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="bg-gray-900 rounded-xl border border-gray-700 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              {/* Header */}
              <RTLFlex className="items-start justify-between mb-6">
                <div>
                  <h2 className="text-2xl font-bold text-white mb-2">{selectedIdea.title}</h2>
                  <p className="text-gray-300">{selectedIdea.description}</p>
                </div>
                <button
                  onClick={() => setSelectedIdea(null)}
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  ✕
                </button>
              </RTLFlex>

              {/* Content sections would continue here... */}
              <div className="text-center py-8">
                <p className="text-gray-400">
                  {t('businessIdeas.detailedViewDescription')}
                </p>
                <button
                  onClick={() => setSelectedIdea(null)}
                  className="mt-4 px-6 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                >
                  {t('businessIdeas.close')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Empty State */}
      {filteredIdeas.length === 0 && !isGenerating && (
        <div className="text-center py-12">
          <Lightbulb className="mx-auto mb-4 text-gray-600" size={48} />
          <h3 className="text-xl font-semibold text-gray-400 mb-2">{t('businessIdeas.noIdeasFound')}</h3>
          <p className="text-gray-500 mb-4">
            {t('businessIdeas.adjustPreferences')}
          </p>
          <button
            onClick={generateBusinessIdeas}
            className="px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
          >
            {t('businessIdeas.generateIdeas')}
          </button>
        </div>
      )}
    </div>
  );
};

export default BusinessIdeaGenerator;
