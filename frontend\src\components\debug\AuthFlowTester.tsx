import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAppSelector } from '../../store/hooks';
import { 
  getRedirectDebugInfo, 
  resetRedirectState, 
  getAuthRedirectPath,
  isProtectedRoute,
  isAuthRoute 
} from '../../utils/authRedirectManager';
import { getPrimaryDashboardRoute } from '../../utils/roleBasedRouting';
import { Play, RotateCcw, AlertTriangle, CheckCircle, Info } from 'lucide-react';

/**
 * Authentication Flow Tester - Development only
 * Tests and debugs authentication redirect logic
 */
const AuthFlowTester: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [testResults, setTestResults] = useState<any[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  
  const { user, isAuthenticated, isLoading } = useAppSelector(state => state.auth);
  const navigate = useNavigate();
  const location = useLocation();

  // Test scenarios
  const testScenarios = [
    { path: '/dashboard', description: 'Dashboard access (authenticated)' },
    { path: '/admin', description: 'Admin access (admin required)' },
    { path: '/login', description: 'Login page (should redirect if authenticated)' },
    { path: '/register', description: 'Register page (should redirect if authenticated)' },
    { path: '/profile', description: 'Profile page (authenticated)' },
    { path: '/invalid-route', description: 'Invalid route (should show 404 or redirect)' },
    { path: '/super_admin', description: 'Super admin access (super admin required)' },
  ];

  const runAuthFlowTests = async () => {
    setIsRunning(true);
    const results = [];

    for (const scenario of testScenarios) {
      const result = {
        path: scenario.path,
        description: scenario.description,
        isProtected: isProtectedRoute(scenario.path),
        isAuth: isAuthRoute(scenario.path),
        authRedirect: getAuthRedirectPath(user, scenario.path, isAuthenticated),
        userCanAccess: true, // This would need more complex logic
        timestamp: new Date().toISOString()
      };
      
      results.push(result);
    }

    setTestResults(results);
    setIsRunning(false);
  };

  const handleResetRedirectState = () => {
    resetRedirectState();
    setTestResults([]);
  };

  const redirectDebugInfo = getRedirectDebugInfo();
  const currentDashboard = user ? getPrimaryDashboardRoute(user) : null;

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <>
      {/* Floating Button */}
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-32 right-4 z-50 bg-green-600 hover:bg-green-700 text-white p-3 rounded-full shadow-lg transition-colors"
        title="Auth Flow Tester"
      >
        <Play className="w-5 h-5" />
      </button>

      {/* Test Panel */}
      {isOpen && (
        <div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Authentication Flow Tester
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Test and debug authentication redirect logic
                </p>
              </div>
              <button
                onClick={() => setIsOpen(false)}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              >
                ✕
              </button>
            </div>

            <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
              {/* Current State */}
              <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
                  Current State
                </h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <strong>Current Path:</strong> {location.pathname}
                  </div>
                  <div>
                    <strong>Authenticated:</strong> {isAuthenticated ? '✅ Yes' : '❌ No'}
                  </div>
                  <div>
                    <strong>Loading:</strong> {isLoading ? '⏳ Yes' : '✅ No'}
                  </div>
                  <div>
                    <strong>User:</strong> {user?.username || 'None'}
                  </div>
                  <div>
                    <strong>User Dashboard:</strong> {currentDashboard || 'N/A'}
                  </div>
                  <div>
                    <strong>Is Protected Route:</strong> {isProtectedRoute(location.pathname) ? '🔒 Yes' : '🌐 No'}
                  </div>
                </div>
              </div>

              {/* Redirect State */}
              <div className="mb-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
                  Redirect State
                </h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <strong>Is Redirecting:</strong> {redirectDebugInfo.isRedirecting ? '🔄 Yes' : '✅ No'}
                  </div>
                  <div>
                    <strong>Last Redirect:</strong> {redirectDebugInfo.lastRedirectPath || 'None'}
                  </div>
                  <div>
                    <strong>Redirect Count:</strong> {redirectDebugInfo.redirectCount}
                  </div>
                  <div>
                    <strong>Can Redirect Now:</strong> {redirectDebugInfo.canRedirectNow ? '✅ Yes' : '❌ No'}
                  </div>
                  <div>
                    <strong>Time Since Last:</strong> {redirectDebugInfo.timeSinceLastRedirect}ms
                  </div>
                </div>
              </div>

              {/* Controls */}
              <div className="mb-6 flex gap-4">
                <button
                  onClick={runAuthFlowTests}
                  disabled={isRunning}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
                >
                  <Play className={`w-4 h-4 ${isRunning ? 'animate-pulse' : ''}`} />
                  {isRunning ? 'Running Tests...' : 'Run Auth Tests'}
                </button>
                
                <button
                  onClick={handleResetRedirectState}
                  className="flex items-center gap-2 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
                >
                  <RotateCcw className="w-4 h-4" />
                  Reset Redirect State
                </button>
              </div>

              {/* Test Results */}
              {testResults.length > 0 && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    Test Results
                  </h3>
                  <div className="space-y-3">
                    {testResults.map((result, index) => (
                      <div
                        key={index}
                        className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
                      >
                        <div className="flex items-start justify-between mb-2">
                          <div>
                            <div className="font-medium text-gray-900 dark:text-white">
                              {result.path}
                            </div>
                            <div className="text-sm text-gray-600 dark:text-gray-300">
                              {result.description}
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            {result.isProtected && (
                              <span className="px-2 py-1 bg-red-100 text-red-800 text-xs rounded">
                                Protected
                              </span>
                            )}
                            {result.isAuth && (
                              <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                                Auth Route
                              </span>
                            )}
                          </div>
                        </div>
                        
                        <div className="text-sm space-y-1">
                          <div>
                            <strong>Auth Redirect:</strong> {result.authRedirect || 'None'}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Footer */}
            <div className="p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
              <p className="text-sm text-gray-600 dark:text-gray-300">
                This tool helps test authentication flows and identify redirect issues.
              </p>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default AuthFlowTester;
