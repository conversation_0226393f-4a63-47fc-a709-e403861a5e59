import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { createThread } from '../store/forumSlice';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import { RichTextEditor, TagSelector } from '../components/common';
import { ArrowLeft, Send, AlertCircle, Tag as TagIcon } from 'lucide-react';
import { Tag } from '../services/api';
import { useTranslation } from 'react-i18next';
const CreateThreadPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);
  const { loading, error } = useAppSelector((state) => state.forum);

  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [selectedTags, setSelectedTags] = useState<Tag[]>([]);
  const [topicId, setTopicId] = useState<number | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);

  useEffect(() => {
    // Get topic ID from URL query parameter
    const params = new URLSearchParams(location.search);
    const topic = params.get('topic');

    if (topic) {
      setTopicId(parseInt(topic));
    } else {
      // Redirect to forums if no topic ID is provided
      navigate('/forum');
    }
  }, [location, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user || !topicId) return;

    // Validate form
    if (!title.trim()) {
      setFormError(t('forum.createThread.titleRequired'));
      return;
    }

    if (!content.trim()) {
      setFormError(t('forum.createThread.contentRequired'));
      return;
    }

    setIsSubmitting(true);
    setFormError(null);

    try {
      const result = await dispatch(createThread({
        title,
        content,
        topic: topicId,
        author_id: user.id,
        tag_ids: selectedTags.map(tag => tag.id)
      })).unwrap();

      // Navigate to the new thread
      navigate(`/forum/thread/${result.id}`);
    } catch (err) {
      console.error("Failed to create thread:", err);
      setFormError(t('forum.createThread.createFailed'));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className={`min-h-screen flex flex-col bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
      {/* Create thread page uses its own navigation - no main navbar needed */}

      <main className={`flex-grow container mx-auto px-4 py-8 ${isRTL ? "flex-row-reverse" : ""}`}>
        {/* Breadcrumb */}
        <div className="mb-6">
          <Link to="/forum" className={`text-purple-400 hover:text-purple-300 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <ArrowLeft size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
            {t('forum.createThread.backToForums')}
          </Link>
        </div>

        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
          <h1 className="text-2xl font-bold mb-6">{t('forum.createThread.title')}</h1>

          {formError && (
            <div className="bg-red-900/30 border border-red-800 rounded-lg p-4 mb-6">
              <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <AlertCircle size={20} className={`text-red-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                <div className="text-red-300">{formError}</div>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit}>
            <div className="mb-6">
              <label htmlFor="title" className="block text-sm font-medium mb-2">
                {t('forum.createThread.threadTitle')}
              </label>
              <input
                type="text"
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="w-full bg-indigo-900/30 border border-indigo-800/50 rounded-lg py-2 px-4 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder={t('forum.createThread.titlePlaceholder')}
                required
              />
            </div>

            <div className="mb-6">
              <label htmlFor="content" className="block text-sm font-medium mb-2">
                {t('forum.createThread.threadContent')}
              </label>
              <RichTextEditor
                value={content}
                onChange={setContent}
                placeholder={t('forum.createThread.contentPlaceholder')}
              />
            </div>

            <div className="mb-6">
              <label className="block text-sm font-medium mb-2">
                {t('forum.createThread.tags')}
              </label>
              <TagSelector
                selectedTags={selectedTags}
                onChange={setSelectedTags}
                placeholder={t('forum.createThread.tagsPlaceholder')}
              />
              <div className="text-xs text-gray-400 mt-1">
                {t('forum.createThread.tagsHelp')}
              </div>

              {selectedTags.length > 0 && (
                <div className={`flex flex-wrap gap-2 mt-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                  {selectedTags.map(tag => (
                    <span
                      key={tag.id}
                      className={`inline-flex items-center px-2 py-1 rounded-full text-xs bg-indigo-800/50 text-indigo-300 ${isRTL ? "flex-row-reverse" : ""}`}
                    >
                      <TagIcon size={12} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                      {tag.name}
                    </span>
                  ))}
                </div>
              )}
            </div>

            <div className={`flex justify-end ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                type="button"
                onClick={() => navigate(-1)}
                className={`px-4 py-2 bg-indigo-800/50 rounded-lg font-medium hover:bg-indigo-700/50 transition-colors mr-4 ${isRTL ? "space-x-reverse" : ""}`}
              >
                {t('common.cancel')}
              </button>
              <button
                type="submit"
                disabled={isSubmitting || !title.trim() || !content.trim()}
                className={`px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300 flex items-center disabled:opacity-50 disabled:cursor-not-allowed ${isRTL ? "flex-row-reverse" : ""}`}
              >
                {isSubmitting ? (
                  <>
                    <div className={`animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2 ${isRTL ? "space-x-reverse" : ""}`}></div>
                    {t('forum.createThread.creating')}
                  </>
                ) : (
                  <>
                    <Send size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                    {t('forum.createThread.createButton')}
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default CreateThreadPage;
