/**
 * AI Test Page - Shows AI components working
 */

import React from 'react';
import { <PERSON><PERSON>, Zap, Target, TrendingUp, AlertTriangle, BarChart3 } from 'lucide-react';
import { AIStatusIndicator } from '../components/ai/AIStatusIndicator';
import { ConsolidatedAIStatus } from '../components/ai/ConsolidatedAIStatus';

const AITestPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-purple-900/20 dark:to-blue-900/20">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent mb-4">
            🤖 AI Beyond Chat - Working!
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            Your AI components are integrated throughout the app
          </p>
        </div>

        {/* AI Status Banner */}
        <div className="bg-gradient-to-r from-purple-500/10 to-blue-500/10 border border-purple-200 dark:border-purple-700 rounded-lg p-4 mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
              <Bot className="h-6 w-6 text-purple-500" />
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-white">AI Working: Enhancement AI</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">Auto-Enhanced: Food Delivery App • 2 minutes ago</p>
              </div>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-purple-600">5</div>
              <div className="text-sm text-gray-500">actions today</div>
            </div>
          </div>
        </div>

        {/* AI Components Demo */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Navbar AI Indicator */}
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
            <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Navbar AI Indicator</h3>
            <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
              This appears in your navigation bar when you're logged in:
            </p>
            <AIStatusIndicator variant="compact" />
          </div>

          {/* AI Stats */}
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
            <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">AI Activity Stats</h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-purple-50 dark:bg-purple-900/20 rounded p-3">
                <div className="flex items-center space-x-2">
                  <Bot className="h-5 w-5 text-purple-500" />
                  <span className="text-sm font-medium">Enhanced</span>
                </div>
                <div className="text-2xl font-bold text-purple-600">3</div>
              </div>
              <div className="bg-green-50 dark:bg-green-900/20 rounded p-3">
                <div className="flex items-center space-x-2">
                  <Target className="h-5 w-5 text-green-500" />
                  <span className="text-sm font-medium">Opportunities</span>
                </div>
                <div className="text-2xl font-bold text-green-600">2</div>
              </div>
            </div>
          </div>
        </div>

        {/* AI Workers Status */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg mb-8">
          <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">AI Workers Status</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[
              { name: '🤖 Enhancement AI', status: 'Working', lastAction: 'Auto-enhanced Food Delivery App', icon: Bot, color: 'purple' },
              { name: '🎯 Opportunity AI', status: 'Active', lastAction: 'Found market opportunity', icon: Target, color: 'green' },
              { name: '⚠️ Risk Assessment AI', status: 'Idle', lastAction: 'Identified competitive risk', icon: AlertTriangle, color: 'red' },
              { name: '🚀 Progress AI', status: 'Active', lastAction: 'Suggested acceleration plan', icon: TrendingUp, color: 'blue' },
              { name: '📊 Market Analysis AI', status: 'Working', lastAction: 'Analyzed 12 competitors', icon: BarChart3, color: 'orange' }
            ].map((worker, index) => (
              <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <worker.icon className={`h-5 w-5 text-${worker.color}-500`} />
                    <span className="font-medium text-sm">{worker.name}</span>
                  </div>
                  <div className={`w-2 h-2 rounded-full ${
                    worker.status === 'Working' ? 'bg-green-500 animate-pulse' :
                    worker.status === 'Active' ? 'bg-blue-500' : 'bg-gray-400'}
                  }`} />
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-300">
                  <div className="font-medium">Status: {worker.status}</div>
                  <div>Last: {worker.lastAction}</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Recent AI Actions */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg mb-8">
          <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Recent AI Actions</h3>
          <div className="space-y-3">
            {[
              { icon: Bot, title: '🤖 AI Auto-Enhanced: Food Delivery App', desc: 'Added 3 revenue optimization opportunities', time: '2 minutes ago', color: 'purple' },
              { icon: Target, title: '🎯 New Market Opportunity Detected', desc: 'Found emerging trend in sustainable packaging', time: '5 minutes ago', color: 'green' },
              { icon: TrendingUp, title: '🚀 Progress Acceleration Plan', desc: 'Suggested 5 quick wins to get momentum back', time: '10 minutes ago', color: 'blue' },
              { icon: AlertTriangle, title: '⚠️ Competitive Risk Identified', desc: 'New competitor detected with defensive strategies', time: '15 minutes ago', color: 'red' },
              { icon: BarChart3, title: '📊 Market Analysis Complete', desc: 'Analyzed competitors and found pricing gap', time: '20 minutes ago', color: 'orange' }
            ].map((action, index) => (
              <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <action.icon className={`h-5 w-5 text-${action.color}-500 mt-0.5`} />
                <div className="flex-1">
                  <div className="font-medium text-sm text-gray-900 dark:text-white">{action.title}</div>
                  <div className="text-xs text-gray-600 dark:text-gray-300">{action.desc}</div>
                  <div className="text-xs text-gray-500 mt-1">{action.time}</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* How to See AI in Your App */}
        <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-200 dark:border-blue-700 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">🎯 How to See AI in Your App</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">✅ What You Should See:</h4>
              <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                <li>• AI indicator in the navbar (when logged in)</li>
                <li>• AI notification widget in bottom-right corner</li>
                <li>• "🤖 AI Beyond Chat" button in navigation</li>
                <li>• Toast notifications for new AI actions</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">🔧 Quick Actions:</h4>
              <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                <li>• <a href="/ai/automatic" className="text-purple-600 hover:text-purple-800">Visit AI Dashboard</a></li>
                <li>• <a href="/incubator" className="text-purple-600 hover:text-purple-800">Create Business Idea</a></li>
                <li>• <a href="/login" className="text-purple-600 hover:text-purple-800">Login to see AI features</a></li>
                <li>• Look for purple "AI Active" indicator</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* The notification widget should appear here automatically */}
    </div>
  );
};

export default AITestPage;
