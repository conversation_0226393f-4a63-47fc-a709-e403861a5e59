from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.contrib.auth.models import User
from django.db.models import Count, Q
from django.utils import timezone
from datetime import timedelta
from forums.models import ForumPost, ForumThread, UserReputation
from api.models import MembershipApplication
from .models import UserProfile, RoleApplication
from .serializers import UserSerializer, RoleApplicationSerializer
from api.permissions import IsAdminUser


class ModeratorViewSet(viewsets.ViewSet):
    """
    ViewSet for moderator-specific functionality
    """
    permission_classes = [permissions.IsAuthenticated]

    def get_permissions(self):
        """
        Instantiates and returns the list of permissions that this view requires.
        """
        if self.action in ['dashboard_stats', 'recent_reports', 'flagged_content']:
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [IsAdminUser]
        return [permission() for permission in permission_classes]

    @action(detail=False, methods=['get'])
    def dashboard_stats(self, request):
        """
        Get moderator dashboard statistics
        """
        # Check if user has moderator role
        user_profile = getattr(request.user, 'profile', None)
        if not user_profile or not user_profile.has_role('moderator'):
            if not (request.user.is_staff or request.user.is_superuser):
                return Response(
                    {'error': 'Moderator access required'}, 
                    status=status.HTTP_403_FORBIDDEN
                )

        today = timezone.now().date()
        week_ago = today - timedelta(days=7)

        # Get forum statistics
        total_reports = ForumPost.objects.filter(is_flagged=True).count()
        pending_reports = ForumPost.objects.filter(is_flagged=True, is_approved=None).count()
        resolved_today = ForumPost.objects.filter(
            is_flagged=True, 
            is_approved__isnull=False,
            updated_at__date=today
        ).count()

        # Get user statistics
        active_users = User.objects.filter(
            last_login__gte=timezone.now() - timedelta(days=7)
        ).count()

        # Get flagged content count
        flagged_content = ForumPost.objects.filter(is_flagged=True).count()

        # Calculate community health (simplified metric)
        total_posts = ForumPost.objects.count()
        flagged_percentage = (flagged_content / max(total_posts, 1)) * 100
        community_health = max(0, 100 - flagged_percentage * 10)  # Simple calculation

        stats = {
            'totalReports': total_reports,
            'pendingReports': pending_reports,
            'resolvedToday': resolved_today,
            'activeUsers': active_users,
            'flaggedContent': flagged_content,
            'communityHealth': round(community_health, 1)
        }

        return Response(stats)

    @action(detail=False, methods=['get'])
    def recent_reports(self, request):
        """
        Get recent content reports
        """
        # Check moderator access
        user_profile = getattr(request.user, 'profile', None)
        if not user_profile or not user_profile.has_role('moderator'):
            if not (request.user.is_staff or request.user.is_superuser):
                return Response(
                    {'error': 'Moderator access required'}, 
                    status=status.HTTP_403_FORBIDDEN
                )

        # Get recent flagged posts
        flagged_posts = ForumPost.objects.filter(
            is_flagged=True
        ).select_related('author', 'thread').order_by('-created_at')[:10]

        reports = []
        for post in flagged_posts:
            report_status = 'pending'
            if post.is_approved is True:
                report_status = 'resolved'
            elif post.is_approved is False:
                report_status = 'dismissed'

            reports.append({
                'id': str(post.id),
                'type': 'post',
                'content': post.content[:100] + '...' if len(post.content) > 100 else post.content,
                'reporter': 'system',  # You might want to add a reporter field to the model
                'reason': 'Flagged content',
                'status': report_status,
                'createdAt': post.created_at.isoformat(),
                'priority': 'medium'  # You might want to add priority logic
            })

        return Response(reports)

    @action(detail=False, methods=['get'])
    def flagged_content(self, request):
        """
        Get all flagged content for review
        """
        # Check moderator access
        user_profile = getattr(request.user, 'profile', None)
        if not user_profile or not user_profile.has_role('moderator'):
            if not (request.user.is_staff or request.user.is_superuser):
                return Response(
                    {'error': 'Moderator access required'}, 
                    status=status.HTTP_403_FORBIDDEN
                )

        flagged_posts = ForumPost.objects.filter(
            is_flagged=True,
            is_approved__isnull=True
        ).select_related('author', 'thread').order_by('-created_at')

        content = []
        for post in flagged_posts:
            content.append({
                'id': post.id,
                'type': 'post',
                'content': post.content,
                'author': post.author.username,
                'thread': post.thread.title,
                'createdAt': post.created_at.isoformat(),
                'flaggedAt': post.updated_at.isoformat()
            })

        return Response(content)

    @action(detail=True, methods=['post'])
    def approve_content(self, request, pk=None):
        """
        Approve flagged content
        """
        # Check moderator access
        user_profile = getattr(request.user, 'profile', None)
        if not user_profile or not user_profile.has_role('moderator'):
            if not (request.user.is_staff or request.user.is_superuser):
                return Response(
                    {'error': 'Moderator access required'}, 
                    status=status.HTTP_403_FORBIDDEN
                )

        try:
            post = ForumPost.objects.get(id=pk, is_flagged=True)
            post.is_approved = True
            post.is_flagged = False
            post.save()
            
            return Response({'message': 'Content approved successfully'})
        except ForumPost.DoesNotExist:
            return Response(
                {'error': 'Flagged post not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=True, methods=['post'])
    def reject_content(self, request, pk=None):
        """
        Reject/remove flagged content
        """
        # Check moderator access
        user_profile = getattr(request.user, 'profile', None)
        if not user_profile or not user_profile.has_role('moderator'):
            if not (request.user.is_staff or request.user.is_superuser):
                return Response(
                    {'error': 'Moderator access required'}, 
                    status=status.HTTP_403_FORBIDDEN
                )

        try:
            post = ForumPost.objects.get(id=pk, is_flagged=True)
            post.is_approved = False
            post.is_active = False  # Hide the post
            post.save()
            
            return Response({'message': 'Content rejected successfully'})
        except ForumPost.DoesNotExist:
            return Response(
                {'error': 'Flagged post not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=False, methods=['get'])
    def user_management(self, request):
        """
        Get user management data for moderators
        """
        # Check moderator access
        user_profile = getattr(request.user, 'profile', None)
        if not user_profile or not user_profile.has_role('moderator'):
            if not (request.user.is_staff or request.user.is_superuser):
                return Response(
                    {'error': 'Moderator access required'}, 
                    status=status.HTTP_403_FORBIDDEN
                )

        # Get users with recent activity
        recent_users = User.objects.filter(
            last_login__gte=timezone.now() - timedelta(days=30)
        ).select_related('profile').order_by('-last_login')[:20]

        users_data = []
        for user in recent_users:
            # Get user's post count and reputation
            post_count = ForumPost.objects.filter(author=user).count()
            try:
                reputation = UserReputation.objects.get(user=user)
                reputation_points = reputation.points
            except UserReputation.DoesNotExist:
                reputation_points = 0

            users_data.append({
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'lastLogin': user.last_login.isoformat() if user.last_login else None,
                'postCount': post_count,
                'reputation': reputation_points,
                'isActive': user.is_active,
                'joinedAt': user.date_joined.isoformat()
            })

        return Response(users_data)

    @action(detail=False, methods=['get'])
    def analytics(self, request):
        """
        Get moderation analytics
        """
        # Check moderator access
        user_profile = getattr(request.user, 'profile', None)
        if not user_profile or not user_profile.has_role('moderator'):
            if not (request.user.is_staff or request.user.is_superuser):
                return Response(
                    {'error': 'Moderator access required'}, 
                    status=status.HTTP_403_FORBIDDEN
                )

        # Get analytics data for the last 30 days
        thirty_days_ago = timezone.now() - timedelta(days=30)
        
        daily_stats = []
        for i in range(30):
            date = (timezone.now() - timedelta(days=i)).date()
            
            posts_count = ForumPost.objects.filter(created_at__date=date).count()
            flagged_count = ForumPost.objects.filter(
                created_at__date=date, 
                is_flagged=True
            ).count()
            
            daily_stats.append({
                'date': date.isoformat(),
                'posts': posts_count,
                'flagged': flagged_count
            })

        analytics_data = {
            'dailyStats': daily_stats,
            'totalUsers': User.objects.count(),
            'activeUsers': User.objects.filter(
                last_login__gte=thirty_days_ago
            ).count(),
            'totalPosts': ForumPost.objects.count(),
            'flaggedPosts': ForumPost.objects.filter(is_flagged=True).count()
        }

        return Response(analytics_data)
