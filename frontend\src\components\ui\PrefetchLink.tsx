import React from 'react';
import { Link, LinkProps, useNavigate } from 'react-router-dom';
import { useQueryClient } from '@tanstack/react-query';
import { QueryKey } from '@tanstack/react-query';

import { useTranslation } from 'react-i18next';
interface PrefetchLinkProps extends LinkProps {
  prefetchQueryKey?: QueryKey;
  prefetchQueryFn?: () => Promise<any>;
  prefetchTimeout?: number;
  children: React.ReactNode;
}

/**
 * A Link component that prefetches data for the target route when hovered
 * This is different from PreloadLink which preloads the component code
 */
const PrefetchLink: React.FC<PrefetchLinkProps> = ({ to,
  prefetchQueryKey,
  prefetchQueryFn,
  prefetchTimeout = 200,
  children,
  ...props
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [isPrefetched, setIsPrefetched] = React.useState(false);
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  let timer: NodeJS.Timeout | null = null;

  const handleMouseEnter = () => {
    if (isPrefetched || !prefetchQueryKey || !prefetchQueryFn) return;

    // Set a timeout to avoid prefetching if the user just moves the mouse over the link briefly
    timer = setTimeout(() => {
      // Prefetch the data
      prefetchQueryFn().then(() => {
        setIsPrefetched(true);
        console.log(`Prefetched data for route: ${to}`, prefetchQueryKey);
      });
    }, prefetchTimeout);
  };

  const handleMouseLeave = () => {
    // Clear the timeout if the user moves the mouse away before the timeout
    if (timer) {
      clearTimeout(timer);
      timer = null;
    }
  };

  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    if (props.onClick) {
      props.onClick(e);
    }
    
    // If the link is not a standard navigation (e.g., has onClick that prevents default),
    // don't interfere with it
    if (e.defaultPrevented) return;
    
    // If the data is not yet prefetched, prefetch it immediately
    if (!isPrefetched && prefetchQueryKey && prefetchQueryFn) {
      e.preventDefault();
      prefetchQueryFn().then(() => {
        navigate(to.toString());
      });
    }
  };

  return (
    <Link
      to={to}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={prefetchQueryKey && prefetchQueryFn ? handleClick : undefined}
      {...props}
    >
      {children}
    </Link>
  );
};

export default PrefetchLink;
