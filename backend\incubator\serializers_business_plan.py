from rest_framework import serializers
from django.contrib.auth.models import User
from django.db import models
from .models_business_plan import (
    BusinessPlanTemplate, BusinessPlan, BusinessPlanSection,
    CustomBusinessPlanTemplate, TemplateSectionDefinition
)
from .models_base import BusinessIdea
from users.serializers import UserSerializer

class TemplateSectionDefinitionSerializer(serializers.ModelSerializer):
    """Serializer for template section definitions"""

    class Meta:
        model = TemplateSectionDefinition
        fields = [
            'id', 'title', 'key', 'description', 'section_type',
            'default_content', 'structure_definition', 'ai_prompt_template',
            'is_system', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class BusinessPlanTemplateSerializer(serializers.ModelSerializer):
    """Serializer for business plan templates"""

    template_type_display = serializers.SerializerMethodField()
    category = serializers.CharField(source='industry', read_only=True)
    author_details = UserSerializer(source='author', read_only=True)
    popularity_score = serializers.ReadOnlyField()
    is_new = serializers.ReadOnlyField()

    class Meta:
        model = BusinessPlanTemplate
        fields = [
            'id', 'name', 'description', 'industry', 'category', 'template_type', 'template_type_display',
            'sections', 'allows_customization', 'customization_options',
            'difficulty_level', 'estimated_time', 'usage_count', 'rating', 'rating_count', 'completion_rate',
            'is_premium', 'is_featured', 'is_bestseller', 'tags', 'icon', 'author', 'author_details',
            'popularity_score', 'is_new', 'is_active', 'is_system', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'usage_count', 'rating', 'rating_count', 'completion_rate',
                           'is_bestseller', 'popularity_score', 'is_new', 'author_details',
                           'created_at', 'updated_at']

    def get_template_type_display(self, obj):
        return dict(BusinessPlanTemplate.TEMPLATE_TYPE_CHOICES).get(obj.template_type, obj.template_type)


class CustomBusinessPlanTemplateSerializer(serializers.ModelSerializer):
    """Serializer for custom business plan templates"""

    base_template_details = BusinessPlanTemplateSerializer(source='base_template', read_only=True)
    owner_details = UserSerializer(source='owner', read_only=True)
    shared_with_details = UserSerializer(source='shared_with', many=True, read_only=True)

    class Meta:
        model = CustomBusinessPlanTemplate
        fields = [
            'id', 'base_template', 'base_template_details', 'name', 'description',
            'owner', 'owner_details', 'sections', 'custom_prompts', 'custom_instructions',
            'is_public', 'shared_with', 'shared_with_details', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'owner_details', 'base_template_details',
                           'shared_with_details', 'created_at', 'updated_at']

    def create(self, validated_data):
        """Create a custom template and set the owner"""
        user = self.context['request'].user
        validated_data['owner'] = user

        # Get shared_with users
        shared_with = validated_data.pop('shared_with', [])

        # Create the custom template
        custom_template = CustomBusinessPlanTemplate.objects.create(**validated_data)

        # Add shared users
        if shared_with:
            custom_template.shared_with.set(shared_with)

        return custom_template


class BusinessPlanSectionSerializer(serializers.ModelSerializer):
    """Serializer for business plan sections"""

    section_definition_details = TemplateSectionDefinitionSerializer(source='section_definition', read_only=True)

    class Meta:
        model = BusinessPlanSection
        fields = [
            'id', 'business_plan', 'section_definition', 'section_definition_details',
            'title', 'key', 'content', 'order', 'is_required', 'is_completed',
            'custom_prompt', 'custom_instructions', 'additional_data', 'ai_suggestions'
        ]
        read_only_fields = ['id', 'section_definition_details']


class BusinessPlanSerializer(serializers.ModelSerializer):
    """Serializer for business plans"""

    owner_details = UserSerializer(source='owner', read_only=True)
    business_idea_title = serializers.SerializerMethodField()
    template_name = serializers.CharField(source='template.name', read_only=True, allow_null=True)
    custom_template_details = CustomBusinessPlanTemplateSerializer(source='custom_template', read_only=True)
    sections = BusinessPlanSectionSerializer(many=True, read_only=True)
    status_display = serializers.SerializerMethodField()

    class Meta:
        model = BusinessPlan
        fields = [
            'id', 'business_idea', 'business_idea_title',
            'template', 'template_name', 'custom_template', 'custom_template_details',
            'owner', 'owner_details', 'title', 'status', 'status_display',
            'content', 'ai_feedback', 'completion_percentage', 'version',
            'sections', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'owner', 'owner_details', 'business_idea_title', 'template_name',
                           'custom_template_details', 'completion_percentage', 'version',
                           'created_at', 'updated_at']

    def get_status_display(self, obj):
        return dict(BusinessPlan.STATUS_CHOICES).get(obj.status, obj.status)

    def get_business_idea_title(self, obj):
        return obj.business_idea.title if obj.business_idea else None

    def create(self, validated_data):
        """Create a business plan and its sections from a template or custom template"""
        print(f"Serializer create called with: {validated_data}")

        # Create the business plan (owner and version will be set by perform_create in ViewSet)
        business_plan = super().create(validated_data)
        print(f"Business plan created in serializer: {business_plan.id}")

        # Create sections after the business plan is created
        self._create_sections_for_plan(business_plan)

        return business_plan

    def _create_sections_for_plan(self, business_plan):
        """Create sections for a business plan from template"""
        try:
            # If a custom template is provided, use that
            if business_plan.custom_template:
                custom_template = business_plan.custom_template
                # Handle both old format (with 'sections' wrapper) and new format (direct sections)
                if 'sections' in custom_template.sections and isinstance(custom_template.sections['sections'], dict):
                    sections_data = custom_template.sections['sections']  # Old format
                else:
                    sections_data = custom_template.sections  # New format

                # Convert dict to list format for processing
                if isinstance(sections_data, dict):
                    sections_list = []
                    for key, section_data in sections_data.items():
                        # Create a copy to avoid modifying the original
                        section_copy = dict(section_data) if isinstance(section_data, dict) else {}
                        section_copy['key'] = key
                        sections_list.append(section_copy)
                    sections_data = sections_list
                elif not isinstance(sections_data, list):
                    sections_data = []

                for section_data in sections_data:
                    # Try to get the section definition if available
                    section_definition = None
                    section_definition_key = section_data.get('section_definition_key')
                    if section_definition_key:
                        try:
                            section_definition = TemplateSectionDefinition.objects.get(key=section_definition_key)
                        except TemplateSectionDefinition.DoesNotExist:
                            pass

                    # Create the section with custom prompts and instructions if available
                    section_key = section_data.get('key', '')
                    custom_prompt = custom_template.custom_prompts.get(section_key, '') if hasattr(custom_template, 'custom_prompts') else ''
                    custom_instructions = custom_template.custom_instructions.get(section_key, '') if hasattr(custom_template, 'custom_instructions') else ''

                    BusinessPlanSection.objects.create(
                        business_plan=business_plan,
                        section_definition=section_definition,
                        title=section_data.get('title', ''),
                        key=section_key,
                        content=section_data.get('content', section_data.get('default_content', '')),
                        order=section_data.get('order', 0),
                        is_required=section_data.get('is_required', True),
                        custom_prompt=custom_prompt,
                        custom_instructions=custom_instructions,
                        additional_data=section_data.get('additional_data', {})
                    )

            # If a standard template is provided, create sections based on the template
            elif business_plan.template:
                template = business_plan.template
                print(f"Creating sections from template: {template.name}")

                # Handle both old format (with 'sections' wrapper) and new format (direct sections)
                if 'sections' in template.sections and isinstance(template.sections['sections'], dict):
                    sections_data = template.sections['sections']  # Old format
                else:
                    sections_data = template.sections  # New format

                # Convert dict to list format for processing
                if isinstance(sections_data, dict):
                    sections_list = []
                    for key, section_data in sections_data.items():
                        # Create a copy to avoid modifying the original
                        section_copy = dict(section_data) if isinstance(section_data, dict) else {}
                        section_copy['key'] = key
                        sections_list.append(section_copy)
                    sections_data = sections_list
                elif not isinstance(sections_data, list):
                    sections_data = []

                print(f"Creating {len(sections_data)} sections")

                for section_data in sections_data:
                    # Try to get the section definition if available
                    section_definition = None
                    section_definition_key = section_data.get('section_definition_key')
                    if section_definition_key:
                        try:
                            section_definition = TemplateSectionDefinition.objects.get(key=section_definition_key)
                        except TemplateSectionDefinition.DoesNotExist:
                            pass

                    section = BusinessPlanSection.objects.create(
                        business_plan=business_plan,
                        section_definition=section_definition,
                        title=section_data.get('title', ''),
                        key=section_data.get('key', ''),
                        content=section_data.get('content', section_data.get('default_content', '')),
                        order=section_data.get('order', 0),
                        is_required=section_data.get('is_required', True),
                        additional_data=section_data.get('additional_data', {})
                    )
                    print(f"Created section: {section.title}")
            else:
                print("No template provided")

        except Exception as e:
            print(f"Error creating sections: {e}")
            import traceback
            traceback.print_exc()
            raise


class BusinessPlanDetailSerializer(BusinessPlanSerializer):
    """Detailed serializer for business plans with sections"""

    sections = BusinessPlanSectionSerializer(many=True, read_only=True)

    class Meta(BusinessPlanSerializer.Meta):
        fields = BusinessPlanSerializer.Meta.fields
