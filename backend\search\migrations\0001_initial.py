# Generated by Django 5.1.7 on 2025-05-14 20:42

import django.contrib.postgres.indexes
import django.contrib.postgres.search
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='SearchDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content_type', models.Char<PERSON>ield(max_length=20)),
                ('object_id', models.IntegerField()),
                ('title', models.CharField(max_length=255)),
                ('content', models.TextField()),
                ('author', models.Char<PERSON>ield(blank=True, max_length=150)),
                ('created_at', models.DateTimeField()),
                ('updated_at', models.DateTimeField()),
                ('tags', models.JSONField(blank=True, default=list)),
                ('metadata', models.J<PERSON><PERSON>ield(blank=True, default=dict)),
                ('search_vector', django.contrib.postgres.search.SearchVectorField(null=True)),
            ],
            options={
                'indexes': [django.contrib.postgres.indexes.GinIndex(fields=['search_vector'], name='search_sear_search__b5d516_gin'), models.Index(fields=['content_type'], name='search_sear_content_b6dc6b_idx'), models.Index(fields=['object_id'], name='search_sear_object__019ba5_idx'), models.Index(fields=['created_at'], name='search_sear_created_9638b5_idx')],
                'unique_together': {('content_type', 'object_id')},
            },
        ),
    ]
