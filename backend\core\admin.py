from django.contrib import admin
from .models import AIConfiguration, AIUsageLog, AIServiceStatus, AIRateLimit


@admin.register(AIConfiguration)
class AIConfigurationAdmin(admin.ModelAdmin):
    list_display = ['provider', 'key', 'config_type', 'is_active', 'is_sensitive', 'updated_at']
    list_filter = ['provider', 'config_type', 'is_active', 'is_sensitive']
    search_fields = ['key', 'description']
    readonly_fields = ['created_at', 'updated_at']
    
    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        # Mask sensitive values in admin
        if obj and obj.is_sensitive:
            form.base_fields['value'].widget.attrs['type'] = 'password'
        return form


@admin.register(AIUsageLog)
class AIUsageLogAdmin(admin.ModelAdmin):
    list_display = ['provider', 'model', 'user', 'tokens_used', 'cost', 'response_status', 'created_at']
    list_filter = ['provider', 'model', 'response_status', 'created_at']
    search_fields = ['user__username', 'endpoint']
    readonly_fields = ['created_at']
    date_hierarchy = 'created_at'


@admin.register(AIServiceStatus)
class AIServiceStatusAdmin(admin.ModelAdmin):
    list_display = ['provider', 'status', 'response_time', 'success_rate', 'is_configured', 'last_check']
    list_filter = ['status', 'is_configured']
    readonly_fields = ['last_check']


@admin.register(AIRateLimit)
class AIRateLimitAdmin(admin.ModelAdmin):
    list_display = ['provider', 'limit_type', 'identifier', 'max_requests', 'period', 'current_requests', 'is_active']
    list_filter = ['provider', 'limit_type', 'period', 'is_active']
    search_fields = ['identifier']
