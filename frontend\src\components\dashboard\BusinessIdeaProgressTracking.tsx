import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  Lightbulb,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertTriangle,
  ArrowRight,
  Calendar,
  User
} from 'lucide-react';
import { businessIdeasAPI, BusinessIdea } from '../../services/incubatorApi';
import { BusinessIdeaProgress } from '../../services/api';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Bar } from 'react-chartjs-2';
import { useTranslation } from 'react-i18next';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface BusinessIdeaProgressTrackingProps {
  businessIdeaId: number;
}

const BusinessIdeaProgressTracking: React.FC<BusinessIdeaProgressTrackingProps> = ({ businessIdeaId  }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [loading, setLoading] = useState(true);
  const [idea, setIdea] = useState<BusinessIdea | null>(null);
  const [progress, setProgress] = useState<BusinessIdeaProgress | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch both the business idea and its progress tracking data
        const [ideaData, progressData] = await Promise.all([
          businessIdeasAPI.getBusinessIdea(businessIdeaId),
          businessIdeasAPI.getProgressTracking(businessIdeaId)
        ]);

        setIdea(ideaData);
        setProgress(progressData);
        setError(null);
      } catch (err) {
        console.error('Error fetching business idea progress:', err);
        setError(t("dashboard.failed.to.load", "Failed to load progress data. Please try again later."));
      } finally {
        setLoading(false);
      }
    };

    if (businessIdeaId) {
      fetchData();
    }
  }, [businessIdeaId]);

  // Prepare chart data for updates by month
  const prepareUpdatesChartData = () => {
    if (!progress) return null;

    const months = Object.keys(progress.updates_by_month);
    const updateCounts = months.map(month => progress.updates_by_month[month]);

    return {
      labels: months,
      datasets: [
        {
          label: t("dashboard.updates", "Updates"),
          data: updateCounts,
          backgroundColor: 'rgba(99, 102, 241, 0.7)', // indigo
          borderColor: 'rgba(99, 102, 241, 1)',
          borderWidth: 1,
        },
      ],
    };
  };

  // Prepare chart data for common themes
  const prepareThemesChartData = () => {
    if (!progress) return null;

    // Combine achievement and challenge themes
    const achievementThemes = progress.common_achievement_themes.map(theme => ({
      word: theme.word,
      count: theme.count,
      type: 'achievement'
    }));

    const challengeThemes = progress.common_challenge_themes.map(theme => ({
      word: theme.word,
      count: theme.count,
      type: 'challenge'
    }));

    const allThemes = [...achievementThemes, ...challengeThemes]
      .sort((a, b) => b.count - a.count)
      .slice(0, 10); // Take top 10 themes

    return {
      labels: allThemes.map(theme => theme.word),
      datasets: [
        {
          label: t("dashboard.frequency", "Frequency"),
          data: allThemes.map(theme => theme.count),
          backgroundColor: allThemes.map(theme =>
            theme.type === 'achievement'
              ? 'rgba(34, 197, 94, 0.7)' // green for achievements
              : 'rgba(234, 179, 8, 0.7)'  // yellow for challenges
          ),
          borderColor: allThemes.map(theme =>
            theme.type === 'achievement'
              ? 'rgba(34, 197, 94, 1)'
              : 'rgba(234, 179, 8, 1)'
          ),
          borderWidth: 1,
        },
      ],
    };
  };

  const updatesChartData = prepareUpdatesChartData();
  const themesChartData = prepareThemesChartData();

  // Get stage display name
  const getStageDisplayName = (stage: string) => {
    const stageMap: Record<string, string> = {
      'concept': t("dashboard.concept.stage.validation", "Concept Stage"),
      'validation': t("dashboard.validation.stage", "Validation Stage"),
      'development': t("dashboard.development.stage", "Development Stage"),
      'scaling': t("dashboard.scaling.stage", "Scaling Stage"),
      'established': t("dashboard.established.business", "Established Business")
    };
    return stageMap[stage] || stage;
  };

  if (loading) {
    return (
      <div className={`flex justify-center py-12 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-900/30 backdrop-blur-sm rounded-lg p-4 border border-red-800/50">
        <div className="text-red-400">{error}</div>
      </div>
    );
  }

  if (!idea || !progress) {
    return (
      <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50">
        <div className="text-gray-400">{t("dashboard.no.progress.data", "No progress data available for this business idea.")}</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
        <h2 className="text-xl font-bold">Progress Tracking: {idea.title}</h2>
        <Link
          to={`/dashboard/business-ideas/${idea.id}`}
          className={`text-purple-400 hover:text-purple-300 text-sm flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
        >
          View Idea Details <ArrowRight size={14} className={`ml-1 ${isRTL ? "space-x-reverse" : ""}`} />
        </Link>
      </div>

      {/* Progress Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50">
          <div className={`flex items-center mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
            <Lightbulb size={20} className={`mr-2 text-yellow-400 ${isRTL ? "space-x-reverse" : ""}`} />
            <h3 className="font-semibold">{t("dashboard.current.stage", "Current Stage")}</h3>
          </div>
          <div className="text-lg font-medium">{getStageDisplayName(progress.current_stage)}</div>
        </div>

        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50">
          <div className={`flex items-center mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
            <TrendingUp size={20} className={`mr-2 text-green-400 ${isRTL ? "space-x-reverse" : ""}`} />
            <h3 className="font-semibold">{t("dashboard.progress.updates", "Progress Updates")}</h3>
          </div>
          <div className="text-lg font-medium">{progress.total_updates} updates</div>
          <p className="text-sm text-gray-400">
            {progress.update_frequency > 0
              ? `~${progress.update_frequency.toFixed(1)} updates per month`
              : t("dashboard.no.regular.updates", "No regular updates yet")}
          </p>
        </div>

        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50">
          <div className={`flex items-center mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
            <Clock size={20} className={`mr-2 text-blue-400 ${isRTL ? "space-x-reverse" : ""}`} />
            <h3 className="font-semibold">{t("dashboard.idea.age", "Idea Age")}</h3>
          </div>
          <div className="text-lg font-medium">{progress.idea_age_days} days</div>
          <p className="text-sm text-gray-400">
            {progress.idea_age_days > 30
              ? `${Math.floor(progress.idea_age_days / 30)} months`
              : t("dashboard.less.than.a", "Less than a month")}
          </p>
        </div>
      </div>

      {/* Updates Chart */}
      {updatesChartData && (
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50">
          <h3 className="text-lg font-semibold mb-4">{t("dashboard.updates.by.month", "Updates by Month")}</h3>
          <div className="h-64">
            <Bar
              data={updatesChartData}
              options={{
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                  y: {
                    beginAtZero: true,
                    ticks: {
                      color: '#e2e8f0',
                    },
                    grid: {
                      color: 'rgba(226, 232, 240, 0.1)',
                    },
                  },
                  x: {
                    ticks: {
                      color: '#e2e8f0',
                    },
                    grid: {
                      color: 'rgba(226, 232, 240, 0.1)',
                    },
                  },
                },
                plugins: {
                  legend: {
                    labels: {
                      color: '#e2e8f0',
                    },
                  },
                  title: {
                    display: true,
                    text: t("dashboard.progress.updates.over", "Progress Updates Over Time"),
                    color: '#e2e8f0',
                  },
                },
              }}
            />
          </div>
        </div>
      )}

      {/* Common Themes */}
      {themesChartData && (
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50">
          <h3 className="text-lg font-semibold mb-4">{t("dashboard.common.themes", "Common Themes")}</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <h4 className={`text-md font-medium text-green-300 mb-2 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <CheckCircle size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> Achievements
              </h4>
              <ul className="space-y-1">
                {progress.common_achievement_themes.map((theme, index) => (
                  <li key={index} className={`flex justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
                    <span>{theme.word}</span>
                    <span className="text-gray-400">{theme.count}x</span>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h4 className={`text-md font-medium text-yellow-300 mb-2 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <AlertTriangle size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> Challenges
              </h4>
              <ul className="space-y-1">
                {progress.common_challenge_themes.map((theme, index) => (
                  <li key={index} className={`flex justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
                    <span>{theme.word}</span>
                    <span className="text-gray-400">{theme.count}x</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {themesChartData.labels.length > 0 && (
            <div className="h-64">
              <Bar
                data={themesChartData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  indexAxis: 'y' as const,
                  scales: {
                    y: {
                      ticks: {
                        color: '#e2e8f0',
                      },
                      grid: {
                        color: 'rgba(226, 232, 240, 0.1)',
                      },
                    },
                    x: {
                      beginAtZero: true,
                      ticks: {
                        color: '#e2e8f0',
                      },
                      grid: {
                        color: 'rgba(226, 232, 240, 0.1)',
                      },
                    },
                  },
                  plugins: {
                    legend: {
                      display: false,
                    },
                    title: {
                      display: true,
                      text: t("dashboard.key.themes.frequency", "Key Themes Frequency"),
                      color: '#e2e8f0',
                    },
                  },
                }}
              />
            </div>
          )}
        </div>
      )}

      {/* Recent Updates */}
      <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50">
        <div className={`flex items-center justify-between mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
          <h3 className="text-lg font-semibold">{t("dashboard.recent.updates", "Recent Updates")}</h3>
          <Link
            to={`/dashboard/progress-updates?business_idea=${idea.id}`}
            className={`text-purple-400 hover:text-purple-300 text-sm flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
          >
            View All Updates <ArrowRight size={14} className={`ml-1 ${isRTL ? "space-x-reverse" : ""}`} />
          </Link>
        </div>

        {progress.recent_updates.length === 0 ? (
          <div className="text-gray-400">{t("dashboard.no.updates.recorded", "No updates recorded yet.")}</div>
        ) : (
          <div className="space-y-3">
            {progress.recent_updates.map(update => (
              <div key={update.id} className="bg-indigo-800/30 p-3 rounded">
                <Link
                  to={`/dashboard/progress-updates/${update.id}`}
                  className="text-purple-300 hover:text-purple-200 font-medium"
                >
                  {update.title}
                </Link>
                <div className={`flex items-center justify-between mt-2 text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
                  <span className={`text-gray-400 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    <Calendar size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                    {new Date(update.created_at).toLocaleDateString()}
                  </span>
                  <span className={`text-gray-400 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    <User size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                    {update.created_by}
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default BusinessIdeaProgressTracking;
