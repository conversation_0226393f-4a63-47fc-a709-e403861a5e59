/**
 * Simple Template Creator Component
 * Easy-to-use template creation interface
 */

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useLanguage } from "../../hooks/useLanguage";
import {
  Plus,
  Trash2,
  Save,
  ArrowLeft,
  ArrowRight,
  CheckCircle,
  AlertCircle,
  Users,
  Building,
  ShoppingCart,
  Heart,
  Monitor,
  Utensils
} from 'lucide-react';

interface TemplateSection {
  id: string;
  title: string;
  description: string;
  content: string;
  required: boolean;
  order: number;
}

interface SimpleTemplateCreatorProps {
  onTemplateCreated?: (templateData: any) => void;
  onCancel?: () => void;
}

const SimpleTemplateCreator: React.FC<SimpleTemplateCreatorProps> = ({ onTemplateCreated,
  onCancel
 }) => {
  const { isRTL } = useLanguage(); // Use the language direction from the context
  const { t } = useTranslation(); // Get translation function
  const navigate = useNavigate();

  const [currentStep, setCurrentStep] = useState(1);
  const [templateName, setTemplateName] = useState('');
  const [templateDescription, setTemplateDescription] = useState('');
  const [templateCategory, setTemplateCategory] = useState('general');
  const [templateDifficulty, setTemplateDifficulty] = useState<'beginner' | 'intermediate' | 'advanced'>('beginner');
  const [sections, setSections] = useState<TemplateSection[]>([
    {
      id: 'executive_summary',
      title: 'Executive Summary',
      description: 'Brief overview of your business',
      content: 'Provide a compelling summary of your business concept, mission, and key success factors.',
      required: true,
      order: 1
    },
    {
      id: 'business_description',
      title: 'Business Description',
      description: 'Detailed description of your business',
      content: 'Describe your business, products/services, target market, and competitive advantages.',
      required: true,
      order: 2
    },
    {
      id: 'market_analysis',
      title: 'Market Analysis',
      description: 'Analysis of your target market',
      content: 'Research and analyze your target market, customer segments, and market trends.',
      required: true,
      order: 3
    }
  ]);
  const [saving, setSaving] = useState(false);
  const [success, setSuccess] = useState('');
  const [error, setError] = useState('');

  const categories = [
    { id: 'general', name: t("common.general.business", "General Business"), icon: Building },
    { id: 'technology', name: 'Technology', icon: Monitor },
    { id: 'retail', name: t("common.retail", "Retail"), icon: ShoppingCart },
    { id: 'services', name: t("common.services", "Services"), icon: Users },
    { id: 'hospitality', name: t("common.hospitality", "Hospitality"), icon: Utensils },
    { id: 'nonprofit', name: t("common.nonprofit", "Non-Profit"), icon: Heart }
  ];

  const addSection = () => {
    const newSection: TemplateSection = {
      id: `section_${Date.now()}`,
      title: 'New Section',
      description: 'Description for this section',
      content: 'Content guidelines for this section...',
      required: false,
      order: sections.length + 1
    };
    setSections([...sections, newSection]);
  };

  const removeSection = (sectionId: string) => {
    setSections(sections.filter(section => section.id !== sectionId));
  };

  const updateSection = (sectionId: string, field: keyof TemplateSection, value: any) => {
    setSections(sections.map(section =>
      section.id === sectionId
        ? { ...section, [field]: value }
        : section
    ));
  };

  const saveTemplate = async () => {
    if (!templateName.trim()) {
      setError(t("common.template.name.is", "Template name is required"));
      return;
    }

    if (sections.length === 0) {
      setError(t("common.at.least.one", "At least one section is required"));
      return;
    }

    setSaving(true);
    setError('');

    try {
      // Use real API to create template
      const { customTemplatesAPI } = await import('../../services/templateCustomizationApi');

      const templateData = await customTemplatesAPI.createTemplate({
        name: templateName,
        description: templateDescription,
        base_template: 1, // Default base template
        sections: sections.reduce((acc, section) => {
          acc[section.id] = {
            title: section.title,
            description: section.description,
            order: section.order,
            required: section.required,
            estimated_time: section.estimatedTime
          };
          return acc;
        }, {} as any),
        is_public: false
      });

      setSuccess(t("common.template.created.successfully", "Template created successfully!"));

      if (onTemplateCreated) {
        onTemplateCreated(templateData);
      }

      // Navigate back after success
      setTimeout(() => {
        navigate('/dashboard/templates');
      }, 2000);

    } catch (err) {
      setError(t("common.failed.to.create", "Failed to create template. Please try again."));
    } finally {
      setSaving(false);
    }
  };

  const renderStepIndicator = () => (
    <div className={`flex items-center justify-center mb-8 ${isRTL ? "flex-row-reverse" : ""}`}>
      {[1, 2, 3].map((step) => (
        <div key={step} className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
            currentStep >= step
              ? 'bg-purple-600 text-white'
              : 'bg-gray-700 text-gray-400'}
          }`}>
            {currentStep > step ? <CheckCircle size={16} /> : step}
          </div>
          {step < 3 && (
            <div className={`w-12 h-0.5 ${
              currentStep > step ? 'bg-purple-600' : 'bg-gray-700'}
            }`} />
          )}
        </div>
      ))}
    </div>
  );

  const renderStep1 = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-xl font-semibold mb-4">Basic Information</h3>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">Template Name *</label>
            <input
              type="text"
              value={templateName}
              onChange={(e) => setTemplateName(e.target.value)}
              placeholder="e.g., My Custom Business Plan"
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-purple-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Description</label>
            <textarea
              value={templateDescription}
              onChange={(e) => setTemplateDescription(e.target.value)}
              placeholder={t("common.describe.what.this", "Describe what this template is for...")}
              rows={3}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-purple-500"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">Category</label>
              <select
                value={templateCategory}
                onChange={(e) => setTemplateCategory(e.target.value)}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-purple-500"
              >
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Difficulty</label>
              <select
                value={templateDifficulty}
                onChange={(e) => setTemplateDifficulty(e.target.value as any)}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-purple-500"
              >
                <option value="beginner">Beginner</option>
                <option value="intermediate">Intermediate</option>
                <option value="advanced">Advanced</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-6">
      <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
        <h3 className="text-xl font-semibold">Template Sections</h3>
        <button
          onClick={addSection}
          className={`flex items-center px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors text-sm ${isRTL ? "flex-row-reverse" : ""}`}
        >
          <Plus size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
          Add Section
        </button>
      </div>

      <div className="space-y-4">
        {sections.map((section, index) => (
          <div key={section.id} className="bg-gray-800/50 rounded-lg p-4">
            <div className={`flex items-center justify-between mb-3 ${isRTL ? "flex-row-reverse" : ""}`}>
              <span className="text-sm text-gray-400">Section {index + 1}</span>
              {!section.required && (
                <button
                  onClick={() => removeSection(section.id)}
                  className="text-red-400 hover:text-red-300"
                >
                  <Trash2 size={16} />
                </button>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
              <div>
                <label className="block text-sm font-medium mb-1">Title</label>
                <input
                  type="text"
                  value={section.title}
                  onChange={(e) => updateSection(section.id, 'title', e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-purple-500 text-sm"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Description</label>
                <input
                  type="text"
                  value={section.description}
                  onChange={(e) => updateSection(section.id, 'description', e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-purple-500 text-sm"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Content Guidelines</label>
              <textarea
                value={section.content}
                onChange={(e) => updateSection(section.id, 'content', e.target.value)}
                rows={2}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-purple-500 text-sm"
              />
            </div>

            <div className="mt-3">
              <label className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <input
                  type="checkbox"
                  checked={section.required}
                  onChange={(e) => updateSection(section.id, 'required', e.target.checked)}
                  className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`}
                />
                <span className="text-sm">Required section</span>
              </label>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderStep3 = () => (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold">Review & Save</h3>

      <div className="bg-gray-800/50 rounded-lg p-6">
        <h4 className="font-semibold mb-4">Template Summary</h4>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <span className="text-sm text-gray-400">Name:</span>
            <p className="font-medium">{templateName || 'Untitled Template'}</p>
          </div>
          <div>
            <span className="text-sm text-gray-400">Category:</span>
            <p className="font-medium">{categories.find(c => c.id === templateCategory)?.name}</p>
          </div>
          <div>
            <span className="text-sm text-gray-400">Difficulty:</span>
            <p className="font-medium capitalize">{templateDifficulty}</p>
          </div>
          <div>
            <span className="text-sm text-gray-400">Sections:</span>
            <p className="font-medium">{sections.length} sections</p>
          </div>
        </div>

        {templateDescription && (
          <div className="mb-4">
            <span className="text-sm text-gray-400">Description:</span>
            <p className="text-gray-300">{templateDescription}</p>
          </div>
        )}

        <div>
          <span className="text-sm text-gray-400">Sections:</span>
          <ul className="mt-2 space-y-1">
            {sections.map((section, index) => (
              <li key={section.id} className={`text-sm flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <span className="w-6 text-gray-500">{index + 1}.</span>
                <span>{section.title}</span>
                {section.required && (
                  <span className={`ml-2 px-2 py-0.5 bg-red-600/20 text-red-400 text-xs rounded ${isRTL ? "space-x-reverse" : ""}`}>Required</span>
                )}
              </li>
            ))}
          </ul>
        </div>
      </div>

      {error && (
        <div className="bg-red-900/20 border border-red-500/50 rounded-lg p-4">
          <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <AlertCircle size={20} className={`text-red-400 mr-3 ${isRTL ? "space-x-reverse" : ""}`} />
            <span className="text-red-400">{error}</span>
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-900/20 border border-green-500/50 rounded-lg p-4">
          <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <CheckCircle size={20} className={`text-green-400 mr-3 ${isRTL ? "space-x-reverse" : ""}`} />
            <span className="text-green-400">{success}</span>
          </div>
        </div>
      )}
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className={`flex items-center justify-between h-16 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                onClick={() => onCancel ? onCancel() : navigate(-1)}
                className={`p-2 hover:bg-gray-700 rounded-lg transition-colors mr-4 ${isRTL ? "space-x-reverse" : ""}`}
              >
                <ArrowLeft size={20} />
              </button>
              <div>
                <h1 className="text-xl font-bold">Create Custom Template</h1>
                <p className="text-gray-400 text-sm">Build your own business plan template</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {renderStepIndicator()}

        <div className="bg-gray-800/50 rounded-lg p-6">
          {currentStep === 1 && renderStep1()}
          {currentStep === 2 && renderStep2()}
          {currentStep === 3 && renderStep3()}
        </div>

        {/* Navigation */}
        <div className={`flex items-center justify-between mt-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <button
            onClick={() => setCurrentStep(Math.max(1, currentStep - 1))}
            disabled={currentStep === 1}
            className={`flex items-center px-4 py-2 bg-gray-700 hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <ArrowLeft size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
            Previous
          </button>

          {currentStep < 3 ? (
            <button
              onClick={() => setCurrentStep(Math.min(3, currentStep + 1))}
              className={`flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
            >
              Next
              <ArrowRight size={16} className={`ml-2 ${isRTL ? "space-x-reverse" : ""}`} />
            </button>
          ) : (
            <button
              onClick={saveTemplate}
              disabled={saving}
              className={`flex items-center px-6 py-2 bg-green-600 hover:bg-green-700 disabled:opacity-50 text-white rounded-lg transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
            >
              {saving ? (
                <>
                  <div className={`animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 ${isRTL ? "space-x-reverse" : ""}`}></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                  Save Template
                </>
              )}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default SimpleTemplateCreator;
