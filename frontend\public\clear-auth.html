<!DOCTYPE html>
<html>
<head>
    <title>Clear Authentication</title>
</head>
<body>
    <h1>Clear Authentication Tokens</h1>
    <button onclick="clearAuth()">Clear Auth Tokens</button>
    <p id="status"></p>
    
    <script>
        function clearAuth() {
            // Clear all authentication-related items from localStorage
            localStorage.removeItem('yasmeen_auth_token');
            localStorage.removeItem('yasmeen_refresh_token');
            localStorage.removeItem('persist:auth');
            localStorage.removeItem('persist:root');
            
            // Clear sessionStorage as well
            sessionStorage.clear();
            
            document.getElementById('status').innerHTML = 'Authentication tokens cleared! Please go back to the app and log in again.';
            
            // Redirect to login after 2 seconds
            setTimeout(() => {
                window.location.href = '/login';
            }, 2000);
        }
    </script>
</body>
</html>
