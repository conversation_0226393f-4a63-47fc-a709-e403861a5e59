import React from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../hooks/useLanguage';
import { RTLText, RTLFlex, RTLIcon } from '../../../common';
import { Lightbulb, Plus, FileText, Sparkles } from 'lucide-react';

const NoIdeasCard: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  return (
    <div className="mb-8">
      <div className="bg-gradient-to-r from-purple-900/30 to-blue-900/30 backdrop-blur-sm rounded-lg p-8 border border-purple-500/30 text-center">
        <div className="w-20 h-20 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
          <RTLIcon icon={Lightbulb} size={32} className="text-purple-400" />
        </div>
        
        <RTLText as="h3" className="text-2xl font-bold text-white mb-4">
          {t('dashboard.noIdeas.title', 'Ready to Start Your Journey?')}
        </RTLText>
        
        <RTLText as="p" className="text-gray-300 mb-8 max-w-2xl mx-auto leading-relaxed">
          {t('dashboard.noIdeas.description', 'Transform your entrepreneurial dreams into reality. Create your first business idea and let our AI help you develop it into a comprehensive business plan.')}
        </RTLText>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <div className="bg-purple-800/20 rounded-lg p-4 border border-purple-500/20">
            <RTLIcon icon={Plus} size={24} className="text-purple-400 mx-auto mb-3" />
            <RTLText as="h4" className="font-semibold text-white mb-2">
              {t('dashboard.noIdeas.create.title', 'Create New Idea')}
            </RTLText>
            <RTLText as="p" className="text-gray-400 text-sm">
              {t('dashboard.noIdeas.create.description', 'Start from scratch with your unique business concept')}
            </RTLText>
          </div>

          <div className="bg-blue-800/20 rounded-lg p-4 border border-blue-500/20">
            <RTLIcon icon={FileText} size={24} className="text-blue-400 mx-auto mb-3" />
            <RTLText as="h4" className="font-semibold text-white mb-2">
              {t('dashboard.noIdeas.template.title', 'Use Template')}
            </RTLText>
            <RTLText as="p" className="text-gray-400 text-sm">
              {t('dashboard.noIdeas.template.description', 'Choose from proven business plan templates')}
            </RTLText>
          </div>

          <div className="bg-green-800/20 rounded-lg p-4 border border-green-500/20">
            <RTLIcon icon={Sparkles} size={24} className="text-green-400 mx-auto mb-3" />
            <RTLText as="h4" className="font-semibold text-white mb-2">
              {t('dashboard.noIdeas.ai.title', 'AI Assistant')}
            </RTLText>
            <RTLText as="p" className="text-gray-400 text-sm">
              {t('dashboard.noIdeas.ai.description', 'Get personalized guidance and suggestions')}
            </RTLText>
          </div>
        </div>

        <RTLFlex className="flex-col sm:flex-row gap-4 justify-center">
          <Link
            to="/dashboard/business-ideas/new"
            className={`px-8 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <RTLIcon icon={Plus} size={20} className={isRTL ? "ml-2" : "mr-2"} />
            {t('dashboard.noIdeas.createButton', 'Create Your First Idea')}
          </Link>
          
          <Link
            to="/dashboard/templates"
            className={`px-8 py-3 bg-transparent border border-purple-500 text-purple-300 hover:bg-purple-500/20 rounded-lg font-medium transition-colors flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <RTLIcon icon={FileText} size={20} className={isRTL ? "ml-2" : "mr-2"} />
            {t('dashboard.noIdeas.templatesButton', 'Browse Templates')}
          </Link>
        </RTLFlex>
      </div>
    </div>
  );
};

export default NoIdeasCard;
