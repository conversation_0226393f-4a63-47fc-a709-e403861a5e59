import i18n from 'i18next';

/**
 * Locale-specific validation utility
 * Provides validation functions that adapt to different locales
 */

// Define validation patterns by locale
export const VALIDATION_PATTERNS = {
  // English (US) patterns
  en: {
    // Phone number: (************* or ************
    phoneNumber: /^(\+\d{1,3}\s?)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/,
    // Postal code: 12345 or 12345-6789
    postalCode: /^\d{5}(-\d{4})?$/,
    // Date: MM/DD/YYYY
    date: /^(0[1-9]|1[0-2])\/(0[1-9]|[12]\d|3[01])\/\d{4}$/,
    // Time: HH:MM AM/PM
    time: /^(0?[1-9]|1[0-2]):[0-5][0-9] ?([AP]M)$/i,
    // Currency: $123.45
    currency: /^\$\d{1,3}(,\d{3})*(\.\d{2})?$/,
    // Name: Letters, spaces, hyphens, and apostrophes
    name: /^[A-Za-z\s'-]+$/,
    // Address: Alphanumeric with common punctuation
    address: /^[A-Za-z0-9\s,.'-]+$/,
    // Email: Standard email format
    email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  },
  
  // Arabic patterns
  ar: {
    // Phone number: International format or local formats used in Arabic-speaking countries
    phoneNumber: /^(\+\d{1,3}\s?)?\d{9,10}$/,
    // Postal code: Varies by country, this is a generic 5-digit pattern
    postalCode: /^\d{5}$/,
    // Date: DD/MM/YYYY (common in Arabic-speaking countries)
    date: /^(0[1-9]|[12]\d|3[01])\/(0[1-9]|1[0-2])\/\d{4}$/,
    // Time: HH:MM in 24-hour format
    time: /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/,
    // Currency: Generic number with optional decimal
    currency: /^\d{1,3}(,\d{3})*(\.\d{2})?$/,
    // Name: Arabic letters, spaces, and common name punctuation
    name: /^[\u0600-\u06FF\s'-]+$/,
    // Address: Arabic and numeric characters with common punctuation
    address: /^[\u0600-\u06FF0-9\s,.'-]+$/,
    // Email: Standard email format
    email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  },
};

// Type for validation patterns
export type ValidationPatterns = typeof VALIDATION_PATTERNS.en;

/**
 * Get validation patterns for the current or specified language
 * @param lang Optional language code
 * @returns Validation patterns
 */
export const getValidationPatterns = (lang?: string): ValidationPatterns => {
  const currentLang = lang || i18n.language || 'en';
  return VALIDATION_PATTERNS[currentLang as keyof typeof VALIDATION_PATTERNS] || VALIDATION_PATTERNS.en;
};

/**
 * Validate a phone number based on the current locale
 * @param phoneNumber Phone number to validate
 * @param lang Optional language code
 * @returns True if valid, false otherwise
 */
export const validatePhoneNumber = (phoneNumber: string, lang?: string): boolean => {
  const patterns = getValidationPatterns(lang);
  return patterns.phoneNumber.test(phoneNumber);
};

/**
 * Validate a postal code based on the current locale
 * @param postalCode Postal code to validate
 * @param lang Optional language code
 * @returns True if valid, false otherwise
 */
export const validatePostalCode = (postalCode: string, lang?: string): boolean => {
  const patterns = getValidationPatterns(lang);
  return patterns.postalCode.test(postalCode);
};

/**
 * Validate a date string based on the current locale
 * @param dateString Date string to validate
 * @param lang Optional language code
 * @returns True if valid, false otherwise
 */
export const validateDate = (dateString: string, lang?: string): boolean => {
  const patterns = getValidationPatterns(lang);
  
  // First check if the format matches the pattern
  if (!patterns.date.test(dateString)) {
    return false;
  }
  
  // Then check if it's a valid date
  const parts = dateString.split('/');
  let day, month, year;
  
  // Parse based on locale format
  if (lang === 'en') {
    // MM/DD/YYYY format for English
    month = parseInt(parts[0], 10);
    day = parseInt(parts[1], 10);
  } else {
    // DD/MM/YYYY format for other languages
    day = parseInt(parts[0], 10);
    month = parseInt(parts[1], 10);
  }
  year = parseInt(parts[2], 10);
  
  // Check if the date is valid
  const date = new Date(year, month - 1, day);
  return (
    date.getFullYear() === year &&
    date.getMonth() === month - 1 &&
    date.getDate() === day
  );
};

/**
 * Validate a time string based on the current locale
 * @param timeString Time string to validate
 * @param lang Optional language code
 * @returns True if valid, false otherwise
 */
export const validateTime = (timeString: string, lang?: string): boolean => {
  const patterns = getValidationPatterns(lang);
  return patterns.time.test(timeString);
};

/**
 * Validate a currency string based on the current locale
 * @param currencyString Currency string to validate
 * @param lang Optional language code
 * @returns True if valid, false otherwise
 */
export const validateCurrency = (currencyString: string, lang?: string): boolean => {
  const patterns = getValidationPatterns(lang);
  return patterns.currency.test(currencyString);
};

/**
 * Validate a name based on the current locale
 * @param name Name to validate
 * @param lang Optional language code
 * @returns True if valid, false otherwise
 */
export const validateName = (name: string, lang?: string): boolean => {
  const patterns = getValidationPatterns(lang);
  return patterns.name.test(name);
};

/**
 * Validate an address based on the current locale
 * @param address Address to validate
 * @param lang Optional language code
 * @returns True if valid, false otherwise
 */
export const validateAddress = (address: string, lang?: string): boolean => {
  const patterns = getValidationPatterns(lang);
  return patterns.address.test(address);
};

/**
 * Validate an email address
 * @param email Email to validate
 * @returns True if valid, false otherwise
 */
export const validateEmail = (email: string): boolean => {
  // Email validation is the same for all locales
  const patterns = getValidationPatterns();
  return patterns.email.test(email);
};

/**
 * Validate a URL
 * @param url URL to validate
 * @returns True if valid, false otherwise
 */
export const validateUrl = (url: string): boolean => {
  const urlPattern = /^https?:\/\/.+/;
  return urlPattern.test(url);
};

/**
 * Validate a password
 * @param password Password to validate
 * @param minLength Minimum length (default: 8)
 * @returns True if valid, false otherwise
 */
export const validatePassword = (password: string, minLength: number = 8): boolean => {
  if (password.length < minLength) return false;

  // Check for at least one uppercase, one lowercase, one number
  const hasUppercase = /[A-Z]/.test(password);
  const hasLowercase = /[a-z]/.test(password);
  const hasNumber = /\d/.test(password);

  return hasUppercase && hasLowercase && hasNumber;
};

/**
 * Validate a required field
 * @param value Value to validate
 * @returns True if valid, false otherwise
 */
export const validateRequired = (value: any): boolean => {
  if (value === null || value === undefined) return false;
  if (typeof value === 'string') return value.trim().length > 0;
  if (Array.isArray(value)) return value.length > 0;
  return true;
};

/**
 * Validate minimum length
 * @param value Value to validate
 * @param minLength Minimum length
 * @returns True if valid, false otherwise
 */
export const validateMinLength = (value: string, minLength: number): boolean => {
  return value.length >= minLength;
};

/**
 * Validate maximum length
 * @param value Value to validate
 * @param maxLength Maximum length
 * @returns True if valid, false otherwise
 */
export const validateMaxLength = (value: string, maxLength: number): boolean => {
  return value.length <= maxLength;
};

/**
 * Validate a positive number
 * @param value Value to validate
 * @returns True if valid, false otherwise
 */
export const validatePositiveNumber = (value: number): boolean => {
  return value > 0;
};

/**
 * Validate that one number is greater than another
 * @param value1 First value
 * @param value2 Second value
 * @returns True if value1 > value2
 */
export const validateGreaterThan = (value1: number, value2: number): boolean => {
  return value1 > value2;
};

/**
 * Get a localized error message for a validation error
 * @param field Field name
 * @param type Validation type
 * @param t Translation function
 * @returns Localized error message
 */
export const getValidationErrorMessage = (
  field: string,
  type: 'required' | 'pattern' | 'minLength' | 'maxLength' | 'min' | 'max',
  t: (key: string, options?: any) => string
): string => {
  switch (type) {
    case 'required':
      return t('validation.required', { field: t(`fields.${field}`) });
    case 'pattern':
      return t('validation.pattern', { field: t(`fields.${field}`) });
    case 'minLength':
      return t('validation.minLength', { field: t(`fields.${field}`) });
    case 'maxLength':
      return t('validation.maxLength', { field: t(`fields.${field}`) });
    case 'min':
      return t('validation.min', { field: t(`fields.${field}`) });
    case 'max':
      return t('validation.max', { field: t(`fields.${field}`) });
    default:
      return t('validation.invalid', { field: t(`fields.${field}`) });
  }
};

export default {
  validatePhoneNumber,
  validatePostalCode,
  validateDate,
  validateTime,
  validateCurrency,
  validateName,
  validateAddress,
  validateEmail,
  validateUrl,
  validatePassword,
  validateRequired,
  validateMinLength,
  validateMaxLength,
  validatePositiveNumber,
  validateGreaterThan,
  getValidationErrorMessage,
  getValidationPatterns,
};
