import i18n from 'i18next';

/**
 * Cultural adaptations utility
 * Provides functions for adapting content based on cultural preferences
 */

// Define cultural preferences by language
export const CULTURAL_PREFERENCES = {
  en: {
    // English cultural preferences
    greetingTime: {
      morning: { start: 5, end: 12, greeting: 'Good morning' },
      afternoon: { start: 12, end: 17, greeting: 'Good afternoon' },
      evening: { start: 17, end: 22, greeting: 'Good evening' },
      night: { start: 22, end: 5, greeting: 'Good night' },
    },
    weekStartsOn: 0, // Sunday
    weekendDays: [0, 6], // Sunday and Saturday
    businessHours: { start: 9, end: 17 }, // 9 AM to 5 PM
    formalAddressingRequired: false,
    honorifics: {
      male: 'Mr.',
      female: 'Ms.',
      neutral: '',
    },
    measurementSystem: 'imperial', // imperial or metric
    paperSize: 'letter', // letter or a4
    phoneNumberFormat: '(XXX) XXX-XXXX',
    addressFormat: [
      'street',
      'city',
      'state',
      'zip',
      'country',
    ],
    nameFormat: '{firstName} {lastName}',
  },
  ar: {
    // Arabic cultural preferences
    greetingTime: {
      morning: { start: 5, end: 12, greeting: 'صباح الخير' },
      afternoon: { start: 12, end: 17, greeting: 'مساء الخير' },
      evening: { start: 17, end: 22, greeting: 'مساء الخير' },
      night: { start: 22, end: 5, greeting: 'تصبح على خير' },
    },
    weekStartsOn: 6, // Saturday in many Arabic countries
    weekendDays: [5, 6], // Friday and Saturday in many Arabic countries
    businessHours: { start: 8, end: 16 }, // 8 AM to 4 PM
    formalAddressingRequired: true,
    honorifics: {
      male: 'السيد',
      female: 'السيدة',
      neutral: '',
    },
    measurementSystem: 'metric',
    paperSize: 'a4',
    phoneNumberFormat: 'XXX XXX XXXX',
    addressFormat: [
      'street',
      'city',
      'state',
      'country',
      'zip',
    ],
    nameFormat: '{firstName} {lastName}',
  },
};

// Type for cultural preferences
export type CulturalPreferences = typeof CULTURAL_PREFERENCES.en;

/**
 * Get cultural preferences for the current or specified language
 * @param lang Optional language code
 * @returns Cultural preferences
 */
export const getCulturalPreferences = (lang?: string): CulturalPreferences => {
  const currentLang = lang || i18n.language || 'en';
  return CULTURAL_PREFERENCES[currentLang as keyof typeof CULTURAL_PREFERENCES] || CULTURAL_PREFERENCES.en;
};

/**
 * Get a time-appropriate greeting based on the current time and language
 * @param date Optional date to use (defaults to current time)
 * @returns Appropriate greeting
 */
export const getTimeBasedGreeting = (date: Date = new Date()): string => {
  const preferences = getCulturalPreferences();
  const hour = date.getHours();

  // Determine which part of the day it is
  let greeting = '';
  if (hour >= preferences.greetingTime.morning.start && hour < preferences.greetingTime.morning.end) {
    greeting = preferences.greetingTime.morning.greeting;
  } else if (hour >= preferences.greetingTime.afternoon.start && hour < preferences.greetingTime.afternoon.end) {
    greeting = preferences.greetingTime.afternoon.greeting;
  } else if (hour >= preferences.greetingTime.evening.start && hour < preferences.greetingTime.evening.end) {
    greeting = preferences.greetingTime.evening.greeting;
  } else {
    greeting = preferences.greetingTime.night.greeting;
  }

  return greeting;
};

/**
 * Format a name according to cultural preferences
 * @param firstName First name
 * @param lastName Last name
 * @param options Optional formatting options
 * @returns Formatted name
 */
export const formatName = (
  firstName: string,
  lastName: string,
  options?: {
    includeHonorific?: boolean;
    gender?: 'male' | 'female' | 'neutral';
    formal?: boolean;
  }
): string => {
  const preferences = getCulturalPreferences();
  
  let formattedName = preferences.nameFormat
    .replace('{firstName}', firstName)
    .replace('{lastName}', lastName);
  
  // Add honorific if requested
  if (options?.includeHonorific && options.gender) {
    const honorific = preferences.honorifics[options.gender];
    formattedName = `${honorific} ${formattedName}`;
  }
  
  return formattedName;
};

/**
 * Check if a given date is a weekend day
 * @param date Date to check
 * @returns True if the date is a weekend day
 */
export const isWeekendDay = (date: Date): boolean => {
  const preferences = getCulturalPreferences();
  const day = date.getDay();
  return preferences.weekendDays.includes(day);
};

/**
 * Check if a given time is within business hours
 * @param date Date to check
 * @returns True if the time is within business hours
 */
export const isBusinessHours = (date: Date): boolean => {
  const preferences = getCulturalPreferences();
  const hour = date.getHours();
  return hour >= preferences.businessHours.start && hour < preferences.businessHours.end;
};

export default {
  getCulturalPreferences,
  getTimeBasedGreeting,
  formatName,
  isWeekendDay,
  isBusinessHours,
  CULTURAL_PREFERENCES,
};
