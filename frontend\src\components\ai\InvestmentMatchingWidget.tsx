import { useLanguage } from "../../hooks/useLanguage";
/**
 * Investment Matching Widget
 * Smart AI system for matching entrepreneurs with potential investors
 */

import React, { useState, useEffect } from 'react';
import {
  DollarSign,
  TrendingUp,
  Target,
  Clock,
  CheckCircle,
  AlertTriangle,
  FileText,
  Calendar,
  BarChart3,
  ArrowRight,
  RefreshCw,
  Filter,
  Star
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { getAuthToken } from '../../services/api';

interface InvestmentMatch {
  id: string;
  business_idea_id: number;
  funding_opportunity_id: number;
  investment_type: string;
  match_score: number;
  priority: string;
  match_reasoning: string;
  funding_amount_range: string;
  investor_requirements: string[];
  preparation_steps: string[];
  success_probability: number;
  timeline_estimate: string;
  pitch_recommendations: string[];
  due_diligence_checklist: string[];
  created_at: string;
}

interface InvestmentMatchingWidgetProps {
  businessIdeaId: number;
  className?: string;
}

export const InvestmentMatchingWidget: React.FC<InvestmentMatchingWidgetProps> = ({ businessIdeaId,
  className = ''
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [matches, setMatches] = useState<InvestmentMatch[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedMatch, setSelectedMatch] = useState<InvestmentMatch | null>(null);
  const [filterType, setFilterType] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('match_score');

  const fetchInvestmentMatches = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/ai/autonomous/investment-matching/?business_idea_id=${businessIdeaId}`, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(t("ai.failed.to.fetch", "Failed to fetch investment matches"));
      }

      const data = await response.json();
      setMatches(data.matches || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const takeAction = async (matchId: string, action: string) => {
    try {
      const response = await fetch('/api/ai/autonomous/investment-matching/', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          match_id: matchId,
          action: action
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to ${action} investment match`);
      }

      const result = await response.json();
      alert(`${action.charAt(0).toUpperCase() + action.slice(1)} action initiated successfully!`);
    } catch (err) {
      setError(err instanceof Error ? err.message : `Failed to ${action} investment match`);
    }
  };

  useEffect(() => {
    if (businessIdeaId) {
      fetchInvestmentMatches();
    }
  }, [businessIdeaId]);

  const getInvestmentTypeIcon = (type: string) => {
    switch (type) {
      case 'seed_funding':
        return <Target className="w-5 h-5 text-green-500" />;
      case 'series_a':
      case 'series_b':
        return <TrendingUp className="w-5 h-5 text-blue-500" />;
      case 'venture_capital':
        return <BarChart3 className="w-5 h-5 text-purple-500" />;
      case 'angel_investment':
        return <Star className="w-5 h-5 text-yellow-500" />;
      default:
        return <DollarSign className="w-5 h-5 text-gray-500" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'critical':
        return 'text-red-600 bg-red-100 border-red-200';
      case 'high':
        return 'text-orange-600 bg-orange-100 border-orange-200';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      case 'low':
        return 'text-green-600 bg-green-100 border-green-200';
      default:
        return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const formatInvestmentType = (type: string) => {
    return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const getSuccessProbabilityColor = (probability: number) => {
    if (probability >= 0.8) return 'text-green-600';
    if (probability >= 0.6) return 'text-yellow-600';
    if (probability >= 0.4) return 'text-orange-600';
    return 'text-red-600';
  };

  const sortedAndFilteredMatches = matches
    .filter(match => filterType === 'all' || match.investment_type === filterType)
    .sort((a, b) => {
      switch (sortBy) {
        case 'match_score':
          return b.match_score - a.match_score;
        case 'success_probability':
          return b.success_probability - a.success_probability;
        case 'priority':
          const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
          return (priorityOrder[b.priority as keyof typeof priorityOrder] || 0) -
                 (priorityOrder[a.priority as keyof typeof priorityOrder] || 0);
        default:
          return 0;
      }
    });

  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg shadow-lg border border-gray-200 p-6 ${className}`}>
        <div className={`flex items-center justify-center space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
          <RefreshCw className="w-6 h-6 text-green-500 animate-spin" />
          <span className="text-lg font-medium text-gray-900">
            Finding investment matches...
          </span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white rounded-lg shadow-lg border border-red-200 p-6 ${className}`}>
        <div className={`flex items-center space-x-3 mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
          <AlertTriangle className="w-6 h-6 text-red-500" />
          <h3 className="text-lg font-semibold text-gray-900">t("common.investment.matching.error", "Investment Matching Error")</h3>
        </div>
        <p className="text-red-600 mb-4">{error}</p>
        <button
          onClick={fetchInvestmentMatches}
          className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-lg border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
            <DollarSign className="w-6 h-6 text-green-500" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900">t("common.smart.investment.matching", "Smart Investment Matching")</h3>
              <p className="text-sm text-gray-600">
                {matches.length} investment opportunities found
              </p>
            </div>
          </div>
          <button
            onClick={fetchInvestmentMatches}
            className={`bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <RefreshCw className="w-4 h-4" />
            <span>t("common.refresh", t("common.refresh", "Refresh"))</span>
          </button>
        </div>
      </div>

      {/* Filters and Sorting */}
      <div className="p-6 border-b border-gray-200 bg-gray-50">
        <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`flex items-center space-x-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
              <Filter className="w-4 h-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">t("common.filter", "Filter:")</span>
            </div>

            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-1 text-sm"
            >
              <option value="all">t("common.all.types", "All Types")</option>
              <option value="seed_funding">t("common.seed.funding", "Seed Funding")</option>
              <option value="series_a">t("common.series.a", "Series A")</option>
              <option value="series_b">t("common.series.b", "Series B")</option>
              <option value="venture_capital">t("common.venture.capital", "Venture Capital")</option>
              <option value="angel_investment">t("common.angel.investment", "Angel Investment")</option>
              <option value="government_grant">t("common.government.grant", "Government Grant")</option>
            </select>
          </div>

          <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
            <span className="text-sm font-medium text-gray-700">t("common.sort.by", "Sort by:")</span>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-1 text-sm"
            >
              <option value="match_score">t("common.match.score", "Match Score")</option>
              <option value="success_probability">t("common.success.probability", "Success Probability")</option>
              <option value="priority">t("common.priority", t("common.priority", "Priority"))</option>
            </select>
          </div>
        </div>
      </div>

      {/* Investment Matches */}
      <div className="p-6">
        {sortedAndFilteredMatches.length === 0 ? (
          <div className="text-center py-8">
            <DollarSign className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">t("common.no.investment.matches", "No Investment Matches")</h4>
            <p className="text-gray-600">
              {matches.length === 0
                ? {t("common.no.investment.opportunities", "No investment opportunities available for this business idea."
                : "No matches found with current filters.")}
              }
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {sortedAndFilteredMatches.map((match) => (
              <div
                key={match.id}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className={`flex items-start justify-between mb-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                    {getInvestmentTypeIcon(match.investment_type)}
                    <div>
                      <h4 className="font-medium text-gray-900">
                        {formatInvestmentType(match.investment_type)}
                      </h4>
                      <p className="text-sm text-gray-600">{match.funding_amount_range}</p>
                    </div>
                  </div>
                  <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getPriorityColor(match.priority)}`}>
                      {match.priority}
                    </span>
                  </div>
                </div>

                <p className="text-sm text-gray-700 mb-3">{match.match_reasoning}</p>

                <div className="grid grid-cols-3 gap-4 mb-3">
                  <div className="text-center">
                    <div className="text-lg font-semibold text-blue-600">
                      {Math.round(match.match_score * 100)}%
                    </div>
                    <div className="text-xs text-gray-500">t("common.match.score", "Match Score")</div>
                  </div>
                  <div className="text-center">
                    <div className={`text-lg font-semibold ${getSuccessProbabilityColor(match.success_probability)}`}>
                      {Math.round(match.success_probability * 100)}%
                    </div>
                    <div className="text-xs text-gray-500">t("common.success.rate", "Success Rate")</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-purple-600">
                      {match.timeline_estimate}
                    </div>
                    <div className="text-xs text-gray-500">t("common.timeline", "Timeline")</div>
                  </div>
                </div>

                <div className="mb-3">
                  <h5 className="text-sm font-medium text-gray-900 mb-1">t("common.top.preparation.steps", "Top Preparation Steps:")</h5>
                  <ul className="text-sm text-gray-600 space-y-1">
                    {match.preparation_steps.slice(0, 2).map((step, index) => (
                      <li key={index} className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <CheckCircle className="w-3 h-3 text-green-500" />
                        <span>{step}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
                  <button
                    onClick={() => setSelectedMatch(match)}
                    className={`text-green-600 hover:text-green-800 text-sm font-medium flex items-center space-x-1 ${isRTL ? "flex-row-reverse" : ""}`}
                  >
                    <FileText className="w-4 h-4" />
                    <span>t("common.view.details", "View Details")</span>
                  </button>
                  <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <button
                      onClick={() => takeAction(match.id, 'prepare')}
                      className="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors"
                    >
                      Prepare
                    </button>
                    <button
                      onClick={() => takeAction(match.id, 'apply')}
                      className={`bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm font-medium flex items-center space-x-1 transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
                    >
                      <span>t("common.apply", t("common.apply", "Apply"))</span>
                      <ArrowRight className="w-3 h-3" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Match Details Modal */}
      {selectedMatch && (
        <div className={`fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
                <h3 className="text-lg font-semibold text-gray-900">
                  Investment Match Details
                </h3>
                <button
                  onClick={() => setSelectedMatch(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>
            </div>

            <div className="p-6 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">t("common.investor.requirements", "Investor Requirements:")</h4>
                  <ul className="text-sm text-gray-700 space-y-1">
                    {selectedMatch.investor_requirements.map((req, index) => (
                      <li key={index} className={`flex items-start space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <FileText className="w-4 h-4 text-blue-500 mt-0.5" />
                        <span>{req}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 mb-2">t("common.preparation.steps", "Preparation Steps:")</h4>
                  <ul className="text-sm text-gray-700 space-y-1">
                    {selectedMatch.preparation_steps.map((step, index) => (
                      <li key={index} className={`flex items-start space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                        <span>{step}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              <div>
                <h4 className="font-medium text-gray-900 mb-2">t("common.pitch.recommendations", "Pitch Recommendations:")</h4>
                <ul className="text-sm text-gray-700 space-y-1">
                  {selectedMatch.pitch_recommendations.map((rec, index) => (
                    <li key={index} className={`flex items-start space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <Target className="w-4 h-4 text-purple-500 mt-0.5" />
                      <span>{rec}</span>
                    </li>
                  ))}
                </ul>
              </div>

              <div>
                <h4 className="font-medium text-gray-900 mb-2">t("common.due.diligence.checklist", "Due Diligence Checklist:")</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {selectedMatch.due_diligence_checklist.map((item, index) => (
                    <div key={index} className={`flex items-center space-x-2 text-sm text-gray-700 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <Clock className="w-3 h-3 text-orange-500" />
                      <span>{item}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className={`p-6 border-t border-gray-200 flex justify-end space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                onClick={() => setSelectedMatch(null)}
                className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Close
              </button>
              <button
                onClick={() => {
                  takeAction(selectedMatch.id, 'prepare');
                  setSelectedMatch(null);}
                }}
                className="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg transition-colors"
              >
                Start Preparation
              </button>
              <button
                onClick={() => {
                  takeAction(selectedMatch.id, 'apply');
                  setSelectedMatch(null);}
                }}
                className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
              >
                Apply Now
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default InvestmentMatchingWidget;
