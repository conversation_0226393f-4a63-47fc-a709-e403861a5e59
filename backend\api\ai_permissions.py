"""
AI Permissions and Role-Based Access Control
Handles AI feature access based on user roles and permissions
"""

from rest_framework import permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth.models import User
from users.models import UserProfile, UserRole
import json


class AIFeaturePermission(permissions.BasePermission):
    """
    Custom permission for AI features based on user roles
    """
    
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
        
        # Get the required AI capability from the view
        required_capability = getattr(view, 'required_ai_capability', None)
        if not required_capability:
            return True  # No specific requirement
        
        return self.user_has_ai_capability(request.user, required_capability)
    
    def user_has_ai_capability(self, user, capability_id):
        """Check if user has access to specific AI capability"""
        user_capabilities = get_user_ai_capabilities(user)
        return any(cap['id'] == capability_id for cap in user_capabilities)


def get_user_roles(user):
    """Get all active roles for a user"""
    if not user.is_authenticated:
        return []
    
    roles = []
    
    # ✅ FIXED: Check for Super Admin first
    if user.is_superuser:
        roles.append('super_admin')  # Super Admin gets super_admin role
        roles.append('admin')        # Also include admin capabilities
    elif user.is_staff:
        roles.append('admin')
    
    # Get roles from profile
    try:
        profile = user.userprofile
        active_roles = profile.get_active_roles()
        for role in active_roles:
            if role.name not in roles:
                roles.append(role.name)
    except UserProfile.DoesNotExist:
        pass
    
    # Default to 'user' if no roles found
    if not roles:
        roles.append('user')
    
    return roles


def get_user_ai_capabilities(user):
    """Get AI capabilities for a user based on their roles"""
    user_roles = get_user_roles(user)
    
    # Define AI capabilities by role
    ai_capabilities = {
        'user': [
            {
                'id': 'basic_chat',
                'name': 'AI Assistant',
                'description': 'Basic AI chat for business guidance',
                'features': ['General business advice', 'Basic idea validation', 'Simple Q&A'],
                'rate_limit': {'requests': 20, 'period': 'hour'}
            },
            {
                'id': 'basic_analysis',
                'name': 'Basic Business Analysis',
                'description': 'Simple business idea analysis',
                'features': ['Basic SWOT analysis', 'Simple market feedback'],
                'rate_limit': {'requests': 5, 'period': 'day'}
            }
        ],
        'mentor': [
            {
                'id': 'advanced_chat',
                'name': 'Advanced AI Mentor',
                'description': 'Enhanced AI for mentoring',
                'features': ['Advanced strategy advice', 'Detailed analysis', 'Mentorship tools'],
                'rate_limit': {'requests': 100, 'period': 'hour'}
            },
            {
                'id': 'mentor_analysis',
                'name': 'Mentor-Level Analysis',
                'description': 'Comprehensive business analysis',
                'features': ['Comprehensive SWOT', 'Market assessment', 'Growth strategies'],
                'rate_limit': {'requests': 20, 'period': 'day'}
            },
            {
                'id': 'mentee_support',
                'name': 'Mentee Support Tools',
                'description': 'AI tools for mentoring',
                'features': ['Progress tracking', 'Personalized advice', 'Session planning'],
                'rate_limit': {'requests': 50, 'period': 'day'}
            }
        ],
        'investor': [
            {
                'id': 'investment_analysis',
                'name': 'Investment Analysis AI',
                'description': 'AI-powered investment analysis',
                'features': ['Due diligence', 'Market sizing', 'Financial analysis', 'Risk assessment'],
                'rate_limit': {'requests': 50, 'period': 'day'}
            },
            {
                'id': 'market_intelligence',
                'name': 'Market Intelligence',
                'description': 'Advanced market research',
                'features': ['Market trends', 'Competitor analysis', 'Industry reports'],
                'rate_limit': {'requests': 30, 'period': 'day'}
            }
        ],
        'moderator': [
            {
                'id': 'content_moderation',
                'name': 'AI Content Moderation',
                'description': 'AI-assisted content moderation',
                'features': ['Content screening', 'Sentiment analysis', 'Spam detection'],
                'rate_limit': {'requests': 200, 'period': 'hour'}
            },
            {
                'id': 'community_insights',
                'name': 'Community Analytics',
                'description': 'AI community insights',
                'features': ['User engagement', 'Community trends', 'Growth recommendations'],
                'rate_limit': {'requests': 100, 'period': 'day'}
            }
        ],
        'admin': [
            {
                'id': 'admin_analytics',
                'name': 'Admin AI Analytics',
                'description': 'Comprehensive AI analytics',
                'features': ['Platform analytics', 'AI usage stats', 'Performance optimization'],
                'rate_limit': {'requests': 1000, 'period': 'hour'}
            },
            {
                'id': 'ai_management',
                'name': 'AI System Management',
                'description': 'AI system configuration',
                'features': ['Model configuration', 'Rate limit management', 'Feature control'],
                'rate_limit': {'requests': 500, 'period': 'day'}
            }
        ],
        # ✅ ADDED: Super Admin AI capabilities
        'super_admin': [
            {
                'id': 'super_admin_ai_control',
                'name': 'Super Admin AI Control',
                'description': 'Complete AI system control and management',
                'features': ['Full AI system access', 'Advanced analytics', 'System configuration', 'All AI features'],
                'rate_limit': {'requests': 10000, 'period': 'hour'}  # Higher limits for Super Admin
            },
            {
                'id': 'ai_system_management',
                'name': 'AI System Management',
                'description': 'Complete AI system administration',
                'features': ['Model management', 'API configuration', 'Performance tuning', 'Security controls'],
                'rate_limit': {'requests': 5000, 'period': 'day'}
            },
            {
                'id': 'advanced_ai_analytics',
                'name': 'Advanced AI Analytics',
                'description': 'Comprehensive AI analytics and monitoring',
                'features': ['System metrics', 'Usage analytics', 'Performance monitoring', 'Security auditing'],
                'rate_limit': {'requests': 2000, 'period': 'hour'}
            }
        ]
    }
    
    # Collect capabilities based on user roles
    capabilities = []
    for role in user_roles:
        role_capabilities = ai_capabilities.get(role, [])
        capabilities.extend(role_capabilities)
    
    # Remove duplicates
    unique_capabilities = []
    seen_ids = set()
    for cap in capabilities:
        if cap['id'] not in seen_ids:
            unique_capabilities.append(cap)
            seen_ids.add(cap['id'])
    
    return unique_capabilities


def get_user_ai_rate_limits(user):
    """Get AI rate limits for a user based on their highest role"""
    user_roles = get_user_roles(user)
    
    # Define rate limits by role (highest role wins)
    rate_limits = {
        'super_admin': {'chat': 10000, 'analysis': 5000, 'generation': 2000},  # ✅ ADDED: Highest limits for Super Admin
        'admin': {'chat': 1000, 'analysis': 500, 'generation': 200},
        'moderator': {'chat': 200, 'analysis': 100, 'generation': 50},
        'investor': {'chat': 100, 'analysis': 50, 'generation': 25},
        'mentor': {'chat': 100, 'analysis': 50, 'generation': 25},
        'user': {'chat': 20, 'analysis': 5, 'generation': 3}
    }
    
    # Get highest rate limit based on roles
    max_limits = rate_limits['user']
    
    for role in user_roles:
        role_limits = rate_limits.get(role)
        if role_limits and role_limits['chat'] > max_limits['chat']:
            max_limits = role_limits
    
    return max_limits


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_user_ai_access(request):
    """Get user's AI access information"""
    user = request.user
    
    try:
        user_roles = get_user_roles(user)
        capabilities = get_user_ai_capabilities(user)
        rate_limits = get_user_ai_rate_limits(user)
        
        return Response({
            'success': True,
            'data': {
                'user_id': user.id,
                'username': user.username,
                'roles': user_roles,
                'capabilities': capabilities,
                'rate_limits': rate_limits,
                'primary_role': user_roles[0] if user_roles else 'user',
                'has_advanced_features': any(role in ['super_admin', 'admin', 'moderator', 'investor', 'mentor'] for role in user_roles)
            }
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def check_ai_capability_access(request):
    """Check if user has access to a specific AI capability"""
    capability_id = request.data.get('capability_id')
    
    if not capability_id:
        return Response({
            'success': False,
            'error': 'capability_id is required'
        }, status=status.HTTP_400_BAD_REQUEST)
    
    try:
        user_capabilities = get_user_ai_capabilities(request.user)
        has_access = any(cap['id'] == capability_id for cap in user_capabilities)
        
        return Response({
            'success': True,
            'data': {
                'capability_id': capability_id,
                'has_access': has_access,
                'user_roles': get_user_roles(request.user)
            }
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.IsAdminUser])
def get_all_ai_capabilities(request):
    """Get all available AI capabilities (admin only)"""
    try:
        # This would typically come from a database or configuration
        all_capabilities = {}
        
        # Get capabilities for each role
        for role in ['user', 'mentor', 'investor', 'moderator', 'admin']:
            mock_user = type('MockUser', (), {
                'is_authenticated': True,
                'is_staff': role == 'admin',
                'is_superuser': role == 'admin',
                'userprofile': None
            })()
            
            # Mock the role assignment
            if role != 'admin':
                mock_profile = type('MockProfile', (), {
                    'get_active_roles': lambda: [type('MockRole', (), {'name': role})()]
                })()
                mock_user.userprofile = mock_profile
            
            all_capabilities[role] = get_user_ai_capabilities(mock_user)
        
        return Response({
            'success': True,
            'data': all_capabilities
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
