/**
 * Autonomous Business Advisor
 * AI-powered autonomous business advisor with decision-making capabilities
 */

import React, { useState, useEffect } from 'react';
import {
  Brain,
  Zap,
  Target,
  TrendingUp,
  CheckCircle,
  AlertTriangle,
  Lightbulb,
  Clock,
  Award,
  Users,
  DollarSign,
  BarChart3,
  Settings,
  Play,
  Pause,
  RefreshCw,
  Eye,
  MessageSquare,
  Sparkles,
  Activity
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useConsolidatedAI } from '../../hooks/useConsolidatedAI';
import { getAuthToken } from '../../services/api';


interface AutonomousDecision {
  id: string;
  type: 'strategic' | 'operational' | 'financial' | 'marketing' | 'product';
  title: string;
  description: string;
  reasoning: string[];
  confidence: number;
  impact: 'high' | 'medium' | 'low';
  urgency: 'immediate' | 'soon' | 'planned';
  status: 'pending' | 'approved' | 'rejected' | 'implemented';
  created_at: Date;
  deadline?: Date;
  expected_outcome: string;
  success_metrics: string[];
  risk_factors: string[];
  alternatives: Array<{
    option: string;
    pros: string[];
    cons: string[];
  }>;
}

interface AdvisorInsight {
  id: string;
  category: 'opportunity' | 'threat' | 'optimization' | 'trend' | 'recommendation';
  title: string;
  description: string;
  priority: 'critical' | 'high' | 'medium' | 'low';
  confidence: number;
  data_sources: string[];
  action_items: string[];
  timeline: string;
  potential_impact: number;
}

interface AutonomousBusinessAdvisorProps {
  businessIdeaId: number;
  businessIdeaData: any;
  userId?: number;
  onDecisionUpdate?: (decisions: AutonomousDecision[]) => void;
  className?: string;
}

export const AutonomousBusinessAdvisor: React.FC<AutonomousBusinessAdvisorProps> = ({
  businessIdeaId,
  businessIdeaData,
  userId,
  onDecisionUpdate,
  className = ''
}) => {
  const { t } = useTranslation();
  const [isActive, setIsActive] = useState(true);
  const [decisions, setDecisions] = useState<AutonomousDecision[]>([]);
  const [insights, setInsights] = useState<AdvisorInsight[]>([]);
  const [selectedDecision, setSelectedDecision] = useState<AutonomousDecision | null>(null);
  const [advisorMode, setAdvisorMode] = useState<'autonomous' | 'collaborative' | 'advisory'>('collaborative');
  const [isProcessing, setIsProcessing] = useState(false);

  const { sendMessage } = useConsolidatedAI();

  useEffect(() => {
    if (isActive) {
      generateAutonomousInsights();
      const interval = setInterval(() => {
        generateAutonomousInsights();
      }, 300000); // Every 5 minutes

      return () => clearInterval(interval);
    }
  }, [isActive, businessIdeaData, advisorMode]);

  const generateAutonomousInsights = async () => {
    setIsProcessing(true);
    try {
      const analysis = await generateBusinessAnalysis();
      setDecisions(analysis.decisions);
      setInsights(analysis.insights);
      onDecisionUpdate?.(analysis.decisions);
    } catch (error) {
      console.error('Autonomous analysis failed:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const generateBusinessAnalysis = async () => {
    const prompt = `As an autonomous business advisor, analyze this business idea and provide strategic recommendations:

Business Idea: ${JSON.stringify(businessIdeaData)}
Advisor Mode: ${advisorMode}

Please provide:
1. Strategic decisions that should be made
2. Operational recommendations
3. Financial optimization opportunities
4. Marketing strategy suggestions
5. Product development priorities

For each recommendation, include reasoning, confidence level, expected impact, and implementation timeline.`;

    const response = await sendMessage(prompt, {
      userId,
      businessIdeaId,
      language: 'en',
      context: {
        type: 'autonomous_advisor',
        mode: advisorMode,
        businessIdea: businessIdeaData
      }
    });

    // Generate autonomous decisions and insights using real AI API
    try {
      const response = await fetch('/api/ai/autonomous-business-advisor/', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          business_data: businessIdeaData,
          analysis_type: 'comprehensive'
        })
      });

      const data = await response.json();
      const decisions = data.decisions || [];
      const insights = data.insights || [];

      return { decisions, insights };
    } catch (error) {
      console.error('Error fetching autonomous advisor data:', error);
      return { decisions: [], insights: [] };
    }
  };

  const handleDecisionAction = (decisionId: string, action: 'approve' | 'reject') => {
    setDecisions(prev => prev.map(decision => 
      decision.id === decisionId 
        ? { ...decision, status: action === 'approve' ? 'approved' : 'rejected' }
        : decision
    ));
  };

  const getDecisionIcon = (type: string) => {
    switch (type) {
      case 'strategic':
        return <Target className="w-5 h-5 text-purple-500" />;
      case 'operational':
        return <Settings className="w-5 h-5 text-blue-500" />;
      case 'financial':
        return <DollarSign className="w-5 h-5 text-green-500" />;
      case 'marketing':
        return <TrendingUp className="w-5 h-5 text-orange-500" />;
      case 'product':
        return <Lightbulb className="w-5 h-5 text-yellow-500" />;
      default:
        return <Brain className="w-5 h-5 text-gray-500" />;
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'immediate':
        return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30';
      case 'soon':
        return 'text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/30';
      case 'planned':
        return 'text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30';
      default:
        return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900/30';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30';
      case 'rejected':
        return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30';
      case 'implemented':
        return 'text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30';
      default:
        return 'text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/30';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical':
        return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30';
      case 'high':
        return 'text-orange-600 dark:text-orange-400 bg-orange-100 dark:bg-orange-900/30';
      case 'medium':
        return 'text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/30';
      case 'low':
        return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30';
      default:
        return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900/30';
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
            <Brain className="w-6 h-6 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              {t('ai.advisor.title', 'Autonomous Business Advisor')}
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {t('ai.advisor.subtitle', 'AI-powered autonomous business decision making')}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          {/* Advisor Mode Selector */}
          <select
            value={advisorMode}
            onChange={(e) => setAdvisorMode(e.target.value as any)}
            className="px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
          >
            <option value="advisory">Advisory Mode</option>
            <option value="collaborative">Collaborative Mode</option>
            <option value="autonomous">Autonomous Mode</option>
          </select>

          {/* Active Toggle */}
          <button
            onClick={() => setIsActive(!isActive)}
            className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${
              isActive
                ? 'bg-green-600 text-white hover:bg-green-700'
                : 'bg-gray-600 text-white hover:bg-gray-700'
            }`}
          >
            {isActive ? <Play className="w-4 h-4" /> : <Pause className="w-4 h-4" />}
            <span>{isActive ? t('ai.advisor.active', 'Active') : t('ai.advisor.paused', 'Paused')}</span>
          </button>

          {/* Refresh */}
          <button
            onClick={generateAutonomousInsights}
            disabled={isProcessing}
            className="flex items-center space-x-2 px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 transition-colors"
          >
            <RefreshCw className={`w-4 h-4 ${isProcessing ? 'animate-spin' : ''}`} />
            <span>{t('ai.advisor.refresh', 'Refresh')}</span>
          </button>
        </div>
      </div>

      {/* Status Indicator */}
      {isActive && (
        <div className="bg-gradient-to-r from-purple-900/20 to-blue-900/20 border border-purple-700/30 backdrop-blur-sm rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <Activity className="w-5 h-5 text-purple-500 animate-pulse" />
              <span className="font-medium text-purple-900 dark:text-purple-100">
                {t('ai.advisor.monitoring', 'AI Advisor is actively monitoring your business')}
              </span>
            </div>
            <div className="flex items-center space-x-4 text-sm text-purple-700 dark:text-purple-300">
              <span>{decisions.length} {t('ai.advisor.decisions', 'decisions pending')}</span>
              <span>{insights.length} {t('ai.advisor.insights', 'insights available')}</span>
            </div>
          </div>
        </div>
      )}

      {/* Autonomous Decisions */}
      {decisions.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center space-x-2">
            <Zap className="w-5 h-5 text-purple-500" />
            <span>{t('ai.advisor.autonomousDecisions', 'Autonomous Decisions')}</span>
          </h3>
          
          {decisions.map((decision) => (
            <div
              key={decision.id}
              className="border rounded-lg p-4 hover:shadow-lg transition-all duration-200 bg-gray-800/50 border-gray-700 hover:border-purple-600"
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-start space-x-3">
                  {getDecisionIcon(decision.type)}
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-1">
                      {decision.title}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                      {decision.description}
                    </p>
                    <div className="flex items-center space-x-3">
                      <span className={`text-xs px-2 py-1 rounded-full ${getUrgencyColor(decision.urgency)}`}>
                        {decision.urgency}
                      </span>
                      <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(decision.status)}`}>
                        {decision.status}
                      </span>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {decision.confidence}% confidence
                      </span>
                    </div>
                  </div>
                </div>

                {decision.status === 'pending' && (
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleDecisionAction(decision.id, 'approve')}
                      className="px-3 py-1 text-xs bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                    >
                      {t('ai.advisor.approve', 'Approve')}
                    </button>
                    <button
                      onClick={() => handleDecisionAction(decision.id, 'reject')}
                      className="px-3 py-1 text-xs bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                    >
                      {t('ai.advisor.reject', 'Reject')}
                    </button>
                    <button
                      onClick={() => setSelectedDecision(selectedDecision?.id === decision.id ? null : decision)}
                      className="px-3 py-1 text-xs bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      <Eye className="w-3 h-3" />
                    </button>
                  </div>
                )}
              </div>

              {/* Expanded Decision Details */}
              {selectedDecision?.id === decision.id && (
                <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700 space-y-4">
                  {/* Reasoning */}
                  <div>
                    <h5 className="font-medium text-gray-900 dark:text-white mb-2">
                      {t('ai.advisor.reasoning', 'AI Reasoning')}
                    </h5>
                    <ul className="space-y-1">
                      {decision.reasoning.map((reason, index) => (
                        <li key={index} className="flex items-start space-x-2">
                          <CheckCircle className="w-3 h-3 text-green-500 mt-1 flex-shrink-0" />
                          <span className="text-xs text-gray-600 dark:text-gray-400">{reason}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Expected Outcome */}
                  <div>
                    <h5 className="font-medium text-gray-900 dark:text-white mb-2">
                      {t('ai.advisor.expectedOutcome', 'Expected Outcome')}
                    </h5>
                    <p className="text-xs text-gray-600 dark:text-gray-400">{decision.expected_outcome}</p>
                  </div>

                  {/* Alternatives */}
                  <div>
                    <h5 className="font-medium text-gray-900 dark:text-white mb-2">
                      {t('ai.advisor.alternatives', 'Alternative Options')}
                    </h5>
                    <div className="space-y-2">
                      {decision.alternatives.map((alt, index) => (
                        <div key={index} className="p-2 bg-gray-50 dark:bg-gray-800 rounded-lg">
                          <h6 className="text-xs font-medium text-gray-900 dark:text-white mb-1">
                            {alt.option}
                          </h6>
                          <div className="grid grid-cols-2 gap-2 text-xs">
                            <div>
                              <span className="text-green-600 dark:text-green-400 font-medium">Pros:</span>
                              <ul className="text-gray-600 dark:text-gray-400">
                                {alt.pros.map((pro, i) => <li key={i}>• {pro}</li>)}
                              </ul>
                            </div>
                            <div>
                              <span className="text-red-600 dark:text-red-400 font-medium">Cons:</span>
                              <ul className="text-gray-600 dark:text-gray-400">
                                {alt.cons.map((con, i) => <li key={i}>• {con}</li>)}
                              </ul>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* AI Insights */}
      {insights.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center space-x-2">
            <Sparkles className="w-5 h-5 text-blue-500" />
            <span>{t('ai.advisor.insights', 'AI Insights')}</span>
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {insights.map((insight) => (
              <div
                key={insight.id}
                className="border rounded-lg p-4 bg-gray-800/50 border-gray-700"
              >
                <div className="flex items-start justify-between mb-3">
                  <h4 className="font-medium text-gray-900 dark:text-white">{insight.title}</h4>
                  <span className={`text-xs px-2 py-1 rounded-full ${getPriorityColor(insight.priority)}`}>
                    {insight.priority}
                  </span>
                </div>
                
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  {insight.description}
                </p>
                
                <div className="flex items-center justify-between text-xs">
                  <span className="text-gray-500 dark:text-gray-400">
                    {insight.confidence}% confidence
                  </span>
                  <span className="text-blue-600 dark:text-blue-400">
                    +{insight.potential_impact}% impact
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Empty State */}
      {!isActive && (
        <div className="text-center py-12">
          <Brain className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {t('ai.advisor.paused', 'AI Advisor is Paused')}
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            {t('ai.advisor.pausedDescription', 'Activate the AI advisor to receive autonomous business recommendations')}
          </p>
        </div>
      )}
    </div>
  );
};
