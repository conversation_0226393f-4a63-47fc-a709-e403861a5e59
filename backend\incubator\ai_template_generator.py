"""
AI-Powered Template Generation Service
Generates custom business plan templates based on user input using AI
"""

import json
import logging
from typing import Dict, List, Optional, Any
from django.conf import settings
from .models_business_plan import BusinessPlanTemplate, TemplateSectionDefinition
from .template_definitions import get_template_definition, TEMPLATE_REGISTRY

logger = logging.getLogger(__name__)


class AITemplateGenerator:
    """AI-powered template generation service"""
    
    def __init__(self):
        self.base_templates = TEMPLATE_REGISTRY
        
    def generate_custom_template(
        self,
        business_description: str,
        industry: str,
        business_type: str,
        target_market: str,
        funding_stage: str = "startup",
        special_requirements: List[str] = None,
        user_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Generate a custom template based on user input
        
        Args:
            business_description: Description of the business
            industry: Industry category
            business_type: Type of business
            target_market: Target market description
            funding_stage: Stage of funding (startup, growth, expansion)
            special_requirements: Special sections or requirements
            user_id: User ID for personalization
            
        Returns:
            Dict containing the generated template
        """
        try:
            # Analyze business requirements
            analysis = self._analyze_business_requirements(
                business_description, industry, business_type, target_market, funding_stage
            )
            
            # Select base template
            base_template = self._select_base_template(analysis)
            
            # Generate custom sections
            custom_sections = self._generate_custom_sections(
                analysis, special_requirements or []
            )
            
            # Merge and optimize template
            final_template = self._merge_and_optimize_template(
                base_template, custom_sections, analysis
            )
            
            # Add AI-generated content suggestions
            final_template = self._add_ai_content_suggestions(final_template, analysis)
            
            return {
                "success": True,
                "template": final_template,
                "analysis": analysis,
                "recommendations": self._generate_recommendations(analysis)
            }
            
        except Exception as e:
            logger.error(f"Error generating custom template: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "fallback_template": self._get_fallback_template(business_type)
            }
    
    def _analyze_business_requirements(
        self, 
        business_description: str, 
        industry: str, 
        business_type: str, 
        target_market: str, 
        funding_stage: str
    ) -> Dict[str, Any]:
        """Analyze business requirements to determine template needs"""
        
        # AI analysis simulation (in production, this would use actual AI)
        analysis = {
            "business_description": business_description,
            "industry": industry,
            "business_type": business_type,
            "target_market": target_market,
            "funding_stage": funding_stage,
            "complexity_level": self._determine_complexity_level(business_description, funding_stage),
            "required_sections": self._identify_required_sections(business_type, industry),
            "optional_sections": self._identify_optional_sections(business_type, funding_stage),
            "industry_specific_needs": self._identify_industry_needs(industry),
            "regulatory_requirements": self._identify_regulatory_requirements(industry),
            "financial_complexity": self._assess_financial_complexity(business_type, funding_stage)
        }
        
        return analysis
    
    def _select_base_template(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Select the most appropriate base template"""
        business_type = analysis["business_type"].lower()
        
        # Template mapping based on business type
        template_mapping = {
            "saas": "saas",
            "software": "saas",
            "app": "mobile_app",
            "mobile": "mobile_app",
            "ecommerce": "ecommerce",
            "online_store": "ecommerce",
            "restaurant": "restaurant",
            "food": "restaurant",
            "consulting": "consulting",
            "service": "consulting",
            "nonprofit": "nonprofit",
            "ngo": "nonprofit",
            "fintech": "fintech",
            "finance": "fintech",
            "healthcare": "healthcare",
            "medical": "healthcare",
            "manufacturing": "manufacturing",
            "production": "manufacturing",
            "real_estate": "real_estate",
            "property": "real_estate",
            "education": "education",
            "learning": "education",
            "franchise": "franchise",
            "green": "green_business",
            "sustainable": "green_business"
        }
        
        # Find best matching template
        selected_template_type = "standard"  # default
        for keyword, template_type in template_mapping.items():
            if keyword in business_type or keyword in analysis["industry"].lower():
                selected_template_type = template_type
                break
        
        return get_template_definition(selected_template_type)
    
    def _generate_custom_sections(
        self, 
        analysis: Dict[str, Any], 
        special_requirements: List[str]
    ) -> Dict[str, Any]:
        """Generate custom sections based on analysis"""
        custom_sections = {}
        
        # Add industry-specific sections
        if analysis["industry"].lower() == "technology":
            custom_sections["technology_roadmap"] = {
                "title": "Technology Roadmap",
                "description": "Technical development timeline and milestones",
                "section_type": "feature_roadmap",
                "order": 50,
                "required": False,
                "ai_prompt": f"Create technology roadmap for {analysis['business_description']}",
                "guiding_questions": [
                    "What's your technical development timeline?",
                    "What are your key technical milestones?",
                    "How will you scale your technology?",
                    "What technical risks do you face?"
                ]
            }
        
        if analysis["industry"].lower() == "healthcare":
            custom_sections["regulatory_compliance"] = {
                "title": "Healthcare Regulatory Compliance",
                "description": "Healthcare regulations and compliance requirements",
                "section_type": "checklist",
                "order": 45,
                "required": True,
                "ai_prompt": "Create healthcare compliance plan for medical business",
                "guiding_questions": [
                    "What healthcare regulations apply?",
                    "How will you ensure HIPAA compliance?",
                    "What licenses do you need?",
                    "What quality standards must you meet?"
                ]
            }
        
        # Add funding-stage specific sections
        if analysis["funding_stage"] in ["growth", "expansion"]:
            custom_sections["scaling_strategy"] = {
                "title": "Scaling Strategy",
                "description": "How you'll scale operations and grow the business",
                "section_type": "timeline",
                "order": 55,
                "required": True,
                "ai_prompt": f"Create scaling strategy for {analysis['business_type']} business",
                "guiding_questions": [
                    "How will you scale operations?",
                    "What resources do you need for growth?",
                    "What are your expansion plans?",
                    "How will you maintain quality during growth?"
                ]
            }
        
        # Add special requirement sections
        for requirement in special_requirements:
            if requirement.lower() == "international":
                custom_sections["international_expansion"] = {
                    "title": "International Expansion Strategy",
                    "description": "Strategy for expanding to international markets",
                    "section_type": "timeline",
                    "order": 60,
                    "required": False,
                    "ai_prompt": "Create international expansion strategy",
                    "guiding_questions": [
                        "Which markets will you target?",
                        "What are the regulatory requirements?",
                        "How will you adapt your product/service?",
                        "What are the cultural considerations?"
                    ]
                }
            elif requirement.lower() == "sustainability":
                custom_sections["sustainability_plan"] = {
                    "title": "Sustainability & Environmental Impact",
                    "description": "Environmental sustainability and impact strategy",
                    "section_type": "kpi_dashboard",
                    "order": 65,
                    "required": False,
                    "ai_prompt": "Create sustainability plan for business",
                    "guiding_questions": [
                        "How will you minimize environmental impact?",
                        "What sustainable practices will you implement?",
                        "How will you measure environmental impact?",
                        "What certifications will you pursue?"
                    ]
                }
        
        return custom_sections
    
    def _merge_and_optimize_template(
        self, 
        base_template: Dict[str, Any], 
        custom_sections: Dict[str, Any], 
        analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Merge base template with custom sections and optimize"""
        
        # Start with base template
        merged_template = base_template.copy()
        
        # Add custom sections
        merged_template["sections"].update(custom_sections)
        
        # Optimize section order
        sections_with_order = []
        for section_key, section_data in merged_template["sections"].items():
            sections_with_order.append((section_data.get("order", 999), section_key, section_data))
        
        # Sort by order
        sections_with_order.sort(key=lambda x: x[0])
        
        # Rebuild sections dict with proper order
        optimized_sections = {}
        for i, (_, section_key, section_data) in enumerate(sections_with_order):
            section_data["order"] = i + 1
            optimized_sections[section_key] = section_data
        
        merged_template["sections"] = optimized_sections
        
        # Update template metadata
        merged_template["name"] = f"Custom {analysis['business_type'].title()} Business Plan"
        merged_template["description"] = f"AI-generated template for {analysis['business_description']}"
        merged_template["template_type"] = "custom"
        merged_template["is_ai_generated"] = True
        merged_template["generation_metadata"] = {
            "generated_at": "2024-01-01T00:00:00Z",  # Would use actual timestamp
            "analysis": analysis,
            "base_template": base_template["template_type"],
            "custom_sections_count": len(custom_sections)
        }
        
        return merged_template
    
    def _add_ai_content_suggestions(
        self, 
        template: Dict[str, Any], 
        analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Add AI-generated content suggestions to each section"""
        
        for section_key, section_data in template["sections"].items():
            # Generate AI content suggestions
            section_data["ai_content_suggestions"] = self._generate_section_content_suggestions(
                section_data, analysis
            )
            
            # Add dynamic prompts with business context
            if "ai_prompt" in section_data:
                section_data["ai_prompt"] = section_data["ai_prompt"].format(
                    industry=analysis["industry"],
                    business_type=analysis["business_type"],
                    target_market=analysis["target_market"],
                    business_description=analysis["business_description"]
                )
        
        return template
    
    def _generate_section_content_suggestions(
        self, 
        section_data: Dict[str, Any], 
        analysis: Dict[str, Any]
    ) -> List[str]:
        """Generate content suggestions for a specific section"""
        
        # AI content generation simulation
        suggestions = [
            f"Consider including information about {analysis['target_market']} in this section",
            f"Highlight how your {analysis['business_type']} approach differentiates you",
            f"Include specific metrics and KPIs relevant to {analysis['industry']}",
            "Use data and research to support your statements",
            "Include visual elements like charts or diagrams where appropriate"
        ]
        
        # Section-specific suggestions
        section_type = section_data.get("section_type", "text")
        if section_type == "financial_forecast":
            suggestions.extend([
                "Include 3-5 year financial projections",
                "Show multiple scenarios (conservative, realistic, optimistic)",
                "Include cash flow analysis and break-even calculations"
            ])
        elif section_type == "market_sizing":
            suggestions.extend([
                "Use TAM, SAM, SOM framework for market sizing",
                "Include market growth trends and drivers",
                "Cite credible market research sources"
            ])
        
        return suggestions[:5]  # Limit to 5 suggestions
    
    def _generate_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate recommendations for the business plan"""
        
        recommendations = []
        
        if analysis["complexity_level"] == "high":
            recommendations.append("Consider breaking down complex sections into smaller, manageable parts")
        
        if analysis["funding_stage"] == "startup":
            recommendations.append("Focus on proving market demand and achieving product-market fit")
        
        if analysis["financial_complexity"] == "high":
            recommendations.append("Consider hiring a financial advisor or accountant for detailed projections")
        
        recommendations.extend([
            "Regularly update your business plan as your business evolves",
            "Get feedback from mentors and industry experts",
            "Use real data and research to support your assumptions",
            "Keep your executive summary concise but compelling"
        ])
        
        return recommendations
    
    def _determine_complexity_level(self, business_description: str, funding_stage: str) -> str:
        """Determine the complexity level of the business"""
        complexity_indicators = ["ai", "blockchain", "biotech", "pharmaceutical", "aerospace"]
        
        if any(indicator in business_description.lower() for indicator in complexity_indicators):
            return "high"
        elif funding_stage in ["growth", "expansion"]:
            return "medium"
        else:
            return "low"
    
    def _identify_required_sections(self, business_type: str, industry: str) -> List[str]:
        """Identify required sections based on business type and industry"""
        base_required = ["executive_summary", "market_analysis", "financial_projections"]
        
        if business_type.lower() in ["saas", "software", "app"]:
            base_required.extend(["technology_stack", "user_personas"])
        
        if industry.lower() == "healthcare":
            base_required.append("regulatory_compliance")
        
        return base_required
    
    def _identify_optional_sections(self, business_type: str, funding_stage: str) -> List[str]:
        """Identify optional sections that might be useful"""
        optional = ["competitive_analysis", "marketing_strategy", "operations_plan"]
        
        if funding_stage in ["growth", "expansion"]:
            optional.extend(["scaling_strategy", "team_expansion"])
        
        return optional
    
    def _identify_industry_needs(self, industry: str) -> List[str]:
        """Identify industry-specific needs"""
        industry_needs = {
            "technology": ["technical_roadmap", "ip_strategy"],
            "healthcare": ["regulatory_compliance", "clinical_trials"],
            "finance": ["regulatory_compliance", "risk_management"],
            "education": ["curriculum_design", "accreditation"],
            "manufacturing": ["supply_chain", "quality_control"]
        }
        
        return industry_needs.get(industry.lower(), [])
    
    def _identify_regulatory_requirements(self, industry: str) -> List[str]:
        """Identify regulatory requirements by industry"""
        regulatory_reqs = {
            "healthcare": ["HIPAA", "FDA", "Medical Licensing"],
            "finance": ["SEC", "Banking Regulations", "AML/KYC"],
            "education": ["Accreditation", "Student Privacy"],
            "food": ["FDA", "Health Department", "Food Safety"]
        }
        
        return regulatory_reqs.get(industry.lower(), [])
    
    def _assess_financial_complexity(self, business_type: str, funding_stage: str) -> str:
        """Assess the financial complexity of the business"""
        high_complexity_types = ["fintech", "manufacturing", "healthcare", "real_estate"]
        
        if business_type.lower() in high_complexity_types:
            return "high"
        elif funding_stage in ["growth", "expansion"]:
            return "medium"
        else:
            return "low"
    
    def _get_fallback_template(self, business_type: str) -> Dict[str, Any]:
        """Get fallback template in case of errors"""
        return get_template_definition("standard")


# Singleton instance
ai_template_generator = AITemplateGenerator()
