import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import Button from '../components/ui/Button';
import { Input } from '../components/ui/input';
import { Badge } from '../components/ui/badge';
import { Send, Bot, User, Sparkles, MessageSquare, Zap, Brain, Lightbulb } from 'lucide-react';

interface ChatMessage {
  id: string;
  content: string;
  sender: 'user' | 'yasmeen';
  timestamp: Date;
  type?: 'text' | 'suggestion' | 'analysis';
}

interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  prompt: string;
}

const YasmeenChatPage: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const quickActions: QuickAction[] = [
    {
      id: '1',
      title: 'Business Idea Analysis',
      description: 'Get insights on your business concept',
      icon: <Lightbulb className="w-5 h-5" />,
      prompt: 'Can you help me analyze my business idea and provide feedback on its viability?'
    },
    {
      id: '2',
      title: 'Market Research',
      description: 'Research your target market',
      icon: <Brain className="w-5 h-5" />,
      prompt: 'I need help conducting market research for my business. Can you guide me through the process?'
    },
    {
      id: '3',
      title: 'Business Plan Help',
      description: 'Assistance with business planning',
      icon: <Zap className="w-5 h-5" />,
      prompt: 'Can you help me create a comprehensive business plan for my startup?'
    },
    {
      id: '4',
      title: 'Funding Strategy',
      description: 'Explore funding options',
      icon: <Sparkles className="w-5 h-5" />,
      prompt: 'What are the best funding options for my type of business and how should I approach investors?'
    }
  ];

  // Initial welcome message
  useEffect(() => {
    const welcomeMessage: ChatMessage = {
      id: '1',
      content: `Hello! I'm Yasmeen, your AI business assistant. I'm here to help you with:

• Business idea development and validation
• Market research and analysis
• Business plan creation
• Funding strategies and investor preparation
• Growth strategies and scaling advice

How can I assist you with your entrepreneurial journey today?`,
      sender: 'yasmeen',
      timestamp: new Date(),
      type: 'text'
    };
    setMessages([welcomeMessage]);
  }, []);

  // Scroll to bottom when new messages are added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSendMessage = async (content: string) => {
    if (!content.trim()) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content: content.trim(),
      sender: 'user',
      timestamp: new Date(),
      type: 'text'
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);
    setIsTyping(true);

    // Simulate AI response
    setTimeout(() => {
      const aiResponse: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: generateAIResponse(content),
        sender: 'yasmeen',
        timestamp: new Date(),
        type: 'text'
      };
      
      setMessages(prev => [...prev, aiResponse]);
      setIsLoading(false);
      setIsTyping(false);
    }, 2000);
  };

  const generateAIResponse = (userInput: string): string => {
    const input = userInput.toLowerCase();
    
    if (input.includes('business idea') || input.includes('concept')) {
      return `Great! I'd love to help you analyze your business idea. To provide the best insights, I'll need to understand:

1. **What problem does your business solve?** - The core issue you're addressing
2. **Who is your target audience?** - Demographics and characteristics of your ideal customers
3. **What's your unique value proposition?** - What makes you different from competitors
4. **How will you make money?** - Your revenue model and pricing strategy

Could you share some details about these aspects of your business idea?`;
    }
    
    if (input.includes('market research') || input.includes('target market')) {
      return `Excellent! Market research is crucial for business success. Here's a structured approach I recommend:

**Primary Research:**
• Customer surveys and interviews
• Focus groups with target demographics
• Competitor analysis and pricing studies

**Secondary Research:**
• Industry reports and market size data
• Government statistics and economic indicators
• Online tools like Google Trends and social media insights

**Key Questions to Answer:**
• What's the total addressable market (TAM)?
• Who are your direct and indirect competitors?
• What are current market trends and growth projections?
• What are customer pain points and unmet needs?

Would you like me to help you develop a specific research plan for your industry?`;
    }
    
    if (input.includes('business plan') || input.includes('planning')) {
      return `I'll help you create a comprehensive business plan! Here's the essential structure:

**Executive Summary** - Overview of your business concept
**Company Description** - Mission, vision, and business model
**Market Analysis** - Industry overview and target market research
**Organization & Management** - Team structure and key personnel
**Products/Services** - Detailed description of offerings
**Marketing & Sales Strategy** - Customer acquisition and retention
**Financial Projections** - Revenue forecasts and funding requirements
**Funding Request** - Investment needs and use of funds

Each section should be detailed but concise. Would you like to start with a specific section, or do you have questions about any particular area?`;
    }
    
    if (input.includes('funding') || input.includes('investor') || input.includes('capital')) {
      return `Great question! Here are the main funding options for startups:

**Early Stage:**
• Personal savings and bootstrapping
• Friends and family funding
• Angel investors
• Crowdfunding platforms

**Growth Stage:**
• Venture capital (VC) funding
• Private equity
• Bank loans and lines of credit
• Government grants and programs

**Key Preparation Steps:**
• Develop a solid business plan and financial projections
• Create a compelling pitch deck
• Build a minimum viable product (MVP)
• Demonstrate market traction and customer validation

The best option depends on your business type, growth stage, and funding needs. What stage is your business currently in?`;
    }
    
    return `Thank you for your question! I'm here to help with all aspects of business development. Whether you need assistance with:

• Refining your business concept
• Conducting market research
• Creating financial projections
• Developing marketing strategies
• Preparing for investors

I can provide detailed guidance and actionable insights. Could you be more specific about what aspect of your business you'd like to focus on?`;
  };

  const handleQuickAction = (action: QuickAction) => {
    handleSendMessage(action.prompt);
  };

  const formatTimestamp = (timestamp: Date) => {
    return timestamp.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-4xl mx-auto p-6">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center gap-3 mb-2">
            <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
              <Bot className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Yasmeen AI Assistant</h1>
              <p className="text-gray-600 dark:text-gray-400">Your intelligent business advisor</p>
            </div>
          </div>
          <Badge className="bg-green-100 text-green-800">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
            Online
          </Badge>
        </div>

        {/* Quick Actions */}
        {messages.length <= 1 && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="w-5 h-5" />
                Quick Start
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {quickActions.map((action) => (
                  <div
                    key={action.id}
                    onClick={() => handleQuickAction(action)}
                    className="p-4 border rounded-lg hover:shadow-md transition-shadow cursor-pointer hover:border-blue-300"
                  >
                    <div className="flex items-start gap-3">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        {action.icon}
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900 dark:text-white">{action.title}</h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">{action.description}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Chat Messages */}
        <Card className="mb-6">
          <CardContent className="p-0">
            <div className="h-96 overflow-y-auto p-6 space-y-4">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex gap-3 ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  {message.sender === 'yasmeen' && (
                    <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center flex-shrink-0">
                      <Bot className="w-4 h-4 text-white" />
                    </div>
                  )}
                  
                  <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                    message.sender === 'user'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white'
                  }`}>
                    <div className="whitespace-pre-wrap">{message.content}</div>
                    <div className={`text-xs mt-1 ${
                      message.sender === 'user' ? 'text-blue-100' : 'text-gray-500'
                    }`}>
                      {formatTimestamp(message.timestamp)}
                    </div>
                  </div>

                  {message.sender === 'user' && (
                    <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                      <User className="w-4 h-4 text-white" />
                    </div>
                  )}
                </div>
              ))}

              {isTyping && (
                <div className="flex gap-3 justify-start">
                  <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                    <Bot className="w-4 h-4 text-white" />
                  </div>
                  <div className="bg-gray-100 dark:bg-gray-800 px-4 py-2 rounded-lg">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                  </div>
                </div>
              )}
              
              <div ref={messagesEndRef} />
            </div>
          </CardContent>
        </Card>

        {/* Message Input */}
        <Card>
          <CardContent className="p-4">
            <div className="flex gap-2">
              <Input
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                placeholder="Ask me anything about your business..."
                onKeyPress={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSendMessage(inputMessage);
                  }
                }}
                disabled={isLoading}
                className="flex-1"
              />
              <Button
                onClick={() => handleSendMessage(inputMessage)}
                disabled={!inputMessage.trim() || isLoading}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Send className="w-4 h-4" />
              </Button>
            </div>
            <div className="flex items-center gap-2 mt-2 text-xs text-gray-500">
              <MessageSquare className="w-3 h-3" />
              Press Enter to send, Shift+Enter for new line
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default YasmeenChatPage;
