import React, { useState } from 'react';
import { Search, Plus, Edit, Trash2, CheckCircle, Clock, AlertCircle, Target, TrendingUp, Calendar, Filter, RefreshCw } from 'lucide-react';

import { AdvancedFilter, BulkActions } from '../common';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';
import { 
  useBusinessMilestonesList, 
  useCreateBusinessMilestone, 
  useUpdateBusinessMilestone, 
  useDeleteBusinessMilestone,
  useCompleteMilestone,
  useUpdateMilestoneStatus,
  useBulkMilestoneUpdate,
  useMilestoneAnalytics
} from '../../../hooks/useMilestones';
import { BusinessMilestone } from '../../../services/milestoneApi';
import { Alert, Button } from '../../ui';
import MilestoneModal from './components/MilestoneModal';
import MilestoneDeleteModal from './components/MilestoneDeleteModal';

interface FilterValue {
  status?: string;
  priority?: string;
  business_idea?: string;
  due_date_range?: string;
  search?: string;
}

const MilestonesManagement: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  // State management
  const [selectedMilestones, setSelectedMilestones] = useState<number[]>([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedMilestone, setSelectedMilestone] = useState<BusinessMilestone | null>(null);
  const [filters, setFilters] = useState<FilterValue>({});
  const [searchTerm, setSearchTerm] = useState('');

  // API hooks
  const { data: milestonesData, isLoading, error, refetch } = useBusinessMilestonesList(filters);
  const { data: analytics } = useMilestoneAnalytics();
  const createMilestone = useCreateBusinessMilestone();
  const updateMilestone = useUpdateBusinessMilestone();
  const deleteMilestone = useDeleteBusinessMilestone();
  const completeMilestone = useCompleteMilestone();
  const updateStatus = useUpdateMilestoneStatus();
  const bulkUpdate = useBulkMilestoneUpdate();

  const milestones = milestonesData?.results || [];

  // Filter options
  const filterOptions = [
    {
      key: 'status',
      label: t('admin.status', 'Status'),
      type: 'select' as const,
      options: [
        { value: 'not_started', label: t('milestone.status.notStarted', 'Not Started') },
        { value: 'in_progress', label: t('milestone.status.inProgress', 'In Progress') },
        { value: 'completed', label: t('milestone.status.completed', 'Completed') },
        { value: 'delayed', label: t('milestone.status.delayed', 'Delayed') },
        { value: 'cancelled', label: t('milestone.status.cancelled', 'Cancelled') }
      ]
    },
    {
      key: 'priority',
      label: t('milestone.priority', 'Priority'),
      type: 'select' as const,
      options: [
        { value: 'low', label: t('milestone.priority.low', 'Low') },
        { value: 'medium', label: t('milestone.priority.medium', 'Medium') },
        { value: 'high', label: t('milestone.priority.high', 'High') },
        { value: 'critical', label: t('milestone.priority.critical', 'Critical') }
      ]
    },
    {
      key: 'due_date_range',
      label: t('milestone.dueDate', 'Due Date'),
      type: 'select' as const,
      options: [
        { value: 'overdue', label: t('milestone.overdue', 'Overdue') },
        { value: 'this_week', label: t('milestone.thisWeek', 'This Week') },
        { value: 'this_month', label: t('milestone.thisMonth', 'This Month') },
        { value: 'next_month', label: t('milestone.nextMonth', 'Next Month') }
      ]
    },
    {
      key: 'search',
      label: t('common.search', 'Search'),
      type: 'text' as const,
      placeholder: t('milestone.searchPlaceholder', 'Search milestones...')
    }
  ];

  // Bulk actions
  const bulkActions = [
    {
      id: 'complete',
      label: t('milestone.completeSelected', 'Mark as Completed'),
      icon: CheckCircle,
      variant: 'primary' as const,
      action: async (ids: number[]) => {
        await bulkUpdate.mutateAsync({
          milestone_ids: ids,
          action: 'complete'
        });
        setSelectedMilestones([]);
      }
    },
    {
      id: 'start',
      label: t('milestone.startSelected', 'Start Selected'),
      icon: Clock,
      variant: 'secondary' as const,
      action: async (ids: number[]) => {
        await bulkUpdate.mutateAsync({
          milestone_ids: ids,
          action: 'start'
        });
        setSelectedMilestones([]);
      }
    },
    {
      id: 'delay',
      label: t('milestone.delaySelected', 'Mark as Delayed'),
      icon: AlertCircle,
      variant: 'warning' as const,
      action: async (ids: number[]) => {
        await bulkUpdate.mutateAsync({
          milestone_ids: ids,
          action: 'delay'
        });
        setSelectedMilestones([]);
      }
    },
    {
      id: 'delete',
      label: t('common.delete', 'Delete'),
      icon: Trash2,
      variant: 'danger' as const,
      action: async (ids: number[]) => {
        for (const id of ids) {
          await deleteMilestone.mutateAsync(id);
        }
        setSelectedMilestones([]);
      }
    }
  ];

  // Event handlers
  const handleCreateMilestone = () => {
    setSelectedMilestone(null);
    setShowCreateModal(true);
  };

  const handleEditMilestone = (milestone: BusinessMilestone) => {
    setSelectedMilestone(milestone);
    setShowEditModal(true);
  };

  const handleDeleteMilestone = (milestone: BusinessMilestone) => {
    setSelectedMilestone(milestone);
    setShowDeleteModal(true);
  };

  const handleCompleteMilestone = async (milestone: BusinessMilestone) => {
    try {
      await completeMilestone.mutateAsync({
        id: milestone.id,
        data: { completion_notes: 'Marked as completed from admin panel' }
      });
    } catch (error) {
      console.error('Failed to complete milestone:', error);
    }
  };

  const handleFilterChange = (newFilters: FilterValue) => {
    setFilters(newFilters);
  };

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setFilters(prev => ({ ...prev, search: term }));
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      not_started: { color: 'bg-gray-500', icon: Clock, label: t('milestone.status.notStarted', 'Not Started') },
      in_progress: { color: 'bg-blue-500', icon: Clock, label: t('milestone.status.inProgress', 'In Progress') },
      completed: { color: 'bg-green-500', icon: CheckCircle, label: t('milestone.status.completed', 'Completed') },
      delayed: { color: 'bg-orange-500', icon: AlertCircle, label: t('milestone.status.delayed', 'Delayed') },
      cancelled: { color: 'bg-red-500', icon: Trash2, label: t('milestone.status.cancelled', 'Cancelled') }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.not_started;
    const Icon = config.icon;
    
    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium text-white ${config.color} flex items-center gap-1`}>
        <Icon size={12} />
        {config.label}
      </span>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      low: { color: 'bg-gray-500', label: t('milestone.priority.low', 'Low') },
      medium: { color: 'bg-blue-500', label: t('milestone.priority.medium', 'Medium') },
      high: { color: 'bg-orange-500', label: t('milestone.priority.high', 'High') },
      critical: { color: 'bg-red-500', label: t('milestone.priority.critical', 'Critical') }
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium;
    
    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium text-white ${config.color}`}>
        {config.label}
      </span>
    );
  };

  const isOverdue = (dueDate: string) => {
    return new Date(dueDate) < new Date() && dueDate;
  };

  if (error) {
    return (
      <DashboardLayout currentPage="milestones">
        <Alert variant="error">
          {t('common.error.loadFailed', 'Failed to load data. Please try again.')}
        </Alert>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout currentPage="milestones">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-white">
              {t('milestone.management', 'Milestones Management')}
            </h1>
            <p className="text-gray-400 mt-1">
              {t('milestone.managementDescription', 'Track and manage business milestones')}
            </p>
          </div>
          <div className="flex gap-3">
            <Button
              onClick={() => refetch()}
              variant="secondary"
              size="sm"
              disabled={isLoading}
            >
              <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              {t('common.refresh', 'Refresh')}
            </Button>
            <Button onClick={handleCreateMilestone} size="sm">
              <Plus className="w-4 h-4 mr-2" />
              {t('milestone.create', 'Create Milestone')}
            </Button>
          </div>
        </div>

        {/* Analytics Cards */}
        {analytics && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-indigo-900/50 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center">
                  <Target className="w-5 h-5 text-blue-400" />
                </div>
                <div>
                  <div className="text-gray-400 text-sm">{t('milestone.totalMilestones', 'Total Milestones')}</div>
                  <div className="text-white font-bold">{analytics.total_milestones}</div>
                </div>
              </div>
            </div>
            <div className="bg-indigo-900/50 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                </div>
                <div>
                  <div className="text-gray-400 text-sm">{t('milestone.completed', 'Completed')}</div>
                  <div className="text-white font-bold">{analytics.completed_milestones}</div>
                </div>
              </div>
            </div>
            <div className="bg-indigo-900/50 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-red-500/20 flex items-center justify-center">
                  <AlertCircle className="w-5 h-5 text-red-400" />
                </div>
                <div>
                  <div className="text-gray-400 text-sm">{t('milestone.overdue', 'Overdue')}</div>
                  <div className="text-white font-bold">{analytics.overdue_milestones}</div>
                </div>
              </div>
            </div>
            <div className="bg-indigo-900/50 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-purple-500/20 flex items-center justify-center">
                  <TrendingUp className="w-5 h-5 text-purple-400" />
                </div>
                <div>
                  <div className="text-gray-400 text-sm">{t('milestone.completionRate', 'Completion Rate')}</div>
                  <div className="text-white font-bold">{analytics.completion_rate}%</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Filters and Search */}
        <div className="bg-indigo-900/50 rounded-lg p-4">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder={t('milestone.searchPlaceholder', 'Search milestones...')}
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                />
              </div>
            </div>
            <AdvancedFilter
              filterOptions={filterOptions.map(option => ({
                id: option.key,
                label: option.label,
                type: option.type,
                options: option.options
              }))}
              onFilterChange={(newFilters) => {
                const convertedFilters: FilterValue = {};
                Object.entries(newFilters).forEach(([key, filter]) => {
                  convertedFilters[key] = filter.value as string;
                });
                handleFilterChange(convertedFilters);
              }}
              activeFilters={Object.entries(filters).reduce((acc, [key, value]) => {
                if (value) {
                  acc[key] = { id: key, value };
                }
                return acc;
              }, {} as Record<string, { id: string; value: any }>)}
            />
          </div>
        </div>

        {/* Bulk Actions */}
        {selectedMilestones.length > 0 && (
          <BulkActions
            selectedCount={selectedMilestones.length}
            actions={bulkActions}
            onClearSelection={() => setSelectedMilestones([])}
          />
        )}

        {/* Milestones Table */}
        <div className="bg-indigo-900/50 rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-indigo-800/50">
                <tr>
                  <th className="px-4 py-3 text-left">
                    <input
                      type="checkbox"
                      checked={selectedMilestones.length === milestones.length && milestones.length > 0}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedMilestones(milestones.map(m => m.id));
                        } else {
                          setSelectedMilestones([]);
                        }
                      }}
                      className="rounded border-gray-600 bg-indigo-800 text-purple-500 focus:ring-purple-500"
                    />
                  </th>
                  <th className="px-4 py-3 text-left text-gray-300 font-medium">
                    {t('milestone.title', 'Milestone')}
                  </th>
                  <th className="px-4 py-3 text-left text-gray-300 font-medium">
                    {t('milestone.businessIdea', 'Business Idea')}
                  </th>
                  <th className="px-4 py-3 text-left text-gray-300 font-medium">
                    {t('milestone.priority', 'Priority')}
                  </th>
                  <th className="px-4 py-3 text-left text-gray-300 font-medium">
                    {t('admin.status', 'Status')}
                  </th>
                  <th className="px-4 py-3 text-left text-gray-300 font-medium">
                    {t('milestone.dueDate', 'Due Date')}
                  </th>
                  <th className="px-4 py-3 text-right text-gray-300 font-medium">
                    {t('common.actions', 'Actions')}
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-indigo-800/50">
                {isLoading ? (
                  <tr>
                    <td colSpan={7} className="px-4 py-8 text-center text-gray-400">
                      {t('common.loading', 'Loading...')}
                    </td>
                  </tr>
                ) : milestones.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="px-4 py-8 text-center text-gray-400">
                      {t('milestone.noMilestones', 'No milestones found')}
                    </td>
                  </tr>
                ) : (
                  milestones.map((milestone) => (
                    <tr key={milestone.id} className="hover:bg-indigo-800/30">
                      <td className="px-4 py-3">
                        <input
                          type="checkbox"
                          checked={selectedMilestones.includes(milestone.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedMilestones(prev => [...prev, milestone.id]);
                            } else {
                              setSelectedMilestones(prev => prev.filter(id => id !== milestone.id));
                            }
                          }}
                          className="rounded border-gray-600 bg-indigo-800 text-purple-500 focus:ring-purple-500"
                        />
                      </td>
                      <td className="px-4 py-3">
                        <div className="text-white font-medium">{milestone.title}</div>
                        {milestone.description && (
                          <div className="text-gray-400 text-sm line-clamp-2 mt-1">
                            {milestone.description}
                          </div>
                        )}
                      </td>
                      <td className="px-4 py-3">
                        <div className="text-gray-300">{milestone.business_idea_title}</div>
                      </td>
                      <td className="px-4 py-3">
                        {getPriorityBadge(milestone.priority)}
                      </td>
                      <td className="px-4 py-3">
                        {getStatusBadge(milestone.status)}
                      </td>
                      <td className="px-4 py-3">
                        <div className={`text-sm ${isOverdue(milestone.due_date) ? 'text-red-400' : 'text-gray-300'}`}>
                          {milestone.due_date ? new Date(milestone.due_date).toLocaleDateString() : 'No due date'}
                          {isOverdue(milestone.due_date) && (
                            <div className="text-red-400 text-xs">Overdue</div>
                          )}
                        </div>
                      </td>
                      <td className="px-4 py-3">
                        <div className="flex justify-end gap-2">
                          {milestone.status !== 'completed' && (
                            <Button
                              onClick={() => handleCompleteMilestone(milestone)}
                              variant="primary"
                              size="sm"
                              disabled={completeMilestone.isPending}
                            >
                              <CheckCircle className="w-4 h-4" />
                            </Button>
                          )}
                          <Button
                            onClick={() => handleEditMilestone(milestone)}
                            variant="secondary"
                            size="sm"
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button
                            onClick={() => handleDeleteMilestone(milestone)}
                            variant="danger"
                            size="sm"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Modals */}
        <MilestoneModal
          isOpen={showCreateModal || showEditModal}
          onClose={() => {
            setShowCreateModal(false);
            setShowEditModal(false);
            setSelectedMilestone(null);
          }}
          milestone={selectedMilestone}
          isEditing={showEditModal}
        />

        <MilestoneDeleteModal
          isOpen={showDeleteModal}
          onClose={() => {
            setShowDeleteModal(false);
            setSelectedMilestone(null);
          }}
          milestone={selectedMilestone}
        />
      </div>
    </DashboardLayout>
  );
};

export default MilestonesManagement;
