import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { useAppSelector } from '../../store/hooks';
import { useCRUD } from '../../hooks/useCRUD';
import { FundingOpportunity, fundingOpportunitiesAPI } from '../../services/incubatorApi';
import FundingOpportunityForm from '../../components/incubator/forms/FundingOpportunityForm';
import { CRUDTable } from '../../components/common';
import AuthenticatedLayout from '../../components/layout/AuthenticatedLayout';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  DollarSign, 
  Calendar,
  AlertCircle,
  CheckCircle,
  Clock,
  Award,
  Globe,
  TrendingUp
} from 'lucide-react';

const FundingOpportunitiesPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { user } = useAppSelector(state => state.auth);
  
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [selectedOpportunity, setSelectedOpportunity] = useState<FundingOpportunity | null>(null);

  // CRUD operations for funding opportunities
  const fundingCRUD = useCRUD({
    create: async (data: Partial<FundingOpportunity>) => {
      if (!user) throw new Error('User not authenticated');
      return fundingOpportunitiesAPI.createFundingOpportunity({
        ...data,
        created_by_id: user.id
      });
    },
    read: () => fundingOpportunitiesAPI.getFundingOpportunities(),
    update: (id: number, data: Partial<FundingOpportunity>) => 
      fundingOpportunitiesAPI.updateFundingOpportunity(id, data),
    delete: (id: number) => fundingOpportunitiesAPI.deleteFundingOpportunity(id)
  }, {
    onSuccess: (operation) => {
      if (operation === 'create') {
        setShowCreateForm(false);
      } else if (operation === 'update') {
        setShowEditForm(false);
        setSelectedOpportunity(null);
      } else if (operation === 'delete') {
        setShowDeleteDialog(false);
        setSelectedOpportunity(null);
      }
    }
  });

  // Load data on component mount
  useEffect(() => {
    fundingCRUD.readItems();
  }, []);

  // Handle create
  const handleCreate = async (data: Partial<FundingOpportunity>) => {
    return await fundingCRUD.createItem(data);
  };

  // Handle edit
  const handleEdit = (opportunity: FundingOpportunity) => {
    setSelectedOpportunity(opportunity);
    setShowEditForm(true);
  };

  // Handle update
  const handleUpdate = async (data: Partial<FundingOpportunity>) => {
    if (!selectedOpportunity) return false;
    return await fundingCRUD.updateItem(selectedOpportunity.id, data);
  };

  // Handle delete
  const handleDelete = (opportunity: FundingOpportunity) => {
    setSelectedOpportunity(opportunity);
    setShowDeleteDialog(true);
  };

  // Confirm delete
  const confirmDelete = async () => {
    if (!selectedOpportunity) return;
    await fundingCRUD.deleteItem(selectedOpportunity.id);
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Table columns configuration
  const columns = [
    {
      key: 'title',
      label: t('funding.title', 'Title'),
      render: (opportunity: FundingOpportunity) => (
        <div>
          <div className="font-medium text-white">{opportunity.title}</div>
          <div className="text-sm text-gray-400 truncate max-w-xs">
            {opportunity.description}
          </div>
        </div>
      )
    },
    {
      key: 'funding_type',
      label: t('funding.type', 'Type'),
      render: (opportunity: FundingOpportunity) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          opportunity.funding_type === 'grant' ? 'bg-green-900/50 text-green-200' :
          opportunity.funding_type === 'loan' ? 'bg-blue-900/50 text-blue-200' :
          opportunity.funding_type === 'investment' ? 'bg-purple-900/50 text-purple-200' :
          opportunity.funding_type === 'competition' ? 'bg-yellow-900/50 text-yellow-200' :
          'bg-orange-900/50 text-orange-200'
        }`}>
          {t(`funding.types.${opportunity.funding_type}`, opportunity.funding_type)}
        </span>
      )
    },
    {
      key: 'amount_range',
      label: t('funding.amount', 'Amount'),
      render: (opportunity: FundingOpportunity) => (
        <div className="flex items-center gap-2">
          <DollarSign size={16} className="text-green-400" />
          <span className="text-gray-300">
            {opportunity.amount_min > 0 && opportunity.amount_max > 0 
              ? `${formatCurrency(opportunity.amount_min)} - ${formatCurrency(opportunity.amount_max)}`
              : opportunity.amount_max > 0 
                ? `Up to ${formatCurrency(opportunity.amount_max)}`
                : opportunity.amount_min > 0 
                  ? `From ${formatCurrency(opportunity.amount_min)}`
                  : 'Variable'
            }
          </span>
        </div>
      )
    },
    {
      key: 'deadline',
      label: t('funding.deadline', 'Deadline'),
      render: (opportunity: FundingOpportunity) => (
        <div className="flex items-center gap-2">
          <Calendar size={16} className="text-blue-400" />
          <span className={`text-sm ${
            new Date(opportunity.deadline) < new Date() ? 'text-red-400' : 'text-gray-300'
          }`}>
            {new Date(opportunity.deadline).toLocaleDateString()}
          </span>
        </div>
      )
    },
    {
      key: 'status',
      label: t('funding.status', 'Status'),
      render: (opportunity: FundingOpportunity) => (
        <div className="flex items-center gap-2">
          {opportunity.is_active ? (
            <CheckCircle size={16} className="text-green-400" />
          ) : (
            <Clock size={16} className="text-gray-400" />
          )}
          <span className={`text-sm ${
            opportunity.is_active ? 'text-green-400' : 'text-gray-400'
          }`}>
            {opportunity.is_active ? t('funding.active', 'Active') : t('funding.inactive', 'Inactive')}
          </span>
        </div>
      )
    },
    {
      key: 'created_at',
      label: t('common.created', 'Created'),
      render: (opportunity: FundingOpportunity) => (
        <span className="text-gray-400 text-sm">
          {new Date(opportunity.created_at).toLocaleDateString()}
        </span>
      )
    }
  ];

  // Table actions
  const actions = [
    {
      label: t('common.view', 'View'),
      icon: Eye,
      onClick: (opportunity: FundingOpportunity) => {
        // Navigate to opportunity detail page
        window.location.href = `/funding/${opportunity.id}`;
      },
      variant: 'secondary' as const
    },
    {
      label: t('common.edit', 'Edit'),
      icon: Edit,
      onClick: handleEdit,
      variant: 'primary' as const,
      condition: (opportunity: FundingOpportunity) => opportunity.created_by?.id === user?.id
    },
    {
      label: t('common.delete', 'Delete'),
      icon: Trash2,
      onClick: handleDelete,
      variant: 'danger' as const,
      condition: (opportunity: FundingOpportunity) => opportunity.created_by?.id === user?.id
    }
  ];

  // Stats cards data
  const allOpportunities = fundingCRUD.data;
  const userOpportunities = allOpportunities.filter(opp => opp.created_by?.id === user?.id);
  const activeOpportunities = allOpportunities.filter(opp => opp.is_active);
  const upcomingDeadlines = allOpportunities.filter(opp => {
    const deadline = new Date(opp.deadline);
    const now = new Date();
    const thirtyDaysFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
    return deadline >= now && deadline <= thirtyDaysFromNow;
  });

  const stats = [
    {
      title: t('funding.totalOpportunities', 'Total Opportunities'),
      value: allOpportunities.length,
      icon: DollarSign,
      color: 'blue'
    },
    {
      title: t('funding.activeOpportunities', 'Active'),
      value: activeOpportunities.length,
      icon: CheckCircle,
      color: 'green'
    },
    {
      title: t('funding.upcomingDeadlines', 'Upcoming Deadlines'),
      value: upcomingDeadlines.length,
      icon: Calendar,
      color: 'yellow'
    },
    {
      title: t('funding.myOpportunities', 'My Opportunities'),
      value: userOpportunities.length,
      icon: Award,
      color: 'purple'
    }
  ];

  return (
    <AuthenticatedLayout>
      <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="space-y-6">
        {/* Header */}
        <div className={`flex flex-col sm:flex-row sm:items-center sm:justify-between ${isRTL ? 'sm:flex-row-reverse' : ''}`}>
          <div>
            <h1 className="text-2xl font-bold text-white">
              {t('funding.fundingOpportunities', 'Funding Opportunities')}
            </h1>
            <p className="text-gray-400 mt-1">
              {t('funding.discoverAndManage', 'Discover and manage funding opportunities')}
            </p>
          </div>
          <button
            onClick={() => setShowCreateForm(true)}
            className="mt-4 sm:mt-0 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center gap-2"
          >
            <Plus size={20} />
            {t('funding.createOpportunity', 'Create Opportunity')}
          </button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <div key={index} className="bg-gray-800 rounded-lg p-6">
              <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div>
                  <p className="text-gray-400 text-sm">{stat.title}</p>
                  <p className="text-2xl font-bold text-white mt-1">{stat.value}</p>
                </div>
                <div className={`p-3 rounded-lg ${
                  stat.color === 'blue' ? 'bg-blue-900/50' :
                  stat.color === 'green' ? 'bg-green-900/50' :
                  stat.color === 'yellow' ? 'bg-yellow-900/50' :
                  'bg-purple-900/50'
                }`}>
                  <stat.icon size={24} className={
                    stat.color === 'blue' ? 'text-blue-400' :
                    stat.color === 'green' ? 'text-green-400' :
                    stat.color === 'yellow' ? 'text-yellow-400' :
                    'text-purple-400'
                  } />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Error Display */}
        {fundingCRUD.error && (
          <div className="bg-red-900/50 border border-red-500 rounded-lg p-4">
            <div className="flex items-center gap-2">
              <AlertCircle size={20} className="text-red-400" />
              <span className="text-red-200">{fundingCRUD.error}</span>
            </div>
          </div>
        )}

        {/* Funding Opportunities Table */}
        <div className="bg-gray-800 rounded-lg">
          <CRUDTable
            data={fundingCRUD.data}
            columns={columns}
            actions={actions}
            isLoading={fundingCRUD.isLoading}
            emptyMessage={t('funding.noOpportunities', 'No funding opportunities found. Create the first opportunity to get started!')}
            searchPlaceholder={t('funding.searchOpportunities', 'Search funding opportunities...')}
            title={t('funding.allOpportunities', 'All Opportunities')}
            createButtonLabel={t('funding.createOpportunity', 'Create Opportunity')}
            onCreate={() => setShowCreateForm(true)}
          />
        </div>
      </div>

      {/* Create Form Modal */}
      {showCreateForm && (
        <FundingOpportunityForm
          mode="create"
          onSubmit={handleCreate}
          onCancel={() => setShowCreateForm(false)}
          isSubmitting={fundingCRUD.isLoading}
        />
      )}

      {/* Edit Form Modal */}
      {showEditForm && selectedOpportunity && (
        <FundingOpportunityForm
          mode="edit"
          initialData={selectedOpportunity}
          onSubmit={handleUpdate}
          onCancel={() => {
            setShowEditForm(false);
            setSelectedOpportunity(null);
          }}
          isSubmitting={fundingCRUD.isLoading}
        />
      )}

      {/* Delete Confirmation Dialog */}
      {showDeleteDialog && selectedOpportunity && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-gray-900 rounded-lg shadow-xl w-full max-w-md">
            <div className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <AlertCircle size={24} className="text-red-400" />
                <h3 className="text-lg font-semibold text-white">
                  {t('common.confirmDelete', 'Confirm Delete')}
                </h3>
              </div>
              <p className="text-gray-300 mb-6">
                {t('funding.deleteConfirmation', 'Are you sure you want to delete this funding opportunity? This action cannot be undone.')}
              </p>
              <div className={`flex gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <button
                  onClick={() => {
                    setShowDeleteDialog(false);
                    setSelectedOpportunity(null);
                  }}
                  className="flex-1 px-4 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors"
                  disabled={fundingCRUD.isLoading}
                >
                  {t('common.cancel', 'Cancel')}
                </button>
                <button
                  onClick={confirmDelete}
                  className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
                  disabled={fundingCRUD.isLoading}
                >
                  {fundingCRUD.isLoading ? t('common.deleting', 'Deleting...') : t('common.delete', 'Delete')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
              </div>
        </div>
      </div>
    </AuthenticatedLayout>
  );
};

export default FundingOpportunitiesPage;
