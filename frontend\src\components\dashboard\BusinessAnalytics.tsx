import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  BarChart2,
  RefreshCw,
  TrendingUp,
  Users,
  Award,
  ArrowRight,
  Zap,
  AlertCircle,
  CheckCircle,
  Download
} from 'lucide-react';
import { useAppSelector } from '../../store/hooks';
import { BusinessAnalytics as BusinessAnalyticsType, businessAnalyticsAPI } from '../../services/milestoneApi';
import { exportToCSV, exportToPDF } from '../../utils/exportUtils';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  ArcElement,
} from 'chart.js';
import { Bar, Radar, Doughnut } from 'react-chartjs-2';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

interface BusinessAnalyticsProps {
  businessIdeaId: number;
  businessIdeaTitle: string;
}

const BusinessAnalyticsComponent: React.FC<BusinessAnalyticsProps> = ({ businessIdeaId, businessIdeaTitle  }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { user } = useAppSelector((state) => state.auth);
  const [analytics, setAnalytics] = useState<BusinessAnalyticsType | null>(null);
  const [loading, setLoading] = useState(true);
  const [calculating, setCalculating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  useEffect(() => {
    fetchAnalytics();
  }, [businessIdeaId]);

  const fetchAnalytics = async () => {
    setLoading(true);
    try {
      const data = await businessAnalyticsAPI.getAnalytics(businessIdeaId);
      setAnalytics(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching business analytics:', err);
      setError(t("dashboard.failed.to.load", "Failed to load analytics. Please try again later."));
    } finally {
      setLoading(false);
    }
  };

  const handleCalculateAnalytics = async () => {
    setCalculating(true);
    setSuccessMessage(null);
    try {
      const result = await businessAnalyticsAPI.calculateAnalytics(businessIdeaId);
      setAnalytics(result.analytics);
      setSuccessMessage(t("dashboard.analytics.calculated.successfully", "Analytics calculated successfully."));
      setError(null);
    } catch (err) {
      console.error('Error calculating business analytics:', err);
      setError(t("dashboard.failed.to.calculate", "Failed to calculate analytics. Please try again later."));
    } finally {
      setCalculating(false);
    }
  };

  const handleExportCSV = () => {
    if (!analytics) return;

    const data = [
      {
        'Business Idea': businessIdeaTitle,
        'Progress Rate': analytics.progress_rate.toFixed(2),
        'Milestone Completion Rate': `${analytics.milestone_completion_rate.toFixed(1)}%`,
        'Goal Achievement Rate': `${analytics.goal_achievement_rate.toFixed(1)}%`,
        'Team Size': analytics.team_size,
        'Mentor Engagement': `${analytics.mentor_engagement.toFixed(1)}%`,
        'Industry Percentile': `${analytics.industry_percentile.toFixed(1)}%`,
        'Stage Percentile': `${analytics.stage_percentile.toFixed(1)}%`,
        'Last Calculated': new Date(analytics.last_calculated).toLocaleDateString()
      }
    ];

    exportToCSV(data, `${businessIdeaTitle.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_analytics`);
  };

  const handleExportPDF = () => {
    if (!analytics) return;

    const columns = [
      { header: t("dashboard.metric.datakey.metric", "Metric"), dataKey: 'metric' },
      { header: t("dashboard.value.datakey.value", "Value"), dataKey: 'value' },
      { header: t("dashboard.benchmark.datakey.benchmark", "Benchmark"), dataKey: 'benchmark' }
    ];

    const data = [
      {
        metric: t("dashboard.progress.rate", "Progress Rate"),
        value: analytics.progress_rate.toFixed(2),
        benchmark: t("dashboard.updates.per.month", "Updates per month")
      },
      {
        metric: t("dashboard.milestone.completion.rate", "Milestone Completion Rate"),
        value: `${analytics.milestone_completion_rate.toFixed(1)}%`,
        benchmark: '70% is average'
      },
      {
        metric: t("dashboard.goal.achievement.rate", "Goal Achievement Rate"),
        value: `${analytics.goal_achievement_rate.toFixed(1)}%`,
        benchmark: '60% is average'
      },
      {
        metric: t("dashboard.team.size", "Team Size"),
        value: analytics.team_size.toString(),
        benchmark: '3 is average'
      },
      {
        metric: t("dashboard.mentor.engagement", "Mentor Engagement"),
        value: `${analytics.mentor_engagement.toFixed(1)}%`,
        benchmark: '50% is average'
      },
      {
        metric: t("dashboard.industry.percentile", "Industry Percentile"),
        value: `${analytics.industry_percentile.toFixed(1)}%`,
        benchmark: t("dashboard.higher.is.better", "Higher is better")
      },
      {
        metric: t("dashboard.stage.percentile", "Stage Percentile"),
        value: `${analytics.stage_percentile.toFixed(1)}%`,
        benchmark: t("dashboard.higher.is.better", "Higher is better")
      }
    ];

    exportToPDF(
      columns,
      data,
      `${businessIdeaTitle} - Analytics`,
      `Comparative Analytics for ${businessIdeaTitle}`,
      `${businessIdeaTitle.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_analytics`
    );
  };

  // Prepare radar chart data
  const prepareRadarChartData = () => {
    if (!analytics) return null;

    return {
      labels: [
        t("dashboard.progress.rate", "Progress Rate"),
        'Milestone Completion',
        'Goal Achievement',
        t("dashboard.team.size", "Team Size"),
        'Mentor Engagement',
        t("dashboard.industry.percentile", "Industry Percentile"),
        'Stage Percentile'
      ],
      datasets: [
        {
          label: businessIdeaTitle,
          data: [
            // Normalize values to 0-100 scale
            Math.min(100, analytics.progress_rate * 20), // 5 updates/month = 100%
            analytics.milestone_completion_rate,
            analytics.goal_achievement_rate,
            Math.min(100, analytics.team_size * 25), // 4 team members = 100%
            analytics.mentor_engagement,
            analytics.industry_percentile,
            analytics.stage_percentile
          ],
          backgroundColor: 'rgba(99, 102, 241, 0.2)',
          borderColor: 'rgba(99, 102, 241, 1)',
          borderWidth: 2,
          pointBackgroundColor: 'rgba(99, 102, 241, 1)',
          pointBorderColor: '#fff',
          pointHoverBackgroundColor: '#fff',
          pointHoverBorderColor: 'rgba(99, 102, 241, 1)',
        }
      ],
    };
  };

  // Prepare percentile comparison chart data
  const preparePercentileChartData = () => {
    if (!analytics) return null;

    return {
      labels: [t("dashboard.industry.percentile", "Industry Percentile"), 'Stage Percentile'],
      datasets: [
        {
          label: businessIdeaTitle,
          data: [analytics.industry_percentile, analytics.stage_percentile],
          backgroundColor: [
            'rgba(99, 102, 241, 0.7)',
            'rgba(139, 92, 246, 0.7)',
          ],
          borderColor: [
            'rgba(99, 102, 241, 1)',
            'rgba(139, 92, 246, 1)',
          ],
          borderWidth: 1,
        },
      ],
    };
  };

  // Prepare similar ideas comparison chart data
  const prepareSimilarIdeasChartData = () => {
    if (!analytics || !analytics.similar_ideas || analytics.similar_ideas.length === 0) return null;

    const labels = [businessIdeaTitle, ...analytics.similar_ideas.map(idea => idea.title.substring(0, 15) + (idea.title.length > 15 ? '...' : ''))];

    // Calculate progress updates for current idea
    const currentIdeaProgressCount = analytics.progress_rate * 3; // Approximate based on progress rate

    const data = [currentIdeaProgressCount, ...analytics.similar_ideas.map(idea => idea.progress_count || 0)];

    return {
      labels,
      datasets: [
        {
          label: t("dashboard.progress.updates", "Progress Updates"),
          data,
          backgroundColor: labels.map((_, index) =>
            index === 0
              ? 'rgba(99, 102, 241, 0.7)' // Current idea
              : 'rgba(209, 213, 219, 0.7)' // Similar ideas
          ),
          borderColor: labels.map((_, index) =>
            index === 0
              ? 'rgba(99, 102, 241, 1)'
              : 'rgba(209, 213, 219, 1)'
          ),
          borderWidth: 1,
        },
      ],
    };
  };

  const radarChartData = prepareRadarChartData();
  const percentileChartData = preparePercentileChartData();
  const similarIdeasChartData = prepareSimilarIdeasChartData();

  if (loading) {
    return (
      <div className={`flex justify-center py-12 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
        <h2 className="text-xl font-bold">{t("dashboard.comparative.analytics", "Comparative Analytics")}</h2>
        <div className={`flex space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
          {analytics && (
            <>
              <button
                onClick={handleExportCSV}
                className={`px-3 py-1.5 bg-green-600 hover:bg-green-700 rounded-md text-white text-sm flex items-center transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <Download size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> CSV
              </button>
              <button
                onClick={handleExportPDF}
                className={`px-3 py-1.5 bg-red-600 hover:bg-red-700 rounded-md text-white text-sm flex items-center transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <Download size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> PDF
              </button>
            </>
          )}
          <button
            onClick={handleCalculateAnalytics}
            disabled={calculating}
            className={`px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white flex items-center transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <RefreshCw size={16} className={`mr-2 ${calculating ? 'animate-spin' : ''}`} />
            {calculating ? t("dashboard.calculating.analytics.recalculate", "Calculating...") : analytics ? 'Recalculate' : 'Calculate Analytics'}
          </button>
        </div>
      </div>

      {successMessage && (
        <div className={`bg-green-900/30 backdrop-blur-sm rounded-lg p-4 border border-green-800/50 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
          <CheckCircle size={20} className={`text-green-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
          <div className="text-green-400">{successMessage}</div>
        </div>
      )}

      {error && (
        <div className={`bg-red-900/30 backdrop-blur-sm rounded-lg p-4 border border-red-800/50 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
          <AlertCircle size={20} className={`text-red-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
          <div className="text-red-400">{error}</div>
        </div>
      )}

      {!analytics ? (
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50 text-center">
          <div className="text-gray-400 mb-4">{t("dashboard.no.analytics.data", "No analytics data available yet. Calculate analytics to benchmark your business idea against similar ideas.")}</div>
          <button
            onClick={handleCalculateAnalytics}
            disabled={calculating}
            className={`px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white flex items-center mx-auto transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <BarChart2 size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
            Calculate Analytics
          </button>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50">
              <div className={`flex items-center mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                <TrendingUp size={20} className={`mr-2 text-blue-400 ${isRTL ? "space-x-reverse" : ""}`} />
                <h3 className="font-semibold">{t("dashboard.progress.rate", "Progress Rate")}</h3>
              </div>
              <div className="text-2xl font-bold">{analytics.progress_rate.toFixed(2)}</div>
              <p className="text-sm text-gray-400">{t("dashboard.updates.per.month", "Updates per month")}</p>
            </div>

            <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50">
              <div className={`flex items-center mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                <CheckCircle size={20} className={`mr-2 text-green-400 ${isRTL ? "space-x-reverse" : ""}`} />
                <h3 className="font-semibold">{t("dashboard.milestone.completion", "Milestone Completion")}</h3>
              </div>
              <div className="text-2xl font-bold">{analytics.milestone_completion_rate.toFixed(1)}%</div>
              <p className="text-sm text-gray-400">{t("dashboard.completed.on.time", "Completed on time")}</p>
            </div>

            <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50">
              <div className={`flex items-center mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                <Users size={20} className={`mr-2 text-purple-400 ${isRTL ? "space-x-reverse" : ""}`} />
                <h3 className="font-semibold">{t("dashboard.team.size", "Team Size")}</h3>
              </div>
              <div className="text-2xl font-bold">{analytics.team_size}</div>
              <p className="text-sm text-gray-400">{t("dashboard.team.members", "Team members")}</p>
            </div>

            <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50">
              <div className={`flex items-center mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                <Award size={20} className={`mr-2 text-yellow-400 ${isRTL ? "space-x-reverse" : ""}`} />
                <h3 className="font-semibold">{t("dashboard.mentor.engagement", "Mentor Engagement")}</h3>
              </div>
              <div className="text-2xl font-bold">{analytics.mentor_engagement.toFixed(1)}%</div>
              <p className="text-sm text-gray-400">{t("dashboard.engagement.score", "Engagement score")}</p>
            </div>
          </div>

          {/* Percentile Rankings */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50">
              <h3 className="text-lg font-semibold mb-4">{t("dashboard.percentile.rankings", "Percentile Rankings")}</h3>
              <div className={`flex items-center justify-between mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                <span>{t("dashboard.industry.percentile", "Industry Percentile")}</span>
                <span className="font-bold">{analytics.industry_percentile.toFixed(1)}%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2 mb-4">
                <div
                  className="h-2 rounded-full bg-indigo-500"
                  style={{ width: `${analytics.industry_percentile}%` }}
                ></div>
              </div>

              <div className={`flex items-center justify-between mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                <span>{t("dashboard.stage.percentile", "Stage Percentile")}</span>
                <span className="font-bold">{analytics.stage_percentile.toFixed(1)}%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2 mb-4">
                <div
                  className="h-2 rounded-full bg-purple-500"
                  style={{ width: `${analytics.stage_percentile}%` }}
                ></div>
              </div>

              <div className="text-sm text-gray-400 mt-2">
                Percentile rankings show how your business idea compares to others in the same industry and stage.
              </div>
            </div>

            {percentileChartData && (
              <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50">
                <h3 className="text-lg font-semibold mb-4">{t("dashboard.percentile.comparison", "Percentile Comparison")}</h3>
                <div className="h-64">
                  <Bar
                    data={percentileChartData}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      scales: {
                        y: {
                          beginAtZero: true,
                          max: 100,
                          ticks: {
                            color: '#e2e8f0',
                          },
                          grid: {
                            color: 'rgba(226, 232, 240, 0.1)',
                          },
                        },
                        x: {
                          ticks: {
                            color: '#e2e8f0',
                          },
                          grid: {
                            color: 'rgba(226, 232, 240, 0.1)',
                          },
                        },
                      },
                      plugins: {
                        legend: {
                          display: false,
                        },
                      },
                    }}
                  />
                </div>
              </div>
            )}
          </div>

          {/* Performance Radar Chart */}
          {radarChartData && (
            <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50">
              <h3 className="text-lg font-semibold mb-4">{t("dashboard.performance.overview", "Performance Overview")}</h3>
              <div className="h-80">
                <Radar
                  data={radarChartData}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                      r: {
                        angleLines: {
                          color: 'rgba(226, 232, 240, 0.2)',
                        },
                        grid: {
                          color: 'rgba(226, 232, 240, 0.2)',
                        },
                        pointLabels: {
                          color: '#e2e8f0',
                          font: {
                            size: 12,
                          },
                        },
                        ticks: {
                          backdropColor: 'transparent',
                          color: '#e2e8f0',
                        },
                      },
                    },
                    plugins: {
                      legend: {
                        labels: {
                          color: '#e2e8f0',
                        },
                      },
                    },
                  }}
                />
              </div>
            </div>
          )}

          {/* Similar Ideas Comparison */}
          {similarIdeasChartData && (
            <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50">
              <h3 className="text-lg font-semibold mb-4">{t("dashboard.comparison.with.similar", "Comparison with Similar Ideas")}</h3>
              <div className="h-80">
                <Bar
                  data={similarIdeasChartData}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y' as const,
                    scales: {
                      y: {
                        ticks: {
                          color: '#e2e8f0',
                        },
                        grid: {
                          color: 'rgba(226, 232, 240, 0.1)',
                        },
                      },
                      x: {
                        beginAtZero: true,
                        ticks: {
                          color: '#e2e8f0',
                        },
                        grid: {
                          color: 'rgba(226, 232, 240, 0.1)',
                        },
                      },
                    },
                    plugins: {
                      legend: {
                        display: false,
                      },
                      tooltip: {
                        callbacks: {
                          title: (items) => {
                            return items[0].label;
                          },
                          label: (context) => {
                            return `Progress Updates: ${context.raw}`;
                          },
                        },
                      },
                    },
                  }}
                />
              </div>
              <div className="text-sm text-gray-400 mt-4">
                This chart compares your business idea's progress with similar ideas in the same industry or stage.
              </div>
            </div>
          )}

          {/* Last Updated */}
          <div className="text-sm text-gray-400 text-right">
            Last calculated: {new Date(analytics.last_calculated).toLocaleString()}
          </div>
        </div>
      )}
    </div>
  );
};

export default BusinessAnalyticsComponent;
