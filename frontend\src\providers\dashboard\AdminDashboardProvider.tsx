/**
 * Admin Dashboard Content Provider
 * Provides admin-specific dashboard data, stats, and actions
 */

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useAppSelector } from '../../store/hooks';
import { DashboardStat, DashboardQuickAction } from '../../types/dashboard';
import { adminAPI } from '../../services/api';
import {
  Users,
  Calendar,
  BookOpen,
  MessageSquare,
  BarChart3,
  Eye,
  Shield,
  Settings,
  TrendingUp,
  Activity
} from 'lucide-react';

interface AdminDashboardData {
  stats: DashboardStat[];
  quickActions: DashboardQuickAction[];
  systemHealth: any;
  recentActivity: any[];
  loading: boolean;
  error: string | null;
  refreshData: () => Promise<void>;
}

const AdminDashboardContext = createContext<AdminDashboardData | null>(null);

interface AdminDashboardProviderProps {
  children: React.ReactNode;
}

export const AdminDashboardProvider: React.FC<AdminDashboardProviderProps> = ({ children }) => {
  const { user } = useAppSelector(state => state.auth);
  const [stats, setStats] = useState<DashboardStat[]>([]);
  const [quickActions, setQuickActions] = useState<DashboardQuickAction[]>([]);
  const [systemHealth, setSystemHealth] = useState<any>(null);
  const [recentActivity, setRecentActivity] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch admin dashboard data
  const fetchDashboardData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // In a real implementation, these would be actual API calls
      // For now, we'll use mock data that simulates admin-level information
      
      // Mock admin stats
      const adminStats: DashboardStat[] = [
        {
          id: 'total_users',
          title: 'Total Users',
          value: 8567,
          icon: Users,
          color: 'bg-blue-600/30',
          change: 5.2,
          changeType: 'increase',
          description: 'Registered platform users'
        },
        {
          id: 'active_events',
          title: 'Active Events',
          value: 45,
          icon: Calendar,
          color: 'bg-purple-600/30',
          change: 12.0,
          changeType: 'increase',
          description: 'Currently running events'
        },
        {
          id: 'resources',
          title: 'Resources',
          value: 234,
          icon: BookOpen,
          color: 'bg-green-600/30',
          change: 3.1,
          changeType: 'increase',
          description: 'Available learning resources'
        },
        {
          id: 'community_posts',
          title: 'Community Posts',
          value: 1567,
          icon: MessageSquare,
          color: 'bg-orange-600/30',
          change: 8.7,
          changeType: 'increase',
          description: 'User-generated content'
        }
      ];

      // Mock admin quick actions
      const adminQuickActions: DashboardQuickAction[] = [
        {
          id: 'user_management',
          title: 'User Management',
          description: 'Manage users, roles, and permissions',
          icon: Users,
          href: '/admin/users',
          color: 'bg-blue-600/20 hover:bg-blue-600/30 border-blue-500/30',
        },
        {
          id: 'content_management',
          title: 'Content Management',
          description: 'Manage posts, events, and resources',
          icon: BookOpen,
          href: '/admin/content',
          color: 'bg-green-600/20 hover:bg-green-600/30 border-green-500/30',
        },
        {
          id: 'analytics',
          title: 'Analytics Dashboard',
          description: 'View platform analytics and reports',
          icon: BarChart3,
          href: '/admin/analytics',
          color: 'bg-purple-600/20 hover:bg-purple-600/30 border-purple-500/30',
        },
        {
          id: 'moderation',
          title: 'Content Moderation',
          description: 'Review and moderate user content',
          icon: Eye,
          href: '/admin/moderation',
          color: 'bg-orange-600/20 hover:bg-orange-600/30 border-orange-500/30',
          badge: 12, // Mock pending moderation count
        },
        {
          id: 'events_management',
          title: 'Events Management',
          description: 'Create and manage platform events',
          icon: Calendar,
          href: '/admin/events',
          color: 'bg-indigo-600/20 hover:bg-indigo-600/30 border-indigo-500/30',
        },
        {
          id: 'system_settings',
          title: 'System Settings',
          description: 'Configure platform settings',
          icon: Settings,
          href: '/admin/settings',
          color: 'bg-gray-600/20 hover:bg-gray-600/30 border-gray-500/30',
        }
      ];

      // Mock system health data
      const mockSystemHealth = {
        status: 'healthy',
        uptime: '99.8%',
        activeUsers: 1247,
        serverLoad: 'Normal',
        databaseStatus: 'Optimal'
      };

      // Mock recent activity
      const mockRecentActivity = [
        {
          id: '1',
          type: 'user_registration',
          description: 'New user registered: <EMAIL>',
          timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(), // 15 minutes ago
          severity: 'info'
        },
        {
          id: '2',
          type: 'content_flagged',
          description: 'Post flagged for review by community',
          timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
          severity: 'warning'
        },
        {
          id: '3',
          type: 'event_created',
          description: 'New event "Startup Pitch Night" created',
          timestamp: new Date(Date.now() - 1000 * 60 * 45).toISOString(), // 45 minutes ago
          severity: 'info'
        },
        {
          id: '4',
          type: 'system_update',
          description: 'System maintenance completed successfully',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
          severity: 'success'
        }
      ];

      setStats(adminStats);
      setQuickActions(adminQuickActions);
      setSystemHealth(mockSystemHealth);
      setRecentActivity(mockRecentActivity);

    } catch (err) {
      console.error('Error fetching admin dashboard data:', err);
      setError('Failed to load admin dashboard data');
      
      // Fallback to basic mock data
      const fallbackStats: DashboardStat[] = [
        {
          id: 'total_users',
          title: 'Total Users',
          value: 0,
          icon: Users,
          color: 'bg-blue-600/30',
          change: 0,
          changeType: 'neutral',
          description: 'Registered platform users'
        },
        {
          id: 'active_events',
          title: 'Active Events',
          value: 0,
          icon: Calendar,
          color: 'bg-purple-600/30',
          change: 0,
          changeType: 'neutral',
          description: 'Currently running events'
        }
      ];

      const fallbackQuickActions: DashboardQuickAction[] = [
        {
          id: 'user_management',
          title: 'User Management',
          description: 'Manage users, roles, and permissions',
          icon: Users,
          href: '/admin/users',
          color: 'bg-blue-600/20 hover:bg-blue-600/30 border-blue-500/30',
        },
        {
          id: 'content_management',
          title: 'Content Management',
          description: 'Manage posts, events, and resources',
          icon: BookOpen,
          href: '/admin/content',
          color: 'bg-green-600/20 hover:bg-green-600/30 border-green-500/30',
        }
      ];

      setStats(fallbackStats);
      setQuickActions(fallbackQuickActions);
      setSystemHealth({ status: 'unknown' });
      setRecentActivity([]);
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Initial data fetch
  useEffect(() => {
    if (user) {
      fetchDashboardData();
    }
  }, [user, fetchDashboardData]);

  const contextValue: AdminDashboardData = {
    stats,
    quickActions,
    systemHealth,
    recentActivity,
    loading,
    error,
    refreshData: fetchDashboardData,
  };

  return (
    <AdminDashboardContext.Provider value={contextValue}>
      {children}
    </AdminDashboardContext.Provider>
  );
};

// Hook to use admin dashboard data
export const useAdminDashboard = (): AdminDashboardData => {
  const context = useContext(AdminDashboardContext);
  if (!context) {
    throw new Error('useAdminDashboard must be used within an AdminDashboardProvider');
  }
  return context;
};

export default AdminDashboardProvider;
