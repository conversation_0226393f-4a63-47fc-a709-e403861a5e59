import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactNode } from 'react';
import {
  useBusinessPlansList,
  useBusinessPlan,
  useCreateBusinessPlan,
  useUpdateBusinessPlan,
  useDeleteBusinessPlan,
  useGenerateBusinessPlanFeedback,
  useDuplicateBusinessPlan
} from '../useBusinessPlans';

// Mock the API module
jest.mock('../../services/businessPlanApi', () => ({
  businessPlanApi: {
    getBusinessPlans: jest.fn(),
    getBusinessPlan: jest.fn(),
    createBusinessPlan: jest.fn(),
    updateBusinessPlan: jest.fn(),
    deleteBusinessPlan: jest.fn(),
    generateFeedback: jest.fn(),
    duplicatePlan: jest.fn(),
  }
}));

// Mock data
const mockBusinessPlan = {
  id: 1,
  title: 'Test Business Plan',
  business_idea: 1,
  business_idea_title: 'Test Business Idea',
  status: 'draft',
  content: {
    executive_summary: 'Test summary',
    market_analysis: 'Test analysis'
  },
  owner_details: {
    first_name: '<PERSON>',
    last_name: 'Doe'
  },
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
};

const mockBusinessPlansList = {
  results: [mockBusinessPlan],
  count: 1,
  next: null,
  previous: null
};

// Test wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
      mutations: {
        retry: false,
      },
    },
  });

  return ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

describe('useBusinessPlans hooks', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('useBusinessPlansList', () => {
    it('should fetch business plans list successfully', async () => {
      const mockApi = require('../../services/businessPlanApi').businessPlanApi;
      mockApi.getBusinessPlans.mockResolvedValue(mockBusinessPlansList);

      const { result } = renderHook(() => useBusinessPlansList(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(mockBusinessPlansList);
      expect(mockApi.getBusinessPlans).toHaveBeenCalledTimes(1);
    });

    it('should handle filters in business plans list', async () => {
      const mockApi = require('../../services/businessPlanApi').businessPlanApi;
      mockApi.getBusinessPlans.mockResolvedValue(mockBusinessPlansList);

      const filters = { status: 'draft', search: 'test' };
      const { result } = renderHook(() => useBusinessPlansList(filters), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockApi.getBusinessPlans).toHaveBeenCalledWith(filters);
    });

    it('should handle error when fetching business plans list fails', async () => {
      const mockApi = require('../../services/businessPlanApi').businessPlanApi;
      const errorMessage = 'Failed to fetch business plans';
      mockApi.getBusinessPlans.mockRejectedValue(new Error(errorMessage));

      const { result } = renderHook(() => useBusinessPlansList(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error).toBeInstanceOf(Error);
    });
  });

  describe('useBusinessPlan', () => {
    it('should fetch single business plan successfully', async () => {
      const mockApi = require('../../services/businessPlanApi').businessPlanApi;
      mockApi.getBusinessPlan.mockResolvedValue(mockBusinessPlan);

      const { result } = renderHook(() => useBusinessPlan(1), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(mockBusinessPlan);
      expect(mockApi.getBusinessPlan).toHaveBeenCalledWith(1);
    });
  });

  describe('useCreateBusinessPlan', () => {
    it('should create business plan successfully', async () => {
      const mockApi = require('../../services/businessPlanApi').businessPlanApi;
      mockApi.createBusinessPlan.mockResolvedValue(mockBusinessPlan);

      const { result } = renderHook(() => useCreateBusinessPlan(), {
        wrapper: createWrapper(),
      });

      const newPlanData = {
        title: 'New Business Plan',
        business_idea: 1,
        status: 'draft'
      };

      await waitFor(() => {
        result.current.mutate(newPlanData);
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockApi.createBusinessPlan).toHaveBeenCalledWith(newPlanData);
    });

    it('should handle error when creating business plan fails', async () => {
      const mockApi = require('../../services/businessPlanApi').businessPlanApi;
      const errorMessage = 'Failed to create business plan';
      mockApi.createBusinessPlan.mockRejectedValue(new Error(errorMessage));

      const { result } = renderHook(() => useCreateBusinessPlan(), {
        wrapper: createWrapper(),
      });

      const newPlanData = {
        title: 'New Business Plan',
        business_idea: 1,
        status: 'draft'
      };

      await waitFor(() => {
        result.current.mutate(newPlanData);
      });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error).toBeInstanceOf(Error);
    });
  });

  describe('useUpdateBusinessPlan', () => {
    it('should update business plan successfully', async () => {
      const mockApi = require('../../services/businessPlanApi').businessPlanApi;
      const updatedPlan = { ...mockBusinessPlan, title: 'Updated Plan' };
      mockApi.updateBusinessPlan.mockResolvedValue(updatedPlan);

      const { result } = renderHook(() => useUpdateBusinessPlan(), {
        wrapper: createWrapper(),
      });

      const updateData = {
        id: 1,
        data: { title: 'Updated Plan' }
      };

      await waitFor(() => {
        result.current.mutate(updateData);
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockApi.updateBusinessPlan).toHaveBeenCalledWith(1, { title: 'Updated Plan' });
    });
  });

  describe('useDeleteBusinessPlan', () => {
    it('should delete business plan successfully', async () => {
      const mockApi = require('../../services/businessPlanApi').businessPlanApi;
      mockApi.deleteBusinessPlan.mockResolvedValue(undefined);

      const { result } = renderHook(() => useDeleteBusinessPlan(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        result.current.mutate(1);
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockApi.deleteBusinessPlan).toHaveBeenCalledWith(1);
    });
  });

  describe('useGenerateBusinessPlanFeedback', () => {
    it('should generate AI feedback successfully', async () => {
      const mockApi = require('../../services/businessPlanApi').businessPlanApi;
      const planWithFeedback = { ...mockBusinessPlan, ai_feedback: 'Generated feedback' };
      mockApi.generateFeedback.mockResolvedValue(planWithFeedback);

      const { result } = renderHook(() => useGenerateBusinessPlanFeedback(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        result.current.mutate({ business_plan_id: 1 });
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockApi.generateFeedback).toHaveBeenCalledWith({ business_plan_id: 1 });
    });
  });

  describe('useDuplicateBusinessPlan', () => {
    it('should duplicate business plan successfully', async () => {
      const mockApi = require('../../services/businessPlanApi').businessPlanApi;
      const duplicatedPlan = { ...mockBusinessPlan, id: 2, title: 'Copy of Test Business Plan' };
      mockApi.duplicatePlan.mockResolvedValue(duplicatedPlan);

      const { result } = renderHook(() => useDuplicateBusinessPlan(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        result.current.mutate({ business_plan_id: 1 });
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockApi.duplicatePlan).toHaveBeenCalledWith({ business_plan_id: 1 });
    });
  });
});

// Integration tests
describe('Business Plans CRUD Integration', () => {
  it('should handle complete CRUD workflow', async () => {
    const mockApi = require('../../services/businessPlanApi').businessPlanApi;
    
    // Setup mocks for complete workflow
    mockApi.createBusinessPlan.mockResolvedValue(mockBusinessPlan);
    mockApi.getBusinessPlan.mockResolvedValue(mockBusinessPlan);
    mockApi.updateBusinessPlan.mockResolvedValue({ ...mockBusinessPlan, title: 'Updated' });
    mockApi.deleteBusinessPlan.mockResolvedValue(undefined);

    const wrapper = createWrapper();

    // Test create
    const { result: createResult } = renderHook(() => useCreateBusinessPlan(), { wrapper });
    await waitFor(() => {
      createResult.current.mutate({
        title: 'Test Plan',
        business_idea: 1,
        status: 'draft'
      });
    });

    // Test read
    const { result: readResult } = renderHook(() => useBusinessPlan(1), { wrapper });
    await waitFor(() => {
      expect(readResult.current.isSuccess).toBe(true);
    });

    // Test update
    const { result: updateResult } = renderHook(() => useUpdateBusinessPlan(), { wrapper });
    await waitFor(() => {
      updateResult.current.mutate({
        id: 1,
        data: { title: 'Updated' }
      });
    });

    // Test delete
    const { result: deleteResult } = renderHook(() => useDeleteBusinessPlan(), { wrapper });
    await waitFor(() => {
      deleteResult.current.mutate(1);
    });

    await waitFor(() => {
      expect(deleteResult.current.isSuccess).toBe(true);
    });

    // Verify all operations were called
    expect(mockApi.createBusinessPlan).toHaveBeenCalled();
    expect(mockApi.getBusinessPlan).toHaveBeenCalled();
    expect(mockApi.updateBusinessPlan).toHaveBeenCalled();
    expect(mockApi.deleteBusinessPlan).toHaveBeenCalled();
  });
});
