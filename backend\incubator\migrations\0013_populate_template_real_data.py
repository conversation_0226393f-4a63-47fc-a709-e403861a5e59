# Data migration to populate template fields with real data

from django.db import migrations
from django.core.management import call_command


def populate_template_data(apps, schema_editor):
    """Populate template fields with real data"""
    try:
        call_command('populate_template_data', '--update-existing')
    except Exception as e:
        print(f"Warning: Could not populate template data: {e}")
        # Don't fail the migration if this fails


def reverse_populate_template_data(apps, schema_editor):
    """Reverse operation - reset fields to defaults"""
    BusinessPlanTemplate = apps.get_model('incubator', 'BusinessPlanTemplate')
    
    # Reset analytics fields to defaults
    BusinessPlanTemplate.objects.all().update(
        usage_count=0,
        rating=0.0,
        rating_count=0,
        completion_rate=0.0,
        is_bestseller=False
    )


class Migration(migrations.Migration):

    dependencies = [
        ('incubator', '0012_add_template_analytics_fields'),
    ]

    operations = [
        migrations.RunPython(
            populate_template_data,
            reverse_populate_template_data,
        ),
    ]
