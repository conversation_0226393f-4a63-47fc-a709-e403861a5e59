import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  HelpCircle, 
  Search, 
  Book, 
  MessageCircle, 
  Mail, 
  Phone,
  ChevronDown,
  ChevronRight,
  ExternalLink,
  Video,
  FileText,
  Users,
  Lightbulb,
  Settings,
  Shield
} from 'lucide-react';

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
}

interface HelpCategory {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  articles: number;
}

const HelpSupportPage: React.FC = () => {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  const helpCategories: HelpCategory[] = [
    {
      id: 'getting-started',
      title: t('Getting Started'),
      description: t('Learn the basics of using our platform'),
      icon: <Lightbulb className="h-6 w-6" />,
      articles: 12
    },
    {
      id: 'business-plans',
      title: t('Business Plans'),
      description: t('Create and manage your business plans'),
      icon: <FileText className="h-6 w-6" />,
      articles: 8
    },
    {
      id: 'mentorship',
      title: t('Mentorship'),
      description: t('Connect with mentors and manage sessions'),
      icon: <Users className="h-6 w-6" />,
      articles: 15
    },
    {
      id: 'funding',
      title: t('Funding & Investment'),
      description: t('Find investors and manage funding'),
      icon: <Book className="h-6 w-6" />,
      articles: 10
    },
    {
      id: 'account',
      title: t('Account & Settings'),
      description: t('Manage your account and preferences'),
      icon: <Settings className="h-6 w-6" />,
      articles: 6
    },
    {
      id: 'security',
      title: t('Security & Privacy'),
      description: t('Keep your account safe and secure'),
      icon: <Shield className="h-6 w-6" />,
      articles: 5
    }
  ];

  const faqItems: FAQItem[] = [
    {
      id: '1',
      question: t('How do I create my first business plan?'),
      answer: t('To create your first business plan, navigate to the Dashboard and click on "Business Plans". Then click "Create New Plan" and follow the step-by-step wizard. You can also use our AI-powered generator for assistance.'),
      category: 'business-plans'
    },
    {
      id: '2',
      question: t('How do I find a mentor?'),
      answer: t('Go to the Mentorship section in your dashboard. Browse available mentors by expertise, industry, or rating. Click on a mentor\'s profile to view their background and request a session.'),
      category: 'mentorship'
    },
    {
      id: '3',
      question: t('What types of funding opportunities are available?'),
      answer: t('Our platform offers various funding opportunities including angel investment, venture capital, grants, and crowdfunding. Visit the Funding section to explore current opportunities that match your business stage and industry.'),
      category: 'funding'
    },
    {
      id: '4',
      question: t('How do I update my profile information?'),
      answer: t('Click on your profile picture in the top right corner and select "Profile Settings". From there, you can update your personal information, bio, expertise areas, and profile picture.'),
      category: 'account'
    },
    {
      id: '5',
      question: t('Is my data secure on this platform?'),
      answer: t('Yes, we take security seriously. All data is encrypted in transit and at rest. We use industry-standard security measures and comply with data protection regulations. You can review our privacy policy for more details.'),
      category: 'security'
    },
    {
      id: '6',
      question: t('How do I get started as a new user?'),
      answer: t('Welcome! Start by completing your profile, then explore the Dashboard to familiarize yourself with available features. Consider creating your first business idea or browsing mentors to get started.'),
      category: 'getting-started'
    }
  ];

  const filteredFAQs = faqItems.filter(faq => 
    (selectedCategory ? faq.category === selectedCategory : true) &&
    (searchQuery ? 
      faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
      : true
    )
  );

  const toggleFAQ = (id: string) => {
    setExpandedFAQ(expandedFAQ === id ? null : id);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <div className="bg-white/70 backdrop-blur-md border-b border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 text-center">
          <div className="flex items-center justify-center space-x-3 mb-4">
            <div className="p-3 bg-blue-100 rounded-lg">
              <HelpCircle className="h-8 w-8 text-blue-600" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-4">{t('Help & Support Center')}</h1>
          <p className="text-lg text-gray-600 mb-8">{t('Find answers to your questions and get the help you need')}</p>
          
          {/* Search Bar */}
          <div className="max-w-2xl mx-auto relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder={t('Search for help articles, FAQs, and guides...')}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          <div className="bg-white/70 backdrop-blur-md rounded-xl border border-white/20 p-6 text-center hover:shadow-lg transition-shadow cursor-pointer">
            <MessageCircle className="h-12 w-12 text-blue-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">{t('Live Chat')}</h3>
            <p className="text-gray-600 mb-4">{t('Get instant help from our support team')}</p>
            <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
              {t('Start Chat')}
            </button>
          </div>

          <div className="bg-white/70 backdrop-blur-md rounded-xl border border-white/20 p-6 text-center hover:shadow-lg transition-shadow cursor-pointer">
            <Mail className="h-12 w-12 text-green-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">{t('Email Support')}</h3>
            <p className="text-gray-600 mb-4">{t('Send us a detailed message about your issue')}</p>
            <button className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors">
              {t('Send Email')}
            </button>
          </div>

          <div className="bg-white/70 backdrop-blur-md rounded-xl border border-white/20 p-6 text-center hover:shadow-lg transition-shadow cursor-pointer">
            <Video className="h-12 w-12 text-purple-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">{t('Video Tutorials')}</h3>
            <p className="text-gray-600 mb-4">{t('Watch step-by-step guides and tutorials')}</p>
            <button className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors">
              {t('Watch Videos')}
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Help Categories */}
          <div className="lg:col-span-1">
            <div className="bg-white/70 backdrop-blur-md rounded-xl border border-white/20 overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">{t('Help Categories')}</h2>
              </div>
              <div className="p-6">
                <div className="space-y-3">
                  <button
                    onClick={() => setSelectedCategory(null)}
                    className={`w-full text-left px-3 py-2 rounded-md transition-colors ${
                      selectedCategory === null ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-50'
                    }`}
                  >
                    {t('All Categories')}
                  </button>
                  {helpCategories.map((category) => (
                    <button
                      key={category.id}
                      onClick={() => setSelectedCategory(category.id)}
                      className={`w-full text-left px-3 py-2 rounded-md transition-colors ${
                        selectedCategory === category.id ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-50'
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <div className="text-gray-500">{category.icon}</div>
                        <div className="flex-1">
                          <div className="font-medium">{category.title}</div>
                          <div className="text-sm text-gray-500">{category.articles} {t('articles')}</div>
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="lg:col-span-2">
            <div className="bg-white/70 backdrop-blur-md rounded-xl border border-white/20 overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">
                  {selectedCategory 
                    ? helpCategories.find(c => c.id === selectedCategory)?.title || t('Frequently Asked Questions')
                    : t('Frequently Asked Questions')
                  }
                </h2>
              </div>
              <div className="p-6">
                {filteredFAQs.length === 0 ? (
                  <div className="text-center py-8">
                    <HelpCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">{t('No results found')}</h3>
                    <p className="text-gray-600">{t('Try adjusting your search or browse different categories')}</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {filteredFAQs.map((faq) => (
                      <div key={faq.id} className="border border-gray-200 rounded-lg overflow-hidden">
                        <button
                          onClick={() => toggleFAQ(faq.id)}
                          className="w-full px-4 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
                        >
                          <span className="font-medium text-gray-900">{faq.question}</span>
                          {expandedFAQ === faq.id ? (
                            <ChevronDown className="h-5 w-5 text-gray-500" />
                          ) : (
                            <ChevronRight className="h-5 w-5 text-gray-500" />
                          )}
                        </button>
                        {expandedFAQ === faq.id && (
                          <div className="px-4 pb-4 text-gray-600">
                            {faq.answer}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Contact Information */}
            <div className="mt-8 bg-white/70 backdrop-blur-md rounded-xl border border-white/20 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">{t('Still need help?')}</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-3">
                  <Mail className="h-5 w-5 text-gray-500" />
                  <div>
                    <div className="font-medium text-gray-900">{t('Email Support')}</div>
                    <div className="text-sm text-gray-600"><EMAIL></div>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Phone className="h-5 w-5 text-gray-500" />
                  <div>
                    <div className="font-medium text-gray-900">{t('Phone Support')}</div>
                    <div className="text-sm text-gray-600">+****************</div>
                  </div>
                </div>
              </div>
              <div className="mt-4 pt-4 border-t border-gray-200">
                <p className="text-sm text-gray-600">
                  {t('Our support team is available Monday through Friday, 9 AM to 6 PM EST. We typically respond to emails within 24 hours.')}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HelpSupportPage;
