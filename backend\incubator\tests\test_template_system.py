"""
Comprehensive Test Suite for Template System
Tests all template functionality including AI generation, analytics, and collaboration
"""

import json
from datetime import datetime, timedelta
from django.test import TestCase, TransactionTestCase
from django.contrib.auth.models import User
from django.utils import timezone
from unittest.mock import patch, MagicMock

from ..models_business_plan import BusinessPlanTemplate, TemplateSectionDefinition
from ..models_template_analytics import (
    TemplateUsageAnalytics, TemplatePerformanceMetrics, 
    TemplateSectionAnalytics, UserTemplateInteraction
)
from ..models_collaborative_templates import (
    CollaborativeTemplate, TemplateCollaborator, TemplateVersion,
    TemplateComment, TemplateChangeLog
)
from ..ai_template_generator import AITemplateGenerator
from ..template_definitions import get_template_definition, TEMPLATE_REGISTRY


class TemplateDefinitionTests(TestCase):
    """Test template definitions and registry"""
    
    def test_template_registry_completeness(self):
        """Test that all templates in registry are properly defined"""
        for template_type, template_def in TEMPLATE_REGISTRY.items():
            self.assertIn('name', template_def)
            self.assertIn('description', template_def)
            self.assertIn('template_type', template_def)
            self.assertIn('sections', template_def)
            self.assertEqual(template_def['template_type'], template_type)
    
    def test_template_sections_structure(self):
        """Test that template sections have required fields"""
        for template_type, template_def in TEMPLATE_REGISTRY.items():
            sections = template_def['sections']
            self.assertIsInstance(sections, dict)
            
            for section_key, section_data in sections.items():
                required_fields = ['title', 'description', 'section_type', 'order', 'required']
                for field in required_fields:
                    self.assertIn(field, section_data, 
                                f"Missing {field} in {section_key} of {template_type}")
    
    def test_get_template_definition(self):
        """Test template definition retrieval"""
        # Test existing template
        saas_template = get_template_definition('saas')
        self.assertEqual(saas_template['template_type'], 'saas')
        
        # Test non-existing template (should return standard)
        unknown_template = get_template_definition('unknown_type')
        self.assertEqual(unknown_template['template_type'], 'standard')


class AITemplateGeneratorTests(TestCase):
    """Test AI-powered template generation"""
    
    def setUp(self):
        self.generator = AITemplateGenerator()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_generate_custom_template_success(self):
        """Test successful custom template generation"""
        result = self.generator.generate_custom_template(
            business_description="AI-powered fitness app for personal trainers",
            industry="Technology",
            business_type="mobile app",
            target_market="Personal trainers and fitness enthusiasts",
            funding_stage="startup",
            special_requirements=["sustainability"],
            user_id=self.user.id
        )
        
        self.assertTrue(result['success'])
        self.assertIn('template', result)
        self.assertIn('analysis', result)
        self.assertIn('recommendations', result)
        
        template = result['template']
        self.assertEqual(template['template_type'], 'custom')
        self.assertTrue(template['is_ai_generated'])
        self.assertIn('sections', template)
    
    def test_analyze_business_requirements(self):
        """Test business requirements analysis"""
        analysis = self.generator._analyze_business_requirements(
            "Healthcare SaaS platform for patient management",
            "Healthcare",
            "saas",
            "Medical practices",
            "growth"
        )
        
        self.assertEqual(analysis['industry'], "Healthcare")
        self.assertEqual(analysis['business_type'], "saas")
        self.assertIn('complexity_level', analysis)
        self.assertIn('required_sections', analysis)
        self.assertIn('regulatory_requirements', analysis)
    
    def test_select_base_template(self):
        """Test base template selection logic"""
        # Test SaaS selection
        analysis = {'business_type': 'saas software', 'industry': 'Technology'}
        template = self.generator._select_base_template(analysis)
        self.assertEqual(template['template_type'], 'saas')
        
        # Test healthcare selection
        analysis = {'business_type': 'medical practice', 'industry': 'Healthcare'}
        template = self.generator._select_base_template(analysis)
        self.assertEqual(template['template_type'], 'healthcare')
    
    def test_generate_custom_sections(self):
        """Test custom section generation"""
        analysis = {
            'industry': 'Technology',
            'business_type': 'saas',
            'funding_stage': 'growth'
        }
        
        custom_sections = self.generator._generate_custom_sections(
            analysis, ['international', 'sustainability']
        )
        
        # Should include scaling strategy for growth stage
        self.assertIn('scaling_strategy', custom_sections)
        
        # Should include international expansion
        self.assertIn('international_expansion', custom_sections)
        
        # Should include sustainability plan
        self.assertIn('sustainability_plan', custom_sections)


class TemplateAnalyticsTests(TestCase):
    """Test template analytics and tracking"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.template = BusinessPlanTemplate.objects.create(
            name="Test Template",
            description="Test template for analytics",
            template_type="standard",
            industry="Technology",
            sections={"test_section": {"title": "Test Section"}},
            is_system=True
        )
    
    def test_usage_analytics_creation(self):
        """Test creation of usage analytics"""
        analytics = TemplateUsageAnalytics.objects.create(
            template=self.template,
            user=self.user,
            sections_completed=5,
            total_sections=10,
            completion_percentage=50.0,
            rating=4
        )
        
        self.assertEqual(analytics.template, self.template)
        self.assertEqual(analytics.user, self.user)
        self.assertEqual(analytics.completion_percentage, 50.0)
        self.assertFalse(analytics.is_completed)
    
    def test_performance_metrics_calculation(self):
        """Test performance metrics calculation"""
        # Create performance metrics
        metrics = TemplatePerformanceMetrics.objects.create(template=self.template)
        
        # Create some usage analytics
        for i in range(10):
            TemplateUsageAnalytics.objects.create(
                template=self.template,
                user=self.user,
                sections_completed=8 if i < 5 else 10,
                total_sections=10,
                completion_percentage=80.0 if i < 5 else 100.0,
                rating=4 if i < 7 else 5,
                selected_at=timezone.now(),
                completed_at=timezone.now() if i >= 3 else None
            )
        
        # Update metrics
        metrics.update_metrics()
        
        self.assertEqual(metrics.total_views, 10)
        self.assertEqual(metrics.total_selections, 10)
        self.assertEqual(metrics.total_completions, 7)  # Only 7 have completed_at
        self.assertEqual(metrics.selection_rate, 100.0)
        self.assertGreater(metrics.completion_rate, 0)
        self.assertGreater(metrics.average_rating, 0)
    
    def test_user_interaction_tracking(self):
        """Test user interaction tracking"""
        interaction = UserTemplateInteraction.objects.create(
            user=self.user,
            template=self.template,
            session_id="test_session_123",
            action_type="view",
            section_key="executive_summary",
            action_data={"page_url": "/templates/1"}
        )
        
        self.assertEqual(interaction.user, self.user)
        self.assertEqual(interaction.action_type, "view")
        self.assertEqual(interaction.section_key, "executive_summary")


class CollaborativeTemplateTests(TestCase):
    """Test collaborative template features"""
    
    def setUp(self):
        self.owner = User.objects.create_user(
            username='owner',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.collaborator = User.objects.create_user(
            username='collaborator',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.template = BusinessPlanTemplate.objects.create(
            name="Collaborative Template",
            description="Test collaborative template",
            template_type="standard",
            industry="Technology",
            sections={"test_section": {"title": "Test Section"}},
            is_system=False
        )
        
        self.collaborative_template = CollaborativeTemplate.objects.create(
            base_template=self.template,
            is_collaborative=True,
            collaboration_mode='invite_only',
            allow_comments=True
        )
    
    def test_collaborator_creation(self):
        """Test adding collaborators to template"""
        collaborator = TemplateCollaborator.objects.create(
            template=self.collaborative_template,
            user=self.collaborator,
            role='editor',
            can_edit_sections=True,
            invited_by=self.owner
        )
        
        self.assertEqual(collaborator.template, self.collaborative_template)
        self.assertEqual(collaborator.user, self.collaborator)
        self.assertEqual(collaborator.role, 'editor')
        self.assertTrue(collaborator.can_edit_sections)
        self.assertFalse(collaborator.has_accepted_invitation)
    
    def test_template_versioning(self):
        """Test template version control"""
        version = TemplateVersion.objects.create(
            template=self.collaborative_template,
            version_number="1.0",
            version_name="Initial Version",
            description="First version of the template",
            template_data={"name": "Test Template"},
            sections_data={"test_section": {"title": "Test Section"}},
            created_by=self.owner,
            is_current=True
        )
        
        self.assertEqual(version.template, self.collaborative_template)
        self.assertEqual(version.version_number, "1.0")
        self.assertEqual(version.created_by, self.owner)
        self.assertTrue(version.is_current)
    
    def test_template_comments(self):
        """Test commenting system"""
        comment = TemplateComment.objects.create(
            template=self.collaborative_template,
            section_key="executive_summary",
            author=self.collaborator,
            content="This section needs more detail about the market analysis.",
            comment_type="suggestion"
        )
        
        self.assertEqual(comment.template, self.collaborative_template)
        self.assertEqual(comment.section_key, "executive_summary")
        self.assertEqual(comment.author, self.collaborator)
        self.assertEqual(comment.comment_type, "suggestion")
        self.assertFalse(comment.is_resolved)
    
    def test_change_logging(self):
        """Test change log tracking"""
        change_log = TemplateChangeLog.objects.create(
            template=self.collaborative_template,
            changed_by=self.owner,
            change_type="section_edited",
            section_key="executive_summary",
            old_value={"content": "Old content"},
            new_value={"content": "New content"},
            change_description="Updated executive summary content"
        )
        
        self.assertEqual(change_log.template, self.collaborative_template)
        self.assertEqual(change_log.changed_by, self.owner)
        self.assertEqual(change_log.change_type, "section_edited")


class TemplateIntegrationTests(TransactionTestCase):
    """Integration tests for the complete template system"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_complete_template_workflow(self):
        """Test complete template workflow from creation to completion"""
        # 1. Generate AI template
        generator = AITemplateGenerator()
        result = generator.generate_custom_template(
            business_description="E-commerce platform for handmade crafts",
            industry="E-commerce",
            business_type="ecommerce",
            target_market="Craft makers and buyers",
            funding_stage="startup"
        )
        
        self.assertTrue(result['success'])
        
        # 2. Create template in database
        template_data = result['template']
        template = BusinessPlanTemplate.objects.create(
            name=template_data['name'],
            description=template_data['description'],
            template_type=template_data['template_type'],
            industry="E-commerce",
            sections=template_data['sections'],
            is_system=False
        )
        
        # 3. Track usage analytics
        analytics = TemplateUsageAnalytics.objects.create(
            template=template,
            user=self.user,
            selected_at=timezone.now()
        )
        
        # 4. Simulate user working on template
        analytics.started_at = timezone.now()
        analytics.sections_completed = 3
        analytics.total_sections = len(template_data['sections'])
        analytics.completion_percentage = (3 / len(template_data['sections'])) * 100
        analytics.save()
        
        # 5. Add user interactions
        UserTemplateInteraction.objects.create(
            user=self.user,
            template=template,
            session_id="test_session",
            action_type="start",
            timestamp=timezone.now()
        )
        
        # 6. Complete template
        analytics.completed_at = timezone.now()
        analytics.completion_percentage = 100.0
        analytics.rating = 5
        analytics.save()
        
        # Verify the workflow
        self.assertIsNotNone(analytics.selected_at)
        self.assertIsNotNone(analytics.started_at)
        self.assertIsNotNone(analytics.completed_at)
        self.assertEqual(analytics.completion_percentage, 100.0)
        self.assertEqual(analytics.rating, 5)
        self.assertTrue(analytics.is_completed)
    
    def test_collaborative_template_workflow(self):
        """Test collaborative template workflow"""
        # 1. Create base template
        template = BusinessPlanTemplate.objects.create(
            name="Collaborative Test Template",
            description="Test template for collaboration",
            template_type="standard",
            industry="Technology",
            sections={"section1": {"title": "Section 1"}},
            is_system=False
        )
        
        # 2. Enable collaboration
        collaborative_template = CollaborativeTemplate.objects.create(
            base_template=template,
            is_collaborative=True,
            collaboration_mode='invite_only'
        )
        
        # 3. Add collaborators
        owner = TemplateCollaborator.objects.create(
            template=collaborative_template,
            user=self.user,
            role='owner',
            can_edit_sections=True,
            can_invite_others=True
        )
        
        # 4. Create version
        version = TemplateVersion.objects.create(
            template=collaborative_template,
            version_number="1.0",
            template_data={"name": template.name},
            sections_data=template.sections,
            created_by=self.user,
            is_current=True
        )
        
        # 5. Add comment
        comment = TemplateComment.objects.create(
            template=collaborative_template,
            section_key="section1",
            author=self.user,
            content="This section looks good!",
            comment_type="general"
        )
        
        # 6. Log change
        change_log = TemplateChangeLog.objects.create(
            template=collaborative_template,
            changed_by=self.user,
            change_type="comment_added",
            change_description="Added comment to section1"
        )
        
        # Verify collaborative workflow
        self.assertTrue(collaborative_template.is_collaborative)
        self.assertEqual(collaborative_template.collaborators.count(), 1)
        self.assertEqual(collaborative_template.versions.count(), 1)
        self.assertEqual(collaborative_template.comments.count(), 1)
        self.assertEqual(collaborative_template.change_logs.count(), 1)


class TemplatePerformanceTests(TestCase):
    """Test template system performance and optimization"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_bulk_template_creation(self):
        """Test creating multiple templates efficiently"""
        templates_data = []
        for i in range(100):
            templates_data.append(BusinessPlanTemplate(
                name=f"Template {i}",
                description=f"Test template {i}",
                template_type="standard",
                industry="Technology",
                sections={"section1": {"title": f"Section {i}"}},
                is_system=True
            ))
        
        # Bulk create
        templates = BusinessPlanTemplate.objects.bulk_create(templates_data)
        
        self.assertEqual(len(templates), 100)
        self.assertEqual(BusinessPlanTemplate.objects.count(), 100)
    
    def test_analytics_query_optimization(self):
        """Test optimized queries for analytics"""
        # Create test data
        template = BusinessPlanTemplate.objects.create(
            name="Performance Test Template",
            description="Template for performance testing",
            template_type="standard",
            industry="Technology",
            sections={"section1": {"title": "Section 1"}},
            is_system=True
        )
        
        # Create analytics data
        analytics_data = []
        for i in range(1000):
            analytics_data.append(TemplateUsageAnalytics(
                template=template,
                user=self.user,
                completion_percentage=i % 100,
                rating=(i % 5) + 1
            ))
        
        TemplateUsageAnalytics.objects.bulk_create(analytics_data)
        
        # Test optimized queries
        with self.assertNumQueries(1):
            # Should use a single query with aggregation
            metrics = TemplateUsageAnalytics.objects.filter(
                template=template
            ).aggregate(
                avg_completion=models.Avg('completion_percentage'),
                avg_rating=models.Avg('rating'),
                total_count=models.Count('id')
            )
        
        self.assertIsNotNone(metrics['avg_completion'])
        self.assertIsNotNone(metrics['avg_rating'])
        self.assertEqual(metrics['total_count'], 1000)
