from django.core.files.storage import FileSystemStorage
from django.conf import settings
from PIL import Image, ExifTags
import os
import io
from pathlib import Path

class OptimizedImageStorage(FileSystemStorage):
    """
    Custom storage backend that optimizes images before saving them.
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
    def _save(self, name, content):
        # Check if the file is an image
        if self._is_image(name):
            # Optimize the image
            optimized_content = self._optimize_image(content)
            # Save the optimized image
            return super()._save(name, optimized_content)
        
        # If not an image, save as is
        return super()._save(name, content)
    
    def _is_image(self, name):
        """Check if the file is an image based on its extension."""
        ext = Path(name).suffix.lower()
        return ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp']
    
    def _optimize_image(self, content):
        """Optimize the image by resizing and compressing it."""
        # Maximum dimensions
        MAX_WIDTH = 1200
        MAX_HEIGHT = 1200
        # JPEG quality (1-100)
        QUALITY = 85
        
        # Read the image
        img = Image.open(content)
        
        # Handle EXIF orientation
        try:
            for orientation in ExifTags.TAGS.keys():
                if ExifTags.TAGS[orientation] == 'Orientation':
                    break
            
            exif = dict(img._getexif().items())
            
            if exif[orientation] == 2:
                img = img.transpose(Image.FLIP_LEFT_RIGHT)
            elif exif[orientation] == 3:
                img = img.rotate(180)
            elif exif[orientation] == 4:
                img = img.transpose(Image.FLIP_TOP_BOTTOM)
            elif exif[orientation] == 5:
                img = img.transpose(Image.FLIP_LEFT_RIGHT).rotate(90)
            elif exif[orientation] == 6:
                img = img.rotate(270)
            elif exif[orientation] == 7:
                img = img.transpose(Image.FLIP_LEFT_RIGHT).rotate(270)
            elif exif[orientation] == 8:
                img = img.rotate(90)
        except (AttributeError, KeyError, IndexError):
            # No EXIF data or no orientation tag
            pass
        
        # Resize if needed
        if img.width > MAX_WIDTH or img.height > MAX_HEIGHT:
            img.thumbnail((MAX_WIDTH, MAX_HEIGHT), Image.LANCZOS)
        
        # Convert to RGB if needed (for saving as JPEG)
        if img.mode != 'RGB' and content.name.lower().endswith(('.jpg', '.jpeg')):
            img = img.convert('RGB')
        
        # Save to a BytesIO object
        output = io.BytesIO()
        
        # Determine format based on original file extension
        format = Path(content.name).suffix.lower().lstrip('.')
        if format in ('jpg', 'jpeg'):
            img.save(output, format='JPEG', quality=QUALITY, optimize=True)
        elif format == 'png':
            img.save(output, format='PNG', optimize=True)
        elif format == 'gif':
            img.save(output, format='GIF')
        elif format == 'webp':
            img.save(output, format='WEBP', quality=QUALITY)
        else:
            # Fallback to JPEG
            img.save(output, format='JPEG', quality=QUALITY, optimize=True)
        
        # Reset file pointer
        output.seek(0)
        
        # Return a ContentFile
        from django.core.files.base import ContentFile
        return ContentFile(output.getvalue())
