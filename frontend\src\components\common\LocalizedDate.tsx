import React from 'react';
import { formatDate, formatTime, formatDateTime, formatRelativeTime } from '../../utils/dateTimeFormatter';

interface LocalizedDateProps {
  date: string | Date | number;
  format?: 'date' | 'time' | 'datetime' | 'relative';
  formatString?: string;
  className?: string;
  showRelativeTime?: boolean;
  baseDate?: Date;
}

/**
 * Component for displaying localized dates
 */
const LocalizedDate: React.FC<LocalizedDateProps> = ({
  date,
  format = 'date',
  formatString,
  className = '',
  showRelativeTime = false,
  baseDate,
}) => {
  if (!date) {
    return null;
  }

  let formattedDate = '';

  // Format based on the specified format type
  switch (format) {
    case 'time':
      formattedDate = formatTime(date, formatString);
      break;
    case 'datetime':
      formattedDate = formatDateTime(date, formatString);
      break;
    case 'relative':
      formattedDate = formatRelativeTime(date, baseDate);
      break;
    case 'date':
    default:
      formattedDate = formatDate(date, formatString);
      break;
  }

  // Add relative time if requested
  if (showRelativeTime && format !== 'relative') {
    formattedDate = `${formattedDate} (${formatRelativeTime(date, baseDate)})`;
  }

  return <span className={className}>{formattedDate}</span>;
};

export default LocalizedDate;
