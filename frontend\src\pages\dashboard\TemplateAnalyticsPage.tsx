/**
 * Template Analytics Page
 * Comprehensive analytics dashboard for template performance and usage
 */

import React from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { BarChart3, TrendingUp, Users, Target } from 'lucide-react';
// AuthenticatedLayout removed - page is already wrapped by route layout
import TemplateAnalyticsDashboard from '../../components/analytics/TemplateAnalyticsDashboard';
import { RTLText, RTLFlex } from '../../components/common';

const TemplateAnalyticsPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
      <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="space-y-6">
        {/* Page Header */}
        <div className="bg-gray-800/50 rounded-lg p-6">
          <RTLFlex className="items-center justify-between">
            <div>
              <RTLFlex className="items-center mb-2">
                <BarChart3 className={`text-purple-400 mr-3 ${isRTL ? "space-x-reverse" : ""}`} size={28} />
                <RTLText as="h1" className="text-2xl font-bold">
                  {t('analytics.title')}
                </RTLText>
              </RTLFlex>
              <p className="text-gray-300">
                {t('analytics.description')}
              </p>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-400">15</div>
                <div className="text-sm text-gray-400">{t('analytics.totalTemplates')}</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400">2.8K</div>
                <div className="text-sm text-gray-400">{t('analytics.totalUsage')}</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-400">4.3</div>
                <div className="text-sm text-gray-400">{t('analytics.avgRating')}</div>
              </div>
            </div>
          </RTLFlex>
        </div>

        {/* Analytics Dashboard */}
        <TemplateAnalyticsDashboard />
      </div>
              </div>
        </div>
      </div>
    </div>
  );
};

export default TemplateAnalyticsPage;
