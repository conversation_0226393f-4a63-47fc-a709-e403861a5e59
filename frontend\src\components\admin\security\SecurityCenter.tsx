import React, { useState, useEffect } from 'react';
import { Shield, Lock, Eye, AlertTriangle, Users, Globe, Key, Activity, Ban, CheckCircle } from 'lucide-react';
import { DashboardLayout } from '../dashboard';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';

interface SecurityEvent {
  id: string;
  type: 'login_attempt' | 'failed_login' | 'suspicious_activity' | 'data_breach' | 'permission_change';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  timestamp: string;
  user?: string;
  ip_address?: string;
  location?: string;
  resolved: boolean;
}

interface SecurityMetric {
  id: string;
  name: string;
  value: number;
  change: number;
  status: 'good' | 'warning' | 'critical';
  icon: React.ReactNode;
  color: string;
}

interface SecurityPolicy {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  category: 'authentication' | 'authorization' | 'data_protection' | 'monitoring';
  severity: 'low' | 'medium' | 'high';
}

const SecurityCenter: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  // State management
  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([]);
  const [securityMetrics, setSecurityMetrics] = useState<SecurityMetric[]>([]);
  const [securityPolicies, setSecurityPolicies] = useState<SecurityPolicy[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'events' | 'policies'>('overview');

  // Mock data
  useEffect(() => {
    const mockMetrics: SecurityMetric[] = [
      {
        id: 'failed_logins',
        name: 'Failed Logins (24h)',
        value: 23,
        change: -15.2,
        status: 'good',
        icon: <Lock size={20} />,
        color: 'text-green-400'
      },
      {
        id: 'suspicious_activities',
        name: 'Suspicious Activities',
        value: 7,
        change: 12.5,
        status: 'warning',
        icon: <Eye size={20} />,
        color: 'text-yellow-400'
      },
      {
        id: 'blocked_ips',
        name: 'Blocked IPs',
        value: 156,
        change: 8.3,
        status: 'good',
        icon: <Ban size={20} />,
        color: 'text-blue-400'
      },
      {
        id: 'active_sessions',
        name: 'Active Sessions',
        value: 342,
        change: 5.7,
        status: 'good',
        icon: <Users size={20} />,
        color: 'text-purple-400'
      }
    ];

    const mockEvents: SecurityEvent[] = [
      {
        id: '1',
        type: 'failed_login',
        severity: 'medium',
        title: 'Multiple Failed Login Attempts',
        description: 'User attempted to login 5 times with incorrect password',
        timestamp: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
        user: '<EMAIL>',
        ip_address: '*************',
        location: 'New York, US',
        resolved: false
      },
      {
        id: '2',
        type: 'suspicious_activity',
        severity: 'high',
        title: 'Unusual Access Pattern',
        description: 'User accessing system from multiple locations simultaneously',
        timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        user: '<EMAIL>',
        ip_address: '************',
        location: 'London, UK',
        resolved: false
      }
    ];

    const mockPolicies: SecurityPolicy[] = [
      {
        id: '1',
        name: 'Two-Factor Authentication',
        description: 'Require 2FA for all admin accounts',
        enabled: true,
        category: 'authentication',
        severity: 'high'
      },
      {
        id: '2',
        name: 'Password Complexity',
        description: 'Enforce strong password requirements',
        enabled: true,
        category: 'authentication',
        severity: 'medium'
      },
      {
        id: '3',
        name: 'Session Timeout',
        description: 'Automatic logout after 30 minutes of inactivity',
        enabled: true,
        category: 'authentication',
        severity: 'medium'
      }
    ];

    setSecurityMetrics(mockMetrics);
    setSecurityEvents(mockEvents);
    setSecurityPolicies(mockPolicies);
    setLoading(false);
  }, []);

  // Get severity color
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'text-red-400';
      case 'high':
        return 'text-orange-400';
      case 'medium':
        return 'text-yellow-400';
      case 'low':
        return 'text-green-400';
      default:
        return 'text-gray-400';
    }
  };

  // Get severity background
  const getSeverityBg = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-500/20';
      case 'high':
        return 'bg-orange-500/20';
      case 'medium':
        return 'bg-yellow-500/20';
      case 'low':
        return 'bg-green-500/20';
      default:
        return 'bg-gray-500/20';
    }
  };

  // Get event icon
  const getEventIcon = (type: string) => {
    switch (type) {
      case 'login_attempt':
        return <Key size={16} className="text-blue-400" />;
      case 'failed_login':
        return <Lock size={16} className="text-red-400" />;
      case 'suspicious_activity':
        return <Eye size={16} className="text-yellow-400" />;
      case 'data_breach':
        return <AlertTriangle size={16} className="text-red-400" />;
      case 'permission_change':
        return <Users size={16} className="text-purple-400" />;
      default:
        return <Activity size={16} className="text-gray-400" />;
    }
  };

  // Toggle policy
  const togglePolicy = (policyId: string) => {
    setSecurityPolicies(prev => prev.map(policy => 
      policy.id === policyId 
        ? { ...policy, enabled: !policy.enabled }
        : policy
    ));
  };

  // Get relative time
  const getRelativeTime = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffMs = now.getTime() - time.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 1) return t('security.just.now', 'Just now');
    if (diffMins < 60) return t('security.minutes.ago', '{{count}}m ago', { count: diffMins });
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return t('security.hours.ago', '{{count}}h ago', { count: diffHours });
    return time.toLocaleDateString();
  };

  // Tabs
  const tabs = [
    { id: 'overview', label: t('security.overview', 'Overview'), icon: <Shield size={16} /> },
    { id: 'events', label: t('security.events', 'Security Events'), icon: <AlertTriangle size={16} /> },
    { id: 'policies', label: t('security.policies', 'Security Policies'), icon: <Lock size={16} /> }
  ];

  return (
    <DashboardLayout currentPage="security">
      {/* Header */}
      <div className={`mb-8 ${isRTL ? "text-right" : ""}`}>
        <h1 className="text-2xl font-bold text-white">{t('admin.security.center', 'Security Center')}</h1>
        <div className="text-gray-400 mt-1">{t('admin.security.description', 'Monitor and manage platform security')}</div>
      </div>

      {/* Tabs */}
      <div className="mb-8">
        <div className={`flex space-x-1 bg-indigo-900/30 p-1 rounded-lg ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-300 ${
                activeTab === tab.id
                  ? 'bg-purple-600 text-white shadow-lg'
                  : 'text-gray-300 hover:text-white hover:bg-indigo-800/50'
              } ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}
            >
              {tab.icon}
              <span>{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div>
          {/* Security Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {securityMetrics.map((metric) => (
              <div key={metric.id} className="bg-indigo-900/30 backdrop-blur-sm rounded-xl p-6 border border-indigo-800/50">
                <div className={`flex items-center justify-between mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className={metric.color}>
                    {metric.icon}
                  </div>
                  <div className={`flex items-center space-x-1 text-sm ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                    <span className={metric.change >= 0 ? 'text-red-400' : 'text-green-400'}>
                      {metric.change > 0 ? '+' : ''}{metric.change.toFixed(1)}%
                    </span>
                  </div>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-white mb-1">{metric.value}</h3>
                  <p className="text-gray-400 text-sm">{metric.name}</p>
                </div>
              </div>
            ))}
          </div>

          {/* Recent Events */}
          <div className="bg-indigo-900/30 backdrop-blur-sm rounded-xl p-6 border border-indigo-800/50">
            <h3 className="text-lg font-semibold text-white mb-6">{t('security.recent.events', 'Recent Security Events')}</h3>
            
            <div className="space-y-3">
              {securityEvents.slice(0, 5).map((event) => (
                <div key={event.id} className={`p-3 bg-indigo-800/30 rounded-lg border-l-4 ${
                  event.severity === 'critical' ? 'border-l-red-500' :
                  event.severity === 'high' ? 'border-l-orange-500' :
                  event.severity === 'medium' ? 'border-l-yellow-500' : 'border-l-green-500'
                }`}>
                  <div className={`flex items-start space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                    <div className="flex-shrink-0 mt-1">
                      {getEventIcon(event.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className={`flex items-center justify-between mb-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <h4 className="text-white font-medium text-sm">{event.title}</h4>
                        <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityBg(event.severity)} ${getSeverityColor(event.severity)}`}>
                            {event.severity}
                          </span>
                          {event.resolved && (
                            <CheckCircle size={14} className="text-green-400" />
                          )}
                        </div>
                      </div>
                      <p className="text-gray-300 text-sm mb-2">{event.description}</p>
                      <div className={`flex items-center justify-between text-xs ${isRTL ? "flex-row-reverse" : ""}`}>
                        <span className="text-gray-400">{event.user || 'System'}</span>
                        <span className="text-gray-400">{getRelativeTime(event.timestamp)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'policies' && (
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-xl p-6 border border-indigo-800/50">
          <h3 className="text-lg font-semibold text-white mb-6">{t('security.policies.management', 'Security Policies Management')}</h3>
          
          <div className="space-y-4">
            {securityPolicies.map((policy) => (
              <div key={policy.id} className="p-4 bg-indigo-800/30 rounded-lg">
                <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className="flex-1">
                    <div className={`flex items-center space-x-3 mb-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                      <h4 className="text-white font-medium">{policy.name}</h4>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityBg(policy.severity)} ${getSeverityColor(policy.severity)}`}>
                        {policy.severity}
                      </span>
                    </div>
                    <p className="text-gray-400 text-sm">{policy.description}</p>
                  </div>
                  <div className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                    <span className={`text-sm ${policy.enabled ? 'text-green-400' : 'text-gray-400'}`}>
                      {policy.enabled ? t('security.enabled', 'Enabled') : t('security.disabled', 'Disabled')}
                    </span>
                    <button
                      onClick={() => togglePolicy(policy.id)}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        policy.enabled ? 'bg-purple-600' : 'bg-gray-600'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          policy.enabled ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </DashboardLayout>
  );
};

export default SecurityCenter;
