"""
Management command to fix empty business plans by creating sections from templates
"""

from django.core.management.base import BaseCommand
from incubator.models_business_plan import BusinessPlan, BusinessPlanSection, BusinessPlanTemplate
from incubator.template_definitions import STANDARD_TEMPLATE


class Command(BaseCommand):
    help = 'Fix empty business plans by creating sections from templates'

    def add_arguments(self, parser):
        parser.add_argument(
            '--plan-id',
            type=int,
            help='Fix specific business plan by ID',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes',
        )

    def handle(self, *args, **options):
        plan_id = options.get('plan_id')
        dry_run = options.get('dry_run', False)

        if plan_id:
            plans = BusinessPlan.objects.filter(id=plan_id)
            if not plans.exists():
                self.stdout.write(
                    self.style.ERROR(f'Business plan with ID {plan_id} not found')
                )
                return
        else:
            plans = BusinessPlan.objects.all()

        self.stdout.write(f'Found {plans.count()} business plan(s) to check')

        for plan in plans:
            self.stdout.write(f'\nChecking plan {plan.id}: {plan.title}')
            
            # Check if plan has sections
            sections_count = BusinessPlanSection.objects.filter(business_plan=plan).count()
            self.stdout.write(f'  Current sections: {sections_count}')

            if sections_count == 0:
                self.stdout.write(f'  Plan {plan.id} has no sections - fixing...')
                
                if dry_run:
                    self.stdout.write('  [DRY RUN] Would create sections from template')
                    continue

                # Create sections from template
                if plan.template:
                    self.create_sections_from_template(plan, plan.template)
                else:
                    # Use standard template as fallback
                    self.create_sections_from_standard_template(plan)
                
                # Update completion percentage
                plan.completion_percentage = 0
                plan.save()
                
                self.stdout.write(
                    self.style.SUCCESS(f'  ✅ Created sections for plan {plan.id}')
                )
            else:
                # Check if sections have content
                empty_sections = BusinessPlanSection.objects.filter(
                    business_plan=plan,
                    content__isnull=True
                ).count() + BusinessPlanSection.objects.filter(
                    business_plan=plan,
                    content=''
                ).count()
                
                if empty_sections > 0:
                    self.stdout.write(f'  Plan {plan.id} has {empty_sections} empty sections')
                else:
                    self.stdout.write(f'  Plan {plan.id} looks good')

        self.stdout.write(
            self.style.SUCCESS('\nDone!')
        )

    def create_sections_from_template(self, business_plan, template):
        """Create sections from a business plan template"""
        sections_data = template.sections
        
        # Handle different template formats
        if isinstance(sections_data, dict):
            if 'sections' in sections_data:
                sections_data = sections_data['sections']
            
            # Convert dict to list
            if isinstance(sections_data, dict):
                sections_list = []
                for key, section_data in sections_data.items():
                    section_copy = dict(section_data) if isinstance(section_data, dict) else {}
                    section_copy['key'] = key
                    sections_list.append(section_copy)
                sections_data = sections_list

        if not isinstance(sections_data, list):
            self.stdout.write(
                self.style.WARNING(f'  Invalid template format for {template.name}')
            )
            return

        for i, section_data in enumerate(sections_data):
            BusinessPlanSection.objects.create(
                business_plan=business_plan,
                title=section_data.get('title', f'Section {i+1}'),
                key=section_data.get('key', f'section_{i+1}'),
                content='',  # Start with empty content
                order=section_data.get('order', i),
                is_required=section_data.get('required', True),
                is_completed=False
            )

    def create_sections_from_standard_template(self, business_plan):
        """Create sections from the standard template"""
        sections_data = STANDARD_TEMPLATE['sections']
        
        for i, (key, section_data) in enumerate(sections_data.items()):
            BusinessPlanSection.objects.create(
                business_plan=business_plan,
                title=section_data.get('title', f'Section {i+1}'),
                key=key,
                content='',  # Start with empty content
                order=section_data.get('order', i),
                is_required=section_data.get('required', True),
                is_completed=False
            )
