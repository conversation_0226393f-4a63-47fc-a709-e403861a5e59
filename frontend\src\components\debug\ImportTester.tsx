import React, { useState } from 'react';
import { Play, CheckCircle, XCircle, AlertTriangle } from 'lucide-react';

interface ImportTest {
  name: string;
  importFn: () => Promise<any>;
  status: 'idle' | 'loading' | 'success' | 'error';
  error?: string;
  result?: any;
}

/**
 * Component to test individual imports from BusinessIdeasPage
 */
const ImportTester: React.FC = () => {
  const [tests, setTests] = useState<ImportTest[]>([
    {
      name: 'React',
      importFn: () => import('react'),
      status: 'idle'
    },
    {
      name: 'useTranslation',
      importFn: () => import('react-i18next').then(m => ({ useTranslation: m.useTranslation })),
      status: 'idle'
    },
    {
      name: 'useLanguage',
      importFn: () => import('../../hooks/useLanguage'),
      status: 'idle'
    },
    {
      name: 'useAppSelector',
      importFn: () => import('../../store/hooks').then(m => ({ useAppSelector: m.useAppSelector })),
      status: 'idle'
    },
    {
      name: 'useCRUD',
      importFn: () => import('../../hooks/useCRUD'),
      status: 'idle'
    },
    {
      name: 'incubatorApi',
      importFn: () => import('../../services/incubatorApi'),
      status: 'idle'
    },
    {
      name: 'BusinessIdeaForm',
      importFn: () => import('../../components/incubator/BusinessIdeaForm'),
      status: 'idle'
    },
    {
      name: 'EnhancedCRUDTable',
      importFn: () => import('../../components/common').then(m => ({ EnhancedCRUDTable: m.EnhancedCRUDTable })),
      status: 'idle'
    },
    {
      name: 'AuthenticatedLayout',
      importFn: () => import('../../components/layout/AuthenticatedLayout'),
      status: 'idle'
    },
    {
      name: 'lucide-react',
      importFn: () => import('lucide-react').then(m => ({ Plus: m.Plus, Edit: m.Edit })),
      status: 'idle'
    },
    {
      name: 'BusinessIdeasPage (Direct)',
      importFn: () => import('../../pages/dashboard/BusinessIdeasPage'),
      status: 'idle'
    }
  ]);

  const [isVisible, setIsVisible] = useState(false);

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const runTest = async (testIndex: number) => {
    const test = tests[testIndex];
    
    setTests(prev => prev.map((t, i) => 
      i === testIndex ? { ...t, status: 'loading', error: undefined } : t
    ));

    try {
      const result = await test.importFn();
      setTests(prev => prev.map((t, i) => 
        i === testIndex ? { ...t, status: 'success', result } : t
      ));
    } catch (error) {
      setTests(prev => prev.map((t, i) => 
        i === testIndex ? { 
          ...t, 
          status: 'error', 
          error: error instanceof Error ? error.message : String(error) 
        } : t
      ));
    }
  };

  const runAllTests = async () => {
    for (let i = 0; i < tests.length; i++) {
      await runTest(i);
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  };

  const getStatusIcon = (status: ImportTest['status']) => {
    switch (status) {
      case 'loading':
        return <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />;
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <div className="w-4 h-4 border border-gray-400 rounded-full" />;
    }
  };

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-32 right-4 z-50 bg-purple-600 text-white p-2 rounded-lg shadow-lg hover:bg-purple-700 transition-colors"
        title="Import Tester"
      >
        <Play className="w-4 h-4" />
      </button>
    );
  }

  return (
    <div className="fixed bottom-32 right-4 z-50 bg-gray-900 border border-gray-700 rounded-lg shadow-lg max-w-lg max-h-96 overflow-hidden">
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-700">
        <h3 className="text-white font-medium">Import Tests</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-white"
        >
          ×
        </button>
      </div>

      {/* Content */}
      <div className="p-3 space-y-3 overflow-y-auto max-h-80">
        <div className="flex gap-2">
          <button
            onClick={runAllTests}
            className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition-colors"
          >
            Run All
          </button>
          <button
            onClick={() => setTests(prev => prev.map(t => ({ ...t, status: 'idle', error: undefined })))}
            className="bg-gray-600 text-white px-3 py-1 rounded text-sm hover:bg-gray-700 transition-colors"
          >
            Reset
          </button>
        </div>

        <div className="space-y-2">
          {tests.map((test, index) => (
            <div key={test.name} className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-2 flex-1 min-w-0">
                <button
                  onClick={() => runTest(index)}
                  className="text-blue-400 hover:text-blue-300 text-xs"
                >
                  ▶
                </button>
                <span className="text-gray-300 truncate">{test.name}</span>
              </div>
              <div className="flex items-center gap-2 flex-shrink-0">
                {getStatusIcon(test.status)}
                {test.error && (
                  <AlertTriangle 
                    className="w-3 h-3 text-yellow-500" 
                    title={test.error}
                  />
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Error Details */}
        {tests.some(t => t.error) && (
          <div className="border-t border-gray-700 pt-2">
            <h4 className="text-white text-xs font-medium mb-1">Errors:</h4>
            <div className="space-y-1 max-h-20 overflow-y-auto">
              {tests.filter(t => t.error).map(test => (
                <div key={test.name} className="text-xs">
                  <span className="text-red-400">{test.name}:</span>
                  <span className="text-gray-300 ml-1">{test.error}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ImportTester;
