"""
Centralized Gemini AI Configuration
Single source of truth for Gemini AI across the application.
Database-driven configuration that Super Admin can manage from frontend.
"""

import os
import logging
from typing import Optional, Dict, Any
from django.conf import settings
from django.core.cache import cache
import google.generativeai as genai

# Configure logging
logger = logging.getLogger(__name__)

class CentralGeminiConfig:
    """
    Centralized Gemini AI configuration for the entire application
    Database-driven configuration with caching for performance
    """

    _instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not self._initialized:
            self._setup_config()
            self._initialized = True

    def _setup_config(self):
        """Setup Gemini configuration from database"""
        self.api_key = None
        self.model_name = 'gemini-2.0-flash'
        self.max_tokens = 4000
        self.temperature = 0.7
        self.is_available = False
        self.model = None

        # Load configuration from database first, then fallback
        self._load_database_config()

        # Initialize Gemini if API key is available
        if self.api_key:
            self._initialize_gemini()

        logger.info(f"🤖 Central Gemini Config initialized - Available: {self.is_available}")

    def _load_database_config(self):
        """Load Gemini configuration from database"""
        try:
            from .models import AIConfiguration

            # Get Gemini configurations from database
            self.api_key = AIConfiguration.get_config('gemini', 'api_key')
            self.model_name = AIConfiguration.get_config('gemini', 'default_model', 'gemini-2.0-flash')
            self.max_tokens = int(AIConfiguration.get_config('gemini', 'max_tokens', 4000))
            self.temperature = float(AIConfiguration.get_config('gemini', 'temperature', 0.7))

            if self.api_key:
                logger.info("✅ Loaded Gemini config from database")
                return

        except Exception as e:
            logger.warning(f"Failed to load database config: {e}")

        # Fallback to environment/settings
        self._load_fallback_config()

    def _load_fallback_config(self):
        """Fallback configuration from environment variables"""
        # Try environment variable first
        self.api_key = os.getenv('GEMINI_API_KEY')

        # Try Django settings
        if not self.api_key:
            self.api_key = getattr(settings, 'GEMINI_API_KEY', None)

        # Use working key for development if nothing else is set
        if not self.api_key or self.api_key in ['your-gemini-api-key-here']:
            self.api_key = 'AIzaSyBNAUxgVNbpfqKy0c7cLakUX96JG-EqlK4'

        logger.info("📁 Using fallback Gemini config")

    def _initialize_gemini(self):
        """Initialize Gemini AI service"""
        try:
            genai.configure(api_key=self.api_key)
            self.model = genai.GenerativeModel(self.model_name)
            self.is_available = True
            logger.info("✅ Gemini AI initialized successfully")
        except Exception as e:
            logger.warning(f"⚠️ Gemini AI initialization failed: {e}")
            self.is_available = False
            self.model = None

    def generate_content(self, prompt: str, **kwargs) -> Optional[str]:
        """Generate content using Gemini AI"""
        if not self.is_available or not self.model:
            return None

        try:
            # Use configured parameters or override with kwargs
            generation_config = {
                'max_output_tokens': kwargs.get('max_tokens', self.max_tokens),
                'temperature': kwargs.get('temperature', self.temperature),
            }

            response = self.model.generate_content(prompt, generation_config=generation_config)
            return response.text if response and response.text else None
        except Exception as e:
            logger.error(f"Content generation error: {e}")
            return None

    def get_status(self) -> Dict[str, Any]:
        """Get Gemini service status"""
        return {
            'service': 'gemini',
            'available': self.is_available,
            'model': self.model_name,
            'api_key_configured': bool(self.api_key),
            'max_tokens': self.max_tokens,
            'temperature': self.temperature,
            'features': {
                'content_generation': True,
                'chat': True,
                'business_analysis': True,
                'multilingual': True,
                'vision': 'gemini-pro-vision' in self.model_name
            }
        }

    def reload_config(self):
        """Reload configuration (useful for API key updates)"""
        self._initialized = False
        self._setup_config()

    def update_config(self, api_key=None, model_name=None, max_tokens=None, temperature=None):
        """Update configuration and reinitialize"""
        if api_key:
            self.api_key = api_key
        if model_name:
            self.model_name = model_name
        if max_tokens:
            self.max_tokens = max_tokens
        if temperature:
            self.temperature = temperature

        # Reinitialize with new config
        if self.api_key:
            self._initialize_gemini()
    
# Global instance - singleton pattern
_gemini_config = None

def get_gemini_config() -> CentralGeminiConfig:
    """Get the global Gemini configuration instance"""
    global _gemini_config
    if _gemini_config is None:
        _gemini_config = CentralGeminiConfig()
    return _gemini_config

def is_gemini_available() -> bool:
    """Check if Gemini service is available"""
    return get_gemini_config().is_available

def generate_gemini_content(prompt: str, **kwargs) -> Optional[str]:
    """Generate content using Gemini AI with central config"""
    return get_gemini_config().generate_content(prompt, **kwargs)

def get_gemini_status() -> Dict[str, Any]:
    """Get Gemini service status"""
    return get_gemini_config().get_status()

def reload_gemini_config():
    """Reload Gemini configuration"""
    get_gemini_config().reload_config()

def update_gemini_config(api_key=None, model_name=None, max_tokens=None, temperature=None):
    """Update Gemini configuration"""
    get_gemini_config().update_config(api_key, model_name, max_tokens, temperature)

# Backward compatibility aliases
get_ai_config = get_gemini_config
is_ai_available = is_gemini_available
generate_ai_content = generate_gemini_content
get_ai_status = get_gemini_status
reload_ai_config = reload_gemini_config


# Language-specific prompt helpers for Gemini
class GeminiPrompts:
    """Centralized Gemini prompts for different languages and contexts"""

    @staticmethod
    def get_system_prompt(language: str = 'en', context: str = 'general') -> str:
        """Get system prompt based on language and context"""

        if language == 'ar':
            base_prompt = """أنت ياسمين، مساعد ذكي متخصص في ريادة الأعمال والذكاء الاصطناعي.

خصائصك:
- تتحدث العربية بطلاقة وتفهم الثقافة العربية
- خبير في تخطيط الأعمال والتحليل المالي
- متخصص في الذكاء الاصطناعي وعلوم البيانات
- تقدم نصائح عملية ومفيدة

تعليمات:
- أجب دائماً باللغة العربية الفصحى
- كن مفيداً ومهنياً ومتفهماً للسياق الثقافي
- قدم معلومات دقيقة ومحدثة"""
        else:
            base_prompt = """You are Yasmeen, an intelligent AI assistant specialized in business and entrepreneurship.

Your characteristics:
- Expert in business planning and financial analysis
- Specialized in AI and data science
- Provide practical and useful advice
- Understand cultural context and business environments

Instructions:
- Always be helpful, professional, and contextually aware
- Provide accurate and up-to-date information
- Focus on actionable business insights"""

        # Add context-specific prompts
        if context == 'business_plan':
            if language == 'ar':
                base_prompt += "\n\nأنت متخصص في إنشاء خطط الأعمال الشاملة والمفصلة."
            else:
                base_prompt += "\n\nYou specialize in creating comprehensive and detailed business plans."

        elif context == 'mentorship':
            if language == 'ar':
                base_prompt += "\n\nأنت مرشد أعمال خبير يقدم التوجيه والنصائح للرياديين."
            else:
                base_prompt += "\n\nYou are an expert business mentor providing guidance to entrepreneurs."

        return base_prompt

# Backward compatibility
AIPrompts = GeminiPrompts



# Fallback responses when AI is not available
class AIFallbacks:
    """Centralized fallback responses"""
    
    @staticmethod
    def get_chat_fallback(language: str = 'en') -> str:
        """Get chat fallback response"""
        if language == 'ar':
            return """مرحباً! أنا ياسمين، مساعدك الذكي في ريادة الأعمال.

خدمة الذكاء الاصطناعي غير متاحة حالياً، لكن يمكنني مساعدتك في:
• تطوير أفكار الأعمال
• التخطيط الاستراتيجي
• تحليل السوق
• النصائح العملية

كيف يمكنني مساعدتك اليوم؟"""
        else:
            return """Hello! I'm Yasmeen, your intelligent business assistant.

The AI service is currently unavailable, but I can help you with:
• Business idea development
• Strategic planning
• Market analysis
• Practical advice

How can I help you today?"""
    
    @staticmethod
    def get_business_analysis_fallback(business_idea: str, language: str = 'en') -> str:
        """Get business analysis fallback"""
        if language == 'ar':
            return f"""تحليل فكرة العمل: {business_idea}

📊 تحليل أولي:

✅ نقاط القوة المحتملة:
• فكرة مبتكرة تستحق التطوير
• إمكانية لخدمة احتياجات السوق
• فرصة للنمو والتوسع

⚠️ التحديات المتوقعة:
• الحاجة لدراسة السوق المتعمقة
• تحديد الجمهور المستهدف
• وضع استراتيجية تسويق فعالة

💡 التوصيات:
• ابدأ بدراسة جدوى مفصلة
• تحدث مع العملاء المحتملين
• طور نموذج أولي للاختبار

🎯 الخطوات التالية:
1. تحديد السوق المستهدف
2. تحليل المنافسين
3. تطوير المنتج/الخدمة
4. وضع خطة التسويق"""
        else:
            return f"""Business Idea Analysis: {business_idea}

📊 Initial Analysis:

✅ Potential Strengths:
• Innovative idea worth developing
• Potential to serve market needs
• Opportunity for growth and expansion

⚠️ Expected Challenges:
• Need for in-depth market research
• Target audience identification
• Effective marketing strategy development

💡 Recommendations:
• Start with detailed feasibility study
• Talk to potential customers
• Develop prototype for testing

🎯 Next Steps:
1. Define target market
2. Analyze competitors
3. Develop product/service
4. Create marketing plan"""
    
    @staticmethod
    def get_error_message(language: str = 'en') -> str:
        """Get error message"""
        if language == 'ar':
            return "أعتذر، حدث خطأ تقني. يرجى المحاولة مرة أخرى لاحقاً."
        else:
            return "I apologize, but a technical error occurred. Please try again later."
