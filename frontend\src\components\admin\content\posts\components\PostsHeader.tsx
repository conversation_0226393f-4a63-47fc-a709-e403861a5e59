import React from 'react';
import { Plus } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../../hooks/useLanguage';
interface PostsHeaderProps {
  onCreatePost: () => void;
}

const PostsHeader: React.FC<PostsHeaderProps> = ({ onCreatePost  }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  // Prevent default form submission
  const preventFormSubmission = (e: React.FormEvent) => {
    e.preventDefault();
    e.stopPropagation();
    return false;
  };

  return (
    <div onSubmit={preventFormSubmission} className={`mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
      <div>
        <h1 className="text-2xl font-bold">t("admin.posts.management", "Posts Management")</h1>
        <p className="text-gray-400 mt-1">t("admin.manage.community.posts", "Manage community posts and discussions")</p>
      </div>
      <button
        type="button"
        onClick={onCreatePost}
        className={`mt-4 sm:mt-0 px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300 flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}
      >
        <Plus size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
        Create New Post
      </button>
    </div>
  );
};

export default PostsHeader;
