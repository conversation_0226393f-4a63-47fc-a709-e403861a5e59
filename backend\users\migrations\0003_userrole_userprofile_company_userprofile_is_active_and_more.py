# Generated by Django 4.2.21 on 2025-06-03 03:36

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("users", "0002_alter_userprofile_profile_image"),
    ]

    operations = [
        migrations.CreateModel(
            name="UserRole",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        choices=[
                            ("admin", "Administrator"),
                            ("moderator", "Moderator"),
                            ("mentor", "Mentor"),
                            ("investor", "Investor"),
                            ("expert_advisor", "Expert Advisor"),
                            ("business_analyst", "Business Analyst"),
                            ("content_creator", "Content Creator"),
                            ("community_manager", "Community Manager"),
                            ("startup_founder", "Startup Founder"),
                            ("accelerator_manager", "Accelerator Manager"),
                            ("legal_advisor", "Legal Advisor"),
                            ("marketing_specialist", "Marketing Specialist"),
                            ("technical_advisor", "Technical Advisor"),
                            ("user", "Regular User"),
                        ],
                        max_length=50,
                        unique=True,
                    ),
                ),
                ("display_name", models.CharField(max_length=100)),
                ("description", models.TextField(blank=True)),
                (
                    "permission_level",
                    models.CharField(
                        choices=[
                            ("read", "Read Only"),
                            ("write", "Read & Write"),
                            ("moderate", "Moderate Content"),
                            ("admin", "Full Admin Access"),
                        ],
                        default="read",
                        max_length=20,
                    ),
                ),
                ("can_moderate_content", models.BooleanField(default=False)),
                ("can_manage_users", models.BooleanField(default=False)),
                ("can_access_analytics", models.BooleanField(default=False)),
                ("can_create_events", models.BooleanField(default=False)),
                ("can_approve_ideas", models.BooleanField(default=False)),
                ("can_manage_funding", models.BooleanField(default=False)),
                ("can_provide_mentorship", models.BooleanField(default=False)),
                ("can_review_applications", models.BooleanField(default=False)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ["name"],
            },
        ),
        migrations.AddField(
            model_name="userprofile",
            name="company",
            field=models.CharField(blank=True, max_length=200),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="is_active",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="is_verified",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="position",
            field=models.CharField(blank=True, max_length=200),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="verification_date",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="years_of_experience",
            field=models.PositiveIntegerField(
                default=0,
                validators=[
                    django.core.validators.MinValueValidator(0),
                    django.core.validators.MaxValueValidator(50),
                ],
            ),
        ),
        migrations.CreateModel(
            name="UserRoleAssignment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("assigned_at", models.DateTimeField(auto_now_add=True)),
                (
                    "expires_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="Optional expiration date for temporary roles",
                        null=True,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                (
                    "notes",
                    models.TextField(
                        blank=True, help_text="Notes about this role assignment"
                    ),
                ),
                ("is_approved", models.BooleanField(default=True)),
                ("approved_at", models.DateTimeField(blank=True, null=True)),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="role_approvals_made",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "assigned_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="role_assignments_made",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "role",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="users.userrole"
                    ),
                ),
                (
                    "user_profile",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="users.userprofile",
                    ),
                ),
            ],
            options={
                "ordering": ["-assigned_at"],
                "unique_together": {("user_profile", "role")},
            },
        ),
        migrations.AddField(
            model_name="userprofile",
            name="primary_role",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="primary_users",
                to="users.userrole",
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="roles",
            field=models.ManyToManyField(
                blank=True, through="users.UserRoleAssignment", to="users.userrole"
            ),
        ),
        migrations.CreateModel(
            name="RoleApplication",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "motivation",
                    models.TextField(help_text="Why do you want this role?"),
                ),
                (
                    "qualifications",
                    models.TextField(help_text="What qualifies you for this role?"),
                ),
                (
                    "experience",
                    models.TextField(blank=True, help_text="Relevant experience"),
                ),
                (
                    "portfolio_url",
                    models.URLField(blank=True, help_text="Portfolio or work samples"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending Review"),
                            ("approved", "Approved"),
                            ("rejected", "Rejected"),
                            ("withdrawn", "Withdrawn"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("reviewed_at", models.DateTimeField(blank=True, null=True)),
                (
                    "admin_notes",
                    models.TextField(blank=True, help_text="Internal notes for admins"),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "requested_role",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="users.userrole"
                    ),
                ),
                (
                    "reviewed_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="role_reviews_made",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="role_applications",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
                "unique_together": {("user", "requested_role", "status")},
            },
        ),
    ]
