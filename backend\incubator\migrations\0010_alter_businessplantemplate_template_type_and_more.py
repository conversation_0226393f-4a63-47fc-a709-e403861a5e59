# Generated by Django 4.2.21 on 2025-05-31 18:56

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("incubator", "0009_alter_businessplantemplate_template_type_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="businessplantemplate",
            name="template_type",
            field=models.CharField(
                choices=[
                    ("standard", "Standard"),
                    ("lean", "Lean Canvas"),
                    ("detailed", "Detailed"),
                    ("investor", "Investor-Ready"),
                    ("startup", "Startup"),
                    ("nonprofit", "Non-Profit"),
                    ("service", "Service Business"),
                    ("product", "Product Business"),
                    ("ecommerce", "E-commerce"),
                    ("saas", "SaaS/Software"),
                    ("restaurant", "Restaurant/Food Service"),
                    ("retail", "Retail"),
                    ("consulting", "Consulting"),
                    ("franchise", "Franchise"),
                    ("manufacturing", "Manufacturing"),
                    ("healthcare", "Healthcare"),
                    ("education", "Education"),
                    ("real_estate", "Real Estate"),
                    ("fintech", "FinTech"),
                    ("marketplace", "Marketplace"),
                    ("subscription", "Subscription Business"),
                    ("mobile_app", "Mobile App"),
                    ("social_impact", "Social Impact"),
                    ("green_business", "Green/Sustainable Business"),
                    ("ai_startup", "AI/Tech Startup"),
                    ("blockchain", "Blockchain/Crypto"),
                    ("gaming", "Gaming/Entertainment"),
                    ("fitness", "Fitness & Wellness"),
                    ("food_truck", "Food Truck"),
                    ("beauty_salon", "Beauty Salon"),
                    ("pet_services", "Pet Services"),
                    ("automotive", "Automotive"),
                    ("photography", "Photography"),
                    ("event_planning", "Event Planning"),
                    ("cleaning_services", "Cleaning Services"),
                    ("landscaping", "Landscaping"),
                    ("home_services", "Home Services"),
                    ("digital_marketing", "Digital Marketing"),
                    ("dropshipping", "Dropshipping"),
                    ("affiliate_marketing", "Affiliate Marketing"),
                    ("coaching", "Coaching"),
                    ("tutoring", "Tutoring"),
                    ("travel_tourism", "Travel & Tourism"),
                    ("import_export", "Import/Export"),
                    ("logistics", "Logistics"),
                    ("security_services", "Security Services"),
                    ("legal_services", "Legal Services"),
                    ("accounting_services", "Accounting Services"),
                    ("insurance_services", "Insurance Services"),
                    ("international", "International Expansion"),
                    ("acquisition", "Business Acquisition"),
                    ("pivot", "Business Pivot"),
                    ("custom", "Custom"),
                ],
                default="standard",
                max_length=20,
            ),
        ),
        migrations.CreateModel(
            name="TemplatePerformanceMetrics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("total_views", models.IntegerField(default=0)),
                ("total_selections", models.IntegerField(default=0)),
                ("total_completions", models.IntegerField(default=0)),
                ("unique_users", models.IntegerField(default=0)),
                ("selection_rate", models.FloatField(default=0.0)),
                ("completion_rate", models.FloatField(default=0.0)),
                (
                    "average_completion_time",
                    models.DurationField(blank=True, null=True),
                ),
                ("average_rating", models.FloatField(default=0.0)),
                ("total_ratings", models.IntegerField(default=0)),
                ("net_promoter_score", models.FloatField(default=0.0)),
                ("business_plans_published", models.IntegerField(default=0)),
                ("funding_success_rate", models.FloatField(default=0.0)),
                ("business_launch_rate", models.FloatField(default=0.0)),
                ("usage_trends", models.JSONField(blank=True, default=dict)),
                ("performance_trends", models.JSONField(blank=True, default=dict)),
                ("last_updated", models.DateTimeField(auto_now=True)),
                (
                    "template",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="performance_metrics",
                        to="incubator.businessplantemplate",
                    ),
                ),
            ],
            options={
                "db_table": "template_performance_metrics",
            },
        ),
        migrations.CreateModel(
            name="TemplateABTest",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200)),
                ("description", models.TextField()),
                ("traffic_split", models.FloatField(default=50.0)),
                ("start_date", models.DateTimeField()),
                ("end_date", models.DateTimeField()),
                ("is_active", models.BooleanField(default=True)),
                (
                    "primary_metric",
                    models.CharField(
                        choices=[
                            ("completion_rate", "Completion Rate"),
                            ("selection_rate", "Selection Rate"),
                            ("user_rating", "User Rating"),
                            ("time_to_complete", "Time to Complete"),
                            ("business_success", "Business Success Rate"),
                        ],
                        max_length=50,
                    ),
                ),
                ("original_metric_value", models.FloatField(blank=True, null=True)),
                ("variant_metric_value", models.FloatField(blank=True, null=True)),
                ("statistical_significance", models.FloatField(blank=True, null=True)),
                (
                    "winner",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("original", "Original"),
                            ("variant", "Variant"),
                            ("inconclusive", "Inconclusive"),
                        ],
                        max_length=20,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "original_template",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="ab_tests_original",
                        to="incubator.businessplantemplate",
                    ),
                ),
                (
                    "variant_template",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="ab_tests_variant",
                        to="incubator.businessplantemplate",
                    ),
                ),
            ],
            options={
                "db_table": "template_ab_test",
            },
        ),
        migrations.CreateModel(
            name="UserTemplateInteraction",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("session_id", models.CharField(max_length=100)),
                (
                    "action_type",
                    models.CharField(
                        choices=[
                            ("view", "View Template"),
                            ("select", "Select Template"),
                            ("start", "Start Working"),
                            ("save_section", "Save Section"),
                            ("complete_section", "Complete Section"),
                            ("use_ai", "Use AI Assistance"),
                            ("customize", "Customize Section"),
                            ("export", "Export Business Plan"),
                            ("share", "Share Business Plan"),
                            ("rate", "Rate Template"),
                            ("feedback", "Provide Feedback"),
                        ],
                        max_length=50,
                    ),
                ),
                ("section_key", models.CharField(blank=True, max_length=100)),
                ("action_data", models.JSONField(blank=True, default=dict)),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                ("duration", models.DurationField(blank=True, null=True)),
                ("device_type", models.CharField(blank=True, max_length=20)),
                ("browser", models.CharField(blank=True, max_length=50)),
                (
                    "template",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="incubator.businessplantemplate",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "user_template_interaction",
                "indexes": [
                    models.Index(
                        fields=["user", "timestamp"],
                        name="user_templa_user_id_bd72f3_idx",
                    ),
                    models.Index(
                        fields=["template", "action_type"],
                        name="user_templa_templat_ebb591_idx",
                    ),
                    models.Index(
                        fields=["session_id"], name="user_templa_session_e9cb9d_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="TemplateUsageAnalytics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("viewed_at", models.DateTimeField(auto_now_add=True)),
                ("selected_at", models.DateTimeField(blank=True, null=True)),
                ("started_at", models.DateTimeField(blank=True, null=True)),
                ("completed_at", models.DateTimeField(blank=True, null=True)),
                ("time_spent_viewing", models.DurationField(blank=True, null=True)),
                ("time_spent_working", models.DurationField(blank=True, null=True)),
                ("sections_completed", models.IntegerField(default=0)),
                ("total_sections", models.IntegerField(default=0)),
                ("completion_percentage", models.FloatField(default=0.0)),
                (
                    "rating",
                    models.IntegerField(
                        blank=True,
                        choices=[
                            (1, "Very Poor"),
                            (2, "Poor"),
                            (3, "Average"),
                            (4, "Good"),
                            (5, "Excellent"),
                        ],
                        null=True,
                    ),
                ),
                ("feedback_text", models.TextField(blank=True)),
                ("business_plan_published", models.BooleanField(default=False)),
                ("funding_received", models.BooleanField(default=False)),
                ("business_launched", models.BooleanField(default=False)),
                ("user_agent", models.TextField(blank=True)),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("referrer", models.URLField(blank=True)),
                (
                    "template",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="usage_analytics",
                        to="incubator.businessplantemplate",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "template_usage_analytics",
                "indexes": [
                    models.Index(
                        fields=["template", "viewed_at"],
                        name="template_us_templat_8f072f_idx",
                    ),
                    models.Index(
                        fields=["user", "viewed_at"],
                        name="template_us_user_id_ad19f2_idx",
                    ),
                    models.Index(
                        fields=["completed_at"], name="template_us_complet_38ffb3_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="TemplateSectionAnalytics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("section_key", models.CharField(max_length=100)),
                ("section_title", models.CharField(max_length=200)),
                ("total_views", models.IntegerField(default=0)),
                ("total_completions", models.IntegerField(default=0)),
                ("average_time_spent", models.DurationField(blank=True, null=True)),
                ("completion_rate", models.FloatField(default=0.0)),
                ("ai_assistance_used", models.IntegerField(default=0)),
                ("content_regenerated", models.IntegerField(default=0)),
                ("section_customized", models.IntegerField(default=0)),
                ("average_content_length", models.IntegerField(default=0)),
                ("user_satisfaction_score", models.FloatField(default=0.0)),
                ("last_updated", models.DateTimeField(auto_now=True)),
                (
                    "template",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="section_analytics",
                        to="incubator.businessplantemplate",
                    ),
                ),
            ],
            options={
                "db_table": "template_section_analytics",
                "indexes": [
                    models.Index(
                        fields=["template", "completion_rate"],
                        name="template_se_templat_727e10_idx",
                    ),
                    models.Index(
                        fields=["section_key", "completion_rate"],
                        name="template_se_section_46cbc8_idx",
                    ),
                ],
                "unique_together": {("template", "section_key")},
            },
        ),
        migrations.CreateModel(
            name="TemplateRecommendation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("recommendation_score", models.FloatField()),
                ("recommendation_reason", models.TextField()),
                (
                    "recommendation_type",
                    models.CharField(
                        choices=[
                            ("similar_users", "Similar Users"),
                            ("industry_match", "Industry Match"),
                            ("business_type", "Business Type"),
                            ("completion_rate", "High Completion Rate"),
                            ("success_rate", "High Success Rate"),
                            ("ai_generated", "AI Generated"),
                        ],
                        max_length=50,
                    ),
                ),
                ("shown_at", models.DateTimeField(auto_now_add=True)),
                ("clicked_at", models.DateTimeField(blank=True, null=True)),
                ("selected_at", models.DateTimeField(blank=True, null=True)),
                ("dismissed_at", models.DateTimeField(blank=True, null=True)),
                ("recommendation_context", models.JSONField(blank=True, default=dict)),
                (
                    "recommended_template",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="recommendations",
                        to="incubator.businessplantemplate",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "template_recommendation",
                "indexes": [
                    models.Index(
                        fields=["user", "shown_at"],
                        name="template_re_user_id_0ffccf_idx",
                    ),
                    models.Index(
                        fields=["recommendation_score"],
                        name="template_re_recomme_0350d1_idx",
                    ),
                ],
                "unique_together": {("user", "recommended_template")},
            },
        ),
    ]
