/* Custom styles for BusinessPlanEditor ReactQuill integration with Glass Morphism */

/* Glass morphism theme adjustments for ReactQuill */
.business-plan-editor .ql-toolbar {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  border-left: 1px solid rgba(255, 255, 255, 0.2);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.business-plan-editor .ql-container {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  border-left: 1px solid rgba(255, 255, 255, 0.2);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  font-size: 14px;
  line-height: 1.6;
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.business-plan-editor .ql-editor {
  min-height: 350px;
  padding: 16px;
  color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

.business-plan-editor .ql-editor.ql-blank::before {
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
}

/* Improve focus states with glass morphism */
.business-plan-editor .ql-editor:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2);
}

/* Better spacing for lists */
.business-plan-editor .ql-editor ol,
.business-plan-editor .ql-editor ul {
  margin: 12px 0;
  padding-left: 24px;
}

.business-plan-editor .ql-editor li {
  margin: 4px 0;
}

/* Improve blockquote styling with glass morphism */
.business-plan-editor .ql-editor blockquote {
  border-left: 4px solid rgba(255, 255, 255, 0.2);
  margin: 16px 0;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  font-style: italic;
  color: rgba(255, 255, 255, 0.7);
}

/* Better link styling with glass morphism */
.business-plan-editor .ql-editor a {
  color: rgba(255, 255, 255, 0.9);
  text-decoration: underline;
}

.business-plan-editor .ql-editor a:hover {
  color: #ffffff;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .business-plan-editor .ql-toolbar {
    padding: 8px;
    flex-wrap: wrap;
  }

  .business-plan-editor .ql-editor {
    min-height: 250px;
    padding: 12px;
    font-size: 16px; /* Prevent zoom on iOS */
  }

  .auto-save-indicator {
    font-size: 10px;
    padding: 2px 6px;
  }

  .keyboard-shortcuts-tooltip {
    font-size: 10px;
    padding: 8px;
  }
}

/* RTL support with glass morphism */
.business-plan-editor[dir="rtl"] .ql-editor {
  text-align: right;
  direction: rtl;
}

.business-plan-editor[dir="rtl"] .ql-editor ol,
.business-plan-editor[dir="rtl"] .ql-editor ul {
  padding-right: 24px;
  padding-left: 0;
}

.business-plan-editor[dir="rtl"] .ql-editor blockquote {
  border-right: 4px solid rgba(255, 255, 255, 0.2);
  border-left: none;
}

/* Smooth transitions */
.business-plan-editor .ql-container,
.business-plan-editor .ql-toolbar {
  transition: border-color 0.2s ease-in-out;
}

/* Loading state for editor */
.business-plan-editor.loading .ql-editor {
  opacity: 0.6;
  pointer-events: none;
}

/* Auto-save indicator */
.auto-save-indicator {
  position: absolute;
  top: 8px;
  background-color: rgba(34, 197, 94, 0.9);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
  z-index: 10;
}

.auto-save-indicator.show {
  opacity: 1;
}

.auto-save-indicator.error {
  background-color: rgba(239, 68, 68, 0.9);
}

/* RTL positioning handled by Tailwind classes in component */

/* Section navigation improvements */
.section-nav-item {
  transition: all 0.2s ease-in-out;
}

.section-nav-item:hover {
  transform: translateX(2px);
}

.section-nav-item.rtl:hover {
  transform: translateX(-2px);
}

/* Progress bar enhancements */
.progress-bar-container {
  position: relative;
  overflow: hidden;
}

.progress-bar-fill {
  transition: width 0.5s ease-in-out;
  background: linear-gradient(90deg, #7c3aed, #a855f7, #c084fc);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Keyboard shortcut tooltip */
.keyboard-shortcuts-tooltip {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 11px;
  line-height: 1.4;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Improved button states */
.action-button {
  transition: all 0.2s ease-in-out;
}

.action-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.action-button:active {
  transform: translateY(0);
}

/* Better focus indicators for accessibility with glass morphism */
.focus-visible {
  outline: 2px solid rgba(255, 255, 255, 0.2);
  outline-offset: 2px;
}
