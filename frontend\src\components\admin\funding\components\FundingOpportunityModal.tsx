import React, { useState, useEffect } from 'react';
import { DollarSign, Calendar, FileText, Target } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../hooks/useLanguage';
import { useCreateFundingOpportunity, useUpdateFundingOpportunity } from '../../../../hooks/useFunding';
import { FundingOpportunity } from '../../../../services/incubatorApi';
import { Button } from '../../../ui';
import { BaseModal } from '../../../common';
import { validateRequired, validatePositiveNumber, validateGreaterThan } from '../../../../utils/localeValidation';

interface FundingOpportunityModalProps {
  isOpen: boolean;
  onClose: () => void;
  opportunity: FundingOpportunity | null;
  isEditing: boolean;
}

interface FormData {
  title: string;
  description: string;
  funding_type: 'seed' | 'series_a' | 'series_b' | 'grant' | 'loan';
  min_amount: number;
  max_amount: number;
  equity_percentage: number;
  deadline: string;
  requirements: string;
  terms_and_conditions: string;
  status: 'active' | 'closed' | 'draft';
  investor_profile: number;
}

const FundingOpportunityModal: React.FC<FundingOpportunityModalProps> = ({
  isOpen,
  onClose,
  opportunity,
  isEditing
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  const createOpportunity = useCreateFundingOpportunity();
  const updateOpportunity = useUpdateFundingOpportunity();

  const [formData, setFormData] = useState<FormData>({
    title: '',
    description: '',
    funding_type: 'seed',
    min_amount: 0,
    max_amount: 0,
    equity_percentage: 0,
    deadline: '',
    requirements: '',
    terms_and_conditions: '',
    status: 'draft',
    investor_profile: 0
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeSection, setActiveSection] = useState('basic');

  // Initialize form data when opportunity changes
  useEffect(() => {
    if (isEditing && opportunity) {
      setFormData({
        title: opportunity.title || '',
        description: opportunity.description || '',
        funding_type: opportunity.funding_type || 'seed',
        min_amount: opportunity.min_amount || 0,
        max_amount: opportunity.max_amount || 0,
        equity_percentage: opportunity.equity_percentage || 0,
        deadline: opportunity.deadline ? opportunity.deadline.split('T')[0] : '',
        requirements: opportunity.requirements || '',
        terms_and_conditions: opportunity.terms_and_conditions || '',
        status: opportunity.status || 'draft',
        investor_profile: opportunity.investor_profile || 0
      });
    } else {
      // Reset form for create mode
      setFormData({
        title: '',
        description: '',
        funding_type: 'seed',
        min_amount: 0,
        max_amount: 0,
        equity_percentage: 0,
        deadline: '',
        requirements: '',
        terms_and_conditions: '',
        status: 'draft',
        investor_profile: 0
      });
    }
    setErrors({});
  }, [isEditing, opportunity]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? Number(value) : value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!validateRequired(formData.title)) {
      newErrors.title = t('validation.required', 'This field is required');
    }
    if (!validateRequired(formData.description)) {
      newErrors.description = t('validation.required', 'This field is required');
    }
    if (!validatePositiveNumber(formData.min_amount)) {
      newErrors.min_amount = t('validation.positiveNumber', 'Must be a positive number');
    }
    if (!validatePositiveNumber(formData.max_amount)) {
      newErrors.max_amount = t('validation.positiveNumber', 'Must be a positive number');
    }
    if (!validateGreaterThan(formData.max_amount, formData.min_amount)) {
      newErrors.max_amount = t('validation.maxGreaterThanMin', 'Maximum amount must be greater than minimum');
    }
    if (formData.equity_percentage < 0 || formData.equity_percentage > 100) {
      newErrors.equity_percentage = t('validation.percentage', 'Must be between 0 and 100');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      if (isEditing && opportunity) {
        await updateOpportunity.mutateAsync({
          id: opportunity.id,
          data: formData
        });
      } else {
        await createOpportunity.mutateAsync(formData);
      }
      onClose();
    } catch (error) {
      console.error('Failed to save funding opportunity:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const sections = [
    { id: 'basic', label: t('funding.basicInfo', 'Basic Information'), icon: FileText },
    { id: 'financial', label: t('funding.financialDetails', 'Financial Details'), icon: DollarSign },
    { id: 'terms', label: t('funding.termsAndConditions', 'Terms & Conditions'), icon: Target }
  ];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-indigo-900/90 rounded-xl shadow-lg w-full max-w-4xl max-h-[90vh] overflow-hidden">
        <div className="flex h-full">
          {/* Sidebar */}
          <div className="w-64 bg-indigo-800/50 border-r border-indigo-700/50 p-4">
            <h3 className="text-lg font-semibold text-white mb-4">
              {isEditing ? t('funding.editOpportunity', 'Edit Opportunity') : t('funding.createOpportunity', 'Create Opportunity')}
            </h3>
            <nav className="space-y-2">
              {sections.map((section) => {
                const Icon = section.icon;
                return (
                  <button
                    key={section.id}
                    onClick={() => setActiveSection(section.id)}
                    className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-colors ${
                      activeSection === section.id
                        ? 'bg-purple-600 text-white'
                        : 'text-gray-300 hover:bg-indigo-700/50'
                    }`}
                  >
                    <Icon size={16} />
                    {section.label}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Main Content */}
          <div className="flex-1 flex flex-col">
            {/* Header */}
            <div className="flex justify-between items-center p-6 border-b border-indigo-700/50">
              <h2 className="text-xl font-bold text-white">
                {activeSection === 'basic' ? t('funding.basicInfo', 'Basic Information') : 
                 activeSection === 'financial' ? t('funding.financialDetails', 'Financial Details') :
                 t('funding.termsAndConditions', 'Terms & Conditions')}
              </h2>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-white"
              >
                <X size={20} />
              </button>
            </div>

            {/* Form Content */}
            <div className="flex-1 overflow-y-auto p-6">
              <form onSubmit={handleSubmit} className="space-y-6">
                {activeSection === 'basic' && (
                  <>
                    {/* Basic Information */}
                    <div>
                      <label className="block text-gray-300 mb-2">
                        {t('funding.title', 'Opportunity Title')} *
                      </label>
                      <input
                        type="text"
                        name="title"
                        value={formData.title}
                        onChange={handleInputChange}
                        className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                        placeholder={t('funding.titlePlaceholder', 'Enter opportunity title')}
                      />
                      {errors.title && <p className="text-red-400 text-sm mt-1">{errors.title}</p>}
                    </div>

                    <div>
                      <label className="block text-gray-300 mb-2">
                        {t('funding.description', 'Description')} *
                      </label>
                      <textarea
                        name="description"
                        value={formData.description}
                        onChange={handleInputChange}
                        rows={4}
                        className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                        placeholder={t('funding.descriptionPlaceholder', 'Describe the funding opportunity...')}
                      />
                      {errors.description && <p className="text-red-400 text-sm mt-1">{errors.description}</p>}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-gray-300 mb-2">
                          {t('funding.type', 'Funding Type')} *
                        </label>
                        <select
                          name="funding_type"
                          value={formData.funding_type}
                          onChange={handleInputChange}
                          className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                        >
                          <option value="seed">{t('funding.type.seed', 'Seed')}</option>
                          <option value="series_a">{t('funding.type.seriesA', 'Series A')}</option>
                          <option value="series_b">{t('funding.type.seriesB', 'Series B')}</option>
                          <option value="grant">{t('funding.type.grant', 'Grant')}</option>
                          <option value="loan">{t('funding.type.loan', 'Loan')}</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-gray-300 mb-2">
                          {t('admin.status', 'Status')}
                        </label>
                        <select
                          name="status"
                          value={formData.status}
                          onChange={handleInputChange}
                          className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                        >
                          <option value="draft">{t('funding.status.draft', 'Draft')}</option>
                          <option value="active">{t('funding.status.active', 'Active')}</option>
                          <option value="closed">{t('funding.status.closed', 'Closed')}</option>
                        </select>
                      </div>
                    </div>

                    <div>
                      <label className="block text-gray-300 mb-2">
                        {t('funding.deadline', 'Application Deadline')}
                      </label>
                      <input
                        type="date"
                        name="deadline"
                        value={formData.deadline}
                        onChange={handleInputChange}
                        className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                      />
                    </div>
                  </>
                )}

                {activeSection === 'financial' && (
                  <>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-gray-300 mb-2">
                          {t('funding.minAmount', 'Minimum Amount ($)')} *
                        </label>
                        <input
                          type="number"
                          name="min_amount"
                          value={formData.min_amount}
                          onChange={handleInputChange}
                          min="0"
                          className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                        />
                        {errors.min_amount && <p className="text-red-400 text-sm mt-1">{errors.min_amount}</p>}
                      </div>

                      <div>
                        <label className="block text-gray-300 mb-2">
                          {t('funding.maxAmount', 'Maximum Amount ($)')} *
                        </label>
                        <input
                          type="number"
                          name="max_amount"
                          value={formData.max_amount}
                          onChange={handleInputChange}
                          min="0"
                          className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                        />
                        {errors.max_amount && <p className="text-red-400 text-sm mt-1">{errors.max_amount}</p>}
                      </div>
                    </div>

                    <div>
                      <label className="block text-gray-300 mb-2">
                        {t('funding.equityPercentage', 'Equity Percentage (%)')}
                      </label>
                      <input
                        type="number"
                        name="equity_percentage"
                        value={formData.equity_percentage}
                        onChange={handleInputChange}
                        min="0"
                        max="100"
                        step="0.1"
                        className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                      />
                      {errors.equity_percentage && <p className="text-red-400 text-sm mt-1">{errors.equity_percentage}</p>}
                    </div>
                  </>
                )}

                {activeSection === 'terms' && (
                  <>
                    <div>
                      <label className="block text-gray-300 mb-2">
                        {t('funding.requirements', 'Requirements')}
                      </label>
                      <textarea
                        name="requirements"
                        value={formData.requirements}
                        onChange={handleInputChange}
                        rows={4}
                        className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                        placeholder={t('funding.requirementsPlaceholder', 'List the requirements for this funding opportunity...')}
                      />
                    </div>

                    <div>
                      <label className="block text-gray-300 mb-2">
                        {t('funding.termsAndConditions', 'Terms and Conditions')}
                      </label>
                      <textarea
                        name="terms_and_conditions"
                        value={formData.terms_and_conditions}
                        onChange={handleInputChange}
                        rows={6}
                        className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                        placeholder={t('funding.termsPlaceholder', 'Enter the terms and conditions...')}
                      />
                    </div>
                  </>
                )}
              </form>
            </div>

            {/* Footer */}
            <div className="flex justify-end gap-3 p-6 border-t border-indigo-700/50">
              <Button
                type="button"
                onClick={onClose}
                variant="secondary"
                disabled={isSubmitting}
              >
                {t('common.cancel', 'Cancel')}
              </Button>
              <Button
                type="submit"
                onClick={handleSubmit}
                disabled={isSubmitting}
              >
                {isSubmitting ? t('common.saving', 'Saving...') : 
                 isEditing ? t('common.update', 'Update') : t('common.create', 'Create')}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FundingOpportunityModal;
