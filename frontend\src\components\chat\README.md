# Chat Components

This directory contains reusable components for the chat functionality in the application.

## Component Structure

### Main Components

- **~~EnhancedYasmeenChat~~**: ❌ **REMOVED** - Replaced by `ConsolidatedAIChat` in `/components/ai/`

### Page Components

- **ChatPageHeader**: The header section for the chat page with title and back button
- **ChatHeader**: The header for the chat interface with options like clear chat
- **MessageList**: Displays the list of messages in the chat
- **ChatInput**: Input area for typing and sending messages
- **ChatCapabilities**: Displays the capabilities of the chat assistant

## Usage

### Basic Chat Page

```tsx
import {
  ChatHeader,
  ChatInput,
  MessageList,
  ChatCapabilities,
  ChatPageHeader
} from '../components/chat';

const ChatPage = () => {
  // State and handlers...

  return (
    <div>
      <ChatPageHeader />

      <div className="chat-container">
        <ChatHeader onClearChat={handleClearChat} />
        <MessageList messages={messages} isLoading={isLoading} error={error} />
        <ChatInput
          inputMessage={inputMessage}
          setInputMessage={setInputMessage}
          handleSendMessage={handleSendMessage}
          handleKeyPress={handleKeyPress}
          isLoading={isLoading}
        />
      </div>

      <ChatCapabilities />
    </div>
  );
};
```

### Enhanced Chat Widget (DEPRECATED)

```tsx
// ❌ OLD WAY - DON'T USE (Component removed)
// import { EnhancedYasmeenChat } from '../components/chat';

// ✅ NEW WAY - Use ConsolidatedAIChat instead
import { ConsolidatedAIChat } from '../components/ai/ConsolidatedAIChat';

const SomePage = () => {
  return (
    <div>
      {/* Page content */}

      {/* Add consolidated AI chat widget */}
      <ConsolidatedAIChat
        businessIdeaId={123}
        userId={user.id}
        userName={user.first_name}
        language="auto"
      />
    </div>
  );
};
```

## Component Props

### ChatHeader
- `onClearChat`: Function to clear chat history

### MessageList
- `messages`: Array of chat messages
- `isLoading`: Boolean indicating if a message is being processed
- `error`: Error message string or null

### ChatInput
- `inputMessage`: Current input message
- `setInputMessage`: Function to update input message
- `handleSendMessage`: Function to send message
- `handleKeyPress`: Function to handle key press events
- `isLoading`: Boolean indicating if a message is being sent

### YasmeenChat
- `businessIdeaId`: Optional ID for business idea context
- `businessIdeaTitle`: Optional title for business idea context
- `initialOpen`: Boolean to control if chat is initially open

### EnhancedYasmeenChat
- Same props as YasmeenChat with additional file handling capabilities
