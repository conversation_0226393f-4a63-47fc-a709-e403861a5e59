import React, { useState, useEffect } from 'react';
import {
  FileText,
  Plus,
  Trash2,
  Edit,
  Save,
  X,
  ChevronDown,
  ChevronUp,
  Move,
  Copy,
  Share2,
  Eye,
  EyeOff,
  RefreshCw,
  Check,
  AlertCircle,
  HelpCircle,
  Sparkles
} from 'lucide-react';
import { useAppSelector } from '../../store/hooks';
import {
  BusinessPlanTemplate,
  TemplateSectionDefinition,
  CustomBusinessPlanTemplate,
  businessPlanTemplatesAPI,
  templateSectionDefinitionsAPI,
  customTemplatesAPI
} from '../../services/templateCustomizationApi';
import { useTranslation } from 'react-i18next';

interface TemplateCustomizerProps {
  onTemplateCreated?: (templateId: number) => void;
  onCancel?: () => void;
}

const TemplateCustomizer: React.FC<TemplateCustomizerProps> = ({ onTemplateCreated,
  onCancel
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { user } = useAppSelector(state => state.auth);

  // State for templates and sections
  const [baseTemplates, setBaseTemplates] = useState<BusinessPlanTemplate[]>([]);
  const [sectionDefinitions, setSectionDefinitions] = useState<TemplateSectionDefinition[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<BusinessPlanTemplate | null>(null);
  const [customizationOptions, setCustomizationOptions] = useState<any>(null);

  // State for the custom template being created
  const [customTemplate, setCustomTemplate] = useState<Partial<CustomBusinessPlanTemplate>>({
    name: '',
    description: '',
    sections: { sections: [] },
    custom_prompts: {},
    custom_instructions: {},
    is_public: false
  });

  // UI state
  const [loading, setLoading] = useState(false);
  const [templateLoading, setTemplateLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({});
  const [step, setStep] = useState<'select' | 'customize' | 'finalize'>('select');

  // Fetch base templates and section definitions
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Only fetch customizable templates
        const templates = await businessPlanTemplatesAPI.getTemplates(undefined, undefined, true);
        setBaseTemplates(templates);

        const sections = await templateSectionDefinitionsAPI.getSectionDefinitions();
        setSectionDefinitions(sections);

        setError(null);
      } catch (err) {
        console.error('Error fetching template data:', err);
        setError(t("common.failed.to.load", "Failed to load templates. Please try again."));
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Fetch customization options when a template is selected
  useEffect(() => {
    if (selectedTemplate) {
      const fetchCustomizationOptions = async () => {
        setTemplateLoading(true);
        try {
          const options = await businessPlanTemplatesAPI.getCustomizationOptions(selectedTemplate.id);
          setCustomizationOptions(options);

          // Initialize the custom template with the base template's sections
          setCustomTemplate(prev => ({
            ...prev,
            base_template: selectedTemplate.id,
            name: `Custom ${selectedTemplate.name}`,
            description: `Customized version of ${selectedTemplate.name}`,
            sections: selectedTemplate.sections
          }));

          // Initialize expanded sections
          const expanded: Record<string, boolean> = {};
          selectedTemplate.sections.sections.forEach((section: any, index: number) => {
            expanded[section.key] = index < 3; // Expand first 3 sections by default
          });
          setExpandedSections(expanded);

          setError(null);
        } catch (err) {
          console.error('Error fetching customization options:', err);
          setError(t("common.failed.to.load", "Failed to load customization options. Please try again."));
        } finally {
          setTemplateLoading(false);
        }
      };

      fetchCustomizationOptions();
    }
  }, [selectedTemplate]);

  const handleTemplateSelect = (templateId: number) => {
    const template = baseTemplates.find(t => t.id === templateId);
    if (template) {
      setSelectedTemplate(template);
      setStep('customize');
    }
  };

  const handleSectionToggle = (sectionKey: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionKey]: !prev[sectionKey]
    }));
  };

  const handleSectionMove = (sectionKey: string, direction: 'up' | 'down') => {
    const sections = [...customTemplate.sections.sections];
    const index = sections.findIndex(s => s.key === sectionKey);

    if (index === -1) return;

    if (direction === 'up' && index > 0) {
      // Move section up
      const temp = sections[index];
      sections[index] = sections[index - 1];
      sections[index - 1] = temp;
    } else if (direction === 'down' && index < sections.length - 1) {
      // Move section down
      const temp = sections[index];
      sections[index] = sections[index + 1];
      sections[index + 1] = temp;
    }

    // Update order property
    sections.forEach((section, i) => {
      section.order = i;
    });

    setCustomTemplate(prev => ({
      ...prev,
      sections: {
        ...prev.sections,
        sections
      }
    }));
  };

  const handleSectionRemove = (sectionKey: string) => {
    const sections = customTemplate.sections.sections.filter(s => s.key !== sectionKey);

    // Update order property
    sections.forEach((section, i) => {
      section.order = i;
    });

    setCustomTemplate(prev => ({
      ...prev,
      sections: {
        ...prev.sections,
        sections
      }
    }));
  };

  const handleSectionAdd = (sectionDefinition: TemplateSectionDefinition) => {
    const sections = [...customTemplate.sections.sections];
    const newSectionKey = `${sectionDefinition.key}_${Date.now()}`;

    sections.push({
      title: sectionDefinition.title,
      key: newSectionKey,
      order: sections.length,
      is_required: false,
      section_definition_key: sectionDefinition.key,
      default_content: sectionDefinition.default_content,
      additional_data: {}
    });

    setCustomTemplate(prev => ({
      ...prev,
      sections: {
        ...prev.sections,
        sections
      }
    }));

    // Expand the new section
    setExpandedSections(prev => ({
      ...prev,
      [newSectionKey]: true
    }));
  };

  const handleSectionTitleChange = (sectionKey: string, newTitle: string) => {
    const sections = [...customTemplate.sections.sections];
    const index = sections.findIndex(s => s.key === sectionKey);

    if (index === -1) return;

    sections[index] = {
      ...sections[index],
      title: newTitle
    };

    setCustomTemplate(prev => ({
      ...prev,
      sections: {
        ...prev.sections,
        sections
      }
    }));
  };

  const handleCustomPromptChange = (sectionKey: string, prompt: string) => {
    setCustomTemplate(prev => ({
      ...prev,
      custom_prompts: {
        ...prev.custom_prompts,
        [sectionKey]: prompt
      }
    }));
  };

  const handleCustomInstructionsChange = (sectionKey: string, instructions: string) => {
    setCustomTemplate(prev => ({
      ...prev,
      custom_instructions: {
        ...prev.custom_instructions,
        [sectionKey]: instructions
      }
    }));
  };

  const handleCreateTemplate = async () => {
    if (!customTemplate.name || !customTemplate.base_template) {
      setError(t("common.template.name.and", "Template name and base template are required"));
      return;
    }

    setLoading(true);
    try {
      const createdTemplate = await customTemplatesAPI.createCustomTemplate(customTemplate);
      setSuccess(t("common.custom.template.created", "Custom template created successfully!"));

      // Call the callback if provided
      if (onTemplateCreated) {
        onTemplateCreated(createdTemplate.id);
      }
    } catch (err) {
      console.error('Error creating custom template:', err);
      setError(t("common.failed.to.create", "Failed to create custom template. Please try again."));
    } finally {
      setLoading(false);
    }
  };

  const renderTemplateSelectionStep = () => (
    <div>
      <h3 className="text-lg font-medium mb-4">{t("common.select.a.base", "Select a Base Template")}</h3>

      {loading ? (
        <div className={`flex justify-center py-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <RefreshCw size={32} className="animate-spin text-purple-500" />
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {baseTemplates.map(template => (
            <div
              key={template.id}
              className="bg-gray-800 border border-gray-700 rounded-lg p-4 hover:border-purple-500 cursor-pointer transition-colors"
              onClick={() => handleTemplateSelect(template.id)}
            >
              <h4 className="font-medium text-lg mb-1">{template.name}</h4>
              <div className={`flex items-center text-sm text-gray-400 mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                <span className={`bg-indigo-900/50 text-indigo-300 px-2 py-0.5 rounded text-xs mr-2 ${isRTL ? "space-x-reverse" : ""}`}>
                  {template.template_type_display}
                </span>
                <span>{template.industry}</span>
              </div>
              <div className="text-gray-300 text-sm mb-3">{template.description}</div>
              <div className={`flex justify-between items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <span className="text-xs text-gray-400">
                  {template.sections.sections?.length || 0} sections
                </span>
                <button className="text-purple-400 hover:text-purple-300 text-sm">
                  Customize <Sparkles size={14} className={`inline ml-1 ${isRTL ? "space-x-reverse" : ""}`} />
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {error && (
        <div className={`mt-4 p-3 bg-red-900/50 border border-red-800 rounded-md text-red-200 flex items-start ${isRTL ? "flex-row-reverse" : ""}`}>
          <AlertCircle size={18} className={`mr-2 mt-0.5 flex-shrink-0 ${isRTL ? "flex-row-reverse" : ""}`} />
          <span>{error}</span>
        </div>
      )}

      <div className={`mt-6 flex justify-end ${isRTL ? "flex-row-reverse" : ""}`}>
        {onCancel && (
          <button
            onClick={onCancel}
            className={`px-4 py-2 text-gray-300 hover:text-white mr-2 ${isRTL ? "space-x-reverse" : ""}`}
          >
            Cancel
          </button>
        )}
      </div>
    </div>
  );

  const renderCustomizationStep = () => (
    <div>
      <h3 className="text-lg font-medium mb-4">{t("common.customize.template", "Customize Template")}</h3>

      {templateLoading ? (
        <div className={`flex justify-center py-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <RefreshCw size={32} className="animate-spin text-purple-500" />
        </div>
      ) : (
        <>
          <div className="mb-6">
            <div className="mb-4">
              <label htmlFor="template-name" className="block text-sm font-medium text-gray-300 mb-1">
                Template Name
              </label>
              <input
                id="template-name"
                type="text"
                value={customTemplate.name || ''}
                onChange={(e) => setCustomTemplate(prev => ({ ...prev, name: e.target.value }))}
                className="w-full p-2 bg-gray-800 border border-gray-700 rounded-md text-white"
                placeholder={t("common.enter.a.name", "Enter a name for your custom template")}
              />
            </div>

            <div className="mb-4">
              <label htmlFor="template-description" className="block text-sm font-medium text-gray-300 mb-1">
                Description
              </label>
              <textarea
                id="template-description"
                value={customTemplate.description || ''}
                onChange={(e) => setCustomTemplate(prev => ({ ...prev, description: e.target.value }))}
                className="w-full p-2 bg-gray-800 border border-gray-700 rounded-md text-white"
                placeholder={t("common.describe.your.custom", "Describe your custom template")}
                rows={3}
              />
            </div>

            <div className={`mb-4 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <input
                id="template-public"
                type="checkbox"
                checked={customTemplate.is_public || false}
                onChange={(e) => setCustomTemplate(prev => ({ ...prev, is_public: e.target.checked }))}
                className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`}
              />
              <label htmlFor="template-public" className="text-sm text-gray-300">
                Make this template public (visible to all users)
              </label>
            </div>
          </div>

          <div className="mb-6">
            <div className={`flex justify-between items-center mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
              <h4 className="font-medium">{t("common.template.sections", "Template Sections")}</h4>
              <div className="relative group">
                <button className="text-gray-400 hover:text-white">
                  <HelpCircle size={16} />
                </button>
                <div className="absolute right-0 w-64 bg-gray-900 border border-gray-700 rounded-md p-3 text-xs text-gray-300 hidden group-hover:block z-10">
                  Customize your template by reordering, removing, or adding sections. You can also add custom AI prompts and instructions for each section.
                </div>
              </div>
            </div>

            <div className="space-y-2 mb-4">
              {customTemplate.sections?.sections?.map((section: any) => (
                <div
                  key={section.key}
                  className="bg-gray-800 border border-gray-700 rounded-md overflow-hidden"
                >
                  <div
                    className={`flex justify-between items-center p-3 bg-gray-700 cursor-pointer ${isRTL ? "flex-row-reverse" : ""}`}
                    onClick={() => handleSectionToggle(section.key)}
                  >
                    <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                      <FileText size={16} className={`mr-2 text-gray-400 ${isRTL ? "space-x-reverse" : ""}`} />
                      <span>{section.title}</span>
                    </div>
                    <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleSectionMove(section.key, 'up');
                        }}
                        className="p-1 text-gray-400 hover:text-white"
                        title={t("common.move.up", "Move up")}
                      >
                        <ChevronUp size={16} />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleSectionMove(section.key, 'down');
                        }}
                        className="p-1 text-gray-400 hover:text-white"
                        title={t("common.move.down", "Move down")}
                      >
                        <ChevronDown size={16} />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleSectionRemove(section.key);
                        }}
                        className="p-1 text-gray-400 hover:text-white"
                        title={t("common.remove.section", "Remove section")}
                      >
                        <Trash2 size={16} />
                      </button>
                      {expandedSections[section.key] ? (
                        <ChevronUp size={16} />
                      ) : (
                        <ChevronDown size={16} />
                      )}
                    </div>
                  </div>

                  {expandedSections[section.key] && (
                    <div className="p-3">
                      <div className="mb-3">
                        <label className="block text-sm font-medium text-gray-300 mb-1">
                          Section Title
                        </label>
                        <input
                          type="text"
                          value={section.title}
                          onChange={(e) => handleSectionTitleChange(section.key, e.target.value)}
                          className="w-full p-2 bg-gray-700 border border-gray-600 rounded-md text-white"
                        />
                      </div>

                      <div className="mb-3">
                        <label className="block text-sm font-medium text-gray-300 mb-1">
                          Custom AI Prompt
                        </label>
                        <textarea
                          value={customTemplate.custom_prompts?.[section.key] || ''}
                          onChange={(e) => handleCustomPromptChange(section.key, e.target.value)}
                          className="w-full p-2 bg-gray-700 border border-gray-600 rounded-md text-white"
                          placeholder={t("common.enter.a.custom", "Enter a custom prompt for AI to generate this section")}
                          rows={3}
                        />
                        <div className="text-xs text-gray-400 mt-1">
                          Custom prompt for AI to generate this section content
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-1">
                          Custom Instructions
                        </label>
                        <textarea
                          value={customTemplate.custom_instructions?.[section.key] || ''}
                          onChange={(e) => handleCustomInstructionsChange(section.key, e.target.value)}
                          className="w-full p-2 bg-gray-700 border border-gray-600 rounded-md text-white"
                          placeholder={t("common.enter.custom.instructions", "Enter custom instructions for this section")}
                          rows={2}
                        />
                        <div className="text-xs text-gray-400 mt-1">
                          Instructions shown to users when filling out this section
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>

            <div className="mt-4">
              <h4 className="font-medium mb-2">{t("common.add.section", "Add Section")}</h4>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {sectionDefinitions.map(section => (
                  <button
                    key={section.id}
                    onClick={() => handleSectionAdd(section)}
                    className={`p-2 bg-gray-800 hover:bg-gray-700 border border-gray-700 rounded-md text-sm flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}
                  >
                    <Plus size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                    {section.title}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </>
      )}

      {error && (
        <div className={`mt-4 p-3 bg-red-900/50 border border-red-800 rounded-md text-red-200 flex items-start ${isRTL ? "flex-row-reverse" : ""}`}>
          <AlertCircle size={18} className={`mr-2 mt-0.5 flex-shrink-0 ${isRTL ? "flex-row-reverse" : ""}`} />
          <span>{error}</span>
        </div>
      )}

      <div className={`mt-6 flex justify-end ${isRTL ? "flex-row-reverse" : ""}`}>
        <button
          onClick={() => setStep('select')}
          className={`px-4 py-2 text-gray-300 hover:text-white mr-2 ${isRTL ? "space-x-reverse" : ""}`}
        >
          Back
        </button>
        <button
          onClick={handleCreateTemplate}
          disabled={loading || !customTemplate.name}
          className={`px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-800/50 disabled:cursor-not-allowed rounded-md text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
        >
          {loading ? (
            <RefreshCw size={16} className={`mr-1 animate-spin ${isRTL ? "space-x-reverse" : ""}`} />
          ) : (
            <Save size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
          )}
          Create Template
        </button>
      </div>
    </div>
  );

  return (
    <div className="bg-gray-900 rounded-lg border border-gray-800 p-6">
      <div className="mb-6">
        <h2 className={`text-xl font-semibold flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
          <Sparkles size={20} className={`mr-2 text-purple-400 ${isRTL ? "space-x-reverse" : ""}`} />
          Business Plan Template Customizer
        </h2>
        <div className="text-gray-400 mt-1">
          Create a custom business plan template tailored to your specific needs
        </div>
      </div>

      {success ? (
        <div className="p-6 text-center">
          <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-900/30 mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            <Check size={32} className="text-green-400" />
          </div>
          <h3 className="text-xl font-medium mb-2">{t("common.template.created.successfully", "Template Created Successfully!")}</h3>
          <p className="text-gray-400 mb-6">
            Your custom template is now ready to use for creating business plans.
          </p>
          {onCancel && (
            <button
              onClick={onCancel}
              className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white"
            >
              Done
            </button>
          )}
        </div>
      ) : (
        <>
          {step === 'select' && renderTemplateSelectionStep()}
          {step === 'customize' && renderCustomizationStep()}
        </>
      )}
    </div>
  );
};

export default TemplateCustomizer;
