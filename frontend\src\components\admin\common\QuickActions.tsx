import React, { useState } from 'react';
import { Plus, Users, DollarSign, Calendar, FileText, Mail, Settings, Zap, Target, Building } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';
import { useNavigate } from 'react-router-dom';

interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  action: () => void;
  category: 'create' | 'manage' | 'communicate' | 'analyze';
  shortcut?: string;
}

interface QuickActionsProps {
  isOpen: boolean;
  onClose: () => void;
}

const QuickActions: React.FC<QuickActionsProps> = ({ isOpen, onClose }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Define quick actions
  const quickActions: QuickAction[] = [
    {
      id: 'create-funding-opportunity',
      title: t('quick.action.create.funding', 'Create Funding Opportunity'),
      description: t('quick.action.create.funding.desc', 'Add a new funding opportunity for startups'),
      icon: <DollarSign size={20} />,
      color: 'bg-green-500',
      category: 'create',
      shortcut: 'Ctrl+F',
      action: () => {
        navigate('/admin/incubator/funding-opportunities');
        onClose();
      }
    },
    {
      id: 'create-event',
      title: t('quick.action.create.event', 'Create Event'),
      description: t('quick.action.create.event.desc', 'Schedule a new event or workshop'),
      icon: <Calendar size={20} />,
      color: 'bg-blue-500',
      category: 'create',
      shortcut: 'Ctrl+E',
      action: () => {
        navigate('/admin/events');
        onClose();
      }
    },
    {
      id: 'create-post',
      title: t('quick.action.create.post', 'Create Post'),
      description: t('quick.action.create.post.desc', 'Publish a new blog post or announcement'),
      icon: <FileText size={20} />,
      color: 'bg-purple-500',
      category: 'create',
      shortcut: 'Ctrl+P',
      action: () => {
        navigate('/admin/posts');
        onClose();
      }
    },
    {
      id: 'manage-users',
      title: t('quick.action.manage.users', 'Manage Users'),
      description: t('quick.action.manage.users.desc', 'View and manage user accounts'),
      icon: <Users size={20} />,
      color: 'bg-indigo-500',
      category: 'manage',
      shortcut: 'Ctrl+U',
      action: () => {
        navigate('/admin/users');
        onClose();
      }
    },
    {
      id: 'review-applications',
      title: t('quick.action.review.applications', 'Review Applications'),
      description: t('quick.action.review.applications.desc', 'Review pending membership applications'),
      icon: <FileText size={20} />,
      color: 'bg-orange-500',
      category: 'manage',
      shortcut: 'Ctrl+R',
      action: () => {
        navigate('/admin/membership');
        onClose();
      }
    },
    {
      id: 'mentorship-matches',
      title: t('quick.action.mentorship.matches', 'Mentorship Matches'),
      description: t('quick.action.mentorship.matches.desc', 'Manage mentor-mentee relationships'),
      icon: <Target size={20} />,
      color: 'bg-cyan-500',
      category: 'manage',
      action: () => {
        navigate('/admin/incubator/mentorship-matches');
        onClose();
      }
    },
    {
      id: 'send-newsletter',
      title: t('quick.action.send.newsletter', 'Send Newsletter'),
      description: t('quick.action.send.newsletter.desc', 'Send newsletter to all subscribers'),
      icon: <Mail size={20} />,
      color: 'bg-pink-500',
      category: 'communicate',
      action: () => {
        // Open newsletter modal or navigate to newsletter page
        console.log('Send newsletter');
        onClose();
      }
    },
    {
      id: 'bulk-email',
      title: t('quick.action.bulk.email', 'Bulk Email'),
      description: t('quick.action.bulk.email.desc', 'Send email to selected users'),
      icon: <Mail size={20} />,
      color: 'bg-red-500',
      category: 'communicate',
      action: () => {
        // Open bulk email modal
        console.log('Bulk email');
        onClose();
      }
    },
    {
      id: 'view-analytics',
      title: t('quick.action.view.analytics', 'View Analytics'),
      description: t('quick.action.view.analytics.desc', 'Check platform performance metrics'),
      icon: <Zap size={20} />,
      color: 'bg-yellow-500',
      category: 'analyze',
      shortcut: 'Ctrl+A',
      action: () => {
        navigate('/admin/analytics');
        onClose();
      }
    },
    {
      id: 'business-ideas',
      title: t('quick.action.business.ideas', 'Business Ideas'),
      description: t('quick.action.business.ideas.desc', 'Review submitted business ideas'),
      icon: <Building size={20} />,
      color: 'bg-teal-500',
      category: 'manage',
      action: () => {
        navigate('/admin/incubator');
        onClose();
      }
    },
    {
      id: 'system-settings',
      title: t('quick.action.system.settings', 'System Settings'),
      description: t('quick.action.system.settings.desc', 'Configure platform settings'),
      icon: <Settings size={20} />,
      color: 'bg-gray-500',
      category: 'manage',
      action: () => {
        navigate('/admin/settings');
        onClose();
      }
    }
  ];

  // Filter actions by category
  const filteredActions = selectedCategory === 'all' 
    ? quickActions 
    : quickActions.filter(action => action.category === selectedCategory);

  // Categories
  const categories = [
    { value: 'all', label: t('quick.action.all', 'All'), icon: <Zap size={16} /> },
    { value: 'create', label: t('quick.action.create', 'Create'), icon: <Plus size={16} /> },
    { value: 'manage', label: t('quick.action.manage', 'Manage'), icon: <Settings size={16} /> },
    { value: 'communicate', label: t('quick.action.communicate', 'Communicate'), icon: <Mail size={16} /> },
    { value: 'analyze', label: t('quick.action.analyze', 'Analyze'), icon: <Zap size={16} /> }
  ];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-indigo-900/90 backdrop-blur-sm rounded-xl border border-indigo-800 w-full max-w-4xl max-h-[80vh] overflow-hidden">
        <div className="p-6">
          {/* Header */}
          <div className={`flex justify-between items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div>
              <h2 className="text-xl font-bold text-white">{t('quick.actions.title', 'Quick Actions')}</h2>
              <p className="text-gray-400 text-sm mt-1">
                {t('quick.actions.description', 'Quickly access common administrative tasks')}
              </p>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-indigo-800/50 rounded-lg transition-colors"
            >
              <Plus size={20} className="text-gray-400 rotate-45" />
            </button>
          </div>

          {/* Categories */}
          <div className="mb-6">
            <div className={`flex flex-wrap gap-2 ${isRTL ? "flex-row-reverse" : ""}`}>
              {categories.map((category) => (
                <button
                  key={category.value}
                  onClick={() => setSelectedCategory(category.value)}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-lg font-medium transition-all duration-300 ${
                    selectedCategory === category.value
                      ? 'bg-purple-600 text-white shadow-lg'
                      : 'bg-indigo-800/50 text-gray-300 hover:bg-indigo-700/50'
                  } ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}
                >
                  {category.icon}
                  <span>{category.label}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Actions Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
            {filteredActions.map((action) => (
              <button
                key={action.id}
                onClick={action.action}
                className="group p-4 bg-indigo-800/30 hover:bg-indigo-700/50 rounded-xl border border-indigo-700/50 hover:border-purple-500/50 transition-all duration-300 hover:shadow-glow text-left"
              >
                <div className={`flex items-start space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse text-right" : ""}`}>
                  <div className={`flex-shrink-0 w-10 h-10 ${action.color} rounded-lg flex items-center justify-center text-white group-hover:scale-110 transition-transform duration-300`}>
                    {action.icon}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="text-white font-medium mb-1 group-hover:text-purple-300 transition-colors">
                      {action.title}
                    </h3>
                    <p className="text-gray-400 text-sm line-clamp-2">
                      {action.description}
                    </p>
                    {action.shortcut && (
                      <div className="mt-2">
                        <span className="inline-block px-2 py-1 bg-indigo-900/50 rounded text-xs text-gray-400 font-mono">
                          {action.shortcut}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </button>
            ))}
          </div>

          {filteredActions.length === 0 && (
            <div className="text-center py-12">
              <Zap size={48} className="mx-auto text-gray-600 mb-4" />
              <h3 className="text-lg font-medium text-gray-400 mb-2">
                {t('quick.actions.no.actions', 'No actions found')}
              </h3>
              <p className="text-gray-500">
                {t('quick.actions.try.different.category', 'Try selecting a different category')}
              </p>
            </div>
          )}

          {/* Footer */}
          <div className="mt-6 pt-4 border-t border-indigo-800/50">
            <div className="text-center">
              <p className="text-gray-400 text-sm">
                {t('quick.actions.tip', 'Tip: Use keyboard shortcuts for faster access')}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Quick Actions Hook
export const useQuickActions = () => {
  const [isOpen, setIsOpen] = useState(false);

  // Keyboard shortcuts
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Open quick actions with Ctrl+K or Cmd+K
      if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault();
        setIsOpen(true);
      }
      
      // Close with Escape
      if (event.key === 'Escape') {
        setIsOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  return {
    isOpen,
    setIsOpen
  };
};

export default QuickActions;
