import React from 'react';
import TokenErrorHandler from '../components/auth/TokenErrorHandler';

const TokenRecoveryPage: React.FC = () => {
  return (
    <TokenErrorHandler 
      error="Given token not valid for any token type"
      onRetry={() => {
        // Redirect to dashboard after successful token refresh
        window.location.href = '/dashboard';
      }}
    />
  );
};

export default TokenRecoveryPage;
