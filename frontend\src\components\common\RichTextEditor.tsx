import React, { useEffect } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

const modules = {
  toolbar: [
    [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
    ['bold', 'italic', 'underline', 'strike'],
    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
    [{ 'color': [] }, { 'background': [] }],
    ['link', 'image'],
    ['clean']
  ],
};

const formats = [
  'header',
  'bold', 'italic', 'underline', 'strike',
  'list', 'bullet',
  'color', 'background',
  'link', 'image'
];

const RichTextEditor: React.FC<RichTextEditorProps> = ({
  value,
  onChange,
  placeholder,
  className = ''
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const defaultPlaceholder = placeholder || t("common.write.something", "Write something...");

  // Load CSS dynamically to avoid SSR issues
  useEffect(() => {
    import('react-quill/dist/quill.snow.css');
  }, []);

  return (
    <div className={`rich-text-editor ${className} ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
      <ReactQuill
        theme="snow"
        value={value}
        onChange={onChange}
        modules={modules}
        formats={formats}
        placeholder={defaultPlaceholder}
        className="bg-indigo-950/50 rounded-lg text-white"
      />
      <style>{`
        .rich-text-editor .ql-toolbar {
          background-color: rgba(67, 56, 202, 0.3);
          border-color: rgba(99, 102, 241, 0.4) !important;
          border-top-left-radius: 0.5rem;
          border-top-right-radius: 0.5rem;
        }
        .rich-text-editor .ql-container {
          border-color: rgba(99, 102, 241, 0.4) !important;
          border-bottom-left-radius: 0.5rem;
          border-bottom-right-radius: 0.5rem;
          min-height: 150px;
        }
        .rich-text-editor .ql-editor {
          min-height: 150px;
        }
        .rich-text-editor .ql-editor.ql-blank::before {
          color: rgba(255, 255, 255, 0.5);
        }
        .rich-text-editor .ql-picker {
          color: white !important;
        }
        .rich-text-editor .ql-picker-options {
          background-color: #1e1b4b !important;
          border-color: rgba(99, 102, 241, 0.4) !important;
        }
        .rich-text-editor .ql-stroke {
          stroke: white !important;
        }
        .rich-text-editor .ql-fill {
          fill: white !important;
        }
        .rich-text-editor .ql-picker-label {
          color: white !important;
        }
        .rich-text-editor.rtl .ql-editor {
          text-align: right;
          direction: rtl;
        }
        .rich-text-editor.rtl .ql-toolbar {
          direction: rtl;
        }
      `}</style>
    </div>
  );
};

export default RichTextEditor;
