import React, { useState, useEffect, useRef } from 'react';
import { Search as SearchIcon, X, Loader2 } from 'lucide-react';
import { api } from '../../services/api';
import { useLanguage } from '../../hooks/useLanguage';
import { useTranslation } from 'react-i18next';
interface SearchResult {
  id: number;
  content_type: string;
  object_id: number;
  title: string;
  title_highlighted?: string;
  content_highlighted?: string;
  author: string;
  created_at: string;
  score?: number;
}

interface SearchBarProps {
  onResultSelect?: (result: SearchResult) => void;
  placeholder?: string;
  className?: string;
  autoFocus?: boolean;
}

const SearchBar: React.FC<SearchBarProps> = ({
  onResultSelect,
  placeholder,
  className = '',
  autoFocus = false
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const defaultPlaceholder = placeholder || t("common.search", "Search...");
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Handle click outside to close results
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Auto focus
  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  // Debounced search
  useEffect(() => {
    if (!query.trim()) {
      setResults([]);
      setIsOpen(false);
      return;
    }

    const timer = setTimeout(() => {
      performSearch();
    }, 300);

    return () => clearTimeout(timer);
  }, [query]);

  const performSearch = async () => {
    if (!query.trim()) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`${api.API_URL}/search/query/?q=${encodeURIComponent(query)}&limit=5`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('yasmeen_auth_token')}`
        }
      });

      if (!response.ok) {
        throw new Error(t("common.search.failed", "Search failed"));
      }

      const data = await response.json();

      // Handle different response formats
      if (data && typeof data === 'object') {
        if ('results' in data && Array.isArray(data.results)) {
          // Paginated response
          setResults(data.results);
        } else if (Array.isArray(data)) {
          // Direct array response
          setResults(data);
        } else {
          console.error("Unexpected search response format:", data);
          setResults([]);
        }
      } else {
        console.error("Invalid search response:", data);
        setResults([]);
      }

      setIsOpen(true);
    } catch (err) {
      console.error('Search error:', err);
      setError(t("common.failed.to.perform", "Failed to perform search"));
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setQuery(e.target.value);
  };

  const handleClear = () => {
    setQuery('');
    setResults([]);
    setIsOpen(false);
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  const handleResultClick = (result: SearchResult) => {
    if (onResultSelect) {
      onResultSelect(result);
    }
    setIsOpen(false);
  };

  const getContentTypeIcon = (type: string) => {
    switch (type) {
      case 'post':
        return '📝';
      case 'event':
        return '📅';
      case 'resource':
        return '📚';
      case 'user':
        return '👤';
      default:
        return '📄';
    }
  };

  return (
    <div className={`relative ${className}`} ref={searchRef}>
      <div className="relative">
        <div className={`absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none ${isRTL ? "flex-row-reverse" : ""}`}>
          {isLoading ? (
            <Loader2 size={18} className="text-gray-400 animate-spin" />
          ) : (
            <SearchIcon size={18} className="text-gray-400" />
          )}
        </div>
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          placeholder={defaultPlaceholder}
          className="w-full py-2 pl-10 pr-10 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
          onFocus={() => query.trim() && setIsOpen(true)}
        />
        {query && (
          <button
            className={`absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-white ${isRTL ? "flex-row-reverse" : ""}`}
            onClick={handleClear}
          >
            <X size={18} />
          </button>
        )}
      </div>

      {isOpen && results.length > 0 && (
        <div className="absolute z-50 w-full mt-1 bg-indigo-900 border border-indigo-700 rounded-lg shadow-lg max-h-96 overflow-y-auto">
          <ul className="py-1">
            {results.map((result) => (
              <li
                key={`${result.content_type}-${result.object_id}`}
                className="px-4 py-2 hover:bg-indigo-800 cursor-pointer"
                onClick={() => handleResultClick(result)}
              >
                <div className={`flex items-start ${isRTL ? "flex-row-reverse" : ""}`}>
                  <span className={`mr-2 text-lg ${isRTL ? "space-x-reverse" : ""}`}>{getContentTypeIcon(result.content_type)}</span>
                  <div className={`flex-1 min-w-0 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div
                      className="font-medium text-white"
                      dangerouslySetInnerHTML={{ __html: result.title_highlighted || result.title }}
                    />
                    {result.content_highlighted && (
                      <div
                        className="text-sm text-gray-300 mt-1 line-clamp-2"
                        dangerouslySetInnerHTML={{ __html: result.content_highlighted }}
                      />
                    )}
                    <div className="text-xs text-gray-400 mt-1">
                      {result.author && <span>By {result.author} • </span>}
                      <span>{new Date(result.created_at).toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}

      {isOpen && results.length === 0 && !isLoading && query.trim() && (
        <div className="absolute z-50 w-full mt-1 bg-indigo-900 border border-indigo-700 rounded-lg shadow-lg p-4 text-center">
          <div className="text-gray-300">{t("common.no.results.found", "No results found")}</div>
        </div>
      )}

      {error && (
        <div className="absolute z-50 w-full mt-1 bg-red-900/70 border border-red-700 rounded-lg shadow-lg p-4 text-center">
          <p className="text-red-200">{error}</p>
        </div>
      )}
    </div>
  );
};

export default SearchBar;
