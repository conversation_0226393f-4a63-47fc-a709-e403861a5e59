/**
 * Template Comparison Tool
 * Side-by-side comparison of business plan templates
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  GitCompare,
  Star,
  Clock,
  Users,
  Target,
  CheckCircle,
  X,
  Plus,
  TrendingUp,
  Award,
  Zap,
  Eye,
  Download
} from 'lucide-react';
import { RTLText, RTLFlex } from '../common';

interface TemplateComparisonData {
  id: string;
  name: string;
  description: string;
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime: number;
  sections: number;
  popularity: number;
  completionRate: number;
  averageRating: number;
  totalRatings: number;
  usageCount: number;
  successRate: number;
  features: string[];
  pros: string[];
  cons: string[];
  bestFor: string[];
  icon: React.ComponentType<any>;
  isNew?: boolean;
  isPremium?: boolean;
  price?: number;
}

interface TemplateComparisonToolProps {
  templates: TemplateComparisonData[];
  onSelectTemplate?: (templateId: string) => void;
  onPreviewTemplate?: (templateId: string) => void;
  maxComparisons?: number;
}

const TemplateComparisonTool: React.FC<TemplateComparisonToolProps> = ({ templates,
  onSelectTemplate,
  onPreviewTemplate,
  maxComparisons = 3
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [selectedTemplates, setSelectedTemplates] = useState<TemplateComparisonData[]>([]);
  const [availableTemplates, setAvailableTemplates] = useState<TemplateComparisonData[]>(templates);
  const [showAddModal, setShowAddModal] = useState(false);

  useEffect(() => {
    setAvailableTemplates(templates.filter(t => !selectedTemplates.find(s => s.id === t.id)));
  }, [templates, selectedTemplates]);

  const addTemplate = (template: TemplateComparisonData) => {
    if (selectedTemplates.length < maxComparisons) {
      setSelectedTemplates([...selectedTemplates, template]);
      setShowAddModal(false);
    }
  };

  const removeTemplate = (templateId: string) => {
    setSelectedTemplates(selectedTemplates.filter(t => t.id !== templateId));
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'text-green-400 bg-green-400/10';
      case 'intermediate': return 'text-yellow-400 bg-yellow-400/10';
      case 'advanced': return 'text-red-400 bg-red-400/10';
      default: return 'text-gray-400 bg-gray-400/10';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-400';
    if (score >= 80) return 'text-yellow-400';
    if (score >= 70) return 'text-orange-400';
    return 'text-red-400';
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        size={12}
        className={i < rating ? 'text-yellow-400 fill-current' : 'text-gray-600'}
      />
    ));
  };

  const renderMetricComparison = (label: string, values: (number | string)[], unit?: string, isHigherBetter: boolean = true) => {
    const numericValues = values.map(v => typeof v === 'string' ? parseFloat(v) || 0 : v);
    const bestValue = isHigherBetter ? Math.max(...numericValues) : Math.min(...numericValues);

    return (
      <div className="py-3 border-b border-gray-700 last:border-b-0">
        <div className="text-sm font-medium text-gray-300 mb-2">{label}</div>
        <div className="grid grid-cols-1 gap-2" style={{ gridTemplateColumns: `repeat(${selectedTemplates.length}, 1fr)` }}>
          {values.map((value, index) => {
            const numericValue = typeof value === 'string' ? parseFloat(value) || 0 : value;
            const isBest = numericValue === bestValue;

            return (
              <div
                key={index}
                className={`text-center p-2 rounded ${
                  isBest ? 'bg-green-600/20 text-green-400' : 'text-gray-400'}
                }`}
              >
                <span className="font-semibold">
                  {typeof value === 'number' ? value.toFixed(1) : value}
                  {unit}
                </span>
                {isBest && <CheckCircle size={14} className={`inline ml-1 ${isRTL ? "space-x-reverse" : ""}`} />}
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <RTLFlex className="items-center justify-between">
        <div>
          <RTLFlex className="items-center mb-2">
            <GitCompare size={24} className={`text-purple-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
            <RTLText as="h3" className="text-xl font-bold">
              Template Comparison
            </RTLText>
          </RTLFlex>
          <p className="text-gray-400 text-sm">
            Compare templates side-by-side to find the perfect fit for your business
          </p>
        </div>
      </RTLFlex>

      {/* Selected Templates */}
      {selectedTemplates.length > 0 ? (
        <div className="bg-gray-800/50 rounded-lg p-6">
          {/* Template Headers */}
          <div className="grid gap-4 mb-6" style={{ gridTemplateColumns: `repeat(${selectedTemplates.length}, 1fr)` }}>
            {selectedTemplates.map((template) => {
              const Icon = template.icon;

              return (
                <div key={template.id} className="bg-gray-700/50 rounded-lg p-4 relative">
                  {/* Remove Button */}
                  <button
                    onClick={() => removeTemplate(template.id)}
                    className="absolute top-2 right-2 p-1 bg-gray-600 hover:bg-red-600 rounded-full text-gray-300 hover:text-white transition-colors"
                  >
                    <X size={14} />
                  </button>

                  {/* Badges */}
                  <div className={`flex gap-1 mb-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                    {template.isNew && (
                      <span className="px-2 py-1 bg-green-600 text-white text-xs rounded-full">
                        New
                      </span>
                    )}
                    {template.isPremium && (
                      <span className="px-2 py-1 bg-yellow-600 text-white text-xs rounded-full">
                        Premium
                      </span>
                    )}
                  </div>

                  {/* Template Info */}
                  <div className="text-center">
                    <div className="p-3 bg-purple-600/20 rounded-lg w-fit mx-auto mb-3">
                      <Icon size={24} className="text-purple-400" />
                    </div>
                    <RTLText as="h4" className="font-semibold text-lg mb-1">
                      {template.name}
                    </RTLText>
                    <div className={`flex items-center justify-center mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                      {renderStars(template.popularity)}
                      <span className={`text-xs text-gray-400 ml-1 ${isRTL ? "space-x-reverse" : ""}`}>
                        ({template.totalRatings})
                      </span>
                    </div>
                    <span className={`px-2 py-1 rounded text-xs ${getDifficultyColor(template.difficulty)}`}>
                      {template.difficulty}
                    </span>
                  </div>

                  {/* Action Buttons */}
                  <div className={`flex gap-2 mt-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                    {onPreviewTemplate && (
                      <button
                        onClick={() => onPreviewTemplate(template.id)}
                        className={`flex-1 px-3 py-2 bg-gray-600 hover:bg-gray-500 text-white rounded text-sm transition-colors flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}
                      >
                        <Eye size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                        Preview
                      </button>
                    )}
                    {onSelectTemplate && (
                      <button
                        onClick={() => onSelectTemplate(template.id)}
                        className={`flex-1 px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded text-sm transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
                      >
                        Select
                      </button>
                    )}
                  </div>
                </div>
              );
            })}
          </div>

          {/* Comparison Metrics */}
          <div className="space-y-4">
            <RTLText as="h4" className="text-lg font-semibold mb-4">
              Key Metrics Comparison
            </RTLText>

            {renderMetricComparison(
              'Estimated Time (hours)',
              selectedTemplates.map(t => t.estimatedTime),
              'h',
              false
            )}

            {renderMetricComparison(
              t("common.number.of.sections", "Number of Sections"),
              selectedTemplates.map(t => t.sections)
            )}

            {renderMetricComparison(
              t("common.average.rating", "Average Rating"),
              selectedTemplates.map(t => t.averageRating),
              '/5'
            )}

            {renderMetricComparison(
              t("common.completion.rate", "Completion Rate"),
              selectedTemplates.map(t => t.completionRate),
              '%'
            )}

            {renderMetricComparison(
              t("common.success.rate", "Success Rate"),
              selectedTemplates.map(t => t.successRate),
              '%'
            )}

            {renderMetricComparison(
              t("common.total.usage", "Total Usage"),
              selectedTemplates.map(t => t.usageCount)
            )}
          </div>

          {/* Features Comparison */}
          <div className="mt-6">
            <RTLText as="h4" className="text-lg font-semibold mb-4">
              Features & Benefits
            </RTLText>

            <div className="grid gap-6" style={{ gridTemplateColumns: `repeat(${selectedTemplates.length}, 1fr)` }}>
              {selectedTemplates.map((template) => (
                <div key={template.id} className="space-y-4">
                  {/* Features */}
                  <div>
                    <div className="text-sm font-medium text-gray-300 mb-2">t("common.features", "Features")</div>
                    <div className="space-y-1">
                      {template.features.map((feature, index) => (
                        <div key={index} className={`flex items-start text-xs text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                          <CheckCircle size={12} className={`text-green-400 mr-1 mt-0.5 flex-shrink-0 ${isRTL ? "flex-row-reverse" : ""}`} />
                          <span>{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Best For */}
                  <div>
                    <div className="text-sm font-medium text-gray-300 mb-2">t("common.best.for", "Best For")</div>
                    <div className="space-y-1">
                      {template.bestFor.map((item, index) => (
                        <div key={index} className={`flex items-start text-xs text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                          <Target size={12} className={`text-blue-400 mr-1 mt-0.5 flex-shrink-0 ${isRTL ? "flex-row-reverse" : ""}`} />
                          <span>{item}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Pros */}
                  <div>
                    <div className="text-sm font-medium text-green-400 mb-2">t("common.pros", "Pros")</div>
                    <div className="space-y-1">
                      {template.pros.map((pro, index) => (
                        <div key={index} className={`flex items-start text-xs text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                          <Plus size={12} className={`text-green-400 mr-1 mt-0.5 flex-shrink-0 ${isRTL ? "flex-row-reverse" : ""}`} />
                          <span>{pro}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Cons */}
                  <div>
                    <div className="text-sm font-medium text-red-400 mb-2">t("common.considerations", "Considerations")</div>
                    <div className="space-y-1">
                      {template.cons.map((con, index) => (
                        <div key={index} className={`flex items-start text-xs text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                          <X size={12} className={`text-red-400 mr-1 mt-0.5 flex-shrink-0 ${isRTL ? "flex-row-reverse" : ""}`} />
                          <span>{con}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      ) : (
        <div className="bg-gray-800/50 rounded-lg p-12 text-center">
          <GitCompare size={48} className="text-gray-600 mx-auto mb-4" />
          <RTLText as="h4" className="text-lg font-semibold text-gray-400 mb-2">
            No Templates Selected
          </RTLText>
          <p className="text-gray-500 mb-4">
            Add templates to compare their features, metrics, and benefits side-by-side.
          </p>
        </div>
      )}

      {/* Add Template Button */}
      {selectedTemplates.length < maxComparisons && (
        <div className="text-center">
          <button
            onClick={() => setShowAddModal(true)}
            className={`px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors flex items-center mx-auto ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <Plus size={20} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
            Add Template to Compare
          </button>
        </div>
      )}

      {/* Add Template Modal */}
      {showAddModal && (
        <div className={`fixed inset-0 bg-black/50 flex items-center justify-center z-50 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="bg-gray-800 rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <RTLFlex className="items-center justify-between mb-4">
              <RTLText as="h3" className="text-lg font-semibold">
                Select Template to Compare
              </RTLText>
              <button
                onClick={() => setShowAddModal(false)}
                className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
              >
                <X size={20} />
              </button>
            </RTLFlex>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {availableTemplates.map((template) => {
                const Icon = template.icon;

                return (
                  <div
                    key={template.id}
                    onClick={() => addTemplate(template)}
                    className="bg-gray-700/50 rounded-lg p-4 cursor-pointer transition-all hover:bg-gray-600/50 border border-gray-600 hover:border-purple-500"
                  >
                    <div className="text-center">
                      <div className="p-2 bg-purple-600/20 rounded-lg w-fit mx-auto mb-2">
                        <Icon size={20} className="text-purple-400" />
                      </div>
                      <RTLText as="h4" className="font-semibold mb-1">
                        {template.name}
                      </RTLText>
                      <div className={`flex items-center justify-center mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                        {renderStars(template.popularity)}
                      </div>
                      <span className={`px-2 py-1 rounded text-xs ${getDifficultyColor(template.difficulty)}`}>
                        {template.difficulty}
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TemplateComparisonTool;
