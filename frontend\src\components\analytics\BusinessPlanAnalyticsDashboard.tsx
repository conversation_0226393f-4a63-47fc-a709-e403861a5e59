import React, { useState, useEffect } from 'react';
import {
  Clock,
  Users,
  Download,
  TrendingUp,
  FileText,
  BarChart3,
  Calendar,
  Target,
  Award,
  Activity
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import {
  businessPlanAnalyticsAPI,
  DashboardOverview,
  TemplateUsageAnalytics
} from '../../services/businessPlanAnalyticsApi';

interface BusinessPlanAnalyticsDashboardProps {
  businessPlanId?: number;
  dateRange?: number;
}

const BusinessPlanAnalyticsDashboard: React.FC<BusinessPlanAnalyticsDashboardProps> = ({
  businessPlanId,
  dateRange = 30
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  const [overview, setOverview] = useState<DashboardOverview | null>(null);
  const [templateUsage, setTemplateUsage] = useState<TemplateUsageAnalytics[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedDateRange, setSelectedDateRange] = useState(dateRange);

  useEffect(() => {
    fetchAnalytics();
  }, [selectedDateRange, businessPlanId]);

  const fetchAnalytics = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const [overviewData, templateData] = await Promise.all([
        businessPlanAnalyticsAPI.getDashboardOverview(selectedDateRange),
        businessPlanAnalyticsAPI.getTemplateUsageAnalytics(selectedDateRange)
      ]);
      
      setOverview(overviewData);
      setTemplateUsage(templateData);
    } catch (err) {
      console.error('Error fetching analytics:', err);
      setError(t('analytics.errorLoading', 'Failed to load analytics data'));
    } finally {
      setLoading(false);
    }
  };

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  const formatPercentage = (value: number): string => {
    return `${Math.round(value)}%`;
  };

  if (loading) {
    return (
      <div className="bg-white/10 backdrop-blur-sm rounded-lg p-12 border border-white/20">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <h3 className="text-lg font-semibold text-white mb-2">
            {t('analytics.loading', 'Loading Analytics')}
          </h3>
          <p className="text-gray-400">
            {t('analytics.loadingDescription', 'Please wait while we gather your analytics data...')}
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-red-500/30">
        <div className="text-center">
          <div className="text-red-400 mb-2">
            <Activity size={48} className="mx-auto" />
          </div>
          <h3 className="text-lg font-semibold text-white mb-2">
            {t('analytics.error', 'Analytics Error')}
          </h3>
          <p className="text-gray-300 mb-4">{error}</p>
          <button
            onClick={fetchAnalytics}
            className="px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg text-white font-medium transition-colors"
          >
            {t('common.tryAgain', 'Try Again')}
          </button>
        </div>
      </div>
    );
  }

  if (!overview) return null;

  return (
    <div className="space-y-6">
      {/* Header with Date Range Selector */}
      <div className={`flex flex-col sm:flex-row sm:items-center sm:justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
        <div>
          <h2 className="text-2xl font-bold text-white mb-2">
            {t('analytics.businessPlanAnalytics', 'Business Plan Analytics')}
          </h2>
          <p className="text-gray-300">
            {t('analytics.insightsDescription', 'Comprehensive insights into your business plan activities')}
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <select
            value={selectedDateRange}
            onChange={(e) => setSelectedDateRange(Number(e.target.value))}
            className={`px-4 py-2 bg-white/20 border border-white/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 ${isRTL ? 'text-right' : ''}`}
          >
            <option value={7}>{t('analytics.last7Days', 'Last 7 Days')}</option>
            <option value={30}>{t('analytics.last30Days', 'Last 30 Days')}</option>
            <option value={90}>{t('analytics.last90Days', 'Last 90 Days')}</option>
            <option value={365}>{t('analytics.lastYear', 'Last Year')}</option>
          </select>
        </div>
      </div>

      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Time Spent */}
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
          <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <Clock size={24} className={`text-blue-400 ${isRTL ? 'ml-3' : 'mr-3'}`} />
            <div>
              <p className="text-sm text-gray-400">{t('analytics.timeSpent', 'Time Spent')}</p>
              <p className="text-2xl font-bold text-white">
                {formatDuration(overview.time_analytics.total_time_seconds)}
              </p>
            </div>
          </div>
        </div>

        {/* Collaboration */}
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
          <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <Users size={24} className={`text-green-400 ${isRTL ? 'ml-3' : 'mr-3'}`} />
            <div>
              <p className="text-sm text-gray-400">{t('analytics.collaborators', 'Collaborators')}</p>
              <p className="text-2xl font-bold text-white">
                {overview.collaboration_analytics.unique_collaborators}
              </p>
            </div>
          </div>
        </div>

        {/* Exports */}
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
          <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <Download size={24} className={`text-purple-400 ${isRTL ? 'ml-3' : 'mr-3'}`} />
            <div>
              <p className="text-sm text-gray-400">{t('analytics.exports', 'Exports')}</p>
              <p className="text-2xl font-bold text-white">
                {overview.export_analytics.total_exports}
              </p>
            </div>
          </div>
        </div>

        {/* Success Rate */}
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
          <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <TrendingUp size={24} className={`text-orange-400 ${isRTL ? 'ml-3' : 'mr-3'}`} />
            <div>
              <p className="text-sm text-gray-400">{t('analytics.successRate', 'Success Rate')}</p>
              <p className="text-2xl font-bold text-white">
                {formatPercentage(overview.success_metrics.completion_rate)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* User Metrics */}
      <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
        <h3 className="text-lg font-semibold text-white mb-4">
          {t('analytics.yourProgress', 'Your Progress')}
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-white">{overview.user_metrics.total_plans}</div>
            <div className="text-sm text-gray-400">{t('analytics.totalPlans', 'Total Plans')}</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-400">{overview.user_metrics.completed_plans}</div>
            <div className="text-sm text-gray-400">{t('analytics.completed', 'Completed')}</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-400">{overview.user_metrics.in_progress_plans}</div>
            <div className="text-sm text-gray-400">{t('analytics.inProgress', 'In Progress')}</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-400">{overview.user_metrics.draft_plans}</div>
            <div className="text-sm text-gray-400">{t('analytics.drafts', 'Drafts')}</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-400">
              {formatPercentage(overview.user_metrics.average_completion)}
            </div>
            <div className="text-sm text-gray-400">{t('analytics.avgCompletion', 'Avg Completion')}</div>
          </div>
        </div>
      </div>

      {/* Most Used Templates */}
      <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
        <h3 className="text-lg font-semibold text-white mb-4">
          {t('analytics.mostUsedTemplates', 'Most Used Templates')}
        </h3>
        {templateUsage.length > 0 ? (
          <div className="space-y-4">
            {templateUsage.slice(0, 5).map((template, index) => (
              <div key={template.template__id} className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
                <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className={`w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold ${isRTL ? 'ml-3' : 'mr-3'}`}>
                    {index + 1}
                  </div>
                  <div>
                    <div className="text-white font-medium">{template.template__name}</div>
                    <div className="text-sm text-gray-400">{template.template__industry}</div>
                  </div>
                </div>
                <div className={`text-right ${isRTL ? "text-left" : ""}`}>
                  <div className="text-white font-bold">{template.usage_count}</div>
                  <div className="text-sm text-gray-400">
                    {formatPercentage(template.completion_success_rate)} {t('analytics.success', 'success')}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center text-gray-400 py-8">
            <FileText size={48} className="mx-auto mb-4 opacity-50" />
            <p>{t('analytics.noTemplateData', 'No template usage data available')}</p>
          </div>
        )}
      </div>

      {/* Activity Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Collaboration Activity */}
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
          <h3 className="text-lg font-semibold text-white mb-4">
            {t('analytics.collaborationActivity', 'Collaboration Activity')}
          </h3>
          {overview.collaboration_analytics.action_breakdown.length > 0 ? (
            <div className="space-y-3">
              {overview.collaboration_analytics.action_breakdown.map((action) => (
                <div key={action.action_type} className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
                  <span className="text-gray-300 capitalize">{action.action_type}</span>
                  <span className="text-white font-bold">{action.count}</span>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center text-gray-400 py-4">
              <p>{t('analytics.noCollaborationData', 'No collaboration data available')}</p>
            </div>
          )}
        </div>

        {/* Export Formats */}
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
          <h3 className="text-lg font-semibold text-white mb-4">
            {t('analytics.exportFormats', 'Export Formats')}
          </h3>
          {overview.export_analytics.format_breakdown.length > 0 ? (
            <div className="space-y-3">
              {overview.export_analytics.format_breakdown.map((format) => (
                <div key={format.export_format} className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
                  <span className="text-gray-300 uppercase">{format.export_format}</span>
                  <span className="text-white font-bold">{format.count}</span>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center text-gray-400 py-4">
              <p>{t('analytics.noExportData', 'No export data available')}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BusinessPlanAnalyticsDashboard;
