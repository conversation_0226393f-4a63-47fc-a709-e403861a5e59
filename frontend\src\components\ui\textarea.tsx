import React from 'react';

interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  className?: string;
  variant?: 'default' | 'light';
}

export const Textarea: React.FC<TextareaProps> = ({ className = '', variant = 'default', ...props }) => {
  const getVariantClasses = () => {
    switch (variant) {
      case 'light':
        return 'glass-light';
      default:
        return 'theme-bg-input';
    }
  };

  return (
    <textarea
      className={`
        flex min-h-[80px] w-full rounded-md border border-glass-border
        ${getVariantClasses()} px-3 py-2 text-sm
        text-glass-primary
        placeholder:text-glass-muted
        focus:outline-none focus:ring-2 focus:ring-glass-border focus:border-glass-border
        disabled:cursor-not-allowed disabled:opacity-50
        resize-vertical
        transition-all duration-300
        ${className}
      `}
      {...props}
    />
  );
};
