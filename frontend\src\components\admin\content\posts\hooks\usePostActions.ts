import { useState, useCallback } from 'react';
import { Post, postsAPI } from '../../../../../services/api';
import { useAppSelector } from '../../../../../store/hooks';

interface PostFormData {
  title: string;
  content: string;
}

interface UsePostActionsResult {
  formData: PostFormData;
  formError: string | null;
  formSuccess: string | null;
  formSubmitting: boolean;
  selectedPost: Post | null;
  isCreateModalOpen: boolean;
  isEditModalOpen: boolean;
  isDeleteModalOpen: boolean;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | string, editorName?: string) => void;
  resetForm: () => void;
  openCreateModal: () => void;
  closeCreateModal: () => void;
  handleEditPost: (post: Post) => void;
  closeEditModal: () => void;
  handleDeletePost: (post: Post) => void;
  closeDeleteModal: () => void;
  createPost: () => Promise<Post | null>;
  updatePost: () => Promise<Post | null>;
  confirmDeletePost: () => Promise<boolean>;
  preventFormSubmission: (e: React.FormEvent) => void;
}

export const usePostActions = (
  refreshPosts: () => void,
  allPosts: Post[],
  setAllPosts: React.Dispatch<React.SetStateAction<Post[]>>
): UsePostActionsResult => {
  const { user } = useAppSelector(state => state.auth);
  const [formData, setFormData] = useState<PostFormData>({
    title: '',
    content: ''
  });
  const [formError, setFormError] = useState<string | null>(null);
  const [formSuccess, setFormSuccess] = useState<string | null>(null);
  const [formSubmitting, setFormSubmitting] = useState(false);
  const [selectedPost, setSelectedPost] = useState<Post | null>(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  // Handle input change
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | string, editorName?: string) => {
    if (typeof e === 'string' && editorName) {
      // Handle rich text editor input
      setFormData(prev => ({ ...prev, [editorName]: e }));
    } else if (typeof e !== 'string') {
      // Handle regular input
      const { name, value } = e.target;
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  }, []);

  // Reset form
  const resetForm = useCallback(() => {
    setFormData({
      title: '',
      content: ''
    });
    setFormError(null);
    setFormSuccess(null);
  }, []);

  // Open create modal
  const openCreateModal = useCallback(() => {
    resetForm();
    setIsCreateModalOpen(true);
  }, [resetForm]);

  // Close create modal
  const closeCreateModal = useCallback(() => {
    setIsCreateModalOpen(false);
  }, []);

  // Handle edit post
  const handleEditPost = useCallback((post: Post) => {
    setSelectedPost(post);
    setFormData({
      title: post.title,
      content: post.content
    });
    setIsEditModalOpen(true);
  }, []);

  // Close edit modal
  const closeEditModal = useCallback(() => {
    setIsEditModalOpen(false);
  }, []);

  // Handle delete post
  const handleDeletePost = useCallback((post: Post) => {
    setSelectedPost(post);
    setIsDeleteModalOpen(true);
  }, []);

  // Close delete modal
  const closeDeleteModal = useCallback(() => {
    setIsDeleteModalOpen(false);
  }, []);

  // Create new post
  const createPost = useCallback(async (): Promise<Post | null> => {
    setFormSubmitting(true);
    setFormError(null);
    setFormSuccess(null);

    try {
      // Validate form
      if (!formData.title || !formData.content) {
        setFormError('Please fill in all required fields');
        setFormSubmitting(false);
        return null;
      }

      // Create post data
      const postData = {
        title: formData.title,
        content: formData.content,
        author_id: user?.id || 0
      };

      // Call API to create post
      const newPost = await postsAPI.createPost(postData);

      // Update local state
      setAllPosts([newPost, ...allPosts]);
      refreshPosts();

      // Show success message
      setFormSuccess('Post created successfully!');

      // Close modal after a delay
      setTimeout(() => {
        setIsCreateModalOpen(false);
        resetForm();
      }, 1500);

      return newPost;
    } catch (error) {
      console.error('Error creating post:', error);
      setFormError('Failed to create post. Please try again.');
      return null;
    } finally {
      setFormSubmitting(false);
    }
  }, [formData, user, allPosts, setAllPosts, refreshPosts, resetForm]);

  // Update post
  const updatePost = useCallback(async (): Promise<Post | null> => {
    if (!selectedPost) return null;

    setFormSubmitting(true);
    setFormError(null);
    setFormSuccess(null);

    try {
      // Validate form
      if (!formData.title || !formData.content) {
        setFormError('Please fill in all required fields');
        setFormSubmitting(false);
        return null;
      }

      // Update post data
      const updatedPost = await postsAPI.updatePost(selectedPost.id, {
        title: formData.title,
        content: formData.content
      });

      // Update local state
      setAllPosts(allPosts.map(post => 
        post.id === updatedPost.id ? updatedPost : post
      ));
      refreshPosts();

      // Show success message
      setFormSuccess('Post updated successfully!');

      // Close modal after a delay
      setTimeout(() => {
        setIsEditModalOpen(false);
        resetForm();
      }, 1500);

      return updatedPost;
    } catch (error) {
      console.error('Error updating post:', error);
      setFormError('Failed to update post. Please try again.');
      return null;
    } finally {
      setFormSubmitting(false);
    }
  }, [selectedPost, formData, allPosts, setAllPosts, refreshPosts, resetForm]);

  // Confirm delete post
  const confirmDeletePost = useCallback(async (): Promise<boolean> => {
    if (!selectedPost) return false;

    setFormSubmitting(true);
    try {
      // Call the API to delete the post
      await postsAPI.deletePost(selectedPost.id);

      // Update the local state
      setAllPosts(allPosts.filter(post => post.id !== selectedPost.id));
      refreshPosts();

      setIsDeleteModalOpen(false);
      setSelectedPost(null);
      return true;
    } catch (error) {
      console.error('Error deleting post:', error);
      // Show error message to user
      setFormError('Failed to delete post. Please try again.');
      return false;
    } finally {
      setFormSubmitting(false);
    }
  }, [selectedPost, allPosts, setAllPosts, refreshPosts]);

  // Prevent form submission
  const preventFormSubmission = useCallback((e: React.FormEvent) => {
    e.preventDefault();
  }, []);

  return {
    formData,
    formError,
    formSuccess,
    formSubmitting,
    selectedPost,
    isCreateModalOpen,
    isEditModalOpen,
    isDeleteModalOpen,
    handleInputChange,
    resetForm,
    openCreateModal,
    closeCreateModal,
    handleEditPost,
    closeEditModal,
    handleDeletePost,
    closeDeleteModal,
    createPost,
    updatePost,
    confirmDeletePost,
    preventFormSubmission
  };
};

export default usePostActions;
