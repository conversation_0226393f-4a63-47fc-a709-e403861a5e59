/**
 * New Features Test Page
 * Comprehensive testing interface for all newly added features
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  TestTube,
  CheckCircle,
  XCircle,
  AlertCircle,
  Brain,
  Search,
  FileText,
  Zap,
  Link,
  Gamepad2,
  Mic,
  BarChart3
} from 'lucide-react';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import { RTLText, RTLFlex } from '../components/common';
import OfflineIndicator from '../components/common/OfflineIndicator';
import { useLanguage } from '../hooks/useLanguage';
import TemplateSelector from '../components/templates/TemplateSelector';
import TemplateViewer from '../components/templates/TemplateViewer';
import { SVGSearchBar } from '../components/search/SVGSearchComponents';
import PerformanceOptimizer from '../components/performance/PerformanceOptimizer';
import RealTimeAnalytics from '../components/analytics/RealTimeAnalytics';
import ArabicVoiceInput from '../components/voice/ArabicVoiceInput';
import { searchAPI, SearchResult, SearchFilters } from '../services/api';

interface TestResult {
  name: string;
  status: 'pending' | 'success' | 'error' | 'warning';
  message: string;
  details?: string;
}

const NewFeaturesTestPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunningTests, setIsRunningTests] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [previewTemplateId, setPreviewTemplateId] = useState<string>('');
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [currentTestSection, setCurrentTestSection] = useState<string>('overview');

  // Test sections
  const testSections = [
    { id: 'overview', name: 'Overview', icon: TestTube },
    { id: 'templates', name: 'New Templates', icon: FileText },
    { id: 'search', name: 'Advanced Search', icon: Search },
    { id: 'arabic', name: 'Arabic AI', icon: Brain },
    { id: 'performance', name: 'Performance', icon: Zap },
    { id: 'analytics', name: 'Analytics', icon: BarChart3 },
    { id: 'voice', name: 'Voice Input', icon: Mic },
    { id: 'integration', name: 'Integration', icon: Zap }
  ];

  // New template types to test
  const newTemplateTypes = [
    { id: 'ai_startup', name: 'AI/Tech Startup', icon: Brain, color: 'purple' },
    { id: 'blockchain', name: 'Blockchain/Crypto', icon: Link, color: 'blue' },
    { id: 'gaming', name: 'Gaming/Entertainment', icon: Gamepad2, color: 'green' }
  ];

  useEffect(() => {
    // Initialize test results
    setTestResults([
      { name: 'Template System', status: 'pending', message: 'Ready to test' },
      { name: 'Advanced Search', status: 'pending', message: 'Ready to test' },
      { name: 'Arabic AI Service', status: 'pending', message: 'Ready to test' },
      { name: 'Translation Keys', status: 'pending', message: 'Ready to test' },
      { name: 'Component Integration', status: 'pending', message: 'Ready to test' }
    ]);
  }, []);

  const updateTestResult = (name: string, status: TestResult['status'], message: string, details?: string) => {
    setTestResults(prev => prev.map(test =>
      test.name === name ? { ...test, status, message, details } : test
    ));
  };

  const runAllTests = async () => {
    setIsRunningTests(true);

    try {
      // Test 1: Template System
      updateTestResult('Template System', 'pending', 'Testing new template types...');
      await testTemplateSystem();

      // Test 2: Advanced Search
      updateTestResult('Advanced Search', 'pending', 'Testing search functionality...');
      await testAdvancedSearch();

      // Test 3: Arabic AI Service
      updateTestResult('Arabic AI Service', 'pending', 'Testing Arabic AI capabilities...');
      await testArabicAI();

      // Test 4: Translation Keys
      updateTestResult('Translation Keys', 'pending', 'Checking translation keys...');
      await testTranslationKeys();

      // Test 5: Component Integration
      updateTestResult('Component Integration', 'pending', 'Testing component integration...');
      await testComponentIntegration();

    } catch (error) {
      console.error('Test suite error:', error);
    } finally {
      setIsRunningTests(false);
    }
  };

  const testTemplateSystem = async () => {
    try {
      // Test if new template types are available
      const hasNewTemplates = newTemplateTypes.every(template => {
        const translationKey = `templates.templates.${template.id}`;
        return t(translationKey) !== translationKey; // Check if translation exists
      });

      if (hasNewTemplates) {
        updateTestResult('Template System', 'success', 'All new template types are available',
          'AI Startup, Blockchain, and Gaming templates loaded successfully');
      } else {
        updateTestResult('Template System', 'warning', 'Some template types may be missing',
          'Check translation keys and template definitions');
      }
    } catch (error) {
      updateTestResult('Template System', 'error', 'Template system test failed',
        error instanceof Error ? error.message : 'Unknown error');
    }
  };

  const testAdvancedSearch = async () => {
    try {
      // Test search API availability
      const testQuery = 'test search';
      const response = await searchAPI.universalSearch(testQuery, {}, 5);

      updateTestResult('Advanced Search', 'success', 'Search API is working',
        `Found ${response.results.length} results for test query`);
    } catch (error) {
      updateTestResult('Advanced Search', 'warning', 'Search API may not be fully configured',
        'This is expected if backend is not running');
    }
  };

  const testArabicAI = async () => {
    try {
      // Test Arabic text detection and translation keys
      const arabicKeys = [
        'search.aiSuggestions',
        'templates.templates.aiStartup',
        'templates.templates.blockchain'
      ];

      const hasArabicSupport = arabicKeys.every(key => t(key) !== key);

      if (hasArabicSupport) {
        updateTestResult('Arabic AI Service', 'success', 'Arabic translations are available',
          'All required Arabic translation keys found');
      } else {
        updateTestResult('Arabic AI Service', 'warning', 'Some Arabic translations may be missing',
          'Check Arabic translation file');
      }
    } catch (error) {
      updateTestResult('Arabic AI Service', 'error', 'Arabic AI test failed',
        error instanceof Error ? error.message : 'Unknown error');
    }
  };

  const testTranslationKeys = async () => {
    try {
      const requiredKeys = [
        'templates.templates.aiStartup',
        'templates.templates.blockchain',
        'templates.templates.gaming',
        'search.advancedPlaceholder',
        'search.advancedFilters'
      ];

      const missingKeys = requiredKeys.filter(key => t(key) === key);

      if (missingKeys.length === 0) {
        updateTestResult('Translation Keys', 'success', 'All translation keys are available',
          `Checked ${requiredKeys.length} required keys`);
      } else {
        updateTestResult('Translation Keys', 'warning', `${missingKeys.length} translation keys missing`,
          `Missing: ${missingKeys.join(', ')}`);
      }
    } catch (error) {
      updateTestResult('Translation Keys', 'error', 'Translation test failed',
        error instanceof Error ? error.message : 'Unknown error');
    }
  };

  const testComponentIntegration = async () => {
    try {
      // Test if components can be rendered without errors
      const componentsWorking = selectedTemplate !== undefined && searchResults !== undefined;

      if (componentsWorking) {
        updateTestResult('Component Integration', 'success', 'Components are working correctly',
          'TemplateSelector and SVGSearchBar rendered successfully');
      } else {
        updateTestResult('Component Integration', 'error', 'Component integration issues detected',
          'Some components may have rendering problems');
      }
    } catch (error) {
      updateTestResult('Component Integration', 'error', 'Component integration test failed',
        error instanceof Error ? error.message : 'Unknown error');
    }
  };

  const handleSearch = (query: string, filters: SearchFilters) => {
    console.log('Search performed:', { query, filters });
    // This would normally trigger a search
  };

  const handleSearchResults = (results: SearchResult[]) => {
    setSearchResults(results);
    console.log('Search results received:', results);
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success': return <CheckCircle className="text-green-500" size={20} />;
      case 'error': return <XCircle className="text-red-500" size={20} />;
      case 'warning': return <AlertCircle className="text-yellow-500" size={20} />;
      default: return <div className="w-5 h-5 border-2 border-gray-400 rounded-full animate-pulse" />;
    }
  };

  const renderOverview = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-white mb-4">New Features Test Suite</h2>
        <p className="text-gray-300 mb-6">
          Test all newly implemented features including templates, search, and Arabic AI support
        </p>

        <button
          onClick={runAllTests}
          disabled={isRunningTests}
          className={`px-6 py-3 rounded-lg font-medium transition-all ${
            isRunningTests
              ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
              : 'bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600'}
          }`}
        >
          {isRunningTests ? 'Running Tests...' : 'Run All Tests'}
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {newTemplateTypes.map(template => {
          const Icon = template.icon;
          return (
            <div key={template.id} className="bg-gray-800/50 rounded-lg p-4">
              <RTLFlex className="items-center mb-2">
                <Icon className={`text-${template.color}-400`} size={24} />
                <RTLText className={`font-medium ${isRTL ? 'mr-3' : 'ml-3'}`}>
                  {template.name}
                </RTLText>
              </RTLFlex>
              <p className="text-sm text-gray-400">
                {t(`templates.templates.${template.id}Desc`)}
              </p>
            </div>
          );
        })}
      </div>

      <div className="bg-gray-800/50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Test Results</h3>
        <div className="space-y-3">
          {testResults.map((test, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-gray-700/50 rounded-lg">
              <RTLFlex className="items-center">
                {getStatusIcon(test.status)}
                <div className={isRTL ? 'mr-3' : 'ml-3'}>
                  <RTLText className="font-medium">{test.name}</RTLText>
                  <p className="text-sm text-gray-400">{test.message}</p>
                  {test.details && (
                    <p className="text-xs text-gray-500 mt-1">{test.details}</p>
                  )}
                </div>
              </RTLFlex>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderTemplatesTest = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-white">Template System Test</h2>
      <p className="text-gray-300">
        Test the new template types and template selector component
      </p>

      <TemplateSelector
        onSelectTemplate={setSelectedTemplate}
        selectedTemplate={selectedTemplate}
        showCategories={true}
        showFilters={true}
        showActionButtons={true}
        onPreviewTemplate={(templateId) => {
          setPreviewTemplateId(templateId);
          setShowPreviewModal(true);
          console.log('Preview template:', templateId);
        }}
        onUseTemplate={(templateId) => {
          alert(`Using template: ${templateId} - This would navigate to business plan creation`);
          console.log('Use template:', templateId);
        }}
        onCreateTemplate={() => {
          alert('Create new template functionality');
          console.log('Create new template');
        }}
      />

      {selectedTemplate && (
        <div className="bg-green-900/20 border border-green-500/50 rounded-lg p-4">
          <p className="text-green-400">
            ✅ Template selected: {selectedTemplate}
          </p>
          <p className="text-gray-300 text-sm mt-2">
            Click "Preview" to see template details or "Use Template" to start creating a business plan.
          </p>
        </div>
      )}
    </div>
  );

  const renderSearchTest = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-white">Advanced Search Test</h2>
      <p className="text-gray-300">
        Test the new advanced search interface with AI suggestions
      </p>

      <SVGSearchBar
        onSearch={(query, filters) => {
          handleSearch(query);
          handleSearchResults([]);
        }}
        placeholder="Search with AI suggestions..."
        animated={true}
      />

      {searchResults.length > 0 && (
        <div className="bg-blue-900/20 border border-blue-500/50 rounded-lg p-4">
          <p className="text-blue-400">
            ✅ Search results received: {searchResults.length} items
          </p>
        </div>
      )}
    </div>
  );

  const renderPerformanceTest = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-white">Performance Optimizer Test</h2>
      <p className="text-gray-300">
        Test the performance monitoring and optimization features
      </p>

      <PerformanceOptimizer />
    </div>
  );

  const renderAnalyticsTest = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-white">Real-Time Analytics Test</h2>
      <p className="text-gray-300">
        Test the advanced analytics dashboard with live data
      </p>

      <RealTimeAnalytics />
    </div>
  );

  const renderVoiceTest = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-white">Arabic Voice Input Test</h2>
      <p className="text-gray-300">
        Test the voice-to-text functionality with Arabic language support
      </p>

      <ArabicVoiceInput
        onTranscript={(text, language) => {
          console.log('Voice transcript:', { text, language });
          // You could update a state here to show the transcript
        }}
        onError={(error) => {
          console.error('Voice input error:', error);
        }}
        supportedLanguages={['ar-SA', 'ar-EG', 'ar-AE', 'en-US']}
        defaultLanguage="ar-SA"
      />
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 text-white">
      <Navbar />

      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4 flex items-center justify-center">
            <TestTube className={`${isRTL ? 'ml-3' : 'mr-3'} text-purple-400`} size={32} />
            New Features Test Page
          </h1>
          <p className="text-gray-300 text-lg">
            Comprehensive testing interface for all newly implemented features
          </p>

          {/* Offline Indicator */}
          <div className="mt-4 flex justify-center">
            <OfflineIndicator showDetails={true} />
          </div>
        </div>

        {/* Navigation */}
        <div className="flex flex-wrap justify-center gap-2 mb-8">
          {testSections.map(section => {
            const Icon = section.icon;
            return (
              <button
                key={section.id}
                onClick={() => setCurrentTestSection(section.id)}
                className={`flex items-center px-4 py-2 rounded-lg transition-colors ${
                  currentTestSection === section.id
                    ? 'bg-purple-600 text-white'
                    : 'bg-gray-800/50 text-gray-300 hover:bg-gray-700/50'}
                }`}
              >
                <Icon size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
                {section.name}
              </button>
            );
          })}
        </div>

        {/* Content */}
        <div className="max-w-6xl mx-auto">
          {currentTestSection === 'overview' && renderOverview()}
          {currentTestSection === 'templates' && renderTemplatesTest()}
          {currentTestSection === 'search' && renderSearchTest()}
          {currentTestSection === 'performance' && renderPerformanceTest()}
          {currentTestSection === 'analytics' && renderAnalyticsTest()}
          {currentTestSection === 'voice' && renderVoiceTest()}
          {currentTestSection === 'arabic' && (
            <div className="text-center py-12">
              <Brain className="mx-auto mb-4 text-purple-400" size={48} />
              <h2 className="text-2xl font-bold mb-4">Arabic AI Service Test</h2>
              <p className="text-gray-300">
                Arabic AI service testing requires backend integration.
                Check the test results in the Overview section.
              </p>
            </div>
          )}
          {currentTestSection === 'integration' && (
            <div className="text-center py-12">
              <Zap className="mx-auto mb-4 text-yellow-400" size={48} />
              <h2 className="text-2xl font-bold mb-4">Integration Test</h2>
              <p className="text-gray-300">
                Integration tests verify that all components work together correctly.
                Run the full test suite from the Overview section.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Template Viewer Modal */}
      <TemplateViewer
        templateId={previewTemplateId}
        isOpen={showPreviewModal}
        onClose={() => setShowPreviewModal(false)}
        onUseTemplate={(templateId) => {
          alert(`Using template: ${templateId} - This would navigate to business plan creation`);
          console.log('Use template from modal:', templateId);
        }}
        mode="preview"
      />

      <Footer />
    </div>
  );
};

export default NewFeaturesTestPage;
