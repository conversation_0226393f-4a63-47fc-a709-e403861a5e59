import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Play, CheckCircle, XCircle, AlertTriangle, Navigation, ArrowRight } from 'lucide-react';
import { useAppSelector } from '../../store/hooks';

interface RouteTest {
  name: string;
  path: string;
  description: string;
  requiredRole?: string[];
  status: 'idle' | 'testing' | 'success' | 'error';
  error?: string;
  category: 'main' | 'content' | 'ai' | 'admin' | 'super_admin';
}

/**
 * Component to test navigation to different routes
 */
const RouteTester: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, isAuthenticated } = useAppSelector(state => state.auth);
  const [isVisible, setIsVisible] = useState(false);
  const [tests, setTests] = useState<RouteTest[]>([
    // Main Routes
    { name: 'Dashboard', path: '/dashboard', description: 'Main user dashboard', category: 'main', status: 'idle' },
    { name: 'Business Ideas', path: '/dashboard/business-ideas', description: 'Business ideas management', category: 'main', status: 'idle' },
    { name: 'Business Plans', path: '/dashboard/business-plans', description: 'Business plans management', category: 'main', status: 'idle' },
    { name: 'Incubator', path: '/dashboard/incubator', description: 'Business incubator', category: 'main', status: 'idle' },
    { name: 'Analytics', path: '/dashboard/analytics', description: 'Analytics dashboard', category: 'main', status: 'idle' },
    { name: 'Profile', path: '/profile', description: 'User profile', category: 'main', status: 'idle' },
    { name: 'Settings', path: '/settings', description: 'User settings', category: 'main', status: 'idle' },

    // Content Routes
    { name: 'Posts', path: '/dashboard/posts', description: 'Posts management', category: 'content', status: 'idle' },
    { name: 'Events', path: '/dashboard/events', description: 'Events management', category: 'content', status: 'idle' },
    { name: 'Resources', path: '/dashboard/resources', description: 'Resources management', category: 'content', status: 'idle' },
    { name: 'Templates', path: '/dashboard/templates', description: 'Templates overview', category: 'content', status: 'idle' },

    // AI Routes
    { name: 'AI Chat', path: '/chat/enhanced', description: 'Enhanced AI chat', category: 'ai', status: 'idle' },

    // Admin Routes
    { name: 'Admin Dashboard', path: '/admin', description: 'Admin dashboard', requiredRole: ['admin', 'super_admin'], category: 'admin', status: 'idle' },
    { name: 'User Management', path: '/admin/users', description: 'User management', requiredRole: ['admin', 'super_admin'], category: 'admin', status: 'idle' },

    // Super Admin Routes
    { name: 'Super Admin', path: '/super_admin', description: 'Super admin dashboard', requiredRole: ['super_admin'], category: 'super_admin', status: 'idle' },
  ]);

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const testRoute = async (testIndex: number) => {
    const test = tests[testIndex];
    
    setTests(prev => prev.map((t, i) => 
      i === testIndex ? { ...t, status: 'testing', error: undefined } : t
    ));

    try {
      // Navigate to the route
      navigate(test.path);
      
      // Wait a bit for navigation to complete
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Check if we're on the expected path (or a related path)
      const currentPath = window.location.pathname;
      const isOnExpectedPath = currentPath === test.path || currentPath.startsWith(test.path);
      
      if (isOnExpectedPath) {
        setTests(prev => prev.map((t, i) => 
          i === testIndex ? { ...t, status: 'success' } : t
        ));
      } else {
        setTests(prev => prev.map((t, i) => 
          i === testIndex ? { 
            ...t, 
            status: 'error', 
            error: `Redirected to ${currentPath} instead of ${test.path}` 
          } : t
        ));
      }
    } catch (error) {
      setTests(prev => prev.map((t, i) => 
        i === testIndex ? { 
          ...t, 
          status: 'error', 
          error: error instanceof Error ? error.message : String(error) 
        } : t
      ));
    }
  };

  const testAllRoutes = async () => {
    for (let i = 0; i < tests.length; i++) {
      const test = tests[i];
      
      // Skip routes that require roles the user doesn't have
      if (test.requiredRole && !test.requiredRole.some(role => 
        user?.is_admin && role === 'admin' ||
        user?.is_super_admin && role === 'super_admin'
      )) {
        continue;
      }
      
      await testRoute(i);
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  };

  const getStatusIcon = (status: RouteTest['status']) => {
    switch (status) {
      case 'testing':
        return <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />;
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <div className="w-4 h-4 border border-gray-400 rounded-full" />;
    }
  };

  const getCategoryColor = (category: RouteTest['category']) => {
    switch (category) {
      case 'main': return 'text-blue-400';
      case 'content': return 'text-green-400';
      case 'ai': return 'text-purple-400';
      case 'admin': return 'text-orange-400';
      case 'super_admin': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const organizeTestsByCategory = () => {
    const categories = {
      main: tests.filter(t => t.category === 'main'),
      content: tests.filter(t => t.category === 'content'),
      ai: tests.filter(t => t.category === 'ai'),
      admin: tests.filter(t => t.category === 'admin'),
      super_admin: tests.filter(t => t.category === 'super_admin'),
    };
    return categories;
  };

  const categorizedTests = organizeTestsByCategory();

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-44 right-4 z-50 bg-green-600 text-white p-2 rounded-lg shadow-lg hover:bg-green-700 transition-colors"
        title="Route Tester"
      >
        <Navigation className="w-4 h-4" />
      </button>
    );
  }

  return (
    <div className="fixed bottom-44 right-4 z-50 bg-gray-900 border border-gray-700 rounded-lg shadow-lg max-w-md max-h-96 overflow-hidden">
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-700">
        <h3 className="text-white font-medium">Route Tests</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-white"
        >
          ×
        </button>
      </div>

      {/* Content */}
      <div className="p-3 space-y-3 overflow-y-auto max-h-80">
        <div className="flex gap-2">
          <button
            onClick={testAllRoutes}
            className="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 transition-colors"
          >
            Test All
          </button>
          <button
            onClick={() => setTests(prev => prev.map(t => ({ ...t, status: 'idle', error: undefined })))}
            className="bg-gray-600 text-white px-3 py-1 rounded text-sm hover:bg-gray-700 transition-colors"
          >
            Reset
          </button>
        </div>

        <div className="text-xs text-gray-400">
          Current: {location.pathname}
        </div>

        {/* Categorized Tests */}
        {Object.entries(categorizedTests).map(([category, categoryTests]) => (
          categoryTests.length > 0 && (
            <div key={category} className="space-y-1">
              <h4 className={`text-xs font-medium uppercase tracking-wider ${getCategoryColor(category as RouteTest['category'])}`}>
                {category.replace('_', ' ')}
              </h4>
              {categoryTests.map((test, index) => {
                const globalIndex = tests.findIndex(t => t === test);
                return (
                  <div key={test.path} className="flex items-center justify-between text-sm bg-gray-800 rounded p-2">
                    <div className="flex items-center gap-2 flex-1 min-w-0">
                      <button
                        onClick={() => testRoute(globalIndex)}
                        className="text-blue-400 hover:text-blue-300 text-xs"
                      >
                        ▶
                      </button>
                      <div className="flex-1 min-w-0">
                        <div className="text-gray-300 truncate">{test.name}</div>
                        <div className="text-xs text-gray-500 truncate">{test.path}</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2 flex-shrink-0">
                      {getStatusIcon(test.status)}
                      {test.error && (
                        <AlertTriangle 
                          className="w-3 h-3 text-yellow-500" 
                          title={test.error}
                        />
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          )
        ))}

        {/* Summary */}
        <div className="border-t border-gray-700 pt-2 text-xs text-gray-400">
          <div>Total: {tests.length}</div>
          <div>Success: {tests.filter(t => t.status === 'success').length}</div>
          <div>Errors: {tests.filter(t => t.status === 'error').length}</div>
        </div>
      </div>
    </div>
  );
};

export default RouteTester;
