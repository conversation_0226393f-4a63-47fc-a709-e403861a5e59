import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';
import { Resource } from '../../../services/api';
import { Loader, Save, X, AlertCircle, Link, FileText, Video, Image } from 'lucide-react';
import { validateRequired, validateMinLength, validateMaxLength, validateUrl } from '../../../utils/localeValidation';

interface ResourceFormProps {
  initialData?: Partial<Resource>;
  onSubmit: (data: Partial<Resource>) => Promise<boolean>;
  onCancel: () => void;
  isSubmitting?: boolean;
  mode: 'create' | 'edit';
}

interface FormData {
  title: string;
  description: string;
  content: string;
  resource_type: 'article' | 'video' | 'document' | 'link' | 'template' | 'tool';
  category: string;
  tags: string;
  url: string;
  is_featured: boolean;
  is_published: boolean;
}

interface FormErrors {
  [key: string]: string;
}

const ResourceForm: React.FC<ResourceFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isSubmitting = false,
  mode
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [formData, setFormData] = useState<FormData>({
    title: initialData?.title || '',
    description: initialData?.description || '',
    content: initialData?.content || '',
    resource_type: initialData?.resource_type || 'article',
    category: initialData?.category || '',
    tags: initialData?.tags || '',
    url: initialData?.url || '',
    is_featured: initialData?.is_featured ?? false,
    is_published: initialData?.is_published ?? true
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  // Validation rules
  const validateField = (name: string, value: string | boolean): string => {
    switch (name) {
      case 'title':
        if (!validateRequired(value)) {
          return t('validation.required', 'This field is required');
        }
        if (typeof value === 'string' && !validateMinLength(value, 5)) {
          return t('validation.minLength', 'Must be at least 5 characters');
        }
        if (typeof value === 'string' && !validateMaxLength(value, 200)) {
          return t('validation.maxLength', 'Must be less than 200 characters');
        }
        break;
      case 'description':
        if (!validateRequired(value)) {
          return t('validation.required', 'This field is required');
        }
        if (typeof value === 'string' && !validateMinLength(value, 20)) {
          return t('validation.minLength', 'Must be at least 20 characters');
        }
        break;
      case 'content':
        if (!validateRequired(value)) {
          return t('validation.required', 'This field is required');
        }
        if (typeof value === 'string' && !validateMinLength(value, 50)) {
          return t('validation.minLength', 'Must be at least 50 characters');
        }
        break;
      case 'category':
        if (!validateRequired(value)) {
          return t('validation.required', 'This field is required');
        }
        break;
      case 'url':
        if (value && typeof value === 'string' && value.trim()) {
          if (!validateUrl(value)) {
            return t('validation.invalidUrl', 'Must be a valid URL starting with http:// or https://');
          }
        }
        break;
    }
    return '';
  };

  // Validate all fields
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};
    
    Object.keys(formData).forEach(key => {
      if (key !== 'is_featured' && key !== 'is_published' && key !== 'tags' && key !== 'url') {
        const error = validateField(key, formData[key as keyof FormData]);
        if (error) {
          newErrors[key] = error;
        }
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    let processedValue: string | boolean = value;
    
    if (type === 'checkbox') {
      processedValue = (e.target as HTMLInputElement).checked;
    }
    
    setFormData(prev => ({
      ...prev,
      [name]: processedValue
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // Handle field blur
  const handleBlur = (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    setTouched(prev => ({
      ...prev,
      [name]: true
    }));

    // Validate field on blur
    let processedValue: string | boolean = value;
    if (type === 'checkbox') {
      processedValue = (e.target as HTMLInputElement).checked;
    }
    
    const error = validateField(name, processedValue);
    setErrors(prev => ({
      ...prev,
      [name]: error
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Mark all fields as touched
    const allTouched = Object.keys(formData).reduce((acc, key) => {
      acc[key] = true;
      return acc;
    }, {} as Record<string, boolean>);
    setTouched(allTouched);

    // Validate form
    if (!validateForm()) {
      return;
    }

    // Submit form
    const success = await onSubmit(formData);
    if (success) {
      // Form will be closed by parent component
    }
  };

  const resourceTypeOptions = [
    { value: 'article', label: t('resources.types.article', 'Article'), icon: FileText },
    { value: 'video', label: t('resources.types.video', 'Video'), icon: Video },
    { value: 'document', label: t('resources.types.document', 'Document'), icon: FileText },
    { value: 'link', label: t('resources.types.link', 'External Link'), icon: Link },
    { value: 'template', label: t('resources.types.template', 'Template'), icon: FileText },
    { value: 'tool', label: t('resources.types.tool', 'Tool'), icon: FileText }
  ];

  const categoryOptions = [
    'Business Planning',
    'Marketing',
    'Finance',
    'Legal',
    'Technology',
    'Operations',
    'Human Resources',
    'Sales',
    'Product Development',
    'Strategy'
  ];

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <div className="bg-gray-900 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className={`flex items-center justify-between p-6 border-b border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <h2 className="text-xl font-semibold text-white">
            {mode === 'create' 
              ? t('resources.createResource', 'Create Resource')
              : t('resources.editResource', 'Edit Resource')
            }
          </h2>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-white transition-colors"
            disabled={isSubmitting}
          >
            <X size={24} />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('resources.title', 'Title')} *
            </label>
            <input
              type="text"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              onBlur={handleBlur}
              className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white ${
                errors.title && touched.title ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder={t('resources.titlePlaceholder', 'Enter resource title')}
              disabled={isSubmitting}
            />
            {errors.title && touched.title && (
              <p className="mt-1 text-sm text-red-400 flex items-center">
                <AlertCircle size={16} className="mr-1" />
                {errors.title}
              </p>
            )}
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('resources.description', 'Description')} *
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              onBlur={handleBlur}
              rows={3}
              className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white resize-vertical ${
                errors.description && touched.description ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder={t('resources.descriptionPlaceholder', 'Provide a brief description of the resource')}
              disabled={isSubmitting}
            />
            {errors.description && touched.description && (
              <p className="mt-1 text-sm text-red-400 flex items-center">
                <AlertCircle size={16} className="mr-1" />
                {errors.description}
              </p>
            )}
          </div>

          {/* Resource Type and Category */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('resources.type', 'Resource Type')} *
              </label>
              <select
                name="resource_type"
                value={formData.resource_type}
                onChange={handleInputChange}
                onBlur={handleBlur}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                disabled={isSubmitting}
              >
                {resourceTypeOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('resources.category', 'Category')} *
              </label>
              <select
                name="category"
                value={formData.category}
                onChange={handleInputChange}
                onBlur={handleBlur}
                className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white ${
                  errors.category && touched.category ? 'border-red-500' : 'border-gray-600'
                }`}
                disabled={isSubmitting}
              >
                <option value="">{t('resources.selectCategory', 'Select a category')}</option>
                {categoryOptions.map(category => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
              {errors.category && touched.category && (
                <p className="mt-1 text-sm text-red-400 flex items-center">
                  <AlertCircle size={16} className="mr-1" />
                  {errors.category}
                </p>
              )}
            </div>
          </div>

          {/* Content */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('resources.content', 'Content')} *
            </label>
            <textarea
              name="content"
              value={formData.content}
              onChange={handleInputChange}
              onBlur={handleBlur}
              rows={8}
              className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white resize-vertical ${
                errors.content && touched.content ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder={t('resources.contentPlaceholder', 'Enter the main content of the resource...')}
              disabled={isSubmitting}
            />
            {errors.content && touched.content && (
              <p className="mt-1 text-sm text-red-400 flex items-center">
                <AlertCircle size={16} className="mr-1" />
                {errors.content}
              </p>
            )}
          </div>

          {/* URL and Tags */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('resources.url', 'External URL')}
              </label>
              <input
                type="url"
                name="url"
                value={formData.url}
                onChange={handleInputChange}
                onBlur={handleBlur}
                className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white ${
                  errors.url && touched.url ? 'border-red-500' : 'border-gray-600'
                }`}
                placeholder="https://example.com"
                disabled={isSubmitting}
              />
              {errors.url && touched.url && (
                <p className="mt-1 text-sm text-red-400 flex items-center">
                  <AlertCircle size={16} className="mr-1" />
                  {errors.url}
                </p>
              )}
              <p className="mt-1 text-xs text-gray-400">
                {t('resources.urlHelp', 'Optional: Link to external resource')}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('resources.tags', 'Tags')}
              </label>
              <input
                type="text"
                name="tags"
                value={formData.tags}
                onChange={handleInputChange}
                onBlur={handleBlur}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                placeholder={t('resources.tagsPlaceholder', 'startup, business, marketing')}
                disabled={isSubmitting}
              />
              <p className="mt-1 text-xs text-gray-400">
                {t('resources.tagsHelp', 'Separate tags with commas')}
              </p>
            </div>
          </div>

          {/* Settings */}
          <div className="space-y-4">
            <div>
              <label className="flex items-center gap-3">
                <input
                  type="checkbox"
                  name="is_featured"
                  checked={formData.is_featured}
                  onChange={handleInputChange}
                  className="w-4 h-4 text-purple-600 bg-gray-800 border-gray-600 rounded focus:ring-purple-500 focus:ring-2"
                  disabled={isSubmitting}
                />
                <span className="text-sm font-medium text-gray-300">
                  {t('resources.isFeatured', 'Featured resource')}
                </span>
              </label>
              <p className="mt-1 text-xs text-gray-400 ml-7">
                {t('resources.featuredHelp', 'Featured resources appear prominently in listings')}
              </p>
            </div>

            <div>
              <label className="flex items-center gap-3">
                <input
                  type="checkbox"
                  name="is_published"
                  checked={formData.is_published}
                  onChange={handleInputChange}
                  className="w-4 h-4 text-purple-600 bg-gray-800 border-gray-600 rounded focus:ring-purple-500 focus:ring-2"
                  disabled={isSubmitting}
                />
                <span className="text-sm font-medium text-gray-300">
                  {t('resources.isPublished', 'Published')}
                </span>
              </label>
              <p className="mt-1 text-xs text-gray-400 ml-7">
                {t('resources.publishedHelp', 'Only published resources are visible to users')}
              </p>
            </div>
          </div>

          {/* Form Actions */}
          <div className={`flex gap-4 pt-4 border-t border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <button
              type="button"
              onClick={onCancel}
              className="px-6 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors"
              disabled={isSubmitting}
            >
              {t('common.cancel', 'Cancel')}
            </button>
            <button
              type="submit"
              className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader size={16} className="animate-spin" />
                  {t('common.saving', 'Saving...')}
                </>
              ) : (
                <>
                  <Save size={16} />
                  {mode === 'create'
                    ? t('common.create', 'Create')
                    : t('common.update', 'Update')
                  }
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ResourceForm;
