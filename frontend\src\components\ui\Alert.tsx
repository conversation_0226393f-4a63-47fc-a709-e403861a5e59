import React from 'react';
import { AlertCircle, CheckCircle, Info, XCircle, X } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';

type AlertVariant = 'info' | 'success' | 'warning' | 'error';

interface AlertProps {
  variant?: AlertVariant;
  title?: string;
  children: React.ReactNode;
  onClose?: () => void;
  className?: string;
}

const Alert: React.FC<AlertProps> = ({
  variant = 'info',
  title,
  children,
  onClose,
  className = '',
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  // Variant-specific styles
  const variantStyles = {
    info: {
      className: 'glass-light border-blue-500/30',
      icon: <Info className="h-5 w-5 text-blue-400" />,
      title: t('common.info'),
    },
    success: {
      className: 'glass-light border-green-500/30',
      icon: <CheckCircle className="h-5 w-5 text-green-400" />,
      title: t('common.success'),
    },
    warning: {
      className: 'glass-light border-yellow-500/30',
      icon: <AlertCircle className="h-5 w-5 text-yellow-400" />,
      title: t('common.warning'),
    },
    error: {
      className: 'glass-light border-red-500/30',
      icon: <XCircle className="h-5 w-5 text-red-400" />,
      title: t('common.error'),
    },
  };

  const { className: variantClassName, icon, title: defaultTitle } = variantStyles[variant];

  return (
    <div className={`rounded-lg border p-4 ${variantClassName} ${className}`}>
      <div className={`flex ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`flex-shrink-0 ${isRTL ? "flex-row-reverse" : ""}`}>{icon}</div>
        <div className={`ml-3 flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
          {(title || defaultTitle) && (
            <h3 className="text-sm font-medium text-glass-primary">
              {title || defaultTitle}
            </h3>
          )}
          <div className="text-sm mt-1 text-glass-secondary">
            {children}
          </div>
        </div>
        {onClose && (
          <div className="ml-auto pl-3">
            <button
              type="button"
              className={`inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2 text-glass-primary hover:bg-glass-hover focus:ring-glass-border ${isRTL ? "flex-row-reverse" : ""}`}
              onClick={onClose}
            >
              <span className="sr-only">{t('common.close')}</span>
              <X className="h-5 w-5" />
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Alert;
