import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Arrow<PERSON><PERSON><PERSON>, Bo<PERSON>, Sparkles } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../../store/hooks';
import { RTLIcon, RTLText, RTLFlex } from '../common';
const ChatPageHeader: React.FC = () => {
  const { t } = useTranslation();
  const { language } = useAppSelector(state => state.language);

  return (
    <div className="mb-6">
      <RTLFlex
        as={Link}
        to="/dashboard"
        className="text-purple-400 hover:text-purple-300 mb-4"
        align="center"
      >
        <RTLIcon icon={ArrowLeft} size={16} className={language === 'ar' ? 'ml-1' : 'mr-1'} flipInRTL={true} />
        {t('common.backToDashboard')}
      </RTLFlex>

      <RTLFlex className="items-center">
        <div className={`w-12 h-12 rounded-full bg-purple-600 flex items-center justify-center ${language === 'ar' ? 'ml-3' : 'mr-3'}`}>
          <Bot size={24} className="text-white" />
        </div>
        <div>
          <RTLFlex as="h1" className="text-2xl font-bold" align="center">
            {t('chat.title')} <Sparkles size={20} className={`${language === 'ar' ? 'mr-2' : 'ml-2'} text-purple-400`} />
          </RTLFlex>
          <RTLText as="p" className="text-gray-400">
            {t('chat.subtitle')}
          </RTLText>
        </div>
      </RTLFlex>
    </div>
  );
};

export default ChatPageHeader;
