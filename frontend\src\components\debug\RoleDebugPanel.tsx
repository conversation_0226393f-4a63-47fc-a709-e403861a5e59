/**
 * Role Debug Panel
 * 
 * Development-only component to debug role detection and navigation filtering
 */

import React from 'react';
import { useAppSelector } from '../../store/hooks';
import { createRoleManager } from '../../utils/unifiedRoleManager';
import { isSuperAdmin } from '../../utils/roleBasedRouting';

const RoleDebugPanel: React.FC = () => {
  const { user, isAuthenticated } = useAppSelector(state => state.auth);

  // Only show in development mode and to super admins
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  // Only show to super admins
  if (!isAuthenticated || !user || !isSuperAdmin(user)) {
    return null;
  }

  const roleManager = createRoleManager(user);
  const debugInfo = roleManager.getDebugInfo();

  return (
    <div className="fixed bottom-4 right-4 bg-black/90 text-white p-4 rounded-lg max-w-md text-xs font-mono z-50">
      <h3 className="text-yellow-400 font-bold mb-2">🔍 Role Debug Panel</h3>
      
      <div className="space-y-2">
        <div>
          <span className="text-blue-400">Authenticated:</span> {isAuthenticated ? '✅' : '❌'}
        </div>
        
        {debugInfo && (
          <>
            <div>
              <span className="text-blue-400">User:</span> {debugInfo.user}
            </div>
            
            <div>
              <span className="text-blue-400">Primary Role:</span> 
              <span className="text-green-400 ml-1">{debugInfo.primaryRole}</span>
            </div>
            
            <div>
              <span className="text-blue-400">All Roles:</span> 
              <span className="text-green-400 ml-1">[{debugInfo.roles.join(', ')}]</span>
            </div>
            
            <div>
              <span className="text-blue-400">Permissions:</span> 
              <span className="text-purple-400 ml-1">[{debugInfo.permissions.join(', ')}]</span>
            </div>
            
            <div>
              <span className="text-blue-400">Dashboard Route:</span> 
              <span className="text-cyan-400 ml-1">{debugInfo.dashboardRoute}</span>
            </div>
            
            <div>
              <span className="text-blue-400">Is Super Admin:</span> {debugInfo.isSuperAdmin ? '✅' : '❌'}
            </div>
            
            <div>
              <span className="text-blue-400">Is Admin:</span> {debugInfo.isAdmin ? '✅' : '❌'}
            </div>
            
            <div className="mt-2 pt-2 border-t border-gray-600">
              <div className="text-gray-400 text-xs">Raw User Data:</div>
              <div className="text-xs">
                <div>is_admin: {debugInfo.rawUserData.is_admin ? '✅' : '❌'}</div>
                <div>is_superuser: {debugInfo.rawUserData.is_superuser ? '✅' : '❌'}</div>
                <div>active_roles: [{debugInfo.rawUserData.active_roles.join(', ')}]</div>
                <div>primary_role: {debugInfo.rawUserData.primary_role || 'null'}</div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default RoleDebugPanel;
