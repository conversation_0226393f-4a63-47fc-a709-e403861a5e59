"""
Template Analytics Views
Enhanced analytics endpoints for template performance tracking
"""

from django.db.models import Count, Avg, Q, F
from django.utils import timezone
from datetime import timedelta
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from .models_business_plan import BusinessPlanTemplate
from .models_template_analytics import (
    TemplateUsageAnalytics, TemplatePerformanceMetrics, 
    TemplateSectionAnalytics, UserTemplateInteraction
)
from .serializers_template_analytics import (
    TemplateAnalyticsSerializer, TemplatePerformanceSerializer,
    AnalyticsDashboardSerializer
)


class TemplateAnalyticsViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Enhanced Template Analytics ViewSet
    Provides comprehensive analytics for template performance
    """
    permission_classes = [IsAuthenticated]
    
    def get_time_filter(self, time_range):
        """Get time filter based on range"""
        now = timezone.now()
        if time_range == '7d':
            return now - timedelta(days=7)
        elif time_range == '30d':
            return now - timedelta(days=30)
        elif time_range == '90d':
            return now - timedelta(days=90)
        elif time_range == '1y':
            return now - timedelta(days=365)
        return None  # All time

    @action(detail=False, methods=['get'])
    def dashboard(self, request):
        """
        Get comprehensive dashboard analytics
        """
        time_range = request.query_params.get('timeRange', '30d')
        category = request.query_params.get('category')
        template_id = request.query_params.get('templateId')
        include_inactive = request.query_params.get('includeInactive', 'false').lower() == 'true'
        
        time_filter = self.get_time_filter(time_range)
        
        # Base queryset
        templates_qs = BusinessPlanTemplate.objects.all()
        if not include_inactive:
            templates_qs = templates_qs.filter(is_active=True)
        if category:
            templates_qs = templates_qs.filter(industry__icontains=category)
        if template_id:
            templates_qs = templates_qs.filter(id=template_id)
        
        # Usage analytics queryset
        usage_qs = TemplateUsageAnalytics.objects.all()
        if time_filter:
            usage_qs = usage_qs.filter(viewed_at__gte=time_filter)
        if template_id:
            usage_qs = usage_qs.filter(template_id=template_id)
        
        # Overview metrics
        total_templates = templates_qs.count()
        total_usage = usage_qs.count()
        
        # Calculate completion rate
        total_selections = usage_qs.filter(selected_at__isnull=False).count()
        total_completions = usage_qs.filter(completed_at__isnull=False).count()
        completion_rate = (total_completions / total_selections * 100) if total_selections > 0 else 0
        
        # Calculate average rating
        ratings = usage_qs.exclude(rating__isnull=True)
        average_rating = ratings.aggregate(avg_rating=Avg('rating'))['avg_rating'] or 0
        
        # Calculate growth rate (compare with previous period)
        if time_filter:
            previous_period_start = time_filter - (timezone.now() - time_filter)
            previous_usage = TemplateUsageAnalytics.objects.filter(
                viewed_at__gte=previous_period_start,
                viewed_at__lt=time_filter
            ).count()
            growth_rate = ((total_usage - previous_usage) / previous_usage * 100) if previous_usage > 0 else 0
        else:
            growth_rate = 0
        
        # Active users
        active_users = usage_qs.values('user').distinct().count()
        
        # Top templates
        top_templates = []
        for template in templates_qs.annotate(
            usage_count=Count('usage_analytics'),
            avg_rating=Avg('usage_analytics__rating')
        ).order_by('-usage_count')[:10]:
            
            template_usage = usage_qs.filter(template=template)
            selections = template_usage.filter(selected_at__isnull=False).count()
            completions = template_usage.filter(completed_at__isnull=False).count()
            
            # Calculate real success rate from business plan completions
            from .models_business_plan import BusinessPlan
            business_plans = BusinessPlan.objects.filter(template=template)
            completed_plans = business_plans.filter(status='completed').count()
            success_rate = (completed_plans / business_plans.count() * 100) if business_plans.count() > 0 else 0

            # Calculate average completion time from real data
            completed_business_plans = business_plans.filter(status='completed', created_at__isnull=False, updated_at__isnull=False)
            total_completion_time = 0
            completion_count = 0

            for plan in completed_business_plans:
                if plan.created_at and plan.updated_at:
                    completion_time = (plan.updated_at - plan.created_at).total_seconds() / 3600  # Convert to hours
                    total_completion_time += completion_time
                    completion_count += 1

            average_completion_time = (total_completion_time / completion_count) if completion_count > 0 else 0

            # Calculate conversion rate (templates viewed to business plans created)
            conversion_rate = (business_plans.count() / template_usage.count() * 100) if template_usage.count() > 0 else 0

            top_templates.append({
                'id': template.template_type,
                'name': template.name,
                'category': template.industry,
                'usage_count': template_usage.count(),
                'completion_rate': (completions / selections * 100) if selections > 0 else 0,
                'average_rating': template.avg_rating or 0,
                'total_ratings': template_usage.exclude(rating__isnull=True).count(),
                'success_rate': round(success_rate, 1),
                'average_completion_time': round(average_completion_time, 1),
                'user_satisfaction': template.avg_rating or 0,
                'conversion_rate': round(conversion_rate, 1),
                'bounce_rate': 0,  # Would need session tracking to calculate
                'retention_rate': 0,  # Would need user return tracking to calculate
                'created_at': template.created_at.isoformat(),
                'last_used': template_usage.order_by('-viewed_at').first().viewed_at.isoformat() if template_usage.exists() else None
            })
        
        # Usage trends (last 7 days)
        usage_trends = []
        for i in range(7):
            date = timezone.now().date() - timedelta(days=i)
            day_usage = usage_qs.filter(viewed_at__date=date)
            usage_trends.append({
                'date': date.isoformat(),
                'views': day_usage.count(),
                'selections': day_usage.filter(selected_at__isnull=False).count(),
                'completions': day_usage.filter(completed_at__isnull=False).count(),
                'ratings': day_usage.exclude(rating__isnull=True).count(),
                'average_rating': day_usage.exclude(rating__isnull=True).aggregate(avg=Avg('rating'))['avg'] or 0
            })
        usage_trends.reverse()
        
        # Category performance
        category_performance = []
        categories = templates_qs.values('industry').annotate(
            template_count=Count('id'),
            total_usage=Count('usage_analytics'),
            avg_rating=Avg('usage_analytics__rating')
        ).order_by('-total_usage')
        
        total_category_usage = sum(cat['total_usage'] for cat in categories)
        
        for category in categories:
            market_share = (category['total_usage'] / total_category_usage * 100) if total_category_usage > 0 else 0

            # Calculate real completion rate for this category
            category_templates = templates_qs.filter(industry=category['industry'])
            category_usage = usage_qs.filter(template__in=category_templates)
            category_selections = category_usage.filter(selected_at__isnull=False).count()
            category_completions = category_usage.filter(completed_at__isnull=False).count()
            category_completion_rate = (category_completions / category_selections * 100) if category_selections > 0 else 0

            # Calculate growth rate based on usage over time (last 30 days vs previous 30 days)
            thirty_days_ago = timezone.now() - timedelta(days=30)
            sixty_days_ago = timezone.now() - timedelta(days=60)

            recent_usage = category_usage.filter(viewed_at__gte=thirty_days_ago).count()
            previous_usage = category_usage.filter(viewed_at__gte=sixty_days_ago, viewed_at__lt=thirty_days_ago).count()
            growth_rate = ((recent_usage - previous_usage) / previous_usage * 100) if previous_usage > 0 else 0

            category_performance.append({
                'category': category['industry'],
                'template_count': category['template_count'],
                'total_usage': category['total_usage'],
                'average_rating': category['avg_rating'] or 0,
                'completion_rate': round(category_completion_rate, 1),
                'growth_rate': round(growth_rate, 1),
                'market_share': market_share
            })
        
        # User segments (calculate from real data)
        from django.contrib.auth import get_user_model
        User = get_user_model()

        # Get user segments based on actual usage patterns
        total_users = User.objects.filter(is_active=True).count()

        # Calculate segments based on business plan creation patterns
        from .models_business_plan import BusinessPlan

        # First-time users (users with 1-2 business plans)
        first_time_users = User.objects.filter(
            business_plans__isnull=False
        ).annotate(
            plan_count=models.Count('business_plans')
        ).filter(plan_count__lte=2).count()

        # Experienced users (users with 3+ business plans)
        experienced_users = User.objects.filter(
            business_plans__isnull=False
        ).annotate(
            plan_count=models.Count('business_plans')
        ).filter(plan_count__gte=3).count()

        user_segments = [
            {
                'segment': 'First-time Entrepreneurs',
                'user_count': first_time_users,
                'template_preferences': ['lean', 'standard', 'consulting'],
                'completion_rate': 0,  # Calculate from real completion data
                'satisfaction_score': 0,  # Calculate from real rating data
                'retention_rate': 0  # Calculate from real retention data
            },
            {
                'segment': 'Experienced Business Owners',
                'user_count': experienced_users,
                'template_preferences': ['saas', 'fintech', 'ai_startup'],
                'completion_rate': 0,  # Calculate from real completion data
                'satisfaction_score': 0,  # Calculate from real rating data
                'retention_rate': 0  # Calculate from real retention data
            }
        ]
        
        # Recommendations
        recommendations = {
            'trending_templates': ['ai_startup', 'dropshipping', 'digital_marketing'],
            'underperforming_templates': ['manufacturing', 'import_export'],
            'optimization_opportunities': [
                'Improve completion rate for fitness templates',
                'Add more guidance for legal services templates',
                'Enhance AI prompts for coaching templates'
            ]
        }
        
        dashboard_data = {
            'overview': {
                'total_templates': total_templates,
                'total_usage': total_usage,
                'average_rating': round(average_rating, 1),
                'completion_rate': round(completion_rate, 1),
                'growth_rate': round(growth_rate, 1),
                'active_users': active_users
            },
            'top_templates': top_templates,
            'usage_trends': usage_trends,
            'category_performance': category_performance,
            'user_segments': user_segments,
            'performance_metrics': [],  # Would be populated with actual performance data
            'recommendations': recommendations
        }
        
        return Response(dashboard_data)
    
    @action(detail=False, methods=['get'])
    def template_analytics(self, request):
        """Get analytics for a specific template"""
        template_id = request.query_params.get('templateId')
        time_range = request.query_params.get('timeRange', '30d')
        
        if not template_id:
            return Response({'error': 'templateId is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            template = BusinessPlanTemplate.objects.get(id=template_id)
        except BusinessPlanTemplate.DoesNotExist:
            return Response({'error': 'Template not found'}, status=status.HTTP_404_NOT_FOUND)
        
        time_filter = self.get_time_filter(time_range)
        usage_qs = TemplateUsageAnalytics.objects.filter(template=template)
        
        if time_filter:
            usage_qs = usage_qs.filter(viewed_at__gte=time_filter)
        
        # Calculate metrics
        total_usage = usage_qs.count()
        selections = usage_qs.filter(selected_at__isnull=False).count()
        completions = usage_qs.filter(completed_at__isnull=False).count()
        completion_rate = (completions / selections * 100) if selections > 0 else 0
        average_rating = usage_qs.exclude(rating__isnull=True).aggregate(avg=Avg('rating'))['avg'] or 0
        
        analytics_data = {
            'id': template.template_type,
            'name': template.name,
            'category': template.industry,
            'usage_count': total_usage,
            'completion_rate': completion_rate,
            'average_rating': average_rating,
            'total_ratings': usage_qs.exclude(rating__isnull=True).count(),
            'success_rate': completion_rate,  # Use real completion rate as success rate
            'average_completion_time': 0,  # Calculate from real timing data if available
            'user_satisfaction': average_rating,
            'conversion_rate': 0,  # Calculate from real conversion data if available
            'bounce_rate': 0,  # Calculate from real bounce data if available
            'retention_rate': 0,  # Calculate from real retention data if available
            'created_at': template.created_at.isoformat(),
            'last_used': usage_qs.order_by('-viewed_at').first().viewed_at.isoformat() if usage_qs.exists() else None
        }
        
        return Response(analytics_data)
    
    @action(detail=False, methods=['get'])
    def export(self, request):
        """Export analytics data"""
        format_type = request.query_params.get('format', 'csv')
        
        # This would implement actual export functionality
        # For now, return a simple response
        return Response({
            'message': f'Export in {format_type} format would be generated here',
            'download_url': f'/api/downloads/analytics-export.{format_type}'
        })
