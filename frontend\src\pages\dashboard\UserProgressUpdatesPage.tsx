import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Plus, Search, Edit, Trash2, TrendingUp, Calendar, Target, Loader2, CheckCircle, AlertCircle } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
// MainLayout removed - handled by routing system
import { ProgressUpdate, progressUpdatesAPI, BusinessIdea, businessIdeasAPI } from '../../services/incubatorApi';
import { useAppSelector } from '../../store/hooks';
import { RTLText, RTLFlex, RTLIcon } from '../../components/rtl';

const UserProgressUpdatesPage: React.FC = () => {
  const { t } = useTranslation();
  const { language, isRTL } = useLanguage();
  const { user } = useAppSelector(state => state.auth);

  const [progressUpdates, setProgressUpdates] = useState<ProgressUpdate[]>([]);
  const [businessIdeas, setBusinessIdeas] = useState<BusinessIdea[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUpdate, setSelectedUpdate] = useState<ProgressUpdate | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [creating, setCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [selectedBusinessIdea, setSelectedBusinessIdea] = useState<number | null>(null);

  const [formData, setFormData] = useState({
    business_idea: '',
    title: '',
    description: '',
    progress_percentage: 0,
    milestone_achieved: false,
    challenges_faced: '',
    next_steps: '',
  });

  useEffect(() => {
    fetchData();
  }, [user]);

  const fetchData = async () => {
    setLoading(true);
    try {
      // Fetch user's business ideas
      const ideas = await businessIdeasAPI.getBusinessIdeas();
      const userIdeas = ideas.filter(idea => idea.owner.id === user?.id);
      setBusinessIdeas(userIdeas);

      // Fetch user's progress updates
      const updates = await progressUpdatesAPI.getProgressUpdates();
      const userUpdates = updates.filter(update => 
        userIdeas.some(idea => idea.id === update.business_idea.id)
      );
      setProgressUpdates(userUpdates);
    } catch (error) {
      console.error('Error fetching data:', error);
      setError(t('progress.errors.failedToLoad', 'Failed to load progress updates'));
    } finally {
      setLoading(false);
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : 
              type === 'number' ? parseInt(value) || 0 : value
    }));
  };

  const openCreateModal = () => {
    setFormData({
      business_idea: '',
      title: '',
      description: '',
      progress_percentage: 0,
      milestone_achieved: false,
      challenges_faced: '',
      next_steps: '',
    });
    setError(null);
    setSuccess(null);
    setIsCreateModalOpen(true);
  };

  const openDeleteModal = (update: ProgressUpdate) => {
    setSelectedUpdate(update);
    setIsDeleteModalOpen(true);
  };

  const handleCreateUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    setCreating(true);
    setError(null);

    try {
      await progressUpdatesAPI.createProgressUpdate({
        ...formData,
        business_idea_id: parseInt(formData.business_idea),
      });

      setSuccess(t('progress.success.created', 'Progress update created successfully'));
      fetchData();
      setIsCreateModalOpen(false);
      
      // Clear success message after delay
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error('Error creating progress update:', error);
      setError(t('progress.errors.failedToCreate', 'Failed to create progress update'));
    } finally {
      setCreating(false);
    }
  };

  const handleDeleteUpdate = async () => {
    if (!selectedUpdate) return;

    setDeleting(true);
    try {
      await progressUpdatesAPI.deleteProgressUpdate(selectedUpdate.id);
      setSuccess(t('progress.success.deleted', 'Progress update deleted successfully'));
      fetchData();
      setIsDeleteModalOpen(false);
      setSelectedUpdate(null);
      
      // Clear success message after delay
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error('Error deleting progress update:', error);
      setError(t('progress.errors.failedToDelete', 'Failed to delete progress update'));
    } finally {
      setDeleting(false);
    }
  };

  // Filter progress updates based on search term and selected business idea
  const filteredUpdates = progressUpdates.filter(update => {
    const matchesSearch = 
      update.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      update.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      update.business_idea.title.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesBusinessIdea = !selectedBusinessIdea || update.business_idea.id === selectedBusinessIdea;
    
    return matchesSearch && matchesBusinessIdea;
  });

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(language === 'ar' ? 'ar-SA' : undefined, {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getProgressColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-400';
    if (percentage >= 50) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getProgressBgColor = (percentage: number) => {
    if (percentage >= 80) return 'bg-green-600';
    if (percentage >= 50) return 'bg-yellow-600';
    return 'bg-red-600';
  };

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
        {/* Header */}
        <div className={`mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
          <div>
            <RTLText as="h1" className="text-2xl font-bold text-white">{t('progress.myUpdates', 'My Progress Updates')}</RTLText>
            <RTLText as="div" className="text-gray-300 mt-1">{t('progress.trackYourProgress', 'Track your business idea progress')}</RTLText>
          </div>
          <button
            onClick={openCreateModal}
            className="mt-4 sm:mt-0 px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300 flex items-center justify-center"
            disabled={businessIdeas.length === 0}
          >
            <Plus className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
            {t('progress.addUpdate', 'Add Progress Update')}
          </button>
        </div>

        {/* Error/Success Messages */}
        {error && (
          <div className="mb-4 p-3 bg-red-900/50 border border-red-500 rounded-md text-red-200">
            {error}
          </div>
        )}

        {success && (
          <div className="mb-4 p-3 bg-green-900/50 border border-green-500 rounded-md text-green-200">
            {success}
          </div>
        )}

        {businessIdeas.length === 0 && !loading && (
          <div className="mb-6 p-4 bg-yellow-900/30 border border-yellow-800 rounded-lg text-yellow-200">
            <RTLText as="div" className="flex items-center">
              <AlertCircle className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
              {t('progress.noBusinessIdeas', 'You need to create a business idea first before adding progress updates.')}
            </RTLText>
            <Link 
              to="/dashboard/business-ideas" 
              className="text-yellow-400 hover:text-yellow-300 underline mt-2 inline-block"
            >
              {t('progress.createBusinessIdea', 'Create your first business idea')}
            </Link>
          </div>
        )}

        {/* Search and Filters */}
        <div className={`mb-6 flex flex-col md:flex-row gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`relative flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
            <input
              type="text"
              placeholder={t('progress.searchUpdates', 'Search progress updates...')}
              value={searchTerm}
              onChange={handleSearchChange}
              className={`w-full px-4 py-2 ${language === 'ar' ? 'pr-10' : 'pl-10'} bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400`}
              dir={language === 'ar' ? 'rtl' : 'ltr'}
            />
            <Search size={18} className={`absolute ${language === 'ar' ? 'right-3' : 'left-3'} top-2.5 text-gray-400`} />
          </div>

          <select
            value={selectedBusinessIdea || ''}
            onChange={(e) => setSelectedBusinessIdea(e.target.value ? parseInt(e.target.value) : null)}
            className="px-4 py-2 bg-white/20 border border-white/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <option value="">{t('progress.allBusinessIdeas', 'All Business Ideas')}</option>
            {businessIdeas.map(idea => (
              <option key={idea.id} value={idea.id}>{idea.title}</option>
            ))}
          </select>
        </div>

        {/* Progress Updates List */}
        {loading ? (
          <div className="flex justify-center py-12">
            <div className="flex items-center space-x-3">
              <Loader2 className="w-6 h-6 animate-spin" />
              <span>{t('common.loading', 'Loading...')}</span>
            </div>
          </div>
        ) : filteredUpdates.length > 0 ? (
          <div className="space-y-6">
            {filteredUpdates.map(update => (
              <div
                key={update.id}
                className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:border-purple-500/50 transition-colors"
              >
                <div className={`flex justify-between items-start mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className="flex-1">
                    <RTLFlex align="center" className="mb-2">
                      <div className="p-2 bg-indigo-900/50 rounded-lg mr-3">
                        <TrendingUp size={20} />
                      </div>
                      <div>
                        <RTLText as="h3" className="text-xl font-bold">{update.title}</RTLText>
                        <RTLText as="div" className="text-sm text-purple-400">{update.business_idea.title}</RTLText>
                      </div>
                      {update.milestone_achieved && (
                        <div className="ml-3 px-2 py-1 bg-green-900/30 text-green-300 rounded-full text-xs flex items-center">
                          <CheckCircle size={14} className={`${language === 'ar' ? 'ml-1' : 'mr-1'}`} />
                          {t('progress.milestoneAchieved', 'Milestone Achieved')}
                        </div>
                      )}
                    </RTLFlex>

                    <RTLText as="div" className="text-gray-300 mb-4">
                      {update.description}
                    </RTLText>

                    {/* Progress Bar */}
                    <div className="mb-4">
                      <RTLFlex justify="between" align="center" className="mb-2">
                        <span className="text-sm text-gray-400">{t('progress.progress', 'Progress')}</span>
                        <span className={`text-sm font-bold ${getProgressColor(update.progress_percentage)}`}>
                          {update.progress_percentage}%
                        </span>
                      </RTLFlex>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${getProgressBgColor(update.progress_percentage)}`}
                          style={{ width: `${update.progress_percentage}%` }}
                        ></div>
                      </div>
                    </div>

                    {update.challenges_faced && (
                      <div className="mb-3">
                        <RTLText as="div" className="text-sm font-medium text-red-400 mb-1">
                          {t('progress.challenges', 'Challenges Faced')}
                        </RTLText>
                        <RTLText as="div" className="text-sm text-gray-300">
                          {update.challenges_faced}
                        </RTLText>
                      </div>
                    )}

                    {update.next_steps && (
                      <div className="mb-3">
                        <RTLText as="div" className="text-sm font-medium text-blue-400 mb-1">
                          {t('progress.nextSteps', 'Next Steps')}
                        </RTLText>
                        <RTLText as="div" className="text-sm text-gray-300">
                          {update.next_steps}
                        </RTLText>
                      </div>
                    )}

                    <div className="flex items-center text-xs text-gray-400">
                      <Calendar className={`w-4 h-4 ${language === 'ar' ? 'ml-1' : 'mr-1'}`} />
                      {formatDate(update.created_at)}
                    </div>
                  </div>

                  <div className={`flex space-x-2 ml-4 ${isRTL ? "flex-row-reverse space-x-reverse mr-4 ml-0" : ""}`}>
                    <Link
                      to={`/progress-updates/edit/${update.id}`}
                      className="p-2 bg-blue-900/50 hover:bg-blue-800/50 rounded-md transition-colors"
                      title={t('common.edit', 'Edit')}
                    >
                      <Edit size={16} />
                    </Link>
                    <button
                      onClick={() => openDeleteModal(update)}
                      className="p-2 bg-red-900/50 hover:bg-red-800/50 rounded-md transition-colors"
                      title={t('common.delete', 'Delete')}
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20">
            <TrendingUp size={48} className="mx-auto text-gray-500 mb-4" />
            <RTLText as="h3" className="text-xl font-semibold mb-2">
              {searchTerm || selectedBusinessIdea ? t('progress.noUpdatesFound', 'No progress updates found') : t('progress.noUpdatesYet', 'No progress updates yet')}
            </RTLText>
            <RTLText as="div" className="text-gray-400 mb-4">
              {searchTerm || selectedBusinessIdea
                ? t('progress.tryDifferentSearch', 'Try a different search term or filter')
                : businessIdeas.length > 0 
                  ? t('progress.addFirstUpdate', 'Add your first progress update to track your journey')
                  : t('progress.createBusinessIdeaFirst', 'Create a business idea first to start tracking progress')
              }
            </RTLText>
            {businessIdeas.length > 0 ? (
              <button
                onClick={openCreateModal}
                className="px-4 py-2 bg-purple-600 rounded-lg hover:bg-purple-700 transition-colors inline-flex items-center"
              >
                <Plus className={`w-4 h-4 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                {t('progress.addUpdate', 'Add Progress Update')}
              </button>
            ) : (
              <Link
                to="/dashboard/business-ideas"
                className="px-4 py-2 bg-purple-600 rounded-lg hover:bg-purple-700 transition-colors inline-flex items-center"
              >
                <Target className={`w-4 h-4 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                {t('progress.createBusinessIdea', 'Create Business Idea')}
              </Link>
            )}
          </div>
        )}
      </div>

      {/* Create Progress Update Modal */}
      {isCreateModalOpen && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
          <div className="bg-indigo-900 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto p-6">
            <RTLText as="h3" className="text-xl font-bold mb-6">{t('progress.addUpdate', 'Add Progress Update')}</RTLText>
            
            <form onSubmit={handleCreateUpdate} className="space-y-4">
              <div>
                <label className="block text-gray-300 mb-2">{t('progress.businessIdea', 'Business Idea')} *</label>
                <select
                  name="business_idea"
                  value={formData.business_idea}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                  required
                >
                  <option value="">{t('progress.selectBusinessIdea', 'Select a business idea')}</option>
                  {businessIdeas.map(idea => (
                    <option key={idea.id} value={idea.id}>{idea.title}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-gray-300 mb-2">{t('progress.title', 'Update Title')} *</label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                  required
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                />
              </div>

              <div>
                <label className="block text-gray-300 mb-2">{t('progress.description', 'Description')} *</label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-4 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none"
                  required
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                />
              </div>

              <div>
                <label className="block text-gray-300 mb-2">{t('progress.percentage', 'Progress Percentage')} *</label>
                <input
                  type="number"
                  name="progress_percentage"
                  value={formData.progress_percentage}
                  onChange={handleInputChange}
                  min="0"
                  max="100"
                  className="w-full px-4 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                  required
                />
              </div>

              <div>
                <label className="flex items-center space-x-2 text-gray-300">
                  <input
                    type="checkbox"
                    name="milestone_achieved"
                    checked={formData.milestone_achieved}
                    onChange={handleInputChange}
                    className="rounded"
                  />
                  <span>{t('progress.milestoneAchieved', 'Milestone Achieved')}</span>
                </label>
              </div>

              <div>
                <label className="block text-gray-300 mb-2">{t('progress.challenges', 'Challenges Faced')} (optional)</label>
                <textarea
                  name="challenges_faced"
                  value={formData.challenges_faced}
                  onChange={handleInputChange}
                  rows={2}
                  className="w-full px-4 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none"
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                />
              </div>

              <div>
                <label className="block text-gray-300 mb-2">{t('progress.nextSteps', 'Next Steps')} (optional)</label>
                <textarea
                  name="next_steps"
                  value={formData.next_steps}
                  onChange={handleInputChange}
                  rows={2}
                  className="w-full px-4 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none"
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                />
              </div>

              {error && (
                <div className="p-3 bg-red-900/50 border border-red-500 rounded-md text-red-200">
                  {error}
                </div>
              )}

              <div className={`flex justify-end space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                <button
                  type="button"
                  onClick={() => setIsCreateModalOpen(false)}
                  className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
                  disabled={creating}
                >
                  {t('common.cancel', 'Cancel')}
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors flex items-center disabled:opacity-50"
                  disabled={creating}
                >
                  {creating ? (
                    <>
                      <Loader2 className={`w-4 h-4 animate-spin ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                      {t('common.creating', 'Creating...')}
                    </>
                  ) : (
                    <>
                      <Plus className={`w-4 h-4 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                      {t('progress.addUpdate', 'Add Update')}
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {isDeleteModalOpen && selectedUpdate && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
          <div className="bg-indigo-900 rounded-lg w-full max-w-md p-6">
            <RTLText as="h3" className="text-xl font-bold mb-4">{t('progress.confirmDelete', 'Confirm Delete')}</RTLText>
            <RTLText as="div" className="text-gray-300 mb-6">
              {t('progress.deleteWarning', 'Are you sure you want to delete this progress update? This action cannot be undone.')}
            </RTLText>
            <RTLText as="div" className="text-sm text-gray-400 mb-6 p-3 bg-indigo-950/50 rounded border-l-4 border-purple-500">
              <strong>{selectedUpdate.title}</strong>
              <br />
              <span className="text-purple-400">{selectedUpdate.business_idea.title}</span>
            </RTLText>
            <div className={`flex justify-end space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
              <button
                onClick={() => setIsDeleteModalOpen(false)}
                className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
                disabled={deleting}
              >
                {t('common.cancel', 'Cancel')}
              </button>
              <button
                onClick={handleDeleteUpdate}
                className="px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors flex items-center disabled:opacity-50"
                disabled={deleting}
              >
                {deleting ? (
                  <>
                    <Loader2 className={`w-4 h-4 animate-spin ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                    {t('common.deleting', 'Deleting...')}
                  </>
                ) : (
                  <>
                    <Trash2 className={`w-4 h-4 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                    {t('common.delete', 'Delete')}
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserProgressUpdatesPage;
