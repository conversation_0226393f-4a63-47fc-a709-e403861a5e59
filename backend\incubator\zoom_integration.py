"""
Zoom API integration for video conferencing.
"""
import os
import json
import time
import requests
import jwt
from datetime import datetime, timedelta
from dotenv import load_dotenv
from django.conf import settings
from .video_conferencing import VideoConferencingProvider, VideoConferencingError

# Load environment variables
load_dotenv()

# Zoom API credentials
ZOOM_API_KEY = os.getenv('ZOOM_API_KEY')
ZOOM_API_SECRET = os.getenv('ZOOM_API_SECRET')
ZOOM_USER_ID = os.getenv('ZOOM_USER_ID')  # The user ID or email of the Zoom account

class ZoomProvider(VideoConferencingProvider):
    """Zoom API integration for video conferencing"""
    
    def __init__(self):
        """Initialize with API credentials"""
        self.api_key = ZOOM_API_KEY
        self.api_secret = ZOOM_API_SECRET
        self.user_id = ZOOM_USER_ID
        
        if not all([self.api_key, self.api_secret, self.user_id]):
            raise VideoConferencingError("Zoom API credentials are not configured")
    
    def _generate_jwt_token(self):
        """Generate a JWT token for Zoom API authentication"""
        token_exp = datetime.utcnow() + timedelta(minutes=60)
        
        payload = {
            'iss': self.api_key,
            'exp': int(token_exp.timestamp())
        }
        
        return jwt.encode(payload, self.api_secret, algorithm='HS256')
    
    def _make_api_request(self, method, endpoint, data=None):
        """Make a request to the Zoom API"""
        token = self._generate_jwt_token()
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        url = f'https://api.zoom.us/v2/{endpoint}'
        
        try:
            if method.lower() == 'get':
                response = requests.get(url, headers=headers)
            elif method.lower() == 'post':
                response = requests.post(url, headers=headers, json=data)
            elif method.lower() == 'patch':
                response = requests.patch(url, headers=headers, json=data)
            elif method.lower() == 'delete':
                response = requests.delete(url, headers=headers)
            else:
                raise VideoConferencingError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            
            if response.status_code == 204:  # No content
                return {}
                
            return response.json()
            
        except requests.exceptions.RequestException as e:
            error_message = str(e)
            if hasattr(e, 'response') and e.response is not None:
                try:
                    error_data = e.response.json()
                    error_message = error_data.get('message', str(e))
                except:
                    pass
            
            raise VideoConferencingError(f"Zoom API error: {error_message}")
    
    def create_meeting(self, title, start_time, duration_minutes, **kwargs):
        """
        Create a Zoom meeting
        
        Args:
            title: Meeting title
            start_time: Start time (datetime)
            duration_minutes: Duration in minutes
            
        Returns:
            dict: Meeting details including id, password, and join URL
        """
        # Format start time for Zoom API (ISO 8601 format)
        start_time_str = start_time.strftime('%Y-%m-%dT%H:%M:%S')
        
        # Prepare meeting data
        meeting_data = {
            'topic': title,
            'type': 2,  # Scheduled meeting
            'start_time': start_time_str,
            'duration': duration_minutes,
            'timezone': 'UTC',
            'settings': {
                'host_video': True,
                'participant_video': True,
                'join_before_host': True,
                'mute_upon_entry': False,
                'waiting_room': False,
                'auto_recording': 'none'
            }
        }
        
        # Create meeting
        response = self._make_api_request('post', f'users/{self.user_id}/meetings', meeting_data)
        
        # Extract meeting details
        meeting_id = response.get('id')
        meeting_password = response.get('password')
        join_url = response.get('join_url')
        
        return {
            'provider': 'zoom',
            'meeting_id': str(meeting_id),
            'meeting_password': meeting_password,
            'meeting_link': join_url,
            'host_link': response.get('start_url'),
            'raw_data': response
        }
    
    def update_meeting(self, meeting_id, **kwargs):
        """
        Update an existing Zoom meeting
        
        Args:
            meeting_id: Zoom meeting ID
            **kwargs: Meeting parameters to update
            
        Returns:
            dict: Updated meeting details
        """
        # Extract parameters
        title = kwargs.get('title')
        start_time = kwargs.get('start_time')
        duration_minutes = kwargs.get('duration_minutes')
        
        # Prepare update data
        update_data = {}
        
        if title:
            update_data['topic'] = title
            
        if start_time:
            update_data['start_time'] = start_time.strftime('%Y-%m-%dT%H:%M:%S')
            
        if duration_minutes:
            update_data['duration'] = duration_minutes
        
        # Update meeting
        self._make_api_request('patch', f'meetings/{meeting_id}', update_data)
        
        # Get updated meeting details
        response = self._make_api_request('get', f'meetings/{meeting_id}')
        
        return {
            'provider': 'zoom',
            'meeting_id': str(response.get('id')),
            'meeting_password': response.get('password'),
            'meeting_link': response.get('join_url'),
            'host_link': response.get('start_url'),
            'raw_data': response
        }
    
    def delete_meeting(self, meeting_id):
        """
        Delete/cancel a Zoom meeting
        
        Args:
            meeting_id: Zoom meeting ID
            
        Returns:
            bool: True if successful
        """
        self._make_api_request('delete', f'meetings/{meeting_id}')
        return True
    
    def get_join_url(self, meeting_id, **kwargs):
        """
        Get URL to join the Zoom meeting
        
        Args:
            meeting_id: Zoom meeting ID
            
        Returns:
            str: Join URL
        """
        response = self._make_api_request('get', f'meetings/{meeting_id}')
        return response.get('join_url', '')


class GoogleMeetProvider(VideoConferencingProvider):
    """Google Meet integration for video conferencing"""
    
    def __init__(self):
        """Initialize with API credentials"""
        # This is a placeholder for Google Meet integration
        # Actual implementation would require Google Calendar API
        pass
    
    def create_meeting(self, title, start_time, duration_minutes, **kwargs):
        """Create a Google Meet meeting (placeholder)"""
        # This is a placeholder for Google Meet integration
        # In a real implementation, this would create a Google Calendar event with Meet
        
        # Generate a fake meeting link for demonstration
        meeting_id = f"meet-{int(time.time())}"
        join_url = f"https://meet.google.com/{meeting_id}"
        
        return {
            'provider': 'google_meet',
            'meeting_id': meeting_id,
            'meeting_password': None,  # Google Meet doesn't use passwords
            'meeting_link': join_url,
            'host_link': join_url,  # Same link for host and participants
        }
    
    def update_meeting(self, meeting_id, **kwargs):
        """Update a Google Meet meeting (placeholder)"""
        # Placeholder
        return self.create_meeting(
            kwargs.get('title', 'Updated Meeting'),
            kwargs.get('start_time', datetime.now()),
            kwargs.get('duration_minutes', 60)
        )
    
    def delete_meeting(self, meeting_id):
        """Delete a Google Meet meeting (placeholder)"""
        # Placeholder
        return True
    
    def get_join_url(self, meeting_id, **kwargs):
        """Get URL to join the Google Meet meeting (placeholder)"""
        # Placeholder
        return f"https://meet.google.com/{meeting_id}"
