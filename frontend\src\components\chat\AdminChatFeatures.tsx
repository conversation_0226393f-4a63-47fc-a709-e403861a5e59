import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import {
  Shield,
  Users,
  Eye,
  AlertTriangle,
  MessageSquareMore,
  Database,
  Settings,
  BarChart3,
  FileText,
  Search,
  Filter,
  Download,
  UserCheck,
  Ban,
  Flag
} from 'lucide-react';

interface AdminChatFeaturesProps {
  onUserImpersonation: () => void;
  onChatModeration: () => void;
  onBulkOperations: () => void;
  onSystemCommands: () => void;
  onChatMonitoring: () => void;
  onAdvancedAnalytics: () => void;
  onResponseTemplates: () => void;
  onEscalationSystem: () => void;
  messageCount: number;
  activeUsers: number;
}

const AdminChatFeatures: React.FC<AdminChatFeaturesProps> = ({ onUserImpersonation,
  onChatModeration,
  onBulkOperations,
  onSystemCommands,
  onChatMonitoring,
  onAdvancedAnalytics,
  onResponseTemplates,
  onEscalationSystem,
  messageCount,
  activeUsers
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [showAdminFeatures, setShowAdminFeatures] = useState(false);

  const adminFeatures = [
    {
      id: 'impersonation',
      icon: <UserCheck size={16} />,
      label: t('admin.chat.userImpersonation'),
      description: t('admin.chat.impersonationDesc'),
      onClick: onUserImpersonation,
      category: 'support'
    },
    {
      id: 'moderation',
      icon: <Shield size={16} />,
      label: t('admin.chat.chatModeration'),
      description: t('admin.chat.moderationDesc'),
      onClick: onChatModeration,
      category: 'moderation'
    },
    {
      id: 'bulk',
      icon: <Database size={16} />,
      label: t('admin.chat.bulkOperations'),
      description: t('admin.chat.bulkDesc'),
      onClick: onBulkOperations,
      category: 'management'
    },
    {
      id: 'system',
      icon: <Settings size={16} />,
      label: t('admin.chat.systemCommands'),
      description: t('admin.chat.systemDesc'),
      onClick: onSystemCommands,
      category: 'system'
    },
    {
      id: 'monitoring',
      icon: <Eye size={16} />,
      label: t('admin.chat.chatMonitoring'),
      description: t('admin.chat.monitoringDesc'),
      onClick: onChatMonitoring,
      category: 'monitoring'
    },
    {
      id: 'analytics',
      icon: <BarChart3 size={16} />,
      label: t('admin.chat.advancedAnalytics'),
      description: t('admin.chat.analyticsDesc'),
      onClick: onAdvancedAnalytics,
      category: 'analytics'
    },
    {
      id: 'templates',
      icon: <MessageSquareMore size={16} />,
      label: t('admin.chat.responseTemplates'),
      description: t('admin.chat.templatesDesc'),
      onClick: onResponseTemplates,
      category: 'templates'
    },
    {
      id: 'escalation',
      icon: <Flag size={16} />,
      label: t('admin.chat.escalationSystem'),
      description: t('admin.chat.escalationDesc'),
      onClick: onEscalationSystem,
      category: 'escalation'
    }
  ];

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'support':
        return 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700 text-blue-700 dark:text-blue-300';
      case 'moderation':
        return 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-700 text-red-700 dark:text-red-300';
      case 'management':
        return 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700 text-green-700 dark:text-green-300';
      case 'system':
        return 'bg-purple-50 dark:bg-purple-900/20 border-purple-200 dark:border-purple-700 text-purple-700 dark:text-purple-300';
      case 'monitoring':
        return 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-700 text-yellow-700 dark:text-yellow-300';
      case 'analytics':
        return 'bg-indigo-50 dark:bg-indigo-900/20 border-indigo-200 dark:border-indigo-700 text-indigo-700 dark:text-indigo-300';
      case 'templates':
        return 'bg-pink-50 dark:bg-pink-900/20 border-pink-200 dark:border-pink-700 text-pink-700 dark:text-pink-300';
      case 'escalation':
        return 'bg-orange-50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-700 text-orange-700 dark:text-orange-300';
      default:
        return 'bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300';
    }
  };

  return (
    <div className="relative">
      {/* Admin Features Toggle Button */}
      <button
        onClick={() => setShowAdminFeatures(!showAdminFeatures)}
        className={`p-2 rounded-md text-red-600 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/20 transition-colors ${
          showAdminFeatures ? 'bg-red-100 dark:bg-red-900/20' : ''}
        }`}
        title={t('admin.chat.adminFeatures')}
      >
        <Shield size={18} />
      </button>

      {/* Admin Features Panel */}
      {showAdminFeatures && (
        <div className={`absolute ${isRTL ? 'left-0' : 'right-0'} top-full mt-2 w-96 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50`}>
          <div className="p-4">
            <div className={`flex items-center mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <Shield size={20} className={`text-red-600 ${isRTL ? 'ml-2' : 'mr-2'}`} />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {t('admin.chat.adminFeatures')}
              </h3>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-2 gap-3 mb-4">
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                <div className="text-sm text-gray-600 dark:text-gray-400">{t('admin.chat.totalMessages')}</div>
                <div className="text-lg font-semibold text-gray-900 dark:text-white">{messageCount}</div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                <div className="text-sm text-gray-600 dark:text-gray-400">{t('admin.chat.activeUsers')}</div>
                <div className="text-lg font-semibold text-gray-900 dark:text-white">{activeUsers}</div>
              </div>
            </div>

            {/* Admin Feature Grid */}
            <div className="grid grid-cols-2 gap-2">
              {adminFeatures.map((feature) => (
                <button
                  key={feature.id}
                  onClick={feature.onClick}
                  className={`p-3 rounded-lg border text-left transition-colors hover:shadow-md ${getCategoryColor(feature.category)} ${isRTL ? 'text-right' : 'text-left'}`}
                >
                  <div className={`flex items-center mb-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <span className={isRTL ? 'ml-2' : 'mr-2'}>{feature.icon}</span>
                    <span className="text-sm font-medium">{feature.label}</span>
                  </div>
                  <p className="text-xs opacity-80">
                    {feature.description}
                  </p>
                </button>
              ))}
            </div>

            {/* Admin Warning */}
            <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg">
              <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                <AlertTriangle size={16} className={`text-red-600 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                <span className="text-sm text-red-700 dark:text-red-300 font-medium">
                  {t('admin.chat.adminWarning')}
                </span>
              </div>
              <p className="text-xs text-red-600 dark:text-red-400 mt-1">
                {t('admin.chat.adminWarningDesc')}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminChatFeatures;
