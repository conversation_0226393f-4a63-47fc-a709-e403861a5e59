from rest_framework import viewsets, permissions, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from django.db.models import Q
from django_filters.rest_framework import DjangoFilterBackend
from django.contrib.auth.models import User

from incubator.models import BusinessIdea
from .models import AIRecommendation, AIRecommendationFeedback
from .serializers import AIRecommendationSerializer, AIRecommendationFeedbackSerializer
from core.ai_service import ai_chat
from api.permissions import IsAdminUser


class AIRecommendationViewSet(viewsets.ModelViewSet):
    queryset = AIRecommendation.objects.all().order_by('-created_at')
    serializer_class = AIRecommendationSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['business_idea', 'recommendation_type', 'priority', 'is_implemented']
    search_fields = ['title', 'description', 'reasoning', 'action_items']
    ordering_fields = ['created_at', 'relevance_score', 'priority']

    def get_queryset(self):
        # For regular users, only show recommendations for their own business ideas
        if self.request.user.is_authenticated and not (self.request.user.is_staff or self.request.user.is_superuser):
            return AIRecommendation.objects.filter(
                Q(business_idea__owner=self.request.user) | 
                Q(business_idea__collaborators=self.request.user)
            ).distinct().order_by('-created_at')
        return super().get_queryset()

    @action(detail=False, methods=['post'])
    def generate_recommendations(self, request):
        """Generate AI recommendations for a business idea"""
        business_idea_id = request.data.get('business_idea_id')
        
        if not business_idea_id:
            return Response(
                {"error": "Business idea ID is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            business_idea = BusinessIdea.objects.get(id=business_idea_id)
        except BusinessIdea.DoesNotExist:
            return Response(
                {"error": "Business idea not found"},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Check if user has permission to generate recommendations for this idea
        if not (request.user.is_staff or request.user.is_superuser or 
                request.user == business_idea.owner or 
                business_idea.collaborators.filter(id=request.user.id).exists()):
            return Response(
                {"error": "You don't have permission to generate recommendations for this idea"},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Generate recommendations using centralized AI service
        business_idea = BusinessIdea.objects.get(id=business_idea_id)
        prompt = f"Generate business recommendations for the following idea: {business_idea.title}. Description: {business_idea.description}"
        ai_response = ai_chat(prompt, language='en', user_id=request.user.id)
        recommendations = ai_response.get('response', '')
        
        if not recommendations:
            return Response(
                {"error": "Failed to generate recommendations. Please try again later."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        
        return Response({
            "message": f"Generated {len(recommendations)} AI recommendations",
            "recommendations": AIRecommendationSerializer(recommendations, many=True).data
        })

    @action(detail=True, methods=['post'])
    def mark_implemented(self, request, pk=None):
        """Mark a recommendation as implemented"""
        recommendation = self.get_object()
        
        # Check if user has permission to update this recommendation
        if not (request.user.is_staff or request.user.is_superuser or 
                request.user == recommendation.business_idea.owner or 
                recommendation.business_idea.collaborators.filter(id=request.user.id).exists()):
            return Response(
                {"error": "You don't have permission to update this recommendation"},
                status=status.HTTP_403_FORBIDDEN
            )
        
        implementation_notes = request.data.get('implementation_notes', '')
        
        recommendation.is_implemented = True
        recommendation.implementation_notes = implementation_notes
        recommendation.implemented_by = request.user
        recommendation.implemented_at = timezone.now()
        recommendation.save()
        
        return Response({
            "message": "Recommendation marked as implemented",
            "recommendation": AIRecommendationSerializer(recommendation).data
        })


class AIRecommendationFeedbackViewSet(viewsets.ModelViewSet):
    queryset = AIRecommendationFeedback.objects.all().order_by('-created_at')
    serializer_class = AIRecommendationFeedbackSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['recommendation', 'user', 'feedback_type']

    def get_queryset(self):
        # For regular users, only show feedback for recommendations on their own business ideas
        if self.request.user.is_authenticated and not (self.request.user.is_staff or self.request.user.is_superuser):
            return AIRecommendationFeedback.objects.filter(
                Q(recommendation__business_idea__owner=self.request.user) | 
                Q(recommendation__business_idea__collaborators=self.request.user) |
                Q(user=self.request.user)
            ).distinct().order_by('-created_at')
        return super().get_queryset()

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)
