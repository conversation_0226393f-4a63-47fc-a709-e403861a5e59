import React, { useState, useEffect } from 'react';
import { X, User, Building, DollarSign, Target } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../hooks/useLanguage';
import { useCreateInvestorProfile, useUpdateInvestorProfile } from '../../../../hooks/useFunding';
import { InvestorProfile } from '../../../../services/incubatorApi';
import { Button } from '../../../ui';
import { validateRequired, validatePositiveNumber, validateGreaterThan } from '../../../../utils/localeValidation';

interface InvestorProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
  profile: InvestorProfile | null;
  isEditing: boolean;
}

interface FormData {
  user_id: number;
  organization: string;
  title: string;
  bio: string;
  min_investment: number;
  max_investment: number;
  investment_focus: string[];
  preferred_stages: string[];
  geographic_focus: string[];
  portfolio_size: number;
  years_experience: number;
  linkedin_url: string;
  website_url: string;
  is_verified: boolean;
  is_active: boolean;
}

const InvestorProfileModal: React.FC<InvestorProfileModalProps> = ({
  isOpen,
  onClose,
  profile,
  isEditing
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  const createProfile = useCreateInvestorProfile();
  const updateProfile = useUpdateInvestorProfile();

  const [formData, setFormData] = useState<FormData>({
    user_id: 0,
    organization: '',
    title: '',
    bio: '',
    min_investment: 0,
    max_investment: 0,
    investment_focus: [],
    preferred_stages: [],
    geographic_focus: [],
    portfolio_size: 0,
    years_experience: 0,
    linkedin_url: '',
    website_url: '',
    is_verified: false,
    is_active: true
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeSection, setActiveSection] = useState('basic');

  // Initialize form data when profile changes
  useEffect(() => {
    if (isEditing && profile) {
      setFormData({
        user_id: profile.user.id,
        organization: profile.organization || '',
        title: profile.title || '',
        bio: profile.bio || '',
        min_investment: profile.min_investment || 0,
        max_investment: profile.max_investment || 0,
        investment_focus: profile.investment_focus || [],
        preferred_stages: profile.preferred_stages || [],
        geographic_focus: profile.geographic_focus || [],
        portfolio_size: profile.portfolio_size || 0,
        years_experience: profile.years_experience || 0,
        linkedin_url: profile.linkedin_url || '',
        website_url: profile.website_url || '',
        is_verified: profile.is_verified || false,
        is_active: profile.is_active || true
      });
    } else {
      // Reset form for create mode
      setFormData({
        user_id: 0,
        organization: '',
        title: '',
        bio: '',
        min_investment: 0,
        max_investment: 0,
        investment_focus: [],
        preferred_stages: [],
        geographic_focus: [],
        portfolio_size: 0,
        years_experience: 0,
        linkedin_url: '',
        website_url: '',
        is_verified: false,
        is_active: true
      });
    }
    setErrors({});
  }, [isEditing, profile]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked :
              type === 'number' ? Number(value) : value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleArrayChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: (prev[field] as string[]).includes(value)
        ? (prev[field] as string[]).filter(item => item !== value)
        : [...(prev[field] as string[]), value]
    }));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!validateRequired(formData.organization)) {
      newErrors.organization = t('validation.required', 'This field is required');
    }
    if (!validateRequired(formData.title)) {
      newErrors.title = t('validation.required', 'This field is required');
    }
    if (!validateRequired(formData.bio)) {
      newErrors.bio = t('validation.required', 'This field is required');
    }
    if (!validatePositiveNumber(formData.min_investment)) {
      newErrors.min_investment = t('validation.positiveNumber', 'Must be a positive number');
    }
    if (!validatePositiveNumber(formData.max_investment)) {
      newErrors.max_investment = t('validation.positiveNumber', 'Must be a positive number');
    }
    if (!validateGreaterThan(formData.max_investment, formData.min_investment)) {
      newErrors.max_investment = t('validation.maxGreaterThanMin', 'Maximum must be greater than minimum');
    }
    if (!validateRequired(formData.investment_focus)) {
      newErrors.investment_focus = t('validation.selectAtLeastOne', 'Select at least one focus area');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      if (isEditing && profile) {
        await updateProfile.mutateAsync({
          id: profile.id,
          data: formData
        });
      } else {
        await createProfile.mutateAsync(formData);
      }
      onClose();
    } catch (error) {
      console.error('Failed to save investor profile:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const sections = [
    { id: 'basic', label: t('investor.basicInfo', 'Basic Information'), icon: User },
    { id: 'investment', label: t('investor.investmentDetails', 'Investment Details'), icon: DollarSign },
    { id: 'preferences', label: t('investor.preferences', 'Preferences'), icon: Target }
  ];

  const investmentFocusOptions = [
    'Technology', 'Healthcare', 'FinTech', 'E-commerce', 'Sustainability',
    'AI/ML', 'Blockchain', 'IoT', 'Cybersecurity', 'EdTech', 'FoodTech', 'PropTech'
  ];

  const stageOptions = [
    'Pre-seed', 'Seed', 'Series A', 'Series B', 'Series C', 'Growth', 'Late Stage'
  ];

  const geographicOptions = [
    'North America', 'Europe', 'Asia Pacific', 'Latin America', 'Middle East', 'Africa', 'Global'
  ];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-indigo-900/90 rounded-xl shadow-lg w-full max-w-4xl max-h-[90vh] overflow-hidden">
        <div className="flex h-full">
          {/* Sidebar */}
          <div className="w-64 bg-indigo-800/50 border-r border-indigo-700/50 p-4">
            <h3 className="text-lg font-semibold text-white mb-4">
              {isEditing ? t('investor.editProfile', 'Edit Profile') : t('investor.createProfile', 'Create Profile')}
            </h3>
            <nav className="space-y-2">
              {sections.map((section) => {
                const Icon = section.icon;
                return (
                  <button
                    key={section.id}
                    onClick={() => setActiveSection(section.id)}
                    className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-colors ${
                      activeSection === section.id
                        ? 'bg-purple-600 text-white'
                        : 'text-gray-300 hover:bg-indigo-700/50'
                    }`}
                  >
                    <Icon size={16} />
                    {section.label}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Main Content */}
          <div className="flex-1 flex flex-col">
            {/* Header */}
            <div className="flex justify-between items-center p-6 border-b border-indigo-700/50">
              <h2 className="text-xl font-bold text-white">
                {activeSection === 'basic' ? t('investor.basicInfo', 'Basic Information') : 
                 activeSection === 'investment' ? t('investor.investmentDetails', 'Investment Details') :
                 t('investor.preferences', 'Preferences')}
              </h2>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-white"
              >
                <X size={20} />
              </button>
            </div>

            {/* Form Content */}
            <div className="flex-1 overflow-y-auto p-6">
              <form onSubmit={handleSubmit} className="space-y-6">
                {activeSection === 'basic' && (
                  <>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-gray-300 mb-2">
                          {t('investor.organization', 'Organization')} *
                        </label>
                        <input
                          type="text"
                          name="organization"
                          value={formData.organization}
                          onChange={handleInputChange}
                          className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                          placeholder={t('investor.organizationPlaceholder', 'Enter organization name')}
                        />
                        {errors.organization && <p className="text-red-400 text-sm mt-1">{errors.organization}</p>}
                      </div>

                      <div>
                        <label className="block text-gray-300 mb-2">
                          {t('investor.title', 'Title/Position')} *
                        </label>
                        <input
                          type="text"
                          name="title"
                          value={formData.title}
                          onChange={handleInputChange}
                          className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                          placeholder={t('investor.titlePlaceholder', 'Enter title or position')}
                        />
                        {errors.title && <p className="text-red-400 text-sm mt-1">{errors.title}</p>}
                      </div>
                    </div>

                    <div>
                      <label className="block text-gray-300 mb-2">
                        {t('investor.bio', 'Bio')} *
                      </label>
                      <textarea
                        name="bio"
                        value={formData.bio}
                        onChange={handleInputChange}
                        rows={4}
                        className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                        placeholder={t('investor.bioPlaceholder', 'Tell us about your investment experience and approach...')}
                      />
                      {errors.bio && <p className="text-red-400 text-sm mt-1">{errors.bio}</p>}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-gray-300 mb-2">
                          {t('investor.yearsExperience', 'Years of Experience')}
                        </label>
                        <input
                          type="number"
                          name="years_experience"
                          value={formData.years_experience}
                          onChange={handleInputChange}
                          min="0"
                          className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                        />
                      </div>

                      <div>
                        <label className="block text-gray-300 mb-2">
                          {t('investor.portfolioSize', 'Portfolio Size')}
                        </label>
                        <input
                          type="number"
                          name="portfolio_size"
                          value={formData.portfolio_size}
                          onChange={handleInputChange}
                          min="0"
                          className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-gray-300 mb-2">
                          {t('investor.linkedinUrl', 'LinkedIn URL')}
                        </label>
                        <input
                          type="url"
                          name="linkedin_url"
                          value={formData.linkedin_url}
                          onChange={handleInputChange}
                          className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                          placeholder="https://linkedin.com/in/..."
                        />
                      </div>

                      <div>
                        <label className="block text-gray-300 mb-2">
                          {t('investor.websiteUrl', 'Website URL')}
                        </label>
                        <input
                          type="url"
                          name="website_url"
                          value={formData.website_url}
                          onChange={handleInputChange}
                          className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                          placeholder="https://..."
                        />
                      </div>
                    </div>

                    <div className="flex items-center gap-4">
                      <label className="flex items-center text-gray-300">
                        <input
                          type="checkbox"
                          name="is_verified"
                          checked={formData.is_verified}
                          onChange={handleInputChange}
                          className="rounded border-gray-600 bg-indigo-800 text-purple-500 focus:ring-purple-500 mr-2"
                        />
                        {t('investor.verified', 'Verified Investor')}
                      </label>
                      <label className="flex items-center text-gray-300">
                        <input
                          type="checkbox"
                          name="is_active"
                          checked={formData.is_active}
                          onChange={handleInputChange}
                          className="rounded border-gray-600 bg-indigo-800 text-purple-500 focus:ring-purple-500 mr-2"
                        />
                        {t('investor.active', 'Active')}
                      </label>
                    </div>
                  </>
                )}

                {activeSection === 'investment' && (
                  <>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-gray-300 mb-2">
                          {t('investor.minInvestment', 'Minimum Investment ($)')} *
                        </label>
                        <input
                          type="number"
                          name="min_investment"
                          value={formData.min_investment}
                          onChange={handleInputChange}
                          min="0"
                          className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                        />
                        {errors.min_investment && <p className="text-red-400 text-sm mt-1">{errors.min_investment}</p>}
                      </div>

                      <div>
                        <label className="block text-gray-300 mb-2">
                          {t('investor.maxInvestment', 'Maximum Investment ($)')} *
                        </label>
                        <input
                          type="number"
                          name="max_investment"
                          value={formData.max_investment}
                          onChange={handleInputChange}
                          min="0"
                          className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                        />
                        {errors.max_investment && <p className="text-red-400 text-sm mt-1">{errors.max_investment}</p>}
                      </div>
                    </div>

                    <div>
                      <label className="block text-gray-300 mb-2">
                        {t('investor.investmentFocus', 'Investment Focus Areas')} *
                      </label>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                        {investmentFocusOptions.map((focus) => (
                          <label key={focus} className="flex items-center text-gray-300">
                            <input
                              type="checkbox"
                              checked={formData.investment_focus.includes(focus)}
                              onChange={() => handleArrayChange('investment_focus', focus)}
                              className="rounded border-gray-600 bg-indigo-800 text-purple-500 focus:ring-purple-500 mr-2"
                            />
                            {focus}
                          </label>
                        ))}
                      </div>
                      {errors.investment_focus && <p className="text-red-400 text-sm mt-1">{errors.investment_focus}</p>}
                    </div>
                  </>
                )}

                {activeSection === 'preferences' && (
                  <>
                    <div>
                      <label className="block text-gray-300 mb-2">
                        {t('investor.preferredStages', 'Preferred Investment Stages')}
                      </label>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                        {stageOptions.map((stage) => (
                          <label key={stage} className="flex items-center text-gray-300">
                            <input
                              type="checkbox"
                              checked={formData.preferred_stages.includes(stage)}
                              onChange={() => handleArrayChange('preferred_stages', stage)}
                              className="rounded border-gray-600 bg-indigo-800 text-purple-500 focus:ring-purple-500 mr-2"
                            />
                            {stage}
                          </label>
                        ))}
                      </div>
                    </div>

                    <div>
                      <label className="block text-gray-300 mb-2">
                        {t('investor.geographicFocus', 'Geographic Focus')}
                      </label>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                        {geographicOptions.map((region) => (
                          <label key={region} className="flex items-center text-gray-300">
                            <input
                              type="checkbox"
                              checked={formData.geographic_focus.includes(region)}
                              onChange={() => handleArrayChange('geographic_focus', region)}
                              className="rounded border-gray-600 bg-indigo-800 text-purple-500 focus:ring-purple-500 mr-2"
                            />
                            {region}
                          </label>
                        ))}
                      </div>
                    </div>
                  </>
                )}
              </form>
            </div>

            {/* Footer */}
            <div className="flex justify-end gap-3 p-6 border-t border-indigo-700/50">
              <Button
                type="button"
                onClick={onClose}
                variant="secondary"
                disabled={isSubmitting}
              >
                {t('common.cancel', 'Cancel')}
              </Button>
              <Button
                type="submit"
                onClick={handleSubmit}
                disabled={isSubmitting}
              >
                {isSubmitting ? t('common.saving', 'Saving...') : 
                 isEditing ? t('common.update', 'Update') : t('common.create', 'Create')}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InvestorProfileModal;
