import React from 'react';
import { <PERSON>, <PERSON>, <PERSON><PERSON>pen, DollarSign, ArrowRight, Spark<PERSON> } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../store/hooks';
import { RTLText, RTLFlex, RTLIcon } from './common';
import { Link } from 'react-router-dom';
import { useLanguage } from '../hooks/useLanguage';

const About = () => {
  const { t } = useTranslation();
  const { language } = useAppSelector(state => state.language);
  const { isRTL } = useLanguage();

  return (
    <section id="about" className="py-20 relative overflow-hidden">
      <div className="container mx-auto px-4">
        {/* Header Section - Hero Style */}
        <div className="max-w-4xl mx-auto text-center mb-16">
          <div className="mb-6">
            <div className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium mb-6 glass-light text-glass-primary border border-glass-border ${isRTL ? "flex-row-reverse" : ""}`}>
              <Sparkles className={`w-4 h-4 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
              Virtual Incubator • AI-Powered • Expert Mentorship
            </div>
          </div>

          <RTLText as="h2" className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400">
              {t('home.about.title')} {t('app.name')}
            </span>
          </RTLText>

          <RTLText as="p" className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto leading-relaxed text-glass-secondary">
            {t('home.about.description')}
          </RTLText>
        </div>

        {/* Features Grid - Hero Style */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {/* Card 1 - Community */}
          <div className="p-6 rounded-2xl backdrop-blur-sm glass-light border border-glass-border hover:scale-105 transition-all duration-300">
            <div className="flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 mb-4 mx-auto">
              <RTLIcon icon={Users} size={24} className="text-white" />
            </div>
            <RTLText as="h3" className="text-lg font-semibold mb-2 text-glass-primary text-center">
              {t('home.about.community')}
            </RTLText>
            <RTLText as="p" className="text-sm text-glass-secondary text-center">
              {t('home.about.communityDescription')}
            </RTLText>
          </div>

          {/* Card 2 - Mentorship */}
          <div className="p-6 rounded-2xl backdrop-blur-sm glass-light border border-glass-border hover:scale-105 transition-all duration-300">
            <div className="flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-cyan-500 mb-4 mx-auto">
              <RTLIcon icon={Brain} size={24} className="text-white" />
            </div>
            <RTLText as="h3" className="text-lg font-semibold mb-2 text-glass-primary text-center">
              {t('home.about.mentorship')}
            </RTLText>
            <RTLText as="p" className="text-sm text-glass-secondary text-center">
              {t('home.about.mentorshipDescription')}
            </RTLText>
          </div>

          {/* Card 3 - Resources */}
          <div className="p-6 rounded-2xl backdrop-blur-sm glass-light border border-glass-border hover:scale-105 transition-all duration-300">
            <div className="flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-r from-green-500 to-emerald-500 mb-4 mx-auto">
              <RTLIcon icon={BookOpen} size={24} className="text-white" />
            </div>
            <RTLText as="h3" className="text-lg font-semibold mb-2 text-glass-primary text-center">
              {t('home.about.resources')}
            </RTLText>
            <RTLText as="p" className="text-sm text-glass-secondary text-center">
              {t('home.about.resourcesDescription')}
            </RTLText>
          </div>

          {/* Card 4 - Funding */}
          <div className="p-6 rounded-2xl backdrop-blur-sm glass-light border border-glass-border hover:scale-105 transition-all duration-300">
            <div className="flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-r from-yellow-500 to-orange-500 mb-4 mx-auto">
              <RTLIcon icon={DollarSign} size={24} className="text-white" />
            </div>
            <RTLText as="h3" className="text-lg font-semibold mb-2 text-glass-primary text-center">
              {t('home.about.funding')}
            </RTLText>
            <RTLText as="p" className="text-sm text-glass-secondary text-center">
              {t('home.about.fundingDescription')}
            </RTLText>
          </div>
        </div>

        {/* Stats Section - Hero Style */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
          <div className="text-center p-6 rounded-2xl backdrop-blur-sm transition-all duration-300 hover:scale-105 glass-light border">
            <div className="text-3xl md:text-4xl font-bold mb-2 text-glass-primary">500+</div>
            <div className="text-sm text-glass-secondary">{t('hero.stats.entrepreneurs')}</div>
          </div>
          <div className="text-center p-6 rounded-2xl backdrop-blur-sm transition-all duration-300 hover:scale-105 glass-light border">
            <div className="text-3xl md:text-4xl font-bold mb-2 text-glass-primary">150+</div>
            <div className="text-sm text-glass-secondary">{t('hero.stats.ideasLaunched')}</div>
          </div>
          <div className="text-center p-6 rounded-2xl backdrop-blur-sm transition-all duration-300 hover:scale-105 glass-light border">
            <div className="text-3xl md:text-4xl font-bold mb-2 text-glass-primary">85%</div>
            <div className="text-sm text-glass-secondary">{t('hero.stats.successRate')}</div>
          </div>
          <div className="text-center p-6 rounded-2xl backdrop-blur-sm transition-all duration-300 hover:scale-105 glass-light border">
            <div className="text-3xl md:text-4xl font-bold mb-2 text-glass-primary">50+</div>
            <div className="text-sm text-glass-secondary">{t('hero.stats.mentors')}</div>
          </div>
        </div>

        {/* Call to Action Section */}
        <div className="mt-16 text-center">
          <div className="glass-light rounded-2xl p-8 border shadow-lg">
            <div className="max-w-3xl mx-auto">
              <div className="flex items-center justify-center mb-6">
                <div className="w-16 h-16 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center">
                  <Sparkles className="w-8 h-8 text-white" />
                </div>
              </div>

              <RTLText as="h3" className="text-3xl font-bold mb-4 text-glass-primary">
                {t('home.about.readyToTransform')}
              </RTLText>

              <RTLText as="p" className="text-lg text-glass-secondary mb-8">
                {t('home.about.joinEntrepreneurs')}
              </RTLText>

              <RTLFlex className={`flex-col sm:flex-row items-center justify-center gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                <Link
                  to="/register"
                  className={`px-8 py-4 rounded-full flex items-center gap-2 transform hover:scale-105 transition-all duration-300 cursor-pointer bg-gradient-to-r from-purple-500 to-blue-500 text-white hover:shadow-lg font-semibold text-lg ${isRTL ? "flex-row-reverse" : ""}`}
                >
                  {t('hero.startJourney')} <RTLIcon icon={ArrowRight} size={20} flipInRTL={true} />
                </Link>
                <Link
                  to="/incubator"
                  className="px-8 py-4 bg-transparent rounded-full font-semibold text-lg transition-all duration-300 cursor-pointer border-2 border-purple-500 text-glass-primary hover:bg-purple-500/10 hover:shadow-lg"
                >
                  {t('home.about.explorePrograms')}
                </Link>
              </RTLFlex>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;