/**
 * Consolidated RTL Support Styles
 * 
 * This file consolidates all RTL styles from rtl.css, rtl-logical.css, and admin-rtl.css
 * into a single comprehensive RTL stylesheet for better maintainability.
 */

/* ===== BASE RTL STYLES ===== */
[dir="rtl"] {
  direction: rtl;
  text-align: right;
}

[dir="ltr"] {
  direction: ltr;
  text-align: left;
}

/* ===== FONT SETTINGS FOR ARABIC ===== */
[dir="rtl"] body,
[dir="rtl"] .universal-sidebar,
[dir="rtl"] .dashboard-content,
[dir="rtl"] button,
[dir="rtl"] input,
[dir="rtl"] select,
[dir="rtl"] textarea {
  font-family: 'Tajawal', 'Noto Sans Arabic', 'Cairo', sans-serif;
  font-weight: 400;
  line-height: 1.6;
  letter-spacing: 0;
}

/* ===== ICON FLIPPING ===== */
[dir="rtl"] .flip-in-rtl {
  transform: scaleX(-1);
}

/* ===== FLEX DIRECTION ===== */
[dir="rtl"] .flex-row {
  flex-direction: row-reverse;
}

[dir="rtl"] .nav-item,
[dir="rtl"] .mobile-menu-item,
[dir="rtl"] .modal-header {
  flex-direction: row-reverse;
}

/* ===== MARGIN ADJUSTMENTS ===== */
[dir="rtl"] .ml-1 { margin-left: 0; margin-right: 0.25rem; }
[dir="rtl"] .ml-2 { margin-left: 0; margin-right: 0.5rem; }
[dir="rtl"] .ml-3 { margin-left: 0; margin-right: 0.75rem; }
[dir="rtl"] .ml-4 { margin-left: 0; margin-right: 1rem; }
[dir="rtl"] .ml-5 { margin-left: 0; margin-right: 1.25rem; }
[dir="rtl"] .ml-6 { margin-left: 0; margin-right: 1.5rem; }

[dir="rtl"] .mr-1 { margin-right: 0; margin-left: 0.25rem; }
[dir="rtl"] .mr-2 { margin-right: 0; margin-left: 0.5rem; }
[dir="rtl"] .mr-3 { margin-right: 0; margin-left: 0.75rem; }
[dir="rtl"] .mr-4 { margin-right: 0; margin-left: 1rem; }
[dir="rtl"] .mr-5 { margin-right: 0; margin-left: 1.25rem; }
[dir="rtl"] .mr-6 { margin-right: 0; margin-left: 1.5rem; }

/* ===== PADDING ADJUSTMENTS ===== */
[dir="rtl"] .pl-1 { padding-left: 0; padding-right: 0.25rem; }
[dir="rtl"] .pl-2 { padding-left: 0; padding-right: 0.5rem; }
[dir="rtl"] .pl-3 { padding-left: 0; padding-right: 0.75rem; }
[dir="rtl"] .pl-4 { padding-left: 0; padding-right: 1rem; }
[dir="rtl"] .pl-5 { padding-left: 0; padding-right: 1.25rem; }
[dir="rtl"] .pl-6 { padding-left: 0; padding-right: 1.5rem; }

[dir="rtl"] .pr-1 { padding-right: 0; padding-left: 0.25rem; }
[dir="rtl"] .pr-2 { padding-right: 0; padding-left: 0.5rem; }
[dir="rtl"] .pr-3 { padding-right: 0; padding-left: 0.75rem; }
[dir="rtl"] .pr-4 { padding-right: 0; padding-left: 1rem; }
[dir="rtl"] .pr-5 { padding-right: 0; padding-left: 1.25rem; }
[dir="rtl"] .pr-6 { padding-right: 0; padding-left: 1.5rem; }

/* ===== UNIVERSAL SIDEBAR LAYOUT ADJUSTMENTS ===== */
[dir="rtl"] .universal-sidebar {
  right: 0;
  left: auto;
}

[dir="ltr"] .universal-sidebar {
  left: 0;
  right: auto;
}

/* Main content spacing for RTL with UniversalSidebar */
[dir="rtl"] .sidebar-layout-main.with-sidebar {
  margin-right: 320px;
  margin-left: 0;
}

[dir="ltr"] .sidebar-layout-main.with-sidebar {
  margin-left: 320px;
  margin-right: 0;
}

/* ===== SIDEBAR POSITIONING FIXES ===== */
[dir="rtl"] .fixed.inset-y-0.left-0 {
  left: auto !important;
  right: 0 !important;
}

[dir="rtl"] .fixed.inset-y-0.right-0 {
  right: auto !important;
  left: 0 !important;
}

/* ===== NAVIGATION ITEMS ===== */
[dir="rtl"] .nav-item {
  text-align: right;
}

[dir="rtl"] .nav-item .nav-icon {
  margin-left: 0.75rem;
  margin-right: 0;
}

/* ===== FORM CONTROLS ===== */
[dir="rtl"] .form-input,
[dir="rtl"] .form-select,
[dir="rtl"] .form-textarea {
  text-align: right;
}

[dir="rtl"] .form-checkbox,
[dir="rtl"] .form-radio {
  margin-left: 0.5rem;
  margin-right: 0;
}

/* ===== BUTTONS ===== */
[dir="rtl"] .btn-with-icon {
  flex-direction: row-reverse;
}

[dir="rtl"] .btn-icon-left {
  margin-left: 0.5rem;
  margin-right: 0;
}

[dir="rtl"] .btn-icon-right {
  margin-right: 0.5rem;
  margin-left: 0;
}

/* ===== TABLES ===== */
[dir="rtl"] .admin-table {
  direction: rtl;
}

[dir="rtl"] .admin-table th,
[dir="rtl"] .admin-table td {
  text-align: right;
}

[dir="rtl"] .admin-table th:first-child,
[dir="rtl"] .admin-table td:first-child {
  border-radius: 0 0.375rem 0.375rem 0;
}

[dir="rtl"] .admin-table th:last-child,
[dir="rtl"] .admin-table td:last-child {
  border-radius: 0.375rem 0 0 0.375rem;
}

/* ===== SEARCH COMPONENTS ===== */
[dir="rtl"] .search-input {
  padding-right: 2.5rem;
  padding-left: 1rem;
  text-align: right;
}

[dir="ltr"] .search-input {
  padding-left: 2.5rem;
  padding-right: 1rem;
  text-align: left;
}

[dir="rtl"] .search-icon {
  right: 0.75rem;
  left: auto;
}

[dir="ltr"] .search-icon {
  left: 0.75rem;
  right: auto;
}

/* ===== MODALS ===== */
[dir="rtl"] .modal-content {
  text-align: right;
}

[dir="rtl"] .modal-close {
  left: 1rem;
  right: auto;
}

[dir="ltr"] .modal-close {
  right: 1rem;
  left: auto;
}

/* ===== PAGINATION ===== */
[dir="rtl"] .pagination {
  flex-direction: row-reverse;
}

/* ===== TOOLTIPS ===== */
[dir="rtl"] .tooltip {
  text-align: right;
}

/* ===== MOBILE MENU ===== */
[dir="rtl"] .mobile-menu {
  text-align: right;
}

/* ===== CHARTS (Keep LTR for data visualization) ===== */
[dir="rtl"] .chart-container {
  direction: ltr;
}

[dir="rtl"] .chart-title {
  text-align: right;
  direction: rtl;
}

/* ===== UTILITY CLASSES ===== */
.text-direction-rtl {
  direction: rtl;
  text-align: right;
}

.text-direction-ltr {
  direction: ltr;
  text-align: left;
}

.flex-rtl {
  flex-direction: row-reverse;
}

.flex-ltr {
  flex-direction: row;
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */
@media (max-width: 1024px) {
  [dir="rtl"] .admin-main-content,
  [dir="ltr"] .admin-main-content {
    padding-left: 0;
    padding-right: 0;
  }
}

/* ===== LOADING SPINNER (Keep centered) ===== */
.loading-spinner {
  margin: 0 auto;
}

/* ===== TAILWIND RTL FIXES ===== */
[dir="rtl"] .lg\:pr-64 {
  padding-right: 16rem !important;
  padding-left: 0 !important;
}

[dir="rtl"] .lg\:pl-64 {
  padding-left: 0 !important;
  padding-right: 16rem !important;
}

[dir="rtl"] .rtl-fix-mr-3 {
  margin-right: 0 !important;
  margin-left: 0.75rem !important;
}

[dir="rtl"] .rtl-fix-ml-3 {
  margin-left: 0 !important;
  margin-right: 0.75rem !important;
}

[dir="rtl"] .rtl-fix-pr-64 {
  padding-right: 0 !important;
  padding-left: 16rem !important;
}

[dir="rtl"] .rtl-fix-pl-64 {
  padding-left: 0 !important;
  padding-right: 16rem !important;
}

/* ===== TEXT ALIGNMENT ===== */
[dir="rtl"] .text-left {
  text-align: right;
}

[dir="rtl"] .text-right {
  text-align: left;
}

/* ===== FLOAT ADJUSTMENTS ===== */
[dir="rtl"] .float-left {
  float: right;
}

[dir="rtl"] .float-right {
  float: left;
}

/* ===== BORDER RADIUS ADJUSTMENTS ===== */
[dir="rtl"] .rounded-l {
  border-radius: 0 0.25rem 0.25rem 0;
}

[dir="rtl"] .rounded-r {
  border-radius: 0.25rem 0 0 0.25rem;
}

[dir="rtl"] .rounded-tl {
  border-top-left-radius: 0;
  border-top-right-radius: 0.25rem;
}

[dir="rtl"] .rounded-tr {
  border-top-right-radius: 0;
  border-top-left-radius: 0.25rem;
}

[dir="rtl"] .rounded-bl {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0.25rem;
}

[dir="rtl"] .rounded-br {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0.25rem;
}

/* ===== LOGICAL PROPERTIES (Modern CSS) ===== */
.margin-inline-start-1 { margin-inline-start: 0.25rem; }
.margin-inline-start-2 { margin-inline-start: 0.5rem; }
.margin-inline-start-3 { margin-inline-start: 0.75rem; }
.margin-inline-start-4 { margin-inline-start: 1rem; }

.margin-inline-end-1 { margin-inline-end: 0.25rem; }
.margin-inline-end-2 { margin-inline-end: 0.5rem; }
.margin-inline-end-3 { margin-inline-end: 0.75rem; }
.margin-inline-end-4 { margin-inline-end: 1rem; }

.padding-inline-start-1 { padding-inline-start: 0.25rem; }
.padding-inline-start-2 { padding-inline-start: 0.5rem; }
.padding-inline-start-3 { padding-inline-start: 0.75rem; }
.padding-inline-start-4 { padding-inline-start: 1rem; }

.padding-inline-end-1 { padding-inline-end: 0.25rem; }
.padding-inline-end-2 { padding-inline-end: 0.5rem; }
.padding-inline-end-3 { padding-inline-end: 0.75rem; }
.padding-inline-end-4 { padding-inline-end: 1rem; }

.text-align-start { text-align: start; }
.text-align-end { text-align: end; }
