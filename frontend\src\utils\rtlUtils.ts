import { useLanguage } from '../hooks/useLanguage';

/**
 * Hook to get current language direction
 * @returns Object with isRTL and other language utilities
 * @deprecated Use useLanguage hook instead
 */
export const useLanguageDirection = () => {
  const { isRTL, language, direction } = useLanguage();
  return { isRTL, language, direction };
};

/**
 * Get margin class with logical properties
 * @param direction 'start' | 'end' | 'top' | 'bottom'
 * @param size spacing size (1-12)
 * @returns Tailwind class for margin
 */
export const getMarginClass = (direction: 'start' | 'end' | 'top' | 'bottom', size: number) => {
  switch (direction) {
    case 'start':
      return `ms-${size}`;
    case 'end':
      return `me-${size}`;
    case 'top':
      return `mt-${size}`;
    case 'bottom':
      return `mb-${size}`;
  }
};

/**
 * Get padding class with logical properties
 * @param direction 'start' | 'end' | 'top' | 'bottom'
 * @param size spacing size (1-12)
 * @returns Tailwind class for padding
 */
export const getPaddingClass = (direction: 'start' | 'end' | 'top' | 'bottom', size: number) => {
  switch (direction) {
    case 'start':
      return `ps-${size}`;
    case 'end':
      return `pe-${size}`;
    case 'top':
      return `pt-${size}`;
    case 'bottom':
      return `pb-${size}`;
  }
};

/**
 * Get border class with logical properties
 * @param direction 'start' | 'end' | 'top' | 'bottom'
 * @returns Tailwind class for border
 */
export const getBorderClass = (direction: 'start' | 'end' | 'top' | 'bottom') => {
  switch (direction) {
    case 'start':
      return 'border-s';
    case 'end':
      return 'border-e';
    case 'top':
      return 'border-t';
    case 'bottom':
      return 'border-b';
  }
};

/**
 * Get rounded class with logical properties
 * @param direction 'start' | 'end' | 'start-top' | 'start-bottom' | 'end-top' | 'end-bottom'
 * @param size size (sm, md, lg, xl, 2xl, etc.)
 * @returns Tailwind class for rounded corners
 */
export const getRoundedClass = (
  direction: 'start' | 'end' | 'start-top' | 'start-bottom' | 'end-top' | 'end-bottom',
  size: string = ''
) => {
  const sizeStr = size ? `-${size}` : '';

  switch (direction) {
    case 'start':
      return `rounded-s${sizeStr}`;
    case 'end':
      return `rounded-e${sizeStr}`;
    case 'start-top':
      return `rounded-ts${sizeStr}`;
    case 'start-bottom':
      return `rounded-bs${sizeStr}`;
    case 'end-top':
      return `rounded-te${sizeStr}`;
    case 'end-bottom':
      return `rounded-be${sizeStr}`;
  }
};

/**
 * Get text alignment class with logical properties
 * @param alignment 'start' | 'end' | 'center'
 * @returns Tailwind class for text alignment
 */
export const getTextAlignClass = (alignment: 'start' | 'end' | 'center') => {
  switch (alignment) {
    case 'start':
      return 'text-start';
    case 'end':
      return 'text-end';
    case 'center':
      return 'text-center';
  }
};

/**
 * Get float class with logical properties
 * @param direction 'start' | 'end'
 * @returns Tailwind class for float
 */
export const getFloatClass = (direction: 'start' | 'end') => {
  switch (direction) {
    case 'start':
      return 'float-start';
    case 'end':
      return 'float-end';
  }
};

/**
 * Get position class with logical properties
 * @param position 'start' | 'end'
 * @param size size (0, 1, 2, etc.)
 * @returns Tailwind class for position
 */
export const getPositionClass = (position: 'start' | 'end', size: number | string) => {
  return `${position}-${size}`;
};

/**
 * Get a CSS object with logical properties
 * @param properties Object with logical properties
 * @returns CSS object with physical properties based on current language direction
 */
export const getLogicalStyles = (properties: {
  marginStart?: string | number;
  marginEnd?: string | number;
  paddingStart?: string | number;
  paddingEnd?: string | number;
  borderStart?: string;
  borderEnd?: string;
  textAlign?: 'start' | 'end' | 'center';
  [key: string]: any;
}) => {
  const direction = document.documentElement.dir || 'ltr';
  const isRTL = direction === 'rtl';

  const result: Record<string, any> = {};

  // Map logical properties to physical properties based on direction
  Object.entries(properties).forEach(([key, value]) => {
    switch (key) {
      case 'marginStart':
        result[isRTL ? 'marginRight' : 'marginLeft'] = value;
        break;
      case 'marginEnd':
        result[isRTL ? 'marginLeft' : 'marginRight'] = value;
        break;
      case 'paddingStart':
        result[isRTL ? 'paddingRight' : 'paddingLeft'] = value;
        break;
      case 'paddingEnd':
        result[isRTL ? 'paddingLeft' : 'paddingRight'] = value;
        break;
      case 'borderStart':
        result[isRTL ? 'borderRight' : 'borderLeft'] = value;
        break;
      case 'borderEnd':
        result[isRTL ? 'borderLeft' : 'borderRight'] = value;
        break;
      case 'textAlign':
        if (value === 'start') {
          result.textAlign = isRTL ? 'right' : 'left';
        } else if (value === 'end') {
          result.textAlign = isRTL ? 'left' : 'right';
        } else {
          result.textAlign = value;
        }
        break;
      default:
        result[key] = value;
    }
  });

  return result;
};
