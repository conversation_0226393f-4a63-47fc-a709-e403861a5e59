import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Check, X, AlertTriangle, Search, Filter, Eye, RefreshCw, Calendar, MapPin, Briefcase, User, Mail, Phone, Link as LinkIcon, Loader2 } from 'lucide-react';
import { DashboardLayout } from '../dashboard';
import { membershipAPI, MembershipApplication } from '../../../services/api';
import { useAppSelector } from '../../../store/hooks';
import { formatDate as formatDateUtil, formatRelativeTime } from '../../../utils/dateTimeFormatter';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';
const MembershipApplicationsManagement: React.FC = () => {
  const { user } = useAppSelector(state => state.auth);
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [applications, setApplications] = useState<MembershipApplication[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState<'all' | 'pending' | 'approved' | 'rejected'>('pending');
  const [selectedApplication, setSelectedApplication] = useState<MembershipApplication | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isReviewModalOpen, setIsReviewModalOpen] = useState(false);
  const [reviewNotes, setReviewNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch applications - memoized to prevent unnecessary re-renders
  const fetchApplications = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await membershipAPI.getApplications();
      setApplications(data || []);
    } catch (err) {
      console.error('Error fetching applications:', err);
      setError(t("admin.failed.to.load", "Failed to load membership applications. Please try again."));
    } finally {
      setLoading(false);
    }
  }, []);

  // Load applications on component mount
  useEffect(() => {
    fetchApplications();
  }, [fetchApplications]);

  // Filter applications based on search term and status filter - memoized for performance
  const filteredApplications = useMemo(() => {
    return applications.filter(app => {
      const matchesSearch =
        app.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        app.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        app.expertise_areas.toLowerCase().includes(searchTerm.toLowerCase()) ||
        app.location.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesFilter = filter === 'all' || app.status === filter;

      return matchesSearch && matchesFilter;
    });
  }, [applications, searchTerm, filter]);

  // Format date for display using centralized utility
  const formatDateForDisplay = (dateString: string) => {
    if (!dateString) return t("admin.unknown.date", "Unknown date");

    try {
      const formattedDate = formatDateUtil(dateString);
      const relativeTime = formatRelativeTime(dateString);
      return `${formattedDate} (${relativeTime})`;
    } catch (e) {
      console.error('Error formatting date:', e);
      return dateString;
    }
  };

  // Handle application review - memoized to prevent unnecessary re-renders
  const handleReview = useCallback(async (status: 'approved' | 'rejected') => {
    if (!selectedApplication) return;

    setIsSubmitting(true);
    try {
      await membershipAPI.reviewApplication(selectedApplication.id, status, reviewNotes);

      // Update local state
      setApplications(prev => prev.map(app =>
        app.id === selectedApplication.id
          ? { ...app, status, review_notes: reviewNotes, reviewed_by: user, reviewed_at: new Date().toISOString() }
          : app
      ));

      setIsReviewModalOpen(false);
      setSelectedApplication(null);
      setReviewNotes('');
    } catch (err) {
      console.error('Error reviewing application:', err);
      setError(t("admin.failed.to.review", "Failed to review application. Please try again."));
    } finally {
      setIsSubmitting(false);
    }
  }, [selectedApplication, reviewNotes, user]);

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-600/20 text-green-400';
      case 'rejected': return 'bg-red-600/20 text-red-400';
      default: return 'bg-yellow-600/20 text-yellow-400';
    }
  };

  // Get expertise level badge color
  const getExpertiseLevelColor = (level: string) => {
    switch (level) {
      case 'beginner': return 'bg-blue-600/20 text-blue-400';
      case 'intermediate': return 'bg-green-600/20 text-green-400';
      case 'advanced': return 'bg-purple-600/20 text-purple-400';
      case 'expert': return 'bg-yellow-600/20 text-yellow-400';
      default: return 'bg-gray-600/20 text-gray-400';
    }
  };

  return (
    <DashboardLayout currentPage="membership">
      <div className="mb-8">
        <h1 className="text-2xl font-bold">{t('admin.membershipApplications')}</h1>
        <div className="text-gray-400 mt-1">{t('admin.membershipApplicationsDescription')}</div>
      </div>

      <div className="bg-indigo-900/20 rounded-xl overflow-hidden backdrop-blur-sm shadow-lg">
        <div className="p-4 border-b border-indigo-800/50">
          <div className={`flex flex-col md:flex-row md:items-center md:space-x-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className={`relative flex-1 mb-4 md:mb-0 ${isRTL ? "flex-row-reverse" : ""}`}>
              <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder={t('admin.searchApplications')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
              />
            </div>
            <div className={`flex space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                onClick={() => setFilter('all')}
                className={`px-3 py-1.5 rounded-lg text-sm ${
                  filter === 'all' ? 'bg-gray-600/30 text-white' : 'bg-indigo-900/30 text-gray-300 hover:bg-indigo-800/30'}
                }`}
              >
                {t('common.all')}
              </button>
              <button
                onClick={() => setFilter('pending')}
                className={`px-3 py-1.5 rounded-lg text-sm ${
                  filter === 'pending' ? 'bg-yellow-600/30 text-white' : 'bg-indigo-900/30 text-gray-300 hover:bg-indigo-800/30'}
                }`}
              >
                {t('admin.pending')}
              </button>
              <button
                onClick={() => setFilter('approved')}
                className={`px-3 py-1.5 rounded-lg text-sm ${
                  filter === 'approved' ? 'bg-green-600/30 text-white' : 'bg-indigo-900/30 text-gray-300 hover:bg-indigo-800/30'}
                }`}
              >
                {t('admin.approved')}
              </button>
              <button
                onClick={() => setFilter('rejected')}
                className={`px-3 py-1.5 rounded-lg text-sm ${
                  filter === 'rejected' ? 'bg-red-600/30 text-white' : 'bg-indigo-900/30 text-gray-300 hover:bg-indigo-800/30'}
                }`}
              >
                {t('admin.rejected')}
              </button>
              <button
                onClick={fetchApplications}
                className={`px-3 py-1.5 rounded-lg text-sm bg-indigo-900/30 text-gray-300 hover:bg-indigo-800/30 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <RefreshCw size={14} className={`mr-1 ${loading ? 'animate-spin' : ''}`} />
                {t('common.refresh')}
              </button>
            </div>
          </div>
        </div>

        {error && (
          <div className={`p-4 m-4 bg-red-900/30 border border-red-500/50 rounded-lg flex items-start ${isRTL ? "flex-row-reverse" : ""}`}>
            <AlertTriangle size={20} className={`text-red-400 mr-3 mt-0.5 flex-shrink-0 ${isRTL ? "flex-row-reverse" : ""}`} />
            <div className="text-red-200">{error}</div>
          </div>
        )}

        {loading ? (
          <div className={`flex justify-center items-center py-20 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className="w-12 h-12 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
          </div>
        ) : (
          <div className="divide-y divide-indigo-800/30">
            {filteredApplications.length === 0 ? (
              <div className="text-center py-10">
                <div className="text-gray-400 text-lg">{t('admin.noApplicationsFound')}</div>
              </div>
            ) : (
              filteredApplications.map((application) => (
                <div key={application.id} className="p-6 hover:bg-indigo-900/10">
                  <div className={`flex justify-between items-start ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div>
                      <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                        <h3 className="text-lg font-semibold">{application.full_name}</h3>
                        <span className={`ml-3 px-2 py-1 text-xs rounded-full ${getStatusColor(application.status)}`}>
                          {application.status.charAt(0).toUpperCase() + application.status.slice(1)}
                        </span>
                        <span className={`ml-2 px-2 py-1 text-xs rounded-full ${getExpertiseLevelColor(application.expertise_level)}`}>
                          {application.expertise_level.charAt(0).toUpperCase() + application.expertise_level.slice(1)}
                        </span>
                      </div>
                      <div className={`flex items-center text-sm text-gray-400 mt-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <Mail size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                        <span>{application.email}</span>
                        <span className="mx-2">•</span>
                        <MapPin size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                        <span>{application.location}</span>
                        <span className="mx-2">•</span>
                        <Calendar size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                        <span>{formatDateForDisplay(application.created_at)}</span>
                      </div>
                    </div>
                    <div className={`flex space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <button
                        onClick={() => {
                          setSelectedApplication(application);
                          setIsViewModalOpen(true);
                        }}
                        className="px-3 py-1.5 bg-indigo-800/80 rounded-lg text-white hover:bg-indigo-700/80 text-sm"
                      >
                        <Eye size={16} className={`mr-1 inline ${isRTL ? "space-x-reverse" : ""}`} />
                        {t('admin.viewDetails')}
                      </button>
                      {application.status === 'pending' && (
                        <button
                          onClick={() => {
                            setSelectedApplication(application);
                            setReviewNotes('');
                            setIsReviewModalOpen(true);
                          }}
                          className="px-3 py-1.5 bg-purple-800/80 rounded-lg text-white hover:bg-purple-700/80 text-sm"
                        >
                          {t('admin.review')}
                        </button>
                      )}
                    </div>
                  </div>

                  <div className="mt-4">
                    <div className="text-gray-300 line-clamp-2">
                      {application.expertise_areas}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        )}
      </div>

      {/* View Application Modal */}
      {isViewModalOpen && selectedApplication && (
        <div className={`fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70 backdrop-blur-sm ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="bg-gradient-to-b from-indigo-900/90 to-purple-900/90 rounded-xl p-6 max-w-3xl w-full max-h-[90vh] overflow-y-auto">
            <div className={`flex justify-between items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
              <h3 className="text-2xl font-semibold">{t('admin.applicationDetails')}</h3>
              <button
                onClick={() => setIsViewModalOpen(false)}
                className="p-2 rounded-full bg-indigo-800/50 hover:bg-indigo-700/50"
              >
                <X size={20} />
              </button>
            </div>

            <div className="space-y-6">
              {/* Personal Information */}
              <div className="bg-indigo-950/50 rounded-lg p-4">
                <h4 className={`text-lg font-semibold mb-4 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                  <User size={18} className={`mr-2 text-purple-400 ${isRTL ? "space-x-reverse" : ""}`} />
                  {t('admin.personalInformation')}
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm text-gray-400">{t('membership.fullName')}</div>
                    <p className="text-white">{selectedApplication.full_name}</p>
                  </div>
                  <div>
                    <div className="text-sm text-gray-400">{t('auth.email')}</div>
                    <p className="text-white">{selectedApplication.email}</p>
                  </div>
                  <div>
                    <div className="text-sm text-gray-400">{t('membership.phoneNumber')}</div>
                    <p className="text-white">{selectedApplication.phone || t('admin.notProvided')}</p>
                  </div>
                  <div>
                    <div className="text-sm text-gray-400">{t('membership.location')}</div>
                    <p className="text-white">{selectedApplication.location}</p>
                  </div>
                </div>
              </div>

              {/* Expertise */}
              <div className="bg-indigo-950/50 rounded-lg p-4">
                <h4 className={`text-lg font-semibold mb-4 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                  <Briefcase size={18} className={`mr-2 text-purple-400 ${isRTL ? "space-x-reverse" : ""}`} />
                  {t('admin.expertiseBackground')}
                </h4>
                <div className="mb-4">
                  <div className="text-sm text-gray-400">{t('membership.expertiseLevel')}</div>
                  <p className={`inline-block px-2 py-1 rounded-full text-sm ${getExpertiseLevelColor(selectedApplication.expertise_level)}`}>
                    {selectedApplication.expertise_level.charAt(0).toUpperCase() + selectedApplication.expertise_level.slice(1)}
                  </p>
                </div>
                <div className="mb-4">
                  <div className="text-sm text-gray-400">{t('membership.expertiseAreas')}</div>
                  <p className="text-white">{selectedApplication.expertise_areas}</p>
                </div>
                <div className="mb-4">
                  <div className="text-sm text-gray-400">{t('membership.background')}</div>
                  <p className="text-white whitespace-pre-wrap">{selectedApplication.background}</p>
                </div>
                <div>
                  <div className="text-sm text-gray-400">{t('membership.motivation')}</div>
                  <p className="text-white whitespace-pre-wrap">{selectedApplication.motivation}</p>
                </div>
              </div>

              {/* Profiles */}
              <div className="bg-indigo-950/50 rounded-lg p-4">
                <h4 className={`text-lg font-semibold mb-4 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                  <LinkIcon size={18} className={`mr-2 text-purple-400 ${isRTL ? "space-x-reverse" : ""}`} />
                  {t('admin.onlineProfiles')}
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <div className="text-sm text-gray-400">{t('membership.linkedinProfile')}</div>
                    {selectedApplication.linkedin_profile ? (
                      <a
                        href={selectedApplication.linkedin_profile}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-purple-400 hover:text-purple-300"
                      >
                        {t('admin.viewProfile')}
                      </a>
                    ) : (
                      <p className="text-white">{t('admin.notProvided')}</p>
                    )}
                  </div>
                  <div>
                    <div className="text-sm text-gray-400">{t('membership.githubProfile')}</div>
                    {selectedApplication.github_profile ? (
                      <a
                        href={selectedApplication.github_profile}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-purple-400 hover:text-purple-300"
                      >
                        {t('admin.viewProfile')}
                      </a>
                    ) : (
                      <p className="text-white">{t('admin.notProvided')}</p>
                    )}
                  </div>
                  <div>
                    <div className="text-sm text-gray-400">{t('membership.portfolioWebsite')}</div>
                    {selectedApplication.portfolio_url ? (
                      <a
                        href={selectedApplication.portfolio_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-purple-400 hover:text-purple-300"
                      >
                        {t('admin.viewWebsite')}
                      </a>
                    ) : (
                      <p className="text-white">{t('admin.notProvided')}</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Application Status */}
              <div className="bg-indigo-950/50 rounded-lg p-4">
                <h4 className={`text-lg font-semibold mb-4 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                  <AlertTriangle size={18} className={`mr-2 text-purple-400 ${isRTL ? "space-x-reverse" : ""}`} />
                  {t('admin.applicationStatus')}
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm text-gray-400">{t('admin.status')}</div>
                    <p className={`inline-block px-2 py-1 rounded-full text-sm ${getStatusColor(selectedApplication.status)}`}>
                      {selectedApplication.status.charAt(0).toUpperCase() + selectedApplication.status.slice(1)}
                    </p>
                  </div>
                  <div>
                    <div className="text-sm text-gray-400">{t('admin.submittedOn')}</div>
                    <p className="text-white">{formatDateForDisplay(selectedApplication.created_at)}</p>
                  </div>
                  {selectedApplication.reviewed_at && (
                    <>
                      <div>
                        <div className="text-sm text-gray-400">{t('admin.reviewedBy')}</div>
                        <p className="text-white">{selectedApplication.reviewed_by?.username || t('common.unknown')}</p>
                      </div>
                      <div>
                        <div className="text-sm text-gray-400">{t('admin.reviewedOn')}</div>
                        <p className="text-white">{formatDateForDisplay(selectedApplication.reviewed_at)}</p>
                      </div>
                    </>
                  )}
                </div>
                {selectedApplication.review_notes && (
                  <div className="mt-4">
                    <div className="text-sm text-gray-400">{t('admin.reviewNotes')}</div>
                    <p className="text-white whitespace-pre-wrap">{selectedApplication.review_notes}</p>
                  </div>
                )}
              </div>

              <div className={`flex justify-end space-x-3 pt-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                <button
                  onClick={() => setIsViewModalOpen(false)}
                  className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg"
                >
                  {t('common.close')}
                </button>
                {selectedApplication.status === 'pending' && (
                  <button
                    onClick={() => {
                      setIsViewModalOpen(false);
                      setReviewNotes('');
                      setIsReviewModalOpen(true);
                    }}
                    className="px-4 py-2 bg-purple-600 hover:bg-purple-500 rounded-lg"
                  >
                    {t('admin.reviewApplication')}
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Review Application Modal */}
      {isReviewModalOpen && selectedApplication && (
        <div className={`fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70 backdrop-blur-sm ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="bg-gradient-to-b from-indigo-900/90 to-purple-900/90 rounded-xl p-6 max-w-xl w-full">
            <div className={`flex justify-between items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
              <h3 className="text-2xl font-semibold">{t('admin.reviewApplication')}</h3>
              <button
                onClick={() => setIsReviewModalOpen(false)}
                className="p-2 rounded-full bg-indigo-800/50 hover:bg-indigo-700/50"
              >
                <X size={20} />
              </button>
            </div>

            <div className="mb-6">
              <h4 className="font-medium mb-2">{selectedApplication.full_name}</h4>
              <div className="text-gray-300 mb-4">{selectedApplication.email}</div>

              <div className="bg-indigo-950/50 p-4 rounded-lg mb-4">
                <label className="block text-sm font-medium mb-2">{t('admin.reviewNotes')}</label>
                <textarea
                  value={reviewNotes}
                  onChange={(e) => setReviewNotes(e.target.value)}
                  className="w-full px-4 py-2 bg-indigo-900/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  rows={3}
                  placeholder={t('admin.addNotes')}
                ></textarea>
              </div>
            </div>

            <div className={`flex justify-end space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                onClick={() => setIsReviewModalOpen(false)}
                className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg"
                disabled={isSubmitting}
              >
                {t('common.cancel')}
              </button>
              <button
                onClick={() => handleReview('rejected')}
                className={`px-4 py-2 bg-red-600 hover:bg-red-500 rounded-lg flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <Loader2 size={18} className={`mr-2 animate-spin ${isRTL ? "space-x-reverse" : ""}`} />
                ) : (
                  <X size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                )}
                {t('admin.reject')}
              </button>
              <button
                onClick={() => handleReview('approved')}
                className={`px-4 py-2 bg-green-600 hover:bg-green-500 rounded-lg flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <Loader2 size={18} className={`mr-2 animate-spin ${isRTL ? "space-x-reverse" : ""}`} />
                ) : (
                  <Check size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                )}
                {t('admin.approve')}
              </button>
            </div>
          </div>
        </div>
      )}
    </DashboardLayout>
  );
};

export default MembershipApplicationsManagement;
