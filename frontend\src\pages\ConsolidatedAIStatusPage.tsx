/**
 * Consolidated AI Status Page
 * Dedicated page for monitoring AI service health and performance
 */

import React from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { ArrowLeft, Activity, Sparkles } from 'lucide-react';
import ConsolidatedAIStatus from '../components/ai/ConsolidatedAIStatus';
import AuthenticatedLayout from '../components/layout/AuthenticatedLayout';
import { useLanguage } from '../hooks/useLanguage';

export const ConsolidatedAIStatusPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  return (
    <AuthenticatedLayout>
      <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="p-6">
              {/* Header */}
              <div className="mb-6">
                <div className={`flex items-center space-x-4 mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className={`flex items-center justify-center w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl ${isRTL ? "flex-row-reverse" : ""}`}>
                    <Activity className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-white">
                      {t('ai.status.title', 'AI Service Status')}
                    </h1>
                    <p className="text-gray-300">
                      {t('ai.status.subtitle', 'Monitor consolidated AI service health and performance')}
                    </p>
                  </div>
                </div>
              </div>

              {/* Main Content */}
              <ConsolidatedAIStatus
                autoRefresh={true}
                refreshInterval={30000}
              />
            </div>
          </div>
        </div>
      </div>
    </AuthenticatedLayout>
  );
};

export default ConsolidatedAIStatusPage;
