from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Q
from django.utils import timezone

from .models_business_plan import (
    BusinessPlanTemplate, CustomBusinessPlanTemplate, TemplateSectionDefinition
)
from .serializers_business_plan import (
    BusinessPlanTemplateSerializer, CustomBusinessPlanTemplateSerializer,
    TemplateSectionDefinitionSerializer
)
from api.permissions import IsAdminUser, IsOwnerOrAdmin

class TemplateSectionDefinitionViewSet(viewsets.ModelViewSet):
    """ViewSet for template section definitions"""
    queryset = TemplateSectionDefinition.objects.all()
    serializer_class = TemplateSectionDefinitionSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_permissions(self):
        """
        Allow public read access to section definitions, only admins can modify system definitions
        """
        if self.action in ['update', 'partial_update', 'destroy']:
            return [IsAdminUser()]
        elif self.action in ['list', 'retrieve']:
            # Allow public access to browse section definitions
            return [permissions.AllowAny()]
        return super().get_permissions()

    def get_queryset(self):
        """
        Filter section definitions based on query parameters
        """
        queryset = TemplateSectionDefinition.objects.all()

        # Filter by section type
        section_type = self.request.query_params.get('section_type')
        if section_type:
            queryset = queryset.filter(section_type=section_type)

        # Filter by system/custom
        is_system = self.request.query_params.get('is_system')
        if is_system is not None:
            is_system_bool = is_system.lower() == 'true'
            queryset = queryset.filter(is_system=is_system_bool)

        return queryset


class BusinessPlanTemplateViewSet(viewsets.ModelViewSet):
    """ViewSet for business plan templates"""
    queryset = BusinessPlanTemplate.objects.filter(is_active=True)
    serializer_class = BusinessPlanTemplateSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_permissions(self):
        """
        Allow public read access to templates, authenticated users can create/use them, only admins can modify system templates
        """
        if self.action in ['update', 'partial_update', 'destroy']:
            return [IsAdminUser()]
        elif self.action in ['list', 'retrieve', 'categories']:
            # Allow public access to browse templates
            return [permissions.AllowAny()]
        return super().get_permissions()

    def get_queryset(self):
        """
        Filter templates based on query parameters
        """
        queryset = BusinessPlanTemplate.objects.filter(is_active=True)

        # Filter by industry
        industry = self.request.query_params.get('industry')
        if industry:
            queryset = queryset.filter(industry=industry)

        # Filter by template type
        template_type = self.request.query_params.get('template_type')
        if template_type:
            queryset = queryset.filter(template_type=template_type)

        # Filter by customization
        allows_customization = self.request.query_params.get('allows_customization')
        if allows_customization is not None:
            allows_customization_bool = allows_customization.lower() == 'true'
            queryset = queryset.filter(allows_customization=allows_customization_bool)

        return queryset

    @action(detail=True, methods=['get'])
    def customization_options(self, request, pk=None):
        """
        Get customization options for a template
        """
        template = self.get_object()
        return Response(template.customization_options)

    @action(detail=False, methods=['get'])
    def categories(self, request):
        """
        Get template categories with counts
        """
        from django.db.models import Count

        # Get categories with template counts
        categories = BusinessPlanTemplate.objects.filter(is_active=True).values('industry').annotate(
            count=Count('id')
        ).order_by('industry')

        # Format the response
        category_data = []
        category_descriptions = {
            'Technology': 'Templates for tech companies and software businesses',
            'Restaurants & Hospitality': 'Templates for restaurants and hospitality businesses',
            'E-commerce': 'Templates for online stores and digital commerce',
            'Professional Services': 'Templates for consulting and professional services',
            'Manufacturing': 'Templates for manufacturing and industrial companies',
            'Healthcare': 'Templates for healthcare and medical services',
            'Education': 'Templates for educational institutions and services',
            'General': 'General templates for all business types',
            'Retail': 'Templates for retail and consumer goods businesses',
            'Finance': 'Templates for financial services and fintech companies',
            'Real Estate': 'Templates for real estate and property businesses',
            'Agriculture': 'Templates for agricultural and farming businesses'
        }

        for category in categories:
            if category['industry']:
                category_data.append({
                    'name': category['industry'],
                    'count': category['count'],
                    'description': category_descriptions.get(category['industry'], f'Templates for {category["industry"]} businesses')
                })

        # Add default categories if no templates exist
        if not category_data:
            default_categories = [
                {'name': 'Technology', 'count': 0, 'description': 'Templates for tech companies and software businesses'},
                {'name': 'Restaurants & Hospitality', 'count': 0, 'description': 'Templates for restaurants and hospitality businesses'},
                {'name': 'E-commerce', 'count': 0, 'description': 'Templates for online stores and digital commerce'},
                {'name': 'Professional Services', 'count': 0, 'description': 'Templates for consulting and professional services'},
                {'name': 'General', 'count': 0, 'description': 'General templates for all business types'}
            ]
            category_data = default_categories

        return Response(category_data)

    @action(detail=True, methods=['get'])
    def analytics(self, request, pk=None):
        """Get analytics for a specific template"""
        template = self.get_object()

        # Import here to avoid circular imports
        from .models_business_plan import BusinessPlan

        # Get business plans using this template
        business_plans = BusinessPlan.objects.filter(template=template)

        # Calculate basic analytics
        usage_count = business_plans.count()
        completed_plans = business_plans.filter(status='completed').count()
        success_rate = (completed_plans / usage_count * 100) if usage_count > 0 else 0

        # Calculate average completion time from real data
        # This would require tracking creation and completion timestamps
        average_completion_time = 0  # Set to 0 until we implement time tracking

        # Get user ratings from real data
        user_ratings = []  # Would come from a ratings model if implemented

        # Monthly usage from real data
        from django.utils import timezone
        from datetime import datetime, timedelta

        # Get monthly usage for the last 12 months
        monthly_usage = []
        current_date = timezone.now()

        for i in range(12):
            month_start = current_date.replace(day=1) - timedelta(days=30*i)
            month_end = month_start + timedelta(days=30)

            month_usage = business_plans.filter(
                created_at__gte=month_start,
                created_at__lt=month_end
            ).count()

            monthly_usage.append({
                'month': month_start.strftime('%Y-%m'),
                'usage_count': month_usage
            })

        analytics_data = {
            'usage_count': usage_count,
            'success_rate': round(success_rate, 1),
            'average_completion_time': average_completion_time,
            'user_ratings': user_ratings,
            'monthly_usage': monthly_usage,
        }

        return Response(analytics_data)

    @action(detail=True, methods=['post'])
    def rate(self, request, pk=None):
        """Rate a template"""
        template = self.get_object()
        rating = request.data.get('rating')
        review = request.data.get('review', '')

        if not rating or not (1 <= rating <= 5):
            return Response(
                {"error": "Rating must be between 1 and 5"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # For now, just return success
        # In a real implementation, you would save the rating to a TemplateRating model
        return Response({
            "message": "Rating submitted successfully",
            "rating": rating,
            "review": review
        })

    @action(detail=False, methods=['get', 'post'])
    def favorites(self, request):
        """Get or manage user's favorite templates"""
        if not request.user.is_authenticated:
            return Response(
                {"error": "Authentication required"},
                status=status.HTTP_401_UNAUTHORIZED
            )

        if request.method == 'GET':
            # Get user's favorite templates
            from django.contrib.auth.models import User
            user_profile = getattr(request.user, 'profile', None)
            if user_profile and hasattr(user_profile, 'favorite_templates'):
                favorite_ids = list(user_profile.favorite_templates.values_list('id', flat=True))
            else:
                # Fallback: use a simple approach with user preferences
                favorite_ids = []

            return Response({"favorites": favorite_ids})

        elif request.method == 'POST':
            # Add template to favorites
            template_id = request.data.get('template_id')
            if not template_id:
                return Response(
                    {"error": "template_id is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            try:
                template = self.get_queryset().get(id=template_id)
                # For now, just return success - would implement actual favorites model
                return Response({"message": "Template added to favorites"})
            except BusinessPlanTemplate.DoesNotExist:
                return Response(
                    {"error": "Template not found"},
                    status=status.HTTP_404_NOT_FOUND
                )

    @action(detail=True, methods=['delete'], url_path='favorites')
    def remove_favorite(self, request, pk=None):
        """Remove template from user's favorites"""
        if not request.user.is_authenticated:
            return Response(
                {"error": "Authentication required"},
                status=status.HTTP_401_UNAUTHORIZED
            )

        template = self.get_object()
        # For now, just return success - would implement actual favorites model
        return Response({"message": "Template removed from favorites"})


class CustomBusinessPlanTemplateViewSet(viewsets.ModelViewSet):
    """ViewSet for custom business plan templates"""
    serializer_class = CustomBusinessPlanTemplateSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """
        Return templates owned by the user or shared with them,
        plus public templates
        """
        user = self.request.user

        # If admin, show all templates
        if user.is_staff or user.is_superuser:
            return CustomBusinessPlanTemplate.objects.all()

        # For regular users, show their templates, shared templates, and public templates
        return CustomBusinessPlanTemplate.objects.filter(
            Q(owner=user) |
            Q(shared_with=user) |
            Q(is_public=True)
        ).distinct()

    def perform_create(self, serializer):
        """Set the owner to the current user"""
        serializer.save(owner=self.request.user)

    def get_permissions(self):
        """
        Only owners or admins can update or delete custom templates
        """
        if self.action in ['update', 'partial_update', 'destroy']:
            return [IsOwnerOrAdmin()]
        return super().get_permissions()

    @action(detail=True, methods=['post'])
    def share(self, request, pk=None):
        """
        Share a custom template with other users
        """
        custom_template = self.get_object()

        # Check if the user is the owner
        if custom_template.owner != request.user and not (request.user.is_staff or request.user.is_superuser):
            return Response(
                {"error": "You do not have permission to share this template"},
                status=status.HTTP_403_FORBIDDEN
            )

        # Get user IDs to share with
        user_ids = request.data.get('user_ids', [])
        if not user_ids:
            return Response(
                {"error": "No users specified"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Add users to shared_with
        from django.contrib.auth.models import User
        users = User.objects.filter(id__in=user_ids)
        custom_template.shared_with.add(*users)

        return Response({"message": f"Template shared with {users.count()} users"})

    @action(detail=True, methods=['post'])
    def unshare(self, request, pk=None):
        """
        Remove sharing for a custom template
        """
        custom_template = self.get_object()

        # Check if the user is the owner
        if custom_template.owner != request.user and not (request.user.is_staff or request.user.is_superuser):
            return Response(
                {"error": "You do not have permission to unshare this template"},
                status=status.HTTP_403_FORBIDDEN
            )

        # Get user IDs to unshare
        user_ids = request.data.get('user_ids', [])
        if not user_ids:
            return Response(
                {"error": "No users specified"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Remove users from shared_with
        from django.contrib.auth.models import User
        users = User.objects.filter(id__in=user_ids)
        custom_template.shared_with.remove(*users)

        return Response({"message": f"Template unshared with {users.count()} users"})

    @action(detail=True, methods=['post'])
    def make_public(self, request, pk=None):
        """
        Make a custom template public
        """
        custom_template = self.get_object()

        # Check if the user is the owner
        if custom_template.owner != request.user and not (request.user.is_staff or request.user.is_superuser):
            return Response(
                {"error": "You do not have permission to change this template's visibility"},
                status=status.HTTP_403_FORBIDDEN
            )

        custom_template.is_public = True
        custom_template.save()

        return Response({"message": "Template is now public"})

    @action(detail=True, methods=['post'])
    def make_private(self, request, pk=None):
        """
        Make a custom template private
        """
        custom_template = self.get_object()

        # Check if the user is the owner
        if custom_template.owner != request.user and not (request.user.is_staff or request.user.is_superuser):
            return Response(
                {"error": "You do not have permission to change this template's visibility"},
                status=status.HTTP_403_FORBIDDEN
            )

        custom_template.is_public = False
        custom_template.save()

        return Response({"message": "Template is now private"})
