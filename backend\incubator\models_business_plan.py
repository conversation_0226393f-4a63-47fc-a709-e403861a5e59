from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from datetime import timedelta
from .models_base import BusinessIdea

class BusinessPlanTemplate(models.Model):
    """Templates for business plans with industry-specific sections"""

    TEMPLATE_TYPE_CHOICES = (
        ('standard', 'Standard'),
        ('lean', 'Lean Canvas'),
        ('detailed', 'Detailed'),
        ('investor', 'Investor-Ready'),
        ('startup', 'Startup'),
        ('nonprofit', 'Non-Profit'),
        ('service', 'Service Business'),
        ('product', 'Product Business'),
        ('ecommerce', 'E-commerce'),
        ('saas', 'SaaS/Software'),
        ('restaurant', 'Restaurant/Food Service'),
        ('retail', 'Retail'),
        ('consulting', 'Consulting'),
        ('franchise', 'Franchise'),
        ('manufacturing', 'Manufacturing'),
        ('healthcare', 'Healthcare'),
        ('education', 'Education'),
        ('real_estate', 'Real Estate'),
        ('fintech', 'FinTech'),
        ('marketplace', 'Marketplace'),
        ('subscription', 'Subscription Business'),
        ('mobile_app', 'Mobile App'),
        ('social_impact', 'Social Impact'),
        ('green_business', 'Green/Sustainable Business'),
        ('ai_startup', 'AI/Tech Startup'),
        ('blockchain', 'Blockchain/Crypto'),
        ('gaming', 'Gaming/Entertainment'),
        # New template types
        ('fitness', 'Fitness & Wellness'),
        ('food_truck', 'Food Truck'),
        ('beauty_salon', 'Beauty Salon'),
        ('pet_services', 'Pet Services'),
        ('automotive', 'Automotive'),
        ('photography', 'Photography'),
        ('event_planning', 'Event Planning'),
        ('cleaning_services', 'Cleaning Services'),
        ('landscaping', 'Landscaping'),
        ('home_services', 'Home Services'),
        ('digital_marketing', 'Digital Marketing'),
        ('dropshipping', 'Dropshipping'),
        ('affiliate_marketing', 'Affiliate Marketing'),
        ('coaching', 'Coaching'),
        ('tutoring', 'Tutoring'),
        ('travel_tourism', 'Travel & Tourism'),
        ('import_export', 'Import/Export'),
        ('logistics', 'Logistics'),
        ('security_services', 'Security Services'),
        ('legal_services', 'Legal Services'),
        ('accounting_services', 'Accounting Services'),
        ('insurance_services', 'Insurance Services'),
        # Legacy choices
        ('international', 'International Expansion'),
        ('acquisition', 'Business Acquisition'),
        ('pivot', 'Business Pivot'),
        ('custom', 'Custom'),
    )

    name = models.CharField(max_length=100)
    description = models.TextField()
    industry = models.CharField(max_length=100)
    template_type = models.CharField(max_length=20, choices=TEMPLATE_TYPE_CHOICES, default='standard')

    # Template structure stored as JSON
    sections = models.JSONField(help_text="JSON structure defining the sections of this template")

    # Template customization options
    allows_customization = models.BooleanField(default=True)
    customization_options = models.JSONField(default=dict, help_text="JSON structure defining customization options")

    # Template metrics and properties (real data fields)
    difficulty_level = models.CharField(max_length=20, choices=[
        ('beginner', 'Beginner'),
        ('intermediate', 'Intermediate'),
        ('advanced', 'Advanced')
    ], default='intermediate')
    estimated_time = models.PositiveIntegerField(default=8, help_text="Estimated completion time in hours")

    # Analytics fields (calculated from TemplateUsageAnalytics)
    usage_count = models.PositiveIntegerField(default=0, help_text="Total number of times this template has been used")
    rating = models.FloatField(default=0.0, help_text="Average user rating (0-5)")
    rating_count = models.PositiveIntegerField(default=0, help_text="Total number of ratings")
    completion_rate = models.FloatField(default=0.0, help_text="Percentage of users who complete this template")

    # Template properties
    is_premium = models.BooleanField(default=False, help_text="Whether this is a premium template")
    is_featured = models.BooleanField(default=False, help_text="Whether this template is featured")
    is_bestseller = models.BooleanField(default=False, help_text="Whether this template is a bestseller")

    # Template metadata
    tags = models.JSONField(default=list, help_text="List of tags for this template")
    icon = models.CharField(max_length=50, default='FileText', help_text="Icon name for this template")
    author = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='authored_templates', help_text="Template author")

    # Metadata
    is_active = models.BooleanField(default=True)
    is_system = models.BooleanField(default=False, help_text="Whether this is a system-provided template")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} - {self.industry} ({self.template_type})"

    def update_analytics(self):
        """Update template analytics from usage data"""
        from .models_template_analytics import TemplateUsageAnalytics

        analytics = TemplateUsageAnalytics.objects.filter(template=self)

        # Update usage count
        self.usage_count = analytics.count()

        # Update rating
        rated_analytics = analytics.filter(rating__isnull=False)
        if rated_analytics.exists():
            from django.db.models import Avg
            avg_rating = rated_analytics.aggregate(avg_rating=Avg('rating'))['avg_rating']
            self.rating = round(avg_rating, 1) if avg_rating else 0.0
            self.rating_count = rated_analytics.count()

        # Update completion rate
        completed_analytics = analytics.filter(completed_at__isnull=False)
        started_analytics = analytics.filter(started_at__isnull=False)
        if started_analytics.exists():
            self.completion_rate = round((completed_analytics.count() / started_analytics.count()) * 100, 1)

        # Update bestseller status based on usage
        if self.usage_count > 1000 and self.rating >= 4.0:
            self.is_bestseller = True

        self.save(update_fields=['usage_count', 'rating', 'rating_count', 'completion_rate', 'is_bestseller'])

    @property
    def popularity_score(self):
        """Calculate popularity score (0-5) based on usage and rating"""
        usage_score = min(self.usage_count / 200, 3)  # Max 3 points for usage
        rating_score = (self.rating / 5) * 2  # Max 2 points for rating
        return min(round(usage_score + rating_score), 5)

    @property
    def is_new(self):
        """Check if template was created in the last 30 days"""
        from django.utils import timezone
        from datetime import timedelta
        return self.created_at > timezone.now() - timedelta(days=30)

    class Meta:
        ordering = ['industry', 'name']


class CustomBusinessPlanTemplate(models.Model):
    """User-customized business plan templates"""

    base_template = models.ForeignKey(BusinessPlanTemplate, on_delete=models.CASCADE, related_name='custom_templates')
    name = models.CharField(max_length=100)
    description = models.TextField()
    owner = models.ForeignKey(User, on_delete=models.CASCADE, related_name='custom_templates')

    # Customized template structure stored as JSON
    sections = models.JSONField(help_text="JSON structure defining the sections of this custom template")

    # Additional customization
    custom_prompts = models.JSONField(default=dict, help_text="Custom AI prompts for each section")
    custom_instructions = models.JSONField(default=dict, help_text="Custom instructions for each section")

    # Sharing settings
    is_public = models.BooleanField(default=False)
    shared_with = models.ManyToManyField(User, related_name='shared_templates', blank=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} (Custom) - {self.owner.username}"

    class Meta:
        ordering = ['-updated_at']
        unique_together = ['owner', 'name']


class BusinessPlan(models.Model):
    """Business plans created by users"""

    STATUS_CHOICES = (
        ('draft', 'Draft'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('archived', 'Archived'),
    )

    business_idea = models.ForeignKey(BusinessIdea, on_delete=models.CASCADE, related_name='business_plans', null=True, blank=True)
    template = models.ForeignKey(BusinessPlanTemplate, on_delete=models.SET_NULL, null=True, blank=True, related_name='business_plans')
    custom_template = models.ForeignKey(CustomBusinessPlanTemplate, on_delete=models.SET_NULL, null=True, blank=True, related_name='business_plans')
    owner = models.ForeignKey(User, on_delete=models.CASCADE, related_name='business_plans')

    title = models.CharField(max_length=200)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')

    # Content stored as JSON
    content = models.JSONField(default=dict, help_text="JSON structure containing the business plan content")

    # AI-generated feedback
    ai_feedback = models.JSONField(default=dict, help_text="AI-generated feedback on the business plan")

    # Completion metrics
    completion_percentage = models.PositiveSmallIntegerField(default=0, validators=[MinValueValidator(0), MaxValueValidator(100)])

    # Version tracking
    version = models.PositiveSmallIntegerField(default=1)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        if self.business_idea:
            return f"{self.title} - {self.business_idea.title}"
        return self.title

    def get_time_spent(self):
        """Calculate total time spent on this business plan"""
        from .models_business_plan_analytics import BusinessPlanSession
        sessions = BusinessPlanSession.objects.filter(business_plan=self)
        total_time = timedelta()
        for session in sessions:
            if session.end_time:
                total_time += session.end_time - session.start_time
        return total_time

    def get_collaboration_stats(self):
        """Get collaboration statistics for this business plan"""
        from .models_business_plan_analytics import BusinessPlanCollaboration
        collaborations = BusinessPlanCollaboration.objects.filter(business_plan=self)
        return {
            'total_collaborators': collaborations.values('user').distinct().count(),
            'total_comments': collaborations.filter(action_type='comment').count(),
            'total_edits': collaborations.filter(action_type='edit').count(),
            'total_reviews': collaborations.filter(action_type='review').count(),
        }

    def get_export_stats(self):
        """Get export/download statistics for this business plan"""
        from .models_business_plan_analytics import BusinessPlanExport
        exports = BusinessPlanExport.objects.filter(business_plan=self)
        return {
            'total_exports': exports.count(),
            'pdf_exports': exports.filter(export_format='pdf').count(),
            'word_exports': exports.filter(export_format='word').count(),
            'excel_exports': exports.filter(export_format='excel').count(),
            'last_export': exports.order_by('-created_at').first(),
        }

    class Meta:
        ordering = ['-updated_at']
        # Note: unique_together with nullable fields can cause issues,
        # so we'll handle uniqueness in the application logic if needed


class TemplateSectionDefinition(models.Model):
    """Definitions for sections that can be included in business plan templates"""

    SECTION_TYPE_CHOICES = (
        ('text', 'Text'),
        ('financial', 'Financial'),
        ('chart', 'Chart/Graph'),
        ('table', 'Table'),
        ('image', 'Image'),
        ('swot', 'SWOT Analysis'),
        ('timeline', 'Timeline'),
        ('checklist', 'Checklist'),
        ('canvas', 'Business Model Canvas'),
        ('persona', 'Customer Persona'),
        ('journey_map', 'Customer Journey Map'),
        ('competitive_matrix', 'Competitive Analysis Matrix'),
        ('risk_matrix', 'Risk Assessment Matrix'),
        ('financial_forecast', 'Financial Forecast'),
        ('revenue_model', 'Revenue Model'),
        ('pricing_strategy', 'Pricing Strategy'),
        ('marketing_mix', 'Marketing Mix (4Ps)'),
        ('value_proposition', 'Value Proposition Canvas'),
        ('lean_canvas', 'Lean Canvas'),
        ('pitch_deck', 'Pitch Deck Slide'),
        ('milestone_tracker', 'Milestone Tracker'),
        ('team_structure', 'Team Structure'),
        ('technology_stack', 'Technology Stack'),
        ('supply_chain', 'Supply Chain Diagram'),
        ('process_flow', 'Process Flow'),
        ('market_sizing', 'Market Sizing'),
        ('user_story', 'User Stories'),
        ('feature_roadmap', 'Feature Roadmap'),
        ('kpi_dashboard', 'KPI Dashboard'),
        ('custom', 'Custom'),
    )

    title = models.CharField(max_length=100)
    key = models.CharField(max_length=50, unique=True, help_text="Unique identifier for this section type")
    description = models.TextField()
    section_type = models.CharField(max_length=20, choices=SECTION_TYPE_CHOICES, default='text')

    # Default content and structure
    default_content = models.TextField(blank=True)
    structure_definition = models.JSONField(default=dict, help_text="JSON schema defining the structure of this section type")

    # AI generation settings
    ai_prompt_template = models.TextField(blank=True, help_text="Template for AI prompts to generate this section")

    # Metadata
    is_system = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.title} ({self.section_type})"

    class Meta:
        ordering = ['title']


class BusinessPlanSection(models.Model):
    """Individual sections of a business plan"""

    business_plan = models.ForeignKey(BusinessPlan, on_delete=models.CASCADE, related_name='sections')
    section_definition = models.ForeignKey(TemplateSectionDefinition, on_delete=models.SET_NULL, null=True, blank=True)

    title = models.CharField(max_length=100)
    key = models.CharField(max_length=50, help_text="Unique identifier for this section")
    content = models.TextField(blank=True)

    # Section metadata
    order = models.PositiveSmallIntegerField(default=0)
    is_required = models.BooleanField(default=True)
    is_completed = models.BooleanField(default=False)

    # Section customization
    custom_prompt = models.TextField(blank=True, help_text="Custom AI prompt for this specific section")
    custom_instructions = models.TextField(blank=True, help_text="Custom instructions for this section")

    # Additional data (for financial sections, charts, etc.)
    additional_data = models.JSONField(default=dict, blank=True, help_text="Additional data for specialized section types")

    # AI assistance
    ai_suggestions = models.TextField(blank=True, null=True)

    def __str__(self):
        return f"{self.title} - {self.business_plan.title}"

    class Meta:
        ordering = ['business_plan', 'order']
        unique_together = ['business_plan', 'key']
