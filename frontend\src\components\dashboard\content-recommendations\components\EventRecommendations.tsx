import React from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../../../../store/hooks';
import { Calendar, User } from 'lucide-react';
import RecommendationSection from './RecommendationSection';
import { useLanguage } from '../../../../hooks/useLanguage';
interface EventRecommendation {
  id: number;
  title: string;
  date: string;
  organizer: string;
}

interface EventRecommendationsProps {
  events: EventRecommendation[];
}

const EventRecommendations: React.FC<EventRecommendationsProps> = ({ events  }) => {
  const { t } = useTranslation();
  const { isRTL, language } = useLanguage();

  if (events.length === 0) return null;

  return (
    <RecommendationSection
      title={t('dashboard.contentRecommendations.eventsYouMightLike')}
      icon={<Calendar size={20} className={`${language === 'ar' ? 'ml-2' : 'mr-2'} text-blue-400`} />}
      viewAllLink="/events"
      viewAllText={t('dashboard.contentRecommendations.viewAllEvents')}
    >
      {events.map(event => (
        <div key={event.id} className="bg-indigo-800/30 p-3 rounded">
          <Link to={`/events/${event.id}`} className="text-purple-300 hover:text-purple-200 font-medium">
            {event.title}
          </Link>
          <div className={`flex items-center justify-between mt-2 text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
            <span className="text-gray-400">
              {new Date(event.date).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
            </span>
            <span className="text-gray-400">
              <User size={14} className={`inline ${language === 'ar' ? 'ml-1' : 'mr-1'}`} />
              {event.organizer}
            </span>
          </div>
        </div>
      ))}
    </RecommendationSection>
  );
};

export default EventRecommendations;
