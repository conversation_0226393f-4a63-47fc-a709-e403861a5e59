import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Shield, AlertTriangle, CheckCircle, XCircle, Eye,
  Lock, Key, Activity, RefreshCw, Filter, Search,
  Clock, User, Globe, TrendingUp, TrendingDown
} from 'lucide-react';
import AuthenticatedLayout from '../../layout/AuthenticatedLayout';
import { getAuthToken } from '../../../services/api';

interface SecurityEvent {
  id: string;
  event_type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  user__username?: string;
  ip_address?: string;
  description: string;
  is_resolved: boolean;
  created_at: string;
}

interface SecurityStats {
  total_events_7d: number;
  critical_events_7d: number;
  unresolved_events: number;
  failed_logins_24h: number;
}

interface SecurityRecommendation {
  type: 'info' | 'warning' | 'critical';
  title: string;
  description: string;
  action: string;
}

const SecurityCenter: React.FC = () => {
  const { t } = useTranslation();
  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([]);
  const [securityStats, setSecurityStats] = useState<SecurityStats | null>(null);
  const [recommendations, setRecommendations] = useState<SecurityRecommendation[]>([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'events' | 'stats' | 'recommendations'>('events');
  const [filterSeverity, setFilterSeverity] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchSecurityData();
  }, []);

  const fetchSecurityData = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/superadmin/system/security/', {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setSecurityEvents(data.recent_events || []);
        setSecurityStats(data.statistics || null);
        setRecommendations(data.security_recommendations || []);
      } else {
        console.error('Failed to fetch security data:', response.status);
        setSecurityEvents([]);
        setSecurityStats(null);
        setRecommendations([]);
      }
    } catch (error) {
      console.error('Failed to fetch security data:', error);
      setSecurityEvents([]);
      setSecurityStats(null);
      setRecommendations([]);
    } finally {
      setLoading(false);
    }
  };

  const resolveEvent = async (eventId: string) => {
    try {
      // This would call the API to resolve the event
      setSecurityEvents(prev => prev.map(event => 
        event.id === eventId ? { ...event, is_resolved: true } : event
      ));
    } catch (error) {
      console.error('Failed to resolve event:', error);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-400 bg-red-900/20 border-red-500/20';
      case 'high': return 'text-orange-400 bg-orange-900/20 border-orange-500/20';
      case 'medium': return 'text-yellow-400 bg-yellow-900/20 border-yellow-500/20';
      case 'low': return 'text-green-400 bg-green-900/20 border-green-500/20';
      default: return 'text-gray-400 bg-gray-900/20 border-gray-500/20';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return <XCircle className="w-4 h-4" />;
      case 'high': return <AlertTriangle className="w-4 h-4" />;
      case 'medium': return <Eye className="w-4 h-4" />;
      case 'low': return <CheckCircle className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  const getRecommendationColor = (type: string) => {
    switch (type) {
      case 'critical': return 'border-red-500 bg-red-900/20';
      case 'warning': return 'border-yellow-500 bg-yellow-900/20';
      case 'info': return 'border-blue-500 bg-blue-900/20';
      default: return 'border-gray-500 bg-gray-900/20';
    }
  };

  const filteredEvents = securityEvents.filter(event => {
    const matchesSeverity = filterSeverity === 'all' || event.severity === filterSeverity;
    const matchesSearch = searchTerm === '' || 
      event.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.event_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (event.user__username && event.user__username.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesSeverity && matchesSearch;
  });

  const tabs = [
    { id: 'events', name: 'Security Events', icon: <Activity className="w-4 h-4" /> },
    { id: 'stats', name: 'Statistics', icon: <TrendingUp className="w-4 h-4" /> },
    { id: 'recommendations', name: 'Recommendations', icon: <Shield className="w-4 h-4" /> }
  ];

  const renderEvents = () => (
    <div className="space-y-6">
      {/* Filters */}
      <div className="flex flex-wrap gap-4 items-center">
        <div className="flex items-center gap-2">
          <Filter className="w-4 h-4 text-gray-400" />
          <select
            value={filterSeverity}
            onChange={(e) => setFilterSeverity(e.target.value)}
            className="px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white text-sm"
          >
            <option value="all">All Severities</option>
            <option value="critical">Critical</option>
            <option value="high">High</option>
            <option value="medium">Medium</option>
            <option value="low">Low</option>
          </select>
        </div>
        
        <div className="flex items-center gap-2">
          <Search className="w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search events..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white text-sm"
          />
        </div>
      </div>

      {/* Events List */}
      <div className="space-y-4">
        {filteredEvents.length === 0 ? (
          <div className="text-center py-12">
            <Shield className="w-16 h-16 text-green-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-white mb-2">No Security Events</h3>
            <p className="text-gray-400">No security events match your current filters</p>
          </div>
        ) : (
          filteredEvents.map((event) => (
            <div key={event.id} className={`p-4 rounded-lg border ${getSeverityColor(event.severity)} ${event.is_resolved ? 'opacity-60' : ''}`}>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    {getSeverityIcon(event.severity)}
                    <span className="font-medium text-white capitalize">{event.event_type.replace('_', ' ')}</span>
                    <span className={`text-xs px-2 py-1 rounded-full ${getSeverityColor(event.severity)}`}>
                      {event.severity.toUpperCase()}
                    </span>
                    {event.is_resolved && (
                      <span className="text-xs px-2 py-1 rounded-full bg-green-800 text-green-300">
                        RESOLVED
                      </span>
                    )}
                  </div>
                  <p className="text-gray-300 mb-2">{event.description}</p>
                  <div className="flex items-center gap-4 text-sm text-gray-400">
                    {event.user__username && (
                      <span className="flex items-center gap-1">
                        <User className="w-3 h-3" />
                        {event.user__username}
                      </span>
                    )}
                    {event.ip_address && (
                      <span className="flex items-center gap-1">
                        <Globe className="w-3 h-3" />
                        {event.ip_address}
                      </span>
                    )}
                    <span className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      {new Date(event.created_at).toLocaleString()}
                    </span>
                  </div>
                </div>
                {!event.is_resolved && (
                  <button
                    onClick={() => resolveEvent(event.id)}
                    className="ml-4 px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-sm rounded-lg transition-colors"
                  >
                    Resolve
                  </button>
                )}
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );

  const renderStats = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div className="flex items-center gap-2 mb-2">
          <Activity className="w-5 h-5 text-blue-400" />
          <span className="text-sm text-gray-400">Total Events (7d)</span>
        </div>
        <div className="text-2xl font-bold text-white">{securityStats?.total_events_7d || 0}</div>
      </div>

      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div className="flex items-center gap-2 mb-2">
          <XCircle className="w-5 h-5 text-red-400" />
          <span className="text-sm text-gray-400">Critical Events (7d)</span>
        </div>
        <div className="text-2xl font-bold text-white">{securityStats?.critical_events_7d || 0}</div>
      </div>

      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div className="flex items-center gap-2 mb-2">
          <AlertTriangle className="w-5 h-5 text-yellow-400" />
          <span className="text-sm text-gray-400">Unresolved Events</span>
        </div>
        <div className="text-2xl font-bold text-white">{securityStats?.unresolved_events || 0}</div>
      </div>

      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div className="flex items-center gap-2 mb-2">
          <Lock className="w-5 h-5 text-orange-400" />
          <span className="text-sm text-gray-400">Failed Logins (24h)</span>
        </div>
        <div className="text-2xl font-bold text-white">{securityStats?.failed_logins_24h || 0}</div>
      </div>
    </div>
  );

  const renderRecommendations = () => (
    <div className="space-y-4">
      {recommendations.length === 0 ? (
        <div className="text-center py-12">
          <CheckCircle className="w-16 h-16 text-green-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-white mb-2">All Good!</h3>
          <p className="text-gray-400">No security recommendations at this time</p>
        </div>
      ) : (
        recommendations.map((rec, index) => (
          <div key={index} className={`p-4 rounded-lg border ${getRecommendationColor(rec.type)}`}>
            <div className="flex items-start gap-3">
              <div className="mt-1">
                {rec.type === 'critical' && <XCircle className="w-5 h-5 text-red-400" />}
                {rec.type === 'warning' && <AlertTriangle className="w-5 h-5 text-yellow-400" />}
                {rec.type === 'info' && <CheckCircle className="w-5 h-5 text-blue-400" />}
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-white mb-1">{rec.title}</h3>
                <p className="text-gray-300 mb-2">{rec.description}</p>
                <p className="text-sm text-gray-400">
                  <strong>Recommended Action:</strong> {rec.action}
                </p>
              </div>
            </div>
          </div>
        ))
      )}
    </div>
  );

  return (
    <AuthenticatedLayout>
      <div className="text-white">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-2">
            <Shield className="w-8 h-8 text-red-400" />
            <h1 className="text-3xl font-bold text-white">
              {t('superAdmin.security.title', 'Security Center')}
            </h1>
          </div>
          <p className="text-gray-400">
            {t('superAdmin.security.subtitle', 'Monitor security events and system protection')}
          </p>
        </div>

        {/* Controls */}
        <div className="flex justify-between items-center mb-8">
          <div className="text-sm text-gray-400">
            Last updated: {new Date().toLocaleString()}
          </div>
          <button
            onClick={fetchSecurityData}
            disabled={loading}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors disabled:opacity-50"
          >
            {loading ? <RefreshCw className="w-4 h-4 animate-spin" /> : <RefreshCw className="w-4 h-4" />}
            Refresh
          </button>
        </div>

        {/* Tabs */}
        <div className="flex flex-wrap gap-2 mb-8 border-b border-gray-700">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center gap-2 px-4 py-2 rounded-t-lg transition-colors ${
                activeTab === tab.id
                  ? 'bg-red-600 text-white border-b-2 border-red-400'
                  : 'text-gray-400 hover:text-white hover:bg-gray-800'
              }`}
            >
              {tab.icon}
              {tab.name}
            </button>
          ))}
        </div>

        {/* Content */}
        {loading && activeTab === 'events' ? (
          <div className="flex items-center justify-center py-12">
            <RefreshCw className="w-8 h-8 animate-spin text-red-400" />
            <span className="ml-2 text-gray-400">Loading security data...</span>
          </div>
        ) : (
          <>
            {activeTab === 'events' && renderEvents()}
            {activeTab === 'stats' && renderStats()}
            {activeTab === 'recommendations' && renderRecommendations()}
          </>
        )}
      </div>
    </AuthenticatedLayout>
  );
};

export default SecurityCenter;
