import React, { useState, useEffect } from 'react';
import {
  <PERSON>R<PERSON>,
  BookOpen,
  Star,
  Users,
  TrendingUp,
  Award,
  Zap,
  Target,
  Clock,
  CheckCircle,
  Sparkles,
  Brain,
  Rocket,
  Globe,
  Play,
  Download,
  Eye,
  Heart
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { RTLText, RTLFlex, RTLIcon } from '../common';
interface TemplatesLandingSectionProps {
  onBrowseTemplates?: () => void;
  onGetStarted?: () => void;
}

export const TemplatesLandingSection: React.FC<TemplatesLandingSectionProps> = ({ onBrowseTemplates,
  onGetStarted
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();

  const [currentFeature, setCurrentFeature] = useState(0);
  const [stats, setStats] = useState({
    totalTemplates: 0,
    activeUsers: 0,
    successRate: 0,
    avgRating: 0
  });

  // Fetch real stats from API
  useEffect(() => {
    const fetchRealStats = async () => {
      try {
        const { businessPlanTemplatesAPI } = await import('../../services/templateCustomizationApi');
        const templates = await businessPlanTemplatesAPI.getTemplates();

        if (Array.isArray(templates)) {
          const totalTemplates = templates.length;
          const totalRatings = templates.reduce((sum, template) => sum + (template.rating_count || 0), 0);
          const avgRating = totalRatings > 0
            ? templates.reduce((sum, template) => sum + (template.rating || 0) * (template.rating_count || 0), 0) / totalRatings
            : 0;
          const totalUsage = templates.reduce((sum, template) => sum + (template.usage_count || 0), 0);
          const completedTemplates = templates.filter(template => (template.completion_rate || 0) > 80).length;
          const successRate = totalTemplates > 0 ? (completedTemplates / totalTemplates) * 100 : 0;

          setStats({
            totalTemplates,
            activeUsers: Math.floor(totalUsage / 3), // Estimate active users from usage
            successRate: Math.round(successRate),
            avgRating: Math.round(avgRating * 10) / 10
          });
        }
      } catch (error) {
        console.error('Error fetching template stats:', error);
        // Keep default values on error
      }
    };

    fetchRealStats();
  }, []);

  // Featured template categories
  const categories = [
    {
      icon: Rocket,
      name: t('templatesLanding.categories.startup'),
      description: t('templatesLanding.categories.startupDesc'),
      count: 45,
      color: 'from-purple-500 to-pink-500',
      popular: true
    },
    {
      icon: TrendingUp,
      name: t('templatesLanding.categories.growth'),
      description: t('templatesLanding.categories.growthDesc'),
      count: 32,
      color: 'from-blue-500 to-cyan-500',
      popular: false
    },
    {
      icon: Target,
      name: t('templatesLanding.categories.investor'),
      description: t('templatesLanding.categories.investorDesc'),
      count: 28,
      color: 'from-green-500 to-emerald-500',
      popular: true
    },
    {
      icon: Globe,
      name: t('templatesLanding.categories.international'),
      description: t('templatesLanding.categories.internationalDesc'),
      count: 21,
      color: 'from-orange-500 to-red-500',
      popular: false
    }
  ];

  // Key features
  const features = [
    {
      icon: Brain,
      title: t('templatesLanding.features.aiPowered'),
      description: t('templatesLanding.features.aiPoweredDesc')
    },
    {
      icon: Users,
      title: t('templatesLanding.features.collaboration'),
      description: t('templatesLanding.features.collaborationDesc')
    },
    {
      icon: Award,
      title: t('templatesLanding.features.professional'),
      description: t('templatesLanding.features.professionalDesc')
    },
    {
      icon: Zap,
      title: t('templatesLanding.features.automation'),
      description: t('templatesLanding.features.automationDesc')
    }
  ];

  // Auto-rotate features
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentFeature((prev) => (prev + 1) % features.length);
    }, 3000);

    return () => clearInterval(interval);
  }, [features.length]);

  const handleBrowseTemplates = () => {
    if (onBrowseTemplates) {
      onBrowseTemplates();
    } else {
      navigate('/dashboard/templates');
    }
  };

  const handleGetStarted = () => {
    if (onGetStarted) {
      onGetStarted();
    } else {
      navigate('/dashboard/business-plans/new');
    }
  };

  return (
    <div className="space-y-12">
      {/* Hero Section */}
      <div className="text-center space-y-6">
        <div className={`inline-flex items-center px-4 py-2 bg-purple-500/20 text-purple-300 rounded-full text-sm font-medium border border-purple-500/30 ${isRTL ? "flex-row-reverse" : ""}`}>
          <Sparkles className={`w-4 h-4 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
          {t('templatesLanding.badge')}
        </div>

        <div className="space-y-4">
          <RTLText as="h1" className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400 bg-clip-text text-transparent">
            {t('templatesLanding.title')}
          </RTLText>

          <RTLText as="h2" className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto">
            {t('templatesLanding.subtitle')}
          </RTLText>

          <RTLText className="text-lg text-gray-400 max-w-4xl mx-auto leading-relaxed">
            {t('templatesLanding.description')}
          </RTLText>
        </div>

        {/* CTA Buttons */}
        <RTLFlex className={`flex-col sm:flex-row items-center justify-center gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
          <button
            onClick={handleBrowseTemplates}
            className={`group px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg font-semibold text-lg transform hover:scale-105 transition-all duration-300 hover:shadow-2xl hover:shadow-purple-500/25 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <BookOpen className={`w-5 h-5 mr-3 ${isRTL ? "space-x-reverse" : ""}`} />
            {t('templatesLanding.browseTemplates')}
            <RTLIcon
              icon={ArrowRight}
              size={20}
              flipInRTL={true}
              className={`ml-3 group-hover:translate-x-1 transition-transform duration-300 ${isRTL ? "space-x-reverse" : ""}`}
            />
          </button>

          <button
            onClick={handleGetStarted}
            className={`px-8 py-4 bg-transparent border-2 border-purple-500 text-purple-400 rounded-lg font-semibold text-lg hover:bg-purple-500 hover:text-white transition-all duration-300 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <Rocket className={`w-5 h-5 mr-3 ${isRTL ? "space-x-reverse" : ""}`} />
            {t('templatesLanding.getStarted')}
          </button>
        </RTLFlex>
      </div>

      {/* Stats Section */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
        <div className="text-center p-6 bg-gray-800/50 rounded-xl border border-gray-700 backdrop-blur-sm">
          <BookOpen className="w-8 h-8 mx-auto mb-3 text-purple-400" />
          <div className="text-3xl font-bold text-white mb-1">{stats.totalTemplates}+</div>
          <div className="text-sm text-gray-400">{t('templatesLanding.stats.templates')}</div>
        </div>

        <div className="text-center p-6 bg-gray-800/50 rounded-xl border border-gray-700 backdrop-blur-sm">
          <Users className="w-8 h-8 mx-auto mb-3 text-blue-400" />
          <div className="text-3xl font-bold text-white mb-1">{stats.activeUsers.toLocaleString()}+</div>
          <div className="text-sm text-gray-400">{t('templatesLanding.stats.users')}</div>
        </div>

        <div className="text-center p-6 bg-gray-800/50 rounded-xl border border-gray-700 backdrop-blur-sm">
          <TrendingUp className="w-8 h-8 mx-auto mb-3 text-green-400" />
          <div className="text-3xl font-bold text-white mb-1">{stats.successRate}%</div>
          <div className="text-sm text-gray-400">{t('templatesLanding.stats.success')}</div>
        </div>

        <div className="text-center p-6 bg-gray-800/50 rounded-xl border border-gray-700 backdrop-blur-sm">
          <Star className="w-8 h-8 mx-auto mb-3 text-yellow-400" />
          <div className="text-3xl font-bold text-white mb-1">{stats.avgRating}</div>
          <div className="text-sm text-gray-400">{t('templatesLanding.stats.rating')}</div>
        </div>
      </div>

      {/* Categories Section */}
      <div className="space-y-8">
        <div className="text-center">
          <RTLText as="h3" className="text-3xl font-bold mb-4">
            {t('templatesLanding.categoriesTitle')}
          </RTLText>
          <RTLText className="text-lg text-gray-400 max-w-2xl mx-auto">
            {t('templatesLanding.categoriesDescription')}
          </RTLText>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {categories.map((category, index) => (
            <div
              key={index}
              className="relative group p-6 bg-gray-800/50 rounded-xl border border-gray-700 hover:border-purple-500/50 transition-all duration-300 cursor-pointer"
              onClick={handleBrowseTemplates}
            >
              {category.popular && (
                <div className="absolute -top-2 -right-2 px-2 py-1 bg-gradient-to-r from-purple-600 to-pink-600 text-white text-xs rounded-full">
                  {t('templatesLanding.popular')}
                </div>
              )}

              <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${category.color} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                <category.icon className="w-6 h-6 text-white" />
              </div>

              <RTLText as="h4" className="text-lg font-semibold mb-2">
                {category.name}
              </RTLText>

              <RTLText className="text-sm text-gray-400 mb-3">
                {category.description}
              </RTLText>

              <RTLFlex className="items-center justify-between">
                <span className="text-xs text-purple-300">
                  {category.count} {t('templatesLanding.templatesAvailable')}
                </span>
                <ArrowRight className="w-4 h-4 text-gray-400 group-hover:text-purple-400 group-hover:translate-x-1 transition-all duration-300" />
              </RTLFlex>
            </div>
          ))}
        </div>
      </div>

      {/* Features Section */}
      <div className="bg-gradient-to-r from-purple-900/20 to-blue-900/20 rounded-2xl p-8 border border-purple-500/20">
        <div className="text-center mb-8">
          <RTLText as="h3" className="text-3xl font-bold mb-4">
            {t('templatesLanding.featuresTitle')}
          </RTLText>
          <RTLText className="text-lg text-gray-400">
            {t('templatesLanding.featuresDescription')}
          </RTLText>
        </div>

        {/* Current Feature Display */}
        <div className="text-center mb-8">
          <div className={`inline-flex items-center justify-center w-16 h-16 bg-purple-600/20 rounded-full mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            {React.createElement(features[currentFeature].icon, {
              className: "w-8 h-8 text-purple-400"
            })}
          </div>

          <RTLText as="h4" className="text-xl font-semibold mb-2">
            {features[currentFeature].title}
          </RTLText>

          <RTLText className="text-gray-300 max-w-md mx-auto">
            {features[currentFeature].description}
          </RTLText>
        </div>

        {/* Feature Indicators */}
        <div className={`flex justify-center gap-2 mb-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          {features.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentFeature(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentFeature
                  ? 'bg-purple-500 scale-125'
                  : 'bg-gray-600 hover:bg-gray-500'}
              }`}
            />
          ))}
        </div>

        {/* All Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {features.map((feature, index) => (
            <div
              key={index}
              className={`p-4 rounded-lg border transition-all duration-300 cursor-pointer ${
                index === currentFeature
                  ? 'bg-purple-600/20 border-purple-500/50'
                  : 'bg-gray-800/30 border-gray-700 hover:border-purple-500/30'}
              }`}
              onClick={() => setCurrentFeature(index)}
            >
              <feature.icon className={`w-6 h-6 mb-2 ${
                index === currentFeature ? 'text-purple-400' : 'text-gray-400'}
              }`} />
              <RTLText className="font-medium text-sm">
                {feature.title}
              </RTLText>
            </div>
          ))}
        </div>
      </div>

      {/* Call to Action */}
      <div className="text-center bg-gray-800/50 rounded-2xl p-8 border border-gray-700">
        <RTLText as="h3" className="text-2xl font-bold mb-4">
          {t('templatesLanding.ctaTitle')}
        </RTLText>
        <RTLText className="text-gray-400 mb-6 max-w-2xl mx-auto">
          {t('templatesLanding.ctaDescription')}
        </RTLText>

        <RTLFlex className={`flex-col sm:flex-row items-center justify-center gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
          <button
            onClick={handleBrowseTemplates}
            className={`px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <Eye className={`w-5 h-5 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
            {t('templatesLanding.exploreTemplates')}
          </button>

          <button
            onClick={handleGetStarted}
            className={`px-6 py-3 bg-transparent border border-purple-500 text-purple-400 hover:bg-purple-500 hover:text-white rounded-lg font-medium transition-colors flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <Play className={`w-5 h-5 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
            {t('templatesLanding.startBuilding')}
          </button>
        </RTLFlex>
      </div>
    </div>
  );
};
