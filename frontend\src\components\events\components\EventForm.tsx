import React from 'react';
import { X } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { RTLText } from '../../common';
export interface EventFormData {
  title: string;
  description: string;
  date: string;
  time: string;
  location: string;
  is_virtual: boolean;
  virtual_link: string;
}

interface EventFormProps {
  formData: EventFormData;
  onInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  onSubmit: (e: React.FormEvent) => void;
  onCancel: () => void;
}

const EventForm: React.FC<EventFormProps> = ({ formData,
  onInputChange,
  onSubmit,
  onCancel
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  return (
    <div className="bg-gradient-to-r from-purple-900/20 to-blue-900/20 rounded-xl p-8 backdrop-blur-sm max-w-2xl mx-auto">
      <div className={`flex justify-between items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
        <h3 className="text-2xl font-semibold">t("common.create.new.event", "Create New Event")</h3>
        <button
          onClick={onCancel}
          className="text-gray-400 hover:text-white"
        >
          <X size={24} />
        </button>
      </div>

      <form onSubmit={onSubmit} className="space-y-4">
        <div>
          <label htmlFor="title" className="block text-sm font-medium text-gray-300 mb-1">
            {t('home.events.eventTitle')}*
          </label>
          <input
            id="title"
            name="title"
            type="text"
            required
            value={formData.title}
            onChange={onInputChange}
            className="w-full px-4 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
          />
        </div>

        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-300 mb-1">
            {t('home.events.description')}*
          </label>
          <textarea
            id="description"
            name="description"
            required
            rows={4}
            value={formData.description}
            onChange={onInputChange}
            className="w-full px-4 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
          ></textarea>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label htmlFor="date" className="block text-sm font-medium text-gray-300 mb-1">
              {t('home.events.date')}*
            </label>
            <input
              id="date"
              name="date"
              type="date"
              required
              value={formData.date}
              onChange={onInputChange}
              className="w-full px-4 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
            />
          </div>
          <div>
            <label htmlFor="time" className="block text-sm font-medium text-gray-300 mb-1">
              {t('home.events.time')}*
            </label>
            <input
              id="time"
              name="time"
              type="time"
              required
              value={formData.time}
              onChange={onInputChange}
              className="w-full px-4 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
            />
          </div>
        </div>

        <div>
          <label htmlFor="location" className="block text-sm font-medium text-gray-300 mb-1">
            {t('home.events.location')}*
          </label>
          <input
            id="location"
            name="location"
            type="text"
            required
            value={formData.location}
            onChange={onInputChange}
            className="w-full px-4 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
          />
        </div>

        <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
          <input
            id="is_virtual"
            name="is_virtual"
            type="checkbox"
            checked={formData.is_virtual}
            onChange={onInputChange}
            className="w-4 h-4 text-purple-600 bg-indigo-950/50 border-indigo-800 rounded focus:ring-purple-500"
          />
          <label htmlFor="is_virtual" className={`ml-2 text-sm font-medium text-gray-300 ${isRTL ? "space-x-reverse" : ""}`}>
            {t('home.events.virtualEvent')}
          </label>
        </div>

        {formData.is_virtual && (
          <div>
            <label htmlFor="virtual_link" className="block text-sm font-medium text-gray-300 mb-1">
              {t('home.events.virtualMeetingLink')}
            </label>
            <input
              id="virtual_link"
              name="virtual_link"
              type="url"
              value={formData.virtual_link}
              onChange={onInputChange}
              className="w-full px-4 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
              placeholder="https://..."
            />
          </div>
        )}

        <div className="pt-4">
          <button
            type="submit"
            className="w-full px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300"
          >
            {t('home.events.createNewEvent')}
          </button>
        </div>
      </form>
    </div>
  );
};

export default EventForm;
