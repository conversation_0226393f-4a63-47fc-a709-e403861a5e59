import React, { useState, useEffect } from 'react';
import { Save, X, Target, Plus } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { BusinessIdea } from '../../services/incubatorApi';

interface Goal {
  id?: number;
  title: string;
  description: string;
  target_date: string;
  status: 'not_started' | 'in_progress' | 'completed' | 'delayed' | 'cancelled';
  timeframe: 'short_term' | 'medium_term' | 'long_term';
  business_idea: number;
  target_value?: string;
  current_value?: string;
  measurement_unit?: string;
  completion_notes?: string;
}

interface GoalFormProps {
  goal?: Goal;
  businessIdea: BusinessIdea;
  onSave: (data: Partial<Goal>) => Promise<boolean>;
  onCancel: () => void;
  isLoading?: boolean;
  mode: 'create' | 'edit';
}

const GoalForm: React.FC<GoalFormProps> = ({
  goal,
  businessIdea,
  onSave,
  onCancel,
  isLoading = false,
  mode
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [formData, setFormData] = useState({
    title: goal?.title || '',
    description: goal?.description || '',
    target_date: goal?.target_date || '',
    status: goal?.status || 'not_started',
    timeframe: goal?.timeframe || 'medium_term',
    business_idea: businessIdea.id,
    target_value: goal?.target_value || '',
    current_value: goal?.current_value || '',
    measurement_unit: goal?.measurement_unit || '',
    completion_notes: goal?.completion_notes || ''
  });

  const [formError, setFormError] = useState<string | null>(null);
  const [hasChanges, setHasChanges] = useState(mode === 'create');

  useEffect(() => {
    if (mode === 'edit' && goal) {
      // Check if form has changes
      const hasFormChanges = Object.keys(formData).some(key => {
        if (key === 'business_idea') return false;
        const formValue = formData[key as keyof typeof formData];
        const originalValue = goal[key as keyof Goal] || '';
        return formValue !== originalValue;
      });
      setHasChanges(hasFormChanges);
    }
  }, [formData, goal, mode]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    setFormError(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);

    // Validate required fields
    if (!formData.title.trim()) {
      setFormError(t('incubator.goal.titleRequired'));
      return;
    }

    if (!formData.description.trim()) {
      setFormError(t('incubator.goal.descriptionRequired'));
      return;
    }

    if (!formData.target_date) {
      setFormError(t('incubator.goal.targetDateRequired'));
      return;
    }

    // Validate target date is not in the past (unless goal is completed)
    const targetDate = new Date(formData.target_date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (targetDate < today && formData.status !== 'completed') {
      setFormError(t('incubator.goal.targetDatePastError'));
      return;
    }

    const success = await onSave(formData);
    if (!success) {
      setFormError(mode === 'create' 
        ? t('incubator.goal.createError')
        : t('incubator.goal.updateError')
      );
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 rounded-xl border border-blue-500/30 max-w-2xl w-full max-h-[90vh] overflow-hidden shadow-2xl">
        {/* Header */}
        <div className={`flex items-center justify-between p-6 border-b border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            {mode === 'create' ? (
              <Plus size={24} className={`text-blue-400 ${isRTL ? 'ml-3' : 'mr-3'}`} />
            ) : (
              <Target size={24} className={`text-blue-400 ${isRTL ? 'ml-3' : 'mr-3'}`} />
            )}
            <h2 className="text-xl font-semibold text-white">
              {mode === 'create' 
                ? t('incubator.goal.createTitle')
                : t('incubator.goal.editTitle')
              }
            </h2>
          </div>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-white transition-colors"
            disabled={isLoading}
          >
            <X size={20} />
          </button>
        </div>

        {/* Business Idea Info */}
        <div className="px-6 py-4 bg-gray-800/50 border-b border-gray-700">
          <p className={`text-sm text-gray-400 ${isRTL ? 'text-right' : ''}`}>
            {t('incubator.goal.forBusinessIdea')}: 
            <span className="text-white font-medium ml-1">{businessIdea.title}</span>
          </p>
        </div>

        {/* Form */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Error Message */}
            {formError && (
              <div className="bg-red-900/30 border border-red-500/50 rounded-lg p-4">
                <p className="text-red-400 text-sm">{formError}</p>
              </div>
            )}

            {/* Title */}
            <div>
              <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                {t('incubator.goal.title')} *
              </label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-gray-400 ${isRTL ? 'text-right' : ''}`}
                placeholder={t('incubator.goal.titlePlaceholder')}
                required
                disabled={isLoading}
              />
            </div>

            {/* Description */}
            <div>
              <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                {t('incubator.goal.description')} *
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={4}
                className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-gray-400 resize-none ${isRTL ? 'text-right' : ''}`}
                placeholder={t('incubator.goal.descriptionPlaceholder')}
                required
                disabled={isLoading}
              />
            </div>

            {/* Target Date and Timeframe Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Target Date */}
              <div>
                <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                  {t('incubator.goal.targetDate')} *
                </label>
                <input
                  type="date"
                  name="target_date"
                  value={formData.target_date}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white ${isRTL ? 'text-right' : ''}`}
                  required
                  disabled={isLoading}
                />
              </div>

              {/* Timeframe */}
              <div>
                <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                  {t('incubator.goal.timeframe')}
                </label>
                <select
                  name="timeframe"
                  value={formData.timeframe}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white ${isRTL ? 'text-right' : ''}`}
                  disabled={isLoading}
                >
                  <option value="short_term">{t('incubator.goal.timeframe.shortTerm')}</option>
                  <option value="medium_term">{t('incubator.goal.timeframe.mediumTerm')}</option>
                  <option value="long_term">{t('incubator.goal.timeframe.longTerm')}</option>
                </select>
              </div>
            </div>

            {/* Measurement Fields */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Target Value */}
              <div>
                <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                  {t('incubator.goal.targetValue')}
                </label>
                <input
                  type="text"
                  name="target_value"
                  value={formData.target_value}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-gray-400 ${isRTL ? 'text-right' : ''}`}
                  placeholder={t('incubator.goal.targetValuePlaceholder')}
                  disabled={isLoading}
                />
              </div>

              {/* Current Value */}
              <div>
                <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                  {t('incubator.goal.currentValue')}
                </label>
                <input
                  type="text"
                  name="current_value"
                  value={formData.current_value}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-gray-400 ${isRTL ? 'text-right' : ''}`}
                  placeholder={t('incubator.goal.currentValuePlaceholder')}
                  disabled={isLoading}
                />
              </div>

              {/* Measurement Unit */}
              <div>
                <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                  {t('incubator.goal.measurementUnit')}
                </label>
                <input
                  type="text"
                  name="measurement_unit"
                  value={formData.measurement_unit}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-gray-400 ${isRTL ? 'text-right' : ''}`}
                  placeholder={t('incubator.goal.measurementUnitPlaceholder')}
                  disabled={isLoading}
                />
              </div>
            </div>

            {/* Status */}
            <div>
              <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                {t('incubator.goal.status')}
              </label>
              <select
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white ${isRTL ? 'text-right' : ''}`}
                disabled={isLoading}
              >
                <option value="not_started">{t('incubator.goal.status.notStarted')}</option>
                <option value="in_progress">{t('incubator.goal.status.inProgress')}</option>
                <option value="completed">{t('incubator.goal.status.completed')}</option>
                <option value="delayed">{t('incubator.goal.status.delayed')}</option>
                <option value="cancelled">{t('incubator.goal.status.cancelled')}</option>
              </select>
            </div>

            {/* Completion Notes (only show if status is completed) */}
            {formData.status === 'completed' && (
              <div>
                <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                  {t('incubator.goal.completionNotes')}
                </label>
                <textarea
                  name="completion_notes"
                  value={formData.completion_notes}
                  onChange={handleInputChange}
                  rows={3}
                  className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-gray-400 resize-none ${isRTL ? 'text-right' : ''}`}
                  placeholder={t('incubator.goal.completionNotesPlaceholder')}
                  disabled={isLoading}
                />
              </div>
            )}
          </form>
        </div>

        {/* Footer */}
        <div className={`flex justify-end gap-4 p-6 border-t border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <button
            type="button"
            onClick={onCancel}
            className="px-6 py-2 border border-gray-600 rounded-lg hover:bg-gray-800 transition-colors text-white disabled:opacity-50"
            disabled={isLoading}
          >
            {t('common.cancel')}
          </button>
          <button
            onClick={handleSubmit}
            disabled={isLoading || (mode === 'edit' && !hasChanges)}
            className="px-6 py-2 bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors flex items-center text-white disabled:opacity-50"
          >
            {isLoading ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                {mode === 'create' ? t('common.creating') : t('common.saving')}
              </>
            ) : (
              <>
                <Save size={16} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                {mode === 'create' ? t('common.create') : t('common.saveChanges')}
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default GoalForm;
