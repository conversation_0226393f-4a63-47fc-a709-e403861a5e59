/**
 * End-to-End tests for Admin CRUD workflows
 * These tests simulate complete user workflows for managing entities
 */

import { test, expect, Page } from '@playwright/test';

// Test configuration
const ADMIN_EMAIL = '<EMAIL>';
const ADMIN_PASSWORD = 'testpassword';
const BASE_URL = process.env.REACT_APP_BASE_URL || 'http://localhost:3000';

// Helper functions
async function loginAsAdmin(page: Page) {
  await page.goto(`${BASE_URL}/login`);
  await page.fill('[data-testid="email-input"]', ADMIN_EMAIL);
  await page.fill('[data-testid="password-input"]', ADMIN_PASSWORD);
  await page.click('[data-testid="login-button"]');
  await page.waitForURL('**/admin/**');
}

async function navigateToBusinessPlans(page: Page) {
  await page.goto(`${BASE_URL}/admin/incubator/business-plans`);
  await page.waitForSelector('[data-testid="business-plans-table"]');
}

async function navigateToMentorProfiles(page: Page) {
  await page.goto(`${BASE_URL}/admin/incubator/mentor-profiles`);
  await page.waitForSelector('[data-testid="mentor-profiles-table"]');
}

async function navigateToFunding(page: Page) {
  await page.goto(`${BASE_URL}/admin/funding`);
  await page.waitForSelector('[data-testid="funding-opportunities-table"]');
}

async function navigateToMilestones(page: Page) {
  await page.goto(`${BASE_URL}/admin/milestones`);
  await page.waitForSelector('[data-testid="milestones-table"]');
}

test.describe('Admin CRUD Workflows', () => {
  test.beforeEach(async ({ page }) => {
    await loginAsAdmin(page);
  });

  test.describe('Business Plans Management', () => {
    test('should complete full CRUD workflow for business plans', async ({ page }) => {
      await navigateToBusinessPlans(page);

      // CREATE: Add new business plan
      await page.click('[data-testid="create-plan-button"]');
      await page.waitForSelector('[data-testid="business-plan-modal"]');

      // Fill basic information
      await page.fill('[name="title"]', 'E2E Test Business Plan');
      await page.fill('[name="business_idea"]', '1');
      await page.selectOption('[name="status"]', 'draft');

      // Navigate to content section
      await page.click('[data-testid="content-section-tab"]');
      await page.fill('[name="content.executive_summary"]', 'This is a test executive summary for E2E testing.');
      await page.fill('[name="content.market_analysis"]', 'Test market analysis content.');

      // Save the plan
      await page.click('[data-testid="save-button"]');
      await page.waitForSelector('[data-testid="business-plan-modal"]', { state: 'hidden' });

      // Verify plan appears in table
      await expect(page.locator('text=E2E Test Business Plan')).toBeVisible();

      // READ: Verify plan details
      const planRow = page.locator('[data-testid="business-plan-row"]').filter({ hasText: 'E2E Test Business Plan' });
      await expect(planRow).toBeVisible();
      await expect(planRow.locator('[data-testid="status-badge"]')).toContainText('Draft');

      // UPDATE: Edit the business plan
      await planRow.locator('[data-testid="edit-button"]').click();
      await page.waitForSelector('[data-testid="business-plan-modal"]');

      await page.fill('[name="title"]', 'Updated E2E Test Business Plan');
      await page.selectOption('[name="status"]', 'in_progress');
      await page.click('[data-testid="save-button"]');
      await page.waitForSelector('[data-testid="business-plan-modal"]', { state: 'hidden' });

      // Verify updates
      await expect(page.locator('text=Updated E2E Test Business Plan')).toBeVisible();
      const updatedRow = page.locator('[data-testid="business-plan-row"]').filter({ hasText: 'Updated E2E Test Business Plan' });
      await expect(updatedRow.locator('[data-testid="status-badge"]')).toContainText('In Progress');

      // Test bulk operations
      await updatedRow.locator('[data-testid="row-checkbox"]').check();
      await expect(page.locator('[data-testid="bulk-actions"]')).toBeVisible();
      await expect(page.locator('[data-testid="selected-count"]')).toContainText('1 selected');

      // Test AI feedback generation
      await page.click('[data-testid="generate-feedback-bulk-action"]');
      await page.waitForResponse(response => response.url().includes('/generate-feedback/'));

      // Test plan duplication
      await updatedRow.locator('[data-testid="duplicate-button"]').click();
      await page.waitForResponse(response => response.url().includes('/duplicate/'));
      await expect(page.locator('text=Copy of Updated E2E Test Business Plan')).toBeVisible();

      // DELETE: Remove the business plan
      await updatedRow.locator('[data-testid="delete-button"]').click();
      await page.waitForSelector('[data-testid="business-plan-delete-modal"]');
      await page.click('[data-testid="confirm-delete-button"]');
      await page.waitForSelector('[data-testid="business-plan-delete-modal"]', { state: 'hidden' });

      // Verify plan is removed
      await expect(page.locator('text=Updated E2E Test Business Plan')).not.toBeVisible();
    });

    test('should handle search and filtering', async ({ page }) => {
      await navigateToBusinessPlans(page);

      // Test search functionality
      await page.fill('[data-testid="search-input"]', 'test search query');
      await page.waitForResponse(response => response.url().includes('search=test%20search%20query'));

      // Test advanced filtering
      await page.click('[data-testid="advanced-filter-button"]');
      await page.selectOption('[data-testid="status-filter"]', 'draft');
      await page.click('[data-testid="apply-filters-button"]');
      await page.waitForResponse(response => response.url().includes('status=draft'));

      // Clear filters
      await page.click('[data-testid="clear-filters-button"]');
      await page.waitForResponse(response => !response.url().includes('status='));
    });
  });

  test.describe('Mentor Profiles Management', () => {
    test('should complete full CRUD workflow for mentor profiles', async ({ page }) => {
      await navigateToMentorProfiles(page);

      // CREATE: Add new mentor profile
      await page.click('[data-testid="create-profile-button"]');
      await page.waitForSelector('[data-testid="mentor-profile-modal"]');

      // Fill basic information
      await page.fill('[name="company"]', 'E2E Test Company');
      await page.fill('[name="position"]', 'Senior Developer');
      await page.fill('[name="years_of_experience"]', '5');
      await page.fill('[name="bio"]', 'This is a test mentor profile for E2E testing.');

      // Select expertise areas
      await page.check('[data-testid="expertise-technology"]');
      await page.check('[data-testid="expertise-marketing"]');

      // Set availability
      await page.selectOption('[name="availability"]', 'weekdays');
      await page.fill('[name="max_mentees"]', '3');
      await page.check('[name="is_accepting_mentees"]');

      // Save the profile
      await page.click('[data-testid="save-button"]');
      await page.waitForSelector('[data-testid="mentor-profile-modal"]', { state: 'hidden' });

      // Verify profile appears in table
      await expect(page.locator('text=E2E Test Company')).toBeVisible();

      // READ: Verify profile details
      const profileRow = page.locator('[data-testid="mentor-profile-row"]').filter({ hasText: 'E2E Test Company' });
      await expect(profileRow).toBeVisible();
      await expect(profileRow.locator('[data-testid="status-badge"]')).toContainText('Pending');

      // UPDATE: Verify the mentor profile
      await profileRow.locator('[data-testid="edit-button"]').click();
      await page.waitForSelector('[data-testid="mentor-profile-modal"]');

      await page.check('[name="is_verified"]');
      await page.click('[data-testid="save-button"]');
      await page.waitForSelector('[data-testid="mentor-profile-modal"]', { state: 'hidden' });

      // Verify verification status
      const updatedRow = page.locator('[data-testid="mentor-profile-row"]').filter({ hasText: 'E2E Test Company' });
      await expect(updatedRow.locator('[data-testid="status-badge"]')).toContainText('Verified');

      // Test bulk verification
      await updatedRow.locator('[data-testid="row-checkbox"]').check();
      await page.click('[data-testid="verify-bulk-action"]');
      await page.waitForResponse(response => response.url().includes('/mentor-profiles/'));

      // DELETE: Remove the mentor profile
      await updatedRow.locator('[data-testid="delete-button"]').click();
      await page.waitForSelector('[data-testid="mentor-profile-delete-modal"]');
      await page.click('[data-testid="confirm-delete-button"]');
      await page.waitForSelector('[data-testid="mentor-profile-delete-modal"]', { state: 'hidden' });

      // Verify profile is removed
      await expect(page.locator('text=E2E Test Company')).not.toBeVisible();
    });
  });

  test.describe('Funding Management', () => {
    test('should complete full CRUD workflow for funding opportunities', async ({ page }) => {
      await navigateToFunding(page);

      // CREATE: Add new funding opportunity
      await page.click('[data-testid="create-opportunity-button"]');
      await page.waitForSelector('[data-testid="funding-opportunity-modal"]');

      // Fill basic information
      await page.fill('[name="title"]', 'E2E Test Funding Opportunity');
      await page.fill('[name="description"]', 'This is a test funding opportunity for E2E testing.');
      await page.selectOption('[name="funding_type"]', 'seed');
      await page.selectOption('[name="status"]', 'active');

      // Navigate to financial details
      await page.click('[data-testid="financial-section-tab"]');
      await page.fill('[name="min_amount"]', '10000');
      await page.fill('[name="max_amount"]', '100000');
      await page.fill('[name="equity_percentage"]', '10');

      // Navigate to terms
      await page.click('[data-testid="terms-section-tab"]');
      await page.fill('[name="requirements"]', 'Test requirements for funding.');
      await page.fill('[name="terms_and_conditions"]', 'Test terms and conditions.');

      // Save the opportunity
      await page.click('[data-testid="save-button"]');
      await page.waitForSelector('[data-testid="funding-opportunity-modal"]', { state: 'hidden' });

      // Verify opportunity appears in table
      await expect(page.locator('text=E2E Test Funding Opportunity')).toBeVisible();

      // READ: Verify opportunity details
      const opportunityRow = page.locator('[data-testid="funding-opportunity-row"]').filter({ hasText: 'E2E Test Funding Opportunity' });
      await expect(opportunityRow).toBeVisible();
      await expect(opportunityRow.locator('[data-testid="status-badge"]')).toContainText('Active');
      await expect(opportunityRow.locator('[data-testid="amount-range"]')).toContainText('$10,000 - $100,000');

      // UPDATE: Edit the funding opportunity
      await opportunityRow.locator('[data-testid="edit-button"]').click();
      await page.waitForSelector('[data-testid="funding-opportunity-modal"]');

      await page.fill('[name="title"]', 'Updated E2E Test Funding Opportunity');
      await page.click('[data-testid="financial-section-tab"]');
      await page.fill('[name="max_amount"]', '200000');
      await page.click('[data-testid="save-button"]');
      await page.waitForSelector('[data-testid="funding-opportunity-modal"]', { state: 'hidden' });

      // Verify updates
      await expect(page.locator('text=Updated E2E Test Funding Opportunity')).toBeVisible();
      const updatedRow = page.locator('[data-testid="funding-opportunity-row"]').filter({ hasText: 'Updated E2E Test Funding Opportunity' });
      await expect(updatedRow.locator('[data-testid="amount-range"]')).toContainText('$10,000 - $200,000');

      // Test bulk operations
      await updatedRow.locator('[data-testid="row-checkbox"]').check();
      await page.click('[data-testid="deactivate-bulk-action"]');
      await page.waitForResponse(response => response.url().includes('/funding-opportunities/'));

      // DELETE: Remove the funding opportunity
      await updatedRow.locator('[data-testid="delete-button"]').click();
      await page.waitForSelector('[data-testid="funding-opportunity-delete-modal"]');
      await page.click('[data-testid="confirm-delete-button"]');
      await page.waitForSelector('[data-testid="funding-opportunity-delete-modal"]', { state: 'hidden' });

      // Verify opportunity is removed
      await expect(page.locator('text=Updated E2E Test Funding Opportunity')).not.toBeVisible();
    });
  });

  test.describe('Milestones Management', () => {
    test('should complete full CRUD workflow for milestones', async ({ page }) => {
      await navigateToMilestones(page);

      // CREATE: Add new milestone
      await page.click('[data-testid="create-milestone-button"]');
      await page.waitForSelector('[data-testid="milestone-modal"]');

      // Fill milestone information
      await page.fill('[name="title"]', 'E2E Test Milestone');
      await page.fill('[name="description"]', 'This is a test milestone for E2E testing.');
      await page.fill('[name="business_idea"]', '1');
      await page.fill('[name="due_date"]', '2024-12-31');
      await page.selectOption('[name="priority"]', 'high');
      await page.fill('[name="estimated_hours"]', '40');

      // Save the milestone
      await page.click('[data-testid="save-button"]');
      await page.waitForSelector('[data-testid="milestone-modal"]', { state: 'hidden' });

      // Verify milestone appears in table
      await expect(page.locator('text=E2E Test Milestone')).toBeVisible();

      // READ: Verify milestone details
      const milestoneRow = page.locator('[data-testid="milestone-row"]').filter({ hasText: 'E2E Test Milestone' });
      await expect(milestoneRow).toBeVisible();
      await expect(milestoneRow.locator('[data-testid="priority-badge"]')).toContainText('High');
      await expect(milestoneRow.locator('[data-testid="status-badge"]')).toContainText('Not Started');

      // UPDATE: Start the milestone
      await milestoneRow.locator('[data-testid="edit-button"]').click();
      await page.waitForSelector('[data-testid="milestone-modal"]');

      await page.selectOption('[name="status"]', 'in_progress');
      await page.click('[data-testid="save-button"]');
      await page.waitForSelector('[data-testid="milestone-modal"]', { state: 'hidden' });

      // Verify status update
      const updatedRow = page.locator('[data-testid="milestone-row"]').filter({ hasText: 'E2E Test Milestone' });
      await expect(updatedRow.locator('[data-testid="status-badge"]')).toContainText('In Progress');

      // Test milestone completion
      await updatedRow.locator('[data-testid="complete-button"]').click();
      await page.waitForResponse(response => response.url().includes('/complete/'));
      await expect(updatedRow.locator('[data-testid="status-badge"]')).toContainText('Completed');

      // Test bulk operations
      await updatedRow.locator('[data-testid="row-checkbox"]').check();
      await page.click('[data-testid="start-bulk-action"]');
      await page.waitForResponse(response => response.url().includes('/bulk-update/'));

      // DELETE: Remove the milestone
      await updatedRow.locator('[data-testid="delete-button"]').click();
      await page.waitForSelector('[data-testid="milestone-delete-modal"]');
      await page.click('[data-testid="confirm-delete-button"]');
      await page.waitForSelector('[data-testid="milestone-delete-modal"]', { state: 'hidden' });

      // Verify milestone is removed
      await expect(page.locator('text=E2E Test Milestone')).not.toBeVisible();
    });

    test('should display analytics correctly', async ({ page }) => {
      await navigateToMilestones(page);

      // Verify analytics cards are displayed
      await expect(page.locator('[data-testid="total-milestones-card"]')).toBeVisible();
      await expect(page.locator('[data-testid="completed-milestones-card"]')).toBeVisible();
      await expect(page.locator('[data-testid="overdue-milestones-card"]')).toBeVisible();
      await expect(page.locator('[data-testid="completion-rate-card"]')).toBeVisible();

      // Verify analytics data is loaded
      await expect(page.locator('[data-testid="total-milestones-count"]')).not.toBeEmpty();
      await expect(page.locator('[data-testid="completion-rate-percentage"]')).toContainText('%');
    });
  });

  test.describe('Cross-Entity Integration', () => {
    test('should handle relationships between entities', async ({ page }) => {
      // Test that business plans can reference business ideas
      await navigateToBusinessPlans(page);
      await page.click('[data-testid="create-plan-button"]');
      await page.waitForSelector('[data-testid="business-plan-modal"]');

      // Verify business idea dropdown is populated
      const businessIdeaSelect = page.locator('[name="business_idea"]');
      await expect(businessIdeaSelect).toBeVisible();

      // Test that milestones can reference business ideas
      await navigateToMilestones(page);
      await page.click('[data-testid="create-milestone-button"]');
      await page.waitForSelector('[data-testid="milestone-modal"]');

      // Verify business idea field is available
      const milestoneBusinessIdeaInput = page.locator('[name="business_idea"]');
      await expect(milestoneBusinessIdeaInput).toBeVisible();
    });
  });
});
