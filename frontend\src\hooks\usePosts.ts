import { useCallback } from 'react';
import { useQueryClient, useMutation } from '@tanstack/react-query';
import {
  useApiList,
  useApiQuery,
  useApiCreate,
  useApiUpdate,
  useApiDelete,
  useApiInfinite,
  useApiPatch,
  useApiUpdateById,
  useApiDeleteById,
  usePrefetchApiQuery,
  usePrefetchApiList
} from './useApi';
import { Post, Comment } from '../services/api';
import { QueryParams } from '../types/api';
import { ApiClient } from '../services/apiClient';

// Create a shared API client instance
const apiClient = new ApiClient();

// Query keys - using arrays for better type safety and organization
export const PostKeys = {
  all: ['posts'] as const,
  lists: () => [...PostKeys.all, 'list'] as const,
  list: (filters: QueryParams) => [...PostKeys.lists(), filters] as const,
  infiniteList: (filters: QueryParams) => [...PostKeys.lists(), 'infinite', filters] as const,
  details: () => [...PostKeys.all, 'detail'] as const,
  detail: (id: number) => [...PostKeys.details(), id] as const,
};

/**
 * Hook for fetching a list of posts
 */
export function usePostsList(params?: QueryParams) {
  return useApiList<Post>(
    PostKeys.list(params || {}),
    '/posts/',
    params
  );
}

/**
 * Hook for infinite scrolling of posts
 */
export function usePostsInfinite(params?: QueryParams) {
  return useApiInfinite<Post>(
    PostKeys.infiniteList(params || {}),
    '/posts/',
    params
  );
}

/**
 * Hook for fetching a single post
 */
export function usePost(id: number) {
  return useApiQuery<Post>(
    PostKeys.detail(id),
    `/posts/${id}/`
  );
}

/**
 * Hook for prefetching a single post
 */
export function usePrefetchPost(id: number) {
  return usePrefetchApiQuery<Post>(
    PostKeys.detail(id),
    `/posts/${id}/`
  );
}

/**
 * Hook for prefetching a list of posts
 */
export function usePrefetchPostsList(params?: QueryParams) {
  return usePrefetchApiList<Post>(
    PostKeys.list(params || {}),
    '/posts/',
    params
  );
}

/**
 * Hook for creating a post
 */
export function useCreatePost() {
  const queryClient = useQueryClient();

  return useApiCreate<Post, Partial<Post> & { author_id: number }>(
    '/posts/',
    {
      onSuccess: (newPost) => {
        // Invalidate posts list queries to refetch data
        queryClient.invalidateQueries({ queryKey: PostKeys.lists() });

        // Optionally add the new post to the cache
        queryClient.setQueryData(PostKeys.detail(newPost.id), newPost);
      }
    }
  );
}

/**
 * Hook for updating a post
 */
export function useUpdatePost() {
  const queryClient = useQueryClient();

  return useApiUpdateById<Post, Partial<Post>>(
    '/posts/:id/',
    {
      onSuccess: (updatedPost) => {
        // Update the post in the cache
        if (updatedPost.id) {
          queryClient.setQueryData(PostKeys.detail(updatedPost.id), updatedPost);
        }

        // Invalidate posts list queries to refetch data
        queryClient.invalidateQueries({ queryKey: PostKeys.lists() });
      }
    }
  );
}

/**
 * Hook for partially updating a post
 */
export function usePatchPost() {
  const queryClient = useQueryClient();

  return useApiPatchById<Post, Partial<Post>>(
    '/posts/:id/',
    {
      onSuccess: (updatedPost) => {
        // Update the post in the cache
        if (updatedPost.id) {
          // Get the current post data
          const currentData = queryClient.getQueryData<Post>(PostKeys.detail(updatedPost.id));

          // If we have current data, merge it with the updated data
          if (currentData) {
            queryClient.setQueryData(
              PostKeys.detail(updatedPost.id),
              { ...currentData, ...updatedPost }
            );
          } else {
            // Otherwise just set the updated data
            queryClient.setQueryData(PostKeys.detail(updatedPost.id), updatedPost);
          }
        }

        // Invalidate posts list queries to refetch data
        queryClient.invalidateQueries({ queryKey: PostKeys.lists() });
      }
    }
  );
}

/**
 * Hook for deleting a post
 */
export function useDeletePost() {
  const queryClient = useQueryClient();

  return useApiDeleteById<Post>(
    '/posts/:id/',
    {
      onSuccess: (_, id) => {
        // Remove the post from the cache
        queryClient.removeQueries({ queryKey: PostKeys.detail(Number(id)) });

        // Invalidate posts list queries to refetch data
        queryClient.invalidateQueries({ queryKey: PostKeys.lists() });
      }
    }
  );
}

/**
 * Hook for liking a post
 */
export function useLikePost() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: (id: number) => {
      return apiClient.post<{ message: string }>(`/posts/${id}/like/`);
    },
    onSuccess: (_, id) => {
      // Invalidate the specific post and posts list
      queryClient.invalidateQueries({ queryKey: PostKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: PostKeys.lists() });
    }
  });

  return {
    likePost: mutation.mutate,
    isLiking: mutation.isPending,
    error: mutation.error
  };
}

/**
 * Hook for unliking a post
 */
export function useUnlikePost() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: (id: number) => {
      return apiClient.post<{ message: string }>(`/posts/${id}/unlike/`);
    },
    onSuccess: (_, id) => {
      // Invalidate the specific post and posts list
      queryClient.invalidateQueries({ queryKey: PostKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: PostKeys.lists() });
    }
  });

  return {
    unlikePost: mutation.mutate,
    isUnliking: mutation.isPending,
    error: mutation.error
  };
}

/**
 * Hook for commenting on a post
 */
export function useCommentPost() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: ({ postId, content }: { postId: number, content: string }) => {
      return apiClient.post<Comment>(`/posts/${postId}/comments/`, { content });
    },
    onSuccess: (_, { postId }) => {
      // Invalidate the specific post and comments list
      queryClient.invalidateQueries({ queryKey: PostKeys.detail(postId) });
      queryClient.invalidateQueries({ queryKey: ['comments', { post_id: postId }] });
    }
  });

  return {
    commentPost: mutation.mutate,
    isCommenting: mutation.isPending,
    error: mutation.error
  };
}
