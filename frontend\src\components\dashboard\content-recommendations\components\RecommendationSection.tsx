import React from 'react';
import { Link } from 'react-router-dom';
import { useAppSelector } from '../../../../store/hooks';
import { ArrowRight } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../hooks/useLanguage';

interface RecommendationSectionProps {
  title: string;
  icon: React.ReactNode;
  viewAllLink: string;
  viewAllText: string;
  children: React.ReactNode;
}

const RecommendationSection: React.FC<RecommendationSectionProps> = ({ title,
  icon,
  viewAllLink,
  viewAllText,
  children
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { language } = useAppSelector(state => state.language);

  return (
    <div className="glass-light backdrop-blur-sm rounded-lg p-4 border border-glass-border">
      <div className={`flex items-center mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
        {icon}
        <h3 className="text-lg font-semibold text-glass-primary ml-3">{title}</h3>
      </div>

      <div className="space-y-3">
        {children}
      </div>

      <div className="mt-3">
        <Link
          to={viewAllLink}
          className={`text-sm flex items-center text-purple-400 hover:text-purple-300 transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
        >
          {viewAllText} <ArrowRight size={14} className={language === 'ar' ? 'mr-1' : 'ml-1'} />
        </Link>
      </div>
    </div>
  );
};

export default RecommendationSection;
