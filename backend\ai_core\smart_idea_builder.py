"""
Smart Business Idea Builder
AI that works behind the scenes to enhance business idea creation
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional
from django.contrib.auth.models import User

# Use the new centralized AI service instead of deprecated ai_recommendations
from core.ai_service import ai_generate_intelligent_content, ai_is_available, get_ai_service
from incubator.models import BusinessIdea

logger = logging.getLogger(__name__)


class SmartIdeaBuilder:
    """
    AI that automatically enhances business ideas as users create them
    No chat interface - just intelligent background assistance
    """

    def __init__(self):
        self.ai_service = get_ai_service()
    
    async def enhance_business_idea(self, business_idea: BusinessIdea) -> Dict[str, Any]:
        """
        Automatically enhance a business idea with AI-generated content
        This runs in the background as user creates their idea
        """
        try:
            enhancements = {}
            
            # Auto-generate missing sections
            if not business_idea.problem_statement:
                enhancements['problem_statement'] = await self._generate_problem_statement(business_idea)
            
            if not business_idea.solution_description:
                enhancements['solution_description'] = await self._generate_solution_description(business_idea)
            
            if not business_idea.target_audience:
                enhancements['target_audience'] = await self._generate_target_audience(business_idea)
            
            if not business_idea.market_opportunity:
                enhancements['market_opportunity'] = await self._generate_market_opportunity(business_idea)
            
            # Generate intelligent suggestions
            enhancements['ai_suggestions'] = await self._generate_improvement_suggestions(business_idea)
            
            # Auto-populate market data
            enhancements['market_data'] = await self._fetch_market_data(business_idea)
            
            # Suggest similar successful businesses
            enhancements['similar_businesses'] = await self._find_similar_businesses(business_idea)
            
            # Generate initial SWOT analysis
            enhancements['swot_preview'] = await self._generate_swot_preview(business_idea)
            
            return {
                'success': True,
                'enhancements': enhancements,
                'auto_generated_fields': len([k for k in enhancements.keys() if k in ['problem_statement', 'solution_description', 'target_audience', 'market_opportunity']])
            }
            
        except Exception as e:
            logger.error(f"Error enhancing business idea {business_idea.id}: {e}")
            return {'success': False, 'error': str(e)}
    
    async def _generate_problem_statement(self, business_idea: BusinessIdea) -> str:
        """Auto-generate problem statement based on title and description"""
        prompt = f"""
        Based on this business idea: "{business_idea.title}"
        Description: "{business_idea.description}"
        
        Generate a clear, specific problem statement that this business solves.
        Focus on:
        - Who has this problem
        - Why it's important
        - Current pain points
        - Market gap
        
        Keep it concise (2-3 sentences) and specific.
        """
        
        try:
            response = ai_generate_intelligent_content(
                'problem_statement',
                {
                    'business_title': business_idea.title,
                    'business_description': business_idea.description
                },
                'en'
            )
            if response.get('success'):
                return response.get('data', {}).get('content', '').strip()
            return ""
        except Exception as e:
            logger.error(f"Error generating problem statement: {e}")
            return ""
    
    async def _generate_solution_description(self, business_idea: BusinessIdea) -> str:
        """Auto-generate solution description"""
        prompt = f"""
        Business Idea: "{business_idea.title}"
        Problem: "{business_idea.problem_statement or business_idea.description}"
        
        Generate a clear solution description that explains:
        - How this business solves the problem
        - Key features or approach
        - What makes it unique
        - Value proposition
        
        Keep it practical and specific (2-3 sentences).
        """
        
        try:
            response = ai_generate_intelligent_content(
                'solution_description',
                {
                    'business_title': business_idea.title,
                    'problem_statement': business_idea.problem_statement or business_idea.description
                },
                'en'
            )
            if response.get('success'):
                return response.get('data', {}).get('content', '').strip()
            return ""
        except Exception as e:
            logger.error(f"Error generating solution description: {e}")
            return ""
    
    async def _generate_target_audience(self, business_idea: BusinessIdea) -> str:
        """Auto-generate target audience based on business idea"""
        prompt = f"""
        Business: "{business_idea.title}"
        Problem: "{business_idea.problem_statement or 'Not specified'}"
        Solution: "{business_idea.solution_description or business_idea.description}"
        
        Define the target audience for this business:
        - Primary customer segments
        - Demographics and characteristics
        - Specific needs and pain points
        - Market size estimate
        
        Be specific and actionable (2-3 sentences).
        """
        
        try:
            response = ai_generate_intelligent_content(
                'target_audience',
                {
                    'business_title': business_idea.title,
                    'problem_statement': business_idea.problem_statement or 'Not specified',
                    'solution_description': business_idea.solution_description or business_idea.description
                },
                'en'
            )
            if response.get('success'):
                return response.get('data', {}).get('content', '').strip()
            return ""
        except Exception as e:
            logger.error(f"Error generating target audience: {e}")
            return ""
    
    async def _generate_market_opportunity(self, business_idea: BusinessIdea) -> str:
        """Auto-generate market opportunity analysis"""
        prompt = f"""
        Business: "{business_idea.title}"
        Target Audience: "{business_idea.target_audience or 'General market'}"
        
        Describe the market opportunity:
        - Market size and growth potential
        - Current trends supporting this business
        - Competitive landscape overview
        - Revenue potential
        
        Focus on opportunity and potential (2-3 sentences).
        """
        
        try:
            response = ai_generate_intelligent_content(
                'market_opportunity',
                {
                    'business_title': business_idea.title,
                    'target_audience': business_idea.target_audience or 'General market'
                },
                'en'
            )
            if response.get('success'):
                return response.get('data', {}).get('content', '').strip()
            return ""
        except Exception as e:
            logger.error(f"Error generating market opportunity: {e}")
            return ""
    
    async def _generate_improvement_suggestions(self, business_idea: BusinessIdea) -> List[str]:
        """Generate intelligent suggestions for improving the business idea"""
        prompt = f"""
        Analyze this business idea and provide 3-5 specific improvement suggestions:
        
        Title: {business_idea.title}
        Description: {business_idea.description}
        Problem: {business_idea.problem_statement or 'Not defined'}
        Solution: {business_idea.solution_description or 'Not defined'}
        Target Audience: {business_idea.target_audience or 'Not defined'}
        
        Provide actionable suggestions for:
        - Clarifying the value proposition
        - Improving market positioning
        - Strengthening the business model
        - Addressing potential challenges
        
        Format as a simple list of suggestions.
        """
        
        try:
            response = ai_generate_intelligent_content(
                'improvement_suggestions',
                {
                    'business_title': business_idea.title,
                    'business_description': business_idea.description,
                    'problem_statement': business_idea.problem_statement or 'Not defined',
                    'solution_description': business_idea.solution_description or 'Not defined',
                    'target_audience': business_idea.target_audience or 'Not defined'
                },
                'en'
            )

            if response.get('success'):
                suggestions_text = response.get('data', {}).get('content', '')
                # Parse suggestions from text
                suggestions = []
                for line in suggestions_text.split('\n'):
                    line = line.strip()
                    if line and (line.startswith('-') or line.startswith('•') or line.startswith('*')):
                        suggestion = line.lstrip('-•* ').strip()
                        if suggestion:
                            suggestions.append(suggestion)

                return suggestions[:5]  # Return max 5 suggestions
            return []

        except Exception as e:
            logger.error(f"Error generating improvement suggestions: {e}")
            return []
    
    async def _fetch_market_data(self, business_idea: BusinessIdea) -> Dict[str, Any]:
        """Fetch relevant market data (simplified version)"""
        # In a real implementation, this would connect to market data APIs
        # For now, we'll generate AI-based market insights
        
        prompt = f"""
        Provide market data insights for this business:
        Title: {business_idea.title}
        Industry: {getattr(business_idea, 'industry', 'General')}
        
        Provide realistic estimates for:
        - Market size (in billions)
        - Annual growth rate (%)
        - Competition level (Low/Medium/High)
        - Market maturity (Emerging/Growing/Mature)
        
        Format as: Market Size: $X.XB, Growth: X%, Competition: Level, Maturity: Stage
        """
        
        try:
            response = ai_generate_intelligent_content(
                'market_data',
                {
                    'business_title': business_idea.title,
                    'industry': getattr(business_idea, 'industry', 'General')
                },
                'en'
            )

            if response.get('success'):
                market_text = response.get('data', {}).get('content', '')
                # Parse market data (simplified)
                return {
                    'market_size': 'Data available',
                    'growth_rate': 'Estimated',
                    'competition_level': 'Analyzed',
                    'market_maturity': 'Assessed',
                    'raw_data': market_text.strip()
                }
            return {}

        except Exception as e:
            logger.error(f"Error fetching market data: {e}")
            return {}
    
    async def _find_similar_businesses(self, business_idea: BusinessIdea) -> List[Dict[str, str]]:
        """Find similar successful businesses for learning"""
        prompt = f"""
        Find 3-5 similar successful businesses or companies that solve similar problems:
        
        Business: {business_idea.title}
        Problem: {business_idea.problem_statement or business_idea.description}
        
        For each similar business, provide:
        - Company name
        - Brief description (1 sentence)
        - Key success factor
        
        Format as: Company Name: Description | Success Factor: Factor
        """
        
        try:
            response = ai_generate_intelligent_content(
                'similar_businesses',
                {
                    'business_title': business_idea.title,
                    'problem_statement': business_idea.problem_statement or business_idea.description
                },
                'en'
            )

            if response.get('success'):
                similar_text = response.get('data', {}).get('content', '')
                # Parse similar businesses (simplified)
                similar_businesses = []
                for line in similar_text.split('\n'):
                    if ':' in line and '|' in line:
                        parts = line.split('|')
                        if len(parts) >= 2:
                            name_desc = parts[0].strip()
                            success_factor = parts[1].strip()

                            if ':' in name_desc:
                                name, description = name_desc.split(':', 1)
                                similar_businesses.append({
                                    'name': name.strip(),
                                    'description': description.strip(),
                                    'success_factor': success_factor.replace('Success Factor:', '').strip()
                                })

                return similar_businesses[:5]
            return []

        except Exception as e:
            logger.error(f"Error finding similar businesses: {e}")
            return []
    
    async def _generate_swot_preview(self, business_idea: BusinessIdea) -> Dict[str, List[str]]:
        """Generate a quick SWOT analysis preview"""
        prompt = f"""
        Generate a brief SWOT analysis for this business idea:
        
        Business: {business_idea.title}
        Description: {business_idea.description}
        
        Provide 2-3 points for each:
        - Strengths
        - Weaknesses  
        - Opportunities
        - Threats
        
        Keep each point concise (one sentence).
        """
        
        try:
            response = ai_generate_intelligent_content(
                'swot_analysis',
                {
                    'business_title': business_idea.title,
                    'business_description': business_idea.description
                },
                'en'
            )

            if response.get('success'):
                swot_text = response.get('data', {}).get('content', '')
                # Parse SWOT (simplified)
                swot = {
                    'strengths': ['AI-generated strength analysis available'],
                    'weaknesses': ['AI-generated weakness analysis available'],
                    'opportunities': ['AI-generated opportunity analysis available'],
                    'threats': ['AI-generated threat analysis available'],
                    'raw_analysis': swot_text.strip()
                }
                return swot
            return {}

        except Exception as e:
            logger.error(f"Error generating SWOT preview: {e}")
            return {}
    
    def suggest_next_sections(self, business_idea: BusinessIdea) -> List[Dict[str, str]]:
        """Suggest which sections to fill next based on current progress"""
        suggestions = []
        
        if not business_idea.problem_statement:
            suggestions.append({
                'section': 'problem_statement',
                'title': 'Define the Problem',
                'description': 'Clearly articulate the problem your business solves',
                'priority': 'high'
            })
        
        if not business_idea.solution_description:
            suggestions.append({
                'section': 'solution_description',
                'title': 'Describe Your Solution',
                'description': 'Explain how your business solves the problem',
                'priority': 'high'
            })
        
        if not business_idea.target_audience:
            suggestions.append({
                'section': 'target_audience',
                'title': 'Identify Target Audience',
                'description': 'Define who your customers are',
                'priority': 'medium'
            })
        
        if not business_idea.business_model:
            suggestions.append({
                'section': 'business_model',
                'title': 'Business Model',
                'description': 'How will you make money?',
                'priority': 'medium'
            })
        
        return suggestions


# Global instance
smart_idea_builder = SmartIdeaBuilder()
