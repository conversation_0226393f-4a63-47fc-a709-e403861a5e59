import React from 'react';
import { UnifiedDashboard } from '../../components/dashboard/unified';
import { ModeratorDashboardProvider } from '../../providers/dashboard';

/**
 * Moderator Dashboard Page
 * Now uses the unified dashboard architecture with moderator-specific providers
 *
 * This page has been migrated from a custom implementation to use the
 * consolidated UnifiedDashboard component with ModeratorDashboardProvider
 * for role-specific data and functionality.
 */
const ModeratorDashboardPage: React.FC = () => {
  return (
    <ModeratorDashboardProvider>
      <UnifiedDashboard
        role="moderator"
        className="moderator-dashboard"
      />
    </ModeratorDashboardProvider>
  );
};

export default ModeratorDashboardPage;
