import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Eye, EyeOff, User, Shield, AlertTriangle, Search,
  Clock, Activity, LogOut, UserCheck, Filter,
  ChevronDown, ChevronUp, RefreshCw, X
} from 'lucide-react';
import { getAuthToken } from '../../../services/api';
import AuthenticatedLayout from '../../layout/AuthenticatedLayout';

interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  is_active: boolean;
  is_staff: boolean;
  is_superuser: boolean;
  date_joined: string;
  last_login: string | null;
  role: string;
}

interface ImpersonationSession {
  id: string;
  target_user: User;
  started_at: string;
  duration: number;
  actions_performed: number;
  is_active: boolean;
}

const UserImpersonation: React.FC = () => {
  const { t } = useTranslation();
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [currentImpersonation, setCurrentImpersonation] = useState<ImpersonationSession | null>(null);
  const [impersonationHistory, setImpersonationHistory] = useState<ImpersonationSession[]>([]);
  const [loading, setLoading] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState<{
    isOpen: boolean;
    user: User | null;
  }>({ isOpen: false, user: null });

  useEffect(() => {
    fetchUsers();
    fetchImpersonationHistory();
  }, []);

  useEffect(() => {
    filterUsers();
  }, [users, searchTerm, roleFilter, statusFilter]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/superadmin/users/users-management/', {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setUsers(data.users || []);
      } else {
        console.error('Failed to fetch users:', response.status);
        setUsers([]);
      }
    } catch (error) {
      console.error('Failed to fetch users:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchImpersonationHistory = async () => {
    try {
      const response = await fetch('/api/superadmin/users/impersonation-history/', {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setImpersonationHistory(data.history || []);
      } else {
        console.error('Failed to fetch impersonation history:', response.status);
        setImpersonationHistory([]);
      }
    } catch (error) {
      console.error('Failed to fetch impersonation history:', error);
      setImpersonationHistory([]);
    }
  };

  const filterUsers = () => {
    let filtered = users;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(user =>
        user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        `${user.first_name} ${user.last_name}`.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Role filter
    if (roleFilter !== 'all') {
      filtered = filtered.filter(user => user.role === roleFilter);
    }

    // Status filter
    if (statusFilter !== 'all') {
      if (statusFilter === 'active') {
        filtered = filtered.filter(user => user.is_active);
      } else if (statusFilter === 'inactive') {
        filtered = filtered.filter(user => !user.is_active);
      }
    }

    // Exclude super admins from impersonation
    filtered = filtered.filter(user => !user.is_superuser);

    setFilteredUsers(filtered);
  };

  const startImpersonation = async (user: User) => {
    try {
      setLoading(true);
      
      // API call to start impersonation
      const response = await fetch('/api/users/super-admin/impersonate-user/', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ user_id: user.id })
      });

      if (response.ok) {
        const data = await response.json();
        
        // Create impersonation session
        const session: ImpersonationSession = {
          id: Date.now().toString(),
          target_user: user,
          started_at: new Date().toISOString(),
          duration: 0,
          actions_performed: 0,
          is_active: true
        };
        
        setCurrentImpersonation(session);
        setShowConfirmDialog({ isOpen: false, user: null });
        
        // In a real app, you might redirect to the user's dashboard
        console.log(`Started impersonating user: ${user.username}`);
      } else {
        throw new Error('Failed to start impersonation');
      }
    } catch (error) {
      console.error('Impersonation error:', error);
    } finally {
      setLoading(false);
    }
  };

  const stopImpersonation = async () => {
    if (!currentImpersonation) return;

    try {
      setLoading(true);
      
      // API call to stop impersonation
      const response = await fetch('/api/users/super-admin/stop-impersonation/', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        }
      });

      if (response.ok) {
        // Update history
        const updatedSession = {
          ...currentImpersonation,
          is_active: false,
          duration: Math.floor((Date.now() - new Date(currentImpersonation.started_at).getTime()) / 1000)
        };
        
        setImpersonationHistory(prev => [updatedSession, ...prev]);
        setCurrentImpersonation(null);
        
        console.log('Stopped impersonation');
      }
    } catch (error) {
      console.error('Stop impersonation error:', error);
    } finally {
      setLoading(false);
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'text-purple-400';
      case 'moderator': return 'text-blue-400';
      case 'mentor': return 'text-green-400';
      case 'investor': return 'text-yellow-400';
      default: return 'text-gray-400';
    }
  };

  const getRoleBadge = (role: string) => {
    const colors = {
      admin: 'bg-purple-900/20 text-purple-300 border-purple-500/20',
      moderator: 'bg-blue-900/20 text-blue-300 border-blue-500/20',
      mentor: 'bg-green-900/20 text-green-300 border-green-500/20',
      investor: 'bg-yellow-900/20 text-yellow-300 border-yellow-500/20',
      user: 'bg-gray-900/20 text-gray-300 border-gray-500/20'
    };
    
    return (
      <span className={`px-2 py-1 text-xs rounded border ${colors[role as keyof typeof colors]}`}>
        {role.toUpperCase()}
      </span>
    );
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  return (
    <AuthenticatedLayout>
      <div className="text-white">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-2">
            <Eye className="w-8 h-8 text-orange-400" />
            <h1 className="text-3xl font-bold text-white">
              {t('superAdmin.impersonation.title', 'User Impersonation')}
            </h1>
          </div>
          <p className="text-gray-400">
            {t('superAdmin.impersonation.subtitle', 'Impersonate users for support and debugging purposes')}
          </p>
        </div>

      {/* Current Impersonation Alert */}
      {currentImpersonation && (
        <div className="bg-orange-900/20 border border-orange-500 rounded-lg p-4 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <UserCheck className="w-5 h-5 text-orange-400" />
              <div>
                <span className="text-orange-300 font-medium">
                  Currently impersonating: {currentImpersonation.target_user.username}
                </span>
                <div className="text-sm text-orange-400">
                  Started: {new Date(currentImpersonation.started_at).toLocaleString()}
                </div>
              </div>
            </div>
            <button
              onClick={stopImpersonation}
              disabled={loading}
              className="flex items-center gap-2 px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors disabled:opacity-50"
            >
              <LogOut className="w-4 h-4" />
              Stop Impersonation
            </button>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search users..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:border-blue-500 focus:outline-none"
          />
        </div>

        <select
          value={roleFilter}
          onChange={(e) => setRoleFilter(e.target.value)}
          className="px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:border-blue-500 focus:outline-none"
        >
          <option value="all">All Roles</option>
          <option value="user">User</option>
          <option value="mentor">Mentor</option>
          <option value="investor">Investor</option>
          <option value="moderator">Moderator</option>
          <option value="admin">Admin</option>
        </select>

        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:border-blue-500 focus:outline-none"
        >
          <option value="all">All Status</option>
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
        </select>
      </div>

      {/* Users List */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 mb-8">
        <div className="p-4 border-b border-gray-700">
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <User className="w-5 h-5" />
            Available Users ({filteredUsers.length})
          </h2>
        </div>
        
        <div className="divide-y divide-gray-700">
          {filteredUsers.map((user) => (
            <div key={user.id} className="p-4 hover:bg-gray-750 transition-colors">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center">
                    <User className="w-5 h-5 text-gray-400" />
                  </div>
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium">{user.username}</span>
                      {getRoleBadge(user.role)}
                      {!user.is_active && (
                        <span className="px-2 py-1 text-xs bg-red-900/20 text-red-300 border border-red-500/20 rounded">
                          INACTIVE
                        </span>
                      )}
                    </div>
                    <div className="text-sm text-gray-400">
                      {user.first_name} {user.last_name} • {user.email}
                    </div>
                    <div className="text-xs text-gray-500">
                      Joined: {new Date(user.date_joined).toLocaleDateString()}
                      {user.last_login && (
                        <span> • Last login: {new Date(user.last_login).toLocaleDateString()}</span>
                      )}
                    </div>
                  </div>
                </div>
                
                <button
                  onClick={() => setShowConfirmDialog({ isOpen: true, user })}
                  disabled={!user.is_active || currentImpersonation !== null || loading}
                  className="flex items-center gap-2 px-4 py-2 bg-orange-600 hover:bg-orange-700 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Eye className="w-4 h-4" />
                  Impersonate
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Impersonation History */}
      {impersonationHistory.length > 0 && (
        <div className="bg-gray-800 rounded-lg border border-gray-700">
          <div className="p-4 border-b border-gray-700">
            <h2 className="text-lg font-semibold flex items-center gap-2">
              <Clock className="w-5 h-5" />
              Impersonation History
            </h2>
          </div>
          
          <div className="divide-y divide-gray-700">
            {impersonationHistory.map((session) => (
              <div key={session.id} className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium mb-1">
                      {session.target_user.username}
                    </div>
                    <div className="text-sm text-gray-400">
                      {new Date(session.started_at).toLocaleString()}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-gray-300">
                      Duration: {formatDuration(session.duration)}
                    </div>
                    <div className="text-xs text-gray-500">
                      {session.actions_performed} actions
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Confirmation Dialog */}
      {showConfirmDialog.isOpen && showConfirmDialog.user && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4 border border-gray-700">
            <div className="flex items-center gap-3 mb-4">
              <AlertTriangle className="w-6 h-6 text-orange-400" />
              <h3 className="text-lg font-semibold">Confirm Impersonation</h3>
            </div>
            
            <p className="text-gray-300 mb-4">
              Are you sure you want to impersonate <strong>{showConfirmDialog.user.username}</strong>?
            </p>
            
            <div className="bg-yellow-900/20 border border-yellow-500/20 rounded-lg p-3 mb-4">
              <div className="flex items-center gap-2 text-yellow-300 text-sm">
                <Shield className="w-4 h-4" />
                <span>This action will be logged for security audit purposes.</span>
              </div>
            </div>

            <div className="flex gap-3">
              <button
                onClick={() => setShowConfirmDialog({ isOpen: false, user: null })}
                className="flex-1 py-2 px-4 bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => startImpersonation(showConfirmDialog.user!)}
                disabled={loading}
                className="flex-1 py-2 px-4 bg-orange-600 hover:bg-orange-700 rounded-lg transition-colors disabled:opacity-50"
              >
                {loading ? 'Starting...' : 'Start Impersonation'}
              </button>
            </div>
          </div>
        </div>
      )}
      </div>
    </AuthenticatedLayout>
  );
};

export default UserImpersonation;
