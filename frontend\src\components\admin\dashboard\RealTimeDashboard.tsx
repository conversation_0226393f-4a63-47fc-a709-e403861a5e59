import React, { useState, useEffect } from 'react';
import { Activity, Users, DollarSign, TrendingUp, AlertCircle, CheckCircle, Clock, Zap } from 'lucide-react';
// DashboardLayout removed - handled by routing system with AuthenticatedLayout
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';

interface RealTimeMetric {
  id: string;
  label: string;
  value: number;
  change: number;
  trend: 'up' | 'down' | 'stable';
  icon: React.ReactNode;
  color: string;
  unit?: string;
}

interface LiveActivity {
  id: string;
  type: 'user_joined' | 'idea_submitted' | 'funding_applied' | 'mentorship_matched' | 'milestone_completed';
  message: string;
  user: string;
  timestamp: string;
  priority: 'low' | 'medium' | 'high';
}

interface SystemStatus {
  service: string;
  status: 'healthy' | 'warning' | 'error';
  responseTime: number;
  uptime: number;
  lastCheck: string;
}

const RealTimeDashboard: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  // State management
  const [metrics, setMetrics] = useState<RealTimeMetric[]>([]);
  const [activities, setActivities] = useState<LiveActivity[]>([]);
  const [systemStatus, setSystemStatus] = useState<SystemStatus[]>([]);
  const [isConnected, setIsConnected] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  // Initialize real-time data
  useEffect(() => {
    // Mock real-time metrics
    const initialMetrics: RealTimeMetric[] = [
      {
        id: 'active_users',
        label: t('dashboard.active.users', 'Active Users'),
        value: 127,
        change: 8.5,
        trend: 'up',
        icon: <Users size={20} />,
        color: 'text-blue-400'
      },
      {
        id: 'new_applications',
        label: t('dashboard.new.applications', 'New Applications'),
        value: 23,
        change: 12.3,
        trend: 'up',
        icon: <TrendingUp size={20} />,
        color: 'text-green-400'
      },
      {
        id: 'funding_requests',
        label: t('dashboard.funding.requests', 'Funding Requests'),
        value: 8,
        change: -2.1,
        trend: 'down',
        icon: <DollarSign size={20} />,
        color: 'text-yellow-400'
      },
      {
        id: 'system_load',
        label: t('dashboard.system.load', 'System Load'),
        value: 67,
        change: 5.2,
        trend: 'up',
        icon: <Activity size={20} />,
        color: 'text-purple-400',
        unit: '%'
      }
    ];

    const initialActivities: LiveActivity[] = [
      {
        id: '1',
        type: 'user_joined',
        message: 'Sarah Wilson joined the platform',
        user: 'Sarah Wilson',
        timestamp: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
        priority: 'low'
      },
      {
        id: '2',
        type: 'funding_applied',
        message: 'TechStart applied for $50K funding',
        user: 'John Doe',
        timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
        priority: 'high'
      },
      {
        id: '3',
        type: 'milestone_completed',
        message: 'HealthApp completed MVP milestone',
        user: 'Jane Smith',
        timestamp: new Date(Date.now() - 8 * 60 * 1000).toISOString(),
        priority: 'medium'
      }
    ];

    const initialSystemStatus: SystemStatus[] = [
      {
        service: 'API Server',
        status: 'healthy',
        responseTime: 45,
        uptime: 99.9,
        lastCheck: new Date().toISOString()
      },
      {
        service: 'Database',
        status: 'healthy',
        responseTime: 12,
        uptime: 99.8,
        lastCheck: new Date().toISOString()
      },
      {
        service: 'File Storage',
        status: 'warning',
        responseTime: 156,
        uptime: 98.5,
        lastCheck: new Date().toISOString()
      },
      {
        service: 'Email Service',
        status: 'healthy',
        responseTime: 89,
        uptime: 99.7,
        lastCheck: new Date().toISOString()
      }
    ];

    setMetrics(initialMetrics);
    setActivities(initialActivities);
    setSystemStatus(initialSystemStatus);
  }, [t]);

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      // Update metrics with random changes
      setMetrics(prev => prev.map(metric => ({
        ...metric,
        value: Math.max(0, metric.value + (Math.random() - 0.5) * 10),
        change: (Math.random() - 0.5) * 20,
        trend: Math.random() > 0.5 ? 'up' : 'down'
      })));

      // Add new activity occasionally
      if (Math.random() > 0.7) {
        const newActivity: LiveActivity = {
          id: Date.now().toString(),
          type: 'user_joined',
          message: `User ${Math.floor(Math.random() * 1000)} performed an action`,
          user: `User ${Math.floor(Math.random() * 1000)}`,
          timestamp: new Date().toISOString(),
          priority: Math.random() > 0.7 ? 'high' : Math.random() > 0.4 ? 'medium' : 'low'
        };

        setActivities(prev => [newActivity, ...prev.slice(0, 9)]);
      }

      setLastUpdate(new Date());
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  // Get activity icon
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'user_joined':
        return <Users size={16} className="text-blue-400" />;
      case 'idea_submitted':
        return <TrendingUp size={16} className="text-green-400" />;
      case 'funding_applied':
        return <DollarSign size={16} className="text-yellow-400" />;
      case 'mentorship_matched':
        return <Users size={16} className="text-purple-400" />;
      case 'milestone_completed':
        return <CheckCircle size={16} className="text-green-400" />;
      default:
        return <Activity size={16} className="text-gray-400" />;
    }
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle size={16} className="text-green-400" />;
      case 'warning':
        return <AlertCircle size={16} className="text-yellow-400" />;
      case 'error':
        return <AlertCircle size={16} className="text-red-400" />;
      default:
        return <Clock size={16} className="text-gray-400" />;
    }
  };

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'border-l-red-500';
      case 'medium':
        return 'border-l-yellow-500';
      case 'low':
        return 'border-l-green-500';
      default:
        return 'border-l-gray-500';
    }
  };

  // Format relative time
  const getRelativeTime = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffMs = now.getTime() - time.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 1) return t('dashboard.just.now', 'Just now');
    if (diffMins < 60) return t('dashboard.minutes.ago', '{{count}}m ago', { count: diffMins });
    return time.toLocaleTimeString();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900">
      <div className="px-4 sm:px-6 lg:px-8 py-6">
        <div className="max-w-7xl mx-auto">
      {/* Header */}
      <div className={`mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
        <div>
          <h1 className="text-2xl font-bold text-white">{t('dashboard.realtime.title', 'Real-Time Dashboard')}</h1>
          <div className="text-gray-400 mt-1">{t('dashboard.realtime.description', 'Live platform monitoring and metrics')}</div>
        </div>
        <div className={`mt-4 sm:mt-0 flex items-center space-x-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
          <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'} animate-pulse`}></div>
            <span className="text-sm text-gray-400">
              {isConnected ? t('dashboard.connected', 'Connected') : t('dashboard.disconnected', 'Disconnected')}
            </span>
          </div>
          <span className="text-xs text-gray-500">
            {t('dashboard.last.update', 'Last update')}: {lastUpdate.toLocaleTimeString()}
          </span>
        </div>
      </div>

      {/* Real-Time Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {metrics.map((metric) => (
          <div key={metric.id} className="bg-indigo-900/30 backdrop-blur-sm rounded-xl p-6 border border-indigo-800/50 hover:border-purple-500/50 transition-all duration-300">
            <div className={`flex items-center justify-between mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
              <div className={metric.color}>
                {metric.icon}
              </div>
              <div className={`flex items-center space-x-1 text-sm ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                <TrendingUp size={14} className={metric.trend === 'up' ? 'text-green-400' : metric.trend === 'down' ? 'text-red-400' : 'text-gray-400'} />
                <span className={metric.trend === 'up' ? 'text-green-400' : metric.trend === 'down' ? 'text-red-400' : 'text-gray-400'}>
                  {metric.change > 0 ? '+' : ''}{metric.change.toFixed(1)}%
                </span>
              </div>
            </div>
            <div>
              <h3 className="text-2xl font-bold text-white mb-1">
                {Math.floor(metric.value)}{metric.unit || ''}
              </h3>
              <p className="text-gray-400 text-sm">{metric.label}</p>
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Live Activities */}
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-xl p-6 border border-indigo-800/50">
          <div className={`flex items-center justify-between mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
            <h3 className="text-lg font-semibold text-white">{t('dashboard.live.activities', 'Live Activities')}</h3>
            <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
              <Zap size={16} className="text-yellow-400" />
              <span className="text-sm text-gray-400">{t('dashboard.live', 'Live')}</span>
            </div>
          </div>
          
          <div className="space-y-3 max-h-80 overflow-y-auto">
            {activities.map((activity) => (
              <div key={activity.id} className={`p-3 bg-indigo-800/30 rounded-lg border-l-4 ${getPriorityColor(activity.priority)} hover:bg-indigo-700/30 transition-colors`}>
                <div className={`flex items-start space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                  <div className="flex-shrink-0 mt-1">
                    {getActivityIcon(activity.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-white text-sm font-medium">{activity.message}</p>
                    <div className={`flex items-center space-x-2 mt-1 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                      <span className="text-gray-400 text-xs">{activity.user}</span>
                      <span className="text-gray-500 text-xs">•</span>
                      <span className="text-gray-400 text-xs">{getRelativeTime(activity.timestamp)}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* System Status */}
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-xl p-6 border border-indigo-800/50">
          <h3 className="text-lg font-semibold text-white mb-6">{t('dashboard.system.status', 'System Status')}</h3>
          
          <div className="space-y-4">
            {systemStatus.map((service, index) => (
              <div key={index} className="p-4 bg-indigo-800/30 rounded-lg">
                <div className={`flex items-center justify-between mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                    {getStatusIcon(service.status)}
                    <span className="text-white font-medium">{service.service}</span>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    service.status === 'healthy' ? 'bg-green-500/20 text-green-300' :
                    service.status === 'warning' ? 'bg-yellow-500/20 text-yellow-300' :
                    'bg-red-500/20 text-red-300'
                  }`}>
                    {service.status}
                  </span>
                </div>
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-400">{t('dashboard.response.time', 'Response Time')}: </span>
                    <span className="text-white">{service.responseTime}ms</span>
                  </div>
                  <div>
                    <span className="text-gray-400">{t('dashboard.uptime', 'Uptime')}: </span>
                    <span className="text-white">{service.uptime}%</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
        </div>
      </div>
    </div>
  );
};

export default RealTimeDashboard;
