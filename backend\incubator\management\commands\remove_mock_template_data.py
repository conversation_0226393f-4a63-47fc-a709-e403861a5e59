"""
Django management command to remove mock data from templates
"""

from django.core.management.base import BaseCommand
from incubator.models_business_plan import BusinessPlanTemplate


class Command(BaseCommand):
    help = 'Remove mock usage counts and ratings from templates'

    def handle(self, *args, **options):
        self.stdout.write('Removing mock data from templates...')
        
        templates = BusinessPlanTemplate.objects.all()
        updated_count = 0
        
        for template in templates:
            if template.customization_options and isinstance(template.customization_options, dict):
                # Reset usage count and rating to zero (real data will populate these)
                if 'usage_count' in template.customization_options:
                    template.customization_options['usage_count'] = 0
                if 'average_rating' in template.customization_options:
                    template.customization_options['average_rating'] = 0.0
                
                template.save()
                updated_count += 1
                self.stdout.write(f'Updated template: {template.name}')
        
        self.stdout.write(
            self.style.SUCCESS(f'Successfully updated {updated_count} templates to remove mock data')
        )
