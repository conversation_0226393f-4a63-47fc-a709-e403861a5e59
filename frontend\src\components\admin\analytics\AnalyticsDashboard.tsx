import React, { useState, useEffect } from 'react';
import { BarChart3, TrendingUp, Users, DollarSign, Target, Calendar, Award, Building } from 'lucide-react';
// DashboardLayout removed - handled by routing system with AuthenticatedLayout
import { analyticsAPI } from '../../../services/analyticsApi';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';

interface AnalyticsData {
  overview: {
    total_users: number;
    total_business_ideas: number;
    total_funding_raised: number;
    active_mentorships: number;
    completed_milestones: number;
    success_rate: number;
  };
  growth: {
    user_growth: number;
    idea_growth: number;
    funding_growth: number;
    mentorship_growth: number;
  };
  categories: {
    name: string;
    count: number;
    percentage: number;
  }[];
  recent_activities: {
    type: string;
    description: string;
    timestamp: string;
    user: string;
  }[];
}

const AnalyticsDashboard: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  // State management
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('30d');

  // Fetch analytics data
  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      const data = await analyticsAPI.getAnalytics(timeRange);
      setAnalytics(data);
    } catch (error) {
      console.error('Error fetching analytics:', error);
      // Mock data for demonstration
      setAnalytics({
        overview: {
          total_users: 1247,
          total_business_ideas: 342,
          total_funding_raised: 2450000,
          active_mentorships: 89,
          completed_milestones: 156,
          success_rate: 73.5
        },
        growth: {
          user_growth: 12.5,
          idea_growth: 8.3,
          funding_growth: 23.7,
          mentorship_growth: 15.2
        },
        categories: [
          { name: 'Technology', count: 89, percentage: 26 },
          { name: 'Healthcare', count: 67, percentage: 20 },
          { name: 'Finance', count: 54, percentage: 16 },
          { name: 'Education', count: 43, percentage: 13 },
          { name: 'E-commerce', count: 38, percentage: 11 },
          { name: 'Other', count: 51, percentage: 14 }
        ],
        recent_activities: [
          { type: 'funding', description: 'TechStart received $50K funding', timestamp: '2024-01-15T10:30:00Z', user: 'John Doe' },
          { type: 'milestone', description: 'HealthApp completed MVP milestone', timestamp: '2024-01-15T09:15:00Z', user: 'Jane Smith' },
          { type: 'mentorship', description: 'New mentorship match created', timestamp: '2024-01-15T08:45:00Z', user: 'Mike Johnson' },
          { type: 'idea', description: 'New business idea submitted', timestamp: '2024-01-15T08:20:00Z', user: 'Sarah Wilson' }
        ]
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalytics();
  }, [timeRange]);

  // Get activity icon
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'funding':
        return <DollarSign size={16} className="text-green-400" />;
      case 'milestone':
        return <Target size={16} className="text-blue-400" />;
      case 'mentorship':
        return <Users size={16} className="text-purple-400" />;
      case 'idea':
        return <Building size={16} className="text-orange-400" />;
      default:
        return <BarChart3 size={16} className="text-gray-400" />;
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    if (amount >= 1000000) {
      return `$${(amount / 1000000).toFixed(1)}M`;
    } else if (amount >= 1000) {
      return `$${(amount / 1000).toFixed(0)}K`;
    }
    return `$${amount.toLocaleString()}`;
  };

  // Get growth color
  const getGrowthColor = (growth: number) => {
    return growth >= 0 ? 'text-green-400' : 'text-red-400';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900">
        <div className="px-4 sm:px-6 lg:px-8 py-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex justify-center py-12">
              <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900">
        <div className="px-4 sm:px-6 lg:px-8 py-6">
          <div className="max-w-7xl mx-auto">
            <div className="text-center py-12">
              <BarChart3 size={48} className="mx-auto text-gray-600 mb-4" />
              <h3 className="text-lg font-medium text-gray-400 mb-2">
                {t('analytics.no.data', 'No analytics data available')}
              </h3>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900">
      <div className="px-4 sm:px-6 lg:px-8 py-6">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className={`mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
        <div>
          <h1 className="text-2xl font-bold text-white">{t('admin.analytics.dashboard', 'Analytics Dashboard')}</h1>
          <div className="text-gray-400 mt-1">{t('admin.analytics.description', 'Comprehensive insights and metrics')}</div>
        </div>
        <div className="mt-4 sm:mt-0">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="bg-white/20 border border-white/30 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <option value="7d">{t('analytics.last.7.days', 'Last 7 days')}</option>
            <option value="30d">{t('analytics.last.30.days', 'Last 30 days')}</option>
            <option value="90d">{t('analytics.last.90.days', 'Last 90 days')}</option>
            <option value="1y">{t('analytics.last.year', 'Last year')}</option>
          </select>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8">
        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
          <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
            <div>
              <p className="text-gray-300 text-sm">{t('analytics.total.users', 'Total Users')}</p>
              <p className="text-2xl font-bold text-white">{analytics.overview.total_users.toLocaleString()}</p>
              <p className={`text-sm ${getGrowthColor(analytics.growth.user_growth)}`}>
                +{analytics.growth.user_growth}%
              </p>
            </div>
            <Users size={24} className="text-blue-400" />
          </div>
        </div>

        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
          <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
            <div>
              <p className="text-gray-300 text-sm">{t('analytics.business.ideas', 'Business Ideas')}</p>
              <p className="text-2xl font-bold text-white">{analytics.overview.total_business_ideas.toLocaleString()}</p>
              <p className={`text-sm ${getGrowthColor(analytics.growth.idea_growth)}`}>
                +{analytics.growth.idea_growth}%
              </p>
            </div>
            <Building size={24} className="text-orange-400" />
          </div>
        </div>

        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
          <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
            <div>
              <p className="text-gray-300 text-sm">{t('analytics.funding.raised', 'Funding Raised')}</p>
              <p className="text-2xl font-bold text-white">{formatCurrency(analytics.overview.total_funding_raised)}</p>
              <p className={`text-sm ${getGrowthColor(analytics.growth.funding_growth)}`}>
                +{analytics.growth.funding_growth}%
              </p>
            </div>
            <DollarSign size={24} className="text-green-400" />
          </div>
        </div>

        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
          <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
            <div>
              <p className="text-gray-300 text-sm">{t('analytics.active.mentorships', 'Active Mentorships')}</p>
              <p className="text-2xl font-bold text-white">{analytics.overview.active_mentorships.toLocaleString()}</p>
              <p className={`text-sm ${getGrowthColor(analytics.growth.mentorship_growth)}`}>
                +{analytics.growth.mentorship_growth}%
              </p>
            </div>
            <Users size={24} className="text-purple-400" />
          </div>
        </div>

        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
          <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
            <div>
              <p className="text-gray-300 text-sm">{t('analytics.completed.milestones', 'Completed Milestones')}</p>
              <p className="text-2xl font-bold text-white">{analytics.overview.completed_milestones.toLocaleString()}</p>
            </div>
            <Target size={24} className="text-cyan-400" />
          </div>
        </div>

        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
          <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
            <div>
              <p className="text-gray-300 text-sm">{t('analytics.success.rate', 'Success Rate')}</p>
              <p className="text-2xl font-bold text-white">{analytics.overview.success_rate}%</p>
            </div>
            <Award size={24} className="text-yellow-400" />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Categories Distribution */}
        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
          <h3 className="text-lg font-semibold text-white mb-6">{t('analytics.categories.distribution', 'Categories Distribution')}</h3>
          <div className="space-y-4">
            {analytics.categories.map((category, index) => (
              <div key={index} className="space-y-2">
                <div className={`flex justify-between items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                  <span className="text-gray-300 text-sm">{category.name}</span>
                  <span className="text-white font-medium">{category.count} ({category.percentage}%)</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div
                    className="h-2 rounded-full bg-gradient-to-r from-purple-600 to-blue-600 transition-all duration-300"
                    style={{ width: `${category.percentage}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Recent Activities */}
        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
          <h3 className="text-lg font-semibold text-white mb-6">{t('analytics.recent.activities', 'Recent Activities')}</h3>
          <div className="space-y-4">
            {analytics.recent_activities.map((activity, index) => (
              <div key={index} className={`flex items-start space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                <div className="flex-shrink-0 w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                  {getActivityIcon(activity.type)}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-white text-sm font-medium">{activity.description}</p>
                  <div className={`flex items-center space-x-2 mt-1 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                    <span className="text-gray-300 text-xs">{activity.user}</span>
                    <span className="text-gray-400 text-xs">•</span>
                    <span className="text-gray-300 text-xs">
                      {new Date(activity.timestamp).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsDashboard;
