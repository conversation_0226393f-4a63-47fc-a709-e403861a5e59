import { useLanguage } from "../../hooks/useLanguage";
/**
 * Business Intelligence Widget
 * A compact widget that can be embedded in business idea pages
 */

import React, { useState, useEffect } from 'react';
import {
  BarChart3,
  TrendingUp,
  DollarSign,
  AlertTriangle,
  Lightbulb,
  Eye,
  RefreshCw,
  ChevronRight,
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import {
  businessIntelligenceApi,
  BusinessAnalysis,
  getViabilityColor,
  getFundingReadinessColor,
  formatAnalysisDate,
} from '../../services/businessIntelligenceApi';

interface BusinessIntelligenceWidgetProps {
  businessIdeaId: number;
  businessIdeaTitle?: string;
  language?: string;
  onViewFullAnalysis?: () => void;
  className?: string;
}

export const BusinessIntelligenceWidget: React.FC<BusinessIntelligenceWidgetProps> = ({ businessIdeaId,
  businessIdeaTitle,
  language = 'en',
  onViewFullAnalysis,
  className = '',
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [analysis, setAnalysis] = useState<BusinessAnalysis | null>(null);
  const [loading, setLoading] = useState(false);
  const [analyzing, setAnalyzing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadAnalysis();
  }, [businessIdeaId]);

  const loadAnalysis = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await businessIntelligenceApi.getBusinessAnalysis(businessIdeaId);
      if (response.has_analysis && response.analysis) {
        setAnalysis(response.analysis);
      }
    } catch (err) {
      console.error('Error loading analysis:', err);
      setError(t("ai.failed.to.load", "Failed to load analysis"));
    } finally {
      setLoading(false);
    }
  };

  const runQuickAnalysis = async () => {
    setAnalyzing(true);
    setError(null);
    
    try {
      const response = await businessIntelligenceApi.analyzeBusinessIdea(businessIdeaId, {
        language,
        analysis_type: 'comprehensive',
      });
      
      setAnalysis(response.analysis);
    } catch (err) {
      console.error('Error running analysis:', err);
      setError(t("ai.failed.to.run", "Failed to run analysis"));
    } finally {
      setAnalyzing(false);
    }
  };

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
        <div className={`flex items-center justify-center h-32 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="text-center">
            <RefreshCw className="w-6 h-6 animate-spin mx-auto mb-2 text-blue-500" />
            <p className="text-sm text-gray-600">
              {language === 'ar' ? 'جاري التحميل...' : t("common.loading", t("common.loading", "Loading..."))}
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-lg ${className}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <BarChart3 className={`w-5 h-5 text-blue-500 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
            <h3 className="text-lg font-semibold text-gray-900">
              {language === 'ar' ? 'ذكاء الأعمال' : t("common.business.intelligence", "Business Intelligence")}
            </h3>
          </div>
          
          {analysis && (
            <div className="text-xs text-gray-500">
              {formatAnalysisDate(analysis.updated_at)}
            </div>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
            <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <AlertTriangle className={`w-4 h-4 text-red-500 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        )}

        {!analysis && !analyzing && (
          <div className="text-center py-8">
            <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-3" />
            <h4 className="text-sm font-medium text-gray-900 mb-2">
              {language === 'ar' ? 'لا يوجد تحليل متاح' : t("common.no.analysis.available", "No Analysis Available")}
            </h4>
            <p className="text-xs text-gray-600 mb-4">
              {language === 'ar' 
                ? 'احصل على رؤى مدعومة بالذكاء الاصطناعي'
                : t("common.get.aipowered.insights", "Get AI-powered insights for your business idea")
              }
            </p>
            <button
              onClick={runQuickAnalysis}
              className="px-4 py-2 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600"
            >
              {language === 'ar' ? 'بدء التحليل' : t("common.start.analysis", "Start Analysis")}
            </button>
          </div>
        )}

        {analyzing && (
          <div className="text-center py-8">
            <RefreshCw className="w-12 h-12 animate-spin text-blue-500 mx-auto mb-3" />
            <h4 className="text-sm font-medium text-gray-900 mb-2">
              {language === 'ar' ? 'جاري التحليل...' : t("common.analyzing", t("common.analyzing", "Analyzing..."))}
            </h4>
            <p className="text-xs text-gray-600">
              {language === 'ar' 
                ? 'يرجى الانتظار...'
                : t("common.please.wait", "Please wait...")
              }
            </p>
          </div>
        )}

        {analysis && (
          <div className="space-y-4">
            {/* Viability Score */}
            <div className={`flex items-center justify-between p-4 bg-gray-50 rounded-lg ${isRTL ? "flex-row-reverse" : ""}`}>
              <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <TrendingUp className={`w-4 h-4 text-blue-500 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    {language === 'ar' ? 'نقاط الجدوى' : t("common.viability.score", "Viability Score")}
                  </p>
                  <p className="text-xs text-gray-600">{analysis.viability_level}</p>
                </div>
              </div>
              <div className={`text-lg font-bold ${getViabilityColor(analysis.viability_score)}`}>
                {analysis.viability_score?.toFixed(1) || 'N/A'}/10
              </div>
            </div>

            {/* Funding Readiness */}
            <div className={`flex items-center justify-between p-4 bg-gray-50 rounded-lg ${isRTL ? "flex-row-reverse" : ""}`}>
              <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <DollarSign className={`w-4 h-4 text-green-500 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    {language === 'ar' ? 'جاهزية التمويل' : t("common.funding.readiness", "Funding Readiness")}
                  </p>
                  <p className="text-xs text-gray-600">{analysis.funding_readiness_level}</p>
                </div>
              </div>
              <div className={`text-lg font-bold ${getFundingReadinessColor(analysis.funding_readiness_level)}`}>
                {analysis.funding_readiness_score?.toFixed(1) || 'N/A'}/10
              </div>
            </div>

            {/* Quick Insights */}
            <div>
              <h4 className={`text-sm font-medium text-gray-900 mb-2 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <Lightbulb className={`w-4 h-4 text-yellow-500 mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                {language === 'ar' ? 'رؤى سريعة' : t("common.quick.insights", "Quick Insights")}
              </h4>
              <div className="space-y-2">
                {analysis.insights?.slice(0, 3).map((insight) => (
                  <div
                    key={insight.id}
                    className={`flex items-start p-3 bg-blue-50 rounded-md ${isRTL ? "flex-row-reverse" : ""}`}
                  >
                    <div className={`flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <p className="text-xs font-medium text-blue-900 mb-1">
                        {insight.title}
                      </p>
                      <p className="text-xs text-blue-700 line-clamp-2">
                        {insight.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Top Recommendations */}
            {analysis.recommendations_by_priority && analysis.recommendations_by_priority.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-2">
                  {language === 'ar' ? 'أهم التوصيات' : t("common.top.recommendations", "Top Recommendations")}
                </h4>
                <div className="space-y-2">
                  {analysis.recommendations_by_priority.slice(0, 2).map((recommendation, index) => (
                    <div
                      key={index}
                      className={`flex items-start p-3 bg-green-50 rounded-md ${isRTL ? "flex-row-reverse" : ""}`}
                    >
                      <div className={`flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <p className="text-xs font-medium text-green-900 mb-1">
                          {recommendation.title}
                        </p>
                        <p className="text-xs text-green-700 line-clamp-2">
                          {recommendation.description}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Footer */}
      {analysis && (
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
            <button
              onClick={runQuickAnalysis}
              disabled={analyzing}
              className={`flex items-center text-sm text-blue-600 hover:text-blue-700 disabled:opacity-50 ${isRTL ? "flex-row-reverse" : ""}`}
            >
              <RefreshCw className={`w-4 h-4 mr-1 ${analyzing ? 'animate-spin' : ''}`} />
              {language === 'ar' ? 'تحديث التحليل' : t("common.refresh.analysis", "Refresh Analysis")}
            </button>
            
            {onViewFullAnalysis && (
              <button
                onClick={onViewFullAnalysis}
                className={`flex items-center text-sm text-blue-600 hover:text-blue-700 ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <Eye className={`w-4 h-4 mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                {language === 'ar' ? 'عرض التحليل الكامل' : t("common.view.full.analysis", "View Full Analysis")}
                <ChevronRight className={`w-4 h-4 ml-1 ${isRTL ? "space-x-reverse" : ""}`} />
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default BusinessIntelligenceWidget;
