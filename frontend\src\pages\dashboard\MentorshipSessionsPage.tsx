import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import {
  Calendar,
  Clock,
  Video,
  Users,
  MessageSquare,
  Star,
  ArrowRight,
  Plus,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Search,
  Filter,
  X
} from 'lucide-react';
import { useAppDispatch, useAppSelector } from '../../store/hooks';
import AuthenticatedLayout from '../../components/layout/AuthenticatedLayout';
import {
  fetchMentorshipSessions,
  fetchUpcomingSessions,
  fetchPastSessions
} from '../../store/incubatorSlice';
import { MentorshipSession } from '../../services/incubatorApi';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';

const MentorshipSessionsPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const { mentorshipSessions, isLoading, error } = useAppSelector(state => state.incubator);

  const [activeTab, setActiveTab] = useState<'upcoming' | 'past'>(
    searchParams.get('tab') === 'past' ? 'past' : 'upcoming'
  );
  const [sessions, setSessions] = useState<MentorshipSession[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string | null>(null);

  useEffect(() => {
    // Update URL when tab changes
    setSearchParams({ tab: activeTab });

    // Fetch sessions based on active tab
    if (activeTab === 'upcoming') {
      dispatch(fetchUpcomingSessions());
    } else {
      dispatch(fetchPastSessions());
    }
  }, [dispatch, activeTab, setSearchParams]);

  useEffect(() => {
    // Filter sessions based on active tab
    if (mentorshipSessions.length > 0) {
      const now = new Date();

      if (activeTab === 'upcoming') {
        const upcoming = mentorshipSessions.filter(session =>
          new Date(session.scheduled_at) > now &&
          ['scheduled', 'rescheduled'].includes(session.status)
        ).sort((a, b) => new Date(a.scheduled_at).getTime() - new Date(b.scheduled_at).getTime());

        setSessions(upcoming);
      } else {
        const past = mentorshipSessions.filter(session =>
          new Date(session.scheduled_at) < now ||
          ['completed', 'cancelled'].includes(session.status)
        ).sort((a, b) => new Date(b.scheduled_at).getTime() - new Date(a.scheduled_at).getTime());

        setSessions(past);
      }
    }
  }, [mentorshipSessions, activeTab]);

  const refreshData = () => {
    if (activeTab === 'upcoming') {
      dispatch(fetchUpcomingSessions());
    } else {
      dispatch(fetchPastSessions());
    }
  };

  const formatSessionTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString(undefined, {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getSessionStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'bg-blue-900/50 text-blue-200';
      case 'completed':
        return 'bg-green-900/50 text-green-200';
      case 'cancelled':
        return 'bg-red-900/50 text-red-200';
      case 'rescheduled':
        return 'bg-yellow-900/50 text-yellow-200';
      case 'in_progress':
        return 'bg-purple-900/50 text-purple-200';
      default:
        return 'bg-gray-900/50 text-gray-200';
    }
  };

  const getSessionTypeIcon = (type: string) => {
    switch (type) {
      case 'video':
        return <Video size={16} className="text-blue-400" />;
      case 'phone':
        return <Clock size={16} className="text-green-400" />;
      case 'in_person':
        return <Users size={16} className="text-yellow-400" />;
      case 'chat':
        return <MessageSquare size={16} className="text-purple-400" />;
      default:
        return <Calendar size={16} className="text-gray-400" />;
    }
  };

  // Filter sessions based on search term and status filter
  const filteredSessions = sessions.filter(session => {
    const matchesSearch = searchTerm === '' ||
      session.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (session.description && session.description.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesStatus = statusFilter === null || session.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  return (
    <AuthenticatedLayout>
      <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="p-6">
        {/* Header */}
        <div className={`flex justify-between items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div>
            <h1 className="text-2xl font-bold text-white">{t("dashboard.mentorship.sessions", "Mentorship Sessions")}</h1>
            <div className="text-gray-400 mt-1">
              View and manage your mentorship sessions
            </div>
          </div>
          <div className={`flex space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
            <button
              onClick={refreshData}
              className="p-2 bg-indigo-900/50 hover:bg-indigo-800/50 rounded-md"
              title={t("dashboard.refresh.data", "Refresh data")}
            >
              <RefreshCw size={18} />
            </button>
            <Link
              to="/dashboard/mentorship/sessions/schedule"
              className={`px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
            >
              <Plus size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
              Schedule Session
            </Link>
          </div>
        </div>

        {/* Tabs */}
        <div className="mb-6 border-b border-indigo-800/50">
          <div className={`flex space-x-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            <button
              onClick={() => setActiveTab('upcoming')}
              className={`px-4 py-2 flex items-center ${
                activeTab === 'upcoming'
                  ? 'text-purple-400 border-b-2 border-purple-400 font-medium'
                  : 'text-gray-400 hover:text-gray-300'}
              }`}
            >
              <Calendar size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} /> Upcoming Sessions
            </button>
            <button
              onClick={() => setActiveTab('past')}
              className={`px-4 py-2 flex items-center ${
                activeTab === 'past'
                  ? 'text-purple-400 border-b-2 border-purple-400 font-medium'
                  : 'text-gray-400 hover:text-gray-300'}
              }`}
            >
              <Clock size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} /> Past Sessions
            </button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className={`mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`relative flex-grow max-w-md ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className={`absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none ${isRTL ? "flex-row-reverse" : ""}`}>
              <Search size={16} className="text-gray-400" />
            </div>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder={t("dashboard.search.sessions", "Search sessions...")}
              className="w-full pl-10 pr-4 py-2 bg-indigo-950 border border-indigo-800 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
            {searchTerm && (
              <button
                onClick={() => setSearchTerm('')}
                className={`absolute inset-y-0 right-0 flex items-center pr-3 ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <X size={16} className="text-gray-400 hover:text-gray-300" />
              </button>
            )}
          </div>

          <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <Filter size={16} className={`mr-2 text-gray-400 ${isRTL ? "space-x-reverse" : ""}`} />
              <span className={`text-gray-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`}>{t("dashboard.status", "Status:")}</span>
            </div>
            <select
              value={statusFilter || ''}
              onChange={(e) => setStatusFilter(e.target.value || null)}
              className="bg-indigo-950 border border-indigo-800 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              <option value="">{t("dashboard.all", "All")}</option>
              <option value="scheduled">{t("dashboard.scheduled", "Scheduled")}</option>
              <option value="rescheduled">{t("dashboard.rescheduled", "Rescheduled")}</option>
              <option value="completed">{t("dashboard.completed", "Completed")}</option>
              <option value="cancelled">{t("dashboard.cancelled", "Cancelled")}</option>
              <option value="in_progress">{t("dashboard.in.progress", "In Progress")}</option>
            </select>
          </div>
        </div>

        {isLoading && (
          <div className={`flex justify-center py-12 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
          </div>
        )}

        {error && (
          <div className={`bg-red-900/30 text-red-200 p-4 rounded-lg mb-6 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <AlertCircle size={20} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
            <span>Error loading sessions: {error}</span>
          </div>
        )}

        {!isLoading && !error && (
          <>
            {filteredSessions.length === 0 ? (
              <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50 text-center">
                <Calendar size={40} className="mx-auto mb-3 text-gray-500" />
                <h3 className="text-lg font-medium mb-2">{t("dashboard.no.sessions.found", "No Sessions Found")}</h3>
                <div className="text-gray-400 mb-4">
                  {activeTab === 'upcoming'
                    ? t("dashboard.you.dont.have", "You don't have any upcoming mentorship sessions scheduled.")
                    : "You don't have any past mentorship sessions."}
                </div>
                {activeTab === 'upcoming' && (
                  <Link
                    to="/dashboard/mentorship/sessions/schedule"
                    className={`px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white inline-flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                  >
                    <Calendar size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                    Schedule a Session
                  </Link>
                )}
              </div>
            ) : (
              <div className="grid grid-cols-1 gap-4">
                {filteredSessions.map(session => (
                  <div
                    key={session.id}
                    className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50"
                  >
                    <div className={`flex flex-col md:flex-row md:items-center md:justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
                      <div className="mb-3 md:mb-0">
                        <div className={`flex items-center mb-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                          <h3 className={`font-medium mr-3 ${isRTL ? "space-x-reverse" : ""}`}>{session.title}</h3>
                          <span className={`px-2 py-0.5 rounded-full text-xs ${getSessionStatusColor(session.status)}`}>
                            {session.status_display}
                          </span>
                        </div>

                        <div className="text-gray-400 text-sm">
                          <div className={`flex items-center mb-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                            <Calendar size={14} className={`mr-1 text-gray-500 ${isRTL ? "space-x-reverse" : ""}`} />
                            {formatSessionTime(session.scheduled_at)}
                          </div>
                          <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                            <Clock size={14} className={`mr-1 text-gray-500 ${isRTL ? "space-x-reverse" : ""}`} />
                            {session.duration_minutes} minutes
                          </div>
                        </div>
                      </div>

                      <div className={`flex flex-col md:items-end ${isRTL ? "flex-row-reverse" : ""}`}>
                        <div className={`flex items-center mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                          {getSessionTypeIcon(session.session_type)}
                          <span className={`text-sm ml-1 ${isRTL ? "space-x-reverse" : ""}`}>{session.session_type_display}</span>
                        </div>

                        <div className={`flex space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                          {activeTab === 'past' && session.status === 'completed' && !session.feedback?.length && (
                            <Link
                              to={`/dashboard/mentorship/sessions/${session.id}/feedback`}
                              className={`text-green-400 hover:text-green-300 text-sm flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                            >
                              <MessageSquare size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                              Provide Feedback
                            </Link>
                          )}

                          {activeTab === 'upcoming' && session.status !== 'cancelled' && (
                            <Link
                              to={`/dashboard/mentorship/sessions/${session.id}/join`}
                              className={`text-blue-400 hover:text-blue-300 text-sm flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                            >
                              <Video size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                              Join Session
                            </Link>
                          )}

                          <Link
                            to={`/dashboard/mentorship/sessions/${session.id}`}
                            className={`text-purple-400 hover:text-purple-300 text-sm flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                          >
                            View Details <ArrowRight size={14} className={`ml-1 ${isRTL ? "space-x-reverse" : ""}`} />
                          </Link>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </>
        )}
      </div>
              </div>
        </div>
      </div>
    </AuthenticatedLayout>
  );
};

export default MentorshipSessionsPage;
