import React, { useState, useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import {
  Calendar,
  Clock,
  Video,
  Users,
  MessageSquare,
  Star,
  ArrowLeft,
  Edit,
  Trash2,
  AlertCircle,
  CheckCircle,
  X,
  ExternalLink,
  Copy
} from 'lucide-react';
import { useAppDispatch, useAppSelector } from '../../store/hooks';
// MainLayout removed - handled by routing system
import {
  fetchMentorshipSession,
  updateMentorshipSession,
  fetchSessionFeedback
} from '../../store/incubatorSlice';
import { VideoConferenceRoom, MentorshipFeedbackForm } from '../../components/incubator';
import { useTranslation } from 'react-i18next';

const MentorshipSessionDetailPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const { sessionId } = useParams<{ sessionId: string }>();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { selectedMentorshipSession, mentorshipFeedback, isLoading, error } = useAppSelector(state => state.incubator);
  const { user } = useAppSelector(state => state.auth);

  const [showCancelModal, setShowCancelModal] = useState(false);
  const [showFeedbackForm, setShowFeedbackForm] = useState(false);
  const [cancelReason, setCancelReason] = useState('');
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [copySuccess, setCopySuccess] = useState(false);

  useEffect(() => {
    if (sessionId) {
      dispatch(fetchMentorshipSession(parseInt(sessionId)));
      dispatch(fetchSessionFeedback(parseInt(sessionId)));
    }
  }, [dispatch, sessionId]);

  const formatSessionTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString(undefined, {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getSessionStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'bg-blue-900/50 text-blue-200';
      case 'completed':
        return 'bg-green-900/50 text-green-200';
      case 'cancelled':
        return 'bg-red-900/50 text-red-200';
      case 'rescheduled':
        return 'bg-yellow-900/50 text-yellow-200';
      case 'in_progress':
        return 'bg-purple-900/50 text-purple-200';
      default:
        return 'bg-gray-900/50 text-gray-200';
    }
  };

  const handleStartSession = async () => {
    if (!sessionId) return;

    try {
      await dispatch(updateMentorshipSession({
        id: parseInt(sessionId),
        action: 'start_session'
      })).unwrap();

      setSuccessMessage(t("dashboard.session.started.successfully", "Session started successfully!"));
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (err) {
      setErrorMessage(t("dashboard.failed.to.start", "Failed to start session. Please try again."));
      setTimeout(() => setErrorMessage(null), 3000);
    }
  };

  const handleCompleteSession = async () => {
    if (!sessionId) return;

    try {
      await dispatch(updateMentorshipSession({
        id: parseInt(sessionId),
        action: 'complete_session'
      })).unwrap();

      setSuccessMessage(t("dashboard.session.completed.successfully", "Session completed successfully!"));
      setTimeout(() => {
        setSuccessMessage(null);
        setShowFeedbackForm(true);
      }, 1500);
    } catch (err) {
      setErrorMessage(t("dashboard.failed.to.complete", "Failed to complete session. Please try again."));
      setTimeout(() => setErrorMessage(null), 3000);
    }
  };

  const handleCancelSession = async () => {
    if (!sessionId) return;

    try {
      await dispatch(updateMentorshipSession({
        id: parseInt(sessionId),
        action: 'cancel_session'
      })).unwrap();

      setSuccessMessage(t("dashboard.session.cancelled.successfully", "Session cancelled successfully!"));
      setShowCancelModal(false);
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (err) {
      setErrorMessage(t("dashboard.failed.to.cancel", "Failed to cancel session. Please try again."));
      setTimeout(() => setErrorMessage(null), 3000);
    }
  };

  const handleCopyMeetingLink = () => {
    if (selectedMentorshipSession?.meeting_link) {
      navigator.clipboard.writeText(selectedMentorshipSession.meeting_link);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    }
  };

  const handleOpenMeetingLink = () => {
    if (selectedMentorshipSession?.meeting_link) {
      window.open(selectedMentorshipSession.meeting_link, '_blank');
    }
  };

  const handleFeedbackSubmitted = () => {
    setShowFeedbackForm(false);
    setSuccessMessage(t("dashboard.feedback.submitted.successfully", "Feedback submitted successfully!"));
    setTimeout(() => setSuccessMessage(null), 3000);

    // Refresh session data to show the new feedback
    if (sessionId) {
      dispatch(fetchMentorshipSession(parseInt(sessionId)));
      dispatch(fetchSessionFeedback(parseInt(sessionId)));
    }
  };

  const canStartSession = selectedMentorshipSession &&
    ['scheduled', 'rescheduled'].includes(selectedMentorshipSession.status);

  const canCompleteSession = selectedMentorshipSession &&
    selectedMentorshipSession.status === 'in_progress';

  const canCancelSession = selectedMentorshipSession &&
    ['scheduled', 'rescheduled'].includes(selectedMentorshipSession.status);

  const canProvideFeedback = selectedMentorshipSession &&
    selectedMentorshipSession.status === 'completed' &&
    (!selectedMentorshipSession.feedback || selectedMentorshipSession.feedback.length === 0);

  const isUpcoming = selectedMentorshipSession &&
    new Date(selectedMentorshipSession.scheduled_at) > new Date() &&
    ['scheduled', 'rescheduled'].includes(selectedMentorshipSession.status);

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="p-6">
        {/* Back button */}
        <div className="mb-6">
          <Link
            to="/dashboard/mentorship/sessions"
            className={`text-gray-400 hover:text-gray-300 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <ArrowLeft size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
            Back to Sessions
          </Link>
        </div>

        {isLoading && !selectedMentorshipSession && (
          <div className={`flex justify-center py-12 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
          </div>
        )}

        {error && (
          <div className={`bg-red-900/30 text-red-200 p-4 rounded-lg mb-6 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <AlertCircle size={20} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
            <span>Error loading session: {error}</span>
          </div>
        )}

        {successMessage && (
          <div className={`bg-green-900/30 text-green-200 p-4 rounded-lg mb-6 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <CheckCircle size={20} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
            <span>{successMessage}</span>
          </div>
        )}

        {errorMessage && (
          <div className={`bg-red-900/30 text-red-200 p-4 rounded-lg mb-6 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <AlertCircle size={20} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
            <span>{errorMessage}</span>
          </div>
        )}

        {selectedMentorshipSession && (
          <>
            {/* Session Header */}
            <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50 mb-6">
              <div className={`flex flex-col md:flex-row md:justify-between md:items-start gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                <div>
                  <div className={`flex items-center mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <h1 className={`text-2xl font-bold mr-3 ${isRTL ? "space-x-reverse" : ""}`}>{selectedMentorshipSession.title}</h1>
                    <span className={`px-3 py-1 rounded-full text-sm ${getSessionStatusColor(selectedMentorshipSession.status)}`}>
                      {selectedMentorshipSession.status_display}
                    </span>
                  </div>

                  <div className="text-gray-400 mb-4">
                    {selectedMentorshipSession.description || t("dashboard.no.description.provided", "No description provided.")}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-gray-300">
                    <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                      <Calendar size={18} className={`mr-2 text-purple-400 ${isRTL ? "space-x-reverse" : ""}`} />
                      <span>{formatSessionTime(selectedMentorshipSession.scheduled_at)}</span>
                    </div>

                    <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                      <Clock size={18} className={`mr-2 text-blue-400 ${isRTL ? "space-x-reverse" : ""}`} />
                      <span>{selectedMentorshipSession.duration_minutes} minutes</span>
                    </div>

                    <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                      <Video size={18} className={`mr-2 text-green-400 ${isRTL ? "space-x-reverse" : ""}`} />
                      <span>{selectedMentorshipSession.session_type_display}</span>
                    </div>

                    {selectedMentorshipSession.location && (
                      <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                        <Users size={18} className={`mr-2 text-yellow-400 ${isRTL ? "space-x-reverse" : ""}`} />
                        <span>{selectedMentorshipSession.location}</span>
                      </div>
                    )}
                  </div>
                </div>

                <div className={`flex flex-col gap-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                  {canStartSession && (
                    <button
                      onClick={handleStartSession}
                      className={`px-4 py-2 bg-green-600 hover:bg-green-700 rounded-md text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                    >
                      <Video size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                      Start Session
                    </button>
                  )}

                  {canCompleteSession && (
                    <button
                      onClick={handleCompleteSession}
                      className={`px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-md text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                    >
                      <CheckCircle size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                      Complete Session
                    </button>
                  )}

                  {canCancelSession && (
                    <button
                      onClick={() => setShowCancelModal(true)}
                      className={`px-4 py-2 bg-red-600 hover:bg-red-700 rounded-md text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                    >
                      <X size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                      Cancel Session
                    </button>
                  )}

                  {canProvideFeedback && (
                    <button
                      onClick={() => setShowFeedbackForm(true)}
                      className={`px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                    >
                      <MessageSquare size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                      Provide Feedback
                    </button>
                  )}
                </div>
              </div>
            </div>

            {/* Video Conference Section */}
            {selectedMentorshipSession.session_type === 'video' && (
              <div className="mb-6">
                <h2 className="text-xl font-semibold mb-4">{t("dashboard.video.conference", "Video Conference")}</h2>

                {selectedMentorshipSession.meeting_link ? (
                  <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
                    <div className={`flex flex-col md:flex-row md:justify-between md:items-center gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <div>
                        <h3 className="font-medium mb-2">{t("dashboard.meeting.details", "Meeting Details")}</h3>
                        <div className={`flex items-center mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                          <Video size={16} className={`mr-2 text-blue-400 ${isRTL ? "space-x-reverse" : ""}`} />
                          <span>{selectedMentorshipSession.video_provider_display || t("dashboard.video.conference", "Video Conference")}</span>
                        </div>

                        <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                          <div className={`bg-indigo-950 border border-indigo-800 rounded-md px-3 py-2 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                            <span className={`text-gray-300 mr-2 truncate max-w-xs ${isRTL ? "space-x-reverse" : ""}`}>
                              {selectedMentorshipSession.meeting_link}
                            </span>
                            <button
                              onClick={handleCopyMeetingLink}
                              className="p-1 hover:bg-indigo-800 rounded"
                              title={t("dashboard.copy.link", "Copy link")}
                            >
                              {copySuccess ? (
                                <CheckCircle size={16} className="text-green-400" />
                              ) : (
                                <Copy size={16} className="text-gray-400" />
                              )}
                            </button>
                          </div>
                        </div>
                      </div>

                      <div className={`flex gap-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <button
                          onClick={handleOpenMeetingLink}
                          className={`px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-md text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                        >
                          <ExternalLink size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                          Open in New Tab
                        </button>

                        <Link
                          to={`/dashboard/mentorship/sessions/${selectedMentorshipSession.id}/join`}
                          className={`px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                        >
                          <Video size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                          Join Session
                        </Link>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50 text-center">
                    <Video size={40} className="mx-auto mb-3 text-gray-500" />
                    <h3 className="text-lg font-medium mb-2">{t("dashboard.no.meeting.link", "No Meeting Link Available")}</h3>
                    <div className="text-gray-400 mb-4">
                      This session doesn't have a video conference link yet.
                    </div>

                    {isUpcoming && (
                      <button
                        onClick={() => {
                          // Create meeting link logic
                        }}
                        className={`px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white inline-flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                      >
                        <Video size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                        Create Meeting Link
                      </button>
                    )}
                  </div>
                )}
              </div>
            )}

            {/* Feedback Section */}
            {showFeedbackForm ? (
              <div className="mb-6">
                <h2 className="text-xl font-semibold mb-4">{t("dashboard.provide.feedback", "Provide Feedback")}</h2>
                <MentorshipFeedbackForm
                  sessionId={parseInt(sessionId!)}
                  onSuccess={handleFeedbackSubmitted}
                  onCancel={() => setShowFeedbackForm(false)}
                />
              </div>
            ) : selectedMentorshipSession.feedback && selectedMentorshipSession.feedback.length > 0 ? (
              <div className="mb-6">
                <h2 className="text-xl font-semibold mb-4">{t("dashboard.session.feedback", "Session Feedback")}</h2>
                <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
                  {selectedMentorshipSession.feedback.map(feedback => (
                    <div key={feedback.id} className="mb-4 last:mb-0">
                      <div className={`flex items-center justify-between mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                          <div className={`w-8 h-8 rounded-full bg-indigo-800 flex items-center justify-center mr-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                            <Users size={16} className="text-blue-400" />
                          </div>
                          <span className="font-medium">
                            {feedback.provided_by.first_name} {feedback.provided_by.last_name}
                          </span>
                          <span className={`text-gray-400 text-sm ml-2 ${isRTL ? "space-x-reverse" : ""}`}>
                            {feedback.is_from_mentee ? t("dashboard.mentee", "Mentee") : t("dashboard.mentor", "Mentor")}
                          </span>
                        </div>
                        <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                          {[1, 2, 3, 4, 5].map(star => (
                            <Star
                              key={star}
                              size={16}
                              className={star <= feedback.rating ? 'text-yellow-400 fill-yellow-400' : 'text-gray-600'}
                            />
                          ))}
                          <span className={`ml-1 text-gray-300 ${isRTL ? "space-x-reverse" : ""}`}>{feedback.rating}/5</span>
                        </div>
                      </div>
                      <div className="text-gray-300 mb-2">{feedback.comments}</div>
                      {feedback.highlights && (
                        <div className="mb-2">
                          <span className="text-green-400 text-sm font-medium">{t("dashboard.highlights", "Highlights: ")}</span>
                          <span className="text-gray-400">{feedback.highlights}</span>
                        </div>
                      )}
                      {feedback.areas_of_improvement && (
                        <div>
                          <span className="text-blue-400 text-sm font-medium">{t("dashboard.areas.for.improvement", "Areas for improvement: ")}</span>
                          <span className="text-gray-400">{feedback.areas_of_improvement}</span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            ) : null}
          </>
        )}

        {/* Cancel Session Modal */}
        {showCancelModal && (
          <div className={`fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className="bg-indigo-900 rounded-lg p-6 max-w-md w-full">
              <h3 className="text-xl font-bold mb-4">{t("dashboard.cancel.session", "Cancel Session")}</h3>
              <div className="text-gray-300 mb-4">
                Are you sure you want to cancel this session? This action cannot be undone.
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium mb-1">
                  Reason for cancellation (optional)
                </label>
                <textarea
                  value={cancelReason}
                  onChange={(e) => setCancelReason(e.target.value)}
                  rows={3}
                  className="w-full bg-indigo-950 border border-indigo-700 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder={t("dashboard.enter.reason.for", "Enter reason for cancellation...")}
                />
              </div>

              <div className={`flex justify-end space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                <button
                  onClick={() => setShowCancelModal(false)}
                  className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-md text-white"
                >
                  Cancel
                </button>
                <button
                  onClick={handleCancelSession}
                  className={`px-4 py-2 bg-red-600 hover:bg-red-700 rounded-md text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                >
                  <X size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                  Confirm Cancellation
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
              </div>
        </div>
      </div>
    </div>
  );
};

export default MentorshipSessionDetailPage;
