import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import {
  Users,
  RefreshCw,
  Briefcase,
  Award,
  Star,
  ArrowRight,
  MessageSquare,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { useAppSelector } from '../../store/hooks';
import { MentorRecommendation, mentorRecommendationsAPI } from '../../services/milestoneApi';
import { mentorshipApplicationsAPI } from '../../services/incubatorApi';

interface MentorRecommendationsProps {
  businessIdeaId: number;
  businessIdeaTitle: string;
}

const MentorRecommendations: React.FC<MentorRecommendationsProps> = ({
  businessIdeaId, businessIdeaTitle }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { user } = useAppSelector((state) => state.auth);
  const [recommendations, setRecommendations] = useState<MentorRecommendation[]>([]);
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [showApplyModal, setShowApplyModal] = useState(false);
  const [selectedMentor, setSelectedMentor] = useState<MentorRecommendation | null>(null);
  const [applicationData, setApplicationData] = useState({
    goals: '',
    specific_areas: '',
    commitment: '',
    preferred_communication: 'video',
    preferred_expertise: ''
  });

  useEffect(() => {
    fetchRecommendations();
  }, [businessIdeaId]);

  const fetchRecommendations = async () => {
    setLoading(true);
    try {
      const data = await mentorRecommendationsAPI.getRecommendations(businessIdeaId);
      setRecommendations(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching mentor recommendations:', err);
      setError(t("dashboard.failed.to.load", "Failed to load mentor recommendations. Please try again later."));
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateRecommendations = async () => {
    setGenerating(true);
    setSuccessMessage(null);
    try {
      const result = await mentorRecommendationsAPI.generateRecommendations(businessIdeaId);
      setRecommendations(result.recommendations);
      setSuccessMessage(`Generated ${result.recommendations.length} mentor recommendations based on your business idea progress.`);
      setError(null);
    } catch (err) {
      console.error('Error generating mentor recommendations:', err);
      setError(t("dashboard.failed.to.generate", "Failed to generate mentor recommendations. Please try again later."));
    } finally {
      setGenerating(false);
    }
  };

  const handleApplyToMentor = async () => {
    if (!selectedMentor || !user) return;

    try {
      await mentorshipApplicationsAPI.createMentorshipApplication({
        business_idea: businessIdeaId,
        applicant_id: user.id,
        preferred_mentor_id: selectedMentor.mentor.id,
        goals: applicationData.goals,
        specific_areas: applicationData.specific_areas,
        commitment: applicationData.commitment,
        preferred_communication: applicationData.preferred_communication as any,
        preferred_expertise: applicationData.preferred_expertise || undefined
      });

      // Mark recommendation as applied
      await mentorRecommendationsAPI.markAsApplied(selectedMentor.id);

      // Refresh recommendations
      fetchRecommendations();

      setShowApplyModal(false);
      setSelectedMentor(null);
      setApplicationData({
        goals: '',
        specific_areas: '',
        commitment: '',
        preferred_communication: 'video',
        preferred_expertise: ''
      });

      setSuccessMessage(t("dashboard.mentorship.application.submitted", "Mentorship application submitted successfully!"));
    } catch (err) {
      console.error('Error applying to mentor:', err);
      setError(t("dashboard.failed.to.submit", "Failed to submit mentorship application. Please try again later."));
    }
  };

  const getMatchScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-400';
    if (score >= 60) return 'text-blue-400';
    if (score >= 40) return 'text-yellow-400';
    return 'text-gray-400';
  };

  if (loading) {
    return (
      <div className={`flex justify-center py-12 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
        <h2 className="text-xl font-bold">{t("dashboard.mentor.recommendations", "Mentor Recommendations")}</h2>
        <button
          onClick={handleGenerateRecommendations}
          disabled={generating}
          className={`px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white flex items-center transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${isRTL ? "flex-row-reverse" : ""}`}
        >
          <RefreshCw size={16} className={`mr-2 ${generating ? 'animate-spin' : ''}`} />
          {generating ? t("dashboard.generating.generate.recommendations", "Generating...") : t("dashboard.generate.recommendations", "Generate Recommendations")}
        </button>
      </div>

      {successMessage && (
        <div className={`bg-green-900/30 backdrop-blur-sm rounded-lg p-4 border border-green-800/50 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
          <CheckCircle size={20} className={`text-green-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
          <div className="text-green-400">{successMessage}</div>
        </div>
      )}

      {error && (
        <div className={`bg-red-900/30 backdrop-blur-sm rounded-lg p-4 border border-red-800/50 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
          <AlertCircle size={20} className={`text-red-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
          <div className="text-red-400">{error}</div>
        </div>
      )}

      {recommendations.length === 0 ? (
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50 text-center">
          <div className="text-gray-400 mb-4">{t("dashboard.no.mentor.recommendations", "No mentor recommendations available yet. Generate recommendations based on your business idea progress.")}</div>
          <button
            onClick={handleGenerateRecommendations}
            disabled={generating}
            className={`px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white flex items-center mx-auto transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <RefreshCw size={16} className={`mr-2 ${generating ? 'animate-spin' : ''}`} />
            {generating ? t("dashboard.generating.generate.recommendations", "Generating...") : t("dashboard.generate.recommendations", "Generate Recommendations")}
          </button>
        </div>
      ) : (
        <div className="space-y-4">
          {recommendations.map(recommendation => (
            <div
              key={recommendation.id}
              className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50"
            >
              <div className={`flex flex-col md:flex-row md:items-center justify-between mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                <div className={`flex items-center mb-2 md:mb-0 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className={`w-12 h-12 rounded-full bg-purple-700 flex items-center justify-center text-white text-lg font-bold mr-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                    {recommendation.mentor.user.first_name.charAt(0)}{recommendation.mentor.user.last_name.charAt(0)}
                  </div>
                  <div>
                    <h3 className="text-lg font-medium">
                      {recommendation.mentor.user.first_name} {recommendation.mentor.user.last_name}
                    </h3>
                    <div className="text-gray-400 text-sm">
                      {recommendation.mentor.position} at {recommendation.mentor.company}
                    </div>
                  </div>
                </div>

                <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className={`text-xl font-bold ${getMatchScoreColor(recommendation.match_score)} mr-2`}>
                    {Math.round(recommendation.match_score)}%
                  </div>
                  <div className="text-sm text-gray-400">{t("dashboard.match", "Match")}</div>
                </div>
              </div>

              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-300 mb-2">{t("dashboard.expertise.match", "Expertise Match")}</h4>
                <div className={`flex flex-wrap gap-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                  {recommendation.expertise_match.split(',').map((expertise, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-purple-900/50 text-purple-200 rounded-md text-xs"
                    >
                      {expertise.trim()}
                    </span>
                  ))}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 text-sm">
                <div className={`flex items-center text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <Briefcase size={14} className={`mr-1 text-blue-400 ${isRTL ? "space-x-reverse" : ""}`} />
                  <span>{recommendation.mentor.years_of_experience} years experience</span>
                </div>

                <div className={`flex items-center text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <Award size={14} className={`mr-1 text-yellow-400 ${isRTL ? "space-x-reverse" : ""}`} />
                  <span>{recommendation.mentor.expertise_areas.length} expertise areas</span>
                </div>

                <div className={`flex items-center text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <Star size={14} className={`mr-1 text-purple-400 ${isRTL ? "space-x-reverse" : ""}`} />
                  <span>{recommendation.mentor.availability_display}</span>
                </div>
              </div>

              <div className={`flex justify-between items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <Link
                  to={`/incubator/mentors/${recommendation.mentor.id}`}
                  className={`text-purple-400 hover:text-purple-300 text-sm flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                >
                  View Profile <ArrowRight size={14} className={`ml-1 ${isRTL ? "space-x-reverse" : ""}`} />
                </Link>

                {recommendation.is_applied ? (
                  <span className={`px-3 py-1.5 bg-green-900/50 text-green-200 rounded-md text-sm flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    <CheckCircle size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> Applied
                  </span>
                ) : recommendation.is_matched ? (
                  <span className={`px-3 py-1.5 bg-blue-900/50 text-blue-200 rounded-md text-sm flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    <Users size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> Matched
                  </span>
                ) : (
                  <button
                    onClick={() => {
                      setSelectedMentor(recommendation);
                      setShowApplyModal(true);
                    }}
                    className={`px-3 py-1.5 bg-purple-600 hover:bg-purple-700 rounded-md text-white text-sm flex items-center transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
                  >
                    <MessageSquare size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> Apply for Mentorship
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Apply for Mentorship Modal */}
      {showApplyModal && selectedMentor && (
        <div className={`fixed inset-0 bg-black/70 flex items-center justify-center z-50 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="bg-gray-900 rounded-lg p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <h3 className="text-xl font-bold mb-4">{t("dashboard.apply.for.mentorship", "Apply for Mentorship")}</h3>
            <div className={`mb-4 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <div className={`w-12 h-12 rounded-full bg-purple-700 flex items-center justify-center text-white text-lg font-bold mr-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                {selectedMentor.mentor.user.first_name.charAt(0)}{selectedMentor.mentor.user.last_name.charAt(0)}
              </div>
              <div>
                <h4 className="font-medium">
                  {selectedMentor.mentor.user.first_name} {selectedMentor.mentor.user.last_name}
                </h4>
                <div className="text-gray-400 text-sm">
                  {selectedMentor.mentor.position} at {selectedMentor.mentor.company}
                </div>
              </div>
            </div>

            <div className="space-y-4 mb-6">
              <div>
                <label className="block text-sm font-medium mb-1">{t("dashboard.what.do.you", "What do you hope to achieve with mentorship?")}</label>
                <textarea
                  className="w-full bg-gray-800 border border-gray-700 rounded-md p-2 text-white"
                  rows={3}
                  value={applicationData.goals}
                  onChange={(e) => setApplicationData({...applicationData, goals: e.target.value})}
                  placeholder={t("dashboard.describe.your.mentorship", "Describe your mentorship goals...")}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">{t("dashboard.what.specific.areas", "What specific areas do you need help with?")}</label>
                <textarea
                  className="w-full bg-gray-800 border border-gray-700 rounded-md p-2 text-white"
                  rows={3}
                  value={applicationData.specific_areas}
                  onChange={(e) => setApplicationData({...applicationData, specific_areas: e.target.value})}
                  placeholder={t("dashboard.describe.specific.areas", "Describe specific areas where you need guidance...")}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">{t("dashboard.how.much.time", "How much time can you commit to working with a mentor?")}</label>
                <textarea
                  className="w-full bg-gray-800 border border-gray-700 rounded-md p-2 text-white"
                  rows={2}
                  value={applicationData.commitment}
                  onChange={(e) => setApplicationData({...applicationData, commitment: e.target.value})}
                  placeholder={t("dashboard.describe.your.time", "Describe your time commitment...")}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">{t("dashboard.preferred.communication.method", "Preferred Communication Method")}</label>
                <select
                  className="w-full bg-gray-800 border border-gray-700 rounded-md p-2 text-white"
                  value={applicationData.preferred_communication}
                  onChange={(e) => setApplicationData({...applicationData, preferred_communication: e.target.value})}
                  required
                >
                  <option value="video">{t("dashboard.video.call", "Video Call")}</option>
                  <option value="phone">{t("dashboard.phone.call", "Phone Call")}</option>
                  <option value="email">{t("dashboard.email", "Email")}</option>
                  <option value="chat">Chat/Messaging</option>
                  <option value="in_person">{t("dashboard.in.person", "In Person")}</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Preferred Expertise (Optional)</label>
                <select
                  className="w-full bg-gray-800 border border-gray-700 rounded-md p-2 text-white"
                  value={applicationData.preferred_expertise}
                  onChange={(e) => setApplicationData({...applicationData, preferred_expertise: e.target.value})}
                >
                  <option value="">Select Expertise (Optional)</option>
                  <option value="business_strategy">{t("dashboard.business.strategy", "Business Strategy")}</option>
                  <option value="marketing">Marketing & Sales</option>
                  <option value="finance">Finance & Accounting</option>
                  <option value="operations">Operations & Logistics</option>
                  <option value="technology">Technology & Development</option>
                  <option value="product">{t("dashboard.product.management", "Product Management")}</option>
                  <option value="legal">Legal & Compliance</option>
                  <option value="hr">{t("dashboard.human.resources", "Human Resources")}</option>
                  <option value="fundraising">Fundraising & Investment</option>
                  <option value="international">{t("dashboard.international.business", "International Business")}</option>
                  <option value="ecommerce">{t("dashboard.ecommerce", "E-Commerce")}</option>
                  <option value="social_impact">{t("dashboard.social.impact", "Social Impact")}</option>
                  <option value="other">{t("dashboard.other", "Other")}</option>
                </select>
              </div>
            </div>

            <div className={`flex justify-end space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                onClick={() => {
                  setShowApplyModal(false);
                  setSelectedMentor(null);
                  setApplicationData({
                    goals: '',
                    specific_areas: '',
                    commitment: '',
                    preferred_communication: 'video',
                    preferred_expertise: ''
                  });
                }}
                className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-md text-white"
              >
                Cancel
              </button>
              <button
                onClick={handleApplyToMentor}
                disabled={!applicationData.goals || !applicationData.specific_areas || !applicationData.commitment}
                className={`px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white flex items-center disabled:opacity-50 disabled:cursor-not-allowed ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <MessageSquare size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} /> Submit Application
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MentorRecommendations;
