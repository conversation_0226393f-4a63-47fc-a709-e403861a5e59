import React, { useState } from 'react';
import { Award, Star, MessageSquare, Heart, Users, TrendingUp, DollarSign, Calendar, ArrowRight } from 'lucide-react';
import MembershipForm from './MembershipForm';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../store/hooks';
import { RTLText, RTLFlex, RTLIcon } from './common';
import { Link } from 'react-router-dom';


import { useLanguage } from "../hooks/useLanguage";
const Testimonial = ({ quote, name, title, image }: { quote: string; name: string; title: string; image: string }) => {
  const { language } = useAppSelector(state => state.language);

  return (
    <div className="glass-light rounded-xl p-6 hover:shadow-purple-500/10 transition-all duration-300 border">
      <div className="mb-4">
        <RTLIcon icon={MessageSquare} size={24} className="text-purple-400" />
      </div>
      <RTLText as="div" className="text-glass-secondary mb-6 italic">"{quote}"</RTLText>
      <RTLFlex className="items-center">
        <div className={`w-12 h-12 rounded-full overflow-hidden ${language === 'ar' ? 'ml-4' : 'mr-4'}`}>
          <img src={image} alt={name} className="w-full h-full object-cover" />
        </div>
        <div>
          <RTLText as="h4" className="font-semibold text-glass-primary">{name}</RTLText>
          <RTLText as="p" className="text-sm text-glass-secondary">
            {title}
          </RTLText>
        </div>
      </RTLFlex>
    </div>
  );
};

const SuccessStory = ({ title, description, image }: { title: string; description: string; image: string }) => {
  const { language } = useAppSelector(state => state.language);

  return (
    <div className="glass-light rounded-xl overflow-hidden shadow-lg group border">
      <div className="h-48 overflow-hidden">
        <img
          src={image}
          alt={title}
          className="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-500"
        />
      </div>
      <div className="p-6">
        <RTLFlex className="items-center mb-3">
          <RTLIcon icon={Award} size={20} className="text-purple-400" />
          <RTLText as="h3" className="text-xl font-semibold text-glass-primary">{title}</RTLText>
        </RTLFlex>
        <RTLText as="div" className="text-glass-secondary">{description}</RTLText>
      </div>
    </div>
  );
};

const Community = () => {
  const { t } = useTranslation();
  const { language } = useAppSelector(state => state.language);
  const [showMembershipForm, setShowMembershipForm] = useState(false);

  const testimonials = [
    {
      quote: "Yasmeen AI's virtual incubator transformed my idea into a thriving e-commerce business. The AI-powered guidance and mentor support were invaluable.",
      name: "Ahmad Khalid",
      title: "Founder @ TechMart Solutions",
      image: "https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
    },
    {
      quote: "From concept to launch in 6 months! The incubator's structured program and expert mentorship helped me secure my first round of funding.",
      name: "Layla Mahmoud",
      title: "CEO @ GreenTech Innovations",
      image: "https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
    },
    {
      quote: "The entrepreneur community here is incredible. I found my co-founder, secured investment, and launched my fintech startup all through this platform.",
      name: "Omar Farouk",
      title: "Co-Founder @ PayFlow",
      image: "https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
    },
  ];

  const successStories = [
    {
      title: "TechMart Solutions - $2M Series A",
      description: "E-commerce platform serving 50,000+ customers across the Middle East. Secured Series A funding after 18 months in our incubator program.",
      image: "https://images.pexels.com/photos/3683074/pexels-photo-3683074.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
    },
    {
      title: "GreenTech Innovations - Sustainability Leader",
      description: "Revolutionary clean energy solution now deployed in 5 countries. Winner of the Arab Innovation Award 2024.",
      image: "https://images.pexels.com/photos/3861958/pexels-photo-3861958.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
    },
  ];

  return (
    <section id="community" className="py-20 relative overflow-hidden">
      <div className="container mx-auto px-4">
        {/* Header Section */}
        <div className="max-w-4xl mx-auto text-center mb-16">
          <div className="mb-6">
            <div className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium mb-6 glass-light text-glass-primary border border-glass-border ${language === 'ar' ? "flex-row-reverse" : ""}`}>
              <Users className={`w-4 h-4 mr-2 ${language === 'ar' ? "space-x-reverse" : ""}`} />
              Entrepreneur Community • Success Stories • Networking
            </div>
          </div>

          <RTLText as="h2" align="center" className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
            {t('community.our')} <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400">{t('community.title')}</span>
          </RTLText>

          <RTLText as="p" align="center" className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto leading-relaxed text-glass-secondary">
            {t('community.description')}
          </RTLText>
        </div>

        {/* Community Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
          <div className="text-center p-6 rounded-2xl backdrop-blur-sm transition-all duration-300 hover:scale-105 glass-light border">
            <div className="text-3xl md:text-4xl font-bold mb-2 text-glass-primary">500+</div>
            <div className="text-sm text-glass-secondary">{t('community.stats.activeEntrepreneurs')}</div>
          </div>
          <div className="text-center p-6 rounded-2xl backdrop-blur-sm transition-all duration-300 hover:scale-105 glass-light border">
            <div className="text-3xl md:text-4xl font-bold mb-2 text-glass-primary">150+</div>
            <div className="text-sm text-glass-secondary">{t('community.stats.successfulLaunches')}</div>
          </div>
          <div className="text-center p-6 rounded-2xl backdrop-blur-sm transition-all duration-300 hover:scale-105 glass-light border">
            <div className="text-3xl md:text-4xl font-bold mb-2 text-glass-primary">$50M+</div>
            <div className="text-sm text-glass-secondary">{t('community.stats.fundingSecured')}</div>
          </div>
          <div className="text-center p-6 rounded-2xl backdrop-blur-sm transition-all duration-300 hover:scale-105 glass-light border">
            <div className="text-3xl md:text-4xl font-bold mb-2 text-glass-primary">2000+</div>
            <div className="text-sm text-glass-secondary">{t('community.stats.mentorshipSessions')}</div>
          </div>
        </div>

        {/* Testimonials Section */}
        <div className="mb-20">
          <RTLFlex as="h3" className="text-3xl font-bold mb-8 items-center text-glass-primary justify-center">
            <div className="w-12 h-12 rounded-full bg-gradient-to-r from-yellow-500 to-orange-500 flex items-center justify-center mr-4">
              <RTLIcon icon={Star} className="text-white" size={24} />
            </div>
            {t('community.testimonials')}
          </RTLFlex>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Testimonial key={index} {...testimonial} />
            ))}
          </div>
        </div>

        {/* Success Stories Section */}
        <div className="mb-20">
          <RTLFlex as="h3" className="text-3xl font-bold mb-8 items-center text-glass-primary justify-center">
            <div className="w-12 h-12 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center mr-4">
              <RTLIcon icon={Award} className="text-white" size={24} />
            </div>
            {t('community.successStories')}
          </RTLFlex>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {successStories.map((story, index) => (
              <SuccessStory key={index} {...story} />
            ))}
            {/* Add one more success story */}
            <SuccessStory
              title="PayFlow - Fintech Innovation"
              description="Revolutionary payment solution processing $10M+ monthly transactions. Expanded to 3 countries within first year."
              image="https://images.pexels.com/photos/3184465/pexels-photo-3184465.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
            />
          </div>
        </div>

        {/* Upcoming Events Section */}
        <div className="mb-20">
          <RTLFlex as="h3" className="text-3xl font-bold mb-8 items-center text-glass-primary justify-center">
            <div className="w-12 h-12 rounded-full bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center mr-4">
              <RTLIcon icon={Calendar} className="text-white" size={24} />
            </div>
            {t('community.events.title')}
          </RTLFlex>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="p-6 rounded-2xl backdrop-blur-sm glass-light border border-glass-border hover:scale-105 transition-all duration-300">
              <div className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center mb-4">
                <Users size={20} className="text-white" />
              </div>
              <RTLText as="h4" className="font-semibold mb-2 text-glass-primary">{t('community.events.entrepreneurshipWorkshop')}</RTLText>
              <RTLText as="p" className="text-sm text-glass-secondary mb-3">Dec 15, 2024</RTLText>
              <RTLText as="p" className="text-xs text-glass-secondary">Learn essential entrepreneurship skills</RTLText>
            </div>

            <div className="p-6 rounded-2xl backdrop-blur-sm glass-light border border-glass-border hover:scale-105 transition-all duration-300">
              <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center mb-4">
                <TrendingUp size={20} className="text-white" />
              </div>
              <RTLText as="h4" className="font-semibold mb-2 text-glass-primary">{t('community.events.pitchNight')}</RTLText>
              <RTLText as="p" className="text-sm text-glass-secondary mb-3">Dec 20, 2024</RTLText>
              <RTLText as="p" className="text-xs text-glass-secondary">Present your startup to investors</RTLText>
            </div>

            <div className="p-6 rounded-2xl backdrop-blur-sm glass-light border border-glass-border hover:scale-105 transition-all duration-300">
              <div className="w-10 h-10 rounded-full bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center mb-4">
                <Users size={20} className="text-white" />
              </div>
              <RTLText as="h4" className="font-semibold mb-2 text-glass-primary">{t('community.events.networkingMeetup')}</RTLText>
              <RTLText as="p" className="text-sm text-glass-secondary mb-3">Dec 25, 2024</RTLText>
              <RTLText as="p" className="text-xs text-glass-secondary">Connect with fellow entrepreneurs</RTLText>
            </div>

            <div className="p-6 rounded-2xl backdrop-blur-sm glass-light border border-glass-border hover:scale-105 transition-all duration-300">
              <div className="w-10 h-10 rounded-full bg-gradient-to-r from-yellow-500 to-orange-500 flex items-center justify-center mb-4">
                <Star size={20} className="text-white" />
              </div>
              <RTLText as="h4" className="font-semibold mb-2 text-glass-primary">{t('community.events.aiForBusiness')}</RTLText>
              <RTLText as="p" className="text-sm text-glass-secondary mb-3">Dec 30, 2024</RTLText>
              <RTLText as="p" className="text-xs text-glass-secondary">AI tools for business growth</RTLText>
            </div>
          </div>
        </div>

        {/* Join Community Section */}
        <div className="glass-light rounded-2xl p-8 border border-glass-border shadow-lg">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-10 items-center">
            <div>
              <div className="flex items-center mb-6">
                <div className="w-16 h-16 rounded-full bg-gradient-to-r from-red-500 to-pink-500 flex items-center justify-center">
                  <Heart className="w-8 h-8 text-white" />
                </div>
                <RTLText as="h3" className="text-3xl font-bold ml-4 text-glass-primary">
                  {t('community.joinOurCommunity')}
                </RTLText>
              </div>

              <RTLText as="p" className="text-lg text-glass-secondary mb-8">
                {t('community.joinDescription')}
              </RTLText>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                <div className="flex items-center p-4 rounded-xl glass-light border border-glass-border">
                  <Users className="w-6 h-6 text-purple-400 mr-3" />
                  <RTLText as="span" className="text-glass-secondary">{t('community.benefits.mentorship')}</RTLText>
                </div>
                <div className="flex items-center p-4 rounded-xl glass-light border border-glass-border">
                  <TrendingUp className="w-6 h-6 text-blue-400 mr-3" />
                  <RTLText as="span" className="text-glass-secondary">{t('community.benefits.networking')}</RTLText>
                </div>
                <div className="flex items-center p-4 rounded-xl glass-light border border-glass-border">
                  <Star className="w-6 h-6 text-green-400 mr-3" />
                  <RTLText as="span" className="text-glass-secondary">{t('community.benefits.resources')}</RTLText>
                </div>
                <div className="flex items-center p-4 rounded-xl glass-light border border-glass-border">
                  <Calendar className="w-6 h-6 text-yellow-400 mr-3" />
                  <RTLText as="span" className="text-glass-secondary">{t('community.benefits.events')}</RTLText>
                </div>
              </div>

              <RTLFlex className={`flex-col sm:flex-row items-center gap-4 ${language === 'ar' ? "flex-row-reverse" : ""}`}>
                <button
                  onClick={() => setShowMembershipForm(true)}
                  className={`px-8 py-4 rounded-full flex items-center gap-2 transform hover:scale-105 transition-all duration-300 cursor-pointer bg-gradient-to-r from-purple-500 to-blue-500 text-white hover:shadow-lg font-semibold text-lg ${language === 'ar' ? "flex-row-reverse" : ""}`}
                >
                  {t('community.applyForMembership')} <RTLIcon icon={ArrowRight} size={20} flipInRTL={true} />
                </button>
                <Link
                  to="/forum"
                  className="px-8 py-4 bg-transparent rounded-full font-semibold text-lg transition-all duration-300 cursor-pointer border-2 border-purple-500 text-glass-primary hover:bg-purple-500/10 hover:shadow-lg"
                >
                  Explore Community
                </Link>
              </RTLFlex>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="h-64 rounded-lg overflow-hidden">
                <img
                  src="https://images.pexels.com/photos/3184360/pexels-photo-3184360.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
                  alt={t("common.community.meetup", "Community meetup")}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="h-64 rounded-lg overflow-hidden">
                <img
                  src="https://images.pexels.com/photos/7129701/pexels-photo-7129701.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
                  alt={t("common.ai.workshop", "AI workshop")}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="h-64 rounded-lg overflow-hidden col-span-2">
                <img
                  src="https://images.pexels.com/photos/3184291/pexels-photo-3184291.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
                  alt={t("common.community.collaboration", "Community collaboration")}
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Membership Application Form */}
      {showMembershipForm && (
        <MembershipForm onClose={() => setShowMembershipForm(false)} />
      )}
    </section>
  );
};

export default Community;