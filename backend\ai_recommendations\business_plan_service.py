import os
import json
import google.generativeai as genai
from django.conf import settings
from dotenv import load_dotenv
from incubator.models import BusinessIdea
from incubator.models_business_plan import BusinessPlan, BusinessPlanTemplate, BusinessPlanSection
from core.ai_config import get_gemini_config, generate_gemini_content

# Load environment variables
load_dotenv()

# Use centralized configuration
def get_configured_model():
    """Get configured Gemini model"""
    config = get_gemini_config()
    return config.model if config.is_available else None

def generate_business_plan_template(industry):
    """
    Generate a business plan template for a specific industry using Gemini AI
    
    Args:
        industry: Industry for which to generate a template
        
    Returns:
        Dictionary containing the template structure
    """
    try:
        # Create the prompt for Gemini
        prompt = f"""
        You are an expert business consultant specializing in {industry}. 
        Create a comprehensive business plan template specifically for {industry} businesses.
        
        The template should include all essential sections that a {industry} business plan needs.
        For each section, provide:
        1. A title
        2. A brief description of what should be included
        3. 2-3 guiding questions to help the user complete the section
        
        Format your response as a JSON array of sections, where each section has the following structure:
        {{
            "key": "unique_section_identifier",
            "title": "Section Title",
            "description": "Brief description of the section",
            "guiding_questions": ["Question 1?", "Question 2?", "Question 3?"],
            "is_required": true/false,
            "order": numeric_order
        }}
        
        Include at least the following standard sections (but customize them for {industry}):
        - Executive Summary
        - Company Description
        - Market Analysis
        - Organization & Management
        - Service/Product Line
        - Marketing & Sales
        - Financial Projections
        
        Also include 2-3 sections that are specifically important for {industry} businesses.
        """
        
        # Generate template using centralized Gemini config
        response = generate_gemini_content(prompt)
        if not response:
            return None
        
        # Parse the response
        response_text = response
        
        # Extract JSON from the response
        if '```json' in response_text:
            json_start = response_text.find('```json') + 7
            json_end = response_text.find('```', json_start)
            json_str = response_text[json_start:json_end].strip()
        elif '[' in response_text and ']' in response_text:
            json_start = response_text.find('[')
            json_end = response_text.rfind(']') + 1
            json_str = response_text[json_start:json_end].strip()
        else:
            json_str = response_text
            
        # Parse the JSON
        template_sections = json.loads(json_str)
        
        return {
            "sections": template_sections
        }
        
    except Exception as e:
        print(f"Error generating business plan template: {e}")
        return None


def generate_section_content(business_idea_id, section_key, section_title, guiding_questions):
    """
    Generate content for a specific section of a business plan
    
    Args:
        business_idea_id: ID of the business idea
        section_key: Key identifier for the section
        section_title: Title of the section
        guiding_questions: List of guiding questions for the section
        
    Returns:
        Generated content for the section
    """
    try:
        # Get the business idea
        business_idea = BusinessIdea.objects.get(id=business_idea_id)
        
        # Create context for the AI
        context = {
            "business_idea": {
                "title": business_idea.title,
                "description": business_idea.description,
                "problem_statement": business_idea.problem_statement,
                "solution_description": business_idea.solution_description,
                "target_audience": business_idea.target_audience,
                "market_opportunity": business_idea.market_opportunity,
                "business_model": business_idea.business_model,
                "current_stage": business_idea.current_stage,
                "tags": [tag.name for tag in business_idea.tags.all()]
            }
        }
        
        # Format the guiding questions
        questions_text = "\n".join([f"- {q}" for q in guiding_questions])
        
        # Create the prompt for Gemini
        prompt = f"""
        You are writing a business plan for the following business idea:
        
        Business Name: {business_idea.title}
        Description: {business_idea.description}
        Problem Statement: {business_idea.problem_statement}
        Solution: {business_idea.solution_description}
        Target Audience: {business_idea.target_audience}
        Current Stage: {business_idea.current_stage}
        
        I need you to write the "{section_title}" section of the business plan.
        
        Here are some guiding questions to address in this section:
        {questions_text}
        
        Write a comprehensive, well-structured, and professional response that would be appropriate for a formal business plan.
        Focus on being specific, data-driven where possible, and tailored to this specific business idea.
        Write in a professional business tone. Use paragraphs, bullet points, and formatting as appropriate.
        """
        
        # Generate content using centralized Gemini config
        response = generate_gemini_content(prompt)

        # Return the generated content
        return response
        
    except Exception as e:
        print(f"Error generating section content: {e}")
        return None


def analyze_business_plan(business_plan_id):
    """
    Analyze a business plan and provide feedback

    Args:
        business_plan_id: ID of the business plan to analyze

    Returns:
        Dictionary containing feedback for each section and overall feedback
    """
    try:
        print(f"Starting analysis for business plan {business_plan_id}")

        # Check if Gemini is available
        config = get_gemini_config()
        if not config.is_available:
            print("Gemini AI not available, returning fallback response")
            return {
                "overall_feedback": {
                    "strengths": ["Business plan structure is in place"],
                    "areas_for_improvement": ["AI analysis unavailable - Gemini not configured"],
                    "suggestions": ["Configure Gemini API key for detailed analysis"],
                    "completion_score": 50
                },
                "section_feedback": {}
            }

        # Get the business plan and its sections
        business_plan = BusinessPlan.objects.get(id=business_plan_id)
        sections = BusinessPlanSection.objects.filter(business_plan=business_plan).order_by('order')

        print(f"Found business plan: {business_plan.title}")
        print(f"Found {sections.count()} sections")
        
        # Create context for the AI
        business_idea_title = business_plan.business_idea.title if business_plan.business_idea else "No specific business idea"

        context = {
            "business_plan": {
                "title": business_plan.title,
                "business_idea": business_idea_title,
                "sections": [
                    {
                        "title": section.title,
                        "content": section.content
                    } for section in sections if section.content
                ]
            }
        }
        
        # Create the prompt for Gemini
        prompt = f"""
        You are an expert business consultant reviewing a business plan. Analyze the following business plan and provide constructive feedback.

        Business Plan: {business_plan.title}
        Business Idea: {business_idea_title}
        
        Here are the sections of the business plan:
        
        {json.dumps(context["business_plan"]["sections"], indent=2)}
        
        For each section, provide:
        1. Strengths: What is well done in this section
        2. Areas for improvement: What could be improved or is missing
        3. Suggestions: Specific suggestions for improving the section
        
        Also provide overall feedback on the business plan as a whole.
        
        Format your response as a JSON object with the following structure:
        {{
            "overall_feedback": {{
                "strengths": ["Strength 1", "Strength 2", ...],
                "areas_for_improvement": ["Area 1", "Area 2", ...],
                "suggestions": ["Suggestion 1", "Suggestion 2", ...],
                "completion_score": 0-100
            }},
            "section_feedback": {{
                "section_title_1": {{
                    "strengths": ["Strength 1", "Strength 2", ...],
                    "areas_for_improvement": ["Area 1", "Area 2", ...],
                    "suggestions": ["Suggestion 1", "Suggestion 2", ...],
                    "completion_score": 0-100
                }},
                ...
            }}
        }}
        """
        
        # Generate feedback using centralized Gemini config
        print("Sending request to Gemini API...")
        response = generate_gemini_content(prompt)

        if not response:
            print("No response from Gemini API")
            return {
                "overall_feedback": {
                    "strengths": ["Business plan structure is in place"],
                    "areas_for_improvement": ["AI analysis unavailable"],
                    "suggestions": ["Please try again later"],
                    "completion_score": 50
                },
                "section_feedback": {}
            }

        # Parse the response
        response_text = response
        print(f"Received response from Gemini API (length: {len(response_text)})")
        
        # Extract JSON from the response
        if '```json' in response_text:
            json_start = response_text.find('```json') + 7
            json_end = response_text.find('```', json_start)
            json_str = response_text[json_start:json_end].strip()
        elif '{' in response_text and '}' in response_text:
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            json_str = response_text[json_start:json_end].strip()
        else:
            json_str = response_text
            
        # Parse the JSON
        try:
            feedback = json.loads(json_str)
        except json.JSONDecodeError as json_error:
            print(f"JSON parsing error: {json_error}")
            print(f"Response text: {response_text}")
            # Return a fallback response
            feedback = {
                "overall_feedback": {
                    "strengths": ["Business plan structure is in place"],
                    "areas_for_improvement": ["Content could be more detailed", "Consider adding more specific data"],
                    "suggestions": ["Expand on market research", "Include financial projections", "Add competitive analysis"],
                    "completion_score": 60
                },
                "section_feedback": {}
            }

        return feedback

    except BusinessPlan.DoesNotExist:
        print(f"Business plan with ID {business_plan_id} not found")
        return None
    except Exception as e:
        print(f"Error analyzing business plan: {e}")
        # Return a fallback response instead of None
        return {
            "overall_feedback": {
                "strengths": ["Business plan exists and has basic structure"],
                "areas_for_improvement": ["Analysis could not be completed due to technical issues"],
                "suggestions": ["Please try again later", "Ensure all sections have content"],
                "completion_score": 50
            },
            "section_feedback": {}
        }
