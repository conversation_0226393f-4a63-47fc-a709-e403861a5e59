import React from 'react';
import { useTranslation } from 'react-i18next';
import { RTLText } from '../common';
import { EventList, CreateEventSection } from './components';
import { useEventsWithQuery } from './hooks/useEventsWithQuery';
import { useEventForm } from './hooks';
import { formatEventData } from './utils';
const EventsWithQuery: React.FC = () => {
  const { t } = useTranslation();
  
  // Use custom hooks with React Query
  const {
    events,
    mockData,
    isLoading,
    isAttending,
    isUnattending,
    error,
    isAuthenticated,
    handleAttendEvent,
    handleUnattendEvent,
    loadMockData,
    refetch
  } = useEventsWithQuery({
    ordering: '-date',
    page_size: 6
  });

  const {
    showEventForm,
    eventFormData,
    handleInputChange,
    handleSubmitEvent,
    setShowEventForm
  } = useEventForm();

  // Handle showing sample data
  const handleShowSampleData = () => {
    loadMockData();
  };

  // Handle refresh
  const handleRefresh = () => {
    refetch();
  };

  return (
    <section id="events" className="py-20 bg-indigo-950/50">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-16">
          <RTLText as="h2" align="center" className="text-3xl md:text-4xl font-bold mb-4">
            {t('home.events.title')} <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-blue-400">{t('home.events.subtitle')}</span>
          </RTLText>
          <RTLText as="p" align="center" className="text-gray-300 text-lg">
            {t('home.events.description')}
          </RTLText>
        </div>

        {/* Event List */}
        <EventList
          events={events.length > 0 ? events : mockData}
          isLoading={isLoading || isAttending || isUnattending}
          error={error}
          onAttend={handleAttendEvent}
          onUnattend={handleUnattendEvent}
          onShowSampleData={handleShowSampleData}
          onRefresh={handleRefresh}
        />

        {/* Create Event Section */}
        <CreateEventSection
          isAuthenticated={isAuthenticated}
          showEventForm={showEventForm}
          formData={eventFormData}
          onInputChange={handleInputChange}
          onSubmit={handleSubmitEvent}
          onShowForm={() => setShowEventForm(true)}
          onHideForm={() => setShowEventForm(false)}
        />
      </div>
    </section>
  );
};

export default EventsWithQuery;
