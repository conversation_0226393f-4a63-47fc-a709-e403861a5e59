"""
System Logs Views
API views for system logs management
"""

from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from django.db.models import Q, Count
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from django.http import HttpResponse
import csv
import io

from .models_logs import SystemLog
from .serializers_logs import (
    SystemLogSerializer, SystemLogCreateSerializer, 
    LogStatsSerializer, LogFilterSerializer
)


class IsAdminUser(permissions.BasePermission):
    """
    Custom permission to only allow admin users to access logs.
    """
    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated and request.user.is_staff


class LogPagination(PageNumberPagination):
    """Custom pagination for logs"""
    page_size = 50
    page_size_query_param = 'page_size'
    max_page_size = 100


class SystemLogListView(generics.ListAPIView):
    """
    List system logs with filtering and pagination
    """
    serializer_class = SystemLogSerializer
    permission_classes = [IsAdminUser]
    pagination_class = LogPagination
    
    def get_queryset(self):
        queryset = SystemLog.objects.select_related('user').all()
        
        # Apply filters
        level = self.request.query_params.get('level')
        if level and level != 'all':
            queryset = queryset.filter(level=level)
        
        category = self.request.query_params.get('category')
        if category and category != 'all':
            queryset = queryset.filter(category=category)
        
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(message__icontains=search) |
                Q(details__icontains=search) |
                Q(source__icontains=search) |
                Q(user__username__icontains=search) |
                Q(user__email__icontains=search)
            )
        
        start_date = self.request.query_params.get('start_date')
        if start_date:
            queryset = queryset.filter(timestamp__gte=start_date)
        
        end_date = self.request.query_params.get('end_date')
        if end_date:
            queryset = queryset.filter(timestamp__lte=end_date)
        
        user_id = self.request.query_params.get('user_id')
        if user_id:
            queryset = queryset.filter(user_id=user_id)
        
        source = self.request.query_params.get('source')
        if source:
            queryset = queryset.filter(source__icontains=source)
        
        return queryset


class SystemLogCreateView(generics.CreateAPIView):
    """
    Create a new system log entry
    """
    serializer_class = SystemLogCreateSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def perform_create(self, serializer):
        # Add request metadata
        serializer.save(
            ip_address=self.get_client_ip(),
            user_agent=self.request.META.get('HTTP_USER_AGENT', ''),
            request_id=self.request.META.get('HTTP_X_REQUEST_ID', '')
        )
    
    def get_client_ip(self):
        """Get client IP address"""
        x_forwarded_for = self.request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = self.request.META.get('REMOTE_ADDR')
        return ip


class SystemLogDetailView(generics.RetrieveAPIView):
    """
    Retrieve a specific system log entry
    """
    queryset = SystemLog.objects.select_related('user').all()
    serializer_class = SystemLogSerializer
    permission_classes = [IsAdminUser]


@api_view(['GET'])
@permission_classes([IsAdminUser])
def log_stats(request):
    """
    Get system log statistics
    """
    # Get date range for last hour
    one_hour_ago = timezone.now() - timedelta(hours=1)
    
    # Basic counts
    total_logs = SystemLog.objects.count()
    error_count = SystemLog.objects.filter(level='error').count()
    warning_count = SystemLog.objects.filter(level='warning').count()
    info_count = SystemLog.objects.filter(level='info').count()
    success_count = SystemLog.objects.filter(level='success').count()
    last_hour_count = SystemLog.objects.filter(timestamp__gte=one_hour_ago).count()
    
    # Category breakdown
    categories = dict(
        SystemLog.objects.values('category')
        .annotate(count=Count('category'))
        .values_list('category', 'count')
    )
    
    # Level breakdown
    levels = dict(
        SystemLog.objects.values('level')
        .annotate(count=Count('level'))
        .values_list('level', 'count')
    )
    
    # Recent errors (last 10)
    recent_errors = SystemLog.objects.filter(level='error').order_by('-timestamp')[:10]
    recent_errors_serialized = SystemLogSerializer(recent_errors, many=True).data
    
    stats = {
        'total_logs': total_logs,
        'error_count': error_count,
        'warning_count': warning_count,
        'info_count': info_count,
        'success_count': success_count,
        'last_hour_count': last_hour_count,
        'categories': categories,
        'levels': levels,
        'recent_errors': recent_errors_serialized
    }
    
    serializer = LogStatsSerializer(stats)
    return Response(serializer.data)


@api_view(['GET'])
@permission_classes([IsAdminUser])
def export_logs(request):
    """
    Export logs to CSV
    """
    # Apply same filters as list view
    queryset = SystemLog.objects.select_related('user').all()
    
    level = request.query_params.get('level')
    if level and level != 'all':
        queryset = queryset.filter(level=level)
    
    category = request.query_params.get('category')
    if category and category != 'all':
        queryset = queryset.filter(category=category)
    
    search = request.query_params.get('search')
    if search:
        queryset = queryset.filter(
            Q(message__icontains=search) |
            Q(details__icontains=search) |
            Q(source__icontains=search)
        )
    
    # Limit to last 10000 records for performance
    queryset = queryset[:10000]
    
    # Create CSV response
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="system_logs_{timezone.now().strftime("%Y%m%d_%H%M%S")}.csv"'
    
    writer = csv.writer(response)
    writer.writerow([
        'Timestamp', 'Level', 'Category', 'Message', 'Details', 
        'Source', 'User', 'IP Address', 'User Agent'
    ])
    
    for log in queryset:
        writer.writerow([
            log.timestamp.isoformat(),
            log.level,
            log.category,
            log.message,
            log.details or '',
            log.source or '',
            log.user.username if log.user else '',
            log.ip_address or '',
            log.user_agent or ''
        ])
    
    return response


@api_view(['DELETE'])
@permission_classes([IsAdminUser])
def cleanup_logs(request):
    """
    Clean up old logs (older than specified days)
    """
    days = request.data.get('days', 30)
    if days < 1:
        return Response(
            {'error': 'Days must be at least 1'}, 
            status=status.HTTP_400_BAD_REQUEST
        )
    
    cutoff_date = timezone.now() - timedelta(days=days)
    deleted_count, _ = SystemLog.objects.filter(timestamp__lt=cutoff_date).delete()
    
    # Log the cleanup action
    SystemLog.log_info(
        message=f"Cleaned up {deleted_count} log entries older than {days} days",
        category='maintenance',
        user=request.user,
        source='log_cleanup'
    )
    
    return Response({
        'message': f'Deleted {deleted_count} log entries',
        'deleted_count': deleted_count
    })


@api_view(['GET'])
@permission_classes([IsAdminUser])
def log_categories(request):
    """
    Get available log categories
    """
    categories = [choice[0] for choice in SystemLog.CATEGORIES]
    return Response({'categories': categories})


@api_view(['GET'])
@permission_classes([IsAdminUser])
def log_levels(request):
    """
    Get available log levels
    """
    levels = [choice[0] for choice in SystemLog.LOG_LEVELS]
    return Response({'levels': levels})
