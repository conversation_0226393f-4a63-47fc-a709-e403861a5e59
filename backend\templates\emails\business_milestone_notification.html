<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Business Milestone Update</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #6366f1;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 5px 5px 0 0;
        }
        .content {
            background-color: #f9fafb;
            padding: 20px;
            border-radius: 0 0 5px 5px;
            border: 1px solid #e5e7eb;
            border-top: none;
        }
        .milestone-details {
            background-color: white;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #e5e7eb;
        }
        .milestone-details h3 {
            margin-top: 0;
            color: #6366f1;
        }
        .detail-row {
            margin-bottom: 10px;
        }
        .detail-label {
            font-weight: bold;
            color: #6b7280;
        }
        .button {
            display: inline-block;
            background-color: #6366f1;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .action-box {
            background-color: #e0e7ff;
            border-left: 4px solid #6366f1;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 0 5px 5px 0;
        }
        .priority-high {
            color: #ef4444;
            font-weight: bold;
        }
        .priority-medium {
            color: #f59e0b;
            font-weight: bold;
        }
        .priority-low {
            color: #10b981;
            font-weight: bold;
        }
        .priority-critical {
            color: #dc2626;
            font-weight: bold;
            text-transform: uppercase;
        }
        .footer {
            margin-top: 20px;
            text-align: center;
            color: #6b7280;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Business Milestone {{ action|title }}</h1>
        </div>
        <div class="content">
            <div class="action-box">
                <h3 style="margin-top: 0;">A milestone has been {{ action }} for your business idea</h3>
                <p>This milestone is part of your business development journey and will help track progress towards your goals.</p>
            </div>
            
            <div class="milestone-details">
                <h3>{{ milestone.title }}</h3>
                
                <div class="detail-row">
                    <span class="detail-label">Business Idea:</span> {{ business_idea.title }}
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">Description:</span>
                    <p>{{ milestone.description }}</p>
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">Due Date:</span> {{ due_date }}
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">Status:</span> {{ status }}
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">Priority:</span> 
                    {% if priority == 'High' %}
                        <span class="priority-high">{{ priority }}</span>
                    {% elif priority == 'Medium' %}
                        <span class="priority-medium">{{ priority }}</span>
                    {% elif priority == 'Low' %}
                        <span class="priority-low">{{ priority }}</span>
                    {% elif priority == 'Critical' %}
                        <span class="priority-critical">{{ priority }}</span>
                    {% else %}
                        {{ priority }}
                    {% endif %}
                </div>
                
                {% if milestone.assigned_to %}
                <div class="detail-row">
                    <span class="detail-label">Assigned To:</span> {{ milestone.assigned_to.get_full_name|default:milestone.assigned_to.username }}
                </div>
                {% endif %}
            </div>
            
            <p>Milestones help you track progress and stay on schedule with your business development. Make sure to:</p>
            <ul>
                <li>Break down this milestone into smaller tasks if needed</li>
                <li>Update the status as you make progress</li>
                <li>Reach out for help if you encounter obstacles</li>
                <li>Document your progress with regular updates</li>
            </ul>
            
            <a href="http://localhost:5173/dashboard/incubator/ideas/{{ business_idea.id }}/milestones/{{ milestone.id }}" class="button">View Milestone Details</a>
            
            <p>Keep up the great work on your entrepreneurial journey!</p>
            
            <p>Best regards,<br>Yasmeen AI Team</p>
        </div>
        <div class="footer">
            <p>This is an automated message from Yasmeen AI. Please do not reply to this email.</p>
        </div>
    </div>
</body>
</html>
