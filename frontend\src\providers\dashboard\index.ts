/**
 * Dashboard Providers Index
 * Export all dashboard providers and related utilities
 */

// Role-specific providers
export { default as UserDashboardProvider, useUserDashboard } from './UserDashboardProvider';
export { default as AdminDashboardProvider, useAdminDashboard } from './AdminDashboardProvider';
export { default as SuperAdminDashboardProvider, useSuperAdminDashboard } from './SuperAdminDashboardProvider';
export { default as ModeratorDashboardProvider, useModeratorDashboard } from './ModeratorDashboardProvider';

// Factory and unified providers
export { 
  default as DashboardProviderFactory,
  GlobalDashboardProvider,
  useDashboardData,
  useUnifiedDashboardData
} from './DashboardProviderFactory';

// Re-export types for convenience
export * from '../../types/dashboard';
export * from '../../utils/dashboardUtils';
