/**
 * Dashboard Consolidation Tests
 * Tests for the unified dashboard architecture and role-specific functionality
 */

import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import { I18nextProvider } from 'react-i18next';
import i18n from '../../i18n';

// Import components to test
import { UnifiedDashboard } from '../../components/dashboard/unified';
import { DashboardProviderFactory } from '../../providers/dashboard';
import { useUnifiedDashboardData } from '../../hooks/useUnifiedDashboardData';
import { getDashboardRole } from '../../utils/dashboardUtils';

// Mock store setup
const createMockStore = (userRole: string) => {
  return configureStore({
    reducer: {
      auth: (state = { user: { id: '1', username: 'testuser', role: userRole } }) => state,
    },
  });
};

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode; userRole: string }> = ({ 
  children, 
  userRole 
}) => {
  const store = createMockStore(userRole);
  
  return (
    <Provider store={store}>
      <BrowserRouter>
        <I18nextProvider i18n={i18n}>
          {children}
        </I18nextProvider>
      </BrowserRouter>
    </Provider>
  );
};

// Mock the dashboard data service
jest.mock('../../services/dashboardDataService', () => ({
  dashboardDataService: {
    getDashboardData: jest.fn().mockResolvedValue({
      stats: [
        {
          id: 'test_stat',
          title: 'Test Stat',
          value: 42,
          icon: () => null,
          color: 'bg-blue-600/30',
          change: 5.2,
          changeType: 'increase'
        }
      ],
      quickActions: [
        {
          id: 'test_action',
          title: 'Test Action',
          description: 'Test action description',
          icon: () => null,
          href: '/test',
          color: 'bg-blue-600/20'
        }
      ],
      additionalData: {}
    }),
    clearCache: jest.fn(),
    clearAllCache: jest.fn()
  }
}));

describe('Dashboard Consolidation', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Role Detection', () => {
    test('should correctly identify user role', () => {
      const userRole = getDashboardRole({ id: '1', username: 'user', role: 'user' } as any);
      expect(userRole).toBe('user');
    });

    test('should correctly identify admin role', () => {
      const adminRole = getDashboardRole({ id: '1', username: 'admin', role: 'admin' } as any);
      expect(adminRole).toBe('admin');
    });

    test('should correctly identify super admin role', () => {
      const superAdminRole = getDashboardRole({ id: '1', username: 'superadmin', role: 'super_admin' } as any);
      expect(superAdminRole).toBe('super_admin');
    });

    test('should correctly identify moderator role', () => {
      const moderatorRole = getDashboardRole({ id: '1', username: 'moderator', role: 'moderator' } as any);
      expect(moderatorRole).toBe('moderator');
    });

    test('should default to user role for null user', () => {
      const defaultRole = getDashboardRole(null);
      expect(defaultRole).toBe('user');
    });
  });

  describe('UnifiedDashboard Component', () => {
    test('should render user dashboard correctly', async () => {
      render(
        <TestWrapper userRole="user">
          <DashboardProviderFactory>
            <UnifiedDashboard role="user" />
          </DashboardProviderFactory>
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText(/welcome/i)).toBeInTheDocument();
      });
    });

    test('should render admin dashboard correctly', async () => {
      render(
        <TestWrapper userRole="admin">
          <DashboardProviderFactory>
            <UnifiedDashboard role="admin" />
          </DashboardProviderFactory>
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText(/admin/i)).toBeInTheDocument();
      });
    });

    test('should render super admin dashboard correctly', async () => {
      render(
        <TestWrapper userRole="super_admin">
          <DashboardProviderFactory>
            <UnifiedDashboard role="super_admin" />
          </DashboardProviderFactory>
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText(/super admin/i)).toBeInTheDocument();
      });
    });

    test('should render moderator dashboard correctly', async () => {
      render(
        <TestWrapper userRole="moderator">
          <DashboardProviderFactory>
            <UnifiedDashboard role="moderator" />
          </DashboardProviderFactory>
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText(/moderator/i)).toBeInTheDocument();
      });
    });
  });

  describe('Dashboard Data Hook', () => {
    test('should fetch and return dashboard data', async () => {
      const TestComponent = () => {
        const { stats, quickActions, loading, error } = useUnifiedDashboardData();
        
        if (loading) return <div>Loading...</div>;
        if (error) return <div>Error: {error}</div>;
        
        return (
          <div>
            <div data-testid="stats-count">{stats.length}</div>
            <div data-testid="actions-count">{quickActions.length}</div>
          </div>
        );
      };

      render(
        <TestWrapper userRole="user">
          <TestComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('stats-count')).toHaveTextContent('1');
        expect(screen.getByTestId('actions-count')).toHaveTextContent('1');
      });
    });

    test('should handle refresh data functionality', async () => {
      const TestComponent = () => {
        const { refreshData, loading } = useUnifiedDashboardData();
        
        return (
          <div>
            <button onClick={refreshData} disabled={loading}>
              Refresh
            </button>
            {loading && <div>Loading...</div>}
          </div>
        );
      };

      render(
        <TestWrapper userRole="user">
          <TestComponent />
        </TestWrapper>
      );

      const refreshButton = screen.getByText('Refresh');
      fireEvent.click(refreshButton);

      await waitFor(() => {
        expect(refreshButton).not.toBeDisabled();
      });
    });
  });

  describe('Role-Specific Content', () => {
    test('should show user-specific quick actions', async () => {
      const TestComponent = () => {
        const { quickActions } = useUnifiedDashboardData({ role: 'user' });
        
        return (
          <div>
            {quickActions.map(action => (
              <div key={action.id} data-testid={`action-${action.id}`}>
                {action.title}
              </div>
            ))}
          </div>
        );
      };

      render(
        <TestWrapper userRole="user">
          <TestComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('action-test_action')).toBeInTheDocument();
      });
    });

    test('should show admin-specific stats', async () => {
      const TestComponent = () => {
        const { stats } = useUnifiedDashboardData({ role: 'admin' });
        
        return (
          <div>
            {stats.map(stat => (
              <div key={stat.id} data-testid={`stat-${stat.id}`}>
                {stat.title}: {stat.value}
              </div>
            ))}
          </div>
        );
      };

      render(
        <TestWrapper userRole="admin">
          <TestComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('stat-test_stat')).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling', () => {
    test('should handle data fetching errors gracefully', async () => {
      // Mock error response
      const mockError = new Error('Network error');
      jest.mocked(require('../../services/dashboardDataService').dashboardDataService.getDashboardData)
        .mockRejectedValueOnce(mockError);

      const TestComponent = () => {
        const { error, loading } = useUnifiedDashboardData();
        
        if (loading) return <div>Loading...</div>;
        if (error) return <div data-testid="error">Error: {error}</div>;
        
        return <div>Success</div>;
      };

      render(
        <TestWrapper userRole="user">
          <TestComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('error')).toBeInTheDocument();
      });
    });
  });

  describe('Performance', () => {
    test('should cache dashboard data appropriately', async () => {
      const mockGetDashboardData = jest.mocked(
        require('../../services/dashboardDataService').dashboardDataService.getDashboardData
      );

      const TestComponent = () => {
        const { refreshData } = useUnifiedDashboardData();
        
        return (
          <button onClick={() => refreshData()}>
            Refresh
          </button>
        );
      };

      render(
        <TestWrapper userRole="user">
          <TestComponent />
        </TestWrapper>
      );

      // Initial call
      await waitFor(() => {
        expect(mockGetDashboardData).toHaveBeenCalledTimes(1);
      });

      // Refresh call
      fireEvent.click(screen.getByText('Refresh'));
      
      await waitFor(() => {
        expect(mockGetDashboardData).toHaveBeenCalledTimes(2);
      });
    });
  });
});

describe('Integration Tests', () => {
  test('should maintain functionality across role switches', async () => {
    const TestComponent = ({ role }: { role: string }) => {
      const { stats, quickActions } = useUnifiedDashboardData({ role: role as any });
      
      return (
        <div>
          <div data-testid="role">{role}</div>
          <div data-testid="stats-count">{stats.length}</div>
          <div data-testid="actions-count">{quickActions.length}</div>
        </div>
      );
    };

    const { rerender } = render(
      <TestWrapper userRole="user">
        <TestComponent role="user" />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByTestId('role')).toHaveTextContent('user');
    });

    rerender(
      <TestWrapper userRole="admin">
        <TestComponent role="admin" />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByTestId('role')).toHaveTextContent('admin');
    });
  });
});
