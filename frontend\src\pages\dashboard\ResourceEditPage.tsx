import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Save, Loader2, FileText, Video, BookOpen, Link as LinkIcon, Download } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import AuthenticatedLayout from '../../components/layout/AuthenticatedLayout';
import { Resource, resourcesAPI } from '../../services/api';
import { useAppSelector } from '../../store/hooks';
import { RTLText, RTLFlex } from '../../components/rtl';

const ResourceEditPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { language, isRTL } = useLanguage();
  const { user } = useAppSelector(state => state.auth);

  const [resource, setResource] = useState<Resource | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    resource_type: 'article',
    url: '',
    file: null as File | null,
  });

  const resourceTypes = [
    { value: 'article', label: t('resources.types.article', 'Article'), icon: FileText },
    { value: 'video', label: t('resources.types.video', 'Video'), icon: Video },
    { value: 'document', label: t('resources.types.document', 'Document'), icon: BookOpen },
    { value: 'link', label: t('resources.types.link', 'Link'), icon: LinkIcon },
    { value: 'tool', label: t('resources.types.tool', 'Tool'), icon: Download },
  ];

  useEffect(() => {
    if (id) {
      fetchResource();
    }
  }, [id]);

  const fetchResource = async () => {
    if (!id) return;
    
    setLoading(true);
    try {
      const resourceData = await resourcesAPI.getResource(parseInt(id));
      
      // Check if user owns this resource
      if (resourceData.author.id !== user?.id && !user?.is_staff && !user?.is_superuser) {
        setError(t('resources.errors.notOwner', 'You can only edit your own resources'));
        return;
      }

      setResource(resourceData);
      setFormData({
        title: resourceData.title,
        description: resourceData.description,
        resource_type: resourceData.resource_type,
        url: resourceData.url || '',
        file: null, // Don't pre-populate file
      });
    } catch (error) {
      console.error('Error fetching resource:', error);
      setError(t('resources.errors.failedToLoad', 'Failed to load resource'));
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    if (type === 'file') {
      const fileInput = e.target as HTMLInputElement;
      setFormData(prev => ({
        ...prev,
        [name]: fileInput.files?.[0] || null
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!id || !resource) return;

    setSaving(true);
    setError(null);

    try {
      const resourceData = new FormData();
      resourceData.append('title', formData.title);
      resourceData.append('description', formData.description);
      resourceData.append('resource_type', formData.resource_type);
      
      if (formData.url) {
        resourceData.append('url', formData.url);
      }
      
      if (formData.file) {
        resourceData.append('file', formData.file);
      }

      await resourcesAPI.updateResource(parseInt(id), resourceData as any);
      setSuccess(t('resources.success.updated', 'Resource updated successfully'));
      
      // Navigate back after success
      setTimeout(() => {
        navigate('/dashboard/my-resources');
      }, 1500);
    } catch (error) {
      console.error('Error updating resource:', error);
      setError(t('resources.errors.failedToUpdate', 'Failed to update resource'));
    } finally {
      setSaving(false);
    }
  };

  const getResourceIcon = (type: string) => {
    const resourceType = resourceTypes.find(rt => rt.value === type);
    const IconComponent = resourceType?.icon || FileText;
    return <IconComponent size={20} />;
  };

  if (loading) {
    return (
      <AuthenticatedLayout>
      <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="flex justify-center items-center py-12">
          <div className="flex items-center space-x-3">
            <Loader2 className="w-6 h-6 animate-spin" />
            <span>{t('common.loading', 'Loading...')}</span>
          </div>
        </div>
                </div>
        </div>
      </div>
    </AuthenticatedLayout>
    );
  }

  if (error && !resource) {
    return (
      <AuthenticatedLayout>
        <div className="text-center py-12">
          <div className="text-red-400 mb-4">{error}</div>
          <button
            onClick={() => navigate('/dashboard/my-resources')}
            className="px-4 py-2 bg-purple-600 rounded-lg hover:bg-purple-700 transition-colors"
          >
            {t('common.goBack', 'Go Back')}
          </button>
        </div>
      </AuthenticatedLayout>
    );
  }

  return (
    <AuthenticatedLayout>
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className={`flex items-center justify-between mb-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`flex items-center space-x-4 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
            <button
              onClick={() => navigate('/dashboard/my-resources')}
              className="p-2 rounded-lg hover:bg-indigo-800/50 transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <div>
              <RTLText as="h1" className="text-2xl font-bold">
                {t('resources.editResource', 'Edit Resource')}
              </RTLText>
              <RTLText as="div" className="text-gray-400 mt-1">
                {t('resources.updateYourResource', 'Update your shared resource')}
              </RTLText>
            </div>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSave} className="space-y-6">
          <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
            {/* Resource Type Display */}
            <div className="mb-6 p-4 bg-indigo-950/50 rounded-lg border border-indigo-800">
              <RTLFlex align="center">
                <div className="p-2 bg-indigo-900/50 rounded-lg mr-3">
                  {getResourceIcon(formData.resource_type)}
                </div>
                <div>
                  <RTLText as="div" className="text-sm text-gray-400">
                    {t('resources.currentType', 'Current Resource Type')}
                  </RTLText>
                  <RTLText as="div" className="font-medium">
                    {resourceTypes.find(rt => rt.value === formData.resource_type)?.label}
                  </RTLText>
                </div>
              </RTLFlex>
            </div>

            {/* Title */}
            <div className="mb-6">
              <label className="block text-gray-300 mb-2 font-medium">
                {t('resources.title', 'Title')} *
              </label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                className="w-full px-4 py-3 bg-indigo-900/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:border-purple-500 transition-colors"
                placeholder={t('resources.enterTitle', 'Enter resource title')}
                required
                dir={language === 'ar' ? 'rtl' : 'ltr'}
              />
            </div>

            {/* Description */}
            <div className="mb-6">
              <label className="block text-gray-300 mb-2 font-medium">
                {t('resources.description', 'Description')} *
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={4}
                className="w-full px-4 py-3 bg-indigo-900/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:border-purple-500 transition-colors resize-none"
                placeholder={t('resources.enterDescription', 'Describe your resource')}
                required
                dir={language === 'ar' ? 'rtl' : 'ltr'}
              />
            </div>

            {/* Resource Type */}
            <div className="mb-6">
              <label className="block text-gray-300 mb-2 font-medium">
                {t('resources.type', 'Resource Type')} *
              </label>
              <select
                name="resource_type"
                value={formData.resource_type}
                onChange={handleInputChange}
                className="w-full px-4 py-3 bg-indigo-900/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:border-purple-500 transition-colors"
                required
              >
                {resourceTypes.map(type => (
                  <option key={type.value} value={type.value}>{type.label}</option>
                ))}
              </select>
            </div>

            {/* URL */}
            <div className="mb-6">
              <label className="block text-gray-300 mb-2 font-medium">
                {t('resources.url', 'URL')} (optional)
              </label>
              <input
                type="url"
                name="url"
                value={formData.url}
                onChange={handleInputChange}
                className="w-full px-4 py-3 bg-indigo-900/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:border-purple-500 transition-colors"
                placeholder="https://..."
              />
              {resource?.url && (
                <div className="mt-2 text-sm text-gray-400">
                  {t('resources.currentUrl', 'Current URL')}: 
                  <a 
                    href={resource.url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-blue-400 hover:text-blue-300 ml-1"
                  >
                    {resource.url}
                  </a>
                </div>
              )}
            </div>

            {/* File Upload */}
            <div className="mb-6">
              <label className="block text-gray-300 mb-2 font-medium">
                {t('resources.file', 'File')} (optional)
              </label>
              <input
                type="file"
                name="file"
                onChange={handleInputChange}
                className="w-full px-4 py-3 bg-indigo-900/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:border-purple-500 transition-colors"
                accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif,.mp4,.mp3"
              />
              <div className="mt-2 text-sm text-gray-400">
                {t('resources.fileNote', 'Upload a new file to replace the current one (if any)')}
              </div>
              {resource?.file && (
                <div className="mt-2 text-sm text-gray-400">
                  {t('resources.currentFile', 'Current file')}: 
                  <a 
                    href={resource.file} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-blue-400 hover:text-blue-300 ml-1"
                  >
                    {t('resources.viewFile', 'View current file')}
                  </a>
                </div>
              )}
            </div>
          </div>

          {/* Error/Success Messages */}
          {error && (
            <div className="p-4 bg-red-900/50 border border-red-800 rounded-lg text-red-200">
              {error}
            </div>
          )}

          {success && (
            <div className="p-4 bg-green-900/50 border border-green-800 rounded-lg text-green-200">
              {success}
            </div>
          )}

          {/* Action Buttons */}
          <div className={`flex justify-end space-x-4 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
            <button
              type="button"
              onClick={() => navigate('/dashboard/my-resources')}
              className="px-6 py-3 bg-gray-700 hover:bg-gray-600 rounded-lg font-medium transition-colors"
              disabled={saving}
            >
              {t('common.cancel', 'Cancel')}
            </button>
            <RTLFlex
              as="button"
              type="submit"
              className="px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={saving}
              align="center"
            >
              {saving ? (
                <>
                  <Loader2 className={`w-5 h-5 animate-spin ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                  {t('common.saving', 'Saving...')}
                </>
              ) : (
                <>
                  <Save className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                  {t('common.save', 'Save Changes')}
                </>
              )}
            </RTLFlex>
          </div>
        </form>
      </div>
    </AuthenticatedLayout>
  );
};

export default ResourceEditPage;
