"""
Email template utilities for handling multilingual email templates.
This module provides functions for selecting the appropriate language template
based on user preferences and rendering email templates with the correct language.
"""

import os
from django.conf import settings
from django.template.loader import render_to_string
from django.utils.translation import activate, get_language


def get_user_language(user):
    """
    Get the preferred language for a user.
    
    Args:
        user: User instance
        
    Returns:
        str: Language code (e.g., 'en', 'ar')
    """
    # If user has a language preference, use it
    if hasattr(user, 'profile') and hasattr(user.profile, 'language'):
        return user.profile.language
    
    # Default to English
    return 'en'


def render_email_template(template_name, context, language=None):
    """
    Render an email template in the specified language.
    
    Args:
        template_name: Base name of the template (without language suffix)
        context: Context data for the template
        language: Language code (e.g., 'en', 'ar')
        
    Returns:
        tuple: (subject, html_content, text_content)
    """
    # Save current language
    current_language = get_language()
    
    try:
        # Activate the specified language
        if language:
            activate(language)
        
        # Check if language-specific template exists
        template_path = f"emails/{template_name}"
        if language:
            lang_template_path = f"emails/{template_name}_{language}.html"
            if os.path.exists(os.path.join(settings.TEMPLATES[0]['DIRS'][0], lang_template_path)):
                template_path = lang_template_path
        
        # Render HTML content
        html_content = render_to_string(f"{template_path}.html", context)
        
        # Render text content if available
        text_content = None
        try:
            text_content = render_to_string(f"{template_path}.txt", context)
        except:
            # If text template doesn't exist, use HTML content
            pass
        
        # Get subject from context or use default
        subject = context.get('subject', 'Yasmeen AI')
        
        return subject, html_content, text_content
    
    finally:
        # Restore original language
        activate(current_language)


def get_email_context(template_name, context, user=None):
    """
    Prepare context for email templates with language-specific translations.
    
    Args:
        template_name: Name of the template
        context: Base context data
        user: User instance (optional)
        
    Returns:
        dict: Enhanced context with translations
    """
    # Determine language
    language = 'en'
    if user:
        language = get_user_language(user)
    
    # Add language to context
    enhanced_context = context.copy()
    enhanced_context['language'] = language
    enhanced_context['dir'] = 'rtl' if language == 'ar' else 'ltr'
    
    # Add common translations
    translations = get_email_translations(template_name, language)
    enhanced_context.update(translations)
    
    # Add site URL
    enhanced_context['site_url'] = settings.SITE_URL
    
    return enhanced_context


def get_email_translations(template_name, language):
    """
    Get translations for email templates based on template name and language.
    
    Args:
        template_name: Name of the template
        language: Language code
        
    Returns:
        dict: Translations for the template
    """
    # Common translations for all templates
    common_translations = {
        'en': {
            'site_name': 'Yasmeen AI',
            'footer_text': 'This is an automated message from Yasmeen AI. Please do not reply to this email.',
            'copyright': '© 2023 Yasmeen AI. All rights reserved.',
        },
        'ar': {
            'site_name': 'ياسمين للذكاء الاصطناعي',
            'footer_text': 'هذه رسالة آلية من ياسمين للذكاء الاصطناعي. يرجى عدم الرد على هذا البريد الإلكتروني.',
            'copyright': '© 2023 ياسمين للذكاء الاصطناعي. جميع الحقوق محفوظة.',
        }
    }
    
    # Template-specific translations
    template_translations = {
        'welcome': {
            'en': {
                'subject': 'Welcome to Yasmeen AI',
                'title': 'Welcome to Yasmeen AI',
                'greeting': 'Hello',
                'thank_you': 'Thank you for joining Yasmeen AI. We\'re excited to have you on board and can\'t wait to help you on your entrepreneurial journey.',
                'getting_started': 'Getting Started',
                'things_to_do': 'Here are a few things you can do to get started with Yasmeen AI:',
                'complete_profile': 'Complete Your Profile',
                'complete_profile_desc': 'Add more details to your profile to help us personalize your experience and connect you with the right resources.',
                'update_profile': 'Update Profile',
                'explore_dashboard': 'Explore the Dashboard',
                'explore_dashboard_desc': 'Familiarize yourself with the dashboard and discover all the tools and resources available to you.',
                'go_to_dashboard': 'Go to Dashboard',
                'key_features': 'Key Features',
                'key_features_desc': 'Yasmeen AI offers a variety of features to help you develop and grow your business ideas:',
            },
            'ar': {
                'subject': 'مرحباً بك في ياسمين للذكاء الاصطناعي',
                'title': 'مرحباً بك في ياسمين للذكاء الاصطناعي',
                'greeting': 'مرحباً',
                'thank_you': 'شكراً لانضمامك إلى ياسمين للذكاء الاصطناعي. نحن متحمسون لوجودك معنا ولا نطيق الانتظار لمساعدتك في رحلتك الريادية.',
                'getting_started': 'البدء',
                'things_to_do': 'إليك بعض الأشياء التي يمكنك القيام بها للبدء مع ياسمين للذكاء الاصطناعي:',
                'complete_profile': 'أكمل ملفك الشخصي',
                'complete_profile_desc': 'أضف المزيد من التفاصيل إلى ملفك الشخصي لمساعدتنا في تخصيص تجربتك وربطك بالموارد المناسبة.',
                'update_profile': 'تحديث الملف الشخصي',
                'explore_dashboard': 'استكشف لوحة التحكم',
                'explore_dashboard_desc': 'تعرف على لوحة التحكم واكتشف جميع الأدوات والموارد المتاحة لك.',
                'go_to_dashboard': 'الذهاب إلى لوحة التحكم',
                'key_features': 'الميزات الرئيسية',
                'key_features_desc': 'يقدم ياسمين للذكاء الاصطناعي مجموعة متنوعة من الميزات لمساعدتك في تطوير وتنمية أفكار عملك:',
            }
        },
        # Add more template-specific translations as needed
    }
    
    # Get translations for the specified template and language
    translations = common_translations.get(language, common_translations['en']).copy()
    
    if template_name in template_translations:
        template_lang_translations = template_translations[template_name].get(
            language, template_translations[template_name]['en']
        )
        translations.update(template_lang_translations)
    
    return translations
