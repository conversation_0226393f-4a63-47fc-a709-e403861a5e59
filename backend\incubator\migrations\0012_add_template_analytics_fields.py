# Generated migration to add analytics fields to BusinessPlanTemplate

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('incubator', '0011_alter_businessplan_unique_together_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='businessplantemplate',
            name='difficulty_level',
            field=models.CharField(choices=[('beginner', 'Beginner'), ('intermediate', 'Intermediate'), ('advanced', 'Advanced')], default='intermediate', max_length=20),
        ),
        migrations.AddField(
            model_name='businessplantemplate',
            name='estimated_time',
            field=models.PositiveIntegerField(default=8, help_text='Estimated completion time in hours'),
        ),
        migrations.AddField(
            model_name='businessplantemplate',
            name='usage_count',
            field=models.PositiveIntegerField(default=0, help_text='Total number of times this template has been used'),
        ),
        migrations.AddField(
            model_name='businessplantemplate',
            name='rating',
            field=models.FloatField(default=0.0, help_text='Average user rating (0-5)'),
        ),
        migrations.AddField(
            model_name='businessplantemplate',
            name='rating_count',
            field=models.PositiveIntegerField(default=0, help_text='Total number of ratings'),
        ),
        migrations.AddField(
            model_name='businessplantemplate',
            name='completion_rate',
            field=models.FloatField(default=0.0, help_text='Percentage of users who complete this template'),
        ),
        migrations.AddField(
            model_name='businessplantemplate',
            name='is_premium',
            field=models.BooleanField(default=False, help_text='Whether this is a premium template'),
        ),
        migrations.AddField(
            model_name='businessplantemplate',
            name='is_featured',
            field=models.BooleanField(default=False, help_text='Whether this template is featured'),
        ),
        migrations.AddField(
            model_name='businessplantemplate',
            name='is_bestseller',
            field=models.BooleanField(default=False, help_text='Whether this template is a bestseller'),
        ),
        migrations.AddField(
            model_name='businessplantemplate',
            name='tags',
            field=models.JSONField(default=list, help_text='List of tags for this template'),
        ),
        migrations.AddField(
            model_name='businessplantemplate',
            name='icon',
            field=models.CharField(default='FileText', help_text='Icon name for this template', max_length=50),
        ),
        migrations.AddField(
            model_name='businessplantemplate',
            name='author',
            field=models.ForeignKey(blank=True, help_text='Template author', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='authored_templates', to=settings.AUTH_USER_MODEL),
        ),
    ]
