"""
Template Analytics and Tracking Models
Track template usage, performance, and user interactions
"""

from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from .models_business_plan import BusinessPlanTemplate
import json


class TemplateUsageAnalytics(models.Model):
    """Track template usage and performance metrics"""
    
    template = models.ForeignKey(
        BusinessPlanTemplate, 
        on_delete=models.CASCADE,
        related_name='usage_analytics'
    )
    user = models.ForeignKey(
        User, 
        on_delete=models.CASCADE,
        null=True, 
        blank=True
    )
    
    # Usage tracking
    viewed_at = models.DateTimeField(auto_now_add=True)
    selected_at = models.DateTimeField(null=True, blank=True)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    # Interaction metrics
    time_spent_viewing = models.DurationField(null=True, blank=True)
    time_spent_working = models.DurationField(null=True, blank=True)
    sections_completed = models.IntegerField(default=0)
    total_sections = models.IntegerField(default=0)
    completion_percentage = models.FloatField(default=0.0)
    
    # User feedback
    rating = models.IntegerField(null=True, blank=True, choices=[
        (1, 'Very Poor'),
        (2, 'Poor'),
        (3, 'Average'),
        (4, 'Good'),
        (5, 'Excellent')
    ])
    feedback_text = models.TextField(blank=True)
    
    # Success metrics
    business_plan_published = models.BooleanField(default=False)
    funding_received = models.BooleanField(default=False)
    business_launched = models.BooleanField(default=False)
    
    # Metadata
    user_agent = models.TextField(blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    referrer = models.URLField(blank=True)
    
    class Meta:
        db_table = 'template_usage_analytics'
        indexes = [
            models.Index(fields=['template', 'viewed_at']),
            models.Index(fields=['user', 'viewed_at']),
            models.Index(fields=['completed_at']),
        ]
    
    def __str__(self):
        return f"Usage: {self.template.name} by {self.user.username if self.user else 'Anonymous'}"
    
    @property
    def is_completed(self):
        return self.completed_at is not None
    
    @property
    def success_score(self):
        """Calculate success score based on completion and outcomes"""
        score = 0
        if self.completion_percentage >= 100:
            score += 30
        elif self.completion_percentage >= 75:
            score += 20
        elif self.completion_percentage >= 50:
            score += 10
        
        if self.business_plan_published:
            score += 25
        if self.funding_received:
            score += 25
        if self.business_launched:
            score += 20
        
        return min(score, 100)


class TemplatePerformanceMetrics(models.Model):
    """Aggregated performance metrics for templates"""
    
    template = models.OneToOneField(
        BusinessPlanTemplate,
        on_delete=models.CASCADE,
        related_name='performance_metrics'
    )
    
    # Usage metrics
    total_views = models.IntegerField(default=0)
    total_selections = models.IntegerField(default=0)
    total_completions = models.IntegerField(default=0)
    unique_users = models.IntegerField(default=0)
    
    # Performance ratios
    selection_rate = models.FloatField(default=0.0)  # selections / views
    completion_rate = models.FloatField(default=0.0)  # completions / selections
    average_completion_time = models.DurationField(null=True, blank=True)
    
    # Quality metrics
    average_rating = models.FloatField(default=0.0)
    total_ratings = models.IntegerField(default=0)
    net_promoter_score = models.FloatField(default=0.0)
    
    # Success metrics
    business_plans_published = models.IntegerField(default=0)
    funding_success_rate = models.FloatField(default=0.0)
    business_launch_rate = models.FloatField(default=0.0)
    
    # Trend data (JSON field for storing time-series data)
    usage_trends = models.JSONField(default=dict, blank=True)
    performance_trends = models.JSONField(default=dict, blank=True)
    
    # Last updated
    last_updated = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'template_performance_metrics'
    
    def __str__(self):
        return f"Metrics: {self.template.name}"
    
    def update_metrics(self):
        """Update all metrics based on current usage data"""
        analytics = TemplateUsageAnalytics.objects.filter(template=self.template)
        
        # Basic counts
        self.total_views = analytics.count()
        self.total_selections = analytics.filter(selected_at__isnull=False).count()
        self.total_completions = analytics.filter(completed_at__isnull=False).count()
        self.unique_users = analytics.values('user').distinct().count()
        
        # Rates
        self.selection_rate = (self.total_selections / self.total_views) * 100 if self.total_views > 0 else 0
        self.completion_rate = (self.total_completions / self.total_selections) * 100 if self.total_selections > 0 else 0
        
        # Quality metrics
        rated_analytics = analytics.filter(rating__isnull=False)
        if rated_analytics.exists():
            self.average_rating = rated_analytics.aggregate(avg_rating=models.Avg('rating'))['avg_rating']
            self.total_ratings = rated_analytics.count()
            
            # Calculate NPS (simplified)
            promoters = rated_analytics.filter(rating__gte=4).count()
            detractors = rated_analytics.filter(rating__lte=2).count()
            self.net_promoter_score = ((promoters - detractors) / self.total_ratings) * 100 if self.total_ratings > 0 else 0
        
        # Success metrics
        self.business_plans_published = analytics.filter(business_plan_published=True).count()
        self.funding_success_rate = (analytics.filter(funding_received=True).count() / self.total_completions) * 100 if self.total_completions > 0 else 0
        self.business_launch_rate = (analytics.filter(business_launched=True).count() / self.total_completions) * 100 if self.total_completions > 0 else 0
        
        self.save()


class TemplateSectionAnalytics(models.Model):
    """Track analytics for individual template sections"""
    
    template = models.ForeignKey(
        BusinessPlanTemplate,
        on_delete=models.CASCADE,
        related_name='section_analytics'
    )
    section_key = models.CharField(max_length=100)
    section_title = models.CharField(max_length=200)
    
    # Section performance
    total_views = models.IntegerField(default=0)
    total_completions = models.IntegerField(default=0)
    average_time_spent = models.DurationField(null=True, blank=True)
    completion_rate = models.FloatField(default=0.0)
    
    # User interaction
    ai_assistance_used = models.IntegerField(default=0)
    content_regenerated = models.IntegerField(default=0)
    section_customized = models.IntegerField(default=0)
    
    # Quality indicators
    average_content_length = models.IntegerField(default=0)
    user_satisfaction_score = models.FloatField(default=0.0)
    
    # Last updated
    last_updated = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'template_section_analytics'
        unique_together = ['template', 'section_key']
        indexes = [
            models.Index(fields=['template', 'completion_rate']),
            models.Index(fields=['section_key', 'completion_rate']),
        ]
    
    def __str__(self):
        return f"Section Analytics: {self.section_title} ({self.template.name})"


class UserTemplateInteraction(models.Model):
    """Track detailed user interactions with templates"""
    
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    template = models.ForeignKey(BusinessPlanTemplate, on_delete=models.CASCADE)
    session_id = models.CharField(max_length=100)
    
    # Interaction details
    action_type = models.CharField(max_length=50, choices=[
        ('view', 'View Template'),
        ('select', 'Select Template'),
        ('start', 'Start Working'),
        ('save_section', 'Save Section'),
        ('complete_section', 'Complete Section'),
        ('use_ai', 'Use AI Assistance'),
        ('customize', 'Customize Section'),
        ('export', 'Export Business Plan'),
        ('share', 'Share Business Plan'),
        ('rate', 'Rate Template'),
        ('feedback', 'Provide Feedback'),
    ])
    
    section_key = models.CharField(max_length=100, blank=True)
    action_data = models.JSONField(default=dict, blank=True)
    
    # Timing
    timestamp = models.DateTimeField(auto_now_add=True)
    duration = models.DurationField(null=True, blank=True)
    
    # Context
    device_type = models.CharField(max_length=20, blank=True)
    browser = models.CharField(max_length=50, blank=True)
    
    class Meta:
        db_table = 'user_template_interaction'
        indexes = [
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['template', 'action_type']),
            models.Index(fields=['session_id']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.action_type} - {self.template.name}"


class TemplateRecommendation(models.Model):
    """Store template recommendations for users"""
    
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    recommended_template = models.ForeignKey(
        BusinessPlanTemplate,
        on_delete=models.CASCADE,
        related_name='recommendations'
    )
    
    # Recommendation details
    recommendation_score = models.FloatField()
    recommendation_reason = models.TextField()
    recommendation_type = models.CharField(max_length=50, choices=[
        ('similar_users', 'Similar Users'),
        ('industry_match', 'Industry Match'),
        ('business_type', 'Business Type'),
        ('completion_rate', 'High Completion Rate'),
        ('success_rate', 'High Success Rate'),
        ('ai_generated', 'AI Generated'),
    ])
    
    # Interaction tracking
    shown_at = models.DateTimeField(auto_now_add=True)
    clicked_at = models.DateTimeField(null=True, blank=True)
    selected_at = models.DateTimeField(null=True, blank=True)
    dismissed_at = models.DateTimeField(null=True, blank=True)
    
    # Metadata
    recommendation_context = models.JSONField(default=dict, blank=True)
    
    class Meta:
        db_table = 'template_recommendation'
        unique_together = ['user', 'recommended_template']
        indexes = [
            models.Index(fields=['user', 'shown_at']),
            models.Index(fields=['recommendation_score']),
        ]
    
    def __str__(self):
        return f"Recommendation: {self.recommended_template.name} for {self.user.username}"
    
    @property
    def was_clicked(self):
        return self.clicked_at is not None
    
    @property
    def was_selected(self):
        return self.selected_at is not None
    
    @property
    def was_dismissed(self):
        return self.dismissed_at is not None


class TemplateABTest(models.Model):
    """A/B testing for template variations"""
    
    name = models.CharField(max_length=200)
    description = models.TextField()
    
    # Test configuration
    original_template = models.ForeignKey(
        BusinessPlanTemplate,
        on_delete=models.CASCADE,
        related_name='ab_tests_original'
    )
    variant_template = models.ForeignKey(
        BusinessPlanTemplate,
        on_delete=models.CASCADE,
        related_name='ab_tests_variant'
    )
    
    # Test parameters
    traffic_split = models.FloatField(default=50.0)  # Percentage for variant
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    is_active = models.BooleanField(default=True)
    
    # Success metrics to track
    primary_metric = models.CharField(max_length=50, choices=[
        ('completion_rate', 'Completion Rate'),
        ('selection_rate', 'Selection Rate'),
        ('user_rating', 'User Rating'),
        ('time_to_complete', 'Time to Complete'),
        ('business_success', 'Business Success Rate'),
    ])
    
    # Results
    original_metric_value = models.FloatField(null=True, blank=True)
    variant_metric_value = models.FloatField(null=True, blank=True)
    statistical_significance = models.FloatField(null=True, blank=True)
    winner = models.CharField(max_length=20, choices=[
        ('original', 'Original'),
        ('variant', 'Variant'),
        ('inconclusive', 'Inconclusive'),
    ], blank=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'template_ab_test'
    
    def __str__(self):
        return f"A/B Test: {self.name}"
    
    @property
    def is_running(self):
        now = timezone.now()
        return self.is_active and self.start_date <= now <= self.end_date
    
    def calculate_results(self):
        """Calculate A/B test results"""
        # This would implement statistical analysis
        # For now, just a placeholder
        pass
