import React, { useState } from 'react';
import { Trash2, Al<PERSON><PERSON>riangle } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../hooks/useLanguage';
import { useDeleteMentorProfile } from '../../../../hooks/useMentorship';
import { MentorProfile } from '../../../../services/incubatorApi';
import { Button } from '../../../ui';

interface MentorProfileDeleteModalProps {
  isOpen: boolean;
  onClose: () => void;
  profile: MentorProfile | null;
}

const MentorProfileDeleteModal: React.FC<MentorProfileDeleteModalProps> = ({
  isOpen,
  onClose,
  profile
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  const deleteProfile = useDeleteMentorProfile();
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    if (!profile) return;

    setIsDeleting(true);
    try {
      await deleteProfile.mutateAsync(profile.id);
      onClose();
    } catch (error) {
      console.error('Failed to delete mentor profile:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  if (!isOpen || !profile) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-indigo-900/90 rounded-xl shadow-lg w-full max-w-md">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center gap-3 mb-4">
            <div className="w-12 h-12 rounded-full bg-red-500/20 flex items-center justify-center">
              <AlertTriangle className="w-6 h-6 text-red-400" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">
                {t('mentor.deleteProfile', 'Delete Mentor Profile')}
              </h2>
              <p className="text-gray-400 text-sm">
                {t('common.actionCannotBeUndone', 'This action cannot be undone')}
              </p>
            </div>
          </div>

          {/* Content */}
          <div className="mb-6">
            <p className="text-gray-300 mb-4">
              {t('mentor.deleteConfirmation', 'Are you sure you want to delete this mentor profile?')}
            </p>
            
            <div className="bg-indigo-800/30 rounded-lg p-4 border border-indigo-700/50">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-purple-700 flex items-center justify-center text-white text-sm font-bold">
                  {profile.user.first_name.charAt(0)}{profile.user.last_name.charAt(0)}
                </div>
                <div>
                  <div className="text-white font-medium">
                    {profile.user.first_name} {profile.user.last_name}
                  </div>
                  <div className="text-gray-400 text-sm">
                    {profile.position} at {profile.company}
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-4 p-3 bg-red-900/20 border border-red-800/50 rounded-lg">
              <p className="text-red-300 text-sm">
                <strong>{t('common.warning', 'Warning')}:</strong> {t('mentor.deleteWarning', 'Deleting this profile will also remove all associated mentorship matches and sessions.')}
              </p>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3">
            <Button
              type="button"
              onClick={onClose}
              variant="secondary"
              disabled={isDeleting}
            >
              {t('common.cancel', 'Cancel')}
            </Button>
            <Button
              type="button"
              onClick={handleDelete}
              variant="danger"
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  {t('common.deleting', 'Deleting...')}
                </>
              ) : (
                <>
                  <Trash2 className="w-4 h-4 mr-2" />
                  {t('common.delete', 'Delete')}
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MentorProfileDeleteModal;
