import React from 'react';
import { ArrowUp, ArrowDown } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../hooks/useLanguage';

interface StatCardProps {
  title: string;
  value: number | string;
  icon: React.ReactNode;
  change?: number;
  suffix?: string;
}

/**
 * A card component for displaying statistics with optional change indicator
 */
const StatCard: React.FC<StatCardProps> = ({ title, value, icon, change, suffix  }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  return (
    <div className="glass-morphism rounded-xl p-4 backdrop-blur-sm shadow-lg border border-glass-border">
      <div className={`flex justify-between items-start ${isRTL ? "flex-row-reverse" : ""}`}>
        <div>
          <p className="text-sm text-glass-secondary">
            {title}
          </p>
          <div className="text-2xl font-bold mt-1 text-glass-primary">
            {typeof value === 'number' && !isNaN(value) ? value.toLocaleString() : value}
            {suffix && <span className={`text-sm ml-1 ${isRTL ? "space-x-reverse" : ""}`}>{suffix}</span>}
          </div>
          {change !== undefined && (
            <div className={`flex items-center mt-2 text-sm ${change >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              {change >= 0 ? <ArrowUp size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> : <ArrowDown size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />}
              <span>{Math.abs(change).toFixed(1)}%</span>
            </div>
          )}
        </div>
        <div className="p-2 rounded-lg glass-light">
          {icon}
        </div>
      </div>
    </div>
  );
};

export default StatCard;
