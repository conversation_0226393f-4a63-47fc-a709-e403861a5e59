/**
 * Super Admin Dashboard Content Provider
 * Provides super admin-specific dashboard data, stats, and actions
 */

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useAppSelector } from '../../store/hooks';
import { DashboardStat, DashboardQuickAction } from '../../types/dashboard';
import {
  Users,
  Shield,
  Activity,
  DollarSign,
  Settings,
  BarChart3,
  Crown,
  AlertTriangle,
  Database,
  Server,
  Lock,
  Monitor
} from 'lucide-react';

interface SuperAdminDashboardData {
  stats: DashboardStat[];
  quickActions: DashboardQuickAction[];
  systemMetrics: any;
  securityAlerts: any[];
  loading: boolean;
  error: string | null;
  refreshData: () => Promise<void>;
}

const SuperAdminDashboardContext = createContext<SuperAdminDashboardData | null>(null);

interface SuperAdminDashboardProviderProps {
  children: React.ReactNode;
}

export const SuperAdminDashboardProvider: React.FC<SuperAdminDashboardProviderProps> = ({ children }) => {
  const { user } = useAppSelector(state => state.auth);
  const [stats, setStats] = useState<DashboardStat[]>([]);
  const [quickActions, setQuickActions] = useState<DashboardQuickAction[]>([]);
  const [systemMetrics, setSystemMetrics] = useState<any>(null);
  const [securityAlerts, setSecurityAlerts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch super admin dashboard data
  const fetchDashboardData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Mock super admin stats - highest level system metrics
      const superAdminStats: DashboardStat[] = [
        {
          id: 'total_users',
          title: 'Total Users',
          value: 12547,
          icon: Users,
          color: 'bg-blue-600/30',
          change: 8.5,
          changeType: 'increase',
          description: 'All registered users across the platform'
        },
        {
          id: 'system_health',
          title: 'System Health',
          value: '99.8%',
          icon: Shield,
          color: 'bg-green-600/30',
          change: 0.2,
          changeType: 'increase',
          description: 'Overall system uptime and performance'
        },
        {
          id: 'active_sessions',
          title: 'Active Sessions',
          value: 1247,
          icon: Activity,
          color: 'bg-purple-600/30',
          change: -2.1,
          changeType: 'decrease',
          description: 'Current active user sessions'
        },
        {
          id: 'revenue',
          title: 'Monthly Revenue',
          value: 125000,
          icon: DollarSign,
          color: 'bg-emerald-600/30',
          change: 15.3,
          changeType: 'increase',
          suffix: 'USD',
          description: 'Platform revenue this month'
        }
      ];

      // Mock super admin quick actions - system-level controls
      const superAdminQuickActions: DashboardQuickAction[] = [
        {
          id: 'system_management',
          title: 'System Management',
          description: 'Manage core system settings and configuration',
          icon: Settings,
          href: '/super_admin/system-management',
          color: 'bg-red-600/20 hover:bg-red-600/30 border-red-500/30',
        },
        {
          id: 'user_management',
          title: 'Global User Management',
          description: 'Manage all users and administrative permissions',
          icon: Users,
          href: '/super_admin/users',
          color: 'bg-blue-600/20 hover:bg-blue-600/30 border-blue-500/30',
        },
        {
          id: 'system_monitoring',
          title: 'System Monitoring',
          description: 'Monitor system health, performance, and metrics',
          icon: Monitor,
          href: '/super_admin/monitoring',
          color: 'bg-green-600/20 hover:bg-green-600/30 border-green-500/30',
        },
        {
          id: 'security_center',
          title: 'Security Center',
          description: 'Manage security settings, logs, and alerts',
          icon: Lock,
          href: '/super_admin/security',
          color: 'bg-purple-600/20 hover:bg-purple-600/30 border-purple-500/30',
          badge: 3, // Mock security alerts count
        },
        {
          id: 'database_management',
          title: 'Database Management',
          description: 'Manage database operations and backups',
          icon: Database,
          href: '/super_admin/database',
          color: 'bg-orange-600/20 hover:bg-orange-600/30 border-orange-500/30',
        },
        {
          id: 'server_management',
          title: 'Server Management',
          description: 'Manage server infrastructure and deployment',
          icon: Server,
          href: '/super_admin/servers',
          color: 'bg-gray-600/20 hover:bg-gray-600/30 border-gray-500/30',
        }
      ];

      // Mock system metrics
      const mockSystemMetrics = {
        cpuUsage: 45.2,
        memoryUsage: 67.8,
        diskUsage: 34.1,
        networkTraffic: 'Normal',
        databaseConnections: 156,
        activeProcesses: 89,
        uptime: '45 days, 12 hours',
        lastBackup: '2 hours ago'
      };

      // Mock security alerts
      const mockSecurityAlerts = [
        {
          id: '1',
          type: 'failed_login_attempts',
          severity: 'medium',
          description: 'Multiple failed login attempts detected from IP *************',
          timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
          status: 'active'
        },
        {
          id: '2',
          type: 'suspicious_activity',
          severity: 'high',
          description: 'Unusual API access pattern detected',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
          status: 'investigating'
        },
        {
          id: '3',
          type: 'system_update',
          severity: 'low',
          description: 'Security patch applied successfully',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6).toISOString(), // 6 hours ago
          status: 'resolved'
        }
      ];

      setStats(superAdminStats);
      setQuickActions(superAdminQuickActions);
      setSystemMetrics(mockSystemMetrics);
      setSecurityAlerts(mockSecurityAlerts);

    } catch (err) {
      console.error('Error fetching super admin dashboard data:', err);
      setError('Failed to load super admin dashboard data');
      
      // Fallback to basic mock data
      const fallbackStats: DashboardStat[] = [
        {
          id: 'total_users',
          title: 'Total Users',
          value: 0,
          icon: Users,
          color: 'bg-blue-600/30',
          change: 0,
          changeType: 'neutral',
          description: 'All registered users'
        },
        {
          id: 'system_health',
          title: 'System Health',
          value: 'Unknown',
          icon: Shield,
          color: 'bg-gray-600/30',
          change: 0,
          changeType: 'neutral',
          description: 'System status unavailable'
        }
      ];

      const fallbackQuickActions: DashboardQuickAction[] = [
        {
          id: 'system_management',
          title: 'System Management',
          description: 'Manage core system settings',
          icon: Settings,
          href: '/super_admin/system-management',
          color: 'bg-red-600/20 hover:bg-red-600/30 border-red-500/30',
        },
        {
          id: 'user_management',
          title: 'User Management',
          description: 'Manage all users and permissions',
          icon: Users,
          href: '/super_admin/users',
          color: 'bg-blue-600/20 hover:bg-blue-600/30 border-blue-500/30',
        }
      ];

      setStats(fallbackStats);
      setQuickActions(fallbackQuickActions);
      setSystemMetrics({ status: 'unknown' });
      setSecurityAlerts([]);
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Initial data fetch
  useEffect(() => {
    if (user) {
      fetchDashboardData();
    }
  }, [user, fetchDashboardData]);

  const contextValue: SuperAdminDashboardData = {
    stats,
    quickActions,
    systemMetrics,
    securityAlerts,
    loading,
    error,
    refreshData: fetchDashboardData,
  };

  return (
    <SuperAdminDashboardContext.Provider value={contextValue}>
      {children}
    </SuperAdminDashboardContext.Provider>
  );
};

// Hook to use super admin dashboard data
export const useSuperAdminDashboard = (): SuperAdminDashboardData => {
  const context = useContext(SuperAdminDashboardContext);
  if (!context) {
    throw new Error('useSuperAdminDashboard must be used within a SuperAdminDashboardProvider');
  }
  return context;
};

export default SuperAdminDashboardProvider;
