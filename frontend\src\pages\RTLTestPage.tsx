import React from 'react';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../store/hooks';
import { useLanguage } from '../hooks/useLanguage';
import {
  LanguageSwitcher,
  RTLIcon,
  RTLText,
  RTLFlex
} from '../components/common';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import {
  Globe,
  ArrowLeft,
  ArrowRight,
  AlignLeft,
  AlignRight,
  LayoutDashboard,
  Settings,
  User,
  Calendar,
  MessageSquare,
  FileText
} from 'lucide-react';

const RTLTestPage: React.FC = () => {
  const { t } = useTranslation();
  const { language, direction } = useAppSelector(state => state.language);
  const { isRTL } = useLanguage();

  // Helper functions for RTL classes
  const getMarginClass = (direction: 'right' | 'left', size: number) => {
    if (direction === 'right') {
      return isRTL ? `ml-${size}` : `mr-${size}`;
    } else {
      return isRTL ? `mr-${size}` : `ml-${size}`;
    }
  };

  const getPaddingClass = (direction: 'right' | 'left', size: number) => {
    if (direction === 'right') {
      return isRTL ? `pl-${size}` : `pr-${size}`;
    } else {
      return isRTL ? `pr-${size}` : `pl-${size}`;
    }
  };

  const getBorderClass = (direction: 'right' | 'left') => {
    if (direction === 'right') {
      return isRTL ? 'border-l' : 'border-r';
    } else {
      return isRTL ? 'border-r' : 'border-l';
    }
  };

  const getTextAlignClass = (alignment: 'start' | 'end' | 'center') => {
    switch (alignment) {
      case 'start':
        return isRTL ? 'text-right' : 'text-left';
      case 'end':
        return isRTL ? 'text-left' : 'text-right';
      case 'center':
        return 'text-center';
      default:
        return 'text-left';
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <main className="flex-grow container mx-auto px-4 py-16 mt-8">
        <div className="max-w-4xl mx-auto">
          <RTLFlex as="h1" className="text-3xl font-bold mb-8" align="center">
            <RTLIcon icon={Globe} size={28} className={getMarginClass('right', 3)} />
            <span>{t('app.name')} - {t('i18n.rtlTestPage')}</span>
          </RTLFlex>

          <div className="glass-light rounded-lg p-6 mb-8 border">
            <h2 className="text-xl font-semibold mb-4 text-glass-primary">{t('i18n.settings')}</h2>
            <div className="space-y-2">
              <div className="text-glass-secondary">
                <strong>{t('i18n.currentLanguage')}:</strong> {language} ({t('nav.language')})
              </div>
              <p className="text-glass-secondary">
                <strong>{t('i18n.direction')}:</strong> {direction}
              </p>
              <p className="text-glass-secondary">
                <strong>{t('i18n.isRTL')}:</strong> {isRTL ? t('i18n.yes') : t('i18n.no')}
              </p>
              <div className="mt-4">
                <LanguageSwitcher variant="dropdown" />
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            <div className="glass-light rounded-lg p-6 border">
              <h2 className="text-xl font-semibold mb-4 text-glass-primary">{t('i18n.textAlignment')}</h2>
              <RTLText align="start" className="mb-2 text-glass-secondary">
                This text is aligned to the start (right in RTL, left in LTR).
              </RTLText>
              <RTLText align="end" className="mb-2 text-glass-secondary">
                This text is aligned to the end (left in RTL, right in LTR).
              </RTLText>
              <RTLText align="center" className="mb-2 text-glass-secondary">
                This text is always centered.
              </RTLText>
            </div>

            <div className="glass-light rounded-lg p-6 border">
              <h2 className="text-xl font-semibold mb-4 text-glass-primary">{t('i18n.marginPadding')}</h2>
              <div className={`flex items-center mb-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <span className={`${getPaddingClass('right', 4)} ${getBorderClass('right')} text-glass-secondary`}>
                  {t('i18n.paddingExample')} {isRTL ? 'Left' : 'Right'}
                </span>
                <span className="text-glass-secondary">Content</span>
              </div>
              <div className="flex items-center mb-2">
                <span className={`${getMarginClass('right', 4)} text-glass-secondary`}>
                  {t('i18n.marginExample')} {isRTL ? 'Left' : 'Right'}
                </span>
                <span className="text-glass-secondary">Content</span>
              </div>
            </div>
          </div>

          <div className="glass-light rounded-lg p-6 mb-8 border">
            <h2 className="text-xl font-semibold mb-4 text-glass-primary">{t('i18n.directionalIcons')}</h2>
            <div className="mb-4 text-glass-secondary">{t('i18n.iconFlip')}</div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <RTLFlex align="center">
                <RTLIcon icon={ArrowLeft} size={20} flipInRTL={true} className={getMarginClass('right', 2)} />
                <span className="text-glass-secondary">Arrow Left</span>
              </RTLFlex>
              <RTLFlex align="center">
                <RTLIcon icon={ArrowRight} size={20} flipInRTL={true} className={getMarginClass('right', 2)} />
                <span className="text-glass-secondary">Arrow Right</span>
              </RTLFlex>
              <RTLFlex align="center">
                <RTLIcon icon={AlignLeft} size={20} flipInRTL={true} className={getMarginClass('right', 2)} />
                <span className="text-glass-secondary">Align Left</span>
              </RTLFlex>
              <RTLFlex align="center">
                <RTLIcon icon={AlignRight} size={20} flipInRTL={true} className={getMarginClass('right', 2)} />
                <span className="text-glass-secondary">Align Right</span>
              </RTLFlex>
            </div>
          </div>

          <div className="glass-light rounded-lg p-6 border">
            <h2 className="text-xl font-semibold mb-4 text-glass-primary">{t('i18n.navigationExample')}</h2>
            <div className="flex flex-col space-y-2">
              <a
                href="#"
                className="p-2 rounded glass-light hover:glass-hover text-glass-primary"
              >
                <RTLFlex align="center">
                  <RTLIcon icon={LayoutDashboard} size={20} className={getMarginClass('right', 2)} />
                  <span>{t('i18n.dashboard')}</span>
                </RTLFlex>
              </a>
              <a
                href="#"
                className="p-2 rounded glass-light hover:glass-hover text-glass-primary"
              >
                <RTLFlex align="center">
                  <RTLIcon icon={User} size={20} className={getMarginClass('right', 2)} />
                  <span>{t('i18n.profile')}</span>
                </RTLFlex>
              </a>
              <a
                href="#"
                className="p-2 rounded glass-light hover:glass-hover text-glass-primary"
              >
                <RTLFlex align="center">
                  <RTLIcon icon={Calendar} size={20} className={getMarginClass('right', 2)} />
                  <span>{t('i18n.calendar')}</span>
                </RTLFlex>
              </a>
              <a
                href="#"
                className="p-2 rounded glass-light hover:glass-hover text-glass-primary"
              >
                <RTLFlex align="center">
                  <RTLIcon icon={MessageSquare} size={20} className={getMarginClass('right', 2)} />
                  <span>{t('i18n.messages')}</span>
                </RTLFlex>
              </a>
              <a
                href="#"
                className="p-2 rounded glass-light hover:glass-hover text-glass-primary"
              >
                <RTLFlex align="center">
                  <RTLIcon icon={FileText} size={20} className={getMarginClass('right', 2)} />
                  <span>{t('i18n.documents')}</span>
                </RTLFlex>
              </a>
              <a
                href="#"
                className="p-2 rounded glass-light hover:glass-hover text-glass-primary"
              >
                <RTLFlex align="center">
                  <RTLIcon icon={Settings} size={20} className={getMarginClass('right', 2)} />
                  <span>{t('i18n.settings')}</span>
                </RTLFlex>
              </a>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default RTLTestPage;
