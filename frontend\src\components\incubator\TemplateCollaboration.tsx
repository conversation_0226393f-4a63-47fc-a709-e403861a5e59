import React, { useState, useEffect } from 'react';
import {
  Users,
  UserPlus,
  MessageSquare,
  Edit,
  Eye,
  Clock,
  CheckCircle,
  AlertCircle,
  Share2,
  Crown,
  Mail,
  Bell,
  History,
  GitBranch,
  Merge
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { RTLText, RTLFlex } from '../common';

interface Collaborator {
  id: number;
  user: {
    id: number;
    username: string;
    first_name: string;
    last_name: string;
    email: string;
    avatar?: string;
  };
  role: 'owner' | 'editor' | 'viewer' | 'reviewer';
  permissions: string[];
  joined_at: string;
  last_active: string;
  status: 'active' | 'pending' | 'inactive';
}

interface TemplateVersion {
  id: number;
  version: string;
  created_by: {
    username: string;
    first_name: string;
    last_name: string;
  };
  created_at: string;
  changes_summary: string;
  is_current: boolean;
}

interface Comment {
  id: number;
  user: {
    username: string;
    first_name: string;
    last_name: string;
  };
  content: string;
  section_key?: string;
  created_at: string;
  replies: Comment[];
}

interface TemplateCollaborationProps {
  templateId: number;
  isOwner: boolean;
  onCollaboratorsChange?: (collaborators: Collaborator[]) => void;
}

export const TemplateCollaboration: React.FC<TemplateCollaborationProps> = ({ templateId,
  isOwner,
  onCollaboratorsChange
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [activeTab, setActiveTab] = useState<'collaborators' | 'comments' | 'versions'>('collaborators');
  const [collaborators, setCollaborators] = useState<Collaborator[]>([]);
  const [comments, setComments] = useState<Comment[]>([]);
  const [versions, setVersions] = useState<TemplateVersion[]>([]);
  const [loading, setLoading] = useState(false);
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState<'editor' | 'viewer' | 'reviewer'>('viewer');
  const [newComment, setNewComment] = useState('');

  useEffect(() => {
    fetchCollaborationData();
  }, [templateId]);

  const fetchCollaborationData = async () => {
    setLoading(true);
    try {
      if (!templateId) {
        setCollaborators([]);
        setComments([]);
        setVersions([]);
        return;
      }

      // Fetch real collaboration data from API
      const { customTemplatesAPI } = await import('../../services/templateCustomizationApi');

      // Get template collaborators
      const collaboratorsData = await customTemplatesAPI.getTemplateCollaborators?.(templateId) || [];

      // Get template comments/feedback
      const commentsData = await customTemplatesAPI.getTemplateComments?.(templateId) || [];

      // Get template versions/history
      const versionsData = await customTemplatesAPI.getTemplateVersions?.(templateId) || [];

      setCollaborators(collaboratorsData);
      setComments(commentsData);
      setVersions(versionsData);

      if (onCollaboratorsChange) {
        onCollaboratorsChange(collaboratorsData);
      }
    } catch (error) {
      console.error('Error fetching collaboration data:', error);
      // Set empty arrays on error
      setCollaborators([]);
      setComments([]);
      setVersions([]);
    } finally {
      setLoading(false);
    }
  };

  const handleInviteCollaborator = async () => {
    if (!inviteEmail.trim()) return;

    try {
      // Mock API call - replace with actual implementation
      console.log("Inviting collaborator:", { email: inviteEmail, role: inviteRole });
      setShowInviteModal(false);
      setInviteEmail('');
      setInviteRole('viewer');
      // Refresh collaborators
      fetchCollaborationData();
    } catch (error) {
      console.error('Error inviting collaborator:', error);
    }
  };

  const handleAddComment = async () => {
    if (!newComment.trim()) return;

    try {
      // Mock API call - replace with actual implementation
      console.log("Adding comment:", newComment);
      setNewComment('');
      // Refresh comments
      fetchCollaborationData();
    } catch (error) {
      console.error('Error adding comment:', error);
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'owner': return <Crown className="text-yellow-400" size={16} />;
      case 'editor': return <Edit className="text-blue-400" size={16} />;
      case 'reviewer': return <MessageSquare className="text-green-400" size={16} />;
      case 'viewer': return <Eye className="text-gray-400" size={16} />;
      default: return <Users className="text-gray-400" size={16} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-400';
      case 'pending': return 'text-yellow-400';
      case 'inactive': return 'text-gray-400';
      default: return 'text-gray-400';
    }
  };

  const renderCollaborators = () => (
    <div className="space-y-4">
      {/* Header */}
      <RTLFlex className="items-center justify-between">
        <RTLText as="h4" className="font-semibold">
          {t('collaboration.collaborators')} ({collaborators.length})
        </RTLText>

        {isOwner && (
          <button
            onClick={() => setShowInviteModal(true)}
            className={`px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg text-white transition-colors flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <UserPlus size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
            {t('collaboration.inviteCollaborator')}
          </button>
        )}
      </RTLFlex>

      {/* Collaborators List */}
      <div className="space-y-3">
        {collaborators.map((collaborator) => (
          <div
            key={collaborator.id}
            className="bg-gray-700/50 rounded-lg p-4 border border-gray-600"
          >
            <RTLFlex className="items-center justify-between">
              <RTLFlex className="items-center">
                <div className={`w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center text-white font-medium ${isRTL ? "flex-row-reverse" : ""}`}>
                  {collaborator.user.first_name[0]}{collaborator.user.last_name[0]}
                </div>

                <div className={isRTL ? 'mr-3' : 'ml-3'}>
                  <RTLFlex className="items-center">
                    <RTLText className="font-medium">
                      {collaborator.user.first_name} {collaborator.user.last_name}
                    </RTLText>
                    {getRoleIcon(collaborator.role)}
                  </RTLFlex>
                  <RTLText className="text-sm text-gray-400">
                    {collaborator.user.email}
                  </RTLText>
                </div>
              </RTLFlex>

              <div className="text-right">
                <RTLText className={`text-sm font-medium ${getStatusColor(collaborator.status)}`}>
                  {t(`collaboration.${collaborator.status}`)}
                </RTLText>
                <RTLText className="text-xs text-gray-400">
                  {t('collaboration.lastActive')}: {new Date(collaborator.last_active).toLocaleDateString()}
                </RTLText>
              </div>
            </RTLFlex>
          </div>
        ))}
      </div>
    </div>
  );

  const renderComments = () => (
    <div className="space-y-4">
      {/* Add Comment */}
      <div className="bg-gray-700/50 rounded-lg p-4 border border-gray-600">
        <textarea
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
          placeholder={t('collaboration.addComment')}
          className="w-full bg-gray-800 border border-gray-600 rounded-lg p-3 text-white placeholder-gray-400 resize-none"
          rows={3}
        />
        <RTLFlex className="items-center justify-end mt-3">
          <button
            onClick={handleAddComment}
            disabled={!newComment.trim()}
            className="px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed rounded-lg text-white transition-colors"
          >
            {t('collaboration.postComment')}
          </button>
        </RTLFlex>
      </div>

      {/* Comments List */}
      <div className="space-y-3">
        {comments.map((comment) => (
          <div
            key={comment.id}
            className="bg-gray-700/50 rounded-lg p-4 border border-gray-600"
          >
            <RTLFlex className="items-start justify-between mb-2">
              <RTLFlex className="items-center">
                <div className={`w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium ${isRTL ? "flex-row-reverse" : ""}`}>
                  {comment.user.first_name[0]}{comment.user.last_name[0]}
                </div>
                <div className={isRTL ? 'mr-3' : 'ml-3'}>
                  <RTLText className="font-medium text-sm">
                    {comment.user.first_name} {comment.user.last_name}
                  </RTLText>
                  <RTLText className="text-xs text-gray-400">
                    {new Date(comment.created_at).toLocaleString()}
                  </RTLText>
                </div>
              </RTLFlex>

              {comment.section_key && (
                <span className="px-2 py-1 bg-purple-600/20 text-purple-300 rounded text-xs">
                  {comment.section_key}
                </span>
              )}
            </RTLFlex>

            <RTLText className="text-gray-300">
              {comment.content}
            </RTLText>
          </div>
        ))}
      </div>
    </div>
  );

  const renderVersions = () => (
    <div className="space-y-4">
      <RTLText as="h4" className="font-semibold">
        {t('collaboration.versionHistory')}
      </RTLText>

      <div className="space-y-3">
        {versions.map((version) => (
          <div
            key={version.id}
            className={`bg-gray-700/50 rounded-lg p-4 border ${
              version.is_current ? 'border-purple-500' : 'border-gray-600'}
            }`}
          >
            <RTLFlex className="items-center justify-between mb-2">
              <RTLFlex className="items-center">
                <GitBranch className="text-purple-400" size={16} />
                <RTLText className={`font-medium ml-2 ${isRTL ? "space-x-reverse" : ""}`}>
                  {t('collaboration.version')} {version.version}
                </RTLText>
                {version.is_current && (
                  <span className={`px-2 py-1 bg-green-600/20 text-green-300 rounded text-xs ml-2 ${isRTL ? "space-x-reverse" : ""}`}>
                    {t('collaboration.current')}
                  </span>
                )}
              </RTLFlex>

              <RTLText className="text-sm text-gray-400">
                {new Date(version.created_at).toLocaleString()}
              </RTLText>
            </RTLFlex>

            <RTLText className="text-gray-300 text-sm mb-2">
              {version.changes_summary}
            </RTLText>

            <RTLText className="text-xs text-gray-400">
              {t('collaboration.by')} {version.created_by.first_name} {version.created_by.last_name}
            </RTLText>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Tabs */}
      <div className="border-b border-gray-700">
        <nav className={`flex space-x-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          {[
            { key: 'collaborators', label: t('collaboration.collaborators'), icon: Users },
            { key: 'comments', label: t('collaboration.comments'), icon: MessageSquare },
            { key: 'versions', label: t('collaboration.versions'), icon: History }
          ].map(({ key, label, icon: Icon }) => (
            <button
              key={key}
              onClick={() => setActiveTab(key as any)}
              className={`py-4 px-2 border-b-2 font-medium text-sm transition-colors flex items-center ${
                activeTab === key
                  ? 'border-purple-500 text-purple-400'
                  : 'border-transparent text-gray-400 hover:text-gray-300'}
              }`}
            >
              <Icon size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
              {label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div>
        {activeTab === 'collaborators' && renderCollaborators()}
        {activeTab === 'comments' && renderComments()}
        {activeTab === 'versions' && renderVersions()}
      </div>

      {/* Invite Modal */}
      {showInviteModal && (
        <div className={`fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="bg-gray-900 rounded-lg max-w-md w-full p-6">
            <RTLText as="h3" className="text-lg font-semibold mb-4">
              {t('collaboration.inviteCollaborator')}
            </RTLText>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  {t('collaboration.emailAddress')}
                </label>
                <input
                  type="email"
                  value={inviteEmail}
                  onChange={(e) => setInviteEmail(e.target.value)}
                  className="w-full bg-gray-800 border border-gray-600 rounded-lg p-3 text-white placeholder-gray-400"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  {t('collaboration.role')}
                </label>
                <select
                  value={inviteRole}
                  onChange={(e) => setInviteRole(e.target.value as any)}
                  className="w-full bg-gray-800 border border-gray-600 rounded-lg p-3 text-white"
                >
                  <option value="viewer">{t('collaboration.viewer')}</option>
                  <option value="reviewer">{t('collaboration.reviewer')}</option>
                  <option value="editor">{t('collaboration.editor')}</option>
                </select>
              </div>
            </div>

            <RTLFlex className="items-center justify-end gap-3 mt-6">
              <button
                onClick={() => setShowInviteModal(false)}
                className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
              >
                {t('common.cancel')}
              </button>
              <button
                onClick={handleInviteCollaborator}
                className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg text-white transition-colors"
              >
                {t('collaboration.sendInvite')}
              </button>
            </RTLFlex>
          </div>
        </div>
      )}
    </div>
  );
};
