"""
Universal Search API
Comprehensive search across all platform content types
"""

from django.db.models import Q, F
from django.contrib.postgres.search import SearchVector, SearchQuery, SearchRank
from django.conf import settings
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from typing import List, Dict, Any
import re
from datetime import datetime, timedelta

# Import models from different apps
from incubator.models import BusinessIdea
from forums.models import ForumThread, ForumPost
from users.models import User
from .models import SearchDocument

def detect_database_type():
    """Detect if we're using PostgreSQL or SQLite"""
    return 'postgresql' in settings.DATABASES['default']['ENGINE']

def calculate_relevance_score(query: str, text: str) -> float:
    """Calculate relevance score for search results"""
    if not query or not text:
        return 0.0
    
    query_lower = query.lower()
    text_lower = text.lower()
    
    # Exact match gets highest score
    if query_lower == text_lower:
        return 1.0
    
    # Title/beginning match gets high score
    if text_lower.startswith(query_lower):
        return 0.9
    
    # Contains query gets medium score
    if query_lower in text_lower:
        return 0.7
    
    # Word matching
    query_words = query_lower.split()
    text_words = text_lower.split()
    matching_words = len([word for word in query_words if any(word in text_word for text_word in text_words)])
    
    if matching_words > 0:
        return (matching_words / len(query_words)) * 0.6
    
    return 0.0

def search_business_ideas(query: str, filters: Dict[str, Any], limit: int = 20) -> List[Dict]:
    """Search business ideas"""
    try:
        queryset = BusinessIdea.objects.select_related('user').filter(
            moderation_status='approved'
        )
        
        # Apply date filter
        if filters.get('dateRange'):
            date_filter = filters['dateRange']
            if date_filter == 'today':
                queryset = queryset.filter(created_at__date=datetime.now().date())
            elif date_filter == 'week':
                queryset = queryset.filter(created_at__gte=datetime.now() - timedelta(days=7))
            elif date_filter == 'month':
                queryset = queryset.filter(created_at__gte=datetime.now() - timedelta(days=30))
            elif date_filter == 'year':
                queryset = queryset.filter(created_at__gte=datetime.now() - timedelta(days=365))
        
        # Apply author filter
        if filters.get('author'):
            queryset = queryset.filter(user__username__icontains=filters['author'])
        
        # Apply search query
        if detect_database_type():
            # PostgreSQL full-text search
            search_vector = SearchVector('title', weight='A') + SearchVector('description', weight='B')
            search_query = SearchQuery(query)
            queryset = queryset.annotate(
                rank=SearchRank(search_vector, search_query)
            ).filter(rank__gt=0).order_by('-rank')
        else:
            # SQLite basic search
            queryset = queryset.filter(
                Q(title__icontains=query) | 
                Q(description__icontains=query) |
                Q(problem_statement__icontains=query) |
                Q(solution_description__icontains=query)
            ).distinct()
        
        results = []
        for idea in queryset[:limit]:
            score = calculate_relevance_score(query, f"{idea.title} {idea.description}")
            results.append({
                'id': f'business-{idea.id}',
                'type': 'business',
                'title': idea.title,
                'description': idea.description[:200] + '...' if len(idea.description) > 200 else idea.description,
                'score': score,
                'metadata': {
                    'stage': idea.current_stage,
                    'author': idea.user.username if idea.user else 'Unknown',
                    'created_at': idea.created_at.isoformat(),
                    'url': f'/dashboard/business-ideas/{idea.id}'
                }
            })
        
        return results
    except Exception as e:
        print(f"Error searching business ideas: {e}")
        return []

def search_forum_content(query: str, filters: Dict[str, Any], limit: int = 20) -> List[Dict]:
    """Search forum threads and posts"""
    try:
        results = []
        
        # Search threads
        thread_queryset = ForumThread.objects.select_related('author', 'topic').filter(
            is_approved=True
        )
        
        # Apply filters
        if filters.get('dateRange'):
            date_filter = filters['dateRange']
            if date_filter == 'today':
                thread_queryset = thread_queryset.filter(created_at__date=datetime.now().date())
            elif date_filter == 'week':
                thread_queryset = thread_queryset.filter(created_at__gte=datetime.now() - timedelta(days=7))
            elif date_filter == 'month':
                thread_queryset = thread_queryset.filter(created_at__gte=datetime.now() - timedelta(days=30))
            elif date_filter == 'year':
                thread_queryset = thread_queryset.filter(created_at__gte=datetime.now() - timedelta(days=365))
        
        if filters.get('author'):
            thread_queryset = thread_queryset.filter(author__username__icontains=filters['author'])
        
        # Apply search query
        if detect_database_type():
            search_vector = SearchVector('title', weight='A') + SearchVector('content', weight='B')
            search_query = SearchQuery(query)
            thread_queryset = thread_queryset.annotate(
                rank=SearchRank(search_vector, search_query)
            ).filter(rank__gt=0).order_by('-rank')
        else:
            thread_queryset = thread_queryset.filter(
                Q(title__icontains=query) | 
                Q(content__icontains=query)
            ).distinct()
        
        for thread in thread_queryset[:limit//2]:
            score = calculate_relevance_score(query, f"{thread.title} {thread.content}")
            results.append({
                'id': f'forum-thread-{thread.id}',
                'type': 'forum',
                'title': thread.title,
                'description': thread.content[:200] + '...' if len(thread.content) > 200 else thread.content,
                'score': score,
                'metadata': {
                    'type': 'thread',
                    'author': thread.author.username if thread.author else 'Unknown',
                    'topic': thread.topic.title if thread.topic else 'General',
                    'replies': thread.replies.count(),
                    'created_at': thread.created_at.isoformat(),
                    'url': f'/forum/thread/{thread.id}'
                }
            })
        
        return results
    except Exception as e:
        print(f"Error searching forum content: {e}")
        return []

def search_users(query: str, filters: Dict[str, Any], limit: int = 10) -> List[Dict]:
    """Search user profiles"""
    try:
        queryset = User.objects.filter(is_active=True)
        
        # Apply search query
        queryset = queryset.filter(
            Q(username__icontains=query) |
            Q(first_name__icontains=query) |
            Q(last_name__icontains=query) |
            Q(bio__icontains=query)
        ).distinct()
        
        results = []
        for user in queryset[:limit]:
            score = calculate_relevance_score(query, f"{user.username} {user.first_name} {user.last_name} {user.bio or ''}")
            results.append({
                'id': f'user-{user.id}',
                'type': 'user',
                'title': user.username,
                'description': f"{user.first_name} {user.last_name}".strip() or user.bio or 'User profile',
                'score': score,
                'metadata': {
                    'email': user.email if user.email else None,
                    'full_name': f"{user.first_name} {user.last_name}".strip(),
                    'bio': user.bio,
                    'date_joined': user.date_joined.isoformat(),
                    'url': f'/profile/{user.username}'
                }
            })
        
        return results
    except Exception as e:
        print(f"Error searching users: {e}")
        return []

def search_resources(query: str, filters: Dict[str, Any], limit: int = 20) -> List[Dict]:
    """Search resources using SearchDocument model"""
    try:
        queryset = SearchDocument.objects.filter(content_type='resource')
        
        # Apply search query
        queryset = queryset.filter(
            Q(title__icontains=query) |
            Q(content__icontains=query)
        ).distinct()
        
        results = []
        for doc in queryset[:limit]:
            score = calculate_relevance_score(query, f"{doc.title} {doc.content}")
            results.append({
                'id': f'resource-{doc.object_id}',
                'type': 'resource',
                'title': doc.title,
                'description': doc.content[:200] + '...' if len(doc.content) > 200 else doc.content,
                'score': score,
                'metadata': {
                    'author': doc.author,
                    'created_at': doc.created_at.isoformat(),
                    'url': f'/resources/{doc.object_id}'
                }
            })
        
        return results
    except Exception as e:
        print(f"Error searching resources: {e}")
        return []

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def universal_search(request):
    """
    Universal search endpoint that searches across all content types
    """
    query = request.GET.get('q', '').strip()
    content_types = request.GET.get('types', '').split(',') if request.GET.get('types') else []
    date_range = request.GET.get('dateRange', '')
    author = request.GET.get('author', '')
    limit = min(int(request.GET.get('limit', 50)), 100)  # Max 100 results
    
    if not query:
        return Response({
            'error': 'Query parameter "q" is required'
        }, status=status.HTTP_400_BAD_REQUEST)
    
    if len(query) < 2:
        return Response({
            'error': 'Query must be at least 2 characters long'
        }, status=status.HTTP_400_BAD_REQUEST)
    
    # Prepare filters
    filters = {
        'dateRange': date_range,
        'author': author
    }
    
    # Determine which content types to search
    search_types = content_types if content_types and content_types != [''] else ['business', 'forum', 'resource', 'user']
    
    all_results = []
    
    # Search each content type
    if 'business' in search_types:
        business_results = search_business_ideas(query, filters, limit // 4)
        all_results.extend(business_results)
    
    if 'forum' in search_types:
        forum_results = search_forum_content(query, filters, limit // 4)
        all_results.extend(forum_results)
    
    if 'user' in search_types:
        user_results = search_users(query, filters, limit // 4)
        all_results.extend(user_results)
    
    if 'resource' in search_types:
        resource_results = search_resources(query, filters, limit // 4)
        all_results.extend(resource_results)
    
    # Sort by relevance score
    all_results.sort(key=lambda x: x['score'], reverse=True)
    
    # Limit total results
    all_results = all_results[:limit]
    
    return Response({
        'query': query,
        'total_results': len(all_results),
        'results': all_results,
        'search_types': search_types,
        'filters': filters
    })

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def search_suggestions(request):
    """
    Get search suggestions based on query
    """
    query = request.GET.get('q', '').strip()
    
    if len(query) < 2:
        return Response({'suggestions': []})
    
    suggestions = []
    
    # Get suggestions from business ideas
    try:
        business_titles = BusinessIdea.objects.filter(
            title__icontains=query,
            moderation_status='approved'
        ).values_list('title', flat=True)[:5]
        suggestions.extend(list(business_titles))
    except:
        pass
    
    # Get suggestions from forum threads
    try:
        forum_titles = ForumThread.objects.filter(
            title__icontains=query,
            is_approved=True
        ).values_list('title', flat=True)[:5]
        suggestions.extend(list(forum_titles))
    except:
        pass
    
    # Remove duplicates and limit
    suggestions = list(set(suggestions))[:10]
    
    return Response({'suggestions': suggestions})
