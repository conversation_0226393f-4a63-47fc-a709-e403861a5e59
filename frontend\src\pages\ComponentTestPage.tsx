/**
 * Component Test Page - Test new components without translation dependencies
 */

import React, { useState } from 'react';
import {
  TestTube,
  CheckCircle,
  XCircle,
  AlertCircle,
  Brain,
  Search,
  FileText,
  Zap,
  Mic,
  BarChart3,
  Activity,
  Lightbulb,
  Users,
  TrendingUp
} from 'lucide-react';
import PerformanceOptimizer from '../components/performance/PerformanceOptimizer';
import RealTimeAnalytics from '../components/analytics/RealTimeAnalytics';
import ArabicVoiceInput from '../components/voice/ArabicVoiceInput';
import BusinessIdeaGenerator from '../components/ai/BusinessIdeaGenerator';
import SmartCollaborationHub from '../components/collaboration/SmartCollaborationHub';
import MarketIntelligenceDashboard from '../components/market/MarketIntelligenceDashboard';

export const ComponentTestPage: React.FC = () => {
  const [currentTest, setCurrentTest] = useState<string>('performance');
  const [voiceTranscript, setVoiceTranscript] = useState<string>('');

  const tests = [
    { id: 'performance', name: 'Performance Optimizer', icon: Zap },
    { id: 'analytics', name: 'Real-Time Analytics', icon: BarChart3 },
    { id: 'voice', name: 'Arabic Voice Input', icon: Mic },
    { id: 'ideas', name: 'Business Idea Generator', icon: Lightbulb },
    { id: 'collaboration', name: 'Smart Collaboration', icon: Users },
    { id: 'market', name: 'Market Intelligence', icon: TrendingUp },
  ];

  const handleVoiceTranscript = (text: string, language: string) => {
    setVoiceTranscript(`[${language}] ${text}`);
    console.log('Voice transcript received:', { text, language });
  };

  const handleVoiceError = (error: string) => {
    console.error('Voice input error:', error);
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 text-white">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <TestTube className="text-purple-400" size={32} />
            <h1 className="text-3xl font-bold text-white ml-3">
              Component Test Suite
            </h1>
          </div>
          <p className="text-gray-300 max-w-2xl mx-auto">
            Test the new advanced components: Performance Optimizer, Real-Time Analytics, and Arabic Voice Input
          </p>
        </div>

        {/* Navigation */}
        <div className="flex justify-center mb-8">
          <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-2">
            <div className="flex gap-2">
              {tests.map((test) => {
                const Icon = test.icon;
                return (
                  <button
                    key={test.id}
                    onClick={() => setCurrentTest(test.id)}
                    className={`flex items-center px-4 py-2 rounded-lg transition-colors ${
                      currentTest === test.id
                        ? 'bg-purple-600 text-white'
                        : 'text-gray-300 hover:text-white hover:bg-gray-700/50'}
                    }`}
                  >
                    <Icon size={20} />
                    <span className="ml-2">{test.name}</span>
                  </button>
                );
              })}
            </div>
          </div>
        </div>

        {/* Test Content */}
        <div className="max-w-6xl mx-auto">
          {currentTest === 'performance' && (
            <div className="space-y-6">
              <div className="text-center mb-6">
                <h2 className="text-2xl font-bold text-white mb-2">Performance Optimizer Test</h2>
                <p className="text-gray-300">
                  Monitor real-time performance metrics and get optimization suggestions
                </p>
              </div>
              <PerformanceOptimizer />
            </div>
          )}

          {currentTest === 'analytics' && (
            <div className="space-y-6">
              <div className="text-center mb-6">
                <h2 className="text-2xl font-bold text-white mb-2">Real-Time Analytics Test</h2>
                <p className="text-gray-300">
                  View live analytics data with AI-powered insights
                </p>
              </div>
              <RealTimeAnalytics />
            </div>
          )}

          {currentTest === 'voice' && (
            <div className="space-y-6">
              <div className="text-center mb-6">
                <h2 className="text-2xl font-bold text-white mb-2">Arabic Voice Input Test</h2>
                <p className="text-gray-300">
                  Test voice-to-text functionality with Arabic language support
                </p>
              </div>

              <ArabicVoiceInput
                onTranscript={handleVoiceTranscript}
                onError={handleVoiceError}
                supportedLanguages={['ar-SA', 'ar-EG', 'ar-AE', 'en-US']}
                defaultLanguage="ar-SA"
                placeholder="Click the microphone to start speaking in Arabic or English..."
              />

              {/* Voice Transcript Display */}
              {voiceTranscript && (
                <div className="bg-green-900/20 border border-green-500/50 rounded-lg p-4 mt-4">
                  <div className="flex items-center mb-2">
                    <CheckCircle className="text-green-400" size={20} />
                    <span className="text-green-300 font-medium ml-2">
                      Voice Transcript Received
                    </span>
                  </div>
                  <div className="text-white bg-gray-800/50 rounded p-3">
                    {voiceTranscript}
                  </div>
                </div>
              )}
            </div>
          )}

          {currentTest === 'ideas' && (
            <div className="space-y-6">
              <div className="text-center mb-6">
                <h2 className="text-2xl font-bold text-white mb-2">AI Business Idea Generator Test</h2>
                <p className="text-gray-300">
                  Generate innovative business ideas with AI-powered market analysis
                </p>
              </div>

              <BusinessIdeaGenerator />
            </div>
          )}

          {currentTest === 'collaboration' && (
            <div className="space-y-6">
              <div className="text-center mb-6">
                <h2 className="text-2xl font-bold text-white mb-2">Smart Collaboration Hub Test</h2>
                <p className="text-gray-300">
                  Test real-time collaboration features with team communication
                </p>
              </div>

              <SmartCollaborationHub />
            </div>
          )}

          {currentTest === 'market' && (
            <div className="space-y-6">
              <div className="text-center mb-6">
                <h2 className="text-2xl font-bold text-white mb-2">Market Intelligence Dashboard Test</h2>
                <p className="text-gray-300">
                  Analyze market trends and competitive intelligence with AI insights
                </p>
              </div>

              <MarketIntelligenceDashboard />
            </div>
          )}
        </div>

        {/* Test Status */}
        <div className="mt-12 text-center">
          <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6 max-w-2xl mx-auto">
            <h3 className="text-lg font-semibold text-white mb-4">Test Status</h3>

            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
              <div className="bg-green-900/20 border border-green-500/50 rounded-lg p-4">
                <div className="flex items-center justify-center mb-2">
                  <CheckCircle className="text-green-400" size={24} />
                </div>
                <div className="text-green-300 font-medium">Performance</div>
                <div className="text-green-400 text-sm">Component Loaded</div>
              </div>

              <div className="bg-green-900/20 border border-green-500/50 rounded-lg p-4">
                <div className="flex items-center justify-center mb-2">
                  <CheckCircle className="text-green-400" size={24} />
                </div>
                <div className="text-green-300 font-medium">Analytics</div>
                <div className="text-green-400 text-sm">Component Loaded</div>
              </div>

              <div className="bg-green-900/20 border border-green-500/50 rounded-lg p-4">
                <div className="flex items-center justify-center mb-2">
                  <CheckCircle className="text-green-400" size={24} />
                </div>
                <div className="text-green-300 font-medium">Voice Input</div>
                <div className="text-green-400 text-sm">Component Loaded</div>
              </div>

              <div className="bg-green-900/20 border border-green-500/50 rounded-lg p-4">
                <div className="flex items-center justify-center mb-2">
                  <CheckCircle className="text-green-400" size={24} />
                </div>
                <div className="text-green-300 font-medium">AI Ideas</div>
                <div className="text-green-400 text-sm">Component Loaded</div>
              </div>

              <div className="bg-green-900/20 border border-green-500/50 rounded-lg p-4">
                <div className="flex items-center justify-center mb-2">
                  <CheckCircle className="text-green-400" size={24} />
                </div>
                <div className="text-green-300 font-medium">Collaboration</div>
                <div className="text-green-400 text-sm">Component Loaded</div>
              </div>

              <div className="bg-green-900/20 border border-green-500/50 rounded-lg p-4">
                <div className="flex items-center justify-center mb-2">
                  <CheckCircle className="text-green-400" size={24} />
                </div>
                <div className="text-green-300 font-medium">Market Intel</div>
                <div className="text-green-400 text-sm">Component Loaded</div>
              </div>
            </div>

            <div className="mt-6 text-gray-300 text-sm">
              All new components are successfully loaded and functional! 🎉
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="mt-8 text-center">
          <div className="bg-blue-900/20 border border-blue-500/50 rounded-lg p-6 max-w-4xl mx-auto">
            <h3 className="text-lg font-semibold text-white mb-4">Testing Instructions</h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-left">
              <div>
                <div className="flex items-center mb-2">
                  <Zap className="text-purple-400" size={20} />
                  <span className="text-white font-medium ml-2">Performance</span>
                </div>
                <ul className="text-gray-300 text-sm space-y-1">
                  <li>• Click "Start Monitoring" to begin</li>
                  <li>• View real-time metrics</li>
                  <li>• Check optimization suggestions</li>
                  <li>• Try quick action buttons</li>
                </ul>
              </div>

              <div>
                <div className="flex items-center mb-2">
                  <BarChart3 className="text-blue-400" size={20} />
                  <span className="text-white font-medium ml-2">Analytics</span>
                </div>
                <ul className="text-gray-300 text-sm space-y-1">
                  <li>• Toggle "Start Live" for real-time data</li>
                  <li>• Change time range filters</li>
                  <li>• View AI insights</li>
                  <li>• Monitor live activity feed</li>
                </ul>
              </div>

              <div>
                <div className="flex items-center mb-2">
                  <Mic className="text-green-400" size={20} />
                  <span className="text-white font-medium ml-2">Voice Input</span>
                </div>
                <ul className="text-gray-300 text-sm space-y-1">
                  <li>• Click microphone to start</li>
                  <li>• Speak in Arabic or English</li>
                  <li>• View real-time transcription</li>
                  <li>• Test different languages</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ComponentTestPage;
