import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import AuthenticatedLayout from '../../components/layout/AuthenticatedLayout';
import { 
  Briefcase, 
  TrendingUp, 
  TrendingDown,
  DollarSign, 
  Calendar,
  Building,
  ArrowUpRight,
  ArrowDownRight,
  Target,
  PieChart
} from 'lucide-react';

interface PortfolioItem {
  id: string;
  companyName: string;
  industry: string;
  investmentAmount: number;
  currentValue: number;
  roi: number;
  status: 'growing' | 'stable' | 'declining';
  lastUpdate: string;
  shares: number;
  investmentDate: string;
  stage: string;
}

const InvestorPortfolioPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [portfolio, setPortfolio] = useState<PortfolioItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [sortBy, setSortBy] = useState<'name' | 'roi' | 'value' | 'date'>('roi');
  const [filterStatus, setFilterStatus] = useState<'all' | 'growing' | 'stable' | 'declining'>('all');

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setPortfolio([
        {
          id: '1',
          companyName: 'TechStart AI',
          industry: 'Artificial Intelligence',
          investmentAmount: 500000,
          currentValue: 750000,
          roi: 50,
          status: 'growing',
          lastUpdate: '2024-01-15',
          shares: 5000,
          investmentDate: '2023-06-15',
          stage: 'Series A'
        },
        {
          id: '2',
          companyName: 'GreenTech Solutions',
          industry: 'Clean Energy',
          investmentAmount: 300000,
          currentValue: 420000,
          roi: 40,
          status: 'growing',
          lastUpdate: '2024-01-12',
          shares: 3000,
          investmentDate: '2023-08-20',
          stage: 'Seed'
        },
        {
          id: '3',
          companyName: 'HealthCare Plus',
          industry: 'Healthcare',
          investmentAmount: 400000,
          currentValue: 380000,
          roi: -5,
          status: 'declining',
          lastUpdate: '2024-01-10',
          shares: 4000,
          investmentDate: '2023-04-10',
          stage: 'Series B'
        },
        {
          id: '4',
          companyName: 'FinTech Pro',
          industry: 'Financial Technology',
          investmentAmount: 250000,
          currentValue: 275000,
          roi: 10,
          status: 'stable',
          lastUpdate: '2024-01-14',
          shares: 2500,
          investmentDate: '2023-09-05',
          stage: 'Series A'
        }
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'growing': return <ArrowUpRight className="h-4 w-4 text-green-400" />;
      case 'declining': return <ArrowDownRight className="h-4 w-4 text-red-400" />;
      default: return <Target className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'growing': return 'text-green-400';
      case 'declining': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const totalInvestment = portfolio.reduce((sum, item) => sum + item.investmentAmount, 0);
  const totalCurrentValue = portfolio.reduce((sum, item) => sum + item.currentValue, 0);
  const totalROI = totalInvestment > 0 ? ((totalCurrentValue - totalInvestment) / totalInvestment) * 100 : 0;

  const filteredPortfolio = portfolio
    .filter(item => filterStatus === 'all' || item.status === filterStatus)
    .sort((a, b) => {
      switch (sortBy) {
        case 'name': return a.companyName.localeCompare(b.companyName);
        case 'roi': return b.roi - a.roi;
        case 'value': return b.currentValue - a.currentValue;
        case 'date': return new Date(b.investmentDate).getTime() - new Date(a.investmentDate).getTime();
        default: return 0;
      }
    });

  if (loading) {
    return (
      <AuthenticatedLayout>
      <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">{t('Loading portfolio...')}</p>
          </div>
        </div>
                </div>
        </div>
      </div>
    </AuthenticatedLayout>
    );
  }

  return (
    <AuthenticatedLayout>
      <div className="p-6">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center space-x-3 mb-2">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Briefcase className="h-6 w-6 text-purple-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-white">{t('Investment Portfolio')}</h1>
              <p className="text-gray-300">{t('Track and manage your investments')}</p>
            </div>
          </div>
        </div>

        {/* Portfolio Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">{t('Total Investment')}</p>
                <p className="text-2xl font-bold text-white">{formatCurrency(totalInvestment)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-blue-400" />
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">{t('Current Value')}</p>
                <p className="text-2xl font-bold text-white">{formatCurrency(totalCurrentValue)}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-400" />
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">{t('Total ROI')}</p>
                <p className={`text-2xl font-bold ${totalROI >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  {totalROI >= 0 ? '+' : ''}{totalROI.toFixed(1)}%
                </p>
              </div>
              {totalROI >= 0 ? 
                <TrendingUp className="h-8 w-8 text-green-400" /> : 
                <TrendingDown className="h-8 w-8 text-red-400" />
              }
            </div>
          </div>
        </div>

        {/* Filters and Sorting */}
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 mb-6">
          <div className="flex flex-wrap gap-4 items-center">
            <div>
              <label className="text-gray-300 text-sm mr-2">{t('Sort by')}:</label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="px-3 py-1 bg-white/20 border border-white/30 rounded text-white text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="roi">{t('ROI')}</option>
                <option value="value">{t('Current Value')}</option>
                <option value="name">{t('Company Name')}</option>
                <option value="date">{t('Investment Date')}</option>
              </select>
            </div>

            <div>
              <label className="text-gray-300 text-sm mr-2">{t('Filter by status')}:</label>
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value as any)}
                className="px-3 py-1 bg-white/20 border border-white/30 rounded text-white text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="all">{t('All')}</option>
                <option value="growing">{t('Growing')}</option>
                <option value="stable">{t('Stable')}</option>
                <option value="declining">{t('Declining')}</option>
              </select>
            </div>
          </div>
        </div>

        {/* Portfolio Items */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredPortfolio.map((item) => (
            <div key={item.id} className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-white">{item.companyName}</h3>
                  <p className="text-gray-300">{item.industry} • {item.stage}</p>
                </div>
                <div className="flex items-center space-x-1">
                  {getStatusIcon(item.status)}
                  <span className={`text-sm font-medium ${item.roi >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                    {item.roi >= 0 ? '+' : ''}{item.roi.toFixed(1)}%
                  </span>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                <div>
                  <span className="text-gray-400">{t('Investment')}: </span>
                  <span className="text-white font-medium">{formatCurrency(item.investmentAmount)}</span>
                </div>
                <div>
                  <span className="text-gray-400">{t('Current Value')}: </span>
                  <span className="text-white font-medium">{formatCurrency(item.currentValue)}</span>
                </div>
                <div>
                  <span className="text-gray-400">{t('Shares')}: </span>
                  <span className="text-white font-medium">{item.shares.toLocaleString()}</span>
                </div>
                <div>
                  <span className="text-gray-400">{t('Status')}: </span>
                  <span className={`font-medium capitalize ${getStatusColor(item.status)}`}>
                    {item.status}
                  </span>
                </div>
              </div>
              
              <div className="text-xs text-gray-400 mb-4">
                <div>{t('Investment Date')}: {new Date(item.investmentDate).toLocaleDateString()}</div>
                <div>{t('Last Updated')}: {new Date(item.lastUpdate).toLocaleDateString()}</div>
              </div>
              
              <div className="flex space-x-3">
                <button className="flex-1 px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors text-sm">
                  {t('View Details')}
                </button>
                <button className="flex-1 px-4 py-2 bg-white/20 text-white rounded-md hover:bg-white/30 transition-colors text-sm">
                  {t('Manage')}
                </button>
              </div>
            </div>
          ))}
        </div>

        {filteredPortfolio.length === 0 && (
          <div className="text-center py-12">
            <Briefcase className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-300 mb-2">{t('No investments found')}</h3>
            <p className="text-gray-400">{t('Try adjusting your filter criteria')}</p>
          </div>
        )}
      </div>
    </AuthenticatedLayout>
  );
};

export default InvestorPortfolioPage;
