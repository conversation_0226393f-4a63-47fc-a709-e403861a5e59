import { useCallback } from 'react';
import { useQueryClient, useMutation } from '@tanstack/react-query';
import { 
  useApiList, 
  useApiQuery, 
  useApiCreate, 
  useApiUpdate, 
  useApiDelete,
  useApiInfinite,
  useApiPatch,
  useApiUpdateById,
  useApiDeleteById,
  usePrefetchApiQuery,
  usePrefetchApiList
} from './useApi';
import { User, UserProfile } from '../services/api';
import { QueryParams } from '../types/api';
import { ApiClient } from '../services/apiClient';

// Create a shared API client instance
const apiClient = new ApiClient();

// Query keys - using arrays for better type safety and organization
export const UserKeys = {
  all: ['users'] as const,
  lists: () => [...UserKeys.all, 'list'] as const,
  list: (filters: QueryParams) => [...UserKeys.lists(), filters] as const,
  infiniteList: (filters: QueryParams) => [...UserKeys.lists(), 'infinite', filters] as const,
  details: () => [...UserKeys.all, 'detail'] as const,
  detail: (id: number | 'me') => [...UserKeys.details(), id] as const,
  profile: (id: number) => [...UserKeys.all, 'profile', id] as const,
};

/**
 * Hook for fetching a list of users
 */
export function useUsersList(params?: QueryParams) {
  return useApiList<User>(
    UserKeys.list(params || {}),
    '/users/users/',
    params
  );
}

/**
 * Hook for infinite scrolling of users
 */
export function useUsersInfinite(params?: QueryParams) {
  return useApiInfinite<User>(
    UserKeys.infiniteList(params || {}),
    '/users/users/',
    params
  );
}

/**
 * Hook for fetching a single user
 */
export function useUser(id: number) {
  return useApiQuery<User>(
    UserKeys.detail(id),
    `/users/users/${id}/`
  );
}

/**
 * Hook for fetching the current user
 */
export function useCurrentUser() {
  return useApiQuery<User>(
    UserKeys.detail('me'),
    '/users/users/me/',
    undefined,
    {
      // Don't refetch on window focus for the current user
      refetchOnWindowFocus: false,
      // Keep the data fresh for 5 minutes
      staleTime: 5 * 60 * 1000,
    }
  );
}

/**
 * Hook for prefetching a single user
 */
export function usePrefetchUser(id: number) {
  return usePrefetchApiQuery<User>(
    UserKeys.detail(id),
    `/users/users/${id}/`
  );
}

/**
 * Hook for prefetching a list of users
 */
export function usePrefetchUsersList(params?: QueryParams) {
  return usePrefetchApiList<User>(
    UserKeys.list(params || {}),
    '/users/users/',
    params
  );
}

/**
 * Hook for creating a user
 */
export function useCreateUser() {
  const queryClient = useQueryClient();
  
  return useApiCreate<User, Partial<User>>(
    '/users/users/',
    {
      onSuccess: (newUser) => {
        // Invalidate users list queries to refetch data
        queryClient.invalidateQueries({ queryKey: UserKeys.lists() });
        
        // Optionally add the new user to the cache
        queryClient.setQueryData(UserKeys.detail(newUser.id), newUser);
      }
    }
  );
}

/**
 * Hook for updating a user
 */
export function useUpdateUser() {
  const queryClient = useQueryClient();
  
  return useApiUpdateById<User, Partial<User>>(
    '/users/users/:id/',
    {
      onSuccess: (updatedUser) => {
        // Update the user in the cache
        if (updatedUser.id) {
          queryClient.setQueryData(UserKeys.detail(updatedUser.id), updatedUser);
          
          // If this is the current user, also update the 'me' cache
          const currentUser = queryClient.getQueryData<User>(UserKeys.detail('me'));
          if (currentUser && currentUser.id === updatedUser.id) {
            queryClient.setQueryData(UserKeys.detail('me'), updatedUser);
          }
        }
        
        // Invalidate users list queries to refetch data
        queryClient.invalidateQueries({ queryKey: UserKeys.lists() });
      }
    }
  );
}

/**
 * Hook for updating the current user
 */
export function useUpdateCurrentUser() {
  const queryClient = useQueryClient();
  
  return useApiUpdate<User, Partial<User>>(
    '/users/users/me/',
    {
      onSuccess: (updatedUser) => {
        // Update the current user in the cache
        queryClient.setQueryData(UserKeys.detail('me'), updatedUser);
        
        // Also update the user detail cache if it exists
        if (updatedUser.id) {
          queryClient.setQueryData(UserKeys.detail(updatedUser.id), updatedUser);
        }
        
        // Invalidate users list queries to refetch data
        queryClient.invalidateQueries({ queryKey: UserKeys.lists() });
      }
    }
  );
}

/**
 * Hook for updating a user profile
 */
export function useUpdateUserProfile() {
  const queryClient = useQueryClient();
  
  return useApiUpdateById<UserProfile, Partial<UserProfile>>(
    '/users/profiles/:id/',
    {
      onSuccess: (updatedProfile, { id }) => {
        // Update the profile in the cache
        queryClient.setQueryData(UserKeys.profile(Number(id)), updatedProfile);
        
        // Invalidate the user detail to refetch with updated profile
        queryClient.invalidateQueries({ 
          queryKey: UserKeys.detail(Number(id)),
          exact: false
        });
        
        // Invalidate current user if this is the current user's profile
        const currentUser = queryClient.getQueryData<User>(UserKeys.detail('me'));
        if (currentUser && currentUser.profile && currentUser.profile.id === Number(id)) {
          queryClient.invalidateQueries({ queryKey: UserKeys.detail('me') });
        }
      }
    }
  );
}
