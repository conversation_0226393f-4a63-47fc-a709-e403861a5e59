import React, { useState } from 'react';
import { useAppSelector } from '../../store/hooks';
// Removed roleAccessValidator - using enhancedRoleValidator instead
import {
  validateRoleBasedAccessControl,
  getRoleAccessHealthScore
} from '../../utils/enhancedRoleValidator';
import { UserRole } from '../../routes/routeConfig';
import { Shield, CheckCircle, XCircle, AlertTriangle, Play, Users, BarChart3 } from 'lucide-react';

/**
 * Role-Based Access Control Tester - Development only
 * Comprehensive testing of role permissions and access control
 */
const RoleAccessTester: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [testResults, setTestResults] = useState<RoleValidationReport[]>([]);
  const [selectedRole, setSelectedRole] = useState<UserRole | null>(null);
  const [isRunning, setIsRunning] = useState(false);
  const [selectedReport, setSelectedReport] = useState<RoleValidationReport | null>(null);
  
  const { user } = useAppSelector(state => state.auth);

  const roles: UserRole[] = ['user', 'admin', 'super_admin', 'mentor', 'investor', 'moderator'];

  const runAllTests = async () => {
    setIsRunning(true);
    try {
      const results = runRoleValidationTests();
      setTestResults(results);
    } catch (error) {
      console.error('Role validation tests failed:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const runSingleRoleTest = async (role: UserRole) => {
    setIsRunning(true);
    try {
      const result = validateRoleAccess(role);
      setTestResults(prev => {
        const filtered = prev.filter(r => r.role !== role);
        return [...filtered, result];
      });
      setSelectedReport(result);
    } catch (error) {
      console.error(`Role validation test failed for ${role}:`, error);
    } finally {
      setIsRunning(false);
    }
  };

  const getRoleColor = (role: UserRole): string => {
    switch (role) {
      case 'super_admin': return 'text-red-600 bg-red-100';
      case 'admin': return 'text-blue-600 bg-blue-100';
      case 'moderator': return 'text-orange-600 bg-orange-100';
      case 'mentor': return 'text-green-600 bg-green-100';
      case 'investor': return 'text-purple-600 bg-purple-100';
      case 'user': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getScoreColor = (score: number): string => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <>
      {/* Floating Button */}
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-44 right-4 z-50 bg-indigo-600 hover:bg-indigo-700 text-white p-3 rounded-full shadow-lg transition-colors"
        title="Role Access Tester"
      >
        <Shield className="w-5 h-5" />
      </button>

      {/* Test Panel */}
      {isOpen && (
        <div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Role-Based Access Control Tester
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Validate role permissions and access control across all routes
                </p>
              </div>
              <button
                onClick={() => setIsOpen(false)}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              >
                ✕
              </button>
            </div>

            <div className="flex h-[calc(90vh-120px)]">
              {/* Left Panel - Controls and Summary */}
              <div className="w-1/3 p-6 border-r border-gray-200 dark:border-gray-700 overflow-y-auto">
                {/* Current User Info */}
                <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    Current User
                  </h3>
                  <div className="text-sm space-y-1">
                    <div><strong>Username:</strong> {user?.username || 'Not logged in'}</div>
                    <div><strong>Admin:</strong> {user?.is_admin ? '✅ Yes' : '❌ No'}</div>
                    <div><strong>Super Admin:</strong> {user?.is_superuser ? '✅ Yes' : '❌ No'}</div>
                    <div><strong>Primary Role:</strong> {user?.profile?.primary_role?.name || 'None'}</div>
                  </div>
                </div>

                {/* Controls */}
                <div className="mb-6">
                  <button
                    onClick={runAllTests}
                    disabled={isRunning}
                    className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50 transition-colors mb-4"
                  >
                    <Play className={`w-4 h-4 ${isRunning ? 'animate-pulse' : ''}`} />
                    {isRunning ? 'Running Tests...' : 'Run All Role Tests'}
                  </button>

                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white">Test Individual Roles:</h4>
                    {roles.map(role => (
                      <button
                        key={role}
                        onClick={() => runSingleRoleTest(role)}
                        disabled={isRunning}
                        className={`w-full px-3 py-2 text-sm rounded-lg border transition-colors ${getRoleColor(role)} hover:opacity-80 disabled:opacity-50`}
                      >
                        Test {role.replace('_', ' ').toUpperCase()}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Results Summary */}
                {testResults.length > 0 && (
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                      Test Results Summary
                    </h3>
                    <div className="space-y-3">
                      {testResults.map(report => (
                        <div
                          key={report.role}
                          onClick={() => setSelectedReport(report)}
                          className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                            selectedReport?.role === report.role 
                              ? 'border-indigo-500 bg-indigo-50 dark:bg-indigo-900/20' 
                              : 'border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700'
                          }`}
                        >
                          <div className="flex items-center justify-between mb-2">
                            <span className={`px-2 py-1 text-xs rounded ${getRoleColor(report.role)}`}>
                              {report.role.replace('_', ' ').toUpperCase()}
                            </span>
                            <span className={`text-sm font-medium ${getScoreColor(report.overallScore)}`}>
                              {report.overallScore}%
                            </span>
                          </div>
                          <div className="text-xs text-gray-600 dark:text-gray-300">
                            {report.passedTests}/{report.totalTests} tests passed
                          </div>
                          {report.failedTests > 0 && (
                            <div className="text-xs text-red-600 mt-1">
                              {report.failedTests} failures
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Right Panel - Detailed Results */}
              <div className="flex-1 p-6 overflow-y-auto">
                {selectedReport ? (
                  <div>
                    <div className="flex items-center justify-between mb-6">
                      <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                        {selectedReport.role.replace('_', ' ').toUpperCase()} Role Test Results
                      </h3>
                      <div className="flex items-center gap-4">
                        <div className="text-center">
                          <div className={`text-2xl font-bold ${getScoreColor(selectedReport.overallScore)}`}>
                            {selectedReport.overallScore}%
                          </div>
                          <div className="text-xs text-gray-600 dark:text-gray-300">Overall Score</div>
                        </div>
                      </div>
                    </div>

                    {/* Stats */}
                    <div className="grid grid-cols-3 gap-4 mb-6">
                      <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg text-center">
                        <div className="text-2xl font-bold text-green-600">{selectedReport.passedTests}</div>
                        <div className="text-sm text-gray-600 dark:text-gray-300">Passed</div>
                      </div>
                      <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg text-center">
                        <div className="text-2xl font-bold text-red-600">{selectedReport.failedTests}</div>
                        <div className="text-sm text-gray-600 dark:text-gray-300">Failed</div>
                      </div>
                      <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg text-center">
                        <div className="text-2xl font-bold text-blue-600">{selectedReport.totalTests}</div>
                        <div className="text-sm text-gray-600 dark:text-gray-300">Total</div>
                      </div>
                    </div>

                    {/* Detailed Results */}
                    <div className="space-y-2">
                      <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
                        Route Access Tests
                      </h4>
                      {selectedReport.results.map((result, index) => (
                        <div
                          key={index}
                          className={`p-3 border rounded-lg ${
                            result.passed 
                              ? 'border-green-200 bg-green-50 dark:bg-green-900/20' 
                              : 'border-red-200 bg-red-50 dark:bg-red-900/20'
                          }`}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              {result.passed ? (
                                <CheckCircle className="w-4 h-4 text-green-600" />
                              ) : (
                                <XCircle className="w-4 h-4 text-red-600" />
                              )}
                              <span className="font-medium text-gray-900 dark:text-white">
                                {result.route}
                              </span>
                            </div>
                            <div className="text-sm">
                              {result.expectedAccess ? '✅ Should Access' : '❌ Should Deny'}
                              {' → '}
                              {result.actualAccess ? '✅ Granted' : '❌ Denied'}
                            </div>
                          </div>
                          {!result.passed && result.reason && (
                            <div className="mt-2 text-sm text-red-600">
                              {result.reason}
                            </div>
                          )}
                          {(result.requiredRoles || result.requiredPermissions) && (
                            <div className="mt-2 text-xs text-gray-600 dark:text-gray-300">
                              {result.requiredRoles && (
                                <span>Roles: {result.requiredRoles.join(', ')} </span>
                              )}
                              {result.requiredPermissions && (
                                <span>Permissions: {result.requiredPermissions.join(', ')}</span>
                              )}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-500 dark:text-gray-400">
                    <div className="text-center">
                      <Users className="w-16 h-16 mx-auto mb-4 opacity-50" />
                      <p>Run tests and select a role to view detailed results</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default RoleAccessTester;
