/**
 * Dashboard Utilities
 * Helper functions for dashboard consolidation and role-based rendering
 */

import { User } from '../store/authSlice';
import { 
  DashboardRole, 
  DashboardConfig, 
  DashboardStat, 
  DashboardQuickAction,
  DashboardTheme,
  DEFAULT_DASHBOARD_CONFIGS,
  DASHBOARD_THEMES
} from '../types/dashboard';
import { getUserDashboardType } from './roleBasedRouting';

/**
 * Determine dashboard role from user object
 */
export function getDashboardRole(user: User | null): DashboardRole {
  if (!user) return 'user';
  
  const dashboardType = getUserDashboardType(user);
  return dashboardType as DashboardRole;
}

/**
 * Get dashboard configuration for a specific role
 */
export function getDashboardConfig(role: DashboardRole): DashboardConfig {
  const defaultConfig = DEFAULT_DASHBOARD_CONFIGS[role];
  
  // Base configuration that all roles share
  const baseConfig: DashboardConfig = {
    role,
    title: defaultConfig.title || 'Dashboard',
    subtitle: defaultConfig.subtitle || 'Welcome to your dashboard',
    backgroundGradient: defaultConfig.backgroundGradient || 'from-gray-900 to-gray-800',
    sections: [], // Will be populated by role-specific providers
    stats: [], // Will be populated by role-specific providers
    quickActions: [], // Will be populated by role-specific providers
    refreshInterval: defaultConfig.refreshInterval || 300000,
    showSystemStatus: defaultConfig.showSystemStatus || false,
    showNotifications: defaultConfig.showNotifications || true,
  };

  return baseConfig;
}

/**
 * Get dashboard theme for a specific role
 */
export function getDashboardTheme(role: DashboardRole): DashboardTheme {
  return DASHBOARD_THEMES[role];
}

/**
 * Check if user has permission to access a dashboard feature
 */
export function hasPermission(user: User | null, feature: string): boolean {
  if (!user) return false;
  
  const role = getDashboardRole(user);
  
  // Define permissions for each role
  const permissions: Record<DashboardRole, string[]> = {
    user: ['view_own_data', 'create_ideas', 'view_resources'],
    mentor: ['view_own_data', 'view_mentees', 'manage_sessions', 'view_resources'],
    investor: ['view_own_data', 'view_opportunities', 'manage_investments', 'view_analytics'],
    moderator: ['view_own_data', 'moderate_content', 'view_reports', 'manage_users'],
    admin: ['view_all_data', 'manage_users', 'manage_content', 'view_analytics', 'system_settings'],
    super_admin: ['*'], // All permissions
  };
  
  const rolePermissions = permissions[role] || [];
  return rolePermissions.includes('*') || rolePermissions.includes(feature);
}

/**
 * Format dashboard statistics for display
 */
export function formatStatValue(value: number | string, type?: 'number' | 'percentage' | 'currency'): string {
  if (typeof value === 'string') return value;
  
  switch (type) {
    case 'percentage':
      return `${value.toFixed(1)}%`;
    case 'currency':
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(value);
    case 'number':
    default:
      return value.toLocaleString();
  }
}

/**
 * Get change indicator for statistics
 */
export function getChangeIndicator(change: number): {
  type: 'increase' | 'decrease' | 'neutral';
  color: string;
  symbol: string;
} {
  if (change > 0) {
    return { type: 'increase', color: 'text-green-400', symbol: '+' };
  } else if (change < 0) {
    return { type: 'decrease', color: 'text-red-400', symbol: '' };
  } else {
    return { type: 'neutral', color: 'text-gray-400', symbol: '' };
  }
}

/**
 * Filter dashboard sections based on role permissions
 */
export function filterSectionsByRole(sections: any[], role: DashboardRole): any[] {
  const roleSectionMap: Record<DashboardRole, string[]> = {
    user: ['welcome', 'stats', 'quick_actions', 'recent_items', 'recommendations'],
    mentor: ['welcome', 'stats', 'quick_actions', 'activity', 'recent_items'],
    investor: ['welcome', 'stats', 'quick_actions', 'analytics', 'recent_items'],
    moderator: ['welcome', 'stats', 'quick_actions', 'activity', 'notifications'],
    admin: ['welcome', 'stats', 'quick_actions', 'analytics', 'activity', 'system_health', 'notifications'],
    super_admin: ['welcome', 'stats', 'quick_actions', 'analytics', 'activity', 'system_health', 'notifications'],
  };
  
  const allowedSections = roleSectionMap[role] || [];
  return sections.filter(section => allowedSections.includes(section.type));
}

/**
 * Get grid layout configuration for dashboard role
 */
export function getGridLayout(role: DashboardRole): {
  gridCols: string;
  gap: string;
  maxWidth: string;
} {
  const layouts: Record<DashboardRole, { gridCols: string; gap: string; maxWidth: string }> = {
    user: { gridCols: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3', gap: 'gap-6', maxWidth: 'max-w-7xl' },
    mentor: { gridCols: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3', gap: 'gap-6', maxWidth: 'max-w-7xl' },
    investor: { gridCols: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4', gap: 'gap-6', maxWidth: 'max-w-7xl' },
    moderator: { gridCols: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3', gap: 'gap-6', maxWidth: 'max-w-6xl' },
    admin: { gridCols: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4', gap: 'gap-6', maxWidth: 'max-w-7xl' },
    super_admin: { gridCols: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4', gap: 'gap-8', maxWidth: 'max-w-8xl' },
  };
  
  return layouts[role] || layouts.user;
}

/**
 * Generate dashboard breadcrumbs based on role and current page
 */
export function getDashboardBreadcrumbs(role: DashboardRole, currentPage?: string): Array<{
  label: string;
  href?: string;
  current: boolean;
}> {
  const baseBreadcrumbs = [
    { label: 'Home', href: '/', current: false },
  ];
  
  const roleBreadcrumbs: Record<DashboardRole, { label: string; href: string }> = {
    user: { label: 'Dashboard', href: '/dashboard' },
    mentor: { label: 'Mentor Dashboard', href: '/dashboard' },
    investor: { label: 'Investor Dashboard', href: '/dashboard' },
    moderator: { label: 'Moderator Dashboard', href: '/dashboard' },
    admin: { label: 'Admin Dashboard', href: '/admin' },
    super_admin: { label: 'Super Admin Dashboard', href: '/super_admin' },
  };
  
  const roleBreadcrumb = roleBreadcrumbs[role];
  baseBreadcrumbs.push({
    label: roleBreadcrumb.label,
    href: currentPage ? roleBreadcrumb.href : undefined,
    current: !currentPage,
  });
  
  if (currentPage) {
    baseBreadcrumbs.push({
      label: currentPage,
      current: true,
    });
  }
  
  return baseBreadcrumbs;
}

/**
 * Validate dashboard configuration
 */
export function validateDashboardConfig(config: DashboardConfig): {
  valid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  if (!config.role) {
    errors.push('Dashboard role is required');
  }
  
  if (!config.title) {
    errors.push('Dashboard title is required');
  }
  
  if (!config.backgroundGradient) {
    errors.push('Background gradient is required');
  }
  
  if (!Array.isArray(config.sections)) {
    errors.push('Sections must be an array');
  }
  
  if (!Array.isArray(config.stats)) {
    errors.push('Stats must be an array');
  }
  
  if (!Array.isArray(config.quickActions)) {
    errors.push('Quick actions must be an array');
  }
  
  return {
    valid: errors.length === 0,
    errors,
  };
}

/**
 * Merge dashboard configurations
 */
export function mergeDashboardConfigs(
  baseConfig: DashboardConfig,
  overrideConfig: Partial<DashboardConfig>
): DashboardConfig {
  return {
    ...baseConfig,
    ...overrideConfig,
    sections: overrideConfig.sections || baseConfig.sections,
    stats: overrideConfig.stats || baseConfig.stats,
    quickActions: overrideConfig.quickActions || baseConfig.quickActions,
  };
}

/**
 * Get dashboard refresh interval based on role and data sensitivity
 */
export function getRefreshInterval(role: DashboardRole, dataType?: string): number {
  const baseIntervals: Record<DashboardRole, number> = {
    user: 300000, // 5 minutes
    mentor: 300000, // 5 minutes
    investor: 300000, // 5 minutes
    moderator: 120000, // 2 minutes
    admin: 60000, // 1 minute
    super_admin: 30000, // 30 seconds
  };
  
  const baseInterval = baseIntervals[role];
  
  // Adjust based on data type
  if (dataType === 'real_time') {
    return Math.min(baseInterval, 30000); // Max 30 seconds for real-time data
  } else if (dataType === 'system_health') {
    return Math.min(baseInterval, 60000); // Max 1 minute for system health
  }
  
  return baseInterval;
}

/**
 * Check if dashboard should show loading state
 */
export function shouldShowLoading(loadingStates: Record<string, boolean>): boolean {
  const criticalSections = ['stats', 'welcome', 'quick_actions'];
  return criticalSections.some(section => loadingStates[section]);
}

/**
 * Generate dashboard analytics event
 */
export function trackDashboardEvent(
  role: DashboardRole,
  action: string,
  section?: string,
  metadata?: Record<string, any>
): void {
  // This would integrate with your analytics service
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', action, {
      event_category: 'dashboard',
      event_label: `${role}_${section || 'general'}`,
      custom_parameters: metadata,
    });
  }
  
  // Console log for development
  if (process.env.NODE_ENV === 'development') {
    console.log('Dashboard Event:', {
      role,
      action,
      section,
      metadata,
      timestamp: new Date().toISOString(),
    });
  }
}
