import { useState, useEffect, useCallback } from 'react';
import { Post } from '../../../../../services/api';
import { FilterValue } from '../../../common/AdvancedFilter';

interface UsePostFiltersResult {
  searchTerm: string;
  setSearchTerm: React.Dispatch<React.SetStateAction<string>>;
  activeFilters: FilterValue[];
  setActiveFilters: React.Dispatch<React.SetStateAction<FilterValue[]>>;
  handleFilterChange: (filters: FilterValue[]) => void;
  filteredPosts: Post[];
  postFilterOptions: {
    id: string;
    label: string;
    options: { value: string; label: string }[];
  }[];
}

export const usePostFilters = (posts: Post[]): UsePostFiltersResult => {
  const [searchTerm, setSearchTerm] = useState('');
  const [activeFilters, setActiveFilters] = useState<FilterValue[]>([]);

  // Define filter options
  const postFilterOptions = [
    {
      id: 'status',
      label: 'Status',
      options: [
        { value: 'published', label: 'Published' },
        { value: 'draft', label: 'Draft' },
        { value: 'archived', label: 'Archived' }
      ]
    },
    {
      id: 'date',
      label: 'Date',
      options: [
        { value: 'today', label: 'Today' },
        { value: 'this_week', label: 'This Week' },
        { value: 'this_month', label: 'This Month' },
        { value: 'this_year', label: 'This Year' }
      ]
    },
    {
      id: 'author',
      label: 'Author',
      options: [
        { value: 'me', label: 'Me' },
        { value: 'admin', label: 'Admin' },
        { value: 'moderator', label: 'Moderator' },
        { value: 'user', label: 'Regular User' }
      ]
    }
  ];

  // Handle filter change
  const handleFilterChange = useCallback((filters: FilterValue[]) => {
    setActiveFilters(filters);
  }, []);

  // Filter posts based on search term and active filters
  const filteredPosts = posts.filter(post => {
    // Search term filter
    const matchesSearch =
      post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.author.username.toLowerCase().includes(searchTerm.toLowerCase());

    if (!matchesSearch) return false;

    // Apply active filters
    if (activeFilters.length === 0) return true;

    return activeFilters.every(filter => {
      switch (filter.id) {
        case 'status':
          // In a real app, you would check the post status
          return true;
        case 'date':
          const postDate = new Date(post.created_at);
          const now = new Date();
          
          if (filter.value === 'today') {
            return postDate.toDateString() === now.toDateString();
          } else if (filter.value === 'this_week') {
            const startOfWeek = new Date(now);
            startOfWeek.setDate(now.getDate() - now.getDay());
            return postDate >= startOfWeek;
          } else if (filter.value === 'this_month') {
            return postDate.getMonth() === now.getMonth() && 
                   postDate.getFullYear() === now.getFullYear();
          } else if (filter.value === 'this_year') {
            return postDate.getFullYear() === now.getFullYear();
          }
          return true;
        case 'author':
          if (filter.value === 'admin') {
            return post.author.is_admin === true;
          } else if (filter.value === 'me') {
            // In a real app, you would compare with the current user ID
            return true;
          }
          return true;
        default:
          return true;
      }
    });
  });

  return {
    searchTerm,
    setSearchTerm,
    activeFilters,
    setActiveFilters,
    handleFilterChange,
    filteredPosts,
    postFilterOptions
  };
};

export default usePostFilters;
