import { useState, useEffect, useCallback } from 'react';
import { 
  businessPlanTemplatesAPI, 
  customTemplatesAPI,
  BusinessPlanTemplate,
  CustomBusinessPlanTemplate,
  TemplateFilters,
  TemplateCategory,
  TemplateAnalytics,
  TemplateRating
} from '../services/templateCustomizationApi';

export interface UseTemplatesReturn {
  // Data
  templates: BusinessPlanTemplate[];
  customTemplates: CustomBusinessPlanTemplate[];
  categories: TemplateCategory[];
  
  // Loading states
  loading: boolean;
  categoriesLoading: boolean;
  
  // Error states
  error: string | null;
  
  // Actions
  fetchTemplates: (filters?: TemplateFilters) => Promise<void>;
  fetchCustomTemplates: () => Promise<void>;
  fetchCategories: () => Promise<void>;
  createCustomTemplate: (data: Partial<CustomBusinessPlanTemplate>) => Promise<CustomBusinessPlanTemplate | null>;
  updateCustomTemplate: (id: number, data: Partial<CustomBusinessPlanTemplate>) => Promise<void>;
  deleteCustomTemplate: (id: number) => Promise<void>;
  rateTemplate: (id: number, rating: TemplateRating) => Promise<void>;
  getTemplateAnalytics: (id: number) => Promise<TemplateAnalytics | null>;
  
  // Utility functions
  refreshTemplates: () => Promise<void>;
  clearError: () => void;
}

export const useTemplates = (): UseTemplatesReturn => {
  const [templates, setTemplates] = useState<BusinessPlanTemplate[]>([]);
  const [customTemplates, setCustomTemplates] = useState<CustomBusinessPlanTemplate[]>([]);
  const [categories, setCategories] = useState<TemplateCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [categoriesLoading, setCategoriesLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const fetchTemplates = useCallback(async (filters?: TemplateFilters) => {
    setLoading(true);
    setError(null);

    try {
      const fetchedTemplates = await businessPlanTemplatesAPI.getTemplates(filters);
      // Ensure we always set an array
      if (Array.isArray(fetchedTemplates)) {
        setTemplates(fetchedTemplates);
      } else {
        console.warn('Templates API returned non-array:', fetchedTemplates);
        setTemplates([]);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch templates';
      setError(errorMessage);
      console.error('Error fetching templates:', err);
      // Ensure empty array on error
      setTemplates([]);
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchCustomTemplates = useCallback(async () => {
    try {
      const fetchedCustomTemplates = await customTemplatesAPI.getCustomTemplates();
      // Ensure we always set an array
      if (Array.isArray(fetchedCustomTemplates)) {
        setCustomTemplates(fetchedCustomTemplates);
      } else {
        console.warn('Custom templates API returned non-array:', fetchedCustomTemplates);
        setCustomTemplates([]);
      }
    } catch (err) {
      console.error('Error fetching custom templates:', err);
      // Don't set error for custom templates as they're optional, but ensure empty array
      setCustomTemplates([]);
    }
  }, []);

  const fetchCategories = useCallback(async () => {
    setCategoriesLoading(true);

    try {
      const fetchedCategories = await businessPlanTemplatesAPI.getCategories();
      // Only set categories if we get valid data from API
      if (Array.isArray(fetchedCategories) && fetchedCategories.length > 0) {
        setCategories(fetchedCategories);
      } else {
        // Set empty array if no categories available - let UI handle empty state
        setCategories([]);
      }
    } catch (err) {
      console.error('Error fetching categories:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch categories';
      setError(errorMessage);
      // Set empty array on error - let UI handle empty state gracefully
      setCategories([]);
    } finally {
      setCategoriesLoading(false);
    }
  }, []);

  const createCustomTemplate = useCallback(async (data: Partial<CustomBusinessPlanTemplate>): Promise<CustomBusinessPlanTemplate | null> => {
    try {
      const newTemplate = await customTemplatesAPI.createCustomTemplate(data);
      setCustomTemplates(prev => [...prev, newTemplate]);
      return newTemplate;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create custom template';
      setError(errorMessage);
      console.error('Error creating custom template:', err);
      return null;
    }
  }, []);

  const updateCustomTemplate = useCallback(async (id: number, data: Partial<CustomBusinessPlanTemplate>) => {
    try {
      const updatedTemplate = await customTemplatesAPI.updateCustomTemplate(id, data);
      setCustomTemplates(prev => 
        prev.map(template => template.id === id ? updatedTemplate : template)
      );
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update custom template';
      setError(errorMessage);
      console.error('Error updating custom template:', err);
    }
  }, []);

  const deleteCustomTemplate = useCallback(async (id: number) => {
    try {
      await customTemplatesAPI.deleteCustomTemplate(id);
      setCustomTemplates(prev => prev.filter(template => template.id !== id));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete custom template';
      setError(errorMessage);
      console.error('Error deleting custom template:', err);
    }
  }, []);

  const rateTemplate = useCallback(async (id: number, rating: TemplateRating) => {
    try {
      await businessPlanTemplatesAPI.rateTemplate(id, rating);
      // Optionally refresh templates to get updated rating
      await fetchTemplates();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to rate template';
      setError(errorMessage);
      console.error('Error rating template:', err);
    }
  }, [fetchTemplates]);

  const getTemplateAnalytics = useCallback(async (id: number): Promise<TemplateAnalytics | null> => {
    try {
      return await businessPlanTemplatesAPI.getTemplateAnalytics(id);
    } catch (err) {
      console.error('Error fetching template analytics:', err);
      return null;
    }
  }, []);

  const refreshTemplates = useCallback(async () => {
    await Promise.all([
      fetchTemplates(),
      fetchCustomTemplates(),
      fetchCategories()
    ]);
  }, [fetchTemplates, fetchCustomTemplates, fetchCategories]);

  // Initial load
  useEffect(() => {
    refreshTemplates();
  }, []);

  return {
    // Data
    templates,
    customTemplates,
    categories,
    
    // Loading states
    loading,
    categoriesLoading,
    
    // Error states
    error,
    
    // Actions
    fetchTemplates,
    fetchCustomTemplates,
    fetchCategories,
    createCustomTemplate,
    updateCustomTemplate,
    deleteCustomTemplate,
    rateTemplate,
    getTemplateAnalytics,
    
    // Utility functions
    refreshTemplates,
    clearError
  };
};

export default useTemplates;
