# Generated by Django 5.1.7 on 2025-05-15 18:12

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('incubator', '0004_mentorshipfeedback_communication_rating_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BusinessPlanTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('industry', models.CharField(max_length=100)),
                ('sections', models.JSONField(help_text='JSON structure defining the sections of this template')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['industry', 'name'],
            },
        ),
        migrations.CreateModel(
            name='BusinessPlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('archived', 'Archived')], default='draft', max_length=20)),
                ('content', models.JSONField(default=dict, help_text='JSON structure containing the business plan content')),
                ('ai_feedback', models.JSONField(default=dict, help_text='AI-generated feedback on the business plan')),
                ('completion_percentage', models.PositiveSmallIntegerField(default=0, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('version', models.PositiveSmallIntegerField(default=1)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('business_idea', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='business_plans', to='incubator.businessidea')),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='business_plans', to=settings.AUTH_USER_MODEL)),
                ('template', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='business_plans', to='incubator.businessplantemplate')),
            ],
            options={
                'ordering': ['-updated_at'],
                'unique_together': {('business_idea', 'version')},
            },
        ),
        migrations.CreateModel(
            name='BusinessPlanSection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100)),
                ('key', models.CharField(help_text='Unique identifier for this section', max_length=50)),
                ('content', models.TextField(blank=True)),
                ('order', models.PositiveSmallIntegerField(default=0)),
                ('is_required', models.BooleanField(default=True)),
                ('is_completed', models.BooleanField(default=False)),
                ('ai_suggestions', models.TextField(blank=True, null=True)),
                ('business_plan', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sections', to='incubator.businessplan')),
            ],
            options={
                'ordering': ['business_plan', 'order'],
                'unique_together': {('business_plan', 'key')},
            },
        ),
    ]
