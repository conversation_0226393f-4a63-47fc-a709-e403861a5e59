import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { useAppSelector } from '../../store/hooks';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  FileText, 
  Calendar,
  Target,
  Activity,
  RefreshCw,
  Download,
  Filter,
  Eye,
  MessageSquare,
  Heart,
  Share2,
  Lightbulb,
  CheckCircle,
  Clock,
  AlertCircle
} from 'lucide-react';
import { businessIdeasAPI } from '../../services/incubatorApi';
import { postsAPI, eventsAPI } from '../../services/api';
import { RTLText, RTLFlex } from '../../components/rtl';

interface UserStats {
  businessIdeas: {
    total: number;
    approved: number;
    pending: number;
    rejected: number;
  };
  posts: {
    total: number;
    totalViews: number;
    totalLikes: number;
    totalComments: number;
  };
  events: {
    total: number;
    upcoming: number;
    past: number;
    totalAttendees: number;
  };
  engagement: {
    totalViews: number;
    totalLikes: number;
    totalShares: number;
    engagementRate: number;
  };
}

const UserAnalyticsPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { user } = useAppSelector(state => state.auth);
  
  const [stats, setStats] = useState<UserStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState('30d');

  useEffect(() => {
    fetchAnalytics();
  }, [timeRange]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch user's business ideas
      const businessIdeas = await businessIdeasAPI.getBusinessIdeas();
      const userBusinessIdeas = businessIdeas.filter(idea => idea.user === user?.id);

      // Fetch user's posts
      const posts = await postsAPI.getPosts();
      const userPosts = posts.filter(post => post.author.id === user?.id);

      // Fetch events (if user has created any)
      const events = await eventsAPI.getEvents();
      const userEvents = events.filter(event => event.organizer.id === user?.id);

      // Calculate stats
      const businessIdeasStats = {
        total: userBusinessIdeas.length,
        approved: userBusinessIdeas.filter(idea => idea.status === 'approved').length,
        pending: userBusinessIdeas.filter(idea => idea.status === 'pending').length,
        rejected: userBusinessIdeas.filter(idea => idea.status === 'rejected').length,
      };

      const postsStats = {
        total: userPosts.length,
        totalViews: userPosts.reduce((sum, post) => sum + (post.views || 0), 0),
        totalLikes: userPosts.reduce((sum, post) => sum + (post.like_count || 0), 0),
        totalComments: userPosts.reduce((sum, post) => sum + (post.comments?.length || 0), 0),
      };

      const eventsStats = {
        total: userEvents.length,
        upcoming: userEvents.filter(event => new Date(event.date) > new Date()).length,
        past: userEvents.filter(event => new Date(event.date) <= new Date()).length,
        totalAttendees: userEvents.reduce((sum, event) => sum + (event.attendee_count || 0), 0),
      };

      const engagementStats = {
        totalViews: postsStats.totalViews,
        totalLikes: postsStats.totalLikes,
        totalShares: userPosts.reduce((sum, post) => sum + (post.shares || 0), 0),
        engagementRate: postsStats.totalViews > 0 ? 
          ((postsStats.totalLikes + postsStats.totalComments) / postsStats.totalViews * 100) : 0,
      };

      setStats({
        businessIdeas: businessIdeasStats,
        posts: postsStats,
        events: eventsStats,
        engagement: engagementStats,
      });

    } catch (err) {
      console.error('Error fetching analytics:', err);
      setError(t('analytics.dashboard.error'));
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchAnalytics();
    setRefreshing(false);
  };

  const StatCard: React.FC<{
    title: string;
    value: number | string;
    icon: React.ElementType;
    color: string;
    subtitle?: string;
    trend?: number;
  }> = ({ title, value, icon: Icon, color, subtitle, trend }) => (
    <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
      <RTLFlex className="items-start justify-between">
        <div className="flex-1">
          <RTLText as="p" className="text-gray-300 text-sm mb-1">{title}</RTLText>
          <RTLText as="p" className="text-2xl font-bold text-white mb-1">{value}</RTLText>
          {subtitle && (
            <RTLText as="p" className="text-gray-400 text-xs">{subtitle}</RTLText>
          )}
        </div>
        <div className={`p-3 rounded-lg ${color}`}>
          <Icon size={24} className="text-white" />
        </div>
      </RTLFlex>
      {trend !== undefined && (
        <div className={`mt-3 flex items-center text-sm ${trend >= 0 ? 'text-green-400' : 'text-red-400'}`}>
          <TrendingUp size={16} className={`mr-1 ${trend < 0 ? 'rotate-180' : ''}`} />
          <span>{Math.abs(trend)}% {trend >= 0 ? t('analytics.comparison.increase') : t('analytics.comparison.decrease')}</span>
        </div>
      )}
    </div>
  );

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center py-20">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto mb-4"></div>
            <RTLText as="p" className="text-gray-400">{t('analytics.dashboard.loading')}</RTLText>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-900/30 backdrop-blur-sm rounded-lg p-6 border border-red-800/50 text-center">
          <AlertCircle size={40} className="mx-auto mb-3 text-red-500" />
          <RTLText as="h3" className="text-xl font-bold mb-2 text-white">{t('analytics.dashboard.error')}</RTLText>
          <RTLText as="p" className="text-gray-400 mb-4">{error}</RTLText>
          <button
            onClick={handleRefresh}
            className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white inline-flex items-center"
          >
            <RefreshCw size={16} className="mr-1" />
            {t('analytics.dashboard.refresh')}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <RTLText as="h1" className="text-2xl font-bold text-white mb-2">
            {t('analytics.dashboard.title')}
          </RTLText>
          <RTLText as="p" className="text-gray-400">
            {t('analytics.dashboard.subtitle')}
          </RTLText>
        </div>
        
        <RTLFlex className="items-center gap-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="bg-white/10 border border-white/20 rounded-md px-3 py-2 text-white text-sm"
          >
            <option value="7d">{t('analytics.timeframes.last7days')}</option>
            <option value="30d">{t('analytics.timeframes.last30days')}</option>
            <option value="90d">{t('analytics.timeframes.last90days')}</option>
          </select>
          
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:opacity-50 rounded-md text-white inline-flex items-center"
          >
            <RefreshCw size={16} className={`mr-1 ${refreshing ? 'animate-spin' : ''}`} />
            {t('analytics.dashboard.refresh')}
          </button>
        </RTLFlex>
      </div>

      {stats && (
        <>
          {/* Business Ideas Stats */}
          <div>
            <RTLText as="h2" className="text-xl font-semibold text-white mb-4">
              {t('analytics.businessIdeas')}
            </RTLText>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <StatCard
                title={t('analytics.total')}
                value={stats.businessIdeas.total}
                icon={Lightbulb}
                color="bg-blue-600"
              />
              <StatCard
                title={t('analytics.approved')}
                value={stats.businessIdeas.approved}
                icon={CheckCircle}
                color="bg-green-600"
              />
              <StatCard
                title={t('analytics.pending')}
                value={stats.businessIdeas.pending}
                icon={Clock}
                color="bg-yellow-600"
              />
              <StatCard
                title={t('analytics.rejected')}
                value={stats.businessIdeas.rejected}
                icon={AlertCircle}
                color="bg-red-600"
              />
            </div>
          </div>

          {/* Posts Stats */}
          <div>
            <RTLText as="h2" className="text-xl font-semibold text-white mb-4">
              {t('analytics.posts')}
            </RTLText>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <StatCard
                title={t('analytics.totalPosts')}
                value={stats.posts.total}
                icon={FileText}
                color="bg-purple-600"
              />
              <StatCard
                title={t('analytics.metrics.views')}
                value={stats.posts.totalViews}
                icon={Eye}
                color="bg-indigo-600"
              />
              <StatCard
                title={t('analytics.metrics.likes')}
                value={stats.posts.totalLikes}
                icon={Heart}
                color="bg-pink-600"
              />
              <StatCard
                title={t('analytics.totalComments')}
                value={stats.posts.totalComments}
                icon={MessageSquare}
                color="bg-cyan-600"
                subtitle={`${stats.posts.total > 0 ? (stats.posts.totalComments / stats.posts.total).toFixed(1) : 0} ${t('analytics.avgPerPost')}`}
              />
            </div>
          </div>

          {/* Events Stats */}
          <div>
            <RTLText as="h2" className="text-xl font-semibold text-white mb-4">
              {t('analytics.events')}
            </RTLText>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <StatCard
                title={t('analytics.totalEvents')}
                value={stats.events.total}
                icon={Calendar}
                color="bg-orange-600"
              />
              <StatCard
                title={t('analytics.upcoming')}
                value={stats.events.upcoming}
                icon={Clock}
                color="bg-green-600"
              />
              <StatCard
                title={t('analytics.past')}
                value={stats.events.past}
                icon={CheckCircle}
                color="bg-gray-600"
              />
              <StatCard
                title={t('analytics.totalAttendees')}
                value={stats.events.totalAttendees}
                icon={Users}
                color="bg-teal-600"
              />
            </div>
          </div>

          {/* Engagement Overview */}
          <div>
            <RTLText as="h2" className="text-xl font-semibold text-white mb-4">
              {t('analytics.metrics.engagement')}
            </RTLText>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <StatCard
                title={t('analytics.metrics.views')}
                value={stats.engagement.totalViews}
                icon={Eye}
                color="bg-blue-600"
              />
              <StatCard
                title={t('analytics.metrics.likes')}
                value={stats.engagement.totalLikes}
                icon={Heart}
                color="bg-pink-600"
              />
              <StatCard
                title={t('analytics.metrics.shares')}
                value={stats.engagement.totalShares}
                icon={Share2}
                color="bg-green-600"
              />
              <StatCard
                title={t('analytics.metrics.engagement')}
                value={`${stats.engagement.engagementRate.toFixed(1)}%`}
                icon={Activity}
                color="bg-purple-600"
              />
            </div>
          </div>

          {/* Quick Actions */}
          <div>
            <RTLText as="h2" className="text-xl font-semibold text-white mb-4">
              {t('analytics.quickActions')}
            </RTLText>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <button className="p-4 bg-blue-600 hover:bg-blue-700 rounded-lg text-white transition-colors flex items-center">
                <Lightbulb size={20} className={isRTL ? 'ml-3' : 'mr-3'} />
                {t('analytics.newIdea')}
              </button>
              <button className="p-4 bg-purple-600 hover:bg-purple-700 rounded-lg text-white transition-colors flex items-center">
                <FileText size={20} className={isRTL ? 'ml-3' : 'mr-3'} />
                {t('analytics.newPost')}
              </button>
              <button className="p-4 bg-orange-600 hover:bg-orange-700 rounded-lg text-white transition-colors flex items-center">
                <Calendar size={20} className={isRTL ? 'ml-3' : 'mr-3'} />
                {t('analytics.newEvent')}
              </button>
              <button className="p-4 bg-green-600 hover:bg-green-700 rounded-lg text-white transition-colors flex items-center">
                <Target size={20} className={isRTL ? 'ml-3' : 'mr-3'} />
                {t('analytics.newUpdate')}
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default UserAnalyticsPage;
