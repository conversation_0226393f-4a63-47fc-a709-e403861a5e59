import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { ArrowLeft, Users, Star, MapPin, Clock, CheckCircle, AlertCircle } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { useAppDispatch, useAppSelector } from '../../store/hooks';
// MainLayout removed - handled by routing system
import { MentorshipApplicationForm, MentorProfileCard } from '../../components/incubator';
import { fetchMentorProfiles } from '../../store/incubatorSlice';
import { MentorProfile } from '../../services/incubatorApi';
const MentorshipApplicationPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [searchParams] = useSearchParams();
  const { mentorProfiles, isLoading, error } = useAppSelector(state => state.incubator);

  const [selectedMentor, setSelectedMentor] = useState<MentorProfile | null>(null);
  const [showApplicationForm, setShowApplicationForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedExpertise, setSelectedExpertise] = useState<string>('all');
  const [applicationSuccess, setApplicationSuccess] = useState(false);

  // Get mentor ID from URL params if specified
  const mentorId = searchParams.get('mentor');

  useEffect(() => {
    dispatch(fetchMentorProfiles());
  }, [dispatch]);

  useEffect(() => {
    // If mentor ID is specified in URL, select that mentor
    if (mentorId && mentorProfiles.length > 0) {
      const mentor = mentorProfiles.find(m => m.id.toString() === mentorId);
      if (mentor) {
        setSelectedMentor(mentor);
        setShowApplicationForm(true);
      }
    }
  }, [mentorId, mentorProfiles]);

  const handleMentorSelect = (mentor: MentorProfile) => {
    setSelectedMentor(mentor);
    setShowApplicationForm(true);
  };

  const handleApplicationSuccess = () => {
    setApplicationSuccess(true);
    setShowApplicationForm(false);
    setTimeout(() => {
      navigate('/dashboard/mentorship');
    }, 3000);
  };

  const handleApplicationCancel = () => {
    setShowApplicationForm(false);
    setSelectedMentor(null);
  };

  // Filter mentors based on search and expertise
  const filteredMentors = mentorProfiles.filter(mentor => {
    const fullName = `${mentor.user.first_name} ${mentor.user.last_name}`;
    const matchesSearch = fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         mentor.expertise_areas.some((exp) => exp.specific_expertise.toLowerCase().includes(searchTerm.toLowerCase())) ||
                         mentor.bio.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesExpertise = selectedExpertise === 'all' ||
                           mentor.expertise_areas.some((exp) => exp.specific_expertise.toLowerCase().includes(selectedExpertise.toLowerCase()));

    return matchesSearch && matchesExpertise && mentor.is_accepting_mentees;
  });

  // Get unique expertise areas for filter
  const expertiseAreas = Array.from(
    new Set(mentorProfiles.flatMap(mentor => mentor.expertise_areas.map(exp => exp.specific_expertise)))
  ).sort();

  if (applicationSuccess) {
    return (
      <AuthenticatedLayout>
      <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="p-6 max-w-2xl mx-auto text-center py-12">
          <CheckCircle size={64} className="mx-auto text-green-400 mb-6" />
          <h1 className="text-3xl font-bold text-white mb-4">
            {t('mentorship.applicationSubmitted')}
          </h1>
          <p className="text-gray-300 mb-6">
            {t('mentorship.applicationSubmittedDescription')}
          </p>
          <div className="bg-indigo-900/50 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
            <h3 className="text-lg font-semibold mb-2">{t('mentorship.nextSteps')}</h3>
            <ul className={` text-gray-300 space-y-2 ${isRTL ? "text-right" : "text-left"}`}>
              <li className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <div className={`w-2 h-2 bg-purple-400 rounded-full mr-3 ${isRTL ? "space-x-reverse" : ""}`}></div>
                {t('mentorship.reviewProcess')}
              </li>
              <li className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <div className={`w-2 h-2 bg-purple-400 rounded-full mr-3 ${isRTL ? "space-x-reverse" : ""}`}></div>
                {t('mentorship.mentorContact')}
              </li>
              <li className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <div className={`w-2 h-2 bg-purple-400 rounded-full mr-3 ${isRTL ? "space-x-reverse" : ""}`}></div>
                {t('mentorship.sessionScheduling')}
              </li>
            </ul>
          </div>
          <p className="text-sm text-gray-400 mt-4">
            {t('mentorship.redirectingToDashboard')}
          </p>
        </div>
                </div>
        </div>
      </div>
    </AuthenticatedLayout>
    );
  }

  if (showApplicationForm && selectedMentor) {
    return (
      <AuthenticatedLayout>
        <div className="p-6 max-w-4xl mx-auto">
          {/* Back button */}
          <div className="mb-6">
            <button
              onClick={handleApplicationCancel}
              className={`text-gray-400 hover:text-gray-300 flex items-center transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
            >
              <ArrowLeft size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
              {t('mentorship.backToMentors')}
            </button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Selected Mentor Info */}
            <div className="lg:col-span-1">
              <h2 className="text-xl font-semibold text-white mb-4">
                {t('mentorship.selectedMentor')}
              </h2>
              <MentorProfileCard
                mentor={selectedMentor}
              />
            </div>

            {/* Application Form */}
            <div className="lg:col-span-2">
              <h2 className="text-xl font-semibold text-white mb-4">
                {t('mentorship.applicationForm')}
              </h2>
              <div className="bg-indigo-900/50 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
                <MentorshipApplicationForm
                  preferredMentor={selectedMentor}
                  onSubmitSuccess={handleApplicationSuccess}
                  onCancel={handleApplicationCancel}
                />
              </div>
            </div>
          </div>
        </div>
      </RoleDashboardLayout>
    );
  }

  return (
    <AuthenticatedLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className={`flex flex-col sm:flex-row sm:items-center sm:justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
          <div>
            <div className="mb-4">
              <Link
                to="/dashboard/mentorship"
                className={`text-gray-400 hover:text-gray-300 flex items-center transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <ArrowLeft size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                {t('mentorship.backToDashboard')}
              </Link>
            </div>
            <h1 className="text-2xl font-bold text-white mb-2">
              {t('mentorship.findMentor')}
            </h1>
            <p className="text-gray-300">
              {t('mentorship.findMentorDescription')}
            </p>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-indigo-900/50 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Search */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('mentorship.searchMentors')}
              </label>
              <input
                type="text"
                placeholder={t('mentorship.searchPlaceholder')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>

            {/* Expertise Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('mentorship.filterByExpertise')}
              </label>
              <select
                value={selectedExpertise}
                onChange={(e) => setSelectedExpertise(e.target.value)}
                className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="all">{t('mentorship.allExpertise')}</option>
                {expertiseAreas.map(expertise => (
                  <option key={expertise} value={expertise}>
                    {expertise}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Mentors Grid */}
        {isLoading ? (
          <div className={`flex justify-center py-12 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <AlertCircle size={48} className="mx-auto text-red-400 mb-4" />
            <h3 className="text-xl font-semibold mb-2">{t('common.error')}</h3>
            <p className="text-gray-400">{error}</p>
          </div>
        ) : filteredMentors.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredMentors.map(mentor => (
              <div key={mentor.id} onClick={() => handleMentorSelect(mentor)} className="cursor-pointer">
                <MentorProfileCard
                  mentor={mentor}
                />
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Users size={48} className="mx-auto text-gray-500 mb-4" />
            <h3 className="text-xl font-semibold mb-2">{t('mentorship.noMentorsFound')}</h3>
            <p className="text-gray-400">
              {searchTerm || selectedExpertise !== 'all'
                ? t('mentorship.tryDifferentSearch')
                : t('mentorship.noMentorsAvailable')
              }
            </p>
          </div>
        )}
      </div>
    </RoleDashboardLayout>
  );
};

export default MentorshipApplicationPage;
