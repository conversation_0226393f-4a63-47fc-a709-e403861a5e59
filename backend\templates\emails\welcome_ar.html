<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', 'Noto Sans Arabic', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            text-align: right;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #8b5cf6;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 5px 5px 0 0;
        }
        .content {
            background-color: #f9fafb;
            padding: 20px;
            border-radius: 0 0 5px 5px;
            border: 1px solid #e5e7eb;
            border-top: none;
        }
        .welcome-box {
            background-color: #ede9fe;
            border-right: 4px solid #8b5cf6;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px 0 0 5px;
        }
        .feature-section {
            margin-bottom: 25px;
        }
        .feature-section h3 {
            color: #8b5cf6;
            margin-bottom: 10px;
        }
        .button {
            display: inline-block;
            background-color: #8b5cf6;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px;
            margin-top: 10px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 20px;
        }
        .feature-card {
            background-color: white;
            border: 1px solid #e5e7eb;
            border-radius: 5px;
            padding: 15px;
        }
        .feature-card h4 {
            color: #8b5cf6;
            margin-top: 0;
        }
        .footer {
            margin-top: 20px;
            text-align: center;
            color: #6b7280;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{ title }}</h1>
        </div>
        <div class="content">
            <div class="welcome-box">
                <h2 style="margin-top: 0;">{{ greeting }} {{ name }}!</h2>
                <p>{{ thank_you }}</p>
            </div>
            
            <div class="feature-section">
                <h3>{{ getting_started }}</h3>
                <p>{{ things_to_do }}</p>
                
                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>{{ complete_profile }}</h4>
                        <p>{{ complete_profile_desc }}</p>
                        <a href="{{ site_url }}/dashboard/profile" class="button">{{ update_profile }}</a>
                    </div>
                    
                    <div class="feature-card">
                        <h4>{{ explore_dashboard }}</h4>
                        <p>{{ explore_dashboard_desc }}</p>
                        <a href="{{ site_url }}/dashboard" class="button">{{ go_to_dashboard }}</a>
                    </div>
                </div>
            </div>
            
            <div class="feature-section">
                <h3>{{ key_features }}</h3>
                <p>{{ key_features_desc }}</p>
                
                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>{{ businessIncubator }}</h4>
                        <p>{{ businessIncubatorDesc }}</p>
                        <a href="{{ site_url }}/dashboard/incubator" class="button">{{ exploreIncubator }}</a>
                    </div>
                    
                    <div class="feature-card">
                        <h4>{{ mentorship }}</h4>
                        <p>{{ mentorshipDesc }}</p>
                        <a href="{{ site_url }}/dashboard/mentorship" class="button">{{ findMentors }}</a>
                    </div>
                    
                    <div class="feature-card">
                        <h4>{{ fundingOpportunities }}</h4>
                        <p>{{ fundingOpportunitiesDesc }}</p>
                        <a href="{{ site_url }}/dashboard/funding" class="button">{{ viewOpportunities }}</a>
                    </div>
                    
                    <div class="feature-card">
                        <h4>{{ aiRecommendations }}</h4>
                        <p>{{ aiRecommendationsDesc }}</p>
                        <a href="{{ site_url }}/dashboard/ai-recommendations" class="button">{{ getRecommendations }}</a>
                    </div>
                </div>
            </div>
            
            <p>{{ commitment }}</p>
            
            <p>{{ regards }}<br>{{ team }}</p>
        </div>
        <div class="footer">
            <p>{{ sentTo }}</p>
            <p>{{ copyright }}</p>
        </div>
    </div>
</body>
</html>
