import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Monitor, Activity, AlertTriangle, CheckCircle, XCircle,
  RefreshCw, Bell, BellOff, Settings, Eye, Clock,
  Server, Database, Zap, Brain, Shield, Globe,
  TrendingUp, TrendingDown, Cpu, HardDrive, Network
} from 'lucide-react';
import AuthenticatedLayout from '../../layout/AuthenticatedLayout';
import { getAuthToken } from '../../../services/api';

interface SystemHealth {
  component: string;
  status: 'healthy' | 'warning' | 'critical' | 'offline';
  response_time: number;
  uptime: number;
  last_check: string;
  error_message?: string;
  metrics: {
    cpu_usage?: number;
    memory_usage?: number;
    disk_usage?: number;
    network_latency?: number;
  };
}

interface AIAlert {
  id: string;
  type: 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: string;
  component: string;
  resolved: boolean;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

interface PerformanceMetric {
  timestamp: string;
  cpu_usage: number;
  memory_usage: number;
  response_time: number;
  requests_per_second: number;
  error_rate: number;
}

const AIMonitoring: React.FC = () => {
  const { t } = useTranslation();
  const [systemHealth, setSystemHealth] = useState<SystemHealth[]>([]);
  const [alerts, setAlerts] = useState<AIAlert[]>([]);
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetric[]>([]);
  const [loading, setLoading] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(30); // seconds
  const [activeTab, setActiveTab] = useState<'health' | 'alerts' | 'performance'>('health');

  useEffect(() => {
    fetchMonitoringData();
    
    if (autoRefresh) {
      const interval = setInterval(fetchMonitoringData, refreshInterval * 1000);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval]);

  const fetchMonitoringData = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/superadmin/system/ai_monitoring/', {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setSystemHealth(data.system_health || []);
        setAlerts(data.alerts || []);
        setPerformanceMetrics(data.performance_metrics || []);
      } else {
        console.error('Failed to fetch AI monitoring data:', response.status);
        setSystemHealth([]);
        setAlerts([]);
        setPerformanceMetrics([]);
      }
    } catch (error) {
      console.error('Failed to fetch monitoring data:', error);
    } finally {
      setLoading(false);
    }
  };

  const resolveAlert = async (alertId: string) => {
    try {
      const response = await fetch(`/api/superadmin/system/ai-monitoring/alerts/${alertId}/resolve/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        setAlerts(prev => prev.map(alert => 
          alert.id === alertId ? { ...alert, resolved: true } : alert
        ));
      }
    } catch (error) {
      console.error('Failed to resolve alert:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-400';
      case 'warning': return 'text-yellow-400';
      case 'critical': return 'text-red-400';
      case 'offline': return 'text-gray-400';
      default: return 'text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="w-5 h-5" />;
      case 'warning': return <AlertTriangle className="w-5 h-5" />;
      case 'critical': return <XCircle className="w-5 h-5" />;
      case 'offline': return <XCircle className="w-5 h-5" />;
      default: return <Activity className="w-5 h-5" />;
    }
  };

  const getAlertColor = (type: string) => {
    switch (type) {
      case 'error': return 'border-red-500 bg-red-900/20';
      case 'warning': return 'border-yellow-500 bg-yellow-900/20';
      case 'info': return 'border-blue-500 bg-blue-900/20';
      default: return 'border-gray-500 bg-gray-900/20';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-400';
      case 'high': return 'text-orange-400';
      case 'medium': return 'text-yellow-400';
      case 'low': return 'text-green-400';
      default: return 'text-gray-400';
    }
  };

  const getComponentIcon = (component: string) => {
    if (component.includes('AI') || component.includes('Gemini')) return <Brain className="w-4 h-4" />;
    if (component.includes('Filter')) return <Shield className="w-4 h-4" />;
    if (component.includes('Analytics')) return <Activity className="w-4 h-4" />;
    if (component.includes('Rate')) return <Zap className="w-4 h-4" />;
    return <Server className="w-4 h-4" />;
  };

  const tabs = [
    { id: 'health', name: 'System Health', icon: <Monitor className="w-4 h-4" /> },
    { id: 'alerts', name: 'Alerts', icon: <Bell className="w-4 h-4" /> },
    { id: 'performance', name: 'Performance', icon: <TrendingUp className="w-4 h-4" /> }
  ];

  const renderSystemHealth = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {systemHealth.map((component) => (
        <div key={component.component} className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              {getComponentIcon(component.component)}
              <h3 className="font-semibold text-white">{component.component}</h3>
            </div>
            <div className={`flex items-center gap-1 ${getStatusColor(component.status)}`}>
              {getStatusIcon(component.status)}
              <span className="text-sm capitalize">{component.status}</span>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Response Time:</span>
              <span className="text-white">{component.response_time.toFixed(1)}s</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Uptime:</span>
              <span className="text-white">{component.uptime.toFixed(1)}%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Last Check:</span>
              <span className="text-white">{new Date(component.last_check).toLocaleTimeString()}</span>
            </div>

            {component.metrics && (
              <div className="mt-4 pt-4 border-t border-gray-700">
                <h4 className="text-sm font-medium text-gray-300 mb-2">Metrics</h4>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  {component.metrics.cpu_usage && (
                    <div className="flex justify-between">
                      <span className="text-gray-400">CPU:</span>
                      <span className="text-white">{component.metrics.cpu_usage}%</span>
                    </div>
                  )}
                  {component.metrics.memory_usage && (
                    <div className="flex justify-between">
                      <span className="text-gray-400">Memory:</span>
                      <span className="text-white">{component.metrics.memory_usage}%</span>
                    </div>
                  )}
                  {component.metrics.network_latency && (
                    <div className="flex justify-between">
                      <span className="text-gray-400">Latency:</span>
                      <span className="text-white">{component.metrics.network_latency}ms</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {component.error_message && (
              <div className="mt-4 p-3 bg-red-900/20 border border-red-500/20 rounded-lg">
                <p className="text-red-300 text-sm">{component.error_message}</p>
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );

  const renderAlerts = () => (
    <div className="space-y-4">
      {alerts.length === 0 ? (
        <div className="text-center py-12">
          <CheckCircle className="w-16 h-16 text-green-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-white mb-2">No Active Alerts</h3>
          <p className="text-gray-400">All systems are running smoothly</p>
        </div>
      ) : (
        alerts.map((alert) => (
          <div key={alert.id} className={`p-4 rounded-lg border ${getAlertColor(alert.type)} ${alert.resolved ? 'opacity-60' : ''}`}>
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  {getComponentIcon(alert.component)}
                  <span className="font-medium text-white">{alert.title}</span>
                  <span className={`text-xs px-2 py-1 rounded-full ${getSeverityColor(alert.severity)} bg-gray-800`}>
                    {alert.severity.toUpperCase()}
                  </span>
                  {alert.resolved && (
                    <span className="text-xs px-2 py-1 rounded-full bg-green-800 text-green-300">
                      RESOLVED
                    </span>
                  )}
                </div>
                <p className="text-gray-300 mb-2">{alert.message}</p>
                <div className="flex items-center gap-4 text-sm text-gray-400">
                  <span>Component: {alert.component}</span>
                  <span>Time: {new Date(alert.timestamp).toLocaleString()}</span>
                </div>
              </div>
              {!alert.resolved && (
                <button
                  onClick={() => resolveAlert(alert.id)}
                  className="ml-4 px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-sm rounded-lg transition-colors"
                >
                  Resolve
                </button>
              )}
            </div>
          </div>
        ))
      )}
    </div>
  );

  const renderPerformance = () => (
    <div className="space-y-6">
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">Performance Metrics (Last 24 Hours)</h3>
        <div className="h-64 flex items-center justify-center border-2 border-dashed border-gray-600 rounded-lg">
          <div className="text-center">
            <TrendingUp className="w-12 h-12 text-gray-500 mx-auto mb-2" />
            <p className="text-gray-500">Performance charts would go here</p>
            <p className="text-sm text-gray-600">Showing {performanceMetrics.length} data points</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {performanceMetrics.length > 0 && (
          <>
            <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
              <div className="flex items-center gap-2 mb-2">
                <Cpu className="w-4 h-4 text-blue-400" />
                <span className="text-sm text-gray-400">Avg CPU Usage</span>
              </div>
              <div className="text-xl font-bold text-white">
                {(performanceMetrics.reduce((sum, m) => sum + m.cpu_usage, 0) / performanceMetrics.length).toFixed(1)}%
              </div>
            </div>

            <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
              <div className="flex items-center gap-2 mb-2">
                <HardDrive className="w-4 h-4 text-green-400" />
                <span className="text-sm text-gray-400">Avg Memory Usage</span>
              </div>
              <div className="text-xl font-bold text-white">
                {(performanceMetrics.reduce((sum, m) => sum + m.memory_usage, 0) / performanceMetrics.length).toFixed(1)}%
              </div>
            </div>

            <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
              <div className="flex items-center gap-2 mb-2">
                <Clock className="w-4 h-4 text-yellow-400" />
                <span className="text-sm text-gray-400">Avg Response Time</span>
              </div>
              <div className="text-xl font-bold text-white">
                {(performanceMetrics.reduce((sum, m) => sum + m.response_time, 0) / performanceMetrics.length).toFixed(1)}s
              </div>
            </div>

            <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
              <div className="flex items-center gap-2 mb-2">
                <Activity className="w-4 h-4 text-purple-400" />
                <span className="text-sm text-gray-400">Avg Requests/sec</span>
              </div>
              <div className="text-xl font-bold text-white">
                {(performanceMetrics.reduce((sum, m) => sum + m.requests_per_second, 0) / performanceMetrics.length).toFixed(1)}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );

  return (
    <AuthenticatedLayout>
      <div className="text-white">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-2">
            <Monitor className="w-8 h-8 text-green-400" />
            <h1 className="text-3xl font-bold text-white">
              {t('superAdmin.aiMonitoring.title', 'AI Monitoring')}
            </h1>
          </div>
          <p className="text-gray-400">
            {t('superAdmin.aiMonitoring.subtitle', 'Real-time monitoring of AI system health and performance')}
          </p>
        </div>

        {/* Controls */}
        <div className="flex flex-wrap items-center justify-between gap-4 mb-8">
          <div className="flex items-center gap-4">
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
                className="rounded border-gray-600 bg-gray-700 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-300">Auto-refresh</span>
            </label>
            
            {autoRefresh && (
              <select
                value={refreshInterval}
                onChange={(e) => setRefreshInterval(Number(e.target.value))}
                className="px-3 py-1 bg-gray-700 border border-gray-600 rounded-lg text-white text-sm"
              >
                <option value={10}>10s</option>
                <option value={30}>30s</option>
                <option value={60}>1m</option>
                <option value={300}>5m</option>
              </select>
            )}
          </div>

          <button
            onClick={fetchMonitoringData}
            disabled={loading}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors disabled:opacity-50"
          >
            {loading ? <RefreshCw className="w-4 h-4 animate-spin" /> : <RefreshCw className="w-4 h-4" />}
            Refresh
          </button>
        </div>

        {/* Tabs */}
        <div className="flex flex-wrap gap-2 mb-8 border-b border-gray-700">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center gap-2 px-4 py-2 rounded-t-lg transition-colors ${
                activeTab === tab.id
                  ? 'bg-green-600 text-white border-b-2 border-green-400'
                  : 'text-gray-400 hover:text-white hover:bg-gray-800'
              }`}
            >
              {tab.icon}
              {tab.name}
              {tab.id === 'alerts' && alerts.filter(a => !a.resolved).length > 0 && (
                <span className="bg-red-500 text-white text-xs rounded-full px-2 py-1 ml-1">
                  {alerts.filter(a => !a.resolved).length}
                </span>
              )}
            </button>
          ))}
        </div>

        {/* Content */}
        {loading && activeTab === 'health' ? (
          <div className="flex items-center justify-center py-12">
            <RefreshCw className="w-8 h-8 animate-spin text-green-400" />
            <span className="ml-2 text-gray-400">Loading monitoring data...</span>
          </div>
        ) : (
          <>
            {activeTab === 'health' && renderSystemHealth()}
            {activeTab === 'alerts' && renderAlerts()}
            {activeTab === 'performance' && renderPerformance()}
          </>
        )}
      </div>
    </AuthenticatedLayout>
  );
};

export default AIMonitoring;
