import React from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { useLocation } from 'react-router-dom';
import AuthenticatedLayout from '../../components/layout/AuthenticatedLayout';
import { 
  TrendingUp, 
  FileText, 
  Eye, 
  BarChart3,
  Settings,
  Users,
  Wrench,
  Target,
  Building,
  Calendar,
  DollarSign
} from 'lucide-react';

const InvestorGenericPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const location = useLocation();
  
  // Extract page info from pathname
  const getPageInfo = () => {
    const path = location.pathname;
    
    if (path.includes('/deals')) {
      return {
        title: t('Active Deals'),
        description: t('Manage your active investment deals and negotiations'),
        icon: FileText,
        color: 'blue'
      };
    }
    
    if (path.includes('/due-diligence')) {
      return {
        title: t('Due Diligence'),
        description: t('Comprehensive analysis and risk assessment tools'),
        icon: Eye,
        color: 'purple'
      };
    }
    
    if (path.includes('/market-analysis')) {
      return {
        title: t('Market Analysis'),
        description: t('Market trends and sector analysis'),
        icon: TrendingUp,
        color: 'green'
      };
    }
    
    if (path.includes('/reports')) {
      return {
        title: t('Investment Reports'),
        description: t('Detailed reports and performance analytics'),
        icon: BarChart3,
        color: 'indigo'
      };
    }
    
    if (path.includes('/profile')) {
      return {
        title: t('Investor Profile'),
        description: t('Manage your investor profile and credentials'),
        icon: Users,
        color: 'purple'
      };
    }
    
    if (path.includes('/preferences')) {
      return {
        title: t('Investment Preferences'),
        description: t('Configure your investment criteria and preferences'),
        icon: Settings,
        color: 'gray'
      };
    }
    
    if (path.includes('/tools')) {
      return {
        title: t('Investment Tools'),
        description: t('Calculators and analysis tools for investors'),
        icon: Wrench,
        color: 'orange'
      };
    }
    
    if (path.includes('/network')) {
      return {
        title: t('Investor Network'),
        description: t('Connect with other investors and entrepreneurs'),
        icon: Users,
        color: 'blue'
      };
    }
    
    if (path.includes('/trends')) {
      return {
        title: t('Market Trends'),
        description: t('Latest market trends and investment insights'),
        icon: TrendingUp,
        color: 'green'
      };
    }
    
    if (path.includes('/sectors')) {
      return {
        title: t('Sector Analysis'),
        description: t('Industry sector performance and opportunities'),
        icon: Building,
        color: 'blue'
      };
    }
    
    if (path.includes('/criteria')) {
      return {
        title: t('Investment Criteria'),
        description: t('Define and manage your investment criteria'),
        icon: Target,
        color: 'red'
      };
    }
    
    if (path.includes('/performance')) {
      return {
        title: t('Performance Metrics'),
        description: t('Track and analyze investment performance'),
        icon: BarChart3,
        color: 'green'
      };
    }
    
    if (path.includes('/connections')) {
      return {
        title: t('Connections'),
        description: t('Manage your professional connections'),
        icon: Users,
        color: 'blue'
      };
    }
    
    if (path.includes('/calculators')) {
      return {
        title: t('Investment Calculators'),
        description: t('Financial calculators and modeling tools'),
        icon: Wrench,
        color: 'purple'
      };
    }
    
    // Default fallback
    return {
      title: t('Investor Dashboard'),
      description: t('Investment management and analysis'),
      icon: TrendingUp,
      color: 'purple'
    };
  };

  const pageInfo = getPageInfo();
  const IconComponent = pageInfo.icon;

  const getColorClasses = (color: string) => {
    const colorMap = {
      purple: 'bg-purple-100 text-purple-600 border-purple-200',
      blue: 'bg-blue-100 text-blue-600 border-blue-200',
      green: 'bg-green-100 text-green-600 border-green-200',
      red: 'bg-red-100 text-red-600 border-red-200',
      orange: 'bg-orange-100 text-orange-600 border-orange-200',
      indigo: 'bg-indigo-100 text-indigo-600 border-indigo-200',
      gray: 'bg-gray-100 text-gray-600 border-gray-200'
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.purple;
  };

  return (
    <AuthenticatedLayout>
      <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="p-6">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <div className={`p-3 rounded-lg ${getColorClasses(pageInfo.color)}`}>
              <IconComponent className="h-8 w-8" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-white">{pageInfo.title}</h1>
              <p className="text-gray-300 text-lg">{pageInfo.description}</p>
            </div>
          </div>
        </div>

        {/* Coming Soon Content */}
        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-8 border border-white/20 text-center">
          <div className="max-w-md mx-auto">
            <IconComponent className="h-16 w-16 text-gray-400 mx-auto mb-6" />
            
            <h2 className="text-2xl font-bold text-white mb-4">
              {t('Coming Soon')}
            </h2>
            
            <p className="text-gray-300 mb-6">
              {t('This feature is currently under development. We\'re working hard to bring you the best investment management tools.')}
            </p>
            
            <div className="bg-white/5 rounded-lg p-4 mb-6">
              <h3 className="text-lg font-semibold text-white mb-2">
                {t('What to expect:')}
              </h3>
              <ul className="text-gray-300 text-left space-y-2">
                <li className="flex items-center">
                  <div className="w-2 h-2 bg-purple-400 rounded-full mr-3"></div>
                  {t('Advanced analytics and reporting')}
                </li>
                <li className="flex items-center">
                  <div className="w-2 h-2 bg-purple-400 rounded-full mr-3"></div>
                  {t('Real-time data and insights')}
                </li>
                <li className="flex items-center">
                  <div className="w-2 h-2 bg-purple-400 rounded-full mr-3"></div>
                  {t('Intuitive user interface')}
                </li>
                <li className="flex items-center">
                  <div className="w-2 h-2 bg-purple-400 rounded-full mr-3"></div>
                  {t('Comprehensive investment tools')}
                </li>
              </ul>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <button className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                {t('Get Notified')}
              </button>
              <button className="px-6 py-3 bg-white/20 text-white rounded-lg hover:bg-white/30 transition-colors">
                {t('View Roadmap')}
              </button>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
            <div className="flex items-center space-x-3 mb-3">
              <Target className="h-6 w-6 text-purple-400" />
              <h3 className="text-lg font-semibold text-white">{t('Opportunities')}</h3>
            </div>
            <p className="text-gray-300 text-sm mb-4">
              {t('Explore new investment opportunities')}
            </p>
            <button className="w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors text-sm">
              {t('View Opportunities')}
            </button>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
            <div className="flex items-center space-x-3 mb-3">
              <BarChart3 className="h-6 w-6 text-blue-400" />
              <h3 className="text-lg font-semibold text-white">{t('Portfolio')}</h3>
            </div>
            <p className="text-gray-300 text-sm mb-4">
              {t('Track your investment portfolio')}
            </p>
            <button className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm">
              {t('View Portfolio')}
            </button>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
            <div className="flex items-center space-x-3 mb-3">
              <TrendingUp className="h-6 w-6 text-green-400" />
              <h3 className="text-lg font-semibold text-white">{t('Analytics')}</h3>
            </div>
            <p className="text-gray-300 text-sm mb-4">
              {t('Analyze market trends and performance')}
            </p>
            <button className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors text-sm">
              {t('View Analytics')}
            </button>
          </div>
        </div>
      </div>
              </div>
        </div>
      </div>
    </AuthenticatedLayout>
  );
};

export default InvestorGenericPage;
