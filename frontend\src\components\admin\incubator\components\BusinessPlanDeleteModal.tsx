import React, { useState } from 'react';
import { Trash2, <PERSON><PERSON><PERSON><PERSON>gle, FileText } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../hooks/useLanguage';
import { useDeleteBusinessPlan } from '../../../../hooks/useBusinessPlans';
import { BusinessPlan } from '../../../../services/businessPlanApi';
import { Button } from '../../../ui';

interface BusinessPlanDeleteModalProps {
  isOpen: boolean;
  onClose: () => void;
  plan: BusinessPlan | null;
}

const BusinessPlanDeleteModal: React.FC<BusinessPlanDeleteModalProps> = ({
  isOpen,
  onClose,
  plan
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  const deletePlan = useDeleteBusinessPlan();
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    if (!plan) return;

    setIsDeleting(true);
    try {
      await deletePlan.mutateAsync(plan.id);
      onClose();
    } catch (error) {
      console.error('Failed to delete business plan:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  if (!isOpen || !plan) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-indigo-900/90 rounded-xl shadow-lg w-full max-w-md">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center gap-3 mb-4">
            <div className="w-12 h-12 rounded-full bg-red-500/20 flex items-center justify-center">
              <AlertTriangle className="w-6 h-6 text-red-400" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">
                {t('businessPlan.deletePlan', 'Delete Business Plan')}
              </h2>
              <p className="text-gray-400 text-sm">
                {t('common.actionCannotBeUndone', 'This action cannot be undone')}
              </p>
            </div>
          </div>

          {/* Content */}
          <div className="mb-6">
            <p className="text-gray-300 mb-4">
              {t('businessPlan.deleteConfirmation', 'Are you sure you want to delete this business plan?')}
            </p>
            
            <div className="bg-indigo-800/30 rounded-lg p-4 border border-indigo-700/50">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-blue-700 flex items-center justify-center">
                  <FileText className="w-5 h-5 text-blue-300" />
                </div>
                <div>
                  <div className="text-white font-medium">
                    {plan.title || 'Untitled Plan'}
                  </div>
                  <div className="text-gray-400 text-sm">
                    {plan.business_idea_title}
                  </div>
                  <div className="text-gray-500 text-xs">
                    Status: {plan.status_display}
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-4 p-3 bg-red-900/20 border border-red-800/50 rounded-lg">
              <p className="text-red-300 text-sm">
                <strong>{t('common.warning', 'Warning')}:</strong> {t('businessPlan.deleteWarning', 'Deleting this business plan will permanently remove all content and cannot be recovered.')}
              </p>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3">
            <Button
              type="button"
              onClick={onClose}
              variant="secondary"
              disabled={isDeleting}
            >
              {t('common.cancel', 'Cancel')}
            </Button>
            <Button
              type="button"
              onClick={handleDelete}
              variant="danger"
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  {t('common.deleting', 'Deleting...')}
                </>
              ) : (
                <>
                  <Trash2 className="w-4 h-4 mr-2" />
                  {t('common.delete', 'Delete')}
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BusinessPlanDeleteModal;
