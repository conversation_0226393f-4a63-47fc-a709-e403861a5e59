import React, { useState, useEffect } from 'react';
import { Search, Target, Calendar, CheckCircle, Clock, AlertTriangle, TrendingUp, Flag } from 'lucide-react';
import { DashboardLayout } from '../dashboard';
import { BusinessMilestone, businessMilestonesAPI } from '../../../services/incubatorApi';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';

const BusinessMilestonesManagement: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  // State management
  const [milestones, setMilestones] = useState<BusinessMilestone[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch business milestones
  const fetchMilestones = async () => {
    try {
      setLoading(true);
      const data = await businessMilestonesAPI.getBusinessMilestones();
      setMilestones(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error fetching business milestones:', error);
      setMilestones([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMilestones();
  }, []);

  // Filter milestones based on search
  const filteredMilestones = milestones.filter(milestone => {
    return milestone.business_idea.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      milestone.business_idea.user.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      milestone.business_idea.user.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      milestone.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (milestone.description && milestone.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
      milestone.status.toLowerCase().includes(searchTerm.toLowerCase());
  });

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Get milestone status display
  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'not_started':
        return {
          color: 'bg-gray-500/20 text-gray-300',
          icon: <Clock size={16} />,
          label: t('milestone.status.not_started', 'Not Started')
        };
      case 'in_progress':
        return {
          color: 'bg-blue-500/20 text-blue-300',
          icon: <TrendingUp size={16} />,
          label: t('milestone.status.in_progress', 'In Progress')
        };
      case 'completed':
        return {
          color: 'bg-green-500/20 text-green-300',
          icon: <CheckCircle size={16} />,
          label: t('milestone.status.completed', 'Completed')
        };
      case 'overdue':
        return {
          color: 'bg-red-500/20 text-red-300',
          icon: <AlertTriangle size={16} />,
          label: t('milestone.status.overdue', 'Overdue')
        };
      case 'cancelled':
        return {
          color: 'bg-orange-500/20 text-orange-300',
          icon: <AlertTriangle size={16} />,
          label: t('milestone.status.cancelled', 'Cancelled')
        };
      default:
        return {
          color: 'bg-gray-500/20 text-gray-300',
          icon: <Clock size={16} />,
          label: status
        };
    }
  };

  // Get milestone priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-500/20 text-red-300';
      case 'medium':
        return 'bg-yellow-500/20 text-yellow-300';
      case 'low':
        return 'bg-green-500/20 text-green-300';
      default:
        return 'bg-gray-500/20 text-gray-300';
    }
  };

  // Get milestone type icon
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'revenue':
        return <TrendingUp size={16} className="text-green-400" />;
      case 'product':
        return <Target size={16} className="text-blue-400" />;
      case 'team':
        return <Flag size={16} className="text-purple-400" />;
      case 'funding':
        return <Target size={16} className="text-orange-400" />;
      case 'market':
        return <TrendingUp size={16} className="text-cyan-400" />;
      default:
        return <Target size={16} className="text-gray-400" />;
    }
  };

  // Calculate days until deadline
  const getDaysUntilDeadline = (deadline: string) => {
    const today = new Date();
    const deadlineDate = new Date(deadline);
    const diffTime = deadlineDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  return (
    <DashboardLayout currentPage="incubator">
      {/* Header */}
      <div className={`mb-8 ${isRTL ? "text-right" : ""}`}>
        <h1 className="text-2xl font-bold text-white">{t('admin.business.milestones.management', 'Business Milestones Management')}</h1>
        <div className="text-gray-400 mt-1">{t('admin.business.milestones.description', 'Track and manage business milestones and goals')}</div>
      </div>

      {/* Search */}
      <div className="mb-6">
        <div className="relative">
          <Search size={20} className={`absolute top-3 text-gray-400 ${isRTL ? "right-3" : "left-3"}`} />
          <input
            type="text"
            placeholder={t('admin.search.milestones', 'Search business milestones...')}
            value={searchTerm}
            onChange={handleSearchChange}
            className={`w-full bg-indigo-900/50 border border-indigo-800 rounded-lg py-2.5 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 ${isRTL ? "pr-10 pl-4" : "pl-10 pr-4"}`}
          />
        </div>
      </div>

      {/* Milestones List */}
      {loading ? (
        <div className="flex justify-center py-12">
          <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      ) : filteredMilestones.length > 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredMilestones.map((milestone) => {
            const statusDisplay = getStatusDisplay(milestone.status);
            const priorityColor = getPriorityColor(milestone.priority);
            const typeIcon = getTypeIcon(milestone.milestone_type);
            const daysUntilDeadline = milestone.target_date ? getDaysUntilDeadline(milestone.target_date) : null;
            
            return (
              <div key={milestone.id} className="bg-indigo-900/30 backdrop-blur-sm rounded-xl overflow-hidden border border-indigo-800/50 hover:border-purple-500/50 transition-all duration-300 hover:shadow-glow">
                <div className="p-6">
                  <div className={`flex justify-between items-start mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className="flex-1">
                      <div className={`flex items-center space-x-2 mb-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 ${statusDisplay.color} ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                          {statusDisplay.icon}
                          <span>{statusDisplay.label}</span>
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${priorityColor}`}>
                          {t(`milestone.priority.${milestone.priority}`, milestone.priority)}
                        </span>
                      </div>
                      
                      <div className={`flex items-center space-x-2 mb-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                        {typeIcon}
                        <h3 className="text-lg font-semibold text-white line-clamp-2">{milestone.title}</h3>
                      </div>
                    </div>
                  </div>

                  {/* Business Idea Info */}
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-300 mb-2">{t('milestone.business.idea', 'Business Idea')}</h4>
                    <div className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                      <div className="w-8 h-8 rounded-full bg-purple-700 flex items-center justify-center text-white text-xs font-bold">
                        {milestone.business_idea.user.first_name.charAt(0)}{milestone.business_idea.user.last_name.charAt(0)}
                      </div>
                      <div>
                        <p className="text-white text-sm font-medium line-clamp-1">{milestone.business_idea.title}</p>
                        <p className="text-gray-400 text-xs">
                          {milestone.business_idea.user.first_name} {milestone.business_idea.user.last_name}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Milestone Details */}
                  <div className="space-y-3">
                    {milestone.description && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-300 mb-1">{t('milestone.description', 'Description')}</h4>
                        <p className="text-gray-300 text-sm line-clamp-3">{milestone.description}</p>
                      </div>
                    )}

                    {/* Progress */}
                    {milestone.progress_percentage !== undefined && (
                      <div>
                        <div className={`flex justify-between items-center mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                          <span className="text-sm font-medium text-gray-300">{t('milestone.progress', 'Progress')}</span>
                          <span className="text-sm font-medium text-white">{milestone.progress_percentage}%</span>
                        </div>
                        <div className="w-full bg-gray-700 rounded-full h-2">
                          <div 
                            className={`h-2 rounded-full transition-all duration-300 ${
                              milestone.progress_percentage >= 80 ? 'bg-green-500' :
                              milestone.progress_percentage >= 60 ? 'bg-blue-500' :
                              milestone.progress_percentage >= 40 ? 'bg-yellow-500' :
                              milestone.progress_percentage >= 20 ? 'bg-orange-500' : 'bg-red-500'
                            }`}
                            style={{ width: `${milestone.progress_percentage}%` }}
                          ></div>
                        </div>
                      </div>
                    )}

                    {/* Target Value */}
                    {milestone.target_value && (
                      <div className={`flex items-center text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
                        <Target size={16} className={`text-blue-400 ${isRTL ? "ml-2" : "mr-2"}`} />
                        <span className="text-gray-300">{t('milestone.target', 'Target')}: </span>
                        <span className="text-white font-medium ml-1">{milestone.target_value}</span>
                      </div>
                    )}

                    {/* Current Value */}
                    {milestone.current_value && (
                      <div className={`flex items-center text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
                        <TrendingUp size={16} className={`text-green-400 ${isRTL ? "ml-2" : "mr-2"}`} />
                        <span className="text-gray-300">{t('milestone.current', 'Current')}: </span>
                        <span className="text-white font-medium ml-1">{milestone.current_value}</span>
                      </div>
                    )}

                    {/* Dates */}
                    <div className="grid grid-cols-2 gap-3">
                      {milestone.target_date && (
                        <div>
                          <div className={`flex items-center space-x-1 text-xs ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                            <Calendar size={14} className="text-blue-400" />
                            <span className="text-gray-400">{t('milestone.target.date', 'Target Date')}</span>
                          </div>
                          <p className="text-white text-sm mt-1">{new Date(milestone.target_date).toLocaleDateString()}</p>
                          {daysUntilDeadline !== null && (
                            <p className={`text-xs mt-1 ${
                              daysUntilDeadline < 0 ? 'text-red-300' :
                              daysUntilDeadline < 7 ? 'text-yellow-300' : 'text-green-300'
                            }`}>
                              {daysUntilDeadline < 0 
                                ? t('milestone.overdue.by', 'Overdue by {{days}} days', { days: Math.abs(daysUntilDeadline) })
                                : t('milestone.due.in', 'Due in {{days}} days', { days: daysUntilDeadline })
                              }
                            </p>
                          )}
                        </div>
                      )}

                      {milestone.completed_date && (
                        <div>
                          <div className={`flex items-center space-x-1 text-xs ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                            <CheckCircle size={14} className="text-green-400" />
                            <span className="text-gray-400">{t('milestone.completed.date', 'Completed')}</span>
                          </div>
                          <p className="text-white text-sm mt-1">{new Date(milestone.completed_date).toLocaleDateString()}</p>
                        </div>
                      )}
                    </div>

                    {/* Notes */}
                    {milestone.notes && (
                      <div className="pt-3 border-t border-indigo-800/50">
                        <h4 className="text-sm font-medium text-gray-300 mb-1">{t('milestone.notes', 'Notes')}</h4>
                        <p className="text-gray-300 text-sm line-clamp-2">{milestone.notes}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        <div className="text-center py-12">
          <Target size={48} className="mx-auto text-gray-600 mb-4" />
          <h3 className="text-lg font-medium text-gray-400 mb-2">
            {searchTerm 
              ? t('admin.no.milestones.found', 'No business milestones found')
              : t('admin.no.milestones', 'No business milestones yet')
            }
          </h3>
          <p className="text-gray-500 mb-4">
            {searchTerm
              ? t('admin.try.different.search', 'Try adjusting your search')
              : t('admin.no.milestones.description', 'Business milestones will appear here when entrepreneurs set their goals')
            }
          </p>
        </div>
      )}
    </DashboardLayout>
  );
};

export default BusinessMilestonesManagement;
