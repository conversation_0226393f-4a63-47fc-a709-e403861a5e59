import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  Bell,
  AlertTriangle,
  Info,
  CheckCircle,
  XCircle,
  X,
  Eye,
  EyeOff
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../hooks/useLanguage';
import { RTLText } from '../../../common';

interface Notification {
  id: string;
  type: 'info' | 'warning' | 'success' | 'error';
  title: string;
  message: string;
  timestamp: Date;
  isRead: boolean;
  actionUrl?: string;
}

interface NotificationsCenterProps {
  className?: string;
}

const NotificationsCenter: React.FC<NotificationsCenterProps> = ({ className = ''  }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [notifications, setNotifications] = useState<Notification[]>([]);

  // Fetch real notifications from API
  useEffect(() => {
    const fetchNotifications = async () => {
      try {
        const response = await fetch('/api/admin/notifications');

        if (response.ok) {
          const data = await response.json();
          setNotifications(data.notifications || []);
        } else {
          throw new Error('Failed to fetch notifications');
        }
      } catch (error) {
        console.error('Error fetching notifications:', error);

        // Fallback to sample notifications if API fails
        const fallbackNotifications: Notification[] = [
          {
            id: '1',
            type: 'warning',
            title: t('admin.notifications.pendingMemberships', 'Pending Memberships'),
            message: t('admin.notifications.membershipMessage', '5 new membership applications require review'),
            timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
            isRead: false,
            actionUrl: '/admin/membership'
          },
          {
            id: '2',
            type: 'info',
            title: t('admin.notifications.newEvent', 'New Event Created'),
            message: t('admin.notifications.eventMessage', 'Tech Meetup 2024 has been scheduled'),
            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
            isRead: false,
            actionUrl: '/admin/events'
          },
          {
            id: '3',
            type: 'success',
            title: t('admin.notifications.systemUpdate', 'System Update Complete'),
            message: t('admin.notifications.updateMessage', 'Platform updated to version 2.1.0'),
            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6), // 6 hours ago
            isRead: true
          },
          {
            id: '4',
            type: 'error',
            title: t('admin.notifications.storageWarning', 'Storage Warning'),
            message: t('admin.notifications.storageMessage', 'Disk usage is above 80% threshold'),
            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 12), // 12 hours ago
            isRead: false,
            actionUrl: '/admin/system-logs'
          }
        ];

        setNotifications(fallbackNotifications);
      }
    };

    fetchNotifications();

    // Auto-refresh notifications every 2 minutes
    const interval = setInterval(fetchNotifications, 2 * 60 * 1000);

    return () => clearInterval(interval);
  }, [t]);

  const [showOnlyUnread, setShowOnlyUnread] = useState(false);

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'info':
        return <Info size={16} className="text-blue-400" />;
      case 'warning':
        return <AlertTriangle size={16} className="text-yellow-400" />;
      case 'success':
        return <CheckCircle size={16} className="text-green-400" />;
      case 'error':
        return <XCircle size={16} className="text-red-400" />;
      default:
        return <Info size={16} className="text-gray-400" />;
    }
  };

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'info':
        return 'border-blue-500/30 bg-blue-500/10';
      case 'warning':
        return 'border-yellow-500/30 bg-yellow-500/10';
      case 'success':
        return 'border-green-500/30 bg-green-500/10';
      case 'error':
        return 'border-red-500/30 bg-red-500/10';
      default:
        return 'border-gray-500/30 bg-gray-500/10';
    }
  };

  const markAsRead = async (id: string) => {
    try {
      // Update in database
      const response = await fetch(`/api/admin/notifications/${id}/read`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        // Update local state
        setNotifications(prev =>
          prev.map(notif =>
            notif.id === id ? { ...notif, isRead: true } : notif
          )
        );
      } else {
        console.error('Failed to mark notification as read');
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
      // Still update local state as fallback
      setNotifications(prev =>
        prev.map(notif =>
          notif.id === id ? { ...notif, isRead: true } : notif
        )
      );
    }
  };

  const dismissNotification = async (id: string) => {
    try {
      // Delete from database
      const response = await fetch(`/api/admin/notifications/${id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        // Update local state
        setNotifications(prev => prev.filter(notif => notif.id !== id));
      } else {
        console.error('Failed to dismiss notification');
      }
    } catch (error) {
      console.error('Error dismissing notification:', error);
      // Still update local state as fallback
      setNotifications(prev => prev.filter(notif => notif.id !== id));
    }
  };

  const formatTimeAgo = (timestamp: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60));

    if (diffInMinutes < 60) {
      return `${diffInMinutes}${t('admin.minutesAgo')}`;
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60);
      return `${hours}${t('admin.hoursAgo')}`;
    } else {
      const days = Math.floor(diffInMinutes / 1440);
      return `${days}${t('admin.daysAgo')}`;
    }
  };

  const filteredNotifications = showOnlyUnread
    ? notifications.filter(n => !n.isRead)
    : notifications;

  const unreadCount = notifications.filter(n => !n.isRead).length;

  return (
    <div className={`bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-6 ${className}`}>
      <div className={`flex items-center justify-between mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
        <div className={`flex items-center space-x-3 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
          <div className="relative p-2 bg-purple-600/20 rounded-lg">
            <Bell size={20} className="text-purple-400" />
            {unreadCount > 0 && (
              <div className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center animate-pulse">
                {unreadCount}
              </div>
            )}
          </div>
          <div>
            <RTLText as="h3" className="text-lg font-semibold text-white">{t('admin.notifications.title', 'Notifications')}</RTLText>
            <RTLText as="p" className="text-sm text-gray-400">
              {unreadCount > 0 ?
                t('admin.notifications.unreadCount', `${unreadCount} unread notifications`) :
                t('admin.notifications.allRead', 'All notifications read')
              }
            </RTLText>
          </div>
        </div>

        <button
          onClick={() => setShowOnlyUnread(!showOnlyUnread)}
          className={`flex items-center px-4 py-2 bg-purple-600/20 hover:bg-purple-600/30 border border-purple-500/30 rounded-xl text-sm transition-all duration-300 hover:scale-105 ${isRTL ? 'flex-row-reverse' : ''}`}
        >
          {showOnlyUnread ? <Eye size={16} className="text-purple-400" /> : <EyeOff size={16} className="text-purple-400" />}
          <span className={`text-white ${isRTL ? 'mr-2' : 'ml-2'}`}>
            {showOnlyUnread ? t('admin.showAll', 'Show All') : t('admin.unreadOnly', 'Unread Only')}
          </span>
        </button>
      </div>

      <div className="space-y-4 max-h-96 overflow-y-auto scrollbar-thin scrollbar-thumb-purple-500/50 scrollbar-track-transparent">
        {filteredNotifications.length === 0 ? (
          <div className="text-center py-12 text-gray-400">
            <div className="p-4 bg-white/5 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
              <Bell size={32} className="opacity-50" />
            </div>
            <RTLText className="text-lg font-medium mb-2">
              {showOnlyUnread ?
                t('admin.notifications.noUnread', 'No unread notifications') :
                t('admin.notifications.noNotifications', 'No notifications')
              }
            </RTLText>
            <RTLText className="text-sm">
              {t('admin.notifications.allCaughtUp', 'You\'re all caught up!')}
            </RTLText>
          </div>
        ) : (
          filteredNotifications.map((notification) => (
            <div
              key={notification.id}
              className={`group relative p-5 rounded-xl border transition-all duration-300 hover:scale-[1.02] hover:shadow-lg ${
                getNotificationColor(notification.type)}
              } ${notification.isRead ? 'opacity-70' : 'shadow-md'}`}
            >
              {/* Unread indicator */}
              {!notification.isRead && (
                <div className="absolute top-3 right-3 w-3 h-3 bg-purple-500 rounded-full animate-pulse"></div>
              )}

              <div className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className={`flex-shrink-0 p-2 rounded-lg ${
                  notification.type === 'info' ? 'bg-blue-500/20' :
                  notification.type === 'warning' ? 'bg-yellow-500/20' :
                  notification.type === 'success' ? 'bg-green-500/20' :
                  'bg-red-500/20'
                } group-hover:scale-110 transition-transform duration-300 ${isRTL ? 'ml-4' : 'mr-4'}`}>
                  {getNotificationIcon(notification.type)}
                </div>

                <div className="flex-1 min-w-0">
                  <div className={`flex items-start justify-between mb-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <RTLText as="h4" className="font-semibold text-white text-base leading-tight">
                      {notification.title}
                    </RTLText>
                    <RTLText as="span" className="text-xs text-gray-400 whitespace-nowrap">
                      {formatTimeAgo(notification.timestamp)}
                    </RTLText>
                  </div>

                  <RTLText as="p" className="text-sm text-gray-300 mb-4 leading-relaxed">
                    {notification.message}
                  </RTLText>

                  <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className={`flex items-center space-x-3 ${isRTL ? 'space-x-reverse' : ''}`}>
                      {notification.actionUrl && (
                        <Link
                          to={notification.actionUrl}
                          className="inline-flex items-center px-3 py-1.5 bg-purple-600/30 hover:bg-purple-600/50 text-purple-300 hover:text-white text-xs rounded-lg transition-all duration-300 hover:scale-105"
                          onClick={() => markAsRead(notification.id)}
                        >
                          {t('admin.viewDetails', 'View Details')}
                        </Link>
                      )}

                      {!notification.isRead && (
                        <button
                          onClick={() => markAsRead(notification.id)}
                          className="inline-flex items-center px-3 py-1.5 bg-white/10 hover:bg-white/20 text-gray-300 hover:text-white text-xs rounded-lg transition-all duration-300"
                        >
                          {t('admin.markRead', 'Mark as Read')}
                        </button>
                      )}
                    </div>

                    <button
                      onClick={() => dismissNotification(notification.id)}
                      className="p-1.5 rounded-lg bg-white/10 hover:bg-red-500/20 text-gray-400 hover:text-red-400 transition-all duration-300 hover:scale-110"
                      title={t('admin.dismiss', 'Dismiss')}
                    >
                      <X size={14} />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default NotificationsCenter;
