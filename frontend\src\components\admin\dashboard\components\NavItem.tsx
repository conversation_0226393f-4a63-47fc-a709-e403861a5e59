import React from 'react';
import { Link } from 'react-router-dom';
import { NavItemType } from '../types';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../hooks/useLanguage';
interface NavItemProps {
  item: NavItemType;
  isActive: boolean;
}

export const NavItem: React.FC<NavItemProps> = ({ item, isActive  }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  return (
    <Link
      to={item.path}
      className={`flex items-center px-4 py-3 rounded-lg transition-colors ${
        isActive
          ? 'bg-purple-600/30 text-white'
          : 'text-gray-300 hover:bg-white/20 hover:text-white'}
      } ${isRTL ? 'text-right' : 'text-left'}`}
    >
      <span className={isRTL ? 'ml-3' : 'mr-3'}>{item.icon}</span>
      {item.name}
    </Link>
  );
};
