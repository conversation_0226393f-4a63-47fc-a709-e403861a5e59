import React, { useState } from 'react';
import { Trash2, <PERSON>ert<PERSON>riangle, DollarSign } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../hooks/useLanguage';
import { useDeleteFundingOpportunity } from '../../../../hooks/useFunding';
import { FundingOpportunity } from '../../../../services/incubatorApi';
import { Button } from '../../../ui';

interface FundingOpportunityDeleteModalProps {
  isOpen: boolean;
  onClose: () => void;
  opportunity: FundingOpportunity | null;
}

const FundingOpportunityDeleteModal: React.FC<FundingOpportunityDeleteModalProps> = ({
  isOpen,
  onClose,
  opportunity
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  const deleteOpportunity = useDeleteFundingOpportunity();
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    if (!opportunity) return;

    setIsDeleting(true);
    try {
      await deleteOpportunity.mutateAsync(opportunity.id);
      onClose();
    } catch (error) {
      console.error('Failed to delete funding opportunity:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  if (!isOpen || !opportunity) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-indigo-900/90 rounded-xl shadow-lg w-full max-w-md">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center gap-3 mb-4">
            <div className="w-12 h-12 rounded-full bg-red-500/20 flex items-center justify-center">
              <AlertTriangle className="w-6 h-6 text-red-400" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">
                {t('funding.deleteOpportunity', 'Delete Funding Opportunity')}
              </h2>
              <p className="text-gray-400 text-sm">
                {t('common.actionCannotBeUndone', 'This action cannot be undone')}
              </p>
            </div>
          </div>

          {/* Content */}
          <div className="mb-6">
            <p className="text-gray-300 mb-4">
              {t('funding.deleteConfirmation', 'Are you sure you want to delete this funding opportunity?')}
            </p>
            
            <div className="bg-indigo-800/30 rounded-lg p-4 border border-indigo-700/50">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-green-700 flex items-center justify-center">
                  <DollarSign className="w-5 h-5 text-green-300" />
                </div>
                <div>
                  <div className="text-white font-medium">
                    {opportunity.title}
                  </div>
                  <div className="text-gray-400 text-sm">
                    {opportunity.funding_type} • ${opportunity.min_amount?.toLocaleString()} - ${opportunity.max_amount?.toLocaleString()}
                  </div>
                  <div className="text-gray-500 text-xs">
                    Status: {opportunity.status}
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-4 p-3 bg-red-900/20 border border-red-800/50 rounded-lg">
              <p className="text-red-300 text-sm">
                <strong>{t('common.warning', 'Warning')}:</strong> {t('funding.deleteWarning', 'Deleting this funding opportunity will also remove all associated applications and cannot be recovered.')}
              </p>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3">
            <Button
              type="button"
              onClick={onClose}
              variant="secondary"
              disabled={isDeleting}
            >
              {t('common.cancel', 'Cancel')}
            </Button>
            <Button
              type="button"
              onClick={handleDelete}
              variant="danger"
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  {t('common.deleting', 'Deleting...')}
                </>
              ) : (
                <>
                  <Trash2 className="w-4 h-4 mr-2" />
                  {t('common.delete', 'Delete')}
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FundingOpportunityDeleteModal;
