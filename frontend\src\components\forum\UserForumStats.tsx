import React, { useState, useEffect } from 'react';
import { useAppSelector } from '../../store/hooks';
import { userAPI } from '../../services/api';
import {
  MessageSquare,
  ThumbsUp,
  Award,
  TrendingUp,
  Calendar,
  RefreshCw,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { useTranslation } from 'react-i18next';

const UserForumStats: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const { user } = useAppSelector(state => state.auth);
  const [loading, setLoading] = useState<boolean>(true);
  const [forumActivity, setForumActivity] = useState<any>(null);
  const [showRecentThreads, setShowRecentThreads] = useState<boolean>(true);
  const [showRecentPosts, setShowRecentPosts] = useState<boolean>(true);
  const [showRecentActivities, setShowRecentActivities] = useState<boolean>(true);

  useEffect(() => {
    fetchForumActivity();
  }, []);

  const fetchForumActivity = async () => {
    setLoading(true);
    try {
      const data = await userAPI.getForumActivity();
      setForumActivity(data);
    } catch (error) {
      console.error('Error fetching forum activity:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
        <div className={`flex justify-center items-center py-12 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
        </div>
      </div>
    );
  }

  if (!forumActivity) {
    return (
      <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
        <div className={`flex justify-between items-center mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
          <h2 className="text-xl font-semibold">{t("common.forum.activity", "Forum Activity")}</h2>
          <button
            onClick={fetchForumActivity}
            className="p-2 bg-indigo-900/30 border border-indigo-800/50 rounded-lg hover:bg-indigo-800/30"
            title={t("common.refresh", "Refresh")}
          >
            <RefreshCw size={18} />
          </button>
        </div>
        <div className="text-center py-8">{t("common.no.forum.activity", "No forum activity data available")}</div>
      </div>
    );
  }

  // Prepare data for activity chart
  const activityData = Object.keys(forumActivity.threads_by_day).map(date => ({
    date,
    threads: forumActivity.threads_by_day[date],
    posts: forumActivity.posts_by_day[date]
  })).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

  return (
    <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
      <div className={`flex justify-between items-center mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
        <h2 className="text-xl font-semibold">{t("common.forum.activity", "Forum Activity")}</h2>
        <button
          onClick={fetchForumActivity}
          className="p-2 bg-indigo-900/30 border border-indigo-800/50 rounded-lg hover:bg-indigo-800/30"
          title={t("common.refresh", "Refresh")}
        >
          <RefreshCw size={18} />
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50">
          <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
            <h3 className="text-sm font-medium">{t("common.threads", "Threads")}</h3>
            <MessageSquare size={16} className="text-purple-400" />
          </div>
          <div className="text-2xl font-bold mt-2">{forumActivity.thread_count}</div>
        </div>

        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50">
          <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
            <h3 className="text-sm font-medium">{t("common.posts", "Posts")}</h3>
            <MessageSquare size={16} className="text-purple-400" />
          </div>
          <div className="text-2xl font-bold mt-2">{forumActivity.post_count}</div>
        </div>

        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50">
          <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
            <h3 className="text-sm font-medium">{t("common.likes", "Likes")}</h3>
            <ThumbsUp size={16} className="text-purple-400" />
          </div>
          <div className="text-2xl font-bold mt-2">{forumActivity.likes_received}</div>
        </div>

        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50">
          <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
            <h3 className="text-sm font-medium">{t("common.solutions", "Solutions")}</h3>
            <Award size={16} className="text-purple-400" />
          </div>
          <div className="text-2xl font-bold mt-2">{forumActivity.solution_count}</div>
        </div>
      </div>

      {/* Reputation Card */}
      <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50 mb-6">
        <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
          <h3 className="text-lg font-medium">{t("common.reputation", "Reputation")}</h3>
          <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <span className={`px-2 py-1 rounded text-xs ${
              forumActivity.reputation.level === 'Newcomer' ? 'bg-gray-700/50' :
              forumActivity.reputation.level === 'Contributor' ? 'bg-blue-700/50' :
              forumActivity.reputation.level === 'Active Member' ? 'bg-green-700/50' :
              forumActivity.reputation.level === 'Expert' ? 'bg-yellow-700/50' :
              forumActivity.reputation.level === 'Mentor' ? 'bg-orange-700/50' :
              'bg-purple-700/50'}
            }`}>
              {forumActivity.reputation.level}
            </span>
          </div>
        </div>
        <div className="text-3xl font-bold mt-2">{forumActivity.reputation.points} points</div>
        <div className="grid grid-cols-2 gap-4 mt-4">
          <div>
            <div className="text-sm text-gray-400">t("common.threads.created", "Threads Created")</div>
            <p className="text-lg">{forumActivity.reputation.threads_created}</p>
          </div>
          <div>
            <div className="text-sm text-gray-400">t("common.posts.created", "Posts Created")</div>
            <p className="text-lg">{forumActivity.reputation.posts_created}</p>
          </div>
          <div>
            <div className="text-sm text-gray-400">t("common.solutions.provided", "Solutions Provided")</div>
            <p className="text-lg">{forumActivity.reputation.solutions_provided}</p>
          </div>
          <div>
            <div className="text-sm text-gray-400">t("common.likes.received", "Likes Received")</div>
            <p className="text-lg">{forumActivity.reputation.likes_received}</p>
          </div>
        </div>
      </div>

      {/* Activity Chart */}
      <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50 mb-6">
        <h3 className="text-lg font-medium mb-4">t("common.activity.over.time", "Activity Over Time")</h3>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={activityData}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#444" />
              <XAxis dataKey="date" stroke="#aaa" />
              <YAxis stroke="#aaa" />
              <Tooltip
                contentStyle={{ backgroundColor: '#1e1e3f', borderColor: '#444', color: '#fff' }}
              />
              <Legend />
              <Line type="monotone" dataKey="threads" name="Threads" stroke="#8884d8" activeDot={{ r: 8 }} />
              <Line type="monotone" dataKey="posts" name="Posts" stroke="#82ca9d" activeDot={{ r: 8 }} />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Recent Threads */}
      <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50 mb-6">
        <div
          className={`flex justify-between items-center cursor-pointer ${isRTL ? "flex-row-reverse" : ""}`}
          onClick={() => setShowRecentThreads(!showRecentThreads)}
        >
          <h3 className="text-lg font-medium">t("common.recent.threads", "Recent Threads")</h3>
          {showRecentThreads ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
        </div>

        {showRecentThreads && (
          <div className="mt-4">
            {forumActivity.recent_threads.length > 0 ? (
              <div className="space-y-3">
                {forumActivity.recent_threads.map((thread: any) => (
                  <div key={thread.id} className="border-b border-indigo-800/30 pb-3">
                    <a
                      href={`/forum/thread/${thread.id}`}
                      className="text-purple-400 hover:text-purple-300 font-medium"
                    >
                      {thread.title}
                    </a>
                    <div className={`flex justify-between mt-1 text-sm text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <span>in {thread.topic}</span>
                      <span>{new Date(thread.created_at).toLocaleDateString()}</span>
                    </div>
                    <div className="text-sm mt-1">
                      {thread.post_count} {thread.post_count === 1 ? 'reply' : 'replies'}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4">t("common.no.threads.created", "No threads created yet")</div>
            )}
          </div>
        )}
      </div>

      {/* Recent Posts */}
      <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50 mb-6">
        <div
          className={`flex justify-between items-center cursor-pointer ${isRTL ? "flex-row-reverse" : ""}`}
          onClick={() => setShowRecentPosts(!showRecentPosts)}
        >
          <h3 className="text-lg font-medium">t("common.recent.posts", "Recent Posts")</h3>
          {showRecentPosts ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
        </div>

        {showRecentPosts && (
          <div className="mt-4">
            {forumActivity.recent_posts.length > 0 ? (
              <div className="space-y-3">
                {forumActivity.recent_posts.map((post: any) => (
                  <div key={post.id} className="border-b border-indigo-800/30 pb-3">
                    <div className="text-sm">
                      <span>t("common.in.thread", "In thread: ")</span>
                      <a
                        href={`/forum/thread/${post.thread_id}`}
                        className="text-purple-400 hover:text-purple-300"
                      >
                        {post.thread_title}
                      </a>
                    </div>
                    <div className="mt-1" dangerouslySetInnerHTML={{ __html: post.content }} />
                    <div className={`flex justify-between mt-1 text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
                      <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <span className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                          <ThumbsUp size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> {post.like_count}
                        </span>
                        {post.is_solution && (
                          <span className={`flex items-center text-green-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                            <Award size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> Solution
                          </span>
                        )}
                      </div>
                      <span className="text-gray-400">{new Date(post.created_at).toLocaleDateString()}</span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4">t("common.no.posts.created", "No posts created yet")</div>
            )}
          </div>
        )}
      </div>

      {/* Recent Activities */}
      <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50">
        <div
          className={`flex justify-between items-center cursor-pointer ${isRTL ? "flex-row-reverse" : ""}`}
          onClick={() => setShowRecentActivities(!showRecentActivities)}
        >
          <h3 className="text-lg font-medium">t("common.recent.reputation.activities", "Recent Reputation Activities")</h3>
          {showRecentActivities ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
        </div>

        {showRecentActivities && (
          <div className="mt-4">
            {forumActivity.recent_activities.length > 0 ? (
              <div className="space-y-3">
                {forumActivity.recent_activities.map((activity: any) => (
                  <div key={activity.id} className="border-b border-indigo-800/30 pb-3">
                    <div className={`flex justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
                      <span className="font-medium">{activity.description}</span>
                      <span className={`font-bold ${activity.points > 0 ? 'text-green-400' : 'text-red-400'}`}>
                        {activity.points > 0 ? '+' : ''}{activity.points} points
                      </span>
                    </div>
                    <div className="text-sm text-gray-400 mt-1">
                      {new Date(activity.created_at).toLocaleDateString()}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-center py-4">t("common.no.reputation.activities", "No reputation activities yet")</p>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default UserForumStats;
