"""
Smart Notification System for Yasmeen AI Platform
AI-powered, personalized notifications with multi-channel delivery
"""

import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from django.utils import timezone
from django.contrib.auth.models import User
from django.db.models import Q
import json
import logging
from enum import Enum

logger = logging.getLogger(__name__)

class NotificationType(Enum):
    """Notification types"""
    AI_RECOMMENDATION = "ai_recommendation"
    MENTORSHIP_UPDATE = "mentorship_update"
    BUSINESS_MILESTONE = "business_milestone"
    FORUM_ACTIVITY = "forum_activity"
    FUNDING_OPPORTUNITY = "funding_opportunity"
    SYSTEM_UPDATE = "system_update"
    COLLABORATION_INVITE = "collaboration_invite"
    DEADLINE_REMINDER = "deadline_reminder"
    ACHIEVEMENT = "achievement"
    MARKET_INSIGHT = "market_insight"

class NotificationPriority(Enum):
    """Notification priority levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"

class NotificationChannel(Enum):
    """Notification delivery channels"""
    IN_APP = "in_app"
    EMAIL = "email"
    SMS = "sms"
    PUSH = "push"
    SLACK = "slack"
    WEBHOOK = "webhook"

class SmartNotificationEngine:
    """AI-powered notification engine"""
    
    def __init__(self):
        self.user_preferences = {}
        self.notification_history = {}
        
    async def send_notification(self, 
                              user_id: int, 
                              notification_type: NotificationType,
                              title: str,
                              message: str,
                              priority: NotificationPriority = NotificationPriority.MEDIUM,
                              data: Optional[Dict] = None,
                              channels: Optional[List[NotificationChannel]] = None) -> Dict:
        """Send intelligent notification to user"""
        try:
            user = User.objects.get(id=user_id)
            
            # Get user preferences
            preferences = await self._get_user_preferences(user_id)
            
            # Determine optimal channels if not specified
            if not channels:
                channels = await self._determine_optimal_channels(
                    user_id, notification_type, priority, preferences
                )
            
            # Check if notification should be sent (frequency limits, etc.)
            if not await self._should_send_notification(user_id, notification_type, priority):
                return {'success': False, 'reason': 'Frequency limit reached'}
            
            # Personalize message
            personalized_message = await self._personalize_message(
                user, message, notification_type, data
            )
            
            # Create notification record
            notification = await self._create_notification_record(
                user_id, notification_type, title, personalized_message, 
                priority, data, channels
            )
            
            # Send through selected channels
            delivery_results = {}
            for channel in channels:
                result = await self._send_through_channel(
                    channel, user, title, personalized_message, notification, data
                )
                delivery_results[channel.value] = result
            
            # Update notification history
            await self._update_notification_history(user_id, notification_type, priority)
            
            return {
                'success': True,
                'notification_id': notification['id'],
                'channels_used': [c.value for c in channels],
                'delivery_results': delivery_results
            }
            
        except Exception as e:
            logger.error(f"Smart notification failed: {e}")
            return {'success': False, 'error': str(e)}
    
    async def _get_user_preferences(self, user_id: int) -> Dict:
        """Get user notification preferences"""
        try:
            from .models import NotificationPreference
            
            prefs = NotificationPreference.objects.filter(user_id=user_id).first()
            if prefs:
                return prefs.preferences
            
            # Default preferences
            return {
                'channels': {
                    'in_app': True,
                    'email': True,
                    'push': True,
                    'sms': False
                },
                'frequency': {
                    'ai_recommendation': 'immediate',
                    'mentorship_update': 'immediate',
                    'business_milestone': 'immediate',
                    'forum_activity': 'daily_digest',
                    'funding_opportunity': 'immediate',
                    'system_update': 'weekly_digest'
                },
                'quiet_hours': {
                    'enabled': True,
                    'start': '22:00',
                    'end': '08:00',
                    'timezone': 'UTC'
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to get user preferences: {e}")
            return {}
    
    async def _determine_optimal_channels(self, 
                                        user_id: int, 
                                        notification_type: NotificationType,
                                        priority: NotificationPriority,
                                        preferences: Dict) -> List[NotificationChannel]:
        """Determine optimal notification channels using AI"""
        channels = []
        
        # Always include in-app notifications
        channels.append(NotificationChannel.IN_APP)
        
        # Priority-based channel selection
        if priority == NotificationPriority.URGENT:
            channels.extend([
                NotificationChannel.EMAIL,
                NotificationChannel.PUSH,
                NotificationChannel.SMS
            ])
        elif priority == NotificationPriority.HIGH:
            channels.extend([
                NotificationChannel.EMAIL,
                NotificationChannel.PUSH
            ])
        elif priority == NotificationPriority.MEDIUM:
            channels.append(NotificationChannel.EMAIL)
        
        # Filter based on user preferences
        user_channels = preferences.get('channels', {})
        filtered_channels = []
        
        for channel in channels:
            if user_channels.get(channel.value, True):
                filtered_channels.append(channel)
        
        # Check quiet hours for non-urgent notifications
        if priority != NotificationPriority.URGENT:
            if await self._is_quiet_hours(user_id, preferences):
                # Only keep in-app notifications during quiet hours
                filtered_channels = [NotificationChannel.IN_APP]
        
        return filtered_channels
    
    async def _should_send_notification(self, 
                                      user_id: int, 
                                      notification_type: NotificationType,
                                      priority: NotificationPriority) -> bool:
        """Check if notification should be sent based on frequency limits"""
        try:
            # Always send urgent notifications
            if priority == NotificationPriority.URGENT:
                return True
            
            # Check recent notifications of same type
            recent_count = await self._get_recent_notification_count(
                user_id, notification_type, hours=1
            )
            
            # Frequency limits by type
            limits = {
                NotificationType.AI_RECOMMENDATION: 5,
                NotificationType.FORUM_ACTIVITY: 10,
                NotificationType.SYSTEM_UPDATE: 2,
                NotificationType.MARKET_INSIGHT: 3
            }
            
            limit = limits.get(notification_type, 5)
            return recent_count < limit
            
        except Exception as e:
            logger.error(f"Failed to check notification frequency: {e}")
            return True
    
    async def _personalize_message(self, 
                                 user: User, 
                                 message: str, 
                                 notification_type: NotificationType,
                                 data: Optional[Dict]) -> str:
        """Personalize notification message for user"""
        try:
            # Get user's name
            user_name = user.first_name or user.username
            
            # Add personal greeting for certain types
            if notification_type in [
                NotificationType.AI_RECOMMENDATION,
                NotificationType.MENTORSHIP_UPDATE,
                NotificationType.ACHIEVEMENT
            ]:
                message = f"Hi {user_name}! {message}"
            
            # Add context-specific information
            if data:
                if 'business_idea_title' in data:
                    message = message.replace(
                        '{business_idea}', 
                        data['business_idea_title']
                    )
                
                if 'mentor_name' in data:
                    message = message.replace(
                        '{mentor_name}', 
                        data['mentor_name']
                    )
            
            return message
            
        except Exception as e:
            logger.error(f"Message personalization failed: {e}")
            return message
    
    async def _create_notification_record(self, 
                                        user_id: int,
                                        notification_type: NotificationType,
                                        title: str,
                                        message: str,
                                        priority: NotificationPriority,
                                        data: Optional[Dict],
                                        channels: List[NotificationChannel]) -> Dict:
        """Create notification record in database"""
        try:
            from .models import Notification
            
            notification = Notification.objects.create(
                user_id=user_id,
                notification_type=notification_type.value,
                title=title,
                message=message,
                priority=priority.value,
                data=data or {},
                channels=[c.value for c in channels],
                created_at=timezone.now()
            )
            
            return {
                'id': notification.id,
                'created_at': notification.created_at.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to create notification record: {e}")
            return {'id': None}
    
    async def _send_through_channel(self, 
                                  channel: NotificationChannel,
                                  user: User,
                                  title: str,
                                  message: str,
                                  notification: Dict,
                                  data: Optional[Dict]) -> Dict:
        """Send notification through specific channel"""
        try:
            if channel == NotificationChannel.IN_APP:
                return await self._send_in_app(user, title, message, notification, data)
            elif channel == NotificationChannel.EMAIL:
                return await self._send_email(user, title, message, notification, data)
            elif channel == NotificationChannel.PUSH:
                return await self._send_push(user, title, message, notification, data)
            elif channel == NotificationChannel.SMS:
                return await self._send_sms(user, title, message, notification, data)
            else:
                return {'success': False, 'error': 'Unsupported channel'}
                
        except Exception as e:
            logger.error(f"Failed to send through {channel.value}: {e}")
            return {'success': False, 'error': str(e)}
    
    async def _send_in_app(self, user: User, title: str, message: str, 
                          notification: Dict, data: Optional[Dict]) -> Dict:
        """Send in-app notification"""
        # In-app notifications are already stored in database
        return {'success': True, 'method': 'database_storage'}
    
    async def _send_email(self, user: User, title: str, message: str,
                         notification: Dict, data: Optional[Dict]) -> Dict:
        """Send email notification"""
        try:
            from django.core.mail import send_mail
            from django.template.loader import render_to_string
            
            # Render email template
            html_content = render_to_string('emails/notification.html', {
                'user': user,
                'title': title,
                'message': message,
                'data': data,
                'notification_id': notification.get('id')
            })
            
            send_mail(
                subject=f"Yasmeen AI: {title}",
                message=message,
                from_email='<EMAIL>',
                recipient_list=[user.email],
                html_message=html_content
            )
            
            return {'success': True, 'method': 'email_sent'}
            
        except Exception as e:
            logger.error(f"Email sending failed: {e}")
            return {'success': False, 'error': str(e)}
    
    async def _send_push(self, user: User, title: str, message: str,
                        notification: Dict, data: Optional[Dict]) -> Dict:
        """Send push notification"""
        try:
            # This would integrate with a push notification service
            # like Firebase Cloud Messaging or OneSignal
            
            push_data = {
                'title': title,
                'body': message,
                'data': data or {},
                'user_id': user.id,
                'notification_id': notification.get('id')
            }
            
            # Simulate push notification sending
            logger.info(f"Push notification sent to user {user.id}: {title}")
            
            return {'success': True, 'method': 'push_notification'}
            
        except Exception as e:
            logger.error(f"Push notification failed: {e}")
            return {'success': False, 'error': str(e)}
    
    async def _send_sms(self, user: User, title: str, message: str,
                       notification: Dict, data: Optional[Dict]) -> Dict:
        """Send SMS notification"""
        try:
            # This would integrate with an SMS service like Twilio
            
            # Get user's phone number
            phone_number = getattr(user.profile, 'phone_number', None) if hasattr(user, 'profile') else None
            
            if not phone_number:
                return {'success': False, 'error': 'No phone number available'}
            
            sms_message = f"Yasmeen AI: {title}\n{message}"
            
            # Simulate SMS sending
            logger.info(f"SMS sent to {phone_number}: {sms_message}")
            
            return {'success': True, 'method': 'sms_sent'}
            
        except Exception as e:
            logger.error(f"SMS sending failed: {e}")
            return {'success': False, 'error': str(e)}
    
    async def _is_quiet_hours(self, user_id: int, preferences: Dict) -> bool:
        """Check if current time is within user's quiet hours"""
        try:
            quiet_hours = preferences.get('quiet_hours', {})
            if not quiet_hours.get('enabled', False):
                return False
            
            # This would need proper timezone handling
            # For now, simplified check
            current_hour = timezone.now().hour
            start_hour = int(quiet_hours.get('start', '22:00').split(':')[0])
            end_hour = int(quiet_hours.get('end', '08:00').split(':')[0])
            
            if start_hour > end_hour:  # Overnight quiet hours
                return current_hour >= start_hour or current_hour <= end_hour
            else:
                return start_hour <= current_hour <= end_hour
                
        except Exception as e:
            logger.error(f"Quiet hours check failed: {e}")
            return False
    
    async def _get_recent_notification_count(self, 
                                           user_id: int, 
                                           notification_type: NotificationType,
                                           hours: int) -> int:
        """Get count of recent notifications of specific type"""
        try:
            from .models import Notification
            
            since = timezone.now() - timedelta(hours=hours)
            return Notification.objects.filter(
                user_id=user_id,
                notification_type=notification_type.value,
                created_at__gte=since
            ).count()
            
        except Exception as e:
            logger.error(f"Failed to get recent notification count: {e}")
            return 0
    
    async def _update_notification_history(self, 
                                         user_id: int,
                                         notification_type: NotificationType,
                                         priority: NotificationPriority):
        """Update notification history for analytics"""
        try:
            if user_id not in self.notification_history:
                self.notification_history[user_id] = {}
            
            type_key = notification_type.value
            if type_key not in self.notification_history[user_id]:
                self.notification_history[user_id][type_key] = []
            
            self.notification_history[user_id][type_key].append({
                'timestamp': timezone.now().isoformat(),
                'priority': priority.value
            })
            
            # Keep only last 100 notifications per type
            if len(self.notification_history[user_id][type_key]) > 100:
                self.notification_history[user_id][type_key] = \
                    self.notification_history[user_id][type_key][-100:]
                    
        except Exception as e:
            logger.error(f"Failed to update notification history: {e}")

# Global notification engine instance
notification_engine = SmartNotificationEngine()

# Export classes and instance
__all__ = [
    'SmartNotificationEngine',
    'NotificationType',
    'NotificationPriority', 
    'NotificationChannel',
    'notification_engine'
]
