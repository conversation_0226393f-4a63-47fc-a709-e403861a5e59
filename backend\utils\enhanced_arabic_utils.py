"""
Enhanced Arabic Language Utilities
Comprehensive Arabic language processing and cultural adaptation
"""

import re
import json
import logging
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime

logger = logging.getLogger(__name__)

# Arabic language processing
try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_PROCESSING_AVAILABLE = True
except ImportError:
    ARABIC_PROCESSING_AVAILABLE = False

try:
    from langdetect import detect, DetectorFactory
    DetectorFactory.seed = 0  # For consistent results
    LANGDETECT_AVAILABLE = True
except ImportError:
    LANGDETECT_AVAILABLE = False


class EnhancedArabicProcessor:
    """Enhanced Arabic language processor with cultural context"""

    def __init__(self):
        # Arabic character ranges
        self.arabic_ranges = [
            (0x0600, 0x06FF),  # Arabic
            (0x0750, 0x077F),  # Arabic Supplement
            (0x08A0, 0x08FF),  # Arabic Extended-A
            (0xFB50, 0xFDFF),  # Arabic Presentation Forms-A
            (0xFE70, 0xFEFF),  # Arabic Presentation Forms-B
        ]

        # Common Arabic business terms
        self.business_terms = {
            'مشروع': 'project',
            'شركة': 'company',
            'عمل': 'business',
            'ريادة': 'entrepreneurship',
            'استثمار': 'investment',
            'تمويل': 'funding',
            'سوق': 'market',
            'عميل': 'customer',
            'منتج': 'product',
            'خدمة': 'service',
            'ربح': 'profit',
            'خسارة': 'loss',
            'نمو': 'growth',
            'تطوير': 'development',
            'ابتكار': 'innovation',
            'تقنية': 'technology',
            'رقمي': 'digital',
            'إلكتروني': 'electronic',
            'تسويق': 'marketing',
            'مبيعات': 'sales'
        }

        # Cultural greetings and expressions
        self.cultural_expressions = {
            'greetings': [
                'السلام عليكم',
                'أهلاً وسهلاً',
                'مرحباً',
                'حياك الله',
                'أهلاً بك'
            ],
            'farewells': [
                'مع السلامة',
                'إلى اللقاء',
                'وداعاً',
                'بارك الله فيك',
                'في أمان الله'
            ],
            'encouragement': [
                'بالتوفيق',
                'إن شاء الله',
                'بإذن الله',
                'الله يوفقك',
                'نجاح باهر'
            ]
        }

    def is_arabic_text(self, text: str) -> bool:
        """Check if text contains Arabic characters"""
        if not text:
            return False

        arabic_count = 0
        total_chars = 0

        for char in text:
            if char.isalpha():
                total_chars += 1
                if self._is_arabic_char(char):
                    arabic_count += 1

        if total_chars == 0:
            return False

        return (arabic_count / total_chars) > 0.3

    def _is_arabic_char(self, char: str) -> bool:
        """Check if a character is Arabic"""
        char_code = ord(char)
        return any(start <= char_code <= end for start, end in self.arabic_ranges)

    def detect_language_advanced(self, text: str) -> Tuple[str, float]:
        """Advanced language detection with confidence score"""
        if not text or len(text.strip()) == 0:
            return 'en', 0.0

        # Count Arabic characters
        arabic_chars = sum(1 for char in text if self._is_arabic_char(char))
        total_chars = len([char for char in text if char.isalnum()])

        if total_chars == 0:
            return 'en', 0.0

        arabic_ratio = arabic_chars / total_chars

        # High confidence Arabic
        if arabic_ratio > 0.7:
            return 'ar', 0.9

        # Medium confidence Arabic
        if arabic_ratio > 0.3:
            return 'ar', 0.7

        # Use langdetect for mixed content
        if LANGDETECT_AVAILABLE and arabic_ratio < 0.3:
            try:
                detected = detect(text)
                confidence = 0.8 if detected == 'ar' else 0.6
                return detected if detected in ['ar', 'en'] else 'en', confidence
            except:
                pass

        # Default to English with low confidence
        return 'en', 0.5 if arabic_ratio == 0 else 0.3

    def process_arabic_text(self, text: str) -> str:
        """Process Arabic text for proper display"""
        if not ARABIC_PROCESSING_AVAILABLE or not self.is_arabic_text(text):
            return text

        try:
            # Reshape Arabic text
            reshaped_text = arabic_reshaper.reshape(text)
            # Apply bidirectional algorithm
            return get_display(reshaped_text)
        except Exception as e:
            print(f"Error processing Arabic text: {e}")
            return text

    def extract_business_terms(self, text: str) -> List[Dict[str, str]]:
        """Extract business terms from Arabic text"""
        found_terms = []
        text_lower = text.lower()

        for arabic_term, english_term in self.business_terms.items():
            if arabic_term in text_lower:
                found_terms.append({
                    'arabic': arabic_term,
                    'english': english_term,
                    'context': self._get_term_context(text, arabic_term)
                })

        return found_terms

    def _get_term_context(self, text: str, term: str, window: int = 20) -> str:
        """Get context around a term"""
        index = text.lower().find(term.lower())
        if index == -1:
            return ""

        start = max(0, index - window)
        end = min(len(text), index + len(term) + window)
        return text[start:end].strip()

    def add_cultural_context(self, text: str, context_type: str = 'business') -> str:
        """Add cultural context to text"""
        if not self.is_arabic_text(text):
            return text

        # Add appropriate greeting if missing
        if not any(greeting in text for greeting in self.cultural_expressions['greetings']):
            if context_type == 'business':
                text = f"أهلاً وسهلاً، {text}"

        # Add encouragement for business contexts
        if context_type == 'business' and any(term in text.lower() for term in ['مشروع', 'عمل', 'فكرة']):
            if not any(enc in text for enc in self.cultural_expressions['encouragement']):
                text += " بالتوفيق إن شاء الله."

        return text

    def format_arabic_business_response(self, content: str, user_name: str = None) -> str:
        """Format Arabic business response with cultural elements"""
        if not self.is_arabic_text(content):
            return content

        # Add greeting with name if provided
        greeting = f"أهلاً {user_name}، " if user_name else "أهلاً وسهلاً، "

        # Ensure proper formatting
        formatted_content = self.process_arabic_text(content)

        # Add cultural closing
        closing = "\n\nبارك الله فيك وبالتوفيق في مشروعك."

        return f"{greeting}{formatted_content}{closing}"

    def analyze_sentiment_arabic(self, text: str) -> Dict[str, Any]:
        """Basic Arabic sentiment analysis"""
        if not self.is_arabic_text(text):
            return {'sentiment': 'neutral', 'confidence': 0.0, 'language': 'non-arabic'}

        # Positive indicators
        positive_words = [
            'ممتاز', 'رائع', 'جيد', 'مفيد', 'نجح', 'تطور', 'نمو', 'ربح',
            'إيجابي', 'مبدع', 'ابتكار', 'تقدم', 'فرصة', 'نجاح'
        ]

        # Negative indicators
        negative_words = [
            'سيء', 'فشل', 'خسارة', 'مشكلة', 'صعب', 'مستحيل', 'خطأ',
            'سلبي', 'ضعيف', 'تراجع', 'أزمة', 'عقبة'
        ]

        text_lower = text.lower()
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)

        if positive_count > negative_count:
            sentiment = 'positive'
            confidence = min(0.8, positive_count / (positive_count + negative_count + 1))
        elif negative_count > positive_count:
            sentiment = 'negative'
            confidence = min(0.8, negative_count / (positive_count + negative_count + 1))
        else:
            sentiment = 'neutral'
            confidence = 0.5

        return {
            'sentiment': sentiment,
            'confidence': confidence,
            'positive_indicators': positive_count,
            'negative_indicators': negative_count,
            'language': 'arabic'
        }

    def get_cultural_business_advice(self, business_type: str, language: str = 'ar') -> str:
        """Get culturally appropriate business advice"""
        advice_templates = {
            'ar': {
                'tech': "في مجال التقنية، من المهم مواكبة التطورات الحديثة والاستثمار في التعلم المستمر. السوق العربي يحتاج لحلول تقنية مبتكرة تراعي الثقافة المحلية.",
                'retail': "في مجال التجارة، التركيز على خدمة العملاء والجودة أساسي للنجاح. فهم احتياجات السوق المحلي والتكيف معها مفتاح النجاح.",
                'service': "في مجال الخدمات، بناء الثقة مع العملاء والحفاظ على السمعة الطيبة أهم من الربح السريع. الكلمة الطيبة تفتح أبواب النجاح.",
                'default': "النجاح في الأعمال يتطلب الصبر والمثابرة والتخطيط الجيد. ابدأ بخطوات صغيرة وثابتة، والله يبارك في العمل الصالح."
            },
            'en': {
                'tech': "In technology, staying updated with modern developments and investing in continuous learning is crucial. The Arab market needs innovative tech solutions that respect local culture.",
                'retail': "In retail, focusing on customer service and quality is essential for success. Understanding and adapting to local market needs is key to success.",
                'service': "In services, building trust with customers and maintaining a good reputation is more important than quick profits. A good reputation opens doors to success.",
                'default': "Business success requires patience, perseverance, and good planning. Start with small, steady steps, and success will follow with dedication."
            }
        }

        lang_advice = advice_templates.get(language, advice_templates['en'])
        return lang_advice.get(business_type, lang_advice['default'])

    def extract_business_terms(self, text: str) -> List[str]:
        """Extract Arabic business terms from text"""
        try:
            business_terms = []

            # Common Arabic business terms
            arabic_business_keywords = [
                'مشروع', 'شركة', 'أعمال', 'تجارة', 'استثمار', 'ربح', 'خسارة',
                'سوق', 'عملاء', 'منافسة', 'تسويق', 'مبيعات', 'إيرادات',
                'تكاليف', 'ميزانية', 'خطة', 'استراتيجية', 'هدف', 'نمو',
                'تطوير', 'ابتكار', 'جودة', 'خدمة', 'منتج', 'علامة تجارية',
                'فريق', 'موظفين', 'إدارة', 'قيادة', 'تدريب', 'مهارات',
                'تمويل', 'قرض', 'بنك', 'فائدة', 'ضمان', 'تأمين',
                'عقد', 'اتفاقية', 'شراكة', 'تعاون', 'تحالف', 'اندماج'
            ]

            # Extract terms found in text
            for term in arabic_business_keywords:
                if term in text:
                    business_terms.append(term)

            return list(set(business_terms))  # Remove duplicates

        except Exception as e:
            logger.error(f"Error extracting business terms: {e}")
            return []

    def detect_language_advanced(self, text: str) -> Tuple[str, float]:
        """Advanced language detection with confidence score"""
        try:
            # Count Arabic characters
            arabic_chars = sum(1 for char in text if '\u0600' <= char <= '\u06FF')
            total_chars = len([char for char in text if char.isalpha()])

            if total_chars == 0:
                return 'unknown', 0.0

            arabic_ratio = arabic_chars / total_chars

            # Determine language and confidence
            if arabic_ratio > 0.7:
                return 'ar', arabic_ratio
            elif arabic_ratio > 0.3:
                return 'mixed', arabic_ratio
            else:
                return 'en', 1.0 - arabic_ratio

        except Exception as e:
            logger.error(f"Error in advanced language detection: {e}")
            return 'unknown', 0.0


# Global instance
_arabic_processor = None

def get_arabic_processor() -> EnhancedArabicProcessor:
    """Get the global Arabic processor instance"""
    global _arabic_processor
    if _arabic_processor is None:
        _arabic_processor = EnhancedArabicProcessor()
    return _arabic_processor

# Convenience functions
def detect_language(text: str) -> str:
    """Detect language of text"""
    processor = get_arabic_processor()
    language, _ = processor.detect_language_advanced(text)
    return language

def detect_language_advanced(text: str) -> Tuple[str, float]:
    """Advanced language detection with confidence"""
    processor = get_arabic_processor()
    return processor.detect_language_advanced(text)

def process_arabic_text(text: str) -> str:
    """Process Arabic text for display"""
    processor = get_arabic_processor()
    return processor.process_arabic_text(text)

def is_arabic_text(text: str) -> bool:
    """Check if text is Arabic"""
    processor = get_arabic_processor()
    return processor.is_arabic_text(text)

def format_arabic_response(content: str, user_name: str = None) -> str:
    """Format Arabic response with cultural context"""
    processor = get_arabic_processor()
    return processor.format_arabic_business_response(content, user_name)

def extract_business_terms(text: str) -> List[str]:
    """Extract Arabic business terms"""
    processor = get_arabic_processor()
    return processor.extract_business_terms(text)

def analyze_sentiment_arabic(text: str) -> Dict[str, Any]:
    """Analyze Arabic text sentiment"""
    processor = get_arabic_processor()
    return processor.analyze_sentiment_arabic(text)
