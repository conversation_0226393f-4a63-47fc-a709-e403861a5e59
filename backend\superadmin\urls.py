from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from .views import (
    SuperAdminSystemViewSet, SuperAdminContentViewSet,
    SuperAdminUserViewSet, SuperAdminForumViewSet,
    SuperAdminIncubatorViewSet, SuperAdminAIViewSet
)

router = DefaultRouter()
router.register(r'system', SuperAdminSystemViewSet, basename='superadmin-system')
router.register(r'content', SuperAdminContentViewSet, basename='superadmin-content')
router.register(r'users', SuperAdminUserViewSet, basename='superadmin-users')
router.register(r'forum', SuperAdminForumViewSet, basename='superadmin-forum')
router.register(r'incubator', SuperAdminIncubatorViewSet, basename='superadmin-incubator')
router.register(r'ai', SuperAdminAIViewSet, basename='superadmin-ai')

urlpatterns = [
    path('', include(router.urls)),
]
