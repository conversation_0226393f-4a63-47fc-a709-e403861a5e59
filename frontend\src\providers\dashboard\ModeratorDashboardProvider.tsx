/**
 * Moderator Dashboard Content Provider
 * Provides moderator-specific dashboard data, stats, and actions
 */

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useAppSelector } from '../../store/hooks';
import { DashboardStat, DashboardQuickAction } from '../../types/dashboard';
import {
  AlertCircle,
  CheckCircle,
  Eye,
  Shield,
  Flag,
  BarChart3,
  Users,
  MessageSquare,
  Clock,
  TrendingUp
} from 'lucide-react';

interface ModeratorDashboardData {
  stats: DashboardStat[];
  quickActions: DashboardQuickAction[];
  pendingReports: any[];
  recentActivity: any[];
  loading: boolean;
  error: string | null;
  refreshData: () => Promise<void>;
}

const ModeratorDashboardContext = createContext<ModeratorDashboardData | null>(null);

interface ModeratorDashboardProviderProps {
  children: React.ReactNode;
}

export const ModeratorDashboardProvider: React.FC<ModeratorDashboardProviderProps> = ({ children }) => {
  const { user } = useAppSelector(state => state.auth);
  const [stats, setStats] = useState<DashboardStat[]>([]);
  const [quickActions, setQuickActions] = useState<DashboardQuickAction[]>([]);
  const [pendingReports, setPendingReports] = useState<any[]>([]);
  const [recentActivity, setRecentActivity] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch moderator dashboard data
  const fetchDashboardData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Mock moderator stats - content moderation focused
      const moderatorStats: DashboardStat[] = [
        {
          id: 'pending_reports',
          title: 'Pending Reports',
          value: 12,
          icon: AlertCircle,
          color: 'bg-red-600/30',
          change: -15.2,
          changeType: 'decrease',
          description: 'Reports requiring immediate attention'
        },
        {
          id: 'resolved_today',
          title: 'Resolved Today',
          value: 8,
          icon: CheckCircle,
          color: 'bg-green-600/30',
          change: 25.0,
          changeType: 'increase',
          description: 'Reports resolved in the last 24 hours'
        },
        {
          id: 'flagged_content',
          title: 'Flagged Content',
          value: 5,
          icon: Eye,
          color: 'bg-yellow-600/30',
          change: -10.0,
          changeType: 'decrease',
          description: 'Content awaiting moderation review'
        },
        {
          id: 'community_health',
          title: 'Community Health',
          value: '85%',
          icon: Shield,
          color: 'bg-blue-600/30',
          change: 2.1,
          changeType: 'increase',
          description: 'Overall community health score'
        }
      ];

      // Mock moderator quick actions
      const moderatorQuickActions: DashboardQuickAction[] = [
        {
          id: 'review_reports',
          title: 'Review Reports',
          description: 'Handle pending user reports and flagged content',
          icon: Flag,
          href: '/dashboard/moderation/reports',
          color: 'bg-red-600/20 hover:bg-red-600/30 border-red-500/30',
          badge: 12,
        },
        {
          id: 'moderate_content',
          title: 'Moderate Content',
          description: 'Review and approve user-generated content',
          icon: Shield,
          href: '/dashboard/moderation/content',
          color: 'bg-blue-600/20 hover:bg-blue-600/30 border-blue-500/30',
          badge: 5,
        },
        {
          id: 'user_management',
          title: 'User Management',
          description: 'Manage user accounts and violations',
          icon: Users,
          href: '/dashboard/moderation/users',
          color: 'bg-purple-600/20 hover:bg-purple-600/30 border-purple-500/30',
        },
        {
          id: 'forum_moderation',
          title: 'Forum Moderation',
          description: 'Moderate forum discussions and posts',
          icon: MessageSquare,
          href: '/dashboard/moderation/forum',
          color: 'bg-orange-600/20 hover:bg-orange-600/30 border-orange-500/30',
        },
        {
          id: 'community_health',
          title: 'Community Health',
          description: 'Monitor community metrics and trends',
          icon: BarChart3,
          href: '/dashboard/moderation/analytics',
          color: 'bg-green-600/20 hover:bg-green-600/30 border-green-500/30',
        },
        {
          id: 'moderation_tools',
          title: 'Moderation Tools',
          description: 'Access advanced moderation features',
          icon: Eye,
          href: '/dashboard/moderation/tools',
          color: 'bg-gray-600/20 hover:bg-gray-600/30 border-gray-500/30',
        }
      ];

      // Mock pending reports
      const mockPendingReports = [
        {
          id: '1',
          type: 'inappropriate_content',
          content: 'Post contains offensive language',
          reporter: 'user123',
          priority: 'high',
          timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
          status: 'pending'
        },
        {
          id: '2',
          type: 'spam',
          content: 'Multiple promotional posts from same user',
          reporter: 'user456',
          priority: 'medium',
          timestamp: new Date(Date.now() - 1000 * 60 * 60).toISOString(),
          status: 'pending'
        },
        {
          id: '3',
          type: 'harassment',
          content: 'User harassment in comments',
          reporter: 'user789',
          priority: 'urgent',
          timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
          status: 'pending'
        }
      ];

      // Mock recent activity
      const mockRecentActivity = [
        {
          id: '1',
          type: 'report_resolved',
          description: 'Resolved spam report for post #1234',
          timestamp: new Date(Date.now() - 1000 * 60 * 45).toISOString(),
          action: 'approved'
        },
        {
          id: '2',
          type: 'content_removed',
          description: 'Removed inappropriate comment from user456',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
          action: 'removed'
        },
        {
          id: '3',
          type: 'user_warned',
          description: 'Issued warning to user789 for policy violation',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 3).toISOString(),
          action: 'warned'
        },
        {
          id: '4',
          type: 'content_approved',
          description: 'Approved business idea submission',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4).toISOString(),
          action: 'approved'
        }
      ];

      setStats(moderatorStats);
      setQuickActions(moderatorQuickActions);
      setPendingReports(mockPendingReports);
      setRecentActivity(mockRecentActivity);

    } catch (err) {
      console.error('Error fetching moderator dashboard data:', err);
      setError('Failed to load moderator dashboard data');
      
      // Fallback to basic mock data
      const fallbackStats: DashboardStat[] = [
        {
          id: 'pending_reports',
          title: 'Pending Reports',
          value: 0,
          icon: AlertCircle,
          color: 'bg-red-600/30',
          change: 0,
          changeType: 'neutral',
          description: 'No pending reports'
        },
        {
          id: 'resolved_today',
          title: 'Resolved Today',
          value: 0,
          icon: CheckCircle,
          color: 'bg-green-600/30',
          change: 0,
          changeType: 'neutral',
          description: 'No reports resolved today'
        }
      ];

      const fallbackQuickActions: DashboardQuickAction[] = [
        {
          id: 'review_reports',
          title: 'Review Reports',
          description: 'Handle pending user reports',
          icon: Flag,
          href: '/dashboard/moderation/reports',
          color: 'bg-red-600/20 hover:bg-red-600/30 border-red-500/30',
        },
        {
          id: 'moderate_content',
          title: 'Moderate Content',
          description: 'Review user content',
          icon: Shield,
          href: '/dashboard/moderation/content',
          color: 'bg-blue-600/20 hover:bg-blue-600/30 border-blue-500/30',
        }
      ];

      setStats(fallbackStats);
      setQuickActions(fallbackQuickActions);
      setPendingReports([]);
      setRecentActivity([]);
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Initial data fetch
  useEffect(() => {
    if (user) {
      fetchDashboardData();
    }
  }, [user, fetchDashboardData]);

  const contextValue: ModeratorDashboardData = {
    stats,
    quickActions,
    pendingReports,
    recentActivity,
    loading,
    error,
    refreshData: fetchDashboardData,
  };

  return (
    <ModeratorDashboardContext.Provider value={contextValue}>
      {children}
    </ModeratorDashboardContext.Provider>
  );
};

// Hook to use moderator dashboard data
export const useModeratorDashboard = (): ModeratorDashboardData => {
  const context = useContext(ModeratorDashboardContext);
  if (!context) {
    throw new Error('useModeratorDashboard must be used within a ModeratorDashboardProvider');
  }
  return context;
};

export default ModeratorDashboardProvider;
