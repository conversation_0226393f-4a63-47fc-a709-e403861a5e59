import React from 'react';
import { BookOpen, ExternalLink, FileText, Video, Wrench, BookMarked, Tag } from 'lucide-react';
import { IncubatorResource } from '../../services/incubatorApi';

import { useTranslation } from 'react-i18next';
interface IncubatorResourceCardProps {
  resource: IncubatorResource;
}

const IncubatorResourceCard: React.FC<IncubatorResourceCardProps> = ({ resource  }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Get resource type icon
  const getResourceTypeIcon = (type: string) => {
    switch (type) {
      case 'article':
        return <FileText size={18} />;
      case 'video':
        return <Video size={18} />;
      case 'template':
        return <FileText size={18} />;
      case 'tool':
        return <Wrench size={18} />;
      case 'course':
        return <BookOpen size={18} />;
      case 'ebook':
        return <BookMarked size={18} />;
      default:
        return <BookOpen size={18} />;
    }
  };

  // Get category badge color
  const getCategoryBadgeColor = (category: string) => {
    switch (category) {
      case 'ideation':
        return 'bg-blue-600/50 text-blue-200';
      case 'validation':
        return 'bg-purple-600/50 text-purple-200';
      case 'planning':
        return 'bg-green-600/50 text-green-200';
      case 'finance':
        return 'bg-yellow-600/50 text-yellow-200';
      case 'marketing':
        return 'bg-pink-600/50 text-pink-200';
      case 'legal':
        return 'bg-red-600/50 text-red-200';
      case 'operations':
        return 'bg-orange-600/50 text-orange-200';
      case 'technology':
        return 'bg-indigo-600/50 text-indigo-200';
      case 'growth':
        return 'bg-teal-600/50 text-teal-200';
      default:
        return 'bg-gray-600/50 text-gray-200';
    }
  };

  // Format category name for display
  const formatCategoryName = (category: string) => {
    return category.charAt(0).toUpperCase() + category.slice(1);
  };

  return (
    <div className="bg-indigo-900/50 rounded-lg overflow-hidden border border-indigo-800 hover:border-purple-500 transition-all duration-300 hover:shadow-glow">
      {resource.image && (
        <div className="h-48 overflow-hidden">
          <img
            src={resource.image}
            alt={resource.title}
            className="w-full h-full object-cover"
          />
        </div>
      )}
      <div className="p-6">
        <div className={`flex justify-between items-start mb-3 ${isRTL ? "flex-row-reverse" : ""}`}>
          <span className={`px-2 py-1 rounded-md text-xs font-medium ${getCategoryBadgeColor(resource.category)}`}>
            {formatCategoryName(resource.category)}
          </span>
          <div className={`flex items-center text-gray-400 text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
            {getResourceTypeIcon(resource.resource_type)}
            <span className={`ml-1 capitalize ${isRTL ? "space-x-reverse" : ""}`}>{resource.resource_type}</span>
          </div>
        </div>

        <h3 className="text-xl font-bold mb-2 text-white">{resource.title}</h3>

        <div className="text-gray-300 mb-4 line-clamp-3">
          {resource.description}
        </div>

        {resource.tags && resource.tags.length > 0 && (
          <div className={`flex flex-wrap gap-2 mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            {resource.tags.map(tag => (
              <span key={tag.id} className={`bg-indigo-800/50 text-indigo-200 px-2 py-1 rounded text-xs flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <Tag size={12} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                {tag.name}
              </span>
            ))}
          </div>
        )}

        <div className={`flex justify-between items-center mt-4 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="text-sm text-gray-400">
            Added {formatDate(resource.created_at)}
          </div>

          <a
            href={resource.url}
            target="_blank"
            rel="noopener noreferrer"
            className={`px-3 py-1.5 bg-purple-600 hover:bg-purple-700 rounded-md text-white text-sm flex items-center transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
          >
            Access Resource <ExternalLink size={14} className={`ml-1 ${isRTL ? "space-x-reverse" : ""}`} />
          </a>
        </div>
      </div>
    </div>
  );
};

export default IncubatorResourceCard;
