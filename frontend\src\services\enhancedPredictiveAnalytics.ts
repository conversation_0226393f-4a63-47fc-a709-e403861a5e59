/**
 * Enhanced Predictive Analytics Service
 * Provides advanced analytics capabilities including:
 * - Startup failure prediction with early warning systems
 * - Market timing optimization
 * - Competitor analysis automation
 * - Customer acquisition cost prediction
 */

import { api } from './api';

export interface BusinessData {
  title?: string;
  description?: string;
  stage?: number;
  has_team?: boolean;
  has_funding?: boolean;
  monthly_revenue?: number;
  burn_rate?: number;
  runway_months?: number;
  customer_count?: number;
  growth_rate?: number;
  team_size?: number;
  founder_experience?: number;
  market_size?: number;
  competition_level?: number;
  industry_category?: number;
  business_model?: number;
  target_market_size?: number;
  product_complexity?: number;
  monthly_revenue_per_customer?: number;
  monthly_churn_rate?: number;
  gross_margin?: number;
}

export interface HistoricalData {
  progress_rate?: number;
  milestone_completion_rate?: number;
  revenue?: number;
  customer_count?: number;
  date?: string;
}

export interface MarketContext {
  market_growth_rate?: number;
  competition_intensity?: number;
  economic_conditions?: number;
  seasonal_factor?: number;
  industry_trends?: string[];
  regulatory_environment?: string;
  funding_availability?: number;
  talent_availability?: number;
  market_sentiment?: string;
}

export interface CompetitorData {
  name?: string;
  threat_level?: number;
  strengths?: string[];
  weaknesses?: string[];
  market_share?: number;
  recent_activities?: string[];
  funding_status?: string;
  growth_rate?: number;
  strength_score?: number;
}

export interface MarketingData {
  total_marketing_budget?: number;
  channel_count?: number;
  conversion_rate?: number;
  brand_awareness?: number;
  channels?: {
    [key: string]: {
      spend: number;
      acquisitions: number;
      cac?: number;
    };
  };
}

export interface FailurePredictionResult {
  failure_probability: number;
  success_probability: number;
  confidence_score: number;
  alert_level: 'low' | 'medium' | 'high' | 'critical';
  warning_indicators: Array<{
    type: string;
    severity: string;
    indicator: string;
    recommendation: string;
  }>;
  risk_factors: Record<string, any>;
  early_warning_signals: string[];
  prevention_recommendations: string[];
  monitoring_schedule: {
    frequency: string;
    metrics: string[];
    review_interval_days: number;
  };
  next_review_date: string;
  benchmark_comparison: Record<string, any>;
  timestamp: string;
}

export interface TimingOptimizationResult {
  timing_scores: {
    launch_score: number;
    pivot_score: number;
    scale_score: number;
  };
  recommended_action: string;
  optimal_timing_windows: Record<string, string>;
  market_conditions: MarketContext;
  timing_recommendations: string[];
  risk_factors: string[];
  competitive_timing: string;
  seasonal_factors: string;
  readiness_assessment: string;
  confidence_level: number;
  timestamp: string;
}

export interface CompetitorAnalysisResult {
  competitive_threat_score: number;
  threat_level: string;
  competitor_analysis: CompetitorData[];
  market_positioning: Record<string, any>;
  competitive_gaps: string[];
  competitive_advantages: string[];
  monitoring_alerts: string[];
  strategic_recommendations: string[];
  market_share_analysis: string;
  differentiation_opportunities: string[];
  competitive_timeline: string;
  next_analysis_date: string;
  timestamp: string;
}

export interface CACPredictionResult {
  predicted_cac: number;
  confidence_score: number;
  cac_category: string;
  channel_analysis: Record<string, {
    cac: number;
    efficiency_score: number;
    volume: number;
    spend: number;
    recommendation: string;
  }>;
  cac_trends: {
    trend: string;
    monthly_change: number;
    projection_3_months: number;
  };
  optimization_recommendations: string[];
  industry_benchmarks: {
    industry_average: number;
    top_quartile: number;
    bottom_quartile: number;
  };
  ltv_cac_analysis: {
    ltv: number;
    cac: number;
    ltv_cac_ratio: number;
    ratio_category: string;
    payback_period_months: number;
    recommendation: string;
  };
  warning_indicators: string[];
  cost_efficiency_score: number;
  recommended_budget_allocation: Record<string, number>;
  monitoring_metrics: string[];
  next_review_date: string;
  timestamp: string;
}

export class EnhancedPredictiveAnalyticsService {
  private static instance: EnhancedPredictiveAnalyticsService;
  private baseUrl = '/ai/predictive-analytics/';

  static getInstance(): EnhancedPredictiveAnalyticsService {
    if (!EnhancedPredictiveAnalyticsService.instance) {
      EnhancedPredictiveAnalyticsService.instance = new EnhancedPredictiveAnalyticsService();
    }
    return EnhancedPredictiveAnalyticsService.instance;
  }

  /**
   * Predict startup failure with early warning systems
   */
  async predictStartupFailure(
    businessData: BusinessData,
    historicalData?: HistoricalData[]
  ): Promise<FailurePredictionResult> {
    const response = await api.post(this.baseUrl, {
      analysis_type: 'failure_prediction',
      business_data: businessData,
      historical_data: historicalData || []
    });
    return response.result;
  }

  /**
   * Optimize market timing for launch, pivot, or scale decisions
   */
  async optimizeMarketTiming(
    businessData: BusinessData,
    marketContext?: MarketContext
  ): Promise<TimingOptimizationResult> {
    const response = await api.post(this.baseUrl, {
      analysis_type: 'timing_optimization',
      business_data: businessData,
      market_context: marketContext || {}
    });
    return response.result;
  }

  /**
   * Analyze competitors with automated monitoring
   */
  async analyzeCompetitors(
    businessData: BusinessData,
    competitorData?: CompetitorData[]
  ): Promise<CompetitorAnalysisResult> {
    const response = await api.post(this.baseUrl, {
      analysis_type: 'competitor_analysis',
      business_data: businessData,
      competitor_data: competitorData || []
    });
    return response.result;
  }

  /**
   * Predict customer acquisition cost with optimization recommendations
   */
  async predictCAC(
    businessData: BusinessData,
    marketingData?: MarketingData
  ): Promise<CACPredictionResult> {
    const response = await api.post(this.baseUrl, {
      analysis_type: 'cac_prediction',
      business_data: businessData,
      marketing_data: marketingData || {}
    });
    return response.result;
  }

  /**
   * Get comprehensive analytics dashboard data
   */
  async getComprehensiveAnalytics(
    businessData: BusinessData,
    options?: {
      includeFailurePrediction?: boolean;
      includeTimingOptimization?: boolean;
      includeCompetitorAnalysis?: boolean;
      includeCACPrediction?: boolean;
      historicalData?: HistoricalData[];
      marketContext?: MarketContext;
      competitorData?: CompetitorData[];
      marketingData?: MarketingData;
    }
  ): Promise<{
    failurePrediction?: FailurePredictionResult;
    timingOptimization?: TimingOptimizationResult;
    competitorAnalysis?: CompetitorAnalysisResult;
    cacPrediction?: CACPredictionResult;
  }> {
    const results: any = {};

    try {
      const promises: Promise<any>[] = [];

      if (options?.includeFailurePrediction !== false) {
        promises.push(
          this.predictStartupFailure(businessData, options?.historicalData)
            .then(result => ({ type: 'failure', result }))
            .catch(error => ({ type: 'failure', error }))
        );
      }

      if (options?.includeTimingOptimization !== false) {
        promises.push(
          this.optimizeMarketTiming(businessData, options?.marketContext)
            .then(result => ({ type: 'timing', result }))
            .catch(error => ({ type: 'timing', error }))
        );
      }

      if (options?.includeCompetitorAnalysis !== false) {
        promises.push(
          this.analyzeCompetitors(businessData, options?.competitorData)
            .then(result => ({ type: 'competitor', result }))
            .catch(error => ({ type: 'competitor', error }))
        );
      }

      if (options?.includeCACPrediction !== false) {
        promises.push(
          this.predictCAC(businessData, options?.marketingData)
            .then(result => ({ type: 'cac', result }))
            .catch(error => ({ type: 'cac', error }))
        );
      }

      const responses = await Promise.allSettled(promises);

      responses.forEach((response) => {
        if (response.status === 'fulfilled') {
          const { type, result, error } = response.value;
          if (!error) {
            switch (type) {
              case 'failure':
                results.failurePrediction = result;
                break;
              case 'timing':
                results.timingOptimization = result;
                break;
              case 'competitor':
                results.competitorAnalysis = result;
                break;
              case 'cac':
                results.cacPrediction = result;
                break;
            }
          }
        }
      });

      return results;
    } catch (error) {
      console.error('Error getting comprehensive analytics:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const enhancedPredictiveAnalytics = EnhancedPredictiveAnalyticsService.getInstance();
