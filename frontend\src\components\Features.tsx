import React from 'react';
import { <PERSON><PERSON><PERSON>, Users, Zap, <PERSON><PERSON>hart, Lightbulb, TrendingUp, DollarSign, Target, Network, LucideIcon } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../store/hooks';
import { RTLText, RTLFlex, RTLIcon } from './common';



interface FeatureCardProps {
  icon: LucideIcon;
  title: string;
  description: string;
  className?: string;
}

const FeatureCard: React.FC<FeatureCardProps> = ({ icon: Icon, title, description, className = ""  }) => {
  return (
    <div className={`glass-light rounded-xl p-6 hover:shadow-lg transition-all duration-300 border ${className}`}>
      <RTLFlex className="items-start gap-4">
        <div className="shrink-0">
          <RTLIcon icon={Icon} size={24} className="text-glass-accent" />
        </div>
        <div>
          <RTLText as="h3" className="text-xl font-semibold mb-2 text-glass-primary">{title}</RTLText>
          <RTLText as="div" className="text-glass-secondary">{description}</RTLText>
        </div>
      </RTLFlex>
    </div>
  );
};

const Features = () => {
  const { t } = useTranslation();
  const { language } = useAppSelector(state => state.language);

  return (
    <section id="features" className="py-20 glass-morphism">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-16">
          <RTLText as="h2" align="center" className="text-3xl md:text-4xl font-bold mb-4 text-glass-primary">
            {t('features.title')} <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-blue-400">{t('features.subtitle')}</span>
          </RTLText>
          <RTLText as="div" align="center" className="text-glass-secondary text-lg">
            {t('features.description')}
          </RTLText>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <FeatureCard
            icon={Target}
            title={t('features.ideaValidation')}
            description={t('features.ideaValidationDesc')}
          />
          <FeatureCard
            icon={BookOpen}
            title={t('features.businessPlanning')}
            description={t('features.businessPlanningDesc')}
          />
          <FeatureCard
            icon={Users}
            title={t('features.mentorshipPrograms')}
            description={t('features.mentorshipProgramsDesc')}
          />
          <FeatureCard
            icon={DollarSign}
            title={t('features.fundingSupport')}
            description={t('features.fundingSupportDesc')}
          />
          <FeatureCard
            icon={PieChart}
            title={t('features.marketResearch')}
            description={t('features.marketResearchDesc')}
          />
          <FeatureCard
            icon={Network}
            title={t('features.networkingEvents')}
            description={t('features.networkingEventsDesc')}
          />
        </div>

        <div className="mt-16">
          <div className="glass-light rounded-xl p-8 border shadow-lg">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-10">
              <div>
                <RTLFlex as="h3" className="text-2xl font-semibold mb-6 items-center text-glass-primary">
                  <RTLIcon icon={TrendingUp} className="text-glass-accent" size={28} flipInRTL={false} />
                  {t('features.incubatorPrograms')}
                </RTLFlex>
                <div className="space-y-6">
                  <div className={`relative ${language === 'ar' ? 'pr-8' : 'pl-8'} before:content-[''] before:absolute ${language === 'ar' ? 'before:right-0' : 'before:left-0'} before:top-0 before:bottom-0 before:w-1 before:bg-gradient-to-b before:from-purple-500 before:to-purple-400 before:rounded-full`}>
                    <RTLText as="h4" className="text-xl font-semibold mb-2 text-glass-primary">
                      {t('features.businessDevelopment')}
                    </RTLText>
                    <RTLText as="div" className="text-glass-secondary mb-3">
                      {t('features.businessDevelopmentDesc')}
                    </RTLText>
                    <RTLFlex className="gap-2">
                      <span className="px-3 py-1 text-xs font-medium rounded-full glass-light text-glass-primary">
                        Strategy
                      </span>
                      <span className="px-3 py-1 text-xs font-medium rounded-full glass-light text-glass-primary">
                        Planning
                      </span>
                      <span className="px-3 py-1 text-xs font-medium rounded-full glass-light text-glass-primary">
                        Execution
                      </span>
                    </RTLFlex>
                  </div>

                  <div className={`relative ${language === 'ar' ? 'pr-8' : 'pl-8'} before:content-[''] before:absolute ${language === 'ar' ? 'before:right-0' : 'before:left-0'} before:top-0 before:bottom-0 before:w-1 before:bg-gradient-to-b before:from-blue-500 before:to-blue-400 before:rounded-full`}>
                    <RTLText as="h4" className="text-xl font-semibold mb-2 text-glass-primary">
                      {t('features.entrepreneurshipTraining')}
                    </RTLText>
                    <RTLText as="div" className="text-glass-secondary mb-3">
                      {t('features.entrepreneurshipTrainingDesc')}
                    </RTLText>
                    <RTLFlex className="gap-2">
                      <span className="px-3 py-1 text-xs font-medium rounded-full glass-light text-glass-primary">
                        Leadership
                      </span>
                      <span className="px-3 py-1 text-xs font-medium rounded-full glass-light text-glass-primary">
                        Innovation
                      </span>
                      <span className="px-3 py-1 text-xs font-medium rounded-full glass-light text-glass-primary">
                        Management
                      </span>
                    </RTLFlex>
                  </div>

                  <div className={`relative ${language === 'ar' ? 'pr-8' : 'pl-8'} before:content-[''] before:absolute ${language === 'ar' ? 'before:right-0' : 'before:left-0'} before:top-0 before:bottom-0 before:w-1 before:bg-gradient-to-b before:from-cyan-500 before:to-cyan-400 before:rounded-full`}>
                    <RTLText as="h4" className="text-xl font-semibold mb-2 text-glass-primary">
                      {t('features.startupAcceleration')}
                    </RTLText>
                    <RTLText as="div" className="text-glass-secondary mb-3">
                      {t('features.startupAccelerationDesc')}
                    </RTLText>
                    <RTLFlex className="gap-2">
                      <span className="px-3 py-1 text-xs font-medium rounded-full glass-light text-glass-primary">
                        Growth
                      </span>
                      <span className="px-3 py-1 text-xs font-medium rounded-full glass-light text-glass-primary">
                        Scaling
                      </span>
                      <span className="px-3 py-1 text-xs font-medium rounded-full glass-light text-glass-primary">
                        Launch
                      </span>
                    </RTLFlex>
                  </div>
                </div>
              </div>

              <div>
                <RTLFlex as="h3" className="text-2xl font-semibold mb-6 items-center text-glass-primary">
                  <RTLIcon icon={Lightbulb} className="text-glass-accent" size={28} flipInRTL={false} />
                  {t('features.entrepreneurSupport')}
                </RTLFlex>
                <div className="glass-light rounded-xl p-6 mb-6 border">
                  <RTLText as="h4" className="text-xl font-semibold mb-4 text-glass-primary">
                    {t('features.aiPoweredGuidance')}
                  </RTLText>
                  <RTLText as="div" className="text-glass-secondary mb-4">
                    {t('features.aiGuidanceDesc')}
                  </RTLText>
                  <ul className="space-y-2 text-glass-secondary">
                    <RTLFlex as="li" className="items-center gap-2">
                      <span className="w-2 h-2 rounded-full bg-purple-400"></span>
                      <RTLText as="span">{t('features.aiFeatures.businessAdvisor')}</RTLText>
                    </RTLFlex>
                    <RTLFlex as="li" className="items-center gap-2">
                      <span className="w-2 h-2 rounded-full bg-purple-400"></span>
                      <RTLText as="span">{t('features.aiFeatures.marketAnalysis')}</RTLText>
                    </RTLFlex>
                    <RTLFlex as="li" className="items-center gap-2">
                      <span className="w-2 h-2 rounded-full bg-purple-400"></span>
                      <RTLText as="span">{t('features.aiFeatures.progressTracking')}</RTLText>
                    </RTLFlex>
                  </ul>
                </div>

                <div className="glass-light rounded-xl p-6 border">
                  <RTLText as="h4" className="text-xl font-semibold mb-4 text-glass-primary">
                    {t('features.communityNetworking')}
                  </RTLText>
                  <RTLText as="p" className="text-glass-secondary mb-4">
                    {t('features.communityDesc')}
                  </RTLText>
                  <button className="glass-morphism px-5 py-2 font-medium text-glass-primary hover:bg-glass-hover active:bg-glass-active rounded-full text-sm hover:shadow-glow transition-all duration-300">
                    {t('features.joinCommunity')}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Features;