import React from 'react';
import { Trash2 } from 'lucide-react';
import { Post } from '../../../../../services/api';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../../hooks/useLanguage';
interface PostDeleteModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedPost: Post | null;
  formError: string | null;
  formSubmitting: boolean;
  onConfirmDelete: () => void;
}

const PostDeleteModal: React.FC<PostDeleteModalProps> = ({ isOpen,
  onClose,
  selectedPost,
  formError,
  formSubmitting,
  onConfirmDelete
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  if (!isOpen || !selectedPost) return null;

  return (
    <div className={`fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4 ${isRTL ? "flex-row-reverse" : ""}`}>
      <div className="bg-indigo-900/90 rounded-xl shadow-lg w-full max-w-md p-6">
        <h2 className="text-xl font-bold mb-4">t("admin.confirm.delete", "Confirm Delete")</h2>
        <div className="text-gray-300 mb-6">
          Are you sure you want to delete the post "{selectedPost.title}"? This action cannot be undone.
        </div>
        {formError && (
          <div className="mb-4 p-3 bg-red-900/50 border border-red-800 text-white rounded-lg">
            {formError}
          </div>
        )}
        <div className={`flex justify-end ${isRTL ? "flex-row-reverse" : ""}`}>
          <button
            type="button"
            onClick={onClose}
            className={`px-4 py-2 bg-indigo-800/50 hover:bg-indigo-700/50 rounded-lg mr-2 ${isRTL ? "space-x-reverse" : ""}`}
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={onConfirmDelete}
            disabled={formSubmitting}
            className={`px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg font-medium flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
          >
            {formSubmitting ? (
              <>
                <div className={`w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 ${isRTL ? "space-x-reverse" : ""}`}></div>
                Deleting...
              </>
            ) : (
              <>
                <Trash2 size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                Delete
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default PostDeleteModal;
