import { useState, useEffect, useCallback } from 'react';
import { forumApi } from '../../../../../services/forumApi';

interface UseForumAnalyticsDataResult {
  activeTab: string;
  setActiveTab: React.Dispatch<React.SetStateAction<string>>;
  loading: boolean;
  overviewData: any;
  activityData: any;
  contributorsData: any;
  popularThreadsData: any;
  engagementData: any;
  period: string;
  setPeriod: React.Dispatch<React.SetStateAction<string>>;
  timeRange: number;
  setTimeRange: React.Dispatch<React.SetStateAction<number>>;
  popularPeriod: string;
  setPopularPeriod: React.Dispatch<React.SetStateAction<string>>;
  fetchOverviewData: () => Promise<void>;
  fetchActivityData: () => Promise<void>;
  fetchContributorsData: () => Promise<void>;
  fetchPopularThreadsData: () => Promise<void>;
  fetchEngagementData: () => Promise<void>;
}

export const useForumAnalyticsData = (): UseForumAnalyticsDataResult => {
  const [activeTab, setActiveTab] = useState<string>('overview');
  const [loading, setLoading] = useState<boolean>(true);
  const [overviewData, setOverviewData] = useState<any>(null);
  const [activityData, setActivityData] = useState<any>(null);
  const [contributorsData, setContributorsData] = useState<any>(null);
  const [popularThreadsData, setPopularThreadsData] = useState<any>(null);
  const [engagementData, setEngagementData] = useState<any>(null);
  const [period, setPeriod] = useState<string>('day');
  const [timeRange, setTimeRange] = useState<number>(30);
  const [popularPeriod, setPopularPeriod] = useState<string>('month');

  // Fetch overview data
  const fetchOverviewData = useCallback(async () => {
    setLoading(true);
    try {
      const data = await forumApi.getForumAnalytics('overview');
      setOverviewData(data);
    } catch (error) {
      console.error('Error fetching overview data:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch activity data
  const fetchActivityData = useCallback(async () => {
    setLoading(true);
    try {
      const data = await forumApi.getForumAnalytics('activity', { period, days: timeRange });
      setActivityData(data);
    } catch (error) {
      console.error('Error fetching activity data:', error);
    } finally {
      setLoading(false);
    }
  }, [period, timeRange]);

  // Fetch contributors data
  const fetchContributorsData = useCallback(async () => {
    setLoading(true);
    try {
      const data = await forumApi.getForumAnalytics('contributors', { limit: 10 });
      setContributorsData(data);
    } catch (error) {
      console.error('Error fetching contributors data:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch popular threads data
  const fetchPopularThreadsData = useCallback(async () => {
    setLoading(true);
    try {
      const data = await forumApi.getForumAnalytics('popular_threads', { period: popularPeriod, limit: 10 });
      setPopularThreadsData(data);
    } catch (error) {
      console.error('Error fetching popular threads data:', error);
    } finally {
      setLoading(false);
    }
  }, [popularPeriod]);

  // Fetch engagement data
  const fetchEngagementData = useCallback(async () => {
    setLoading(true);
    try {
      const data = await forumApi.getForumAnalytics('engagement');
      setEngagementData(data);
    } catch (error) {
      console.error('Error fetching engagement data:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  // Initial data fetch
  useEffect(() => {
    fetchOverviewData();
  }, [fetchOverviewData]);

  // Fetch data when tab changes
  useEffect(() => {
    if (activeTab === 'activity') {
      fetchActivityData();
    } else if (activeTab === 'contributors') {
      fetchContributorsData();
    } else if (activeTab === 'popular') {
      fetchPopularThreadsData();
    } else if (activeTab === 'engagement') {
      fetchEngagementData();
    }
  }, [activeTab, fetchActivityData, fetchContributorsData, fetchPopularThreadsData, fetchEngagementData]);

  return {
    activeTab,
    setActiveTab,
    loading,
    overviewData,
    activityData,
    contributorsData,
    popularThreadsData,
    engagementData,
    period,
    setPeriod,
    timeRange,
    setTimeRange,
    popularPeriod,
    setPopularPeriod,
    fetchOverviewData,
    fetchActivityData,
    fetchContributorsData,
    fetchPopularThreadsData,
    fetchEngagementData
  };
};

export default useForumAnalyticsData;
