import React from 'react';
import Navbar from '../components/Navbar';
import EnhancedHero from '../components/EnhancedHero';
import About from '../components/About';
import EnhancedAIFeatures from '../components/EnhancedAIFeatures';
import AICapabilitiesShowcase from '../components/AICapabilitiesShowcase';
import Features from '../components/Features';
import Events from '../components/Events';
import Community from '../components/Community';
import Contact from '../components/Contact';
import Footer from '../components/Footer';

import { useTranslation } from 'react-i18next';
import { useLanguage } from '../hooks/useLanguage';
const HomePage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  // ✅ REMOVED: Redirect logic moved to SmartFallback for better route handling

  return (
    <div className="min-h-screen">
      <Navbar />
      <EnhancedHero />
      <About />
      <EnhancedAIFeatures />
      <AICapabilitiesShowcase />
      <Features />
      <Events />
      <Community />
      <Contact />
      <Footer />
    </div>
  );
};

export default HomePage;
