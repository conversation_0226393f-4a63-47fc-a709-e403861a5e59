# Generated by Django 5.1.7 on 2025-05-15 19:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('incubator', '0007_templatesectiondefinition_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='mentorshipsession',
            name='calendar_provider',
            field=models.CharField(blank=True, help_text='Calendar provider (google_calendar, outlook, etc.)', max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='mentorshipsession',
            name='mentee_calendar_event_id',
            field=models.CharField(blank=True, help_text='Calendar event ID for mentee', max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='mentorshipsession',
            name='mentor_calendar_event_id',
            field=models.CharField(blank=True, help_text='Calendar event ID for mentor', max_length=200, null=True),
        ),
    ]
