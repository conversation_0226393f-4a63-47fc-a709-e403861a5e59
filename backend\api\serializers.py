from rest_framework import serializers
from django.contrib.auth.models import User
from .models import Event, Resource, Post, Comment, Tag, MembershipApplication
from users.serializers import UserSerializer

class TagSerializer(serializers.ModelSerializer):
    class Meta:
        model = Tag
        fields = ['id', 'name', 'slug', 'description']
        read_only_fields = ['id', 'slug']

class CommentSerializer(serializers.ModelSerializer):
    author = UserSerializer(read_only=True)
    author_id = serializers.IntegerField(write_only=True)

    class Meta:
        model = Comment
        fields = ['id', 'post', 'author', 'author_id', 'content', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']

class PostSerializer(serializers.ModelSerializer):
    author = UserSerializer(read_only=True)
    author_id = serializers.IntegerField(write_only=True)
    comments = CommentSerializer(many=True, read_only=True)
    like_count = serializers.IntegerField(read_only=True)
    is_liked = serializers.SerializerMethodField()
    tags = TagSerializer(many=True, read_only=True)
    tag_ids = serializers.PrimaryKeyRelatedField(
        many=True,
        write_only=True,
        queryset=Tag.objects.all(),
        required=False,
        source='tags'
    )

    class Meta:
        model = Post
        fields = ['id', 'title', 'content', 'author', 'author_id', 'image',
                  'like_count', 'is_liked', 'comments', 'tags', 'tag_ids',
                  'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']

    def get_is_liked(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return obj.likes.filter(id=request.user.id).exists()
        return False

class EventSerializer(serializers.ModelSerializer):
    organizer = UserSerializer(read_only=True)
    organizer_id = serializers.IntegerField(write_only=True)
    attendees = UserSerializer(many=True, read_only=True)
    attendee_count = serializers.SerializerMethodField()
    is_attending = serializers.SerializerMethodField()

    class Meta:
        model = Event
        fields = ['id', 'title', 'description', 'date', 'location', 'is_virtual',
                  'virtual_link', 'image', 'organizer', 'organizer_id', 'attendees',
                  'attendee_count', 'is_attending', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']

    def get_attendee_count(self, obj):
        return obj.attendees.count()

    def get_is_attending(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return obj.attendees.filter(id=request.user.id).exists()
        return False

class ResourceSerializer(serializers.ModelSerializer):
    author = UserSerializer(read_only=True)
    author_id = serializers.IntegerField(write_only=True)
    tags = TagSerializer(many=True, read_only=True)
    tag_ids = serializers.PrimaryKeyRelatedField(
        many=True,
        write_only=True,
        queryset=Tag.objects.all(),
        required=False,
        source='tags'
    )

    class Meta:
        model = Resource
        fields = ['id', 'title', 'description', 'resource_type', 'url',
                  'image', 'author', 'author_id', 'tags', 'tag_ids',
                  'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']

class MembershipApplicationSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    user_id = serializers.IntegerField(write_only=True, required=False)
    reviewed_by = UserSerializer(read_only=True)

    class Meta:
        model = MembershipApplication
        fields = [
            'id', 'user', 'user_id', 'full_name', 'email', 'phone', 'location',
            'expertise_areas', 'expertise_level', 'background', 'motivation',
            'linkedin_profile', 'github_profile', 'portfolio_url',
            'status', 'reviewed_by', 'reviewed_at', 'review_notes',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'status', 'reviewed_by', 'reviewed_at', 'review_notes', 'created_at', 'updated_at']
