/**
 * Enhanced Centralized AI Hook
 * Single React hook for all AI operations across the application
 * Version 2.0 with intelligent features and proper error handling
 * Replaces ALL other AI hooks!
 */

import { useState, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  centralizedAiApi,
  CentralizedAIResponse,
  AIStatus,
  LanguageDetectionResponse
} from '../services/centralizedAiApi';

// Hook return types
interface UseCentralizedAIReturn {
  // Chat functionality
  chat: (message: string, language?: string) => Promise<CentralizedAIResponse>;
  chatAuto: (message: string) => Promise<CentralizedAIResponse>;
  chatEnglish: (message: string) => Promise<CentralizedAIResponse>;
  chatArabic: (message: string) => Promise<CentralizedAIResponse>;

  // Business analysis
  analyzeBusinessIdea: (business_idea: string, language?: string) => Promise<CentralizedAIResponse>;
  analyzeBusinessAuto: (business_idea: string) => Promise<CentralizedAIResponse>;
  analyzeBusinessById: (business_idea_id: number, language?: string) => Promise<CentralizedAIResponse>;

  // Text analysis
  analyzeText: (text: string, language?: string) => Promise<CentralizedAIResponse>;
  analyzeTextAuto: (text: string) => Promise<CentralizedAIResponse>;

  // NEW: Intelligent content generation
  generateIntelligentContent: (data: { content_type: string; context: Record<string, any>; language?: string }) => Promise<CentralizedAIResponse>;
  generateBusinessPlanSection: (data: { business_idea: string; section_type: string; industry?: string; language?: string }) => Promise<CentralizedAIResponse>;
  generateMarketAnalysis: (data: { business_idea: string; target_market: string; language?: string }) => Promise<CentralizedAIResponse>;
  generateFinancialProjections: (data: { business_idea: string; revenue_model: string; language?: string }) => Promise<CentralizedAIResponse>;
  generateRiskAssessment: (data: { business_idea: string; industry: string; language?: string }) => Promise<CentralizedAIResponse>;

  // Language detection
  detectLanguage: (text: string) => Promise<LanguageDetectionResponse>;
  
  // Status and testing
  status: AIStatus | null;
  isStatusLoading: boolean;
  statusError: string | null;
  refreshStatus: () => void;
  testService: () => Promise<any>;
  
  // Loading states
  isChatting: boolean;
  isAnalyzing: boolean;
  isDetectingLanguage: boolean;
  
  // Error states
  chatError: string | null;
  analysisError: string | null;
  detectionError: string | null;
  
  // Last responses
  lastChatResponse: CentralizedAIResponse | null;
  lastAnalysisResponse: CentralizedAIResponse | null;
  
  // Utility functions
  clearErrors: () => void;
  resetState: () => void;
  isAvailable: boolean;
}

/**
 * Main centralized AI hook
 * Replaces ALL other AI hooks in the application
 */
export const useCentralizedAI = (): UseCentralizedAIReturn => {
  const queryClient = useQueryClient();
  
  // Local state
  const [lastChatResponse, setLastChatResponse] = useState<CentralizedAIResponse | null>(null);
  const [lastAnalysisResponse, setLastAnalysisResponse] = useState<CentralizedAIResponse | null>(null);
  const [chatError, setChatError] = useState<string | null>(null);
  const [analysisError, setAnalysisError] = useState<string | null>(null);
  const [detectionError, setDetectionError] = useState<string | null>(null);

  // Status query
  const {
    data: status,
    isLoading: isStatusLoading,
    error: statusError,
    refetch: refreshStatus
  } = useQuery({
    queryKey: ['centralized-ai-status'],
    queryFn: centralizedAiApi.getStatus,
    staleTime: 30000, // 30 seconds
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  // Chat mutation
  const chatMutation = useMutation({
    mutationFn: ({ message, language }: { message: string; language?: string }) =>
      centralizedAiApi.chat({ message, language }),
    onSuccess: (data) => {
      setLastChatResponse(data);
      setChatError(null);
      queryClient.invalidateQueries({ queryKey: ['centralized-ai-status'] });
    },
    onError: (error: any) => {
      setChatError(error.message || 'Failed to send message');
    },
  });

  // Business analysis mutation
  const businessAnalysisMutation = useMutation({
    mutationFn: ({ business_idea, business_idea_id, language }: { 
      business_idea?: string; 
      business_idea_id?: number; 
      language?: string 
    }) =>
      centralizedAiApi.analyzeBusinessIdea({ business_idea, business_idea_id, language }),
    onSuccess: (data) => {
      setLastAnalysisResponse(data);
      setAnalysisError(null);
      queryClient.invalidateQueries({ queryKey: ['centralized-ai-status'] });
    },
    onError: (error: any) => {
      setAnalysisError(error.message || 'Failed to analyze business idea');
    },
  });

  // Text analysis mutation
  const textAnalysisMutation = useMutation({
    mutationFn: ({ text, language }: { text: string; language?: string }) =>
      centralizedAiApi.analyzeText({ text, language }),
    onSuccess: (data) => {
      setLastAnalysisResponse(data);
      setAnalysisError(null);
      queryClient.invalidateQueries({ queryKey: ['centralized-ai-status'] });
    },
    onError: (error: any) => {
      setAnalysisError(error.message || 'Failed to analyze text');
    },
  });

  // Language detection mutation
  const languageDetectionMutation = useMutation({
    mutationFn: (text: string) => centralizedAiApi.detectLanguage(text),
    onError: (error: any) => {
      setDetectionError(error.message || 'Failed to detect language');
    },
  });

  // Callback functions
  const chat = useCallback(
    async (message: string, language?: string) => {
      return chatMutation.mutateAsync({ message, language });
    },
    [chatMutation]
  );

  const chatAuto = useCallback(
    async (message: string) => {
      return chatMutation.mutateAsync({ message, language: 'auto' });
    },
    [chatMutation]
  );

  const chatEnglish = useCallback(
    async (message: string) => {
      return chatMutation.mutateAsync({ message, language: 'en' });
    },
    [chatMutation]
  );

  const chatArabic = useCallback(
    async (message: string) => {
      return chatMutation.mutateAsync({ message, language: 'ar' });
    },
    [chatMutation]
  );

  const analyzeBusinessIdea = useCallback(
    async (business_idea: string, language?: string) => {
      return businessAnalysisMutation.mutateAsync({ business_idea, language });
    },
    [businessAnalysisMutation]
  );

  const analyzeBusinessAuto = useCallback(
    async (business_idea: string) => {
      return businessAnalysisMutation.mutateAsync({ business_idea, language: 'auto' });
    },
    [businessAnalysisMutation]
  );

  const analyzeBusinessById = useCallback(
    async (business_idea_id: number, language?: string) => {
      return businessAnalysisMutation.mutateAsync({ business_idea_id, language });
    },
    [businessAnalysisMutation]
  );

  const analyzeText = useCallback(
    async (text: string, language?: string) => {
      return textAnalysisMutation.mutateAsync({ text, language });
    },
    [textAnalysisMutation]
  );

  const analyzeTextAuto = useCallback(
    async (text: string) => {
      return textAnalysisMutation.mutateAsync({ text, language: 'auto' });
    },
    [textAnalysisMutation]
  );

  const detectLanguage = useCallback(
    async (text: string) => {
      return languageDetectionMutation.mutateAsync(text);
    },
    [languageDetectionMutation]
  );

  // NEW: Intelligent content generation methods
  const generateIntelligentContent = useCallback(
    async (data: { content_type: string; context: Record<string, any>; language?: string }) => {
      return centralizedAiApi.generateIntelligentContent(data);
    },
    []
  );

  const generateBusinessPlanSection = useCallback(
    async (data: { business_idea: string; section_type: string; industry?: string; language?: string }) => {
      return centralizedAiApi.generateBusinessPlanSection(data);
    },
    []
  );

  const generateMarketAnalysis = useCallback(
    async (data: { business_idea: string; target_market: string; language?: string }) => {
      return centralizedAiApi.generateMarketAnalysis(data);
    },
    []
  );

  const generateFinancialProjections = useCallback(
    async (data: { business_idea: string; revenue_model: string; language?: string }) => {
      return centralizedAiApi.generateFinancialProjections(data);
    },
    []
  );

  const generateRiskAssessment = useCallback(
    async (data: { business_idea: string; industry: string; language?: string }) => {
      return centralizedAiApi.generateRiskAssessment(data);
    },
    []
  );

  const testService = useCallback(async () => {
    return centralizedAiApi.test();
  }, []);

  const clearErrors = useCallback(() => {
    setChatError(null);
    setAnalysisError(null);
    setDetectionError(null);
  }, []);

  const resetState = useCallback(() => {
    setLastChatResponse(null);
    setLastAnalysisResponse(null);
    clearErrors();
    chatMutation.reset();
    businessAnalysisMutation.reset();
    textAnalysisMutation.reset();
    languageDetectionMutation.reset();
  }, [chatMutation, businessAnalysisMutation, textAnalysisMutation, languageDetectionMutation, clearErrors]);

  return {
    // Chat functionality
    chat,
    chatAuto,
    chatEnglish,
    chatArabic,

    // Business analysis
    analyzeBusinessIdea,
    analyzeBusinessAuto,
    analyzeBusinessById,

    // Text analysis
    analyzeText,
    analyzeTextAuto,

    // NEW: Intelligent content generation
    generateIntelligentContent,
    generateBusinessPlanSection,
    generateMarketAnalysis,
    generateFinancialProjections,
    generateRiskAssessment,

    // Language detection
    detectLanguage,
    
    // Status and testing
    status: status || null,
    isStatusLoading,
    statusError: statusError?.message || null,
    refreshStatus: () => refreshStatus(),
    testService,
    
    // Loading states
    isChatting: chatMutation.isPending,
    isAnalyzing: businessAnalysisMutation.isPending || textAnalysisMutation.isPending,
    isDetectingLanguage: languageDetectionMutation.isPending,
    
    // Error states
    chatError,
    analysisError,
    detectionError,
    
    // Last responses
    lastChatResponse,
    lastAnalysisResponse,
    
    // Utility functions
    clearErrors,
    resetState,
    isAvailable: status?.available || false,
  };
};

/**
 * Simplified chat hook
 */
export const useCentralizedChat = () => {
  const { chat, chatAuto, isChatting, chatError, lastChatResponse, clearErrors } = useCentralizedAI();
  
  return {
    chat,
    chatAuto,
    isLoading: isChatting,
    error: chatError,
    lastResponse: lastChatResponse,
    clearError: clearErrors,
  };
};

/**
 * Business analysis hook
 */
export const useCentralizedBusinessAnalysis = () => {
  const { analyzeBusinessIdea, analyzeBusinessAuto, analyzeBusinessById, isAnalyzing, analysisError, lastAnalysisResponse } = useCentralizedAI();
  
  return {
    analyzeBusinessIdea,
    analyzeBusinessAuto,
    analyzeBusinessById,
    isLoading: isAnalyzing,
    error: analysisError,
    lastResponse: lastAnalysisResponse,
  };
};

/**
 * AI service status hook
 */
export const useCentralizedAIStatus = () => {
  const { status, isStatusLoading, statusError, refreshStatus, testService, isAvailable } = useCentralizedAI();

  return {
    status,
    isLoading: isStatusLoading,
    error: statusError,
    refresh: refreshStatus,
    test: testService,
    isAvailable,
    isCentralized: status?.centralized_service || false,
  };
};

/**
 * NEW: Intelligent content generation hook
 * Advanced AI features for business intelligence
 */
export const useIntelligentContent = () => {
  const {
    generateIntelligentContent,
    generateBusinessPlanSection,
    generateMarketAnalysis,
    generateFinancialProjections,
    generateRiskAssessment,
    isAvailable
  } = useCentralizedAI();

  return {
    // Core intelligent content generation
    generateContent: generateIntelligentContent,

    // Specialized content generators
    generateBusinessPlanSection,
    generateMarketAnalysis,
    generateFinancialProjections,
    generateRiskAssessment,

    // Status
    isAvailable,

    // Convenience methods
    generateExecutiveSummary: (businessIdea: string, industry?: string) =>
      generateBusinessPlanSection({
        business_idea: businessIdea,
        section_type: 'executive_summary',
        industry
      }),

    generateCompetitorAnalysis: (businessIdea: string, targetMarket: string) =>
      generateMarketAnalysis({
        business_idea: businessIdea,
        target_market: targetMarket
      }),

    generateRevenueProjections: (businessIdea: string, revenueModel: string) =>
      generateFinancialProjections({
        business_idea: businessIdea,
        revenue_model: revenueModel
      }),
  };
};



export default useCentralizedAI;
