/**
 * Automated Test Reporting Dashboard
 * Generates comprehensive test reports and metrics
 */

interface TestResult {
  name: string;
  status: 'passed' | 'failed' | 'skipped';
  duration: number;
  error?: string;
}

interface TestSuite {
  name: string;
  tests: TestResult[];
  duration: number;
  passed: number;
  failed: number;
  skipped: number;
}

interface TestReport {
  timestamp: string;
  totalTests: number;
  totalPassed: number;
  totalFailed: number;
  totalSkipped: number;
  totalDuration: number;
  suites: TestSuite[];
  coverage?: CoverageReport;
  performance?: PerformanceMetrics;
}

interface CoverageReport {
  statements: number;
  branches: number;
  functions: number;
  lines: number;
  files: FileCoverage[];
}

interface FileCoverage {
  path: string;
  statements: number;
  branches: number;
  functions: number;
  lines: number;
}

interface PerformanceMetrics {
  averageTestDuration: number;
  slowestTests: TestResult[];
  fastestTests: TestResult[];
  memoryUsage?: number;
  cpuUsage?: number;
}

export class TestReporter {
  private results: TestSuite[] = [];
  private startTime: number = 0;

  startSuite(suiteName: string): void {
    this.startTime = performance.now();
    console.log(`🧪 Starting test suite: ${suiteName}`);
  }

  endSuite(suiteName: string, tests: TestResult[]): void {
    const endTime = performance.now();
    const duration = endTime - this.startTime;
    
    const suite: TestSuite = {
      name: suiteName,
      tests,
      duration,
      passed: tests.filter(t => t.status === 'passed').length,
      failed: tests.filter(t => t.status === 'failed').length,
      skipped: tests.filter(t => t.status === 'skipped').length
    };

    this.results.push(suite);
    
    console.log(`✅ Completed test suite: ${suiteName}`);
    console.log(`   Tests: ${suite.passed} passed, ${suite.failed} failed, ${suite.skipped} skipped`);
    console.log(`   Duration: ${duration.toFixed(2)}ms`);
  }

  generateReport(): TestReport {
    const totalTests = this.results.reduce((sum, suite) => sum + suite.tests.length, 0);
    const totalPassed = this.results.reduce((sum, suite) => sum + suite.passed, 0);
    const totalFailed = this.results.reduce((sum, suite) => sum + suite.failed, 0);
    const totalSkipped = this.results.reduce((sum, suite) => sum + suite.skipped, 0);
    const totalDuration = this.results.reduce((sum, suite) => sum + suite.duration, 0);

    const allTests = this.results.flatMap(suite => suite.tests);
    const performance = this.calculatePerformanceMetrics(allTests);

    return {
      timestamp: new Date().toISOString(),
      totalTests,
      totalPassed,
      totalFailed,
      totalSkipped,
      totalDuration,
      suites: this.results,
      performance
    };
  }

  private calculatePerformanceMetrics(tests: TestResult[]): PerformanceMetrics {
    const durations = tests.map(t => t.duration);
    const averageTestDuration = durations.reduce((sum, d) => sum + d, 0) / durations.length;
    
    const sortedTests = [...tests].sort((a, b) => b.duration - a.duration);
    const slowestTests = sortedTests.slice(0, 5);
    const fastestTests = sortedTests.slice(-5).reverse();

    return {
      averageTestDuration,
      slowestTests,
      fastestTests,
      memoryUsage: (performance as any).memory?.usedJSHeapSize,
      cpuUsage: this.estimateCpuUsage()
    };
  }

  private estimateCpuUsage(): number {
    // Simple CPU usage estimation based on test execution time
    const totalDuration = this.results.reduce((sum, suite) => sum + suite.duration, 0);
    const testCount = this.results.reduce((sum, suite) => sum + suite.tests.length, 0);
    
    // Normalize to a percentage (this is a rough estimation)
    return Math.min(100, (totalDuration / testCount) * 0.1);
  }

  generateHTMLReport(report: TestReport): string {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRUD Test Report</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 8px 8px 0 0; }
        .header h1 { margin: 0; font-size: 2.5em; }
        .header p { margin: 10px 0 0 0; opacity: 0.9; }
        .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; padding: 30px; }
        .metric { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; }
        .metric-value { font-size: 2em; font-weight: bold; color: #333; }
        .metric-label { color: #666; margin-top: 5px; }
        .passed { color: #28a745; }
        .failed { color: #dc3545; }
        .skipped { color: #ffc107; }
        .suites { padding: 0 30px 30px; }
        .suite { margin-bottom: 30px; border: 1px solid #e9ecef; border-radius: 8px; overflow: hidden; }
        .suite-header { background: #f8f9fa; padding: 15px 20px; border-bottom: 1px solid #e9ecef; }
        .suite-name { font-size: 1.2em; font-weight: bold; margin: 0; }
        .suite-stats { color: #666; margin-top: 5px; }
        .tests { padding: 20px; }
        .test { display: flex; justify-content: space-between; align-items: center; padding: 10px 0; border-bottom: 1px solid #f1f3f4; }
        .test:last-child { border-bottom: none; }
        .test-name { flex: 1; }
        .test-status { padding: 4px 8px; border-radius: 4px; font-size: 0.8em; font-weight: bold; }
        .test-duration { color: #666; margin-left: 10px; }
        .status-passed { background: #d4edda; color: #155724; }
        .status-failed { background: #f8d7da; color: #721c24; }
        .status-skipped { background: #fff3cd; color: #856404; }
        .performance { padding: 30px; background: #f8f9fa; margin-top: 20px; }
        .performance h3 { margin-top: 0; }
        .perf-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .perf-section { background: white; padding: 20px; border-radius: 8px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 CRUD Test Report</h1>
            <p>Generated on ${new Date(report.timestamp).toLocaleString()}</p>
        </div>
        
        <div class="metrics">
            <div class="metric">
                <div class="metric-value">${report.totalTests}</div>
                <div class="metric-label">Total Tests</div>
            </div>
            <div class="metric">
                <div class="metric-value passed">${report.totalPassed}</div>
                <div class="metric-label">Passed</div>
            </div>
            <div class="metric">
                <div class="metric-value failed">${report.totalFailed}</div>
                <div class="metric-label">Failed</div>
            </div>
            <div class="metric">
                <div class="metric-value skipped">${report.totalSkipped}</div>
                <div class="metric-label">Skipped</div>
            </div>
            <div class="metric">
                <div class="metric-value">${(report.totalDuration / 1000).toFixed(2)}s</div>
                <div class="metric-label">Total Duration</div>
            </div>
            <div class="metric">
                <div class="metric-value">${((report.totalPassed / report.totalTests) * 100).toFixed(1)}%</div>
                <div class="metric-label">Success Rate</div>
            </div>
        </div>

        <div class="suites">
            <h2>Test Suites</h2>
            ${report.suites.map(suite => `
                <div class="suite">
                    <div class="suite-header">
                        <h3 class="suite-name">${suite.name}</h3>
                        <div class="suite-stats">
                            ${suite.passed} passed, ${suite.failed} failed, ${suite.skipped} skipped • ${(suite.duration / 1000).toFixed(2)}s
                        </div>
                    </div>
                    <div class="tests">
                        ${suite.tests.map(test => `
                            <div class="test">
                                <div class="test-name">${test.name}</div>
                                <div>
                                    <span class="test-status status-${test.status}">${test.status.toUpperCase()}</span>
                                    <span class="test-duration">${test.duration.toFixed(2)}ms</span>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `).join('')}
        </div>

        ${report.performance ? `
            <div class="performance">
                <h2>Performance Metrics</h2>
                <div class="perf-grid">
                    <div class="perf-section">
                        <h3>Test Performance</h3>
                        <p><strong>Average Duration:</strong> ${report.performance.averageTestDuration.toFixed(2)}ms</p>
                        ${report.performance.memoryUsage ? `<p><strong>Memory Usage:</strong> ${(report.performance.memoryUsage / 1024 / 1024).toFixed(2)}MB</p>` : ''}
                        ${report.performance.cpuUsage ? `<p><strong>CPU Usage:</strong> ${report.performance.cpuUsage.toFixed(1)}%</p>` : ''}
                    </div>
                    
                    <div class="perf-section">
                        <h3>Slowest Tests</h3>
                        ${report.performance.slowestTests.slice(0, 3).map(test => `
                            <p><strong>${test.name}:</strong> ${test.duration.toFixed(2)}ms</p>
                        `).join('')}
                    </div>
                    
                    <div class="perf-section">
                        <h3>Fastest Tests</h3>
                        ${report.performance.fastestTests.slice(0, 3).map(test => `
                            <p><strong>${test.name}:</strong> ${test.duration.toFixed(2)}ms</p>
                        `).join('')}
                    </div>
                </div>
            </div>
        ` : ''}
    </div>
</body>
</html>`;
  }

  generateMarkdownReport(report: TestReport): string {
    const successRate = ((report.totalPassed / report.totalTests) * 100).toFixed(1);
    
    return `# 🧪 CRUD Test Report

**Generated:** ${new Date(report.timestamp).toLocaleString()}

## 📊 Summary

| Metric | Value |
|--------|-------|
| Total Tests | ${report.totalTests} |
| ✅ Passed | ${report.totalPassed} |
| ❌ Failed | ${report.totalFailed} |
| ⏭️ Skipped | ${report.totalSkipped} |
| ⏱️ Duration | ${(report.totalDuration / 1000).toFixed(2)}s |
| 📈 Success Rate | ${successRate}% |

## 🧪 Test Suites

${report.suites.map(suite => `
### ${suite.name}

- **Tests:** ${suite.tests.length}
- **Passed:** ${suite.passed}
- **Failed:** ${suite.failed}
- **Skipped:** ${suite.skipped}
- **Duration:** ${(suite.duration / 1000).toFixed(2)}s

${suite.tests.map(test => `- ${test.status === 'passed' ? '✅' : test.status === 'failed' ? '❌' : '⏭️'} ${test.name} (${test.duration.toFixed(2)}ms)`).join('\n')}
`).join('\n')}

${report.performance ? `
## ⚡ Performance Metrics

- **Average Test Duration:** ${report.performance.averageTestDuration.toFixed(2)}ms
${report.performance.memoryUsage ? `- **Memory Usage:** ${(report.performance.memoryUsage / 1024 / 1024).toFixed(2)}MB` : ''}
${report.performance.cpuUsage ? `- **CPU Usage:** ${report.performance.cpuUsage.toFixed(1)}%` : ''}

### Slowest Tests
${report.performance.slowestTests.slice(0, 5).map(test => `- ${test.name}: ${test.duration.toFixed(2)}ms`).join('\n')}

### Fastest Tests
${report.performance.fastestTests.slice(0, 5).map(test => `- ${test.name}: ${test.duration.toFixed(2)}ms`).join('\n')}
` : ''}

---
*Report generated by CRUD Test Reporter*`;
  }

  saveReport(report: TestReport, format: 'html' | 'markdown' | 'json' = 'html'): void {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
    switch (format) {
      case 'html':
        const htmlContent = this.generateHTMLReport(report);
        this.saveToFile(`test-report-${timestamp}.html`, htmlContent);
        break;
      case 'markdown':
        const markdownContent = this.generateMarkdownReport(report);
        this.saveToFile(`test-report-${timestamp}.md`, markdownContent);
        break;
      case 'json':
        const jsonContent = JSON.stringify(report, null, 2);
        this.saveToFile(`test-report-${timestamp}.json`, jsonContent);
        break;
    }
  }

  private saveToFile(filename: string, content: string): void {
    // In a real environment, this would save to the filesystem
    // For now, we'll just log the content
    console.log(`📄 Generated report: ${filename}`);
    console.log(`📊 Report size: ${content.length} characters`);
  }

  reset(): void {
    this.results = [];
    this.startTime = 0;
  }
}

// Export singleton instance
export const testReporter = new TestReporter();
