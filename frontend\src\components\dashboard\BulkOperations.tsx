import React, { useState } from 'react';
import { 
  Trash2, 
  Edit, 
  Archive, 
  Eye, 
  EyeOff, 
  CheckSquare, 
  Square, 
  MoreHorizontal,
  Loader2,
  AlertTriangle
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { RTLText, RTLFlex } from '../rtl';

interface BulkOperationsProps {
  selectedItems: number[];
  totalItems: number;
  onSelectAll: () => void;
  onDeselectAll: () => void;
  onDelete: (ids: number[]) => Promise<void>;
  onArchive?: (ids: number[]) => Promise<void>;
  onPublish?: (ids: number[]) => Promise<void>;
  onUnpublish?: (ids: number[]) => Promise<void>;
  itemType: string; // 'posts', 'events', 'resources', etc.
  disabled?: boolean;
}

const BulkOperations: React.FC<BulkOperationsProps> = ({
  selectedItems,
  totalItems,
  onSelectAll,
  onDeselectAll,
  onDelete,
  onArchive,
  onPublish,
  onUnpublish,
  itemType,
  disabled = false
}) => {
  const { t } = useTranslation();
  const { language, isRTL } = useLanguage();
  
  const [isDeleting, setIsDeleting] = useState(false);
  const [isArchiving, setIsArchiving] = useState(false);
  const [isPublishing, setIsPublishing] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showActionsMenu, setShowActionsMenu] = useState(false);

  const hasSelectedItems = selectedItems.length > 0;
  const allSelected = selectedItems.length === totalItems && totalItems > 0;

  const handleSelectToggle = () => {
    if (allSelected) {
      onDeselectAll();
    } else {
      onSelectAll();
    }
  };

  const handleDelete = async () => {
    if (selectedItems.length === 0) return;
    
    setIsDeleting(true);
    try {
      await onDelete(selectedItems);
      setShowDeleteConfirm(false);
      onDeselectAll();
    } catch (error) {
      console.error('Error deleting items:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleArchive = async () => {
    if (!onArchive || selectedItems.length === 0) return;
    
    setIsArchiving(true);
    try {
      await onArchive(selectedItems);
      onDeselectAll();
    } catch (error) {
      console.error('Error archiving items:', error);
    } finally {
      setIsArchiving(false);
    }
  };

  const handlePublish = async (publish: boolean) => {
    if (selectedItems.length === 0) return;
    
    setIsPublishing(true);
    try {
      if (publish && onPublish) {
        await onPublish(selectedItems);
      } else if (!publish && onUnpublish) {
        await onUnpublish(selectedItems);
      }
      onDeselectAll();
    } catch (error) {
      console.error('Error updating publish status:', error);
    } finally {
      setIsPublishing(false);
    }
  };

  if (totalItems === 0) {
    return null;
  }

  return (
    <>
      {/* Bulk Operations Bar */}
      <div className={`bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50 mb-6 ${disabled ? 'opacity-50' : ''}`}>
        <RTLFlex align="center" justify="between">
          {/* Selection Controls */}
          <RTLFlex align="center">
            <button
              onClick={handleSelectToggle}
              disabled={disabled}
              className="p-2 hover:bg-indigo-800/50 rounded-lg transition-colors disabled:cursor-not-allowed"
              title={allSelected ? t('bulk.deselectAll', 'Deselect All') : t('bulk.selectAll', 'Select All')}
            >
              {allSelected ? (
                <CheckSquare className="w-5 h-5 text-purple-400" />
              ) : (
                <Square className="w-5 h-5 text-gray-400" />
              )}
            </button>
            
            <RTLText as="div" className={`text-sm text-gray-400 ${language === 'ar' ? 'mr-3' : 'ml-3'}`}>
              {hasSelectedItems ? (
                <>
                  {selectedItems.length} {t('bulk.of', 'of')} {totalItems} {t(`bulk.${itemType}Selected`, `${itemType} selected`)}
                </>
              ) : (
                <>
                  {totalItems} {t(`bulk.${itemType}Total`, `${itemType} total`)}
                </>
              )}
            </RTLText>
          </RTLFlex>

          {/* Bulk Actions */}
          {hasSelectedItems && (
            <RTLFlex align="center" className="space-x-2">
              {/* Delete Button */}
              <button
                onClick={() => setShowDeleteConfirm(true)}
                disabled={disabled || isDeleting}
                className="px-3 py-2 bg-red-900/50 hover:bg-red-800/50 rounded-lg text-red-300 hover:text-red-200 transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                title={t('bulk.delete', 'Delete Selected')}
              >
                {isDeleting ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Trash2 className="w-4 h-4" />
                )}
                <span className={`text-sm ${language === 'ar' ? 'mr-2' : 'ml-2'}`}>
                  {t('bulk.delete', 'Delete')}
                </span>
              </button>

              {/* Archive Button */}
              {onArchive && (
                <button
                  onClick={handleArchive}
                  disabled={disabled || isArchiving}
                  className="px-3 py-2 bg-yellow-900/50 hover:bg-yellow-800/50 rounded-lg text-yellow-300 hover:text-yellow-200 transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                  title={t('bulk.archive', 'Archive Selected')}
                >
                  {isArchiving ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <Archive className="w-4 h-4" />
                  )}
                  <span className={`text-sm ${language === 'ar' ? 'mr-2' : 'ml-2'}`}>
                    {t('bulk.archive', 'Archive')}
                  </span>
                </button>
              )}

              {/* Publish/Unpublish Buttons */}
              {(onPublish || onUnpublish) && (
                <div className="relative">
                  <button
                    onClick={() => setShowActionsMenu(!showActionsMenu)}
                    disabled={disabled || isPublishing}
                    className="px-3 py-2 bg-blue-900/50 hover:bg-blue-800/50 rounded-lg text-blue-300 hover:text-blue-200 transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isPublishing ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <MoreHorizontal className="w-4 h-4" />
                    )}
                    <span className={`text-sm ${language === 'ar' ? 'mr-2' : 'ml-2'}`}>
                      {t('bulk.more', 'More')}
                    </span>
                  </button>

                  {/* Actions Dropdown */}
                  {showActionsMenu && (
                    <div className={`absolute top-full mt-2 ${language === 'ar' ? 'left-0' : 'right-0'} bg-indigo-900 border border-indigo-800 rounded-lg shadow-lg z-10 min-w-[150px]`}>
                      {onPublish && (
                        <button
                          onClick={() => {
                            handlePublish(true);
                            setShowActionsMenu(false);
                          }}
                          className="w-full px-4 py-2 text-left hover:bg-indigo-800/50 transition-colors flex items-center"
                        >
                          <Eye className={`w-4 h-4 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                          {t('bulk.publish', 'Publish')}
                        </button>
                      )}
                      {onUnpublish && (
                        <button
                          onClick={() => {
                            handlePublish(false);
                            setShowActionsMenu(false);
                          }}
                          className="w-full px-4 py-2 text-left hover:bg-indigo-800/50 transition-colors flex items-center"
                        >
                          <EyeOff className={`w-4 h-4 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                          {t('bulk.unpublish', 'Unpublish')}
                        </button>
                      )}
                    </div>
                  )}
                </div>
              )}
            </RTLFlex>
          )}
        </RTLFlex>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
          <div className="bg-indigo-900 rounded-lg w-full max-w-md p-6">
            <RTLFlex align="center" className="mb-4">
              <AlertTriangle className={`w-6 h-6 text-red-400 ${language === 'ar' ? 'ml-3' : 'mr-3'}`} />
              <RTLText as="h3" className="text-xl font-bold">
                {t('bulk.confirmDelete', 'Confirm Delete')}
              </RTLText>
            </RTLFlex>
            
            <RTLText as="div" className="text-gray-300 mb-6">
              {t('bulk.deleteWarning', {
                defaultValue: 'Are you sure you want to delete {{count}} {{itemType}}? This action cannot be undone.',
                count: selectedItems.length,
                itemType: t(`bulk.${itemType}`, itemType)
              })}
            </RTLText>

            <div className="bg-red-900/20 border border-red-800/50 rounded-lg p-3 mb-6">
              <RTLText as="div" className="text-red-300 text-sm">
                <strong>{selectedItems.length}</strong> {t(`bulk.${itemType}`, itemType)} {t('bulk.willBeDeleted', 'will be permanently deleted')}
              </RTLText>
            </div>

            <RTLFlex justify="end" className="space-x-3">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                disabled={isDeleting}
                className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors disabled:opacity-50"
              >
                {t('common.cancel', 'Cancel')}
              </button>
              <button
                onClick={handleDelete}
                disabled={isDeleting}
                className="px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors flex items-center disabled:opacity-50"
              >
                {isDeleting ? (
                  <>
                    <Loader2 className={`w-4 h-4 animate-spin ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                    {t('bulk.deleting', 'Deleting...')}
                  </>
                ) : (
                  <>
                    <Trash2 className={`w-4 h-4 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                    {t('bulk.deleteConfirm', 'Delete')}
                  </>
                )}
              </button>
            </RTLFlex>
          </div>
        </div>
      )}

      {/* Click outside to close actions menu */}
      {showActionsMenu && (
        <div 
          className="fixed inset-0 z-5" 
          onClick={() => setShowActionsMenu(false)}
        />
      )}
    </>
  );
};

export default BulkOperations;
