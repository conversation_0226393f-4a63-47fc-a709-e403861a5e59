import React from 'react';
import { useLanguage } from '../../hooks/useLanguage';
interface RTLChartProps {
  children: React.ReactNode;
  className?: string;
  dir?: 'ltr' | 'rtl';
  style?: React.CSSProperties;
  flipAxes?: boolean;
}

/**
 * RTL-aware chart component
 *
 * This component automatically handles RTL direction for charts
 * It can optionally flip the axes for RTL languages
 */
const RTLChart: React.FC<RTLChartProps> = ({
  children,
  className = '',
  dir,
  style = {},
  flipAxes = true,
}) => {
  const { language, isRTL: contextIsRTL } = useLanguage();
  const isRTL = dir ? dir === 'rtl' : contextIsRTL;

  // Apply RTL-specific styles for charts
  const rtlStyles: React.CSSProperties = isRTL && flipAxes
    ? {
        direction: 'rtl',
        transform: 'scaleX(-1)', // Flip the chart horizontally for RTL
        ...style,
      }
    : {
        direction: isRTL ? 'rtl' : 'ltr',
        ...style,
      };

  return (
    <div
      className={`rtl-chart ${className}`}
      dir={isRTL ? 'rtl' : 'ltr'}
      style={rtlStyles}
    >
      {children}
    </div>
  );
};

export default RTLChart;
