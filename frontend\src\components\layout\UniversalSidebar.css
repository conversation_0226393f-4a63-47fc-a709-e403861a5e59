/* Universal Sidebar Styles */

/* Import Arabic Fonts */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');

.universal-sidebar {
  background: linear-gradient(135deg, 
    rgba(17, 24, 39, 0.95) 0%, 
    rgba(55, 48, 163, 0.85) 50%, 
    rgba(79, 70, 229, 0.95) 100%
  );
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.universal-sidebar.rtl {
  border-right: none;
  border-left: 1px solid rgba(255, 255, 255, 0.1);
}

/* Sidebar Header */
.sidebar-header {
  background: rgba(0, 0, 0, 0.2);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* App Logo */
.app-logo {
  background: linear-gradient(135deg, #8B5CF6 0%, #3B82F6 100%);
  box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
}

/* Navigation Items */
.nav-item {
  position: relative;
  overflow: hidden;
}

.nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 0.75rem;
}

.nav-item:hover::before {
  opacity: 1;
}

.nav-item.active {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.2) 0%, rgba(59, 130, 246, 0.2) 100%);
  border: 1px solid rgba(139, 92, 246, 0.3);
  box-shadow: 0 4px 15px rgba(139, 92, 246, 0.2);
}

.nav-item.active::before {
  opacity: 0;
}

/* Glass Effect */
.glass-light {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

/* Custom Scrollbar */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(139, 92, 246, 0.5) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(139, 92, 246, 0.5);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(139, 92, 246, 0.7);
}

/* RTL Specific Styles */
.universal-sidebar.rtl {
  direction: rtl;
  text-align: right;
}

.universal-sidebar.rtl .nav-item {
  text-align: right;
  direction: rtl;
}

.universal-sidebar.rtl .sidebar-header {
  direction: rtl;
  text-align: right;
}

.universal-sidebar.rtl * {
  direction: rtl;
  text-align: right;
}

/* Risk Badges */
.risk-badge {
  font-size: 0.625rem;
  padding: 0.125rem 0.375rem;
  border-radius: 0.375rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.risk-badge.low {
  background: rgba(34, 197, 94, 0.2);
  color: rgb(34, 197, 94);
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.risk-badge.medium {
  background: rgba(251, 191, 36, 0.2);
  color: rgb(251, 191, 36);
  border: 1px solid rgba(251, 191, 36, 0.3);
}

.risk-badge.high {
  background: rgba(239, 68, 68, 0.2);
  color: rgb(239, 68, 68);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.risk-badge.critical {
  background: rgba(220, 38, 127, 0.2);
  color: rgb(220, 38, 127);
  border: 1px solid rgba(220, 38, 127, 0.3);
  animation: pulse 2s infinite;
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Mobile Specific */
@media (max-width: 1024px) {
  .universal-sidebar {
    width: 288px !important; /* w-72 */
  }
}

/* Collapsed State */
.universal-sidebar.collapsed {
  width: 64px !important; /* w-16 */
}

.universal-sidebar.collapsed .nav-item span {
  display: none;
}

.universal-sidebar.collapsed .sidebar-header h1,
.universal-sidebar.collapsed .sidebar-header p {
  display: none;
}

/* Focus States for Accessibility */
.nav-item:focus,
.glass-light:focus {
  outline: 2px solid rgba(139, 92, 246, 0.5);
  outline-offset: 2px;
}

/* Smooth Transitions */
.universal-sidebar * {
  transition: all 0.3s ease;
}

/* Arabic Font Support */
.universal-sidebar.rtl,
.universal-sidebar.rtl * {
  font-family: 'Cairo', 'Amiri', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
  font-weight: 500;
}

/* Better spacing for Arabic text */
.universal-sidebar.rtl .nav-item span {
  letter-spacing: 0.025em;
  line-height: 1.6;
  font-size: 14px;
  font-weight: 500;
}

.universal-sidebar.rtl h1,
.universal-sidebar.rtl h2,
.universal-sidebar.rtl h3 {
  font-family: 'Cairo', 'Amiri', 'Noto Sans Arabic', sans-serif !important;
  font-weight: 600;
}

/* Improved hover states */
.nav-item:hover {
  transform: translateX(2px);
}

.universal-sidebar.rtl .nav-item:hover {
  transform: translateX(-2px);
}
