/**
 * Collaborative Template Editor
 * Real-time collaborative editing for business plan templates
 */

import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Users,
  MessageCircle,
  Share2,
  History,
  Lock,
  Unlock,
  Eye,
  Edit3,
  Save,
  UserPlus,
  Bell,
  Clock,
  CheckCircle,
  AlertCircle,
  GitBranch,
  Activity
} from 'lucide-react';
import { RTLText, RTLFlex } from '../common';

interface Collaborator {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: 'owner' | 'editor' | 'reviewer' | 'viewer';
  isOnline: boolean;
  lastSeen: string;
  currentSection?: string;
  permissions: {
    canEdit: boolean;
    canComment: boolean;
    canInvite: boolean;
    canApprove: boolean;
  };
}

interface Comment {
  id: string;
  author: Collaborator;
  content: string;
  sectionId: string;
  timestamp: string;
  type: 'comment' | 'suggestion' | 'question' | 'approval';
  isResolved: boolean;
  replies: Comment[];
}

interface TemplateVersion {
  id: string;
  version: string;
  author: Collaborator;
  timestamp: string;
  description: string;
  changes: string[];
  isCurrent: boolean;
}

interface CollaborativeTemplateEditorProps {
  templateId: string;
  currentUser: Collaborator;
  onSave: (data: any) => void;
  onInviteCollaborator: (email: string, role: string) => void;
}

const CollaborativeTemplateEditor: React.FC<CollaborativeTemplateEditorProps> = ({ templateId,
  currentUser,
  onSave,
  onInviteCollaborator
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [collaborators, setCollaborators] = useState<Collaborator[]>([]);
  const [comments, setComments] = useState<Comment[]>([]);
  const [versions, setVersions] = useState<TemplateVersion[]>([]);
  const [activeSection, setActiveSection] = useState<string>('');
  const [showComments, setShowComments] = useState(true);
  const [showCollaborators, setShowCollaborators] = useState(true);
  const [showVersions, setShowVersions] = useState(false);
  const [newComment, setNewComment] = useState('');
  const [selectedCommentType, setSelectedCommentType] = useState<'comment' | 'suggestion' | 'question' | 'approval'>('comment');
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState<'editor' | 'reviewer' | 'viewer'>('editor');
  const [showInviteModal, setShowInviteModal] = useState(false);

  // Load collaboration data from API
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadCollaborationData = async () => {
      try {
        // This would be replaced with actual collaborative API
        // For now, show empty state since collaboration requires backend implementation
        setCollaborators([]);
        setComments([]);
        setVersions([]);
      } catch (error) {
        console.error('Error loading collaboration data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadCollaborationData();
  }, [templateId]);

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'owner': return 'text-purple-400 bg-purple-600/20';
      case 'editor': return 'text-blue-400 bg-blue-600/20';
      case 'reviewer': return 'text-green-400 bg-green-600/20';
      case 'viewer': return 'text-gray-400 bg-gray-600/20';
      default: return 'text-gray-400 bg-gray-600/20';
    }
  };

  const getCommentTypeIcon = (type: string) => {
    switch (type) {
      case 'suggestion': return <Edit3 size={14} className="text-blue-400" />;
      case 'question': return <AlertCircle size={14} className="text-yellow-400" />;
      case 'approval': return <CheckCircle size={14} className="text-green-400" />;
      default: return <MessageCircle size={14} className="text-gray-400" />;
    }
  };

  const addComment = () => {
    if (!newComment.trim()) return;

    const comment: Comment = {
      id: Date.now().toString(),
      author: currentUser,
      content: newComment,
      sectionId: activeSection,
      timestamp: new Date().toISOString(),
      type: selectedCommentType,
      isResolved: false,
      replies: []
    };

    setComments(prev => [...prev, comment]);
    setNewComment('');
  };

  const inviteCollaborator = () => {
    if (!inviteEmail.trim()) return;

    onInviteCollaborator(inviteEmail, inviteRole);
    setInviteEmail('');
    setShowInviteModal(false);
  };

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return t('time.justNow');
    if (diffInMinutes < 60) return t('time.minutesAgo', { count: diffInMinutes });
    if (diffInMinutes < 1440) return t('time.hoursAgo', { count: Math.floor(diffInMinutes / 60) });
    return t('time.daysAgo', { count: Math.floor(diffInMinutes / 1440) });
  };

  return (
    <div className={`flex h-screen bg-gray-900 ${isRTL ? "flex-row-reverse" : ""}`}>
      {/* Main Editor Area */}
      <div className={`flex-1 flex flex-col ${isRTL ? "flex-row-reverse" : ""}`}>
        {/* Toolbar */}
        <div className="bg-gray-800 border-b border-gray-700 p-4">
          <RTLFlex className="items-center justify-between">
            <RTLFlex className="items-center space-x-4">
              <RTLText as="h2" className="text-lg font-semibold">
                {t('collaboration.templateEditor')}
              </RTLText>

              {/* Active Collaborators */}
              <RTLFlex className="items-center space-x-2">
                {collaborators.filter(c => c.isOnline).map(collaborator => (
                  <div
                    key={collaborator.id}
                    className="relative"
                    title={`${collaborator.name} - ${t(`roles.${collaborator.role}`)}`}
                  >
                    <div className={`w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-sm font-medium ${isRTL ? "flex-row-reverse" : ""}`}>
                      {collaborator.name.charAt(0)}
                    </div>
                    <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-gray-800" />
                  </div>
                ))}
              </RTLFlex>
            </RTLFlex>

            <RTLFlex className="items-center space-x-3">
              {/* Save Status */}
              <RTLFlex className="items-center text-sm text-gray-400">
                <CheckCircle size={16} className={`text-green-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                {t('collaboration.autoSaved')}
              </RTLFlex>

              {/* Action Buttons */}
              <button
                onClick={() => setShowCollaborators(!showCollaborators)}
                className={`p-2 rounded-lg transition-colors ${
                  showCollaborators ? 'bg-purple-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}
                }`}
              >
                <Users size={16} />
              </button>

              <button
                onClick={() => setShowComments(!showComments)}
                className={`p-2 rounded-lg transition-colors relative ${
                  showComments ? 'bg-purple-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}
                }`}
              >
                <MessageCircle size={16} />
                {comments.filter(c => !c.isResolved).length > 0 && (
                  <div className={`absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full text-xs flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    {comments.filter(c => !c.isResolved).length}
                  </div>
                )}
              </button>

              <button
                onClick={() => setShowVersions(!showVersions)}
                className={`p-2 rounded-lg transition-colors ${
                  showVersions ? 'bg-purple-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}
                }`}
              >
                <History size={16} />
              </button>

              <button
                onClick={() => setShowInviteModal(true)}
                className={`px-3 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg text-white transition-colors flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <UserPlus size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                {t('collaboration.invite')}
              </button>
            </RTLFlex>
          </RTLFlex>
        </div>

        {/* Editor Content */}
        <div className={`flex-1 p-6 overflow-y-auto ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-4xl mx-auto space-y-8">
            {/* Template sections would go here */}
            <div className="bg-gray-800/50 rounded-lg p-6">
              <RTLText as="h3" className="text-xl font-semibold mb-4">
                {t('sections.executiveSummary')}
              </RTLText>
              <div className="prose prose-invert max-w-none">
                <p className="text-gray-300 leading-relaxed">
                  {t('collaboration.sampleContent')}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Sidebar */}
      <div className={`w-80 bg-gray-800 border-l border-gray-700 flex flex-col ${isRTL ? "flex-row-reverse" : ""}`}>
        {/* Sidebar Tabs */}
        <div className={`flex border-b border-gray-700 ${isRTL ? "flex-row-reverse" : ""}`}>
          <button
            onClick={() => { setShowCollaborators(true); setShowComments(false); setShowVersions(false); }}
            className={`flex-1 p-3 text-sm font-medium transition-colors ${
              showCollaborators ? 'bg-purple-600 text-white' : 'text-gray-400 hover:text-white'}
            }`}
          >
            <Users size={16} className="mx-auto mb-1" />
            {t('collaboration.collaborators')}
          </button>
          <button
            onClick={() => { setShowComments(true); setShowCollaborators(false); setShowVersions(false); }}
            className={`flex-1 p-3 text-sm font-medium transition-colors relative ${
              showComments ? 'bg-purple-600 text-white' : 'text-gray-400 hover:text-white'}
            }`}
          >
            <MessageCircle size={16} className="mx-auto mb-1" />
            {t('collaboration.comments')}
            {comments.filter(c => !c.isResolved).length > 0 && (
              <div className={`absolute top-1 right-1 w-4 h-4 bg-red-500 rounded-full text-xs flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}>
                {comments.filter(c => !c.isResolved).length}
              </div>
            )}
          </button>
          <button
            onClick={() => { setShowVersions(true); setShowCollaborators(false); setShowComments(false); }}
            className={`flex-1 p-3 text-sm font-medium transition-colors ${
              showVersions ? 'bg-purple-600 text-white' : 'text-gray-400 hover:text-white'}
            }`}
          >
            <History size={16} className="mx-auto mb-1" />
            {t('collaboration.versions')}
          </button>
        </div>

        {/* Sidebar Content */}
        <div className={`flex-1 overflow-y-auto ${isRTL ? "flex-row-reverse" : ""}`}>
          {/* Collaborators Panel */}
          {showCollaborators && (
            <div className="p-4 space-y-4">
              <RTLText as="h4" className="font-semibold">
                {t('collaboration.teamMembers')}
              </RTLText>

              {collaborators.map(collaborator => (
                <div key={collaborator.id} className={`flex items-center space-x-3 p-3 bg-gray-700/50 rounded-lg ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className="relative">
                    <div className={`w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center font-medium ${isRTL ? "flex-row-reverse" : ""}`}>
                      {collaborator.name.charAt(0)}
                    </div>
                    {collaborator.isOnline && (
                      <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-gray-700" />
                    )}
                  </div>

                  <div className={`flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <RTLText className="font-medium">{collaborator.name}</RTLText>
                    <div className="text-sm text-gray-400">{collaborator.email}</div>
                    <RTLFlex className="items-center mt-1">
                      <span className={`px-2 py-1 rounded text-xs ${getRoleColor(collaborator.role)}`}>
                        {t(`roles.${collaborator.role}`)}
                      </span>
                      {collaborator.currentSection && (
                        <span className={`ml-2 text-xs text-gray-500 ${isRTL ? "space-x-reverse" : ""}`}>
                          {t('collaboration.editing')} {collaborator.currentSection}
                        </span>
                      )}
                    </RTLFlex>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Comments Panel */}
          {showComments && (
            <div className="p-4 space-y-4">
              <RTLText as="h4" className="font-semibold">
                {t('collaboration.comments')}
              </RTLText>

              {/* Add Comment */}
              <div className="space-y-3">
                <select
                  value={selectedCommentType}
                  onChange={(e) => setSelectedCommentType(e.target.value as any)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white text-sm"
                >
                  <option value="comment">{t('commentTypes.comment')}</option>
                  <option value="suggestion">{t('commentTypes.suggestion')}</option>
                  <option value="question">{t('commentTypes.question')}</option>
                  <option value="approval">{t('commentTypes.approval')}</option>
                </select>

                <textarea
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  placeholder={t('collaboration.addComment')}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white text-sm"
                  rows={3}
                />

                <button
                  onClick={addComment}
                  disabled={!newComment.trim()}
                  className="w-full px-3 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-700 disabled:text-gray-400 rounded-md text-white text-sm transition-colors"
                >
                  {t('collaboration.postComment')}
                </button>
              </div>

              {/* Comments List */}
              <div className="space-y-3">
                {comments.map(comment => (
                  <div key={comment.id} className="p-3 bg-gray-700/50 rounded-lg">
                    <RTLFlex className="items-start justify-between mb-2">
                      <RTLFlex className="items-center">
                        <div className={`w-6 h-6 bg-purple-600 rounded-full flex items-center justify-center text-xs font-medium mr-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                          {comment.author.name.charAt(0)}
                        </div>
                        <div>
                          <RTLText className="text-sm font-medium">{comment.author.name}</RTLText>
                          <div className="text-xs text-gray-400">{formatTimeAgo(comment.timestamp)}</div>
                        </div>
                      </RTLFlex>
                      {getCommentTypeIcon(comment.type)}
                    </RTLFlex>

                    <p className="text-sm text-gray-300 mb-2">{comment.content}</p>

                    <RTLFlex className="items-center justify-between">
                      <span className="text-xs text-gray-500">
                        {t('collaboration.section')}: {comment.sectionId}
                      </span>
                      {!comment.isResolved && (
                        <button className="text-xs text-purple-400 hover:text-purple-300">
                          {t('collaboration.resolve')}
                        </button>
                      )}
                    </RTLFlex>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Versions Panel */}
          {showVersions && (
            <div className="p-4 space-y-4">
              <RTLText as="h4" className="font-semibold">
                {t('collaboration.versionHistory')}
              </RTLText>

              {versions.map(version => (
                <div key={version.id} className={`p-3 rounded-lg border ${
                  version.isCurrent ? 'border-purple-500 bg-purple-600/10' : 'border-gray-600 bg-gray-700/50'}
                }`}>
                  <RTLFlex className="items-center justify-between mb-2">
                    <RTLFlex className="items-center">
                      <GitBranch size={16} className={`text-purple-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                      <RTLText className="font-medium">v{version.version}</RTLText>
                    </RTLFlex>
                    {version.isCurrent && (
                      <span className="px-2 py-1 bg-purple-600 text-white text-xs rounded">
                        {t('collaboration.current')}
                      </span>
                    )}
                  </RTLFlex>

                  <p className="text-sm text-gray-300 mb-2">{version.description}</p>

                  <div className="text-xs text-gray-400 mb-2">
                    {t('collaboration.by')} {version.author.name} • {formatTimeAgo(version.timestamp)}
                  </div>

                  <div className="space-y-1">
                    {version.changes.map((change, index) => (
                      <div key={index} className={`text-xs text-gray-500 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                        <Activity size={12} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                        {change}
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Invite Modal */}
      {showInviteModal && (
        <div className={`fixed inset-0 bg-black/50 flex items-center justify-center z-50 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="bg-gray-800 rounded-lg p-6 w-96">
            <RTLText as="h3" className="text-lg font-semibold mb-4">
              {t('collaboration.inviteCollaborator')}
            </RTLText>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  {t('collaboration.emailAddress')}
                </label>
                <input
                  type="email"
                  value={inviteEmail}
                  onChange={(e) => setInviteEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  {t('collaboration.role')}
                </label>
                <select
                  value={inviteRole}
                  onChange={(e) => setInviteRole(e.target.value as any)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
                >
                  <option value="editor">{t('roles.editor')}</option>
                  <option value="reviewer">{t('roles.reviewer')}</option>
                  <option value="viewer">{t('roles.viewer')}</option>
                </select>
              </div>
            </div>

            <RTLFlex className="justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowInviteModal(false)}
                className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
              >
                {t('common.cancel')}
              </button>
              <button
                onClick={inviteCollaborator}
                disabled={!inviteEmail.trim()}
                className="px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-700 disabled:text-gray-400 rounded-lg text-white transition-colors"
              >
                {t('collaboration.sendInvite')}
              </button>
            </RTLFlex>
          </div>
        </div>
      )}
    </div>
  );
};

export default CollaborativeTemplateEditor;
