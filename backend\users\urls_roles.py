from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views_moderator import ModeratorViewSet
from .views_mentor import MentorViewSet
from .views_investor import InvestorViewSet
from .views_super_admin import SuperAdminViewSet

# Create routers for each role
moderator_router = DefaultRouter()
moderator_router.register(r'moderator', ModeratorViewSet, basename='moderator')

mentor_router = DefaultRouter()
mentor_router.register(r'mentor', MentorViewSet, basename='mentor')

investor_router = DefaultRouter()
investor_router.register(r'investor', InvestorViewSet, basename='investor')

super_admin_router = DefaultRouter()
super_admin_router.register(r'super-admin', SuperAdminViewSet, basename='super-admin')

# Enhanced Super Admin endpoints
enhanced_super_admin_patterns = [
    # System Management
    'dashboard-stats/',           # GET - Enhanced dashboard statistics
    'system-health/',            # GET - Comprehensive system health
    'capabilities/',             # GET - Super Admin capabilities
    'database-schema/',          # GET - Database schema information
    'run-migration/',            # POST - Execute database migrations
    'environment-variables/',    # GET - Environment variables (filtered)
    'create-backup/',           # POST - Create system backup

    # User Management
    'bulk-user-operations/',     # POST - Bulk user operations
    'impersonate-user/',        # POST - User impersonation
    'stop-impersonation/',      # POST - Stop user impersonation
    'create-role/',             # POST - Create new user role
    'assign-role/',             # POST - Assign role to user
    'remove-role/',             # POST - Remove role from user

    # Security & Compliance
    'security-audit/',          # GET - Security audit report
    'system-logs/',             # GET - System logs with filtering
    'security-alerts/',         # GET - Security alerts and notifications

    # AI System Management
    'ai-configuration/',        # GET - AI system configuration
    'update-ai-config/',        # POST - Update AI configuration
    'test-ai-connection/',      # POST - Test AI connection
    'ai-usage-analytics/',      # GET - AI usage analytics

    # Analytics & Reporting
    'advanced-analytics/',      # GET - Advanced platform analytics
    'generate-report/',         # POST - Generate custom reports
    'export-data/',             # POST - Export platform data
]

urlpatterns = [
    # Role-based endpoints
    path('api/roles/', include(moderator_router.urls)),
    path('api/roles/', include(mentor_router.urls)),
    path('api/roles/', include(investor_router.urls)),

    # Super Admin endpoints (separate namespace for security)
    path('api/users/', include(super_admin_router.urls)),
    
    # Specific role endpoints
    path('api/roles/moderator/dashboard-stats/', 
         ModeratorViewSet.as_view({'get': 'dashboard_stats'}), 
         name='moderator-dashboard-stats'),
    path('api/roles/moderator/recent-reports/', 
         ModeratorViewSet.as_view({'get': 'recent_reports'}), 
         name='moderator-recent-reports'),
    path('api/roles/moderator/flagged-content/', 
         ModeratorViewSet.as_view({'get': 'flagged_content'}), 
         name='moderator-flagged-content'),
    path('api/roles/moderator/approve-content/<int:pk>/', 
         ModeratorViewSet.as_view({'post': 'approve_content'}), 
         name='moderator-approve-content'),
    path('api/roles/moderator/reject-content/<int:pk>/', 
         ModeratorViewSet.as_view({'post': 'reject_content'}), 
         name='moderator-reject-content'),
    path('api/roles/moderator/user-management/', 
         ModeratorViewSet.as_view({'get': 'user_management'}), 
         name='moderator-user-management'),
    path('api/roles/moderator/analytics/', 
         ModeratorViewSet.as_view({'get': 'analytics'}), 
         name='moderator-analytics'),
    
    # Mentor endpoints
    path('api/roles/mentor/dashboard-stats/', 
         MentorViewSet.as_view({'get': 'dashboard_stats'}), 
         name='mentor-dashboard-stats'),
    path('api/roles/mentor/my-mentees/', 
         MentorViewSet.as_view({'get': 'my_mentees'}), 
         name='mentor-my-mentees'),
    path('api/roles/mentor/upcoming-sessions/', 
         MentorViewSet.as_view({'get': 'upcoming_sessions'}), 
         name='mentor-upcoming-sessions'),
    path('api/roles/mentor/session-history/', 
         MentorViewSet.as_view({'get': 'session_history'}), 
         name='mentor-session-history'),
    path('api/roles/mentor/analytics/', 
         MentorViewSet.as_view({'get': 'analytics'}), 
         name='mentor-analytics'),
    path('api/roles/mentor/update-availability/', 
         MentorViewSet.as_view({'post': 'update_availability'}), 
         name='mentor-update-availability'),
    
    # Investor endpoints
    path('api/roles/investor/dashboard-stats/', 
         InvestorViewSet.as_view({'get': 'dashboard_stats'}), 
         name='investor-dashboard-stats'),
    path('api/roles/investor/investment-opportunities/', 
         InvestorViewSet.as_view({'get': 'investment_opportunities'}), 
         name='investor-investment-opportunities'),
    path('api/roles/investor/my-portfolio/', 
         InvestorViewSet.as_view({'get': 'my_portfolio'}), 
         name='investor-my-portfolio'),
    path('api/roles/investor/market-analysis/', 
         InvestorViewSet.as_view({'get': 'market_analysis'}), 
         name='investor-market-analysis'),
    path('api/roles/investor/analytics/', 
         InvestorViewSet.as_view({'get': 'analytics'}), 
         name='investor-analytics'),
]
