# Generated by Django 5.1.7 on 2025-05-15 13:08

import api.storage
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('api', '0005_membershipapplication'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BusinessIdea',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('slug', models.SlugField(blank=True, max_length=250, unique=True)),
                ('description', models.TextField()),
                ('problem_statement', models.TextField(help_text='What problem does your business idea solve?')),
                ('solution_description', models.TextField(help_text='How does your idea solve the problem?')),
                ('target_audience', models.TextField(help_text='Who is your target audience?')),
                ('market_opportunity', models.TextField(blank=True, help_text='What is the market opportunity?', null=True)),
                ('business_model', models.TextField(blank=True, help_text='How will your business make money?', null=True)),
                ('current_stage', models.CharField(choices=[('concept', 'Concept Stage'), ('validation', 'Validation Stage'), ('development', 'Development Stage'), ('scaling', 'Scaling Stage'), ('established', 'Established Business')], default='concept', max_length=20)),
                ('image', models.ImageField(blank=True, null=True, storage=api.storage.OptimizedImageStorage(), upload_to='business_idea_images/')),
                ('moderation_status', models.CharField(choices=[('approved', 'Approved'), ('pending', 'Pending Review'), ('rejected', 'Rejected')], default='pending', max_length=20)),
                ('moderation_comment', models.TextField(blank=True, null=True)),
                ('moderated_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('collaborators', models.ManyToManyField(blank=True, related_name='collaborated_ideas', to=settings.AUTH_USER_MODEL)),
                ('moderated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='moderated_ideas', to=settings.AUTH_USER_MODEL)),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='business_ideas', to=settings.AUTH_USER_MODEL)),
                ('tags', models.ManyToManyField(blank=True, related_name='business_ideas', to='api.tag')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='IncubatorResource',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('resource_type', models.CharField(choices=[('article', 'Article'), ('video', 'Video'), ('template', 'Template'), ('tool', 'Tool'), ('course', 'Course'), ('ebook', 'E-Book'), ('other', 'Other')], max_length=20)),
                ('category', models.CharField(choices=[('ideation', 'Business Ideation'), ('validation', 'Market Validation'), ('planning', 'Business Planning'), ('finance', 'Financial Planning'), ('marketing', 'Marketing'), ('legal', 'Legal & Compliance'), ('operations', 'Operations'), ('technology', 'Technology'), ('growth', 'Growth & Scaling'), ('other', 'Other')], max_length=20)),
                ('url', models.URLField()),
                ('image', models.ImageField(blank=True, null=True, storage=api.storage.OptimizedImageStorage(), upload_to='incubator_resource_images/')),
                ('file', models.FileField(blank=True, null=True, upload_to='incubator_resource_files/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='incubator_resources', to=settings.AUTH_USER_MODEL)),
                ('tags', models.ManyToManyField(blank=True, related_name='incubator_resources', to='api.tag')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='MentorshipApplication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('goals', models.TextField(help_text='What do you hope to achieve with mentorship?')),
                ('specific_areas', models.TextField(help_text='What specific areas do you need help with?')),
                ('commitment', models.TextField(help_text='How much time can you commit to working with a mentor?')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected')], default='pending', max_length=20)),
                ('admin_notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('applicant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='mentorship_applications', to=settings.AUTH_USER_MODEL)),
                ('business_idea', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='mentorship_applications', to='incubator.businessidea')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProgressUpdate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('achievements', models.TextField(help_text='What milestones have you achieved?')),
                ('challenges', models.TextField(blank=True, help_text='What challenges are you facing?', null=True)),
                ('next_steps', models.TextField(help_text='What are your next steps?')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('business_idea', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='progress_updates', to='incubator.businessidea')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='progress_updates', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
