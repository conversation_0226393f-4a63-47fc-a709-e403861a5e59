/**
 * Theme Utilities - Compatibility Layer
 * 
 * This file provides compatibility stubs for theme-related utilities
 * while we transition to glass morphism design system.
 */

/**
 * Get text class for glass morphism design
 * @param type - The text type
 * @returns Glass morphism text class
 */
export const getTextClass = (type: 'primary' | 'secondary' | 'accent' | 'muted') => {
  switch (type) {
    case 'primary':
      return 'text-glass-primary';
    case 'secondary':
      return 'text-glass-secondary';
    case 'accent':
      return 'text-glass-accent';
    case 'muted':
      return 'text-glass-muted';
    default:
      return 'text-glass-primary';
  }
};

/**
 * Get input class for glass morphism design
 * @returns Glass morphism input class
 */
export const getInputClass = () => {
  return 'glass-light border border-glass-border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200';
};

/**
 * Get button class for glass morphism design
 * @param variant - Button variant
 * @param size - Button size
 * @param fullWidth - Whether button should be full width
 * @returns Glass morphism button class
 */
export const getButtonClass = (
  variant: 'primary' | 'secondary' | 'outline' | 'danger' | 'success' | 'warning' = 'primary',
  size: 'sm' | 'md' | 'lg' = 'md',
  fullWidth: boolean = false
) => {
  // Base classes
  const baseClass = 'inline-flex items-center justify-center font-medium transition-all duration-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500';

  // Size classes
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg',
  };

  // Variant classes with glass morphism
  const variantClasses = {
    primary: 'glass-morphism text-glass-primary hover:bg-glass-hover active:bg-glass-active border border-glass-border',
    secondary: 'glass-light text-glass-secondary hover:bg-glass-hover border border-glass-border',
    outline: 'border border-glass-border text-glass-primary hover:bg-glass-hover',
    danger: 'glass-morphism text-white bg-red-600/20 hover:bg-red-600/30 border border-red-500/30',
    success: 'glass-morphism text-white bg-green-600/20 hover:bg-green-600/30 border border-green-500/30',
    warning: 'glass-morphism text-white bg-amber-600/20 hover:bg-amber-600/30 border border-amber-500/30'
  };

  // Full width class
  const widthClass = fullWidth ? 'w-full' : '';

  return `${baseClass} ${sizeClasses[size]} ${variantClasses[variant]} ${widthClass}`.trim().replace(/\s+/g, ' ');
};

/**
 * Get card class for glass morphism design
 * @returns Glass morphism card class
 */
export const getCardClass = () => {
  return 'glass-morphism border border-glass-border rounded-lg p-6 backdrop-blur-sm';
};

/**
 * Get background class for glass morphism design
 * @param variant - Background variant
 * @returns Glass morphism background class
 */
export const getBackgroundClass = (variant: 'light' | 'dark' | 'feature' = 'light') => {
  switch (variant) {
    case 'light':
      return 'glass-light';
    case 'dark':
      return 'glass-morphism';
    case 'feature':
      return 'glass-feature';
    default:
      return 'glass-light';
  }
};
