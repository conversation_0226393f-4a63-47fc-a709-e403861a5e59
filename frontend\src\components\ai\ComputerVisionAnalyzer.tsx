/**
 * Computer Vision Analyzer
 * Advanced image analysis for documents, pitch decks, logos, and charts
 */

import React, { useState, useCallback } from 'react';
import {
  Upload,
  FileText,
  Image,
  Bar<PERSON>hart3,
  <PERSON>lette,
  Eye,
  Zap,
  CheckCircle,
  AlertCircle,
  Download,
  Trash2
} from 'lucide-react';
import { useTranslation } from 'react-i18next';


interface ComputerVisionProps {
  onAnalysisComplete?: (results: any) => void;
  className?: string;
}

interface AnalysisResult {
  document_type?: string;
  extracted_text?: string;
  text_confidence?: number;
  structure_analysis?: any;
  business_information?: any;
  key_insights?: string[];
  recommendations?: string[];
  total_slides?: number;
  slides_analysis?: any[];
  overall_assessment?: any;
  strengths?: string[];
  improvement_areas?: string[];
  color_analysis?: any;
  design_analysis?: any;
  brand_perception?: any;
  chart_type?: string;
  extracted_numbers?: number[];
  insights?: string[];
}

export const ComputerVisionAnalyzer: React.FC<ComputerVisionProps> = ({
  onAnalysisComplete,
  className = ''
}) => {
  const { t } = useTranslation();
  const [analysisType, setAnalysisType] = useState<'document_analysis' | 'pitch_deck_analysis' | 'logo_analysis' | 'chart_analysis'>('document_analysis');
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [results, setResults] = useState<AnalysisResult | null>(null);
  const [dragActive, setDragActive] = useState(false);

  const analysisTypes = [
    {
      id: 'document_analysis',
      label: 'Document Analysis',
      description: 'Extract text and analyze business documents',
      icon: FileText,
      color: 'text-blue-600',
      accepts: 'image/*,.pdf'
    },
    {
      id: 'pitch_deck_analysis',
      label: 'Pitch Deck Analysis',
      description: 'Analyze presentation slides and design quality',
      icon: BarChart3,
      color: 'text-green-600',
      accepts: 'image/*'
    },
    {
      id: 'logo_analysis',
      label: 'Logo & Brand Analysis',
      description: 'Analyze logo design and brand perception',
      icon: Palette,
      color: 'text-purple-600',
      accepts: 'image/*'
    },
    {
      id: 'chart_analysis',
      label: 'Chart Data Extraction',
      description: 'Extract data from charts and graphs',
      icon: BarChart3,
      color: 'text-orange-600',
      accepts: 'image/*'
    }
  ];

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    const files = Array.from(e.dataTransfer.files);
    const imageFiles = files.filter(file => file.type.startsWith('image/'));
    
    if (analysisType === 'pitch_deck_analysis') {
      setUploadedFiles(imageFiles);
    } else {
      setUploadedFiles(imageFiles.slice(0, 1)); // Single file for other types
    }
  }, [analysisType]);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    
    if (analysisType === 'pitch_deck_analysis') {
      setUploadedFiles(files);
    } else {
      setUploadedFiles(files.slice(0, 1));
    }
  };

  const removeFile = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const runAnalysis = async () => {
    if (uploadedFiles.length === 0) return;

    setIsAnalyzing(true);
    try {
      const formData = new FormData();
      formData.append('analysis_type', analysisType);
      
      if (analysisType === 'pitch_deck_analysis') {
        uploadedFiles.forEach((file, index) => {
          formData.append('images', file);
        });
      } else {
        formData.append('image', uploadedFiles[0]);
      }

      if (analysisType === 'document_analysis') {
        formData.append('document_type', 'business_plan');
      }

      const response = await fetch('/api/ai/computer-vision/', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error('Analysis failed');
      }

      const data = await response.json();
      setResults(data.result);
      onAnalysisComplete?.(data.result);
    } catch (error) {
      console.error('Computer vision analysis error:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const renderResults = () => {
    if (!results) return null;

    return (
      <div className="mt-6 space-y-6">
        {/* Document Analysis Results */}
        {analysisType === 'document_analysis' && (
          <div className="space-y-4">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Document Analysis Results
              </h3>
              
              {results.extracted_text && (
                <div className="mb-4">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Extracted Text</h4>
                  <div className="bg-gray-50 dark:bg-gray-700 rounded p-3 text-sm text-gray-700 dark:text-gray-300 max-h-40 overflow-y-auto">
                    {results.extracted_text}
                  </div>
                  <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                    Confidence: {((results.text_confidence || 0) * 100).toFixed(1)}%
                  </div>
                </div>
              )}

              {results.business_information && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white mb-2">Business Information</h4>
                    <ul className="space-y-1 text-sm text-gray-600 dark:text-gray-400">
                      <li><strong>Company:</strong> {results.business_information.company_name}</li>
                      <li><strong>Market Size:</strong> {results.business_information.market_size}</li>
                      <li><strong>Revenue Figures:</strong> {results.business_information.revenue_figures?.join(', ') || 'None detected'}</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white mb-2">Key Insights</h4>
                    <ul className="space-y-1">
                      {results.key_insights?.map((insight, index) => (
                        <li key={index} className="text-sm text-gray-600 dark:text-gray-400 flex items-start">
                          <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                          {insight}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Pitch Deck Analysis Results */}
        {analysisType === 'pitch_deck_analysis' && (
          <div className="space-y-4">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Pitch Deck Analysis Results
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{results.total_slides || 0}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Total Slides</div>
                </div>
                
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {results.overall_assessment?.score ? (results.overall_assessment.score * 100).toFixed(0) + '%' : 'N/A'}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Overall Score</div>
                </div>
                
                <div className="text-center">
                  <div className="text-xl font-semibold text-purple-600">
                    {results.overall_assessment?.level || 'N/A'}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Quality Level</div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2 flex items-center">
                    <CheckCircle className="w-5 h-5 text-green-500 mr-2" />
                    Strengths
                  </h4>
                  <ul className="space-y-1">
                    {results.strengths?.map((strength, index) => (
                      <li key={index} className="text-sm text-gray-600 dark:text-gray-400">
                        • {strength}
                      </li>
                    ))}
                  </ul>
                </div>
                
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2 flex items-center">
                    <AlertCircle className="w-5 h-5 text-orange-500 mr-2" />
                    Improvement Areas
                  </h4>
                  <ul className="space-y-1">
                    {results.improvement_areas?.map((area, index) => (
                      <li key={index} className="text-sm text-gray-600 dark:text-gray-400">
                        • {area}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Logo Analysis Results */}
        {analysisType === 'logo_analysis' && (
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Logo & Brand Analysis Results
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">Color Analysis</h4>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  <p>Harmony: {results.color_analysis?.color_harmony || 'Unknown'}</p>
                  <p>Perception: {results.color_analysis?.brand_perception || 'Unknown'}</p>
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">Design Quality</h4>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  <p>Complexity: {results.design_analysis?.complexity || 'Unknown'}</p>
                  <p>Balance: {results.design_analysis?.balance || 'Unknown'}</p>
                  <p>Scalability: {results.design_analysis?.scalability || 'Unknown'}</p>
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">Brand Perception</h4>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  <p>Impact: {results.brand_perception?.emotional_impact || 'Unknown'}</p>
                  <p>Memorability: {results.brand_perception?.memorability || 'Unknown'}</p>
                  <p>Professional: {results.brand_perception?.professionalism || 'Unknown'}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Chart Analysis Results */}
        {analysisType === 'chart_analysis' && (
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Chart Data Extraction Results
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">Chart Information</h4>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  <p><strong>Type:</strong> {results.chart_type || 'Unknown'}</p>
                  <p><strong>Numbers Found:</strong> {results.extracted_numbers?.length || 0}</p>
                </div>
                
                {results.extracted_numbers && results.extracted_numbers.length > 0 && (
                  <div className="mt-3">
                    <h5 className="font-medium text-gray-900 dark:text-white mb-1">Extracted Values</h5>
                    <div className="bg-gray-50 dark:bg-gray-700 rounded p-2 text-sm">
                      {results.extracted_numbers.join(', ')}
                    </div>
                  </div>
                )}
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">Insights</h4>
                <ul className="space-y-1">
                  {results.insights?.map((insight, index) => (
                    <li key={index} className="text-sm text-gray-600 dark:text-gray-400 flex items-start">
                      <Eye className="w-4 h-4 text-blue-500 mt-0.5 mr-2 flex-shrink-0" />
                      {insight}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        )}

        {/* Recommendations */}
        {results.recommendations && results.recommendations.length > 0 && (
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Recommendations
            </h3>
            <ul className="space-y-2">
              {results.recommendations.map((rec, index) => (
                <li key={index} className="text-sm text-gray-600 dark:text-gray-400 flex items-start">
                  <Zap className="w-4 h-4 text-yellow-500 mt-0.5 mr-2 flex-shrink-0" />
                  {rec}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={`computer-vision-analyzer glass-light ${className}`}>
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center space-x-3 mb-4">
            <Eye className="w-8 h-8 text-blue-600" />
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                Computer Vision Analyzer
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                AI-powered image and document analysis
              </p>
            </div>
          </div>

          {/* Analysis Type Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {analysisTypes.map((type) => {
              const Icon = type.icon;
              return (
                <button
                  key={type.id}
                  onClick={() => {
                    setAnalysisType(type.id as any);
                    setUploadedFiles([]);
                    setResults(null);
                  }}
                  className={`p-4 rounded-lg border-2 transition-colors ${
                    analysisType === type.id
                      ? 'border-blue-500/50 glass-morphism'
                      : 'border-glass-border glass-light hover:bg-glass-hover hover:border-glass-border'
                  }`}
                >
                  <Icon className={`w-6 h-6 ${type.color} mb-2`} />
                  <h3 className="font-medium text-glass-primary text-sm">
                    {type.label}
                  </h3>
                  <p className="text-xs text-glass-secondary mt-1">
                    {type.description}
                  </p>
                </button>
              );
            })}
          </div>
        </div>

        {/* File Upload Area */}
        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
            dragActive
              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
              : 'border-gray-300 dark:border-gray-600'
          }`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Upload {analysisType === 'pitch_deck_analysis' ? 'Images' : 'Image'}
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {analysisType === 'pitch_deck_analysis'
              ? 'Drag and drop multiple slide images here, or click to select'
              : 'Drag and drop an image here, or click to select'}
          </p>
          
          <input
            type="file"
            multiple={analysisType === 'pitch_deck_analysis'}
            accept={analysisTypes.find(t => t.id === analysisType)?.accepts}
            onChange={handleFileSelect}
            className="hidden"
            id="file-upload"
          />
          <label
            htmlFor="file-upload"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 cursor-pointer"
          >
            <Upload className="w-4 h-4 mr-2" />
            Select Files
          </label>
        </div>

        {/* Uploaded Files */}
        {uploadedFiles.length > 0 && (
          <div className="mt-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
              Uploaded Files ({uploadedFiles.length})
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {uploadedFiles.map((file, index) => (
                <div key={index} className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Image className="w-8 h-8 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {file.name}
                      </p>
                      <p className="text-xs text-gray-600 dark:text-gray-400">
                        {(file.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => removeFile(index)}
                    className="text-red-500 hover:text-red-700"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              ))}
            </div>
            
            <div className="mt-4 flex justify-center">
              <button
                onClick={runAnalysis}
                disabled={isAnalyzing}
                className="flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                <Eye className={`w-5 h-5 ${isAnalyzing ? 'animate-pulse' : ''}`} />
                <span>{isAnalyzing ? 'Analyzing...' : 'Analyze Images'}</span>
              </button>
            </div>
          </div>
        )}

        {/* Results */}
        {renderResults()}
      </div>
  );
};
