"""
Predefined business plan template definitions for different business types
"""

# Standard Business Plan Template
STANDARD_TEMPLATE = {
    "name": "Standard Business Plan",
    "description": "Comprehensive business plan template suitable for most businesses",
    "template_type": "standard",
    "sections": {
        "executive_summary": {
            "title": "Executive Summary",
            "description": "A concise overview of your business",
            "section_type": "text",
            "order": 1,
            "required": True,
            "ai_prompt": "Create an executive summary for a {industry} business that {business_description}",
            "guiding_questions": [
                "What is your business concept?",
                "What are your key success factors?",
                "What are your financial projections?",
                "What funding do you need?"
            ]
        },
        "company_description": {
            "title": "Company Description",
            "description": "Detailed description of your company",
            "section_type": "text",
            "order": 2,
            "required": True,
            "ai_prompt": "Write a detailed company description for a {industry} business",
            "guiding_questions": [
                "What does your company do?",
                "What makes your company unique?",
                "What is your company's history?",
                "What are your company's goals?"
            ]
        },
        "market_analysis": {
            "title": "Market Analysis",
            "description": "Analysis of your target market and industry",
            "section_type": "market_sizing",
            "order": 3,
            "required": True,
            "ai_prompt": "Analyze the market for {industry} businesses targeting {target_market}",
            "guiding_questions": [
                "Who is your target market?",
                "What is the market size?",
                "What are the market trends?",
                "Who are your competitors?"
            ]
        },
        "competitive_analysis": {
            "title": "Competitive Analysis",
            "description": "Analysis of your competition",
            "section_type": "competitive_matrix",
            "order": 4,
            "required": True,
            "ai_prompt": "Create a competitive analysis for a {industry} business",
            "guiding_questions": [
                "Who are your main competitors?",
                "What are their strengths and weaknesses?",
                "How do you differentiate from them?",
                "What is your competitive advantage?"
            ]
        },
        "products_services": {
            "title": "Products & Services",
            "description": "Description of your products or services",
            "section_type": "text",
            "order": 5,
            "required": True,
            "ai_prompt": "Describe the products and services for a {industry} business",
            "guiding_questions": [
                "What products/services do you offer?",
                "What are the benefits to customers?",
                "How do you price your offerings?",
                "What is your product development plan?"
            ]
        },
        "marketing_strategy": {
            "title": "Marketing & Sales Strategy",
            "description": "Your marketing and sales approach",
            "section_type": "marketing_mix",
            "order": 6,
            "required": True,
            "ai_prompt": "Create a marketing strategy for a {industry} business targeting {target_market}",
            "guiding_questions": [
                "How will you reach your customers?",
                "What is your sales process?",
                "What marketing channels will you use?",
                "What is your customer acquisition cost?"
            ]
        },
        "operations_plan": {
            "title": "Operations Plan",
            "description": "How your business will operate day-to-day",
            "section_type": "process_flow",
            "order": 7,
            "required": True,
            "ai_prompt": "Create an operations plan for a {industry} business",
            "guiding_questions": [
                "How will you deliver your products/services?",
                "What are your key operational processes?",
                "What resources do you need?",
                "How will you ensure quality?"
            ]
        },
        "management_team": {
            "title": "Management Team",
            "description": "Your leadership team and organizational structure",
            "section_type": "team_structure",
            "order": 8,
            "required": True,
            "ai_prompt": "Describe the management team structure for a {industry} business",
            "guiding_questions": [
                "Who are your key team members?",
                "What are their roles and responsibilities?",
                "What experience do they bring?",
                "What gaps need to be filled?"
            ]
        },
        "financial_projections": {
            "title": "Financial Projections",
            "description": "Financial forecasts and projections",
            "section_type": "financial_forecast",
            "order": 9,
            "required": True,
            "ai_prompt": "Create financial projections for a {industry} business",
            "guiding_questions": [
                "What are your revenue projections?",
                "What are your major expenses?",
                "When will you break even?",
                "What are your cash flow needs?"
            ]
        },
        "funding_request": {
            "title": "Funding Request",
            "description": "Your funding needs and how funds will be used",
            "section_type": "text",
            "order": 10,
            "required": False,
            "ai_prompt": "Create a funding request for a {industry} business needing {funding_amount}",
            "guiding_questions": [
                "How much funding do you need?",
                "How will you use the funds?",
                "What type of funding are you seeking?",
                "What returns can investors expect?"
            ]
        }
    }
}

# Lean Canvas Template
LEAN_CANVAS_TEMPLATE = {
    "name": "Lean Canvas",
    "description": "One-page business model template for startups",
    "template_type": "lean",
    "sections": {
        "problem": {
            "title": "Problem",
            "description": "Top 1-3 problems you're solving",
            "section_type": "text",
            "order": 1,
            "required": True,
            "ai_prompt": "Identify the key problems for {target_market} in the {industry} industry",
            "guiding_questions": [
                "What are the top 3 problems you're solving?",
                "How do people solve these problems today?",
                "Why is this problem worth solving?"
            ]
        },
        "solution": {
            "title": "Solution",
            "description": "Top 3 features that solve the problems",
            "section_type": "text",
            "order": 2,
            "required": True,
            "ai_prompt": "Describe solutions for {problem} in the {industry} industry",
            "guiding_questions": [
                "What are your top 3 features?",
                "How do these features solve the problems?",
                "What makes your solution unique?"
            ]
        },
        "unique_value_proposition": {
            "title": "Unique Value Proposition",
            "description": "Single, clear compelling message",
            "section_type": "value_proposition",
            "order": 3,
            "required": True,
            "ai_prompt": "Create a unique value proposition for a {industry} business",
            "guiding_questions": [
                "What makes you different?",
                "Why should customers choose you?",
                "What's your compelling message?"
            ]
        },
        "unfair_advantage": {
            "title": "Unfair Advantage",
            "description": "Something that can't be easily copied",
            "section_type": "text",
            "order": 4,
            "required": True,
            "ai_prompt": "Identify unfair advantages for a {industry} business",
            "guiding_questions": [
                "What can't be easily copied?",
                "What special knowledge do you have?",
                "What unique resources do you possess?"
            ]
        },
        "customer_segments": {
            "title": "Customer Segments",
            "description": "Target customers and users",
            "section_type": "persona",
            "order": 5,
            "required": True,
            "ai_prompt": "Define customer segments for a {industry} business",
            "guiding_questions": [
                "Who are your target customers?",
                "What are their characteristics?",
                "How do you reach them?"
            ]
        },
        "key_metrics": {
            "title": "Key Metrics",
            "description": "Key numbers that tell you how your business is doing",
            "section_type": "kpi_dashboard",
            "order": 6,
            "required": True,
            "ai_prompt": "Define key metrics for a {industry} business",
            "guiding_questions": [
                "What metrics matter most?",
                "How will you measure success?",
                "What are your targets?"
            ]
        },
        "channels": {
            "title": "Channels",
            "description": "Path to customers",
            "section_type": "text",
            "order": 7,
            "required": True,
            "ai_prompt": "Identify distribution channels for a {industry} business",
            "guiding_questions": [
                "How do you reach customers?",
                "What channels work best?",
                "What's your customer acquisition strategy?"
            ]
        },
        "cost_structure": {
            "title": "Cost Structure",
            "description": "Customer acquisition costs, distribution costs, hosting, people, etc.",
            "section_type": "financial",
            "order": 8,
            "required": True,
            "ai_prompt": "Outline cost structure for a {industry} business",
            "guiding_questions": [
                "What are your major costs?",
                "What drives your costs?",
                "How can you optimize costs?"
            ]
        },
        "revenue_streams": {
            "title": "Revenue Streams",
            "description": "How you make money",
            "section_type": "revenue_model",
            "order": 9,
            "required": True,
            "ai_prompt": "Define revenue streams for a {industry} business",
            "guiding_questions": [
                "How do you make money?",
                "What are your revenue sources?",
                "What's your pricing model?"
            ]
        }
    }
}

# SaaS Template
SAAS_TEMPLATE = {
    "name": "SaaS Business Plan",
    "description": "Specialized template for Software as a Service businesses",
    "template_type": "saas",
    "sections": {
        "executive_summary": {
            "title": "Executive Summary",
            "description": "Overview of your SaaS business",
            "section_type": "text",
            "order": 1,
            "required": True,
            "ai_prompt": "Create an executive summary for a SaaS business in {industry}",
            "guiding_questions": [
                "What problem does your software solve?",
                "Who is your target market?",
                "What's your business model?",
                "What are your growth projections?"
            ]
        },
        "product_overview": {
            "title": "Product Overview",
            "description": "Detailed description of your software product",
            "section_type": "text",
            "order": 2,
            "required": True,
            "ai_prompt": "Describe a SaaS product for {industry} that solves {problem}",
            "guiding_questions": [
                "What does your software do?",
                "What are the key features?",
                "How does it solve customer problems?",
                "What's your technology stack?"
            ]
        },
        "technology_stack": {
            "title": "Technology Stack",
            "description": "Technical architecture and infrastructure",
            "section_type": "technology_stack",
            "order": 3,
            "required": True,
            "ai_prompt": "Define technology stack for a {industry} SaaS application",
            "guiding_questions": [
                "What technologies will you use?",
                "How will you ensure scalability?",
                "What about security and compliance?",
                "What's your development approach?"
            ]
        },
        "user_personas": {
            "title": "User Personas",
            "description": "Detailed profiles of your target users",
            "section_type": "persona",
            "order": 4,
            "required": True,
            "ai_prompt": "Create user personas for a {industry} SaaS product",
            "guiding_questions": [
                "Who are your primary users?",
                "What are their pain points?",
                "How do they currently solve problems?",
                "What motivates them to buy?"
            ]
        },
        "feature_roadmap": {
            "title": "Product Roadmap",
            "description": "Development timeline and feature priorities",
            "section_type": "feature_roadmap",
            "order": 5,
            "required": True,
            "ai_prompt": "Create a product roadmap for a {industry} SaaS application",
            "guiding_questions": [
                "What features will you build first?",
                "What's your MVP?",
                "How will you prioritize features?",
                "What's your release schedule?"
            ]
        },
        "saas_metrics": {
            "title": "SaaS Metrics",
            "description": "Key performance indicators for SaaS businesses",
            "section_type": "kpi_dashboard",
            "order": 6,
            "required": True,
            "ai_prompt": "Define SaaS metrics for a {industry} application",
            "guiding_questions": [
                "What's your target MRR/ARR?",
                "What's your churn rate goal?",
                "What's your customer acquisition cost?",
                "What's your lifetime value?"
            ]
        },
        "pricing_strategy": {
            "title": "Pricing Strategy",
            "description": "Pricing model and tiers",
            "section_type": "pricing_strategy",
            "order": 7,
            "required": True,
            "ai_prompt": "Create a pricing strategy for a {industry} SaaS product",
            "guiding_questions": [
                "What's your pricing model?",
                "How many tiers will you have?",
                "How do you justify your pricing?",
                "What's your freemium strategy?"
            ]
        },
        "go_to_market": {
            "title": "Go-to-Market Strategy",
            "description": "How you'll launch and grow your SaaS",
            "section_type": "marketing_mix",
            "order": 8,
            "required": True,
            "ai_prompt": "Create a go-to-market strategy for a {industry} SaaS product",
            "guiding_questions": [
                "How will you acquire customers?",
                "What channels will you use?",
                "What's your content strategy?",
                "How will you scale growth?"
            ]
        }
    }
}

# E-commerce Template
ECOMMERCE_TEMPLATE = {
    "name": "E-commerce Business Plan",
    "description": "Comprehensive template for online retail businesses",
    "template_type": "ecommerce",
    "sections": {
        "executive_summary": {
            "title": "Executive Summary",
            "description": "Overview of your e-commerce business",
            "section_type": "text",
            "order": 1,
            "required": True,
            "ai_prompt": "Create an executive summary for an e-commerce business selling {products}",
            "guiding_questions": [
                "What products will you sell?",
                "Who is your target market?",
                "What's your competitive advantage?",
                "What are your revenue projections?"
            ]
        },
        "product_catalog": {
            "title": "Product Catalog",
            "description": "Detailed description of your product offerings",
            "section_type": "table",
            "order": 2,
            "required": True,
            "ai_prompt": "Create a product catalog for an e-commerce business in {industry}",
            "guiding_questions": [
                "What products will you offer?",
                "How will you source products?",
                "What's your pricing strategy?",
                "How will you manage inventory?"
            ]
        },
        "target_market": {
            "title": "Target Market Analysis",
            "description": "Analysis of your online customer base",
            "section_type": "persona",
            "order": 3,
            "required": True,
            "ai_prompt": "Analyze the target market for {products} e-commerce",
            "guiding_questions": [
                "Who are your ideal customers?",
                "What are their online shopping behaviors?",
                "How do they discover products?",
                "What influences their purchase decisions?"
            ]
        },
        "platform_strategy": {
            "title": "E-commerce Platform Strategy",
            "description": "Technology platform and website strategy",
            "section_type": "technology_stack",
            "order": 4,
            "required": True,
            "ai_prompt": "Define e-commerce platform strategy for {products} business",
            "guiding_questions": [
                "What platform will you use?",
                "What features do you need?",
                "How will you handle payments?",
                "What about mobile optimization?"
            ]
        },
        "digital_marketing": {
            "title": "Digital Marketing Strategy",
            "description": "Online marketing and customer acquisition",
            "section_type": "marketing_mix",
            "order": 5,
            "required": True,
            "ai_prompt": "Create digital marketing strategy for {products} e-commerce",
            "guiding_questions": [
                "How will you drive traffic?",
                "What's your SEO strategy?",
                "How will you use social media?",
                "What's your paid advertising plan?"
            ]
        },
        "logistics_fulfillment": {
            "title": "Logistics & Fulfillment",
            "description": "Order processing and shipping strategy",
            "section_type": "process_flow",
            "order": 6,
            "required": True,
            "ai_prompt": "Design logistics and fulfillment for {products} e-commerce",
            "guiding_questions": [
                "How will you fulfill orders?",
                "What shipping options will you offer?",
                "How will you handle returns?",
                "What about international shipping?"
            ]
        },
        "customer_service": {
            "title": "Customer Service Strategy",
            "description": "Customer support and retention strategy",
            "section_type": "text",
            "order": 7,
            "required": True,
            "ai_prompt": "Create customer service strategy for e-commerce business",
            "guiding_questions": [
                "How will you support customers?",
                "What channels will you use?",
                "How will you handle complaints?",
                "What's your retention strategy?"
            ]
        },
        "financial_projections": {
            "title": "Financial Projections",
            "description": "Revenue, costs, and profitability forecasts",
            "section_type": "financial_forecast",
            "order": 8,
            "required": True,
            "ai_prompt": "Create financial projections for {products} e-commerce business",
            "guiding_questions": [
                "What are your revenue projections?",
                "What are your major costs?",
                "What's your gross margin?",
                "When will you be profitable?"
            ]
        }
    }
}

# Restaurant Template
RESTAURANT_TEMPLATE = {
    "name": "Restaurant Business Plan",
    "description": "Specialized template for restaurant and food service businesses",
    "template_type": "restaurant",
    "sections": {
        "concept_overview": {
            "title": "Restaurant Concept",
            "description": "Your restaurant concept and unique selling proposition",
            "section_type": "text",
            "order": 1,
            "required": True,
            "ai_prompt": "Describe a restaurant concept for {cuisine_type} cuisine",
            "guiding_questions": [
                "What type of restaurant is it?",
                "What cuisine will you serve?",
                "What's your unique concept?",
                "What's the dining experience?"
            ]
        },
        "menu_development": {
            "title": "Menu Development",
            "description": "Menu planning and food cost analysis",
            "section_type": "table",
            "order": 2,
            "required": True,
            "ai_prompt": "Create menu development plan for {cuisine_type} restaurant",
            "guiding_questions": [
                "What dishes will you serve?",
                "How will you price menu items?",
                "What are your food costs?",
                "How often will you update the menu?"
            ]
        },
        "location_analysis": {
            "title": "Location Analysis",
            "description": "Site selection and market analysis",
            "section_type": "market_sizing",
            "order": 3,
            "required": True,
            "ai_prompt": "Analyze restaurant location for {cuisine_type} in {location}",
            "guiding_questions": [
                "Where will you locate your restaurant?",
                "What's the foot traffic like?",
                "Who are your competitors nearby?",
                "What's the demographic profile?"
            ]
        },
        "operations_plan": {
            "title": "Restaurant Operations",
            "description": "Daily operations and service procedures",
            "section_type": "process_flow",
            "order": 4,
            "required": True,
            "ai_prompt": "Create operations plan for {cuisine_type} restaurant",
            "guiding_questions": [
                "What are your hours of operation?",
                "How will you manage kitchen operations?",
                "What's your service style?",
                "How will you ensure food quality?"
            ]
        },
        "staffing_plan": {
            "title": "Staffing Plan",
            "description": "Hiring and staff management strategy",
            "section_type": "team_structure",
            "order": 5,
            "required": True,
            "ai_prompt": "Create staffing plan for {cuisine_type} restaurant",
            "guiding_questions": [
                "How many staff do you need?",
                "What positions will you hire?",
                "How will you train staff?",
                "What's your compensation structure?"
            ]
        },
        "marketing_strategy": {
            "title": "Marketing & Promotion",
            "description": "Customer acquisition and retention strategy",
            "section_type": "marketing_mix",
            "order": 6,
            "required": True,
            "ai_prompt": "Create marketing strategy for {cuisine_type} restaurant",
            "guiding_questions": [
                "How will you attract customers?",
                "What promotional activities will you run?",
                "How will you use social media?",
                "What's your loyalty program?"
            ]
        },
        "financial_projections": {
            "title": "Financial Projections",
            "description": "Revenue, costs, and break-even analysis",
            "section_type": "financial_forecast",
            "order": 7,
            "required": True,
            "ai_prompt": "Create financial projections for {cuisine_type} restaurant",
            "guiding_questions": [
                "What are your startup costs?",
                "What's your expected revenue?",
                "What are your operating expenses?",
                "When will you break even?"
            ]
        }
    }
}

# Consulting Template
CONSULTING_TEMPLATE = {
    "name": "Consulting Business Plan",
    "description": "Template for professional consulting services",
    "template_type": "consulting",
    "sections": {
        "service_overview": {
            "title": "Consulting Services Overview",
            "description": "Description of your consulting services",
            "section_type": "text",
            "order": 1,
            "required": True,
            "ai_prompt": "Describe consulting services for {industry} industry",
            "guiding_questions": [
                "What consulting services do you offer?",
                "What expertise do you bring?",
                "What problems do you solve?",
                "What's your methodology?"
            ]
        },
        "target_clients": {
            "title": "Target Client Analysis",
            "description": "Profile of your ideal clients",
            "section_type": "persona",
            "order": 2,
            "required": True,
            "ai_prompt": "Define target clients for {industry} consulting",
            "guiding_questions": [
                "Who are your ideal clients?",
                "What size companies do you target?",
                "What challenges do they face?",
                "How do they buy consulting services?"
            ]
        },
        "service_delivery": {
            "title": "Service Delivery Model",
            "description": "How you deliver consulting services",
            "section_type": "process_flow",
            "order": 3,
            "required": True,
            "ai_prompt": "Create service delivery model for {industry} consulting",
            "guiding_questions": [
                "How do you deliver services?",
                "What's your engagement process?",
                "How do you ensure quality?",
                "What tools do you use?"
            ]
        },
        "pricing_model": {
            "title": "Pricing Strategy",
            "description": "How you price your consulting services",
            "section_type": "pricing_strategy",
            "order": 4,
            "required": True,
            "ai_prompt": "Create pricing strategy for {industry} consulting",
            "guiding_questions": [
                "How do you price your services?",
                "Do you charge hourly or project-based?",
                "What's your value proposition?",
                "How do you justify your rates?"
            ]
        },
        "business_development": {
            "title": "Business Development Strategy",
            "description": "How you acquire new clients",
            "section_type": "marketing_mix",
            "order": 5,
            "required": True,
            "ai_prompt": "Create business development strategy for consulting",
            "guiding_questions": [
                "How do you find new clients?",
                "What's your sales process?",
                "How do you build relationships?",
                "What's your referral strategy?"
            ]
        }
    }
}

# Mobile App Template
MOBILE_APP_TEMPLATE = {
    "name": "Mobile App Business Plan",
    "description": "Template for mobile application businesses",
    "template_type": "mobile_app",
    "sections": {
        "app_concept": {
            "title": "App Concept",
            "description": "Overview of your mobile application",
            "section_type": "text",
            "order": 1,
            "required": True,
            "ai_prompt": "Describe a mobile app concept for {app_category}",
            "guiding_questions": [
                "What does your app do?",
                "What problem does it solve?",
                "Who is your target audience?",
                "What makes it unique?"
            ]
        },
        "user_experience": {
            "title": "User Experience Design",
            "description": "App design and user journey",
            "section_type": "journey_map",
            "order": 2,
            "required": True,
            "ai_prompt": "Design user experience for {app_category} mobile app",
            "guiding_questions": [
                "What's the user journey?",
                "How is the app structured?",
                "What are the key features?",
                "How do users interact with the app?"
            ]
        },
        "technical_architecture": {
            "title": "Technical Architecture",
            "description": "Technology stack and development approach",
            "section_type": "technology_stack",
            "order": 3,
            "required": True,
            "ai_prompt": "Define technical architecture for {app_category} mobile app",
            "guiding_questions": [
                "What technology will you use?",
                "Native or cross-platform?",
                "What backend services do you need?",
                "How will you handle data?"
            ]
        },
        "monetization_strategy": {
            "title": "Monetization Strategy",
            "description": "How you'll generate revenue from the app",
            "section_type": "revenue_model",
            "order": 4,
            "required": True,
            "ai_prompt": "Create monetization strategy for {app_category} mobile app",
            "guiding_questions": [
                "How will you make money?",
                "Freemium, paid, or ad-supported?",
                "What's your pricing strategy?",
                "What are your revenue projections?"
            ]
        },
        "user_acquisition": {
            "title": "User Acquisition Strategy",
            "description": "How you'll acquire and retain users",
            "section_type": "marketing_mix",
            "order": 5,
            "required": True,
            "ai_prompt": "Create user acquisition strategy for mobile app",
            "guiding_questions": [
                "How will you acquire users?",
                "What marketing channels will you use?",
                "How will you optimize app store presence?",
                "What's your retention strategy?"
            ]
        },
        "development_roadmap": {
            "title": "Development Roadmap",
            "description": "App development timeline and milestones",
            "section_type": "feature_roadmap",
            "order": 6,
            "required": True,
            "ai_prompt": "Create development roadmap for {app_category} mobile app",
            "guiding_questions": [
                "What's your MVP?",
                "What features will you build first?",
                "What's your development timeline?",
                "How will you iterate based on feedback?"
            ]
        }
    }
}

# Non-Profit Template
NONPROFIT_TEMPLATE = {
    "name": "Non-Profit Business Plan",
    "description": "Template for non-profit organizations",
    "template_type": "nonprofit",
    "sections": {
        "mission_vision": {
            "title": "Mission & Vision",
            "description": "Your organization's purpose and goals",
            "section_type": "text",
            "order": 1,
            "required": True,
            "ai_prompt": "Create mission and vision for {cause} non-profit",
            "guiding_questions": [
                "What is your mission?",
                "What is your vision for the future?",
                "What values guide your work?",
                "What impact do you want to make?"
            ]
        },
        "programs_services": {
            "title": "Programs & Services",
            "description": "Description of your programs and services",
            "section_type": "text",
            "order": 2,
            "required": True,
            "ai_prompt": "Describe programs and services for {cause} non-profit",
            "guiding_questions": [
                "What programs will you offer?",
                "Who will benefit from your services?",
                "How will you deliver programs?",
                "What outcomes do you expect?"
            ]
        },
        "target_beneficiaries": {
            "title": "Target Beneficiaries",
            "description": "Who you serve and their needs",
            "section_type": "persona",
            "order": 3,
            "required": True,
            "ai_prompt": "Define target beneficiaries for {cause} non-profit",
            "guiding_questions": [
                "Who do you serve?",
                "What are their needs?",
                "How will you reach them?",
                "How will you measure impact?"
            ]
        },
        "fundraising_strategy": {
            "title": "Fundraising Strategy",
            "description": "How you'll raise funds for your mission",
            "section_type": "revenue_model",
            "order": 4,
            "required": True,
            "ai_prompt": "Create fundraising strategy for {cause} non-profit",
            "guiding_questions": [
                "What are your funding sources?",
                "How will you approach donors?",
                "What grants will you apply for?",
                "What fundraising events will you hold?"
            ]
        },
        "impact_measurement": {
            "title": "Impact Measurement",
            "description": "How you'll measure and report impact",
            "section_type": "kpi_dashboard",
            "order": 5,
            "required": True,
            "ai_prompt": "Define impact measurement for {cause} non-profit",
            "guiding_questions": [
                "How will you measure success?",
                "What metrics will you track?",
                "How will you report to stakeholders?",
                "How will you improve programs?"
            ]
        }
    }
}

# FinTech Template
FINTECH_TEMPLATE = {
    "name": "FinTech Business Plan",
    "description": "Specialized template for financial technology businesses",
    "template_type": "fintech",
    "sections": {
        "executive_summary": {
            "title": "Executive Summary",
            "description": "Overview of your FinTech solution",
            "section_type": "text",
            "order": 1,
            "required": True,
            "ai_prompt": "Create an executive summary for a FinTech business that {business_description}",
            "guiding_questions": [
                "What financial problem does your solution solve?",
                "Who is your target market?",
                "What's your competitive advantage?",
                "What are your revenue projections?"
            ]
        },
        "financial_problem": {
            "title": "Financial Problem & Solution",
            "description": "The financial problem you're solving",
            "section_type": "text",
            "order": 2,
            "required": True,
            "ai_prompt": "Describe the financial problem and solution for a {industry} FinTech",
            "guiding_questions": [
                "What financial pain point are you addressing?",
                "How do people currently solve this problem?",
                "What's your innovative solution?",
                "How does technology enable your solution?"
            ]
        },
        "regulatory_compliance": {
            "title": "Regulatory & Compliance",
            "description": "Regulatory requirements and compliance strategy",
            "section_type": "checklist",
            "order": 3,
            "required": True,
            "ai_prompt": "Create regulatory compliance plan for {industry} FinTech",
            "guiding_questions": [
                "What regulations apply to your business?",
                "How will you ensure compliance?",
                "What licenses do you need?",
                "What are the compliance costs?"
            ]
        },
        "technology_security": {
            "title": "Technology & Security",
            "description": "Technical architecture and security measures",
            "section_type": "technology_stack",
            "order": 4,
            "required": True,
            "ai_prompt": "Define technology and security for FinTech application",
            "guiding_questions": [
                "What's your technology architecture?",
                "How do you ensure data security?",
                "What about fraud prevention?",
                "How do you handle scalability?"
            ]
        },
        "financial_projections": {
            "title": "Financial Projections",
            "description": "Revenue model and financial forecasts",
            "section_type": "financial_forecast",
            "order": 5,
            "required": True,
            "ai_prompt": "Create financial projections for FinTech business",
            "guiding_questions": [
                "What's your revenue model?",
                "What are your transaction volumes?",
                "What are your customer acquisition costs?",
                "When will you break even?"
            ]
        }
    }
}

# Healthcare Template
HEALTHCARE_TEMPLATE = {
    "name": "Healthcare Business Plan",
    "description": "Template for healthcare and medical service businesses",
    "template_type": "healthcare",
    "sections": {
        "healthcare_mission": {
            "title": "Healthcare Mission & Vision",
            "description": "Your healthcare mission and patient care philosophy",
            "section_type": "text",
            "order": 1,
            "required": True,
            "ai_prompt": "Create healthcare mission for {specialty} practice",
            "guiding_questions": [
                "What's your healthcare mission?",
                "What patient populations do you serve?",
                "What's your care philosophy?",
                "What outcomes do you aim to achieve?"
            ]
        },
        "services_offered": {
            "title": "Medical Services & Specialties",
            "description": "Healthcare services and medical specialties",
            "section_type": "table",
            "order": 2,
            "required": True,
            "ai_prompt": "Describe medical services for {specialty} healthcare practice",
            "guiding_questions": [
                "What medical services do you offer?",
                "What are your specialties?",
                "What equipment do you need?",
                "What's your service capacity?"
            ]
        },
        "regulatory_compliance": {
            "title": "Healthcare Regulations & Compliance",
            "description": "Medical regulations and compliance requirements",
            "section_type": "checklist",
            "order": 3,
            "required": True,
            "ai_prompt": "Create healthcare compliance plan for {specialty}",
            "guiding_questions": [
                "What healthcare regulations apply?",
                "How do you ensure HIPAA compliance?",
                "What licenses and certifications do you need?",
                "What quality standards must you meet?"
            ]
        },
        "patient_care_model": {
            "title": "Patient Care Model",
            "description": "How you deliver patient care",
            "section_type": "process_flow",
            "order": 4,
            "required": True,
            "ai_prompt": "Design patient care model for {specialty} practice",
            "guiding_questions": [
                "What's your patient journey?",
                "How do you ensure quality care?",
                "What's your appointment system?",
                "How do you handle emergencies?"
            ]
        },
        "financial_model": {
            "title": "Healthcare Financial Model",
            "description": "Revenue streams and financial projections",
            "section_type": "financial_forecast",
            "order": 5,
            "required": True,
            "ai_prompt": "Create financial model for {specialty} healthcare practice",
            "guiding_questions": [
                "What are your revenue sources?",
                "How do you handle insurance billing?",
                "What are your operational costs?",
                "What's your patient volume projection?"
            ]
        }
    }
}

# Manufacturing Template
MANUFACTURING_TEMPLATE = {
    "name": "Manufacturing Business Plan",
    "description": "Template for manufacturing and production businesses",
    "template_type": "manufacturing",
    "sections": {
        "product_overview": {
            "title": "Product & Manufacturing Overview",
            "description": "Products you manufacture and production overview",
            "section_type": "text",
            "order": 1,
            "required": True,
            "ai_prompt": "Describe manufacturing business for {product_type}",
            "guiding_questions": [
                "What products do you manufacture?",
                "What's your production capacity?",
                "What makes your products unique?",
                "Who are your target customers?"
            ]
        },
        "production_process": {
            "title": "Production Process & Operations",
            "description": "Manufacturing processes and operations",
            "section_type": "process_flow",
            "order": 2,
            "required": True,
            "ai_prompt": "Design production process for {product_type} manufacturing",
            "guiding_questions": [
                "What's your production process?",
                "What equipment do you need?",
                "How do you ensure quality control?",
                "What's your production timeline?"
            ]
        },
        "supply_chain": {
            "title": "Supply Chain Management",
            "description": "Supplier relationships and inventory management",
            "section_type": "supply_chain",
            "order": 3,
            "required": True,
            "ai_prompt": "Create supply chain strategy for {product_type} manufacturing",
            "guiding_questions": [
                "Who are your key suppliers?",
                "How do you manage inventory?",
                "What about raw material costs?",
                "How do you handle supply disruptions?"
            ]
        },
        "quality_safety": {
            "title": "Quality Control & Safety",
            "description": "Quality assurance and workplace safety",
            "section_type": "checklist",
            "order": 4,
            "required": True,
            "ai_prompt": "Create quality and safety plan for manufacturing",
            "guiding_questions": [
                "What quality standards do you follow?",
                "How do you test products?",
                "What safety measures are in place?",
                "How do you handle defects?"
            ]
        },
        "distribution_strategy": {
            "title": "Distribution & Sales Strategy",
            "description": "How you distribute and sell your products",
            "section_type": "marketing_mix",
            "order": 5,
            "required": True,
            "ai_prompt": "Create distribution strategy for {product_type}",
            "guiding_questions": [
                "How do you distribute products?",
                "Who are your sales channels?",
                "What's your pricing strategy?",
                "How do you reach customers?"
            ]
        }
    }
}

# Real Estate Template
REAL_ESTATE_TEMPLATE = {
    "name": "Real Estate Business Plan",
    "description": "Template for real estate investment and development businesses",
    "template_type": "real_estate",
    "sections": {
        "market_analysis": {
            "title": "Real Estate Market Analysis",
            "description": "Analysis of local real estate market conditions",
            "section_type": "market_sizing",
            "order": 1,
            "required": True,
            "ai_prompt": "Analyze real estate market in {location} for {property_type}",
            "guiding_questions": [
                "What's the current market condition?",
                "What are property values and trends?",
                "What's the demand and supply situation?",
                "Who are your target buyers/tenants?"
            ]
        },
        "property_portfolio": {
            "title": "Property Portfolio Strategy",
            "description": "Your real estate investment and development strategy",
            "section_type": "table",
            "order": 2,
            "required": True,
            "ai_prompt": "Create property portfolio strategy for {property_type} in {location}",
            "guiding_questions": [
                "What types of properties will you focus on?",
                "What's your acquisition strategy?",
                "How will you finance properties?",
                "What's your exit strategy?"
            ]
        },
        "financial_projections": {
            "title": "Real Estate Financial Projections",
            "description": "ROI, cash flow, and investment projections",
            "section_type": "financial_forecast",
            "order": 3,
            "required": True,
            "ai_prompt": "Create financial projections for real estate business",
            "guiding_questions": [
                "What's your expected ROI?",
                "What are your cash flow projections?",
                "What are your financing costs?",
                "When will you break even?"
            ]
        }
    }
}

# Education/EdTech Template
EDUCATION_TEMPLATE = {
    "name": "Education/EdTech Business Plan",
    "description": "Template for educational technology and learning businesses",
    "template_type": "education",
    "sections": {
        "educational_mission": {
            "title": "Educational Mission & Vision",
            "description": "Your educational philosophy and learning objectives",
            "section_type": "text",
            "order": 1,
            "required": True,
            "ai_prompt": "Create educational mission for {education_type} platform",
            "guiding_questions": [
                "What's your educational mission?",
                "What learning outcomes do you target?",
                "Who are your learners?",
                "What's your teaching methodology?"
            ]
        },
        "curriculum_content": {
            "title": "Curriculum & Content Strategy",
            "description": "Educational content and curriculum design",
            "section_type": "table",
            "order": 2,
            "required": True,
            "ai_prompt": "Design curriculum for {education_type} platform",
            "guiding_questions": [
                "What subjects/skills will you teach?",
                "How is your curriculum structured?",
                "What content formats will you use?",
                "How do you ensure quality?"
            ]
        },
        "learning_technology": {
            "title": "Learning Technology Platform",
            "description": "Educational technology and delivery platform",
            "section_type": "technology_stack",
            "order": 3,
            "required": True,
            "ai_prompt": "Define learning technology for {education_type} platform",
            "guiding_questions": [
                "What technology platform will you use?",
                "How will you deliver content?",
                "What interactive features will you include?",
                "How will you track progress?"
            ]
        },
        "student_acquisition": {
            "title": "Student Acquisition Strategy",
            "description": "How you'll attract and retain students",
            "section_type": "marketing_mix",
            "order": 4,
            "required": True,
            "ai_prompt": "Create student acquisition strategy for education platform",
            "guiding_questions": [
                "How will you attract students?",
                "What's your pricing model?",
                "How will you retain students?",
                "What's your completion rate target?"
            ]
        }
    }
}

# Franchise Template
FRANCHISE_TEMPLATE = {
    "name": "Franchise Business Plan",
    "description": "Template for franchise operations and expansion",
    "template_type": "franchise",
    "sections": {
        "franchise_concept": {
            "title": "Franchise Concept & Model",
            "description": "Your franchise business model and concept",
            "section_type": "text",
            "order": 1,
            "required": True,
            "ai_prompt": "Describe franchise concept for {business_type}",
            "guiding_questions": [
                "What's your franchise concept?",
                "What makes it franchisable?",
                "What support will you provide?",
                "What's your competitive advantage?"
            ]
        },
        "franchise_system": {
            "title": "Franchise System & Operations",
            "description": "Franchise operations and management system",
            "section_type": "process_flow",
            "order": 2,
            "required": True,
            "ai_prompt": "Design franchise system for {business_type}",
            "guiding_questions": [
                "How will you standardize operations?",
                "What training will you provide?",
                "How will you ensure quality control?",
                "What ongoing support will you offer?"
            ]
        },
        "franchisee_profile": {
            "title": "Ideal Franchisee Profile",
            "description": "Target franchisee characteristics and requirements",
            "section_type": "persona",
            "order": 3,
            "required": True,
            "ai_prompt": "Define ideal franchisee for {business_type} franchise",
            "guiding_questions": [
                "What type of franchisees do you want?",
                "What experience should they have?",
                "What investment level is required?",
                "What are the qualification criteria?"
            ]
        },
        "expansion_strategy": {
            "title": "Franchise Expansion Strategy",
            "description": "How you'll grow your franchise network",
            "section_type": "timeline",
            "order": 4,
            "required": True,
            "ai_prompt": "Create expansion strategy for {business_type} franchise",
            "guiding_questions": [
                "What's your expansion timeline?",
                "Which markets will you target first?",
                "How will you recruit franchisees?",
                "What's your growth projection?"
            ]
        }
    }
}

# Green/Sustainable Business Template
GREEN_BUSINESS_TEMPLATE = {
    "name": "Green/Sustainable Business Plan",
    "description": "Template for environmentally sustainable businesses",
    "template_type": "green_business",
    "sections": {
        "sustainability_mission": {
            "title": "Sustainability Mission & Impact",
            "description": "Your environmental mission and impact goals",
            "section_type": "text",
            "order": 1,
            "required": True,
            "ai_prompt": "Create sustainability mission for {business_type} business",
            "guiding_questions": [
                "What's your environmental mission?",
                "What sustainability goals do you have?",
                "What environmental problems are you solving?",
                "How do you measure impact?"
            ]
        },
        "sustainable_operations": {
            "title": "Sustainable Operations Model",
            "description": "How you'll operate sustainably",
            "section_type": "process_flow",
            "order": 2,
            "required": True,
            "ai_prompt": "Design sustainable operations for {business_type}",
            "guiding_questions": [
                "How will you minimize environmental impact?",
                "What sustainable practices will you implement?",
                "How will you manage waste and resources?",
                "What certifications will you pursue?"
            ]
        },
        "impact_measurement": {
            "title": "Environmental Impact Measurement",
            "description": "How you'll track and report environmental impact",
            "section_type": "kpi_dashboard",
            "order": 3,
            "required": True,
            "ai_prompt": "Define impact measurement for sustainable business",
            "guiding_questions": [
                "What environmental metrics will you track?",
                "How will you measure carbon footprint?",
                "What sustainability reporting will you do?",
                "How will you communicate impact to stakeholders?"
            ]
        }
    }
}

# New Template Types

# Marketplace Template
MARKETPLACE_TEMPLATE = {
    "name": "Marketplace Business Plan",
    "description": "Template for two-sided marketplace platforms",
    "template_type": "marketplace",
    "sections": {
        "executive_summary": {
            "title": "Executive Summary",
            "description": "Overview of your marketplace concept",
            "section_type": "text",
            "order": 1,
            "required": True,
            "ai_prompt": "Create an executive summary for a {industry} marketplace that connects {target_market}",
            "guiding_questions": [
                "What problem does your marketplace solve?",
                "Who are your key stakeholders (buyers and sellers)?",
                "What is your revenue model?",
                "What are your competitive advantages?"
            ]
        },
        "market_analysis": {
            "title": "Two-Sided Market Analysis",
            "description": "Analysis of both supply and demand sides",
            "section_type": "market_sizing",
            "order": 2,
            "required": True,
            "ai_prompt": "Analyze the two-sided market for {industry} marketplace including supply and demand dynamics",
            "guiding_questions": [
                "What is the size of your supply side market?",
                "What is the size of your demand side market?",
                "How will you solve the chicken-and-egg problem?",
                "What are the network effects?"
            ]
        },
        "platform_strategy": {
            "title": "Platform Strategy",
            "description": "Strategy for building and scaling the platform",
            "section_type": "text",
            "order": 3,
            "required": True,
            "ai_prompt": "Develop a platform strategy for {industry} marketplace focusing on user acquisition and retention",
            "guiding_questions": [
                "How will you attract initial users on both sides?",
                "What features will drive engagement?",
                "How will you ensure quality and trust?",
                "What is your scaling strategy?"
            ]
        }
    }
}

# Subscription Business Template
SUBSCRIPTION_TEMPLATE = {
    "name": "Subscription Business Plan",
    "description": "Template for subscription-based business models",
    "template_type": "subscription",
    "sections": {
        "executive_summary": {
            "title": "Executive Summary",
            "description": "Overview of your subscription business",
            "section_type": "text",
            "order": 1,
            "required": True,
            "ai_prompt": "Create an executive summary for a {industry} subscription business offering {service_description}",
            "guiding_questions": [
                "What subscription service do you offer?",
                "What is your target market?",
                "What are your pricing tiers?",
                "What are your retention strategies?"
            ]
        },
        "subscription_model": {
            "title": "Subscription Model Design",
            "description": "Detailed subscription model and pricing strategy",
            "section_type": "revenue_model",
            "order": 2,
            "required": True,
            "ai_prompt": "Design a subscription model for {industry} business with multiple pricing tiers and retention strategies",
            "guiding_questions": [
                "What are your subscription tiers?",
                "How do you price each tier?",
                "What features are included in each tier?",
                "How do you handle upgrades and downgrades?"
            ]
        },
        "customer_lifecycle": {
            "title": "Customer Lifecycle Management",
            "description": "Strategy for acquiring, onboarding, and retaining subscribers",
            "section_type": "customer_journey_map",
            "order": 3,
            "required": True,
            "ai_prompt": "Develop a customer lifecycle strategy for {industry} subscription business focusing on acquisition, onboarding, and retention",
            "guiding_questions": [
                "How do you acquire new subscribers?",
                "What is your onboarding process?",
                "How do you reduce churn?",
                "What are your retention tactics?"
            ]
        }
    }
}

# Social Impact Template
SOCIAL_IMPACT_TEMPLATE = {
    "name": "Social Impact Business Plan",
    "description": "Template for businesses with social or environmental mission",
    "template_type": "social_impact",
    "sections": {
        "mission_vision": {
            "title": "Mission and Social Impact",
            "description": "Your social mission and impact goals",
            "section_type": "text",
            "order": 1,
            "required": True,
            "ai_prompt": "Define the social mission and impact goals for a {industry} business addressing {social_problem}",
            "guiding_questions": [
                "What social or environmental problem are you solving?",
                "What is your theory of change?",
                "How do you measure social impact?",
                "Who are your beneficiaries?"
            ]
        },
        "impact_measurement": {
            "title": "Impact Measurement Framework",
            "description": "How you will measure and report social impact",
            "section_type": "kpi_dashboard",
            "order": 2,
            "required": True,
            "ai_prompt": "Create an impact measurement framework for {industry} social impact business",
            "guiding_questions": [
                "What are your key impact metrics?",
                "How will you collect impact data?",
                "How often will you report on impact?",
                "What frameworks will you use (e.g., SDGs, B-Corp)?"
            ]
        },
        "sustainable_business_model": {
            "title": "Sustainable Business Model",
            "description": "How you balance profit with purpose",
            "section_type": "revenue_model",
            "order": 3,
            "required": True,
            "ai_prompt": "Design a sustainable business model that balances profit with social impact for {industry} business",
            "guiding_questions": [
                "How do you generate revenue while creating impact?",
                "What is your funding strategy?",
                "How do you ensure long-term sustainability?",
                "What partnerships support your mission?"
            ]
        }
    }
}

# AI/Tech Startup Template
AI_STARTUP_TEMPLATE = {
    "name": "AI/Tech Startup Business Plan",
    "description": "Specialized template for AI and technology startups",
    "template_type": "ai_startup",
    "sections": {
        "ai_technology_overview": {
            "title": "AI Technology & Innovation",
            "description": "Overview of your AI technology and innovation",
            "section_type": "technology_overview",
            "order": 1,
            "required": True,
            "ai_prompt": "Describe AI technology for {ai_domain} startup",
            "guiding_questions": [
                "What AI technology are you developing?",
                "What problem does your AI solve?",
                "What's your competitive advantage?",
                "How does your AI work?"
            ]
        },
        "data_strategy": {
            "title": "Data Strategy & Management",
            "description": "How you collect, process, and use data",
            "section_type": "data_flow",
            "order": 2,
            "required": True,
            "ai_prompt": "Create data strategy for {ai_domain} AI startup",
            "guiding_questions": [
                "What data do you need?",
                "How will you collect data?",
                "How do you ensure data quality?",
                "What are your privacy considerations?"
            ]
        },
        "ai_ethics_compliance": {
            "title": "AI Ethics & Compliance",
            "description": "Ethical considerations and regulatory compliance",
            "section_type": "compliance_framework",
            "order": 3,
            "required": True,
            "ai_prompt": "Define AI ethics framework for {ai_domain}",
            "guiding_questions": [
                "What ethical considerations apply?",
                "How do you ensure fairness?",
                "What regulations must you comply with?",
                "How do you handle bias?"
            ]
        }
    }
}

# Blockchain/Crypto Template
BLOCKCHAIN_TEMPLATE = {
    "name": "Blockchain/Crypto Business Plan",
    "description": "Template for blockchain and cryptocurrency businesses",
    "template_type": "blockchain",
    "sections": {
        "blockchain_technology": {
            "title": "Blockchain Technology & Protocol",
            "description": "Your blockchain technology and protocol design",
            "section_type": "technology_architecture",
            "order": 1,
            "required": True,
            "ai_prompt": "Describe blockchain protocol for {blockchain_use_case}",
            "guiding_questions": [
                "What blockchain technology are you using?",
                "What consensus mechanism?",
                "What's your token economics?",
                "How does your protocol work?"
            ]
        },
        "tokenomics": {
            "title": "Tokenomics & Economic Model",
            "description": "Token distribution and economic incentives",
            "section_type": "economic_model",
            "order": 2,
            "required": True,
            "ai_prompt": "Design tokenomics for {blockchain_use_case}",
            "guiding_questions": [
                "What's your token distribution?",
                "How do you incentivize users?",
                "What's your governance model?",
                "How do you prevent inflation?"
            ]
        }
    }
}

# Gaming/Entertainment Template
GAMING_TEMPLATE = {
    "name": "Gaming/Entertainment Business Plan",
    "description": "Template for gaming and entertainment businesses",
    "template_type": "gaming",
    "sections": {
        "game_concept": {
            "title": "Game Concept & Design",
            "description": "Your game concept and core mechanics",
            "section_type": "game_design",
            "order": 1,
            "required": True,
            "ai_prompt": "Describe game concept for {game_genre}",
            "guiding_questions": [
                "What type of game is it?",
                "What are the core mechanics?",
                "Who is your target audience?",
                "What makes it unique?"
            ]
        },
        "monetization_strategy": {
            "title": "Monetization & Revenue Model",
            "description": "How you'll generate revenue from your game",
            "section_type": "revenue_model",
            "order": 2,
            "required": True,
            "ai_prompt": "Create monetization strategy for {game_genre}",
            "guiding_questions": [
                "How will you monetize?",
                "What's your pricing strategy?",
                "Will you use in-app purchases?",
                "What about advertising?"
            ]
        }
    }
}

# Fitness/Wellness Template
FITNESS_TEMPLATE = {
    "name": "Fitness & Wellness Business Plan",
    "description": "Template for fitness centers, gyms, and wellness businesses",
    "template_type": "fitness",
    "sections": {
        "facility_concept": {
            "title": "Facility Concept & Services",
            "description": "Your fitness facility concept and service offerings",
            "section_type": "service_overview",
            "order": 1,
            "required": True,
            "ai_prompt": "Describe fitness facility concept for {facility_type}",
            "guiding_questions": [
                "What type of fitness facility?",
                "What services will you offer?",
                "What equipment will you have?",
                "What makes you different?"
            ]
        },
        "member_acquisition": {
            "title": "Member Acquisition Strategy",
            "description": "How you'll attract and retain members",
            "section_type": "marketing_strategy",
            "order": 2,
            "required": True,
            "ai_prompt": "Create member acquisition strategy for {facility_type}",
            "guiding_questions": [
                "Who is your target demographic?",
                "How will you attract members?",
                "What's your pricing strategy?",
                "How will you retain members?"
            ]
        }
    }
}

# Food Truck Template
FOOD_TRUCK_TEMPLATE = {
    "name": "Food Truck Business Plan",
    "description": "Template for mobile food service businesses",
    "template_type": "food_truck",
    "sections": {
        "concept_menu": {
            "title": "Food Concept & Menu",
            "description": "Your food concept and menu offerings",
            "section_type": "product_overview",
            "order": 1,
            "required": True,
            "ai_prompt": "Describe food truck concept for {cuisine_type}",
            "guiding_questions": [
                "What type of cuisine?",
                "What's your signature dish?",
                "How will you source ingredients?",
                "What's your price range?"
            ]
        },
        "location_strategy": {
            "title": "Location & Route Strategy",
            "description": "Your location strategy and route planning",
            "section_type": "location_analysis",
            "order": 2,
            "required": True,
            "ai_prompt": "Create location strategy for {cuisine_type} food truck",
            "guiding_questions": [
                "Where will you operate?",
                "What's your route schedule?",
                "How will you get permits?",
                "What events will you target?"
            ]
        }
    }
}

# Beauty/Salon Template
BEAUTY_SALON_TEMPLATE = {
    "name": "Beauty Salon Business Plan",
    "description": "Template for beauty salons and spa businesses",
    "template_type": "beauty_salon",
    "sections": {
        "service_menu": {
            "title": "Service Menu & Specializations",
            "description": "Your beauty services and specializations",
            "section_type": "service_catalog",
            "order": 1,
            "required": True,
            "ai_prompt": "Describe beauty salon services for {salon_type}",
            "guiding_questions": [
                "What services will you offer?",
                "What are your specializations?",
                "What products will you use?",
                "What's your pricing structure?"
            ]
        },
        "client_experience": {
            "title": "Client Experience & Retention",
            "description": "How you'll create exceptional client experiences",
            "section_type": "customer_experience",
            "order": 2,
            "required": True,
            "ai_prompt": "Design client experience for {salon_type}",
            "guiding_questions": [
                "What's your client journey?",
                "How will you ensure satisfaction?",
                "What loyalty programs?",
                "How will you handle complaints?"
            ]
        }
    }
}

# Pet Services Template
PET_SERVICES_TEMPLATE = {
    "name": "Pet Services Business Plan",
    "description": "Template for pet care and veterinary businesses",
    "template_type": "pet_services",
    "sections": {
        "service_offerings": {
            "title": "Pet Service Offerings",
            "description": "Your pet care services and specializations",
            "section_type": "service_overview",
            "order": 1,
            "required": True,
            "ai_prompt": "Describe pet services for {service_type}",
            "guiding_questions": [
                "What pet services will you offer?",
                "What animals will you serve?",
                "What are your qualifications?",
                "What equipment do you need?"
            ]
        },
        "safety_protocols": {
            "title": "Safety & Care Protocols",
            "description": "Your safety and animal care protocols",
            "section_type": "operational_procedures",
            "order": 2,
            "required": True,
            "ai_prompt": "Create safety protocols for {service_type}",
            "guiding_questions": [
                "What safety measures?",
                "How do you handle emergencies?",
                "What insurance do you need?",
                "How do you ensure animal welfare?"
            ]
        }
    }
}

# Automotive Template
AUTOMOTIVE_TEMPLATE = {
    "name": "Automotive Business Plan",
    "description": "Template for automotive repair and service businesses",
    "template_type": "automotive",
    "sections": {
        "service_capabilities": {
            "title": "Service Capabilities & Equipment",
            "description": "Your automotive services and equipment",
            "section_type": "technical_capabilities",
            "order": 1,
            "required": True,
            "ai_prompt": "Describe automotive services for {service_type}",
            "guiding_questions": [
                "What automotive services?",
                "What equipment do you need?",
                "What certifications required?",
                "What vehicle types?"
            ]
        },
        "customer_trust": {
            "title": "Customer Trust & Quality Assurance",
            "description": "How you'll build customer trust and ensure quality",
            "section_type": "quality_framework",
            "order": 2,
            "required": True,
            "ai_prompt": "Create trust strategy for {service_type}",
            "guiding_questions": [
                "How will you build trust?",
                "What quality guarantees?",
                "How do you handle warranties?",
                "What's your pricing transparency?"
            ]
        }
    }
}

# Template registry
TEMPLATE_REGISTRY = {
    "standard": STANDARD_TEMPLATE,
    "lean": LEAN_CANVAS_TEMPLATE,
    "saas": SAAS_TEMPLATE,
    "ecommerce": ECOMMERCE_TEMPLATE,
    "restaurant": RESTAURANT_TEMPLATE,
    "consulting": CONSULTING_TEMPLATE,
    "mobile_app": MOBILE_APP_TEMPLATE,
    "nonprofit": NONPROFIT_TEMPLATE,
    "fintech": FINTECH_TEMPLATE,
    "healthcare": HEALTHCARE_TEMPLATE,
    "manufacturing": MANUFACTURING_TEMPLATE,
    "real_estate": REAL_ESTATE_TEMPLATE,
    "education": EDUCATION_TEMPLATE,
    "franchise": FRANCHISE_TEMPLATE,
    "green_business": GREEN_BUSINESS_TEMPLATE,
    "marketplace": MARKETPLACE_TEMPLATE,
    "subscription": SUBSCRIPTION_TEMPLATE,
    "social_impact": SOCIAL_IMPACT_TEMPLATE,
    "ai_startup": AI_STARTUP_TEMPLATE,
    "blockchain": BLOCKCHAIN_TEMPLATE,
    "gaming": GAMING_TEMPLATE,
    "fitness": FITNESS_TEMPLATE,
    "food_truck": FOOD_TRUCK_TEMPLATE,
    "beauty_salon": BEAUTY_SALON_TEMPLATE,
    "pet_services": PET_SERVICES_TEMPLATE,
    "automotive": AUTOMOTIVE_TEMPLATE,
}

# Photography Template
PHOTOGRAPHY_TEMPLATE = {
    "name": "Photography Business Plan",
    "description": "Template for photography and creative services",
    "template_type": "photography",
    "sections": {
        "portfolio_services": {
            "title": "Portfolio & Service Offerings",
            "description": "Your photography style and service packages",
            "section_type": "creative_portfolio",
            "order": 1,
            "required": True,
            "ai_prompt": "Describe photography services for {photography_type}",
            "guiding_questions": [
                "What photography style?",
                "What services do you offer?",
                "What's your target market?",
                "How do you price packages?"
            ]
        }
    }
}

# Event Planning Template
EVENT_PLANNING_TEMPLATE = {
    "name": "Event Planning Business Plan",
    "description": "Template for event planning and coordination services",
    "template_type": "event_planning",
    "sections": {
        "event_specialization": {
            "title": "Event Specialization & Services",
            "description": "Your event planning specializations and services",
            "section_type": "service_specialization",
            "order": 1,
            "required": True,
            "ai_prompt": "Describe event planning services for {event_type}",
            "guiding_questions": [
                "What events do you specialize in?",
                "What services do you provide?",
                "What's your planning process?",
                "How do you handle vendors?"
            ]
        }
    }
}

# Cleaning Services Template
CLEANING_SERVICES_TEMPLATE = {
    "name": "Cleaning Services Business Plan",
    "description": "Template for residential and commercial cleaning services",
    "template_type": "cleaning_services",
    "sections": {
        "service_types": {
            "title": "Cleaning Service Types & Standards",
            "description": "Your cleaning services and quality standards",
            "section_type": "service_standards",
            "order": 1,
            "required": True,
            "ai_prompt": "Describe cleaning services for {service_type}",
            "guiding_questions": [
                "What cleaning services?",
                "Residential or commercial?",
                "What are your standards?",
                "What supplies do you use?"
            ]
        }
    }
}

# Landscaping Template
LANDSCAPING_TEMPLATE = {
    "name": "Landscaping Business Plan",
    "description": "Template for landscaping and lawn care services",
    "template_type": "landscaping",
    "sections": {
        "service_offerings": {
            "title": "Landscaping Services & Capabilities",
            "description": "Your landscaping services and capabilities",
            "section_type": "service_capabilities",
            "order": 1,
            "required": True,
            "ai_prompt": "Describe landscaping services for {service_focus}",
            "guiding_questions": [
                "What landscaping services?",
                "What's your specialty?",
                "What equipment do you need?",
                "Seasonal or year-round?"
            ]
        }
    }
}

# Home Services Template
HOME_SERVICES_TEMPLATE = {
    "name": "Home Services Business Plan",
    "description": "Template for home repair and maintenance services",
    "template_type": "home_services",
    "sections": {
        "service_expertise": {
            "title": "Service Expertise & Capabilities",
            "description": "Your home service expertise and capabilities",
            "section_type": "technical_expertise",
            "order": 1,
            "required": True,
            "ai_prompt": "Describe home services for {service_category}",
            "guiding_questions": [
                "What home services?",
                "What's your expertise?",
                "What tools/equipment needed?",
                "How do you ensure quality?"
            ]
        }
    }
}

# Digital Marketing Template
DIGITAL_MARKETING_TEMPLATE = {
    "name": "Digital Marketing Agency Plan",
    "description": "Template for digital marketing and advertising agencies",
    "template_type": "digital_marketing",
    "sections": {
        "service_portfolio": {
            "title": "Digital Marketing Services",
            "description": "Your digital marketing service portfolio",
            "section_type": "service_portfolio",
            "order": 1,
            "required": True,
            "ai_prompt": "Describe digital marketing services for {specialization}",
            "guiding_questions": [
                "What digital marketing services?",
                "What's your specialization?",
                "What tools do you use?",
                "How do you measure success?"
            ]
        }
    }
}

# Dropshipping Template
DROPSHIPPING_TEMPLATE = {
    "name": "Dropshipping Business Plan",
    "description": "Template for dropshipping e-commerce businesses",
    "template_type": "dropshipping",
    "sections": {
        "product_strategy": {
            "title": "Product Selection & Sourcing",
            "description": "Your product selection and supplier strategy",
            "section_type": "product_sourcing",
            "order": 1,
            "required": True,
            "ai_prompt": "Create product strategy for {product_niche} dropshipping",
            "guiding_questions": [
                "What products will you sell?",
                "Who are your suppliers?",
                "How do you validate products?",
                "What's your pricing strategy?"
            ]
        }
    }
}

# Affiliate Marketing Template
AFFILIATE_MARKETING_TEMPLATE = {
    "name": "Affiliate Marketing Business Plan",
    "description": "Template for affiliate marketing businesses",
    "template_type": "affiliate_marketing",
    "sections": {
        "niche_strategy": {
            "title": "Niche Selection & Content Strategy",
            "description": "Your affiliate marketing niche and content approach",
            "section_type": "content_strategy",
            "order": 1,
            "required": True,
            "ai_prompt": "Create affiliate strategy for {niche} market",
            "guiding_questions": [
                "What's your niche?",
                "What content will you create?",
                "Which affiliate programs?",
                "How will you drive traffic?"
            ]
        }
    }
}

# Coaching Template
COACHING_TEMPLATE = {
    "name": "Coaching Business Plan",
    "description": "Template for life, business, and specialty coaching services",
    "template_type": "coaching",
    "sections": {
        "coaching_methodology": {
            "title": "Coaching Methodology & Approach",
            "description": "Your coaching methodology and service approach",
            "section_type": "methodology_framework",
            "order": 1,
            "required": True,
            "ai_prompt": "Describe coaching methodology for {coaching_type}",
            "guiding_questions": [
                "What type of coaching?",
                "What's your methodology?",
                "What certifications do you have?",
                "How do you measure success?"
            ]
        }
    }
}

# Tutoring Template
TUTORING_TEMPLATE = {
    "name": "Tutoring Business Plan",
    "description": "Template for tutoring and educational services",
    "template_type": "tutoring",
    "sections": {
        "subject_expertise": {
            "title": "Subject Expertise & Teaching Methods",
            "description": "Your subject expertise and teaching approach",
            "section_type": "educational_framework",
            "order": 1,
            "required": True,
            "ai_prompt": "Describe tutoring services for {subject_area}",
            "guiding_questions": [
                "What subjects do you tutor?",
                "What age groups?",
                "What's your teaching method?",
                "How do you track progress?"
            ]
        }
    }
}

# Travel & Tourism Template
TRAVEL_TOURISM_TEMPLATE = {
    "name": "Travel & Tourism Business Plan",
    "description": "Template for travel agencies and tourism businesses",
    "template_type": "travel_tourism",
    "sections": {
        "destination_services": {
            "title": "Destination & Service Offerings",
            "description": "Your travel destinations and service packages",
            "section_type": "destination_portfolio",
            "order": 1,
            "required": True,
            "ai_prompt": "Describe travel services for {destination_type}",
            "guiding_questions": [
                "What destinations do you cover?",
                "What travel services?",
                "What's your specialty?",
                "How do you ensure customer satisfaction?"
            ]
        }
    }
}

# Import/Export Template
IMPORT_EXPORT_TEMPLATE = {
    "name": "Import/Export Business Plan",
    "description": "Template for international trade businesses",
    "template_type": "import_export",
    "sections": {
        "trade_strategy": {
            "title": "Trade Strategy & Product Focus",
            "description": "Your international trade strategy and products",
            "section_type": "trade_framework",
            "order": 1,
            "required": True,
            "ai_prompt": "Create trade strategy for {product_category}",
            "guiding_questions": [
                "What products will you trade?",
                "Which countries/markets?",
                "What are the regulations?",
                "How will you manage logistics?"
            ]
        }
    }
}

# Logistics Template
LOGISTICS_TEMPLATE = {
    "name": "Logistics Business Plan",
    "description": "Template for logistics and transportation services",
    "template_type": "logistics",
    "sections": {
        "service_capabilities": {
            "title": "Logistics Services & Capabilities",
            "description": "Your logistics services and operational capabilities",
            "section_type": "operational_framework",
            "order": 1,
            "required": True,
            "ai_prompt": "Describe logistics services for {service_type}",
            "guiding_questions": [
                "What logistics services?",
                "What's your coverage area?",
                "What equipment do you need?",
                "How do you ensure reliability?"
            ]
        }
    }
}

# Security Services Template
SECURITY_SERVICES_TEMPLATE = {
    "name": "Security Services Business Plan",
    "description": "Template for security and protection services",
    "template_type": "security_services",
    "sections": {
        "security_offerings": {
            "title": "Security Service Offerings",
            "description": "Your security services and protection capabilities",
            "section_type": "security_framework",
            "order": 1,
            "required": True,
            "ai_prompt": "Describe security services for {security_type}",
            "guiding_questions": [
                "What security services?",
                "What certifications needed?",
                "What equipment required?",
                "How do you ensure quality?"
            ]
        }
    }
}

# Legal Services Template
LEGAL_SERVICES_TEMPLATE = {
    "name": "Legal Services Business Plan",
    "description": "Template for law firms and legal service providers",
    "template_type": "legal_services",
    "sections": {
        "practice_areas": {
            "title": "Practice Areas & Legal Expertise",
            "description": "Your legal practice areas and expertise",
            "section_type": "legal_framework",
            "order": 1,
            "required": True,
            "ai_prompt": "Describe legal practice for {practice_area}",
            "guiding_questions": [
                "What practice areas?",
                "What's your expertise?",
                "Who is your target client?",
                "How do you price services?"
            ]
        }
    }
}

# Accounting Services Template
ACCOUNTING_SERVICES_TEMPLATE = {
    "name": "Accounting Services Business Plan",
    "description": "Template for accounting and bookkeeping services",
    "template_type": "accounting_services",
    "sections": {
        "service_offerings": {
            "title": "Accounting Service Offerings",
            "description": "Your accounting and financial services",
            "section_type": "financial_services",
            "order": 1,
            "required": True,
            "ai_prompt": "Describe accounting services for {client_type}",
            "guiding_questions": [
                "What accounting services?",
                "What software do you use?",
                "What certifications do you have?",
                "How do you ensure accuracy?"
            ]
        }
    }
}

# Insurance Services Template
INSURANCE_SERVICES_TEMPLATE = {
    "name": "Insurance Services Business Plan",
    "description": "Template for insurance agencies and brokers",
    "template_type": "insurance_services",
    "sections": {
        "insurance_products": {
            "title": "Insurance Products & Services",
            "description": "Your insurance products and client services",
            "section_type": "insurance_portfolio",
            "order": 1,
            "required": True,
            "ai_prompt": "Describe insurance services for {insurance_type}",
            "guiding_questions": [
                "What insurance products?",
                "What's your target market?",
                "Which carriers do you work with?",
                "How do you build trust?"
            ]
        }
    }
}

# Update template registry with all new templates
TEMPLATE_REGISTRY.update({
    "photography": PHOTOGRAPHY_TEMPLATE,
    "event_planning": EVENT_PLANNING_TEMPLATE,
    "cleaning_services": CLEANING_SERVICES_TEMPLATE,
    "landscaping": LANDSCAPING_TEMPLATE,
    "home_services": HOME_SERVICES_TEMPLATE,
    "digital_marketing": DIGITAL_MARKETING_TEMPLATE,
    "dropshipping": DROPSHIPPING_TEMPLATE,
    "affiliate_marketing": AFFILIATE_MARKETING_TEMPLATE,
    "coaching": COACHING_TEMPLATE,
    "tutoring": TUTORING_TEMPLATE,
    "travel_tourism": TRAVEL_TOURISM_TEMPLATE,
    "import_export": IMPORT_EXPORT_TEMPLATE,
    "logistics": LOGISTICS_TEMPLATE,
    "security_services": SECURITY_SERVICES_TEMPLATE,
    "legal_services": LEGAL_SERVICES_TEMPLATE,
    "accounting_services": ACCOUNTING_SERVICES_TEMPLATE,
    "insurance_services": INSURANCE_SERVICES_TEMPLATE,
})

def get_template_definition(template_type):
    """Get template definition by type"""
    return TEMPLATE_REGISTRY.get(template_type, STANDARD_TEMPLATE)

def get_all_template_types():
    """Get all available template types"""
    return list(TEMPLATE_REGISTRY.keys())
