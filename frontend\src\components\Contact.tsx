import React, { useState } from 'react';
import { Send, Mail, MapPin, Phone, Github, Linkedin, Twitter } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../store/hooks';
import { RTLText, RTLFlex, RTLIcon } from './common';


const Contact = () => {
  const { t } = useTranslation();
  const { language } = useAppSelector(state => state.language);
  const isRTL = language === 'ar';
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Form submission logic would go here
    console.log(formData);
    // Reset form
    setFormData({
      name: '',
      email: '',
      subject: '',
      message: ''
    });
    // Show success message or handle accordingly
  };

  return (
    <section id="contact" className="py-20 glass-light">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-16">
          <RTLText as="h2" align="center" className="text-3xl md:text-4xl font-bold mb-4 text-glass-primary">
            {t('contact.getIn')} <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-blue-400">{t('contact.touch')}</span>
          </RTLText>
          <RTLText as="div" align="center" className="text-glass-secondary text-lg">
            {t('contact.description')}
          </RTLText>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-10">
          <div className="glass-light rounded-xl p-8 border shadow-lg">
            <RTLText as="h3" className="text-2xl font-semibold mb-6 text-glass-primary">{t('contact.sendUsMessage')}</RTLText>
            <form onSubmit={handleSubmit}>
              <div className="mb-4">
                <RTLText as="label" htmlFor="name" className="block text-sm font-medium text-glass-secondary mb-1">
                  {t('contact.form.name')}
                </RTLText>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 bg-white/10 backdrop-blur-sm text-glass-primary placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                  placeholder={t('contact.form.namePlaceholder')}
                  required
                />
              </div>
              <div className="mb-4">
                <RTLText as="label" htmlFor="email" className="block text-sm font-medium text-glass-secondary mb-1">
                  {t('contact.form.email')}
                </RTLText>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 bg-white/10 backdrop-blur-sm text-glass-primary placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                  placeholder={t('contact.form.emailPlaceholder')}
                  required
                />
              </div>
              <div className="mb-4">
                <RTLText as="label" htmlFor="subject" className="block text-sm font-medium text-glass-secondary mb-1">
                  {t('contact.form.subject')}
                </RTLText>
                <input
                  type="text"
                  id="subject"
                  name="subject"
                  value={formData.subject}
                  onChange={handleChange}
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 bg-white/10 backdrop-blur-sm text-glass-primary placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                  placeholder={t('contact.form.subjectPlaceholder')}
                  required
                />
              </div>
              <div className="mb-6">
                <RTLText as="label" htmlFor="message" className="block text-sm font-medium text-glass-secondary mb-1">
                  {t('contact.form.message')}
                </RTLText>
                <textarea
                  id="message"
                  name="message"
                  rows={5}
                  value={formData.message}
                  onChange={handleChange}
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 bg-white/10 backdrop-blur-sm text-glass-primary placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300 resize-none"
                  placeholder={t('contact.form.messagePlaceholder')}
                  required
                ></textarea>
              </div>
              <button
                type="submit"
                className="bg-gradient-to-r from-purple-500 to-blue-500 text-white px-6 py-3 rounded-full font-medium flex items-center gap-2 hover:shadow-glow transition-all duration-300 hover:scale-105"
              >
                {t('contact.form.send')} <RTLIcon icon={Send} size={16} />
              </button>
            </form>
          </div>

          <div>
            <div className="mb-8 glass-light rounded-xl p-8 border shadow-lg">
              <RTLText as="h3" className="text-2xl font-semibold mb-6 text-glass-primary">{t('contact.contactInfo')}</RTLText>
              <div className="space-y-4">
                <RTLFlex className="items-start">
                  <RTLIcon icon={Mail} size={20} className="text-purple-500 mt-1" />
                  <div>
                    <RTLText as="h4" className="font-semibold mb-1 text-glass-primary">{t('contact.email')}</RTLText>
                    <a href="mailto:<EMAIL>" className="text-glass-secondary hover:text-purple-300 transition-colors">
                      <EMAIL>
                    </a>
                  </div>
                </RTLFlex>
                <RTLFlex className="items-start">
                  <RTLIcon icon={MapPin} size={20} className="text-purple-500 mt-1" />
                  <div>
                    <RTLText as="h4" className="font-semibold mb-1 text-glass-primary">{t('contact.location')}</RTLText>
                    <RTLText as="div" className="text-glass-secondary">{t('contact.address')}</RTLText>
                  </div>
                </RTLFlex>
                <RTLFlex className="items-start">
                  <RTLIcon icon={Phone} size={20} className="text-purple-500 mt-1" />
                  <div>
                    <RTLText as="h4" className="font-semibold mb-1 text-glass-primary">{t('contact.phone')}</RTLText>
                    <a href="tel:+963987654321" className="text-glass-secondary hover:text-purple-300 transition-colors">
                      +963 987 654 321
                    </a>
                  </div>
                </RTLFlex>
              </div>
            </div>

            <div className="glass-light rounded-xl p-8 border shadow-lg">
              <RTLText as="h3" className="text-2xl font-semibold mb-6 text-glass-primary">{t('contact.connectWithUs')}</RTLText>
              <RTLText as="div" className="text-glass-secondary mb-6">
                {t('contact.socialDescription')}
              </RTLText>
              <RTLFlex className={`${language === 'ar' ? 'space-x-reverse' : ''} space-x-4`}>
                <a
                  href="#"
                  className={`w-12 h-12 rounded-full flex items-center justify-center transition-colors bg-white/10 hover:bg-purple-600 text-glass-primary hover:text-white backdrop-blur-sm ${isRTL ? "flex-row-reverse" : ""}`}
                  aria-label={t("common.github", "GitHub")}
                >
                  <Github size={22} />
                </a>
                <a
                  href="#"
                  className={`w-12 h-12 rounded-full flex items-center justify-center transition-colors bg-white/10 hover:bg-purple-600 text-glass-primary hover:text-white backdrop-blur-sm ${isRTL ? "flex-row-reverse" : ""}`}
                  aria-label={t("common.linkedin", "LinkedIn")}
                >
                  <Linkedin size={22} />
                </a>
                <a
                  href="#"
                  className={`w-12 h-12 rounded-full flex items-center justify-center transition-colors bg-white/10 hover:bg-purple-600 text-glass-primary hover:text-white backdrop-blur-sm ${isRTL ? "flex-row-reverse" : ""}`}
                  aria-label={t("common.twitter", "Twitter")}
                >
                  <Twitter size={22} />
                </a>
              </RTLFlex>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;