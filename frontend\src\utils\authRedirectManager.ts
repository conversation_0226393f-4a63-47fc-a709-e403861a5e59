/**
 * Centralized Authentication Redirect Manager
 * Prevents redirect loops and manages authentication flow consistently
 */

import { User } from '../services/api';
import { getPrimaryDashboardRoute } from './roleBasedRouting';

interface RedirectState {
  isRedirecting: boolean;
  lastRedirectPath: string | null;
  lastRedirectTime: number;
  redirectCount: number;
}

// Global redirect state to prevent loops
let redirectState: RedirectState = {
  isRedirecting: false,
  lastRedirectPath: null,
  lastRedirectTime: 0,
  redirectCount: 0
};

// Constants
const REDIRECT_COOLDOWN_MS = 1000; // 1 second cooldown between redirects
const MAX_REDIRECTS_PER_SESSION = 5; // Maximum redirects to prevent infinite loops
const REDIRECT_TIMEOUT_MS = 5000; // Reset redirect state after 5 seconds

/**
 * Check if we should allow a redirect to prevent loops
 */
export function canRedirect(targetPath: string): boolean {
  const now = Date.now();
  
  // Reset redirect count if enough time has passed
  if (now - redirectState.lastRedirectTime > REDIRECT_TIMEOUT_MS) {
    redirectState.redirectCount = 0;
  }
  
  // Prevent redirect if we're already redirecting to the same path
  if (redirectState.isRedirecting && redirectState.lastRedirectPath === targetPath) {
    console.warn(`🚫 Redirect loop prevented: Already redirecting to ${targetPath}`);
    return false;
  }
  
  // Prevent redirect if we've exceeded the maximum redirect count
  if (redirectState.redirectCount >= MAX_REDIRECTS_PER_SESSION) {
    console.warn(`🚫 Redirect loop prevented: Maximum redirects (${MAX_REDIRECTS_PER_SESSION}) exceeded`);
    return false;
  }
  
  // Prevent rapid redirects
  if (now - redirectState.lastRedirectTime < REDIRECT_COOLDOWN_MS) {
    console.warn(`🚫 Redirect prevented: Too soon after last redirect (${now - redirectState.lastRedirectTime}ms)`);
    return false;
  }
  
  return true;
}

/**
 * Register a redirect to track state
 */
export function registerRedirect(targetPath: string): void {
  const now = Date.now();
  
  redirectState.isRedirecting = true;
  redirectState.lastRedirectPath = targetPath;
  redirectState.lastRedirectTime = now;
  redirectState.redirectCount++;
  
  if (process.env.NODE_ENV === 'development') {
    console.log(`🔄 Redirect registered: ${targetPath} (count: ${redirectState.redirectCount})`);
  }
  
  // Clear redirect state after a timeout
  setTimeout(() => {
    redirectState.isRedirecting = false;
  }, REDIRECT_COOLDOWN_MS);
}

/**
 * Reset redirect state (useful for testing or manual reset)
 */
export function resetRedirectState(): void {
  redirectState = {
    isRedirecting: false,
    lastRedirectPath: null,
    lastRedirectTime: 0,
    redirectCount: 0
  };
  
  if (process.env.NODE_ENV === 'development') {
    console.log('🔄 Redirect state reset');
  }
}

/**
 * Get the appropriate redirect path for authentication state
 */
export function getAuthRedirectPath(
  user: User | null, 
  currentPath: string,
  isAuthenticated: boolean
): string | null {
  // Don't redirect if we're in a redirect cooldown
  if (redirectState.isRedirecting) {
    return null;
  }
  
  // If not authenticated, redirect to login (unless already there)
  if (!isAuthenticated || !user) {
    const authPaths = ['/login', '/register', '/logout'];
    if (authPaths.includes(currentPath)) {
      return null; // Already on auth page
    }
    return '/login';
  }
  
  // If authenticated but on auth pages, redirect to dashboard
  if (isAuthenticated && user) {
    const authPaths = ['/login', '/register'];
    if (authPaths.includes(currentPath)) {
      const dashboardPath = getPrimaryDashboardRoute(user);
      return dashboardPath;
    }
  }
  
  return null;
}

/**
 * Safe redirect function that prevents loops
 */
export function safeRedirect(
  targetPath: string,
  navigate: (path: string, options?: any) => void,
  options: { replace?: boolean } = { replace: true }
): boolean {
  if (!canRedirect(targetPath)) {
    return false;
  }
  
  registerRedirect(targetPath);
  navigate(targetPath, options);
  return true;
}

/**
 * Check if current path is a protected route that requires authentication
 */
export function isProtectedRoute(path: string): boolean {
  const protectedPrefixes = [
    '/dashboard',
    '/admin',
    '/super_admin',
    '/profile',
    '/settings',
    '/chat',
    '/incubator'
  ];
  
  return protectedPrefixes.some(prefix => path.startsWith(prefix));
}

/**
 * Check if current path is an auth route (login, register, etc.)
 */
export function isAuthRoute(path: string): boolean {
  const authPaths = ['/login', '/register', '/logout', '/forgot-password', '/reset-password'];
  return authPaths.includes(path);
}

/**
 * Get redirect debug info for development
 */
export function getRedirectDebugInfo(): RedirectState & {
  canRedirectNow: boolean;
  timeSinceLastRedirect: number;
} {
  const now = Date.now();
  return {
    ...redirectState,
    canRedirectNow: now - redirectState.lastRedirectTime > REDIRECT_COOLDOWN_MS,
    timeSinceLastRedirect: now - redirectState.lastRedirectTime
  };
}

/**
 * Development helper to expose redirect manager to window
 */
export function exposeRedirectDebugger(): void {
  if (process.env.NODE_ENV === 'development') {
    (window as any).redirectDebugger = {
      getState: getRedirectDebugInfo,
      reset: resetRedirectState,
      canRedirect,
      isProtectedRoute,
      isAuthRoute
    };
  }
}
