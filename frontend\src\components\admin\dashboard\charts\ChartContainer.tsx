import React from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../hooks/useLanguage';
interface ChartContainerProps {
  title: string;
  children: React.ReactNode;
}

/**
 * A container component for charts with consistent styling
 */
const ChartContainer: React.FC<ChartContainerProps> = ({ title, children  }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  return (
    <div className="bg-indigo-900/20 rounded-xl p-4 backdrop-blur-sm shadow-lg">
      <h3 className="text-lg font-semibold mb-4">{title}</h3>
      <div className="h-64">
        {children}
      </div>
    </div>
  );
};

export default ChartContainer;
