import { getAuthToken } from './api';

const API_BASE = process.env.REACT_APP_API_URL || 'http://127.0.0.1:8000';

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  status?: number;
}

class SuperAdminApiService {
  private async makeRequest<T>(endpoint: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    try {
      const token = getAuthToken();
      
      if (!token) {
        console.error('🔐 No authentication token available');
        return { success: false, error: 'Authentication required' };
      }

      const url = `${API_BASE}/api/superadmin${endpoint}`;
      console.log(`🔄 Super Admin API Request: ${options.method || 'GET'} ${url}`);

      const response = await fetch(url, {
        ...options,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          ...options.headers,
        },
      });

      console.log(`📡 Response Status: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`❌ API Error: ${response.status}`, errorText);
        
        return {
          success: false,
          error: `API Error: ${response.status} - ${response.statusText}`,
          status: response.status
        };
      }

      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        const responseText = await response.text();
        console.error('📄 Non-JSON response received:', responseText.substring(0, 200));
        
        return {
          success: false,
          error: 'Server returned non-JSON response'
        };
      }

      const data = await response.json();
      console.log(`✅ API Success:`, Object.keys(data));
      
      return { success: true, data };

    } catch (error) {
      console.error('💥 Network Error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error'
      };
    }
  }

  // Dashboard APIs
  async getDashboardData() {
    return this.makeRequest('/system/dashboard/');
  }

  async getSystemHealth() {
    return this.makeRequest('/system/system_health/');
  }

  async getCapabilities() {
    return this.makeRequest('/system/capabilities/');
  }

  // System Management APIs
  async getSystemConfiguration() {
    return this.makeRequest('/system/configuration/');
  }

  async executeSystemOperation(operationId: string) {
    return this.makeRequest(`/system/operations/${operationId}/`, {
      method: 'POST'
    });
  }

  // AI Management APIs
  async getAIConfiguration() {
    return this.makeRequest('/ai/configuration/');
  }

  async updateAIConfiguration(config: any) {
    return this.makeRequest('/ai/configuration/', {
      method: 'POST',
      body: JSON.stringify(config)
    });
  }

  async getAIAnalytics() {
    return this.makeRequest('/ai/analytics/');
  }

  async getAIMonitoring() {
    return this.makeRequest('/ai/monitoring/');
  }

  // User Management APIs
  async getAdvancedUserData() {
    return this.makeRequest('/users/advanced/');
  }

  async impersonateUser(userId: string) {
    return this.makeRequest(`/users/impersonate/${userId}/`, {
      method: 'POST'
    });
  }

  // Security APIs
  async getSecurityEvents() {
    return this.makeRequest('/system/security/');
  }

  async getSecurityConfiguration() {
    return this.makeRequest('/system/security/configuration/');
  }

  // Performance APIs
  async getPerformanceMetrics() {
    return this.makeRequest('/system/performance/');
  }

  async getPerformanceHistory() {
    return this.makeRequest('/system/performance/history/');
  }

  // Communication APIs
  async getCommunicationData() {
    return this.makeRequest('/system/communication/');
  }

  async sendBroadcastMessage(message: any) {
    return this.makeRequest('/system/communication/broadcast/', {
      method: 'POST',
      body: JSON.stringify(message)
    });
  }

  // Backup APIs
  async getBackupStatus() {
    return this.makeRequest('/system/backup/');
  }

  async createBackup() {
    return this.makeRequest('/system/backup/create/', {
      method: 'POST'
    });
  }

  // System Logs APIs
  async getSystemLogs(filters?: any) {
    const queryParams = filters ? `?${new URLSearchParams(filters).toString()}` : '';
    return this.makeRequest(`/system/logs/${queryParams}`);
  }

  // Content Management APIs
  async getContentStats() {
    return this.makeRequest('/content/stats/');
  }

  async moderateContent(contentId: string, action: string) {
    return this.makeRequest(`/content/moderate/${contentId}/`, {
      method: 'POST',
      body: JSON.stringify({ action })
    });
  }

  // Forum Management APIs
  async getForumStats() {
    return this.makeRequest('/forum/stats/');
  }

  async getForumModeration() {
    return this.makeRequest('/forum/moderation/');
  }

  // Incubator Management APIs
  async getIncubatorStats() {
    return this.makeRequest('/incubator/stats/');
  }

  async getBusinessIdeas() {
    return this.makeRequest('/incubator/business-ideas/');
  }

  // Advanced Analytics APIs
  async getRealTimeMetrics() {
    return this.makeRequest('/system/realtime-metrics/');
  }

  async getAdvancedUserAnalytics() {
    return this.makeRequest('/analytics/users/');
  }

  async getUserBehaviorAnalytics() {
    return this.makeRequest('/analytics/behavior/');
  }

  async getUserSegments() {
    return this.makeRequest('/analytics/segments/');
  }

  async createUserSegment(segmentData: any) {
    return this.makeRequest('/analytics/segments/', {
      method: 'POST',
      body: JSON.stringify(segmentData)
    });
  }

  // Revenue & Financial APIs
  async getRevenueAnalytics() {
    return this.makeRequest('/financial/revenue/');
  }

  async getPaymentAnalytics() {
    return this.makeRequest('/financial/payments/');
  }

  async getSubscriptionMetrics() {
    return this.makeRequest('/financial/subscriptions/');
  }

  async getFinancialReports(reportType: string) {
    return this.makeRequest(`/financial/reports/${reportType}/`);
  }

  // Security & Compliance APIs
  async getAdvancedSecurityData() {
    return this.makeRequest('/security/advanced/');
  }

  async getThreatDetection() {
    return this.makeRequest('/security/threats/');
  }

  async getSecurityRules() {
    return this.makeRequest('/security/rules/');
  }

  async updateSecurityRule(ruleId: string, ruleData: any) {
    return this.makeRequest(`/security/rules/${ruleId}/`, {
      method: 'PUT',
      body: JSON.stringify(ruleData)
    });
  }

  async getComplianceStatus() {
    return this.makeRequest('/security/compliance/');
  }

  async runSecurityScan() {
    return this.makeRequest('/security/scan/', {
      method: 'POST'
    });
  }

  // Content Management APIs
  async getContentAnalytics() {
    return this.makeRequest('/content/analytics/');
  }

  async getContentModerationQueue() {
    return this.makeRequest('/content/moderation/queue/');
  }

  async moderateContentBulk(contentIds: string[], action: string) {
    return this.makeRequest('/content/moderation/bulk/', {
      method: 'POST',
      body: JSON.stringify({ content_ids: contentIds, action })
    });
  }

  // Automation & Workflow APIs
  async getAutomatedTasks() {
    return this.makeRequest('/automation/tasks/');
  }

  async createAutomatedTask(taskData: any) {
    return this.makeRequest('/automation/tasks/', {
      method: 'POST',
      body: JSON.stringify(taskData)
    });
  }

  async getWorkflows() {
    return this.makeRequest('/automation/workflows/');
  }

  async executeWorkflow(workflowId: string) {
    return this.makeRequest(`/automation/workflows/${workflowId}/execute/`, {
      method: 'POST'
    });
  }

  // Advanced AI Management APIs
  async getAIModelPerformance() {
    return this.makeRequest('/ai/models/performance/');
  }

  async getAIUsagePatterns() {
    return this.makeRequest('/ai/usage/patterns/');
  }

  async getAICostOptimization() {
    return this.makeRequest('/ai/cost/optimization/');
  }

  async updateAIModel(modelId: string, modelData: any) {
    return this.makeRequest(`/ai/models/${modelId}/`, {
      method: 'PUT',
      body: JSON.stringify(modelData)
    });
  }

  // Communication & Notification APIs
  async getAdvancedCommunicationData() {
    return this.makeRequest('/communication/advanced/');
  }

  async createEmailCampaign(campaignData: any) {
    return this.makeRequest('/communication/campaigns/', {
      method: 'POST',
      body: JSON.stringify(campaignData)
    });
  }

  async sendBulkNotifications(notificationData: any) {
    return this.makeRequest('/communication/notifications/bulk/', {
      method: 'POST',
      body: JSON.stringify(notificationData)
    });
  }

  async getCommunicationAnalytics() {
    return this.makeRequest('/communication/analytics/');
  }

  // Developer Tools APIs
  async getAPIAnalytics() {
    return this.makeRequest('/developer/api/analytics/');
  }

  async getFeatureFlags() {
    return this.makeRequest('/developer/feature-flags/');
  }

  async updateFeatureFlag(flagId: string, enabled: boolean) {
    return this.makeRequest(`/developer/feature-flags/${flagId}/`, {
      method: 'PUT',
      body: JSON.stringify({ enabled })
    });
  }

  async getEnvironmentStatus() {
    return this.makeRequest('/developer/environments/');
  }

  // Reporting & Export APIs
  async generateCustomReport(reportConfig: any) {
    return this.makeRequest('/reports/custom/', {
      method: 'POST',
      body: JSON.stringify(reportConfig)
    });
  }

  async getScheduledReports() {
    return this.makeRequest('/reports/scheduled/');
  }

  async exportData(exportConfig: any) {
    return this.makeRequest('/export/data/', {
      method: 'POST',
      body: JSON.stringify(exportConfig)
    });
  }

  async getExportHistory() {
    return this.makeRequest('/export/history/');
  }

  // Test API connectivity
  async testConnection() {
    console.log('🧪 Testing Super Admin API connectivity...');

    const tests = [
      { name: 'Dashboard', method: () => this.getDashboardData() },
      { name: 'System Health', method: () => this.getSystemHealth() },
      { name: 'Capabilities', method: () => this.getCapabilities() },
      { name: 'Real-time Metrics', method: () => this.getRealTimeMetrics() },
      { name: 'Security Data', method: () => this.getAdvancedSecurityData() },
      { name: 'Revenue Analytics', method: () => this.getRevenueAnalytics() }
    ];

    const results = [];

    for (const test of tests) {
      console.log(`🔍 Testing ${test.name}...`);
      const result = await test.method();
      results.push({
        name: test.name,
        success: result.success,
        error: result.error,
        status: result.status
      });
    }

    console.log('🧪 API Test Results:', results);
    return results;
  }
}

// Export singleton instance
export const superAdminApi = new SuperAdminApiService();

// Export types for TypeScript
export interface DashboardData {
  system_overview: {
    cpu: { usage_percent: number; count: number; status: string };
    memory: { percent: number; used: number; total: number; status: string };
    disk: { percent: number; used: number; total: number; status: string };
    overall_status: string;
    timestamp: string;
  };
  user_stats: {
    total_users: number;
    active_users: number;
    new_users_today: number;
    role_distribution: Array<{ role__name: string; count: number }>;
  };
  timestamp: string;
}

export interface SystemHealth {
  cpu: { usage_percent: number; count: number; status: string };
  memory: { percent: number; used: number; total: number; status: string };
  disk: { percent: number; used: number; total: number; status: string };
  overall_status: string;
  timestamp: string;
}

export interface Capability {
  id: string;
  name: string;
  description: string;
  features: string[];
}

export default superAdminApi;
