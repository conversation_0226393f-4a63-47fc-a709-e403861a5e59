from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.contrib.auth.models import User
from django.db.models import Count, Q, Sum, Avg
from django.utils import timezone
from django.core.management import call_command
from django.conf import settings
from django.db import connection
from datetime import timedelta, datetime
import psutil
import os
import subprocess
import json
import shutil
from api.super_admin_permissions import (
    IsSuperAdminUser, SuperAdminSystemAccess, SuperAdminAuditMixin,
    get_user_super_admin_capabilities, log_super_admin_action
)
from .models import UserProfile, UserRole, UserRoleAssignment
from .serializers import UserSerializer, UserRoleSerializer
from core.models import AIConfiguration
from core.ai_config import get_gemini_config, update_gemini_config, reload_gemini_config
import logging

logger = logging.getLogger(__name__)


class SuperAdminViewSet(SuperAdminAuditMixin, viewsets.ViewSet):
    """
    Super Admin ViewSet with advanced system management capabilities
    """
    permission_classes = [IsSuperAdminUser]

    def list(self, request):
        """Simple endpoint to test Super Admin API connectivity"""
        return Response({
            'message': 'Super Admin API is working',
            'user': request.user.username,
            'timestamp': timezone.now().isoformat(),
            'available_endpoints': [
                'dashboard-stats/',
                'system-health/',
                'capabilities/',
                'database-schema/',
                'security-audit/',
                'advanced-analytics/'
            ]
        })
    
    @action(detail=False, methods=['get'])
    def dashboard_stats(self, request):
        """Get comprehensive dashboard statistics for Super Admin"""
        try:
            # System metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # User statistics
            total_users = User.objects.count()
            active_users = User.objects.filter(is_active=True).count()
            new_users_today = User.objects.filter(
                date_joined__gte=timezone.now().date()
            ).count()
            
            # Role distribution
            role_distribution = UserRoleAssignment.objects.values(
                'role__name'
            ).annotate(count=Count('id'))
            
            # System health
            system_health = {
                'cpu_usage': cpu_percent,
                'memory_usage': memory.percent,
                'disk_usage': (disk.used / disk.total) * 100,
                'status': 'healthy' if cpu_percent < 80 and memory.percent < 80 else 'warning'
            }
            
            stats = {
                'system_health': system_health,
                'user_stats': {
                    'total_users': total_users,
                    'active_users': active_users,
                    'new_users_today': new_users_today,
                    'role_distribution': list(role_distribution)
                },
                'timestamp': timezone.now().isoformat()
            }
            
            log_super_admin_action(request.user, 'dashboard_stats_viewed')
            return Response(stats)
            
        except Exception as e:
            logger.error(f"Error fetching Super Admin dashboard stats: {str(e)}")
            return Response(
                {'error': 'Failed to fetch dashboard statistics'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'])
    def system_info(self, request):
        """Get detailed system information"""
        try:
            system_info = {
                'platform': os.name,
                'python_version': subprocess.check_output(['python', '--version']).decode().strip(),
                'django_version': subprocess.check_output(['python', '-m', 'django', '--version']).decode().strip(),
                'server_uptime': psutil.boot_time(),
                'cpu_count': psutil.cpu_count(),
                'memory_total': psutil.virtual_memory().total,
                'disk_total': psutil.disk_usage('/').total,
            }
            
            log_super_admin_action(request.user, 'system_info_viewed')
            return Response(system_info)
            
        except Exception as e:
            logger.error(f"Error fetching system info: {str(e)}")
            return Response(
                {'error': 'Failed to fetch system information'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'])
    def capabilities(self, request):
        """Get Super Admin capabilities"""
        capabilities = get_user_super_admin_capabilities(request.user)
        return Response({'capabilities': capabilities})
    
    @action(detail=False, methods=['post'])
    def impersonate_user(self, request):
        """Impersonate another user (Super Admin only)"""
        user_id = request.data.get('user_id')
        if not user_id:
            return Response(
                {'error': 'User ID is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            target_user = User.objects.get(id=user_id)
            
            # Don't allow impersonating other Super Admins
            if target_user.is_superuser:
                return Response(
                    {'error': 'Cannot impersonate other Super Admins'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            # Store impersonation in session
            request.session['impersonated_user_id'] = user_id
            request.session['original_user_id'] = request.user.id
            
            log_super_admin_action(
                request.user, 
                'user_impersonation_started',
                {'target_user': target_user.username}
            )
            
            return Response({
                'message': f'Now impersonating user: {target_user.username}',
                'impersonated_user': UserSerializer(target_user).data
            })
            
        except User.DoesNotExist:
            return Response(
                {'error': 'User not found'},
                status=status.HTTP_404_NOT_FOUND
            )
    
    @action(detail=False, methods=['post'])
    def stop_impersonation(self, request):
        """Stop user impersonation"""
        if 'impersonated_user_id' in request.session:
            impersonated_user_id = request.session.pop('impersonated_user_id')
            request.session.pop('original_user_id', None)
            
            log_super_admin_action(
                request.user,
                'user_impersonation_stopped',
                {'impersonated_user_id': impersonated_user_id}
            )
            
            return Response({'message': 'Impersonation stopped'})
        
        return Response(
            {'error': 'No active impersonation'},
            status=status.HTTP_400_BAD_REQUEST
        )
    
    @action(detail=False, methods=['get'])
    def audit_logs(self, request):
        """Get audit logs (Super Admin actions)"""
        # This would typically fetch from a dedicated audit log table
        # For now, return a placeholder response
        logs = [
            {
                'timestamp': timezone.now().isoformat(),
                'user': request.user.username,
                'action': 'audit_logs_viewed',
                'details': 'Super Admin viewed audit logs'
            }
        ]
        
        log_super_admin_action(request.user, 'audit_logs_viewed')
        return Response({'logs': logs})
    
    @action(detail=False, methods=['post'])
    def create_role(self, request):
        """Create a new user role"""
        try:
            role_data = request.data
            role = UserRole.objects.create(
                name=role_data.get('name'),
                display_name=role_data.get('display_name'),
                description=role_data.get('description', ''),
                permission_level=role_data.get('permission_level', 'read'),
                is_active=role_data.get('is_active', True),
                requires_approval=role_data.get('requires_approval', True)
            )
            
            log_super_admin_action(
                request.user,
                'role_created',
                {'role_name': role.name}
            )
            
            return Response(
                UserRoleSerializer(role).data,
                status=status.HTTP_201_CREATED
            )
            
        except Exception as e:
            logger.error(f"Error creating role: {str(e)}")
            return Response(
                {'error': 'Failed to create role'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'])
    def system_health(self, request):
        """Get comprehensive system health metrics"""
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            
            # Memory metrics
            memory = psutil.virtual_memory()
            
            # Disk metrics
            disk = psutil.disk_usage('/')
            
            # Network metrics (if available)
            try:
                network = psutil.net_io_counters()
                network_stats = {
                    'bytes_sent': network.bytes_sent,
                    'bytes_recv': network.bytes_recv,
                    'packets_sent': network.packets_sent,
                    'packets_recv': network.packets_recv
                }
            except:
                network_stats = None
            
            health_data = {
                'cpu': {
                    'usage_percent': cpu_percent,
                    'count': cpu_count,
                    'status': 'healthy' if cpu_percent < 70 else 'warning' if cpu_percent < 90 else 'critical'
                },
                'memory': {
                    'total': memory.total,
                    'available': memory.available,
                    'percent': memory.percent,
                    'used': memory.used,
                    'status': 'healthy' if memory.percent < 70 else 'warning' if memory.percent < 90 else 'critical'
                },
                'disk': {
                    'total': disk.total,
                    'used': disk.used,
                    'free': disk.free,
                    'percent': (disk.used / disk.total) * 100,
                    'status': 'healthy' if (disk.used / disk.total) < 0.8 else 'warning' if (disk.used / disk.total) < 0.95 else 'critical'
                },
                'network': network_stats,
                'overall_status': 'healthy',  # This would be calculated based on all metrics
                'timestamp': timezone.now().isoformat()
            }
            
            log_super_admin_action(request.user, 'system_health_checked')
            return Response(health_data)
            
        except Exception as e:
            logger.error(f"Error fetching system health: {str(e)}")
            return Response(
                {'error': 'Failed to fetch system health'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def ai_configuration(self, request):
        """Get current AI configuration"""
        try:
            gemini_config = get_gemini_config()

            # Get all AI configurations from database
            ai_configs = AIConfiguration.objects.filter(provider='gemini', is_active=True)

            config_data = {
                'current_status': gemini_config.get_status(),
                'configurations': []
            }

            for config in ai_configs:
                config_data['configurations'].append({
                    'key': config.key,
                    'value': config.get_display_value(),
                    'config_type': config.config_type,
                    'description': config.description,
                    'is_sensitive': config.is_sensitive,
                    'updated_at': config.updated_at.isoformat()
                })

            log_super_admin_action(request.user, 'ai_configuration_viewed')
            return Response(config_data)

        except Exception as e:
            logger.error(f"Error fetching AI configuration: {str(e)}")
            return Response(
                {'error': 'Failed to fetch AI configuration'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    def update_ai_config(self, request):
        """Update AI configuration"""
        try:
            config_updates = request.data.get('configurations', {})

            if not config_updates:
                return Response(
                    {'error': 'No configuration updates provided'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            updated_configs = []

            # Update each configuration
            for key, value in config_updates.items():
                if key == 'api_key' and value:
                    # Special handling for API key
                    config = AIConfiguration.set_config(
                        provider='gemini',
                        key='api_key',
                        value=value,
                        config_type='api_key',
                        is_sensitive=True,
                        user=request.user,
                        description='Gemini API Key for AI services'
                    )
                    updated_configs.append('api_key')

                elif key == 'default_model' and value:
                    config = AIConfiguration.set_config(
                        provider='gemini',
                        key='default_model',
                        value=value,
                        config_type='model_config',
                        is_sensitive=False,
                        user=request.user,
                        description='Default Gemini model to use'
                    )
                    updated_configs.append('default_model')

                elif key == 'max_tokens' and value:
                    config = AIConfiguration.set_config(
                        provider='gemini',
                        key='max_tokens',
                        value=str(value),
                        config_type='model_config',
                        is_sensitive=False,
                        user=request.user,
                        description='Maximum tokens per request'
                    )
                    updated_configs.append('max_tokens')

                elif key == 'temperature' and value is not None:
                    config = AIConfiguration.set_config(
                        provider='gemini',
                        key='temperature',
                        value=str(value),
                        config_type='model_config',
                        is_sensitive=False,
                        user=request.user,
                        description='Model temperature (creativity level)'
                    )
                    updated_configs.append('temperature')

            # Reload the configuration to apply changes
            reload_gemini_config()

            # Get updated status
            gemini_config = get_gemini_config()
            updated_status = gemini_config.get_status()

            log_super_admin_action(
                request.user,
                'ai_configuration_updated',
                {'updated_configs': updated_configs}
            )

            return Response({
                'message': 'AI configuration updated successfully',
                'updated_configs': updated_configs,
                'status': updated_status
            })

        except Exception as e:
            logger.error(f"Error updating AI configuration: {str(e)}")
            return Response(
                {'error': 'Failed to update AI configuration'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    def test_ai_connection(self, request):
        """Test AI connection with current configuration"""
        try:
            gemini_config = get_gemini_config()

            if not gemini_config.is_available:
                return Response({
                    'success': False,
                    'error': 'Gemini AI is not available. Please check configuration.'
                })

            # Test with a simple prompt
            test_prompt = "Hello, this is a test message. Please respond with 'Test successful'."
            response = gemini_config.generate_content(test_prompt)

            if response:
                log_super_admin_action(request.user, 'ai_connection_tested', {'success': True})
                return Response({
                    'success': True,
                    'response': response,
                    'status': gemini_config.get_status()
                })
            else:
                return Response({
                    'success': False,
                    'error': 'No response received from Gemini AI'
                })

        except Exception as e:
            logger.error(f"Error testing AI connection: {str(e)}")
            log_super_admin_action(request.user, 'ai_connection_tested', {'success': False, 'error': str(e)})
            return Response({
                'success': False,
                'error': f'AI connection test failed: {str(e)}'
            })

    # ==================== ENHANCED SYSTEM MANAGEMENT ====================

    @action(detail=False, methods=['get'])
    def database_schema(self, request):
        """Get database schema information"""
        try:
            with connection.cursor() as cursor:
                # Get all tables
                cursor.execute("""
                    SELECT table_name, table_rows, data_length, index_length
                    FROM information_schema.tables
                    WHERE table_schema = DATABASE()
                """)
                tables = cursor.fetchall()

                schema_info = {
                    'total_tables': len(tables),
                    'tables': [
                        {
                            'name': table[0],
                            'rows': table[1] or 0,
                            'data_size': table[2] or 0,
                            'index_size': table[3] or 0
                        }
                        for table in tables
                    ],
                    'database_size': sum(table[2] or 0 for table in tables),
                    'total_rows': sum(table[1] or 0 for table in tables)
                }

                log_super_admin_action(request.user, 'database_schema_viewed')
                return Response(schema_info)

        except Exception as e:
            logger.error(f"Error fetching database schema: {str(e)}")
            return Response(
                {'error': 'Failed to fetch database schema'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    def run_migration(self, request):
        """Run database migrations"""
        try:
            app_name = request.data.get('app_name', '')
            migration_name = request.data.get('migration_name', '')

            if app_name:
                call_command('migrate', app_name, migration_name if migration_name else None)
            else:
                call_command('migrate')

            log_super_admin_action(
                request.user,
                'migration_executed',
                {'app_name': app_name, 'migration_name': migration_name}
            )

            return Response({'message': 'Migration completed successfully'})

        except Exception as e:
            logger.error(f"Error running migration: {str(e)}")
            return Response(
                {'error': f'Migration failed: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def environment_variables(self, request):
        """Get system environment variables (filtered for security)"""
        try:
            # Only show non-sensitive environment variables
            safe_vars = {}
            sensitive_keys = ['SECRET', 'KEY', 'PASSWORD', 'TOKEN', 'API']

            for key, value in os.environ.items():
                if not any(sensitive in key.upper() for sensitive in sensitive_keys):
                    safe_vars[key] = value
                else:
                    safe_vars[key] = '***HIDDEN***'

            log_super_admin_action(request.user, 'environment_variables_viewed')
            return Response({'environment_variables': safe_vars})

        except Exception as e:
            logger.error(f"Error fetching environment variables: {str(e)}")
            return Response(
                {'error': 'Failed to fetch environment variables'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    def create_backup(self, request):
        """Create system backup"""
        try:
            backup_type = request.data.get('backup_type', 'full')  # full, database, files
            backup_name = f"backup_{timezone.now().strftime('%Y%m%d_%H%M%S')}"

            if backup_type in ['full', 'database']:
                # Create database backup
                call_command('dbbackup', '--output-filename', f'{backup_name}_db.sql')

            if backup_type in ['full', 'files']:
                # Create media files backup
                call_command('mediabackup', '--output-filename', f'{backup_name}_media.tar.gz')

            log_super_admin_action(
                request.user,
                'backup_created',
                {'backup_type': backup_type, 'backup_name': backup_name}
            )

            return Response({
                'message': 'Backup created successfully',
                'backup_name': backup_name,
                'backup_type': backup_type
            })

        except Exception as e:
            logger.error(f"Error creating backup: {str(e)}")
            return Response(
                {'error': f'Backup creation failed: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    # ==================== ADVANCED ANALYTICS ====================

    @action(detail=False, methods=['get'])
    def advanced_analytics(self, request):
        """Get advanced platform analytics"""
        try:
            # Time range for analytics
            days = int(request.query_params.get('days', 30))
            start_date = timezone.now() - timedelta(days=days)

            # User analytics
            user_growth = User.objects.filter(
                date_joined__gte=start_date
            ).extra(
                select={'day': 'date(date_joined)'}
            ).values('day').annotate(count=Count('id')).order_by('day')

            # Activity analytics
            active_users_by_day = User.objects.filter(
                last_login__gte=start_date
            ).extra(
                select={'day': 'date(last_login)'}
            ).values('day').annotate(count=Count('id')).order_by('day')

            # Role distribution analytics
            role_analytics = UserRoleAssignment.objects.filter(
                is_active=True,
                assigned_at__gte=start_date
            ).values('role__name').annotate(
                count=Count('id'),
                avg_duration=Avg('assigned_at')
            )

            analytics_data = {
                'user_growth': list(user_growth),
                'active_users': list(active_users_by_day),
                'role_distribution': list(role_analytics),
                'total_users': User.objects.count(),
                'active_users_count': User.objects.filter(is_active=True).count(),
                'period_days': days,
                'generated_at': timezone.now().isoformat()
            }

            log_super_admin_action(request.user, 'advanced_analytics_viewed')
            return Response(analytics_data)

        except Exception as e:
            logger.error(f"Error generating advanced analytics: {str(e)}")
            return Response(
                {'error': 'Failed to generate analytics'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    # ==================== BULK USER OPERATIONS ====================

    @action(detail=False, methods=['post'])
    def bulk_user_operations(self, request):
        """Perform bulk operations on users"""
        try:
            operation = request.data.get('operation')
            user_ids = request.data.get('user_ids', [])

            if not operation or not user_ids:
                return Response(
                    {'error': 'Operation and user_ids are required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            users = User.objects.filter(id__in=user_ids)
            results = {'success': 0, 'failed': 0, 'details': []}

            for user in users:
                try:
                    if operation == 'activate':
                        user.is_active = True
                        user.save()
                        results['success'] += 1
                        results['details'].append(f"Activated user: {user.username}")

                    elif operation == 'deactivate':
                        user.is_active = False
                        user.save()
                        results['success'] += 1
                        results['details'].append(f"Deactivated user: {user.username}")

                    elif operation == 'assign_role':
                        role_name = request.data.get('role_name')
                        if role_name:
                            role = UserRole.objects.get(name=role_name)
                            profile, _ = UserProfile.objects.get_or_create(user=user)
                            UserRoleAssignment.objects.get_or_create(
                                user_profile=profile,
                                role=role,
                                defaults={
                                    'is_active': True,
                                    'is_approved': True,
                                    'assigned_by': request.user
                                }
                            )
                            results['success'] += 1
                            results['details'].append(f"Assigned role {role_name} to {user.username}")

                except Exception as e:
                    results['failed'] += 1
                    results['details'].append(f"Failed for {user.username}: {str(e)}")

            log_super_admin_action(
                request.user,
                'bulk_user_operation',
                {
                    'operation': operation,
                    'user_count': len(user_ids),
                    'success_count': results['success'],
                    'failed_count': results['failed']
                }
            )

            return Response(results)

        except Exception as e:
            logger.error(f"Error in bulk user operations: {str(e)}")
            return Response(
                {'error': f'Bulk operation failed: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    # ==================== SECURITY & COMPLIANCE ====================

    @action(detail=False, methods=['get'])
    def security_audit(self, request):
        """Generate security audit report"""
        try:
            # Failed login attempts
            failed_logins = User.objects.filter(
                last_login__isnull=True,
                date_joined__lt=timezone.now() - timedelta(days=30)
            ).count()

            # Users with multiple roles
            users_multiple_roles = UserProfile.objects.annotate(
                role_count=Count('roles')
            ).filter(role_count__gt=1).count()

            # Inactive users with roles
            inactive_users_with_roles = UserProfile.objects.filter(
                user__is_active=False,
                roles__isnull=False
            ).distinct().count()

            # Super admin count
            super_admin_count = UserRoleAssignment.objects.filter(
                role__name='super_admin',
                is_active=True
            ).count()

            audit_report = {
                'security_metrics': {
                    'failed_login_attempts': failed_logins,
                    'users_with_multiple_roles': users_multiple_roles,
                    'inactive_users_with_roles': inactive_users_with_roles,
                    'super_admin_count': super_admin_count,
                },
                'recommendations': [],
                'risk_level': 'low',
                'generated_at': timezone.now().isoformat()
            }

            # Generate recommendations
            if failed_logins > 100:
                audit_report['recommendations'].append("High number of failed login attempts detected")
                audit_report['risk_level'] = 'medium'

            if super_admin_count > 3:
                audit_report['recommendations'].append("Consider reducing number of Super Admin users")
                audit_report['risk_level'] = 'medium'

            if inactive_users_with_roles > 10:
                audit_report['recommendations'].append("Remove roles from inactive users")
                audit_report['risk_level'] = 'medium'

            log_super_admin_action(request.user, 'security_audit_generated')
            return Response(audit_report)

        except Exception as e:
            logger.error(f"Error generating security audit: {str(e)}")
            return Response(
                {'error': 'Failed to generate security audit'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def system_logs(self, request):
        """Get system logs with filtering"""
        try:
            log_level = request.query_params.get('level', 'INFO')
            days = int(request.query_params.get('days', 7))
            limit = int(request.query_params.get('limit', 100))

            # This is a simplified version - in production, you'd read from actual log files
            logs = {
                'logs': [
                    {
                        'timestamp': timezone.now().isoformat(),
                        'level': 'INFO',
                        'message': 'System startup completed',
                        'module': 'django.core'
                    },
                    {
                        'timestamp': (timezone.now() - timedelta(minutes=5)).isoformat(),
                        'level': 'WARNING',
                        'message': 'High memory usage detected',
                        'module': 'system.monitor'
                    }
                ],
                'total_count': 2,
                'filters': {
                    'level': log_level,
                    'days': days,
                    'limit': limit
                }
            }

            log_super_admin_action(request.user, 'system_logs_viewed')
            return Response(logs)

        except Exception as e:
            logger.error(f"Error fetching system logs: {str(e)}")
            return Response(
                {'error': 'Failed to fetch system logs'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
