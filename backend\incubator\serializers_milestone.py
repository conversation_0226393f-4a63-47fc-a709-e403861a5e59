from rest_framework import serializers
from django.contrib.auth.models import User
from .models_milestone import (
    BusinessMilestone, BusinessGoal, MentorRecommendation, BusinessAnalytics
)
from .models import BusinessIdea, MentorProfile
from users.serializers import UserSerializer

class BusinessMilestoneSerializer(serializers.ModelSerializer):
    created_by = UserSerializer(read_only=True)
    created_by_id = serializers.IntegerField(write_only=True)
    assigned_to = UserSerializer(read_only=True)
    assigned_to_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    business_idea_title = serializers.SerializerMethodField()
    status_display = serializers.SerializerMethodField()
    priority_display = serializers.SerializerMethodField()
    days_remaining = serializers.SerializerMethodField()
    is_overdue = serializers.SerializerMethodField()

    class Meta:
        model = BusinessMilestone
        fields = [
            'id', 'business_idea', 'business_idea_title', 'title', 'description',
            'due_date', 'status', 'status_display', 'priority', 'priority_display',
            'created_by', 'created_by_id', 'assigned_to', 'assigned_to_id',
            'completion_date', 'completion_notes', 'days_remaining', 'is_overdue',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def get_business_idea_title(self, obj):
        return obj.business_idea.title

    def get_status_display(self, obj):
        return dict(BusinessMilestone.STATUS_CHOICES).get(obj.status, obj.status)

    def get_priority_display(self, obj):
        return dict(BusinessMilestone.PRIORITY_CHOICES).get(obj.priority, obj.priority)
    
    def get_days_remaining(self, obj):
        from django.utils import timezone
        import datetime
        
        if obj.status == 'completed':
            return 0
        
        today = timezone.now().date()
        return (obj.due_date - today).days
    
    def get_is_overdue(self, obj):
        from django.utils import timezone
        
        if obj.status == 'completed':
            return False
        
        today = timezone.now().date()
        return obj.due_date < today


class BusinessGoalSerializer(serializers.ModelSerializer):
    created_by = UserSerializer(read_only=True)
    created_by_id = serializers.IntegerField(write_only=True)
    business_idea_title = serializers.SerializerMethodField()
    status_display = serializers.SerializerMethodField()
    timeframe_display = serializers.SerializerMethodField()
    progress_percentage = serializers.SerializerMethodField()

    class Meta:
        model = BusinessGoal
        fields = [
            'id', 'business_idea', 'business_idea_title', 'title', 'description',
            'timeframe', 'timeframe_display', 'target_date', 'status', 'status_display',
            'created_by', 'created_by_id', 'achievement_date', 'achievement_notes',
            'progress_percentage', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def get_business_idea_title(self, obj):
        return obj.business_idea.title

    def get_status_display(self, obj):
        return dict(BusinessGoal.STATUS_CHOICES).get(obj.status, obj.status)

    def get_timeframe_display(self, obj):
        return dict(BusinessGoal.TIMEFRAME_CHOICES).get(obj.timeframe, obj.timeframe)
    
    def get_progress_percentage(self, obj):
        # Calculate progress based on related milestones
        milestones = BusinessMilestone.objects.filter(business_idea=obj.business_idea)
        if not milestones.exists():
            return 0
        
        completed = milestones.filter(status='completed').count()
        total = milestones.count()
        
        return int((completed / total) * 100) if total > 0 else 0


class MentorRecommendationSerializer(serializers.ModelSerializer):
    mentor = serializers.SerializerMethodField()
    business_idea_title = serializers.SerializerMethodField()

    class Meta:
        model = MentorRecommendation
        fields = [
            'id', 'business_idea', 'business_idea_title', 'mentor',
            'match_score', 'match_reason', 'expertise_match',
            'is_applied', 'is_matched', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'match_score', 'match_reason', 'expertise_match', 'created_at', 'updated_at']

    def get_business_idea_title(self, obj):
        return obj.business_idea.title
    
    def get_mentor(self, obj):
        from .serializers import MentorProfileSerializer
        return MentorProfileSerializer(obj.mentor).data


class BusinessAnalyticsSerializer(serializers.ModelSerializer):
    business_idea_title = serializers.SerializerMethodField()
    similar_ideas = serializers.SerializerMethodField()

    class Meta:
        model = BusinessAnalytics
        fields = [
            'id', 'business_idea', 'business_idea_title', 'progress_rate',
            'milestone_completion_rate', 'goal_achievement_rate', 'team_size',
            'mentor_engagement', 'industry_percentile', 'stage_percentile',
            'similar_ideas', 'last_calculated'
        ]
        read_only_fields = ['id', 'progress_rate', 'milestone_completion_rate', 'goal_achievement_rate',
                           'team_size', 'mentor_engagement', 'industry_percentile', 'stage_percentile',
                           'last_calculated']

    def get_business_idea_title(self, obj):
        return obj.business_idea.title
    
    def get_similar_ideas(self, obj):
        # Get similar business ideas based on tags and stage
        from .serializers import BusinessIdeaSerializer
        
        business_idea = obj.business_idea
        tags = business_idea.tags.all()
        
        similar_ideas = BusinessIdea.objects.filter(
            current_stage=business_idea.current_stage,
            tags__in=tags,
            moderation_status='approved'
        ).exclude(
            id=business_idea.id
        ).distinct()[:5]
        
        return BusinessIdeaSerializer(similar_ideas, many=True).data
