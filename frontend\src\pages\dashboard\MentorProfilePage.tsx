import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '../../components/ui/card';
import Button from '../../components/ui/Button';
import { Badge } from '../../components/ui/badge';
import { Input } from '../../components/ui/input';
import { Textarea } from '../../components/ui/textarea';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '../../components/ui/tabs';
import { User, Edit, Save, X, Star, Award, Calendar, Clock, Globe, Linkedin } from 'lucide-react';

interface MentorProfile {
  personalInfo: {
    name: string;
    title: string;
    company: string;
    email: string;
    phone: string;
    location: string;
    bio: string;
    profileImage?: string;
    linkedinUrl?: string;
    websiteUrl?: string;
  };
  expertise: {
    industries: string[];
    skills: string[];
    yearsOfExperience: number;
    specializations: string[];
  };
  mentoring: {
    totalMentees: number;
    activeMentees: number;
    completedMentorships: number;
    averageRating: number;
    totalSessions: number;
    isAcceptingMentees: boolean;
    maxMentees: number;
    hourlyRate?: number;
    availability: string[];
    preferredSessionTypes: string[];
  };
  achievements: {
    certifications: string[];
    awards: string[];
    publications: string[];
    speakingEngagements: string[];
  };
}

const MentorProfilePage: React.FC = () => {
  const [profile, setProfile] = useState<MentorProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [editing, setEditing] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  // Mock data - replace with actual API call
  useEffect(() => {
    const mockProfile: MentorProfile = {
      personalInfo: {
        name: 'Dr. Sarah Johnson',
        title: 'Senior Product Manager',
        company: 'TechCorp Inc.',
        email: '<EMAIL>',
        phone: '+****************',
        location: 'San Francisco, CA',
        bio: 'Experienced product manager with 12+ years in tech startups and enterprise software. Passionate about helping entrepreneurs build successful products and scale their businesses.',
        linkedinUrl: 'https://linkedin.com/in/sarahjohnson',
        websiteUrl: 'https://sarahjohnson.com'
      },
      expertise: {
        industries: ['Technology', 'SaaS', 'E-commerce', 'FinTech'],
        skills: ['Product Management', 'Go-to-Market Strategy', 'User Experience', 'Data Analytics', 'Team Leadership'],
        yearsOfExperience: 12,
        specializations: ['B2B SaaS', 'Mobile Apps', 'AI/ML Products', 'Marketplace Platforms']
      },
      mentoring: {
        totalMentees: 47,
        activeMentees: 8,
        completedMentorships: 39,
        averageRating: 4.8,
        totalSessions: 312,
        isAcceptingMentees: true,
        maxMentees: 10,
        hourlyRate: 150,
        availability: ['Weekdays 9AM-5PM PST', 'Saturday mornings'],
        preferredSessionTypes: ['Video Call', 'In-Person (SF Bay Area)', 'Phone Call']
      },
      achievements: {
        certifications: ['Certified Product Manager (CPM)', 'Agile Certified Practitioner', 'Google Analytics Certified'],
        awards: ['Top Mentor 2023', 'Product Excellence Award 2022', 'Innovation Leadership Award 2021'],
        publications: ['Building Successful SaaS Products', 'The Product Manager\'s Guide to User Research'],
        speakingEngagements: ['ProductCon 2023', 'SaaS Summit 2022', 'Startup Grind SF']
      }
    };

    setTimeout(() => {
      setProfile(mockProfile);
      setLoading(false);
    }, 1000);
  }, []);

  const handleSave = () => {
    // Save profile changes
    setEditing(false);
    // API call would go here
  };

  const handleCancel = () => {
    setEditing(false);
    // Reset changes
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <span key={i} className={`text-lg ${i < rating ? 'text-yellow-400' : 'text-gray-300'}`}>
        ★
      </span>
    ));
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!profile) return null;

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Mentor Profile</h1>
          <p className="text-gray-600 mt-1">Manage your mentor profile and expertise</p>
        </div>
        <div className="flex gap-2">
          {editing ? (
            <>
              <Button variant="outline" onClick={handleCancel}>
                <X className="w-4 h-4 mr-2" />
                Cancel
              </Button>
              <Button onClick={handleSave} className="bg-green-600 hover:bg-green-700">
                <Save className="w-4 h-4 mr-2" />
                Save Changes
              </Button>
            </>
          ) : (
            <Button onClick={() => setEditing(true)} className="bg-blue-600 hover:bg-blue-700">
              <Edit className="w-4 h-4 mr-2" />
              Edit Profile
            </Button>
          )}
        </div>
      </div>

      {/* Profile Header Card */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-start gap-6">
            <div className="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center">
              <User className="w-12 h-12 text-gray-400" />
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2">
                <h2 className="text-2xl font-bold">{profile.personalInfo.name}</h2>
                <Badge className="bg-green-100 text-green-800">
                  {profile.mentoring.isAcceptingMentees ? 'Accepting Mentees' : 'Not Available'}
                </Badge>
              </div>
              <p className="text-lg text-gray-600 mb-1">{profile.personalInfo.title}</p>
              <p className="text-gray-600 mb-3">{profile.personalInfo.company}</p>
              <p className="text-gray-700 mb-4">{profile.personalInfo.bio}</p>
              <div className="flex items-center gap-4">
                {profile.personalInfo.linkedinUrl && (
                  <a href={profile.personalInfo.linkedinUrl} className="flex items-center gap-1 text-blue-600 hover:underline">
                    <Linkedin className="w-4 h-4" />
                    LinkedIn
                  </a>
                )}
                {profile.personalInfo.websiteUrl && (
                  <a href={profile.personalInfo.websiteUrl} className="flex items-center gap-1 text-blue-600 hover:underline">
                    <Globe className="w-4 h-4" />
                    Website
                  </a>
                )}
              </div>
            </div>
            <div className="text-right">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-600">Total Mentees</p>
                  <p className="text-xl font-bold">{profile.mentoring.totalMentees}</p>
                </div>
                <div>
                  <p className="text-gray-600">Avg Rating</p>
                  <div className="flex items-center">
                    <span className="text-xl font-bold mr-1">{profile.mentoring.averageRating}</span>
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Profile Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="expertise">Expertise</TabsTrigger>
          <TabsTrigger value="mentoring">Mentoring</TabsTrigger>
          <TabsTrigger value="achievements">Achievements</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Personal Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="w-5 h-5" />
                  Personal Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {editing ? (
                  <>
                    <div>
                      <label className="block text-sm font-medium mb-1">Name</label>
                      <Input value={profile.personalInfo.name} />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Title</label>
                      <Input value={profile.personalInfo.title} />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Company</label>
                      <Input value={profile.personalInfo.company} />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Email</label>
                      <Input value={profile.personalInfo.email} />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Phone</label>
                      <Input value={profile.personalInfo.phone} />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Location</label>
                      <Input value={profile.personalInfo.location} />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Bio</label>
                      <Textarea value={profile.personalInfo.bio} rows={3} />
                    </div>
                  </>
                ) : (
                  <>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Email:</span>
                      <span>{profile.personalInfo.email}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Phone:</span>
                      <span>{profile.personalInfo.phone}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Location:</span>
                      <span>{profile.personalInfo.location}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Company:</span>
                      <span>{profile.personalInfo.company}</span>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Mentoring Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Star className="w-5 h-5" />
                  Mentoring Statistics
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-600">Years of Experience:</span>
                  <span className="font-semibold">{profile.expertise.yearsOfExperience} years</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Mentees:</span>
                  <span className="font-semibold">{profile.mentoring.totalMentees}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Active Mentees:</span>
                  <span className="font-semibold">{profile.mentoring.activeMentees}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Completed Mentorships:</span>
                  <span className="font-semibold">{profile.mentoring.completedMentorships}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Sessions:</span>
                  <span className="font-semibold">{profile.mentoring.totalSessions}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Average Rating:</span>
                  <div className="flex items-center">
                    <span className="font-semibold mr-2">{profile.mentoring.averageRating}</span>
                    <div className="flex">{renderStars(Math.floor(profile.mentoring.averageRating))}</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Button variant="outline" className="h-20 flex flex-col">
                  <Calendar className="w-6 h-6 mb-2" />
                  <span>Schedule Session</span>
                </Button>
                <Button variant="outline" className="h-20 flex flex-col">
                  <User className="w-6 h-6 mb-2" />
                  <span>View Mentees</span>
                </Button>
                <Button variant="outline" className="h-20 flex flex-col">
                  <Clock className="w-6 h-6 mb-2" />
                  <span>Set Availability</span>
                </Button>
                <Button variant="outline" className="h-20 flex flex-col">
                  <Award className="w-6 h-6 mb-2" />
                  <span>View Analytics</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="expertise" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Industries</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {profile.expertise.industries.map((industry, index) => (
                    <Badge key={index} variant="outline">
                      {industry}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Skills</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {profile.expertise.skills.map((skill, index) => (
                    <Badge key={index} variant="secondary">
                      {skill}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Specializations</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {profile.expertise.specializations.map((spec, index) => (
                    <Badge key={index} className="bg-blue-100 text-blue-800">
                      {spec}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Experience</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <p className="text-3xl font-bold text-blue-600">{profile.expertise.yearsOfExperience}</p>
                  <p className="text-gray-600">Years of Experience</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="mentoring" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Availability</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {profile.mentoring.availability.map((time, index) => (
                    <div key={index} className="p-2 border rounded">
                      {time}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Session Preferences</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {profile.mentoring.preferredSessionTypes.map((type, index) => (
                    <div key={index} className="p-2 border rounded">
                      {type}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Mentoring Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-600">Accepting New Mentees:</span>
                  <Badge className={profile.mentoring.isAcceptingMentees ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                    {profile.mentoring.isAcceptingMentees ? 'Yes' : 'No'}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Max Mentees:</span>
                  <span className="font-semibold">{profile.mentoring.maxMentees}</span>
                </div>
                {profile.mentoring.hourlyRate && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Hourly Rate:</span>
                    <span className="font-semibold">${profile.mentoring.hourlyRate}/hour</span>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Performance Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div>
                    <p className="text-2xl font-bold text-green-600">{profile.mentoring.completedMentorships}</p>
                    <p className="text-sm text-gray-600">Completed</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-blue-600">{profile.mentoring.totalSessions}</p>
                    <p className="text-sm text-gray-600">Total Sessions</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="achievements" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="w-5 h-5" />
                  Certifications
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {profile.achievements.certifications.map((cert, index) => (
                    <div key={index} className="p-2 border rounded">
                      {cert}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Awards</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {profile.achievements.awards.map((award, index) => (
                    <div key={index} className="p-2 border rounded">
                      {award}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Publications</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {profile.achievements.publications.map((pub, index) => (
                    <div key={index} className="p-2 border rounded">
                      {pub}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Speaking Engagements</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {profile.achievements.speakingEngagements.map((engagement, index) => (
                    <div key={index} className="p-2 border rounded">
                      {engagement}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default MentorProfilePage;
