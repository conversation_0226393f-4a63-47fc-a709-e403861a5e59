import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>U<PERSON>, Users } from 'lucide-react';

import { useTranslation } from 'react-i18next';
import { useLanguageDirection } from '../../../../../utils/rtl';
interface Tab {
  id: string;
  label: string;
  icon: React.ReactNode;
}

interface AnalyticsTabsProps {
  activeTab: string;
  onTabChange: (tabId: string) => void;
}

const AnalyticsTabs: React.FC<AnalyticsTabsProps> = ({ activeTab, onTabChange  }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const tabs: Tab[] = [
    { id: 'overview', label: t("admin.overview", t("dashboard.overview", "Overview")), icon: <BarChart size={18} /> },
    { id: 'activity', label: t("admin.activity", "Activity"), icon: <LineChart size={18} /> },
    { id: 'contributors', label: t("admin.contributors", "Contributors"), icon: <Users size={18} /> },
    { id: 'popular', label: t("admin.popular.threads", "Popular Threads"), icon: <TrendingUp size={18} /> },
    { id: 'engagement', label: t("admin.engagement", "Engagement"), icon: <PieChart size={18} /> }
  ];

  return (
    <div className="mb-6">
      <div className={`flex flex-wrap gap-2 ${isRTL ? "flex-row-reverse" : ""}`}>
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={`px-4 py-2 rounded-lg flex items-center ${
              activeTab === tab.id
                ? 'bg-indigo-700 text-white'
                : 'bg-indigo-900/30 text-gray-300 hover:bg-indigo-800/50'}
            }`}
          >
            {tab.icon}
            <span className={`ml-2 ${isRTL ? "space-x-reverse" : ""}`}>{tab.label}</span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default AnalyticsTabs;
