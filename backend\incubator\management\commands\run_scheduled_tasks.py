"""
Management command to run scheduled tasks for the incubator app.
"""
import logging
from django.core.management.base import BaseCommand
from incubator.tasks import send_session_reminders, send_feedback_requests, clean_up_old_sessions

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Run scheduled tasks for the incubator app'

    def handle(self, *args, **options):
        """Run all scheduled tasks"""
        self.stdout.write(self.style.SUCCESS('Starting scheduled tasks...'))
        
        # Send session reminders
        self.stdout.write('Sending session reminders...')
        send_session_reminders()
        
        # Send feedback requests
        self.stdout.write('Sending feedback requests...')
        send_feedback_requests()
        
        # Clean up old sessions
        self.stdout.write('Cleaning up old sessions...')
        clean_up_old_sessions()
        
        self.stdout.write(self.style.SUCCESS('All scheduled tasks completed successfully!'))
