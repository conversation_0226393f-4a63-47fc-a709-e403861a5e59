import React from 'react';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../../store/hooks';
import { RTLText } from '../common';
const ChatCapabilities: React.FC = () => {
  const { t } = useTranslation();
  const { language } = useAppSelector(state => state.language);

  return (
    <div className="mt-8 bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50">
      <RTLText as="h3" className="text-lg font-medium mb-2">{t('chat.capabilities')}</RTLText>
      <ul className={`${language === 'ar' ? 'list-inside' : 'list-disc list-inside'} space-y-1 text-gray-300`}>
        <li><RTLText>{t('chat.businessPlanning')}</RTLText></li>
        <li><RTLText>{t('chat.marketAnalysis')}</RTLText></li>
        <li><RTLText>{t('chat.dataScience')}</RTLText></li>
        <li><RTLText>{t('chat.technicalExplanations')}</RTLText></li>
        <li><RTLText>{t('chat.creativeIdeas')}</RTLText></li>
        <li><RTLText>{t('chat.generalKnowledge')}</RTLText></li>
      </ul>
    </div>
  );
};

export default ChatCapabilities;
