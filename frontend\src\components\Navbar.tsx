import React, { useState, useEffect } from 'react';
import { Menu, X, LogIn, UserCircle, LogOut, LayoutDashboard, Home, Lightbulb, Users, DollarSign, BookOpen, Sparkles, Settings } from 'lucide-react';
import { useNavigate, Link } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { logout } from '../store/authSlice';
import { useTranslation } from 'react-i18next';
import { LanguageSwitcher } from './common';
import { RTLIcon, RTLFlex, RTLContainer } from './rtl';
import { useLanguage } from '../hooks/useLanguage';

const Navbar = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { user, isAuthenticated } = useAppSelector(state => state.auth);
  const { isRTL } = useLanguage();

  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  // Check if user is admin based on the is_admin field from the API
  const isAdmin = user?.is_admin === true;

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  const handleLogout = async () => {
    try {
      console.log('Navbar: Starting logout...');
      await dispatch(logout()).unwrap();
      console.log('Navbar: Logout successful, redirecting...');

      // Clear any remaining storage
      localStorage.clear();
      sessionStorage.clear();

      // Redirect to home page using React Router
      navigate('/', { replace: true });
    } catch (error) {
      console.error('Logout failed:', error);
      // Even if logout fails, clear local state and redirect
      localStorage.clear();
      sessionStorage.clear();
      navigate('/', { replace: true });
    }
  };

  return (
    <>
      <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 glass-light ${isScrolled ? 'backdrop-blur-xl shadow-xl border-b border-white/20' : 'bg-transparent'}`}>
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            {/* Logo */}
            <div className="flex items-center">
              <Link to="/" className="flex items-center group">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <Sparkles className="w-6 h-6 text-white" />
                  </div>
                  <div className="flex flex-col">
                    <span className="text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-blue-400">
                      {t('app.name')}
                    </span>
                    <span className="text-xs text-glass-secondary font-medium">
                      {t('app.tagline')}
                    </span>
                  </div>
                </div>
              </Link>
            </div>

            {/* Desktop Navigation */}
            <RTLFlex className="hidden lg:flex items-center" gap={2}>
              <Link
                to="/"
                className="px-4 py-2 rounded-lg font-medium transition-all duration-300 text-glass-primary hover:text-purple-300 hover:bg-white/10 hover:scale-105"
              >
                {t('nav.home')}
              </Link>

              {/* Incubator Dropdown */}
              <div className="relative group">
                <Link
                  to="/incubator"
                  className="flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-300 text-glass-primary hover:text-purple-300 hover:bg-white/10 hover:scale-105"
                >
                  <Lightbulb size={16} />
                  {t('nav.incubator')}
                </Link>

                {/* Dropdown Menu */}
                <div className="absolute top-full left-0 mt-2 w-64 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                  <div className="glass-light rounded-xl p-4 border border-white/20 shadow-xl backdrop-blur-xl">
                    <div className="space-y-2">
                      <Link
                        to="/incubator/ideas"
                        className="flex items-center gap-3 p-3 rounded-lg hover:bg-white/10 transition-colors"
                      >
                        <Lightbulb size={16} className="text-purple-400" />
                        <div>
                          <div className="font-medium text-glass-primary">Business Ideas</div>
                          <div className="text-xs text-glass-secondary">Validate & develop ideas</div>
                        </div>
                      </Link>
                      <Link
                        to="/mentorship"
                        className="flex items-center gap-3 p-3 rounded-lg hover:bg-white/10 transition-colors"
                      >
                        <Users size={16} className="text-blue-400" />
                        <div>
                          <div className="font-medium text-glass-primary">Mentorship</div>
                          <div className="text-xs text-glass-secondary">Expert guidance</div>
                        </div>
                      </Link>
                      <Link
                        to="/funding"
                        className="flex items-center gap-3 p-3 rounded-lg hover:bg-white/10 transition-colors"
                      >
                        <DollarSign size={16} className="text-green-400" />
                        <div>
                          <div className="font-medium text-glass-primary">Funding</div>
                          <div className="text-xs text-glass-secondary">Investment opportunities</div>
                        </div>
                      </Link>
                    </div>
                  </div>
                </div>
              </div>

              <Link
                to="/resources"
                className="flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-300 text-glass-primary hover:text-purple-300 hover:bg-white/10 hover:scale-105"
              >
                <BookOpen size={16} />
                {t('nav.resources')}
              </Link>

              <Link
                to="/forum"
                className="flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-300 text-glass-primary hover:text-purple-300 hover:bg-white/10 hover:scale-105"
              >
                <Users size={16} />
                {t('nav.forums')}
              </Link>

              <RTLFlex className="items-center" gap={4}>
                <LanguageSwitcher variant="icon" />

                {isAuthenticated ? (
                  <RTLFlex className="items-center" gap={4}>
                    <Link
                      to="/profile"
                      className="flex items-center text-sm hover:text-purple-300 transition-colors gap-2"
                    >
                      <RTLIcon
                        icon={UserCircle}
                        size={20}
                        className="text-purple-400"
                      />
                      <span className="font-medium text-glass-primary">
                        {user?.username}
                      </span>
                    </Link>
                    {!isAdmin && (
                      <Link
                        to="/dashboard"
                        className="flex items-center text-sm transition-colors text-purple-400 hover:text-purple-300 gap-1"
                      >
                        <RTLIcon
                          icon={Home}
                          size={18}
                          className="text-purple-400"
                        />
                        <span>{t('nav.myDashboard')}</span>
                      </Link>
                    )}
                    {isAdmin && (
                      <Link
                        to="/admin"
                        className="flex items-center text-sm transition-colors text-red-400 hover:text-red-300 gap-1"
                      >
                        <RTLIcon
                          icon={LayoutDashboard}
                          size={18}
                          className="text-red-400"
                        />
                        <span>{t('nav.adminDashboard')}</span>
                      </Link>
                    )}
                    <Link
                      to="/settings"
                      className="flex items-center text-sm transition-colors text-gray-400 hover:text-purple-300 gap-1"
                    >
                      <RTLIcon
                        icon={Settings}
                        size={18}
                        className="text-gray-400"
                      />
                      <span>{t('nav.settings', 'Settings')}</span>
                    </Link>
                    <button
                      onClick={handleLogout}
                      className="flex items-center text-sm transition-colors text-glass-secondary hover:text-purple-300 gap-1"
                    >
                      <RTLIcon
                        icon={LogOut}
                        size={18}
                        className="text-glass-secondary"
                      />
                      <span>{t('nav.logout')}</span>
                    </button>
                  </RTLFlex>
                ) : (
                  <RTLFlex className="items-center" gap={3}>
                    <Link
                      to="/login"
                      className="flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 hover:scale-105 text-glass-primary hover:text-white hover:bg-white/10 border border-white/20 hover:border-purple-500/50 gap-2"
                    >
                      <RTLIcon
                        icon={LogIn}
                        size={18}
                        flipInRTL={true}
                        className="text-glass-secondary"
                      />
                      <span>{t('nav.login')}</span>
                    </Link>
                    <Link
                      to="/register"
                      className="px-6 py-2 rounded-lg text-sm font-semibold hover:shadow-lg hover:shadow-purple-500/25 transition-all duration-300 text-white hover:scale-105 transform bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600"
                    >
                      {t('nav.register')}
                    </Link>
                  </RTLFlex>
                )}
              </RTLFlex>
            </RTLFlex>

            {/* Mobile Menu Button */}
            <div className="lg:hidden flex items-center">
              <button
                onClick={toggleMenu}
                className="p-3 rounded-xl transition-all duration-200 hover:scale-105 hover:bg-white/10 text-glass-primary"
                aria-label="Toggle menu"
              >
                {isOpen ? <X size={24} /> : <Menu size={24} />}
              </button>
            </div>
          </div>

          {/* Mobile Navigation */}
          {isOpen && (
            <RTLContainer className="lg:hidden mt-6 pb-6 space-y-3 animate-fadeIn border-t border-gray-200/20 pt-6">
              <Link
                to="/"
                className="flex items-center gap-3 py-3 px-4 rounded-lg transition-all duration-300 hover:text-purple-300 text-glass-primary hover:bg-white/10 hover:scale-105"
                onClick={toggleMenu}
              >
                <Home size={18} />
                {t('nav.home')}
              </Link>

              {/* Incubator Section */}
              <div className="space-y-2">
                <Link
                  to="/incubator"
                  className="flex items-center gap-3 py-3 px-4 rounded-lg transition-all duration-300 hover:text-purple-300 text-glass-primary hover:bg-white/10 hover:scale-105"
                  onClick={toggleMenu}
                >
                  <Lightbulb size={18} />
                  {t('nav.incubator')}
                </Link>

                {/* Incubator Sub-items */}
                <div className="ml-6 space-y-1">
                  <Link
                    to="/incubator/ideas"
                    className="flex items-center gap-3 py-2 px-4 rounded-lg transition-colors text-glass-secondary hover:text-purple-300 hover:bg-white/5 text-sm"
                    onClick={toggleMenu}
                  >
                    <Lightbulb size={16} />
                    Business Ideas
                  </Link>
                  <Link
                    to="/mentorship"
                    className="flex items-center gap-3 py-2 px-4 rounded-lg transition-colors text-glass-secondary hover:text-purple-300 hover:bg-white/5 text-sm"
                    onClick={toggleMenu}
                  >
                    <Users size={16} />
                    Mentorship
                  </Link>
                  <Link
                    to="/funding"
                    className="flex items-center gap-3 py-2 px-4 rounded-lg transition-colors text-glass-secondary hover:text-purple-300 hover:bg-white/5 text-sm"
                    onClick={toggleMenu}
                  >
                    <DollarSign size={16} />
                    Funding
                  </Link>
                </div>
              </div>

              <Link
                to="/resources"
                className="flex items-center gap-3 py-3 px-4 rounded-lg transition-all duration-300 hover:text-purple-300 text-glass-primary hover:bg-white/10 hover:scale-105"
                onClick={toggleMenu}
              >
                <BookOpen size={18} />
                {t('nav.resources')}
              </Link>

              <Link
                to="/forum"
                className="flex items-center gap-3 py-3 px-4 rounded-lg transition-all duration-300 hover:text-purple-300 text-glass-primary hover:bg-white/10 hover:scale-105"
                onClick={toggleMenu}
              >
                <Users size={18} />
                {t('nav.forums')}
              </Link>

              <RTLFlex className="flex-col pt-2" gap={4}>
                <RTLFlex gap={4}>
                  <LanguageSwitcher variant="dropdown" />
                </RTLFlex>

                {isAuthenticated ? (
                  <RTLFlex className="flex-col pt-2 border-t border-white/20" gap={2}>
                    <Link
                      to="/profile"
                      className="flex items-center py-2 hover:text-purple-300 transition-colors gap-2"
                      onClick={toggleMenu}
                    >
                      <RTLIcon
                        icon={UserCircle}
                        size={20}
                        className="text-purple-400"
                      />
                      <span className="font-medium text-glass-primary">
                        {user?.username}
                      </span>
                    </Link>
                    {!isAdmin && (
                      <Link
                        to="/dashboard"
                        className="flex items-center py-2 transition-colors text-purple-400 hover:text-purple-300 gap-2"
                        onClick={toggleMenu}
                      >
                        <RTLIcon
                          icon={Home}
                          size={20}
                          className="text-purple-400"
                        />
                        <span>{t('nav.myDashboard')}</span>
                      </Link>
                    )}
                    {isAdmin && (
                      <Link
                        to="/admin"
                        className="flex items-center py-2 transition-colors text-red-400 hover:text-red-300 gap-2"
                        onClick={toggleMenu}
                      >
                        <RTLIcon
                          icon={LayoutDashboard}
                          size={20}
                          className="text-red-400"
                        />
                        <span>{t('nav.adminDashboard')}</span>
                      </Link>
                    )}
                    <Link
                      to="/settings"
                      className="flex items-center py-2 transition-colors text-gray-400 hover:text-purple-300 gap-2"
                      onClick={toggleMenu}
                    >
                      <RTLIcon
                        icon={Settings}
                        size={20}
                        className="text-gray-400"
                      />
                      <span>{t('nav.settings', 'Settings')}</span>
                    </Link>
                    <button
                      onClick={handleLogout}
                      className="flex items-center py-2 transition-colors text-glass-secondary hover:text-purple-300 gap-2"
                    >
                      <RTLIcon
                        icon={LogOut}
                        size={20}
                        className="text-glass-secondary"
                      />
                      <span>{t('nav.logout')}</span>
                    </button>
                  </RTLFlex>
                ) : (
                  <RTLFlex className="flex-col pt-2 border-t border-white/20" gap={3}>
                    <Link
                      to="/login"
                      className="flex items-center py-3 px-4 rounded-lg transition-all duration-300 hover:scale-105 text-glass-primary hover:text-white hover:bg-white/10 border border-white/20 hover:border-purple-500/50 gap-2"
                      onClick={toggleMenu}
                    >
                      <RTLIcon icon={LogIn} size={20} flipInRTL={true} />
                      <span className="font-medium">{t('nav.login')}</span>
                    </Link>
                    <Link
                      to="/register"
                      className="flex items-center justify-center py-3 px-4 rounded-lg font-semibold hover:shadow-lg hover:shadow-purple-500/25 transition-all duration-300 text-white hover:scale-105 transform bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600"
                      onClick={toggleMenu}
                    >
                      {t('nav.register')}
                    </Link>
                  </RTLFlex>
                )}
              </RTLFlex>
            </RTLContainer>
          )}
        </div>
      </nav>
    </>
  );
};

export default Navbar;