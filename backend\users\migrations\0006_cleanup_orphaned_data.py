# Migration to clean up orphaned data before adding language field

from django.db import migrations


def cleanup_orphaned_data(apps, schema_editor):
    """Remove chat messages and AI recommendations that reference non-existent data"""
    try:
        # Use raw SQL to clean up orphaned records
        with schema_editor.connection.cursor() as cursor:
            # Check if the chat message table exists
            cursor.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='ai_recommendations_chatmessage';
            """)

            if cursor.fetchone():
                # Delete orphaned chat messages
                cursor.execute("""
                    DELETE FROM ai_recommendations_chatmessage
                    WHERE user_id NOT IN (SELECT id FROM auth_user);
                """)
                deleted_count = cursor.rowcount
                print(f"Deleted {deleted_count} orphaned chat messages.")
            else:
                print("ai_recommendations_chatmessage table not found.")

            # Check if the AI recommendation table exists
            cursor.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='ai_recommendations_airecommendation';
            """)

            if cursor.fetchone():
                # Delete orphaned AI recommendations
                cursor.execute("""
                    DELETE FROM ai_recommendations_airecommendation
                    WHERE business_idea_id NOT IN (SELECT id FROM incubator_businessidea);
                """)
                deleted_count = cursor.rowcount
                print(f"Deleted {deleted_count} orphaned AI recommendations.")
            else:
                print("ai_recommendations_airecommendation table not found.")

    except Exception as e:
        print(f"Error during cleanup: {e}")
        # Don't fail the migration if the cleanup fails
        pass


def reverse_cleanup(apps, schema_editor):
    """Reverse operation - no action needed"""
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0005_alter_userrole_name'),
    ]

    operations = [
        migrations.RunPython(
            cleanup_orphaned_data,
            reverse_cleanup,
        ),
    ]
