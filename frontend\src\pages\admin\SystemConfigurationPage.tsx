import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardH<PERSON>er, CardTitle } from '../../components/ui/card';
import { But<PERSON> } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Textarea } from '../../components/ui/textarea';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '../../components/ui/tabs';
import { Badge } from '../../components/ui/badge';
import { Settings, Save, RefreshCw, AlertTriangle, CheckCircle, Database, Mail, Shield, Globe } from 'lucide-react';

interface SystemConfig {
  general: {
    siteName: string;
    siteDescription: string;
    adminEmail: string;
    timezone: string;
    language: string;
    maintenanceMode: boolean;
    registrationEnabled: boolean;
    emailVerificationRequired: boolean;
  };
  database: {
    host: string;
    port: number;
    name: string;
    connectionPool: number;
    backupFrequency: string;
    lastBackup: string;
  };
  email: {
    provider: string;
    smtpHost: string;
    smtpPort: number;
    username: string;
    encryption: string;
    fromAddress: string;
    fromName: string;
  };
  security: {
    sessionTimeout: number;
    maxLoginAttempts: number;
    passwordMinLength: number;
    requireTwoFactor: boolean;
    allowedFileTypes: string[];
    maxFileSize: number;
  };
  api: {
    rateLimit: number;
    apiVersion: string;
    enableCors: boolean;
    allowedOrigins: string[];
    enableLogging: boolean;
  };
}

const SystemConfigurationPage: React.FC = () => {
  const [config, setConfig] = useState<SystemConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('general');
  const [hasChanges, setHasChanges] = useState(false);

  // Mock data - replace with actual API call
  useEffect(() => {
    const mockConfig: SystemConfig = {
      general: {
        siteName: 'Entrepreneur Platform',
        siteDescription: 'A comprehensive platform for entrepreneurs, mentors, and investors',
        adminEmail: '<EMAIL>',
        timezone: 'UTC-8 (PST)',
        language: 'English',
        maintenanceMode: false,
        registrationEnabled: true,
        emailVerificationRequired: true
      },
      database: {
        host: 'localhost',
        port: 5432,
        name: 'entrepreneur_platform',
        connectionPool: 20,
        backupFrequency: 'Daily',
        lastBackup: '2024-01-16T02:00:00Z'
      },
      email: {
        provider: 'SMTP',
        smtpHost: 'smtp.gmail.com',
        smtpPort: 587,
        username: '<EMAIL>',
        encryption: 'TLS',
        fromAddress: '<EMAIL>',
        fromName: 'Entrepreneur Platform'
      },
      security: {
        sessionTimeout: 30,
        maxLoginAttempts: 5,
        passwordMinLength: 8,
        requireTwoFactor: false,
        allowedFileTypes: ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx'],
        maxFileSize: 10
      },
      api: {
        rateLimit: 1000,
        apiVersion: 'v1',
        enableCors: true,
        allowedOrigins: ['https://entrepreneurplatform.com', 'https://app.entrepreneurplatform.com'],
        enableLogging: true
      }
    };

    setTimeout(() => {
      setConfig(mockConfig);
      setLoading(false);
    }, 1000);
  }, []);

  const handleSave = async () => {
    setSaving(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    setSaving(false);
    setHasChanges(false);
    // Show success message
  };

  const handleConfigChange = (section: keyof SystemConfig, field: string, value: any) => {
    if (!config) return;
    
    setConfig({
      ...config,
      [section]: {
        ...config[section],
        [field]: value
      }
    });
    setHasChanges(true);
  };

  const handleTestConnection = async (type: 'database' | 'email') => {
    // Simulate connection test
    await new Promise(resolve => setTimeout(resolve, 1000));
    // Show test result
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!config) return null;

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">System Configuration</h1>
          <p className="text-gray-600 mt-1">Manage system settings and configurations</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" disabled={!hasChanges}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Reset Changes
          </Button>
          <Button 
            onClick={handleSave} 
            disabled={!hasChanges || saving}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {saving ? (
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Save className="w-4 h-4 mr-2" />
            )}
            {saving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </div>

      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">System Status</p>
                <p className="text-lg font-bold text-green-600">Online</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Maintenance Mode</p>
                <p className="text-lg font-bold text-gray-600">
                  {config.general.maintenanceMode ? 'Enabled' : 'Disabled'}
                </p>
              </div>
              <Settings className="h-8 w-8 text-gray-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Last Backup</p>
                <p className="text-lg font-bold text-blue-600">
                  {new Date(config.database.lastBackup).toLocaleDateString()}
                </p>
              </div>
              <Database className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">API Status</p>
                <p className="text-lg font-bold text-green-600">Active</p>
              </div>
              <Globe className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Configuration Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="database">Database</TabsTrigger>
          <TabsTrigger value="email">Email</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="api">API</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>General Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Site Name</label>
                  <Input 
                    value={config.general.siteName}
                    onChange={(e) => handleConfigChange('general', 'siteName', e.target.value)}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Admin Email</label>
                  <Input 
                    type="email"
                    value={config.general.adminEmail}
                    onChange={(e) => handleConfigChange('general', 'adminEmail', e.target.value)}
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Site Description</label>
                <Textarea 
                  value={config.general.siteDescription}
                  onChange={(e) => handleConfigChange('general', 'siteDescription', e.target.value)}
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Timezone</label>
                  <select 
                    value={config.general.timezone}
                    onChange={(e) => handleConfigChange('general', 'timezone', e.target.value)}
                    className="w-full border rounded px-3 py-2"
                  >
                    <option value="UTC-8 (PST)">UTC-8 (PST)</option>
                    <option value="UTC-5 (EST)">UTC-5 (EST)</option>
                    <option value="UTC+0 (GMT)">UTC+0 (GMT)</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Language</label>
                  <select 
                    value={config.general.language}
                    onChange={(e) => handleConfigChange('general', 'language', e.target.value)}
                    className="w-full border rounded px-3 py-2"
                  >
                    <option value="English">English</option>
                    <option value="Spanish">Spanish</option>
                    <option value="French">French</option>
                  </select>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Maintenance Mode</span>
                  <input 
                    type="checkbox" 
                    checked={config.general.maintenanceMode}
                    onChange={(e) => handleConfigChange('general', 'maintenanceMode', e.target.checked)}
                    className="w-4 h-4"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Registration Enabled</span>
                  <input 
                    type="checkbox" 
                    checked={config.general.registrationEnabled}
                    onChange={(e) => handleConfigChange('general', 'registrationEnabled', e.target.checked)}
                    className="w-4 h-4"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Email Verification Required</span>
                  <input 
                    type="checkbox" 
                    checked={config.general.emailVerificationRequired}
                    onChange={(e) => handleConfigChange('general', 'emailVerificationRequired', e.target.checked)}
                    className="w-4 h-4"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="database" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="w-5 h-5" />
                Database Configuration
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Host</label>
                  <Input 
                    value={config.database.host}
                    onChange={(e) => handleConfigChange('database', 'host', e.target.value)}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Port</label>
                  <Input 
                    type="number"
                    value={config.database.port}
                    onChange={(e) => handleConfigChange('database', 'port', parseInt(e.target.value))}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Database Name</label>
                  <Input 
                    value={config.database.name}
                    onChange={(e) => handleConfigChange('database', 'name', e.target.value)}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Connection Pool Size</label>
                  <Input 
                    type="number"
                    value={config.database.connectionPool}
                    onChange={(e) => handleConfigChange('database', 'connectionPool', parseInt(e.target.value))}
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Backup Frequency</label>
                <select 
                  value={config.database.backupFrequency}
                  onChange={(e) => handleConfigChange('database', 'backupFrequency', e.target.value)}
                  className="w-full border rounded px-3 py-2"
                >
                  <option value="Hourly">Hourly</option>
                  <option value="Daily">Daily</option>
                  <option value="Weekly">Weekly</option>
                </select>
              </div>

              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  onClick={() => handleTestConnection('database')}
                >
                  Test Connection
                </Button>
                <Button variant="outline">
                  Create Backup
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="email" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="w-5 h-5" />
                Email Configuration
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">SMTP Host</label>
                  <Input 
                    value={config.email.smtpHost}
                    onChange={(e) => handleConfigChange('email', 'smtpHost', e.target.value)}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">SMTP Port</label>
                  <Input 
                    type="number"
                    value={config.email.smtpPort}
                    onChange={(e) => handleConfigChange('email', 'smtpPort', parseInt(e.target.value))}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Username</label>
                  <Input 
                    value={config.email.username}
                    onChange={(e) => handleConfigChange('email', 'username', e.target.value)}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Encryption</label>
                  <select 
                    value={config.email.encryption}
                    onChange={(e) => handleConfigChange('email', 'encryption', e.target.value)}
                    className="w-full border rounded px-3 py-2"
                  >
                    <option value="TLS">TLS</option>
                    <option value="SSL">SSL</option>
                    <option value="None">None</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">From Address</label>
                  <Input 
                    type="email"
                    value={config.email.fromAddress}
                    onChange={(e) => handleConfigChange('email', 'fromAddress', e.target.value)}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">From Name</label>
                  <Input 
                    value={config.email.fromName}
                    onChange={(e) => handleConfigChange('email', 'fromName', e.target.value)}
                  />
                </div>
              </div>

              <Button 
                variant="outline" 
                onClick={() => handleTestConnection('email')}
              >
                Test Email Configuration
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="w-5 h-5" />
                Security Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Session Timeout (minutes)</label>
                  <Input 
                    type="number"
                    value={config.security.sessionTimeout}
                    onChange={(e) => handleConfigChange('security', 'sessionTimeout', parseInt(e.target.value))}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Max Login Attempts</label>
                  <Input 
                    type="number"
                    value={config.security.maxLoginAttempts}
                    onChange={(e) => handleConfigChange('security', 'maxLoginAttempts', parseInt(e.target.value))}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Password Min Length</label>
                  <Input 
                    type="number"
                    value={config.security.passwordMinLength}
                    onChange={(e) => handleConfigChange('security', 'passwordMinLength', parseInt(e.target.value))}
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Allowed File Types</label>
                <Input 
                  value={config.security.allowedFileTypes.join(', ')}
                  onChange={(e) => handleConfigChange('security', 'allowedFileTypes', e.target.value.split(', '))}
                  placeholder="jpg, png, pdf, doc"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Max File Size (MB)</label>
                <Input 
                  type="number"
                  value={config.security.maxFileSize}
                  onChange={(e) => handleConfigChange('security', 'maxFileSize', parseInt(e.target.value))}
                />
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Require Two-Factor Authentication</span>
                <input 
                  type="checkbox" 
                  checked={config.security.requireTwoFactor}
                  onChange={(e) => handleConfigChange('security', 'requireTwoFactor', e.target.checked)}
                  className="w-4 h-4"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="api" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="w-5 h-5" />
                API Configuration
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Rate Limit (requests/hour)</label>
                  <Input 
                    type="number"
                    value={config.api.rateLimit}
                    onChange={(e) => handleConfigChange('api', 'rateLimit', parseInt(e.target.value))}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">API Version</label>
                  <Input 
                    value={config.api.apiVersion}
                    onChange={(e) => handleConfigChange('api', 'apiVersion', e.target.value)}
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Allowed Origins</label>
                <Textarea 
                  value={config.api.allowedOrigins.join('\n')}
                  onChange={(e) => handleConfigChange('api', 'allowedOrigins', e.target.value.split('\n'))}
                  rows={3}
                  placeholder="https://example.com"
                />
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Enable CORS</span>
                  <input 
                    type="checkbox" 
                    checked={config.api.enableCors}
                    onChange={(e) => handleConfigChange('api', 'enableCors', e.target.checked)}
                    className="w-4 h-4"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Enable API Logging</span>
                  <input 
                    type="checkbox" 
                    checked={config.api.enableLogging}
                    onChange={(e) => handleConfigChange('api', 'enableLogging', e.target.checked)}
                    className="w-4 h-4"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Warning for unsaved changes */}
      {hasChanges && (
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <AlertTriangle className="w-5 h-5 text-orange-600" />
              <span className="text-orange-800">You have unsaved changes. Don't forget to save your configuration.</span>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default SystemConfigurationPage;
