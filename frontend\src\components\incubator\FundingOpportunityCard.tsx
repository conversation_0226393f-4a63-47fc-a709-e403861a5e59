import React from 'react';
import { Calendar, DollarSign, Users, Clock, ArrowRight, Tag } from 'lucide-react';
import { Link } from 'react-router-dom';
import { FundingOpportunity } from '../../services/incubatorApi';

import { useTranslation } from 'react-i18next';
interface FundingOpportunityCardProps {
  opportunity: FundingOpportunity;
}

const FundingOpportunityCard: React.FC<FundingOpportunityCardProps> = ({ opportunity  }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Get funding type badge color
  const getFundingTypeBadgeColor = (type: string) => {
    switch (type) {
      case 'grant':
        return 'bg-green-600/50 text-green-200';
      case 'equity':
        return 'bg-blue-600/50 text-blue-200';
      case 'loan':
        return 'bg-yellow-600/50 text-yellow-200';
      case 'convertible':
        return 'bg-purple-600/50 text-purple-200';
      case 'prize':
        return 'bg-pink-600/50 text-pink-200';
      case 'crowdfunding':
        return 'bg-orange-600/50 text-orange-200';
      default:
        return 'bg-gray-600/50 text-gray-200';
    }
  };

  // Get status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-600/50 text-green-200';
      case 'closed':
        return 'bg-red-600/50 text-red-200';
      case 'draft':
        return 'bg-gray-600/50 text-gray-200';
      default:
        return 'bg-gray-600/50 text-gray-200';
    }
  };

  // Format funding type for display
  const formatFundingType = (type: string) => {
    return type.charAt(0).toUpperCase() + type.slice(1);
  };

  // Check if deadline is approaching (within 7 days)
  const isDeadlineApproaching = () => {
    const deadline = new Date(opportunity.application_deadline);
    const now = new Date();
    const diffTime = deadline.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 7 && diffDays >= 0;
  };

  return (
    <div className="bg-indigo-900/50 rounded-lg overflow-hidden border border-indigo-800 hover:border-purple-500 transition-all duration-300 hover:shadow-glow">
      <div className="p-6">
        <div className={`flex justify-between items-start mb-3 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`flex space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
            <span className={`px-2 py-1 rounded-md text-xs font-medium ${getFundingTypeBadgeColor(opportunity.funding_type)}`}>
              {formatFundingType(opportunity.funding_type)}
            </span>
            <span className={`px-2 py-1 rounded-md text-xs font-medium ${getStatusBadgeColor(opportunity.status)}`}>
              {opportunity.status.charAt(0).toUpperCase() + opportunity.status.slice(1)}
            </span>
          </div>
          <div className={`flex items-center text-gray-400 text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
            <Clock size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
            {formatDate(opportunity.created_at)}
          </div>
        </div>
        
        <h3 className="text-xl font-bold mb-2 text-white">{opportunity.title}</h3>
        
        <div className="text-gray-300 mb-4 line-clamp-3">
          {opportunity.description}
        </div>
        
        <div className={`flex flex-col space-y-2 mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`flex items-center text-gray-300 ${isRTL ? "flex-row-reverse" : ""}`}>
            <DollarSign size={16} className={`mr-2 text-green-400 ${isRTL ? "space-x-reverse" : ""}`} />
            <span className="font-semibold">{formatCurrency(opportunity.amount)}</span>
          </div>
          
          <div className={`flex items-center text-gray-300 ${isRTL ? "flex-row-reverse" : ""}`}>
            <Calendar size={16} className={`mr-2 text-blue-400 ${isRTL ? "space-x-reverse" : ""}`} />
            <span className={isDeadlineApproaching() ? 'text-yellow-300 font-semibold' : ''}>
              Deadline: {formatDate(opportunity.application_deadline)}
              {isDeadlineApproaching() && ' (Approaching!)'}
            </span>
          </div>
          
          <div className={`flex items-center text-gray-300 ${isRTL ? "flex-row-reverse" : ""}`}>
            <Users size={16} className={`mr-2 text-purple-400 ${isRTL ? "space-x-reverse" : ""}`} />
            <span>{opportunity.application_count} applications</span>
          </div>
        </div>
        
        <div className={`flex justify-between items-center mt-4 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="text-sm text-gray-400">
            By {opportunity.provider.username}
          </div>
          
          <Link
            to={`/incubator/funding/${opportunity.id}`}
            className={`px-3 py-1.5 bg-purple-600 hover:bg-purple-700 rounded-md text-white text-sm flex items-center transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
          >
            View Details <ArrowRight size={14} className={`ml-1 ${isRTL ? "space-x-reverse" : ""}`} />
          </Link>
        </div>
      </div>
    </div>
  );
};

export default FundingOpportunityCard;
