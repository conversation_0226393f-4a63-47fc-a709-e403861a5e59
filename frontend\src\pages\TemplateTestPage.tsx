/**
 * Template Test Page
 * Simple test page to verify template functionality
 */

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  ArrowLeft,
  CheckCircle,
  AlertCircle,
  Eye,
  Play,
  FileText
} from 'lucide-react';
import TemplateSelector from '../components/templates/TemplateSelector';

const TemplateTestPage: React.FC = () => {
  const navigate = useNavigate();
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [testResults, setTestResults] = useState<string[]>([]);

  const handleSelectTemplate = (templateId: string) => {
    console.log('handleSelectTemplate called with:', templateId);
    setSelectedTemplate(templateId);
    addTestResult(`✅ Template selected: ${templateId}`);

    // Show visual feedback
    const element = document.querySelector(`[data-template-id="${templateId}"]`);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  };

  const handlePreviewTemplate = (templateId: string) => {
    addTestResult(`👁️ Template previewed: ${templateId}`);
  };

  const handleUseTemplate = (templateId: string) => {
    addTestResult(`🚀 Template used: ${templateId}`);
    // In a real app, this would navigate to the business plan creation page
    console.log('Using template:', templateId);
  };

  const addTestResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <button
                onClick={() => navigate(-1)}
                className="p-2 hover:bg-gray-700 rounded-lg transition-colors mr-4"
              >
                <ArrowLeft size={20} />
              </button>
              <div>
                <h1 className="text-2xl font-bold">Template System Test</h1>
                <p className="text-gray-400 text-sm">
                  Test the enhanced template system functionality
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-400">
                Selected: <span className="text-purple-400">{selectedTemplate || 'None'}</span>
              </div>
              <button
                onClick={clearResults}
                className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
              >
                Clear Results
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Test Results Panel */}
      <div className="bg-gray-800/50 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="bg-gray-800 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-lg font-semibold flex items-center">
                <CheckCircle size={20} className="text-green-400 mr-2" />
                Test Results
              </h3>
              <div className="text-sm text-gray-400">
                {testResults.length} events logged
              </div>
            </div>

            <div className="bg-gray-900 rounded-lg p-3 max-h-32 overflow-y-auto">
              {testResults.length === 0 ? (
                <div className="text-gray-500 text-sm italic">
                  No events yet. Try interacting with the templates below.
                </div>
              ) : (
                <div className="space-y-1">
                  {testResults.map((result, index) => (
                    <div key={index} className="text-sm font-mono text-gray-300">
                      {result}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Instructions */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-6 mb-8">
          <div className="flex items-start">
            <AlertCircle size={20} className="text-blue-400 mr-3 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="text-lg font-semibold text-blue-400 mb-2">
                How to Test the Template System
              </h3>
              <div className="space-y-2 text-gray-300">
                <p>• <strong>Browse Templates:</strong> Scroll through the template grid below</p>
                <p>• <strong>Filter & Search:</strong> Use the search bar and filters to find specific templates</p>
                <p>• <strong>Select Templates:</strong> Click on any template card to select it</p>
                <p>• <strong>Preview Templates:</strong> Click the "Preview" button to open the detailed viewer</p>
                <p>• <strong>Use Templates:</strong> Click "Use Template" to simulate starting a business plan</p>
                <p>• <strong>Check Results:</strong> Watch the test results panel above for logged events</p>
              </div>
            </div>
          </div>
        </div>

        {/* Template System Status */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-gray-800/50 rounded-lg p-6">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-semibold">Template Count</h4>
              <FileText size={20} className="text-blue-400" />
            </div>
            <div className="text-2xl font-bold text-blue-400">38</div>
            <div className="text-sm text-gray-400">Total templates available</div>
          </div>

          <div className="bg-gray-800/50 rounded-lg p-6">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-semibold">New Templates</h4>
              <CheckCircle size={20} className="text-green-400" />
            </div>
            <div className="text-2xl font-bold text-green-400">17</div>
            <div className="text-sm text-gray-400">Recently added</div>
          </div>

          <div className="bg-gray-800/50 rounded-lg p-6">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-semibold">Categories</h4>
              <Eye size={20} className="text-purple-400" />
            </div>
            <div className="text-2xl font-bold text-purple-400">15</div>
            <div className="text-sm text-gray-400">Different categories</div>
          </div>
        </div>

        {/* Template Selector */}
        <TemplateSelector
          onSelectTemplate={handleSelectTemplate}
          selectedTemplate={selectedTemplate}
          showCategories={true}
          showFilters={true}
          showActionButtons={true}
          onPreviewTemplate={handlePreviewTemplate}
          onUseTemplate={handleUseTemplate}
        />

        {/* Selected Template Info */}
        {selectedTemplate && (
          <div className="mt-8 bg-green-900/20 border border-green-500/50 rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-green-400 mb-2">
                  ✅ Template Selected: {selectedTemplate}
                </h3>
                <p className="text-gray-300">
                  The template has been successfully selected. You can now preview it or use it to create a business plan.
                </p>
              </div>
              <div className="flex space-x-3">
                <button
                  onClick={() => handlePreviewTemplate(selectedTemplate)}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center"
                >
                  <Eye size={16} className="mr-2" />
                  Preview
                </button>
                <button
                  onClick={() => handleUseTemplate(selectedTemplate)}
                  className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors flex items-center"
                >
                  <Play size={16} className="mr-2" />
                  Use Template
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TemplateTestPage;
