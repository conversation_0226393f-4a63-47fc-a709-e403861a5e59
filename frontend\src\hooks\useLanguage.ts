/**
 * Language Hook
 * 
 * This hook provides a unified interface for language-related functionality.
 * It combines the Redux state and actions with utility functions.
 */

import { useCallback } from 'react';
import { useAppSelector, useAppDispatch } from '../store/hooks';
import { setLanguage, toggleLanguage } from '../store/languageSlice';
import { isRTLLanguage, getDirectionFromLanguage } from '../utils/rtl';

/**
 * Interface for the language hook return value
 */
export interface UseLanguageReturn {
  /**
   * The current language code
   */
  language: string;
  
  /**
   * The current text direction ('ltr' or 'rtl')
   */
  direction: 'ltr' | 'rtl';
  
  /**
   * Whether the current language is RTL
   */
  isRTL: boolean;
  
  /**
   * Available languages
   */
  availableLanguages: Record<string, string>;
  
  /**
   * Change the language
   * @param lang The language code to change to
   */
  changeLanguage: (lang: string) => void;
  
  /**
   * Toggle between English and Arabic
   */
  toggleLanguage: () => void;
  
  /**
   * Get the direction for a specific language
   * @param lang The language code
   * @returns The text direction ('ltr' or 'rtl')
   */
  getDirectionForLanguage: (lang: string) => 'ltr' | 'rtl';
  
  /**
   * Check if a language is RTL
   * @param lang The language code
   * @returns Whether the language is RTL
   */
  isLanguageRTL: (lang: string) => boolean;
}

/**
 * Hook for language management
 * @returns Language state and functions
 */
export const useLanguage = (): UseLanguageReturn => {
  const dispatch = useAppDispatch();
  const { language, direction, availableLanguages } = useAppSelector(state => state.language);
  
  const isRTL = direction === 'rtl';
  
  /**
   * Change the language
   * @param lang The language code to change to
   */
  const changeLanguage = useCallback((lang: string) => {
    dispatch(setLanguage(lang));
  }, [dispatch]);
  
  /**
   * Toggle between English and Arabic
   */
  const handleToggleLanguage = useCallback(() => {
    dispatch(toggleLanguage());
  }, [dispatch]);
  
  /**
   * Get the direction for a specific language
   * @param lang The language code
   * @returns The text direction ('ltr' or 'rtl')
   */
  const getDirectionForLanguage = useCallback((lang: string) => {
    return getDirectionFromLanguage(lang);
  }, []);
  
  /**
   * Check if a language is RTL
   * @param lang The language code
   * @returns Whether the language is RTL
   */
  const isLanguageRTL = useCallback((lang: string) => {
    return isRTLLanguage(lang);
  }, []);
  
  return {
    language,
    direction,
    isRTL,
    availableLanguages,
    changeLanguage,
    toggleLanguage: handleToggleLanguage,
    getDirectionForLanguage,
    isLanguageRTL
  };
};

export default useLanguage;
