import React, { useState } from 'react';
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ader2 } from 'lucide-react';
import { useAppSelector } from '../store/hooks';
import { membershipAPI } from '../services/api';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../hooks/useLanguage';
import { COUNTRIES, SYRIAN_STATES, getCountryName, getStateName } from '../constants/locations';
interface FormData {
  full_name: string;
  email: string;
  phone: string;
  country: string;
  state: string;
  location: string;
  expertise_areas: string;
  expertise_level: string;
  background: string;
  motivation: string;
  linkedin_profile: string;
  github_profile: string;
  portfolio_url: string;
}

const MembershipForm: React.FC<{ onClose: () => void }> = ({ onClose  }) => {
  const { t, i18n } = useTranslation();
  const { isRTL } = useLanguage();
  const { user } = useAppSelector(state => state.auth);
  const [formData, setFormData] = useState<FormData>(() => {
    // Initialize with default Syria location
    const defaultCountry = COUNTRIES.find(c => c.code === 'SY');
    const defaultLocation = defaultCountry ? getCountryName(defaultCountry, i18n.language) : '';

    return {
      full_name: user ? `${user.first_name} ${user.last_name}`.trim() : '',
      email: user?.email || '',
      phone: '',
      country: 'SY', // Default to Syria
      state: '',
      location: user?.profile?.location || defaultLocation,
      expertise_areas: '',
      expertise_level: 'beginner',
      background: '',
      motivation: '',
      linkedin_profile: user?.profile?.linkedin || '',
      github_profile: user?.profile?.github || '',
      portfolio_url: user?.profile?.website || '',
    };
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [formSuccess, setFormSuccess] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    setFormData(prev => {
      const newData = {
        ...prev,
        [name]: value
      };

      // Reset state when country changes from Syria to another country
      if (name === 'country' && value !== 'SY') {
        newData.state = '';
      }

      // Auto-populate location field based on country and state selection
      if (name === 'country' || name === 'state') {
        const selectedCountry = name === 'country' ? value : prev.country;
        const selectedState = name === 'state' ? value : prev.state;

        if (selectedCountry) {
          const country = COUNTRIES.find(c => c.code === selectedCountry);
          if (country) {
            let locationString = getCountryName(country, i18n.language);

            // Add state if Syria is selected and state is chosen
            if (selectedCountry === 'SY' && selectedState) {
              const state = SYRIAN_STATES.find(s => s.code === selectedState);
              if (state) {
                locationString = `${getStateName(state, i18n.language)}, ${locationString}`;
              }
            }

            newData.location = locationString;
          }
        }
      }

      return newData;
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setFormError(null);

    try {
      // Validate form
      if (!formData.full_name || !formData.email || !formData.country || !formData.location ||
          !formData.expertise_areas || !formData.expertise_level || !formData.background || !formData.motivation) {
        setFormError(t('membership.fillRequiredFields'));
        setIsSubmitting(false);
        return;
      }

      // Validate state for Syria
      if (formData.country === 'SY' && !formData.state) {
        setFormError(t('membership.fillRequiredFields'));
        setIsSubmitting(false);
        return;
      }

      // Debug: Log the form data being submitted
      console.log("Submitting membership application with data:", formData);

      // Submit application
      const response = await membershipAPI.createApplication(formData);

      // If we get here, the request was successful
      setFormSuccess(true);

      // Close the form after 3 seconds
      setTimeout(() => {
        onClose();
      }, 3000);
    } catch (error) {
      console.error('Error submitting application:', error);
      setFormError(t('membership.submitError'));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className={`fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70 backdrop-blur-sm ${isRTL ? "flex-row-reverse" : ""}`}>
      <div className="bg-gradient-to-b from-indigo-900/90 to-purple-900/90 rounded-xl p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {formSuccess ? (
          <div className="text-center py-10">
            <div className={`w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
              <Check size={32} className="text-green-400" />
            </div>
            <h3 className="text-2xl font-semibold mb-4">{t('membership.applicationSubmitted')}</h3>
            <div className="text-gray-300 mb-6">
              {t('membership.applicationSubmittedMessage')}
            </div>
            <button
              onClick={onClose}
              className="px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full font-medium hover:shadow-glow transition-all duration-300"
            >
              {t('common.close')}
            </button>
          </div>
        ) : (
          <>
            <div className={`flex justify-between items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
              <h3 className="text-2xl font-semibold">{t('community.applyForMembership')}</h3>
              <button
                onClick={onClose}
                className="p-2 rounded-full bg-indigo-800/50 hover:bg-indigo-700/50"
              >
                <X size={20} />
              </button>
            </div>

            {formError && (
              <div className={`mb-6 p-4 bg-red-900/30 border border-red-500/50 rounded-lg flex items-start ${isRTL ? "flex-row-reverse" : ""}`}>
                <AlertTriangle size={20} className={`text-red-400 mr-3 mt-0.5 flex-shrink-0 ${isRTL ? "flex-row-reverse" : ""}`} />
                <div className="text-red-200">{formError}</div>
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium mb-2">{t('membership.fullName')} *</label>
                  <input
                    type="text"
                    name="full_name"
                    value={formData.full_name}
                    onChange={handleChange}
                    className="w-full px-4 py-2 bg-indigo-900/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                    placeholder={t('membership.fullNamePlaceholder')}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">{t('auth.email')} *</label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    className="w-full px-4 py-2 bg-indigo-900/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                    placeholder={t('membership.emailPlaceholder')}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">{t('membership.phoneNumber')}</label>
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    className="w-full px-4 py-2 bg-indigo-900/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                    placeholder={t('membership.phoneNumberPlaceholder')}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">{t('membership.country')} *</label>
                  <select
                    name="country"
                    value={formData.country}
                    onChange={handleChange}
                    className="w-full px-4 py-2 bg-indigo-900/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                    required
                  >
                    <option value="">{t('membership.countryPlaceholder')}</option>
                    {COUNTRIES.map((country) => (
                      <option key={country.code} value={country.code}>
                        {getCountryName(country, i18n.language)}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* State field - only show for Syria */}
              {formData.country === 'SY' && (
                <div>
                  <label className="block text-sm font-medium mb-2">{t('membership.state')} *</label>
                  <select
                    name="state"
                    value={formData.state}
                    onChange={handleChange}
                    className="w-full px-4 py-2 bg-indigo-900/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                    required
                  >
                    <option value="">{t('membership.statePlaceholder')}</option>
                    {SYRIAN_STATES.map((state) => (
                      <option key={state.code} value={state.code}>
                        {getStateName(state, i18n.language)}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {/* Location display - auto-generated from country/state */}
              {formData.location && (
                <div>
                  <label className="block text-sm font-medium mb-2 text-gray-400">{t('membership.location')} (Auto-generated)</label>
                  <div className="w-full px-4 py-2 bg-indigo-900/30 border border-indigo-800/50 rounded-lg text-gray-300 text-sm">
                    {formData.location}
                  </div>
                </div>
              )}

              <div>
                <label className="block text-sm font-medium mb-2">{t('membership.expertiseAreas')} *</label>
                <textarea
                  name="expertise_areas"
                  value={formData.expertise_areas}
                  onChange={handleChange}
                  className="w-full px-4 py-2 bg-indigo-900/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  placeholder={t('membership.expertiseAreasPlaceholder')}
                  rows={3}
                  required
                ></textarea>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">{t('membership.expertiseLevel')} *</label>
                <select
                  name="expertise_level"
                  value={formData.expertise_level}
                  onChange={handleChange}
                  className="w-full px-4 py-2 bg-indigo-900/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  required
                >
                  <option value="">{t('membership.selectExpertiseLevel')}</option>
                  <option value="beginner">{t('membership.beginner')}</option>
                  <option value="intermediate">{t('membership.intermediate')}</option>
                  <option value="advanced">{t('membership.advanced')}</option>
                  <option value="expert">{t('membership.expert')}</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">{t('membership.background')} *</label>
                <textarea
                  name="background"
                  value={formData.background}
                  onChange={handleChange}
                  className="w-full px-4 py-2 bg-indigo-900/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  placeholder={t('membership.backgroundPlaceholder')}
                  rows={4}
                  required
                ></textarea>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">{t('membership.motivation')} *</label>
                <textarea
                  name="motivation"
                  value={formData.motivation}
                  onChange={handleChange}
                  className="w-full px-4 py-2 bg-indigo-900/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  placeholder={t('membership.motivationPlaceholder')}
                  rows={4}
                  required
                ></textarea>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium mb-2">{t('membership.linkedinProfile')}</label>
                  <input
                    type="url"
                    name="linkedin_profile"
                    value={formData.linkedin_profile}
                    onChange={handleChange}
                    className="w-full px-4 py-2 bg-indigo-900/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                    placeholder={t('membership.linkedinPlaceholder')}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">{t('membership.githubProfile')}</label>
                  <input
                    type="url"
                    name="github_profile"
                    value={formData.github_profile}
                    onChange={handleChange}
                    className="w-full px-4 py-2 bg-indigo-900/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                    placeholder={t('membership.githubPlaceholder')}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">{t('membership.portfolioWebsite')}</label>
                  <input
                    type="url"
                    name="portfolio_url"
                    value={formData.portfolio_url}
                    onChange={handleChange}
                    className="w-full px-4 py-2 bg-indigo-900/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                    placeholder={t('membership.portfolioPlaceholder')}
                  />
                </div>
              </div>

              <div className="pt-4">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className={`w-full px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full font-medium hover:shadow-glow transition-all duration-300 flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 size={20} className={`mr-2 animate-spin ${isRTL ? "space-x-reverse" : ""}`} />
                      {t('membership.submitting')}
                    </>
                  ) : (
                    t('membership.submitApplication')
                  )}
                </button>
              </div>
            </form>
          </>
        )}
      </div>
    </div>
  );
};

export default MembershipForm;
