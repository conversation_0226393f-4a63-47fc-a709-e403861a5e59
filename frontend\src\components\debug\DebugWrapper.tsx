import React from 'react';
import { useAppSelector } from '../../store/hooks';
import { isSuperAdmin } from '../../utils/roleBasedRouting';

// Only import debug components in development
const AuthStatusChecker = React.lazy(() => import('./AuthStatusChecker'));
const AuthDebugPanel = React.lazy(() => import('./AuthDebugPanel'));
const ModuleLoadingDiagnostic = React.lazy(() => import('./ModuleLoadingDiagnostic'));
const ImportTester = React.lazy(() => import('./ImportTester'));
const RouteTester = React.lazy(() => import('./RouteTester'));
const RouteDiagnostics = React.lazy(() => import('./RouteDiagnostics'));
const ComponentAudit = React.lazy(() => import('./ComponentAudit'));
const AuthFlowTester = React.lazy(() => import('./AuthFlowTester'));
const RoleAccessTester = React.lazy(() => import('./RoleAccessTester'));
const NavigationFlowTester = React.lazy(() => import('./NavigationFlowTester'));
const NavigationHealthDashboard = React.lazy(() => import('./NavigationHealthDashboard'));
const RoleAccessControlTester = React.lazy(() => import('./RoleAccessControlTester'));

/**
 * Debug Wrapper Component
 * Only renders debug components for super admins in development mode
 * Helps keep production builds clean and performant
 */
const DebugWrapper: React.FC = () => {
  const { user, isAuthenticated } = useAppSelector(state => state.auth);

  // Only render debug components in development mode AND for super admins
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  // Only show to super admins
  if (!isAuthenticated || !user || !isSuperAdmin(user)) {
    return null;
  }

  return (
    <React.Suspense fallback={null}>
      <AuthStatusChecker />
      <AuthDebugPanel />
      <ModuleLoadingDiagnostic />
      <ImportTester />
      <RouteTester />
      <RouteDiagnostics />
      <ComponentAudit />
      <AuthFlowTester />
      <RoleAccessTester />
      <NavigationFlowTester />
      <NavigationHealthDashboard />
      <RoleAccessControlTester />
    </React.Suspense>
  );
};

export default DebugWrapper;
