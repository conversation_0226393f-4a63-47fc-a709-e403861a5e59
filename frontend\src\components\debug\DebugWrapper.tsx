import React from 'react';

// Only import debug components in development
const AuthStatusChecker = React.lazy(() => import('./AuthStatusChecker'));
const AuthDebugPanel = React.lazy(() => import('./AuthDebugPanel'));
const ModuleLoadingDiagnostic = React.lazy(() => import('./ModuleLoadingDiagnostic'));
const ImportTester = React.lazy(() => import('./ImportTester'));
const RouteTester = React.lazy(() => import('./RouteTester'));
const RouteDiagnostics = React.lazy(() => import('./RouteDiagnostics'));
const ComponentAudit = React.lazy(() => import('./ComponentAudit'));
const AuthFlowTester = React.lazy(() => import('./AuthFlowTester'));
const RoleAccessTester = React.lazy(() => import('./RoleAccessTester'));
const NavigationFlowTester = React.lazy(() => import('./NavigationFlowTester'));
const NavigationHealthDashboard = React.lazy(() => import('./NavigationHealthDashboard'));
const RoleAccessControlTester = React.lazy(() => import('./RoleAccessControlTester'));

/**
 * Debug Wrapper Component
 * Only renders debug components in development mode
 * Helps keep production builds clean and performant
 */
const DebugWrapper: React.FC = () => {
  // Only render debug components in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <React.Suspense fallback={null}>
      <AuthStatusChecker />
      <AuthDebugPanel />
      <ModuleLoadingDiagnostic />
      <ImportTester />
      <RouteTester />
      <RouteDiagnostics />
      <ComponentAudit />
      <AuthFlowTester />
      <RoleAccessTester />
      <NavigationFlowTester />
      <NavigationHealthDashboard />
      <RoleAccessControlTester />
    </React.Suspense>
  );
};

export default DebugWrapper;
