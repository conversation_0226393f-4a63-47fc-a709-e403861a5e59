import { useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import {
  useApiList,
  useApiQuery,
  useApiCreate,
  useApiUpdate,
  useApiDelete,
  useApiInfinite,
  usePrefetchApiQuery,
  usePrefetchApiList
} from './useApi';
import { 
  MentorProfile, 
  MentorshipApplication, 
  MentorshipMatch, 
  MentorshipSession,
  MentorshipFeedback 
} from '../services/incubatorApi';
import { QueryParams } from '../types/api';

// Query keys for mentorship entities
export const MentorshipKeys = {
  // Mentor Profiles
  mentorProfiles: ['mentor-profiles'] as const,
  mentorProfileLists: () => [...MentorshipKeys.mentorProfiles, 'list'] as const,
  mentorProfileList: (params?: QueryParams) => [...MentorshipKeys.mentorProfileLists(), params] as const,
  mentorProfileDetails: () => [...MentorshipKeys.mentorProfiles, 'detail'] as const,
  mentorProfileDetail: (id: number) => [...MentorshipKeys.mentorProfileDetails(), id] as const,

  // Mentorship Applications
  applications: ['mentorship-applications'] as const,
  applicationLists: () => [...MentorshipKeys.applications, 'list'] as const,
  applicationList: (params?: QueryParams) => [...MentorshipKeys.applicationLists(), params] as const,
  applicationDetails: () => [...MentorshipKeys.applications, 'detail'] as const,
  applicationDetail: (id: number) => [...MentorshipKeys.applicationDetails(), id] as const,

  // Mentorship Matches
  matches: ['mentorship-matches'] as const,
  matchLists: () => [...MentorshipKeys.matches, 'list'] as const,
  matchList: (params?: QueryParams) => [...MentorshipKeys.matchLists(), params] as const,
  matchDetails: () => [...MentorshipKeys.matches, 'detail'] as const,
  matchDetail: (id: number) => [...MentorshipKeys.matchDetails(), id] as const,

  // Mentorship Sessions
  sessions: ['mentorship-sessions'] as const,
  sessionLists: () => [...MentorshipKeys.sessions, 'list'] as const,
  sessionList: (params?: QueryParams) => [...MentorshipKeys.sessionLists(), params] as const,
  sessionDetails: () => [...MentorshipKeys.sessions, 'detail'] as const,
  sessionDetail: (id: number) => [...MentorshipKeys.sessionDetails(), id] as const,

  // Mentorship Feedback
  feedback: ['mentorship-feedback'] as const,
  feedbackLists: () => [...MentorshipKeys.feedback, 'list'] as const,
  feedbackList: (params?: QueryParams) => [...MentorshipKeys.feedbackLists(), params] as const,
  feedbackDetails: () => [...MentorshipKeys.feedback, 'detail'] as const,
  feedbackDetail: (id: number) => [...MentorshipKeys.feedbackDetails(), id] as const,
};

// ============ MENTOR PROFILES ============

export function useMentorProfilesList(params?: QueryParams) {
  return useApiList<MentorProfile>(
    MentorshipKeys.mentorProfileList(params),
    '/incubator/mentor-profiles/',
    params
  );
}

export function useMentorProfilesInfinite(params?: QueryParams) {
  return useApiInfinite<MentorProfile>(
    MentorshipKeys.mentorProfileList(params),
    '/incubator/mentor-profiles/',
    params
  );
}

export function useMentorProfile(id: number) {
  return useApiQuery<MentorProfile>(
    MentorshipKeys.mentorProfileDetail(id),
    `/incubator/mentor-profiles/${id}/`
  );
}

export function useCreateMentorProfile() {
  const queryClient = useQueryClient();

  return useApiCreate<MentorProfile, Partial<MentorProfile>>(
    '/incubator/mentor-profiles/',
    {
      onSuccess: (newProfile) => {
        queryClient.invalidateQueries({ queryKey: MentorshipKeys.mentorProfileLists() });
        queryClient.setQueryData(MentorshipKeys.mentorProfileDetail(newProfile.id), newProfile);
      }
    }
  );
}

export function useUpdateMentorProfile() {
  const queryClient = useQueryClient();

  return useApiUpdate<MentorProfile, Partial<MentorProfile>>(
    (id: number) => `/incubator/mentor-profiles/${id}/`,
    {
      onSuccess: (updatedProfile) => {
        queryClient.setQueryData(MentorshipKeys.mentorProfileDetail(updatedProfile.id), updatedProfile);
        queryClient.invalidateQueries({ queryKey: MentorshipKeys.mentorProfileLists() });
      }
    }
  );
}

export function useDeleteMentorProfile() {
  const queryClient = useQueryClient();

  return useApiDelete(
    (id: number) => `/incubator/mentor-profiles/${id}/`,
    {
      onSuccess: (_, id) => {
        queryClient.removeQueries({ queryKey: MentorshipKeys.mentorProfileDetail(id) });
        queryClient.invalidateQueries({ queryKey: MentorshipKeys.mentorProfileLists() });
      }
    }
  );
}

// ============ MENTORSHIP APPLICATIONS ============

export function useMentorshipApplicationsList(params?: QueryParams) {
  return useApiList<MentorshipApplication>(
    MentorshipKeys.applicationList(params),
    '/incubator/mentorship-applications/',
    params
  );
}

export function useMentorshipApplication(id: number) {
  return useApiQuery<MentorshipApplication>(
    MentorshipKeys.applicationDetail(id),
    `/incubator/mentorship-applications/${id}/`
  );
}

export function useCreateMentorshipApplication() {
  const queryClient = useQueryClient();

  return useApiCreate<MentorshipApplication, Partial<MentorshipApplication>>(
    '/incubator/mentorship-applications/',
    {
      onSuccess: (newApplication) => {
        queryClient.invalidateQueries({ queryKey: MentorshipKeys.applicationLists() });
        queryClient.setQueryData(MentorshipKeys.applicationDetail(newApplication.id), newApplication);
      }
    }
  );
}

export function useUpdateMentorshipApplication() {
  const queryClient = useQueryClient();

  return useApiUpdate<MentorshipApplication, Partial<MentorshipApplication>>(
    (id: number) => `/incubator/mentorship-applications/${id}/`,
    {
      onSuccess: (updatedApplication) => {
        queryClient.setQueryData(MentorshipKeys.applicationDetail(updatedApplication.id), updatedApplication);
        queryClient.invalidateQueries({ queryKey: MentorshipKeys.applicationLists() });
      }
    }
  );
}

export function useDeleteMentorshipApplication() {
  const queryClient = useQueryClient();

  return useApiDelete(
    (id: number) => `/incubator/mentorship-applications/${id}/`,
    {
      onSuccess: (_, id) => {
        queryClient.removeQueries({ queryKey: MentorshipKeys.applicationDetail(id) });
        queryClient.invalidateQueries({ queryKey: MentorshipKeys.applicationLists() });
      }
    }
  );
}

// ============ MENTORSHIP MATCHES ============

export function useMentorshipMatchesList(params?: QueryParams) {
  return useApiList<MentorshipMatch>(
    MentorshipKeys.matchList(params),
    '/incubator/mentorship-matches/',
    params
  );
}

export function useMentorshipMatch(id: number) {
  return useApiQuery<MentorshipMatch>(
    MentorshipKeys.matchDetail(id),
    `/incubator/mentorship-matches/${id}/`
  );
}

export function useCreateMentorshipMatch() {
  const queryClient = useQueryClient();

  return useApiCreate<MentorshipMatch, Partial<MentorshipMatch>>(
    '/incubator/mentorship-matches/',
    {
      onSuccess: (newMatch) => {
        queryClient.invalidateQueries({ queryKey: MentorshipKeys.matchLists() });
        queryClient.setQueryData(MentorshipKeys.matchDetail(newMatch.id), newMatch);
      }
    }
  );
}

export function useUpdateMentorshipMatch() {
  const queryClient = useQueryClient();

  return useApiUpdate<MentorshipMatch, Partial<MentorshipMatch>>(
    (id: number) => `/incubator/mentorship-matches/${id}/`,
    {
      onSuccess: (updatedMatch) => {
        queryClient.setQueryData(MentorshipKeys.matchDetail(updatedMatch.id), updatedMatch);
        queryClient.invalidateQueries({ queryKey: MentorshipKeys.matchLists() });
      }
    }
  );
}

export function useDeleteMentorshipMatch() {
  const queryClient = useQueryClient();

  return useApiDelete(
    (id: number) => `/incubator/mentorship-matches/${id}/`,
    {
      onSuccess: (_, id) => {
        queryClient.removeQueries({ queryKey: MentorshipKeys.matchDetail(id) });
        queryClient.invalidateQueries({ queryKey: MentorshipKeys.matchLists() });
      }
    }
  );
}

// ============ MENTORSHIP SESSIONS ============

export function useMentorshipSessionsList(params?: QueryParams) {
  return useApiList<MentorshipSession>(
    MentorshipKeys.sessionList(params),
    '/incubator/mentorship-sessions/',
    params
  );
}

export function useMentorshipSession(id: number) {
  return useApiQuery<MentorshipSession>(
    MentorshipKeys.sessionDetail(id),
    `/incubator/mentorship-sessions/${id}/`
  );
}

export function useCreateMentorshipSession() {
  const queryClient = useQueryClient();

  return useApiCreate<MentorshipSession, Partial<MentorshipSession>>(
    '/incubator/mentorship-sessions/',
    {
      onSuccess: (newSession) => {
        queryClient.invalidateQueries({ queryKey: MentorshipKeys.sessionLists() });
        queryClient.setQueryData(MentorshipKeys.sessionDetail(newSession.id), newSession);
      }
    }
  );
}

export function useUpdateMentorshipSession() {
  const queryClient = useQueryClient();

  return useApiUpdate<MentorshipSession, Partial<MentorshipSession>>(
    (id: number) => `/incubator/mentorship-sessions/${id}/`,
    {
      onSuccess: (updatedSession) => {
        queryClient.setQueryData(MentorshipKeys.sessionDetail(updatedSession.id), updatedSession);
        queryClient.invalidateQueries({ queryKey: MentorshipKeys.sessionLists() });
      }
    }
  );
}

export function useDeleteMentorshipSession() {
  const queryClient = useQueryClient();

  return useApiDelete(
    (id: number) => `/incubator/mentorship-sessions/${id}/`,
    {
      onSuccess: (_, id) => {
        queryClient.removeQueries({ queryKey: MentorshipKeys.sessionDetail(id) });
        queryClient.invalidateQueries({ queryKey: MentorshipKeys.sessionLists() });
      }
    }
  );
}
