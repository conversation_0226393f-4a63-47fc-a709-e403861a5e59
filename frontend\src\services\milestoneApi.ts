import { apiRequest, PaginatedResponse } from './api';

// Types for Milestone and Goal Tracking
export interface BusinessMilestone {
  id: number;
  business_idea: number;
  business_idea_title: string;
  title: string;
  description: string;
  due_date: string;
  status: 'not_started' | 'in_progress' | 'completed' | 'delayed' | 'cancelled';
  status_display: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  priority_display: string;
  created_by: {
    id: number;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
  };
  assigned_to: {
    id: number;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
  } | null;
  completion_date: string | null;
  completion_notes: string | null;
  days_remaining: number;
  is_overdue: boolean;
  created_at: string;
  updated_at: string;
}

export interface BusinessGoal {
  id: number;
  business_idea: number;
  business_idea_title: string;
  title: string;
  description: string;
  timeframe: 'short_term' | 'medium_term' | 'long_term';
  timeframe_display: string;
  target_date: string | null;
  status: 'active' | 'achieved' | 'revised' | 'abandoned';
  status_display: string;
  created_by: {
    id: number;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
  };
  achievement_date: string | null;
  achievement_notes: string | null;
  progress_percentage: number;
  created_at: string;
  updated_at: string;
}

export interface MentorRecommendation {
  id: number;
  business_idea: number;
  business_idea_title: string;
  mentor: {
    id: number;
    user: {
      id: number;
      username: string;
      email: string;
      first_name: string;
      last_name: string;
    };
    bio: string;
    company: string | null;
    position: string | null;
    years_of_experience: number;
    availability: string;
    availability_display: string;
    expertise_areas: Array<{
      id: number;
      category_display: string;
      specific_expertise: string;
      level_display: string;
    }>;
  };
  match_score: number;
  match_reason: string;
  expertise_match: string;
  is_applied: boolean;
  is_matched: boolean;
  created_at: string;
  updated_at: string;
}

export interface BusinessAnalytics {
  id: number;
  business_idea: number;
  business_idea_title: string;
  progress_rate: number;
  milestone_completion_rate: number;
  goal_achievement_rate: number;
  team_size: number;
  mentor_engagement: number;
  industry_percentile: number;
  stage_percentile: number;
  similar_ideas: Array<{
    id: number;
    title: string;
    current_stage: string;
    owner: {
      username: string;
    };
    progress_count: number;
  }>;
  last_calculated: string;
}

// Business Milestones API
export const businessMilestonesAPI = {
  getMilestones: async (businessIdeaId?: number) => {
    try {
      const endpoint = businessIdeaId
        ? `/incubator/business-milestones/?business_idea=${businessIdeaId}`
        : '/incubator/business-milestones/';

      const response = await apiRequest<PaginatedResponse<BusinessMilestone>>(endpoint);

      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results;
      } else if (Array.isArray(response)) {
        return response;
      }

      console.error('API returned unexpected format for business milestones:', response);
      return [];
    } catch (error) {
      console.error('Error fetching business milestones:', error);
      return [];
    }
  },

  getMilestone: (id: number) =>
    apiRequest<BusinessMilestone>(`/incubator/business-milestones/${id}/`),

  createMilestone: (milestoneData: Partial<BusinessMilestone> & { business_idea: number, created_by_id: number }) =>
    apiRequest<BusinessMilestone>('/incubator/business-milestones/', 'POST', milestoneData),

  updateMilestone: (id: number, milestoneData: Partial<BusinessMilestone>) =>
    apiRequest<BusinessMilestone>(`/incubator/business-milestones/${id}/`, 'PUT', milestoneData),

  deleteMilestone: (id: number) =>
    apiRequest<void>(`/incubator/business-milestones/${id}/`, 'DELETE'),

  completeMilestone: (id: number, completionNotes: string = '') =>
    apiRequest<{ message: string, milestone: BusinessMilestone }>(`/incubator/business-milestones/${id}/complete/`, 'POST', {
      completion_notes: completionNotes
    }),
};

// Business Goals API
export const businessGoalsAPI = {
  getGoals: async (businessIdeaId?: number) => {
    try {
      const endpoint = businessIdeaId
        ? `/incubator/business-goals/?business_idea=${businessIdeaId}`
        : '/incubator/business-goals/';

      const response = await apiRequest<PaginatedResponse<BusinessGoal>>(endpoint);

      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results;
      } else if (Array.isArray(response)) {
        return response;
      }

      console.error('API returned unexpected format for business goals:', response);
      return [];
    } catch (error) {
      console.error('Error fetching business goals:', error);
      return [];
    }
  },

  getGoal: (id: number) =>
    apiRequest<BusinessGoal>(`/incubator/business-goals/${id}/`),

  createGoal: (goalData: Partial<BusinessGoal> & { business_idea: number, created_by_id: number }) =>
    apiRequest<BusinessGoal>('/incubator/business-goals/', 'POST', goalData),

  updateGoal: (id: number, goalData: Partial<BusinessGoal>) =>
    apiRequest<BusinessGoal>(`/incubator/business-goals/${id}/`, 'PUT', goalData),

  deleteGoal: (id: number) =>
    apiRequest<void>(`/incubator/business-goals/${id}/`, 'DELETE'),

  achieveGoal: (id: number, achievementNotes: string = '') =>
    apiRequest<{ message: string, goal: BusinessGoal }>(`/incubator/business-goals/${id}/achieve/`, 'POST', {
      achievement_notes: achievementNotes
    }),
};

// Mentor Recommendations API
export const mentorRecommendationsAPI = {
  getRecommendations: async (businessIdeaId?: number) => {
    try {
      const endpoint = businessIdeaId
        ? `/incubator/mentor-recommendations/?business_idea=${businessIdeaId}`
        : '/incubator/mentor-recommendations/';

      const response = await apiRequest<PaginatedResponse<MentorRecommendation>>(endpoint);

      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results;
      } else if (Array.isArray(response)) {
        return response;
      }

      console.error('API returned unexpected format for mentor recommendations:', response);
      return [];
    } catch (error) {
      console.error('Error fetching mentor recommendations:', error);
      return [];
    }
  },

  getRecommendation: (id: number) =>
    apiRequest<MentorRecommendation>(`/incubator/mentor-recommendations/${id}/`),

  generateRecommendations: (businessIdeaId: number) =>
    apiRequest<{ message: string, recommendations: MentorRecommendation[] }>(
      '/incubator/mentor-recommendations/generate_recommendations/',
      'POST',
      { business_idea_id: businessIdeaId }
    ),

  markAsApplied: (id: number) =>
    apiRequest<MentorRecommendation>(`/incubator/mentor-recommendations/${id}/`, 'PATCH', { is_applied: true }),

  markAsMatched: (id: number) =>
    apiRequest<MentorRecommendation>(`/incubator/mentor-recommendations/${id}/`, 'PATCH', { is_matched: true }),
};

// Business Analytics API
export const businessAnalyticsAPI = {
  getAnalytics: async (businessIdeaId?: number) => {
    try {
      const endpoint = businessIdeaId
        ? `/incubator/business-analytics/?business_idea=${businessIdeaId}`
        : '/incubator/business-analytics/';

      const response = await apiRequest<PaginatedResponse<BusinessAnalytics>>(endpoint);

      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results[0]; // Usually there's only one analytics object per business idea
      } else if (Array.isArray(response) && response.length > 0) {
        return response[0];
      }

      console.error('API returned unexpected format for business analytics:', response);
      return null;
    } catch (error) {
      console.error('Error fetching business analytics:', error);
      return null;
    }
  },

  calculateAnalytics: (businessIdeaId: number) =>
    apiRequest<{ message: string, analytics: BusinessAnalytics }>(
      '/incubator/business-analytics/calculate_analytics/',
      'POST',
      { business_idea_id: businessIdeaId }
    ),
};
