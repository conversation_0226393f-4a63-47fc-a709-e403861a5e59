import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../../store/hooks';
import { getAuthToken, getRefreshToken, authAPI } from '../../services/api';
import { Shield, CheckCircle, XCircle, RefreshCw, LogIn } from 'lucide-react';

const AuthStatusChecker: React.FC = () => {
  const { t } = useTranslation();
  const { user, isAuthenticated, isLoading } = useAppSelector(state => state.auth);
  const [tokenStatus, setTokenStatus] = useState<{
    hasAccessToken: boolean;
    hasRefreshToken: boolean;
    isAccessTokenValid: boolean;
    tokenExpiry?: string;
    error?: string;
  }>({
    hasAccessToken: false,
    hasRefreshToken: false,
    isAccessTokenValid: false
  });
  const [isChecking, setIsChecking] = useState(false);

  useEffect(() => {
    checkTokenStatus();
  }, []);

  const checkTokenStatus = async () => {
    setIsChecking(true);
    try {
      const accessToken = getAuthToken();
      const refreshToken = getRefreshToken();
      
      const status = {
        hasAccessToken: !!accessToken,
        hasRefreshToken: !!refreshToken,
        isAccessTokenValid: false,
        tokenExpiry: undefined as string | undefined,
        error: undefined as string | undefined
      };

      if (accessToken) {
        try {
          // Decode token to check expiry
          const payload = JSON.parse(atob(accessToken.split('.')[1]));
          const now = Math.floor(Date.now() / 1000);
          const isExpired = payload.exp < now;
          
          status.tokenExpiry = new Date(payload.exp * 1000).toLocaleString();
          status.isAccessTokenValid = !isExpired;
          
          if (isExpired) {
            status.error = 'Access token is expired';
          }
        } catch (e) {
          status.error = 'Failed to decode access token';
        }
      }

      setTokenStatus(status);
    } catch (error) {
      setTokenStatus(prev => ({
        ...prev,
        error: 'Failed to check token status'
      }));
    } finally {
      setIsChecking(false);
    }
  };

  const handleRefreshToken = async () => {
    setIsChecking(true);
    try {
      const success = await authAPI.refreshToken();
      if (success) {
        await checkTokenStatus();
        window.location.reload();
      } else {
        setTokenStatus(prev => ({
          ...prev,
          error: 'Token refresh failed'
        }));
      }
    } catch (error) {
      setTokenStatus(prev => ({
        ...prev,
        error: 'Token refresh error'
      }));
    } finally {
      setIsChecking(false);
    }
  };

  const handleLogin = () => {
    window.location.href = '/login';
  };

  // Only show in development
  if (!import.meta.env.DEV) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-gray-900/95 backdrop-blur-sm border border-gray-700 rounded-lg p-4 max-w-sm z-50">
      <div className="flex items-center mb-3">
        <Shield size={20} className="text-blue-400 mr-2" />
        <h3 className="text-white font-medium">Auth Status</h3>
        <button
          onClick={checkTokenStatus}
          disabled={isChecking}
          className="ml-auto p-1 text-gray-400 hover:text-white"
        >
          <RefreshCw size={16} className={isChecking ? 'animate-spin' : ''} />
        </button>
      </div>

      <div className="space-y-2 text-sm">
        {/* Redux Auth State */}
        <div className="flex items-center justify-between">
          <span className="text-gray-300">Redux Auth:</span>
          <div className="flex items-center">
            {isAuthenticated ? (
              <CheckCircle size={16} className="text-green-400" />
            ) : (
              <XCircle size={16} className="text-red-400" />
            )}
            <span className={`ml-1 ${isAuthenticated ? 'text-green-400' : 'text-red-400'}`}>
              {isAuthenticated ? 'Yes' : 'No'}
            </span>
          </div>
        </div>

        {/* User Info */}
        <div className="flex items-center justify-between">
          <span className="text-gray-300">User:</span>
          <span className="text-white text-xs">
            {user ? `${user.username} (${user.id})` : 'None'}
          </span>
        </div>

        {/* Access Token */}
        <div className="flex items-center justify-between">
          <span className="text-gray-300">Access Token:</span>
          <div className="flex items-center">
            {tokenStatus.hasAccessToken ? (
              tokenStatus.isAccessTokenValid ? (
                <CheckCircle size={16} className="text-green-400" />
              ) : (
                <XCircle size={16} className="text-red-400" />
              )
            ) : (
              <XCircle size={16} className="text-red-400" />
            )}
            <span className={`ml-1 text-xs ${
              tokenStatus.hasAccessToken && tokenStatus.isAccessTokenValid 
                ? 'text-green-400' 
                : 'text-red-400'
            }`}>
              {tokenStatus.hasAccessToken 
                ? (tokenStatus.isAccessTokenValid ? 'Valid' : 'Expired')
                : 'Missing'
              }
            </span>
          </div>
        </div>

        {/* Refresh Token */}
        <div className="flex items-center justify-between">
          <span className="text-gray-300">Refresh Token:</span>
          <div className="flex items-center">
            {tokenStatus.hasRefreshToken ? (
              <CheckCircle size={16} className="text-green-400" />
            ) : (
              <XCircle size={16} className="text-red-400" />
            )}
            <span className={`ml-1 text-xs ${tokenStatus.hasRefreshToken ? 'text-green-400' : 'text-red-400'}`}>
              {tokenStatus.hasRefreshToken ? 'Present' : 'Missing'}
            </span>
          </div>
        </div>

        {/* Token Expiry */}
        {tokenStatus.tokenExpiry && (
          <div className="text-xs text-gray-400">
            Expires: {tokenStatus.tokenExpiry}
          </div>
        )}

        {/* Error */}
        {tokenStatus.error && (
          <div className="text-xs text-red-400 bg-red-900/20 p-2 rounded">
            {tokenStatus.error}
          </div>
        )}

        {/* Actions */}
        <div className="flex gap-2 mt-3">
          {tokenStatus.hasRefreshToken && !tokenStatus.isAccessTokenValid && (
            <button
              onClick={handleRefreshToken}
              disabled={isChecking}
              className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800/50 text-white py-1 px-2 rounded text-xs"
            >
              Refresh
            </button>
          )}
          
          {!isAuthenticated && (
            <button
              onClick={handleLogin}
              className="flex-1 bg-purple-600 hover:bg-purple-700 text-white py-1 px-2 rounded text-xs flex items-center justify-center"
            >
              <LogIn size={12} className="mr-1" />
              Login
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default AuthStatusChecker;
