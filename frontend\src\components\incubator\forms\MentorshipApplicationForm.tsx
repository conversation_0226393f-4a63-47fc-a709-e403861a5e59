import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';
import { MentorshipApplication, MentorProfile } from '../../../services/incubatorApi';
import { Loader, Save, X, AlertCircle, User } from 'lucide-react';

interface MentorshipApplicationFormProps {
  initialData?: Partial<MentorshipApplication>;
  mentor?: MentorProfile;
  onSubmit: (data: Partial<MentorshipApplication>) => Promise<boolean>;
  onCancel: () => void;
  isSubmitting?: boolean;
  mode: 'create' | 'edit';
}

interface FormData {
  mentor: number;
  message: string;
  goals: string;
  expectations: string;
  availability: string;
  experience_level: 'beginner' | 'intermediate' | 'advanced';
  preferred_communication: 'email' | 'video_call' | 'chat' | 'in_person';
}

interface FormErrors {
  [key: string]: string;
}

const MentorshipApplicationForm: React.FC<MentorshipApplicationFormProps> = ({
  initialData,
  mentor,
  onSubmit,
  onCancel,
  isSubmitting = false,
  mode
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [formData, setFormData] = useState<FormData>({
    mentor: initialData?.mentor || mentor?.id || 0,
    message: initialData?.message || '',
    goals: initialData?.goals || '',
    expectations: initialData?.expectations || '',
    availability: initialData?.availability || '',
    experience_level: initialData?.experience_level || 'beginner',
    preferred_communication: initialData?.preferred_communication || 'video_call'
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  // Validation rules
  const validateField = (name: string, value: string): string => {
    switch (name) {
      case 'message':
        if (!value.trim()) return t('validation.required', 'This field is required');
        if (value.length < 50) return t('validation.minLength', 'Must be at least 50 characters');
        if (value.length > 1000) return t('validation.maxLength', 'Must be less than 1000 characters');
        break;
      case 'goals':
        if (!value.trim()) return t('validation.required', 'This field is required');
        if (value.length < 20) return t('validation.minLength', 'Must be at least 20 characters');
        break;
      case 'expectations':
        if (!value.trim()) return t('validation.required', 'This field is required');
        if (value.length < 20) return t('validation.minLength', 'Must be at least 20 characters');
        break;
      case 'availability':
        if (!value.trim()) return t('validation.required', 'This field is required');
        if (value.length < 10) return t('validation.minLength', 'Must be at least 10 characters');
        break;
    }
    return '';
  };

  // Validate all fields
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};
    
    Object.keys(formData).forEach(key => {
      if (key !== 'mentor' && key !== 'experience_level' && key !== 'preferred_communication') {
        const error = validateField(key, formData[key as keyof FormData] as string);
        if (error) {
          newErrors[key] = error;
        }
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // Handle field blur
  const handleBlur = (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    setTouched(prev => ({
      ...prev,
      [name]: true
    }));

    // Validate field on blur
    const error = validateField(name, value);
    setErrors(prev => ({
      ...prev,
      [name]: error
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Mark all fields as touched
    const allTouched = Object.keys(formData).reduce((acc, key) => {
      acc[key] = true;
      return acc;
    }, {} as Record<string, boolean>);
    setTouched(allTouched);

    // Validate form
    if (!validateForm()) {
      return;
    }

    // Submit form
    const success = await onSubmit(formData);
    if (success) {
      // Form will be closed by parent component
    }
  };

  const experienceLevelOptions = [
    { value: 'beginner', label: t('mentorship.experience.beginner', 'Beginner') },
    { value: 'intermediate', label: t('mentorship.experience.intermediate', 'Intermediate') },
    { value: 'advanced', label: t('mentorship.experience.advanced', 'Advanced') }
  ];

  const communicationOptions = [
    { value: 'email', label: t('mentorship.communication.email', 'Email') },
    { value: 'video_call', label: t('mentorship.communication.videoCall', 'Video Call') },
    { value: 'chat', label: t('mentorship.communication.chat', 'Chat/Messaging') },
    { value: 'in_person', label: t('mentorship.communication.inPerson', 'In Person') }
  ];

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <div className="bg-gray-900 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className={`flex items-center justify-between p-6 border-b border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div>
            <h2 className="text-xl font-semibold text-white">
              {mode === 'create' 
                ? t('mentorship.applyForMentorship', 'Apply for Mentorship')
                : t('mentorship.editApplication', 'Edit Application')
              }
            </h2>
            {mentor && (
              <div className="flex items-center gap-2 mt-2">
                <User size={16} className="text-gray-400" />
                <p className="text-gray-400 text-sm">
                  {t('mentorship.applyingTo', 'Applying to')}: {mentor.user.first_name} {mentor.user.last_name}
                </p>
              </div>
            )}
          </div>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-white transition-colors"
            disabled={isSubmitting}
          >
            <X size={24} />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Message */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('mentorship.message', 'Introduction Message')} *
            </label>
            <textarea
              name="message"
              value={formData.message}
              onChange={handleInputChange}
              onBlur={handleBlur}
              rows={4}
              className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white resize-vertical ${
                errors.message && touched.message ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder={t('mentorship.messagePlaceholder', 'Introduce yourself and explain why you want this mentor...')}
              disabled={isSubmitting}
            />
            {errors.message && touched.message && (
              <p className="mt-1 text-sm text-red-400 flex items-center">
                <AlertCircle size={16} className="mr-1" />
                {errors.message}
              </p>
            )}
          </div>

          {/* Goals */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('mentorship.goals', 'Your Goals')} *
            </label>
            <textarea
              name="goals"
              value={formData.goals}
              onChange={handleInputChange}
              onBlur={handleBlur}
              rows={3}
              className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white resize-vertical ${
                errors.goals && touched.goals ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder={t('mentorship.goalsPlaceholder', 'What do you hope to achieve through this mentorship?')}
              disabled={isSubmitting}
            />
            {errors.goals && touched.goals && (
              <p className="mt-1 text-sm text-red-400 flex items-center">
                <AlertCircle size={16} className="mr-1" />
                {errors.goals}
              </p>
            )}
          </div>

          {/* Expectations */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('mentorship.expectations', 'Your Expectations')} *
            </label>
            <textarea
              name="expectations"
              value={formData.expectations}
              onChange={handleInputChange}
              onBlur={handleBlur}
              rows={3}
              className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white resize-vertical ${
                errors.expectations && touched.expectations ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder={t('mentorship.expectationsPlaceholder', 'What do you expect from your mentor?')}
              disabled={isSubmitting}
            />
            {errors.expectations && touched.expectations && (
              <p className="mt-1 text-sm text-red-400 flex items-center">
                <AlertCircle size={16} className="mr-1" />
                {errors.expectations}
              </p>
            )}
          </div>

          {/* Availability */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('mentorship.availability', 'Your Availability')} *
            </label>
            <textarea
              name="availability"
              value={formData.availability}
              onChange={handleInputChange}
              onBlur={handleBlur}
              rows={2}
              className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white resize-vertical ${
                errors.availability && touched.availability ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder={t('mentorship.availabilityPlaceholder', 'When are you available for mentorship sessions?')}
              disabled={isSubmitting}
            />
            {errors.availability && touched.availability && (
              <p className="mt-1 text-sm text-red-400 flex items-center">
                <AlertCircle size={16} className="mr-1" />
                {errors.availability}
              </p>
            )}
          </div>

          {/* Experience Level and Communication Preference */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('mentorship.experienceLevel', 'Experience Level')} *
              </label>
              <select
                name="experience_level"
                value={formData.experience_level}
                onChange={handleInputChange}
                onBlur={handleBlur}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                disabled={isSubmitting}
              >
                {experienceLevelOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('mentorship.preferredCommunication', 'Preferred Communication')} *
              </label>
              <select
                name="preferred_communication"
                value={formData.preferred_communication}
                onChange={handleInputChange}
                onBlur={handleBlur}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                disabled={isSubmitting}
              >
                {communicationOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Form Actions */}
          <div className={`flex gap-4 pt-4 border-t border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <button
              type="button"
              onClick={onCancel}
              className="px-6 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors"
              disabled={isSubmitting}
            >
              {t('common.cancel', 'Cancel')}
            </button>
            <button
              type="submit"
              className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader size={16} className="animate-spin" />
                  {t('common.saving', 'Saving...')}
                </>
              ) : (
                <>
                  <Save size={16} />
                  {mode === 'create' 
                    ? t('mentorship.submitApplication', 'Submit Application')
                    : t('common.update', 'Update')
                  }
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default MentorshipApplicationForm;
