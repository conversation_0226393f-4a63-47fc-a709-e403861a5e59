import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Plus, Search, Edit, Trash2, Calendar, MapPin, Users, Loader2, Clock } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
// MainLayout removed - handled by routing system
import { Event, eventsAPI } from '../../services/api';
import { useAppSelector } from '../../store/hooks';
import { RTLText, RTLFlex, RTLIcon } from '../../components/rtl';

const UserEventsPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { language, isRTL } = useLanguage();
  const { user } = useAppSelector(state => state.auth);

  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [creating, setCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    date: '',
    location: '',
    is_virtual: false,
    virtual_link: '',
  });

  useEffect(() => {
    fetchUserEvents();
  }, [user]);

  const fetchUserEvents = async () => {
    setLoading(true);
    try {
      const allEvents = await eventsAPI.getEvents();
      // Filter events to only show the current user's events
      const userEvents = allEvents.filter(event => event.organizer.id === user?.id);
      setEvents(userEvents);
    } catch (error) {
      console.error('Error fetching user events:', error);
      setError(t('events.errors.failedToLoad', 'Failed to load events'));
    } finally {
      setLoading(false);
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const openCreateModal = () => {
    setFormData({
      title: '',
      description: '',
      date: '',
      location: '',
      is_virtual: false,
      virtual_link: '',
    });
    setError(null);
    setSuccess(null);
    setIsCreateModalOpen(true);
  };

  const openDeleteModal = (event: Event) => {
    setSelectedEvent(event);
    setIsDeleteModalOpen(true);
  };

  const handleCreateEvent = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    setCreating(true);
    setError(null);

    try {
      await eventsAPI.createEvent({
        ...formData,
        organizer_id: user.id,
      });

      setSuccess(t('events.success.created', 'Event created successfully'));
      fetchUserEvents();
      setIsCreateModalOpen(false);
      
      // Clear success message after delay
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error('Error creating event:', error);
      setError(t('events.errors.failedToCreate', 'Failed to create event'));
    } finally {
      setCreating(false);
    }
  };

  const handleDeleteEvent = async () => {
    if (!selectedEvent) return;

    setDeleting(true);
    try {
      await eventsAPI.deleteEvent(selectedEvent.id);
      setSuccess(t('events.success.deleted', 'Event deleted successfully'));
      fetchUserEvents();
      setIsDeleteModalOpen(false);
      setSelectedEvent(null);
      
      // Clear success message after delay
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error('Error deleting event:', error);
      setError(t('events.errors.failedToDelete', 'Failed to delete event'));
    } finally {
      setDeleting(false);
    }
  };

  // Filter events based on search term
  const filteredEvents = events.filter(event =>
    event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    event.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    event.location.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(language === 'ar' ? 'ar-SA' : undefined, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const isUpcoming = (dateString: string) => {
    return new Date(dateString) > new Date();
  };

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="p-6">
        {/* Header */}
        <div className={`mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
          <div>
            <RTLText as="h1" className="text-2xl font-bold text-white">{t('events.myEvents', 'My Events')}</RTLText>
            <RTLText as="div" className="text-gray-300 mt-1">{t('events.manageYourEvents', 'Manage your organized events')}</RTLText>
          </div>
          <button
            onClick={openCreateModal}
            className="mt-4 sm:mt-0 px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300 flex items-center justify-center"
          >
            <Plus className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
            {t('events.createNewEvent', 'Create New Event')}
          </button>
        </div>

        {/* Error/Success Messages */}
        {error && (
          <div className="mb-4 p-3 bg-red-900/50 border border-red-500 rounded-md text-red-200">
            {error}
          </div>
        )}

        {success && (
          <div className="mb-4 p-3 bg-green-900/50 border border-green-500 rounded-md text-green-200">
            {success}
          </div>
        )}

        {/* Search */}
        <div className={`mb-6 relative ${isRTL ? "flex-row-reverse" : ""}`}>
          <input
            type="text"
            placeholder={t('events.searchEvents', 'Search your events...')}
            value={searchTerm}
            onChange={handleSearchChange}
            className={`w-full px-4 py-2 ${language === 'ar' ? 'pr-10' : 'pl-10'} bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400`}
            dir={language === 'ar' ? 'rtl' : 'ltr'}
          />
          <Search size={18} className={`absolute ${language === 'ar' ? 'right-3' : 'left-3'} top-2.5 text-gray-400`} />
        </div>

        {/* Events List */}
        {loading ? (
          <div className="flex justify-center py-12">
            <div className="flex items-center space-x-3">
              <Loader2 className="w-6 h-6 animate-spin" />
              <span>{t('common.loading', 'Loading...')}</span>
            </div>
          </div>
        ) : filteredEvents.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredEvents.map(event => (
              <div
                key={event.id}
                className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:border-purple-500/50 transition-colors"
              >
                <div className={`flex justify-between items-start mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className="flex-1">
                    <RTLText as="h3" className="text-xl font-bold mb-2">{event.title}</RTLText>
                    <RTLText as="div" className="text-gray-300 mb-3 line-clamp-3">
                      {event.description}
                    </RTLText>
                  </div>
                  <div className={`flex space-x-2 ml-4 ${isRTL ? "flex-row-reverse space-x-reverse mr-4 ml-0" : ""}`}>
                    <Link
                      to={`/events/edit/${event.id}`}
                      className="p-2 bg-blue-900/50 hover:bg-blue-800/50 rounded-md transition-colors"
                      title={t('common.edit', 'Edit')}
                    >
                      <Edit size={16} />
                    </Link>
                    <button
                      onClick={() => openDeleteModal(event)}
                      className="p-2 bg-red-900/50 hover:bg-red-800/50 rounded-md transition-colors"
                      title={t('common.delete', 'Delete')}
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>

                <div className="space-y-2 text-sm text-gray-400">
                  <RTLFlex align="center">
                    <Calendar className={`w-4 h-4 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                    <span className={isUpcoming(event.date) ? 'text-green-400' : 'text-gray-400'}>
                      {formatDate(event.date)}
                    </span>
                    {isUpcoming(event.date) && (
                      <span className="ml-2 px-2 py-1 bg-green-900/30 text-green-300 rounded-full text-xs">
                        {t('events.upcoming', 'Upcoming')}
                      </span>
                    )}
                  </RTLFlex>
                  
                  <RTLFlex align="center">
                    <MapPin className={`w-4 h-4 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                    {event.is_virtual ? (
                      <span className="text-blue-400">{t('events.virtual', 'Virtual Event')}</span>
                    ) : (
                      <span>{event.location}</span>
                    )}
                  </RTLFlex>

                  <RTLFlex align="center">
                    <Users className={`w-4 h-4 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                    {event.attendees?.length || 0} {t('events.attendees', 'attendees')}
                  </RTLFlex>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20">
            <Calendar size={48} className="mx-auto text-gray-500 mb-4" />
            <RTLText as="h3" className="text-xl font-semibold mb-2">
              {searchTerm ? t('events.noEventsFound', 'No events found') : t('events.noEventsYet', 'No events yet')}
            </RTLText>
            <RTLText as="div" className="text-gray-400 mb-4">
              {searchTerm 
                ? t('events.tryDifferentSearch', 'Try a different search term')
                : t('events.createFirstEvent', 'Create your first event to get started')
              }
            </RTLText>
            <button
              onClick={openCreateModal}
              className="px-4 py-2 bg-purple-600 rounded-lg hover:bg-purple-700 transition-colors inline-flex items-center"
            >
              <Plus className={`w-4 h-4 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
              {t('events.createEvent', 'Create Event')}
            </button>
          </div>
        )}
      </div>

      {/* Create Event Modal */}
      {isCreateModalOpen && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
          <div className="bg-indigo-900 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto p-6">
            <RTLText as="h3" className="text-xl font-bold mb-6">{t('events.createEvent', 'Create Event')}</RTLText>
            
            <form onSubmit={handleCreateEvent} className="space-y-4">
              <div>
                <label className="block text-gray-300 mb-2">{t('events.title', 'Title')} *</label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                  required
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                />
              </div>

              <div>
                <label className="block text-gray-300 mb-2">{t('events.description', 'Description')} *</label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-4 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none"
                  required
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                />
              </div>

              <div>
                <label className="block text-gray-300 mb-2">{t('events.dateTime', 'Date & Time')} *</label>
                <input
                  type="datetime-local"
                  name="date"
                  value={formData.date}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                  required
                />
              </div>

              <div>
                <label className="flex items-center space-x-2 text-gray-300 mb-2">
                  <input
                    type="checkbox"
                    name="is_virtual"
                    checked={formData.is_virtual}
                    onChange={handleInputChange}
                    className="rounded"
                  />
                  <span>{t('events.virtualEvent', 'Virtual Event')}</span>
                </label>
              </div>

              {formData.is_virtual ? (
                <div>
                  <label className="block text-gray-300 mb-2">{t('events.virtualLink', 'Virtual Link')}</label>
                  <input
                    type="url"
                    name="virtual_link"
                    value={formData.virtual_link}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="https://..."
                  />
                </div>
              ) : (
                <div>
                  <label className="block text-gray-300 mb-2">{t('events.location', 'Location')} *</label>
                  <input
                    type="text"
                    name="location"
                    value={formData.location}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                    required={!formData.is_virtual}
                    dir={language === 'ar' ? 'rtl' : 'ltr'}
                  />
                </div>
              )}

              {error && (
                <div className="p-3 bg-red-900/50 border border-red-500 rounded-md text-red-200">
                  {error}
                </div>
              )}

              <div className={`flex justify-end space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                <button
                  type="button"
                  onClick={() => setIsCreateModalOpen(false)}
                  className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
                  disabled={creating}
                >
                  {t('common.cancel', 'Cancel')}
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors flex items-center disabled:opacity-50"
                  disabled={creating}
                >
                  {creating ? (
                    <>
                      <Loader2 className={`w-4 h-4 animate-spin ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                      {t('common.creating', 'Creating...')}
                    </>
                  ) : (
                    <>
                      <Plus className={`w-4 h-4 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                      {t('events.createEvent', 'Create Event')}
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {isDeleteModalOpen && selectedEvent && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
          <div className="bg-indigo-900 rounded-lg w-full max-w-md p-6">
            <RTLText as="h3" className="text-xl font-bold mb-4">{t('events.confirmDelete', 'Confirm Delete')}</RTLText>
            <RTLText as="div" className="text-gray-300 mb-6">
              {t('events.deleteWarning', 'Are you sure you want to delete this event? This action cannot be undone.')}
            </RTLText>
            <RTLText as="div" className="text-sm text-gray-400 mb-6 p-3 bg-indigo-950/50 rounded border-l-4 border-purple-500">
              <strong>{selectedEvent.title}</strong>
              <br />
              {formatDate(selectedEvent.date)}
            </RTLText>
            <div className={`flex justify-end space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
              <button
                onClick={() => setIsDeleteModalOpen(false)}
                className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
                disabled={deleting}
              >
                {t('common.cancel', 'Cancel')}
              </button>
              <button
                onClick={handleDeleteEvent}
                className="px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors flex items-center disabled:opacity-50"
                disabled={deleting}
              >
                {deleting ? (
                  <>
                    <Loader2 className={`w-4 h-4 animate-spin ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                    {t('common.deleting', 'Deleting...')}
                  </>
                ) : (
                  <>
                    <Trash2 className={`w-4 h-4 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                    {t('common.delete', 'Delete')}
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
              </div>
        </div>
      </div>
    </div>
  );
};

export default UserEventsPage;
