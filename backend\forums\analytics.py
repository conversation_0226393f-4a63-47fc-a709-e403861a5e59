from django.db.models import Count, Sum, Avg, F, Q, Case, When, IntegerField, DateTimeField
from django.db.models.functions import <PERSON>runc<PERSON><PERSON>, <PERSON>runcWeek, <PERSON>runcMonth
from django.utils import timezone
from datetime import timedelta
from django.contrib.auth.models import User
from .models import (
    ForumCategory, ForumTopic, ForumThread, ForumPost,
    UserReputation, ReputationActivity, TopicSubscription, ThreadSubscription
)

def get_forum_overview_stats():
    """Get overview statistics for the forum"""
    now = timezone.now()
    last_week = now - timedelta(days=7)
    last_month = now - timedelta(days=30)
    
    # Total counts
    total_threads = ForumThread.objects.count()
    total_posts = ForumPost.objects.count()
    total_users = User.objects.filter(
        Q(forum_threads__isnull=False) | Q(forum_posts__isnull=False)
    ).distinct().count()
    
    # Recent activity
    new_threads_week = ForumThread.objects.filter(created_at__gte=last_week).count()
    new_posts_week = ForumPost.objects.filter(created_at__gte=last_week).count()
    active_users_week = User.objects.filter(
        Q(forum_threads__created_at__gte=last_week) | 
        Q(forum_posts__created_at__gte=last_week)
    ).distinct().count()
    
    # Monthly activity
    new_threads_month = ForumThread.objects.filter(created_at__gte=last_month).count()
    new_posts_month = ForumPost.objects.filter(created_at__gte=last_month).count()
    active_users_month = User.objects.filter(
        Q(forum_threads__created_at__gte=last_month) | 
        Q(forum_posts__created_at__gte=last_month)
    ).distinct().count()
    
    # Most active categories
    active_categories = ForumCategory.objects.annotate(
        thread_count=Count('topics__forum_threads', distinct=True),
        post_count=Count('topics__forum_threads__forum_posts', distinct=True),
        total_activity=Count('topics__forum_threads', distinct=True) + 
                      Count('topics__forum_threads__forum_posts', distinct=True)
    ).order_by('-total_activity')[:5]
    
    # Most active topics
    active_topics = ForumTopic.objects.annotate(
        thread_count=Count('forum_threads', distinct=True),
        post_count=Count('forum_threads__forum_posts', distinct=True),
        total_activity=Count('forum_threads', distinct=True) + 
                      Count('forum_threads__forum_posts', distinct=True)
    ).order_by('-total_activity')[:5]
    
    return {
        'total_stats': {
            'threads': total_threads,
            'posts': total_posts,
            'users': total_users,
        },
        'weekly_stats': {
            'new_threads': new_threads_week,
            'new_posts': new_posts_week,
            'active_users': active_users_week,
            'thread_growth': (new_threads_week / total_threads * 100) if total_threads else 0,
            'post_growth': (new_posts_week / total_posts * 100) if total_posts else 0,
        },
        'monthly_stats': {
            'new_threads': new_threads_month,
            'new_posts': new_posts_month,
            'active_users': active_users_month,
            'thread_growth': (new_threads_month / total_threads * 100) if total_threads else 0,
            'post_growth': (new_posts_month / total_posts * 100) if total_posts else 0,
        },
        'active_categories': [
            {
                'id': cat.id,
                'name': cat.name,
                'thread_count': cat.thread_count,
                'post_count': cat.post_count,
                'total_activity': cat.total_activity
            } for cat in active_categories
        ],
        'active_topics': [
            {
                'id': topic.id,
                'title': topic.title,
                'category': topic.category.name,
                'thread_count': topic.thread_count,
                'post_count': topic.post_count,
                'total_activity': topic.total_activity
            } for topic in active_topics
        ]
    }


def get_activity_over_time(period='day', days=30):
    """
    Get forum activity over time
    
    Args:
        period (str): Time period to group by ('day', 'week', 'month')
        days (int): Number of days to look back
    """
    now = timezone.now()
    start_date = now - timedelta(days=days)
    
    # Choose the right truncation function based on period
    if period == 'week':
        trunc_func = TruncWeek
    elif period == 'month':
        trunc_func = TruncMonth
    else:  # default to day
        trunc_func = TruncDay
    
    # Get thread activity
    thread_activity = ForumThread.objects.filter(
        created_at__gte=start_date
    ).annotate(
        period=trunc_func('created_at')
    ).values('period').annotate(
        count=Count('id')
    ).order_by('period')
    
    # Get post activity
    post_activity = ForumPost.objects.filter(
        created_at__gte=start_date
    ).annotate(
        period=trunc_func('created_at')
    ).values('period').annotate(
        count=Count('id')
    ).order_by('period')
    
    # Get user activity (unique users who created content)
    user_activity = User.objects.filter(
        Q(forum_threads__created_at__gte=start_date) | 
        Q(forum_posts__created_at__gte=start_date)
    ).annotate(
        period=trunc_func(
            Case(
                When(forum_threads__created_at__gte=start_date, then=F('forum_threads__created_at')),
                When(forum_posts__created_at__gte=start_date, then=F('forum_posts__created_at')),
                output_field=DateTimeField()
            )
        )
    ).values('period').annotate(
        count=Count('id', distinct=True)
    ).order_by('period')
    
    return {
        'thread_activity': [
            {
                'period': item['period'].strftime('%Y-%m-%d'),
                'count': item['count']
            } for item in thread_activity
        ],
        'post_activity': [
            {
                'period': item['period'].strftime('%Y-%m-%d'),
                'count': item['count']
            } for item in post_activity
        ],
        'user_activity': [
            {
                'period': item['period'].strftime('%Y-%m-%d'),
                'count': item['count']
            } for item in user_activity
        ]
    }


def get_top_contributors(limit=10):
    """Get top forum contributors"""
    top_users = UserReputation.objects.select_related('user').order_by('-points')[:limit]
    
    return [
        {
            'user_id': rep.user.id,
            'username': rep.user.username,
            'points': rep.points,
            'level': rep.level,
            'threads_created': rep.threads_created,
            'posts_created': rep.posts_created,
            'solutions_provided': rep.solutions_provided,
            'likes_received': rep.likes_received
        } for rep in top_users
    ]


def get_popular_threads(limit=10, period=None):
    """
    Get popular threads
    
    Args:
        limit (int): Number of threads to return
        period (str, optional): Time period ('week', 'month', 'year', None for all time)
    """
    queryset = ForumThread.objects.filter(moderation_status='approved')
    
    # Apply time filter if specified
    if period:
        now = timezone.now()
        if period == 'week':
            start_date = now - timedelta(days=7)
        elif period == 'month':
            start_date = now - timedelta(days=30)
        elif period == 'year':
            start_date = now - timedelta(days=365)
        
        queryset = queryset.filter(created_at__gte=start_date)
    
    # Annotate with activity metrics
    popular_threads = queryset.annotate(
        post_count=Count('forum_posts', distinct=True),
        like_count=Count('forum_posts__likes', distinct=True),
        activity_score=Count('forum_posts') * 2 + F('views')  # Custom activity score
    ).order_by('-activity_score')[:limit]
    
    return [
        {
            'id': thread.id,
            'title': thread.title,
            'author': thread.author.username,
            'topic': thread.topic.title,
            'category': thread.topic.category.name,
            'views': thread.views,
            'post_count': thread.post_count,
            'like_count': thread.like_count,
            'activity_score': thread.activity_score,
            'created_at': thread.created_at
        } for thread in popular_threads
    ]


def get_user_engagement_stats():
    """Get user engagement statistics"""
    # Total users who have participated in the forum
    total_forum_users = User.objects.filter(
        Q(forum_threads__isnull=False) | Q(forum_posts__isnull=False)
    ).distinct().count()
    
    # Total registered users
    total_users = User.objects.count()
    
    # Participation rate
    participation_rate = (total_forum_users / total_users * 100) if total_users else 0
    
    # Average posts per user
    avg_posts_per_user = ForumPost.objects.count() / total_forum_users if total_forum_users else 0
    
    # Average threads per user
    avg_threads_per_user = ForumThread.objects.count() / total_forum_users if total_forum_users else 0
    
    # Users with subscriptions
    users_with_subscriptions = User.objects.filter(
        Q(topic_subscriptions__isnull=False) | Q(thread_subscriptions__isnull=False)
    ).distinct().count()
    
    # Subscription rate
    subscription_rate = (users_with_subscriptions / total_forum_users * 100) if total_forum_users else 0
    
    return {
        'total_forum_users': total_forum_users,
        'total_users': total_users,
        'participation_rate': participation_rate,
        'avg_posts_per_user': avg_posts_per_user,
        'avg_threads_per_user': avg_threads_per_user,
        'users_with_subscriptions': users_with_subscriptions,
        'subscription_rate': subscription_rate
    }
