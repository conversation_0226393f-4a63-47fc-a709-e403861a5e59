import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { User, adminAPI, UserProfile } from '../services/api';
import { DashboardStats, RecentActivity } from '../types/admin';

// Define the state interface
interface AdminState {
  dashboardStats: DashboardStats;
  recentActivity: RecentActivity[];
  users: User[];
  isLoading: boolean;
  error: string | null;
}

// Initial state
const initialState: AdminState = {
  dashboardStats: {
    users: {
      total_users: 0,
      active_users: 0,
      new_users: 0
    },
    events: {
      total_events: 0,
      upcoming_events: 0,
      new_events: 0
    },
    resources: {
      total_resources: 0,
      resources_by_type: {},
      new_resources: 0
    },
    posts: {
      total_posts: 0,
      popular_posts: [],
      new_posts: 0
    }
  },
  recentActivity: [],
  users: [],
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchDashboardStats = createAsyncThunk(
  'admin/fetchDashboardStats',
  async (_, { rejectWithValue }) => {
    try {
      // Fetch real data from the API
      const stats = await adminAPI.getAllStats();
      return stats;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to fetch dashboard stats');
    }
  }
);

export const fetchUsers = createAsyncThunk(
  'admin/fetchUsers',
  async (_, { rejectWithValue }) => {
    try {
      // Fetch real users from the API
      const users = await adminAPI.getUsers();

      // Ensure we always return an array
      if (!Array.isArray(users)) {
        console.error('API returned non-array users:', users);
        return [];
      }

      return users;
    } catch (err) {
      console.error('Error fetching users:', err);
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to fetch users');
    }
  }
);

export const createUser = createAsyncThunk(
  'admin/createUser',
  async (userData: {
    username: string,
    email: string,
    password: string,
    password_confirm: string,
    first_name?: string,
    last_name?: string,
    role?: string,
    is_active?: boolean
  }, { rejectWithValue }) => {
    try {
      console.log('🚀 CREATE USER API CALL - Data:', userData);
      // Create user via the API
      const newUser = await adminAPI.createUser(userData);
      console.log('✅ CREATE USER API SUCCESS - Response:', newUser);

      return newUser;
    } catch (err) {
      console.error('❌ CREATE USER API ERROR:', err);
      console.error('Error details:', {
        message: err instanceof Error ? err.message : 'Unknown error',
        stack: err instanceof Error ? err.stack : undefined,
        type: typeof err,
        err
      });
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to create user');
    }
  }
);

export const updateUser = createAsyncThunk(
  'admin/updateUser',
  async (
    {
      userId,
      userData,
      profileData
    }: {
      userId: number,
      userData?: {
        username?: string,
        email?: string,
        first_name?: string,
        last_name?: string,
        is_admin?: boolean
      },
      profileData?: Partial<UserProfile>
    },
    { rejectWithValue }
  ) => {
    try {
      // Update user data if provided
      let updatedUser = null;
      if (userData) {
        updatedUser = await adminAPI.updateUser(userId, userData);
      }

      // Update profile data if provided
      if (profileData) {
        await adminAPI.updateUserProfile(userId, profileData);
      }

      // Return the updated user data
      return updatedUser || { id: userId };
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to update user');
    }
  }
);

export const deleteUser = createAsyncThunk(
  'admin/deleteUser',
  async (userId: number, { rejectWithValue }) => {
    try {
      console.log('🚀 DELETE USER API CALL - User ID:', userId);
      // Delete user from the API
      await adminAPI.deleteUser(userId);
      console.log('✅ DELETE USER API SUCCESS - User ID:', userId);
      return userId;
    } catch (err) {
      console.error('❌ DELETE USER API ERROR:', err);
      console.error('Error details:', {
        message: err instanceof Error ? err.message : 'Unknown error',
        stack: err instanceof Error ? err.stack : undefined,
        type: typeof err,
        err
      });
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to delete user');
    }
  }
);



export const fetchRecentActivity = createAsyncThunk(
  'admin/fetchRecentActivity',
  async (_, { rejectWithValue }) => {
    try {
      // Fetch real activity data from the API
      const activities = await adminAPI.getRecentActivity();
      return activities;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to fetch recent activity');
    }
  }
);

// Create the slice
const adminSlice = createSlice({
  name: 'admin',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch dashboard stats
    builder.addCase(fetchDashboardStats.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchDashboardStats.fulfilled, (state, action) => {
      state.isLoading = false;
      state.dashboardStats = action.payload;
      state.error = null;
    });
    builder.addCase(fetchDashboardStats.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    // Fetch users
    builder.addCase(fetchUsers.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchUsers.fulfilled, (state, action) => {
      state.isLoading = false;
      // Ensure users is always an array
      state.users = Array.isArray(action.payload) ? action.payload : [];
      state.error = null;

      // Log the users for debugging
      console.log('Users loaded into state:', state.users);
    });
    builder.addCase(fetchUsers.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    // Create user
    builder.addCase(createUser.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(createUser.fulfilled, (state, action) => {
      state.isLoading = false;
      state.error = null;

      // Immediately add the new user to the state
      if (action.payload && typeof action.payload === 'object') {
        console.log('✅ CREATE USER SUCCESS - Adding new user to state:', action.payload);
        state.users.push(action.payload);
        console.log('✅ New users array length:', state.users.length);
      } else {
        console.error('❌ CREATE USER - Invalid payload:', action.payload);
      }
    });
    builder.addCase(createUser.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    // Update user
    builder.addCase(updateUser.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(updateUser.fulfilled, (state, action) => {
      state.isLoading = false;
      state.error = null;

      // Update the user in the state immediately
      if (action.payload && typeof action.payload === 'object' && action.payload.id) {
        const updatedUser = action.payload;
        const userIndex = state.users.findIndex(user => user.id === updatedUser.id);
        if (userIndex !== -1) {
          state.users[userIndex] = { ...state.users[userIndex], ...updatedUser };
          console.log('Updated user in state:', updatedUser);
        }
      } else {
        // If we only have userId (profile update), refresh the users list
        console.log('Profile updated, refreshing users list');
        // We'll need to fetch the updated user data
      }
    });
    builder.addCase(updateUser.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    // Delete user
    builder.addCase(deleteUser.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(deleteUser.fulfilled, (state, action) => {
      state.isLoading = false;
      state.error = null;

      // Remove the user from the state immediately
      if (action.meta.arg) {
        const userId = action.meta.arg;
        const beforeLength = state.users.length;
        state.users = state.users.filter(user => user.id !== userId);
        console.log('✅ DELETE USER SUCCESS - Removed user ID:', userId);
        console.log('✅ Users array length before:', beforeLength, 'after:', state.users.length);
      } else {
        console.error('❌ DELETE USER - No userId in action.meta.arg');
      }
    });
    builder.addCase(deleteUser.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    // Fetch recent activity
    builder.addCase(fetchRecentActivity.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchRecentActivity.fulfilled, (state, action) => {
      state.isLoading = false;
      state.recentActivity = action.payload;
      state.error = null;
    });
    builder.addCase(fetchRecentActivity.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });


  },
});

// Export actions and reducer
export const { clearError } = adminSlice.actions;
export default adminSlice.reducer;
