import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Bar, Line, Doughnut } from 'react-chartjs-2';
import { DashboardStats } from '../../../services/api';
import { ChartContainer, chartOptions, doughnutOptions, chartColors } from './charts';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);
interface DashboardChartsProps {
  stats: DashboardStats;
}

const DashboardCharts: React.FC<DashboardChartsProps> = ({ stats  }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  // User growth chart data
  const userGrowthData = {
    labels: Object.keys(stats.users?.users_by_month || {}),
    datasets: [
      {
        label: t("admin.newUsers", "New Users"),
        data: Object.values(stats.users?.users_by_month || {}),
        borderColor: chartColors.indigo.primary,
        backgroundColor: chartColors.indigo.light,
      },
    ],
  };

  // Events by month chart data
  const eventsByMonthData = {
    labels: Object.keys(stats.events?.events_by_month || {}),
    datasets: [
      {
        label: t("admin.events", "Events"),
        data: Object.values(stats.events?.events_by_month || {}),
        borderColor: chartColors.purple.primary,
        backgroundColor: chartColors.purple.light,
      },
    ],
  };

  // Resources by type chart data
  const resourcesByTypeData = {
    labels: Object.keys(stats.resources?.resources_by_type || {}),
    datasets: [
      {
        label: t("admin.resources", "Resources"),
        data: Object.values(stats.resources?.resources_by_type || {}),
        backgroundColor: [
          chartColors.red.light,
          chartColors.orange.light,
          chartColors.amber.light,
          chartColors.emerald.light,
          chartColors.cyan.light,
          chartColors.indigo.light,
        ],
        borderColor: [
          chartColors.red.primary,
          chartColors.orange.primary,
          chartColors.amber.primary,
          chartColors.emerald.primary,
          chartColors.cyan.primary,
          chartColors.indigo.primary,
        ],
        borderWidth: 1,
      },
    ],
  };

  // Popular posts chart data
  const popularPostsData = {
    labels: stats.posts?.popular_posts?.map(post => post.title.substring(0, 20) + (post.title.length > 20 ? '...' : '')) || [],
    datasets: [
      {
        label: t("admin.likes", "Likes"),
        data: stats.posts?.popular_posts?.map(post => post.like_count) || [],
        backgroundColor: chartColors.sky.light,
        borderColor: chartColors.sky.primary,
        borderWidth: 1,
      },
    ],
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
      <ChartContainer title={t("admin.userGrowth", "User Growth")}>
        <Line
          options={{
            ...chartOptions,
            plugins: {
              ...chartOptions.plugins,
              title: {
                ...chartOptions.plugins.title,
                text: t("admin.newUsersBy", "New Users by Month")
              },
            },
          }}
          data={userGrowthData}
        />
      </ChartContainer>

      <ChartContainer title={t("admin.eventsByMonth", "Events by Month")}>
        <Line
          options={{
            ...chartOptions,
            plugins: {
              ...chartOptions.plugins,
              title: {
                ...chartOptions.plugins.title,
                text: t("admin.eventsByMonth", "Events by Month")
              },
            },
          }}
          data={eventsByMonthData}
        />
      </ChartContainer>

      <ChartContainer title={t("admin.resourcesByType", "Resources by Type")}>
        <Doughnut
          options={{
            ...doughnutOptions,
            plugins: {
              ...doughnutOptions.plugins,
              title: {
                ...doughnutOptions.plugins.title,
                text: t("admin.resourceDistribution", "Resource Distribution")
              },
            },
          }}
          data={resourcesByTypeData}
        />
      </ChartContainer>

      <ChartContainer title={t("admin.popularPosts", "Popular Posts")}>
        <Bar
          options={{
            ...chartOptions,
            plugins: {
              ...chartOptions.plugins,
              title: {
                ...chartOptions.plugins.title,
                text: t("admin.mostLikedPosts", "Most Liked Posts")
              },
            },
            indexAxis: 'y' as const,
          }}
          data={popularPostsData}
        />
      </ChartContainer>
    </div>
  );
};

export default DashboardCharts;
