import React from 'react';
import { Lightbulb, Users, Clock, ArrowRight, Tag } from 'lucide-react';
import { Link } from 'react-router-dom';
import { BusinessIdea } from '../../services/incubatorApi';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../../store/hooks';
import { useLanguage } from '../../hooks/useLanguage';
interface BusinessIdeaCardProps {
  idea: BusinessIdea;
}

const BusinessIdeaCard: React.FC<BusinessIdeaCardProps> = ({ idea  }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { language } = useAppSelector(state => state.language);

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Get stage badge color
  const getStageBadgeColor = (stage: string) => {
    switch (stage) {
      case 'concept':
        return 'bg-blue-600/50 text-blue-200';
      case 'validation':
        return 'bg-purple-600/50 text-purple-200';
      case 'development':
        return 'bg-green-600/50 text-green-200';
      case 'scaling':
        return 'bg-yellow-600/50 text-yellow-200';
      case 'established':
        return 'bg-red-600/50 text-red-200';
      default:
        return 'bg-gray-600/50 text-gray-200';
    }
  };

  // Get moderation status badge color
  const getModerationBadgeColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-600/50 text-green-200';
      case 'pending':
        return 'bg-yellow-600/50 text-yellow-200';
      case 'rejected':
        return 'bg-red-600/50 text-red-200';
      default:
        return 'bg-gray-600/50 text-gray-200';
    }
  };

  // Format stage name for display
  const formatStageName = (stage: string) => {
    return t(`incubator.stages.${stage}`);
  };

  return (
    <div className="bg-indigo-900/50 rounded-lg overflow-hidden border border-indigo-800 hover:border-purple-500 transition-all duration-300 hover:shadow-glow">
      {idea.image && (
        <div className="h-48 overflow-hidden">
          <img
            src={idea.image}
            alt={idea.title}
            className="w-full h-full object-cover"
          />
        </div>
      )}
      <div className="p-6">
        <div className={`flex justify-between items-start mb-3 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`flex space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
            <span className={`px-2 py-1 rounded-md text-xs font-medium ${getStageBadgeColor(idea.current_stage)}`}>
              {formatStageName(idea.current_stage)}
            </span>
            {idea.moderation_status !== 'approved' && (
              <span className={`px-2 py-1 rounded-md text-xs font-medium ${getModerationBadgeColor(idea.moderation_status)}`}>
                {t(`incubator.status.${idea.moderation_status}`)}
              </span>
            )}
          </div>
          <div className={`flex items-center text-gray-400 text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
            <Clock size={14} className={language === 'ar' ? 'ml-1' : 'mr-1'} />
            {formatDate(idea.created_at)}
          </div>
        </div>

        <h3 className="text-xl font-bold mb-2 text-white">{idea.title}</h3>

        <div className="text-gray-300 mb-4 line-clamp-3">
          {idea.description}
        </div>

        {idea.tags && idea.tags.length > 0 && (
          <div className={`flex flex-wrap gap-2 mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            {idea.tags.map(tag => (
              <span key={tag.id} className={`bg-indigo-800/50 text-indigo-200 px-2 py-1 rounded text-xs flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <Tag size={12} className={language === 'ar' ? 'ml-1' : 'mr-1'} />
                {tag.name}
              </span>
            ))}
          </div>
        )}

        <div className={`flex justify-between items-center mt-4 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`flex items-center text-gray-300 ${isRTL ? "flex-row-reverse" : ""}`}>
            <Users size={16} className={language === 'ar' ? 'ml-1' : 'mr-1'} />
            <span className="text-sm">
              {idea.collaborators.length > 0
                ? `${idea.owner.username} + ${idea.collaborators.length} ${t('incubator.businessIdeaCard.collaborators')}`
                : idea.owner.username}
            </span>
          </div>

          <Link
            to={`/incubator/ideas/${idea.id}`}
            className={`text-purple-400 hover:text-purple-300 flex items-center text-sm font-medium ${isRTL ? "flex-row-reverse" : ""}`}
          >
            {t('incubator.businessIdeaCard.viewDetails')} <ArrowRight size={16} className={language === 'ar' ? 'mr-1' : 'ml-1'} />
          </Link>
        </div>
      </div>
    </div>
  );
};

export default BusinessIdeaCard;
