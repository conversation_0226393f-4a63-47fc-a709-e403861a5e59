import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from '../../components/ui/card';
import Button from '../../components/ui/Button';
import { Badge } from '../../components/ui/badge';
import { Input } from '../../components/ui/input';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { Calendar, Clock, Plus, Edit, Trash2, Save, X, CheckCircle } from 'lucide-react';

interface TimeSlot {
  id: string;
  day: string;
  startTime: string;
  endTime: string;
  isRecurring: boolean;
  maxBookings: number;
  currentBookings: number;
  sessionType: 'video' | 'phone' | 'in-person' | 'any';
  isActive: boolean;
}

interface AvailabilitySettings {
  timezone: string;
  bufferTime: number; // minutes between sessions
  maxDailyBookings: number;
  advanceBookingDays: number;
  autoAcceptBookings: boolean;
  allowWeekendBookings: boolean;
  sessionDurations: number[]; // available durations in minutes
}

interface BookingRequest {
  id: string;
  menteeName: string;
  requestedDate: string;
  requestedTime: string;
  duration: number;
  topic: string;
  status: 'pending' | 'approved' | 'rejected';
  message?: string;
}

const MentorAvailabilityPage: React.FC = () => {
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);
  const [settings, setSettings] = useState<AvailabilitySettings | null>(null);
  const [bookingRequests, setBookingRequests] = useState<BookingRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [editing, setEditing] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('schedule');

  // Mock data - replace with actual API call
  useEffect(() => {
    const mockTimeSlots: TimeSlot[] = [
      {
        id: '1',
        day: 'Monday',
        startTime: '09:00',
        endTime: '12:00',
        isRecurring: true,
        maxBookings: 3,
        currentBookings: 2,
        sessionType: 'video',
        isActive: true
      },
      {
        id: '2',
        day: 'Monday',
        startTime: '14:00',
        endTime: '17:00',
        isRecurring: true,
        maxBookings: 3,
        currentBookings: 1,
        sessionType: 'any',
        isActive: true
      },
      {
        id: '3',
        day: 'Wednesday',
        startTime: '10:00',
        endTime: '13:00',
        isRecurring: true,
        maxBookings: 3,
        currentBookings: 3,
        sessionType: 'video',
        isActive: true
      },
      {
        id: '4',
        day: 'Friday',
        startTime: '09:00',
        endTime: '11:00',
        isRecurring: true,
        maxBookings: 2,
        currentBookings: 0,
        sessionType: 'in-person',
        isActive: true
      }
    ];

    const mockSettings: AvailabilitySettings = {
      timezone: 'PST (UTC-8)',
      bufferTime: 15,
      maxDailyBookings: 4,
      advanceBookingDays: 14,
      autoAcceptBookings: false,
      allowWeekendBookings: false,
      sessionDurations: [30, 45, 60, 90]
    };

    const mockBookingRequests: BookingRequest[] = [
      {
        id: '1',
        menteeName: 'Alex Chen',
        requestedDate: '2024-01-22',
        requestedTime: '10:00',
        duration: 60,
        topic: 'Product Strategy Review',
        status: 'pending',
        message: 'Would like to discuss go-to-market strategy for our new feature.'
      },
      {
        id: '2',
        menteeName: 'Sarah Johnson',
        requestedDate: '2024-01-24',
        requestedTime: '14:30',
        duration: 45,
        topic: 'Fundraising Preparation',
        status: 'pending',
        message: 'Need help preparing for Series A pitch deck review.'
      }
    ];

    setTimeout(() => {
      setTimeSlots(mockTimeSlots);
      setSettings(mockSettings);
      setBookingRequests(mockBookingRequests);
      setLoading(false);
    }, 1000);
  }, []);

  const daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

  const getSessionTypeColor = (type: string) => {
    switch (type) {
      case 'video': return 'bg-blue-100 text-blue-800';
      case 'phone': return 'bg-green-100 text-green-800';
      case 'in-person': return 'bg-purple-100 text-purple-800';
      case 'any': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleApproveBooking = (id: string) => {
    setBookingRequests(requests => 
      requests.map(req => 
        req.id === id ? { ...req, status: 'approved' as const } : req
      )
    );
  };

  const handleRejectBooking = (id: string) => {
    setBookingRequests(requests => 
      requests.map(req => 
        req.id === id ? { ...req, status: 'rejected' as const } : req
      )
    );
  };

  const addTimeSlot = () => {
    const newSlot: TimeSlot = {
      id: Date.now().toString(),
      day: 'Monday',
      startTime: '09:00',
      endTime: '10:00',
      isRecurring: true,
      maxBookings: 1,
      currentBookings: 0,
      sessionType: 'video',
      isActive: true
    };
    setTimeSlots([...timeSlots, newSlot]);
    setEditing(newSlot.id);
  };

  const deleteTimeSlot = (id: string) => {
    setTimeSlots(timeSlots.filter(slot => slot.id !== id));
  };

  const toggleSlotActive = (id: string) => {
    setTimeSlots(timeSlots.map(slot => 
      slot.id === id ? { ...slot, isActive: !slot.isActive } : slot
    ));
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Availability Management</h1>
          <p className="text-gray-600 mt-1">Manage your mentoring schedule and booking preferences</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Calendar className="w-4 h-4 mr-2" />
            View Calendar
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700" onClick={addTimeSlot}>
            <Plus className="w-4 h-4 mr-2" />
            Add Time Slot
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Slots</p>
                <p className="text-2xl font-bold">{timeSlots.filter(slot => slot.isActive).length}</p>
              </div>
              <Clock className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending Requests</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {bookingRequests.filter(req => req.status === 'pending').length}
                </p>
              </div>
              <Calendar className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">This Week</p>
                <p className="text-2xl font-bold text-green-600">
                  {timeSlots.reduce((sum, slot) => sum + slot.currentBookings, 0)}
                </p>
                <p className="text-sm text-gray-600">Bookings</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Availability</p>
                <p className="text-2xl font-bold text-purple-600">
                  {Math.round((timeSlots.filter(slot => slot.isActive).length / daysOfWeek.length) * 100)}%
                </p>
                <p className="text-sm text-gray-600">Coverage</p>
              </div>
              <Clock className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Availability Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="schedule">Weekly Schedule</TabsTrigger>
          <TabsTrigger value="requests">Booking Requests ({bookingRequests.filter(req => req.status === 'pending').length})</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="schedule" className="space-y-4">
          <div className="grid grid-cols-1 gap-4">
            {daysOfWeek.map(day => {
              const daySlots = timeSlots.filter(slot => slot.day === day);
              return (
                <Card key={day}>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span>{day}</span>
                      <span className="text-sm font-normal text-gray-600">
                        {daySlots.length} slot{daySlots.length !== 1 ? 's' : ''}
                      </span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {daySlots.length === 0 ? (
                      <p className="text-gray-500 text-center py-4">No availability set for this day</p>
                    ) : (
                      <div className="space-y-3">
                        {daySlots.map(slot => (
                          <div key={slot.id} className="flex items-center justify-between p-3 border rounded">
                            <div className="flex items-center gap-4">
                              <div>
                                <p className="font-medium">{slot.startTime} - {slot.endTime}</p>
                                <div className="flex items-center gap-2 mt-1">
                                  <Badge className={getSessionTypeColor(slot.sessionType)}>
                                    {slot.sessionType}
                                  </Badge>
                                  <span className="text-sm text-gray-600">
                                    {slot.currentBookings}/{slot.maxBookings} booked
                                  </span>
                                  {!slot.isActive && (
                                    <Badge variant="secondary">Inactive</Badge>
                                  )}
                                </div>
                              </div>
                            </div>
                            
                            <div className="flex items-center gap-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => toggleSlotActive(slot.id)}
                                className={slot.isActive ? 'text-orange-600' : 'text-green-600'}
                              >
                                {slot.isActive ? 'Deactivate' : 'Activate'}
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => setEditing(slot.id)}
                              >
                                <Edit className="w-4 h-4" />
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => deleteTimeSlot(slot.id)}
                                className="text-red-600 hover:bg-red-50"
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="requests" className="space-y-4">
          <div className="space-y-4">
            {bookingRequests.map(request => (
              <Card key={request.id}>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="font-semibold text-lg">{request.menteeName}</h3>
                        <Badge className={getStatusColor(request.status)}>
                          {request.status}
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-3">
                        <div>
                          <p className="text-gray-600">Date</p>
                          <p className="font-medium">{new Date(request.requestedDate).toLocaleDateString()}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Time</p>
                          <p className="font-medium">{request.requestedTime}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Duration</p>
                          <p className="font-medium">{request.duration} minutes</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Topic</p>
                          <p className="font-medium">{request.topic}</p>
                        </div>
                      </div>

                      {request.message && (
                        <div className="bg-gray-50 p-3 rounded mb-3">
                          <p className="text-sm"><strong>Message:</strong> {request.message}</p>
                        </div>
                      )}
                    </div>

                    {request.status === 'pending' && (
                      <div className="flex gap-2 ml-4">
                        <Button 
                          size="sm" 
                          className="bg-green-600 hover:bg-green-700"
                          onClick={() => handleApproveBooking(request.id)}
                        >
                          <CheckCircle className="w-4 h-4 mr-1" />
                          Approve
                        </Button>
                        <Button 
                          size="sm" 
                          variant="outline"
                          className="border-red-300 text-red-600 hover:bg-red-50"
                          onClick={() => handleRejectBooking(request.id)}
                        >
                          <X className="w-4 h-4 mr-1" />
                          Reject
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}

            {bookingRequests.length === 0 && (
              <Card>
                <CardContent className="p-8 text-center">
                  <Calendar className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No booking requests at the moment.</p>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          {settings && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>General Settings</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Timezone</label>
                    <Input value={settings.timezone} readOnly />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Buffer Time (minutes)</label>
                    <Input type="number" value={settings.bufferTime} />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Max Daily Bookings</label>
                    <Input type="number" value={settings.maxDailyBookings} />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Advance Booking Days</label>
                    <Input type="number" value={settings.advanceBookingDays} />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Booking Preferences</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Auto-accept bookings</span>
                    <input 
                      type="checkbox" 
                      checked={settings.autoAcceptBookings}
                      className="w-4 h-4"
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Allow weekend bookings</span>
                    <input 
                      type="checkbox" 
                      checked={settings.allowWeekendBookings}
                      className="w-4 h-4"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Available Session Durations</label>
                    <div className="flex flex-wrap gap-2">
                      {settings.sessionDurations.map(duration => (
                        <Badge key={duration} variant="outline">
                          {duration} min
                        </Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          <div className="flex justify-end">
            <Button className="bg-blue-600 hover:bg-blue-700">
              <Save className="w-4 h-4 mr-2" />
              Save Settings
            </Button>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default MentorAvailabilityPage;
