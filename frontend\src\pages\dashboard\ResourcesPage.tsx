import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { useAppSelector } from '../../store/hooks';
import { useCRUD } from '../../hooks/useCRUD';
import { Resource, resourcesAPI } from '../../services/api';
import ResourceForm from '../../components/resources/forms/ResourceForm';
import { CRUDTable } from '../../components/common';
import AuthenticatedLayout from '../../components/layout/AuthenticatedLayout';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  FileText, 
  Video,
  Link,
  Star,
  Calendar,
  AlertCircle,
  CheckCircle,
  Clock,
  BookOpen,
  Tag
} from 'lucide-react';

const ResourcesPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { user } = useAppSelector(state => state.auth);
  
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [selectedResource, setSelectedResource] = useState<Resource | null>(null);

  // CRUD operations for resources
  const resourcesCRUD = useCRUD({
    create: async (data: Partial<Resource>) => {
      if (!user) throw new Error('User not authenticated');
      return resourcesAPI.createResource({
        ...data,
        author_id: user.id
      });
    },
    read: () => resourcesAPI.getResources(),
    update: (id: number, data: Partial<Resource>) => 
      resourcesAPI.updateResource(id, data),
    delete: (id: number) => resourcesAPI.deleteResource(id)
  }, {
    onSuccess: (operation) => {
      if (operation === 'create') {
        setShowCreateForm(false);
      } else if (operation === 'update') {
        setShowEditForm(false);
        setSelectedResource(null);
      } else if (operation === 'delete') {
        setShowDeleteDialog(false);
        setSelectedResource(null);
      }
    }
  });

  // Load data on component mount
  useEffect(() => {
    resourcesCRUD.readItems();
  }, []);

  // Handle create
  const handleCreate = async (data: Partial<Resource>) => {
    return await resourcesCRUD.createItem(data);
  };

  // Handle edit
  const handleEdit = (resource: Resource) => {
    setSelectedResource(resource);
    setShowEditForm(true);
  };

  // Handle update
  const handleUpdate = async (data: Partial<Resource>) => {
    if (!selectedResource) return false;
    return await resourcesCRUD.updateItem(selectedResource.id, data);
  };

  // Handle delete
  const handleDelete = (resource: Resource) => {
    setSelectedResource(resource);
    setShowDeleteDialog(true);
  };

  // Confirm delete
  const confirmDelete = async () => {
    if (!selectedResource) return;
    await resourcesCRUD.deleteItem(selectedResource.id);
  };

  // Get resource type icon
  const getResourceTypeIcon = (type: string) => {
    switch (type) {
      case 'video':
        return <Video size={16} className="text-red-400" />;
      case 'link':
        return <Link size={16} className="text-blue-400" />;
      case 'document':
      case 'template':
      case 'tool':
        return <FileText size={16} className="text-green-400" />;
      default:
        return <BookOpen size={16} className="text-purple-400" />;
    }
  };

  // Table columns configuration
  const columns = [
    {
      key: 'title',
      label: t('resources.title', 'Title'),
      render: (value: any, resource: Resource) => (
        <div>
          <div className="flex items-center gap-2">
            {getResourceTypeIcon(resource.resource_type)}
            <span className="font-medium text-white">{resource.title}</span>
            {resource.is_featured && (
              <Star size={14} className="text-yellow-400" />
            )}
          </div>
          <div className="text-sm text-gray-400 truncate max-w-xs mt-1">
            {resource.description}
          </div>
        </div>
      )
    },
    {
      key: 'resource_type',
      label: t('resources.type', 'Type'),
      render: (value: any, resource: Resource) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          resource.resource_type === 'article' ? 'bg-purple-900/50 text-purple-200' :
          resource.resource_type === 'video' ? 'bg-red-900/50 text-red-200' :
          resource.resource_type === 'document' ? 'bg-green-900/50 text-green-200' :
          resource.resource_type === 'link' ? 'bg-blue-900/50 text-blue-200' :
          resource.resource_type === 'template' ? 'bg-orange-900/50 text-orange-200' :
          'bg-gray-900/50 text-gray-200'
        }`}>
          {t(`resources.types.${resource.resource_type}`, resource.resource_type)}
        </span>
      )
    },
    {
      key: 'category',
      label: t('resources.category', 'Category'),
      render: (value: any, resource: Resource) => (
        <span className="text-gray-300">{resource.category}</span>
      )
    },
    {
      key: 'author',
      label: t('resources.author', 'Author'),
      render: (value: any, resource: Resource) => (
        <div className="flex items-center gap-2">
          {resource.author?.profile?.profile_image ? (
            <img
              src={resource.author.profile.profile_image}
              alt={resource.author.username}
              className="w-6 h-6 rounded-full object-cover"
            />
          ) : (
            <div className="w-6 h-6 bg-purple-600 rounded-full flex items-center justify-center">
              <span className="text-white text-xs font-medium">
                {resource.author?.first_name?.[0] || resource.author?.username[0]}
              </span>
            </div>
          )}
          <span className="text-gray-300 text-sm">
            {resource.author?.first_name} {resource.author?.last_name}
          </span>
        </div>
      )
    },
    {
      key: 'status',
      label: t('resources.status', 'Status'),
      render: (value: any, resource: Resource) => (
        <div className="flex items-center gap-2">
          {resource.is_published ? (
            <CheckCircle size={16} className="text-green-400" />
          ) : (
            <Clock size={16} className="text-yellow-400" />
          )}
          <span className={`text-sm ${
            resource.is_published ? 'text-green-400' : 'text-yellow-400'
          }`}>
            {resource.is_published ? t('resources.published', 'Published') : t('resources.draft', 'Draft')}
          </span>
        </div>
      )
    },
    {
      key: 'created_at',
      label: t('common.created', 'Created'),
      render: (value: any, resource: Resource) => (
        <span className="text-gray-400 text-sm">
          {new Date(resource.created_at).toLocaleDateString()}
        </span>
      )
    }
  ];

  // Table actions
  const actions = [
    {
      label: t('common.view', 'View'),
      icon: <Eye className="w-4 h-4" />,
      onClick: (resource: Resource) => {
        // Navigate to resource detail page
        window.location.href = `/resources/${resource.id}`;
      },
      variant: 'secondary' as const
    },
    {
      label: t('common.edit', 'Edit'),
      icon: <Edit className="w-4 h-4" />,
      onClick: handleEdit,
      variant: 'primary' as const,
      disabled: (resource: Resource) => resource.author?.id !== user?.id
    },
    {
      label: t('common.delete', 'Delete'),
      icon: <Trash2 className="w-4 h-4" />,
      onClick: handleDelete,
      variant: 'danger' as const,
      disabled: (resource: Resource) => resource.author?.id !== user?.id
    }
  ];

  // Stats cards data
  const allResources = resourcesCRUD.data;
  const userResources = allResources.filter(resource => resource.author?.id === user?.id);
  const publishedResources = allResources.filter(resource => resource.is_published);
  const featuredResources = allResources.filter(resource => resource.is_featured);

  const stats = [
    {
      title: t('resources.totalResources', 'Total Resources'),
      value: allResources.length,
      icon: BookOpen,
      color: 'blue'
    },
    {
      title: t('resources.published', 'Published'),
      value: publishedResources.length,
      icon: CheckCircle,
      color: 'green'
    },
    {
      title: t('resources.featured', 'Featured'),
      value: featuredResources.length,
      icon: Star,
      color: 'yellow'
    },
    {
      title: t('resources.myResources', 'My Resources'),
      value: userResources.length,
      icon: FileText,
      color: 'purple'
    }
  ];

  return (
    <AuthenticatedLayout>
      <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="space-y-6">
        {/* Header */}
        <div className={`flex flex-col sm:flex-row sm:items-center sm:justify-between ${isRTL ? 'sm:flex-row-reverse' : ''}`}>
          <div>
            <h1 className="text-2xl font-bold text-white">
              {t('resources.resources', 'Resources')}
            </h1>
            <p className="text-gray-400 mt-1">
              {t('resources.manageResources', 'Create and manage educational resources')}
            </p>
          </div>
          <button
            onClick={() => setShowCreateForm(true)}
            className="mt-4 sm:mt-0 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center gap-2"
          >
            <Plus size={20} />
            {t('resources.createResource', 'Create Resource')}
          </button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <div key={index} className="bg-gray-800 rounded-lg p-6">
              <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div>
                  <p className="text-gray-400 text-sm">{stat.title}</p>
                  <p className="text-2xl font-bold text-white mt-1">{stat.value}</p>
                </div>
                <div className={`p-3 rounded-lg ${
                  stat.color === 'blue' ? 'bg-blue-900/50' :
                  stat.color === 'green' ? 'bg-green-900/50' :
                  stat.color === 'yellow' ? 'bg-yellow-900/50' :
                  'bg-purple-900/50'
                }`}>
                  <stat.icon size={24} className={
                    stat.color === 'blue' ? 'text-blue-400' :
                    stat.color === 'green' ? 'text-green-400' :
                    stat.color === 'yellow' ? 'text-yellow-400' :
                    'text-purple-400'
                  } />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Error Display */}
        {resourcesCRUD.error && (
          <div className="bg-red-900/50 border border-red-500 rounded-lg p-4">
            <div className="flex items-center gap-2">
              <AlertCircle size={20} className="text-red-400" />
              <span className="text-red-200">{resourcesCRUD.error}</span>
            </div>
          </div>
        )}

        {/* Resources Table */}
        <div className="bg-gray-800 rounded-lg">
          <CRUDTable
            data={resourcesCRUD.data}
            columns={columns}
            actions={actions}
            isLoading={resourcesCRUD.isLoading}
            emptyMessage={t('resources.noResources', 'No resources found. Create your first resource to get started!')}
            searchPlaceholder={t('resources.searchResources', 'Search resources...')}
            title={t('resources.allResources', 'All Resources')}
            createButtonLabel={t('resources.createResource', 'Create Resource')}
            onCreate={() => setShowCreateForm(true)}
          />
        </div>
      </div>

      {/* Create Form Modal */}
      {showCreateForm && (
        <ResourceForm
          mode="create"
          onSubmit={handleCreate}
          onCancel={() => setShowCreateForm(false)}
          isSubmitting={resourcesCRUD.isLoading}
        />
      )}

      {/* Edit Form Modal */}
      {showEditForm && selectedResource && (
        <ResourceForm
          mode="edit"
          initialData={selectedResource}
          onSubmit={handleUpdate}
          onCancel={() => {
            setShowEditForm(false);
            setSelectedResource(null);
          }}
          isSubmitting={resourcesCRUD.isLoading}
        />
      )}

      {/* Delete Confirmation Dialog */}
      {showDeleteDialog && selectedResource && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-gray-900 rounded-lg shadow-xl w-full max-w-md">
            <div className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <AlertCircle size={24} className="text-red-400" />
                <h3 className="text-lg font-semibold text-white">
                  {t('common.confirmDelete', 'Confirm Delete')}
                </h3>
              </div>
              <p className="text-gray-300 mb-6">
                {t('resources.deleteConfirmation', 'Are you sure you want to delete this resource? This action cannot be undone.')}
              </p>
              <div className={`flex gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <button
                  onClick={() => {
                    setShowDeleteDialog(false);
                    setSelectedResource(null);
                  }}
                  className="flex-1 px-4 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors"
                  disabled={resourcesCRUD.isLoading}
                >
                  {t('common.cancel', 'Cancel')}
                </button>
                <button
                  onClick={confirmDelete}
                  className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
                  disabled={resourcesCRUD.isLoading}
                >
                  {resourcesCRUD.isLoading ? t('common.deleting', 'Deleting...') : t('common.delete', 'Delete')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
              </div>
        </div>
      </div>
    </AuthenticatedLayout>
  );
};

export default ResourcesPage;
