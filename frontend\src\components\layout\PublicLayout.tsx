import React from 'react';
import Navbar from '../Navbar';
import Footer from '../Footer';
import { useLanguage } from '../../hooks/useLanguage';

interface PublicLayoutProps {
  children: React.ReactNode;
}

/**
 * PublicLayout - For public pages (home, login, register, etc.)
 * Shows: Navbar + Content + Footer
 * Does NOT show: Sidebar
 */
const PublicLayout: React.FC<PublicLayoutProps> = ({ children }) => {
  const { isRTL } = useLanguage();

  return (
    <div className={`min-h-screen flex flex-col ${isRTL ? "flex-row-reverse" : ""}`}>
      {/* Public Navbar */}
      <Navbar />

      {/* Main Content */}
      <main className="flex-grow pt-20">
        {children}
      </main>

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default PublicLayout;
