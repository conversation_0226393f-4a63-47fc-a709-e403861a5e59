import { lazy } from 'react';
import { RouteConfig } from './routeConfig';
import { createSuperAdminRoute } from './routeConfig';

// DEDICATED SUPER ADMIN DASHBOARD - Super Admin users only
const SuperAdminDashboardPage = lazy(() => import('../pages/super-admin/SuperAdminDashboardPage'));
// Legacy SuperAdminDashboard kept for backward compatibility
const SuperAdminDashboard = lazy(() => import('../components/admin/super-admin/SuperAdminDashboard'));
const SystemManagement = lazy(() => import('../components/admin/super-admin/SystemManagement'));
const UserImpersonation = lazy(() => import('../components/admin/super-admin/UserImpersonation'));
const AISystemManagement = lazy(() => import('../components/admin/super-admin/AISystemManagement'));
const AIConfiguration = lazy(() => import('../components/admin/super-admin/AIConfiguration'));
const AIAnalytics = lazy(() => import('../components/admin/super-admin/AIAnalytics'));
const AIMonitoring = lazy(() => import('../components/admin/super-admin/AIMonitoring'));
const SecurityCenter = lazy(() => import('../components/admin/super-admin/SecurityCenter'));
const PerformanceCenter = lazy(() => import('../components/admin/super-admin/PerformanceCenter'));
const APIManagement = lazy(() => import('../components/admin/super-admin/APIManagement'));

// Advanced Analytics Components
const RealTimeSystemDashboard = lazy(() => import('../components/admin/analytics/RealTimeSystemDashboard'));
const AdvancedUserAnalytics = lazy(() => import('../components/admin/analytics/AdvancedUserAnalytics'));

// Financial Management Components
const RevenueAnalytics = lazy(() => import('../components/admin/financial/RevenueAnalytics'));

// Security & Compliance Components
const AdvancedSecurityCenter = lazy(() => import('../components/admin/security/AdvancedSecurityCenter'));
const SystemLogsPage = lazy(() => import('../pages/admin/SystemLogsPage'));
const CommunicationCenter = lazy(() => import('../components/admin/communication/CommunicationCenter'));

// ✅ REMOVED: Duplicate admin component imports - super admin can access admin routes directly
// Super Admin specific system components (not duplicated in admin routes)
const PerformanceMonitoring = lazy(() => import('../components/admin/system/PerformanceMonitoring'));
const SystemHealthDetails = lazy(() => import('../components/admin/system/SystemHealthDetails'));
// ✅ REMOVED: SystemMonitoring, BackupManagement, AnalyticsDashboard - already in admin routes

/**
 * Super Admin-only routes with dedicated /super_admin prefix
 * These routes are completely separate from regular admin routes
 */
export const superAdminRoutes: RouteConfig[] = [
  // Main Super Admin dashboard - DEDICATED SUPER ADMIN ONLY ✅ (using SuperAdminDashboardPage)
  createSuperAdminRoute('/super_admin', SuperAdminDashboardPage, 'Loading Super Admin dashboard...'),

  // Super Admin exclusive features
  createSuperAdminRoute('/super_admin/system-management', SystemManagement, 'Loading system management...'),
  createSuperAdminRoute('/super_admin/user-impersonation', UserImpersonation, 'Loading user impersonation...'),
  createSuperAdminRoute('/super_admin/ai-system-management', AISystemManagement, 'Loading AI system management...'),
  createSuperAdminRoute('/super_admin/ai-configuration', AIConfiguration, 'Loading AI configuration...'),
  createSuperAdminRoute('/super_admin/ai-analytics', AIAnalytics, 'Loading AI analytics...'),
  createSuperAdminRoute('/super_admin/ai-monitoring', AIMonitoring, 'Loading AI monitoring...'),
  createSuperAdminRoute('/super_admin/security', SecurityCenter, 'Loading security center...'),
  createSuperAdminRoute('/super_admin/performance', PerformanceCenter, 'Loading performance center...'),
  createSuperAdminRoute('/super_admin/api', APIManagement, 'Loading API management...'),
  createSuperAdminRoute('/super_admin/communication', CommunicationCenter, 'Loading communication center...'),
  createSuperAdminRoute('/super_admin/system-logs', SystemLogsPage, 'Loading system logs...'),

  // ✅ REMOVED: Duplicate admin routes - super admin can access admin routes directly via /admin/* paths
  // Super admin has access to all admin routes because createAdminRoute allows 'super_admin' role

  // Super Admin exclusive system management features (not duplicated in admin routes)
  createSuperAdminRoute('/super_admin/performance-monitoring', PerformanceMonitoring, 'Loading performance monitoring...'),
  createSuperAdminRoute('/super_admin/system-health', SystemHealthDetails, 'Loading system health details...'),
  // ✅ REMOVED: /super_admin/monitoring, /super_admin/backup, /super_admin/analytics - use admin routes instead

  // Advanced Analytics & Monitoring
  createSuperAdminRoute('/super_admin/realtime-dashboard', RealTimeSystemDashboard, 'Loading real-time dashboard...'),
  createSuperAdminRoute('/super_admin/user-analytics', AdvancedUserAnalytics, 'Loading user analytics...'),

  // Financial Management
  createSuperAdminRoute('/super_admin/revenue-analytics', RevenueAnalytics, 'Loading revenue analytics...'),

  // Advanced Security
  createSuperAdminRoute('/super_admin/advanced-security', AdvancedSecurityCenter, 'Loading advanced security...'),
];

export default superAdminRoutes;
