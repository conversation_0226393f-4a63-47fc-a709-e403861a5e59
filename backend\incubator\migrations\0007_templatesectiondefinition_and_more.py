# Generated by Django 5.1.7 on 2025-05-15 19:24

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('incubator', '0006_metricdefinition_comparativeanalytics_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='TemplateSectionDefinition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100)),
                ('key', models.CharField(help_text='Unique identifier for this section type', max_length=50, unique=True)),
                ('description', models.TextField()),
                ('section_type', models.CharField(choices=[('text', 'Text'), ('financial', 'Financial'), ('chart', 'Chart/Graph'), ('table', 'Table'), ('image', 'Image'), ('swot', 'SWOT Analysis'), ('timeline', 'Timeline'), ('checklist', 'Checklist'), ('custom', 'Custom')], default='text', max_length=20)),
                ('default_content', models.TextField(blank=True)),
                ('structure_definition', models.JSONField(default=dict, help_text='JSON schema defining the structure of this section type')),
                ('ai_prompt_template', models.TextField(blank=True, help_text='Template for AI prompts to generate this section')),
                ('is_system', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['title'],
            },
        ),
        migrations.AddField(
            model_name='businessplansection',
            name='additional_data',
            field=models.JSONField(blank=True, default=dict, help_text='Additional data for specialized section types'),
        ),
        migrations.AddField(
            model_name='businessplansection',
            name='custom_instructions',
            field=models.TextField(blank=True, help_text='Custom instructions for this section'),
        ),
        migrations.AddField(
            model_name='businessplansection',
            name='custom_prompt',
            field=models.TextField(blank=True, help_text='Custom AI prompt for this specific section'),
        ),
        migrations.AddField(
            model_name='businessplantemplate',
            name='allows_customization',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='businessplantemplate',
            name='customization_options',
            field=models.JSONField(default=dict, help_text='JSON structure defining customization options'),
        ),
        migrations.AddField(
            model_name='businessplantemplate',
            name='is_system',
            field=models.BooleanField(default=False, help_text='Whether this is a system-provided template'),
        ),
        migrations.AddField(
            model_name='businessplantemplate',
            name='template_type',
            field=models.CharField(choices=[('standard', 'Standard'), ('lean', 'Lean'), ('detailed', 'Detailed'), ('investor', 'Investor-Ready'), ('startup', 'Startup'), ('nonprofit', 'Non-Profit'), ('service', 'Service Business'), ('product', 'Product Business'), ('custom', 'Custom')], default='standard', max_length=20),
        ),
        migrations.CreateModel(
            name='CustomBusinessPlanTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('sections', models.JSONField(help_text='JSON structure defining the sections of this custom template')),
                ('custom_prompts', models.JSONField(default=dict, help_text='Custom AI prompts for each section')),
                ('custom_instructions', models.JSONField(default=dict, help_text='Custom instructions for each section')),
                ('is_public', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('base_template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='custom_templates', to='incubator.businessplantemplate')),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='custom_templates', to=settings.AUTH_USER_MODEL)),
                ('shared_with', models.ManyToManyField(blank=True, related_name='shared_templates', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-updated_at'],
                'unique_together': {('owner', 'name')},
            },
        ),
        migrations.AddField(
            model_name='businessplan',
            name='custom_template',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='business_plans', to='incubator.custombusinessplantemplate'),
        ),
        migrations.AddField(
            model_name='businessplansection',
            name='section_definition',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='incubator.templatesectiondefinition'),
        ),
    ]
