import React, { useState, useEffect } from 'react';
import { Download, Upload, Database, HardDrive, Calendar, Clock, CheckCircle, AlertTriangle, Play, Pause, Settings } from 'lucide-react';
import { DashboardLayout } from '../dashboard';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';
import { getAuthToken } from '../../../services/api';

interface BackupJob {
  id: string;
  name: string;
  type: 'database' | 'files' | 'full_system' | 'user_data';
  schedule: string;
  status: 'active' | 'paused' | 'failed' | 'completed';
  lastRun: string;
  nextRun: string;
  size: number;
  retention: number;
  location: string;
  enabled: boolean;
}

interface BackupHistory {
  id: string;
  jobId: string;
  jobName: string;
  startTime: string;
  endTime: string;
  status: 'success' | 'failed' | 'partial';
  size: number;
  duration: number;
  errorMessage?: string;
}

interface StorageLocation {
  id: string;
  name: string;
  type: 'local' | 's3' | 'azure' | 'gcp' | 'ftp';
  path: string;
  available: boolean;
  totalSpace: number;
  usedSpace: number;
}

const BackupManagement: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  // State management
  const [backupJobs, setBackupJobs] = useState<BackupJob[]>([]);
  const [backupHistory, setBackupHistory] = useState<BackupHistory[]>([]);
  const [storageLocations, setStorageLocations] = useState<StorageLocation[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedJob, setSelectedJob] = useState<BackupJob | null>(null);

  // Fetch real data
  useEffect(() => {
    fetchBackupData();
  }, []);

  const fetchBackupData = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/superadmin/system/backup/', {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setBackupJobs(data.backup_jobs || []);
        setBackupHistory(data.backup_history || []);
        setStorageLocations(data.storage_locations || []);
      } else {
        console.error('Failed to fetch backup data:', response.status);
        setBackupJobs([]);
        setBackupHistory([]);
        setStorageLocations([]);
      }
    } catch (error) {
      console.error('Failed to fetch backup data:', error);
      setBackupJobs([]);
      setBackupHistory([]);
      setStorageLocations([]);
    } finally {
      setLoading(false);
    }
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  // Format duration
  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
      case 'success':
        return 'text-green-400';
      case 'completed':
        return 'text-blue-400';
      case 'paused':
        return 'text-yellow-400';
      case 'failed':
      case 'partial':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };

  // Get status background
  const getStatusBg = (status: string) => {
    switch (status) {
      case 'active':
      case 'success':
        return 'bg-green-500/20';
      case 'completed':
        return 'bg-blue-500/20';
      case 'paused':
        return 'bg-yellow-500/20';
      case 'failed':
      case 'partial':
        return 'bg-red-500/20';
      default:
        return 'bg-gray-500/20';
    }
  };

  // Get type icon
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'database':
        return <Database size={16} className="text-blue-400" />;
      case 'files':
        return <HardDrive size={16} className="text-green-400" />;
      case 'full_system':
        return <Settings size={16} className="text-purple-400" />;
      case 'user_data':
        return <Upload size={16} className="text-orange-400" />;
      default:
        return <HardDrive size={16} className="text-gray-400" />;
    }
  };

  // Get storage usage percentage
  const getStorageUsage = (location: StorageLocation) => {
    return (location.usedSpace / location.totalSpace) * 100;
  };

  // Toggle job status
  const toggleJobStatus = (jobId: string) => {
    setBackupJobs(prev => prev.map(job => 
      job.id === jobId 
        ? { ...job, enabled: !job.enabled, status: job.enabled ? 'paused' : 'active' }
        : job
    ));
  };

  // Run backup now
  const runBackupNow = (jobId: string) => {
    console.log('Running backup job:', jobId);
    // Implement immediate backup execution
  };

  return (
    <DashboardLayout currentPage="system">
      {/* Header */}
      <div className={`mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
        <div>
          <h1 className="text-2xl font-bold text-white">{t('admin.backup.management', 'Backup Management')}</h1>
          <div className="text-gray-400 mt-1">{t('admin.backup.description', 'Automated backup scheduling and monitoring')}</div>
        </div>
        <button
          onClick={() => setShowCreateModal(true)}
          className="mt-4 sm:mt-0 px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg font-medium transition-colors flex items-center space-x-2"
        >
          <Upload size={16} />
          <span>{t('backup.create.job', 'Create Backup Job')}</span>
        </button>
      </div>

      {/* Storage Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        {storageLocations.map((location) => {
          const usagePercentage = getStorageUsage(location);
          return (
            <div key={location.id} className="bg-indigo-900/30 backdrop-blur-sm rounded-xl p-6 border border-indigo-800/50">
              <div className={`flex items-center justify-between mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                <h3 className="text-white font-medium">{location.name}</h3>
                <div className={`w-3 h-3 rounded-full ${location.available ? 'bg-green-400' : 'bg-red-400'}`}></div>
              </div>
              
              <div className="mb-4">
                <div className={`flex justify-between text-sm mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <span className="text-gray-400">{t('backup.used', 'Used')}</span>
                  <span className="text-white">{formatFileSize(location.usedSpace)} / {formatFileSize(location.totalSpace)}</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full transition-all duration-300 ${
                      usagePercentage > 90 ? 'bg-red-500' :
                      usagePercentage > 70 ? 'bg-yellow-500' : 'bg-green-500'
                    }`}
                    style={{ width: `${usagePercentage}%` }}
                  ></div>
                </div>
                <div className="text-xs text-gray-400 mt-1">{usagePercentage.toFixed(1)}% {t('backup.used.lowercase', 'used')}</div>
              </div>
              
              <div className="text-xs text-gray-400">{location.path}</div>
            </div>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Backup Jobs */}
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-xl p-6 border border-indigo-800/50">
          <h3 className="text-lg font-semibold text-white mb-6">{t('backup.jobs', 'Backup Jobs')}</h3>
          
          <div className="space-y-4">
            {backupJobs.map((job) => (
              <div key={job.id} className="p-4 bg-indigo-800/30 rounded-lg">
                <div className={`flex items-center justify-between mb-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                    {getTypeIcon(job.type)}
                    <div>
                      <h4 className="text-white font-medium">{job.name}</h4>
                      <p className="text-gray-400 text-sm">{job.location}</p>
                    </div>
                  </div>
                  <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusBg(job.status)} ${getStatusColor(job.status)}`}>
                      {job.status}
                    </span>
                    <button
                      onClick={() => toggleJobStatus(job.id)}
                      className={`p-1 rounded ${job.enabled ? 'text-green-400 hover:bg-green-500/20' : 'text-gray-400 hover:bg-gray-500/20'} transition-colors`}
                      title={job.enabled ? t('backup.pause', 'Pause') : t('backup.resume', 'Resume')}
                    >
                      {job.enabled ? <Pause size={16} /> : <Play size={16} />}
                    </button>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4 text-sm mb-3">
                  <div>
                    <span className="text-gray-400">{t('backup.last.run', 'Last Run')}: </span>
                    <span className="text-white">{new Date(job.lastRun).toLocaleDateString()}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">{t('backup.next.run', 'Next Run')}: </span>
                    <span className="text-white">{new Date(job.nextRun).toLocaleDateString()}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">{t('backup.size', 'Size')}: </span>
                    <span className="text-white">{formatFileSize(job.size)}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">{t('backup.retention', 'Retention')}: </span>
                    <span className="text-white">{job.retention} {t('backup.days', 'days')}</span>
                  </div>
                </div>
                
                <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                  <button
                    onClick={() => runBackupNow(job.id)}
                    className="px-3 py-1 bg-blue-600/80 hover:bg-blue-500/80 rounded text-white text-sm transition-colors"
                  >
                    {t('backup.run.now', 'Run Now')}
                  </button>
                  <button
                    onClick={() => setSelectedJob(job)}
                    className="px-3 py-1 bg-gray-600/80 hover:bg-gray-500/80 rounded text-white text-sm transition-colors"
                  >
                    {t('backup.configure', 'Configure')}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Backup History */}
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-xl p-6 border border-indigo-800/50">
          <h3 className="text-lg font-semibold text-white mb-6">{t('backup.recent.history', 'Recent Backup History')}</h3>
          
          <div className="space-y-3">
            {backupHistory.map((backup) => (
              <div key={backup.id} className="p-3 bg-indigo-800/30 rounded-lg">
                <div className={`flex items-center justify-between mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <h4 className="text-white font-medium text-sm">{backup.jobName}</h4>
                  <div className={`flex items-center space-x-1 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                    {backup.status === 'success' ? (
                      <CheckCircle size={14} className="text-green-400" />
                    ) : (
                      <AlertTriangle size={14} className="text-red-400" />
                    )}
                    <span className={`text-xs ${getStatusColor(backup.status)}`}>
                      {backup.status}
                    </span>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-2 text-xs text-gray-400">
                  <div>
                    <Clock size={12} className="inline mr-1" />
                    {formatDuration(backup.duration)}
                  </div>
                  <div>
                    <HardDrive size={12} className="inline mr-1" />
                    {formatFileSize(backup.size)}
                  </div>
                  <div className="col-span-2">
                    <Calendar size={12} className="inline mr-1" />
                    {new Date(backup.startTime).toLocaleString()}
                  </div>
                </div>
                
                {backup.errorMessage && (
                  <div className="mt-2 p-2 bg-red-500/20 rounded text-red-300 text-xs">
                    {backup.errorMessage}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default BackupManagement;
