import React from 'react';
import ModeratorDashboard from '../../components/dashboard/moderator-dashboard/ModeratorDashboard';

/**
 * DEDICATED MODERATOR DASHBOARD PAGE
 * 
 * This page is specifically for moderator users only.
 * It provides content moderation and community management tools.
 * 
 * Key Features:
 * - Content review and moderation
 * - User behavior monitoring
 * - Report management
 * - Community guidelines enforcement
 * - Forum moderation tools
 * - Moderation analytics
 * - User warnings and actions
 * - Content approval workflows
 */
const ModeratorDashboardPage: React.FC = () => {
  return <ModeratorDashboard />;
};

export default ModeratorDashboardPage;
