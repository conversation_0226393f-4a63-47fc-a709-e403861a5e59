from django.contrib import admin
from .models import (
    BusinessIdea, ProgressUpdate, IncubatorResource, MentorshipApplication,
    InvestorProfile, FundingOpportunity, FundingApplication, Investment,
    MentorProfile, MentorExpertise, MentorshipMatch, MentorshipSession, MentorshipFeedback
)
from .models_milestone import (
    BusinessMilestone, BusinessGoal, MentorRecommendation, BusinessAnalytics
)
from .models_business_plan import (
    BusinessPlanTemplate, BusinessPlan, BusinessPlanSection
)
from .models_analytics import (
    PredictiveAnalytics, ComparativeAnalytics, AnalyticsSnapshot, MetricDefinition
)

# Inlines for BusinessIdea
class ProgressUpdateInline(admin.TabularInline):
    model = ProgressUpdate
    extra = 0

class BusinessMilestoneInline(admin.TabularInline):
    model = BusinessMilestone
    extra = 0

class BusinessGoalInline(admin.TabularInline):
    model = BusinessGoal
    extra = 0

class MentorshipApplicationInline(admin.TabularInline):
    model = MentorshipApplication
    extra = 0

class FundingApplicationInline(admin.TabularInline):
    model = FundingApplication
    extra = 0

class InvestmentInline(admin.TabularInline):
    model = Investment
    extra = 0

@admin.register(BusinessIdea)
class BusinessIdeaAdmin(admin.ModelAdmin):
    list_display = ('title', 'owner', 'current_stage', 'moderation_status', 'created_at')
    list_filter = ('current_stage', 'moderation_status', 'created_at')
    search_fields = ('title', 'description', 'problem_statement', 'owner__username')
    prepopulated_fields = {'slug': ('title',)}
    inlines = [
        ProgressUpdateInline,
        BusinessMilestoneInline,
        BusinessGoalInline,
        MentorshipApplicationInline,
        FundingApplicationInline,
        InvestmentInline
    ]
    date_hierarchy = 'created_at'

@admin.register(ProgressUpdate)
class ProgressUpdateAdmin(admin.ModelAdmin):
    list_display = ('title', 'business_idea', 'created_by', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('title', 'description', 'business_idea__title')
    date_hierarchy = 'created_at'

@admin.register(IncubatorResource)
class IncubatorResourceAdmin(admin.ModelAdmin):
    list_display = ('title', 'resource_type', 'category', 'author', 'created_at')
    list_filter = ('resource_type', 'category', 'created_at')
    search_fields = ('title', 'description')
    date_hierarchy = 'created_at'

@admin.register(MentorshipApplication)
class MentorshipApplicationAdmin(admin.ModelAdmin):
    list_display = ('business_idea', 'applicant', 'preferred_mentor', 'preferred_communication', 'preferred_expertise', 'status', 'created_at')
    list_filter = ('status', 'preferred_communication', 'preferred_expertise', 'created_at')
    search_fields = ('business_idea__title', 'applicant__username', 'goals', 'specific_areas')
    date_hierarchy = 'created_at'
    list_editable = ('status',)

@admin.register(InvestorProfile)
class InvestorProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'investor_type', 'company_name', 'is_verified', 'created_at')
    list_filter = ('investor_type', 'is_verified', 'created_at')
    search_fields = ('user__username', 'company_name', 'bio', 'investment_focus')
    date_hierarchy = 'created_at'
    list_editable = ('is_verified',)

@admin.register(FundingOpportunity)
class FundingOpportunityAdmin(admin.ModelAdmin):
    list_display = ('title', 'funding_type', 'amount', 'provider', 'application_deadline', 'status', 'created_at')
    list_filter = ('funding_type', 'status', 'application_deadline', 'created_at')
    search_fields = ('title', 'description', 'provider__username')
    date_hierarchy = 'created_at'
    list_editable = ('status',)
    inlines = [FundingApplicationInline]

@admin.register(FundingApplication)
class FundingApplicationAdmin(admin.ModelAdmin):
    list_display = ('business_idea', 'funding_opportunity', 'applicant', 'requested_amount', 'status', 'created_at')
    list_filter = ('status', 'created_at')
    search_fields = ('business_idea__title', 'applicant__username', 'pitch')
    date_hierarchy = 'created_at'
    list_editable = ('status',)

@admin.register(Investment)
class InvestmentAdmin(admin.ModelAdmin):
    list_display = ('business_idea', 'investor', 'investment_type', 'amount', 'equity_percentage', 'status', 'created_at')
    list_filter = ('investment_type', 'status', 'created_at')
    search_fields = ('business_idea__title', 'investor__username', 'terms')
    date_hierarchy = 'created_at'
    list_editable = ('status',)


class MentorExpertiseInline(admin.TabularInline):
    model = MentorExpertise
    extra = 1


@admin.register(MentorProfile)
class MentorProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'company', 'position', 'years_of_experience', 'availability', 'is_accepting_mentees', 'is_verified')
    list_filter = ('availability', 'is_accepting_mentees', 'is_verified', 'created_at')
    search_fields = ('user__username', 'user__email', 'bio', 'company', 'position')
    date_hierarchy = 'created_at'
    list_editable = ('is_verified', 'is_accepting_mentees')
    inlines = [MentorExpertiseInline]


@admin.register(MentorExpertise)
class MentorExpertiseAdmin(admin.ModelAdmin):
    list_display = ('mentor', 'category', 'specific_expertise', 'level', 'years_experience')
    list_filter = ('category', 'level')
    search_fields = ('mentor__user__username', 'specific_expertise')


class MentorshipSessionInline(admin.TabularInline):
    model = MentorshipSession
    extra = 0


@admin.register(MentorshipMatch)
class MentorshipMatchAdmin(admin.ModelAdmin):
    list_display = ('mentor', 'mentee', 'business_idea', 'status', 'start_date', 'end_date')
    list_filter = ('status', 'start_date', 'created_at')
    search_fields = ('mentor__user__username', 'mentee__username', 'business_idea__title')
    date_hierarchy = 'created_at'
    list_editable = ('status',)
    inlines = [MentorshipSessionInline]


class MentorshipFeedbackInline(admin.TabularInline):
    model = MentorshipFeedback
    extra = 0


@admin.register(MentorshipSession)
class MentorshipSessionAdmin(admin.ModelAdmin):
    list_display = ('title', 'mentorship_match', 'scheduled_at', 'duration_minutes', 'status')
    list_filter = ('status', 'scheduled_at')
    search_fields = ('title', 'description', 'mentorship_match__mentor__user__username', 'mentorship_match__mentee__username')
    date_hierarchy = 'scheduled_at'
    list_editable = ('status',)
    inlines = [MentorshipFeedbackInline]


@admin.register(MentorshipFeedback)
class MentorshipFeedbackAdmin(admin.ModelAdmin):
    list_display = ('session', 'provided_by', 'is_from_mentee', 'rating', 'is_private', 'created_at')
    list_filter = ('rating', 'is_from_mentee', 'is_private', 'created_at')
    search_fields = ('comments', 'areas_of_improvement', 'provided_by__username')
    date_hierarchy = 'created_at'
    list_editable = ('is_private',)


# Register milestone models


@admin.register(BusinessMilestone)
class BusinessMilestoneAdmin(admin.ModelAdmin):
    list_display = ('title', 'business_idea', 'due_date', 'status', 'priority', 'assigned_to')
    list_filter = ('status', 'priority', 'due_date')
    search_fields = ('title', 'description', 'business_idea__title')
    date_hierarchy = 'due_date'


@admin.register(BusinessGoal)
class BusinessGoalAdmin(admin.ModelAdmin):
    list_display = ('title', 'business_idea', 'timeframe', 'target_date', 'status')
    list_filter = ('status', 'timeframe', 'target_date')
    search_fields = ('title', 'description', 'business_idea__title')
    date_hierarchy = 'target_date'


@admin.register(MentorRecommendation)
class MentorRecommendationAdmin(admin.ModelAdmin):
    list_display = ('business_idea', 'mentor', 'match_score', 'is_applied', 'is_matched')
    list_filter = ('is_applied', 'is_matched')
    search_fields = ('business_idea__title', 'mentor__user__username', 'match_reason')
    date_hierarchy = 'created_at'


@admin.register(BusinessAnalytics)
class BusinessAnalyticsAdmin(admin.ModelAdmin):
    list_display = ('business_idea', 'progress_rate', 'milestone_completion_rate', 'goal_achievement_rate', 'last_calculated')
    search_fields = ('business_idea__title',)
    date_hierarchy = 'last_calculated'


# Register business plan models

class BusinessPlanSectionInline(admin.TabularInline):
    model = BusinessPlanSection
    extra = 0


@admin.register(BusinessPlanTemplate)
class BusinessPlanTemplateAdmin(admin.ModelAdmin):
    list_display = ('name', 'industry', 'is_active', 'created_at')
    list_filter = ('industry', 'is_active', 'created_at')
    search_fields = ('name', 'description', 'industry')
    date_hierarchy = 'created_at'
    list_editable = ('is_active',)


@admin.register(BusinessPlan)
class BusinessPlanAdmin(admin.ModelAdmin):
    list_display = ('title', 'business_idea', 'owner', 'status', 'completion_percentage', 'version', 'updated_at')
    list_filter = ('status', 'created_at', 'updated_at')
    search_fields = ('title', 'business_idea__title', 'owner__username')
    date_hierarchy = 'updated_at'
    list_editable = ('status',)
    inlines = [BusinessPlanSectionInline]


@admin.register(BusinessPlanSection)
class BusinessPlanSectionAdmin(admin.ModelAdmin):
    list_display = ('title', 'business_plan', 'key', 'order', 'is_required', 'is_completed')
    list_filter = ('is_required', 'is_completed')
    search_fields = ('title', 'key', 'business_plan__title')
    list_editable = ('order', 'is_required', 'is_completed')


# Register analytics models

@admin.register(PredictiveAnalytics)
class PredictiveAnalyticsAdmin(admin.ModelAdmin):
    list_display = ('business_idea', 'success_probability', 'prediction_confidence', 'last_calculated')
    list_filter = ('prediction_confidence', 'last_calculated')
    search_fields = ('business_idea__title',)
    readonly_fields = ('last_calculated',)


@admin.register(ComparativeAnalytics)
class ComparativeAnalyticsAdmin(admin.ModelAdmin):
    list_display = ('business_idea', 'last_calculated')
    list_filter = ('last_calculated',)
    search_fields = ('business_idea__title',)
    readonly_fields = ('last_calculated',)


@admin.register(AnalyticsSnapshot)
class AnalyticsSnapshotAdmin(admin.ModelAdmin):
    list_display = ('business_idea', 'snapshot_date', 'progress_rate', 'milestone_completion_rate', 'success_probability')
    list_filter = ('snapshot_date',)
    search_fields = ('business_idea__title',)
    date_hierarchy = 'snapshot_date'


@admin.register(MetricDefinition)
class MetricDefinitionAdmin(admin.ModelAdmin):
    list_display = ('display_name', 'metric_key', 'category', 'display_order')
    list_filter = ('category',)
    search_fields = ('display_name', 'metric_key', 'description')
    list_editable = ('display_order',)
