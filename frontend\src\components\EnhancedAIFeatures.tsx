/**
 * Enhanced AI Features Component
 * Showcases the intelligent AI capabilities beyond basic chat
 */

import React from 'react';
import {
  Brain,
  TrendingUp,
  Target,
  AlertTriangle,
  Lightbulb,
  BarChart3,
  Zap,
  Eye,
  Clock,
  Users,
  DollarSign,
  Activity,
  CheckCircle,
  ArrowRight,
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../hooks/useLanguage';

interface EnhancedAIFeaturesProps {
  language?: string;
  className?: string;
}

const EnhancedAIFeatures: React.FC<EnhancedAIFeaturesProps> = ({ language = 'en',
  className = '',
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const intelligentFeatures = [
    {
      icon: <Brain className="w-8 h-8 text-blue-500" />,
      title: language === 'ar' ? 'مستشار الأعمال الذكي' : t("ai.intelligent.business.advisor", "Intelligent Business Advisor"),
      description: language === 'ar'
        ? 'يراقب تقدمك ويقدم توصيات استباقية بناءً على مرحلة عملك الحالية'
        : t("ai.monitors.your.progress", "Monitors your progress and provides proactive recommendations based on your current business stage"),
      features: [
        language === 'ar' ? 'توصيات استباقية' : t("ai.proactive.recommendations", "Proactive Recommendations"),
        language === 'ar' ? 'تتبع المعالم' : t("ai.milestone.tracking", "Milestone Tracking"),
        language === 'ar' ? 'تحليل السياق' : t("ai.context.analysis", "Context Analysis"),
      ],
      color: 'blue',
    },
    {
      icon: <BarChart3 className="w-8 h-8 text-green-500" />,
      title: language === 'ar' ? 'مراقبة صحة الأعمال' : t("ai.business.health.monitoring", "Business Health Monitoring"),
      description: language === 'ar'
        ? 'تقييم شامل لصحة عملك مع نقاط قابلة للتنفيذ للتحسين'
        : t("ai.comprehensive.assessment.of", "Comprehensive assessment of your business health with actionable insights for improvement"),
      features: [
        language === 'ar' ? 'نقاط الصحة (0-100)' : 'Health Scoring (0-100)',
        language === 'ar' ? 'تتبع التقدم' : t("ai.progress.tracking", "Progress Tracking"),
        language === 'ar' ? 'كشف العقد' : t("ai.bottleneck.detection", "Bottleneck Detection"),
      ],
      color: 'green',
    },
    {
      icon: <Target className="w-8 h-8 text-purple-500" />,
      title: language === 'ar' ? 'إدارة الإجراءات الذكية' : t("ai.smart.action.management", "Smart Action Management"),
      description: language === 'ar'
        ? 'إجراءات مرتبة حسب الأولوية مع تقديرات زمنية ومواعيد نهائية ذكية'
        : t("ai.prioritized.actions.with", "Prioritized actions with time estimates and intelligent deadline management"),
      features: [
        language === 'ar' ? 'ترتيب الأولويات' : t("ai.priority.ranking", "Priority Ranking"),
        language === 'ar' ? 'تقديرات الوقت' : t("ai.time.estimates", "Time Estimates"),
        language === 'ar' ? 'المواعيد النهائية الذكية' : t("ai.smart.deadlines", "Smart Deadlines"),
      ],
      color: 'purple',
    },
    {
      icon: <AlertTriangle className="w-8 h-8 text-orange-500" />,
      title: language === 'ar' ? 'التنبيهات الذكية' : t("ai.intelligent.alerts", "Intelligent Alerts"),
      description: language === 'ar'
        ? 'إشعارات في الوقت المناسب حول الفرص والمخاطر والمعالم المهمة'
        : 'Timely notifications about opportunities, risks, and important milestones',
      features: [
        language === 'ar' ? 'تنبيهات الفرص' : t("ai.opportunity.alerts", "Opportunity Alerts"),
        language === 'ar' ? 'تحذيرات المخاطر' : t("ai.risk.warnings", "Risk Warnings"),
        language === 'ar' ? 'تذكيرات المعالم' : t("ai.milestone.reminders", "Milestone Reminders"),
      ],
      color: 'orange',
    },
    {
      icon: <TrendingUp className="w-8 h-8 text-indigo-500" />,
      title: language === 'ar' ? 'التحليلات التنبؤية' : t("ai.predictive.analytics", "Predictive Analytics"),
      description: language === 'ar'
        ? 'توقعات مدعومة بالذكاء الاصطناعي لنجاح الأعمال واتجاهات السوق'
        : t("ai.aipowered.predictions.for", "AI-powered predictions for business success and market trends"),
      features: [
        language === 'ar' ? 'توقع النجاح' : t("ai.success.prediction", "Success Prediction"),
        language === 'ar' ? 'اتجاهات السوق' : t("ai.market.trends", "Market Trends"),
        language === 'ar' ? 'تحليل المخاطر' : t("ai.risk.analysis", "Risk Analysis"),
      ],
      color: 'indigo',
    },
    {
      icon: <Users className="w-8 h-8 text-pink-500" />,
      title: language === 'ar' ? 'الشبكات الذكية' : t("ai.smart.networking", "Smart Networking"),
      description: language === 'ar'
        ? 'مطابقة ذكية مع الموجهين والمستثمرين والشركاء المحتملين'
        : t("ai.intelligent.matching.with", "Intelligent matching with mentors, investors, and potential partners"),
      features: [
        language === 'ar' ? 'مطابقة الموجهين' : t("ai.mentor.matching", "Mentor Matching"),
        language === 'ar' ? 'توافق المستثمرين' : t("ai.investor.compatibility", "Investor Compatibility"),
        language === 'ar' ? 'شراكات استراتيجية' : t("ai.strategic.partnerships", "Strategic Partnerships"),
      ],
      color: 'pink',
    },
  ];

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: 'bg-blue-50 border-blue-200 hover:bg-blue-100',
      green: 'bg-green-50 border-green-200 hover:bg-green-100',
      purple: 'bg-purple-50 border-purple-200 hover:bg-purple-100',
      orange: 'bg-orange-50 border-orange-200 hover:bg-orange-100',
      indigo: 'bg-indigo-50 border-indigo-200 hover:bg-indigo-100',
      pink: 'bg-pink-50 border-pink-200 hover:bg-pink-100',
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  return (
    <section className={`py-16 bg-gray-50 ${className}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <div className={`flex items-center justify-center mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            <Brain className={`w-12 h-12 text-blue-500 mr-3 ${isRTL ? "space-x-reverse" : ""}`} />
            <Zap className="w-8 h-8 text-yellow-500" />
          </div>
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            {language === 'ar' ? 'الذكاء الاصطناعي المتقدم' : t("common.advanced.ai.intelligence", "Advanced AI Intelligence")}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {language === 'ar'
              ? 'تجاوز الدردشة الأساسية - اختبر الذكاء الاصطناعي كشريك أعمال نشط يفهم ويتوقع ويوجه رحلتك الريادية'
              : t("common.beyond.basic.chat", "Beyond basic chat - experience AI as an active business partner that understands, anticipates, and guides your entrepreneurial journey")
            }
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {intelligentFeatures.map((feature, index) => (
            <div
              key={index}
              className={`p-6 rounded-xl border-2 transition-all duration-300 hover:shadow-lg ${getColorClasses(feature.color)}`}
            >
              <div className={`flex items-center mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                {feature.icon}
                <h3 className={`text-xl font-semibold text-gray-900 ml-3 ${isRTL ? "space-x-reverse" : ""}`}>
                  {feature.title}
                </h3>
              </div>

              <p className="text-gray-700 mb-4 leading-relaxed">
                {feature.description}
              </p>

              <ul className="space-y-2">
                {feature.features.map((item, itemIndex) => (
                  <li key={itemIndex} className={`flex items-center text-sm text-gray-600 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <CheckCircle className={`w-4 h-4 text-green-500 mr-2 flex-shrink-0 ${isRTL ? "flex-row-reverse" : ""}`} />
                    {item}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Comparison Section */}
        <div className="bg-white rounded-2xl shadow-xl p-8 mb-16">
          <h3 className="text-2xl font-bold text-center text-gray-900 mb-8">
            {language === 'ar' ? 'التحول من الدردشة إلى الذكاء' : t("common.from.chat.to", "From Chat to Intelligence")}
          </h3>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Before */}
            <div className="bg-gray-50 rounded-xl p-6">
              <h4 className={`text-lg font-semibold text-gray-700 mb-4 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <div className={`w-3 h-3 bg-red-400 rounded-full mr-2 ${isRTL ? "space-x-reverse" : ""}`}></div>
                {language === 'ar' ? 'قبل: دردشة أساسية' : t("common.before.basic.chat", "Before: Basic Chat")}
              </h4>
              <div className="space-y-3 text-sm text-gray-600">
                <div className={`flex items-start ${isRTL ? "flex-row-reverse" : ""}`}>
                  <span className={`text-blue-600 mr-2 ${isRTL ? "space-x-reverse" : ""}`}>👤</span>
                  <span>{language === 'ar' ? '"ماذا يجب أن أفعل بعد ذلك؟"' : '"What should I do next?"'}</span>
                </div>
                <div className={`flex items-start ${isRTL ? "flex-row-reverse" : ""}`}>
                  <span className={`text-gray-500 mr-2 ${isRTL ? "space-x-reverse" : ""}`}>🤖</span>
                  <span>{language === 'ar' ? '"إليك بعض الاقتراحات العامة..."' : '"Here are some general suggestions..."'}</span>
                </div>
              </div>
              <div className="mt-4 text-xs text-gray-500">
                {language === 'ar' ? '• ردود تفاعلية • اقتراحات عامة • لا يوجد سياق' : '• Reactive responses • Generic suggestions • No context'}
              </div>
            </div>

            {/* After */}
            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 border-2 border-blue-200">
              <h4 className={`text-lg font-semibold text-blue-900 mb-4 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <div className={`w-3 h-3 bg-green-400 rounded-full mr-2 ${isRTL ? "space-x-reverse" : ""}`}></div>
                {language === 'ar' ? 'بعد: تطبيق ذكي' : t("common.after.intelligent.app", "After: Intelligent App")}
              </h4>
              <div className="space-y-2 text-sm">
                <div className={`flex items-center text-blue-900 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <Brain className={`w-4 h-4 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                  <span className="font-medium">
                    {language === 'ar' ? 'نقاط صحة الأعمال: 73.2/100 (↑5.3 هذا الأسبوع)' : 'Business Health: 73.2/100 (↑5.3 this week)'}
                  </span>
                </div>
                <div className={`flex items-center text-orange-700 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <Target className={`w-4 h-4 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                  <span>{language === 'ar' ? 'الأولوية التالية: التحقق من السوق (مستحق: 3 أيام)' : 'Next Priority: Market Validation (Due: 3 days)'}</span>
                </div>
                <div className={`flex items-center text-red-700 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <AlertTriangle className={`w-4 h-4 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                  <span>{language === 'ar' ? 'تنبيه: لم يتم تحديث الفكرة التجارية لمدة أسبوعين' : 'Alert: Business idea not updated for 2 weeks'}</span>
                </div>
                <div className={`flex items-center text-green-700 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <Lightbulb className={`w-4 h-4 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                  <span>{language === 'ar' ? 'توصية: إجراء مقابلات العملاء للتحقق' : t("common.recommendation.run.customer", "Recommendation: Run customer interviews")}</span>
                </div>
              </div>
              <div className="mt-4 text-xs text-blue-600">
                {language === 'ar' ? '• إرشادات استباقية • رؤى شخصية • ذكاء سياقي' : '• Proactive guidance • Personalized insights • Contextual intelligence'}
              </div>
            </div>
          </div>
        </div>

        {/* Benefits Section */}
        <div className="text-center">
          <h3 className="text-2xl font-bold text-gray-900 mb-8">
            {language === 'ar' ? 'الفوائد الفورية' : t("common.immediate.benefits", "Immediate Benefits")}
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[
              {
                icon: <Zap className="w-6 h-6 text-yellow-500" />,
                title: language === 'ar' ? 'إرشادات استباقية' : t("ai.proactive.guidance", "Proactive Guidance"),
                description: language === 'ar' ? 'يساعد الذكاء الاصطناعي بنشاط بدلاً من انتظار الأسئلة' : t("ai.ai.actively.helps", "AI actively helps instead of waiting for questions"),
              },
              {
                icon: <Eye className="w-6 h-6 text-blue-500" />,
                title: language === 'ar' ? 'رؤية التقدم' : t("ai.progress.visibility", "Progress Visibility"),
                description: language === 'ar' ? 'فهم واضح لحالة تطوير الأعمال' : t("ai.clear.understanding.of", "Clear understanding of business development status"),
              },
              {
                icon: <Clock className="w-6 h-6 text-green-500" />,
                title: language === 'ar' ? 'تحسين الوقت' : t("ai.time.optimization", "Time Optimization"),
                description: language === 'ar' ? 'إجراءات مرتبة حسب الأولوية مع تقديرات زمنية' : t("ai.prioritized.actions.with", "Prioritized actions with time estimates"),
              },
              {
                icon: <Activity className="w-6 h-6 text-purple-500" />,
                title: language === 'ar' ? 'تحسين مستمر' : t("ai.continuous.improvement", "Continuous Improvement"),
                description: language === 'ar' ? 'رؤى وتحسينات منتظمة' : t("ai.regular.insights.and", "Regular insights and optimization suggestions"),
              },
            ].map((benefit, index) => (
              <div key={index} className="text-center">
                <div className={`flex items-center justify-center w-12 h-12 bg-white rounded-full shadow-md mx-auto mb-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                  {benefit.icon}
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">{benefit.title}</h4>
                <p className="text-sm text-gray-600">{benefit.description}</p>
              </div>
            ))}
          </div>

          {/* CTA */}
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">
              {language === 'ar' ? 'جرب الذكاء الاصطناعي المتقدم اليوم' : t("common.experience.advanced.ai", "Experience Advanced AI Today")}
            </h3>
            <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
              {language === 'ar'
                ? 'انضم إلى رواد الأعمال الذين يستخدمون الذكاء الاصطناعي كشريك أعمال نشط لتسريع نجاحهم'
                : t("common.join.entrepreneurs.who", "Join entrepreneurs who use AI as an active business partner to accelerate their success")
              }
            </p>
            <button className={`bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors inline-flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              {language === 'ar' ? 'ابدأ الآن' : t("common.get.started.now", "Get Started Now")}
              <ArrowRight className={`w-5 h-5 ml-2 ${isRTL ? "space-x-reverse" : ""}`} />
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default EnhancedAIFeatures;
