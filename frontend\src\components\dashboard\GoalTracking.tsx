import React, { useState, useEffect } from 'react';
import {
  Target,
  CheckCircle,
  Clock,
  AlertTriangle,
  Plus,
  Calendar,
  User,
  ArrowRight,
  Edit,
  Trash2,
  Download,
  BarChart
} from 'lucide-react';
import { useAppSelector } from '../../store/hooks';
import { BusinessGoal, businessGoalsAPI } from '../../services/milestoneApi';
import { exportToCSV, exportToPDF } from '../../utils/exportUtils';

import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';

interface GoalTrackingProps {
  businessIdeaId: number;
  businessIdeaTitle: string;
}

const GoalTracking: React.FC<GoalTrackingProps> = ({ businessIdeaId, businessIdeaTitle  }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { user } = useAppSelector((state) => state.auth);
  const [goals, setGoals] = useState<BusinessGoal[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [selectedGoal, setSelectedGoal] = useState<BusinessGoal | null>(null);
  const [achievementNotes, setAchievementNotes] = useState('');
  const [showAchievementModal, setShowAchievementModal] = useState(false);
  const [filterTimeframe, setFilterTimeframe] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');

  useEffect(() => {
    fetchGoals();
  }, [businessIdeaId]);

  const fetchGoals = async () => {
    setLoading(true);
    try {
      const data = await businessGoalsAPI.getGoals(businessIdeaId);
      setGoals(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching goals:', err);
      setError(t("dashboard.failed.to.load", "Failed to load goals. Please try again later."));
    } finally {
      setLoading(false);
    }
  };

  const handleAchieveGoal = async () => {
    if (!selectedGoal) return;

    try {
      await businessGoalsAPI.achieveGoal(selectedGoal.id, achievementNotes);
      fetchGoals();
      setShowAchievementModal(false);
      setAchievementNotes('');
      setSelectedGoal(null);
    } catch (err) {
      console.error('Error achieving goal:', err);
      setError(t("dashboard.failed.to.mark", "Failed to mark goal as achieved. Please try again later."));
    }
  };

  const handleExportCSV = () => {
    const data = goals.map(goal => ({
      Title: goal.title,
      Description: goal.description,
      Status: goal.status_display,
      Timeframe: goal.timeframe_display,
      'Target Date': goal.target_date ? new Date(goal.target_date).toLocaleDateString() : t("dashboard.not.set", "Not set"),
      'Progress': `${goal.progress_percentage}%`,
      'Created By': `${goal.created_by.first_name} ${goal.created_by.last_name}`,
      'Achievement Date': goal.achievement_date ? new Date(goal.achievement_date).toLocaleDateString() : t("dashboard.not.achieved", "Not achieved"),
      'Achievement Notes': goal.achievement_notes || ''
    }));

    exportToCSV(data, `${businessIdeaTitle.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_goals`);
  };

  const handleExportPDF = () => {
    const columns = [
      { header: t("dashboard.title.datakey.title", "Title"), dataKey: 'title' },
      { header: t("dashboard.status.datakey.status", "Status"), dataKey: 'status' },
      { header: t("dashboard.timeframe.datakey.timeframe", "Timeframe"), dataKey: 'timeframe' },
      { header: t("dashboard.target.date.datakey", "Target Date"), dataKey: 'targetDate' },
      { header: t("dashboard.progress.datakey.progress", "Progress"), dataKey: 'progress' }
    ];

    const data = goals.map(goal => ({
      title: goal.title,
      status: goal.status_display,
      timeframe: goal.timeframe_display,
      targetDate: goal.target_date ? new Date(goal.target_date).toLocaleDateString() : t("dashboard.not.set", "Not set"),
      progress: `${goal.progress_percentage}%`
    }));

    exportToPDF(
      columns,
      data,
      `${businessIdeaTitle} - Goals`,
      `Strategic Goals for ${businessIdeaTitle}`,
      `${businessIdeaTitle.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_goals`
    );
  };

  // Apply filters
  const filteredGoals = goals.filter(goal => {
    const matchesTimeframe = filterTimeframe === 'all' || goal.timeframe === filterTimeframe;
    const matchesStatus = filterStatus === 'all' || goal.status === filterStatus;
    return matchesTimeframe && matchesStatus;
  });

  // Group goals by timeframe
  const groupedGoals = {
    short_term: filteredGoals.filter(g => g.timeframe === 'short_term'),
    medium_term: filteredGoals.filter(g => g.timeframe === 'medium_term'),
    long_term: filteredGoals.filter(g => g.timeframe === 'long_term')
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-blue-900/50 text-blue-200';
      case 'achieved': return 'bg-green-900/50 text-green-200';
      case 'revised': return 'bg-yellow-900/50 text-yellow-200';
      case 'abandoned': return 'bg-red-900/50 text-red-200';
      default: return 'bg-gray-700 text-gray-200';
    }
  };

  const getTimeframeColor = (timeframe: string) => {
    switch (timeframe) {
      case 'short_term': return 'bg-green-900/50 text-green-200';
      case 'medium_term': return 'bg-blue-900/50 text-blue-200';
      case 'long_term': return 'bg-purple-900/50 text-purple-200';
      default: return 'bg-gray-700 text-gray-200';
    }
  };

  const getTextClass = (type: 'primary' | 'secondary') => {
    return type === 'primary' ? 'text-glass-primary' : 'text-glass-secondary';
  };

  if (loading) {
    return (
      <div className={`flex justify-center py-12 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="glass-light border rounded-lg p-4 border-red-800/50 bg-red-900/30">
        <div className="text-red-400">{error}</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
        <h2 className="text-xl font-bold">{t("dashboard.strategic.goals", "Strategic Goals")}</h2>
        <div className={`flex space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
          <button
            onClick={handleExportCSV}
            className={`px-3 py-1.5 bg-green-600 hover:bg-green-700 rounded-md text-white text-sm flex items-center transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <Download size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> CSV
          </button>
          <button
            onClick={handleExportPDF}
            className={`px-3 py-1.5 bg-red-600 hover:bg-red-700 rounded-md text-white text-sm flex items-center transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <Download size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> PDF
          </button>
          <button
            onClick={() => setShowAddForm(true)}
            className={`px-3 py-1.5 bg-purple-600 hover:bg-purple-700 rounded-md text-white text-sm flex items-center transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <Plus size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> Add Goal
          </button>
        </div>
      </div>

      {/* Filter Controls */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium mb-2">{t("dashboard.timeframe", "Timeframe")}</label>
          <div className={`flex space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
            <button
              onClick={() => setFilterTimeframe('all')}
              className={`px-3 py-1.5 rounded-md text-sm flex items-center transition-colors ${isRTL ? "flex-row-reverse" : ""} ${
                filterTimeframe === 'all' ? 'bg-purple-600 text-white' : 'glass-light text-glass-secondary hover:bg-gray-700'
              }`}
            >
              All
            </button>
            <button
              onClick={() => setFilterTimeframe('short_term')}
              className={`px-3 py-1.5 rounded-md text-sm flex items-center transition-colors ${isRTL ? "flex-row-reverse" : ""} ${
                filterTimeframe === 'short_term' ? 'bg-purple-600 text-white' : 'glass-light text-glass-secondary hover:bg-gray-700'
              }`}
            >
              Short Term
            </button>
            <button
              onClick={() => setFilterTimeframe('medium_term')}
              className={`px-3 py-1.5 rounded-md text-sm flex items-center transition-colors ${isRTL ? "flex-row-reverse" : ""} ${
                filterTimeframe === 'medium_term' ? 'bg-purple-600 text-white' : 'glass-light text-glass-secondary hover:bg-gray-700'
              }`}
            >
              Medium Term
            </button>
            <button
              onClick={() => setFilterTimeframe('long_term')}
              className={`px-3 py-1.5 rounded-md text-sm flex items-center transition-colors ${isRTL ? "flex-row-reverse" : ""} ${
                filterTimeframe === 'long_term' ? 'bg-purple-600 text-white' : 'glass-light text-glass-secondary hover:bg-gray-700'
              }`}
            >
              Long Term
            </button>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">{t("dashboard.status", t("common.status", "Status"))}</label>
          <div className={`flex space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
            <button
              onClick={() => setFilterStatus('all')}
              className={`px-3 py-1.5 rounded-md text-sm flex items-center transition-colors ${isRTL ? "flex-row-reverse" : ""} ${
                filterStatus === 'all' ? 'bg-purple-600 text-white' : 'glass-light text-glass-secondary hover:bg-gray-700'
              }`}
            >
              All
            </button>
            <button
              onClick={() => setFilterStatus('active')}
              className={`px-3 py-1.5 rounded-md text-sm flex items-center transition-colors ${isRTL ? "flex-row-reverse" : ""} ${
                filterStatus === 'active' ? 'bg-purple-600 text-white' : 'glass-light text-glass-secondary hover:bg-gray-700'
              }`}
            >
              Active
            </button>
            <button
              onClick={() => setFilterStatus('achieved')}
              className={`px-3 py-1.5 rounded-md text-sm flex items-center transition-colors ${isRTL ? "flex-row-reverse" : ""} ${
                filterStatus === 'achieved' ? 'bg-purple-600 text-white' : 'glass-light text-glass-secondary hover:bg-gray-700'
              }`}
            >
              Achieved
            </button>
            <button
              onClick={() => setFilterStatus('revised')}
              className={`px-3 py-1.5 rounded-md text-sm flex items-center transition-colors ${isRTL ? "flex-row-reverse" : ""} ${
                filterStatus === 'revised' ? 'bg-purple-600 text-white' : 'glass-light text-glass-secondary hover:bg-gray-700'
              }`}
            >
              Revised
            </button>
          </div>
        </div>
      </div>

      {/* Goals List */}
      {filteredGoals.length === 0 ? (
        <div className="glass-light border rounded-lg p-6 text-center">
          <p className="mb-4 text-glass-secondary">
            No goals found. Add your first strategic goal to track progress.
          </p>
          <button
            onClick={() => setShowAddForm(true)}
            className={`mt-4 px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white flex items-center mx-auto ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <Plus size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} /> Add Goal
          </button>
        </div>
      ) : (
        <div className="space-y-6">
          {Object.entries(groupedGoals).map(([timeframe, timeframeGoals]) =>
            timeframeGoals.length > 0 && (
              <div key={timeframe} className="space-y-4">
                <h3 className={`text-lg font-medium flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                  <Target size={18} className={`mr-2 text-purple-400 ${isRTL ? "space-x-reverse" : ""}`} />
                  {timeframe === 'short_term' && 'Short Term (0-3 months)'}
                  {timeframe === 'medium_term' && 'Medium Term (3-12 months)'}
                  {timeframe === 'long_term' && 'Long Term (1+ years)'}
                  <span className={`ml-2 text-sm text-gray-400 ${isRTL ? "space-x-reverse" : ""}`}>({timeframeGoals.length})</span>
                </h3>

                <div className="space-y-4">
                  {timeframeGoals.map(goal => (
                    <div
                      key={goal.id}
                      className="glass-light border rounded-lg p-4"
                    >
                      <div className={`flex justify-between items-start mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <h4 className={`text-lg font-medium ${getTextClass('primary')}`}>{goal.title}</h4>
                        <div className={`flex space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                          <span className={`px-2 py-1 rounded text-xs ${getTimeframeColor(goal.timeframe)}`}>
                            {goal.timeframe_display}
                          </span>
                          <span className={`px-2 py-1 rounded text-xs ${getStatusColor(goal.status)}`}>
                            {goal.status_display}
                          </span>
                        </div>
                      </div>

                      <p className={`mb-3 ${getTextClass('secondary')}`}>
                        {goal.description}
                      </p>

                      {/* Progress Bar */}
                      <div className="mb-3">
                        <div className={`flex justify-between text-sm mb-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                          <span className={getTextClass('secondary')}>
                            Progress
                          </span>
                          <span className={getTextClass('secondary')}>
                            {goal.progress_percentage}%
                          </span>
                        </div>
                        <div className="w-full rounded-full h-2 bg-gray-700">
                          <div
                            className={`h-2 rounded-full ${
                              goal.progress_percentage >= 100
                                ? 'bg-green-500'
                                : goal.progress_percentage >= 50
                                ? 'bg-blue-500'
                                : 'bg-purple-500'}
                            }`}
                            style={{ width: `${goal.progress_percentage}%` }}
                          ></div>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4 mb-3 text-sm">
                        {goal.target_date && (
                          <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""} ${getTextClass('secondary')}`}>
                            <Calendar size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                            <span>Target: {new Date(goal.target_date).toLocaleDateString()}</span>
                          </div>
                        )}

                        <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""} ${getTextClass('secondary')}`}>
                          <User size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                          <span>
                            Created by: {goal.created_by.first_name} {goal.created_by.last_name}
                          </span>
                        </div>
                      </div>

                      {goal.status === 'achieved' && goal.achievement_date && (
                        <div className={`mb-3 text-green-400 text-sm flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                          <CheckCircle size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                          <span>Achieved on {new Date(goal.achievement_date).toLocaleDateString()}</span>
                        </div>
                      )}

                      <div className={`flex justify-end space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                        {goal.status === 'active' && (
                          <button
                            onClick={() => {
                              setSelectedGoal(goal);
                              setShowAchievementModal(true);
                            }}
                            className={`px-2 py-1 bg-green-600 hover:bg-green-700 rounded text-white text-xs flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                          >
                            <CheckCircle size={12} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> Mark Achieved
                          </button>
                        )}

                        <button
                          onClick={() => {
                            // Handle edit
                          }}
                          className={`px-2 py-1 bg-blue-600 hover:bg-blue-700 rounded text-white text-xs flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                        >
                          <Edit size={12} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> Edit
                        </button>

                        <button
                          onClick={() => {
                            // Handle delete
                          }}
                          className={`px-2 py-1 bg-red-600 hover:bg-red-700 rounded text-white text-xs flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                        >
                          <Trash2 size={12} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> Delete
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )
          )}
        </div>
      )}

      {/* Achievement Modal */}
      {showAchievementModal && selectedGoal && (
        <div className={`fixed inset-0 bg-black/70 flex items-center justify-center z-50 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="glass-light rounded-lg p-6 max-w-md w-full">
            <h3 className={`text-xl font-bold mb-4 ${getTextClass('primary')}`}>{t("dashboard.mark.goal.as", "Mark Goal as Achieved")}</h3>
            <div className={`mb-4 ${getTextClass('secondary')}`}>Mark "{selectedGoal.title}" as achieved?</div>

            <div className="mb-4">
              <label className={`block text-sm font-medium mb-1 ${getTextClass('secondary')}`}>{t("dashboard.achievement.notes", "Achievement Notes")}</label>
              <div className="w-full border rounded-md p-2 bg-gray-800 border-gray-700">
                <textarea
                  className={`w-full bg-transparent outline-none resize-none ${getTextClass('primary')}`}
                  rows={4}
                  value={achievementNotes}
                  onChange={(e) => setAchievementNotes(e.target.value)}
                  placeholder={t("dashboard.add.any.notes", "Add any notes about achieving this goal...")}
                />
              </div>
            </div>

            <div className={`flex justify-end space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                onClick={() => {
                  setShowAchievementModal(false);
                  setSelectedGoal(null);
                  setAchievementNotes('');
                }}
                className="px-4 py-2 rounded-md bg-gray-700 hover:bg-gray-600 text-white"
              >
                Cancel
              </button>
              <button
                onClick={handleAchieveGoal}
                className={`px-4 py-2 bg-green-600 hover:bg-green-700 rounded-md text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <CheckCircle size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} /> Mark as Achieved
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GoalTracking;
