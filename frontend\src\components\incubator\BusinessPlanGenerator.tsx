import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  FileText,
  Lightbulb,
  Plus,
  RefreshCw,
  Check,
  AlertCircle,
  ChevronDown,
  ChevronUp,
  ArrowRight,
  Sparkles
} from 'lucide-react';
import { useAppSelector } from '../../store/hooks';
import {
  BusinessPlanTemplate,
  BusinessPlan,
  businessPlanTemplatesAPI,
  businessPlansAPI
} from '../../services/businessPlanApi';
import { BusinessIdea, businessIdeasAPI } from '../../services/incubatorApi';

interface BusinessPlanGeneratorProps {
  businessIdeaId?: number;
  preselectedTemplate?: string | null;
  onSuccess?: (businessPlanId: number) => void;
}

const BusinessPlanGenerator: React.FC<BusinessPlanGeneratorProps> = ({
  businessIdeaId,
  preselectedTemplate,
  onSuccess
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { user } = useAppSelector(state => state.auth);

  const [businessIdeas, setBusinessIdeas] = useState<BusinessIdea[]>([]);
  const [templates, setTemplates] = useState<BusinessPlanTemplate[]>([]);
  const [selectedBusinessIdea, setSelectedBusinessIdea] = useState<number | null>(businessIdeaId || null);
  const [selectedTemplate, setSelectedTemplate] = useState<number | null>(null);
  const [planTitle, setPlanTitle] = useState('');
  const [isGeneratingTemplate, setIsGeneratingTemplate] = useState(false);
  const [industry, setIndustry] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showTemplateForm, setShowTemplateForm] = useState(false);

  // Fetch business ideas and templates
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch business ideas
        const ideas = await businessIdeasAPI.getBusinessIdeas();
        setBusinessIdeas(ideas);

        // If businessIdeaId is provided, set it as selected
        if (businessIdeaId) {
          setSelectedBusinessIdea(businessIdeaId);

          // Set default plan title based on business idea
          const idea = ideas.find(i => i.id === businessIdeaId);
          if (idea) {
            setPlanTitle(`${idea.title} - ${t('businessPlan.businessPlan')}`);
          }
        }

        // Fetch templates
        const templateData = await businessPlanTemplatesAPI.getTemplates();
        setTemplates(templateData);

        // If preselectedTemplate is provided, set it as selected
        if (preselectedTemplate) {
          const templateId = parseInt(preselectedTemplate);
          if (!isNaN(templateId)) {
            setSelectedTemplate(templateId);
          }
        }
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(t('businessPlan.failedToLoadData'));
      }
    };

    fetchData();
  }, [businessIdeaId, preselectedTemplate]);

  const handleGenerateTemplate = async () => {
    if (!industry.trim()) {
      setError(t('businessPlan.enterIndustryRequired'));
      return;
    }

    setIsGeneratingTemplate(true);
    setError(null);

    try {
      const newTemplate = await businessPlanTemplatesAPI.generateTemplate(industry);
      setTemplates(prev => [...prev, newTemplate]);
      setSelectedTemplate(newTemplate.id);
      setShowTemplateForm(false);
      setSuccess(t('businessPlan.templateGenerated', { industry }));
    } catch (err) {
      console.error('Error generating template:', err);
      setError(t('businessPlan.failedToGenerateTemplate'));
    } finally {
      setIsGeneratingTemplate(false);
    }
  };

  const handleCreatePlan = async () => {
    if (!selectedTemplate) {
      setError(t('businessPlan.selectTemplateRequired'));
      return;
    }

    if (!planTitle.trim()) {
      setError(t('businessPlan.enterPlanTitleRequired'));
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Create business plan data
      const planData: any = {
        template: selectedTemplate,
        title: planTitle,
        status: 'draft'
      };

      // Only add business_idea if one is selected
      if (selectedBusinessIdea) {
        planData.business_idea = selectedBusinessIdea;
      }

      const newPlan = await businessPlansAPI.createPlan(planData);

      setSuccess(t('businessPlan.planCreated'));

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess(newPlan.id);
      } else {
        // Navigate to the business plan editor
        navigate(`/dashboard/business-plans/${newPlan.id}`);
      }
    } catch (err: any) {
      console.error('Error creating business plan:', err);

      // Handle authentication errors specifically
      if (err?.message?.includes('token not valid') || err?.message?.includes('Unauthorized') || err?.status === 401) {
        setError(t('auth.sessionExpired', 'Your session has expired. Please log in again.'));
        // Optionally redirect to login after a delay
        setTimeout(() => {
          window.location.href = '/login';
        }, 3000);
      } else if (err?.message) {
        setError(err.message);
      } else {
        setError(t('businessPlan.failedToCreate'));
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
      <div className="flex items-center mb-4">
        <FileText size={24} className="text-purple-400 mr-2" />
        <h2 className="text-xl font-bold">{t('businessPlan.createNewPlan')}</h2>
      </div>

      {success && (
        <div className="mb-4 p-3 bg-green-900/50 text-green-200 rounded-md flex items-center">
          <Check size={18} className="mr-2" />
          {success}
        </div>
      )}

      {error && (
        <div className="mb-4 p-3 bg-red-900/50 text-red-200 rounded-md flex items-center">
          <AlertCircle size={18} className="mr-2" />
          {error}
        </div>
      )}

      <div className="space-y-4">
        {/* Template Selection */}
        <div>
          <label className="block text-sm font-medium mb-1">{t('businessPlan.template')}</label>
          <select
            value={selectedTemplate || ''}
            onChange={(e) => setSelectedTemplate(parseInt(e.target.value))}
            disabled={loading}
            className="w-full bg-indigo-950 border border-indigo-700 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <option value="">{t('businessPlan.selectTemplate')}</option>
            {templates.map(template => (
              <option key={template.id} value={template.id}>
                {template.name} - {template.industry}
              </option>
            ))}
          </select>

          <div className="mt-2">
            <button
              type="button"
              onClick={() => setShowTemplateForm(!showTemplateForm)}
              className="text-purple-400 hover:text-purple-300 text-sm flex items-center"
            >
              {showTemplateForm ? <ChevronUp size={16} className="mr-1" /> : <ChevronDown size={16} className="mr-1" />}
              {showTemplateForm ? t('businessPlan.hideTemplateGenerator') : t('businessPlan.generateCustomTemplate')}
            </button>
          </div>
        </div>

        {/* Plan Title */}
        <div>
          <label className="block text-sm font-medium mb-1">{t('businessPlan.planTitle')}</label>
          <input
            type="text"
            value={planTitle}
            onChange={(e) => setPlanTitle(e.target.value)}
            disabled={loading}
            placeholder={t('businessPlan.enterPlanTitle')}
            className="w-full bg-indigo-950 border border-indigo-700 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
          />
        </div>

        {/* Business Idea Selection */}
        <div>
          <label className="block text-sm font-medium mb-1">
            {t('businessPlan.businessIdea')}
          </label>
          <select
            value={selectedBusinessIdea || ''}
            onChange={(e) => {
              const ideaId = e.target.value ? parseInt(e.target.value) : null;
              setSelectedBusinessIdea(ideaId);

              // Update plan title based on selected idea
              if (ideaId) {
                const idea = businessIdeas.find(i => i.id === ideaId);
                if (idea) {
                  setPlanTitle(`${idea.title} - ${t('businessPlan.businessPlan')}`);
                }
              } else {
                // Reset to default title if no business idea selected
                setPlanTitle('');
              }
            }}
            disabled={!!businessIdeaId || loading}
            className="w-full bg-indigo-950 border border-indigo-700 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <option value="">{t('businessPlan.selectBusinessIdea', 'Select a business idea or skip')}</option>
            {businessIdeas.map(idea => (
              <option key={idea.id} value={idea.id}>
                {idea.title}
              </option>
            ))}
          </select>
          <p className="text-xs text-gray-400 mt-1">
            {t('businessPlan.businessIdeaOptionalNote', 'Templates work great on their own! You can always connect a business idea later.')}
          </p>
        </div>

        {/* Template Generator Form */}
        {showTemplateForm && (
          <div className="p-4 bg-indigo-950/50 rounded-md border border-indigo-800/50">
            <h3 className="text-md font-medium mb-2 flex items-center">
              <Sparkles size={16} className="text-yellow-400 mr-1" />
              {t('businessPlan.aiTemplateGenerator')}
            </h3>
            <div className="text-sm text-gray-400 mb-3">
              {t('businessPlan.generateCustomTemplateDesc')}
            </div>

            <div className="flex space-x-2">
              <input
                type="text"
                value={industry}
                onChange={(e) => setIndustry(e.target.value)}
                disabled={isGeneratingTemplate}
                placeholder={t('businessPlan.enterIndustry')}
                className="flex-1 bg-indigo-950 border border-indigo-700 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
              <button
                type="button"
                onClick={handleGenerateTemplate}
                disabled={isGeneratingTemplate || !industry.trim()}
                className="px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-800/50 disabled:cursor-not-allowed rounded-md text-white flex items-center"
              >
                {isGeneratingTemplate ? (
                  <RefreshCw size={16} className="mr-1 animate-spin" />
                ) : (
                  <Sparkles size={16} className="mr-1" />
                )}
                {isGeneratingTemplate ? t('businessPlan.generating') : t('businessPlan.generate')}
              </button>
            </div>
          </div>
        )}

        {/* Create Button */}
        <div className="mt-6">
          <button
            type="button"
            onClick={handleCreatePlan}
            disabled={loading || !selectedTemplate || !planTitle.trim()}
            className="w-full px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-800/50 disabled:cursor-not-allowed rounded-md text-white flex items-center justify-center"
          >
            {loading ? (
              <RefreshCw size={18} className="mr-1 animate-spin" />
            ) : (
              <Plus size={18} className="mr-1" />
            )}
            {loading ? t('businessPlan.creating') : t('businessPlan.createNewPlan')}
          </button>
        </div>
      </div>
    </div>
  );
};

export default BusinessPlanGenerator;
