from django.db import models
from django.contrib.auth.models import User
from .models_base import BusinessIdea
from .models_milestone import BusinessAnalytics

class PredictiveAnalytics(models.Model):
    """Predictive analytics data for business ideas"""

    business_idea = models.OneToOneField(BusinessIdea, on_delete=models.CASCADE, related_name='predictive_analytics')
    
    # Growth predictions (stored as JSON)
    growth_predictions = models.JSONField(default=dict, help_text="Predicted growth metrics for the next 6 months")
    
    # Milestone predictions (stored as JSON)
    milestone_predictions = models.J<PERSON><PERSON>ield(default=dict, help_text="Predicted completion dates for milestones")
    
    # Success probability
    success_probability = models.FloatField(default=0.0, help_text="Probability of business success (0-100)")
    
    # Risk factors (stored as JSON)
    risk_factors = models.JSONField(default=dict, help_text="Identified risk factors and their severity")
    
    # Opportunity areas (stored as JSON)
    opportunity_areas = models.J<PERSON><PERSON>ield(default=dict, help_text="Identified opportunity areas for growth")
    
    # Prediction confidence
    prediction_confidence = models.CharField(
        max_length=20, 
        choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High')],
        default='low',
        help_text="Confidence level in the predictions"
    )
    
    # Last calculated
    last_calculated = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"Predictive Analytics for {self.business_idea.title}"


class ComparativeAnalytics(models.Model):
    """Comparative analytics data for business ideas"""
    
    business_idea = models.OneToOneField(BusinessIdea, on_delete=models.CASCADE, related_name='comparative_analytics')
    
    # Similar ideas (stored as JSON)
    similar_ideas = models.JSONField(default=dict, help_text="List of similar business ideas and their metrics")
    
    # Industry averages (stored as JSON)
    industry_averages = models.JSONField(default=dict, help_text="Average metrics for the industry")
    
    # Stage averages (stored as JSON)
    stage_averages = models.JSONField(default=dict, help_text="Average metrics for the current stage")
    
    # Percentile rankings (stored as JSON)
    percentile_rankings = models.JSONField(default=dict, help_text="Percentile rankings across different metrics")
    
    # Competitive advantages (stored as JSON)
    competitive_advantages = models.JSONField(default=dict, help_text="Identified competitive advantages")
    
    # Competitive disadvantages (stored as JSON)
    competitive_disadvantages = models.JSONField(default=dict, help_text="Identified competitive disadvantages")
    
    # Last calculated
    last_calculated = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"Comparative Analytics for {self.business_idea.title}"


class AnalyticsSnapshot(models.Model):
    """Historical snapshots of analytics data for trend analysis"""
    
    business_idea = models.ForeignKey(BusinessIdea, on_delete=models.CASCADE, related_name='analytics_snapshots')
    
    # Snapshot date
    snapshot_date = models.DateField(auto_now_add=True)
    
    # Progress metrics
    progress_rate = models.FloatField(default=0.0)
    milestone_completion_rate = models.FloatField(default=0.0)
    goal_achievement_rate = models.FloatField(default=0.0)
    
    # Engagement metrics
    team_size = models.IntegerField(default=1)
    mentor_engagement = models.FloatField(default=0.0)
    
    # Comparative metrics
    industry_percentile = models.FloatField(default=0.0)
    stage_percentile = models.FloatField(default=0.0)
    
    # Success probability
    success_probability = models.FloatField(default=0.0)
    
    class Meta:
        ordering = ['-snapshot_date']
        unique_together = ('business_idea', 'snapshot_date')
    
    def __str__(self):
        return f"Analytics Snapshot for {self.business_idea.title} on {self.snapshot_date}"


class MetricDefinition(models.Model):
    """Definitions and explanations for analytics metrics"""
    
    metric_key = models.CharField(max_length=100, unique=True, help_text="Unique identifier for the metric")
    display_name = models.CharField(max_length=100, help_text="User-friendly name for the metric")
    description = models.TextField(help_text="Detailed explanation of what the metric measures")
    calculation_method = models.TextField(help_text="How the metric is calculated")
    
    # Interpretation guidelines
    low_value_interpretation = models.TextField(help_text="What a low value means")
    medium_value_interpretation = models.TextField(help_text="What a medium value means")
    high_value_interpretation = models.TextField(help_text="What a high value means")
    
    # Improvement suggestions
    improvement_suggestions = models.TextField(help_text="Suggestions for improving this metric")
    
    # Visualization settings (stored as JSON)
    visualization_settings = models.JSONField(default=dict, help_text="Settings for visualizing this metric")
    
    # Category
    category = models.CharField(
        max_length=50,
        choices=[
            ('progress', 'Progress Metrics'),
            ('engagement', 'Engagement Metrics'),
            ('comparative', 'Comparative Metrics'),
            ('predictive', 'Predictive Metrics')
        ],
        default='progress'
    )
    
    # Display order
    display_order = models.IntegerField(default=0, help_text="Order in which to display this metric")
    
    class Meta:
        ordering = ['category', 'display_order']
    
    def __str__(self):
        return self.display_name
