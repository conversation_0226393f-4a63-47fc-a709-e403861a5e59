import React, { useEffect } from 'react';

interface LanguageProviderProps {
  children: React.ReactNode;
}

const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  useEffect(() => {
    // Get language from localStorage or default to 'en'
    const savedLanguage = localStorage.getItem('language') || 'en';
    const direction = savedLanguage === 'ar' ? 'rtl' : 'ltr';

    // Set the direction and language attributes on the html element
    document.documentElement.dir = direction;
    document.documentElement.lang = savedLanguage;

    // Add a class to the body for additional styling if needed
    document.body.classList.toggle('rtl', direction === 'rtl');
  }, []);

  return <>{children}</>;
};

// Export the useLanguage hook from the hooks directory
export { useLanguage } from '../hooks/useLanguage';

export default LanguageProvider;
