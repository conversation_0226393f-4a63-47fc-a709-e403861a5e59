import React, { useState, useEffect, useCallback } from 'react';
import { Search, Edit, Trash2, Plus, MessageSquare, Heart, X, RefreshCw } from 'lucide-react';

import { Post, postsAPI } from '../../../services/api';
import { useAppSelector } from '../../../store/hooks';
import { AdvancedFilter } from '../common';
import { RichTextEditor } from '../../../components/common';
import { FilterValue } from '../common/AdvancedFilter';
import useInfiniteScroll from '../../../hooks/useInfiniteScroll';
import { PostSkeleton, SkeletonList } from '../../ui/Skeleton';
import { useLanguage } from '../../../hooks/useLanguage';
import { useTranslation } from 'react-i18next';
interface PostFormData {
  title: string;
  content: string;
}

const PostsManagementInfinite: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const { user } = useAppSelector(state => state.auth);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPost, setSelectedPost] = useState<Post | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [expandedPost, setExpandedPost] = useState<number | null>(null);
  const [formError, setFormError] = useState<string | null>(null);
  const [formSuccess, setFormSuccess] = useState<string | null>(null);
  const [formSubmitting, setFormSubmitting] = useState(false);
  const [allPosts, setAllPosts] = useState<Post[]>([]);

  // Filtering state
  const [activeFilters, setActiveFilters] = useState<Record<string, FilterValue>>({});

  // Bulk actions state
  const [selectedItems, setSelectedItems] = useState<number[]>([]);

  // Form data for create/edit
  const [formData, setFormData] = useState<PostFormData>({
    title: '',
    content: ''
  });

  // Fetch posts with pagination
  const fetchPaginatedPosts = useCallback(async (page: number, limit: number) => {
    try {
      // In a real implementation, you would call an API that supports pagination
      // For now, we'll simulate pagination by slicing the allPosts array
      const start = (page - 1) * limit;
      const end = start + limit;

      // If this is the first page, fetch all posts first
      if (page === 1 && allPosts.length === 0) {
        const posts = await postsAPI.getPosts();
        setAllPosts(posts);
        return posts.slice(0, limit);
      }

      return allPosts.slice(start, end);
    } catch (error) {
      console.error('Error fetching paginated posts:', error);
      throw error;
    }
  }, [allPosts]);

  // Use the infinite scroll hook
  const {
    data: posts,
    loading,
    error,
    hasMore,
    lastElementRef,
    refresh: refreshPosts,
    loadMore
  } = useInfiniteScroll<Post>({
    fetchData: fetchPaginatedPosts,
    limit: 10,
    enabled: !!user?.id,
  });

  // Effect to refresh posts when filters change
  useEffect(() => {
    if (Object.keys(activeFilters).length > 0) {
      refreshPosts();
    }
  }, [activeFilters, refreshPosts]);

  // Filter posts based on search term and active filters
  const filteredPosts = posts.filter(post => {
    // Search term filter
    const matchesSearch =
      post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.author.username.toLowerCase().includes(searchTerm.toLowerCase());

    if (!matchesSearch) return false;

    // Apply advanced filters
    for (const [filterId, filter] of Object.entries(activeFilters)) {
      switch (filterId) {
        case 'title':
          if (!post.title.toLowerCase().includes(String(filter.value).toLowerCase())) return false;
          break;
        case 'content':
          if (!post.content.toLowerCase().includes(String(filter.value).toLowerCase())) return false;
          break;
        case 'author':
          if (!post.author.username.toLowerCase().includes(String(filter.value).toLowerCase())) return false;
          break;
        case 'moderation_status':
          if (post.moderation_status !== filter.value) return false;
          break;
        case 'created_at':
          if (filter.value instanceof Date) {
            const postDate = new Date(post.created_at);
            const filterDate = filter.value;
            if (postDate.getFullYear() !== filterDate.getFullYear() ||
                postDate.getMonth() !== filterDate.getMonth() ||
                postDate.getDate() !== filterDate.getDate()) {
              return false;
            }
          }
          break;
      }
    }

    return true;
  });

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | string, editorName?: string) => {
    if (typeof e === 'string' && editorName) {
      // Handle rich text editor input
      setFormData(prev => ({ ...prev, [editorName]: e }));
    } else if (typeof e !== 'string') {
      // Handle regular input
      const { name, value } = e.target;
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      title: '',
      content: ''
    });
    setFormError(null);
    setFormSuccess(null);
  };

  // Open create modal
  const openCreateModal = () => {
    resetForm();
    setIsCreateModalOpen(true);
  };

  // Handle edit post
  const handleEditPost = (post: Post) => {
    setSelectedPost(post);
    setFormData({
      title: post.title,
      content: post.content
    });
    setIsEditModalOpen(true);
  };

  // Handle delete post
  const handleDeletePost = (post: Post) => {
    setSelectedPost(post);
    setIsDeleteModalOpen(true);
  };

  // Create new post
  const createPost = async () => {
    setFormSubmitting(true);
    setFormError(null);
    setFormSuccess(null);

    try {
      // Validate form
      if (!formData.title || !formData.content) {
        setFormError(t("admin.please.fill.in", "Please fill in all required fields"));
        setFormSubmitting(false);
        return;
      }

      // Create post data
      const postData = {
        title: formData.title,
        content: formData.content,
        author_id: user?.id || 0
      };

      // Call API to create post
      const newPost = await postsAPI.createPost(postData);

      // Update local state
      setAllPosts([newPost, ...allPosts]);
      refreshPosts();

      // Show success message
      setFormSuccess(t("admin.post.created.successfully", "Post created successfully!"));

      // Close modal after a delay
      setTimeout(() => {
        setIsCreateModalOpen(false);
        resetForm();
      }, 1500);
    } catch (error) {
      console.error('Error creating post:', error);
      setFormError(t("admin.failed.to.create", "Failed to create post. Please try again."));
    } finally {
      setFormSubmitting(false);
    }
  };

  // Update post
  const updatePost = async () => {
    if (!selectedPost) return;

    setFormSubmitting(true);
    setFormError(null);
    setFormSuccess(null);

    try {
      // Validate form
      if (!formData.title || !formData.content) {
        setFormError(t("admin.please.fill.in", "Please fill in all required fields"));
        setFormSubmitting(false);
        return;
      }

      // Create post data
      const postData = {
        title: formData.title,
        content: formData.content
      };

      // Call API to update post
      const updatedPost = await postsAPI.updatePost(selectedPost.id, postData);

      // Update local state
      setAllPosts(allPosts.map(post => post.id === updatedPost.id ? updatedPost : post));
      refreshPosts();

      // Show success message
      setFormSuccess(t("admin.post.updated.successfully", "Post updated successfully!"));

      // Close modal after a delay
      setTimeout(() => {
        setIsEditModalOpen(false);
        setSelectedPost(null);
        resetForm();
      }, 1500);
    } catch (error) {
      console.error('Error updating post:', error);
      setFormError(t("admin.failed.to.update", "Failed to update post. Please try again."));
    } finally {
      setFormSubmitting(false);
    }
  };

  // Confirm delete post
  const confirmDeletePost = async () => {
    if (selectedPost) {
      setFormSubmitting(true);
      try {
        // Call the API to delete the post
        await postsAPI.deletePost(selectedPost.id);

        // Update the local state
        setAllPosts(allPosts.filter(post => post.id !== selectedPost.id));
        refreshPosts();

        setIsDeleteModalOpen(false);
        setSelectedPost(null);
      } catch (error) {
        console.error('Error deleting post:', error);
        // Show error message to user
        setFormError(t("admin.failed.to.delete", "Failed to delete post. Please try again."));
      } finally {
        setFormSubmitting(false);
      }
    }
  };

  // Toggle expanded post
  const toggleExpandPost = (postId: number) => {
    setExpandedPost(expandedPost === postId ? null : postId);
  };

  // Handle filter change
  const handleFilterChange = (filters: Record<string, FilterValue>) => {
    setActiveFilters(filters);
    refreshPosts(); // Refresh posts when filters change
  };

  // Handle bulk selection
  const handleDeselectAll = () => {
    setSelectedItems([]);
  };

  const toggleItemSelection = (postId: number) => {
    if (selectedItems.includes(postId)) {
      setSelectedItems(selectedItems.filter(id => id !== postId));
    } else {
      setSelectedItems([...selectedItems, postId]);
    }
  };

  // Bulk action handlers
  const handleBulkApprove = async (ids: number[]): Promise<void> => {
    try {
      // Process posts in batches of 5 for better performance
      const batchSize = 5;
      const batches = [];

      for (let i = 0; i < ids.length; i += batchSize) {
        batches.push(ids.slice(i, i + batchSize));
      }

      // Process each batch sequentially
      for (const batch of batches) {
        await Promise.all(
          batch.map(id => postsAPI.moderatePost(id, 'approved', 'Bulk approved by admin'))
        );
      }

      // Refresh posts after bulk action
      refreshPosts();
      setSelectedItems([]);
      return Promise.resolve();
    } catch (error) {
      console.error('Error approving posts:', error);
      setFormError(t("admin.failed.to.approve", "Failed to approve selected posts"));
      return Promise.reject(error);
    }
  };

  const handleBulkReject = async (ids: number[]): Promise<void> => {
    try {
      // Process posts in batches of 5 for better performance
      const batchSize = 5;
      const batches = [];

      for (let i = 0; i < ids.length; i += batchSize) {
        batches.push(ids.slice(i, i + batchSize));
      }

      // Process each batch sequentially
      for (const batch of batches) {
        await Promise.all(
          batch.map(id => postsAPI.moderatePost(id, 'rejected', 'Bulk rejected by admin'))
        );
      }

      // Refresh posts after bulk action
      refreshPosts();
      setSelectedItems([]);
      return Promise.resolve();
    } catch (error) {
      console.error('Error rejecting posts:', error);
      setFormError(t("admin.failed.to.reject", "Failed to reject selected posts"));
      return Promise.reject(error);
    }
  };

  const handleBulkDelete = async (ids: number[]): Promise<void> => {
    try {
      // Process posts in batches of 5 for better performance
      const batchSize = 5;
      const batches = [];

      for (let i = 0; i < ids.length; i += batchSize) {
        batches.push(ids.slice(i, i + batchSize));
      }

      // Process each batch sequentially
      for (const batch of batches) {
        await Promise.all(
          batch.map(id => postsAPI.deletePost(id))
        );
      }

      // Update local state
      setAllPosts(allPosts.filter(post => !ids.includes(post.id)));
      refreshPosts();
      setSelectedItems([]);
      return Promise.resolve();
    } catch (error) {
      console.error('Error deleting posts:', error);
      setFormError(t("admin.failed.to.delete", "Failed to delete selected posts"));
      return Promise.reject(error);
    }
  };

  // Prevent default form submission
  const preventFormSubmission = (e: React.FormEvent) => {
    e.preventDefault();
    e.stopPropagation();
    return false;
  };

  // Filter options for the advanced filter
  const postFilterOptions = [
    { id: 'title', label: t("admin.title.value.title", "Title"), type: 'text' as const },
    { id: 'content', label: t("admin.content.value.content", "Content"), type: 'text' as const },
    { id: 'author', label: t("admin.author.value.author", "Author"), type: 'text' as const },
    {
      id: 'moderation_status',
      label: t("admin.status", "Status"),
      type: 'select' as const,
      options: [
        { label: t("admin.approved.value.approved", "Approved"), value: 'approved' },
        { label: t("admin.pending.value.pending", "Pending"), value: 'pending' },
        { label: t("admin.rejected.value.rejected", "Rejected"), value: 'rejected' }
      ]
    },
    { id: 'created_at', label: t("admin.created.date", "Created Date"), type: 'date' as const }
  ];

  // Load empty data as fallback
  const loadMockData = () => {
    setAllPosts([]);
    refreshPosts();
  };

  return (
    <DashboardLayout currentPage="posts">
      <div onSubmit={preventFormSubmission} className={`mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
        <div>
          <h1 className="text-2xl font-bold">{t("admin.posts.management", "Posts Management")}</h1>
          <div className="text-gray-400 mt-1">{t("admin.manage.community.posts", "Manage community posts and discussions")}</div>
        </div>
        <button
          type="button"
          onClick={openCreateModal}
          className={`mt-4 sm:mt-0 px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300 flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}
        >
          <Plus size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
          Create New Post
        </button>
      </div>

      {/* Advanced Filter Component */}
      <AdvancedFilter
        filterOptions={postFilterOptions}
        onFilterChange={handleFilterChange}
        activeFilters={activeFilters}
      />

      {/* Search and Bulk Actions */}
      <div className={`mb-6 flex flex-col sm:flex-row justify-between items-start gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className="relative w-full sm:w-64">
          <input
            type="text"
            placeholder={t("admin.search.posts", "Search posts...")}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 bg-indigo-900/30 border border-indigo-800 rounded-lg text-white focus:outline-none focus:border-purple-500"
          />
          <Search className="absolute left-3 top-2.5 text-gray-400" size={18} />
        </div>

        {selectedItems.length > 0 && (
          <div className="bg-indigo-900/30 rounded-lg p-4 mb-4">
            <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
              <span className="text-sm text-gray-300">
                {selectedItems.length} {t("admin.items.selected", "items selected")}
              </span>
              <div className={`flex gap-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                <button
                  onClick={() => handleBulkApprove(selectedItems)}
                  className="px-3 py-1.5 bg-green-600 hover:bg-green-700 rounded text-white text-sm"
                >
                  {t("admin.approve.selected", "Approve Selected")}
                </button>
                <button
                  onClick={() => handleBulkReject(selectedItems)}
                  className="px-3 py-1.5 bg-yellow-600 hover:bg-yellow-700 rounded text-white text-sm"
                >
                  {t("admin.reject.selected", "Reject Selected")}
                </button>
                <button
                  onClick={() => handleBulkDelete(selectedItems)}
                  className="px-3 py-1.5 bg-red-600 hover:bg-red-700 rounded text-white text-sm"
                >
                  {t("admin.delete.selected", "Delete Selected")}
                </button>
                <button
                  onClick={handleDeselectAll}
                  className="px-3 py-1.5 bg-gray-600 hover:bg-gray-700 rounded text-white text-sm"
                >
                  {t("admin.deselect.all", "Deselect All")}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="bg-indigo-900/20 rounded-xl overflow-hidden shadow-lg">
        {error && (
          <div className="p-4 bg-red-900/50 border border-red-800 text-white rounded-lg mb-4">
            <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <X className={`mr-2 text-red-400 ${isRTL ? "space-x-reverse" : ""}`} size={18} />
              {error}
            </div>
            <button
              onClick={loadMockData}
              className="mt-2 px-4 py-2 bg-indigo-700 hover:bg-indigo-600 rounded-lg text-white"
            >
              Show Sample Data Instead
            </button>
          </div>
        )}

        {loading && posts.length === 0 ? (
          <SkeletonList count={5} SkeletonComponent={PostSkeleton} />
        ) : (
          <div className="divide-y divide-indigo-800/30">
            {filteredPosts.length === 0 ? (
              <div className="text-center py-10">
                <div className="text-gray-400 text-lg">{t("admin.no.posts.found", "No posts found matching your search.")}</div>
              </div>
            ) : (
              <>
                {filteredPosts.map((post, index) => (
                  <div
                    key={post.id}
                    className="p-6 hover:bg-indigo-900/10"
                    ref={index === filteredPosts.length - 1 ? lastElementRef : undefined}
                  >
                    <div className={`flex justify-between items-start ${isRTL ? "flex-row-reverse" : ""}`}>
                      <div className={`flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <div className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                          <input
                            type="checkbox"
                            checked={selectedItems.includes(post.id)}
                            onChange={() => toggleItemSelection(post.id)}
                            className="w-4 h-4 rounded border-indigo-700 text-purple-600 focus:ring-purple-500"
                          />
                          <h3 className="text-lg font-semibold">{post.title}</h3>
                          <span className={`px-2 py-0.5 text-xs rounded-full ${
                            post.moderation_status === 'approved' ? 'bg-green-900/30 text-green-400' :
                            post.moderation_status === 'rejected' ? 'bg-red-900/30 text-red-400' :
                            'bg-yellow-900/30 text-yellow-400'}
                          }`}>
                            {post.moderation_status}
                          </span>
                        </div>
                        <div className="text-sm text-gray-300 mt-1">
                          By <span className="text-purple-400">{post.author.username}</span> • {formatDate(post.created_at)}
                        </div>
                        <div className="mt-3">
                          <div
                            className="text-gray-300 rich-text-content line-clamp-2"
                            dangerouslySetInnerHTML={{
                              __html: post.content.length > 300
                                ? `${post.content.substring(0, 300)}...`
                                : post.content
                            }}
                          />
                        </div>
                        <div className={`mt-4 flex items-center space-x-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                          <button
                            onClick={() => toggleExpandPost(post.id)}
                            className={`flex items-center text-sm text-gray-400 hover:text-purple-400 transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
                          >
                            <MessageSquare size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                            {post.comments?.length || 0} Comments
                          </button>
                          <div className={`flex items-center text-sm text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                            <Heart size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                            {post.like_count} Likes
                          </div>
                        </div>
                      </div>
                      <div className={`flex space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <button
                          onClick={() => handleEditPost(post)}
                          className="p-2 bg-indigo-800/30 hover:bg-indigo-700/30 rounded-lg transition-colors"
                        >
                          <Edit size={16} className="text-blue-400" />
                        </button>
                        <button
                          onClick={() => handleDeletePost(post)}
                          className="p-2 bg-indigo-800/30 hover:bg-indigo-700/30 rounded-lg transition-colors"
                        >
                          <Trash2 size={16} className="text-red-400" />
                        </button>
                      </div>
                    </div>

                    {/* Expanded comments section */}
                    {expandedPost === post.id && post.comments && post.comments.length > 0 && (
                      <div className="mt-4 pl-8 border-l-2 border-indigo-800/30">
                        <h4 className="text-sm font-semibold text-gray-300 mb-2">{t("admin.comments", "Comments")}</h4>
                        {post.comments.map(comment => (
                          <div key={comment.id} className="mb-3 last:mb-0">
                            <div className={`flex items-start space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                              <div className={`w-8 h-8 rounded-full bg-indigo-800/50 flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}>
                                <span className="text-xs font-medium">{comment.author.username.charAt(0).toUpperCase()}</span>
                              </div>
                              <div>
                                <div className="text-xs text-purple-400">{comment.author.username} • {formatDate(comment.created_at)}</div>
                                <p className="text-sm text-gray-300 mt-1">{comment.content}</p>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))}

                {/* Loading indicator at the bottom for infinite scroll */}
                {loading && posts.length > 0 && (
                  <div className={`flex justify-center py-6 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className="w-8 h-8 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
                  </div>
                )}

                {/* Load more button as fallback */}
                {hasMore && !loading && (
                  <div className={`flex justify-center py-6 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <button
                      onClick={loadMore}
                      className={`px-4 py-2 bg-indigo-700 hover:bg-indigo-600 rounded-lg text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                    >
                      <RefreshCw size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                      Load More
                    </button>
                  </div>
                )}
              </>
            )}
          </div>
        )}
      </div>

      {/* Create Post Modal */}
      {isCreateModalOpen && (
        <div className={`fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="bg-indigo-900/90 rounded-xl shadow-lg w-full max-w-md p-6 relative">
            <button
              onClick={() => setIsCreateModalOpen(false)}
              className="absolute top-4 right-4 text-gray-400 hover:text-white"
            >
              <X size={20} />
            </button>
            <h2 className="text-xl font-bold mb-6">{t("admin.create.new.post", "Create New Post")}</h2>
            <form onSubmit={preventFormSubmission}>
              <div className="mb-4">
                <label className="block text-gray-300 mb-2">{t("admin.title", t("common.title", "Title"))}</label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 bg-indigo-900/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                  placeholder={t("admin.enter.post.title", "Enter post title")}
                />
              </div>
              <div className="mb-6">
                <label className="block text-gray-300 mb-2">{t("admin.content", "Content")}</label>
                <RichTextEditor
                  value={formData.content}
                  onChange={(value) => handleInputChange(value, 'content')}
                  placeholder={t("admin.enter.post.content", "Enter post content")}
                  className="min-h-[200px]"
                />
              </div>
              {formError && (
                <div className="mb-4 p-3 bg-red-900/50 border border-red-800 text-white rounded-lg">
                  {formError}
                </div>
              )}
              {formSuccess && (
                <div className="mb-4 p-3 bg-green-900/50 border border-green-800 text-white rounded-lg">
                  {formSuccess}
                </div>
              )}
              <div className={`flex justify-end ${isRTL ? "flex-row-reverse" : ""}`}>
                <button
                  type="button"
                  onClick={() => setIsCreateModalOpen(false)}
                  className={`px-4 py-2 bg-indigo-800/50 hover:bg-indigo-700/50 rounded-lg mr-2 ${isRTL ? "space-x-reverse" : ""}`}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={createPost}
                  disabled={formSubmitting}
                  className={`px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                >
                  {formSubmitting ? (
                    <>
                      <div className={`w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 ${isRTL ? "space-x-reverse" : ""}`}></div>
                      Creating...
                    </>
                  ) : (
                    <>{t("admin.create.post", "Create Post")}</>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Edit Post Modal */}
      {isEditModalOpen && selectedPost && (
        <div className={`fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="bg-indigo-900/90 rounded-xl shadow-lg w-full max-w-md p-6 relative">
            <button
              onClick={() => setIsEditModalOpen(false)}
              className="absolute top-4 right-4 text-gray-400 hover:text-white"
            >
              <X size={20} />
            </button>
            <h2 className="text-xl font-bold mb-6">{t("admin.edit.post", "Edit Post")}</h2>
            <form onSubmit={preventFormSubmission}>
              <div className="mb-4">
                <label className="block text-gray-300 mb-2">{t("admin.title", t("common.title", "Title"))}</label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 bg-indigo-900/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                  placeholder={t("admin.enter.post.title", "Enter post title")}
                />
              </div>
              <div className="mb-6">
                <label className="block text-gray-300 mb-2">{t("admin.content", "Content")}</label>
                <RichTextEditor
                  value={formData.content}
                  onChange={(value) => handleInputChange(value, 'content')}
                  placeholder={t("admin.enter.post.content", "Enter post content")}
                  className="min-h-[200px]"
                />
              </div>
              {formError && (
                <div className="mb-4 p-3 bg-red-900/50 border border-red-800 text-white rounded-lg">
                  {formError}
                </div>
              )}
              {formSuccess && (
                <div className="mb-4 p-3 bg-green-900/50 border border-green-800 text-white rounded-lg">
                  {formSuccess}
                </div>
              )}
              <div className={`flex justify-end ${isRTL ? "flex-row-reverse" : ""}`}>
                <button
                  type="button"
                  onClick={() => setIsEditModalOpen(false)}
                  className={`px-4 py-2 bg-indigo-800/50 hover:bg-indigo-700/50 rounded-lg mr-2 ${isRTL ? "space-x-reverse" : ""}`}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={updatePost}
                  disabled={formSubmitting}
                  className={`px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                >
                  {formSubmitting ? (
                    <>
                      <div className={`w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 ${isRTL ? "space-x-reverse" : ""}`}></div>
                      Updating...
                    </>
                  ) : (
                    <>{t("admin.update.post", "Update Post")}</>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {isDeleteModalOpen && selectedPost && (
        <div className={`fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="bg-indigo-900/90 rounded-xl shadow-lg w-full max-w-md p-6">
            <h2 className="text-xl font-bold mb-4">{t("admin.confirm.delete", "Confirm Delete")}</h2>
            <div className="text-gray-300 mb-6">
              Are you sure you want to delete the post "{selectedPost.title}"? This action cannot be undone.
            </div>
            {formError && (
              <div className="mb-4 p-3 bg-red-900/50 border border-red-800 text-white rounded-lg">
                {formError}
              </div>
            )}
            <div className={`flex justify-end ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                type="button"
                onClick={() => setIsDeleteModalOpen(false)}
                className={`px-4 py-2 bg-indigo-800/50 hover:bg-indigo-700/50 rounded-lg mr-2 ${isRTL ? "space-x-reverse" : ""}`}
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={confirmDeletePost}
                disabled={formSubmitting}
                className={`px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg font-medium flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
              >
                {formSubmitting ? (
                  <>
                    <div className={`w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 ${isRTL ? "space-x-reverse" : ""}`}></div>
                    Deleting...
                  </>
                ) : (
                  <>
                    <Trash2 size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                    Delete
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </DashboardLayout>
  );
};

export default PostsManagementInfinite;
