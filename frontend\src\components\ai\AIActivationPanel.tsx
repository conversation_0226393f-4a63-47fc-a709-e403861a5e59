/**
 * AI Activation Panel - Quick start/stop controls for AI services
 * Provides easy access to activate AI workers and services
 */

import React, { useState } from 'react';
import {
  Play,
  Pause,
  RefreshCw,
  Settings,
  AlertTriangle,
  CheckCircle,
  Zap,
  Activity,
  Power,
  Loader
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { automaticAiApi } // Removed automaticAiApi import;

interface AIActivationPanelProps {
  isAIRunning: boolean;
  onStatusChange?: (isRunning: boolean) => void;
  className?: string;
}

export const AIActivationPanel: React.FC<AIActivationPanelProps> = ({
  isAIRunning,
  onStatusChange,
  className = ''
}) => {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleStartAI = async () => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await // automaticAiApi.startAutomaticAI();
      setSuccess('AI services started successfully!');
      if (onStatusChange) {
        onStatusChange(true);
      }

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(null), 3000);
    } catch (error: any) {
      console.error('Error starting AI:', error);

      if (error?.status === 401) {
        setError('Authentication required. Please log in to start AI services.');
      } else if (error?.status === 403) {
        setError('Permission denied. You may not have access to AI management.');
      } else if (error?.status === 500) {
        setError('Server error. Please check if the backend is running.');
      } else {
        setError(`Failed to start AI services: ${error?.message || 'Unknown error'}`);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleStopAI = async () => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await // automaticAiApi.stopAutomaticAI();
      setSuccess('AI services stopped successfully!');
      if (onStatusChange) {
        onStatusChange(false);
      }
      
      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error('Error stopping AI:', error);
      setError('Failed to stop AI services. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTriggerAnalysis = async () => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await // automaticAiApi.triggerManualAnalysis();
      setSuccess('Manual AI analysis triggered successfully!');
      
      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error('Error triggering analysis:', error);
      setError('Failed to trigger AI analysis. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <div className={`p-2 rounded-lg mr-3 ${
            isAIRunning ? 'bg-green-100' : 'bg-red-100'
          }`}>
            <Power className={`w-5 h-5 ${
              isAIRunning ? 'text-green-600' : 'text-red-600'
            }`} />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">AI Control Panel</h3>
            <p className="text-sm text-gray-600">
              Manage AI workers and services
            </p>
          </div>
        </div>
        
        <div className={`px-3 py-1 rounded-full text-sm font-medium ${
          isAIRunning 
            ? 'bg-green-100 text-green-800' 
            : 'bg-red-100 text-red-800'
        }`}>
          {isAIRunning ? 'Online' : 'Offline'}
        </div>
      </div>

      {/* Status Messages */}
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center">
            <AlertTriangle className="w-4 h-4 text-red-600 mr-2" />
            <p className="text-sm text-red-800">{error}</p>
          </div>
        </div>
      )}

      {success && (
        <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center">
            <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
            <p className="text-sm text-green-800">{success}</p>
          </div>
        </div>
      )}

      {/* Control Buttons */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Start/Stop AI */}
        <button
          onClick={isAIRunning ? handleStopAI : handleStartAI}
          disabled={isLoading}
          className={`flex items-center justify-center px-4 py-3 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${
            isAIRunning
              ? 'bg-red-600 hover:bg-red-700 text-white'
              : 'bg-green-600 hover:bg-green-700 text-white'
          }`}
        >
          {isLoading ? (
            <Loader className="w-5 h-5 mr-2 animate-spin" />
          ) : isAIRunning ? (
            <Pause className="w-5 h-5 mr-2" />
          ) : (
            <Play className="w-5 h-5 mr-2" />
          )}
          {isLoading ? 'Processing...' : isAIRunning ? 'Stop AI' : 'Start AI'}
        </button>

        {/* Trigger Manual Analysis */}
        <button
          onClick={handleTriggerAnalysis}
          disabled={isLoading || !isAIRunning}
          className="flex items-center justify-center px-4 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? (
            <Loader className="w-5 h-5 mr-2 animate-spin" />
          ) : (
            <Zap className="w-5 h-5 mr-2" />
          )}
          Trigger Analysis
        </button>

        {/* Refresh Status */}
        <button
          onClick={() => window.location.reload()}
          disabled={isLoading}
          className="flex items-center justify-center px-4 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <RefreshCw className="w-5 h-5 mr-2" />
          Refresh
        </button>
      </div>

      {/* Quick Info */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h4 className="text-sm font-medium text-gray-900 mb-2">Quick Info</h4>
        <div className="space-y-2 text-sm text-gray-600">
          <div className="flex items-center justify-between">
            <span>AI Workers:</span>
            <span className="font-medium">
              {isAIRunning ? 'Active' : 'Inactive'}
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span>Auto Analysis:</span>
            <span className="font-medium">
              {isAIRunning ? 'Enabled' : 'Disabled'}
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span>Real-time Monitoring:</span>
            <span className="font-medium">
              {isAIRunning ? 'Active' : 'Paused'}
            </span>
          </div>
        </div>
      </div>

      {/* Help Text */}
      <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-start">
          <Activity className="w-4 h-4 text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
          <div className="text-sm text-blue-800">
            <p className="font-medium mb-1">How it works:</p>
            <ul className="space-y-1 text-xs">
              <li>• <strong>Start AI:</strong> Activates all AI workers and automatic analysis</li>
              <li>• <strong>Trigger Analysis:</strong> Runs immediate AI analysis on your business ideas</li>
              <li>• <strong>Refresh:</strong> Updates the dashboard with latest data</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};
