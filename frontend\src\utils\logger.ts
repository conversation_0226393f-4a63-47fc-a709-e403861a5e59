/**
 * Production-safe logger utility
 * Only logs in development environment
 */

type LogLevel = 'log' | 'warn' | 'error' | 'info';

class Logger {
  private isDev = import.meta.env.DEV;

  log(...args: any[]) {
    if (this.isDev) {
      console.log(...args);
    }
  }

  warn(...args: any[]) {
    if (this.isDev) {
      console.warn(...args);
    }
  }

  error(...args: any[]) {
    // Always log errors, even in production
    console.error(...args);
  }

  info(...args: any[]) {
    if (this.isDev) {
      console.info(...args);
    }
  }

  debug(...args: any[]) {
    if (this.isDev) {
      console.debug(...args);
    }
  }

  group(label?: string) {
    if (this.isDev && console.group) {
      console.group(label);
    }
  }

  groupEnd() {
    if (this.isDev && console.groupEnd) {
      console.groupEnd();
    }
  }
}

export const logger = new Logger();
export default logger;
