from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.auth.models import User
from .models import UserProfile, UserRole, UserRoleAssignment, RoleApplication

class UserProfileInline(admin.StackedInline):
    model = UserProfile
    can_delete = False
    verbose_name_plural = 'profile'

class UserAdmin(BaseUserAdmin):
    inlines = (UserProfileInline,)

# Re-register UserAdmin
admin.site.unregister(User)
admin.site.register(User, UserAdmin)


@admin.register(UserRole)
class UserRoleAdmin(admin.ModelAdmin):
    list_display = ['name', 'display_name', 'permission_level', 'is_active', 'requires_approval']
    list_filter = ['permission_level', 'is_active', 'requires_approval']
    search_fields = ['name', 'display_name', 'description']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(UserRoleAssignment)
class UserRoleAssignmentAdmin(admin.ModelAdmin):
    list_display = ['user_profile', 'role', 'is_active', 'is_approved', 'assigned_at', 'expires_at']
    list_filter = ['role', 'is_active', 'is_approved', 'assigned_at']
    search_fields = ['user_profile__user__username', 'role__display_name']
    readonly_fields = ['assigned_at']
    raw_id_fields = ['user_profile', 'assigned_by', 'approved_by']


@admin.register(RoleApplication)
class RoleApplicationAdmin(admin.ModelAdmin):
    list_display = ['user', 'requested_role', 'status', 'created_at', 'reviewed_at']
    list_filter = ['status', 'requested_role', 'created_at']
    search_fields = ['user__username', 'requested_role__display_name']
    readonly_fields = ['created_at', 'updated_at']
    raw_id_fields = ['user', 'reviewed_by']

    fieldsets = (
        ('Application Info', {
            'fields': ('user', 'requested_role', 'status')
        }),
        ('Application Details', {
            'fields': ('motivation', 'qualifications', 'experience', 'portfolio_url')
        }),
        ('Review', {
            'fields': ('reviewed_by', 'reviewed_at', 'admin_notes')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
