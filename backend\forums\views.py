from rest_framework import viewsets, permissions, status, filters, serializers
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from django.utils import timezone
from django.db import models, transaction
from django_filters.rest_framework import DjangoFilterBackend
from django.contrib.auth.models import User
from .search import search_forums
from .notifications import (
    notify_on_thread_moderation, notify_on_post_moderation,
    notify_on_solution_marked
)
from .content_filter import analyze_content, should_auto_reject, ContentFlag
from .analytics import (
    get_forum_overview_stats, get_activity_over_time,
    get_top_contributors, get_popular_threads, get_user_engagement_stats
)

from .models import (
    ForumCategory, ForumTopic, ForumThread, ForumPost,
    UserReputation, ReputationActivity, TopicSubscription, ThreadSubscription,
    ForumAttachment
)
from .serializers import (
    ForumCategorySerializer, ForumTopicSerializer, ForumThreadSerializer,
    ForumPostSerializer, UserReputationSerializer, ReputationActivitySerializer,
    TopicSubscriptionSerializer, ThreadSubscriptionSerializer, ForumAttachmentSerializer
)
from api.permissions import IsAdminUser

class ForumCategoryViewSet(viewsets.ModelViewSet):
    queryset = ForumCategory.objects.all().order_by('order', 'name')
    serializer_class = ForumCategorySerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['is_active']
    search_fields = ['name', 'description']
    ordering_fields = ['order', 'name', 'created_at']
    lookup_field = 'slug'

    def get_permissions(self):
        if self.action in ['list', 'retrieve']:
            # Allow anyone to view forum categories (public access)
            permission_classes = [permissions.AllowAny]
        else:
            # Only admin users can create, update, or delete categories
            permission_classes = [IsAdminUser]
        return [permission() for permission in permission_classes]

class ForumTopicViewSet(viewsets.ModelViewSet):
    queryset = ForumTopic.objects.all().order_by('-is_pinned', '-updated_at')
    serializer_class = ForumTopicSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['category', 'is_active', 'is_pinned', 'is_locked']
    search_fields = ['title', 'description']
    ordering_fields = ['title', 'created_at', 'updated_at']
    lookup_field = 'slug'

    def get_permissions(self):
        if self.action in ['list', 'retrieve']:
            # Allow anyone to view forum topics (public access)
            permission_classes = [permissions.AllowAny]
        elif self.action in ['create']:
            # Any authenticated user can create a topic
            permission_classes = [permissions.IsAuthenticated]
        else:
            # Only admin users can update or delete topics
            permission_classes = [IsAdminUser]
        return [permission() for permission in permission_classes]

    def perform_create(self, serializer):
        serializer.save(
            created_by_id=self.request.user.id
        )

    @action(detail=True, methods=['post'])
    def pin(self, request, slug=None):
        """Pin or unpin a topic"""
        topic = self.get_object()
        topic.is_pinned = not topic.is_pinned
        topic.save()
        return Response({
            'message': f"Topic {'pinned' if topic.is_pinned else 'unpinned'} successfully",
            'is_pinned': topic.is_pinned
        })

    @action(detail=True, methods=['post'])
    def lock(self, request, slug=None):
        """Lock or unlock a topic"""
        topic = self.get_object()
        topic.is_locked = not topic.is_locked
        topic.save()
        return Response({
            'message': f"Topic {'locked' if topic.is_locked else 'unlocked'} successfully",
            'is_locked': topic.is_locked
        })

class ForumThreadViewSet(viewsets.ModelViewSet):
    queryset = ForumThread.objects.all().order_by('-is_pinned', '-last_activity')
    serializer_class = ForumThreadSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['topic', 'author', 'is_pinned', 'is_locked', 'moderation_status']
    search_fields = ['title', 'content']
    ordering_fields = ['title', 'created_at', 'last_activity', 'views']

    @action(detail=False, methods=['post'])
    def bulk_moderate(self, request):
        """Bulk moderate threads"""
        if not (request.user.is_staff or request.user.is_superuser):
            return Response(
                {"error": "You do not have permission to moderate threads"},
                status=status.HTTP_403_FORBIDDEN
            )

        thread_ids = request.data.get('thread_ids', [])
        status_value = request.data.get('status')
        comment = request.data.get('comment', '')

        if not thread_ids:
            return Response(
                {"error": "No thread IDs provided"},
                status=status.HTTP_400_BAD_REQUEST
            )

        if status_value not in ['approved', 'rejected']:
            return Response(
                {"error": "Invalid moderation status"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get threads to moderate
        threads = ForumThread.objects.filter(id__in=thread_ids)

        if not threads.exists():
            return Response(
                {"error": "No threads found with the provided IDs"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Moderate threads
        with transaction.atomic():
            for thread in threads:
                thread.moderation_status = status_value
                thread.moderation_comment = comment
                thread.moderated_by = request.user
                thread.moderated_at = timezone.now()
                thread.save()

                # Send notification to thread author
                notify_on_thread_moderation(thread, status_value, request.user)

        return Response({
            "message": f"{threads.count()} threads have been {status_value}",
            "thread_count": threads.count()
        })

    def get_queryset(self):
        # For anonymous users, only show approved threads
        # For authenticated users, show approved threads or their own threads
        if not self.request.user.is_authenticated:
            return ForumThread.objects.filter(
                moderation_status='approved'
            ).order_by('-is_pinned', '-last_activity')
        elif not (self.request.user.is_staff or self.request.user.is_superuser):
            return ForumThread.objects.filter(
                models.Q(moderation_status='approved') |
                models.Q(author=self.request.user)
            ).order_by('-is_pinned', '-last_activity')
        return super().get_queryset()

    def get_permissions(self):
        if self.action in ['list', 'retrieve']:
            # Allow anyone to view forum threads (public access)
            permission_classes = [permissions.AllowAny]
        elif self.action in ['destroy', 'update', 'partial_update']:
            # Only admin users can delete or update threads
            permission_classes = [IsAdminUser]
        else:
            permission_classes = [permissions.IsAuthenticated]
        return [permission() for permission in permission_classes]

    def perform_create(self, serializer):
        with transaction.atomic():
            # If user is admin, auto-approve the thread
            if self.request.user.is_staff or self.request.user.is_superuser:
                thread = serializer.save(
                    author_id=self.request.user.id,
                    moderation_status='approved',
                    moderated_by=self.request.user,
                    moderated_at=timezone.now()
                )
            else:
                thread = serializer.save(author_id=self.request.user.id)

                # Analyze content for inappropriate material
                is_flagged, flags = analyze_content(
                    content=thread.content,
                    content_type='thread',
                    content_id=thread.id
                )

                # Check if thread should be auto-rejected
                if is_flagged and should_auto_reject(flags):
                    thread.moderation_status = 'rejected'
                    thread.moderation_comment = "Automatically rejected due to inappropriate content"
                    thread.moderated_by = None
                    thread.moderated_at = timezone.now()
                    thread.save()

                    # Notify the author
                    notify_on_thread_moderation(thread, 'rejected', None)

            # Update user reputation
            self._update_reputation_for_thread_creation(thread)

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        # Increment view count
        instance.views += 1
        instance.save(update_fields=['views'])
        return super().retrieve(request, *args, **kwargs)

    @action(detail=True, methods=['post'])
    def moderate(self, request, pk=None):
        """Moderate a thread"""
        if not (request.user.is_staff or request.user.is_superuser):
            return Response(
                {"error": "You do not have permission to moderate threads"},
                status=status.HTTP_403_FORBIDDEN
            )

        thread = self.get_object()
        status_value = request.data.get('status')
        comment = request.data.get('comment', '')

        if status_value not in ['approved', 'rejected']:
            return Response(
                {"error": "Invalid moderation status"},
                status=status.HTTP_400_BAD_REQUEST
            )

        thread.moderation_status = status_value
        thread.moderation_comment = comment
        thread.moderated_by = request.user
        thread.moderated_at = timezone.now()
        thread.save()

        # Send notification to thread author
        notify_on_thread_moderation(thread, status_value, request.user)

        return Response({
            "message": f"Thread has been {status_value}",
            "thread_id": thread.id,
            "thread_title": thread.title,
            "moderation_status": thread.moderation_status
        })

    @action(detail=True, methods=['post'])
    def pin(self, request, pk=None):
        """Pin or unpin a thread"""
        if not (request.user.is_staff or request.user.is_superuser):
            return Response(
                {"error": "You do not have permission to pin threads"},
                status=status.HTTP_403_FORBIDDEN
            )

        thread = self.get_object()
        thread.is_pinned = not thread.is_pinned
        thread.save()
        return Response({
            'message': f"Thread {'pinned' if thread.is_pinned else 'unpinned'} successfully",
            'is_pinned': thread.is_pinned
        })

    @action(detail=True, methods=['post'])
    def lock(self, request, pk=None):
        """Lock or unlock a thread"""
        if not (request.user.is_staff or request.user.is_superuser):
            return Response(
                {"error": "You do not have permission to lock threads"},
                status=status.HTTP_403_FORBIDDEN
            )

        thread = self.get_object()
        thread.is_locked = not thread.is_locked
        thread.save()
        return Response({
            'message': f"Thread {'locked' if thread.is_locked else 'unlocked'} successfully",
            'is_locked': thread.is_locked
        })

    def _update_reputation_for_thread_creation(self, thread):
        """Update user reputation when a thread is created"""
        try:
            with transaction.atomic():
                # Get or create user reputation
                reputation, created = UserReputation.objects.get_or_create(user=thread.author)

                # Update reputation stats
                reputation.threads_created += 1
                reputation.points += 5  # Award 5 points for creating a thread
                reputation.save()

                # Update level if needed
                reputation.update_level()

                # Record the activity
                ReputationActivity.objects.create(
                    user=thread.author,
                    activity_type='thread_created',
                    points=5,
                    thread=thread,
                    description=f"Created thread: {thread.title}"
                )
        except Exception as e:
            print(f"Error updating reputation: {e}")

class ForumPostViewSet(viewsets.ModelViewSet):
    queryset = ForumPost.objects.all().order_by('created_at')
    serializer_class = ForumPostSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['thread', 'author', 'is_solution', 'moderation_status']
    search_fields = ['content']
    ordering_fields = ['created_at']

    @action(detail=False, methods=['post'])
    def bulk_moderate(self, request):
        """Bulk moderate posts"""
        if not (request.user.is_staff or request.user.is_superuser):
            return Response(
                {"error": "You do not have permission to moderate posts"},
                status=status.HTTP_403_FORBIDDEN
            )

        post_ids = request.data.get('post_ids', [])
        status_value = request.data.get('status')
        comment = request.data.get('comment', '')

        if not post_ids:
            return Response(
                {"error": "No post IDs provided"},
                status=status.HTTP_400_BAD_REQUEST
            )

        if status_value not in ['approved', 'rejected']:
            return Response(
                {"error": "Invalid moderation status"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get posts to moderate
        posts = ForumPost.objects.filter(id__in=post_ids)

        if not posts.exists():
            return Response(
                {"error": "No posts found with the provided IDs"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Moderate posts
        with transaction.atomic():
            for post in posts:
                post.moderation_status = status_value
                post.moderation_comment = comment
                post.moderated_by = request.user
                post.moderated_at = timezone.now()
                post.save()

                # Send notification to post author
                notify_on_post_moderation(post, status_value, request.user)

        return Response({
            "message": f"{posts.count()} posts have been {status_value}",
            "post_count": posts.count()
        })

    def get_queryset(self):
        # For anonymous users, only show approved posts
        # For authenticated users, show approved posts or their own posts
        if not self.request.user.is_authenticated:
            return ForumPost.objects.filter(
                moderation_status='approved'
            ).order_by('created_at')
        elif not (self.request.user.is_staff or self.request.user.is_superuser):
            return ForumPost.objects.filter(
                models.Q(moderation_status='approved') |
                models.Q(author=self.request.user)
            ).order_by('created_at')
        return super().get_queryset()

    def get_permissions(self):
        if self.action in ['list', 'retrieve']:
            # Allow anyone to view forum posts (public access)
            permission_classes = [permissions.AllowAny]
        elif self.action in ['destroy', 'update', 'partial_update']:
            # Only admin users can delete or update posts
            permission_classes = [IsAdminUser]
        else:
            permission_classes = [permissions.IsAuthenticated]
        return [permission() for permission in permission_classes]

    def perform_create(self, serializer):
        # Check if the thread is locked
        thread_id = self.request.data.get('thread')
        thread = ForumThread.objects.get(id=thread_id)

        if thread.is_locked:
            raise serializers.ValidationError("This thread is locked. You cannot add new posts.")

        with transaction.atomic():
            # If user is admin, auto-approve the post
            if self.request.user.is_staff or self.request.user.is_superuser:
                post = serializer.save(
                    author_id=self.request.user.id,
                    moderation_status='approved',
                    moderated_by=self.request.user,
                    moderated_at=timezone.now()
                )
            else:
                post = serializer.save(author_id=self.request.user.id)

                # Analyze content for inappropriate material
                is_flagged, flags = analyze_content(
                    content=post.content,
                    content_type='post',
                    content_id=post.id
                )

                # Check if post should be auto-rejected
                if is_flagged and should_auto_reject(flags):
                    post.moderation_status = 'rejected'
                    post.moderation_comment = "Automatically rejected due to inappropriate content"
                    post.moderated_by = None
                    post.moderated_at = timezone.now()
                    post.save()

                    # Notify the author
                    notify_on_post_moderation(post, 'rejected', None)

            # Update user reputation
            self._update_reputation_for_post_creation(post)

    @action(detail=True, methods=['post'])
    def like(self, request, pk=None):
        """Like a post"""
        post = self.get_object()
        user = request.user

        if user in post.likes.all():
            return Response(
                {"error": "You have already liked this post"},
                status=status.HTTP_400_BAD_REQUEST
            )

        with transaction.atomic():
            post.likes.add(user)

            # Update reputation for post author
            self._update_reputation_for_like_received(post)

            # Update reputation for user giving the like
            self._update_reputation_for_like_given(user, post)

        return Response({"message": "Post liked successfully"})

    @action(detail=True, methods=['post'])
    def unlike(self, request, pk=None):
        """Unlike a post"""
        post = self.get_object()
        user = request.user

        if user not in post.likes.all():
            return Response(
                {"error": "You have not liked this post"},
                status=status.HTTP_400_BAD_REQUEST
            )

        with transaction.atomic():
            post.likes.remove(user)

            # Reduce reputation for post author
            self._update_reputation_for_unlike(post)

        return Response({"message": "Post unliked successfully"})

    @action(detail=True, methods=['post'])
    def mark_as_solution(self, request, pk=None):
        """Mark a post as the solution to its thread"""
        post = self.get_object()
        thread = post.thread

        # Only thread author or admin can mark a solution
        if not (request.user == thread.author or request.user.is_staff or request.user.is_superuser):
            return Response(
                {"error": "Only the thread author or an admin can mark a solution"},
                status=status.HTTP_403_FORBIDDEN
            )

        with transaction.atomic():
            # Unmark any existing solutions in this thread
            ForumPost.objects.filter(thread=thread, is_solution=True).update(is_solution=False)

            # Mark this post as the solution
            post.is_solution = True
            post.save()

            # Update reputation for solution provider
            self._update_reputation_for_solution(post)

            # Send notification to post author
            notify_on_solution_marked(post)

        return Response({
            "message": "Post marked as solution",
            "post_id": post.id
        })

    @action(detail=True, methods=['post'])
    def moderate(self, request, pk=None):
        """Moderate a post"""
        if not (request.user.is_staff or request.user.is_superuser):
            return Response(
                {"error": "You do not have permission to moderate posts"},
                status=status.HTTP_403_FORBIDDEN
            )

        post = self.get_object()
        status_value = request.data.get('status')
        comment = request.data.get('comment', '')

        if status_value not in ['approved', 'rejected']:
            return Response(
                {"error": "Invalid moderation status"},
                status=status.HTTP_400_BAD_REQUEST
            )

        post.moderation_status = status_value
        post.moderation_comment = comment
        post.moderated_by = request.user
        post.moderated_at = timezone.now()
        post.save()

        # Send notification to post author
        notify_on_post_moderation(post, status_value, request.user)

        return Response({
            "message": f"Post has been {status_value}",
            "post_id": post.id,
            "moderation_status": post.moderation_status
        })

    def _update_reputation_for_post_creation(self, post):
        """Update user reputation when a post is created"""
        try:
            with transaction.atomic():
                # Get or create user reputation
                reputation, created = UserReputation.objects.get_or_create(user=post.author)

                # Update reputation stats
                reputation.posts_created += 1
                reputation.points += 2  # Award 2 points for creating a post
                reputation.save()

                # Update level if needed
                reputation.update_level()

                # Record the activity
                ReputationActivity.objects.create(
                    user=post.author,
                    activity_type='post_created',
                    points=2,
                    post=post,
                    thread=post.thread,
                    description=f"Created post in thread: {post.thread.title}"
                )
        except Exception as e:
            print(f"Error updating reputation: {e}")

    def _update_reputation_for_like_received(self, post):
        """Update reputation when a post receives a like"""
        try:
            with transaction.atomic():
                # Get or create user reputation
                reputation, created = UserReputation.objects.get_or_create(user=post.author)

                # Update reputation stats
                reputation.likes_received += 1
                reputation.points += 1  # Award 1 point for receiving a like
                reputation.save()

                # Update level if needed
                reputation.update_level()

                # Record the activity
                ReputationActivity.objects.create(
                    user=post.author,
                    activity_type='like_received',
                    points=1,
                    post=post,
                    thread=post.thread,
                    description=f"Received like on post in thread: {post.thread.title}"
                )
        except Exception as e:
            print(f"Error updating reputation: {e}")

    def _update_reputation_for_like_given(self, user, post):
        """Update reputation when a user gives a like"""
        try:
            # Record the activity only (no points awarded for giving likes)
            ReputationActivity.objects.create(
                user=user,
                activity_type='like_given',
                points=0,
                post=post,
                thread=post.thread,
                description=f"Gave like to post by {post.author.username} in thread: {post.thread.title}"
            )
        except Exception as e:
            print(f"Error updating reputation: {e}")

    def _update_reputation_for_unlike(self, post):
        """Update reputation when a post is unliked"""
        try:
            with transaction.atomic():
                # Get user reputation
                reputation = UserReputation.objects.get(user=post.author)

                # Update reputation stats
                reputation.likes_received -= 1
                reputation.points -= 1  # Remove 1 point for losing a like
                reputation.save()

                # Update level if needed
                reputation.update_level()

                # We don't record an activity for unlikes
        except Exception as e:
            print(f"Error updating reputation: {e}")

    def _update_reputation_for_solution(self, post):
        """Update reputation when a post is marked as a solution"""
        try:
            with transaction.atomic():
                # Get or create user reputation
                reputation, created = UserReputation.objects.get_or_create(user=post.author)

                # Update reputation stats
                reputation.solutions_provided += 1
                reputation.points += 15  # Award 15 points for providing a solution
                reputation.save()

                # Update level if needed
                reputation.update_level()

                # Record the activity
                ReputationActivity.objects.create(
                    user=post.author,
                    activity_type='solution_provided',
                    points=15,
                    post=post,
                    thread=post.thread,
                    description=f"Provided solution in thread: {post.thread.title}"
                )
        except Exception as e:
            print(f"Error updating reputation: {e}")

class UserReputationViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = UserReputation.objects.all().order_by('-points')
    serializer_class = UserReputationSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['level']
    search_fields = ['user__username', 'user__first_name', 'user__last_name']
    ordering_fields = ['points', 'threads_created', 'posts_created', 'solutions_provided', 'likes_received']

    def get_permissions(self):
        if self.action in ['list', 'retrieve', 'leaderboard']:
            # Allow anyone to view reputation data and leaderboard (public access)
            permission_classes = [permissions.AllowAny]
        else:
            # Require authentication for other actions
            permission_classes = [permissions.IsAuthenticated]
        return [permission() for permission in permission_classes]

    @action(detail=False, methods=['get'])
    def leaderboard(self, request):
        """Get the reputation leaderboard"""
        top_users = self.get_queryset()[:10]  # Get top 10 users
        serializer = self.get_serializer(top_users, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def my_reputation(self, request):
        """Get the current user's reputation"""
        try:
            reputation = UserReputation.objects.get(user=request.user)
            serializer = self.get_serializer(reputation)
            return Response(serializer.data)
        except UserReputation.DoesNotExist:
            # Create a new reputation record if it doesn't exist
            reputation = UserReputation.objects.create(user=request.user)
            serializer = self.get_serializer(reputation)
            return Response(serializer.data)

class ReputationActivityViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = ReputationActivity.objects.all().order_by('-created_at')
    serializer_class = ReputationActivitySerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['user', 'activity_type']
    search_fields = ['description']
    ordering_fields = ['created_at', 'points']
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        # Users can only see their own reputation activities unless they're admin
        if not (self.request.user.is_staff or self.request.user.is_superuser):
            return ReputationActivity.objects.filter(user=self.request.user).order_by('-created_at')
        return super().get_queryset()

    @action(detail=False, methods=['get'])
    def my_activities(self, request):
        """Get the current user's reputation activities"""
        activities = ReputationActivity.objects.filter(user=request.user).order_by('-created_at')
        page = self.paginate_queryset(activities)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(activities, many=True)
        return Response(serializer.data)


class ContentFlagViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing content flags
    """
    queryset = ContentFlag.objects.all().order_by('-created_at')
    serializer_class = None  # We'll define this later
    permission_classes = [IsAdminUser]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['content_type', 'reason', 'severity', 'auto_flagged', 'reviewed']
    search_fields = ['details']
    ordering_fields = ['created_at', 'severity']

    def get_serializer_class(self):
        from .serializers import ContentFlagSerializer
        return ContentFlagSerializer

    @action(detail=True, methods=['post'])
    def review(self, request, pk=None):
        """Mark a flag as reviewed"""
        flag = self.get_object()
        flag.mark_reviewed(request.user)

        # Get the serializer class
        serializer_class = self.get_serializer_class()

        return Response({
            "message": "Flag marked as reviewed",
            "flag": serializer_class(flag, context={'request': request}).data
        })

    @action(detail=True, methods=['post'])
    def approve_content(self, request, pk=None):
        """Approve the flagged content"""
        flag = self.get_object()

        # Mark flag as reviewed
        flag.mark_reviewed(request.user)

        # Approve the content based on content type
        if flag.content_type == 'thread':
            try:
                thread = ForumThread.objects.get(id=flag.content_id)
                thread.moderation_status = 'approved'
                thread.moderated_by = request.user
                thread.moderated_at = timezone.now()
                thread.save()

                # Notify the author
                notify_on_thread_moderation(thread, 'approved', request.user)
            except ForumThread.DoesNotExist:
                return Response(
                    {"error": "Thread not found"},
                    status=status.HTTP_404_NOT_FOUND
                )
        elif flag.content_type == 'post':
            try:
                post = ForumPost.objects.get(id=flag.content_id)
                post.moderation_status = 'approved'
                post.moderated_by = request.user
                post.moderated_at = timezone.now()
                post.save()

                # Notify the author
                notify_on_post_moderation(post, 'approved', request.user)
            except ForumPost.DoesNotExist:
                return Response(
                    {"error": "Post not found"},
                    status=status.HTTP_404_NOT_FOUND
                )

        # Get the serializer class
        serializer_class = self.get_serializer_class()

        return Response({
            "message": f"{flag.content_type.capitalize()} has been approved",
            "flag": serializer_class(flag, context={'request': request}).data
        })

    @action(detail=True, methods=['post'])
    def reject_content(self, request, pk=None):
        """Reject the flagged content"""
        flag = self.get_object()
        comment = request.data.get('comment', f"Rejected due to {flag.reason}")

        # Mark flag as reviewed
        flag.mark_reviewed(request.user)

        # Reject the content based on content type
        if flag.content_type == 'thread':
            try:
                thread = ForumThread.objects.get(id=flag.content_id)
                thread.moderation_status = 'rejected'
                thread.moderation_comment = comment
                thread.moderated_by = request.user
                thread.moderated_at = timezone.now()
                thread.save()

                # Notify the author
                notify_on_thread_moderation(thread, 'rejected', request.user)
            except ForumThread.DoesNotExist:
                return Response(
                    {"error": "Thread not found"},
                    status=status.HTTP_404_NOT_FOUND
                )
        elif flag.content_type == 'post':
            try:
                post = ForumPost.objects.get(id=flag.content_id)
                post.moderation_status = 'rejected'
                post.moderation_comment = comment
                post.moderated_by = request.user
                post.moderated_at = timezone.now()
                post.save()

                # Notify the author
                notify_on_post_moderation(post, 'rejected', request.user)
            except ForumPost.DoesNotExist:
                return Response(
                    {"error": "Post not found"},
                    status=status.HTTP_404_NOT_FOUND
                )

        # Get the serializer class
        serializer_class = self.get_serializer_class()

        return Response({
            "message": f"{flag.content_type.capitalize()} has been rejected",
            "flag": serializer_class(flag, context={'request': request}).data
        })


class TopicSubscriptionViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing topic subscriptions
    """
    serializer_class = TopicSubscriptionSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return TopicSubscription.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

    @action(detail=False, methods=['post'])
    def toggle(self, request):
        """Toggle subscription for a topic"""
        topic_id = request.data.get('topic_id')
        if not topic_id:
            return Response(
                {"error": "Topic ID is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            topic = ForumTopic.objects.get(id=topic_id)
        except ForumTopic.DoesNotExist:
            return Response(
                {"error": "Topic not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Check if subscription exists
        subscription = TopicSubscription.objects.filter(
            user=request.user,
            topic=topic
        ).first()

        if subscription:
            # Unsubscribe
            subscription.delete()
            return Response({
                "message": f"Unsubscribed from topic: {topic.title}",
                "is_subscribed": False
            })
        else:
            # Subscribe
            subscription = TopicSubscription.objects.create(
                user=request.user,
                topic=topic
            )
            return Response({
                "message": f"Subscribed to topic: {topic.title}",
                "is_subscribed": True,
                "subscription": TopicSubscriptionSerializer(subscription).data
            })


class ThreadSubscriptionViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing thread subscriptions
    """
    serializer_class = ThreadSubscriptionSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return ThreadSubscription.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

    @action(detail=False, methods=['post'])
    def toggle(self, request):
        """Toggle subscription for a thread"""
        thread_id = request.data.get('thread_id')
        if not thread_id:
            return Response(
                {"error": "Thread ID is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            thread = ForumThread.objects.get(id=thread_id)
        except ForumThread.DoesNotExist:
            return Response(
                {"error": "Thread not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Check if subscription exists
        subscription = ThreadSubscription.objects.filter(
            user=request.user,
            thread=thread
        ).first()

        if subscription:
            # Unsubscribe
            subscription.delete()
            return Response({
                "message": f"Unsubscribed from thread: {thread.title}",
                "is_subscribed": False
            })
        else:
            # Subscribe
            subscription = ThreadSubscription.objects.create(
                user=request.user,
                thread=thread
            )
            return Response({
                "message": f"Subscribed to thread: {thread.title}",
                "is_subscribed": True,
                "subscription": ThreadSubscriptionSerializer(subscription).data
            })


class ForumAttachmentViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing forum attachments
    """
    queryset = ForumAttachment.objects.all().order_by('-created_at')
    serializer_class = ForumAttachmentSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['thread', 'post', 'file_type', 'uploaded_by']

    def get_queryset(self):
        # Users can only see their own attachments unless they're admin
        if not (self.request.user.is_staff or self.request.user.is_superuser):
            return ForumAttachment.objects.filter(uploaded_by=self.request.user).order_by('-created_at')
        return super().get_queryset()

    def perform_create(self, serializer):
        # Set the uploaded_by field to the current user
        serializer.save(uploaded_by=self.request.user)

    @action(detail=False, methods=['post'])
    def upload_thread_attachment(self, request):
        """Upload an attachment for a thread"""
        thread_id = request.data.get('thread_id')
        if not thread_id:
            return Response(
                {"error": "Thread ID is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            thread = ForumThread.objects.get(id=thread_id)
        except ForumThread.DoesNotExist:
            return Response(
                {"error": "Thread not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Check if user has permission to add attachments to this thread
        if not (request.user == thread.author or request.user.is_staff or request.user.is_superuser):
            return Response(
                {"error": "You do not have permission to add attachments to this thread"},
                status=status.HTTP_403_FORBIDDEN
            )

        # Check if file is provided
        if 'file' not in request.FILES:
            return Response(
                {"error": "No file provided"},
                status=status.HTTP_400_BAD_REQUEST
            )

        file = request.FILES['file']

        # Create attachment
        attachment = ForumAttachment.objects.create(
            file=file,
            file_name=file.name,
            file_size=file.size,
            content_type=file.content_type,
            thread=thread,
            uploaded_by=request.user
        )

        return Response({
            "message": "Attachment uploaded successfully",
            "attachment": ForumAttachmentSerializer(attachment, context={'request': request}).data
        })

    @action(detail=False, methods=['post'])
    def upload_post_attachment(self, request):
        """Upload an attachment for a post"""
        post_id = request.data.get('post_id')
        if not post_id:
            return Response(
                {"error": "Post ID is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            post = ForumPost.objects.get(id=post_id)
        except ForumPost.DoesNotExist:
            return Response(
                {"error": "Post not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Check if user has permission to add attachments to this post
        if not (request.user == post.author or request.user.is_staff or request.user.is_superuser):
            return Response(
                {"error": "You do not have permission to add attachments to this post"},
                status=status.HTTP_403_FORBIDDEN
            )

        # Check if file is provided
        if 'file' not in request.FILES:
            return Response(
                {"error": "No file provided"},
                status=status.HTTP_400_BAD_REQUEST
            )

        file = request.FILES['file']

        # Create attachment
        attachment = ForumAttachment.objects.create(
            file=file,
            file_name=file.name,
            file_size=file.size,
            content_type=file.content_type,
            post=post,
            uploaded_by=request.user
        )

        return Response({
            "message": "Attachment uploaded successfully",
            "attachment": ForumAttachmentSerializer(attachment, context={'request': request}).data
        })


class ForumAnalyticsView(APIView):
    """
    Get analytics data for the forum
    """
    permission_classes = [IsAdminUser]

    def get(self, request):
        """Get forum analytics data"""
        action = request.query_params.get('action', 'overview')

        if action == 'overview':
            data = get_forum_overview_stats()
        elif action == 'activity':
            period = request.query_params.get('period', 'day')
            days = int(request.query_params.get('days', 30))
            data = get_activity_over_time(period=period, days=days)
        elif action == 'contributors':
            limit = int(request.query_params.get('limit', 10))
            data = get_top_contributors(limit=limit)
        elif action == 'popular_threads':
            limit = int(request.query_params.get('limit', 10))
            period = request.query_params.get('period')
            data = get_popular_threads(limit=limit, period=period)
        elif action == 'engagement':
            data = get_user_engagement_stats()
        else:
            return Response(
                {"error": f"Invalid action: {action}"},
                status=status.HTTP_400_BAD_REQUEST
            )

        return Response(data)


class ForumSearchView(APIView):
    """
    Search forum content (threads, posts, topics, categories)
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        query = request.query_params.get('q', '')
        search_type = request.query_params.get('type', 'all')
        category_id = request.query_params.get('category_id')
        topic_id = request.query_params.get('topic_id')

        # Convert to integers if provided
        if category_id:
            try:
                category_id = int(category_id)
            except ValueError:
                category_id = None

        if topic_id:
            try:
                topic_id = int(topic_id)
            except ValueError:
                topic_id = None

        # Perform search
        results = search_forums(
            query=query,
            user=request.user,
            search_type=search_type,
            category_id=category_id,
            topic_id=topic_id
        )

        # Serialize results
        serialized_results = {
            'threads': ForumThreadSerializer(results['threads'], many=True, context={'request': request}).data,
            'posts': ForumPostSerializer(results['posts'], many=True, context={'request': request}).data,
            'topics': ForumTopicSerializer(results['topics'], many=True, context={'request': request}).data,
            'categories': ForumCategorySerializer(results['categories'], many=True, context={'request': request}).data,
        }

        # Add metadata
        response_data = {
            'query': query,
            'total_results': sum(len(results[key]) for key in results),
            'results': serialized_results
        }

        return Response(response_data)
