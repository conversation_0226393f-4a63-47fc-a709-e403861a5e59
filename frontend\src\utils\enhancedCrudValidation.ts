import { TFunction } from 'react-i18next';

export interface ValidationRule {
  field: string;
  required?: boolean;
  type?: 'string' | 'number' | 'email' | 'url' | 'date' | 'boolean' | 'array' | 'object';
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: RegExp;
  custom?: (value: any, data: any) => string | null;
  dependencies?: string[];
  conditional?: (data: any) => boolean;
}

export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string[]>;
  warnings: Record<string, string[]>;
  fieldErrors: string[];
  globalErrors: string[];
}

export class EnhancedCrudValidator {
  private rules: ValidationRule[];
  private t: TFunction;

  constructor(rules: ValidationRule[], t: TFunction) {
    this.rules = rules;
    this.t = t;
  }

  validate(data: any): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: {},
      warnings: {},
      fieldErrors: [],
      globalErrors: []
    };

    // Validate each rule
    for (const rule of this.rules) {
      // Check if rule should be applied conditionally
      if (rule.conditional && !rule.conditional(data)) {
        continue;
      }

      const value = this.getNestedValue(data, rule.field);
      const fieldErrors = this.validateField(value, rule, data);

      if (fieldErrors.length > 0) {
        result.errors[rule.field] = fieldErrors;
        result.fieldErrors.push(...fieldErrors);
        result.isValid = false;
      }
    }

    // Validate dependencies
    this.validateDependencies(data, result);

    return result;
  }

  private validateField(value: any, rule: ValidationRule, data: any): string[] {
    const errors: string[] = [];

    // Required validation
    if (rule.required && this.isEmpty(value)) {
      errors.push(this.t('validation.required'));
      return errors; // Skip other validations if required field is empty
    }

    // Skip other validations if value is empty and not required
    if (this.isEmpty(value)) {
      return errors;
    }

    // Type validation
    if (rule.type && !this.validateType(value, rule.type)) {
      errors.push(this.t(`validation.type.${rule.type}`));
    }

    // Length validations
    if (rule.minLength !== undefined && this.getLength(value) < rule.minLength) {
      errors.push(this.t('validation.minLength', { min: rule.minLength }));
    }

    if (rule.maxLength !== undefined && this.getLength(value) > rule.maxLength) {
      errors.push(this.t('validation.maxLength', { max: rule.maxLength }));
    }

    // Numeric validations
    if (rule.min !== undefined && Number(value) < rule.min) {
      errors.push(this.t('validation.min', { min: rule.min }));
    }

    if (rule.max !== undefined && Number(value) > rule.max) {
      errors.push(this.t('validation.max', { max: rule.max }));
    }

    // Pattern validation
    if (rule.pattern && !rule.pattern.test(String(value))) {
      errors.push(this.t('validation.pattern'));
    }

    // Custom validation
    if (rule.custom) {
      const customError = rule.custom(value, data);
      if (customError) {
        errors.push(customError);
      }
    }

    return errors;
  }

  private validateDependencies(data: any, result: ValidationResult): void {
    for (const rule of this.rules) {
      if (rule.dependencies) {
        for (const dependency of rule.dependencies) {
          const dependencyValue = this.getNestedValue(data, dependency);
          const currentValue = this.getNestedValue(data, rule.field);

          // Check if dependency is satisfied
          if (!this.isEmpty(currentValue) && this.isEmpty(dependencyValue)) {
            const error = this.t('validation.dependency', { 
              field: rule.field, 
              dependency 
            });
            
            if (!result.errors[rule.field]) {
              result.errors[rule.field] = [];
            }
            result.errors[rule.field].push(error);
            result.fieldErrors.push(error);
            result.isValid = false;
          }
        }
      }
    }
  }

  private isEmpty(value: any): boolean {
    if (value === null || value === undefined) return true;
    if (typeof value === 'string') return value.trim() === '';
    if (Array.isArray(value)) return value.length === 0;
    if (typeof value === 'object') return Object.keys(value).length === 0;
    return false;
  }

  private getLength(value: any): number {
    if (typeof value === 'string') return value.length;
    if (Array.isArray(value)) return value.length;
    if (typeof value === 'object' && value !== null) return Object.keys(value).length;
    return String(value).length;
  }

  private validateType(value: any, type: string): boolean {
    switch (type) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'email':
        return typeof value === 'string' && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
      case 'url':
        try {
          new URL(value);
          return true;
        } catch {
          return false;
        }
      case 'date':
        return value instanceof Date || !isNaN(Date.parse(value));
      case 'boolean':
        return typeof value === 'boolean';
      case 'array':
        return Array.isArray(value);
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value);
      default:
        return true;
    }
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }
}

// Predefined validation schemas for common entities
export const validationSchemas = {
  businessIdea: [
    { field: 'title', required: true, type: 'string', minLength: 3, maxLength: 100 },
    { field: 'description', required: true, type: 'string', minLength: 10, maxLength: 1000 },
    { field: 'category', required: true, type: 'string' },
    { field: 'stage', required: true, type: 'string' },
    { field: 'target_audience', type: 'string', maxLength: 500 },
    { field: 'problem_statement', type: 'string', maxLength: 1000 },
    { field: 'solution_description', type: 'string', maxLength: 1000 },
    { field: 'market_opportunity', type: 'string', maxLength: 1000 },
    { field: 'business_model', type: 'string', maxLength: 1000 }
  ],
  
  post: [
    { field: 'title', required: true, type: 'string', minLength: 3, maxLength: 200 },
    { field: 'content', required: true, type: 'string', minLength: 10 },
    { field: 'excerpt', type: 'string', maxLength: 300 },
    { field: 'category', required: true, type: 'string' },
    { field: 'type', required: true, type: 'string' },
    { field: 'status', type: 'string' },
    { field: 'featured_image', type: 'url' }
  ],

  event: [
    { field: 'title', required: true, type: 'string', minLength: 3, maxLength: 200 },
    { field: 'description', required: true, type: 'string', minLength: 10 },
    { field: 'start_date', required: true, type: 'date' },
    { field: 'end_date', type: 'date' },
    { field: 'location', type: 'string', maxLength: 200 },
    { field: 'max_attendees', type: 'number', min: 1 },
    { field: 'registration_deadline', type: 'date' },
    { field: 'event_type', required: true, type: 'string' }
  ],

  resource: [
    { field: 'title', required: true, type: 'string', minLength: 3, maxLength: 200 },
    { field: 'description', required: true, type: 'string', minLength: 10 },
    { field: 'type', required: true, type: 'string' },
    { field: 'category', required: true, type: 'string' },
    { field: 'url', type: 'url' },
    { field: 'file_size', type: 'number', min: 0 },
    { field: 'difficulty_level', type: 'string' }
  ],

  user: [
    { field: 'first_name', required: true, type: 'string', minLength: 2, maxLength: 50 },
    { field: 'last_name', required: true, type: 'string', minLength: 2, maxLength: 50 },
    { field: 'email', required: true, type: 'email' },
    { field: 'phone', type: 'string', pattern: /^\+?[\d\s\-\(\)]+$/ },
    { field: 'bio', type: 'string', maxLength: 1000 },
    { field: 'website', type: 'url' },
    { field: 'location', type: 'string', maxLength: 100 }
  ]
};

// Helper function to create validator with schema
export function createValidator(
  entityType: keyof typeof validationSchemas,
  t: TFunction,
  customRules: ValidationRule[] = []
): EnhancedCrudValidator {
  const rules = [...validationSchemas[entityType], ...customRules];
  return new EnhancedCrudValidator(rules, t);
}
