import React, { useState, useEffect } from 'react';
import { getTimeBasedGreeting, formatName } from '../../utils/culturalAdaptations';
import { useAppSelector } from '../../store/hooks';
import { RTLText } from '.';
interface CulturalGreetingProps {
  firstName?: string;
  lastName?: string;
  gender?: 'male' | 'female' | 'neutral';
  className?: string;
  showName?: boolean;
  formal?: boolean;
}

/**
 * Component for displaying culturally appropriate greetings
 */
const CulturalGreeting: React.FC<CulturalGreetingProps> = ({ firstName = '',
  lastName = '',
  gender = 'neutral',
  className = '',
  showName = true,
  formal = false,
 }) => {
  const { user } = useAppSelector(state => state.auth);
  const [greeting, setGreeting] = useState('');

  useEffect(() => {
    // Get the time-based greeting
    const timeGreeting = getTimeBasedGreeting();

    // Use provided name or user's name from auth state
    const userFirstName = firstName || user?.first_name || user?.username || '';
    const userLastName = lastName || user?.last_name || '';

    // Format the full greeting
    if (showName && (userFirstName || userLastName)) {
      const formattedName = formatName(userFirstName, userLastName, {
        includeHonorific: formal,
        gender,
        formal,
      });

      setGreeting(`${timeGreeting}, ${formattedName}!`);
    } else {
      setGreeting(`${timeGreeting}!`);
    }
  }, [firstName, lastName, gender, showName, formal, user]);

  return (
    <RTLText className={className}>
      {greeting}
    </RTLText>
  );
};

export default CulturalGreeting;
