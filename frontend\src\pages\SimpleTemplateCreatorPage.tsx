import { useTranslation } from 'react-i18next';
/**
 * Simple Template Creator Page
 * Standalone page for creating custom templates
 */

import React from 'react';
import { useNavigate } from 'react-router-dom';
import SimpleTemplateCreator from '../components/templates/SimpleTemplateCreator';

const SimpleTemplateCreatorPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const navigate = useNavigate();

  const handleTemplateCreated = (templateData: any) => {
    console.log("Template created:", templateData);
    // Show success message and redirect
    setTimeout(() => {
      navigate('/dashboard/templates');
    }, 2000);
  };

  const handleCancel = () => {
    navigate('/dashboard/templates');
  };

  return (
    <SimpleTemplateCreator
      onTemplateCreated={handleTemplateCreated}
      onCancel={handleCancel}
    />
  );
};

export default SimpleTemplateCreatorPage;
