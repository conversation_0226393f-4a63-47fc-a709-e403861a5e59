import { lazy } from 'react';
import { RouteConfig, createAdminRoute, createSuperAdminRoute } from './routeConfig';

// DEDICATED ADMIN DASHBOARD - Admin users only
const AdminDashboardPage = lazy(() => import('../pages/admin/AdminDashboardPage'));
// Legacy Dashboard kept for backward compatibility
// const Dashboard = lazy(() => import('../components/admin/dashboard/Dashboard'));
const UsersManagement = lazy(() => import('../components/admin/users/UsersManagement'));
const EventsManagement = lazy(() => import('../components/admin/content/EventsManagement'));
const ResourcesManagement = lazy(() => import('../components/admin/content/ResourcesManagement'));
const PostsManagementPage = lazy(() => import('../components/admin/content/posts/PostsManagementPage'));
const ModerationDashboard = lazy(() => import('../components/admin/content/ModerationDashboard'));
const MembershipApplicationsManagement = lazy(() => import('../components/admin/membership/MembershipApplicationsManagement'));
const BusinessIdeasManagement = lazy(() => import('../components/admin/incubator/BusinessIdeasManagement'));
const FundingOpportunitiesManagement = lazy(() => import('../components/admin/incubator/FundingOpportunitiesManagement'));
const MentorProfilesManagement = lazy(() => import('../components/admin/incubator/MentorProfilesManagement'));
const MentorshipApplicationsManagement = lazy(() => import('../components/admin/incubator/MentorshipApplicationsManagement'));
const MentorshipMatchesManagement = lazy(() => import('../components/admin/incubator/MentorshipMatchesManagement'));
const MentorshipSessionsManagement = lazy(() => import('../components/admin/incubator/MentorshipSessionsManagement'));
const ProgressUpdatesManagement = lazy(() => import('../components/admin/incubator/ProgressUpdatesManagement'));
const InvestorProfilesManagement = lazy(() => import('../components/admin/incubator/InvestorProfilesManagement'));
const FundingApplicationsManagement = lazy(() => import('../components/admin/incubator/FundingApplicationsManagement'));
const BusinessPlansManagement = lazy(() => import('../components/admin/incubator/BusinessPlansManagement'));
const BusinessMilestonesManagement = lazy(() => import('../components/admin/incubator/BusinessMilestonesManagement'));
// const AnalyticsDashboard = lazy(() => import('../components/admin/analytics/AnalyticsDashboard'));

// New Enhanced CRUD Management Components
const FundingManagement = lazy(() => import('../components/admin/funding/FundingManagement'));
const MilestonesManagement = lazy(() => import('../components/admin/milestones/MilestonesManagement'));
// const RealTimeDashboard = lazy(() => import('../components/admin/dashboard/RealTimeDashboard'));
const AdvancedUserManagement = lazy(() => import('../components/admin/users/AdvancedUserManagement'));
const CommunicationCenter = lazy(() => import('../components/admin/communication/CommunicationCenter'));
const SystemMonitoring = lazy(() => import('../components/admin/system/SystemMonitoring'));
const BackupManagement = lazy(() => import('../components/admin/system/BackupManagement'));
const SecurityCenter = lazy(() => import('../components/admin/security/SecurityCenter'));
const ApiManagement = lazy(() => import('../components/admin/api/ApiManagement'));
const PerformanceCenter = lazy(() => import('../components/admin/performance/PerformanceCenter'));
const ForumModeration = lazy(() => import('../components/admin/forum/ForumModeration'));
const ForumAnalytics = lazy(() => import('../components/admin/forum/ForumAnalytics'));
const SystemLogs = lazy(() => import('../components/admin/system/SystemLogs'));
const AdminSettings = lazy(() => import('../components/admin/settings/AdminSettings'));
const MembershipTest = lazy(() => import('../components/admin/membership/MembershipTest'));

// ✅ REMOVED: Super Admin components moved to superAdminRoutes.ts to eliminate duplicates
const ConsolidatedAIPage = lazy(() => import('../pages/ConsolidatedAIPage'));
const ConsolidatedAIStatusPage = lazy(() => import('../pages/ConsolidatedAIStatusPage'));
const AISystemPage = lazy(() => import('../pages/admin/AISystemPage'));
const SystemLogsPage = lazy(() => import('../pages/admin/SystemLogsPage'));

/**
 * Admin-only routes that require admin privileges
 */
export const adminRoutes: RouteConfig[] = [
  // Main admin dashboard - DEDICATED ADMIN ONLY ✅ (using AdminDashboardPage)
  createAdminRoute('/admin', AdminDashboardPage, 'Loading admin dashboard...'),

  // User management
  createAdminRoute('/admin/users', UsersManagement, 'Loading users management...'),

  // Content management
  createAdminRoute('/admin/events', EventsManagement, 'Loading events management...'),
  createAdminRoute('/admin/resources', ResourcesManagement, 'Loading resources management...'),
  createAdminRoute('/admin/posts', PostsManagementPage, 'Loading posts management...'),
  createAdminRoute('/admin/moderation', ModerationDashboard, 'Loading moderation dashboard...'),

  // Forum management
  createAdminRoute('/admin/forum-moderation', ForumModeration, 'Loading forum moderation...'),
  createAdminRoute('/admin/forum-analytics', ForumAnalytics, 'Loading forum analytics...'),

  // Membership management
  createAdminRoute('/admin/membership', MembershipApplicationsManagement, 'Loading membership applications...'),
  createAdminRoute('/admin/membership-test', MembershipTest, 'Loading membership test...'),

  // Business incubator management
  createAdminRoute('/admin/incubator', BusinessIdeasManagement, 'Loading incubator management...'),
  createAdminRoute('/admin/incubator/funding-opportunities', FundingOpportunitiesManagement, 'Loading funding opportunities management...'),
  createAdminRoute('/admin/incubator/mentor-profiles', MentorProfilesManagement, 'Loading mentor profiles management...'),
  createAdminRoute('/admin/incubator/mentorship-applications', MentorshipApplicationsManagement, 'Loading mentorship applications management...'),
  createAdminRoute('/admin/incubator/mentorship-matches', MentorshipMatchesManagement, 'Loading mentorship matches management...'),
  createAdminRoute('/admin/incubator/mentorship-sessions', MentorshipSessionsManagement, 'Loading mentorship sessions management...'),
  createAdminRoute('/admin/incubator/progress-updates', ProgressUpdatesManagement, 'Loading progress updates management...'),
  createAdminRoute('/admin/incubator/investor-profiles', InvestorProfilesManagement, 'Loading investor profiles management...'),
  createAdminRoute('/admin/incubator/funding-applications', FundingApplicationsManagement, 'Loading funding applications management...'),
  createAdminRoute('/admin/incubator/business-plans', BusinessPlansManagement, 'Loading business plans management...'),
  createAdminRoute('/admin/incubator/business-milestones', BusinessMilestonesManagement, 'Loading business milestones management...'),

  // Enhanced CRUD Management
  createAdminRoute('/admin/funding', FundingManagement, 'Loading funding management...'),
  createAdminRoute('/admin/milestones', MilestonesManagement, 'Loading milestones management...'),

  // Analytics and Dashboard
  // createAdminRoute('/admin/analytics', AnalyticsDashboard, 'Loading analytics dashboard...'),
  // createAdminRoute('/admin/dashboard/realtime', RealTimeDashboard, 'Loading real-time dashboard...'),

  // Advanced User Management
  createAdminRoute('/admin/users/advanced', AdvancedUserManagement, 'Loading advanced user management...'),

  // Communication
  createAdminRoute('/admin/communication', CommunicationCenter, 'Loading communication center...'),

  // System Management - ✅ CLEANED UP: Removed duplicated routes
  createAdminRoute('/admin/api', ApiManagement, 'Loading API management...'),
  createAdminRoute('/admin/performance', PerformanceCenter, 'Loading performance center...'),

  // AI system management (admin-only system management, not chat)
  createAdminRoute('/admin/ai-system', AISystemPage, 'Loading AI system management...'),

  // ✅ REMOVED: Super Admin routes moved to dedicated superAdminRoutes.ts
  // This eliminates duplication and route conflicts

  // System management
  createAdminRoute('/admin/system-logs', SystemLogsPage, 'Loading system logs...'),
  createAdminRoute('/admin/settings', AdminSettings, 'Loading admin settings...'),
];

export default adminRoutes;
