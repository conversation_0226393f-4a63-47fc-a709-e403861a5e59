import React from 'react';
import { X } from 'lucide-react';
import { RichTextEditor } from '../../../../../components/common';
import { Post } from '../../../../../services/api';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../../hooks/useLanguage';
interface PostFormData {
  title: string;
  content: string;
}

interface PostEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedPost: Post | null;
  formData: PostFormData;
  formError: string | null;
  formSuccess: string | null;
  formSubmitting: boolean;
  onInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | string, editorName?: string) => void;
  onSubmit: () => void;
  preventFormSubmission: (e: React.FormEvent) => void;
}

const PostEditModal: React.FC<PostEditModalProps> = ({ isOpen,
  onClose,
  selectedPost,
  formData,
  formError,
  formSuccess,
  formSubmitting,
  onInputChange,
  onSubmit,
  preventFormSubmission
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  if (!isOpen || !selectedPost) return null;

  return (
    <div className={`fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4 ${isRTL ? "flex-row-reverse" : ""}`}>
      <div className="bg-indigo-900/90 rounded-xl shadow-lg w-full max-w-md p-6 relative">
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-white"
        >
          <X size={20} />
        </button>
        <h2 className="text-xl font-bold mb-6">t("admin.edit.post", "Edit Post")</h2>
        <form onSubmit={preventFormSubmission}>
          <div className="mb-4">
            <label className="block text-gray-300 mb-2">t("admin.title", t("common.title", "Title"))</label>
            <input
              type="text"
              name="title"
              value={formData.title}
              onChange={onInputChange}
              className="w-full px-4 py-2 bg-indigo-900/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:border-purple-500"
              placeholder={t("admin.enter.post.title", "Enter post title")}
            />
          </div>
          <div className="mb-4">
            <label className="block text-gray-300 mb-2">t("admin.content", "Content")</label>
            <RichTextEditor
              value={formData.content}
              onChange={(value) => onInputChange(value, 'content')}
              placeholder={t("admin.enter.post.content", "Enter post content")}
            />
          </div>

          {formError && (
            <div className="mb-4 p-3 bg-red-900/50 border border-red-800 text-white rounded-lg">
              {formError}
            </div>
          )}

          {formSuccess && (
            <div className="mb-4 p-3 bg-green-900/50 border border-green-800 text-white rounded-lg">
              {formSuccess}
            </div>
          )}

          <div className={`flex justify-end ${isRTL ? "flex-row-reverse" : ""}`}>
            <button
              type="button"
              onClick={onClose}
              className={`px-4 py-2 bg-indigo-800/50 hover:bg-indigo-700/50 rounded-lg mr-2 ${isRTL ? "space-x-reverse" : ""}`}
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={onSubmit}
              disabled={formSubmitting}
              className={`px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
            >
              {formSubmitting ? (
                <>
                  <div className={`w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 ${isRTL ? "space-x-reverse" : ""}`}></div>
                  Updating...
                </>
              ) : (
                t("admin.update.post", "Update Post")
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default PostEditModal;
