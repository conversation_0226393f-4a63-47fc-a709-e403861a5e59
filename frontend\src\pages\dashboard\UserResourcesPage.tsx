import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Plus, Search, Edit, Trash2, BookOpen, FileText, Video, Link as LinkIcon, Loader2, Download } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
// MainLayout removed - handled by routing system
import { Resource, resourcesAPI } from '../../services/api';
import { useAppSelector } from '../../store/hooks';
import { RTLText, RTLFlex, RTLIcon } from '../../components/rtl';

const UserResourcesPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { language, isRTL } = useLanguage();
  const { user } = useAppSelector(state => state.auth);

  const [resources, setResources] = useState<Resource[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedResource, setSelectedResource] = useState<Resource | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [creating, setCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeFilter, setActiveFilter] = useState<string | null>(null);

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    resource_type: 'article',
    url: '',
    file: null as File | null,
  });

  const resourceTypes = [
    { value: 'article', label: t('resources.types.article', 'Article'), icon: FileText },
    { value: 'video', label: t('resources.types.video', 'Video'), icon: Video },
    { value: 'document', label: t('resources.types.document', 'Document'), icon: BookOpen },
    { value: 'link', label: t('resources.types.link', 'Link'), icon: LinkIcon },
    { value: 'tool', label: t('resources.types.tool', 'Tool'), icon: Download },
  ];

  useEffect(() => {
    fetchUserResources();
  }, [user]);

  const fetchUserResources = async () => {
    setLoading(true);
    try {
      const allResources = await resourcesAPI.getResources();
      // Filter resources to only show the current user's resources
      const userResources = allResources.filter(resource => resource.author.id === user?.id);
      setResources(userResources);
    } catch (error) {
      console.error('Error fetching user resources:', error);
      setError(t('resources.errors.failedToLoad', 'Failed to load resources'));
    } finally {
      setLoading(false);
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    if (type === 'file') {
      const fileInput = e.target as HTMLInputElement;
      setFormData(prev => ({
        ...prev,
        [name]: fileInput.files?.[0] || null
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const openCreateModal = () => {
    setFormData({
      title: '',
      description: '',
      resource_type: 'article',
      url: '',
      file: null,
    });
    setError(null);
    setSuccess(null);
    setIsCreateModalOpen(true);
  };

  const openDeleteModal = (resource: Resource) => {
    setSelectedResource(resource);
    setIsDeleteModalOpen(true);
  };

  const handleCreateResource = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    setCreating(true);
    setError(null);

    try {
      const resourceData = new FormData();
      resourceData.append('title', formData.title);
      resourceData.append('description', formData.description);
      resourceData.append('resource_type', formData.resource_type);
      resourceData.append('author_id', user.id.toString());
      
      if (formData.url) {
        resourceData.append('url', formData.url);
      }
      
      if (formData.file) {
        resourceData.append('file', formData.file);
      }

      await resourcesAPI.createResource(resourceData as any);

      setSuccess(t('resources.success.created', 'Resource created successfully'));
      fetchUserResources();
      setIsCreateModalOpen(false);
      
      // Clear success message after delay
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error('Error creating resource:', error);
      setError(t('resources.errors.failedToCreate', 'Failed to create resource'));
    } finally {
      setCreating(false);
    }
  };

  const handleDeleteResource = async () => {
    if (!selectedResource) return;

    setDeleting(true);
    try {
      await resourcesAPI.deleteResource(selectedResource.id);
      setSuccess(t('resources.success.deleted', 'Resource deleted successfully'));
      fetchUserResources();
      setIsDeleteModalOpen(false);
      setSelectedResource(null);
      
      // Clear success message after delay
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error('Error deleting resource:', error);
      setError(t('resources.errors.failedToDelete', 'Failed to delete resource'));
    } finally {
      setDeleting(false);
    }
  };

  // Filter resources based on search term and type filter
  const filteredResources = resources.filter(resource => {
    const matchesSearch = 
      resource.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      resource.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = !activeFilter || resource.resource_type === activeFilter;
    
    return matchesSearch && matchesFilter;
  });

  const getResourceIcon = (type: string) => {
    const resourceType = resourceTypes.find(rt => rt.value === type);
    const IconComponent = resourceType?.icon || FileText;
    return <IconComponent size={20} />;
  };

  const getResourceTypeLabel = (type: string) => {
    const resourceType = resourceTypes.find(rt => rt.value === type);
    return resourceType?.label || type;
  };

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div>
        {/* Header */}
        <div className={`mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
          <div>
            <RTLText as="h1" className="text-2xl font-bold">{t('resources.myResources', 'My Resources')}</RTLText>
            <RTLText as="div" className="text-gray-400 mt-1">{t('resources.manageYourResources', 'Manage your shared resources')}</RTLText>
          </div>
          <button
            onClick={openCreateModal}
            className="mt-4 sm:mt-0 px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300 flex items-center justify-center"
          >
            <Plus className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
            {t('resources.createNewResource', 'Share New Resource')}
          </button>
        </div>

        {/* Error/Success Messages */}
        {error && (
          <div className="mb-4 p-3 bg-red-900/50 border border-red-500 rounded-md text-red-200">
            {error}
          </div>
        )}

        {success && (
          <div className="mb-4 p-3 bg-green-900/50 border border-green-500 rounded-md text-green-200">
            {success}
          </div>
        )}

        {/* Search and Filters */}
        <div className={`mb-6 flex flex-col md:flex-row gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`relative flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
            <input
              type="text"
              placeholder={t('resources.searchResources', 'Search your resources...')}
              value={searchTerm}
              onChange={handleSearchChange}
              className={`w-full px-4 py-2 ${language === 'ar' ? 'pr-10' : 'pl-10'} bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white`}
              dir={language === 'ar' ? 'rtl' : 'ltr'}
            />
            <Search size={18} className={`absolute ${language === 'ar' ? 'right-3' : 'left-3'} top-2.5 text-gray-400`} />
          </div>

          <div className={`flex ${language === 'ar' ? 'space-x-reverse' : 'space-x-2'} space-x-2 flex-wrap gap-2`}>
            <button
              onClick={() => setActiveFilter(null)}
              className={`px-3 py-2 rounded-lg text-sm ${
                activeFilter === null ? 'bg-indigo-700' : 'bg-indigo-900/50 hover:bg-indigo-800/50'
              }`}
            >
              {t('common.all', 'All')}
            </button>
            {resourceTypes.map(type => (
              <button
                key={type.value}
                onClick={() => setActiveFilter(type.value)}
                className={`px-3 py-2 rounded-lg text-sm flex items-center ${
                  activeFilter === type.value ? 'bg-purple-700' : 'bg-purple-900/50 hover:bg-purple-800/50'
                }`}
              >
                <type.icon size={16} className={`${language === 'ar' ? 'ml-1' : 'mr-1'}`} />
                {type.label}
              </button>
            ))}
          </div>
        </div>

        {/* Resources List */}
        {loading ? (
          <div className="flex justify-center py-12">
            <div className="flex items-center space-x-3">
              <Loader2 className="w-6 h-6 animate-spin" />
              <span>{t('common.loading', 'Loading...')}</span>
            </div>
          </div>
        ) : filteredResources.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredResources.map(resource => (
              <div
                key={resource.id}
                className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50 hover:border-indigo-700/50 transition-colors"
              >
                <div className={`flex justify-between items-start mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className="flex-1">
                    <RTLFlex align="center" className="mb-2">
                      <div className="p-2 bg-indigo-900/50 rounded-lg mr-3">
                        {getResourceIcon(resource.resource_type)}
                      </div>
                      <div>
                        <RTLText as="h3" className="font-bold line-clamp-1">{resource.title}</RTLText>
                        <span className="text-xs text-purple-400">{getResourceTypeLabel(resource.resource_type)}</span>
                      </div>
                    </RTLFlex>
                    <RTLText as="div" className="text-gray-300 mb-3 text-sm line-clamp-3">
                      {resource.description}
                    </RTLText>
                  </div>
                  <div className={`flex space-x-2 ml-4 ${isRTL ? "flex-row-reverse space-x-reverse mr-4 ml-0" : ""}`}>
                    <Link
                      to={`/resources/edit/${resource.id}`}
                      className="p-2 bg-blue-900/50 hover:bg-blue-800/50 rounded-md transition-colors"
                      title={t('common.edit', 'Edit')}
                    >
                      <Edit size={16} />
                    </Link>
                    <button
                      onClick={() => openDeleteModal(resource)}
                      className="p-2 bg-red-900/50 hover:bg-red-800/50 rounded-md transition-colors"
                      title={t('common.delete', 'Delete')}
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>

                <div className="text-xs text-gray-400">
                  {t('resources.createdOn', 'Created on')} {new Date(resource.created_at).toLocaleDateString(language === 'ar' ? 'ar-SA' : undefined)}
                </div>

                {resource.url && (
                  <div className="mt-3">
                    <a
                      href={resource.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-400 hover:text-blue-300 text-sm flex items-center"
                    >
                      <LinkIcon size={14} className={`${language === 'ar' ? 'ml-1' : 'mr-1'}`} />
                      {t('resources.viewResource', 'View Resource')}
                    </a>
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12 bg-indigo-900/20 rounded-lg border border-indigo-800">
            <BookOpen size={48} className="mx-auto text-gray-500 mb-4" />
            <RTLText as="h3" className="text-xl font-semibold mb-2">
              {searchTerm || activeFilter ? t('resources.noResourcesFound', 'No resources found') : t('resources.noResourcesYet', 'No resources yet')}
            </RTLText>
            <RTLText as="div" className="text-gray-400 mb-4">
              {searchTerm || activeFilter
                ? t('resources.tryDifferentSearch', 'Try a different search term or filter')
                : t('resources.shareFirstResource', 'Share your first resource to help the community')
              }
            </RTLText>
            <button
              onClick={openCreateModal}
              className="px-4 py-2 bg-purple-600 rounded-lg hover:bg-purple-700 transition-colors inline-flex items-center"
            >
              <Plus className={`w-4 h-4 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
              {t('resources.shareResource', 'Share Resource')}
            </button>
          </div>
        )}
      </div>

      {/* Create Resource Modal */}
      {isCreateModalOpen && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
          <div className="bg-indigo-900 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto p-6">
            <RTLText as="h3" className="text-xl font-bold mb-6">{t('resources.shareResource', 'Share Resource')}</RTLText>
            
            <form onSubmit={handleCreateResource} className="space-y-4">
              <div>
                <label className="block text-gray-300 mb-2">{t('resources.title', 'Title')} *</label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                  required
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                />
              </div>

              <div>
                <label className="block text-gray-300 mb-2">{t('resources.description', 'Description')} *</label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-4 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none"
                  required
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                />
              </div>

              <div>
                <label className="block text-gray-300 mb-2">{t('resources.type', 'Resource Type')} *</label>
                <select
                  name="resource_type"
                  value={formData.resource_type}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                  required
                >
                  {resourceTypes.map(type => (
                    <option key={type.value} value={type.value}>{type.label}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-gray-300 mb-2">{t('resources.url', 'URL')} (optional)</label>
                <input
                  type="url"
                  name="url"
                  value={formData.url}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder="https://..."
                />
              </div>

              <div>
                <label className="block text-gray-300 mb-2">{t('resources.file', 'File')} (optional)</label>
                <input
                  type="file"
                  name="file"
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                  accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif,.mp4,.mp3"
                />
              </div>

              {error && (
                <div className="p-3 bg-red-900/50 border border-red-500 rounded-md text-red-200">
                  {error}
                </div>
              )}

              <div className={`flex justify-end space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                <button
                  type="button"
                  onClick={() => setIsCreateModalOpen(false)}
                  className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
                  disabled={creating}
                >
                  {t('common.cancel', 'Cancel')}
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors flex items-center disabled:opacity-50"
                  disabled={creating}
                >
                  {creating ? (
                    <>
                      <Loader2 className={`w-4 h-4 animate-spin ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                      {t('common.creating', 'Creating...')}
                    </>
                  ) : (
                    <>
                      <Plus className={`w-4 h-4 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                      {t('resources.shareResource', 'Share Resource')}
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {isDeleteModalOpen && selectedResource && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
          <div className="bg-indigo-900 rounded-lg w-full max-w-md p-6">
            <RTLText as="h3" className="text-xl font-bold mb-4">{t('resources.confirmDelete', 'Confirm Delete')}</RTLText>
            <RTLText as="div" className="text-gray-300 mb-6">
              {t('resources.deleteWarning', 'Are you sure you want to delete this resource? This action cannot be undone.')}
            </RTLText>
            <RTLText as="div" className="text-sm text-gray-400 mb-6 p-3 bg-indigo-950/50 rounded border-l-4 border-purple-500">
              <strong>{selectedResource.title}</strong>
              <br />
              <span className="text-purple-400">{getResourceTypeLabel(selectedResource.resource_type)}</span>
            </RTLText>
            <div className={`flex justify-end space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
              <button
                onClick={() => setIsDeleteModalOpen(false)}
                className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
                disabled={deleting}
              >
                {t('common.cancel', 'Cancel')}
              </button>
              <button
                onClick={handleDeleteResource}
                className="px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors flex items-center disabled:opacity-50"
                disabled={deleting}
              >
                {deleting ? (
                  <>
                    <Loader2 className={`w-4 h-4 animate-spin ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                    {t('common.deleting', 'Deleting...')}
                  </>
                ) : (
                  <>
                    <Trash2 className={`w-4 h-4 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                    {t('common.delete', 'Delete')}
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
              </div>
        </div>
      </div>
    </div>
  );
};

export default UserResourcesPage;
