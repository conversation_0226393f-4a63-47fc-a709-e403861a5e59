import React, { useState, useEffect } from 'react';
import { Search, Users, Shield, Edit, Trash2, Mail, Lock, Unlock, UserCheck, UserX, Filter } from 'lucide-react';
import { DashboardLayout } from '../dashboard';
import { BulkOperations, createCommonBulkActions, AdvancedFilter } from '../common';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';
import { getAuthToken } from '../../../services/api';

interface User {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  role: string;
  status: 'active' | 'inactive' | 'suspended' | 'pending';
  last_login: string;
  created_at: string;
  profile_picture?: string;
  phone?: string;
  location?: string;
  verified: boolean;
  permissions: string[];
}

interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  color: string;
}

const AdvancedUserManagement: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  // State management
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUsers, setSelectedUsers] = useState<number[]>([]);
  const [showRoleModal, setShowRoleModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  // Fetch real data
  useEffect(() => {
    fetchRoles();
    fetchUsers();
  }, []);

  const fetchRoles = async () => {
    try {
      const response = await fetch('/api/superadmin/users/roles/', {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setRoles(data.roles || []);
      } else {
        console.error('Failed to fetch roles:', response.status);
        setRoles([]);
      }
    } catch (error) {
      console.error('Failed to fetch roles:', error);
      setRoles([]);
    }
  };

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/superadmin/users/users-management/', {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setUsers(data.users || []);
      } else {
        console.error('Failed to fetch users:', response.status);
        setUsers([]);
      }
    } catch (error) {
      console.error('Failed to fetch users:', error);
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };

  // Filter users
  const filteredUsers = users.filter(user => {
    return user.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.role.toLowerCase().includes(searchTerm.toLowerCase());
  });

  // Get role info
  const getRoleInfo = (roleId: string) => {
    return roles.find(role => role.id === roleId) || {
      id: roleId,
      name: roleId,
      description: '',
      permissions: [],
      color: 'bg-gray-500'
    };
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-500/20 text-green-300';
      case 'inactive':
        return 'bg-gray-500/20 text-gray-300';
      case 'suspended':
        return 'bg-red-500/20 text-red-300';
      case 'pending':
        return 'bg-yellow-500/20 text-yellow-300';
      default:
        return 'bg-gray-500/20 text-gray-300';
    }
  };

  // Handle user selection
  const handleUserSelect = (userId: number) => {
    setSelectedUsers(prev => 
      prev.includes(userId) 
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const handleSelectAll = () => {
    setSelectedUsers(filteredUsers.map(user => user.id));
  };

  const handleClearSelection = () => {
    setSelectedUsers([]);
  };

  // Bulk actions
  const bulkActions = createCommonBulkActions(
    'user',
    async (ids: number[]) => {
      console.log('Activating users:', ids);
      // Implement activate users
    },
    async (ids: number[]) => {
      console.log('Suspending users:', ids);
      // Implement suspend users
    },
    async (ids: number[]) => {
      console.log('Deleting users:', ids);
      // Implement delete users
    },
    async (ids: number[]) => {
      console.log('Exporting users:', ids);
      // Implement export users
    },
    async (ids: number[]) => {
      console.log('Sending email to users:', ids);
      // Implement send email
    }
  );

  // Filter options
  const filterOptions = [
    {
      field: 'role',
      label: t('user.role', 'Role'),
      type: 'select' as const,
      options: roles.map(role => ({ value: role.id, label: role.name }))
    },
    {
      field: 'status',
      label: t('user.status', 'Status'),
      type: 'select' as const,
      options: [
        { value: 'active', label: t('user.status.active', 'Active') },
        { value: 'inactive', label: t('user.status.inactive', 'Inactive') },
        { value: 'suspended', label: t('user.status.suspended', 'Suspended') },
        { value: 'pending', label: t('user.status.pending', 'Pending') }
      ]
    },
    {
      field: 'verified',
      label: t('user.verified', 'Verified'),
      type: 'select' as const,
      options: [
        { value: 'true', label: t('common.yes', 'Yes') },
        { value: 'false', label: t('common.no', 'No') }
      ]
    },
    {
      field: 'created_at',
      label: t('user.created.date', 'Created Date'),
      type: 'date' as const
    }
  ];

  return (
    <DashboardLayout currentPage="users">
      {/* Header */}
      <div className={`mb-8 ${isRTL ? "text-right" : ""}`}>
        <h1 className="text-2xl font-bold text-white">{t('admin.users.advanced.management', 'Advanced User Management')}</h1>
        <div className="text-gray-400 mt-1">{t('admin.users.advanced.description', 'Comprehensive user and role management')}</div>
      </div>

      {/* Search and Filters */}
      <div className="mb-6 space-y-4">
        <div className="relative">
          <Search size={20} className={`absolute top-3 text-gray-400 ${isRTL ? "right-3" : "left-3"}`} />
          <input
            type="text"
            placeholder={t('admin.search.users', 'Search users...')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className={`w-full bg-indigo-900/50 border border-indigo-800 rounded-lg py-2.5 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 ${isRTL ? "pr-10 pl-4" : "pl-10 pr-4"}`}
          />
        </div>

        <AdvancedFilter
          filterOptions={filterOptions}
          onFilterChange={(filters) => console.log('Filters:', filters)}
          activeFilters={{}}
        />
      </div>

      {/* Bulk Operations */}
      <BulkOperations
        selectedItems={selectedUsers}
        totalItems={filteredUsers.length}
        onSelectAll={handleSelectAll}
        onClearSelection={handleClearSelection}
        actions={bulkActions}
        entityName="user"
      />

      {/* Users List */}
      {loading ? (
        <div className="flex justify-center py-12">
          <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      ) : filteredUsers.length > 0 ? (
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-xl overflow-hidden border border-indigo-800/50">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-indigo-800/50">
                <tr>
                  <th className="p-4 text-left">
                    <input
                      type="checkbox"
                      checked={selectedUsers.length === filteredUsers.length}
                      onChange={selectedUsers.length === filteredUsers.length ? handleClearSelection : handleSelectAll}
                      className="w-4 h-4 text-purple-600 bg-indigo-800 border-indigo-600 rounded focus:ring-purple-500"
                    />
                  </th>
                  <th className="p-4 text-left text-gray-300 font-medium">{t('user.user', 'User')}</th>
                  <th className="p-4 text-left text-gray-300 font-medium">{t('user.role', 'Role')}</th>
                  <th className="p-4 text-left text-gray-300 font-medium">{t('user.status', 'Status')}</th>
                  <th className="p-4 text-left text-gray-300 font-medium">{t('user.last.login', 'Last Login')}</th>
                  <th className="p-4 text-left text-gray-300 font-medium">{t('common.actions', 'Actions')}</th>
                </tr>
              </thead>
              <tbody>
                {filteredUsers.map((user) => {
                  const roleInfo = getRoleInfo(user.role);
                  return (
                    <tr key={user.id} className="border-t border-indigo-800/50 hover:bg-indigo-800/20 transition-colors">
                      <td className="p-4">
                        <input
                          type="checkbox"
                          checked={selectedUsers.includes(user.id)}
                          onChange={() => handleUserSelect(user.id)}
                          className="w-4 h-4 text-purple-600 bg-indigo-800 border-indigo-600 rounded focus:ring-purple-500"
                        />
                      </td>
                      <td className="p-4">
                        <div className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                          <div className="w-10 h-10 rounded-full bg-purple-700 flex items-center justify-center text-white text-sm font-bold">
                            {user.first_name.charAt(0)}{user.last_name.charAt(0)}
                          </div>
                          <div>
                            <p className="text-white font-medium">{user.first_name} {user.last_name}</p>
                            <p className="text-gray-400 text-sm">{user.email}</p>
                          </div>
                          {user.verified && (
                            <UserCheck size={16} className="text-green-400" />
                          )}
                        </div>
                      </td>
                      <td className="p-4">
                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${roleInfo.color}/20 text-white`}>
                          {roleInfo.name}
                        </span>
                      </td>
                      <td className="p-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(user.status)}`}>
                          {user.status}
                        </span>
                      </td>
                      <td className="p-4 text-gray-300 text-sm">
                        {new Date(user.last_login).toLocaleDateString()}
                      </td>
                      <td className="p-4">
                        <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                          <button
                            onClick={() => {
                              setSelectedUser(user);
                              setShowRoleModal(true);
                            }}
                            className="p-2 bg-blue-600/80 rounded-lg text-white hover:bg-blue-500/80 transition-colors"
                            title={t('user.edit.role', 'Edit Role')}
                          >
                            <Shield size={16} />
                          </button>
                          <button
                            className="p-2 bg-green-600/80 rounded-lg text-white hover:bg-green-500/80 transition-colors"
                            title={t('user.edit', 'Edit User')}
                          >
                            <Edit size={16} />
                          </button>
                          <button
                            className="p-2 bg-purple-600/80 rounded-lg text-white hover:bg-purple-500/80 transition-colors"
                            title={t('user.send.email', 'Send Email')}
                          >
                            <Mail size={16} />
                          </button>
                          <button
                            className="p-2 bg-red-600/80 rounded-lg text-white hover:bg-red-500/80 transition-colors"
                            title={t('user.delete', 'Delete User')}
                          >
                            <Trash2 size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      ) : (
        <div className="text-center py-12">
          <Users size={48} className="mx-auto text-gray-600 mb-4" />
          <h3 className="text-lg font-medium text-gray-400 mb-2">
            {searchTerm 
              ? t('admin.no.users.found', 'No users found')
              : t('admin.no.users', 'No users yet')
            }
          </h3>
          <p className="text-gray-500 mb-4">
            {searchTerm
              ? t('admin.try.different.search', 'Try adjusting your search')
              : t('admin.no.users.description', 'Users will appear here when they register')
            }
          </p>
        </div>
      )}

      {/* Role Management Modal */}
      {showRoleModal && selectedUser && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-indigo-900/90 backdrop-blur-sm rounded-xl border border-indigo-800 w-full max-w-md">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-white mb-4">
                {t('user.manage.role', 'Manage User Role')}
              </h3>
              
              <div className="mb-4">
                <p className="text-gray-300 mb-2">
                  {t('user.current.user', 'User')}: {selectedUser.first_name} {selectedUser.last_name}
                </p>
                <p className="text-gray-400 text-sm">{selectedUser.email}</p>
              </div>

              <div className="space-y-3 mb-6">
                {roles.map((role) => (
                  <label key={role.id} className="flex items-center space-x-3 p-3 bg-indigo-800/30 rounded-lg hover:bg-indigo-700/30 transition-colors cursor-pointer">
                    <input
                      type="radio"
                      name="role"
                      value={role.id}
                      checked={selectedUser.role === role.id}
                      onChange={() => setSelectedUser({ ...selectedUser, role: role.id })}
                      className="w-4 h-4 text-purple-600 bg-indigo-800 border-indigo-600 focus:ring-purple-500"
                    />
                    <div className="flex-1">
                      <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                        <div className={`w-3 h-3 rounded-full ${role.color}`}></div>
                        <span className="text-white font-medium">{role.name}</span>
                      </div>
                      <p className="text-gray-400 text-sm mt-1">{role.description}</p>
                    </div>
                  </label>
                ))}
              </div>

              <div className={`flex justify-end space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                <button
                  onClick={() => setShowRoleModal(false)}
                  className="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg font-medium transition-colors"
                >
                  {t('common.cancel', 'Cancel')}
                </button>
                <button
                  onClick={() => {
                    // Update user role
                    setUsers(prev => prev.map(u => u.id === selectedUser.id ? selectedUser : u));
                    setShowRoleModal(false);
                  }}
                  className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg font-medium transition-colors"
                >
                  {t('user.update.role', 'Update Role')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </DashboardLayout>
  );
};

export default AdvancedUserManagement;
