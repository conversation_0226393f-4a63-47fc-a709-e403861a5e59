"""
System Logs URLs
URL patterns for system logs API
"""

from django.urls import path
from .views_logs import (
    SystemLogListView, SystemLogCreateView, SystemLogDetailView,
    log_stats, export_logs, cleanup_logs, log_categories, log_levels
)

urlpatterns = [
    # Log CRUD operations
    path('', SystemLogListView.as_view(), name='system-log-list'),
    path('create/', SystemLogCreateView.as_view(), name='system-log-create'),
    path('<uuid:pk>/', SystemLogDetailView.as_view(), name='system-log-detail'),
    
    # Log statistics and analytics
    path('stats/', log_stats, name='log-stats'),
    
    # Log management
    path('export/', export_logs, name='export-logs'),
    path('cleanup/', cleanup_logs, name='cleanup-logs'),
    
    # Metadata
    path('categories/', log_categories, name='log-categories'),
    path('levels/', log_levels, name='log-levels'),
]
