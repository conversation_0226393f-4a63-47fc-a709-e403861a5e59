import React, { useState } from 'react';
import { Filter, X, Search, Calendar, Tag, User, MapPin } from 'lucide-react';

import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';

interface FilterOption {
  id: string;
  label: string;
  type: 'text' | 'select' | 'date' | 'boolean';
  options?: { value: string; label: string }[];
  icon?: React.ReactNode;
}

interface FilterValue {
  id: string;
  value: string | boolean | Date | null;
}

interface AdvancedFilterProps {
  filterOptions: FilterOption[];
  onFilterChange: (filters: Record<string, FilterValue>) => void;
  activeFilters?: Record<string, FilterValue>;
}

const AdvancedFilter: React.FC<AdvancedFilterProps> = ({ filterOptions,
  onFilterChange,
  activeFilters = {},
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState<string | null>(null);
  const [filterValues, setFilterValues] = useState<Record<string, FilterValue>>(activeFilters || {});

  const handleFilterSelect = (filterId: string) => {
    setSelectedFilter(filterId);
  };

  const handleFilterValueChange = (filterId: string, value: string | boolean | Date | null) => {
    const newFilterValues = { ...filterValues };

    if (value === null || value === '') {
      // Remove the filter if value is empty
      delete newFilterValues[filterId];
    } else {
      // Add or update the filter
      newFilterValues[filterId] = { id: filterId, value };
    }

    setFilterValues(newFilterValues);
  };

  const handleApplyFilters = () => {
    // Filter out any empty values
    const filteredValues = { ...filterValues };
    Object.keys(filteredValues).forEach(key => {
      if (filteredValues[key].value === null || filteredValues[key].value === '') {
        delete filteredValues[key];
      }
    });

    onFilterChange(filteredValues);
    setIsOpen(false);
  };

  const handleClearFilters = () => {
    setFilterValues({});
    onFilterChange({});
    setIsOpen(false);
  };

  const handleRemoveFilter = (filterId: string) => {
    const newFilterValues = { ...filterValues };
    delete newFilterValues[filterId];
    setFilterValues(newFilterValues);
    onFilterChange(newFilterValues);
  };

  const getFilterOption = (id: string) => {
    return filterOptions.find(option => option.id === id);
  };

  const getFilterValueLabel = (filter: FilterValue) => {
    const option = getFilterOption(filter.id);
    if (!option) return '';

    if (option.type === 'select' && option.options) {
      const selectedOption = option.options.find(opt => opt.value === filter.value);
      return selectedOption ? selectedOption.label : String(filter.value);
    }

    if (option.type === 'boolean') {
      return filter.value ? t("admin.yes", "Yes") : t("admin.no", "No");
    }

    if (option.type === 'date' && filter.value instanceof Date) {
      return filter.value.toLocaleDateString();
    }

    return String(filter.value);
  };

  return (
    <div className="mb-6">
      <div className={`flex flex-wrap items-center gap-2 mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className={`px-3 py-1.5 bg-indigo-700 hover:bg-indigo-800 rounded-lg flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
        >
          <Filter size={16} className={`mr-1.5 ${isRTL ? "space-x-reverse" : ""}`} />
          {t("admin.advanced.filters", "Advanced Filters")}
        </button>

        {Object.keys(activeFilters || {}).length > 0 && (
          <button
            onClick={handleClearFilters}
            className="px-3 py-1.5 bg-red-600/30 hover:bg-red-600/50 rounded-lg text-sm"
          >
            {t("admin.clear.all.filters", "Clear All Filters")}
          </button>
        )}

        {Object.values(activeFilters || {}).map(filter => {
          const option = getFilterOption(filter.id);
          if (!option) return null;

          return (
            <div key={filter.id} className={`flex items-center bg-indigo-800/40 rounded-lg px-2 py-1 ${isRTL ? "flex-row-reverse" : ""}`}>
              {option.icon && <span className={`mr-1.5 ${isRTL ? "space-x-reverse" : ""}`}>{option.icon}</span>}
              <span className="text-sm">
                {option.label}: <strong>{getFilterValueLabel(filter)}</strong>
              </span>
              <button
                onClick={() => handleRemoveFilter(filter.id)}
                className={`ml-1.5 text-gray-400 hover:text-white ${isRTL ? "space-x-reverse" : ""}`}
              >
                <X size={14} />
              </button>
            </div>
          );
        })}
      </div>

      {isOpen && (
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 mb-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-indigo-900/30 rounded-lg p-3">
              <h3 className="font-medium mb-2">{t("admin.select.filter", "Select Filter")}</h3>
              <div className="space-y-1 max-h-48 overflow-y-auto">
                {filterOptions.map(option => (
                  <button
                    key={option.id}
                    onClick={() => handleFilterSelect(option.id)}
                    className={`w-full text-left px-2 py-1.5 rounded flex items-center ${
                      selectedFilter === option.id ? 'bg-indigo-700' : 'hover:bg-indigo-800/50'}
                    }`}
                  >
                    {option.icon && <span className={`mr-1.5 ${isRTL ? "space-x-reverse" : ""}`}>{option.icon}</span>}
                    {option.label}
                  </button>
                ))}
              </div>
            </div>

            <div className="bg-indigo-900/30 rounded-lg p-3 md:col-span-2">
              <h3 className="font-medium mb-2">{t("admin.filter.value", "Filter Value")}</h3>
              {selectedFilter ? (
                <div>
                  {(() => {
                    const option = getFilterOption(selectedFilter);
                    if (!option) return null;

                    const currentValue = filterValues[selectedFilter]?.value;

                    switch (option.type) {
                      case 'text':
                        return (
                          <div>
                            <label className="block text-sm text-gray-400 mb-1">{option.label}</label>
                            <input
                              type="text"
                              value={currentValue as string || ''}
                              onChange={(e) => handleFilterValueChange(selectedFilter, e.target.value)}
                              className="w-full bg-indigo-900/50 border border-indigo-700 rounded px-3 py-2"
                              placeholder={`Enter ${option.label.toLowerCase()}`}
                            />
                          </div>
                        );
                      case 'select':
                        return (
                          <div>
                            <label className="block text-sm text-gray-400 mb-1">{option.label}</label>
                            <select
                              value={currentValue as string || ''}
                              onChange={(e) => handleFilterValueChange(selectedFilter, e.target.value)}
                              className="w-full bg-indigo-900/50 border border-indigo-700 rounded px-3 py-2"
                            >
                              <option value="">Select {option.label.toLowerCase()}</option>
                              {option.options?.map(opt => (
                                <option key={opt.value} value={opt.value}>
                                  {opt.label}
                                </option>
                              ))}
                            </select>
                          </div>
                        );
                      case 'date':
                        return (
                          <div>
                            <label className="block text-sm text-gray-400 mb-1">{option.label}</label>
                            <input
                              type="date"
                              value={currentValue instanceof Date ? currentValue.toISOString().split('T')[0] : ''}
                              onChange={(e) => {
                                const date = e.target.value ? new Date(e.target.value) : null;
                                handleFilterValueChange(selectedFilter, date);
                              }}
                              className="w-full bg-indigo-900/50 border border-indigo-700 rounded px-3 py-2"
                            />
                          </div>
                        );
                      case 'boolean':
                        return (
                          <div>
                            <label className="block text-sm text-gray-400 mb-1">{option.label}</label>
                            <div className="space-x-4">
                              <label className={`inline-flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                                <input
                                  type="radio"
                                  checked={currentValue === true}
                                  onChange={() => handleFilterValueChange(selectedFilter, true)}
                                  className={`mr-1.5 ${isRTL ? "space-x-reverse" : ""}`}
                                />
                                {t("admin.yes", "Yes")}
                              </label>
                              <label className={`inline-flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                                <input
                                  type="radio"
                                  checked={currentValue === false}
                                  onChange={() => handleFilterValueChange(selectedFilter, false)}
                                  className={`mr-1.5 ${isRTL ? "space-x-reverse" : ""}`}
                                />
                                {t("admin.no", "No")}
                              </label>
                            </div>
                          </div>
                        );
                      default:
                        return null;
                    }
                  })()}
                </div>
              ) : (
                <div className="text-gray-400 text-center py-4">
                  {t("admin.select.filter.from.list", "Select a filter from the list")}
                </div>
              )}
            </div>
          </div>

          <div className={`flex justify-end mt-4 space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
            <button
              onClick={() => setIsOpen(false)}
              className="px-3 py-1.5 bg-gray-700 hover:bg-gray-600 rounded"
            >
              {t("admin.cancel", "Cancel")}
            </button>
            <button
              onClick={handleApplyFilters}
              className="px-3 py-1.5 bg-indigo-600 hover:bg-indigo-700 rounded"
            >
              {t("admin.apply.filters", "Apply Filters")}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

// Predefined filter options for different content types
export const getPostFilterOptions = (t: (key: string, fallback: string) => string): FilterOption[] => [
  { id: 'title', label: t("admin.title.type.text", "Title"), type: 'text', icon: <Search size={16} /> },
  { id: 'content', label: t("admin.content.type.text", "Content"), type: 'text', icon: <Search size={16} /> },
  {
    id: 'moderation_status',
    label: t("admin.status.type.select", "Status"),
    type: 'select',
    icon: <Tag size={16} />,
    options: [
      { value: 'approved', label: t("admin.approved", "Approved") },
      { value: 'pending', label: t("admin.pending", "Pending") },
      { value: 'rejected', label: t("admin.rejected", "Rejected") },
    ]
  },
  { id: 'author', label: t("admin.author.type.text", "Author"), type: 'text', icon: <User size={16} /> },
  { id: 'created_at', label: t("admin.created.date.type", "Created Date"), type: 'date', icon: <Calendar size={16} /> },
];

export const getEventFilterOptions = (t: (key: string, fallback: string) => string): FilterOption[] => [
  { id: 'title', label: t("admin.title.type.text", "Title"), type: 'text', icon: <Search size={16} /> },
  { id: 'location', label: t("admin.location.type.text", "Location"), type: 'text', icon: <MapPin size={16} /> },
  { id: 'is_virtual', label: t("admin.virtual.event.type", "Virtual Event"), type: 'boolean', icon: <Tag size={16} /> },
  {
    id: 'moderation_status',
    label: t("admin.status.type.select", "Status"),
    type: 'select',
    icon: <Tag size={16} />,
    options: [
      { value: 'approved', label: t("admin.approved", "Approved") },
      { value: 'pending', label: t("admin.pending", "Pending") },
      { value: 'rejected', label: t("admin.rejected", "Rejected") },
    ]
  },
  { id: 'date', label: t("admin.event.date.type", "Event Date"), type: 'date', icon: <Calendar size={16} /> },
  { id: 'organizer', label: t("admin.organizer.type.text", "Organizer"), type: 'text', icon: <User size={16} /> },
];

export default AdvancedFilter;
