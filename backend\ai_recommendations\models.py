from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from incubator.models import BusinessIdea


class AIRecommendation(models.Model):
    """AI-generated recommendations for business ideas"""
    
    RECOMMENDATION_TYPES = (
        ('milestone', 'Milestone'),
        ('goal', 'Goal'),
        ('mentor', 'Mentor'),
        ('resource', 'Resource'),
        ('strategy', 'Strategy'),
        ('general', 'General'),
    )
    
    PRIORITY_CHOICES = (
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    )
    
    business_idea = models.ForeignKey(
        BusinessIdea, 
        on_delete=models.CASCADE, 
        related_name='ai_recommendations'
    )
    recommendation_type = models.CharField(
        max_length=20, 
        choices=RECOMMENDATION_TYPES, 
        default='general'
    )
    title = models.CharField(max_length=200)
    description = models.TextField()
    reasoning = models.TextField(help_text="AI's reasoning for this recommendation")
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES, default='medium')
    action_items = models.TextField(help_text="Specific action items to implement")
    expected_outcome = models.TextField(help_text="Expected outcome of implementing this recommendation")
    relevance_score = models.FloatField(
        default=0.0, 
        help_text="Relevance score from 0-100"
    )
    
    # Implementation tracking
    is_implemented = models.BooleanField(default=False)
    implementation_notes = models.TextField(blank=True, null=True)
    implemented_by = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='implemented_recommendations'
    )
    implemented_at = models.DateTimeField(null=True, blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.title} - {self.business_idea.title}"
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['business_idea', 'recommendation_type']),
            models.Index(fields=['priority', 'relevance_score']),
            models.Index(fields=['is_implemented']),
        ]


class AIRecommendationFeedback(models.Model):
    """User feedback on AI recommendations"""
    
    FEEDBACK_TYPES = (
        ('helpful', 'Helpful'),
        ('not_helpful', 'Not Helpful'),
        ('implemented', 'Implemented'),
        ('not_applicable', 'Not Applicable'),
    )
    
    recommendation = models.ForeignKey(
        AIRecommendation, 
        on_delete=models.CASCADE, 
        related_name='feedback'
    )
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    feedback_type = models.CharField(max_length=20, choices=FEEDBACK_TYPES)
    comments = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"{self.feedback_type} - {self.recommendation.title}"
    
    class Meta:
        unique_together = ('recommendation', 'user')
        ordering = ['-created_at']
