import { useState } from 'react';
import { useAppSelector } from '../../../store/hooks';
import { createMockEvents } from '../utils';
import { Event } from '../../../services/api';
import { 
  useEventsList, 
  useAttendEvent, 
  useUnattendEvent 
} from '../../../hooks/useEvents';
import { QueryParams } from '../../../types/api';

export const useEventsWithQuery = (params?: QueryParams) => {
  const { isAuthenticated, user } = useAppSelector(state => state.auth);
  const [mockData, setMockData] = useState<Event[]>([]);

  // Use React Query hooks
  const { 
    data: eventsData,
    isLoading,
    error,
    refetch
  } = useEventsList(params);

  // Ensure events is always an array
  const events = eventsData?.results || [];

  // Use mutation hooks
  const { 
    attendEvent: attendEventMutation, 
    isAttending, 
    error: attendError 
  } = useAttendEvent();

  const { 
    unattendEvent: unattendEventMutation, 
    isUnattending, 
    error: unattendError 
  } = useUnattendEvent();

  // Handle attend event
  const handleAttendEvent = async (eventId: number) => {
    if (!isAuthenticated) {
      alert('Please log in to attend events');
      return;
    }

    try {
      await attendEventMutation(eventId);
    } catch (err) {
      console.error('Failed to attend event:', err);
      alert('Failed to attend event. Please try again.');
    }
  };

  // Handle unattend event
  const handleUnattendEvent = async (eventId: number) => {
    try {
      await unattendEventMutation(eventId);
    } catch (err) {
      console.error('Failed to unattend event:', err);
      alert('Failed to cancel attendance. Please try again.');
    }
  };

  // Load mock data
  const loadMockData = () => {
    const mockEvents = createMockEvents();
    setMockData(mockEvents);
    return mockEvents;
  };

  return {
    events,
    mockData,
    isLoading,
    isAttending,
    isUnattending,
    error: error || attendError || unattendError,
    isAuthenticated,
    user,
    handleAttendEvent,
    handleUnattendEvent,
    loadMockData,
    refetch
  };
};
