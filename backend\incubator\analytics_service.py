import numpy as np
from datetime import datetime, timedelta
from django.utils import timezone
from django.db.models import Count, Avg, Sum, F, Q, ExpressionWrapper, fields
from django.db.models.functions import <PERSON>runcMonth, TruncWeek, TruncDay
from .models_base import BusinessIdea, ProgressUpdate
from .models_milestone import BusinessMilestone, BusinessGoal, BusinessAnalytics
from .models import MentorshipMatch, MentorshipSession, MentorshipFeedback

class PredictiveAnalyticsService:
    """Service for generating predictive analytics for business ideas"""
    
    @staticmethod
    def predict_milestone_completion(business_idea_id):
        """
        Predict milestone completion dates based on historical data
        
        Args:
            business_idea_id: ID of the business idea
            
        Returns:
            dict: Predicted completion dates for incomplete milestones
        """
        try:
            # Get business idea and its milestones
            business_idea = BusinessIdea.objects.get(id=business_idea_id)
            completed_milestones = BusinessMilestone.objects.filter(
                business_idea=business_idea,
                status='completed'
            ).order_by('created_at')
            
            incomplete_milestones = BusinessMilestone.objects.filter(
                business_idea=business_idea
            ).exclude(status='completed').order_by('due_date')
            
            # If no completed milestones, use average completion time from similar ideas
            if completed_milestones.count() < 2:
                # Find similar ideas (same stage or industry)
                similar_ideas = BusinessIdea.objects.filter(
                    Q(current_stage=business_idea.current_stage) |
                    Q(tags__in=business_idea.tags.all())
                ).exclude(id=business_idea.id).distinct()
                
                # Get average completion time for milestones in similar ideas
                similar_completed_milestones = BusinessMilestone.objects.filter(
                    business_idea__in=similar_ideas,
                    status='completed',
                    completion_date__isnull=False,
                    created_at__isnull=False
                )
                
                if similar_completed_milestones.exists():
                    # Calculate average days to complete
                    avg_completion_days = similar_completed_milestones.annotate(
                        days_to_complete=ExpressionWrapper(
                            F('completion_date') - F('created_at'),
                            output_field=fields.DurationField()
                        )
                    ).aggregate(avg_days=Avg('days_to_complete'))['avg_days']
                    
                    if avg_completion_days:
                        avg_days = avg_completion_days.days
                    else:
                        avg_days = 30  # Default to 30 days if no data
                else:
                    avg_days = 30  # Default to 30 days if no data
                
                # Predict completion dates for incomplete milestones
                predictions = {}
                for milestone in incomplete_milestones:
                    if milestone.created_at:
                        predicted_date = milestone.created_at + timedelta(days=avg_days)
                        predictions[milestone.id] = {
                            'milestone_id': milestone.id,
                            'title': milestone.title,
                            'predicted_completion_date': predicted_date,
                            'confidence': 'low',  # Low confidence since using average from similar ideas
                            'days_remaining': (predicted_date - timezone.now()).days
                        }
                
                return predictions
            
            # Calculate average completion time for this business idea's milestones
            completion_times = []
            for milestone in completed_milestones:
                if milestone.completion_date and milestone.created_at:
                    days_to_complete = (milestone.completion_date - milestone.created_at).days
                    completion_times.append(days_to_complete)
            
            if completion_times:
                avg_completion_days = sum(completion_times) / len(completion_times)
                # Calculate standard deviation for confidence interval
                std_dev = np.std(completion_times) if len(completion_times) > 1 else avg_completion_days / 2
            else:
                avg_completion_days = 30  # Default to 30 days if no data
                std_dev = 15  # Default standard deviation
            
            # Predict completion dates for incomplete milestones
            predictions = {}
            for milestone in incomplete_milestones:
                if milestone.created_at:
                    predicted_date = milestone.created_at + timedelta(days=int(avg_completion_days))
                    
                    # Determine confidence level based on standard deviation
                    if std_dev < avg_completion_days / 4:
                        confidence = 'high'
                    elif std_dev < avg_completion_days / 2:
                        confidence = 'medium'
                    else:
                        confidence = 'low'
                    
                    predictions[milestone.id] = {
                        'milestone_id': milestone.id,
                        'title': milestone.title,
                        'predicted_completion_date': predicted_date,
                        'confidence': confidence,
                        'days_remaining': (predicted_date - timezone.now()).days
                    }
            
            return predictions
        
        except Exception as e:
            print(f"Error predicting milestone completion: {e}")
            return {}
    
    @staticmethod
    def predict_growth_trajectory(business_idea_id, months_ahead=6):
        """
        Predict growth trajectory for a business idea
        
        Args:
            business_idea_id: ID of the business idea
            months_ahead: Number of months to predict ahead
            
        Returns:
            dict: Predicted growth metrics
        """
        try:
            # Get business idea and its progress updates
            business_idea = BusinessIdea.objects.get(id=business_idea_id)
            progress_updates = ProgressUpdate.objects.filter(
                business_idea=business_idea
            ).order_by('created_at')
            
            # Get current date and calculate date range
            now = timezone.now()
            start_date = now - timedelta(days=180)  # Look back 6 months
            end_date = now + timedelta(days=30 * months_ahead)  # Look ahead specified months
            
            # If not enough progress updates, use data from similar ideas
            if progress_updates.count() < 3:
                # Find similar ideas (same stage or industry)
                similar_ideas = BusinessIdea.objects.filter(
                    Q(current_stage=business_idea.current_stage) |
                    Q(tags__in=business_idea.tags.all())
                ).exclude(id=business_idea.id).distinct()
                
                # Get average growth metrics from similar ideas
                similar_analytics = BusinessAnalytics.objects.filter(
                    business_idea__in=similar_ideas
                )
                
                if similar_analytics.exists():
                    avg_progress_rate = similar_analytics.aggregate(avg=Avg('progress_rate'))['avg'] or 1.0
                    avg_milestone_rate = similar_analytics.aggregate(avg=Avg('milestone_completion_rate'))['avg'] or 50.0
                    avg_goal_rate = similar_analytics.aggregate(avg=Avg('goal_achievement_rate'))['avg'] or 40.0
                else:
                    # Default values if no similar ideas
                    avg_progress_rate = 1.0
                    avg_milestone_rate = 50.0
                    avg_goal_rate = 40.0
                
                # Generate prediction data points
                months = []
                progress_values = []
                milestone_values = []
                goal_values = []
                
                # Get current values or use defaults
                try:
                    analytics = BusinessAnalytics.objects.get(business_idea=business_idea)
                    current_progress = analytics.progress_rate
                    current_milestone = analytics.milestone_completion_rate
                    current_goal = analytics.goal_achievement_rate
                except BusinessAnalytics.DoesNotExist:
                    current_progress = 0.5
                    current_milestone = 10.0
                    current_goal = 5.0
                
                # Generate prediction for each month
                for i in range(-6, months_ahead + 1):
                    month_date = now + timedelta(days=30 * i)
                    months.append(month_date.strftime('%Y-%m'))
                    
                    if i < 0:
                        # Historical data (use actual if available, otherwise estimate)
                        month_updates = progress_updates.filter(
                            created_at__year=month_date.year,
                            created_at__month=month_date.month
                        ).count()
                        
                        if month_updates > 0:
                            progress_values.append(month_updates)
                        else:
                            progress_values.append(max(0, current_progress * (1 + i/12)))
                        
                        milestone_values.append(max(0, current_milestone * (1 + i/12)))
                        goal_values.append(max(0, current_goal * (1 + i/12)))
                    else:
                        # Future predictions
                        progress_factor = min(2.0, 1.0 + (i * 0.1))  # Cap growth at 2x
                        milestone_factor = min(1.5, 1.0 + (i * 0.05))
                        goal_factor = min(1.5, 1.0 + (i * 0.05))
                        
                        progress_values.append(current_progress * progress_factor)
                        milestone_values.append(min(100, current_milestone * milestone_factor))
                        goal_values.append(min(100, current_goal * goal_factor))
                
                return {
                    'months': months,
                    'progress_rate': progress_values,
                    'milestone_completion': milestone_values,
                    'goal_achievement': goal_values,
                    'confidence': 'low'  # Low confidence since using similar ideas
                }
            
            # Calculate monthly progress updates
            monthly_updates = progress_updates.annotate(
                month=TruncMonth('created_at')
            ).values('month').annotate(count=Count('id')).order_by('month')
            
            # Calculate monthly milestone completions
            monthly_milestones = BusinessMilestone.objects.filter(
                business_idea=business_idea,
                status='completed',
                completion_date__isnull=False
            ).annotate(
                month=TruncMonth('completion_date')
            ).values('month').annotate(count=Count('id')).order_by('month')
            
            # Calculate monthly goal achievements
            monthly_goals = BusinessGoal.objects.filter(
                business_idea=business_idea,
                status='achieved',
                achievement_date__isnull=False
            ).annotate(
                month=TruncMonth('achievement_date')
            ).values('month').annotate(count=Count('id')).order_by('month')
            
            # Prepare data for prediction
            months = []
            progress_values = []
            milestone_values = []
            goal_values = []
            
            # Get current analytics
            try:
                analytics = BusinessAnalytics.objects.get(business_idea=business_idea)
                current_progress = analytics.progress_rate
                current_milestone = analytics.milestone_completion_rate
                current_goal = analytics.goal_achievement_rate
            except BusinessAnalytics.DoesNotExist:
                current_progress = 0.5
                current_milestone = 10.0
                current_goal = 5.0
            
            # Generate month range
            month_range = []
            current_date = start_date
            while current_date <= end_date:
                month_range.append(current_date.replace(day=1))
                # Move to next month
                if current_date.month == 12:
                    current_date = current_date.replace(year=current_date.year + 1, month=1)
                else:
                    current_date = current_date.replace(month=current_date.month + 1)
            
            # Fill in historical data and predictions
            for month_date in month_range:
                month_str = month_date.strftime('%Y-%m')
                months.append(month_str)
                
                # Check if this is historical or future
                if month_date < now:
                    # Historical - use actual data if available
                    month_progress = next((item['count'] for item in monthly_updates if item['month'].strftime('%Y-%m') == month_str), 0)
                    month_milestone = next((item['count'] for item in monthly_milestones if item['month'].strftime('%Y-%m') == month_str), 0)
                    month_goal = next((item['count'] for item in monthly_goals if item['month'].strftime('%Y-%m') == month_str), 0)
                    
                    progress_values.append(month_progress)
                    milestone_values.append(month_milestone)
                    goal_values.append(month_goal)
                else:
                    # Future - predict based on trend
                    months_from_now = ((month_date.year - now.year) * 12 + month_date.month - now.month)
                    
                    # Simple growth model with diminishing returns
                    growth_factor = min(2.0, 1.0 + (months_from_now * 0.08))
                    milestone_factor = min(1.5, 1.0 + (months_from_now * 0.05))
                    goal_factor = min(1.5, 1.0 + (months_from_now * 0.05))
                    
                    predicted_progress = current_progress * growth_factor
                    predicted_milestone = min(100, current_milestone * milestone_factor)
                    predicted_goal = min(100, current_goal * goal_factor)
                    
                    progress_values.append(predicted_progress)
                    milestone_values.append(predicted_milestone)
                    goal_values.append(predicted_goal)
            
            # Determine confidence level based on data quality
            if progress_updates.count() > 10:
                confidence = 'high'
            elif progress_updates.count() > 5:
                confidence = 'medium'
            else:
                confidence = 'low'
            
            return {
                'months': months,
                'progress_rate': progress_values,
                'milestone_completion': milestone_values,
                'goal_achievement': goal_values,
                'confidence': confidence
            }
        
        except Exception as e:
            print(f"Error predicting growth trajectory: {e}")
            return {
                'months': [],
                'progress_rate': [],
                'milestone_completion': [],
                'goal_achievement': [],
                'confidence': 'none'
            }
    
    @staticmethod
    def get_comparative_analytics(business_idea_id):
        """
        Get comparative analytics for a business idea against similar ideas
        
        Args:
            business_idea_id: ID of the business idea
            
        Returns:
            dict: Comparative analytics data
        """
        try:
            # Get business idea
            business_idea = BusinessIdea.objects.get(id=business_idea_id)
            
            # Find similar ideas (same stage or industry)
            similar_ideas = BusinessIdea.objects.filter(
                Q(current_stage=business_idea.current_stage) |
                Q(tags__in=business_idea.tags.all())
            ).exclude(id=business_idea.id).distinct()[:10]
            
            # Get analytics for the business idea
            try:
                analytics = BusinessAnalytics.objects.get(business_idea=business_idea)
            except BusinessAnalytics.DoesNotExist:
                # Create analytics if it doesn't exist
                analytics = BusinessAnalytics.objects.create(business_idea=business_idea)
            
            # Get analytics for similar ideas
            similar_analytics = []
            for idea in similar_ideas:
                try:
                    idea_analytics = BusinessAnalytics.objects.get(business_idea=idea)
                    similar_analytics.append({
                        'id': idea.id,
                        'title': idea.title,
                        'current_stage': idea.current_stage,
                        'progress_rate': idea_analytics.progress_rate,
                        'milestone_completion_rate': idea_analytics.milestone_completion_rate,
                        'goal_achievement_rate': idea_analytics.goal_achievement_rate,
                        'team_size': idea_analytics.team_size,
                        'mentor_engagement': idea_analytics.mentor_engagement
                    })
                except BusinessAnalytics.DoesNotExist:
                    # Skip ideas without analytics
                    continue
            
            # Calculate averages for similar ideas
            if similar_analytics:
                avg_progress_rate = sum(item['progress_rate'] for item in similar_analytics) / len(similar_analytics)
                avg_milestone_rate = sum(item['milestone_completion_rate'] for item in similar_analytics) / len(similar_analytics)
                avg_goal_rate = sum(item['goal_achievement_rate'] for item in similar_analytics) / len(similar_analytics)
                avg_team_size = sum(item['team_size'] for item in similar_analytics) / len(similar_analytics)
                avg_mentor_engagement = sum(item['mentor_engagement'] for item in similar_analytics) / len(similar_analytics)
            else:
                # Default values if no similar ideas with analytics
                avg_progress_rate = 1.0
                avg_milestone_rate = 50.0
                avg_goal_rate = 40.0
                avg_team_size = 2.0
                avg_mentor_engagement = 30.0
            
            # Calculate percentile ranks
            percentile_progress = sum(1 for item in similar_analytics if item['progress_rate'] < analytics.progress_rate) / max(1, len(similar_analytics)) * 100
            percentile_milestone = sum(1 for item in similar_analytics if item['milestone_completion_rate'] < analytics.milestone_completion_rate) / max(1, len(similar_analytics)) * 100
            percentile_goal = sum(1 for item in similar_analytics if item['goal_achievement_rate'] < analytics.goal_achievement_rate) / max(1, len(similar_analytics)) * 100
            percentile_team = sum(1 for item in similar_analytics if item['team_size'] < analytics.team_size) / max(1, len(similar_analytics)) * 100
            percentile_mentor = sum(1 for item in similar_analytics if item['mentor_engagement'] < analytics.mentor_engagement) / max(1, len(similar_analytics)) * 100
            
            return {
                'similar_ideas': similar_analytics,
                'averages': {
                    'progress_rate': avg_progress_rate,
                    'milestone_completion_rate': avg_milestone_rate,
                    'goal_achievement_rate': avg_goal_rate,
                    'team_size': avg_team_size,
                    'mentor_engagement': avg_mentor_engagement
                },
                'percentiles': {
                    'progress_rate': percentile_progress,
                    'milestone_completion_rate': percentile_milestone,
                    'goal_achievement_rate': percentile_goal,
                    'team_size': percentile_team,
                    'mentor_engagement': percentile_mentor
                }
            }
        
        except Exception as e:
            print(f"Error getting comparative analytics: {e}")
            return {
                'similar_ideas': [],
                'averages': {},
                'percentiles': {}
            }
