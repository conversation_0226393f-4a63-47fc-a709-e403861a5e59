{"operations": {"create": "Create", "read": "Read", "update": "Update", "delete": "Delete", "bulkDelete": "Bulk Delete", "bulkUpdate": "Bulk Update", "search": "Search", "filter": "Filter", "sort": "Sort", "export": "Export", "import": "Import"}, "messages": {"creating": "Creating...", "updating": "Updating...", "deleting": "Deleting...", "loading": "Loading...", "saving": "Saving...", "processing": "Processing...", "success": {"created": "Successfully created", "updated": "Successfully updated", "deleted": "Successfully deleted", "saved": "Successfully saved", "imported": "Successfully imported", "exported": "Successfully exported"}, "error": {"create": "Failed to create item", "update": "Failed to update item", "delete": "Failed to delete item", "load": "Failed to load data", "save": "Failed to save changes", "network": "Network error occurred", "validation": "Validation failed", "unauthorized": "Unauthorized access", "notFound": "Item not found", "conflict": "Conflict occurred"}, "confirm": {"delete": "Are you sure you want to delete this item?", "bulkDelete": "Are you sure you want to delete {{count}} items?", "unsavedChanges": "You have unsaved changes. Are you sure you want to leave?", "overwrite": "This will overwrite existing data. Continue?"}}, "labels": {"selectAll": "Select All", "deselectAll": "Deselect All", "selectedItems": "{{count}} items selected", "noItemsSelected": "No items selected", "totalItems": "{{count}} total items", "itemsPerPage": "Items per page", "page": "Page", "of": "of", "showingResults": "Showing {{start}} to {{end}} of {{total}} results", "noResults": "No results found", "emptyState": "No data available", "searchPlaceholder": "Search...", "filterBy": "Filter by", "sortBy": "Sort by", "ascending": "Ascending", "descending": "Descending", "actions": "Actions", "moreActions": "More Actions"}, "pagination": {"first": "First", "previous": "Previous", "next": "Next", "last": "Last", "goToPage": "Go to page", "itemsPerPage": "Items per page", "showAll": "Show All"}, "validation": {"required": "This field is required", "minLength": "Minimum length is {{min}} characters", "maxLength": "Maximum length is {{max}} characters", "email": "Please enter a valid email address", "url": "Please enter a valid URL", "number": "Please enter a valid number", "integer": "Please enter a valid integer", "positive": "Value must be positive", "min": "Value must be at least {{min}}", "max": "Value must be at most {{max}}", "pattern": "Invalid format", "unique": "This value already exists", "custom": "Validation failed"}, "filters": {"all": "All", "active": "Active", "inactive": "Inactive", "draft": "Draft", "published": "Published", "archived": "Archived", "recent": "Recent", "popular": "Popular", "featured": "Featured", "dateRange": "Date Range", "category": "Category", "status": "Status", "author": "Author", "tags": "Tags", "clearFilters": "Clear Filters", "applyFilters": "Apply Filters"}, "bulk": {"selectMode": "Select Mode", "exitSelectMode": "Exit Select Mode", "bulkActions": "Bulk Actions", "deleteSelected": "Delete Selected", "updateSelected": "Update Selected", "exportSelected": "Export Selected", "moveSelected": "Move Selected", "copySelected": "Copy Selected", "archiveSelected": "Archive Selected", "publishSelected": "Publish Selected", "unpublishSelected": "Unpublish Selected"}}