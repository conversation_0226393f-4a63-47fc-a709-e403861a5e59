import { useEffect, useRef } from 'react';
import { createPerformanceMonitor, trackUserInteraction } from '../utils/analytics';

/**
 * Hook to monitor component performance
 * 
 * @param componentName Name of the component to monitor
 * @param options Additional options for monitoring
 * @returns Object with tracking functions
 */
export const usePerformanceMonitor = (
  componentName: string,
  options: {
    trackMounts?: boolean;
    trackUpdates?: boolean;
    trackUnmounts?: boolean;
    metadata?: Record<string, any>;
  } = {}
) => {
  const {
    trackMounts = true,
    trackUpdates = false,
    trackUnmounts = true,
    metadata = {},
  } = options;

  // Create a ref to store the monitor
  const monitorRef = useRef(createPerformanceMonitor(componentName));
  
  // Track mount and unmount
  useEffect(() => {
    if (trackMounts) {
      monitorRef.current.trackMount(metadata);
    }
    
    return () => {
      if (trackUnmounts) {
        monitorRef.current.trackUnmount(metadata);
      }
    };
  }, [componentName, trackMounts, trackUnmounts, metadata]);
  
  // Function to track user interactions
  const trackInteraction = (action: string, interactionMetadata?: Record<string, any>) => {
    trackUserInteraction(`${componentName}:${action}`, {
      ...metadata,
      ...interactionMetadata,
    });
  };
  
  // Function to manually track updates
  const trackUpdate = (updateMetadata?: Record<string, any>) => {
    if (trackUpdates) {
      monitorRef.current.trackUpdate({
        ...metadata,
        ...updateMetadata,
      });
    }
  };
  
  return {
    trackInteraction,
    trackUpdate,
  };
};

export default usePerformanceMonitor;
