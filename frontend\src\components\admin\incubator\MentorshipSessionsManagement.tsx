import React, { useState, useEffect } from 'react';
import { Search, Calendar, Clock, Video, MapPin, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { DashboardLayout } from '../dashboard';
import { MentorshipSession, mentorshipSessionsAPI } from '../../../services/incubatorApi';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';

const MentorshipSessionsManagement: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  // State management
  const [sessions, setSessions] = useState<MentorshipSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch mentorship sessions
  const fetchSessions = async () => {
    try {
      setLoading(true);
      const data = await mentorshipSessionsAPI.getMentorshipSessions();
      setSessions(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error fetching mentorship sessions:', error);
      setSessions([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSessions();
  }, []);

  // Filter sessions based on search
  const filteredSessions = sessions.filter(session => {
    return session.mentorship_match.mentee.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      session.mentorship_match.mentee.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      session.mentorship_match.mentor_profile.user.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      session.mentorship_match.mentor_profile.user.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      session.status.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (session.topic && session.topic.toLowerCase().includes(searchTerm.toLowerCase()));
  });

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Get status color and icon
  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'scheduled':
        return {
          color: 'bg-blue-500/20 text-blue-300',
          icon: <Calendar size={16} />,
          label: t('session.status.scheduled', 'Scheduled')
        };
      case 'completed':
        return {
          color: 'bg-green-500/20 text-green-300',
          icon: <CheckCircle size={16} />,
          label: t('session.status.completed', 'Completed')
        };
      case 'cancelled':
        return {
          color: 'bg-red-500/20 text-red-300',
          icon: <XCircle size={16} />,
          label: t('session.status.cancelled', 'Cancelled')
        };
      case 'in_progress':
        return {
          color: 'bg-yellow-500/20 text-yellow-300',
          icon: <AlertCircle size={16} />,
          label: t('session.status.in_progress', 'In Progress')
        };
      default:
        return {
          color: 'bg-gray-500/20 text-gray-300',
          icon: <Clock size={16} />,
          label: status
        };
    }
  };

  // Format date and time
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };
  };

  // Calculate session duration
  const calculateDuration = (startTime: string, endTime: string) => {
    const start = new Date(startTime);
    const end = new Date(endTime);
    const diffMs = end.getTime() - start.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 60) {
      return `${diffMins} ${t('common.minutes', 'min')}`;
    } else {
      const hours = Math.floor(diffMins / 60);
      const mins = diffMins % 60;
      return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
    }
  };

  return (
    <DashboardLayout currentPage="incubator">
      {/* Header */}
      <div className={`mb-8 ${isRTL ? "text-right" : ""}`}>
        <h1 className="text-2xl font-bold text-white">{t('admin.mentorship.sessions.management', 'Mentorship Sessions Management')}</h1>
        <div className="text-gray-400 mt-1">{t('admin.mentorship.sessions.description', 'View and manage mentorship sessions')}</div>
      </div>

      {/* Search */}
      <div className="mb-6">
        <div className="relative">
          <Search size={20} className={`absolute top-3 text-gray-400 ${isRTL ? "right-3" : "left-3"}`} />
          <input
            type="text"
            placeholder={t('admin.search.sessions', 'Search mentorship sessions...')}
            value={searchTerm}
            onChange={handleSearchChange}
            className={`w-full bg-indigo-900/50 border border-indigo-800 rounded-lg py-2.5 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 ${isRTL ? "pr-10 pl-4" : "pl-10 pr-4"}`}
          />
        </div>
      </div>

      {/* Sessions List */}
      {loading ? (
        <div className="flex justify-center py-12">
          <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      ) : filteredSessions.length > 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredSessions.map((session) => {
            const statusDisplay = getStatusDisplay(session.status);
            const startDateTime = formatDateTime(session.start_time);
            const endDateTime = formatDateTime(session.end_time);
            const duration = calculateDuration(session.start_time, session.end_time);
            
            return (
              <div key={session.id} className="bg-indigo-900/30 backdrop-blur-sm rounded-xl overflow-hidden border border-indigo-800/50 hover:border-purple-500/50 transition-all duration-300 hover:shadow-glow">
                <div className="p-6">
                  <div className={`flex justify-between items-start mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className="flex-1">
                      <div className={`flex items-center space-x-2 mb-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 ${statusDisplay.color} ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                          {statusDisplay.icon}
                          <span>{statusDisplay.label}</span>
                        </span>
                      </div>
                      
                      {session.topic && (
                        <h3 className="text-lg font-semibold text-white mb-2 line-clamp-2">{session.topic}</h3>
                      )}
                    </div>
                  </div>

                  {/* Participants */}
                  <div className="space-y-3 mb-4">
                    <div>
                      <h4 className="text-sm font-medium text-gray-300 mb-2">{t('session.participants', 'Participants')}</h4>
                      <div className="space-y-2">
                        {/* Mentor */}
                        <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                          <div className="w-8 h-8 rounded-full bg-blue-700 flex items-center justify-center text-white text-xs font-bold">
                            {session.mentorship_match.mentor_profile.user.first_name.charAt(0)}{session.mentorship_match.mentor_profile.user.last_name.charAt(0)}
                          </div>
                          <div>
                            <p className="text-white text-sm font-medium">
                              {session.mentorship_match.mentor_profile.user.first_name} {session.mentorship_match.mentor_profile.user.last_name}
                            </p>
                            <p className="text-gray-400 text-xs">{t('session.mentor', 'Mentor')}</p>
                          </div>
                        </div>
                        
                        {/* Mentee */}
                        <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                          <div className="w-8 h-8 rounded-full bg-purple-700 flex items-center justify-center text-white text-xs font-bold">
                            {session.mentorship_match.mentee.first_name.charAt(0)}{session.mentorship_match.mentee.last_name.charAt(0)}
                          </div>
                          <div>
                            <p className="text-white text-sm font-medium">
                              {session.mentorship_match.mentee.first_name} {session.mentorship_match.mentee.last_name}
                            </p>
                            <p className="text-gray-400 text-xs">{t('session.mentee', 'Mentee')}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Session Details */}
                  <div className="space-y-3">
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <div className={`flex items-center space-x-2 text-sm ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                          <Calendar size={16} className="text-blue-400" />
                          <span className="text-gray-300">{t('session.date', 'Date')}</span>
                        </div>
                        <p className="text-white text-sm mt-1">{startDateTime.date}</p>
                      </div>

                      <div>
                        <div className={`flex items-center space-x-2 text-sm ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                          <Clock size={16} className="text-green-400" />
                          <span className="text-gray-300">{t('session.time', 'Time')}</span>
                        </div>
                        <p className="text-white text-sm mt-1">{startDateTime.time}</p>
                      </div>

                      <div>
                        <div className={`flex items-center space-x-2 text-sm ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                          <Clock size={16} className="text-purple-400" />
                          <span className="text-gray-300">{t('session.duration', 'Duration')}</span>
                        </div>
                        <p className="text-white text-sm mt-1">{duration}</p>
                      </div>

                      <div>
                        <div className={`flex items-center space-x-2 text-sm ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                          {session.session_type === 'video_call' ? (
                            <Video size={16} className="text-red-400" />
                          ) : (
                            <MapPin size={16} className="text-orange-400" />
                          )}
                          <span className="text-gray-300">{t('session.type', 'Type')}</span>
                        </div>
                        <p className="text-white text-sm mt-1">
                          {session.session_type === 'video_call' 
                            ? t('session.type.video', 'Video Call')
                            : t('session.type.in_person', 'In Person')
                          }
                        </p>
                      </div>
                    </div>

                    {session.location && (
                      <div>
                        <div className={`flex items-center space-x-2 text-sm ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                          <MapPin size={16} className="text-orange-400" />
                          <span className="text-gray-300">{t('session.location', 'Location')}</span>
                        </div>
                        <p className="text-white text-sm mt-1 line-clamp-1">{session.location}</p>
                      </div>
                    )}

                    {session.agenda && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-300 mb-1">{t('session.agenda', 'Agenda')}</h4>
                        <p className="text-gray-300 text-sm line-clamp-2">{session.agenda}</p>
                      </div>
                    )}

                    {session.notes && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-300 mb-1">{t('session.notes', 'Notes')}</h4>
                        <p className="text-gray-300 text-sm line-clamp-2">{session.notes}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        <div className="text-center py-12">
          <Calendar size={48} className="mx-auto text-gray-600 mb-4" />
          <h3 className="text-lg font-medium text-gray-400 mb-2">
            {searchTerm 
              ? t('admin.no.sessions.found', 'No mentorship sessions found')
              : t('admin.no.sessions', 'No mentorship sessions yet')
            }
          </h3>
          <p className="text-gray-500 mb-4">
            {searchTerm
              ? t('admin.try.different.search', 'Try adjusting your search')
              : t('admin.no.sessions.description', 'Mentorship sessions will appear here when scheduled')
            }
          </p>
        </div>
      )}
    </DashboardLayout>
  );
};

export default MentorshipSessionsManagement;
