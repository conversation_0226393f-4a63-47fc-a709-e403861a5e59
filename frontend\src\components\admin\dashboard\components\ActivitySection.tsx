import React from 'react';
import { useAppSelector } from '../../../../store/hooks';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../hooks/useLanguage';
import { RecentActivity } from '../../../../types/admin';
interface ActivitySectionProps {
  activities: RecentActivity[];
  isLoading: boolean;
}

/**
 * ActivitySection component displays recent user activities
 */
const ActivitySection: React.FC<ActivitySectionProps> = ({ activities, isLoading  }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { language } = useAppSelector(state => state.language);

  // Format the timestamp
  const formatTimeAgo = (timestamp: string) => {
    const activityDate = new Date(timestamp);
    const now = new Date();
    const diffHours = Math.floor((now.getTime() - activityDate.getTime()) / (1000 * 60 * 60));

    if (diffHours < 1) {
      return t('admin.justNow');
    } else if (diffHours < 24) {
      return diffHours === 1 ? t('admin.hourAgo') : t('admin.hoursAgo', { hours: diffHours });
    } else {
      return t('admin.yesterday');
    }
  };

  // Get border color based on activity type
  const getBorderColor = (type: string) => {
    switch (type) {
      case 'event_created':
        return 'border-blue-500';
      case 'resource_shared':
        return 'border-green-500';
      case 'post_created':
        return 'border-orange-500';
      default:
        return 'border-purple-500';
    }
  };

  // Get activity message based on type
  const getActivityMessage = (activity: RecentActivity) => {
    switch (activity.type) {
      case 'user_joined':
        return <span dangerouslySetInnerHTML={{ __html: t('admin.newUserJoined', { user: activity.user }) }} />;
      case 'event_created':
        return <span dangerouslySetInnerHTML={{ __html: t('admin.userCreatedEvent', { user: activity.user }) }} />;
      case 'resource_shared':
        return <span dangerouslySetInnerHTML={{ __html: t('admin.userSharedResource', { user: activity.user }) }} />;
      case 'post_created':
        return <span dangerouslySetInnerHTML={{ __html: t('admin.userPostedCommunity', { user: activity.user }) }} />;
      default:
        return null;
    }
  };

  return (
    <div className={`card p-6 shadow-lg h-full flex flex-col min-h-[500px] ${isRTL ? "flex-row-reverse" : ""}`}>
      <h2 className="text-xl font-semibold mb-4">{t('admin.recentActivity')}</h2>
      <div className={`flex-1 overflow-hidden min-h-0 ${isRTL ? "flex-row-reverse" : ""}`}>
        {isLoading ? (
          <div className={`flex justify-center py-8 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className="w-8 h-8 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
          </div>
        ) : activities.length > 0 ? (
          <div className="h-full overflow-y-auto space-y-3 pr-2 scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-200 dark:scrollbar-thumb-gray-600 dark:scrollbar-track-gray-800">
            {activities.map((activity, index) => (
              <div
                key={index}
                className={`${language === 'ar' ? 'border-r-4 pr-4' : 'border-l-4 pl-4'} ${getBorderColor(activity.type)} py-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-700/50`}
              >
                <div className="text-sm">
                  {getActivityMessage(activity)}
                </div>
                <p className="text-xs text-gray-400 mt-1">{formatTimeAgo(activity.timestamp)}</p>
              </div>
            ))}
          </div>
        ) : (
          <div className={`flex items-center justify-center h-full ${isRTL ? "flex-row-reverse" : ""}`}>
            <p className="text-gray-400 text-center">{t('admin.noRecentActivity')}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ActivitySection;
