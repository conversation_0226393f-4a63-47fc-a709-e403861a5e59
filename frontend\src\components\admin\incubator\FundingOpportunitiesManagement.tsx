import React, { useState, useEffect } from 'react';
import { Search, Edit, Trash2, Plus, DollarSign, Calendar, Building, Eye, X, Check, Filter } from 'lucide-react';
import { DashboardLayout } from '../dashboard';
import { FundingOpportunity, fundingOpportunitiesAPI } from '../../../services/incubatorApi';
import { useAppSelector } from '../../../store/hooks';
import { AdvancedFilter, BulkActions } from '../common';
import { FilterValue } from '../common/AdvancedFilter';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';

interface FundingOpportunityFormData {
  title: string;
  description: string;
  funding_type: string;
  amount_min: string;
  amount_max: string;
  application_deadline: string;
  requirements: string;
  application_process: string;
  contact_email: string;
  website_url: string;
  is_active: boolean;
}

const FundingOpportunitiesManagement: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { user } = useAppSelector(state => state.auth);
  
  // State management
  const [opportunities, setOpportunities] = useState<FundingOpportunity[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedOpportunity, setSelectedOpportunity] = useState<FundingOpportunity | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [formSuccess, setFormSuccess] = useState<string | null>(null);
  const [formSubmitting, setFormSubmitting] = useState(false);
  const [selectedItems, setSelectedItems] = useState<number[]>([]);
  const [filters, setFilters] = useState<FilterValue[]>([]);

  // Form data
  const [formData, setFormData] = useState<FundingOpportunityFormData>({
    title: '',
    description: '',
    funding_type: 'grant',
    amount_min: '',
    amount_max: '',
    application_deadline: '',
    requirements: '',
    application_process: '',
    contact_email: '',
    website_url: '',
    is_active: true,
  });

  // Fetch funding opportunities
  const fetchOpportunities = async () => {
    try {
      setLoading(true);
      const data = await fundingOpportunitiesAPI.getFundingOpportunities();
      setOpportunities(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error fetching funding opportunities:', error);
      setOpportunities([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOpportunities();
  }, []);

  // Filter opportunities based on search and filters
  const filteredOpportunities = opportunities.filter(opportunity => {
    const matchesSearch = opportunity.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         opportunity.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         opportunity.funding_type.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilters = filters.every(filter => {
      switch (filter.field) {
        case 'funding_type':
          return opportunity.funding_type === filter.value;
        case 'is_active':
          return opportunity.is_active === (filter.value === 'true');
        default:
          return true;
      }
    });

    return matchesSearch && matchesFilters;
  });

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Handle form input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  // Open create modal
  const openCreateModal = () => {
    setFormData({
      title: '',
      description: '',
      funding_type: 'grant',
      amount_min: '',
      amount_max: '',
      application_deadline: '',
      requirements: '',
      application_process: '',
      contact_email: '',
      website_url: '',
      is_active: true,
    });
    setFormError(null);
    setFormSuccess(null);
    setIsCreateModalOpen(true);
  };

  // Open edit modal
  const openEditModal = (opportunity: FundingOpportunity) => {
    setSelectedOpportunity(opportunity);
    setFormData({
      title: opportunity.title,
      description: opportunity.description,
      funding_type: opportunity.funding_type,
      amount_min: opportunity.amount_min?.toString() || '',
      amount_max: opportunity.amount_max?.toString() || '',
      application_deadline: opportunity.application_deadline || '',
      requirements: opportunity.requirements || '',
      application_process: opportunity.application_process || '',
      contact_email: opportunity.contact_email || '',
      website_url: opportunity.website_url || '',
      is_active: opportunity.is_active,
    });
    setFormError(null);
    setFormSuccess(null);
    setIsEditModalOpen(true);
  };

  // Open view modal
  const openViewModal = (opportunity: FundingOpportunity) => {
    setSelectedOpportunity(opportunity);
    setIsViewModalOpen(true);
  };

  // Open delete modal
  const openDeleteModal = (opportunity: FundingOpportunity) => {
    setSelectedOpportunity(opportunity);
    setIsDeleteModalOpen(true);
  };

  // Handle create funding opportunity
  const handleCreateOpportunity = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    setFormSubmitting(true);
    setFormError(null);

    try {
      const opportunityData = {
        ...formData,
        provider_id: user.id,
        amount_min: formData.amount_min ? parseFloat(formData.amount_min) : null,
        amount_max: formData.amount_max ? parseFloat(formData.amount_max) : null,
      };

      await fundingOpportunitiesAPI.createFundingOpportunity(opportunityData);
      setFormSuccess(t('admin.funding.opportunity.created', 'Funding opportunity created successfully'));
      fetchOpportunities();

      setTimeout(() => {
        setIsCreateModalOpen(false);
        setFormSuccess(null);
      }, 1500);
    } catch (error) {
      console.error('Error creating funding opportunity:', error);
      setFormError(t('admin.failed.to.create', 'Failed to create funding opportunity'));
    } finally {
      setFormSubmitting(false);
    }
  };

  // Handle update funding opportunity
  const handleUpdateOpportunity = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedOpportunity) return;

    setFormSubmitting(true);
    setFormError(null);

    try {
      const opportunityData = {
        ...formData,
        amount_min: formData.amount_min ? parseFloat(formData.amount_min) : null,
        amount_max: formData.amount_max ? parseFloat(formData.amount_max) : null,
      };

      await fundingOpportunitiesAPI.updateFundingOpportunity(selectedOpportunity.id, opportunityData);
      setFormSuccess(t('admin.funding.opportunity.updated', 'Funding opportunity updated successfully'));
      fetchOpportunities();

      setTimeout(() => {
        setIsEditModalOpen(false);
        setFormSuccess(null);
      }, 1500);
    } catch (error) {
      console.error('Error updating funding opportunity:', error);
      setFormError(t('admin.failed.to.update', 'Failed to update funding opportunity'));
    } finally {
      setFormSubmitting(false);
    }
  };

  // Handle delete funding opportunity
  const handleDeleteOpportunity = async () => {
    if (!selectedOpportunity) return;

    try {
      await fundingOpportunitiesAPI.deleteFundingOpportunity(selectedOpportunity.id);
      setOpportunities(prev => prev.filter(o => o.id !== selectedOpportunity.id));
      setIsDeleteModalOpen(false);
      setSelectedOpportunity(null);
    } catch (error) {
      console.error('Error deleting funding opportunity:', error);
    }
  };

  // Get funding type color
  const getFundingTypeColor = (type: string) => {
    switch (type) {
      case 'grant': return 'bg-green-500/20 text-green-300';
      case 'loan': return 'bg-blue-500/20 text-blue-300';
      case 'equity': return 'bg-purple-500/20 text-purple-300';
      case 'competition': return 'bg-orange-500/20 text-orange-300';
      default: return 'bg-gray-500/20 text-gray-300';
    }
  };

  // Get funding type label
  const getFundingTypeLabel = (type: string) => {
    switch (type) {
      case 'grant': return t('funding.type.grant', 'Grant');
      case 'loan': return t('funding.type.loan', 'Loan');
      case 'equity': return t('funding.type.equity', 'Equity Investment');
      case 'competition': return t('funding.type.competition', 'Competition');
      default: return type;
    }
  };

  // Format amount
  const formatAmount = (min?: number, max?: number) => {
    if (min && max) {
      return `$${min.toLocaleString()} - $${max.toLocaleString()}`;
    } else if (min) {
      return `$${min.toLocaleString()}+`;
    } else if (max) {
      return `Up to $${max.toLocaleString()}`;
    }
    return 'Amount not specified';
  };

  // Filter options
  const filterOptions = [
    {
      field: 'funding_type',
      label: t('funding.type', 'Funding Type'),
      options: [
        { value: 'grant', label: t('funding.type.grant', 'Grant') },
        { value: 'loan', label: t('funding.type.loan', 'Loan') },
        { value: 'equity', label: t('funding.type.equity', 'Equity Investment') },
        { value: 'competition', label: t('funding.type.competition', 'Competition') },
      ]
    },
    {
      field: 'is_active',
      label: t('common.status', 'Status'),
      options: [
        { value: 'true', label: t('common.active', 'Active') },
        { value: 'false', label: t('common.inactive', 'Inactive') },
      ]
    }
  ];

  // Bulk actions
  const bulkActions = [
    {
      label: t('common.activate', 'Activate'),
      action: async (ids: number[]) => {
        // Implementation for bulk activate
        console.log('Bulk activate:', ids);
      }
    },
    {
      label: t('common.deactivate', 'Deactivate'),
      action: async (ids: number[]) => {
        // Implementation for bulk deactivate
        console.log('Bulk deactivate:', ids);
      }
    },
    {
      label: t('common.delete', 'Delete'),
      action: async (ids: number[]) => {
        // Implementation for bulk delete
        console.log('Bulk delete:', ids);
      },
      variant: 'danger' as const
    }
  ];

  return (
    <DashboardLayout currentPage="funding-opportunities">
      {/* Header */}
      <div className={`mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
        <div>
          <h1 className="text-2xl font-bold text-white">{t('admin.funding.opportunities.management', 'Funding Opportunities Management')}</h1>
          <div className="text-gray-400 mt-1">{t('admin.funding.opportunities.description', 'Manage funding opportunities for startups')}</div>
        </div>
        <button
          onClick={openCreateModal}
          className={`mt-4 sm:mt-0 px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300 flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}
        >
          <Plus size={18} className={`mr-2 ${isRTL ? "ml-2 mr-0" : ""}`} />
          {t('admin.funding.add.opportunity', 'Add New Opportunity')}
        </button>
      </div>

      {/* Search and Filters */}
      <div className="mb-6 space-y-4">
        <div className={`flex flex-col sm:flex-row gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="relative flex-1">
            <Search size={20} className={`absolute top-3 text-gray-400 ${isRTL ? "right-3" : "left-3"}`} />
            <input
              type="text"
              placeholder={t('admin.search.opportunities', 'Search funding opportunities...')}
              value={searchTerm}
              onChange={handleSearchChange}
              className={`w-full bg-indigo-900/50 border border-indigo-800 rounded-lg py-2.5 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 ${isRTL ? "pr-10 pl-4" : "pl-10 pr-4"}`}
            />
          </div>
        </div>

        <div className="flex flex-wrap gap-4">
          <AdvancedFilter
            options={filterOptions}
            onFiltersChange={setFilters}
            className="flex-1"
          />
          {selectedItems.length > 0 && (
            <BulkActions
              selectedCount={selectedItems.length}
              actions={bulkActions}
              onClearSelection={() => setSelectedItems([])}
            />
          )}
        </div>
      </div>

      {/* Opportunities List */}
      {loading ? (
        <div className="flex justify-center py-12">
          <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      ) : filteredOpportunities.length > 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredOpportunities.map((opportunity) => (
            <div key={opportunity.id} className="bg-indigo-900/30 backdrop-blur-sm rounded-xl overflow-hidden border border-indigo-800/50 hover:border-purple-500/50 transition-all duration-300 hover:shadow-glow">
              <div className="p-6">
                <div className={`flex justify-between items-start mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-white mb-2 line-clamp-2">{opportunity.title}</h3>
                    <span className={`px-2.5 py-1 rounded-full text-xs font-medium ${getFundingTypeColor(opportunity.funding_type)}`}>
                      {getFundingTypeLabel(opportunity.funding_type)}
                    </span>
                  </div>
                  <div className={`flex space-x-1 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                    <button
                      onClick={() => openViewModal(opportunity)}
                      className="p-1.5 bg-blue-600/80 rounded-full text-white hover:bg-blue-500/80 transition-colors"
                      title={t('common.view', 'View')}
                    >
                      <Eye size={14} />
                    </button>
                    <button
                      onClick={() => openEditModal(opportunity)}
                      className="p-1.5 bg-indigo-600/80 rounded-full text-white hover:bg-indigo-500/80 transition-colors"
                      title={t('common.edit', 'Edit')}
                    >
                      <Edit size={14} />
                    </button>
                    <button
                      onClick={() => openDeleteModal(opportunity)}
                      className="p-1.5 bg-red-600/80 rounded-full text-white hover:bg-red-500/80 transition-colors"
                      title={t('common.delete', 'Delete')}
                    >
                      <Trash2 size={14} />
                    </button>
                  </div>
                </div>

                <p className="text-gray-300 text-sm mb-4 line-clamp-3">{opportunity.description}</p>

                <div className="space-y-2">
                  <div className={`flex items-center text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
                    <DollarSign size={16} className={`text-green-400 ${isRTL ? "ml-2" : "mr-2"}`} />
                    <span className="text-gray-300">{formatAmount(opportunity.amount_min, opportunity.amount_max)}</span>
                  </div>

                  {opportunity.application_deadline && (
                    <div className={`flex items-center text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
                      <Calendar size={16} className={`text-blue-400 ${isRTL ? "ml-2" : "mr-2"}`} />
                      <span className="text-gray-300">
                        {t('funding.deadline', 'Deadline')}: {new Date(opportunity.application_deadline).toLocaleDateString()}
                      </span>
                    </div>
                  )}

                  <div className={`flex items-center text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
                    <Building size={16} className={`text-purple-400 ${isRTL ? "ml-2" : "mr-2"}`} />
                    <span className="text-gray-300">{opportunity.provider.username}</span>
                  </div>
                </div>

                <div className={`flex justify-between items-center mt-4 pt-4 border-t border-indigo-800/50 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <span className={`px-2 py-1 rounded-full text-xs ${opportunity.is_active ? 'bg-green-500/20 text-green-300' : 'bg-red-500/20 text-red-300'}`}>
                    {opportunity.is_active ? t('common.active', 'Active') : t('common.inactive', 'Inactive')}
                  </span>
                  <div className="text-xs text-gray-400">
                    {opportunity.application_count || 0} {t('funding.applications', 'applications')}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <DollarSign size={48} className="mx-auto text-gray-600 mb-4" />
          <h3 className="text-lg font-medium text-gray-400 mb-2">
            {searchTerm || filters.length > 0
              ? t('admin.no.opportunities.found', 'No funding opportunities found')
              : t('admin.no.opportunities', 'No funding opportunities yet')
            }
          </h3>
          <p className="text-gray-500 mb-4">
            {searchTerm || filters.length > 0
              ? t('admin.try.different.search', 'Try adjusting your search or filters')
              : t('admin.create.first.opportunity', 'Create your first funding opportunity to get started')
            }
          </p>
          {!searchTerm && filters.length === 0 && (
            <button
              onClick={openCreateModal}
              className="px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300"
            >
              {t('admin.funding.add.opportunity', 'Add New Opportunity')}
            </button>
          )}
        </div>
      )}

      {/* Create Modal */}
      {isCreateModalOpen && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-indigo-900/90 backdrop-blur-sm rounded-xl border border-indigo-800 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className={`flex justify-between items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
                <h2 className="text-xl font-bold text-white">{t('admin.funding.create.opportunity', 'Create Funding Opportunity')}</h2>
                <button
                  onClick={() => setIsCreateModalOpen(false)}
                  className="p-2 hover:bg-indigo-800/50 rounded-lg transition-colors"
                >
                  <X size={20} className="text-gray-400" />
                </button>
              </div>

              <form onSubmit={handleCreateOpportunity} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      {t('funding.title', 'Title')} *
                    </label>
                    <input
                      type="text"
                      name="title"
                      value={formData.title}
                      onChange={handleInputChange}
                      required
                      className="w-full bg-indigo-800/50 border border-indigo-700 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                      placeholder={t('funding.title.placeholder', 'Enter opportunity title')}
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      {t('funding.description', 'Description')} *
                    </label>
                    <textarea
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      required
                      rows={4}
                      className="w-full bg-indigo-800/50 border border-indigo-700 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                      placeholder={t('funding.description.placeholder', 'Describe the funding opportunity')}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      {t('funding.type', 'Funding Type')} *
                    </label>
                    <select
                      name="funding_type"
                      value={formData.funding_type}
                      onChange={handleInputChange}
                      required
                      className="w-full bg-indigo-800/50 border border-indigo-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500"
                    >
                      <option value="grant">{t('funding.type.grant', 'Grant')}</option>
                      <option value="loan">{t('funding.type.loan', 'Loan')}</option>
                      <option value="equity">{t('funding.type.equity', 'Equity Investment')}</option>
                      <option value="competition">{t('funding.type.competition', 'Competition')}</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      {t('funding.deadline', 'Application Deadline')}
                    </label>
                    <input
                      type="date"
                      name="application_deadline"
                      value={formData.application_deadline}
                      onChange={handleInputChange}
                      className="w-full bg-indigo-800/50 border border-indigo-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      {t('funding.amount.min', 'Minimum Amount')}
                    </label>
                    <input
                      type="number"
                      name="amount_min"
                      value={formData.amount_min}
                      onChange={handleInputChange}
                      min="0"
                      step="1000"
                      className="w-full bg-indigo-800/50 border border-indigo-700 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                      placeholder="0"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      {t('funding.amount.max', 'Maximum Amount')}
                    </label>
                    <input
                      type="number"
                      name="amount_max"
                      value={formData.amount_max}
                      onChange={handleInputChange}
                      min="0"
                      step="1000"
                      className="w-full bg-indigo-800/50 border border-indigo-700 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                      placeholder="0"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      {t('funding.requirements', 'Requirements')}
                    </label>
                    <textarea
                      name="requirements"
                      value={formData.requirements}
                      onChange={handleInputChange}
                      rows={3}
                      className="w-full bg-indigo-800/50 border border-indigo-700 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                      placeholder={t('funding.requirements.placeholder', 'List the requirements for this opportunity')}
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      {t('funding.application.process', 'Application Process')}
                    </label>
                    <textarea
                      name="application_process"
                      value={formData.application_process}
                      onChange={handleInputChange}
                      rows={3}
                      className="w-full bg-indigo-800/50 border border-indigo-700 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                      placeholder={t('funding.application.process.placeholder', 'Describe the application process')}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      {t('funding.contact.email', 'Contact Email')}
                    </label>
                    <input
                      type="email"
                      name="contact_email"
                      value={formData.contact_email}
                      onChange={handleInputChange}
                      className="w-full bg-indigo-800/50 border border-indigo-700 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                      placeholder={t('funding.contact.email.placeholder', '<EMAIL>')}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      {t('funding.website', 'Website URL')}
                    </label>
                    <input
                      type="url"
                      name="website_url"
                      value={formData.website_url}
                      onChange={handleInputChange}
                      className="w-full bg-indigo-800/50 border border-indigo-700 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                      placeholder="https://example.com"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        name="is_active"
                        checked={formData.is_active}
                        onChange={handleInputChange}
                        className="w-4 h-4 text-purple-600 bg-indigo-800/50 border-indigo-700 rounded focus:ring-purple-500"
                      />
                      <span className="text-sm text-gray-300">{t('funding.is.active', 'Active (visible to users)')}</span>
                    </label>
                  </div>
                </div>

                {formError && (
                  <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-3 text-red-300 text-sm">
                    {formError}
                  </div>
                )}

                {formSuccess && (
                  <div className="bg-green-500/20 border border-green-500/50 rounded-lg p-3 text-green-300 text-sm">
                    {formSuccess}
                  </div>
                )}

                <div className={`flex justify-end space-x-3 pt-4 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                  <button
                    type="button"
                    onClick={() => setIsCreateModalOpen(false)}
                    className="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg font-medium transition-colors"
                  >
                    {t('common.cancel', 'Cancel')}
                  </button>
                  <button
                    type="submit"
                    disabled={formSubmitting}
                    className="px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 rounded-lg font-medium transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                  >
                    {formSubmitting ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                        {t('common.creating', 'Creating...')}
                      </>
                    ) : (
                      t('common.create', 'Create')
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Edit Modal - Similar to Create Modal but with update functionality */}
      {isEditModalOpen && selectedOpportunity && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-indigo-900/90 backdrop-blur-sm rounded-xl border border-indigo-800 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className={`flex justify-between items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
                <h2 className="text-xl font-bold text-white">{t('admin.funding.edit.opportunity', 'Edit Funding Opportunity')}</h2>
                <button
                  onClick={() => setIsEditModalOpen(false)}
                  className="p-2 hover:bg-indigo-800/50 rounded-lg transition-colors"
                >
                  <X size={20} className="text-gray-400" />
                </button>
              </div>

              <form onSubmit={handleUpdateOpportunity} className="space-y-4">
                {/* Same form fields as create modal */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      {t('funding.title', 'Title')} *
                    </label>
                    <input
                      type="text"
                      name="title"
                      value={formData.title}
                      onChange={handleInputChange}
                      required
                      className="w-full bg-indigo-800/50 border border-indigo-700 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      {t('funding.description', 'Description')} *
                    </label>
                    <textarea
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      required
                      rows={4}
                      className="w-full bg-indigo-800/50 border border-indigo-700 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      {t('funding.type', 'Funding Type')} *
                    </label>
                    <select
                      name="funding_type"
                      value={formData.funding_type}
                      onChange={handleInputChange}
                      required
                      className="w-full bg-indigo-800/50 border border-indigo-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500"
                    >
                      <option value="grant">{t('funding.type.grant', 'Grant')}</option>
                      <option value="loan">{t('funding.type.loan', 'Loan')}</option>
                      <option value="equity">{t('funding.type.equity', 'Equity Investment')}</option>
                      <option value="competition">{t('funding.type.competition', 'Competition')}</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      {t('funding.deadline', 'Application Deadline')}
                    </label>
                    <input
                      type="date"
                      name="application_deadline"
                      value={formData.application_deadline}
                      onChange={handleInputChange}
                      className="w-full bg-indigo-800/50 border border-indigo-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        name="is_active"
                        checked={formData.is_active}
                        onChange={handleInputChange}
                        className="w-4 h-4 text-purple-600 bg-indigo-800/50 border-indigo-700 rounded focus:ring-purple-500"
                      />
                      <span className="text-sm text-gray-300">{t('funding.is.active', 'Active (visible to users)')}</span>
                    </label>
                  </div>
                </div>

                {formError && (
                  <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-3 text-red-300 text-sm">
                    {formError}
                  </div>
                )}

                {formSuccess && (
                  <div className="bg-green-500/20 border border-green-500/50 rounded-lg p-3 text-green-300 text-sm">
                    {formSuccess}
                  </div>
                )}

                <div className={`flex justify-end space-x-3 pt-4 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                  <button
                    type="button"
                    onClick={() => setIsEditModalOpen(false)}
                    className="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg font-medium transition-colors"
                  >
                    {t('common.cancel', 'Cancel')}
                  </button>
                  <button
                    type="submit"
                    disabled={formSubmitting}
                    className="px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 rounded-lg font-medium transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                  >
                    {formSubmitting ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                        {t('common.updating', 'Updating...')}
                      </>
                    ) : (
                      t('common.update', 'Update')
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* View Modal */}
      {isViewModalOpen && selectedOpportunity && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-indigo-900/90 backdrop-blur-sm rounded-xl border border-indigo-800 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className={`flex justify-between items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
                <h2 className="text-xl font-bold text-white">{selectedOpportunity.title}</h2>
                <button
                  onClick={() => setIsViewModalOpen(false)}
                  className="p-2 hover:bg-indigo-800/50 rounded-lg transition-colors"
                >
                  <X size={20} className="text-gray-400" />
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <span className={`px-2.5 py-1 rounded-full text-xs font-medium ${getFundingTypeColor(selectedOpportunity.funding_type)}`}>
                    {getFundingTypeLabel(selectedOpportunity.funding_type)}
                  </span>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-300 mb-2">{t('funding.description', 'Description')}</h3>
                  <p className="text-white">{selectedOpportunity.description}</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium text-gray-300 mb-2">{t('funding.amount', 'Amount')}</h3>
                    <p className="text-white">{formatAmount(selectedOpportunity.amount_min, selectedOpportunity.amount_max)}</p>
                  </div>

                  {selectedOpportunity.application_deadline && (
                    <div>
                      <h3 className="text-sm font-medium text-gray-300 mb-2">{t('funding.deadline', 'Deadline')}</h3>
                      <p className="text-white">{new Date(selectedOpportunity.application_deadline).toLocaleDateString()}</p>
                    </div>
                  )}
                </div>

                {selectedOpportunity.requirements && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-300 mb-2">{t('funding.requirements', 'Requirements')}</h3>
                    <p className="text-white whitespace-pre-wrap">{selectedOpportunity.requirements}</p>
                  </div>
                )}

                <div className={`flex justify-end pt-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <button
                    onClick={() => setIsViewModalOpen(false)}
                    className="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg font-medium transition-colors"
                  >
                    {t('common.close', 'Close')}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Modal */}
      {isDeleteModalOpen && selectedOpportunity && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-indigo-900/90 backdrop-blur-sm rounded-xl border border-indigo-800 w-full max-w-md">
            <div className="p-6">
              <div className={`flex justify-between items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
                <h2 className="text-xl font-bold text-white">{t('admin.funding.delete.opportunity', 'Delete Funding Opportunity')}</h2>
                <button
                  onClick={() => setIsDeleteModalOpen(false)}
                  className="p-2 hover:bg-indigo-800/50 rounded-lg transition-colors"
                >
                  <X size={20} className="text-gray-400" />
                </button>
              </div>

              <div className="mb-6">
                <p className="text-gray-300 mb-4">
                  {t('admin.funding.delete.confirm', 'Are you sure you want to delete this funding opportunity?')}
                </p>
                <div className="bg-indigo-800/50 rounded-lg p-3">
                  <p className="text-white font-medium">{selectedOpportunity.title}</p>
                  <p className="text-gray-400 text-sm">{selectedOpportunity.funding_type}</p>
                </div>
              </div>

              <div className={`flex justify-end space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                <button
                  onClick={() => setIsDeleteModalOpen(false)}
                  className="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg font-medium transition-colors"
                >
                  {t('common.cancel', 'Cancel')}
                </button>
                <button
                  onClick={handleDeleteOpportunity}
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg font-medium transition-colors"
                >
                  {t('common.delete', 'Delete')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </DashboardLayout>
  );
};

export default FundingOpportunitiesManagement;
