"""
Video conferencing integration for mentorship sessions.
This module provides utilities for creating and managing video conferencing sessions.
"""
import os
import json
import uuid
import logging
import requests
from datetime import datetime, timedelta
from django.conf import settings
from django.utils import timezone

logger = logging.getLogger(__name__)

class VideoConferencingError(Exception):
    """Base exception for video conferencing errors"""
    pass

class VideoConferencingProvider:
    """Base class for video conferencing providers"""

    def create_meeting(self, title, start_time, duration_minutes, **kwargs):
        """Create a meeting and return meeting details"""
        raise NotImplementedError("Subclasses must implement create_meeting")

    def update_meeting(self, meeting_id, **kwargs):
        """Update an existing meeting"""
        raise NotImplementedError("Subclasses must implement update_meeting")

    def delete_meeting(self, meeting_id):
        """Delete/cancel a meeting"""
        raise NotImplementedError("Subclasses must implement delete_meeting")

    def get_join_url(self, meeting_id, **kwargs):
        """Get URL to join the meeting"""
        raise NotImplementedError("Subclasses must implement get_join_url")


class JitsiMeetProvider(VideoConferencingProvider):
    """Jitsi Meet integration - serverless option that works without API keys"""

    def __init__(self, domain="meet.jit.si"):
        self.domain = domain

    def create_meeting(self, title, start_time, duration_minutes, **kwargs):
        """
        Create a Jitsi Meet meeting (generates a room name and link)

        Args:
            title: Meeting title
            start_time: Start time (datetime)
            duration_minutes: Duration in minutes

        Returns:
            dict: Meeting details including id, password, and join URL
        """
        # Generate a unique room name based on title and a UUID
        safe_title = title.lower().replace(' ', '-')
        room_id = f"{safe_title}-{uuid.uuid4().hex[:8]}"

        # Create meeting link
        join_url = f"https://{self.domain}/{room_id}"

        return {
            'provider': 'jitsi',
            'meeting_id': room_id,
            'meeting_password': None,  # Jitsi doesn't require passwords by default
            'meeting_link': join_url,
            'host_link': join_url,  # Same link for host and participants
        }

    def update_meeting(self, meeting_id, **kwargs):
        """
        Update meeting details (not applicable for Jitsi)

        For Jitsi, we can't update an existing room, so we'll just return the current details
        """
        return {
            'provider': 'jitsi',
            'meeting_id': meeting_id,
            'meeting_password': None,
            'meeting_link': f"https://{self.domain}/{meeting_id}",
            'host_link': f"https://{self.domain}/{meeting_id}",
        }

    def delete_meeting(self, meeting_id):
        """
        Delete a meeting (not applicable for Jitsi)

        Jitsi rooms don't need to be explicitly deleted
        """
        return True

    def get_join_url(self, meeting_id, **kwargs):
        """Get URL to join the meeting"""
        return f"https://{self.domain}/{meeting_id}"


class CustomLinkProvider(VideoConferencingProvider):
    """Custom link provider for when users provide their own meeting links"""

    def create_meeting(self, title, start_time, duration_minutes, **kwargs):
        """
        Store custom meeting link

        Args:
            title: Meeting title
            start_time: Start time (datetime)
            duration_minutes: Duration in minutes
            meeting_link: Custom meeting link (required)

        Returns:
            dict: Meeting details
        """
        meeting_link = kwargs.get('meeting_link')
        if not meeting_link:
            raise VideoConferencingError("Meeting link is required for custom provider")

        return {
            'provider': 'custom',
            'meeting_id': None,
            'meeting_password': None,
            'meeting_link': meeting_link,
            'host_link': meeting_link,
        }

    def update_meeting(self, meeting_id, **kwargs):
        """Update custom meeting link"""
        meeting_link = kwargs.get('meeting_link')
        if not meeting_link:
            raise VideoConferencingError("Meeting link is required for custom provider")

        return {
            'provider': 'custom',
            'meeting_id': None,
            'meeting_password': None,
            'meeting_link': meeting_link,
            'host_link': meeting_link,
        }

    def delete_meeting(self, meeting_id):
        """Delete a meeting (not applicable for custom links)"""
        return True

    def get_join_url(self, meeting_id, **kwargs):
        """Get URL to join the meeting"""
        return kwargs.get('meeting_link', '')


# Factory function to get the appropriate provider
def get_video_provider(provider_name):
    """
    Get video conferencing provider by name

    Args:
        provider_name: Name of the provider (jitsi, zoom, etc.)

    Returns:
        VideoConferencingProvider: Provider instance
    """
    # Import providers here to avoid circular imports
    from .zoom_integration import ZoomProvider, GoogleMeetProvider

    providers = {
        'jitsi': JitsiMeetProvider(),
        'custom': CustomLinkProvider(),
        'zoom': ZoomProvider(),
        'google_meet': GoogleMeetProvider(),
        # Add other providers as they are implemented
    }

    if provider_name not in providers:
        raise VideoConferencingError(f"Unsupported video provider: {provider_name}")

    return providers[provider_name]


# Utility function to create a meeting with any supported provider
def create_video_meeting(provider_name, title, start_time, duration_minutes, **kwargs):
    """
    Create a video meeting with the specified provider

    Args:
        provider_name: Name of the provider (jitsi, zoom, etc.)
        title: Meeting title
        start_time: Start time (datetime)
        duration_minutes: Duration in minutes
        **kwargs: Additional provider-specific parameters

    Returns:
        dict: Meeting details including provider, meeting_id, meeting_password, and meeting_link
    """
    provider = get_video_provider(provider_name)
    return provider.create_meeting(title, start_time, duration_minutes, **kwargs)
