import React, { useState, useEffect } from 'react';
import { 
  BarChart3, 
  TrendingUp, 
  MessageSquare, 
  Calendar, 
  BookOpen, 
  Lightbulb,
  Users,
  Eye,
  Heart,
  Target,
  CheckCircle,
  Clock
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { useAppSelector } from '../../store/hooks';
import { RTLText, RTLFlex } from '../rtl';
import { 
  businessIdeasAPI, 
  progressUpdatesAPI 
} from '../../services/incubatorApi';
import { 
  postsAPI, 
  eventsAPI, 
  resourcesAPI 
} from '../../services/api';

interface ContentStats {
  businessIdeas: {
    total: number;
    approved: number;
    pending: number;
    rejected: number;
  };
  posts: {
    total: number;
    totalComments: number;
    avgCommentsPerPost: number;
  };
  events: {
    total: number;
    upcoming: number;
    past: number;
    totalAttendees: number;
  };
  resources: {
    total: number;
    byType: Record<string, number>;
  };
  progressUpdates: {
    total: number;
    milestonesAchieved: number;
    avgProgress: number;
  };
}

const ContentAnalytics: React.FC = () => {
  const { t } = useTranslation();
  const { language, isRTL } = useLanguage();
  const { user } = useAppSelector(state => state.auth);

  const [stats, setStats] = useState<ContentStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      fetchContentStats();
    }
  }, [user]);

  const fetchContentStats = async () => {
    if (!user) return;

    setLoading(true);
    try {
      // Fetch all user content
      const [businessIdeas, posts, events, resources, progressUpdates] = await Promise.all([
        businessIdeasAPI.getBusinessIdeas(),
        postsAPI.getPosts(),
        eventsAPI.getEvents(),
        resourcesAPI.getResources(),
        progressUpdatesAPI.getProgressUpdates()
      ]);

      // Filter user's content
      const userBusinessIdeas = businessIdeas.filter(idea => idea.owner.id === user.id);
      const userPosts = posts.filter(post => post.author.id === user.id);
      const userEvents = events.filter(event => event.organizer.id === user.id);
      const userResources = resources.filter(resource => resource.author.id === user.id);
      const userProgressUpdates = progressUpdates.filter(update => 
        userBusinessIdeas.some(idea => idea.id === update.business_idea.id)
      );

      // Calculate statistics
      const businessIdeasStats = {
        total: userBusinessIdeas.length,
        approved: userBusinessIdeas.filter(idea => idea.moderation_status === 'approved').length,
        pending: userBusinessIdeas.filter(idea => idea.moderation_status === 'pending').length,
        rejected: userBusinessIdeas.filter(idea => idea.moderation_status === 'rejected').length,
      };

      const postsStats = {
        total: userPosts.length,
        totalComments: userPosts.reduce((sum, post) => sum + (post.comments?.length || 0), 0),
        avgCommentsPerPost: userPosts.length > 0 
          ? Math.round(userPosts.reduce((sum, post) => sum + (post.comments?.length || 0), 0) / userPosts.length * 10) / 10
          : 0,
      };

      const now = new Date();
      const eventsStats = {
        total: userEvents.length,
        upcoming: userEvents.filter(event => new Date(event.date) > now).length,
        past: userEvents.filter(event => new Date(event.date) <= now).length,
        totalAttendees: userEvents.reduce((sum, event) => sum + (event.attendees?.length || 0), 0),
      };

      const resourcesByType = userResources.reduce((acc, resource) => {
        acc[resource.resource_type] = (acc[resource.resource_type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const resourcesStats = {
        total: userResources.length,
        byType: resourcesByType,
      };

      const progressStats = {
        total: userProgressUpdates.length,
        milestonesAchieved: userProgressUpdates.filter(update => update.milestone_achieved).length,
        avgProgress: userProgressUpdates.length > 0
          ? Math.round(userProgressUpdates.reduce((sum, update) => sum + update.progress_percentage, 0) / userProgressUpdates.length)
          : 0,
      };

      setStats({
        businessIdeas: businessIdeasStats,
        posts: postsStats,
        events: eventsStats,
        resources: resourcesStats,
        progressUpdates: progressStats,
      });

    } catch (error) {
      console.error('Error fetching content stats:', error);
      setError(t('analytics.errors.failedToLoad', 'Failed to load analytics'));
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
          <span className="ml-3 text-gray-400">{t('analytics.loading', 'Loading analytics...')}</span>
        </div>
      </div>
    );
  }

  if (error || !stats) {
    return (
      <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
        <div className="text-center py-8">
          <BarChart3 size={48} className="mx-auto text-gray-500 mb-4" />
          <RTLText as="div" className="text-gray-400">
            {error || t('analytics.noData', 'No analytics data available')}
          </RTLText>
        </div>
      </div>
    );
  }

  const StatCard: React.FC<{
    icon: React.ReactNode;
    title: string;
    value: string | number;
    subtitle?: string;
    color?: string;
  }> = ({ icon, title, value, subtitle, color = 'text-purple-400' }) => (
    <div className="bg-indigo-950/50 rounded-lg p-4 border border-indigo-800/50">
      <RTLFlex align="center" className="mb-2">
        <div className={`p-2 bg-indigo-900/50 rounded-lg ${language === 'ar' ? 'ml-3' : 'mr-3'}`}>
          {icon}
        </div>
        <div className="flex-1">
          <RTLText as="div" className="text-sm text-gray-400">{title}</RTLText>
          <RTLText as="div" className={`text-2xl font-bold ${color}`}>{value}</RTLText>
          {subtitle && (
            <RTLText as="div" className="text-xs text-gray-500">{subtitle}</RTLText>
          )}
        </div>
      </RTLFlex>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
        <RTLFlex align="center" className="mb-4">
          <BarChart3 className={`w-6 h-6 text-purple-400 ${language === 'ar' ? 'ml-3' : 'mr-3'}`} />
          <RTLText as="h2" className="text-xl font-bold">
            {t('analytics.contentAnalytics', 'Content Analytics')}
          </RTLText>
        </RTLFlex>
        <RTLText as="div" className="text-gray-400">
          {t('analytics.overview', 'Overview of your content and engagement')}
        </RTLText>
      </div>

      {/* Business Ideas Stats */}
      <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
        <RTLFlex align="center" className="mb-4">
          <Lightbulb className={`w-5 h-5 text-yellow-400 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
          <RTLText as="h3" className="text-lg font-semibold">
            {t('analytics.businessIdeas', 'Business Ideas')}
          </RTLText>
        </RTLFlex>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <StatCard
            icon={<Target size={20} />}
            title={t('analytics.total', 'Total')}
            value={stats.businessIdeas.total}
            color="text-blue-400"
          />
          <StatCard
            icon={<CheckCircle size={20} />}
            title={t('analytics.approved', 'Approved')}
            value={stats.businessIdeas.approved}
            color="text-green-400"
          />
          <StatCard
            icon={<Clock size={20} />}
            title={t('analytics.pending', 'Pending')}
            value={stats.businessIdeas.pending}
            color="text-yellow-400"
          />
          <StatCard
            icon={<Target size={20} />}
            title={t('analytics.rejected', 'Rejected')}
            value={stats.businessIdeas.rejected}
            color="text-red-400"
          />
        </div>
      </div>

      {/* Posts & Events Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Posts */}
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
          <RTLFlex align="center" className="mb-4">
            <MessageSquare className={`w-5 h-5 text-blue-400 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
            <RTLText as="h3" className="text-lg font-semibold">
              {t('analytics.posts', 'Posts')}
            </RTLText>
          </RTLFlex>
          <div className="space-y-3">
            <StatCard
              icon={<MessageSquare size={20} />}
              title={t('analytics.totalPosts', 'Total Posts')}
              value={stats.posts.total}
              color="text-blue-400"
            />
            <StatCard
              icon={<Heart size={20} />}
              title={t('analytics.totalComments', 'Total Comments')}
              value={stats.posts.totalComments}
              subtitle={`${stats.posts.avgCommentsPerPost} ${t('analytics.avgPerPost', 'avg per post')}`}
              color="text-pink-400"
            />
          </div>
        </div>

        {/* Events */}
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
          <RTLFlex align="center" className="mb-4">
            <Calendar className={`w-5 h-5 text-green-400 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
            <RTLText as="h3" className="text-lg font-semibold">
              {t('analytics.events', 'Events')}
            </RTLText>
          </RTLFlex>
          <div className="space-y-3">
            <StatCard
              icon={<Calendar size={20} />}
              title={t('analytics.totalEvents', 'Total Events')}
              value={stats.events.total}
              color="text-green-400"
            />
            <StatCard
              icon={<TrendingUp size={20} />}
              title={t('analytics.upcoming', 'Upcoming')}
              value={stats.events.upcoming}
              subtitle={`${stats.events.past} ${t('analytics.past', 'past')}`}
              color="text-yellow-400"
            />
            <StatCard
              icon={<Users size={20} />}
              title={t('analytics.totalAttendees', 'Total Attendees')}
              value={stats.events.totalAttendees}
              color="text-purple-400"
            />
          </div>
        </div>
      </div>

      {/* Resources & Progress */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Resources */}
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
          <RTLFlex align="center" className="mb-4">
            <BookOpen className={`w-5 h-5 text-indigo-400 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
            <RTLText as="h3" className="text-lg font-semibold">
              {t('analytics.resources', 'Resources')}
            </RTLText>
          </RTLFlex>
          <div className="space-y-3">
            <StatCard
              icon={<BookOpen size={20} />}
              title={t('analytics.totalResources', 'Total Resources')}
              value={stats.resources.total}
              color="text-indigo-400"
            />
            {Object.entries(stats.resources.byType).map(([type, count]) => (
              <div key={type} className="flex justify-between items-center py-2 border-b border-indigo-800/30 last:border-b-0">
                <RTLText as="span" className="text-sm text-gray-400 capitalize">
                  {t(`resources.types.${type}`, type)}
                </RTLText>
                <span className="text-indigo-400 font-medium">{count}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Progress Updates */}
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
          <RTLFlex align="center" className="mb-4">
            <TrendingUp className={`w-5 h-5 text-orange-400 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
            <RTLText as="h3" className="text-lg font-semibold">
              {t('analytics.progress', 'Progress')}
            </RTLText>
          </RTLFlex>
          <div className="space-y-3">
            <StatCard
              icon={<TrendingUp size={20} />}
              title={t('analytics.totalUpdates', 'Total Updates')}
              value={stats.progressUpdates.total}
              color="text-orange-400"
            />
            <StatCard
              icon={<CheckCircle size={20} />}
              title={t('analytics.milestones', 'Milestones')}
              value={stats.progressUpdates.milestonesAchieved}
              color="text-green-400"
            />
            <StatCard
              icon={<Target size={20} />}
              title={t('analytics.avgProgress', 'Avg Progress')}
              value={`${stats.progressUpdates.avgProgress}%`}
              color="text-blue-400"
            />
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
        <RTLText as="h3" className="text-lg font-semibold mb-4">
          {t('analytics.quickActions', 'Quick Actions')}
        </RTLText>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button className="p-4 bg-indigo-950/50 rounded-lg border border-indigo-800/50 hover:border-indigo-700/50 transition-colors text-left">
            <Lightbulb className="w-6 h-6 text-yellow-400 mb-2" />
            <RTLText as="div" className="text-sm font-medium">
              {t('analytics.newIdea', 'New Idea')}
            </RTLText>
          </button>
          <button className="p-4 bg-indigo-950/50 rounded-lg border border-indigo-800/50 hover:border-indigo-700/50 transition-colors text-left">
            <MessageSquare className="w-6 h-6 text-blue-400 mb-2" />
            <RTLText as="div" className="text-sm font-medium">
              {t('analytics.newPost', 'New Post')}
            </RTLText>
          </button>
          <button className="p-4 bg-indigo-950/50 rounded-lg border border-indigo-800/50 hover:border-indigo-700/50 transition-colors text-left">
            <Calendar className="w-6 h-6 text-green-400 mb-2" />
            <RTLText as="div" className="text-sm font-medium">
              {t('analytics.newEvent', 'New Event')}
            </RTLText>
          </button>
          <button className="p-4 bg-indigo-950/50 rounded-lg border border-indigo-800/50 hover:border-indigo-700/50 transition-colors text-left">
            <TrendingUp className="w-6 h-6 text-orange-400 mb-2" />
            <RTLText as="div" className="text-sm font-medium">
              {t('analytics.newUpdate', 'Progress Update')}
            </RTLText>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ContentAnalytics;
