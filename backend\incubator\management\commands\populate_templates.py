"""
Django management command to populate the database with predefined business plan templates
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from incubator.models_business_plan import BusinessPlanTemplate, TemplateSectionDefinition
from incubator.template_definitions import TEMPLATE_REGISTRY, get_template_definition


class Command(BaseCommand):
    help = 'Populate the database with predefined business plan templates'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force update existing templates',
        )
        parser.add_argument(
            '--template-type',
            type=str,
            help='Only populate specific template type',
        )

    def handle(self, *args, **options):
        force_update = options.get('force', False)
        specific_type = options.get('template_type')

        self.stdout.write(
            self.style.SUCCESS('Starting template population...')
        )

        with transaction.atomic():
            # Create section definitions first
            self.create_section_definitions()

            # Create templates
            if specific_type:
                if specific_type in TEMPLATE_REGISTRY:
                    self.create_template(specific_type, force_update)
                else:
                    self.stdout.write(
                        self.style.ERROR(f'Template type "{specific_type}" not found')
                    )
                    return
            else:
                for template_type in TEMPLATE_REGISTRY.keys():
                    self.create_template(template_type, force_update)

        self.stdout.write(
            self.style.SUCCESS('Template population completed successfully!')
        )

    def create_section_definitions(self):
        """Create predefined section definitions"""
        section_definitions = [
            {
                'title': 'Executive Summary',
                'key': 'executive_summary',
                'description': 'A concise overview of your business',
                'section_type': 'text',
                'default_content': 'Provide a brief overview of your business concept, target market, competitive advantages, and financial projections.',
                'ai_prompt_template': 'Create an executive summary for a {industry} business that {business_description}',
                'is_system': True
            },
            {
                'title': 'Market Analysis',
                'key': 'market_analysis',
                'description': 'Analysis of your target market and industry',
                'section_type': 'market_sizing',
                'default_content': 'Analyze your target market, industry trends, market size, and growth potential.',
                'ai_prompt_template': 'Analyze the market for {industry} businesses targeting {target_market}',
                'is_system': True
            },
            {
                'title': 'Competitive Analysis',
                'key': 'competitive_analysis',
                'description': 'Analysis of your competition',
                'section_type': 'competitive_matrix',
                'default_content': 'Identify and analyze your main competitors, their strengths and weaknesses.',
                'ai_prompt_template': 'Create a competitive analysis for a {industry} business',
                'is_system': True
            },
            {
                'title': 'Financial Projections',
                'key': 'financial_projections',
                'description': 'Financial forecasts and projections',
                'section_type': 'financial_forecast',
                'default_content': 'Provide detailed financial projections including revenue, expenses, and cash flow.',
                'ai_prompt_template': 'Create financial projections for a {industry} business',
                'is_system': True
            },
            {
                'title': 'Business Model Canvas',
                'key': 'business_model_canvas',
                'description': 'Visual representation of your business model',
                'section_type': 'canvas',
                'default_content': 'Map out your business model using the canvas framework.',
                'ai_prompt_template': 'Create a business model canvas for a {industry} business',
                'is_system': True
            },
            {
                'title': 'Customer Personas',
                'key': 'customer_personas',
                'description': 'Detailed profiles of your target customers',
                'section_type': 'persona',
                'default_content': 'Create detailed customer personas for your target market.',
                'ai_prompt_template': 'Create customer personas for a {industry} business targeting {target_market}',
                'is_system': True
            },
            {
                'title': 'Technology Stack',
                'key': 'technology_stack',
                'description': 'Technical architecture and infrastructure',
                'section_type': 'technology_stack',
                'default_content': 'Define your technology stack and technical requirements.',
                'ai_prompt_template': 'Define technology stack for a {industry} application',
                'is_system': True
            },
            {
                'title': 'Revenue Model',
                'key': 'revenue_model',
                'description': 'How your business generates revenue',
                'section_type': 'revenue_model',
                'default_content': 'Describe how your business will generate revenue.',
                'ai_prompt_template': 'Define revenue model for a {industry} business',
                'is_system': True
            },
            {
                'title': 'Marketing Strategy',
                'key': 'marketing_strategy',
                'description': 'Your marketing and customer acquisition strategy',
                'section_type': 'marketing_mix',
                'default_content': 'Outline your marketing strategy and customer acquisition plan.',
                'ai_prompt_template': 'Create marketing strategy for a {industry} business',
                'is_system': True
            },
            {
                'title': 'Operations Plan',
                'key': 'operations_plan',
                'description': 'How your business will operate day-to-day',
                'section_type': 'process_flow',
                'default_content': 'Describe your operational processes and procedures.',
                'ai_prompt_template': 'Create operations plan for a {industry} business',
                'is_system': True
            }
        ]

        for section_def in section_definitions:
            section, created = TemplateSectionDefinition.objects.get_or_create(
                key=section_def['key'],
                defaults=section_def
            )
            if created:
                self.stdout.write(f'Created section definition: {section.title}')
            else:
                self.stdout.write(f'Section definition already exists: {section.title}')

    def create_template(self, template_type, force_update=False):
        """Create a template from the template registry"""
        template_def = get_template_definition(template_type)

        # Check if template already exists
        existing_template = BusinessPlanTemplate.objects.filter(
            template_type=template_type,
            is_system=True
        ).first()

        if existing_template and not force_update:
            self.stdout.write(f'Template "{template_def["name"]}" already exists. Use --force to update.')
            return

        # Create or update template
        template_data = {
            'name': template_def['name'],
            'description': template_def['description'],
            'industry': self.get_industry_for_template(template_type),
            'template_type': template_type,
            'sections': template_def['sections'],
            'is_system': True,
            'is_active': True,
            'allows_customization': True,
            'customization_options': {
                'allow_section_reorder': True,
                'allow_section_removal': True,
                'allow_custom_sections': True,
                'allow_ai_generation': True
            }
        }

        if existing_template:
            # Update existing template
            for key, value in template_data.items():
                setattr(existing_template, key, value)
            existing_template.save()
            self.stdout.write(
                self.style.SUCCESS(f'Updated template: {template_def["name"]}')
            )
        else:
            # Create new template
            template = BusinessPlanTemplate.objects.create(**template_data)
            self.stdout.write(
                self.style.SUCCESS(f'Created template: {template_def["name"]}')
            )

    def get_industry_for_template(self, template_type):
        """Get appropriate industry for template type"""
        industry_mapping = {
            'saas': 'Technology',
            'ecommerce': 'E-commerce',
            'restaurant': 'Food & Beverage',
            'consulting': 'Professional Services',
            'mobile_app': 'Technology',
            'nonprofit': 'Non-Profit',
            'manufacturing': 'Manufacturing',
            'healthcare': 'Healthcare',
            'education': 'Education',
            'fintech': 'Financial Technology',
            'retail': 'Retail',
        }
        return industry_mapping.get(template_type, 'General')
