import React, { useState, useEffect, useRef } from 'react';
import {
  Send,
  RefreshCw,
  Trash2,
  X,
  Maximize2,
  Minimize2,
  MessageSquare,
  <PERSON><PERSON>,
  User,
  Sparkles
} from 'lucide-react';
import { useAppSelector } from '../../store/hooks';
import { ChatMessage, chatAPI } from '../../services/chatApi';
import { useTranslation } from 'react-i18next';

interface YasmeenChatProps {
  businessIdeaId?: number;
  businessIdeaTitle?: string;
  initialOpen?: boolean;
}

const YasmeenChat: React.FC<YasmeenChatProps> = ({
  businessIdeaId,
  businessIdeaTitle,
  initialOpen = false
}) => {
  const { user } = useAppSelector((state) => state.auth);
  const { t } = useTranslation();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isChatOpen, setIsChatOpen] = useState(initialOpen);
  const [isExpanded, setIsExpanded] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isChatOpen) {
      fetchChatHistory();
    }
  }, [isChatOpen, businessIdeaId]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const fetchChatHistory = async () => {
    setIsLoading(true);
    try {
      const history = await chatAPI.getChatHistory(businessIdeaId);
      if (history.length === 0) {
        // Add welcome message if no history
        const welcomeMessage: ChatMessage = {
          role: 'assistant',
          content: businessIdeaId
            ? t('chat.welcomeWithIdea', { title: businessIdeaTitle })
            : t('chat.welcome'),
          timestamp: new Date().toISOString()
        };
        setMessages([welcomeMessage]);
      } else {
        setMessages(history);
      }
      setError(null);
    } catch (err) {
      console.error('Error fetching chat history:', err);
      setError(t('chat.errorLoadHistory'));
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      role: 'user',
      content: inputMessage,
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    try {
      // Store user message
      await chatAPI.storeMessage(userMessage, businessIdeaId);

      // Send to AI and get response
      const response = await chatAPI.sendMessage(inputMessage, businessIdeaId);

      // Check if there was an action result
      if (response.action_result) {
        // Show action result notification
        if (response.action_result.success) {
          // Add system message about the successful action
          const actionMessage: ChatMessage = {
            role: 'assistant',
            content: `✅ ${response.action_result.message}`,
            timestamp: new Date().toISOString()
          };
          setMessages(prev => [...prev, actionMessage]);

          // Wait a moment before showing the AI response
          await new Promise(resolve => setTimeout(resolve, 500));
        } else {
          // Add error message about the failed action
          const actionErrorMessage: ChatMessage = {
            role: 'assistant',
            content: `❌ ${response.action_result.message}`,
            timestamp: new Date().toISOString()
          };
          setMessages(prev => [...prev, actionErrorMessage]);

          // Wait a moment before showing the AI response
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      // Store AI response
      await chatAPI.storeMessage(response, businessIdeaId);

      setMessages(prev => [...prev, response]);
      setError(null);
    } catch (err) {
      console.error('Error sending message:', err);

      const errorMessage: ChatMessage = {
        role: 'assistant',
        content: t('chat.errorResponse'),
        timestamp: new Date().toISOString()
      };

      setMessages(prev => [...prev, errorMessage]);
      setError(t('chat.errorSendMessage'));
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearChat = async () => {
    try {
      await chatAPI.clearChatHistory(businessIdeaId);

      // Add welcome message after clearing
      const welcomeMessage: ChatMessage = {
        role: 'assistant',
        content: businessIdeaId
          ? t('chat.welcomeWithIdea', { title: businessIdeaTitle })
          : t('chat.welcome'),
        timestamp: new Date().toISOString()
      };

      setMessages([welcomeMessage]);
      setError(null);
    } catch (err) {
      console.error('Error clearing chat:', err);
      setError(t('chat.errorClearHistory'));
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const toggleChat = () => {
    setIsChatOpen(!isChatOpen);
  };

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <>
      {/* Chat Button */}
      {!isChatOpen && (
        <button
          onClick={toggleChat}
          className="fixed bottom-6 right-6 p-4 bg-purple-600 hover:bg-purple-700 rounded-full text-white shadow-lg flex items-center justify-center z-50 transition-all"
        >
          <MessageSquare size={24} />
        </button>
      )}

      {/* Chat Window */}
      {isChatOpen && (
        <div
          className={`fixed ${
            isExpanded ? 'inset-4 md:inset-10' : 'bottom-6 right-6 w-80 md:w-96 h-[500px]'
          } bg-white dark:bg-gray-900 rounded-lg shadow-xl flex flex-col z-50 transition-all duration-300 border border-gray-200 dark:border-gray-700`}
        >
          {/* Chat Header */}
          <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 rounded-t-lg">
            <div className="flex items-center">
              <div className="w-8 h-8 rounded-full bg-purple-600 flex items-center justify-center mr-2">
                <Bot size={16} className="text-white" />
              </div>
              <div>
                <h3 className="font-medium text-white">{t('chat.yasmeenAI')}</h3>
                <div className="text-xs text-gray-400">
                  {businessIdeaTitle ? t('chat.helpingWith', { title: businessIdeaTitle }) : t('chat.yourAssistant')}
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={toggleExpand}
                className="p-1 text-gray-400 hover:text-white transition-colors"
                title={isExpanded ? t('chat.minimize') : t('chat.maximize')}
              >
                {isExpanded ? <Minimize2 size={16} /> : <Maximize2 size={16} />}
              </button>
              <button
                onClick={handleClearChat}
                className="p-1 text-gray-400 hover:text-white transition-colors"
                title={t('chat.clearChat')}
              >
                <Trash2 size={16} />
              </button>
              <button
                onClick={toggleChat}
                className="p-1 text-gray-400 hover:text-white transition-colors"
                title={t('chat.closeChat')}
              >
                <X size={16} />
              </button>
            </div>
          </div>

          {/* Chat Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900">
            {messages.map((message, index) => (
              <div
                key={index}
                className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[80%] rounded-lg p-3 ${
                    message.role === 'user'
                      ? 'bg-purple-600 text-white'
                      : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 border border-gray-200 dark:border-gray-700'
                  }`}
                >
                  <div className="flex items-center mb-1">
                    {message.role === 'user' ? (
                      <>
                        <span className="text-xs font-medium">{t('chat.you')}</span>
                        <User size={12} className="ml-1" />
                      </>
                    ) : (
                      <>
                        <span className="text-xs font-medium">{t('chat.yasmeenAI')}</span>
                        <Sparkles size={12} className="ml-1 text-purple-400" />
                      </>
                    )}
                  </div>
                  <div className="whitespace-pre-wrap">{message.content}</div>
                  <p className="text-xs opacity-70 mt-1">
                    {new Date(message.timestamp).toLocaleTimeString()}
                  </p>
                </div>
              </div>
            ))}

            {isLoading && (
              <div className="flex justify-start">
                <div className="max-w-[80%] rounded-lg p-3 bg-gray-800 text-gray-200">
                  <div className="flex items-center mb-1">
                    <span className="text-xs font-medium">{t('chat.yasmeenAI')}</span>
                    <Sparkles size={12} className="ml-1 text-purple-400" />
                  </div>
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 rounded-full bg-purple-400 animate-bounce" style={{ animationDelay: '0ms' }}></div>
                    <div className="w-2 h-2 rounded-full bg-purple-400 animate-bounce" style={{ animationDelay: '150ms' }}></div>
                    <div className="w-2 h-2 rounded-full bg-purple-400 animate-bounce" style={{ animationDelay: '300ms' }}></div>
                  </div>
                </div>
              </div>
            )}

            {error && (
              <div className="bg-red-900/30 backdrop-blur-sm rounded-lg p-2 text-red-400 text-sm">
                {error}
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>

          {/* Chat Input */}
          <div className="p-3 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
            <div className="flex items-center">
              <textarea
                className="flex-1 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-l-md p-2 text-gray-900 dark:text-white resize-none focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder={t('chat.typeMessage')}
                rows={1}
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyDown={handleKeyPress}
                disabled={isLoading}
              />
              <button
                onClick={handleSendMessage}
                disabled={!inputMessage.trim() || isLoading}
                className="p-2 bg-purple-600 hover:bg-purple-700 rounded-r-md text-white disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? <RefreshCw size={20} className="animate-spin" /> : <Send size={20} />}
              </button>
            </div>
            <p className="text-xs text-gray-500 mt-1 text-center">
              {t('chat.poweredBy')}
            </p>
          </div>
        </div>
      )}
    </>
  );
};

export default YasmeenChat;
