import i18n from 'i18next';

/**
 * Format a number according to the user's locale and preferences
 * @param value Number to format
 * @param options Formatting options
 * @returns Formatted number string
 */
export const formatNumber = (
  value: number,
  options?: {
    minimumFractionDigits?: number;
    maximumFractionDigits?: number;
    useGrouping?: boolean;
  }
): string => {
  if (value === null || value === undefined || isNaN(value)) {
    return '';
  }

  try {
    const language = i18n.language || 'en';
    const defaultDecimalDigits = language === 'ar' ? 2 : 2;

    // Create number formatter based on locale
    const formatter = new Intl.NumberFormat(language, {
      minimumFractionDigits: options?.minimumFractionDigits ?? defaultDecimalDigits,
      maximumFractionDigits: options?.maximumFractionDigits ?? defaultDecimalDigits,
      useGrouping: options?.useGrouping ?? true,
    });

    // Format the number
    const formatted = formatter.format(value);

    // For Arabic, we might need to replace Western digits with Arabic digits
    if (language === 'ar') {
      // This is optional and depends on your requirements
      // Uncomment if you want to use Arabic digits
      /*
      formatted = formatted.replace(/[0-9]/g, (digit) => {
        const arabicDigits = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        return arabicDigits[parseInt(digit)];
      });
      */
    }

    return formatted;
  } catch (error) {
    console.error('Error formatting number:', error);
    return String(value);
  }
};

/**
 * Format a currency value according to the user's locale and preferences
 * @param value Number to format
 * @param options Formatting options
 * @returns Formatted currency string
 */
export const formatCurrency = (
  value: number,
  options?: {
    currency?: string;
    minimumFractionDigits?: number;
    maximumFractionDigits?: number;
    displaySymbol?: boolean;
  }
): string => {
  if (value === null || value === undefined || isNaN(value)) {
    return '';
  }

  try {
    const language = i18n.language || 'en';
    const defaultCurrency = language === 'ar' ? 'SAR' : 'USD';
    const currency = options?.currency || defaultCurrency;

    // Create currency formatter based on locale
    const formatter = new Intl.NumberFormat(language, {
      style: options?.displaySymbol === false ? 'decimal' : 'currency',
      currency: currency,
      minimumFractionDigits: options?.minimumFractionDigits ?? 0,
      maximumFractionDigits: options?.maximumFractionDigits ?? 0,
    });

    // Format the currency
    const formatted = formatter.format(value);

    // For Arabic, we might need to replace Western digits with Arabic digits
    if (language === 'ar') {
      // This is optional and depends on your requirements
      // Uncomment if you want to use Arabic digits
      /*
      formatted = formatted.replace(/[0-9]/g, (digit) => {
        const arabicDigits = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        return arabicDigits[parseInt(digit)];
      });
      */
    }

    return formatted;
  } catch (error) {
    console.error('Error formatting currency:', error);
    return String(value);
  }
};

/**
 * Format a percentage value according to the user's locale and preferences
 * @param value Number to format (0-100 or 0-1)
 * @param options Formatting options
 * @returns Formatted percentage string
 */
export const formatPercentage = (
  value: number,
  options?: {
    minimumFractionDigits?: number;
    maximumFractionDigits?: number;
    convertFromDecimal?: boolean;
  }
): string => {
  if (value === null || value === undefined || isNaN(value)) {
    return '';
  }

  try {
    const language = i18n.language || 'en';
    
    // Convert from decimal to percentage if needed (e.g., 0.75 -> 75)
    const percentValue = options?.convertFromDecimal && value <= 1 ? value * 100 : value;

    // Create percentage formatter based on locale
    const formatter = new Intl.NumberFormat(language, {
      style: 'percent',
      minimumFractionDigits: options?.minimumFractionDigits ?? 0,
      maximumFractionDigits: options?.maximumFractionDigits ?? 0,
    });

    // Format the percentage
    // For percentage style, we divide by 100 because the formatter multiplies by 100
    const formatted = formatter.format(percentValue / 100);

    return formatted;
  } catch (error) {
    console.error('Error formatting percentage:', error);
    return `${value}%`;
  }
};

/**
 * Format a file size in bytes to a human-readable format
 * @param bytes File size in bytes
 * @param decimals Number of decimal places
 * @returns Formatted file size string
 */
export const formatFileSize = (bytes: number, decimals: number = 2): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${formatNumber(parseFloat((bytes / Math.pow(k, i)).toFixed(dm)))} ${sizes[i]}`;
};

export default {
  formatNumber,
  formatCurrency,
  formatPercentage,
  formatFileSize,
};
