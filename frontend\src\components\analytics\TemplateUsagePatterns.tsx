/**
 * Template Usage Patterns Analysis
 * Simplified version with real data integration
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Activity } from 'lucide-react';

interface TemplateUsagePatternsProps {
  timeRange?: string;
  templateId?: string;
  showExport?: boolean;
}

const TemplateUsagePatterns: React.FC<TemplateUsagePatternsProps> = ({ 
  timeRange = '30d',
  templateId,
  showExport = true
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [analyticsData, setAnalyticsData] = useState<any>(null);

  useEffect(() => {
    loadUsagePatterns();
  }, [timeRange, templateId]);

  const loadUsagePatterns = async () => {
    setLoading(true);
    try {
      // Fetch real usage patterns from API
      const { templateAnalyticsAPI } = await import('../../services/templateAnalyticsApi');
      const data = await templateAnalyticsAPI.getDashboardData({
        timeRange: timeRange,
        includeInactive: false
      });

      setAnalyticsData(data);
      
    } catch (error) {
      console.error('Error loading usage patterns:', error);
      setAnalyticsData(null);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">
            {t('analytics.usage.patterns', 'Usage Pattern Analysis')}
          </h2>
          <p className="text-gray-400 mt-1">
            {t('analytics.usage.description', 'Insights into template usage patterns and user behavior')}
          </p>
        </div>
      </div>

      {/* Content */}
      <div className="bg-gray-800/50 rounded-lg p-6">
        {analyticsData ? (
          <div className="space-y-4">
            <div className="flex items-center">
              <Activity className="text-purple-400 mr-2" size={20} />
              <span className="text-white font-medium">
                {t('analytics.data.available', 'Analytics data loaded successfully')}
              </span>
            </div>
            
            {/* Display real analytics data here */}
            <div className="text-gray-300 text-sm">
              {t('analytics.processing', 'Processing analytics data...')}
            </div>
            
            {/* Add more real data visualization components here */}
          </div>
        ) : (
          <div className="text-center py-8">
            <Activity size={48} className="text-gray-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-400 mb-2">
              {t('analytics.no.data', 'No analytics data available')}
            </h3>
            <p className="text-gray-500 text-sm">
              {t('analytics.no.data.description', 'Analytics data will appear here when available from the API.')}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default TemplateUsagePatterns;
