import React, { useState } from 'react';
import { Download, FileText, Table, Calendar, Filter, X } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';

interface ExportField {
  key: string;
  label: string;
  type?: 'text' | 'number' | 'date' | 'boolean';
  required?: boolean;
}

interface ExportOptions {
  format: 'csv' | 'excel' | 'json' | 'pdf';
  fields: string[];
  dateRange?: {
    start: string;
    end: string;
  };
  filters?: Record<string, any>;
  includeHeaders: boolean;
  filename?: string;
}

interface DataExportProps {
  entityName: string;
  availableFields: ExportField[];
  onExport: (options: ExportOptions) => Promise<void>;
  isOpen: boolean;
  onClose: () => void;
}

const DataExport: React.FC<DataExportProps> = ({
  entityName,
  availableFields,
  onExport,
  isOpen,
  onClose
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'csv',
    fields: availableFields.filter(f => f.required).map(f => f.key),
    includeHeaders: true,
    filename: `${entityName}_export_${new Date().toISOString().split('T')[0]}`
  });
  const [isExporting, setIsExporting] = useState(false);

  // Handle field selection
  const handleFieldToggle = (fieldKey: string) => {
    const field = availableFields.find(f => f.key === fieldKey);
    if (field?.required) return; // Can't deselect required fields

    setExportOptions(prev => ({
      ...prev,
      fields: prev.fields.includes(fieldKey)
        ? prev.fields.filter(f => f !== fieldKey)
        : [...prev.fields, fieldKey]
    }));
  };

  // Handle select all/none
  const handleSelectAll = () => {
    setExportOptions(prev => ({
      ...prev,
      fields: availableFields.map(f => f.key)
    }));
  };

  const handleSelectNone = () => {
    setExportOptions(prev => ({
      ...prev,
      fields: availableFields.filter(f => f.required).map(f => f.key)
    }));
  };

  // Handle export
  const handleExport = async () => {
    try {
      setIsExporting(true);
      await onExport(exportOptions);
      onClose();
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsExporting(false);
    }
  };

  // Get format icon
  const getFormatIcon = (format: string) => {
    switch (format) {
      case 'csv':
        return <Table size={16} className="text-green-400" />;
      case 'excel':
        return <Table size={16} className="text-blue-400" />;
      case 'json':
        return <FileText size={16} className="text-purple-400" />;
      case 'pdf':
        return <FileText size={16} className="text-red-400" />;
      default:
        return <Download size={16} className="text-gray-400" />;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-indigo-900/90 backdrop-blur-sm rounded-xl border border-indigo-800 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className={`flex justify-between items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
            <h2 className="text-xl font-bold text-white">
              {t('export.title', 'Export {{entity}}', { entity: entityName })}
            </h2>
            <button
              onClick={onClose}
              className="p-2 hover:bg-indigo-800/50 rounded-lg transition-colors"
            >
              <X size={20} className="text-gray-400" />
            </button>
          </div>

          <div className="space-y-6">
            {/* Export Format */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-3">
                {t('export.format', 'Export Format')}
              </label>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {[
                  { value: 'csv', label: 'CSV', description: t('export.csv.desc', 'Comma-separated values') },
                  { value: 'excel', label: 'Excel', description: t('export.excel.desc', 'Microsoft Excel format') },
                  { value: 'json', label: 'JSON', description: t('export.json.desc', 'JavaScript Object Notation') },
                  { value: 'pdf', label: 'PDF', description: t('export.pdf.desc', 'Portable Document Format') }
                ].map((format) => (
                  <button
                    key={format.value}
                    onClick={() => setExportOptions(prev => ({ ...prev, format: format.value as any }))}
                    className={`p-3 rounded-lg border transition-all duration-300 ${
                      exportOptions.format === format.value
                        ? 'border-purple-500 bg-purple-500/20'
                        : 'border-indigo-700 bg-indigo-800/30 hover:border-indigo-600'
                    }`}
                  >
                    <div className={`flex items-center space-x-2 mb-1 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                      {getFormatIcon(format.value)}
                      <span className="text-white font-medium">{format.label}</span>
                    </div>
                    <p className="text-gray-400 text-xs">{format.description}</p>
                  </button>
                ))}
              </div>
            </div>

            {/* Field Selection */}
            <div>
              <div className={`flex justify-between items-center mb-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                <label className="text-sm font-medium text-gray-300">
                  {t('export.fields', 'Fields to Export')}
                </label>
                <div className={`flex space-x-2 text-sm ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                  <button
                    onClick={handleSelectAll}
                    className="text-blue-400 hover:text-blue-300 transition-colors"
                  >
                    {t('export.select.all', 'Select All')}
                  </button>
                  <span className="text-gray-500">|</span>
                  <button
                    onClick={handleSelectNone}
                    className="text-gray-400 hover:text-gray-300 transition-colors"
                  >
                    {t('export.select.none', 'Select None')}
                  </button>
                </div>
              </div>
              
              <div className="max-h-48 overflow-y-auto border border-indigo-700 rounded-lg p-3 bg-indigo-800/30">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {availableFields.map((field) => (
                    <label
                      key={field.key}
                      className={`flex items-center space-x-2 p-2 rounded hover:bg-indigo-700/30 transition-colors cursor-pointer ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}
                    >
                      <input
                        type="checkbox"
                        checked={exportOptions.fields.includes(field.key)}
                        onChange={() => handleFieldToggle(field.key)}
                        disabled={field.required}
                        className="w-4 h-4 text-purple-600 bg-indigo-800 border-indigo-600 rounded focus:ring-purple-500"
                      />
                      <span className={`text-white text-sm ${field.required ? 'font-medium' : ''}`}>
                        {field.label}
                        {field.required && (
                          <span className="text-red-400 ml-1">*</span>
                        )}
                      </span>
                    </label>
                  ))}
                </div>
              </div>
              <p className="text-gray-400 text-xs mt-2">
                {t('export.selected.count', '{{count}} fields selected', { count: exportOptions.fields.length })}
              </p>
            </div>

            {/* Date Range */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-3">
                {t('export.date.range', 'Date Range (Optional)')}
              </label>
              <div className={`grid grid-cols-2 gap-3 ${isRTL ? "grid-flow-col-dense" : ""}`}>
                <div>
                  <label className="block text-xs text-gray-400 mb-1">
                    {t('export.start.date', 'Start Date')}
                  </label>
                  <input
                    type="date"
                    value={exportOptions.dateRange?.start || ''}
                    onChange={(e) => setExportOptions(prev => ({
                      ...prev,
                      dateRange: {
                        start: e.target.value,
                        end: prev.dateRange?.end || ''
                      }
                    }))}
                    className="w-full bg-indigo-800/50 border border-indigo-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500"
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-400 mb-1">
                    {t('export.end.date', 'End Date')}
                  </label>
                  <input
                    type="date"
                    value={exportOptions.dateRange?.end || ''}
                    onChange={(e) => setExportOptions(prev => ({
                      ...prev,
                      dateRange: {
                        start: prev.dateRange?.start || '',
                        end: e.target.value
                      }
                    }))}
                    className="w-full bg-indigo-800/50 border border-indigo-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500"
                  />
                </div>
              </div>
            </div>

            {/* Additional Options */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-3">
                {t('export.options', 'Additional Options')}
              </label>
              <div className="space-y-3">
                <label className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                  <input
                    type="checkbox"
                    checked={exportOptions.includeHeaders}
                    onChange={(e) => setExportOptions(prev => ({ ...prev, includeHeaders: e.target.checked }))}
                    className="w-4 h-4 text-purple-600 bg-indigo-800 border-indigo-600 rounded focus:ring-purple-500"
                  />
                  <span className="text-white text-sm">
                    {t('export.include.headers', 'Include column headers')}
                  </span>
                </label>

                <div>
                  <label className="block text-xs text-gray-400 mb-1">
                    {t('export.filename', 'Filename')}
                  </label>
                  <input
                    type="text"
                    value={exportOptions.filename || ''}
                    onChange={(e) => setExportOptions(prev => ({ ...prev, filename: e.target.value }))}
                    placeholder={`${entityName}_export`}
                    className="w-full bg-indigo-800/50 border border-indigo-700 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className={`flex justify-end space-x-3 pt-6 mt-6 border-t border-indigo-800/50 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
            <button
              onClick={onClose}
              disabled={isExporting}
              className="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg font-medium transition-colors disabled:opacity-50"
            >
              {t('common.cancel', 'Cancel')}
            </button>
            <button
              onClick={handleExport}
              disabled={isExporting || exportOptions.fields.length === 0}
              className="px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 rounded-lg font-medium transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isExporting ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>{t('export.exporting', 'Exporting...')}</span>
                </>
              ) : (
                <>
                  <Download size={16} />
                  <span>{t('export.download', 'Download Export')}</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DataExport;
