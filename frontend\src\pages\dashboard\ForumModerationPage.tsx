import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import Button from '../../components/ui/Button';
import { Badge } from '../../components/ui/badge';
import { Input } from '../../components/ui/input';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { MessageSquare, Users, Pin, Lock, Trash2, Eye, Flag, Search, Filter, CheckCircle, X } from 'lucide-react';

interface ForumThread {
  id: string;
  title: string;
  author: {
    id: string;
    name: string;
    role: string;
  };
  category: string;
  createdAt: string;
  lastActivity: string;
  replies: number;
  views: number;
  status: 'active' | 'locked' | 'pinned' | 'archived' | 'flagged';
  priority: 'normal' | 'high' | 'urgent';
  reports: number;
  moderationNotes?: string;
}

interface ForumPost {
  id: string;
  threadId: string;
  threadTitle: string;
  content: string;
  author: {
    id: string;
    name: string;
    role: string;
  };
  createdAt: string;
  editedAt?: string;
  status: 'active' | 'hidden' | 'deleted' | 'flagged';
  reports: number;
  likes: number;
  reportReasons: string[];
}

const ForumModerationPage: React.FC = () => {
  const [threads, setThreads] = useState<ForumThread[]>([]);
  const [posts, setPosts] = useState<ForumPost[]>([]);
  const [filteredThreads, setFilteredThreads] = useState<ForumThread[]>([]);
  const [filteredPosts, setFilteredPosts] = useState<ForumPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [activeTab, setActiveTab] = useState('threads');

  // Mock data - replace with actual API call
  useEffect(() => {
    const mockThreads: ForumThread[] = [
      {
        id: '1',
        title: 'Best practices for startup funding',
        author: { id: 'user1', name: 'John Smith', role: 'entrepreneur' },
        category: 'Funding',
        createdAt: '2024-01-15T10:30:00Z',
        lastActivity: '2024-01-16T14:20:00Z',
        replies: 23,
        views: 156,
        status: 'active',
        priority: 'normal',
        reports: 0
      },
      {
        id: '2',
        title: 'URGENT: Need help with legal issues',
        author: { id: 'user2', name: 'Sarah Johnson', role: 'startup_founder' },
        category: 'Legal',
        createdAt: '2024-01-14T16:45:00Z',
        lastActivity: '2024-01-16T09:15:00Z',
        replies: 8,
        views: 89,
        status: 'pinned',
        priority: 'urgent',
        reports: 0
      },
      {
        id: '3',
        title: 'Spam post about cryptocurrency investment',
        author: { id: 'user3', name: 'Mike Wilson', role: 'user' },
        category: 'General',
        createdAt: '2024-01-13T09:15:00Z',
        lastActivity: '2024-01-13T09:15:00Z',
        replies: 2,
        views: 34,
        status: 'flagged',
        priority: 'high',
        reports: 5,
        moderationNotes: 'Multiple reports for spam content'
      },
      {
        id: '4',
        title: 'Product development methodologies',
        author: { id: 'user4', name: 'Emily Davis', role: 'product_manager' },
        category: 'Product',
        createdAt: '2024-01-12T14:20:00Z',
        lastActivity: '2024-01-15T11:30:00Z',
        replies: 45,
        views: 234,
        status: 'locked',
        priority: 'normal',
        reports: 1,
        moderationNotes: 'Locked due to heated discussion'
      }
    ];

    const mockPosts: ForumPost[] = [
      {
        id: '1',
        threadId: '1',
        threadTitle: 'Best practices for startup funding',
        content: 'Great question! I think the key is to have a solid business plan and clear metrics...',
        author: { id: 'user5', name: 'Alex Chen', role: 'investor' },
        createdAt: '2024-01-15T11:00:00Z',
        status: 'active',
        reports: 0,
        likes: 12,
        reportReasons: []
      },
      {
        id: '2',
        threadId: '3',
        threadTitle: 'Spam post about cryptocurrency investment',
        content: 'AMAZING OPPORTUNITY!!! Invest in our new crypto coin and get 1000% returns guaranteed!!!',
        author: { id: 'user3', name: 'Mike Wilson', role: 'user' },
        createdAt: '2024-01-13T09:20:00Z',
        status: 'flagged',
        reports: 8,
        likes: 0,
        reportReasons: ['Spam', 'Misleading content', 'Promotional content']
      }
    ];

    setTimeout(() => {
      setThreads(mockThreads);
      setPosts(mockPosts);
      setFilteredThreads(mockThreads);
      setFilteredPosts(mockPosts);
      setLoading(false);
    }, 1000);
  }, []);

  // Filter threads and posts based on search and filters
  useEffect(() => {
    let filteredT = threads;
    let filteredP = posts;

    if (searchTerm) {
      filteredT = filteredT.filter(thread => 
        thread.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        thread.author.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
      filteredP = filteredP.filter(post => 
        post.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.author.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== 'all') {
      filteredT = filteredT.filter(thread => thread.status === statusFilter);
      filteredP = filteredP.filter(post => post.status === statusFilter);
    }

    if (categoryFilter !== 'all') {
      filteredT = filteredT.filter(thread => thread.category === categoryFilter);
    }

    setFilteredThreads(filteredT);
    setFilteredPosts(filteredP);
  }, [threads, posts, searchTerm, statusFilter, categoryFilter]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'locked': return 'bg-orange-100 text-orange-800';
      case 'pinned': return 'bg-blue-100 text-blue-800';
      case 'archived': return 'bg-gray-100 text-gray-800';
      case 'flagged': return 'bg-red-100 text-red-800';
      case 'hidden': return 'bg-yellow-100 text-yellow-800';
      case 'deleted': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'text-red-600';
      case 'high': return 'text-orange-600';
      case 'normal': return 'text-gray-600';
      default: return 'text-gray-600';
    }
  };

  const handleLockThread = (id: string) => {
    setThreads(threads.map(thread => 
      thread.id === id ? { ...thread, status: 'locked' as const } : thread
    ));
  };

  const handleUnlockThread = (id: string) => {
    setThreads(threads.map(thread => 
      thread.id === id ? { ...thread, status: 'active' as const } : thread
    ));
  };

  const handlePinThread = (id: string) => {
    setThreads(threads.map(thread => 
      thread.id === id ? { ...thread, status: 'pinned' as const } : thread
    ));
  };

  const handleDeleteThread = (id: string) => {
    setThreads(threads.filter(thread => thread.id !== id));
  };

  const handleApprovePost = (id: string) => {
    setPosts(posts.map(post => 
      post.id === id ? { ...post, status: 'active' as const, reports: 0 } : post
    ));
  };

  const handleHidePost = (id: string) => {
    setPosts(posts.map(post => 
      post.id === id ? { ...post, status: 'hidden' as const } : post
    ));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Forum Moderation</h1>
          <p className="text-gray-600 mt-1">Moderate forum threads and posts</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Filter className="w-4 h-4 mr-2" />
            Advanced Filters
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700">
            <Eye className="w-4 h-4 mr-2" />
            Bulk Actions
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Threads</p>
                <p className="text-2xl font-bold">{threads.length}</p>
              </div>
              <MessageSquare className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Flagged Content</p>
                <p className="text-2xl font-bold text-red-600">
                  {threads.filter(t => t.status === 'flagged').length + posts.filter(p => p.status === 'flagged').length}
                </p>
              </div>
              <Flag className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Threads</p>
                <p className="text-2xl font-bold text-green-600">
                  {threads.filter(t => t.status === 'active').length}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Reports</p>
                <p className="text-2xl font-bold text-orange-600">
                  {threads.reduce((sum, t) => sum + t.reports, 0) + posts.reduce((sum, p) => sum + p.reports, 0)}
                </p>
              </div>
              <Users className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search threads and posts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select 
              value={statusFilter} 
              onChange={(e) => setStatusFilter(e.target.value)}
              className="border rounded px-3 py-2"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="flagged">Flagged</option>
              <option value="locked">Locked</option>
              <option value="pinned">Pinned</option>
              <option value="archived">Archived</option>
            </select>
            <select 
              value={categoryFilter} 
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="border rounded px-3 py-2"
            >
              <option value="all">All Categories</option>
              <option value="General">General</option>
              <option value="Funding">Funding</option>
              <option value="Legal">Legal</option>
              <option value="Product">Product</option>
              <option value="Marketing">Marketing</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Forum Moderation Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="threads">Threads ({filteredThreads.length})</TabsTrigger>
          <TabsTrigger value="posts">Posts ({filteredPosts.length})</TabsTrigger>
          <TabsTrigger value="flagged">Flagged Content</TabsTrigger>
        </TabsList>

        <TabsContent value="threads" className="space-y-4">
          <div className="space-y-4">
            {filteredThreads.map((thread) => (
              <Card key={thread.id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="font-semibold text-lg">{thread.title}</h3>
                        <Badge className={getStatusColor(thread.status)}>
                          {thread.status}
                        </Badge>
                        {thread.priority !== 'normal' && (
                          <span className={`text-sm font-medium ${getPriorityColor(thread.priority)}`}>
                            {thread.priority} priority
                          </span>
                        )}
                      </div>
                      
                      <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                        <span>By: {thread.author.name} ({thread.author.role})</span>
                        <span>Category: {thread.category}</span>
                        <span>Created: {formatDate(thread.createdAt)}</span>
                        <span>Last activity: {formatDate(thread.lastActivity)}</span>
                      </div>

                      <div className="flex items-center gap-6 text-sm text-gray-600 mb-3">
                        <span>{thread.replies} replies</span>
                        <span>{thread.views} views</span>
                        {thread.reports > 0 && (
                          <span className="text-red-600">{thread.reports} reports</span>
                        )}
                      </div>

                      {thread.moderationNotes && (
                        <div className="bg-yellow-50 p-3 rounded mb-3">
                          <p className="text-sm text-yellow-800">
                            <strong>Moderation Notes:</strong> {thread.moderationNotes}
                          </p>
                        </div>
                      )}
                    </div>

                    <div className="flex flex-col gap-2 ml-4">
                      <Button variant="outline" size="sm">
                        <Eye className="w-4 h-4 mr-1" />
                        View
                      </Button>
                      
                      {thread.status === 'active' && (
                        <>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleLockThread(thread.id)}
                          >
                            <Lock className="w-4 h-4 mr-1" />
                            Lock
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handlePinThread(thread.id)}
                          >
                            <Pin className="w-4 h-4 mr-1" />
                            Pin
                          </Button>
                        </>
                      )}

                      {thread.status === 'locked' && (
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleUnlockThread(thread.id)}
                        >
                          <CheckCircle className="w-4 h-4 mr-1" />
                          Unlock
                        </Button>
                      )}

                      <Button 
                        variant="outline" 
                        size="sm"
                        className="border-red-300 text-red-600 hover:bg-red-50"
                        onClick={() => handleDeleteThread(thread.id)}
                      >
                        <Trash2 className="w-4 h-4 mr-1" />
                        Delete
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="posts" className="space-y-4">
          <div className="space-y-4">
            {filteredPosts.map((post) => (
              <Card key={post.id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h4 className="font-semibold">Post in: {post.threadTitle}</h4>
                        <Badge className={getStatusColor(post.status)}>
                          {post.status}
                        </Badge>
                      </div>
                      
                      <p className="text-gray-700 mb-3 line-clamp-3">{post.content}</p>
                      
                      <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                        <span>By: {post.author.name} ({post.author.role})</span>
                        <span>Posted: {formatDate(post.createdAt)}</span>
                        <span>{post.likes} likes</span>
                        {post.reports > 0 && (
                          <span className="text-red-600">{post.reports} reports</span>
                        )}
                      </div>

                      {post.reportReasons.length > 0 && (
                        <div className="bg-red-50 p-3 rounded mb-3">
                          <p className="text-sm text-red-800">
                            <strong>Report Reasons:</strong> {post.reportReasons.join(', ')}
                          </p>
                        </div>
                      )}
                    </div>

                    <div className="flex flex-col gap-2 ml-4">
                      <Button variant="outline" size="sm">
                        <Eye className="w-4 h-4 mr-1" />
                        View Full
                      </Button>
                      
                      {post.status === 'flagged' && (
                        <>
                          <Button 
                            variant="outline" 
                            size="sm"
                            className="border-green-300 text-green-600 hover:bg-green-50"
                            onClick={() => handleApprovePost(post.id)}
                          >
                            <CheckCircle className="w-4 h-4 mr-1" />
                            Approve
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm"
                            className="border-orange-300 text-orange-600 hover:bg-orange-50"
                            onClick={() => handleHidePost(post.id)}
                          >
                            <X className="w-4 h-4 mr-1" />
                            Hide
                          </Button>
                        </>
                      )}

                      <Button 
                        variant="outline" 
                        size="sm"
                        className="border-red-300 text-red-600 hover:bg-red-50"
                      >
                        <Trash2 className="w-4 h-4 mr-1" />
                        Delete
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="flagged" className="space-y-4">
          <div className="space-y-4">
            {[...filteredThreads.filter(t => t.status === 'flagged'), ...filteredPosts.filter(p => p.status === 'flagged')].length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <Flag className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No flagged content found.</p>
                </CardContent>
              </Card>
            ) : (
              <>
                {filteredThreads.filter(t => t.status === 'flagged').map((thread) => (
                  <Card key={`thread-${thread.id}`} className="border-red-200">
                    <CardContent className="p-6">
                      <div className="flex items-center gap-2 mb-2">
                        <Flag className="w-5 h-5 text-red-600" />
                        <span className="font-semibold text-red-800">Flagged Thread</span>
                      </div>
                      <h3 className="font-semibold text-lg mb-2">{thread.title}</h3>
                      <p className="text-sm text-gray-600 mb-3">
                        By {thread.author.name} • {thread.reports} reports • {formatDate(thread.createdAt)}
                      </p>
                      {thread.moderationNotes && (
                        <p className="text-sm bg-yellow-50 p-2 rounded">{thread.moderationNotes}</p>
                      )}
                    </CardContent>
                  </Card>
                ))}
                
                {filteredPosts.filter(p => p.status === 'flagged').map((post) => (
                  <Card key={`post-${post.id}`} className="border-red-200">
                    <CardContent className="p-6">
                      <div className="flex items-center gap-2 mb-2">
                        <Flag className="w-5 h-5 text-red-600" />
                        <span className="font-semibold text-red-800">Flagged Post</span>
                      </div>
                      <p className="text-gray-700 mb-3 line-clamp-2">{post.content}</p>
                      <p className="text-sm text-gray-600 mb-3">
                        By {post.author.name} • {post.reports} reports • {formatDate(post.createdAt)}
                      </p>
                      {post.reportReasons.length > 0 && (
                        <p className="text-sm bg-red-50 p-2 rounded">
                          <strong>Reasons:</strong> {post.reportReasons.join(', ')}
                        </p>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ForumModerationPage;
