import { Event } from '../../services/api';

/**
 * Format event data for display
 * @param event Event object from API
 * @returns Formatted event data for display
 */
export const formatEventData = (event: Event) => {
  const eventDate = new Date(event.date);
  const formattedDate = eventDate.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
  const formattedTime = eventDate.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  });

  // Determine event type based on description or title
  let eventType = 'Event';
  if (event.description.toLowerCase().includes('workshop') || event.title.toLowerCase().includes('workshop')) {
    eventType = 'Workshop';
  } else if (event.description.toLowerCase().includes('panel') || event.title.toLowerCase().includes('panel')) {
    eventType = 'Panel';
  } else if (event.description.toLowerCase().includes('symposium') || event.title.toLowerCase().includes('symposium')) {
    eventType = 'Symposium';
  } else if (event.description.toLowerCase().includes('webinar') || event.title.toLowerCase().includes('webinar')) {
    eventType = 'Webinar';
  }

  return {
    id: event.id,
    title: event.title,
    date: formattedDate,
    time: formattedTime,
    location: event.location,
    type: eventType,
    image: event.image || '',
    attendees: event.attendee_count,
    isAttending: event.is_attending,
    isVirtual: event.is_virtual,
    virtualLink: event.virtual_link || undefined
  };
};

/**
 * Create empty events array for testing
 * @returns Empty array of events
 */
export const createMockEvents = (): Event[] => {
  return [];
};
