import React from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../../../../store/hooks';
import { RTLText, RTLFlex, RTLIcon } from '../../../common';
import { Lightbulb, ArrowRight } from 'lucide-react';
import { BusinessIdea } from '../../../../services/incubatorApi';

interface LatestIdeaCardProps {
  latestIdea: BusinessIdea;
}

const LatestIdeaCard: React.FC<LatestIdeaCardProps> = ({ latestIdea }) => {
  const { t } = useTranslation();
  const { language } = useAppSelector(state => state.language);

  if (!latestIdea) return null;

  return (
    <div className="mb-8">
      <RTLText as="h2" className="text-xl font-semibold mb-4">{t('dashboard.latestIdea')}</RTLText>
      <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50">
        <RTLFlex className="items-center mb-3">
          <div className={`p-2 bg-indigo-800/50 rounded-full ${language === 'ar' ? 'ml-3' : 'mr-3'}`}>
            <Lightbulb size={20} className="text-purple-400" />
          </div>
          <RTLText as="h3" className="font-medium">{latestIdea.title}</RTLText>
        </RTLFlex>
        <RTLText as="div" className="text-gray-400 text-sm mb-3 line-clamp-2">
          {latestIdea.description}
        </RTLText>
        <RTLFlex className="justify-between items-center text-sm text-gray-400">
          <span>{t('dashboard.stage')}: <span className="capitalize">{latestIdea.current_stage}</span></span>
          <span>{t('dashboard.created')}: {new Date(latestIdea.created_at).toLocaleDateString(language === 'ar' ? 'ar-SA' : undefined)}</span>
        </RTLFlex>
        <div className="mt-4">
          <RTLFlex
            as={Link}
            to={`/dashboard/business-ideas/${latestIdea.id}`}
            className="text-purple-400 hover:text-purple-300"
            align="center"
          >
            {t('dashboard.viewDetails')} <RTLIcon icon={ArrowRight} size={16} className={language === 'ar' ? 'mr-1' : 'ml-1'} flipInRTL={true} />
          </RTLFlex>
        </div>
      </div>
    </div>
  );
};

export default LatestIdeaCard;
