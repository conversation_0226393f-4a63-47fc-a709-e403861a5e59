# Role System Documentation

## Overview
This document describes the complete role system implementation for the Yasmeen AI platform, ensuring consistency between frontend and backend role definitions.

## Role Definitions

### 1. Super Administrator (`super_admin`)
- **Display Name**: Super Administrator
- **Permission Level**: `super_admin`
- **Description**: Complete system control with highest level access to all platform features, system management, user impersonation, AI system control, and advanced analytics.
- **Auto-Approval**: Yes
- **Frontend Access**: All pages and features
- **Backend Access**: Full system control

### 2. Administrator (`admin`)
- **Display Name**: Administrator
- **Permission Level**: `admin`
- **Description**: Full system administrator with all permissions except super admin functions
- **Auto-Approval**: Yes
- **Frontend Access**: Admin dashboard, user management, content management
- **Backend Access**: Most admin functions (excluding super admin features)

### 3. Moderator (`moderator`)
- **Display Name**: Moderator
- **Permission Level**: `moderate`
- **Description**: Content moderator with moderation permissions
- **Auto-Approval**: No (requires manual approval)
- **Frontend Access**: Moderation tools, content review
- **Backend Access**: Content moderation functions

### 4. <PERSON><PERSON> (`mentor`)
- **Display Name**: Mentor
- **Permission Level**: `write`
- **Description**: Business mentor providing guidance to entrepreneurs
- **Auto-Approval**: No (requires manual approval)
- **Frontend Access**: Mentorship dashboard, business plan reviews
- **Backend Access**: Mentorship-related functions

### 5. Investor (`investor`)
- **Display Name**: Investor
- **Permission Level**: `write`
- **Description**: Investor with access to investment opportunities and analytics
- **Auto-Approval**: No (requires manual approval)
- **Frontend Access**: Investment dashboard, business plan analytics
- **Backend Access**: Investment-related functions

### 6. Regular User (`user`)
- **Display Name**: Regular User
- **Permission Level**: `write`
- **Description**: Standard user with basic permissions
- **Auto-Approval**: Yes
- **Frontend Access**: User dashboard, basic features
- **Backend Access**: Standard user functions

## Permission Levels

1. **`super_admin`**: Highest level - complete system access
2. **`admin`**: Administrative access to most features
3. **`moderate`**: Content moderation capabilities
4. **`write`**: Standard read/write access
5. **`read`**: Read-only access (currently unused)

## Frontend-Backend Consistency

### Frontend Role Types (TypeScript)
```typescript
type UserType = 'user' | 'admin' | 'super_admin' | 'mentor' | 'investor' | 'moderator';
```

### Backend Role Names (Django)
```python
ROLE_CHOICES = [
    ('super_admin', 'Super Administrator'),
    ('admin', 'Administrator'),
    ('moderator', 'Moderator'),
    ('mentor', 'Mentor'),
    ('investor', 'Investor'),
    ('user', 'Regular User'),
]
```

## Role Management Commands

### Synchronize Roles
```bash
python manage.py sync_roles
```
- Creates missing roles
- Updates role properties
- Verifies consistency

### Force Update Roles
```bash
python manage.py sync_roles --force-update
```
- Forces update of existing roles with new properties

### Verify Roles Only
```bash
python manage.py sync_roles --verify-only
```
- Only checks role consistency without making changes

### Manual Scripts
```bash
# Sync roles
python sync_roles.py

# Check consistency
python check_role_consistency.py

# Cleanup unnecessary roles
python cleanup_roles.py
```

## Current Role Distribution
- **super_admin**: 1 user
- **admin**: 0 users
- **moderator**: 1 user
- **mentor**: 1 user
- **investor**: 1 user
- **user**: 11 users

## Role Assignment Process

### Automatic Assignment
- New users automatically get `user` role
- `super_admin` and `admin` roles are auto-approved
- `user` role is auto-approved

### Manual Approval Required
- `moderator` role requires admin approval
- `mentor` role requires admin approval
- `investor` role requires admin approval

## Frontend Permission Checking

The frontend uses type-safe permission checking:

```typescript
// Type guard functions
const isSuperAdminType = (type: UserType): type is 'super_admin' => type === 'super_admin';
const isAdminType = (type: UserType): type is 'admin' => type === 'admin';

// Permission checking
const hasPermissionForItem = (item: NavItem): boolean => {
  if (isSuperAdminType(userType)) {
    return true; // Super admin can access everything
  }
  
  if (item.category === 'system' || item.category === 'security') {
    return isAdminType(userType) || isSuperAdminType(userType);
  }
  
  return item.userTypes.includes(userType);
};
```

## Maintenance

### Regular Checks
1. Run `python check_role_consistency.py` monthly
2. Verify user role assignments quarterly
3. Clean up unused roles as needed

### Adding New Roles
1. Update backend `UserRole` model choices
2. Update frontend TypeScript types
3. Run synchronization scripts
4. Update permission checking logic
5. Test thoroughly

### Removing Roles
1. Migrate users to appropriate roles
2. Update frontend/backend code
3. Run cleanup scripts
4. Verify consistency

## Troubleshooting

### Common Issues
1. **Role not appearing in frontend**: Check TypeScript types and permission logic
2. **Permission denied errors**: Verify role assignments and permission levels
3. **Inconsistent roles**: Run sync and cleanup scripts

### Debug Commands
```bash
# Check all roles
python manage.py shell -c "from users.models import UserRole; [print(f'{r.name}: {r.display_name}') for r in UserRole.objects.all()]"

# Check user roles
python manage.py shell -c "from users.models import UserRoleAssignment; [print(f'{a.user_profile.user.username}: {a.role.name}') for a in UserRoleAssignment.objects.filter(is_active=True)]"
```

## Security Considerations

1. **Super Admin Access**: Extremely limited - only for system owners
2. **Role Escalation**: Prevented through approval processes
3. **Permission Inheritance**: Higher roles don't automatically inherit lower permissions
4. **Audit Trail**: All role changes should be logged (implement if needed)

---

**Last Updated**: Role system synchronized and verified
**Status**: ✅ All roles consistent between frontend and backend
**Total Roles**: 6 (super_admin, admin, moderator, mentor, investor, user)
**Total Users**: 15 (11 with user role assignments)
