# User Dashboard Components

This directory contains reusable components for the user dashboard in the application.

## Component Structure

### Main Component

- **UserDashboard**: The main dashboard component that composes all the sections

### Section Components

- **WelcomeSection**: Displays a welcome message to the user
- **StatsCards**: Shows statistics about the user's business ideas
- **QuickActions**: Provides quick links to common actions
- **LatestIdeaCard**: Displays information about the user's latest business idea
- **BusinessIdeaProgress**: Shows progress tracking for the latest business idea

### Custom Hooks

- **useBusinessIdeas**: Fetches and processes business ideas data for the dashboard

## Usage

The UserDashboard component is designed to be used as the main page for regular users after login. It provides an overview of their activities and quick access to key features.

```tsx
import { UserDashboard } from '../components/dashboard';

const DashboardPage = () => {
  return <UserDashboard />;
};
```

## Component Props

### WelcomeSection
- `user`: The current user object

### StatsCards
- `stats`: Object containing statistics about business ideas
  - `totalIdeas`: Total number of ideas
  - `approvedIdeas`: Number of approved ideas
  - `pendingIdeas`: Number of pending ideas
  - `rejectedIdeas`: Number of rejected ideas

### LatestIdeaCard
- `latestIdea`: The most recent business idea object

### BusinessIdeaProgress
- `latestIdea`: The most recent business idea object

## Data Flow

1. The `useBusinessIdeas` hook fetches business ideas data
2. The main `UserDashboard` component receives this data
3. The data is passed to the appropriate section components
4. Each section component renders its part of the dashboard

## Related Components

The user dashboard also uses these components from other directories:
- `UserActivityAnalytics` from '../user-activity'
- `ContentRecommendations` from '../content-recommendations'
- `ProfileCompletionCard` from '../profile-completion'
- `MainLayout` from '../../layout/MainLayout' (✅ Updated: now uses unified layout system)
