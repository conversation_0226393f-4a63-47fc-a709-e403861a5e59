import { lazy } from 'react';
import { RouteConfig, createRoleRoute } from './routeConfig';

// DEDICATED MODERATOR DASHBOARD - Moderator users only
const ModeratorDashboardPage = lazy(() => import('../pages/moderator/ModeratorDashboardPage'));
// Legacy ModeratorDashboard kept for backward compatibility
const ModeratorDashboard = lazy(() => import('../components/dashboard/moderator-dashboard/ModeratorDashboard'));
const ContentModerationPage = lazy(() => import('../pages/dashboard/ContentModerationPage'));
const UserModerationPage = lazy(() => import('../pages/dashboard/UserModerationPage'));
const ForumModerationPage = lazy(() => import('../pages/dashboard/ForumModerationPage'));
const ReportsManagementPage = lazy(() => import('../pages/dashboard/ReportsManagementPage'));
const ModerationAnalyticsPage = lazy(() => import('../pages/dashboard/ModerationAnalyticsPage'));

// Shared components that moderators can access
const PostsPage = lazy(() => import('../pages/dashboard/PostsPage'));
const EventsPage = lazy(() => import('../pages/dashboard/EventsPage'));
const UserSettings = lazy(() => import('../pages/UserSettings'));

/**
 * Moderator-specific routes for content and user moderation
 */
export const moderatorRoutes: RouteConfig[] = [
  // Main moderator dashboard - DEDICATED MODERATOR ONLY ✅ (using ModeratorDashboardPage)
  createRoleRoute('/dashboard/moderator', ModeratorDashboardPage, ['moderator'], ['moderate'], 'Loading moderator dashboard...'),

  // Content moderation
  createRoleRoute('/dashboard/moderation/content', ContentModerationPage, ['moderator'], ['moderate'], 'Loading content moderation...'),
  createRoleRoute('/dashboard/moderation/users', UserModerationPage, ['moderator'], ['moderate'], 'Loading user moderation...'),
  createRoleRoute('/dashboard/moderation/forum', ForumModerationPage, ['moderator'], ['moderate'], 'Loading forum moderation...'),
  createRoleRoute('/dashboard/moderation/reports', ReportsManagementPage, ['moderator'], ['moderate'], 'Loading reports management...'),
  createRoleRoute('/dashboard/moderation/analytics', ModerationAnalyticsPage, ['moderator'], ['moderate'], 'Loading moderation analytics...'),

  // Shared functionality for moderators
  createRoleRoute('/dashboard/posts', PostsPage, ['moderator'], ['moderate'], 'Loading posts...'),
  createRoleRoute('/dashboard/events', EventsPage, ['moderator'], ['moderate'], 'Loading events...'),
  createRoleRoute('/settings', UserSettings, ['moderator'], ['read'], 'Loading settings...'),
];

export default moderatorRoutes;
