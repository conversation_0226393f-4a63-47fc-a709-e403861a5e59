import { useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import {
  useApiList,
  useApiQuery,
  useApiCreate,
  useApiUpdate,
  useApiDelete,
  useApiInfinite,
  usePrefetchApiQuery,
  usePrefetchApiList
} from './useApi';
import { BusinessMilestone, BusinessGoal } from '../services/milestoneApi';
import { QueryParams } from '../types/api';

// Query keys for milestone entities
export const MilestoneKeys = {
  // Business Milestones
  milestones: ['business-milestones'] as const,
  milestoneLists: () => [...MilestoneKeys.milestones, 'list'] as const,
  milestoneList: (params?: QueryParams) => [...MilestoneKeys.milestoneLists(), params] as const,
  milestoneDetails: () => [...MilestoneKeys.milestones, 'detail'] as const,
  milestoneDetail: (id: number) => [...MilestoneKeys.milestoneDetails(), id] as const,

  // Business Goals
  goals: ['business-goals'] as const,
  goalLists: () => [...MilestoneKeys.goals, 'list'] as const,
  goalList: (params?: QueryParams) => [...MilestoneKeys.goalLists(), params] as const,
  goalDetails: () => [...MilestoneKeys.goals, 'detail'] as const,
  goalDetail: (id: number) => [...MilestoneKeys.goalDetails(), id] as const,
};

// ============ BUSINESS MILESTONES ============

export function useBusinessMilestonesList(params?: QueryParams) {
  return useApiList<BusinessMilestone>(
    MilestoneKeys.milestoneList(params),
    '/incubator/business-milestones/',
    params
  );
}

export function useBusinessMilestonesInfinite(params?: QueryParams) {
  return useApiInfinite<BusinessMilestone>(
    MilestoneKeys.milestoneList(params),
    '/incubator/business-milestones/',
    params
  );
}

export function useBusinessMilestone(id: number) {
  return useApiQuery<BusinessMilestone>(
    MilestoneKeys.milestoneDetail(id),
    `/incubator/business-milestones/${id}/`
  );
}

export function useCreateBusinessMilestone() {
  const queryClient = useQueryClient();

  return useApiCreate<BusinessMilestone, Partial<BusinessMilestone>>(
    '/incubator/business-milestones/',
    {
      onSuccess: (newMilestone) => {
        queryClient.invalidateQueries({ queryKey: MilestoneKeys.milestoneLists() });
        queryClient.setQueryData(MilestoneKeys.milestoneDetail(newMilestone.id), newMilestone);
      }
    }
  );
}

export function useUpdateBusinessMilestone() {
  const queryClient = useQueryClient();

  return useApiUpdate<BusinessMilestone, Partial<BusinessMilestone>>(
    (id: number) => `/incubator/business-milestones/${id}/`,
    {
      onSuccess: (updatedMilestone) => {
        queryClient.setQueryData(MilestoneKeys.milestoneDetail(updatedMilestone.id), updatedMilestone);
        queryClient.invalidateQueries({ queryKey: MilestoneKeys.milestoneLists() });
      }
    }
  );
}

export function useDeleteBusinessMilestone() {
  const queryClient = useQueryClient();

  return useApiDelete(
    (id: number) => `/incubator/business-milestones/${id}/`,
    {
      onSuccess: (_, id) => {
        queryClient.removeQueries({ queryKey: MilestoneKeys.milestoneDetail(id) });
        queryClient.invalidateQueries({ queryKey: MilestoneKeys.milestoneLists() });
      }
    }
  );
}

// ============ BUSINESS GOALS ============

export function useBusinessGoalsList(params?: QueryParams) {
  return useApiList<BusinessGoal>(
    MilestoneKeys.goalList(params),
    '/incubator/business-goals/',
    params
  );
}

export function useBusinessGoalsInfinite(params?: QueryParams) {
  return useApiInfinite<BusinessGoal>(
    MilestoneKeys.goalList(params),
    '/incubator/business-goals/',
    params
  );
}

export function useBusinessGoal(id: number) {
  return useApiQuery<BusinessGoal>(
    MilestoneKeys.goalDetail(id),
    `/incubator/business-goals/${id}/`
  );
}

export function useCreateBusinessGoal() {
  const queryClient = useQueryClient();

  return useApiCreate<BusinessGoal, Partial<BusinessGoal>>(
    '/incubator/business-goals/',
    {
      onSuccess: (newGoal) => {
        queryClient.invalidateQueries({ queryKey: MilestoneKeys.goalLists() });
        queryClient.setQueryData(MilestoneKeys.goalDetail(newGoal.id), newGoal);
      }
    }
  );
}

export function useUpdateBusinessGoal() {
  const queryClient = useQueryClient();

  return useApiUpdate<BusinessGoal, Partial<BusinessGoal>>(
    (id: number) => `/incubator/business-goals/${id}/`,
    {
      onSuccess: (updatedGoal) => {
        queryClient.setQueryData(MilestoneKeys.goalDetail(updatedGoal.id), updatedGoal);
        queryClient.invalidateQueries({ queryKey: MilestoneKeys.goalLists() });
      }
    }
  );
}

export function useDeleteBusinessGoal() {
  const queryClient = useQueryClient();

  return useApiDelete(
    (id: number) => `/incubator/business-goals/${id}/`,
    {
      onSuccess: (_, id) => {
        queryClient.removeQueries({ queryKey: MilestoneKeys.goalDetail(id) });
        queryClient.invalidateQueries({ queryKey: MilestoneKeys.goalLists() });
      }
    }
  );
}

// ============ SPECIALIZED MILESTONE ACTIONS ============

/**
 * Hook for marking milestone as completed
 */
export function useCompleteMilestone() {
  const queryClient = useQueryClient();

  return useApiUpdate<BusinessMilestone, { completion_notes?: string }>(
    (id: number) => `/incubator/business-milestones/${id}/complete/`,
    {
      onSuccess: (updatedMilestone) => {
        queryClient.setQueryData(MilestoneKeys.milestoneDetail(updatedMilestone.id), updatedMilestone);
        queryClient.invalidateQueries({ queryKey: MilestoneKeys.milestoneLists() });
      }
    }
  );
}

/**
 * Hook for updating milestone status
 */
export function useUpdateMilestoneStatus() {
  const queryClient = useQueryClient();

  return useApiUpdate<BusinessMilestone, { 
    status: 'not_started' | 'in_progress' | 'completed' | 'delayed' | 'cancelled';
    notes?: string;
  }>(
    (id: number) => `/incubator/business-milestones/${id}/update-status/`,
    {
      onSuccess: (updatedMilestone) => {
        queryClient.setQueryData(MilestoneKeys.milestoneDetail(updatedMilestone.id), updatedMilestone);
        queryClient.invalidateQueries({ queryKey: MilestoneKeys.milestoneLists() });
      }
    }
  );
}

/**
 * Hook for updating goal progress
 */
export function useUpdateGoalProgress() {
  const queryClient = useQueryClient();

  return useApiUpdate<BusinessGoal, { 
    progress_percentage: number;
    progress_notes?: string;
  }>(
    (id: number) => `/incubator/business-goals/${id}/update-progress/`,
    {
      onSuccess: (updatedGoal) => {
        queryClient.setQueryData(MilestoneKeys.goalDetail(updatedGoal.id), updatedGoal);
        queryClient.invalidateQueries({ queryKey: MilestoneKeys.goalLists() });
      }
    }
  );
}

/**
 * Hook for bulk milestone operations
 */
export function useBulkMilestoneUpdate() {
  const queryClient = useQueryClient();

  return useApiUpdate<{ updated_count: number }, { 
    milestone_ids: number[];
    action: 'complete' | 'start' | 'delay' | 'cancel';
    notes?: string;
  }>(
    () => `/incubator/business-milestones/bulk-update/`,
    {
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: MilestoneKeys.milestoneLists() });
      }
    }
  );
}

/**
 * Hook for generating milestone recommendations
 */
export function useGenerateMilestoneRecommendations() {
  const queryClient = useQueryClient();

  return useApiCreate<{ milestones: BusinessMilestone[] }, { business_idea_id: number }>(
    '/incubator/business-milestones/generate-recommendations/',
    {
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: MilestoneKeys.milestoneLists() });
      }
    }
  );
}

/**
 * Hook for milestone analytics
 */
export function useMilestoneAnalytics(businessIdeaId?: number) {
  return useApiQuery<{
    total_milestones: number;
    completed_milestones: number;
    overdue_milestones: number;
    completion_rate: number;
    average_completion_time: number;
    upcoming_deadlines: BusinessMilestone[];
  }>(
    ['milestone-analytics', businessIdeaId],
    businessIdeaId 
      ? `/incubator/business-milestones/analytics/?business_idea=${businessIdeaId}`
      : '/incubator/business-milestones/analytics/'
  );
}
