import React from 'react';
import { useLanguage } from '../../hooks/useLanguage';

interface SkeletonProps {
  className?: string;
}

/**
 * Base skeleton component that shows a loading placeholder
 */
export const Skeleton: React.FC<SkeletonProps> = ({ className = '' }) => {
  return (
    <div className={`animate-pulse rounded glass-light ${className}`}>
      <div></div>
    </div>
  );
};

/**
 * Skeleton for post items
 */
export const PostSkeleton: React.FC = () => {
  const { isRTL } = useLanguage();
  return (
    <div className="p-6 border-b border-glass-border glass-light">
      <div className={`flex justify-between items-start ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
            <Skeleton className="h-10 w-10 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-3 w-24" />
            </div>
          </div>
          <div className="mt-4 space-y-3">
            <Skeleton className="h-5 w-3/4" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-2/3" />
          </div>
          <div className={`mt-4 flex space-x-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            <Skeleton className="h-8 w-20 rounded-md" />
            <Skeleton className="h-8 w-20 rounded-md" />
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * Skeleton for event cards
 */
export const EventSkeleton: React.FC = () => {
  const { isRTL } = useLanguage();
  return (
    <div className="rounded-xl overflow-hidden shadow-lg glass-morphism">
      <Skeleton className="h-40 w-full" />
      <div className="p-5 space-y-4">
        <Skeleton className="h-6 w-3/4" />
        <div className="space-y-2">
          <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
            <Skeleton className="h-4 w-4 rounded-full" />
            <Skeleton className="h-4 w-32" />
          </div>
          <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
            <Skeleton className="h-4 w-4 rounded-full" />
            <Skeleton className="h-4 w-40" />
          </div>
        </div>
        <div className={`flex justify-between items-center ${isRTL ? "flex-row-reverse" : ""}`}>
          <Skeleton className="h-8 w-24 rounded-md" />
          <Skeleton className="h-6 w-16 rounded-full" />
        </div>
      </div>
    </div>
  );
};

/**
 * Skeleton for resource cards
 */
export const ResourceSkeleton: React.FC = () => {
  const { isRTL } = useLanguage();
  return (
    <div className="rounded-xl overflow-hidden shadow-lg glass-morphism">
      <Skeleton className="h-32 w-full" />
      <div className="p-5 space-y-3">
        <Skeleton className="h-5 w-3/4" />
        <Skeleton className="h-4 w-1/3" />
        <div className="space-y-2">
          <Skeleton className="h-3 w-full" />
          <Skeleton className="h-3 w-full" />
          <Skeleton className="h-3 w-2/3" />
        </div>
        <div className={`flex justify-between items-center pt-2 ${isRTL ? "flex-row-reverse" : ""}`}>
          <Skeleton className="h-8 w-24 rounded-md" />
          <Skeleton className="h-6 w-6 rounded-full" />
        </div>
      </div>
    </div>
  );
};

/**
 * Skeleton for user list items
 */
export const UserSkeleton: React.FC = () => {
  const { isRTL } = useLanguage();
  return (
    <div className="p-4 border-b border-glass-border glass-light">
      <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
          <Skeleton className="h-10 w-10 rounded-full" />
          <div className="space-y-2">
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-3 w-40" />
          </div>
        </div>
        <div className={`flex space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
          <Skeleton className="h-8 w-8 rounded-md" />
          <Skeleton className="h-8 w-8 rounded-md" />
        </div>
      </div>
    </div>
  );
};

/**
 * Creates multiple skeleton items
 */
export const SkeletonList: React.FC<{
  count: number;
  SkeletonComponent: React.FC;
  containerClassName?: string;
}> = ({ count, SkeletonComponent, containerClassName = '' }) => {
  return (
    <div className={containerClassName}>
      {Array(count)
        .fill(0)
        .map((_, index) => (
          <SkeletonComponent key={index} />
        ))}
    </div>
  );
};
