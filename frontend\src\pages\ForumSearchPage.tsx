import React from 'react';
import { Link } from 'react-router-dom';
import { Home, ChevronRight, Search } from 'lucide-react';
import AdvancedForumSearch from '../components/forum/AdvancedForumSearch';
import { useLanguage } from '../hooks/useLanguage';
import { useTranslation } from 'react-i18next';
const ForumSearchPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  return (
    <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 text-white py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-6xl mx-auto">
        {/* Breadcrumb */}
        <nav className={`flex mb-6 text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
          <ol className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
            <li>
              <Link to="/" className="text-gray-400 hover:text-white">
                <Home size={16} />
              </Link>
            </li>
            <li className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <ChevronRight size={14} className="text-gray-500" />
              <Link to="/forum" className={`ml-2 text-gray-400 hover:text-white ${isRTL ? "space-x-reverse" : ""}`}>
                Forum
              </Link>
            </li>
            <li className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <ChevronRight size={14} className="text-gray-500" />
              <span className={`ml-2 text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <Search size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                Search
              </span>
            </li>
          </ol>
        </nav>

        <h1 className="text-3xl font-bold mb-8">t("common.forum.search", "Forum Search")</h1>

        <AdvancedForumSearch />
      </div>
    </div>
  );
};

export default ForumSearchPage;
