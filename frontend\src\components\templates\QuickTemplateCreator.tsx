/**
 * Quick Template Creator Modal
 * Simple modal for creating templates quickly
 */

import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import {
  X,
  Plus,
  Save,
  FileText,
  CheckCircle,
  AlertCircle,
  Sparkles
} from 'lucide-react';
import { RTLText, RTLFlex } from '../common';

interface QuickTemplateCreatorProps {
  isOpen: boolean;
  onClose: () => void;
  onTemplateCreated?: (template: any) => void;
}

const QuickTemplateCreator: React.FC<QuickTemplateCreatorProps> = ({ isOpen,
  onClose,
  onTemplateCreated
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [templateName, setTemplateName] = useState('');
  const [templateDescription, setTemplateDescription] = useState('');
  const [templateCategory, setTemplateCategory] = useState('general');
  const [saving, setSaving] = useState(false);
  const [success, setSuccess] = useState('');
  const [error, setError] = useState('');

  const categories = [
    { id: 'general', name: t("common.general.business", "General Business") },
    { id: 'technology', name: t("common.technology", "Technology") },
    { id: 'retail', name: t("common.retail", "Retail") },
    { id: 'services', name: t("common.services", "Services") },
    { id: 'hospitality', name: t("common.hospitality", "Hospitality") },
    { id: 'nonprofit', name: t("common.nonprofit", "Non-Profit") }
  ];

  const handleSave = async () => {
    if (!templateName.trim()) {
      setError(t("common.template.name.is", "Template name is required"));
      return;
    }

    setSaving(true);
    setError('');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      const newTemplate = {
        id: `quick_${Date.now()}`,
        name: templateName,
        description: templateDescription || `Custom ${templateName} template`,
        category: templateCategory,
        difficulty: 'beginner' as const,
        estimatedTime: 6,
        sections: 8,
        popularity: 3,
        isNew: true,
        isCustom: true,
        createdAt: new Date().toISOString()
      };

      // Store in localStorage for demo
      const existingTemplates = JSON.parse(localStorage.getItem('quickTemplates') || '[]');
      existingTemplates.push(newTemplate);
      localStorage.setItem('quickTemplates', JSON.stringify(existingTemplates));

      setSuccess(t("common.template.created.successfully", "Template created successfully!"));

      if (onTemplateCreated) {
        onTemplateCreated(newTemplate);
      }

      // Close modal after success
      setTimeout(() => {
        handleClose();
      }, 1500);

    } catch (err) {
      setError(t("common.failed.to.create", "Failed to create template. Please try again."));
    } finally {
      setSaving(false);
    }
  };

  const handleClose = () => {
    setTemplateName('');
    setTemplateDescription('');
    setTemplateCategory('general');
    setSaving(false);
    setSuccess('');
    setError('');
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className={`fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4 ${isRTL ? "flex-row-reverse" : ""}`}>
      <div className="bg-black/30 backdrop-blur-sm rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto border border-white/20">
        {/* Header */}
        <div className={`flex items-center justify-between p-6 border-b border-white/20 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <Sparkles size={20} className={`text-purple-400 mr-3 ${isRTL ? "space-x-reverse" : ""}`} />
            <h3 className="text-lg font-semibold text-white">{t("common.quick.template.creator", "Quick Template Creator")}</h3>
          </div>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-4">
          {/* Success Message */}
          {success && (
            <div className="bg-green-900/20 border border-green-500/50 rounded-lg p-4">
              <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <CheckCircle size={20} className={`text-green-400 mr-3 ${isRTL ? "space-x-reverse" : ""}`} />
                <span className="text-green-400">{success}</span>
              </div>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="bg-red-900/20 border border-red-500/50 rounded-lg p-4">
              <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <AlertCircle size={20} className={`text-red-400 mr-3 ${isRTL ? "space-x-reverse" : ""}`} />
                <span className="text-red-400">{error}</span>
              </div>
            </div>
          )}

          {/* Form */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Template Name *
              </label>
              <input
                type="text"
                value={templateName}
                onChange={(e) => setTemplateName(e.target.value)}
                placeholder="e.g., My Custom Business Plan"
                className="w-full px-3 py-2 bg-white/20 border border-white/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                disabled={saving}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Description
              </label>
              <textarea
                value={templateDescription}
                onChange={(e) => setTemplateDescription(e.target.value)}
                placeholder={t("common.brief.description.of", "Brief description of what this template is for...")}
                rows={3}
                className="w-full px-3 py-2 bg-white/20 border border-white/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                disabled={saving}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Category
              </label>
              <select
                value={templateCategory}
                onChange={(e) => setTemplateCategory(e.target.value)}
                className="w-full px-3 py-2 bg-white/20 border border-white/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                disabled={saving}
              >
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Info Box */}
          <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4">
            <div className={`flex items-start ${isRTL ? "flex-row-reverse" : ""}`}>
              <FileText size={16} className={`text-blue-400 mr-2 mt-0.5 flex-shrink-0 ${isRTL ? "flex-row-reverse" : ""}`} />
              <div className="text-sm text-blue-300">
                <p className="font-medium mb-1">{t("common.quick.template", "Quick Template")}</p>
                <p className="text-blue-400">
                  This will create a basic template with standard business plan sections.
                  You can customize it further after creation.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className={`flex items-center justify-end space-x-3 p-6 border-t border-gray-700 ${isRTL ? "flex-row-reverse" : ""}`}>
          <button
            onClick={handleClose}
            disabled={saving}
            className="px-4 py-2 text-gray-300 hover:text-white transition-colors disabled:opacity-50"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={saving || !templateName.trim()}
            className={`flex items-center px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 hover:shadow-glow disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-all duration-300 ${isRTL ? "flex-row-reverse" : ""}`}
          >
            {saving ? (
              <>
                <div className={`animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 ${isRTL ? "space-x-reverse" : ""}`}></div>
                Creating...
              </>
            ) : (
              <>
                <Save size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                Create Template
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default QuickTemplateCreator;
