from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from users.models import User<PERSON>ro<PERSON>le, UserRole, UserRoleAssignment
from django.utils import timezone
from django.db.models import Q


class Command(BaseCommand):
    help = 'List all admin users in the database'

    def add_arguments(self, parser):
        parser.add_argument(
            '--format',
            type=str,
            choices=['table', 'json', 'csv'],
            default='table',
            help='Output format (default: table)'
        )
        parser.add_argument(
            '--include-inactive',
            action='store_true',
            help='Include inactive users'
        )
        parser.add_argument(
            '--role-filter',
            type=str,
            choices=['super_admin', 'admin', 'moderator', 'all'],
            default='all',
            help='Filter by specific admin role (default: all)'
        )
        parser.add_argument(
            '--reset-password',
            type=str,
            help='Reset password for specific username (will prompt for new password)'
        )
        parser.add_argument(
            '--show-password-info',
            action='store_true',
            help='Show password-related information (last changed, etc.)'
        )

    def handle(self, *args, **options):
        # Handle password reset if requested
        if options['reset_password']:
            self._reset_user_password(options['reset_password'])
            return

        self.stdout.write(self.style.SUCCESS('🔍 Fetching admin users from database...'))

        # Define admin roles
        admin_roles = ['super_admin', 'admin', 'moderator']

        # Filter by specific role if requested
        if options['role_filter'] != 'all':
            admin_roles = [options['role_filter']]

        # Build query
        query = Q(roles__name__in=admin_roles)

        if not options['include_inactive']:
            query &= Q(is_active=True) & Q(user__is_active=True)

        # Get admin users
        admin_profiles = UserProfile.objects.filter(query).distinct().select_related(
            'user'
        ).prefetch_related(
            'roles',
            'userroleassignment_set__role'
        )

        if not admin_profiles.exists():
            self.stdout.write(self.style.WARNING('❌ No admin users found in the database'))
            return

        # Show password security notice
        self._show_password_notice()

        # Format output based on requested format
        if options['format'] == 'json':
            self._output_json(admin_profiles, options['show_password_info'])
        elif options['format'] == 'csv':
            self._output_csv(admin_profiles, options['show_password_info'])
        else:
            self._output_table(admin_profiles, options['show_password_info'])

    def _show_password_notice(self):
        """Show important notice about passwords"""
        self.stdout.write(self.style.WARNING('\n🔐 PASSWORD SECURITY NOTICE:'))
        self.stdout.write(self.style.WARNING('━' * 50))
        self.stdout.write(self.style.WARNING('• Passwords are stored as secure hashes and cannot be retrieved'))
        self.stdout.write(self.style.WARNING('• Use --reset-password <username> to reset a user\'s password'))
        self.stdout.write(self.style.WARNING('• Default passwords should be changed immediately after creation'))
        self.stdout.write(self.style.WARNING('• Consider using strong, unique passwords for all admin accounts'))
        self.stdout.write('')

    def _reset_user_password(self, username):
        """Reset password for a specific user"""
        try:
            user = User.objects.get(username=username)

            self.stdout.write(self.style.SUCCESS(f'🔄 Resetting password for user: {username}'))

            # Prompt for new password
            import getpass
            while True:
                password1 = getpass.getpass('Enter new password: ')
                password2 = getpass.getpass('Confirm new password: ')

                if password1 == password2:
                    if len(password1) < 8:
                        self.stdout.write(self.style.ERROR('❌ Password must be at least 8 characters long'))
                        continue
                    break
                else:
                    self.stdout.write(self.style.ERROR('❌ Passwords do not match. Please try again.'))

            # Set new password
            user.set_password(password1)
            user.save()

            self.stdout.write(self.style.SUCCESS(f'✅ Password successfully reset for {username}'))
            self.stdout.write(self.style.WARNING('⚠️  Please inform the user to change their password on first login'))

        except User.DoesNotExist:
            self.stdout.write(self.style.ERROR(f'❌ User "{username}" not found'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Error resetting password: {str(e)}'))

    def _output_table(self, admin_profiles, show_password_info=False):
        """Output in table format"""
        self.stdout.write(self.style.SUCCESS(f'\n📊 Found {admin_profiles.count()} admin users:\n'))

        # Header - adjust based on password info
        if show_password_info:
            header = f"{'ID':<5} {'Username':<20} {'Email':<30} {'Name':<25} {'Roles':<30} {'Status':<10} {'Last Login':<20} {'Password Info':<25}"
        else:
            header = f"{'ID':<5} {'Username':<20} {'Email':<30} {'Name':<25} {'Roles':<30} {'Status':<10} {'Last Login':<20}"

        self.stdout.write(self.style.SUCCESS(header))
        self.stdout.write(self.style.SUCCESS('-' * len(header)))
        
        for profile in admin_profiles:
            user = profile.user
            
            # Get active roles
            active_assignments = UserRoleAssignment.objects.filter(
                user_profile=profile,
                is_active=True,
                is_approved=True
            ).select_related('role')
            
            roles = ', '.join([assignment.role.name for assignment in active_assignments])
            
            # Format status
            status = '✅ Active' if profile.is_active and user.is_active else '❌ Inactive'
            
            # Format last login
            last_login = user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login else 'Never'
            
            # Format name
            full_name = f"{user.first_name} {user.last_name}".strip() or 'N/A'

            # Add password info if requested
            if show_password_info:
                # Check if password is set and when it was last changed
                has_password = bool(user.password)
                password_info = "Set" if has_password else "Not Set"
                if has_password and hasattr(user, 'date_joined'):
                    # Approximate password age (Django doesn't track password change dates by default)
                    password_info += f" (Since: {user.date_joined.strftime('%Y-%m-%d')})"

                row = f"{user.id:<5} {user.username:<20} {user.email:<30} {full_name:<25} {roles:<30} {status:<10} {last_login:<20} {password_info:<25}"
            else:
                row = f"{user.id:<5} {user.username:<20} {user.email:<30} {full_name:<25} {roles:<30} {status:<10} {last_login:<20}"

            self.stdout.write(row)
        
        self.stdout.write('')
    
    def _output_json(self, admin_profiles, show_password_info=False):
        """Output in JSON format"""
        import json
        
        admins_data = []
        for profile in admin_profiles:
            user = profile.user
            
            # Get active roles
            active_assignments = UserRoleAssignment.objects.filter(
                user_profile=profile,
                is_active=True,
                is_approved=True
            ).select_related('role')
            
            roles = [
                {
                    'name': assignment.role.name,
                    'display_name': assignment.role.display_name,
                    'permission_level': assignment.role.permission_level,
                    'assigned_at': assignment.assigned_at.isoformat() if assignment.assigned_at else None
                }
                for assignment in active_assignments
            ]
            
            admin_data = {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'is_active': user.is_active,
                'is_staff': user.is_staff,
                'is_superuser': user.is_superuser,
                'last_login': user.last_login.isoformat() if user.last_login else None,
                'date_joined': user.date_joined.isoformat(),
                'profile': {
                    'bio': profile.bio,
                    'location': profile.location,
                    'company': profile.company,
                    'language': profile.language,
                    'is_active': profile.is_active
                },
                'roles': roles
            }

            # Add password info if requested
            if show_password_info:
                admin_data['password_info'] = {
                    'has_password': bool(user.password),
                    'password_hash_algorithm': user.password.split('$')[0] if user.password else None,
                    'account_created': user.date_joined.isoformat()
                }
            admins_data.append(admin_data)
        
        self.stdout.write(json.dumps(admins_data, indent=2))
    
    def _output_csv(self, admin_profiles, show_password_info=False):
        """Output in CSV format"""
        import csv
        import sys
        
        writer = csv.writer(sys.stdout)
        
        # Header - adjust based on password info
        if show_password_info:
            header = [
                'ID', 'Username', 'Email', 'First Name', 'Last Name',
                'Is Active', 'Is Staff', 'Is Superuser', 'Last Login',
                'Date Joined', 'Roles', 'Bio', 'Location', 'Company', 'Language',
                'Has Password', 'Password Algorithm'
            ]
        else:
            header = [
                'ID', 'Username', 'Email', 'First Name', 'Last Name',
                'Is Active', 'Is Staff', 'Is Superuser', 'Last Login',
                'Date Joined', 'Roles', 'Bio', 'Location', 'Company', 'Language'
            ]

        writer.writerow(header)
        
        for profile in admin_profiles:
            user = profile.user
            
            # Get active roles
            active_assignments = UserRoleAssignment.objects.filter(
                user_profile=profile,
                is_active=True,
                is_approved=True
            ).select_related('role')
            
            roles = '; '.join([assignment.role.name for assignment in active_assignments])

            row_data = [
                user.id,
                user.username,
                user.email,
                user.first_name,
                user.last_name,
                user.is_active,
                user.is_staff,
                user.is_superuser,
                user.last_login.isoformat() if user.last_login else '',
                user.date_joined.isoformat(),
                roles,
                profile.bio,
                profile.location,
                profile.company,
                profile.language
            ]

            # Add password info if requested
            if show_password_info:
                row_data.extend([
                    bool(user.password),
                    user.password.split('$')[0] if user.password else ''
                ])

            writer.writerow(row_data)
