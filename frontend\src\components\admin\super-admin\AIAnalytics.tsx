import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  BarChart3, TrendingUp, TrendingDown, Users, DollarSign,
  Clock, Activity, Brain, Zap, Target, AlertTriangle,
  CheckCircle, RefreshCw, Download, Calendar, Filter
} from 'lucide-react';
import AuthenticatedLayout from '../../layout/AuthenticatedLayout';
import { getAuthToken } from '../../../services/api';

interface AIUsageStats {
  total_requests: number;
  total_cost: number;
  avg_response_time: number;
  success_rate: number;
  active_users: number;
  cost_per_request: number;
  requests_today: number;
  cost_today: number;
}

interface AIUsageData {
  date: string;
  requests: number;
  cost: number;
  response_time: number;
  success_rate: number;
}

interface TopUser {
  username: string;
  requests: number;
  cost: number;
  success_rate: number;
}

interface AIModelStats {
  model: string;
  requests: number;
  cost: number;
  avg_response_time: number;
  success_rate: number;
}

const AIAnalytics: React.FC = () => {
  const { t } = useTranslation();
  const [stats, setStats] = useState<AIUsageStats | null>(null);
  const [usageData, setUsageData] = useState<AIUsageData[]>([]);
  const [topUsers, setTopUsers] = useState<TopUser[]>([]);
  const [modelStats, setModelStats] = useState<AIModelStats[]>([]);
  const [loading, setLoading] = useState(false);
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d'>('30d');
  const [activeTab, setActiveTab] = useState<'overview' | 'usage' | 'users' | 'models'>('overview');

  useEffect(() => {
    fetchAnalytics();
  }, [timeRange]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/superadmin/system/ai_analytics/?range=${timeRange}`, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setStats(data.stats);
        setUsageData(data.usage_data || []);
        setTopUsers(data.top_users || []);
        setModelStats(data.model_stats || []);
      } else {
        console.error('Failed to fetch AI analytics:', response.status);
        setStats(null);
        setUsageData([]);
        setTopUsers([]);
        setModelStats([]);
      }
    } catch (error) {
      console.error('Failed to fetch AI analytics:', error);
      setStats(null);
      setUsageData([]);
      setTopUsers([]);
      setModelStats([]);
    } finally {
      setLoading(false);
    }
  };

  const exportData = async () => {
    try {
      // Create CSV from current data
      const csvContent = [
        'Date,Requests,Cost,Response Time,Success Rate',
        ...usageData.map(item =>
          `${item.date},${item.requests},${item.cost},${item.response_time},${item.success_rate}`
        )
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `ai-analytics-${timeRange}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Failed to export data:', error);
    }
  };

  const formatCurrency = (amount: number) => `$${amount.toFixed(2)}`;
  const formatNumber = (num: number) => num.toLocaleString();
  const formatPercentage = (num: number) => `${num.toFixed(1)}%`;

  const getChangeColor = (change: number) => {
    if (change > 0) return 'text-green-400';
    if (change < 0) return 'text-red-400';
    return 'text-gray-400';
  };

  const tabs = [
    { id: 'overview', name: 'Overview', icon: <BarChart3 className="w-4 h-4" /> },
    { id: 'usage', name: 'Usage Trends', icon: <TrendingUp className="w-4 h-4" /> },
    { id: 'users', name: 'Top Users', icon: <Users className="w-4 h-4" /> },
    { id: 'models', name: 'Model Performance', icon: <Brain className="w-4 h-4" /> }
  ];

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <Activity className="w-5 h-5 text-blue-400" />
              <span className="text-sm text-gray-400">Total Requests</span>
            </div>
          </div>
          <div className="text-2xl font-bold text-white">{formatNumber(stats?.total_requests || 0)}</div>
          <div className="text-sm text-gray-400">
            {formatNumber(stats?.requests_today || 0)} today
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <DollarSign className="w-5 h-5 text-green-400" />
              <span className="text-sm text-gray-400">Total Cost</span>
            </div>
          </div>
          <div className="text-2xl font-bold text-white">{formatCurrency(stats?.total_cost || 0)}</div>
          <div className="text-sm text-gray-400">
            {formatCurrency(stats?.cost_today || 0)} today
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <Clock className="w-5 h-5 text-yellow-400" />
              <span className="text-sm text-gray-400">Avg Response Time</span>
            </div>
          </div>
          <div className="text-2xl font-bold text-white">{stats?.avg_response_time?.toFixed(1) || 0}s</div>
          <div className="text-sm text-gray-400">
            {formatCurrency(stats?.cost_per_request || 0)} per request
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <CheckCircle className="w-5 h-5 text-green-400" />
              <span className="text-sm text-gray-400">Success Rate</span>
            </div>
          </div>
          <div className="text-2xl font-bold text-white">{formatPercentage(stats?.success_rate || 0)}</div>
          <div className="text-sm text-gray-400">
            {formatNumber(stats?.active_users || 0)} active users
          </div>
        </div>
      </div>

      {/* Usage Chart Placeholder */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">Usage Trends ({timeRange})</h3>
        <div className="h-64 flex items-center justify-center border-2 border-dashed border-gray-600 rounded-lg">
          <div className="text-center">
            <BarChart3 className="w-12 h-12 text-gray-500 mx-auto mb-2" />
            <p className="text-gray-500">Chart visualization would go here</p>
            <p className="text-sm text-gray-600">Showing {usageData.length} data points</p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderUsageTrends = () => (
    <div className="space-y-6">
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">Daily Usage Data</h3>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b border-gray-700">
                <th className="text-left py-2 text-gray-400">Date</th>
                <th className="text-right py-2 text-gray-400">Requests</th>
                <th className="text-right py-2 text-gray-400">Cost</th>
                <th className="text-right py-2 text-gray-400">Avg Response</th>
                <th className="text-right py-2 text-gray-400">Success Rate</th>
              </tr>
            </thead>
            <tbody>
              {usageData.slice(-10).map((day) => (
                <tr key={day.date} className="border-b border-gray-800">
                  <td className="py-2 text-white">{new Date(day.date).toLocaleDateString()}</td>
                  <td className="py-2 text-right text-white">{formatNumber(day.requests)}</td>
                  <td className="py-2 text-right text-white">{formatCurrency(day.cost)}</td>
                  <td className="py-2 text-right text-white">{day.response_time.toFixed(1)}s</td>
                  <td className="py-2 text-right text-white">{formatPercentage(day.success_rate)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  const renderTopUsers = () => (
    <div className="space-y-6">
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">Top AI Users</h3>
        <div className="space-y-4">
          {topUsers.map((user, index) => (
            <div key={user.username} className="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold">
                  {index + 1}
                </div>
                <div>
                  <div className="font-medium text-white">{user.username}</div>
                  <div className="text-sm text-gray-400">{formatPercentage(user.success_rate)} success rate</div>
                </div>
              </div>
              <div className="text-right">
                <div className="text-white font-medium">{formatNumber(user.requests)} requests</div>
                <div className="text-sm text-gray-400">{formatCurrency(user.cost)} cost</div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderModelStats = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {modelStats.map((model) => (
          <div key={model.model} className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <div className="flex items-center gap-2 mb-4">
              <Brain className="w-5 h-5 text-purple-400" />
              <h3 className="font-semibold text-white">{model.model}</h3>
            </div>
            
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-400">Requests:</span>
                <span className="text-white">{formatNumber(model.requests)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Cost:</span>
                <span className="text-white">{formatCurrency(model.cost)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Avg Response:</span>
                <span className="text-white">{model.avg_response_time.toFixed(1)}s</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Success Rate:</span>
                <span className="text-white">{formatPercentage(model.success_rate)}</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <AuthenticatedLayout>
      <div className="text-white">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-2">
            <BarChart3 className="w-8 h-8 text-blue-400" />
            <h1 className="text-3xl font-bold text-white">
              {t('superAdmin.aiAnalytics.title', 'AI Analytics')}
            </h1>
          </div>
          <p className="text-gray-400">
            {t('superAdmin.aiAnalytics.subtitle', 'Monitor AI usage, costs, and performance metrics')}
          </p>
        </div>

        {/* Controls */}
        <div className="flex flex-wrap items-center justify-between gap-4 mb-8">
          <div className="flex gap-2">
            {['7d', '30d', '90d'].map((range) => (
              <button
                key={range}
                onClick={() => setTimeRange(range as any)}
                className={`px-4 py-2 rounded-lg transition-colors ${
                  timeRange === range
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                {range === '7d' ? 'Last 7 days' : range === '30d' ? 'Last 30 days' : 'Last 90 days'}
              </button>
            ))}
          </div>
          
          <div className="flex gap-2">
            <button
              onClick={fetchAnalytics}
              disabled={loading}
              className="flex items-center gap-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors disabled:opacity-50"
            >
              {loading ? <RefreshCw className="w-4 h-4 animate-spin" /> : <RefreshCw className="w-4 h-4" />}
              Refresh
            </button>
            <button
              onClick={exportData}
              className="flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg transition-colors"
            >
              <Download className="w-4 h-4" />
              Export
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex flex-wrap gap-2 mb-8 border-b border-gray-700">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center gap-2 px-4 py-2 rounded-t-lg transition-colors ${
                activeTab === tab.id
                  ? 'bg-blue-600 text-white border-b-2 border-blue-400'
                  : 'text-gray-400 hover:text-white hover:bg-gray-800'
              }`}
            >
              {tab.icon}
              {tab.name}
            </button>
          ))}
        </div>

        {/* Content */}
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <RefreshCw className="w-8 h-8 animate-spin text-blue-400" />
            <span className="ml-2 text-gray-400">Loading analytics...</span>
          </div>
        ) : (
          <>
            {activeTab === 'overview' && renderOverview()}
            {activeTab === 'usage' && renderUsageTrends()}
            {activeTab === 'users' && renderTopUsers()}
            {activeTab === 'models' && renderModelStats()}
          </>
        )}
      </div>
    </AuthenticatedLayout>
  );
};

export default AIAnalytics;
