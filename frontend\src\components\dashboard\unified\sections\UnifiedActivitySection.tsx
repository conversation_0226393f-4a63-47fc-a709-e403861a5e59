/**
 * Unified Activity Section
 * Placeholder for activity section that adapts to different user roles
 */

import React from 'react';
import { useTranslation } from 'react-i18next';
import { RTLText } from '../../../common';
import { DashboardRole } from '../../../../types/dashboard';
import { useDashboardTheme } from '../../../../contexts/DashboardContext';
import { Activity } from 'lucide-react';

interface UnifiedActivitySectionProps {
  role: DashboardRole;
  onUpdate?: (sectionId: string, data: any) => void;
  className?: string;
}

const UnifiedActivitySection: React.FC<UnifiedActivitySectionProps> = ({
  role,
  onUpdate,
  className = '',
}) => {
  const { t } = useTranslation();
  const { getCardClasses, getTextClasses } = useDashboardTheme();

  return (
    <div className={className}>
      <div className="mb-6">
        <RTLText as="h2" className={`text-xl font-semibold ${getTextClasses('primary')}`}>
          {t('dashboard.activity', 'Recent Activity')}
        </RTLText>
      </div>
      
      <div className={getCardClasses()}>
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <Activity className={`w-12 h-12 ${getTextClasses('secondary')} mx-auto mb-4`} />
            <RTLText as="p" className={`${getTextClasses('secondary')}`}>
              {t('dashboard.activity.placeholder', 'Activity section for {{role}} coming soon', { role })}
            </RTLText>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UnifiedActivitySection;
