import React, { useState, useEffect } from 'react';
import { Search, Edit, Trash2, Plus, MessageSquare, Heart, MessageCircle, X, Check, RefreshCw, Filter, Tag as TagIcon } from 'lucide-react';
import { DashboardLayout } from '../dashboard';
import { Post, postsAPI, Tag } from '../../../services/api';
import { useAppSelector } from '../../../store/hooks';
import { TablePagination, AdvancedFilter, BulkActions } from '../common';
import { RichTextEditor, TagSelector } from '../../../components/common';
import { FilterValue } from '../common/AdvancedFilter';
import { getPostBulkActions } from '../common/BulkActions';

import { useTranslation } from 'react-i18next';
interface PostFormData {
  title: string;
  content: string;
  image: File | null;
  tag_ids: number[];
}

const PostsManagement: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const { user } = useAppSelector(state => state.auth);
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPost, setSelectedPost] = useState<Post | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [expandedPost, setExpandedPost] = useState<number | null>(null);
  const [formError, setFormError] = useState<string | null>(null);
  const [formSuccess, setFormSuccess] = useState<string | null>(null);
  const [formSubmitting, setFormSubmitting] = useState(false);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Filtering state
  const [activeFilters, setActiveFilters] = useState<FilterValue[]>([]);

  // Bulk actions state
  const [selectedItems, setSelectedItems] = useState<number[]>([]);

  // Selected tags state
  const [selectedTags, setSelectedTags] = useState<Tag[]>([]);

  // Form data for create/edit
  const [formData, setFormData] = useState<PostFormData>({
    title: '',
    content: ''
  });

  // Function to fetch posts with retry mechanism
  const fetchPosts = async (retryCount = 0) => {
    setLoading(true);
    setFormError(null);
    try {
      // Fetch posts from the API
      console.log('Fetching posts... (attempt ' + (retryCount + 1) + ')');
      const posts = await postsAPI.getPosts();
      console.log("Posts fetched:", posts);

      if (Array.isArray(posts)) {
        setPosts(posts);
        setLoading(false);
      } else {
        console.error("Received non-array response:", posts);

        // If we haven't retried too many times, try again
        if (retryCount < 1) {
          console.log("Retrying fetch...");
          setLoading(false); // Set loading to false before retrying
          setTimeout(() => fetchPosts(retryCount + 1), 1000);
        } else {
          setFormError(t("admin.received.invalid.data", "Received invalid data format. Please try again."));
          setLoading(false);
        }
      }
    } catch (error) {
      console.error('Error fetching posts:', error);

      // If we haven't retried too many times, try again
      if (retryCount < 1) {
        console.log('Retrying fetch after error...');
        setLoading(false); // Set loading to false before retrying
        setTimeout(() => fetchPosts(retryCount + 1), 1000);
      } else {
        setFormError(t("admin.failed.to.load", "Failed to load posts. Please try again."));
        setLoading(false);
      }
    }
  };

  // Fetch posts on component mount with a slight delay
  useEffect(() => {
    // Make sure user is authenticated before fetching posts
    if (user?.id) {
      console.log("User authenticated, fetching posts...");

      // Add a small delay before fetching posts
      // This helps ensure the component is fully mounted and authenticated
      const fetchTimer = setTimeout(() => {
        fetchPosts(0); // Start with a fresh fetch attempt
      }, 300);

      // Add a timeout to stop loading after 10 seconds if it's still loading
      const loadingTimeoutTimer = setTimeout(() => {
        setLoading(prevLoading => {
          if (prevLoading) {
            console.log("Loading timeout reached, stopping loading state");
            setFormError(t("admin.loading.took.too", "Loading took too long. Please try again."));
            return false;
          }
          return prevLoading;
        });
      }, 10000);

      // Clean up the timers if the component unmounts
      return () => {
        clearTimeout(fetchTimer);
        clearTimeout(loadingTimeoutTimer);
      };
    } else {
      console.log("Waiting for user authentication...");
    }
  }, [user?.id]); // Re-run when user ID changes (i.e., when user becomes authenticated)

  // Filter posts based on search term and active filters
  const filteredPosts = posts.filter(post => {
    // Search term filter
    const matchesSearch =
      post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.author.username.toLowerCase().includes(searchTerm.toLowerCase());

    if (!matchesSearch) return false;

    // Apply advanced filters
    for (const filter of activeFilters) {
      switch (filter.field) {
        case 'title':
          if (!post.title.toLowerCase().includes(String(filter.value).toLowerCase())) return false;
          break;
        case 'content':
          if (!post.content.toLowerCase().includes(String(filter.value).toLowerCase())) return false;
          break;
        case 'author':
          if (!post.author.username.toLowerCase().includes(String(filter.value).toLowerCase())) return false;
          break;
        case 'moderation_status':
          if (post.moderation_status !== filter.value) return false;
          break;
        case 'created_at':
          if (filter.value instanceof Date) {
            const postDate = new Date(post.created_at);
            const filterDate = filter.value;
            if (postDate.getFullYear() !== filterDate.getFullYear() ||
                postDate.getMonth() !== filterDate.getMonth() ||
                postDate.getDate() !== filterDate.getDate()) {
              return false;
            }
          }
          break;
      }
    }

    return true;
  });

  // Paginate posts
  const indexOfLastPost = currentPage * itemsPerPage;
  const indexOfFirstPost = indexOfLastPost - itemsPerPage;
  const currentPosts = filteredPosts.slice(indexOfFirstPost, indexOfLastPost);

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | string, editorName?: string) => {
    if (typeof e === 'string' && editorName) {
      // Handle rich text editor input
      setFormData(prev => ({ ...prev, [editorName]: e }));
    } else if (typeof e !== 'string') {
      // Handle regular input
      const { name, value } = e.target;
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  // Handle tag selection
  const handleTagsChange = (tags: Tag[]) => {
    setSelectedTags(tags);
    setFormData(prev => ({
      ...prev,
      tag_ids: tags.map(tag => tag.id)
    }));
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      title: '',
      content: '',
      image: null,
      tag_ids: []
    });
    setSelectedTags([]);
    setFormError(null);
    setFormSuccess(null);
  };

  // Open create modal
  const openCreateModal = () => {
    resetForm();
    setIsCreateModalOpen(true);
  };

  // Handle edit post
  const handleEditPost = (post: Post) => {
    setSelectedPost(post);
    setFormData({
      title: post.title,
      content: post.content,
      image: null,
      tag_ids: post.tags.map(tag => tag.id)
    });
    setSelectedTags(post.tags);
    setIsEditModalOpen(true);
  };

  // Handle delete post
  const handleDeletePost = (post: Post) => {
    setSelectedPost(post);
    setIsDeleteModalOpen(true);
  };

  // Create new post
  const createPost = async () => {
    setFormSubmitting(true);
    setFormError(null);
    setFormSuccess(null);

    try {
      // Validate form
      if (!formData.title || !formData.content) {
        setFormError(t("admin.please.fill.in", "Please fill in all required fields"));
        setFormSubmitting(false);
        return;
      }

      // Create post data
      const postData = {
        title: formData.title,
        content: formData.content,
        author_id: user?.id || 0,
        tag_ids: formData.tag_ids
      };

      // Call API to create post
      const newPost = await postsAPI.createPost(postData);

      // Update local state
      setPosts([newPost, ...posts]);

      // Show success message
      setFormSuccess(t("admin.post.created.successfully", "Post created successfully!"));

      // Close modal after a delay
      setTimeout(() => {
        setIsCreateModalOpen(false);
        resetForm();
      }, 1500);
    } catch (error) {
      console.error('Error creating post:', error);
      setFormError(t("admin.failed.to.create", "Failed to create post. Please try again."));
    } finally {
      setFormSubmitting(false);
    }
  };

  // Update post
  const updatePost = async () => {
    if (!selectedPost) return;

    setFormSubmitting(true);
    setFormError(null);
    setFormSuccess(null);

    try {
      // Validate form
      if (!formData.title || !formData.content) {
        setFormError(t("admin.please.fill.in", "Please fill in all required fields"));
        setFormSubmitting(false);
        return;
      }

      // Create post data
      const postData = {
        title: formData.title,
        content: formData.content,
        tag_ids: formData.tag_ids
      };

      // Call API to update post
      const updatedPost = await postsAPI.updatePost(selectedPost.id, postData);

      // Update local state
      setPosts(posts.map(post => post.id === updatedPost.id ? updatedPost : post));

      // Show success message
      setFormSuccess(t("admin.post.updated.successfully", "Post updated successfully!"));

      // Close modal after a delay
      setTimeout(() => {
        setIsEditModalOpen(false);
        setSelectedPost(null);
        resetForm();
      }, 1500);
    } catch (error) {
      console.error('Error updating post:', error);
      setFormError(t("admin.failed.to.update", "Failed to update post. Please try again."));
    } finally {
      setFormSubmitting(false);
    }
  };

  // Confirm delete post
  const confirmDeletePost = async () => {
    if (selectedPost) {
      setFormSubmitting(true);
      try {
        // Call the API to delete the post
        await postsAPI.deletePost(selectedPost.id);
        // Update the local state
        setPosts(posts.filter(post => post.id !== selectedPost.id));
        setIsDeleteModalOpen(false);
        setSelectedPost(null);
      } catch (error) {
        console.error('Error deleting post:', error);
        // Show error message to user
        setFormError(t("admin.failed.to.delete", "Failed to delete post. Please try again."));
      } finally {
        setFormSubmitting(false);
      }
    }
  };

  // Toggle expanded post
  const toggleExpandPost = (postId: number) => {
    setExpandedPost(expandedPost === postId ? null : postId);
  };

  // Handle filter change
  const handleFilterChange = (filters: FilterValue[]) => {
    setActiveFilters(filters);
    setCurrentPage(1); // Reset to first page when filters change
  };

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when items per page changes
  };

  // Handle bulk selection
  const handleSelectAll = () => {
    if (selectedItems.length === filteredPosts.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(filteredPosts.map(post => post.id));
    }
  };

  const handleDeselectAll = () => {
    setSelectedItems([]);
  };

  const toggleItemSelection = (postId: number) => {
    if (selectedItems.includes(postId)) {
      setSelectedItems(selectedItems.filter(id => id !== postId));
    } else {
      setSelectedItems([...selectedItems, postId]);
    }
  };

  // Bulk action handlers
  const handleBulkApprove = async (ids: number[]): Promise<void> => {
    try {
      // Process posts in batches of 5 for better performance
      const batchSize = 5;
      const batches = [];

      for (let i = 0; i < ids.length; i += batchSize) {
        batches.push(ids.slice(i, i + batchSize));
      }

      // Process each batch sequentially
      for (const batch of batches) {
        await Promise.all(
          batch.map(id => postsAPI.moderatePost(id, 'approved', 'Bulk approved by admin'))
        );
      }

      // Refresh posts after bulk action
      await fetchPosts();
      setSelectedItems([]);
      return Promise.resolve();
    } catch (error) {
      console.error('Error approving posts:', error);
      setFormError(t("admin.failed.to.approve", "Failed to approve selected posts"));
      return Promise.reject(error);
    }
  };

  const handleBulkReject = async (ids: number[]): Promise<void> => {
    try {
      // Process posts in batches of 5 for better performance
      const batchSize = 5;
      const batches = [];

      for (let i = 0; i < ids.length; i += batchSize) {
        batches.push(ids.slice(i, i + batchSize));
      }

      // Process each batch sequentially
      for (const batch of batches) {
        await Promise.all(
          batch.map(id => postsAPI.moderatePost(id, 'rejected', 'Bulk rejected by admin'))
        );
      }

      // Refresh posts after bulk action
      await fetchPosts();
      setSelectedItems([]);
      return Promise.resolve();
    } catch (error) {
      console.error('Error rejecting posts:', error);
      setFormError(t("admin.failed.to.reject", "Failed to reject selected posts"));
      return Promise.reject(error);
    }
  };

  const handleBulkDelete = async (ids: number[]): Promise<void> => {
    try {
      // Process posts in batches of 5 for better performance
      const batchSize = 5;
      const batches = [];

      for (let i = 0; i < ids.length; i += batchSize) {
        batches.push(ids.slice(i, i + batchSize));
      }

      // Process each batch sequentially
      for (const batch of batches) {
        await Promise.all(
          batch.map(id => postsAPI.deletePost(id))
        );
      }

      // Update local state
      setPosts(posts.filter(post => !ids.includes(post.id)));
      setSelectedItems([]);
      return Promise.resolve();
    } catch (error) {
      console.error('Error deleting posts:', error);
      setFormError(t("admin.failed.to.delete", "Failed to delete selected posts"));
      return Promise.reject(error);
    }
  };

  // Load empty data as fallback
  const loadMockData = () => {
    setLoading(false);
    setFormError(null);
    setPosts([]);
  };

  // Filter options for posts
  const postFilterOptions = [
    {
      id: 'title',
      label: t("admin.title.type.text", "Title"),
      type: 'text',
      placeholder: 'Filter by title...'
    },
    {
      id: 'content',
      label: t("admin.content.type.text", "Content"),
      type: 'text',
      placeholder: 'Filter by content...'
    },
    {
      id: 'author',
      label: t("admin.author.type.text", "Author"),
      type: 'text',
      placeholder: 'Filter by author...'
    },
    {
      id: 'moderation_status',
      label: t("admin.status.type.select", "Status"),
      type: 'select',
      options: [
        { value: 'pending', label: t("admin.pending", "Pending") },
        { value: 'approved', label: t("admin.approved", "Approved") },
        { value: 'rejected', label: t("admin.rejected", "Rejected") }
      ]
    },
    {
      id: 'created_at',
      label: t("admin.created.date.type", "Created Date"),
      type: 'date'
    }
  ];

  // Bulk actions for posts
  const getPostBulkActions = (
    selectedIds: number[],
    onApprove: (ids: number[]) => Promise<void>,
    onReject: (ids: number[]) => Promise<void>,
    onDelete: (ids: number[]) => Promise<void>
  ) => [
    {
      id: 'approve',
      label: t("admin.approve.selected", "Approve Selected"),
      icon: <Check size={16} className="text-green-400" />,
      action: onApprove,
      variant: 'success'
    },
    {
      id: 'reject',
      label: t("admin.reject.selected", "Reject Selected"),
      icon: <X size={16} className="text-red-400" />,
      action: onReject,
      variant: 'danger'
    },
    {
      id: 'delete',
      label: t("admin.delete.selected", "Delete Selected"),
      icon: <Trash2 size={16} />,
      action: onDelete,
      confirmRequired: true,
      confirmMessage: `Are you sure you want to delete ${selectedIds.length} selected posts? This action cannot be undone.`,
      variant: 'danger'
    }
  ];

  // Prevent default form submission
  const preventFormSubmission = (e: React.FormEvent) => {
    e.preventDefault();
    e.stopPropagation();
    return false;
  };

  return (
    <DashboardLayout currentPage="posts">
      <div onSubmit={preventFormSubmission} className={`mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
        <div>
          <h1 className="text-2xl font-bold">{t("admin.posts.management", "Posts Management")}</h1>
          <div className="text-gray-400 mt-1">{t("admin.manage.community.posts", "Manage community posts and discussions")}</div>
        </div>
        <button
          type="button"
          onClick={openCreateModal}
          className={`mt-4 sm:mt-0 px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300 flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}
        >
          <Plus size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
          Create New Post
        </button>
      </div>

      {/* Advanced Filter Component */}
      <AdvancedFilter
        filterOptions={postFilterOptions}
        onFilterChange={handleFilterChange}
        activeFilters={activeFilters}
      />

      {/* Bulk Actions Component */}
      <BulkActions
        selectedItems={selectedItems}
        totalItems={filteredPosts.length}
        onSelectAll={handleSelectAll}
        onDeselectAll={handleDeselectAll}
        actions={getPostBulkActions(
          selectedItems,
          handleBulkApprove,
          handleBulkReject,
          handleBulkDelete
        )}
      />

      <div className="bg-indigo-900/20 rounded-xl overflow-hidden backdrop-blur-sm shadow-lg">
        <div className="p-4 border-b border-indigo-800/50">
          <div className={`flex flex-col md:flex-row md:items-center gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className={`relative flex-grow ${isRTL ? "flex-row-reverse" : ""}`}>
              <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder={t("admin.search.posts", "Search posts...")}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
              />
            </div>
            <button
              type="button"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                fetchPosts(0); // Start with a fresh fetch attempt
              }}
              className={`px-3 py-2 rounded-lg text-sm bg-indigo-900/30 text-gray-300 hover:bg-indigo-800/30 flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}
              disabled={loading}
            >
              <RefreshCw size={14} className={`mr-1 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </button>
          </div>
        </div>

        {formError && (
          <div className="p-6">
            <div className="p-4 bg-red-900/50 border border-red-500 rounded-lg text-red-200">
              {formError}
              <button
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  fetchPosts(0);
                }}
                className={`ml-4 px-3 py-1 bg-red-700 hover:bg-red-600 rounded-lg text-white text-sm ${isRTL ? "space-x-reverse" : ""}`}
              >
                Try Again
              </button>
            </div>
          </div>
        )}

        {loading ? (
          <div className={`flex flex-col justify-center items-center py-20 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className="w-12 h-12 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mb-6"></div>
            <div className="text-gray-400 mb-4">{t("admin.loading.posts", "Loading posts...")}</div>
            <button
              type="button"
              onClick={loadMockData}
              className="px-4 py-2 bg-indigo-700 hover:bg-indigo-600 rounded-lg text-white"
            >
              Show Sample Data Instead
            </button>
          </div>
        ) : (
          <div className="divide-y divide-indigo-800/30">
            {filteredPosts.length === 0 ? (
              <div className="text-center py-10">
                <div className="text-gray-400 text-lg">{t("admin.no.posts.found", "No posts found matching your search.")}</div>
              </div>
            ) : (
              currentPosts.map((post) => (
                <div key={post.id} className="p-6 hover:bg-indigo-900/10">
                  <div className={`flex justify-between items-start ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className={`flex items-start space-x-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <div className={`flex-shrink-0 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <div
                          className={`w-10 h-10 rounded-full bg-purple-600/30 flex items-center justify-center cursor-pointer ${isRTL ? "flex-row-reverse" : ""}`}
                          onClick={() => toggleItemSelection(post.id)}
                        >
                          {selectedItems.includes(post.id) ? (
                            <Check size={18} className="text-green-400" />
                          ) : (
                            post.author.username.charAt(0).toUpperCase()
                          )}
                        </div>
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold">{post.title}</h3>
                        <div className={`flex items-center text-sm text-gray-400 mt-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                          <span>{post.author.username}</span>
                          <span className="mx-2">•</span>
                          <span>{formatDate(post.created_at)}</span>
                          {post.moderation_status && (
                            <>
                              <span className="mx-2">•</span>
                              <span className={`px-2 py-0.5 rounded-full text-xs ${
                                post.moderation_status === 'approved' ? 'bg-green-900/50 text-green-300' :
                                post.moderation_status === 'rejected' ? 'bg-red-900/50 text-red-300' :
                                'bg-yellow-900/50 text-yellow-300'}
                              }`}>
                                {post.moderation_status.charAt(0).toUpperCase() + post.moderation_status.slice(1)}
                              </span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className={`flex space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <button
                        onClick={() => handleEditPost(post)}
                        className="p-1.5 bg-indigo-800/80 rounded-full text-white hover:bg-indigo-700/80"
                      >
                        <Edit size={16} />
                      </button>
                      <button
                        onClick={() => handleDeletePost(post)}
                        className="p-1.5 bg-red-800/80 rounded-full text-white hover:bg-red-700/80"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </div>

                  <div className="mt-4">
                    <div
                      className={`text-gray-300 rich-text-content ${expandedPost === post.id ? '' : 'line-clamp-2'}`}
                      dangerouslySetInnerHTML={{ __html: post.content }}
                    />
                    {post.content.length > 150 && (
                      <button
                        onClick={() => toggleExpandPost(post.id)}
                        className="text-purple-400 hover:text-purple-300 text-sm mt-1"
                      >
                        {expandedPost === post.id ? t("admin.show.less", "Show less") : t("admin.show.more", "Show more")}
                      </button>
                    )}
                  </div>

                  {post.tags && post.tags.length > 0 && (
                    <div className={`flex flex-wrap gap-2 mt-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                      {post.tags.map(tag => (
                        <div
                          key={tag.id}
                          className={`flex items-center gap-1 bg-indigo-900/50 text-white px-2 py-0.5 rounded-full text-xs ${isRTL ? "flex-row-reverse" : ""}`}
                        >
                          <TagIcon size={12} className="text-purple-400" />
                          <span>{tag.name}</span>
                        </div>
                      ))}
                    </div>
                  )}

                  <div className={`flex items-center mt-4 space-x-6 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className={`flex items-center text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <Heart size={18} className={`mr-1.5 text-red-400 ${isRTL ? "space-x-reverse" : ""}`} />
                      <span>{post.like_count} likes</span>
                    </div>
                    <div className={`flex items-center text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <MessageCircle size={18} className={`mr-1.5 text-blue-400 ${isRTL ? "space-x-reverse" : ""}`} />
                      <span>{post.comments.length} comments</span>
                    </div>
                  </div>

                  {expandedPost === post.id && post.comments.length > 0 && (
                    <div className="mt-6 space-y-4">
                      <h4 className="text-sm font-medium text-gray-300">{t("admin.comments", "Comments")}</h4>
                      <div className="space-y-4 pl-4 border-l border-indigo-800/50">
                        {post.comments.map((comment) => (
                          <div key={comment.id} className={`flex space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                            <div className={`flex-shrink-0 ${isRTL ? "flex-row-reverse" : ""}`}>
                              <div className={`w-8 h-8 rounded-full bg-indigo-600/30 flex items-center justify-center text-xs ${isRTL ? "flex-row-reverse" : ""}`}>
                                {comment.author.username.charAt(0).toUpperCase()}
                              </div>
                            </div>
                            <div>
                              <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                                <span className="font-medium text-sm">{comment.author.username}</span>
                                <span className="mx-2 text-xs text-gray-500">•</span>
                                <span className="text-xs text-gray-500">{formatDate(comment.created_at)}</span>
                              </div>
                              <div className="text-sm text-gray-300 mt-1">{comment.content}</div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))
            )}

            {/* Pagination Component */}
            {filteredPosts.length > 0 && (
              <div className="p-4 border-t border-indigo-800/50">
                <TablePagination
                  totalItems={filteredPosts.length}
                  itemsPerPage={itemsPerPage}
                  currentPage={currentPage}
                  onPageChange={handlePageChange}
                  onItemsPerPageChange={handleItemsPerPageChange}
                />
              </div>
            )}
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {isDeleteModalOpen && selectedPost && (
        <div className={`fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70 backdrop-blur-sm ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="bg-gradient-to-b from-indigo-900/90 to-purple-900/90 rounded-xl p-6 max-w-md w-full">
            <h3 className="text-xl font-semibold mb-4">{t("admin.confirm.deletion", "Confirm Deletion")}</h3>
            <div className="text-gray-300 mb-6">
              Are you sure you want to delete the post <span className="font-medium">{selectedPost.title}</span>? This action cannot be undone.
            </div>
            <div className={`flex justify-end space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                type="button"
                onClick={() => setIsDeleteModalOpen(false)}
                className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg"
                disabled={formSubmitting}
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={confirmDeletePost}
                className={`px-4 py-2 bg-red-600 hover:bg-red-500 rounded-lg flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                disabled={formSubmitting}
              >
                {formSubmitting ? (
                  <>
                    <div className={`w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 ${isRTL ? "space-x-reverse" : ""}`}></div>
                    Deleting...
                  </>
                ) : (
                  <>
                    <Trash2 size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                    Delete
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Create Post Modal */}
      {isCreateModalOpen && (
        <div className={`fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70 backdrop-blur-sm overflow-y-auto ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="bg-gradient-to-b from-indigo-900/90 to-purple-900/90 rounded-xl p-6 max-w-2xl w-full my-8">
            <div className={`flex justify-between items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
              <h3 className="text-xl font-semibold">{t("admin.create.new.post", "Create New Post")}</h3>
              <button
                type="button"
                onClick={() => setIsCreateModalOpen(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <X size={24} />
              </button>
            </div>

            {formError && (
              <div className="mb-4 p-3 bg-red-900/50 border border-red-500 rounded-lg text-red-200">
                {formError}
              </div>
            )}

            {formSuccess && (
              <div className="mb-4 p-3 bg-green-900/50 border border-green-500 rounded-lg text-green-200">
                {formSuccess}
              </div>
            )}

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Post Title *</label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  placeholder={t("admin.enter.post.title", "Enter post title")}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Content *</label>
                <RichTextEditor
                  value={formData.content}
                  onChange={(value) => handleInputChange(value, 'content')}
                  placeholder={t("admin.write.your.post", "Write your post content here...")}
                  className="min-h-[300px]"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">{t("admin.tags", "Tags")}</label>
                <TagSelector
                  selectedTags={selectedTags}
                  onChange={handleTagsChange}
                  className="w-full"
                />
              </div>

              <div className={`flex justify-end space-x-3 mt-6 ${isRTL ? "flex-row-reverse" : ""}`}>
                <button
                  type="button"
                  onClick={() => setIsCreateModalOpen(false)}
                  className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg"
                  disabled={formSubmitting}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={createPost}
                  className={`px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300 flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}
                  disabled={formSubmitting}
                >
                  {formSubmitting ? (
                    <>
                      <div className={`w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 ${isRTL ? "space-x-reverse" : ""}`}></div>
                      Creating...
                    </>
                  ) : (
                    <>
                      <Plus size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                      Create Post
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit Post Modal */}
      {isEditModalOpen && selectedPost && (
        <div className={`fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70 backdrop-blur-sm overflow-y-auto ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="bg-gradient-to-b from-indigo-900/90 to-purple-900/90 rounded-xl p-6 max-w-2xl w-full my-8">
            <div className={`flex justify-between items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
              <h3 className="text-xl font-semibold">{t("admin.edit.post", "Edit Post")}</h3>
              <button
                type="button"
                onClick={() => setIsEditModalOpen(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <X size={24} />
              </button>
            </div>

            {formError && (
              <div className="mb-4 p-3 bg-red-900/50 border border-red-500 rounded-lg text-red-200">
                {formError}
              </div>
            )}

            {formSuccess && (
              <div className="mb-4 p-3 bg-green-900/50 border border-green-500 rounded-lg text-green-200">
                {formSuccess}
              </div>
            )}

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Post Title *</label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  placeholder={t("admin.enter.post.title", "Enter post title")}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Content *</label>
                <RichTextEditor
                  value={formData.content}
                  onChange={(value) => handleInputChange(value, 'content')}
                  placeholder={t("admin.write.your.post", "Write your post content here...")}
                  className="min-h-[300px]"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">{t("admin.tags", "Tags")}</label>
                <TagSelector
                  selectedTags={selectedTags}
                  onChange={handleTagsChange}
                  className="w-full"
                />
              </div>

              <div className={`flex justify-end space-x-3 mt-6 ${isRTL ? "flex-row-reverse" : ""}`}>
                <button
                  type="button"
                  onClick={() => setIsEditModalOpen(false)}
                  className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg"
                  disabled={formSubmitting}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={updatePost}
                  className={`px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300 flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}
                  disabled={formSubmitting}
                >
                  {formSubmitting ? (
                    <>
                      <div className={`w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 ${isRTL ? "space-x-reverse" : ""}`}></div>
                      Updating...
                    </>
                  ) : (
                    <>
                      <Check size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                      Update Post
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </DashboardLayout>
  );
};

export default PostsManagement;
