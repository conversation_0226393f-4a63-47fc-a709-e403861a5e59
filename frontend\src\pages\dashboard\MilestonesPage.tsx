import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { 
  Target, 
  Plus, 
  Edit, 
  Trash2, 
  Calendar, 
  Clock,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Filter
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { useAppSelector } from '../../store/hooks';
// MainLayout removed - handled by routing system
import { BusinessIdea, businessIdeasAPI } from '../../services/incubatorApi';
import MilestoneForm from '../../components/incubator/MilestoneForm';
import ConfirmDialog from '../../components/common/ConfirmDialog';
import useCRUD from '../../hooks/useCRUD';

interface Milestone {
  id: number;
  title: string;
  description: string;
  due_date: string;
  status: 'not_started' | 'in_progress' | 'completed' | 'delayed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'critical';
  business_idea: number;
  assigned_to?: number;
  completion_date?: string;
  completion_notes?: string;
  created_at: string;
  updated_at: string;
}

// Use real milestones API instead of mock data
import { businessMilestonesAPI } from '../../services/milestoneApi';

const MilestonesPage: React.FC = () => {
  const { businessIdeaId } = useParams<{ businessIdeaId: string }>();
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { user } = useAppSelector(state => state.auth);

  const [businessIdea, setBusinessIdea] = useState<BusinessIdea | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showMilestoneForm, setShowMilestoneForm] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [editingMilestone, setEditingMilestone] = useState<Milestone | null>(null);
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [priorityFilter, setPriorityFilter] = useState<string | null>(null);

  // CRUD operations for milestones using real API
  const milestonesCRUD = useCRUD({
    create: (data) => businessMilestonesAPI.createMilestone(data),
    read: () => businessMilestonesAPI.getMilestones(Number(businessIdeaId)),
    update: (id, data) => businessMilestonesAPI.updateMilestone(id, data),
    delete: (id) => businessMilestonesAPI.deleteMilestone(id)
  }, {
    onSuccess: (operation) => {
      if (operation === 'create') {
        setShowMilestoneForm(false);
      } else if (operation === 'update') {
        setEditingMilestone(null);
        setShowMilestoneForm(false);
      } else if (operation === 'delete') {
        setShowDeleteDialog(false);
      }
    }
  });

  useEffect(() => {
    if (businessIdeaId) {
      fetchBusinessIdea();
      milestonesCRUD.readItems();
    }
  }, [businessIdeaId]);

  const fetchBusinessIdea = async () => {
    if (!businessIdeaId) return;
    
    try {
      setLoading(true);
      setError(null);
      const idea = await businessIdeasAPI.getBusinessIdea(parseInt(businessIdeaId));
      setBusinessIdea(idea);
    } catch (err) {
      setError(err instanceof Error ? err.message : t('common.error.unknown'));
    } finally {
      setLoading(false);
    }
  };

  const handleCreateMilestone = () => {
    setEditingMilestone(null);
    setShowMilestoneForm(true);
  };

  const handleEditMilestone = (milestone: Milestone) => {
    setEditingMilestone(milestone);
    setShowMilestoneForm(true);
  };

  const handleDeleteMilestone = (milestone: Milestone) => {
    milestonesCRUD.setSelectedItem(milestone);
    setShowDeleteDialog(true);
  };

  const confirmDelete = async () => {
    if (milestonesCRUD.selectedItem) {
      await milestonesCRUD.deleteItem(milestonesCRUD.selectedItem.id);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle size={16} className="text-green-400" />;
      case 'in_progress':
        return <Clock size={16} className="text-blue-400" />;
      case 'delayed':
        return <AlertTriangle size={16} className="text-yellow-400" />;
      case 'cancelled':
        return <XCircle size={16} className="text-red-400" />;
      default:
        return <Target size={16} className="text-gray-400" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical':
        return 'bg-red-600';
      case 'high':
        return 'bg-orange-600';
      case 'medium':
        return 'bg-yellow-600';
      case 'low':
        return 'bg-green-600';
      default:
        return 'bg-gray-600';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-400 bg-green-900/30';
      case 'in_progress':
        return 'text-blue-400 bg-blue-900/30';
      case 'delayed':
        return 'text-yellow-400 bg-yellow-900/30';
      case 'cancelled':
        return 'text-red-400 bg-red-900/30';
      default:
        return 'text-gray-400 bg-gray-900/30';
    }
  };

  const filteredMilestones = milestonesCRUD.data.filter(milestone => {
    const matchesStatus = !statusFilter || milestone.status === statusFilter;
    const matchesPriority = !priorityFilter || milestone.priority === priorityFilter;
    return matchesStatus && matchesPriority;
  });

  const canEdit = businessIdea && user && (
    businessIdea.owner.id === user.id || 
    businessIdea.collaborators?.some(collab => collab.id === user.id) ||
    user.is_admin
  );

  if (loading) {
    return (
      <AuthenticatedLayout>
      <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="flex justify-center items-center h-64">
          <div className="w-16 h-16 border-4 border-green-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
                </div>
        </div>
      </div>
    </AuthenticatedLayout>
    );
  }

  if (error || !businessIdea) {
    return (
      <AuthenticatedLayout>
        <div className="text-center py-12">
          <Target size={48} className="mx-auto text-gray-600 mb-4" />
          <h3 className="text-lg font-medium text-gray-400 mb-2">
            {error || t('incubator.businessIdea.notFound')}
          </h3>
        </div>
      </AuthenticatedLayout>
    );
  }

  return (
    <AuthenticatedLayout>
      <div className="p-6">
        {/* Header */}
        <div className={`flex items-center justify-between mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div>
            <h1 className="text-2xl font-bold text-white">
              {t('incubator.milestones.title')}
            </h1>
            <p className="text-gray-400 mt-1">
              {t('incubator.milestones.forIdea')}: {businessIdea.title}
            </p>
          </div>

          {canEdit && (
            <button
              onClick={handleCreateMilestone}
              className="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg transition-colors flex items-center text-white"
            >
              <Plus size={16} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
              {t('incubator.milestones.create')}
            </button>
          )}
        </div>

        {/* Filters */}
        <div className={`flex gap-4 mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={`flex gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <button
              onClick={() => setStatusFilter(null)}
              className={`px-3 py-2 rounded-lg text-sm flex items-center ${
                statusFilter === null ? 'bg-gray-700 text-white' : 'bg-gray-800 text-gray-400 hover:text-white'
              }`}
            >
              <Filter size={14} className={`${isRTL ? 'ml-1' : 'mr-1'}`} />
              {t('common.all')}
            </button>
            {['not_started', 'in_progress', 'completed', 'delayed', 'cancelled'].map(status => (
              <button
                key={status}
                onClick={() => setStatusFilter(status)}
                className={`px-3 py-2 rounded-lg text-sm flex items-center ${
                  statusFilter === status ? 'bg-gray-700 text-white' : 'bg-gray-800 text-gray-400 hover:text-white'
                }`}
              >
                {getStatusIcon(status)}
                <span className={`${isRTL ? 'mr-1' : 'ml-1'}`}>
                  {t(`incubator.milestone.status.${status}`)}
                </span>
              </button>
            ))}
          </div>
        </div>

        {/* Milestones List */}
        {milestonesCRUD.isLoading ? (
          <div className="flex justify-center py-12">
            <div className="w-16 h-16 border-4 border-green-500 border-t-transparent rounded-full animate-spin"></div>
          </div>
        ) : filteredMilestones.length > 0 ? (
          <div className="space-y-4">
            {filteredMilestones.map((milestone) => (
              <div
                key={milestone.id}
                className="bg-gray-800/50 rounded-lg p-6 border border-gray-700 hover:border-gray-600 transition-colors"
              >
                <div className={`flex items-start justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className="flex-1">
                    <div className={`flex items-center gap-3 mb-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <h3 className="text-lg font-semibold text-white">{milestone.title}</h3>
                      <span className={`px-2 py-1 rounded text-xs ${getPriorityColor(milestone.priority)}`}>
                        {t(`incubator.milestone.priority.${milestone.priority}`)}
                      </span>
                      <span className={`px-2 py-1 rounded text-xs ${getStatusColor(milestone.status)}`}>
                        {getStatusIcon(milestone.status)}
                        <span className={`${isRTL ? 'mr-1' : 'ml-1'}`}>
                          {t(`incubator.milestone.status.${milestone.status}`)}
                        </span>
                      </span>
                    </div>
                    <p className={`text-gray-300 mb-3 ${isRTL ? 'text-right' : ''}`}>
                      {milestone.description}
                    </p>
                    <div className={`flex items-center gap-4 text-sm text-gray-400 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <Calendar size={14} className={`${isRTL ? 'ml-1' : 'mr-1'}`} />
                        {t('incubator.milestone.dueDate')}: {new Date(milestone.due_date).toLocaleDateString()}
                      </div>
                      {milestone.completion_date && (
                        <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                          <CheckCircle size={14} className={`${isRTL ? 'ml-1' : 'mr-1'}`} />
                          {t('incubator.milestone.completed')}: {new Date(milestone.completion_date).toLocaleDateString()}
                        </div>
                      )}
                    </div>
                    {milestone.completion_notes && (
                      <div className="mt-3 p-3 bg-green-900/20 rounded-lg">
                        <p className={`text-green-300 text-sm ${isRTL ? 'text-right' : ''}`}>
                          <strong>{t('incubator.milestone.completionNotes')}:</strong> {milestone.completion_notes}
                        </p>
                      </div>
                    )}
                  </div>

                  {canEdit && (
                    <div className={`flex gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <button
                        onClick={() => handleEditMilestone(milestone)}
                        className="p-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
                        title={t('common.edit')}
                      >
                        <Edit size={16} />
                      </button>
                      <button
                        onClick={() => handleDeleteMilestone(milestone)}
                        className="p-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors"
                        title={t('common.delete')}
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Target size={48} className="mx-auto text-gray-600 mb-4" />
            <h3 className="text-lg font-medium text-gray-400 mb-2">
              {statusFilter || priorityFilter
                ? t('incubator.milestones.noFilteredMilestones')
                : t('incubator.milestones.noMilestones')
              }
            </h3>
            {canEdit && !statusFilter && !priorityFilter && (
              <button
                onClick={handleCreateMilestone}
                className="mt-4 px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg transition-colors text-white"
              >
                {t('incubator.milestones.createFirst')}
              </button>
            )}
          </div>
        )}
      </div>

      {/* Modals */}
      {showMilestoneForm && businessIdea && (
        <MilestoneForm
          milestone={editingMilestone || undefined}
          businessIdea={businessIdea}
          onSave={editingMilestone ? 
            (data) => milestonesCRUD.updateItem(editingMilestone.id, data) :
            milestonesCRUD.createItem
          }
          onCancel={() => {
            setShowMilestoneForm(false);
            setEditingMilestone(null);
          }}
          isLoading={milestonesCRUD.isLoading}
          mode={editingMilestone ? 'edit' : 'create'}
        />
      )}

      <ConfirmDialog
        isOpen={showDeleteDialog}
        onClose={() => setShowDeleteDialog(false)}
        onConfirm={confirmDelete}
        title={t('incubator.milestones.deleteTitle')}
        message={t('incubator.milestones.deleteMessage', { 
          title: milestonesCRUD.selectedItem?.title 
        })}
        confirmText={t('common.delete')}
        type="danger"
        isLoading={milestonesCRUD.isLoading}
      />
    </AuthenticatedLayout>
  );
};

export default MilestonesPage;
