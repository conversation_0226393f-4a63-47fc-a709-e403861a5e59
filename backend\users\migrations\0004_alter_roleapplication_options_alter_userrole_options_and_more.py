# Generated by Django 4.2.21 on 2025-06-04 05:07

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0003_userrole_userprofile_company_userprofile_is_active_and_more"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="roleapplication",
            options={
                "ordering": ["-created_at"],
                "verbose_name": "Role Application",
                "verbose_name_plural": "Role Applications",
            },
        ),
        migrations.AlterModelOptions(
            name="userrole",
            options={
                "ordering": ["name"],
                "verbose_name": "User Role",
                "verbose_name_plural": "User Roles",
            },
        ),
        migrations.AlterModelOptions(
            name="userroleassignment",
            options={
                "ordering": ["-assigned_at"],
                "verbose_name": "User Role Assignment",
                "verbose_name_plural": "User Role Assignments",
            },
        ),
        migrations.AlterUniqueTogether(
            name="roleapplication",
            unique_together=set(),
        ),
        migrations.RemoveField(
            model_name="userprofile",
            name="is_verified",
        ),
        migrations.RemoveField(
            model_name="userprofile",
            name="position",
        ),
        migrations.RemoveField(
            model_name="userprofile",
            name="verification_date",
        ),
        migrations.RemoveField(
            model_name="userrole",
            name="can_access_analytics",
        ),
        migrations.RemoveField(
            model_name="userrole",
            name="can_approve_ideas",
        ),
        migrations.RemoveField(
            model_name="userrole",
            name="can_create_events",
        ),
        migrations.RemoveField(
            model_name="userrole",
            name="can_manage_funding",
        ),
        migrations.RemoveField(
            model_name="userrole",
            name="can_manage_users",
        ),
        migrations.RemoveField(
            model_name="userrole",
            name="can_moderate_content",
        ),
        migrations.RemoveField(
            model_name="userrole",
            name="can_provide_mentorship",
        ),
        migrations.RemoveField(
            model_name="userrole",
            name="can_review_applications",
        ),
        migrations.AddField(
            model_name="userrole",
            name="requires_approval",
            field=models.BooleanField(
                default=True, help_text="Whether this role requires admin approval"
            ),
        ),
        migrations.AlterField(
            model_name="userprofile",
            name="company",
            field=models.CharField(blank=True, max_length=100),
        ),
    ]
