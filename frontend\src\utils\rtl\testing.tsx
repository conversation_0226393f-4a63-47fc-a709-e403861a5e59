/**
 * RTL Testing Utilities
 *
 * This file contains utility functions for testing RTL components.
 */

import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import languageReducer from '../../store/languageSlice';

/**
 * Interface for RTL test options
 */
interface RTLTestOptions extends Omit<RenderOptions, 'wrapper'> {
  /**
   * The initial language to use for testing
   */
  initialLanguage?: 'en' | 'ar';

  /**
   * Whether to use RTL direction for testing
   */
  isRTL?: boolean;
}

/**
 * Creates a test store with the specified language and direction
 * @param language The language to use
 * @param isRTL Whether to use RTL direction
 * @returns A configured Redux store
 */
export const createTestStore = (language: string = 'en', isRTL: boolean = false) => {
  return configureStore({
    reducer: {
      language: languageReducer,
    },
    preloadedState: {
      language: {
        language,
        direction: isRTL ? 'rtl' : 'ltr',
        availableLanguages: {
          en: 'English',
          ar: 'Arabic',
        },
        isLoading: false,
        error: null,
      },
    },
  });
};

/**
 * Renders a component with RTL testing utilities
 * @param ui The component to render
 * @param options The test options
 * @returns The rendered component
 */
export const renderWithRTL = (
  ui: ReactElement,
  options: RTLTestOptions = {}
) => {
  const {
    initialLanguage = 'en',
    isRTL = initialLanguage === 'ar',
    ...renderOptions
  } = options;

  const store = createTestStore(initialLanguage, isRTL);

  const Wrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    return <Provider store={store}>{children}</Provider>;
  };

  return render(ui, { wrapper: Wrapper, ...renderOptions });
};

/**
 * Checks if an element has RTL-specific styles
 * @param element The element to check
 * @returns True if the element has RTL-specific styles
 */
export const hasRTLStyles = (element: HTMLElement): boolean => {
  const computedStyle = window.getComputedStyle(element);

  // Check for RTL-specific styles
  const hasRTLDirection = computedStyle.direction === 'rtl';
  const hasRTLTextAlign = computedStyle.textAlign === 'right';
  const hasRTLTransform = computedStyle.transform.includes('scaleX(-1)');

  return hasRTLDirection || hasRTLTextAlign || hasRTLTransform;
};

export default {
  renderWithRTL,
  createTestStore,
  hasRTLStyles,
};
