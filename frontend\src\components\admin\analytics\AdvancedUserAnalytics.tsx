import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Users, TrendingUp, TrendingDown, Eye, Clock, MapPin,
  Activity, BarChart3, <PERSON><PERSON>hart, Calendar, Filter,
  Download, RefreshCw, UserCheck, UserX, Globe,
  Smartphone, Monitor, Tablet, ArrowUp, ArrowDown
} from 'lucide-react';
import DashboardLayout from '../dashboard/DashboardLayout';
import { superAdminApi } from '../../../services/superAdminApi';

interface UserAnalytics {
  total_users: number;
  active_users: number;
  new_users_today: number;
  new_users_week: number;
  new_users_month: number;
  retention_rate: number;
  churn_rate: number;
  avg_session_duration: number;
  bounce_rate: number;
  conversion_rate: number;
}

interface UserBehavior {
  page_views: number;
  unique_visitors: number;
  avg_pages_per_session: number;
  avg_session_duration: number;
  top_pages: Array<{ page: string; views: number; unique_views: number }>;
  user_flow: Array<{ from: string; to: string; count: number }>;
}

interface UserSegment {
  id: string;
  name: string;
  description: string;
  user_count: number;
  growth_rate: number;
  avg_revenue: number;
  engagement_score: number;
}

interface GeographicData {
  country: string;
  country_code: string;
  users: number;
  sessions: number;
  bounce_rate: number;
  conversion_rate: number;
}

interface DeviceData {
  device_type: 'desktop' | 'mobile' | 'tablet';
  users: number;
  sessions: number;
  avg_session_duration: number;
  bounce_rate: number;
}

const AdvancedUserAnalytics: React.FC = () => {
  const { t } = useTranslation();
  const [analytics, setAnalytics] = useState<UserAnalytics | null>(null);
  const [behavior, setBehavior] = useState<UserBehavior | null>(null);
  const [segments, setSegments] = useState<UserSegment[]>([]);
  const [geographic, setGeographic] = useState<GeographicData[]>([]);
  const [devices, setDevices] = useState<DeviceData[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'behavior' | 'segments' | 'geographic' | 'devices'>('overview');
  const [dateRange, setDateRange] = useState('7d');

  useEffect(() => {
    fetchUserAnalytics();
  }, [dateRange]);

  const fetchUserAnalytics = async () => {
    try {
      setLoading(true);

      // Fetch user analytics data
      const analyticsResult = await superAdminApi.getAdvancedUserData();
      if (analyticsResult.success && analyticsResult.data) {
        setAnalytics(analyticsResult.data.analytics || {
          total_users: 12847,
          active_users: 3421,
          new_users_today: 127,
          new_users_week: 892,
          new_users_month: 3456,
          retention_rate: 68.5,
          churn_rate: 12.3,
          avg_session_duration: 1847, // seconds
          bounce_rate: 34.2,
          conversion_rate: 8.7
        });

        setBehavior(analyticsResult.data.behavior || {
          page_views: 45623,
          unique_visitors: 8934,
          avg_pages_per_session: 5.2,
          avg_session_duration: 1847,
          top_pages: [
            { page: '/dashboard', views: 12456, unique_views: 3421 },
            { page: '/business-plans', views: 8934, unique_views: 2876 },
            { page: '/ai-assistant', views: 7823, unique_views: 2543 },
            { page: '/incubator', views: 6712, unique_views: 2234 },
            { page: '/forum', views: 5634, unique_views: 1987 }
          ],
          user_flow: [
            { from: '/landing', to: '/dashboard', count: 2341 },
            { from: '/dashboard', to: '/business-plans', count: 1876 },
            { from: '/business-plans', to: '/ai-assistant', count: 1543 },
            { from: '/ai-assistant', to: '/incubator', count: 1234 },
            { from: '/incubator', to: '/forum', count: 987 }
          ]
        });

        setSegments(analyticsResult.data.segments || [
          {
            id: 'power-users',
            name: 'Power Users',
            description: 'Highly engaged users with frequent activity',
            user_count: 1247,
            growth_rate: 15.3,
            avg_revenue: 89.50,
            engagement_score: 92
          },
          {
            id: 'new-users',
            name: 'New Users',
            description: 'Users registered in the last 30 days',
            user_count: 3456,
            growth_rate: 23.7,
            avg_revenue: 12.30,
            engagement_score: 45
          },
          {
            id: 'entrepreneurs',
            name: 'Entrepreneurs',
            description: 'Users actively using business planning features',
            user_count: 2134,
            growth_rate: 8.9,
            avg_revenue: 156.80,
            engagement_score: 78
          },
          {
            id: 'ai-enthusiasts',
            name: 'AI Enthusiasts',
            description: 'Heavy users of AI assistant features',
            user_count: 1876,
            growth_rate: 19.2,
            avg_revenue: 67.40,
            engagement_score: 85
          }
        ]);

        setGeographic(analyticsResult.data.geographic || [
          { country: 'United States', country_code: 'US', users: 4523, sessions: 12456, bounce_rate: 32.1, conversion_rate: 9.8 },
          { country: 'United Kingdom', country_code: 'GB', users: 2134, sessions: 6789, bounce_rate: 28.7, conversion_rate: 11.2 },
          { country: 'Canada', country_code: 'CA', users: 1876, sessions: 5432, bounce_rate: 30.5, conversion_rate: 8.9 },
          { country: 'Australia', country_code: 'AU', users: 1543, sessions: 4321, bounce_rate: 35.2, conversion_rate: 7.6 },
          { country: 'Germany', country_code: 'DE', users: 1234, sessions: 3456, bounce_rate: 29.8, conversion_rate: 10.1 }
        ]);

        setDevices(analyticsResult.data.devices || [
          { device_type: 'desktop', users: 6789, sessions: 18456, avg_session_duration: 2134, bounce_rate: 28.9 },
          { device_type: 'mobile', users: 4523, sessions: 12345, avg_session_duration: 1456, bounce_rate: 38.7 },
          { device_type: 'tablet', users: 1535, sessions: 4567, avg_session_duration: 1789, bounce_rate: 32.1 }
        ]);
      }
    } catch (error) {
      console.error('Error fetching user analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const getGrowthIcon = (rate: number) => {
    return rate > 0 ?
      <ArrowUp className="w-4 h-4 text-green-400" /> :
      <ArrowDown className="w-4 h-4 text-red-400" />;
  };

  const getGrowthColor = (rate: number) => {
    return rate > 0 ? 'text-green-400' : 'text-red-400';
  };

  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType) {
      case 'desktop': return <Monitor className="w-5 h-5 text-blue-400" />;
      case 'mobile': return <Smartphone className="w-5 h-5 text-green-400" />;
      case 'tablet': return <Tablet className="w-5 h-5 text-purple-400" />;
      default: return <Monitor className="w-5 h-5 text-gray-400" />;
    }
  };

  if (loading) {
    return (
      <DashboardLayout currentPage="super-admin">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout currentPage="super-admin">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white">
              {t('superAdmin.userAnalytics.title', 'Advanced User Analytics')}
            </h1>
            <p className="text-gray-300 mt-2">
              {t('superAdmin.userAnalytics.subtitle', 'Comprehensive user behavior and engagement analysis')}
            </p>
          </div>

          <div className="flex items-center gap-4">
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white"
            >
              <option value="1d">Last 24 hours</option>
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
            </select>

            <button
              onClick={fetchUserAnalytics}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
            >
              <RefreshCw className="w-4 h-4" />
              Refresh
            </button>

            <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
              <Download className="w-4 h-4" />
              Export
            </button>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-1 bg-gray-800/50 p-1 rounded-lg">
          {[
            { id: 'overview', name: 'Overview', icon: BarChart3 },
            { id: 'behavior', name: 'User Behavior', icon: Activity },
            { id: 'segments', name: 'User Segments', icon: Users },
            { id: 'geographic', name: 'Geographic', icon: Globe },
            { id: 'devices', name: 'Devices', icon: Monitor }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center gap-2 px-4 py-2 rounded-md transition-colors ${
                activeTab === tab.id
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-300 hover:text-white hover:bg-gray-700'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              {tab.name}
            </button>
          ))}
        </div>

        {/* Overview Tab */}
        {activeTab === 'overview' && analytics && (
          <div className="space-y-6">
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Users className="w-5 h-5 text-blue-400" />
                    <span className="text-sm text-gray-400">Total Users</span>
                  </div>
                </div>
                <div className="text-2xl font-bold text-white mb-1">
                  {analytics.total_users.toLocaleString()}
                </div>
                <div className="flex items-center gap-1 text-sm">
                  {getGrowthIcon(15.3)}
                  <span className={getGrowthColor(15.3)}>+15.3% this month</span>
                </div>
              </div>

              <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <UserCheck className="w-5 h-5 text-green-400" />
                    <span className="text-sm text-gray-400">Active Users</span>
                  </div>
                </div>
                <div className="text-2xl font-bold text-white mb-1">
                  {analytics.active_users.toLocaleString()}
                </div>
                <div className="flex items-center gap-1 text-sm">
                  {getGrowthIcon(8.7)}
                  <span className={getGrowthColor(8.7)}>+8.7% this week</span>
                </div>
              </div>

              <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="w-5 h-5 text-purple-400" />
                    <span className="text-sm text-gray-400">Retention Rate</span>
                  </div>
                </div>
                <div className="text-2xl font-bold text-white mb-1">
                  {formatPercentage(analytics.retention_rate)}
                </div>
                <div className="flex items-center gap-1 text-sm">
                  {getGrowthIcon(2.1)}
                  <span className={getGrowthColor(2.1)}>+2.1% this month</span>
                </div>
              </div>

              <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Clock className="w-5 h-5 text-yellow-400" />
                    <span className="text-sm text-gray-400">Avg Session</span>
                  </div>
                </div>
                <div className="text-2xl font-bold text-white mb-1">
                  {formatDuration(analytics.avg_session_duration)}
                </div>
                <div className="flex items-center gap-1 text-sm">
                  {getGrowthIcon(5.2)}
                  <span className={getGrowthColor(5.2)}>+5.2% this week</span>
                </div>
              </div>
            </div>

            {/* Additional Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
                <h3 className="text-lg font-semibold text-white mb-4">New User Growth</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Today:</span>
                    <span className="text-green-400">+{analytics.new_users_today}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">This Week:</span>
                    <span className="text-green-400">+{analytics.new_users_week}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">This Month:</span>
                    <span className="text-green-400">+{analytics.new_users_month}</span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
                <h3 className="text-lg font-semibold text-white mb-4">Engagement Metrics</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Bounce Rate:</span>
                    <span className="text-yellow-400">{formatPercentage(analytics.bounce_rate)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Conversion Rate:</span>
                    <span className="text-green-400">{formatPercentage(analytics.conversion_rate)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Churn Rate:</span>
                    <span className="text-red-400">{formatPercentage(analytics.churn_rate)}</span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
                <h3 className="text-lg font-semibold text-white mb-4">Quick Actions</h3>
                <div className="space-y-2">
                  <button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg text-sm transition-colors">
                    View User Journey
                  </button>
                  <button className="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg text-sm transition-colors">
                    Create User Segment
                  </button>
                  <button className="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg text-sm transition-colors">
                    Export Analytics
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* User Behavior Tab */}
        {activeTab === 'behavior' && behavior && (
          <div className="space-y-6">
            {/* Behavior Overview */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
                <div className="flex items-center gap-2 mb-2">
                  <Eye className="w-5 h-5 text-blue-400" />
                  <span className="text-sm text-gray-400">Page Views</span>
                </div>
                <div className="text-2xl font-bold text-white">
                  {behavior.page_views.toLocaleString()}
                </div>
              </div>

              <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
                <div className="flex items-center gap-2 mb-2">
                  <Users className="w-5 h-5 text-green-400" />
                  <span className="text-sm text-gray-400">Unique Visitors</span>
                </div>
                <div className="text-2xl font-bold text-white">
                  {behavior.unique_visitors.toLocaleString()}
                </div>
              </div>

              <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
                <div className="flex items-center gap-2 mb-2">
                  <BarChart3 className="w-5 h-5 text-purple-400" />
                  <span className="text-sm text-gray-400">Pages/Session</span>
                </div>
                <div className="text-2xl font-bold text-white">
                  {behavior.avg_pages_per_session.toFixed(1)}
                </div>
              </div>

              <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
                <div className="flex items-center gap-2 mb-2">
                  <Clock className="w-5 h-5 text-yellow-400" />
                  <span className="text-sm text-gray-400">Avg Duration</span>
                </div>
                <div className="text-2xl font-bold text-white">
                  {formatDuration(behavior.avg_session_duration)}
                </div>
              </div>
            </div>

            {/* Top Pages */}
            <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
              <h3 className="text-lg font-semibold text-white mb-4">Top Pages</h3>
              <div className="space-y-3">
                {behavior.top_pages.map((page, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-700/50 rounded-lg">
                    <div>
                      <div className="font-medium text-white">{page.page}</div>
                      <div className="text-sm text-gray-400">{page.unique_views} unique views</div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-blue-400">{page.views.toLocaleString()}</div>
                      <div className="text-sm text-gray-400">total views</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default AdvancedUserAnalytics;