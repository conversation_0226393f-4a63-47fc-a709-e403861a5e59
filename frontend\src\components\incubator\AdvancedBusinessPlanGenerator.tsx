import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  FileText,
  Lightbulb,
  Plus,
  RefreshCw,
  Check,
  AlertCircle,
  ChevronDown,
  ChevronUp,
  ArrowRight,
  Sparkles,
  BarChart2
} from 'lucide-react';
import { useAppSelector } from '../../store/hooks';
import {
  BusinessPlan,
  businessPlansAPI
} from '../../services/businessPlanApi';
import { BusinessIdea, businessIdeasAPI } from '../../services/incubatorApi';

interface AdvancedBusinessPlanGeneratorProps {
  businessIdeaId?: number;
  onSuccess?: (businessPlanId: number) => void;
}

const AdvancedBusinessPlanGenerator: React.FC<AdvancedBusinessPlanGeneratorProps> = ({
  businessIdeaId,
  onSuccess
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { user } = useAppSelector(state => state.auth);

  const [businessIdeas, setBusinessIdeas] = useState<BusinessIdea[]>([]);
  const [selectedBusinessIdea, setSelectedBusinessIdea] = useState<number | null>(businessIdeaId || null);
  const [planTitle, setPlanTitle] = useState('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [showMarketAnalysis, setShowMarketAnalysis] = useState(false);
  const [marketAnalysis, setMarketAnalysis] = useState<string | null>(null);
  const [generatingMarketAnalysis, setGeneratingMarketAnalysis] = useState(false);

  // Fetch business ideas
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch business ideas
        const ideas = await businessIdeasAPI.getBusinessIdeas();
        setBusinessIdeas(ideas);

        // If businessIdeaId is provided, set it as selected
        if (businessIdeaId) {
          setSelectedBusinessIdea(businessIdeaId);

          // Set default plan title based on business idea
          const idea = ideas.find(i => i.id === businessIdeaId);
          if (idea) {
            setPlanTitle(`${idea.title} - ${t('incubator.aiPowered.title')}`);
          }
        }
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(t('businessPlan.failedToLoad'));
      }
    };

    fetchData();
  }, [businessIdeaId]);

  const handleBusinessIdeaChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const ideaId = parseInt(e.target.value);
    setSelectedBusinessIdea(ideaId);

    // Update plan title based on selected idea
    const idea = businessIdeas.find(i => i.id === ideaId);
    if (idea) {
      setPlanTitle(`${idea.title} - ${t('incubator.aiPowered.title')}`);
    } else {
      setPlanTitle('');
    }
  };

  const handleGenerateMarketAnalysis = async () => {
    if (!selectedBusinessIdea) return;

    setGeneratingMarketAnalysis(true);
    setError(null);

    try {
      const response = await businessPlansAPI.generateMarketAnalysis(selectedBusinessIdea);
      setMarketAnalysis(response.market_analysis);
      setShowMarketAnalysis(true);
    } catch (err) {
      console.error('Error generating market analysis:', err);
      setError(t('incubator.aiPowered.errorGenerating'));
    } finally {
      setGeneratingMarketAnalysis(false);
    }
  };

  const handleGenerateCompletePlan = async () => {
    if (!selectedBusinessIdea || !planTitle.trim()) return;

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await businessPlansAPI.generateCompletePlan(selectedBusinessIdea, planTitle);
      setSuccess(t('incubator.aiPowered.planGenerated'));

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess(response.id);
      } else {
        // Navigate to the business plan page after a short delay
        setTimeout(() => {
          navigate(`/dashboard/business-plans/${response.id}`);
        }, 1500);
      }
    } catch (err) {
      console.error('Error generating business plan:', err);
      setError(t('incubator.aiPowered.errorGenerating'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-indigo-950/50 rounded-lg p-6 border border-indigo-800/50">
      <div className="flex items-center mb-4">
        <Sparkles size={24} className="text-purple-400 mr-2" />
        <h2 className="text-xl font-semibold text-white">{t('incubator.aiPowered.title')}</h2>
      </div>

      <div className="text-gray-300 mb-6">
        {t('incubator.aiPowered.description')}
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-900/50 border border-red-800 rounded-md text-red-200 flex items-start">
          <AlertCircle size={18} className="mr-2 mt-0.5 flex-shrink-0" />
          <span>{error}</span>
        </div>
      )}

      {success && (
        <div className="mb-4 p-3 bg-green-900/50 border border-green-800 rounded-md text-green-200 flex items-start">
          <Check size={18} className="mr-2 mt-0.5 flex-shrink-0" />
          <span>{success}</span>
        </div>
      )}

      <div className="space-y-4">
        {/* Business Idea Selection */}
        <div>
          <label htmlFor="business-idea" className="block text-sm font-medium text-gray-300 mb-1">
            {t('businessPlan.businessIdea')}
          </label>
          <select
            id="business-idea"
            value={selectedBusinessIdea || ''}
            onChange={handleBusinessIdeaChange}
            disabled={loading || !!businessIdeaId}
            className="w-full p-2 bg-gray-800 border border-gray-700 rounded-md text-white"
          >
            <option value="">{t('businessPlan.selectBusinessIdea')}</option>
            {businessIdeas.map(idea => (
              <option key={idea.id} value={idea.id}>
                {idea.title}
              </option>
            ))}
          </select>
        </div>

        {/* Plan Title */}
        <div>
          <label htmlFor="plan-title" className="block text-sm font-medium text-gray-300 mb-1">
            {t('businessPlan.planTitle')}
          </label>
          <input
            id="plan-title"
            type="text"
            value={planTitle}
            onChange={(e) => setPlanTitle(e.target.value)}
            disabled={loading}
            placeholder={t('businessPlan.enterPlanTitle')}
            className="w-full p-2 bg-gray-800 border border-gray-700 rounded-md text-white"
          />
        </div>

        {/* Market Analysis Preview */}
        {selectedBusinessIdea && (
          <div className="mt-4">
            <button
              onClick={handleGenerateMarketAnalysis}
              disabled={generatingMarketAnalysis}
              className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 disabled:bg-indigo-800/50 disabled:cursor-not-allowed rounded-md text-white flex items-center"
            >
              {generatingMarketAnalysis ? (
                <RefreshCw size={16} className="mr-1 animate-spin" />
              ) : (
                <BarChart2 size={16} className="mr-1" />
              )}
              {generatingMarketAnalysis ? t('businessPlan.generating') : t('incubator.aiPowered.generateMarketAnalysis')}
            </button>
          </div>
        )}

        {showMarketAnalysis && marketAnalysis && (
          <div className="mt-4 p-4 bg-gray-800/50 border border-gray-700 rounded-md">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-lg font-medium text-white">{t('incubator.aiPowered.marketAnalysisPreview')}</h3>
              <button
                onClick={() => setShowMarketAnalysis(!showMarketAnalysis)}
                className="text-gray-400 hover:text-white"
              >
                {showMarketAnalysis ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
              </button>
            </div>
            <div className="prose prose-invert max-h-60 overflow-y-auto text-sm">
              <div dangerouslySetInnerHTML={{ __html: marketAnalysis.replace(/\n/g, '<br />') }} />
            </div>
          </div>
        )}

        {/* Generate Button */}
        <div className="mt-6">
          <button
            onClick={handleGenerateCompletePlan}
            disabled={loading || !selectedBusinessIdea || !planTitle.trim()}
            className="w-full px-4 py-3 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-800/50 disabled:cursor-not-allowed rounded-md text-white flex items-center justify-center"
          >
            {loading ? (
              <RefreshCw size={18} className="mr-1 animate-spin" />
            ) : (
              <Sparkles size={18} className="mr-1" />
            )}
            {loading ? t('incubator.aiPowered.generating') : t('incubator.aiPowered.generateCompletePlan')}
          </button>
          <p className="text-xs text-gray-400 mt-2 text-center">
            {t('incubator.aiPowered.generatingDescription')}
          </p>
        </div>
      </div>
    </div>
  );
};

export default AdvancedBusinessPlanGenerator;
