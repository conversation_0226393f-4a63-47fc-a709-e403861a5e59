/**
 * Consolidated Business Analysis Component
 * Advanced business analysis using LangGraph workflows and ML insights
 */

import React, { useState, useEffect } from 'react';
import {
  TrendingUp,
  Target,
  Lightbulb,
  BarChart3,
  Brain,
  Globe,
  CheckCircle,
  AlertCircle,
  Loader2,
  Download,
  RefreshCw
} from 'lucide-react';
import { useCentralizedAI } from '../../hooks/useCentralizedAI';
import { useLanguage } from '../../hooks/useLanguage';

interface ConsolidatedBusinessAnalysisProps {
  businessIdeaId: number;
  businessIdea?: any;
  language?: string;
  onAnalysisComplete?: (analysis: any) => void;
  className?: string;
}

interface AnalysisSection {
  title: string;
  content: any;
  type: 'text' | 'list' | 'metrics' | 'recommendations';
  icon: React.ReactNode;
  status: 'pending' | 'loading' | 'complete' | 'error';
}

export const ConsolidatedBusinessAnalysis: React.FC<ConsolidatedBusinessAnalysisProps> = ({
  businessIdeaId,
  businessIdea,
  language = 'en',
  onAnalysisComplete,
  className = '',
}) => {
  const [analysisResults, setAnalysisResults] = useState<any>(null);
  const [analysisProgress, setAnalysisProgress] = useState<AnalysisSection[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { isRTL } = useLanguage();
  const {
    isChatting,
    isAvailable,
    chat,
  } = useCentralizedAI();

  // Mock functions for compatibility
  const availability = {
    consolidatedAI: isAvailable,
    arabicProcessing: true,
  };

  const isWorkflowsAvailable = true;
  const isMLAvailable = true;

  const runComprehensiveAnalysis = async (businessId: number, lang: string) => {
    // Mock comprehensive analysis
    const response = await chat(`Analyze business idea ${businessId} in ${lang}`, lang);
    return {
      business_analysis: {
        business_info: response?.content || 'Business analysis completed',
        market_analysis: 'Market analysis completed',
        feasibility_assessment: { score: 85, viability: 'high' },
        insights: ['Key insight 1', 'Key insight 2'],
      },
      recommendations: [
        { title: 'Recommendation 1', description: 'Description 1', priority: 'high' },
        { title: 'Recommendation 2', description: 'Description 2', priority: 'medium' },
      ],
      ml_insights: { confidence: 0.85, predictions: ['Prediction 1'] },
      recommendationCount: 2,
      workflowType: 'comprehensive',
      hasMLInsights: true,
      hasCulturalAdaptations: lang === 'ar',
    };
  };

  // Initialize analysis sections
  useEffect(() => {
    const sections: AnalysisSection[] = [
      {
        title: language === 'ar' ? 'استخراج معلومات المشروع' : 'Business Information Extraction',
        content: null,
        type: 'text',
        icon: <Target className="w-5 h-5" />,
        status: 'pending',
      },
      {
        title: language === 'ar' ? 'تحليل السوق' : 'Market Analysis',
        content: null,
        type: 'text',
        icon: <BarChart3 className="w-5 h-5" />,
        status: 'pending',
      },
      {
        title: language === 'ar' ? 'تقييم الجدوى' : 'Feasibility Assessment',
        content: null,
        type: 'metrics',
        icon: <CheckCircle className="w-5 h-5" />,
        status: 'pending',
      },
      {
        title: language === 'ar' ? 'الرؤى والتحليلات' : 'Business Insights',
        content: null,
        type: 'list',
        icon: <Lightbulb className="w-5 h-5" />,
        status: 'pending',
      },
      {
        title: language === 'ar' ? 'التوصيات الاستراتيجية' : 'Strategic Recommendations',
        content: null,
        type: 'recommendations',
        icon: <TrendingUp className="w-5 h-5" />,
        status: 'pending',
      },
    ];

    if (isMLAvailable) {
      sections.push({
        title: language === 'ar' ? 'رؤى التعلم الآلي' : 'ML Insights',
        content: null,
        type: 'metrics',
        icon: <Brain className="w-5 h-5" />,
        status: 'pending',
      });
    }

    setAnalysisProgress(sections);
  }, [language, isMLAvailable]);

  const runAnalysis = async () => {
    if (!businessIdeaId || isAnalyzing) return;

    setIsAnalyzing(true);
    setError(null);
    setAnalysisResults(null);

    try {
      // Simulate progressive analysis
      const updateProgress = (index: number, status: 'loading' | 'complete' | 'error', content?: any) => {
        setAnalysisProgress(prev => prev.map((section, i) =>
          i === index ? { ...section, status, content } : section
        ));
      };

      // Start analysis
      for (let i = 0; i < analysisProgress.length; i++) {
        updateProgress(i, 'loading');
        await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate processing time
      }

      // Run the actual analysis
      const analysis = await runComprehensiveAnalysis(businessIdeaId, language);

      if (analysis) {
        setAnalysisResults(analysis);

        // Update sections with real data
        if (analysisResults?.business_analysis) {
          updateProgress(0, 'complete', analysisResults.business_analysis.business_info);
          updateProgress(1, 'complete', analysisResults.business_analysis.market_analysis);
          updateProgress(2, 'complete', analysisResults.business_analysis.feasibility_assessment);
          updateProgress(3, 'complete', analysisResults.business_analysis.insights);
        }

        if (analysisResults?.recommendations) {
          updateProgress(4, 'complete', analysisResults.recommendations);
        }

        if (analysisResults?.ml_insights && isMLAvailable) {
          updateProgress(5, 'complete', analysisResults.ml_insights);
        }

        onAnalysisComplete?.(analysis);
      } else {
        throw new Error('Analysis failed');
      }
    } catch (err) {
      console.error('Analysis error:', err);
      setError(language === 'ar'
        ? 'فشل في إجراء التحليل. يرجى المحاولة مرة أخرى.'
        : 'Failed to run analysis. Please try again.'
      );

      // Mark all as error
      setAnalysisProgress(prev => prev.map(section => ({ ...section, status: 'error' })));
    } finally {
      setIsAnalyzing(false);
    }
  };

  const renderSectionContent = (section: AnalysisSection) => {
    if (!section.content) return null;

    switch (section.type) {
      case 'text':
        return (
          <div className="prose prose-sm max-w-none">
            <p>{typeof section.content === 'string' ? section.content : JSON.stringify(section.content)}</p>
          </div>
        );

      case 'list':
        return (
          <ul className="space-y-2">
            {Array.isArray(section.content) ? section.content.map((item: any, index: number) => (
              <li key={index} className="flex items-start space-x-2">
                <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-sm">{typeof item === 'string' ? item : item.title || item.description}</span>
              </li>
            )) : (
              <li className="text-sm">{section.content}</li>
            )}
          </ul>
        );

      case 'metrics':
        return (
          <div className="grid grid-cols-2 gap-4">
            {Object.entries(section.content || {}).map(([key, value]) => (
              <div key={key} className="bg-gray-50 p-3 rounded-lg">
                <div className="text-sm font-medium text-gray-600 capitalize">
                  {key.replace(/_/g, ' ')}
                </div>
                <div className="text-lg font-semibold text-gray-900">
                  {typeof value === 'number' ? value.toFixed(2) : String(value)}
                </div>
              </div>
            ))}
          </div>
        );

      case 'recommendations':
        return (
          <div className="space-y-3">
            {Array.isArray(section.content) ? section.content.map((rec: any, index: number) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{rec.title}</h4>
                    <p className="text-sm text-gray-600 mt-1">{rec.description}</p>
                    {rec.priority && (
                      <span className={`inline-block px-2 py-1 rounded-full text-xs mt-2 ${
                        rec.priority === 'high' ? 'bg-red-100 text-red-800' :
                        rec.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-green-100 text-green-800'
                      }`}>
                        {rec.priority} priority
                      </span>
                    )}
                  </div>
                </div>
              </div>
            )) : (
              <p className="text-sm">{section.content}</p>
            )}
          </div>
        );

      default:
        return <p className="text-sm">{JSON.stringify(section.content)}</p>;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'loading':
        return <Loader2 className="w-4 h-4 animate-spin text-blue-500" />;
      case 'complete':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <div className="w-4 h-4 border-2 border-gray-300 rounded-full" />;
    }
  };

  const exportAnalysis = () => {
    if (!analysisResults) return;

    const dataStr = JSON.stringify(analysisResults, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `business-analysis-${businessIdeaId}-${Date.now()}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className={`bg-white rounded-lg shadow-lg ${className}`} dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg">
              <BarChart3 className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                {language === 'ar' ? 'التحليل التجاري المتقدم' : 'Advanced Business Analysis'}
              </h2>
              <p className="text-sm text-gray-600">
                {language === 'ar'
                  ? 'تحليل شامل باستخدام سير العمل المتقدم والذكاء الاصطناعي'
                  : 'Comprehensive analysis using advanced workflows and AI'
                }
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {analysisResults && (
              <button
                onClick={exportAnalysis}
                className="flex items-center px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
              >
                <Download className="w-4 h-4 mr-2" />
                {language === 'ar' ? 'تصدير' : 'Export'}
              </button>
            )}

            <button
              onClick={runAnalysis}
              disabled={isAnalyzing || !availability.consolidatedAI}
              className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isAnalyzing ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <RefreshCw className="w-4 h-4 mr-2" />
              )}
              {isAnalyzing
                ? (language === 'ar' ? 'جاري التحليل...' : 'Analyzing...')
                : (language === 'ar' ? 'بدء التحليل' : 'Start Analysis')
              }
            </button>
          </div>
        </div>

        {/* Capability indicators */}
        <div className="flex items-center space-x-4 mt-4 text-sm">
          {isWorkflowsAvailable && (
            <div className="flex items-center text-green-600">
              <CheckCircle className="w-4 h-4 mr-1" />
              {language === 'ar' ? 'سير العمل المتقدم' : 'Advanced Workflows'}
            </div>
          )}
          {isMLAvailable && (
            <div className="flex items-center text-blue-600">
              <Brain className="w-4 h-4 mr-1" />
              {language === 'ar' ? 'رؤى التعلم الآلي' : 'ML Insights'}
            </div>
          )}
          {availability.arabicProcessing && language === 'ar' && (
            <div className="flex items-center text-purple-600">
              <Globe className="w-4 h-4 mr-1" />
              {language === 'ar' ? 'السياق الثقافي' : 'Cultural Context'}
            </div>
          )}
        </div>
      </div>

      {/* Error display */}
      {error && (
        <div className="p-4 bg-red-50 border-l-4 border-red-400">
          <div className="flex">
            <AlertCircle className="w-5 h-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Analysis Progress */}
      <div className="p-6">
        <div className="space-y-6">
          {analysisProgress.map((section, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  {section.icon}
                  <h3 className="font-medium text-gray-900">{section.title}</h3>
                </div>
                {getStatusIcon(section.status)}
              </div>

              {section.status === 'loading' && (
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>
                    {language === 'ar' ? 'جاري المعالجة...' : 'Processing...'}
                  </span>
                </div>
              )}

              {section.status === 'complete' && section.content && (
                <div className="mt-3">
                  {renderSectionContent(section)}
                </div>
              )}

              {section.status === 'error' && (
                <div className="mt-3 text-sm text-red-600">
                  {language === 'ar'
                    ? 'فشل في معالجة هذا القسم'
                    : 'Failed to process this section'
                  }
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Summary */}
        {analysisResults && (
          <div className="mt-8 p-4 bg-blue-50 rounded-lg">
            <h3 className="font-medium text-blue-900 mb-2">
              {language === 'ar' ? 'ملخص التحليل' : 'Analysis Summary'}
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <div className="text-blue-600 font-medium">
                  {language === 'ar' ? 'التوصيات' : 'Recommendations'}
                </div>
                <div className="text-blue-900">
                  {analysisResults.recommendationCount || 0}
                </div>
              </div>
              <div>
                <div className="text-blue-600 font-medium">
                  {language === 'ar' ? 'نوع سير العمل' : 'Workflow Type'}
                </div>
                <div className="text-blue-900 capitalize">
                  {analysisResults.workflowType || 'N/A'}
                </div>
              </div>
              <div>
                <div className="text-blue-600 font-medium">
                  {language === 'ar' ? 'رؤى التعلم الآلي' : 'ML Insights'}
                </div>
                <div className="text-blue-900">
                  {analysisResults.hasMLInsights ? '✓' : '✗'}
                </div>
              </div>
              <div>
                <div className="text-blue-600 font-medium">
                  {language === 'ar' ? 'التكيف الثقافي' : 'Cultural Adaptations'}
                </div>
                <div className="text-blue-900">
                  {analysisResults.hasCulturalAdaptations ? '✓' : '✗'}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ConsolidatedBusinessAnalysis;
