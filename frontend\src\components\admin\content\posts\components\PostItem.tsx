import React from 'react';
import { MessageSquare, Heart, Edit, Trash2, ChevronDown, ChevronUp } from 'lucide-react';
import { Post } from '../../../../../services/api';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../../hooks/useLanguage';
interface PostItemProps {
  post: Post;
  isExpanded: boolean;
  isSelected: boolean;
  onToggleExpand: (postId: number) => void;
  onToggleSelect: (postId: number) => void;
  onEdit: (post: Post) => void;
  onDelete: (post: Post) => void;
}

const PostItem: React.FC<PostItemProps> = ({ post,
  isExpanded,
  isSelected,
  onToggleExpand,
  onToggleSelect,
  onEdit,
  onDelete
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  return (
    <div className="p-6 hover:bg-indigo-900/10">
      <div className={`flex justify-between items-start ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
            <input
              type="checkbox"
              checked={isSelected}
              onChange={() => onToggleSelect(post.id)}
              className="w-4 h-4 rounded border-indigo-700 text-purple-600 focus:ring-purple-500"
            />
            <h3 className="text-lg font-semibold">{post.title}</h3>
          </div>
          <div className={`flex items-center mt-2 text-sm text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
            <span>By {post.author.username}</span>
            <span className="mx-2">•</span>
            <span>{new Date(post.created_at).toLocaleDateString()}</span>
            <div className={`flex items-center ml-4 ${isRTL ? "flex-row-reverse" : ""}`}>
              <MessageSquare size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
              <span>{post.comment_count || 0}</span>
            </div>
            <div className={`flex items-center ml-4 ${isRTL ? "flex-row-reverse" : ""}`}>
              <Heart size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
              <span>{post.like_count || 0}</span>
            </div>
          </div>
        </div>
        <div className={`flex space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
          <button
            onClick={() => onEdit(post)}
            className="p-2 text-gray-400 hover:text-white hover:bg-indigo-800/50 rounded-lg"
          >
            <Edit size={16} />
          </button>
          <button
            onClick={() => onDelete(post)}
            className="p-2 text-gray-400 hover:text-red-400 hover:bg-red-900/20 rounded-lg"
          >
            <Trash2 size={16} />
          </button>
          <button
            onClick={() => onToggleExpand(post.id)}
            className="p-2 text-gray-400 hover:text-white hover:bg-indigo-800/50 rounded-lg"
          >
            {isExpanded ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
          </button>
        </div>
      </div>

      {isExpanded && (
        <div className="mt-4 pl-7">
          <div className="p-4 bg-indigo-900/30 rounded-lg">
            <div dangerouslySetInnerHTML={{ __html: post.content }} />
          </div>
        </div>
      )}
    </div>
  );
};

export default PostItem;
