from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.contrib.auth.models import User
from django.db.models import Count, Sum, Avg, Q
from django.utils import timezone
from datetime import timedelta
from incubator.models import (
    InvestorProfile, FundingOpportunity, FundingApplication, 
    Investment, BusinessIdea
)
from .models import UserProfile
from api.permissions import IsAdminUser


class InvestorViewSet(viewsets.ViewSet):
    """
    ViewSet for investor-specific functionality
    """
    permission_classes = [permissions.IsAuthenticated]

    def get_permissions(self):
        """
        Instantiates and returns the list of permissions that this view requires.
        """
        permission_classes = [permissions.IsAuthenticated]
        return [permission() for permission in permission_classes]

    @action(detail=False, methods=['get'])
    def dashboard_stats(self, request):
        """
        Get investor dashboard statistics
        """
        # Check if user has investor role
        user_profile = getattr(request.user, 'profile', None)
        if not user_profile or not user_profile.has_role('investor'):
            if not (request.user.is_staff or request.user.is_superuser):
                return Response(
                    {'error': 'Investor access required'}, 
                    status=status.HTTP_403_FORBIDDEN
                )

        try:
            investor_profile = InvestorProfile.objects.get(user=request.user)
        except InvestorProfile.DoesNotExist:
            # Create investor profile if it doesn't exist
            investor_profile = InvestorProfile.objects.create(
                user=request.user,
                investment_focus="Technology",
                minimum_investment=10000,
                maximum_investment=1000000
            )

        # Get investment statistics
        total_investments = Investment.objects.filter(investor=investor_profile).count()
        
        active_deals = FundingApplication.objects.filter(
            funding_opportunity__investor=investor_profile,
            status__in=['pending', 'shortlisted', 'approved']
        ).count()

        # Calculate portfolio value
        portfolio_value = Investment.objects.filter(
            investor=investor_profile
        ).aggregate(total=Sum('amount'))['total'] or 0

        # Calculate ROI (simplified calculation)
        total_invested = Investment.objects.filter(
            investor=investor_profile
        ).aggregate(total=Sum('amount'))['total'] or 0
        
        # For demo purposes, assume 18.5% average ROI
        roi = 18.5 if total_invested > 0 else 0

        # Get deals this month
        this_month = timezone.now().replace(day=1)
        deals_this_month = Investment.objects.filter(
            investor=investor_profile,
            created_at__gte=this_month
        ).count()

        # Get pending reviews
        pending_reviews = FundingApplication.objects.filter(
            funding_opportunity__investor=investor_profile,
            status='pending'
        ).count()

        stats = {
            'totalInvestments': total_investments,
            'activeDeals': active_deals,
            'portfolioValue': int(portfolio_value),
            'roi': roi,
            'dealsThisMonth': deals_this_month,
            'pendingReviews': pending_reviews
        }

        return Response(stats)

    @action(detail=False, methods=['get'])
    def investment_opportunities(self, request):
        """
        Get available investment opportunities
        """
        # Check investor access
        user_profile = getattr(request.user, 'profile', None)
        if not user_profile or not user_profile.has_role('investor'):
            if not (request.user.is_staff or request.user.is_superuser):
                return Response(
                    {'error': 'Investor access required'}, 
                    status=status.HTTP_403_FORBIDDEN
                )

        # Get active funding opportunities
        opportunities = FundingOpportunity.objects.filter(
            is_active=True,
            deadline__gte=timezone.now()
        ).select_related('business_idea')[:10]

        opportunities_data = []
        for opp in opportunities:
            # Calculate current funding from applications
            current_funding = FundingApplication.objects.filter(
                funding_opportunity=opp,
                status='funded'
            ).aggregate(total=Sum('requested_amount'))['total'] or 0

            # Calculate funding progress
            funding_progress = (current_funding / opp.funding_goal) * 100 if opp.funding_goal > 0 else 0

            # Determine risk level (simplified)
            risk_level = 'medium'
            if opp.business_idea and opp.business_idea.stage == 'idea':
                risk_level = 'high'
            elif opp.business_idea and opp.business_idea.stage in ['growth', 'scaling']:
                risk_level = 'low'

            opportunities_data.append({
                'id': str(opp.id),
                'companyName': opp.business_idea.title if opp.business_idea else opp.title,
                'industry': opp.business_idea.industry if opp.business_idea else 'Technology',
                'stage': opp.funding_type.title(),
                'fundingGoal': int(opp.funding_goal),
                'currentFunding': int(current_funding),
                'valuation': int(opp.valuation) if opp.valuation else int(opp.funding_goal * 5),
                'roi_projection': 25,  # This could be calculated based on business plan
                'risk_level': risk_level,
                'description': opp.description[:100] + '...' if len(opp.description) > 100 else opp.description,
                'deadline': opp.deadline.isoformat()
            })

        return Response(opportunities_data)

    @action(detail=False, methods=['get'])
    def my_portfolio(self, request):
        """
        Get investor's portfolio
        """
        # Check investor access
        user_profile = getattr(request.user, 'profile', None)
        if not user_profile or not user_profile.has_role('investor'):
            if not (request.user.is_staff or request.user.is_superuser):
                return Response(
                    {'error': 'Investor access required'}, 
                    status=status.HTTP_403_FORBIDDEN
                )

        try:
            investor_profile = InvestorProfile.objects.get(user=request.user)
        except InvestorProfile.DoesNotExist:
            return Response([])

        # Get investor's investments
        investments = Investment.objects.filter(
            investor=investor_profile
        ).select_related('business_idea')

        portfolio = []
        for investment in investments:
            # Calculate current value (simplified - in reality this would be more complex)
            # For demo, assume some investments are growing, some stable, some declining
            import random
            roi_factor = random.uniform(0.8, 1.5)  # Random ROI between -20% and +50%
            current_value = investment.amount * roi_factor
            roi = ((current_value - investment.amount) / investment.amount) * 100

            # Determine status
            if roi > 10:
                status_type = 'growing'
            elif roi > -5:
                status_type = 'stable'
            else:
                status_type = 'declining'

            portfolio.append({
                'id': str(investment.id),
                'companyName': investment.business_idea.title if investment.business_idea else 'Investment',
                'industry': investment.business_idea.industry if investment.business_idea else 'Technology',
                'investmentAmount': int(investment.amount),
                'currentValue': int(current_value),
                'roi': round(roi, 1),
                'status': status_type,
                'lastUpdate': investment.updated_at.isoformat()
            })

        return Response(portfolio)

    @action(detail=False, methods=['get'])
    def market_analysis(self, request):
        """
        Get market analysis data
        """
        # Check investor access
        user_profile = getattr(request.user, 'profile', None)
        if not user_profile or not user_profile.has_role('investor'):
            if not (request.user.is_staff or request.user.is_superuser):
                return Response(
                    {'error': 'Investor access required'}, 
                    status=status.HTTP_403_FORBIDDEN
                )

        # Get market trends (simplified analysis)
        industries = BusinessIdea.objects.values('industry').annotate(
            count=Count('id'),
            avg_funding=Avg('funding_goal')
        ).order_by('-count')[:10]

        market_trends = []
        for industry in industries:
            market_trends.append({
                'industry': industry['industry'] or 'Other',
                'startupCount': industry['count'],
                'averageFunding': int(industry['avg_funding'] or 0),
                'growthRate': random.uniform(5, 25)  # Simulated growth rate
            })

        # Get funding trends over time
        six_months_ago = timezone.now() - timedelta(days=180)
        monthly_funding = []
        
        for i in range(6):
            month_start = timezone.now().replace(day=1) - timedelta(days=30*i)
            month_end = month_start + timedelta(days=30)
            
            funding_amount = FundingApplication.objects.filter(
                created_at__gte=month_start,
                created_at__lt=month_end,
                status='funded'
            ).aggregate(total=Sum('requested_amount'))['total'] or 0
            
            monthly_funding.append({
                'month': month_start.strftime('%Y-%m'),
                'totalFunding': int(funding_amount),
                'dealCount': FundingApplication.objects.filter(
                    created_at__gte=month_start,
                    created_at__lt=month_end,
                    status='funded'
                ).count()
            })

        analysis_data = {
            'marketTrends': market_trends,
            'monthlyFunding': monthly_funding,
            'totalMarketSize': BusinessIdea.objects.count(),
            'activeOpportunities': FundingOpportunity.objects.filter(
                is_active=True,
                deadline__gte=timezone.now()
            ).count()
        }

        return Response(analysis_data)

    @action(detail=False, methods=['get'])
    def analytics(self, request):
        """
        Get investor analytics
        """
        # Check investor access
        user_profile = getattr(request.user, 'profile', None)
        if not user_profile or not user_profile.has_role('investor'):
            if not (request.user.is_staff or request.user.is_superuser):
                return Response(
                    {'error': 'Investor access required'}, 
                    status=status.HTTP_403_FORBIDDEN
                )

        try:
            investor_profile = InvestorProfile.objects.get(user=request.user)
        except InvestorProfile.DoesNotExist:
            return Response({})

        # Get investment analytics
        investments = Investment.objects.filter(investor=investor_profile)
        
        total_invested = investments.aggregate(total=Sum('amount'))['total'] or 0
        investment_count = investments.count()
        
        # Portfolio distribution by industry
        portfolio_distribution = investments.values(
            'business_idea__industry'
        ).annotate(
            count=Count('id'),
            total_amount=Sum('amount')
        ).order_by('-total_amount')

        distribution_data = []
        for item in portfolio_distribution:
            distribution_data.append({
                'industry': item['business_idea__industry'] or 'Other',
                'count': item['count'],
                'amount': int(item['total_amount']),
                'percentage': round((item['total_amount'] / max(total_invested, 1)) * 100, 1)
            })

        analytics_data = {
            'totalInvested': int(total_invested),
            'investmentCount': investment_count,
            'averageInvestment': int(total_invested / max(investment_count, 1)),
            'portfolioDistribution': distribution_data,
            'performanceMetrics': {
                'roi': 18.5,  # Simplified
                'successRate': 75,  # Simplified
                'averageHoldingPeriod': 24  # months
            }
        }

        return Response(analytics_data)
