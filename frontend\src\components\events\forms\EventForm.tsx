import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';
import { Event } from '../../../services/api';
import { Loader, Save, X, AlertCircle, Calendar, MapPin, Users, Clock } from 'lucide-react';

interface EventFormProps {
  initialData?: Partial<Event>;
  onSubmit: (data: Partial<Event>) => Promise<boolean>;
  onCancel: () => void;
  isSubmitting?: boolean;
  mode: 'create' | 'edit';
}

interface FormData {
  title: string;
  description: string;
  content: string;
  event_type: 'workshop' | 'webinar' | 'networking' | 'conference' | 'meetup' | 'competition';
  start_date: string;
  end_date: string;
  location: string;
  is_virtual: boolean;
  virtual_link: string;
  max_attendees: number;
  registration_deadline: string;
  is_free: boolean;
  price: number;
  tags: string;
  is_published: boolean;
}

interface FormErrors {
  [key: string]: string;
}

const EventForm: React.FC<EventFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isSubmitting = false,
  mode
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [formData, setFormData] = useState<FormData>({
    title: initialData?.title || '',
    description: initialData?.description || '',
    content: initialData?.content || '',
    event_type: initialData?.event_type || 'workshop',
    start_date: initialData?.start_date ? new Date(initialData.start_date).toISOString().slice(0, 16) : '',
    end_date: initialData?.end_date ? new Date(initialData.end_date).toISOString().slice(0, 16) : '',
    location: initialData?.location || '',
    is_virtual: initialData?.is_virtual ?? false,
    virtual_link: initialData?.virtual_link || '',
    max_attendees: initialData?.max_attendees || 0,
    registration_deadline: initialData?.registration_deadline ? new Date(initialData.registration_deadline).toISOString().slice(0, 16) : '',
    is_free: initialData?.is_free ?? true,
    price: initialData?.price || 0,
    tags: initialData?.tags || '',
    is_published: initialData?.is_published ?? true
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  // Validation rules
  const validateField = (name: string, value: string | number | boolean): string => {
    switch (name) {
      case 'title':
        if (!value || (typeof value === 'string' && !value.trim())) {
          return t('validation.required', 'This field is required');
        }
        if (typeof value === 'string' && value.length < 5) {
          return t('validation.minLength', 'Must be at least 5 characters');
        }
        break;
      case 'description':
        if (!value || (typeof value === 'string' && !value.trim())) {
          return t('validation.required', 'This field is required');
        }
        if (typeof value === 'string' && value.length < 20) {
          return t('validation.minLength', 'Must be at least 20 characters');
        }
        break;
      case 'content':
        if (!value || (typeof value === 'string' && !value.trim())) {
          return t('validation.required', 'This field is required');
        }
        if (typeof value === 'string' && value.length < 50) {
          return t('validation.minLength', 'Must be at least 50 characters');
        }
        break;
      case 'start_date':
        if (!value || (typeof value === 'string' && !value.trim())) {
          return t('validation.required', 'This field is required');
        }
        if (typeof value === 'string' && new Date(value) < new Date()) {
          return t('validation.futureDate', 'Start date must be in the future');
        }
        break;
      case 'end_date':
        if (!value || (typeof value === 'string' && !value.trim())) {
          return t('validation.required', 'This field is required');
        }
        if (typeof value === 'string' && formData.start_date && new Date(value) <= new Date(formData.start_date)) {
          return t('validation.endAfterStart', 'End date must be after start date');
        }
        break;
      case 'registration_deadline':
        if (!value || (typeof value === 'string' && !value.trim())) {
          return t('validation.required', 'This field is required');
        }
        if (typeof value === 'string' && formData.start_date && new Date(value) >= new Date(formData.start_date)) {
          return t('validation.deadlineBeforeStart', 'Registration deadline must be before start date');
        }
        break;
      case 'location':
        if (!formData.is_virtual && (!value || (typeof value === 'string' && !value.trim()))) {
          return t('validation.required', 'This field is required for in-person events');
        }
        break;
      case 'virtual_link':
        if (formData.is_virtual && (!value || (typeof value === 'string' && !value.trim()))) {
          return t('validation.required', 'This field is required for virtual events');
        }
        if (value && typeof value === 'string' && value.trim()) {
          const urlPattern = /^https?:\/\/.+/;
          if (!urlPattern.test(value)) {
            return t('validation.invalidUrl', 'Must be a valid URL starting with http:// or https://');
          }
        }
        break;
      case 'max_attendees':
        if (typeof value === 'number' && value < 1) {
          return t('validation.minValue', 'Must be at least 1');
        }
        break;
      case 'price':
        if (!formData.is_free && typeof value === 'number' && value <= 0) {
          return t('validation.positiveNumber', 'Price must be greater than 0 for paid events');
        }
        break;
    }
    return '';
  };

  // Validate all fields
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};
    
    Object.keys(formData).forEach(key => {
      if (key !== 'is_virtual' && key !== 'is_free' && key !== 'is_published' && key !== 'tags') {
        const error = validateField(key, formData[key as keyof FormData]);
        if (error) {
          newErrors[key] = error;
        }
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    let processedValue: string | number | boolean = value;
    
    if (type === 'number') {
      processedValue = parseFloat(value) || 0;
    } else if (type === 'checkbox') {
      processedValue = (e.target as HTMLInputElement).checked;
    }
    
    setFormData(prev => ({
      ...prev,
      [name]: processedValue
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // Handle field blur
  const handleBlur = (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    setTouched(prev => ({
      ...prev,
      [name]: true
    }));

    // Validate field on blur
    let processedValue: string | number | boolean = value;
    if (type === 'number') {
      processedValue = parseFloat(value) || 0;
    } else if (type === 'checkbox') {
      processedValue = (e.target as HTMLInputElement).checked;
    }
    
    const error = validateField(name, processedValue);
    setErrors(prev => ({
      ...prev,
      [name]: error
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Mark all fields as touched
    const allTouched = Object.keys(formData).reduce((acc, key) => {
      acc[key] = true;
      return acc;
    }, {} as Record<string, boolean>);
    setTouched(allTouched);

    // Validate form
    if (!validateForm()) {
      return;
    }

    // Submit form
    const success = await onSubmit(formData);
    if (success) {
      // Form will be closed by parent component
    }
  };

  const eventTypeOptions = [
    { value: 'workshop', label: t('events.types.workshop', 'Workshop') },
    { value: 'webinar', label: t('events.types.webinar', 'Webinar') },
    { value: 'networking', label: t('events.types.networking', 'Networking') },
    { value: 'conference', label: t('events.types.conference', 'Conference') },
    { value: 'meetup', label: t('events.types.meetup', 'Meetup') },
    { value: 'competition', label: t('events.types.competition', 'Competition') }
  ];

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <div className="bg-gray-900 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className={`flex items-center justify-between p-6 border-b border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <h2 className="text-xl font-semibold text-white">
            {mode === 'create' 
              ? t('events.createEvent', 'Create Event')
              : t('events.editEvent', 'Edit Event')
            }
          </h2>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-white transition-colors"
            disabled={isSubmitting}
          >
            <X size={24} />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('events.title', 'Title')} *
            </label>
            <input
              type="text"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              onBlur={handleBlur}
              className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white ${
                errors.title && touched.title ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder={t('events.titlePlaceholder', 'Enter event title')}
              disabled={isSubmitting}
            />
            {errors.title && touched.title && (
              <p className="mt-1 text-sm text-red-400 flex items-center">
                <AlertCircle size={16} className="mr-1" />
                {errors.title}
              </p>
            )}
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('events.description', 'Description')} *
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              onBlur={handleBlur}
              rows={3}
              className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white resize-vertical ${
                errors.description && touched.description ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder={t('events.descriptionPlaceholder', 'Provide a brief description of the event')}
              disabled={isSubmitting}
            />
            {errors.description && touched.description && (
              <p className="mt-1 text-sm text-red-400 flex items-center">
                <AlertCircle size={16} className="mr-1" />
                {errors.description}
              </p>
            )}
          </div>

          {/* Event Type */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('events.type', 'Event Type')} *
            </label>
            <select
              name="event_type"
              value={formData.event_type}
              onChange={handleInputChange}
              onBlur={handleBlur}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
              disabled={isSubmitting}
            >
              {eventTypeOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Date and Time */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('events.startDate', 'Start Date & Time')} *
              </label>
              <input
                type="datetime-local"
                name="start_date"
                value={formData.start_date}
                onChange={handleInputChange}
                onBlur={handleBlur}
                className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white ${
                  errors.start_date && touched.start_date ? 'border-red-500' : 'border-gray-600'
                }`}
                disabled={isSubmitting}
              />
              {errors.start_date && touched.start_date && (
                <p className="mt-1 text-sm text-red-400 flex items-center">
                  <AlertCircle size={16} className="mr-1" />
                  {errors.start_date}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('events.endDate', 'End Date & Time')} *
              </label>
              <input
                type="datetime-local"
                name="end_date"
                value={formData.end_date}
                onChange={handleInputChange}
                onBlur={handleBlur}
                className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white ${
                  errors.end_date && touched.end_date ? 'border-red-500' : 'border-gray-600'
                }`}
                disabled={isSubmitting}
              />
              {errors.end_date && touched.end_date && (
                <p className="mt-1 text-sm text-red-400 flex items-center">
                  <AlertCircle size={16} className="mr-1" />
                  {errors.end_date}
                </p>
              )}
            </div>
          </div>

          {/* Virtual Event Toggle */}
          <div>
            <label className="flex items-center gap-3">
              <input
                type="checkbox"
                name="is_virtual"
                checked={formData.is_virtual}
                onChange={handleInputChange}
                className="w-4 h-4 text-purple-600 bg-gray-800 border-gray-600 rounded focus:ring-purple-500 focus:ring-2"
                disabled={isSubmitting}
              />
              <span className="text-sm font-medium text-gray-300">
                {t('events.isVirtual', 'Virtual event')}
              </span>
            </label>
          </div>

          {/* Location or Virtual Link */}
          {formData.is_virtual ? (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('events.virtualLink', 'Virtual Event Link')} *
              </label>
              <input
                type="url"
                name="virtual_link"
                value={formData.virtual_link}
                onChange={handleInputChange}
                onBlur={handleBlur}
                className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white ${
                  errors.virtual_link && touched.virtual_link ? 'border-red-500' : 'border-gray-600'
                }`}
                placeholder="https://zoom.us/j/123456789"
                disabled={isSubmitting}
              />
              {errors.virtual_link && touched.virtual_link && (
                <p className="mt-1 text-sm text-red-400 flex items-center">
                  <AlertCircle size={16} className="mr-1" />
                  {errors.virtual_link}
                </p>
              )}
            </div>
          ) : (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('events.location', 'Location')} *
              </label>
              <input
                type="text"
                name="location"
                value={formData.location}
                onChange={handleInputChange}
                onBlur={handleBlur}
                className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white ${
                  errors.location && touched.location ? 'border-red-500' : 'border-gray-600'
                }`}
                placeholder={t('events.locationPlaceholder', 'Enter event location')}
                disabled={isSubmitting}
              />
              {errors.location && touched.location && (
                <p className="mt-1 text-sm text-red-400 flex items-center">
                  <AlertCircle size={16} className="mr-1" />
                  {errors.location}
                </p>
              )}
            </div>
          )}

          {/* Content */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('events.content', 'Event Details')} *
            </label>
            <textarea
              name="content"
              value={formData.content}
              onChange={handleInputChange}
              onBlur={handleBlur}
              rows={6}
              className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white resize-vertical ${
                errors.content && touched.content ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder={t('events.contentPlaceholder', 'Provide detailed information about the event...')}
              disabled={isSubmitting}
            />
            {errors.content && touched.content && (
              <p className="mt-1 text-sm text-red-400 flex items-center">
                <AlertCircle size={16} className="mr-1" />
                {errors.content}
              </p>
            )}
          </div>

          {/* Registration Settings */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('events.maxAttendees', 'Maximum Attendees')}
              </label>
              <input
                type="number"
                name="max_attendees"
                value={formData.max_attendees}
                onChange={handleInputChange}
                onBlur={handleBlur}
                min="0"
                className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white ${
                  errors.max_attendees && touched.max_attendees ? 'border-red-500' : 'border-gray-600'
                }`}
                placeholder="0 = unlimited"
                disabled={isSubmitting}
              />
              {errors.max_attendees && touched.max_attendees && (
                <p className="mt-1 text-sm text-red-400 flex items-center">
                  <AlertCircle size={16} className="mr-1" />
                  {errors.max_attendees}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('events.registrationDeadline', 'Registration Deadline')} *
              </label>
              <input
                type="datetime-local"
                name="registration_deadline"
                value={formData.registration_deadline}
                onChange={handleInputChange}
                onBlur={handleBlur}
                className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white ${
                  errors.registration_deadline && touched.registration_deadline ? 'border-red-500' : 'border-gray-600'
                }`}
                disabled={isSubmitting}
              />
              {errors.registration_deadline && touched.registration_deadline && (
                <p className="mt-1 text-sm text-red-400 flex items-center">
                  <AlertCircle size={16} className="mr-1" />
                  {errors.registration_deadline}
                </p>
              )}
            </div>
          </div>

          {/* Pricing */}
          <div>
            <label className="flex items-center gap-3 mb-4">
              <input
                type="checkbox"
                name="is_free"
                checked={formData.is_free}
                onChange={handleInputChange}
                className="w-4 h-4 text-purple-600 bg-gray-800 border-gray-600 rounded focus:ring-purple-500 focus:ring-2"
                disabled={isSubmitting}
              />
              <span className="text-sm font-medium text-gray-300">
                {t('events.isFree', 'Free event')}
              </span>
            </label>

            {!formData.is_free && (
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {t('events.price', 'Price')} ($) *
                </label>
                <input
                  type="number"
                  name="price"
                  value={formData.price}
                  onChange={handleInputChange}
                  onBlur={handleBlur}
                  min="0"
                  step="0.01"
                  className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white ${
                    errors.price && touched.price ? 'border-red-500' : 'border-gray-600'
                  }`}
                  placeholder="0.00"
                  disabled={isSubmitting}
                />
                {errors.price && touched.price && (
                  <p className="mt-1 text-sm text-red-400 flex items-center">
                    <AlertCircle size={16} className="mr-1" />
                    {errors.price}
                  </p>
                )}
              </div>
            )}
          </div>

          {/* Tags */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('events.tags', 'Tags')}
            </label>
            <input
              type="text"
              name="tags"
              value={formData.tags}
              onChange={handleInputChange}
              onBlur={handleBlur}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
              placeholder={t('events.tagsPlaceholder', 'workshop, networking, startup')}
              disabled={isSubmitting}
            />
            <p className="mt-1 text-xs text-gray-400">
              {t('events.tagsHelp', 'Separate tags with commas')}
            </p>
          </div>

          {/* Published Status */}
          <div>
            <label className="flex items-center gap-3">
              <input
                type="checkbox"
                name="is_published"
                checked={formData.is_published}
                onChange={handleInputChange}
                className="w-4 h-4 text-purple-600 bg-gray-800 border-gray-600 rounded focus:ring-purple-500 focus:ring-2"
                disabled={isSubmitting}
              />
              <span className="text-sm font-medium text-gray-300">
                {t('events.isPublished', 'Published')}
              </span>
            </label>
            <p className="mt-1 text-xs text-gray-400 ml-7">
              {t('events.publishedHelp', 'Only published events are visible to users')}
            </p>
          </div>

          {/* Form Actions */}
          <div className={`flex gap-4 pt-4 border-t border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <button
              type="button"
              onClick={onCancel}
              className="px-6 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors"
              disabled={isSubmitting}
            >
              {t('common.cancel', 'Cancel')}
            </button>
            <button
              type="submit"
              className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader size={16} className="animate-spin" />
                  {t('common.saving', 'Saving...')}
                </>
              ) : (
                <>
                  <Save size={16} />
                  {mode === 'create'
                    ? t('common.create', 'Create')
                    : t('common.update', 'Update')
                  }
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EventForm;
