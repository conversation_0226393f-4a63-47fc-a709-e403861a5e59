import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  FileText,
  Save,
  RefreshCw,
  Check,
  AlertCircle,
  ChevronDown,
  ChevronUp,
  ArrowLeft,
  Sparkles,
  Download,
  BarChart2,
  Eye
} from 'lucide-react';
import { useAppSelector } from '../../store/hooks';
import {
  BusinessPlan,
  BusinessPlanSection,
  businessPlansAPI,
  businessPlanSectionsAPI
} from '../../services/businessPlanApi';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import './BusinessPlanEditor.css';
import { exportToPDF } from '../../utils/exportUtils';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';

interface BusinessPlanEditorProps {
  businessPlanId?: number;
  onBack?: () => void;
}

const BusinessPlanEditor: React.FC<BusinessPlanEditorProps> = ({ businessPlanId: propBusinessPlanId,
  onBack
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { id: paramId } = useParams<{ id: string }>();
  const businessPlanId = propBusinessPlanId || (paramId ? parseInt(paramId) : undefined);

  const navigate = useNavigate();
  const { user } = useAppSelector(state => state.auth);

  const [businessPlan, setBusinessPlan] = useState<BusinessPlan | null>(null);
  const [sections, setSections] = useState<BusinessPlanSection[]>([]);
  const [expandedSections, setExpandedSections] = useState<Record<number, boolean>>({});
  const [activeSectionId, setActiveSectionId] = useState<number | null>(null);
  const [sectionContent, setSectionContent] = useState<string>('');
  const [isGeneratingContent, setIsGeneratingContent] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [showFeedback, setShowFeedback] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [autoSaveTimeout, setAutoSaveTimeout] = useState<NodeJS.Timeout | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [showUnsavedWarning, setShowUnsavedWarning] = useState(false);
  const [showAutoSaveIndicator, setShowAutoSaveIndicator] = useState(false);
  const [autoSaveError, setAutoSaveError] = useState(false);

  // Fetch business plan and sections
  useEffect(() => {
    const fetchData = async (attempt = 1) => {
      if (!businessPlanId) {
        setError(t("businessPlan.noBusinessPlanId"));
        setLoading(false);
        return;
      }

      try {
        setError(null); // Clear previous errors

        // Fetch business plan and sections separately
        const [plan, planSections] = await Promise.all([
          businessPlansAPI.getPlan(businessPlanId),
          businessPlanSectionsAPI.getSections(businessPlanId)
        ]);

        setBusinessPlan(plan);
        setSections(planSections);
        setRetryCount(0); // Reset retry count on success

        // Initialize expanded sections
        const expanded: Record<number, boolean> = {};
        planSections.forEach(section => {
          expanded[section.id] = false;
        });
        setExpandedSections(expanded);

        // Set first section as active if no section is completed
        const incompleteSections = planSections.filter(s => !s.is_completed);
        if (incompleteSections.length > 0) {
          setActiveSectionId(incompleteSections[0].id);
          setSectionContent(incompleteSections[0].content || '');
        } else if (planSections.length > 0) {
          setActiveSectionId(planSections[0].id);
          setSectionContent(planSections[0].content || '');
        }
      } catch (err) {
        console.error('Error fetching business plan:', err);

        // Determine error type for better user feedback
        let errorMessage = t("businessPlan.failedToLoad");
        if (err instanceof Error) {
          if (err.message.includes('404')) {
            errorMessage = t("businessPlan.planNotFound");
          } else if (err.message.includes('403')) {
            errorMessage = t("businessPlan.accessDenied");
          } else if (err.message.includes('network')) {
            errorMessage = t("businessPlan.networkError");
          }
        }

        if (attempt < 3) {
          // Retry up to 3 times with exponential backoff
          setTimeout(() => {
            setRetryCount(attempt);
            fetchData(attempt + 1);
          }, Math.pow(2, attempt) * 1000);
        } else {
          setError(errorMessage);
        }
      } finally {
        if (attempt >= 3) {
          setLoading(false);
        }
      }
    };

    fetchData();
  }, [businessPlanId, t]);

  // Cleanup effect to prevent memory leaks
  useEffect(() => {
    return () => {
      if (autoSaveTimeout) {
        clearTimeout(autoSaveTimeout);
      }
    };
  }, [autoSaveTimeout]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl+S or Cmd+S to save
      if ((event.ctrlKey || event.metaKey) && event.key === 's') {
        event.preventDefault();
        if (activeSectionId && sectionContent.trim()) {
          handleSaveSection();
        }
      }

      // Ctrl+G or Cmd+G to generate content
      if ((event.ctrlKey || event.metaKey) && event.key === 'g') {
        event.preventDefault();
        if (activeSectionId && !isGeneratingContent) {
          handleGenerateContent();
        }
      }

      // Escape to close unsaved warning
      if (event.key === 'Escape' && showUnsavedWarning) {
        setShowUnsavedWarning(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [activeSectionId, sectionContent, isGeneratingContent, showUnsavedWarning]);

  const handleSectionClick = (sectionId: number) => {
    // If clicking the active section, toggle expanded state
    if (sectionId === activeSectionId) {
      setExpandedSections(prev => ({
        ...prev,
        [sectionId]: !prev[sectionId]
      }));
      return;
    }

    // Check for unsaved changes before switching sections
    if (hasUnsavedChanges && activeSectionId !== null) {
      setShowUnsavedWarning(true);
      return;
    }

    switchToSection(sectionId);
  };

  const switchToSection = (sectionId: number) => {
    // Set new active section
    setActiveSectionId(sectionId);

    // Find the section and set its content
    const section = sections.find(s => s.id === sectionId);
    if (section) {
      setSectionContent(section.content || '');
      setHasUnsavedChanges(false);

      // Expand the section
      setExpandedSections(prev => ({
        ...prev,
        [sectionId]: true
      }));
    }
  };

  const handleDiscardChanges = () => {
    setHasUnsavedChanges(false);
    setShowUnsavedWarning(false);
    // Continue with the section switch that was interrupted
  };

  const handleSaveAndSwitch = async () => {
    await handleSaveSection();
    setShowUnsavedWarning(false);
    // The section switch will happen after save completes
  };

  const handleContentChange = (content: string) => {
    setSectionContent(content);
    setHasUnsavedChanges(true);

    // Clear existing timeout
    if (autoSaveTimeout) {
      clearTimeout(autoSaveTimeout);
    }

    // Set new auto-save timeout (10 seconds for better UX)
    const timeout = setTimeout(() => {
      if (activeSectionId && content.trim() && content !== sections.find(s => s.id === activeSectionId)?.content) {
        handleAutoSave(content);
      }
    }, 10000);

    setAutoSaveTimeout(timeout);
  };

  const handleAutoSave = async (content: string) => {
    if (activeSectionId === null) return;

    try {
      setShowAutoSaveIndicator(true);
      setAutoSaveError(false);

      await businessPlanSectionsAPI.updateSection(activeSectionId, {
        content: content,
        is_completed: false // Auto-save doesn't mark as completed
      });

      setHasUnsavedChanges(false);

      // Hide indicator after 2 seconds
      setTimeout(() => setShowAutoSaveIndicator(false), 2000);
    } catch (err) {
      console.error('Auto-save failed:', err);
      setAutoSaveError(true);
      setTimeout(() => {
        setShowAutoSaveIndicator(false);
        setAutoSaveError(false);
      }, 3000);
    }
  };

  const handleSaveSection = async () => {
    if (activeSectionId === null) return;

    setIsSaving(true);
    setError(null);

    try {
      const updatedSection = await businessPlanSectionsAPI.updateSection(activeSectionId, {
        content: sectionContent,
        is_completed: true
      });

      // Update sections list
      setSections(prev => prev.map(section =>
        section.id === updatedSection.id ? updatedSection : section
      ));

      setSuccess(t("businessPlan.sectionSavedSuccessfully"));
      setHasUnsavedChanges(false);

      // Clear auto-save timeout since we manually saved
      if (autoSaveTimeout) {
        clearTimeout(autoSaveTimeout);
        setAutoSaveTimeout(null);
      }

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(null), 3000);

      // Refresh business plan to update completion percentage
      if (businessPlanId) {
        const updatedPlan = await businessPlansAPI.getPlan(businessPlanId);
        setBusinessPlan(updatedPlan);
      }
    } catch (err) {
      console.error('Error saving section:', err);
      setError(t("businessPlan.failedToSave"));
    } finally {
      setIsSaving(false);
    }
  };

  const handleGenerateContent = async () => {
    if (activeSectionId === null) return;

    setIsGeneratingContent(true);
    setError(null);

    try {
      const updatedSection = await businessPlanSectionsAPI.generateContent(activeSectionId);

      // Update sections list
      setSections(prev => prev.map(section =>
        section.id === updatedSection.id ? updatedSection : section
      ));

      // Update active section content
      setSectionContent(updatedSection.content || '');

      setSuccess(t("businessPlan.contentGeneratedSuccessfully"));

      // Refresh business plan to update completion percentage
      if (businessPlanId) {
        const updatedPlan = await businessPlansAPI.getPlan(businessPlanId);
        setBusinessPlan(updatedPlan);
      }
    } catch (err) {
      console.error('Error generating content:', err);
      setError(t("businessPlan.failedToGenerate"));
    } finally {
      setIsGeneratingContent(false);
    }
  };

  const handleAnalyzePlan = async () => {
    if (!businessPlanId) return;

    setIsAnalyzing(true);
    setError(null);

    try {
      const analyzedPlan = await businessPlansAPI.analyzePlan(businessPlanId);
      setBusinessPlan(analyzedPlan);
      setShowFeedback(true);
      setSuccess(t("businessPlan.businessPlanAnalyzed"));
    } catch (err) {
      console.error('Error analyzing business plan:', err);
      setError(t("businessPlan.failedToAnalyze"));
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleExportPDF = async () => {
    if (!businessPlan) return;

    try {
      // Show loading state
      setSuccess(t("businessPlan.preparingExport"));

      // Prepare comprehensive data for PDF
      const title = businessPlan.title;
      const subtitle = `${t("businessPlan.businessPlanFor")} ${businessPlan.business_idea_title}`;

      // Create detailed content for PDF
      const content = sections.length > 0
        ? sections.map(section => ({
            title: section.title,
            content: section.content || t("businessPlan.noContentAvailable"),
            isCompleted: section.is_completed,
            order: section.order || 0
          }))
        : [{
            title: t("businessPlan.businessPlanStructure"),
            content: t("businessPlan.noSectionsCreated"),
            isCompleted: false,
            order: 0
          }];

      // Sort sections by order
      content.sort((a, b) => a.order - b.order);

      // Enhanced PDF data with better formatting
      const columns = [
        { header: t("businessPlan.sectionDataKey"), dataKey: 'section' },
        { header: t("businessPlan.contentDataKey"), dataKey: 'content' },
        { header: t("businessPlan.statusDataKey"), dataKey: 'status' }
      ];

      const data = content.map(item => ({
        section: item.title,
        content: item.content
          ? item.content.replace(/<[^>]*>/g, ' ').trim()
          : t("businessPlan.noContentAvailable"),
        status: item.isCompleted ? t("common.completed") : t("common.inProgress")
      }));

      // Add comprehensive metadata
      const metadataRow = {
        section: t("businessPlan.businessPlanInfo"),
        content: `${t("businessPlan.title")}: ${businessPlan.title}\n${t("businessPlan.businessIdea")}: ${businessPlan.business_idea_title || t("common.none")}\n${t("businessPlan.status")}: ${businessPlan.status_display}\n${t("businessPlan.completion")}: ${businessPlan.completion_percentage}%\n${t("businessPlan.lastUpdated")}: ${new Date(businessPlan.updated_at).toLocaleDateString()}`,
        status: businessPlan.status_display
      };

      const finalData = [metadataRow, ...data];

      // Generate filename with timestamp
      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `${businessPlan.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_business_plan_${timestamp}`;

      exportToPDF(
        columns,
        finalData,
        title,
        subtitle,
        filename
      );

      setSuccess(t("businessPlan.exportSuccess"));
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error('Export failed:', error);
      setError(t("businessPlan.exportFailed"));
      setTimeout(() => setError(null), 5000);
    }
  };

  if (loading) {
    return (
      <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
        <div className={`flex flex-col items-center justify-center py-12 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mb-4"></div>
          <p className="text-gray-400 text-lg">{t("common.loading")}</p>
          <p className="text-gray-500 text-sm mt-2">{t("businessPlan.loadingPlan")}</p>
          {retryCount > 0 && (
            <div className="text-yellow-400 text-xs mt-2">
              {t("businessPlan.retryAttempt", { attempt: retryCount })}
            </div>
          )}
          <div className="mt-4 text-xs text-gray-600">
            {t("businessPlan.loadingTip")}
          </div>
        </div>
      </div>
    );
  }

  if (!businessPlan && !loading) {
    return (
      <div className="bg-red-900/30 backdrop-blur-sm rounded-lg p-6 border border-red-800/50 text-center">
        <AlertCircle size={40} className="mx-auto mb-3 text-red-500" />
        <h3 className="text-xl font-bold mb-2">{t("businessPlan.businessPlanNotFound")}</h3>
        <div className="text-gray-400 mb-4">
          {error || t("businessPlan.notFoundMessage")}
        </div>
        {retryCount > 0 && (
          <div className="text-yellow-400 mb-4 text-sm">
            {t("businessPlan.retryAttempt", { attempt: retryCount })}
          </div>
        )}
        <div className="text-sm text-gray-500 mb-4">
          {t("businessPlan.troubleshootingTip")}
        </div>
        <div className={`flex gap-3 justify-center ${isRTL ? "flex-row-reverse" : ""}`}>
          <button
            onClick={() => window.location.reload()}
            className={`px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-md text-white inline-flex items-center transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <RefreshCw size={16} className={`${isRTL ? "ml-1" : "mr-1"}`} />
            {t("common.retry")}
          </button>
          <button
            onClick={() => onBack ? onBack() : navigate('/dashboard/business-plans')}
            className={`px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white inline-flex items-center transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <ArrowLeft size={16} className={`${isRTL ? "ml-1" : "mr-1"}`} />
            {t("businessPlan.goBack")}
          </button>
        </div>
      </div>
    );
  }

  const activeSection = sections.find(s => s.id === activeSectionId);
  const completedSections = sections.filter(s => s.is_completed).length;
  const totalSections = sections.length;

  return (
    <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
      <div className={`flex items-center justify-between mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
          <FileText size={24} className={`text-purple-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
          <div>
            <h2 className="text-xl font-bold">{businessPlan.title}</h2>
            <div className="text-gray-400 text-sm">
              {businessPlan.business_idea_title} • {businessPlan.status_display}
            </div>
          </div>
        </div>

        <div className={`flex ${isRTL ? "flex-row-reverse space-x-reverse" : ""} space-x-2`}>
          {/* Keyboard shortcuts help */}
          <div className="relative group">
            <button className="px-2 py-1 bg-gray-700 hover:bg-gray-600 rounded-md text-white text-xs font-mono">
              ⌨️
            </button>
            <div className={`keyboard-shortcuts-tooltip absolute bottom-full mb-2 ${isRTL ? 'right-0' : 'left-0'} bg-gray-800 text-white text-xs rounded-md p-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-20`}>
              <div className="font-semibold mb-2 text-gray-300">{t('businessPlan.keyboardShortcuts')}</div>
              <div className="space-y-1">
                <div><kbd className="bg-gray-700 px-1 rounded">Ctrl+S</kbd> {t('businessPlan.shortcutSave')}</div>
                <div><kbd className="bg-gray-700 px-1 rounded">Ctrl+G</kbd> {t('businessPlan.shortcutGenerate')}</div>
                <div><kbd className="bg-gray-700 px-1 rounded">Esc</kbd> {t('businessPlan.shortcutClose')}</div>
              </div>
            </div>
          </div>

          <button
            onClick={handleExportPDF}
            className={`action-button px-3 py-1 bg-indigo-800 hover:bg-indigo-700 rounded-md text-white text-sm flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <Download size={14} className={`${isRTL ? "ml-1" : "mr-1"}`} />
            {t("businessPlan.exportPDF")}
          </button>

          <button
            onClick={handleAnalyzePlan}
            disabled={isAnalyzing || completedSections === 0}
            className={`action-button px-3 py-1 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-800/50 disabled:cursor-not-allowed disabled:transform-none rounded-md text-white text-sm flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
          >
            {isAnalyzing ? (
              <RefreshCw size={14} className={`${isRTL ? "ml-1" : "mr-1"} animate-spin`} />
            ) : (
              <BarChart2 size={14} className={`${isRTL ? "ml-1" : "mr-1"}`} />
            )}
            {isAnalyzing ? t("businessPlan.analyzing") : t("businessPlan.analyzePlan")}
          </button>

          <button
            onClick={() => onBack ? onBack() : navigate('/dashboard/business-plans')}
            className={`action-button px-3 py-1 bg-indigo-800 hover:bg-indigo-700 rounded-md text-white text-sm flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <ArrowLeft size={14} className={`${isRTL ? "ml-1" : "mr-1"}`} />
            {t("common.back")}
          </button>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="mb-6">
        <div className={`flex justify-between text-sm text-gray-400 mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
          <span className="font-medium">{t("businessPlan.completionLabel")}: {businessPlan.completion_percentage}%</span>
          <span>{completedSections} {t("common.of")} {totalSections} {t("businessPlan.sectionsProgress")}</span>
        </div>
        <div className="progress-bar-container w-full bg-indigo-950 rounded-full h-3 shadow-inner">
          <div
            className="progress-bar-fill h-3 rounded-full"
            style={{ width: `${businessPlan.completion_percentage}%` }}
          ></div>
        </div>
        {businessPlan.completion_percentage > 0 && (
          <div className="text-xs text-gray-500 mt-1">
            {businessPlan.completion_percentage === 100
              ? `🎉 ${t('businessPlan.planCompleted')}`
              : t('businessPlan.percentageRemaining', { percentage: 100 - businessPlan.completion_percentage })}
          </div>
        )}
      </div>

      {success && (
        <div className={`mb-4 p-3 bg-green-900/50 text-green-200 rounded-md flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
          <Check size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
          {success}
        </div>
      )}

      {error && (
        <div className={`mb-4 p-3 bg-red-900/50 text-red-200 rounded-md flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
          <AlertCircle size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
          {error}
        </div>
      )}

      <div className="grid grid-cols-1 xl:grid-cols-3 lg:grid-cols-1 gap-6">
        {/* Sections List */}
        <div className="xl:col-span-1 order-2 xl:order-1">
          <div className="bg-indigo-950/50 rounded-lg p-4 border border-indigo-800/50 sticky top-4">
            <h3 className="text-lg font-medium mb-3">{t("businessPlan.businessPlanSections")}</h3>

            <ul className="space-y-2 max-h-96 overflow-y-auto">
              {sections.map((section, index) => (
                <li key={section.id}>
                  <button
                    onClick={() => handleSectionClick(section.id)}
                    className={`section-nav-item ${isRTL ? 'rtl' : ''} w-full text-left p-3 rounded-md flex items-center justify-between transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50 ${
                      section.id === activeSectionId
                        ? 'bg-purple-900/50 border border-purple-700/50 shadow-md ring-2 ring-purple-500/30'
                        : 'bg-indigo-900/30 hover:bg-indigo-800/30 border border-indigo-800/30 hover:shadow-sm hover:border-indigo-700/50'}
                    }`}
                    aria-label={t('businessPlan.selectSection', { section: section.title })}
                  >
                    <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                      <div className={`flex items-center justify-center w-6 h-6 rounded-full text-xs font-medium ${isRTL ? "ml-2" : "mr-2"} ${
                        section.is_completed
                          ? 'bg-green-500 text-white'
                          : section.id === activeSectionId
                          ? 'bg-purple-500 text-white'
                          : 'bg-gray-600 text-gray-300'
                      }`}>
                        {section.is_completed ? (
                          <Check size={12} />
                        ) : (
                          index + 1
                        )}
                      </div>
                      <div className="flex-1">
                        <span className={`block ${section.is_completed ? 'text-green-300' : 'text-white'}`}>
                          {section.title}
                        </span>
                        {section.content && (
                          <div className="text-xs text-gray-400 mt-1 truncate">
                            {section.content.replace(/<[^>]*>/g, '').substring(0, 50)}...
                          </div>
                        )}
                      </div>
                    </div>
                    {section.id === activeSectionId ? (
                      expandedSections[section.id] ? (
                        <ChevronUp size={16} className="text-purple-400" />
                      ) : (
                        <ChevronDown size={16} className="text-purple-400" />
                      )
                    ) : null}
                  </button>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Section Editor */}
        <div className="xl:col-span-2 order-1 xl:order-2">
          {activeSection ? (
            <div className="bg-indigo-950/50 rounded-lg p-4 border border-indigo-800/50">
              <div className={`flex items-center justify-between mb-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                <h3 className="text-lg font-medium">{activeSection.title}</h3>

                <div className={`flex ${isRTL ? "flex-row-reverse space-x-reverse" : ""} space-x-2`}>
                  <button
                    onClick={handleGenerateContent}
                    disabled={isGeneratingContent}
                    className={`action-button px-3 py-1 bg-yellow-600 hover:bg-yellow-700 disabled:bg-yellow-800/50 disabled:cursor-not-allowed disabled:transform-none rounded-md text-white text-sm flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                  >
                    {isGeneratingContent ? (
                      <RefreshCw size={14} className={`${isRTL ? "ml-1" : "mr-1"} animate-spin`} />
                    ) : (
                      <Sparkles size={14} className={`${isRTL ? "ml-1" : "mr-1"}`} />
                    )}
                    {isGeneratingContent ? t("businessPlan.generating") : t("businessPlan.generateWithAI")}
                  </button>

                  <button
                    onClick={handleSaveSection}
                    disabled={isSaving || !sectionContent.trim()}
                    className={`action-button px-3 py-1 ${hasUnsavedChanges ? 'bg-orange-600 hover:bg-orange-700' : 'bg-purple-600 hover:bg-purple-700'} disabled:bg-purple-800/50 disabled:cursor-not-allowed disabled:transform-none rounded-md text-white text-sm flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                  >
                    {isSaving ? (
                      <RefreshCw size={14} className={`${isRTL ? "ml-1" : "mr-1"} animate-spin`} />
                    ) : (
                      <Save size={14} className={`${isRTL ? "ml-1" : "mr-1"}`} />
                    )}
                    {isSaving ? t("businessPlan.saving") : (hasUnsavedChanges ? t("businessPlan.saveChanges") : t("businessPlan.saveSection"))}
                  </button>
                </div>
              </div>

              {/* Section guidance from template */}
              {expandedSections[activeSection.id] && businessPlan.template && (
                <div className="mb-4 p-3 bg-indigo-900/50 rounded-md border border-indigo-700/50">
                  <h4 className="text-sm font-medium mb-1">{t("businessPlan.guidance")}</h4>
                  <div className="text-sm text-gray-300">
                    {businessPlan.template && businessPlan.template.sections &&
                     businessPlan.template.sections.sections.map((templateSection: any) => {
                      if (templateSection.key === activeSection.key) {
                        return (
                          <div key={templateSection.key}>
                            <div className="mb-2">{templateSection.description}</div>
                            {templateSection.guiding_questions && (
                              <ul className="list-disc pl-5 space-y-1 text-gray-400">
                                {templateSection.guiding_questions.map((question: string, index: number) => (
                                  <li key={index}>{question}</li>
                                ))}
                              </ul>
                            )}
                          </div>
                        );
                      }
                      return null;
                    })}
                  </div>
                </div>
              )}

              {/* Rich Text Editor */}
              <div className={`business-plan-editor relative bg-white rounded-md overflow-hidden shadow-sm ${isRTL ? 'rtl' : ''}`} dir={isRTL ? 'rtl' : 'ltr'}>
                {/* Auto-save indicator */}
                <div className={`auto-save-indicator ${showAutoSaveIndicator ? 'show' : ''} ${autoSaveError ? 'error' : ''} ${isRTL ? 'left-2' : 'right-2'}`}>
                  {autoSaveError ? t('businessPlan.autoSaveFailed') : t('businessPlan.autoSaved')}
                </div>

                <ReactQuill
                  theme="snow"
                  value={sectionContent}
                  onChange={handleContentChange}
                  style={{
                    direction: isRTL ? 'rtl' : 'ltr',
                    minHeight: '350px',
                    fontSize: '14px'
                  }}
                  modules={{
                    toolbar: {
                      container: [
                        [{ 'header': [1, 2, 3, false] }],
                        ['bold', 'italic', 'underline', 'strike'],
                        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                        [{ 'indent': '-1'}, { 'indent': '+1' }],
                        [{ 'align': [] }],
                        ['link', 'blockquote'],
                        ['clean']
                      ]
                    },
                    clipboard: {
                      matchVisual: false
                    }
                  }}
                  formats={[
                    'header', 'bold', 'italic', 'underline', 'strike',
                    'list', 'bullet', 'indent', 'align', 'link', 'blockquote'
                  ]}
                  placeholder={t("businessPlan.startWriting")}
                  bounds=".ql-container"
                />
              </div>
            </div>
          ) : (
            <div className="bg-indigo-950/50 rounded-lg p-6 border border-indigo-800/50 text-center">
              <FileText size={40} className="mx-auto mb-3 text-gray-500" />
              <h3 className="text-lg font-medium mb-2">{t("businessPlan.noSectionSelected")}</h3>
              <div className="text-gray-400">
                {t("businessPlan.selectSectionPrompt")}
              </div>
            </div>
          )}

          {/* AI Feedback */}
          {showFeedback && businessPlan.ai_feedback && businessPlan.ai_feedback.overall_feedback && (
            <div className="mt-6 bg-indigo-950/50 rounded-lg p-4 border border-indigo-800/50">
              <div className={`flex items-center justify-between mb-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                <h3 className={`text-lg font-medium flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                  <Sparkles size={18} className={`text-yellow-400 ${isRTL ? "ml-2" : "mr-2"}`} />
                  {t("businessPlan.aiFeedback")}
                </h3>
                <button
                  onClick={() => setShowFeedback(!showFeedback)}
                  className="text-gray-400 hover:text-white"
                >
                  <ChevronUp size={20} />
                </button>
              </div>

              <div className="mb-4">
                <h4 className="text-md font-medium mb-2">{t("businessPlan.overallFeedback")}</h4>

                <div className="mb-3">
                  <h5 className="text-sm font-medium text-green-400 mb-1">{t("businessPlan.strengths")}</h5>
                  <ul className={`list-disc space-y-1 text-sm ${isRTL ? "pr-5" : "pl-5"}`}>
                    {businessPlan.ai_feedback.overall_feedback.strengths.map((strength, index) => (
                      <li key={index}>{strength}</li>
                    ))}
                  </ul>
                </div>

                <div className="mb-3">
                  <h5 className="text-sm font-medium text-yellow-400 mb-1">{t("businessPlan.areasForImprovement")}</h5>
                  <ul className={`list-disc space-y-1 text-sm ${isRTL ? "pr-5" : "pl-5"}`}>
                    {businessPlan.ai_feedback.overall_feedback.areas_for_improvement.map((area, index) => (
                      <li key={index}>{area}</li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h5 className="text-sm font-medium text-purple-400 mb-1">{t("businessPlan.suggestions")}</h5>
                  <ul className={`list-disc space-y-1 text-sm ${isRTL ? "pr-5" : "pl-5"}`}>
                    {businessPlan.ai_feedback.overall_feedback.suggestions.map((suggestion, index) => (
                      <li key={index}>{suggestion}</li>
                    ))}
                  </ul>
                </div>
              </div>

              {/* Section-specific feedback would go here */}
            </div>
          )}
        </div>
      </div>

      {/* Unsaved Changes Warning Dialog */}
      {showUnsavedWarning && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-indigo-900 rounded-lg p-6 max-w-md mx-4 border border-indigo-700">
            <div className="flex items-center mb-4">
              <AlertCircle size={24} className="text-yellow-400 mr-3" />
              <h3 className="text-lg font-semibold text-white">
                {t("businessPlan.unsavedChanges")}
              </h3>
            </div>
            <p className="text-gray-300 mb-6">
              {t("businessPlan.unsavedChangesMessage")}
            </p>
            <div className={`flex gap-3 ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                onClick={handleSaveAndSwitch}
                className="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-md text-white flex items-center"
              >
                <Save size={16} className={`${isRTL ? "ml-2" : "mr-2"}`} />
                {t("businessPlan.saveAndContinue")}
              </button>
              <button
                onClick={handleDiscardChanges}
                className="px-4 py-2 bg-red-600 hover:bg-red-700 rounded-md text-white"
              >
                {t("businessPlan.discardChanges")}
              </button>
              <button
                onClick={() => setShowUnsavedWarning(false)}
                className="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-md text-white"
              >
                {t("common.cancel")}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BusinessPlanEditor;
