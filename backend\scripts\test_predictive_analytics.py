#!/usr/bin/env python3

"""
Diagnostic script to test the predictive analytics endpoint
and identify the source of 500 Internal Server Error
"""

import os
import sys
import django
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

def test_predictive_analytics_imports():
    """Test all imports required for predictive analytics"""
    print("🔍 Testing Predictive Analytics Imports...")
    
    try:
        print("1. Testing basic Django imports...")
        from django.conf import settings
        from django.http import JsonResponse
        from rest_framework.views import APIView
        from rest_framework.response import Response
        print("   ✅ Django imports successful")
        
        print("2. Testing AI models imports...")
        try:
            from ai_models.predictive_engine import PredictiveAnalyticsEngine
            print("   ✅ PredictiveAnalyticsEngine import successful")
        except ImportError as e:
            print(f"   ❌ PredictiveAnalyticsEngine import failed: {e}")
            return False
            
        try:
            from ai_models.computer_vision import ComputerVisionEngine
            print("   ✅ ComputerVisionEngine import successful")
        except ImportError as e:
            print(f"   ❌ ComputerVisionEngine import failed: {e}")
            
        try:
            from ai_models.voice_ai import VoiceAIEngine
            print("   ✅ VoiceAIEngine import successful")
        except ImportError as e:
            print(f"   ❌ VoiceAIEngine import failed: {e}")
            
        print("3. Testing predictive engine initialization...")
        engine = PredictiveAnalyticsEngine.get_instance()
        print(f"   ✅ Engine initialized: {engine.is_initialized}")
        print(f"   ✅ ML Available: {engine.ml_available}")
        
        print("4. Testing basic prediction...")
        test_data = {
            'title': 'Test Business',
            'description': 'A test business idea',
            'industry': 'technology',
            'stage': 'idea'
        }
        
        result = engine.predict_business_success(test_data)
        print(f"   ✅ Prediction successful: {result.get('success_probability', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_predictive_analytics_view():
    """Test the predictive analytics view directly"""
    print("\n🔍 Testing Predictive Analytics View...")
    
    try:
        from core.ai_views import PredictiveAnalyticsView, ADVANCED_AI_AVAILABLE
        print(f"   ✅ View import successful")
        print(f"   ✅ Advanced AI Available: {ADVANCED_AI_AVAILABLE}")
        
        if not ADVANCED_AI_AVAILABLE:
            print("   ⚠️ Advanced AI features not available - this is the likely cause of 500 errors")
            return False
            
        # Create a mock request
        from unittest.mock import Mock
        from django.contrib.auth.models import User
        
        # Create test user if doesn't exist
        from django.contrib.auth.models import User
        user, created = User.objects.get_or_create(
            username='test_user',
            defaults={'email': '<EMAIL>'}
        )
        
        mock_request = Mock()
        mock_request.user = user
        mock_request.data = {
            'analysis_type': 'success_prediction',
            'business_data': {
                'title': 'Test Business',
                'description': 'A test business idea',
                'industry': 'technology'
            }
        }
        
        view = PredictiveAnalyticsView()
        response = view.post(mock_request)
        
        print(f"   ✅ View response status: {response.status_code}")
        if hasattr(response, 'data'):
            print(f"   ✅ Response data keys: {list(response.data.keys())}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ View test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ml_dependencies():
    """Test ML library dependencies"""
    print("\n🔍 Testing ML Dependencies...")
    
    dependencies = [
        ('numpy', 'np'),
        ('pandas', 'pd'),
        ('sklearn.ensemble', 'RandomForestClassifier'),
        ('sklearn.preprocessing', 'StandardScaler'),
    ]
    
    missing_deps = []
    
    for module_name, import_name in dependencies:
        try:
            if '.' in module_name:
                module = __import__(module_name, fromlist=[import_name])
                getattr(module, import_name)
            else:
                __import__(module_name)
            print(f"   ✅ {module_name} available")
        except ImportError:
            print(f"   ❌ {module_name} missing")
            missing_deps.append(module_name)
    
    if missing_deps:
        print(f"\n⚠️ Missing dependencies: {', '.join(missing_deps)}")
        print("   Install with: pip install scikit-learn pandas numpy")
        return False
    
    return True

def test_database_models():
    """Test database models related to predictive analytics"""
    print("\n🔍 Testing Database Models...")
    
    try:
        from incubator.models_analytics import PredictiveAnalytics
        from incubator.models_base import BusinessIdea
        print("   ✅ Models import successful")
        
        # Test model creation
        from django.contrib.auth.models import User
        user, created = User.objects.get_or_create(
            username='test_user_2',
            defaults={'email': '<EMAIL>'}
        )
        
        business_idea, created = BusinessIdea.objects.get_or_create(
            title='Test Business Idea',
            user=user,
            defaults={
                'description': 'Test description',
                'industry': 'technology'
            }
        )
        
        predictive, created = PredictiveAnalytics.objects.get_or_create(
            business_idea=business_idea,
            defaults={
                'success_probability': 0.75,
                'prediction_confidence': 'medium'
            }
        )
        
        print(f"   ✅ PredictiveAnalytics model test successful: {predictive.id}")
        return True
        
    except Exception as e:
        print(f"❌ Database model test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all diagnostic tests"""
    print("🚀 Predictive Analytics Diagnostic Script")
    print("=" * 50)
    
    tests = [
        ("ML Dependencies", test_ml_dependencies),
        ("Predictive Analytics Imports", test_predictive_analytics_imports),
        ("Database Models", test_database_models),
        ("Predictive Analytics View", test_predictive_analytics_view),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name}...")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 DIAGNOSTIC SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The 500 error might be due to other factors.")
    else:
        print("⚠️ Some tests failed. This likely explains the 500 errors.")
        
        # Provide specific recommendations
        if not results.get("ML Dependencies", True):
            print("\n💡 RECOMMENDATION: Install missing ML dependencies")
            print("   Run: pip install scikit-learn pandas numpy xgboost")
            
        if not results.get("Predictive Analytics Imports", True):
            print("\n💡 RECOMMENDATION: Check ai_models module structure")
            
        if not results.get("Predictive Analytics View", True):
            print("\n💡 RECOMMENDATION: Check ADVANCED_AI_AVAILABLE flag")

if __name__ == "__main__":
    main()
