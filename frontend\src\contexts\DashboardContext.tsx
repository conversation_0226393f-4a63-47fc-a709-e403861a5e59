/**
 * Dashboard Context
 * Centralized state management for unified dashboard components
 */

import React, { createContext, useContext, useReducer, useEffect, useCallback } from 'react';
import { useAppSelector } from '../store/hooks';
import {
  DashboardRole,
  DashboardConfig,
  DashboardStat,
  DashboardQuickAction,
  DashboardTheme,
  DashboardContextValue,
  DashboardLoadingState,
  DashboardError,
} from '../types/dashboard';
import {
  getDashboardRole,
  getDashboardConfig,
  getDashboardTheme,
  getRefreshInterval,
  trackDashboardEvent,
} from '../utils/dashboardUtils';

// Dashboard action types
type DashboardAction =
  | { type: 'SET_ROLE'; payload: DashboardRole }
  | { type: 'SET_CONFIG'; payload: DashboardConfig }
  | { type: 'SET_STATS'; payload: DashboardStat[] }
  | { type: 'SET_QUICK_ACTIONS'; payload: DashboardQuickAction[] }
  | { type: 'SET_SECTION_DATA'; payload: { sectionId: string; data: any } }
  | { type: 'SET_LOADING'; payload: Partial<DashboardLoadingState> }
  | { type: 'ADD_ERROR'; payload: DashboardError }
  | { type: 'CLEAR_ERROR'; payload?: string }
  | { type: 'SET_THEME'; payload: DashboardTheme }
  | { type: 'REFRESH_START' }
  | { type: 'REFRESH_COMPLETE' };

// Dashboard state interface
interface DashboardState {
  role: DashboardRole;
  config: DashboardConfig;
  data: {
    stats: DashboardStat[];
    quickActions: DashboardQuickAction[];
    sectionData: Record<string, any>;
  };
  loading: DashboardLoadingState;
  errors: DashboardError[];
  theme: DashboardTheme;
  lastRefresh: number;
}

// Initial state
const initialState: DashboardState = {
  role: 'user',
  config: getDashboardConfig('user'),
  data: {
    stats: [],
    quickActions: [],
    sectionData: {},
  },
  loading: {
    global: false,
    sections: {},
    stats: false,
    quickActions: false,
  },
  errors: [],
  theme: getDashboardTheme('user'),
  lastRefresh: 0,
};

// Dashboard reducer
function dashboardReducer(state: DashboardState, action: DashboardAction): DashboardState {
  switch (action.type) {
    case 'SET_ROLE':
      return {
        ...state,
        role: action.payload,
        config: getDashboardConfig(action.payload),
        theme: getDashboardTheme(action.payload),
      };

    case 'SET_CONFIG':
      return {
        ...state,
        config: action.payload,
      };

    case 'SET_STATS':
      return {
        ...state,
        data: {
          ...state.data,
          stats: action.payload,
        },
        loading: {
          ...state.loading,
          stats: false,
        },
      };

    case 'SET_QUICK_ACTIONS':
      return {
        ...state,
        data: {
          ...state.data,
          quickActions: action.payload,
        },
        loading: {
          ...state.loading,
          quickActions: false,
        },
      };

    case 'SET_SECTION_DATA':
      return {
        ...state,
        data: {
          ...state.data,
          sectionData: {
            ...state.data.sectionData,
            [action.payload.sectionId]: action.payload.data,
          },
        },
        loading: {
          ...state.loading,
          sections: {
            ...state.loading.sections,
            [action.payload.sectionId]: false,
          },
        },
      };

    case 'SET_LOADING':
      return {
        ...state,
        loading: {
          ...state.loading,
          ...action.payload,
        },
      };

    case 'ADD_ERROR':
      return {
        ...state,
        errors: [...state.errors, action.payload],
      };

    case 'CLEAR_ERROR':
      return {
        ...state,
        errors: action.payload
          ? state.errors.filter((error, index) => index.toString() !== action.payload)
          : [],
      };

    case 'SET_THEME':
      return {
        ...state,
        theme: action.payload,
      };

    case 'REFRESH_START':
      return {
        ...state,
        loading: {
          ...state.loading,
          global: true,
        },
      };

    case 'REFRESH_COMPLETE':
      return {
        ...state,
        loading: {
          ...state.loading,
          global: false,
        },
        lastRefresh: Date.now(),
      };

    default:
      return state;
  }
}

// Create context
const DashboardContext = createContext<DashboardContextValue | null>(null);

// Dashboard provider props
interface DashboardProviderProps {
  children: React.ReactNode;
  initialRole?: DashboardRole;
  config?: Partial<DashboardConfig>;
}

// Dashboard provider component
export const DashboardProvider: React.FC<DashboardProviderProps> = ({
  children,
  initialRole,
  config: configOverride,
}) => {
  const { user } = useAppSelector(state => state.auth);
  const [state, dispatch] = useReducer(dashboardReducer, initialState);

  // Determine role from user or use provided initial role
  const userRole = initialRole || getDashboardRole(user);

  // Initialize role and config
  useEffect(() => {
    if (userRole !== state.role) {
      dispatch({ type: 'SET_ROLE', payload: userRole });
      trackDashboardEvent(userRole, 'dashboard_loaded');
    }
  }, [userRole, state.role]);

  // Apply config override
  useEffect(() => {
    if (configOverride) {
      const mergedConfig = {
        ...state.config,
        ...configOverride,
      };
      dispatch({ type: 'SET_CONFIG', payload: mergedConfig });
    }
  }, [configOverride, state.config]);

  // Refresh data function
  const refreshData = useCallback(async () => {
    try {
      dispatch({ type: 'REFRESH_START' });
      
      // Set loading states
      dispatch({
        type: 'SET_LOADING',
        payload: {
          stats: true,
          quickActions: true,
        },
      });

      // This would be replaced with actual API calls based on role
      // For now, we'll simulate the data loading
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock data - in real implementation, this would come from role-specific data providers
      const mockStats: DashboardStat[] = [];
      const mockQuickActions: DashboardQuickAction[] = [];

      dispatch({ type: 'SET_STATS', payload: mockStats });
      dispatch({ type: 'SET_QUICK_ACTIONS', payload: mockQuickActions });

      trackDashboardEvent(state.role, 'data_refreshed');
    } catch (error) {
      dispatch({
        type: 'ADD_ERROR',
        payload: {
          message: 'Failed to refresh dashboard data',
          type: 'error',
          recoverable: true,
          retryAction: refreshData,
        },
      });
    } finally {
      dispatch({ type: 'REFRESH_COMPLETE' });
    }
  }, [state.role]);

  // Update section data
  const updateSection = useCallback((sectionId: string, data: any) => {
    dispatch({
      type: 'SET_SECTION_DATA',
      payload: { sectionId, data },
    });
    trackDashboardEvent(state.role, 'section_updated', sectionId);
  }, [state.role]);

  // Clear error
  const clearError = useCallback((errorId?: string) => {
    dispatch({ type: 'CLEAR_ERROR', payload: errorId });
  }, []);

  // Auto-refresh setup
  useEffect(() => {
    const interval = getRefreshInterval(state.role);
    const timer = setInterval(refreshData, interval);
    
    // Initial data load
    refreshData();

    return () => clearInterval(timer);
  }, [state.role, refreshData]);

  // Context value
  const contextValue: DashboardContextValue = {
    role: state.role,
    config: state.config,
    data: state.data,
    loading: state.loading,
    errors: state.errors,
    theme: state.theme,
    refreshData,
    updateSection,
    clearError,
  };

  return (
    <DashboardContext.Provider value={contextValue}>
      {children}
    </DashboardContext.Provider>
  );
};

// Hook to use dashboard context
export const useDashboard = (): DashboardContextValue => {
  const context = useContext(DashboardContext);
  if (!context) {
    throw new Error('useDashboard must be used within a DashboardProvider');
  }
  return context;
};

// Hook to use dashboard data with loading state
export const useDashboardData = () => {
  const { data, loading, errors, refreshData } = useDashboard();
  
  return {
    stats: data.stats,
    quickActions: data.quickActions,
    sectionData: data.sectionData,
    isLoading: loading.global || loading.stats || loading.quickActions,
    errors,
    refreshData,
  };
};

// Hook to use dashboard theme
export const useDashboardTheme = () => {
  const { theme, role } = useDashboard();
  
  return {
    theme,
    role,
    getCardClasses: () => `${theme.cardBackground} rounded-lg p-6 ${theme.borderColor} border`,
    getTextClasses: (type: 'primary' | 'secondary' = 'primary') => 
      type === 'primary' ? theme.textPrimary : theme.textSecondary,
    getBackgroundClasses: () => `bg-gradient-to-br ${theme.backgroundGradient}`,
  };
};

// Hook for section-specific loading state
export const useSectionLoading = (sectionId: string) => {
  const { loading, updateSection } = useDashboard();
  
  return {
    isLoading: loading.sections[sectionId] || false,
    setLoading: (isLoading: boolean) => {
      dispatch({
        type: 'SET_LOADING',
        payload: {
          sections: {
            ...loading.sections,
            [sectionId]: isLoading,
          },
        },
      });
    },
    updateData: (data: any) => updateSection(sectionId, data),
  };
};

export default DashboardContext;
