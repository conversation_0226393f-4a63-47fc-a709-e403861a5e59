import React, { useState, useEffect } from 'react';
import {
  Store,
  TrendingUp,
  Crown,
  Star,
  Download,
  DollarSign,
  Gift,
  Award,
  Filter,
  Search,
  ShoppingCart,
  Heart,
  Eye,
  Users,
  Clock,
  Zap,
  CheckCircle,
  Shield
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { BusinessPlanTemplate } from '../../services/businessPlanApi';
import { RTLText, RTLFlex } from '../common';
import { businessPlanTemplatesAPI } from '../../services/templateCustomizationApi';
import { downloadTemplate } from '../../utils/templateDownload';

interface MarketplaceTemplate extends BusinessPlanTemplate {
  price: number;
  is_premium: boolean;
  is_featured: boolean;
  is_bestseller: boolean;
  author: {
    id: number;
    username: string;
    first_name: string;
    last_name: string;
    is_verified: boolean;
    rating: number;
    templates_count: number;
  };
  sales_count: number;
  preview_sections: number;
  license_type: 'personal' | 'commercial' | 'enterprise';
  includes: string[];
  last_updated: string;
}

interface TemplateMarketplaceProps {
  onPurchaseTemplate?: (template: MarketplaceTemplate) => void;
  onPreviewTemplate?: (template: MarketplaceTemplate) => void;
}

export const TemplateMarketplace: React.FC<TemplateMarketplaceProps> = ({ onPurchaseTemplate,
  onPreviewTemplate
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [templates, setTemplates] = useState<MarketplaceTemplate[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [priceFilter, setPriceFilter] = useState('all');
  const [sortBy, setSortBy] = useState('featured');
  const [favorites, setFavorites] = useState<number[]>([]);
  const [downloading, setDownloading] = useState<number | null>(null);

  useEffect(() => {
    fetchMarketplaceTemplates();
  }, [selectedCategory, priceFilter, sortBy]);

  const fetchMarketplaceTemplates = async () => {
    setLoading(true);
    try {
      // Fetch real templates from API
      const realTemplates = await businessPlanTemplatesAPI.getTemplates();

      // Convert to marketplace templates using real data from API
      const marketplaceTemplates: MarketplaceTemplate[] = realTemplates.map(template => ({
        ...template,
        price: template.price || 0, // Use real price from API
        is_premium: template.is_premium || false, // Use real premium status from API
        is_featured: template.is_featured || false, // Use real featured status from API
        is_bestseller: template.is_bestseller || false, // Use real bestseller status from API
        author: template.author_details || {
          id: 0,
          username: 'system',
          first_name: t("common.system", "System"),
          last_name: '',
          is_verified: false,
          rating: 0,
          templates_count: 0
        },
        sales_count: template.usage_count || 0, // Use real usage count from API
        preview_sections: Object.keys(template.sections?.sections || {}).length,
        license_type: template.license_type || 'personal' as const,
        includes: template.includes || [], // Use real includes from API
        last_updated: template.updated_at || template.created_at,
        rating: template.rating || 0, // Use real rating from API
        estimated_time: template.estimated_time || 0 // Use real estimated time from API
      }));

      setTemplates(marketplaceTemplates);
    } catch (error) {
      console.error('Error fetching marketplace templates:', error);
      // Fallback to empty array
      setTemplates([]);
    } finally {
      setLoading(false);
    }
  };

  const toggleFavorite = (templateId: number) => {
    setFavorites(prev =>
      prev.includes(templateId)
        ? prev.filter(id => id !== templateId)
        : [...prev, templateId]
    );
  };

  const handleDownloadTemplate = async (template: MarketplaceTemplate) => {
    setDownloading(template.id);
    try {
      await downloadTemplate(template.id, {
        format: 'json',
        includeInstructions: true,
        includeExamples: true
      });
    } catch (error) {
      console.error('Error downloading template:', error);
    } finally {
      setDownloading(null);
    }
  };

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || template.industry.toLowerCase() === selectedCategory;
    const matchesPrice = priceFilter === 'all' ||
                        (priceFilter === 'free' && template.price === 0) ||
                        (priceFilter === 'premium' && template.price > 0);

    return matchesSearch && matchesCategory && matchesPrice;
  });

  const sortedTemplates = [...filteredTemplates].sort((a, b) => {
    switch (sortBy) {
      case 'featured':
        return (b.is_featured ? 1 : 0) - (a.is_featured ? 1 : 0);
      case 'bestseller':
        return b.sales_count - a.sales_count;
      case 'rating':
        return b.rating - a.rating;
      case 'price_low':
        return a.price - b.price;
      case 'price_high':
        return b.price - a.price;
      case 'newest':
        return new Date(b.last_updated).getTime() - new Date(a.last_updated).getTime();
      default:
        return 0;
    }
  });

  const renderTemplateCard = (template: MarketplaceTemplate) => (
    <div
      key={template.id}
      className="bg-gray-800/50 rounded-lg border border-gray-700 hover:border-purple-500/50 transition-all duration-300 overflow-hidden"
    >
      {/* Header with badges */}
      <div className="relative p-4 pb-2">
        <div className={`absolute top-2 right-2 flex gap-1 ${isRTL ? "flex-row-reverse" : ""}`}>
          {template.is_featured && (
            <span className={`px-2 py-1 bg-gradient-to-r from-purple-600 to-pink-600 text-white text-xs rounded-full flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <Crown size={12} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
              {t('marketplace.featured')}
            </span>
          )}
          {template.is_bestseller && (
            <span className={`px-2 py-1 bg-gradient-to-r from-yellow-600 to-orange-600 text-white text-xs rounded-full flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <TrendingUp size={12} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
              {t('marketplace.bestseller')}
            </span>
          )}
        </div>

        <button
          onClick={() => toggleFavorite(template.id)}
          className={`absolute top-2 left-2 p-2 rounded-full transition-colors ${
            favorites.includes(template.id)
              ? 'text-red-400 bg-red-400/20'
              : 'text-gray-400 hover:text-red-400 bg-gray-700/50'}
          }`}
        >
          <Heart size={16} className={favorites.includes(template.id) ? 'fill-current' : ''} />
        </button>
      </div>

      {/* Content */}
      <div className="p-4 pt-2">
        <RTLText as="h3" className="font-semibold text-lg mb-2">
          {template.name}
        </RTLText>

        <RTLText className="text-gray-300 text-sm mb-3 line-clamp-2">
          {template.description}
        </RTLText>

        {/* Author */}
        <RTLFlex className="items-center mb-3">
          <div className={`w-6 h-6 bg-purple-600 rounded-full flex items-center justify-center text-white text-xs ${isRTL ? "flex-row-reverse" : ""}`}>
            {template.author.first_name[0]}
          </div>
          <RTLText className={`text-sm text-gray-400 ml-2 ${isRTL ? "space-x-reverse" : ""}`}>
            {template.author.first_name} {template.author.last_name}
          </RTLText>
          {template.author.is_verified && (
            <CheckCircle className={`text-blue-400 ml-1 ${isRTL ? "space-x-reverse" : ""}`} size={14} />
          )}
        </RTLFlex>

        {/* Stats */}
        <div className="grid grid-cols-3 gap-2 mb-4 text-xs text-gray-400">
          <RTLFlex className="items-center">
            <Star className="text-yellow-400 fill-current" size={12} />
            <span className={`ml-1 ${isRTL ? "space-x-reverse" : ""}`}>{template.rating}</span>
          </RTLFlex>
          <RTLFlex className="items-center">
            <Download size={12} />
            <span className={`ml-1 ${isRTL ? "space-x-reverse" : ""}`}>{template.sales_count}</span>
          </RTLFlex>
          <RTLFlex className="items-center">
            <Clock size={12} />
            <span className={`ml-1 ${isRTL ? "space-x-reverse" : ""}`}>{template.estimated_time}h</span>
          </RTLFlex>
        </div>

        {/* Includes */}
        <div className="mb-4">
          <RTLText className="text-xs font-medium text-gray-300 mb-2">
            {t('marketplace.includes')}:
          </RTLText>
          <div className="space-y-1">
            {template.includes.slice(0, 3).map((item, index) => (
              <RTLFlex key={index} className="items-center text-xs text-gray-400">
                <CheckCircle className="text-green-400" size={12} />
                <span className={`ml-1 ${isRTL ? "space-x-reverse" : ""}`}>{item}</span>
              </RTLFlex>
            ))}
            {template.includes.length > 3 && (
              <RTLText className="text-xs text-gray-400">
                +{template.includes.length - 3} {t('marketplace.more')}
              </RTLText>
            )}
          </div>
        </div>

        {/* Price and Actions */}
        <RTLFlex className="items-center justify-between">
          <div>
            {template.price === 0 ? (
              <RTLFlex className="items-center text-green-400 font-bold">
                <Gift size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                {t('marketplace.free')}
              </RTLFlex>
            ) : (
              <RTLFlex className="items-center">
                <DollarSign size={16} className="text-purple-400" />
                <span className="text-xl font-bold">{template.price}</span>
              </RTLFlex>
            )}
          </div>

          <RTLFlex className="items-center gap-2">
            <button
              onClick={() => onPreviewTemplate?.(template)}
              className={`px-3 py-1 bg-gray-700 hover:bg-gray-600 rounded text-sm text-white transition-colors flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
            >
              <Eye size={14} className={isRTL ? 'ml-1' : 'mr-1'} />
              {t('marketplace.preview')}
            </button>

            <button
              onClick={() => template.price === 0 ? handleDownloadTemplate(template) : onPurchaseTemplate?.(template)}
              disabled={downloading === template.id}
              className={`px-4 py-1 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed rounded text-sm text-white transition-colors flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
            >
              {downloading === template.id ? (
                <>
                  <div className={`animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-1 ${isRTL ? "space-x-reverse" : ""}`}></div>
                  {t('common.downloading')}
                </>
              ) : template.price === 0 ? (
                <>
                  <Download size={14} className={isRTL ? 'ml-1' : 'mr-1'} />
                  {t('marketplace.download')}
                </>
              ) : (
                <>
                  <ShoppingCart size={14} className={isRTL ? 'ml-1' : 'mr-1'} />
                  {t('marketplace.buy')}
                </>
              )}
            </button>
          </RTLFlex>
        </RTLFlex>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-900/50 to-blue-900/50 rounded-lg p-6 border border-purple-500/30">
        <RTLFlex className="items-center mb-4">
          <Store className={`text-purple-400 mr-3 ${isRTL ? "space-x-reverse" : ""}`} size={24} />
          <RTLText as="h2" className="text-2xl font-bold">
            {t('marketplace.templateMarketplace')}
          </RTLText>
        </RTLFlex>

        <RTLText className="text-gray-300">
          {t('marketplace.description')}
        </RTLText>
      </div>

      {/* Filters */}
      <div className="bg-gray-800/50 rounded-lg p-4 space-y-4">
        {/* Search */}
        <div className="relative">
          <Search className={`absolute top-3 ${isRTL ? 'right-3' : 'left-3'} text-gray-400`} size={20} />
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder={t('marketplace.searchTemplates')}
            className={`w-full bg-gray-700 border border-gray-600 rounded-lg ${isRTL ? 'pr-10 pl-4' : 'pl-10 pr-4'} py-3 text-white placeholder-gray-400 focus:border-purple-500 focus:outline-none`}
          />
        </div>

        {/* Filter Controls */}
        <RTLFlex className={`items-center gap-4 flex-wrap ${isRTL ? "flex-row-reverse" : ""}`}>
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:border-purple-500 focus:outline-none"
          >
            <option value="all">{t('marketplace.allCategories')}</option>
            <option value="technology">{t('marketplace.technology')}</option>
            <option value="e-commerce">{t('marketplace.ecommerce')}</option>
            <option value="healthcare">{t('marketplace.healthcare')}</option>
            <option value="finance">{t('marketplace.finance')}</option>
          </select>

          <select
            value={priceFilter}
            onChange={(e) => setPriceFilter(e.target.value)}
            className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:border-purple-500 focus:outline-none"
          >
            <option value="all">{t('marketplace.allPrices')}</option>
            <option value="free">{t('marketplace.free')}</option>
            <option value="premium">{t('marketplace.premium')}</option>
          </select>

          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:border-purple-500 focus:outline-none"
          >
            <option value="featured">{t('marketplace.featured')}</option>
            <option value="bestseller">{t('marketplace.bestseller')}</option>
            <option value="rating">{t('marketplace.highestRated')}</option>
            <option value="price_low">{t('marketplace.priceLowToHigh')}</option>
            <option value="price_high">{t('marketplace.priceHighToLow')}</option>
            <option value="newest">{t('marketplace.newest')}</option>
          </select>
        </RTLFlex>
      </div>

      {/* Results */}
      {loading ? (
        <div className={`flex items-center justify-center py-12 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
        </div>
      ) : sortedTemplates.length === 0 ? (
        <div className="text-center py-12">
          <Store className="mx-auto mb-4 text-gray-400" size={48} />
          <RTLText className="text-gray-400">
            {t('marketplace.noTemplatesFound')}
          </RTLText>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {sortedTemplates.map(renderTemplateCard)}
        </div>
      )}
    </div>
  );
};
