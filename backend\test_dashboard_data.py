#!/usr/bin/env python
"""
Full-Stack Dashboard Testing Script
Tests the user dashboard with real data and API endpoints
"""

import os
import sys
import django
import requests
import json
from datetime import datetime, timedelta

# Setup Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.db import transaction
from incubator.models import BusinessIdea
from api.models import Post, Comment, Event, Resource

User = get_user_model()

class DashboardTester:
    def __init__(self):
        self.base_url = "http://127.0.0.1:8000/api"
        self.frontend_url = "http://localhost:3000"
        self.test_user = None
        self.auth_token = None
        
    def setup_test_data(self):
        """Create test user and sample data"""
        print("🔧 Setting up test data...")
        
        with transaction.atomic():
            # Create or get test user
            self.test_user, created = User.objects.get_or_create(
                username='testuser',
                defaults={
                    'email': '<EMAIL>',
                    'first_name': 'Test',
                    'last_name': 'User',
                    'is_active': True
                }
            )
            
            if created:
                self.test_user.set_password('testpass123')
                self.test_user.save()
                print(f"✅ Created test user: {self.test_user.username}")
            else:
                print(f"✅ Using existing test user: {self.test_user.username}")
            
            # Create sample business ideas
            business_ideas_data = [
                {
                    'title': 'AI-Powered Food Delivery',
                    'description': 'Revolutionary food delivery service using AI for optimal routing and customer preferences.',
                    'moderation_status': 'approved'
                },
                {
                    'title': 'Sustainable Fashion Platform',
                    'description': 'Online marketplace for eco-friendly and sustainable fashion brands.',
                    'moderation_status': 'pending'
                },
                {
                    'title': 'Virtual Reality Education',
                    'description': 'Immersive VR learning experiences for students and professionals.',
                    'moderation_status': 'rejected'
                }
            ]
            
            for idea_data in business_ideas_data:
                idea, created = BusinessIdea.objects.get_or_create(
                    title=idea_data['title'],
                    owner=self.test_user,
                    defaults={
                        'description': idea_data['description'],
                        'moderation_status': idea_data['moderation_status']
                    }
                )
                if created:
                    print(f"✅ Created business idea: {idea.title}")
            
            # Create sample posts
            posts_data = [
                {'title': 'My First Business Idea', 'content': 'Excited to share my first business idea!'},
                {'title': 'Learning from Failure', 'content': 'What I learned from my rejected idea.'},
                {'title': 'Success Story', 'content': 'My approved idea is getting traction!'}
            ]
            
            for post_data in posts_data:
                post, created = Post.objects.get_or_create(
                    title=post_data['title'],
                    author=self.test_user,
                    defaults={'content': post_data['content']}
                )
                if created:
                    print(f"✅ Created post: {post.title}")
            
            print("✅ Test data setup complete!")
    
    def authenticate_user(self):
        """Authenticate test user and get JWT token"""
        print("🔐 Authenticating test user...")

        # First try to create a superuser for testing
        try:
            from django.contrib.auth import get_user_model
            User = get_user_model()

            # Create or get test user
            user, created = User.objects.get_or_create(
                username='testuser',
                defaults={
                    'email': '<EMAIL>',
                    'first_name': 'Test',
                    'last_name': 'User',
                    'is_active': True,
                    'is_staff': True
                }
            )

            if created or not user.check_password('testpass123'):
                user.set_password('testpass123')
                user.save()
                print(f"✅ Updated user password: {user.username}")

        except Exception as e:
            print(f"⚠️ Could not create user via Django: {e}")

        # Try different authentication endpoints
        auth_endpoints = [
            f"{self.base_url}/users/token/",
            f"{self.base_url}/auth/login/",
            f"{self.base_url}/token/",
        ]

        auth_data = {
            'username': 'testuser',
            'password': 'testpass123'
        }

        for endpoint in auth_endpoints:
            try:
                print(f"Trying endpoint: {endpoint}")
                response = requests.post(endpoint, json=auth_data)
                print(f"Response status: {response.status_code}")

                if response.status_code == 200:
                    token_data = response.json()
                    if 'access' in token_data:
                        self.auth_token = token_data['access']
                        print("✅ Authentication successful!")
                        return True
                    elif 'token' in token_data:
                        self.auth_token = token_data['token']
                        print("✅ Authentication successful!")
                        return True
                else:
                    print(f"Response: {response.text[:200]}")

            except Exception as e:
                print(f"❌ Error with {endpoint}: {e}")

        print("⚠️ Authentication failed, proceeding without token")
        return False
    
    def test_api_endpoints(self):
        """Test all dashboard-related API endpoints"""
        print("🧪 Testing API endpoints...")

        headers = {'Authorization': f'Bearer {self.auth_token}'} if self.auth_token else {}

        endpoints_to_test = [
            ('API Root', f"{self.base_url}/"),
            ('Business Ideas', f"{self.base_url}/incubator/business-ideas/"),
            ('Users List', f"{self.base_url}/users/users/"),
            ('Posts', f"{self.base_url}/posts/"),
            ('Forums', f"{self.base_url}/forums/"),
        ]

        results = {}

        for name, url in endpoints_to_test:
            try:
                print(f"\n🔍 Testing {name}: {url}")
                response = requests.get(url, headers=headers)

                results[name] = {
                    'status_code': response.status_code,
                    'success': response.status_code in [200, 401],  # 401 is expected without auth
                    'url': url
                }

                if response.status_code == 200:
                    try:
                        data = response.json()
                        results[name]['data_length'] = len(data) if isinstance(data, list) else 1
                        print(f"✅ {name}: {response.status_code} - Data received ({results[name]['data_length']} items)")

                        # Show sample data structure
                        if isinstance(data, list) and len(data) > 0:
                            print(f"   Sample keys: {list(data[0].keys())[:5]}")
                        elif isinstance(data, dict):
                            print(f"   Keys: {list(data.keys())[:5]}")

                    except:
                        print(f"✅ {name}: {response.status_code} - Non-JSON response")

                elif response.status_code == 401:
                    print(f"🔒 {name}: {response.status_code} - Authentication required (expected)")

                elif response.status_code == 404:
                    print(f"❌ {name}: {response.status_code} - Endpoint not found")

                else:
                    print(f"⚠️ {name}: {response.status_code} - {response.text[:100]}")

            except Exception as e:
                results[name] = {'error': str(e), 'success': False}
                print(f"❌ {name}: Error - {e}")

        return results
    
    def run_full_test(self):
        """Run complete dashboard test suite"""
        print("🚀 Starting Full-Stack Dashboard Test Suite")
        print("=" * 50)
        
        # Phase 1: Setup
        self.setup_test_data()
        print()
        
        # Phase 2: Authentication
        if not self.authenticate_user():
            print("❌ Cannot proceed without authentication")
            return False
        print()
        
        # Phase 3: API Testing
        api_results = self.test_api_endpoints()
        print()
        
        # Phase 4: Frontend Testing Instructions
        self.print_frontend_test_instructions()
        
        return True
    
    def print_frontend_test_instructions(self):
        """Print detailed frontend testing instructions"""
        print("🌐 FRONTEND TESTING INSTRUCTIONS")
        print("=" * 50)
        print(f"1. Open browser to: {self.frontend_url}")
        print("2. Login with credentials:")
        print("   - Username: testuser")
        print("   - Password: testpass123")
        print()
        print("3. Navigate to User Dashboard and verify:")
        print("   ✅ Statistics show real data (not zeros)")
        print("   ✅ Business Ideas: 3 ideas (1 approved, 1 pending, 1 rejected)")
        print("   ✅ Posts Created: 3 posts")
        print("   ✅ Charts display with real data")
        print("   ✅ Recent activity shows actual posts")
        print("   ✅ Business ideas section shows 3 ideas with status badges")
        print("   ✅ Loading states work properly")
        print("   ✅ Error handling works (try disconnecting backend)")
        print("   ✅ Refresh functionality works")
        print("   ✅ All navigation links work")
        print("   ✅ Responsive design on different screen sizes")
        print("   ✅ RTL support (if Arabic is enabled)")

if __name__ == "__main__":
    tester = DashboardTester()
    tester.run_full_test()
