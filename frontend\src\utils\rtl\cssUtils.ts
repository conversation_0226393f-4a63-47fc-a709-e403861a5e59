/**
 * RTL CSS Utilities
 *
 * This file contains utility functions for handling RTL CSS transformations.
 * It provides functions to convert physical CSS properties to logical ones.
 */

import { useLanguage } from '../../hooks/useLanguage';
import { SizeValue } from './index';

/**
 * Interface for logical margin properties
 */
export interface LogicalMargin {
  marginInlineStart?: string | number;
  marginInlineEnd?: string | number;
  marginBlockStart?: string | number;
  marginBlockEnd?: string | number;
}

/**
 * Interface for logical padding properties
 */
export interface LogicalPadding {
  paddingInlineStart?: string | number;
  paddingInlineEnd?: string | number;
  paddingBlockStart?: string | number;
  paddingBlockEnd?: string | number;
}

/**
 * Interface for logical border properties
 */
export interface LogicalBorder {
  borderInlineStart?: string;
  borderInlineEnd?: string;
  borderBlockStart?: string;
  borderBlockEnd?: string;
}

/**
 * Interface for logical position properties
 */
export interface LogicalPosition {
  insetInlineStart?: string | number;
  insetInlineEnd?: string | number;
  insetBlockStart?: string | number;
  insetBlockEnd?: string | number;
}

/**
 * Interface for spacing properties (margin, padding)
 */
export interface SpacingProps {
  top?: SizeValue;
  bottom?: SizeValue;
  start?: SizeValue;
  end?: SizeValue;
  x?: SizeValue; // horizontal (start and end)
  y?: SizeValue; // vertical (top and bottom)
  all?: SizeValue; // all sides
}

/**
 * Converts physical margin properties to logical ones
 * @param marginLeft Left margin
 * @param marginRight Right margin
 * @param marginTop Top margin
 * @param marginBottom Bottom margin
 * @returns Logical margin properties
 */
export const toLogicalMargin = (
  marginLeft?: string | number,
  marginRight?: string | number,
  marginTop?: string | number,
  marginBottom?: string | number
): LogicalMargin => {
  return {
    marginInlineStart: marginLeft,
    marginInlineEnd: marginRight,
    marginBlockStart: marginTop,
    marginBlockEnd: marginBottom,
  };
};

/**
 * Converts physical padding properties to logical ones
 * @param paddingLeft Left padding
 * @param paddingRight Right padding
 * @param paddingTop Top padding
 * @param paddingBottom Bottom padding
 * @returns Logical padding properties
 */
export const toLogicalPadding = (
  paddingLeft?: string | number,
  paddingRight?: string | number,
  paddingTop?: string | number,
  paddingBottom?: string | number
): LogicalPadding => {
  return {
    paddingInlineStart: paddingLeft,
    paddingInlineEnd: paddingRight,
    paddingBlockStart: paddingTop,
    paddingBlockEnd: paddingBottom,
  };
};

/**
 * Converts physical border properties to logical ones
 * @param borderLeft Left border
 * @param borderRight Right border
 * @param borderTop Top border
 * @param borderBottom Bottom border
 * @returns Logical border properties
 */
export const toLogicalBorder = (
  borderLeft?: string,
  borderRight?: string,
  borderTop?: string,
  borderBottom?: string
): LogicalBorder => {
  return {
    borderInlineStart: borderLeft,
    borderInlineEnd: borderRight,
    borderBlockStart: borderTop,
    borderBlockEnd: borderBottom,
  };
};

/**
 * Converts physical position properties to logical ones
 * @param left Left position
 * @param right Right position
 * @param top Top position
 * @param bottom Bottom position
 * @returns Logical position properties
 */
export const toLogicalPosition = (
  left?: string | number,
  right?: string | number,
  top?: string | number,
  bottom?: string | number
): LogicalPosition => {
  return {
    insetInlineStart: left,
    insetInlineEnd: right,
    insetBlockStart: top,
    insetBlockEnd: bottom,
  };
};

/**
 * Custom hook to get RTL-aware CSS properties
 * @returns Object with functions to get RTL-aware CSS properties
 */
export const useRTLCSSProperties = () => {
  const { isRTL } = useLanguage();

  /**
   * Gets RTL-aware margin properties
   * @param start Start margin (left in LTR, right in RTL)
   * @param end End margin (right in LTR, left in RTL)
   * @param top Top margin
   * @param bottom Bottom margin
   * @returns CSS properties object
   */
  const getMargin = (
    start?: string | number,
    end?: string | number,
    top?: string | number,
    bottom?: string | number
  ) => {
    return {
      marginLeft: isRTL ? end : start,
      marginRight: isRTL ? start : end,
      marginTop: top,
      marginBottom: bottom,
    };
  };

  /**
   * Gets RTL-aware padding properties
   * @param start Start padding (left in LTR, right in RTL)
   * @param end End padding (right in LTR, left in RTL)
   * @param top Top padding
   * @param bottom Bottom padding
   * @returns CSS properties object
   */
  const getPadding = (
    start?: string | number,
    end?: string | number,
    top?: string | number,
    bottom?: string | number
  ) => {
    return {
      paddingLeft: isRTL ? end : start,
      paddingRight: isRTL ? start : end,
      paddingTop: top,
      paddingBottom: bottom,
    };
  };

  /**
   * Gets RTL-aware border properties
   * @param start Start border (left in LTR, right in RTL)
   * @param end End border (right in LTR, left in RTL)
   * @param top Top border
   * @param bottom Bottom border
   * @returns CSS properties object
   */
  const getBorder = (
    start?: string,
    end?: string,
    top?: string,
    bottom?: string
  ) => {
    return {
      borderLeft: isRTL ? end : start,
      borderRight: isRTL ? start : end,
      borderTop: top,
      borderBottom: bottom,
    };
  };

  /**
   * Gets RTL-aware position properties
   * @param start Start position (left in LTR, right in RTL)
   * @param end End position (right in LTR, left in RTL)
   * @param top Top position
   * @param bottom Bottom position
   * @returns CSS properties object
   */
  const getPosition = (
    start?: string | number,
    end?: string | number,
    top?: string | number,
    bottom?: string | number
  ) => {
    return {
      left: isRTL ? end : start,
      right: isRTL ? start : end,
      top,
      bottom,
    };
  };

  /**
   * Gets Tailwind margin classes from spacing props
   * @param props Spacing properties
   * @returns String of Tailwind margin classes
   */
  const getMarginClasses = (props: SpacingProps): string => {
    const classes: string[] = [];

    if (props.all !== undefined) {
      classes.push(`m-${props.all}`);
      return classes.join(' ');
    }

    if (props.x !== undefined) {
      classes.push(`mx-${props.x}`);
    } else {
      if (props.start !== undefined) {
        classes.push(isRTL ? `mr-${props.start}` : `ml-${props.start}`);
      }
      if (props.end !== undefined) {
        classes.push(isRTL ? `ml-${props.end}` : `mr-${props.end}`);
      }
    }

    if (props.y !== undefined) {
      classes.push(`my-${props.y}`);
    } else {
      if (props.top !== undefined) {
        classes.push(`mt-${props.top}`);
      }
      if (props.bottom !== undefined) {
        classes.push(`mb-${props.bottom}`);
      }
    }

    return classes.join(' ');
  };

  /**
   * Gets Tailwind padding classes from spacing props
   * @param props Spacing properties
   * @returns String of Tailwind padding classes
   */
  const getPaddingClasses = (props: SpacingProps): string => {
    const classes: string[] = [];

    if (props.all !== undefined) {
      classes.push(`p-${props.all}`);
      return classes.join(' ');
    }

    if (props.x !== undefined) {
      classes.push(`px-${props.x}`);
    } else {
      if (props.start !== undefined) {
        classes.push(isRTL ? `pr-${props.start}` : `pl-${props.start}`);
      }
      if (props.end !== undefined) {
        classes.push(isRTL ? `pl-${props.end}` : `pr-${props.end}`);
      }
    }

    if (props.y !== undefined) {
      classes.push(`py-${props.y}`);
    } else {
      if (props.top !== undefined) {
        classes.push(`pt-${props.top}`);
      }
      if (props.bottom !== undefined) {
        classes.push(`pb-${props.bottom}`);
      }
    }

    return classes.join(' ');
  };

  return {
    getMargin,
    getPadding,
    getBorder,
    getPosition,
    getMarginClasses,
    getPaddingClasses
  };
};

export default useRTLCSSProperties;
