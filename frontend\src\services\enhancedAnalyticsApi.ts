import { apiRequest } from './api';

// Types for Enhanced Analytics
export interface BusinessAnalytics {
  id: number;
  business_idea: number;
  progress_rate: number;
  milestone_completion_rate: number;
  goal_achievement_rate: number;
  team_size: number;
  mentor_engagement: number;
  last_calculated: string;
  historical_snapshots?: HistoricalSnapshot[];
}

export interface HistoricalSnapshot {
  id: number;
  date: string;
  progress_rate: number;
  milestone_completion_rate: number;
  goal_achievement_rate: number;
  team_size: number;
  mentor_engagement: number;
}

export interface PredictiveAnalytics {
  id: number;
  business_analytics: number;
  predicted_success_probability: number;
  predicted_completion_date: string | null;
  risk_factors: RiskFactor[];
  growth_projections: GrowthProjection[];
  recommendations: string[];
  confidence_score: number;
  last_calculated: string;
}

export interface RiskFactor {
  factor: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  impact_score: number;
  description: string;
}

export interface GrowthProjection {
  metric: string;
  current_value: number;
  projected_value: number;
  timeframe_months: number;
  confidence: number;
}

export interface ComparativeAnalytics {
  id: number;
  business_analytics: number;
  industry_average_progress: number;
  stage_average_progress: number;
  percentile_ranking: number;
  similar_ideas_comparison: SimilarIdeaComparison[];
  benchmark_metrics: BenchmarkMetric[];
  last_calculated: string;
}

export interface SimilarIdeaComparison {
  idea_title: string;
  progress_rate: number;
  milestone_completion_rate: number;
  goal_achievement_rate: number;
  similarity_score: number;
}

export interface BenchmarkMetric {
  metric: string;
  your_value: number;
  industry_average: number;
  top_quartile: number;
  performance_rating: 'poor' | 'below_average' | 'average' | 'above_average' | 'excellent';
}

export interface DashboardSummary {
  overall_health: {
    score: number;
    status: 'poor' | 'fair' | 'good' | 'excellent';
    trend: 'declining' | 'stable' | 'improving';
  };
  key_strengths: Array<{
    metric: string;
    value: number;
    message: string;
  }>;
  key_weaknesses: Array<{
    metric: string;
    value: number;
    message: string;
  }>;
  recent_progress: {
    recent_updates: number;
    recent_milestones: number;
    recent_goals: number;
    total_recent_activities: number;
  };
}

export interface RecommendedAction {
  id: number;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  category: string;
  estimated_impact: number;
  estimated_effort: number;
  deadline: string | null;
  is_completed: boolean;
  created_at: string;
}

export interface AnalyticsDashboard {
  business_analytics: BusinessAnalytics;
  predictive_analytics: PredictiveAnalytics | null;
  comparative_analytics: ComparativeAnalytics | null;
  dashboard_summary: DashboardSummary;
  recommended_actions: RecommendedAction[];
}

// Enhanced Analytics API
export const enhancedAnalyticsAPI = {
  // Get analytics dashboard for a business idea
  getDashboard: async (businessIdeaId: number): Promise<AnalyticsDashboard> => {
    return apiRequest<AnalyticsDashboard>(`/incubator/business-ideas/${businessIdeaId}/analytics/dashboard/`);
  },

  // Get business analytics
  getBusinessAnalytics: async (businessIdeaId: number): Promise<BusinessAnalytics> => {
    return apiRequest<BusinessAnalytics>(`/incubator/business-ideas/${businessIdeaId}/analytics/`);
  },

  // Recalculate analytics
  recalculateAnalytics: async (analyticsId: number): Promise<BusinessAnalytics> => {
    return apiRequest<BusinessAnalytics>(`/incubator/analytics/${analyticsId}/recalculate/`, {
      method: 'POST'
    });
  },

  // Get predictive analytics
  getPredictiveAnalytics: async (analyticsId: number): Promise<PredictiveAnalytics> => {
    return apiRequest<PredictiveAnalytics>(`/incubator/analytics/${analyticsId}/predictive/`);
  },

  // Get comparative analytics
  getComparativeAnalytics: async (analyticsId: number): Promise<ComparativeAnalytics> => {
    return apiRequest<ComparativeAnalytics>(`/incubator/analytics/${analyticsId}/comparative/`);
  },

  // Get recommended actions
  getRecommendedActions: async (businessIdeaId: number): Promise<RecommendedAction[]> => {
    return apiRequest<RecommendedAction[]>(`/incubator/business-ideas/${businessIdeaId}/recommended-actions/`);
  },

  // Create recommended action
  createRecommendedAction: async (businessIdeaId: number, actionData: Partial<RecommendedAction>): Promise<RecommendedAction> => {
    return apiRequest<RecommendedAction>(`/incubator/business-ideas/${businessIdeaId}/recommended-actions/`, {
      method: 'POST',
      body: JSON.stringify(actionData)
    });
  },

  // Update recommended action
  updateRecommendedAction: async (actionId: number, actionData: Partial<RecommendedAction>): Promise<RecommendedAction> => {
    return apiRequest<RecommendedAction>(`/incubator/recommended-actions/${actionId}/`, {
      method: 'PATCH',
      body: JSON.stringify(actionData)
    });
  },

  // Complete recommended action
  completeRecommendedAction: async (actionId: number): Promise<RecommendedAction> => {
    return apiRequest<RecommendedAction>(`/incubator/recommended-actions/${actionId}/complete/`, {
      method: 'POST'
    });
  },

  // Delete recommended action
  deleteRecommendedAction: async (actionId: number): Promise<void> => {
    return apiRequest<void>(`/incubator/recommended-actions/${actionId}/`, {
      method: 'DELETE'
    });
  }
};
