import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Save, Loader2 } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import AuthenticatedLayout from '../../components/layout/AuthenticatedLayout';
import { Post, postsAPI } from '../../services/api';
import { useAppSelector } from '../../store/hooks';
import { RTLText, RTLFlex } from '../../components/rtl';
import { RichTextEditor } from '../../components/common';

const PostEditPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { language, isRTL } = useLanguage();
  const { user } = useAppSelector(state => state.auth);

  const [post, setPost] = useState<Post | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const [formData, setFormData] = useState({
    title: '',
    content: '',
  });

  useEffect(() => {
    if (id) {
      fetchPost();
    }
  }, [id]);

  const fetchPost = async () => {
    if (!id) return;
    
    setLoading(true);
    try {
      const postData = await postsAPI.getPost(parseInt(id));
      
      // Check if user owns this post
      if (postData.author.id !== user?.id && !user?.is_staff && !user?.is_superuser) {
        setError(t('posts.errors.notOwner', 'You can only edit your own posts'));
        return;
      }

      setPost(postData);
      setFormData({
        title: postData.title,
        content: postData.content,
      });
    } catch (error) {
      console.error('Error fetching post:', error);
      setError(t('posts.errors.failedToLoad', 'Failed to load post'));
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | string, fieldName?: string) => {
    if (typeof e === 'string' && fieldName) {
      // Handle rich text editor
      setFormData(prev => ({
        ...prev,
        [fieldName]: e
      }));
    } else if (typeof e !== 'string') {
      // Handle regular inputs
      const { name, value } = e.target;
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!id || !post) return;

    setSaving(true);
    setError(null);

    try {
      await postsAPI.updatePost(parseInt(id), formData);
      setSuccess(t('posts.success.updated', 'Post updated successfully'));
      
      // Navigate back after success
      setTimeout(() => {
        navigate('/forum');
      }, 1500);
    } catch (error) {
      console.error('Error updating post:', error);
      setError(t('posts.errors.failedToUpdate', 'Failed to update post'));
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <AuthenticatedLayout>
      <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="flex justify-center items-center py-12">
          <div className="flex items-center space-x-3">
            <Loader2 className="w-6 h-6 animate-spin" />
            <span>{t('common.loading', 'Loading...')}</span>
          </div>
        </div>
                </div>
        </div>
      </div>
    </AuthenticatedLayout>
    );
  }

  if (error && !post) {
    return (
      <AuthenticatedLayout>
        <div className="text-center py-12">
          <div className="text-red-400 mb-4">{error}</div>
          <button
            onClick={() => navigate('/forum')}
            className="px-4 py-2 bg-purple-600 rounded-lg hover:bg-purple-700 transition-colors"
          >
            {t('common.goBack', 'Go Back')}
          </button>
        </div>
      </AuthenticatedLayout>
    );
  }

  return (
    <AuthenticatedLayout>
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className={`flex items-center justify-between mb-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`flex items-center space-x-4 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
            <button
              onClick={() => navigate('/forum')}
              className="p-2 rounded-lg hover:bg-indigo-800/50 transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <div>
              <RTLText as="h1" className="text-2xl font-bold">
                {t('posts.editPost', 'Edit Post')}
              </RTLText>
              <RTLText as="div" className="text-gray-400 mt-1">
                {t('posts.updateYourPost', 'Update your post content')}
              </RTLText>
            </div>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSave} className="space-y-6">
          <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
            {/* Title */}
            <div className="mb-6">
              <label className="block text-gray-300 mb-2 font-medium">
                {t('posts.title', 'Title')} *
              </label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                className="w-full px-4 py-3 bg-indigo-900/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:border-purple-500 transition-colors"
                placeholder={t('posts.enterTitle', 'Enter your post title')}
                required
                dir={language === 'ar' ? 'rtl' : 'ltr'}
              />
            </div>

            {/* Content */}
            <div className="mb-6">
              <label className="block text-gray-300 mb-2 font-medium">
                {t('posts.content', 'Content')} *
              </label>
              <div className="bg-white rounded-lg">
                <RichTextEditor
                  value={formData.content}
                  onChange={(value) => handleInputChange(value, 'content')}
                  placeholder={t('posts.enterContent', 'Write your post content here...')}
                />
              </div>
            </div>
          </div>

          {/* Error/Success Messages */}
          {error && (
            <div className="p-4 bg-red-900/50 border border-red-800 rounded-lg text-red-200">
              {error}
            </div>
          )}

          {success && (
            <div className="p-4 bg-green-900/50 border border-green-800 rounded-lg text-green-200">
              {success}
            </div>
          )}

          {/* Action Buttons */}
          <div className={`flex justify-end space-x-4 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
            <button
              type="button"
              onClick={() => navigate('/forum')}
              className="px-6 py-3 bg-gray-700 hover:bg-gray-600 rounded-lg font-medium transition-colors"
              disabled={saving}
            >
              {t('common.cancel', 'Cancel')}
            </button>
            <RTLFlex
              as="button"
              type="submit"
              className="px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={saving}
              align="center"
            >
              {saving ? (
                <>
                  <Loader2 className={`w-5 h-5 animate-spin ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                  {t('common.saving', 'Saving...')}
                </>
              ) : (
                <>
                  <Save className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                  {t('common.save', 'Save Changes')}
                </>
              )}
            </RTLFlex>
          </div>
        </form>
      </div>
    </AuthenticatedLayout>
  );
};

export default PostEditPage;
