import React from 'react';
import { Heart } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../store/hooks';
import { RTLText, RTLFlex, RTLIcon } from './common';
import { Link } from 'react-router-dom';

const Footer = () => {
  const { t } = useTranslation();
  const { language } = useAppSelector(state => state.language);
  return (
    <footer className="pt-16 pb-8 glass-light">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
          <div className="col-span-1 md:col-span-1">
            <RTLFlex className="items-center space-x-2 text-xl font-bold mb-4">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-blue-400">
                {t('app.name')}
              </span>
            </RTLFlex>
            <RTLText className="mb-4 text-glass-secondary">
              {t('footer.description')}
            </RTLText>
            <RTLFlex className="items-center text-glass-secondary">
              <RTLIcon icon={Heart} size={16} className="text-red-400" />
              <RTLText as="span">{t('footer.madeWithPassion')}</RTLText>
            </RTLFlex>
          </div>

          <div>
            <RTLText className="font-semibold text-lg mb-4 text-glass-primary">
              {t('footer.incubatorPrograms')}
            </RTLText>
            <ul className="space-y-2">
              <li>
                <Link to="/incubator" className="transition-colors text-glass-secondary hover:text-purple-600">
                  {t('footer.businessIdeas')}
                </Link>
              </li>
              <li>
                <Link to="/mentorship" className="transition-colors text-glass-secondary hover:text-purple-600">
                  {t('footer.mentorship')}
                </Link>
              </li>
              <li>
                <Link to="/funding" className="transition-colors text-glass-secondary hover:text-purple-600">
                  {t('footer.funding')}
                </Link>
              </li>
              <li>
                <Link to="/templates" className="transition-colors text-glass-secondary hover:text-purple-600">
                  {t('footer.templates')}
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <RTLText className="font-semibold text-lg mb-4 text-glass-primary">
              {t('footer.resources')}
            </RTLText>
            <ul className="space-y-2">
              <li>
                <Link to="/resources" className="transition-colors text-glass-secondary hover:text-purple-600">
                  {t('footer.learningPaths')}
                </Link>
              </li>
              <li>
                <Link to="/tools" className="transition-colors text-glass-secondary hover:text-purple-600">
                  {t('footer.businessTools')}
                </Link>
              </li>
              <li>
                <Link to="/guides" className="transition-colors text-glass-secondary hover:text-purple-600">
                  {t('footer.guides')}
                </Link>
              </li>
              <li>
                <Link to="/docs" className="transition-colors text-glass-secondary hover:text-purple-600">
                  {t('footer.documentation')}
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <RTLText className="font-semibold text-lg mb-4 text-glass-primary">
              {t('footer.community')}
            </RTLText>
            <ul className="space-y-2">
              <li>
                <Link to="/register" className="transition-colors text-glass-secondary hover:text-purple-600">
                  {t('footer.joinUs')}
                </Link>
              </li>
              <li>
                <Link to="/events" className="transition-colors text-glass-secondary hover:text-purple-600">
                  {t('footer.events')}
                </Link>
              </li>
              <li>
                <Link to="/forums" className="transition-colors text-glass-secondary hover:text-purple-600">
                  {t('footer.forums')}
                </Link>
              </li>
              <li>
                <Link to="/success-stories" className="transition-colors text-glass-secondary hover:text-purple-600">
                  {t('footer.successStories')}
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <RTLText className="font-semibold text-lg mb-4 text-glass-primary">
              {t('footer.support')}
            </RTLText>
            <ul className="space-y-2">
              <li>
                <Link to="/help" className="transition-colors text-glass-secondary hover:text-purple-600">
                  {t('footer.helpCenter')}
                </Link>
              </li>
              <li>
                <Link to="/contact" className="transition-colors text-glass-secondary hover:text-purple-600">
                  {t('footer.contact')}
                </Link>
              </li>
              <li>
                <Link to="/api-docs" className="transition-colors text-glass-secondary hover:text-purple-600">
                  {t('footer.apiDocs')}
                </Link>
              </li>
              <li>
                <Link to="/newsletter" className="transition-colors text-glass-secondary hover:text-purple-600">
                  {t('footer.newsletter')}
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <RTLText className="font-semibold text-lg mb-4 text-glass-primary">
              {t('footer.legal')}
            </RTLText>
            <ul className="space-y-2">
              <li>
                <a href="#" className="transition-colors text-glass-secondary hover:text-purple-600">
                  {t('footer.privacyPolicy')}
                </a>
              </li>
              <li>
                <a href="#" className="transition-colors text-glass-secondary hover:text-purple-600">
                  {t('footer.termsOfService')}
                </a>
              </li>
              <li>
                <a href="#" className="transition-colors text-glass-secondary hover:text-purple-600">
                  {t('footer.codeOfConduct')}
                </a>
              </li>
              <li>
                <a href="#" className="transition-colors text-glass-secondary hover:text-purple-600">
                  {t('footer.cookiePolicy')}
                </a>
              </li>
            </ul>
          </div>
        </div>

        <div className="pt-8 text-center border-t border-glass-border">
          <RTLText align="center" className="text-sm text-glass-secondary mb-2">
            &copy; {new Date().getFullYear()} {t('app.name')}. {t('footer.allRightsReserved')}
          </RTLText>
          <RTLFlex className="items-center justify-center text-glass-secondary">
            <RTLIcon icon={Heart} size={16} className="text-red-400" />
            <RTLText as="span" className="ml-2">{t('footer.madeWithLove')}</RTLText>
          </RTLFlex>
        </div>
      </div>
    </footer>
  );
};

export default Footer;