import React, { useState, useEffect } from 'react';
import { User, MessageSquare, Award, BookOpen, Clock, ThumbsUp, Star } from 'lucide-react';
import { mentorshipFeedbackAPI, MentorshipFeedback } from '../../services/incubatorApi';
import LoadingSpinner from '../common/LoadingSpinner';
import StarRating from '../common/StarRating';

import { useTranslation } from 'react-i18next';
interface MentorRatingsProps {
  mentorId: number;
}

interface MentorRatingsData {
  mentor: any;
  feedback_count: number;
  overall_rating: number;
  detailed_ratings: {
    knowledge: number;
    communication: number;
    helpfulness: number;
    preparation: number;
    responsiveness: number;
  };
  rating_counts: Record<string, number>;
  recent_feedback: MentorshipFeedback[];
}

const MentorRatings: React.FC<MentorRatingsProps> = ({ mentorId  }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [ratingsData, setRatingsData] = useState<MentorRatingsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'detailed' | 'feedback'>('overview');

  useEffect(() => {
    const fetchRatings = async () => {
      try {
        setLoading(true);
        const data = await mentorshipFeedbackAPI.getMentorRatings(mentorId);
        setRatingsData(data);
        setError(null);
      } catch (err) {
        console.error('Error fetching mentor ratings:', err);
        setError(t("common.failed.to.load", "Failed to load mentor ratings. Please try again later."));
      } finally {
        setLoading(false);
      }
    };

    fetchRatings();
  }, [mentorId]);

  const renderStars = (rating: number) => {
    return <StarRating rating={rating} size={16} showValue={true} />;
  };

  const renderRatingBar = (count: number, total: number) => {
    const percentage = total > 0 ? (count / total) * 100 : 0;

    return (
      <div className="w-full bg-indigo-900/50 rounded-full h-2">
        <div
          className="bg-yellow-400 h-2 rounded-full"
          style={{ width: `${percentage}%` }}
        ></div>
      </div>
    );
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return (
      <div className="bg-red-900/30 border border-red-800/50 rounded-lg p-4">
        <h3 className="text-lg font-medium mb-2 text-red-400">t("common.error", t("common.error", "Error"))</h3>
        <div>{error}</div>
      </div>
    );
  }

  if (!ratingsData) {
    return (
      <div className="bg-indigo-900/30 border border-indigo-800/50 rounded-lg p-4">
        <h3 className="text-lg font-medium mb-2">t("common.no.ratings", "No Ratings")</h3>
        <div>t("common.this.mentor.has", "This mentor has not received any ratings yet.")</div>
      </div>
    );
  }

  const {
    feedback_count,
    overall_rating,
    detailed_ratings,
    rating_counts,
    recent_feedback
  } = ratingsData;

  return (
    <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
      <h2 className="text-xl font-semibold mb-4">Mentor Ratings & Feedback</h2>

      <div className={`flex border-b border-indigo-800/50 mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
        <button
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === 'overview'
              ? 'border-b-2 border-purple-500 text-purple-400'
              : 'text-gray-400 hover:text-white'}
          }`}
          onClick={() => setActiveTab('overview')}
        >
          Overview
        </button>
        <button
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === 'detailed'
              ? 'border-b-2 border-purple-500 text-purple-400'
              : 'text-gray-400 hover:text-white'}
          }`}
          onClick={() => setActiveTab('detailed')}
        >
          Detailed Ratings
        </button>
        <button
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === 'feedback'
              ? 'border-b-2 border-purple-500 text-purple-400'
              : 'text-gray-400 hover:text-white'}
          }`}
          onClick={() => setActiveTab('feedback')}
        >
          Feedback
        </button>
      </div>

      {activeTab === 'overview' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className={`flex flex-col items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className="text-5xl font-bold text-yellow-400 mb-2">
              {overall_rating.toFixed(1)}
            </div>
            <div className={`flex mb-1 ${isRTL ? "flex-row-reverse" : ""}`}>
              {renderStars(overall_rating)}
            </div>
            <div className="text-sm text-gray-400">
              Based on {feedback_count} {feedback_count === 1 ? 'rating' : 'ratings'}
            </div>
          </div>

          <div>
            <h3 className="text-sm font-medium mb-4">t("common.rating.distribution", "Rating Distribution")</h3>

            {[5, 4, 3, 2, 1].map((rating) => (
              <div key={rating} className={`flex items-center mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                <div className={`flex items-center w-16 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <span className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`}>{rating}</span>
                  <Star size={12} className="text-yellow-400" />
                </div>
                <div className={`flex-1 mx-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                  {renderRatingBar(rating_counts[rating.toString()] || 0, feedback_count)}
                </div>
                <div className="w-10 text-right text-sm text-gray-400">
                  {rating_counts[rating.toString()] || 0}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {activeTab === 'detailed' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-indigo-800/20 rounded-lg p-4">
              <div className={`flex items-center mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                <BookOpen size={16} className={`text-purple-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                <h3 className="text-sm font-medium">Knowledge & Expertise</h3>
              </div>
              {renderStars(detailed_ratings.knowledge)}
              <div className="text-xs text-gray-400 mt-2">
                How well the mentor understands the subject matter and can provide valuable insights.
              </div>
            </div>

            <div className="bg-indigo-800/20 rounded-lg p-4">
              <div className={`flex items-center mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                <MessageSquare size={16} className={`text-purple-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                <h3 className="text-sm font-medium">t("common.communication.skills", "Communication Skills")</h3>
              </div>
              {renderStars(detailed_ratings.communication)}
              <div className="text-xs text-gray-400 mt-2">
                How clearly and effectively the mentor communicates ideas and feedback.
              </div>
            </div>

            <div className="bg-indigo-800/20 rounded-lg p-4">
              <div className={`flex items-center mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                <ThumbsUp size={16} className={`text-purple-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                <h3 className="text-sm font-medium">t("common.helpfulness", "Helpfulness")</h3>
              </div>
              {renderStars(detailed_ratings.helpfulness)}
              <div className="text-xs text-gray-400 mt-2">
                How useful and practical the mentor's guidance and advice is.
              </div>
            </div>

            <div className="bg-indigo-800/20 rounded-lg p-4">
              <div className={`flex items-center mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                <Award size={16} className={`text-purple-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                <h3 className="text-sm font-medium">Preparation & Organization</h3>
              </div>
              {renderStars(detailed_ratings.preparation)}
              <div className="text-xs text-gray-400 mt-2">
                How well-prepared and organized the mentor is for sessions.
              </div>
            </div>

            <div className="bg-indigo-800/20 rounded-lg p-4 md:col-span-2">
              <div className={`flex items-center mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                <Clock size={16} className={`text-purple-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                <h3 className="text-sm font-medium">t("common.responsiveness", "Responsiveness")</h3>
              </div>
              {renderStars(detailed_ratings.responsiveness)}
              <div className="text-xs text-gray-400 mt-2">
                How quickly and thoroughly the mentor responds to questions and requests.
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'feedback' && (
        <div className="space-y-4">
          {recent_feedback.length > 0 ? (
            recent_feedback.map((feedback) => (
              <div key={feedback.id} className="bg-indigo-800/20 rounded-lg p-4">
                <div className={`flex items-center justify-between mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    <User size={16} className={`text-purple-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                    <span className="text-sm font-medium">
                      {feedback.provided_by.first_name} {feedback.provided_by.last_name}
                    </span>
                  </div>
                  <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    {renderStars(feedback.rating)}
                  </div>
                </div>

                <div className="text-sm text-gray-300 mb-2">{feedback.comments}</div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  {feedback.highlights && (
                    <div>
                      <h4 className="text-xs font-medium text-green-400 mb-1">t("common.highlights", "Highlights")</h4>
                      <div className="text-xs text-gray-400">{feedback.highlights}</div>
                    </div>
                  )}

                  {feedback.areas_of_improvement && (
                    <div>
                      <h4 className="text-xs font-medium text-yellow-400 mb-1">t("common.areas.for.improvement", "Areas for Improvement")</h4>
                      <div className="text-xs text-gray-400">{feedback.areas_of_improvement}</div>
                    </div>
                  )}
                </div>

                <div className="text-xs text-gray-500 mt-2">
                  {new Date(feedback.created_at).toLocaleDateString()}
                </div>
              </div>
            ) : (
            <div className="text-center py-6">
              <p className="text-gray-400">t("common.no.feedback.available", "No feedback available yet.")</div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default MentorRatings;
