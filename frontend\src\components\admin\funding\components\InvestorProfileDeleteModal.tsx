import React, { useState } from 'react';
import { Trash2, Alert<PERSON>riangle, Building } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../hooks/useLanguage';
import { useDeleteInvestorProfile } from '../../../../hooks/useFunding';
import { InvestorProfile } from '../../../../services/incubatorApi';
import { Button } from '../../../ui';

interface InvestorProfileDeleteModalProps {
  isOpen: boolean;
  onClose: () => void;
  profile: InvestorProfile | null;
}

const InvestorProfileDeleteModal: React.FC<InvestorProfileDeleteModalProps> = ({
  isOpen,
  onClose,
  profile
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  const deleteProfile = useDeleteInvestorProfile();
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    if (!profile) return;

    setIsDeleting(true);
    try {
      await deleteProfile.mutateAsync(profile.id);
      onClose();
    } catch (error) {
      console.error('Failed to delete investor profile:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  if (!isOpen || !profile) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-indigo-900/90 rounded-xl shadow-lg w-full max-w-md">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center gap-3 mb-4">
            <div className="w-12 h-12 rounded-full bg-red-500/20 flex items-center justify-center">
              <AlertTriangle className="w-6 h-6 text-red-400" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">
                {t('investor.deleteProfile', 'Delete Investor Profile')}
              </h2>
              <p className="text-gray-400 text-sm">
                {t('common.actionCannotBeUndone', 'This action cannot be undone')}
              </p>
            </div>
          </div>

          {/* Content */}
          <div className="mb-6">
            <p className="text-gray-300 mb-4">
              {t('investor.deleteConfirmation', 'Are you sure you want to delete this investor profile?')}
            </p>
            
            <div className="bg-indigo-800/30 rounded-lg p-4 border border-indigo-700/50">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-green-700 flex items-center justify-center">
                  <Building className="w-5 h-5 text-green-300" />
                </div>
                <div>
                  <div className="text-white font-medium">
                    {profile.user.first_name} {profile.user.last_name}
                  </div>
                  <div className="text-gray-400 text-sm">
                    {profile.title} at {profile.organization}
                  </div>
                  <div className="text-gray-500 text-xs">
                    Investment Range: ${profile.min_investment?.toLocaleString()} - ${profile.max_investment?.toLocaleString()}
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-4 p-3 bg-red-900/20 border border-red-800/50 rounded-lg">
              <p className="text-red-300 text-sm">
                <strong>{t('common.warning', 'Warning')}:</strong> {t('investor.deleteWarning', 'Deleting this investor profile will also remove all associated investments and funding applications.')}
              </p>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3">
            <Button
              type="button"
              onClick={onClose}
              variant="secondary"
              disabled={isDeleting}
            >
              {t('common.cancel', 'Cancel')}
            </Button>
            <Button
              type="button"
              onClick={handleDelete}
              variant="danger"
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  {t('common.deleting', 'Deleting...')}
                </>
              ) : (
                <>
                  <Trash2 className="w-4 h-4 mr-2" />
                  {t('common.delete', 'Delete')}
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InvestorProfileDeleteModal;
