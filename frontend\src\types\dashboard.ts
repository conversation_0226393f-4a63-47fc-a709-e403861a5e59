/**
 * Unified Dashboard Types and Interfaces
 * Defines the architecture for consolidated dashboard components
 */

import { ReactNode } from 'react';
import { LucideIcon } from 'lucide-react';
import { User } from '../store/authSlice';

// Dashboard user roles
export type DashboardRole = 'user' | 'admin' | 'super_admin' | 'mentor' | 'investor' | 'moderator';

// Dashboard section types
export type DashboardSectionType = 
  | 'welcome' 
  | 'stats' 
  | 'quick_actions' 
  | 'analytics' 
  | 'activity' 
  | 'notifications'
  | 'system_health'
  | 'recent_items'
  | 'recommendations';

// Base stat item interface
export interface DashboardStat {
  id: string;
  title: string;
  value: number | string;
  icon: LucideIcon;
  color: string;
  change?: number;
  changeType?: 'increase' | 'decrease' | 'neutral';
  suffix?: string;
  description?: string;
}

// Quick action item interface
export interface DashboardQuickAction {
  id: string;
  title: string;
  description: string;
  icon: LucideIcon;
  href?: string;
  onClick?: () => void;
  color: string;
  badge?: number;
  disabled?: boolean;
}

// Dashboard section configuration
export interface DashboardSection {
  id: string;
  type: DashboardSectionType;
  title: string;
  component: ReactNode;
  order: number;
  visible: boolean;
  colSpan?: number; // For grid layout (1-12)
  rowSpan?: number; // For grid layout
  className?: string;
}

// Role-specific dashboard configuration
export interface DashboardConfig {
  role: DashboardRole;
  title: string;
  subtitle: string;
  backgroundGradient: string;
  sections: DashboardSection[];
  stats: DashboardStat[];
  quickActions: DashboardQuickAction[];
  refreshInterval?: number; // in milliseconds
  showSystemStatus?: boolean;
  showNotifications?: boolean;
}

// Dashboard data providers interface
export interface DashboardDataProvider {
  role: DashboardRole;
  fetchStats: () => Promise<DashboardStat[]>;
  fetchQuickActions: () => Promise<DashboardQuickAction[]>;
  fetchSectionData: (sectionType: DashboardSectionType) => Promise<any>;
  refreshData: () => Promise<void>;
}

// Welcome section props
export interface WelcomeSectionProps {
  user: User | null;
  role: DashboardRole;
  title?: string;
  subtitle?: string;
  showSystemStatus?: boolean;
  additionalStats?: DashboardStat[];
}

// Stats section props
export interface StatsSectionProps {
  stats: DashboardStat[];
  loading?: boolean;
  role: DashboardRole;
  gridCols?: number;
}

// Quick actions props
export interface QuickActionsSectionProps {
  actions: DashboardQuickAction[];
  role: DashboardRole;
  maxActions?: number;
}

// Analytics section props
export interface AnalyticsSectionProps {
  role: DashboardRole;
  timeRange?: string;
  showEnhanced?: boolean;
  onToggleView?: () => void;
}

// Activity section props
export interface ActivitySectionProps {
  role: DashboardRole;
  maxItems?: number;
  showFilters?: boolean;
}

// Unified dashboard props
export interface UnifiedDashboardProps {
  role?: DashboardRole; // If not provided, will be determined from user
  user?: User | null;
  config?: Partial<DashboardConfig>;
  className?: string;
  onSectionUpdate?: (sectionId: string, data: any) => void;
}

// Dashboard theme configuration
export interface DashboardTheme {
  role: DashboardRole;
  primaryColor: string;
  secondaryColor: string;
  backgroundGradient: string;
  cardBackground: string;
  textPrimary: string;
  textSecondary: string;
  borderColor: string;
  accentColor: string;
}

// Dashboard layout configuration
export interface DashboardLayoutConfig {
  role: DashboardRole;
  gridTemplate: string; // CSS grid template
  gap: string;
  padding: string;
  maxWidth: string;
  showSidebar: boolean;
  sidebarWidth: string;
}

// Real-time data update interface
export interface DashboardRealTimeData {
  sectionId: string;
  data: any;
  timestamp: number;
  source: string;
}

// Dashboard error handling
export interface DashboardError {
  sectionId?: string;
  message: string;
  type: 'warning' | 'error' | 'info';
  recoverable: boolean;
  retryAction?: () => void;
}

// Dashboard loading states
export interface DashboardLoadingState {
  global: boolean;
  sections: Record<string, boolean>;
  stats: boolean;
  quickActions: boolean;
}

// Dashboard context interface
export interface DashboardContextValue {
  role: DashboardRole;
  config: DashboardConfig;
  data: {
    stats: DashboardStat[];
    quickActions: DashboardQuickAction[];
    sectionData: Record<string, any>;
  };
  loading: DashboardLoadingState;
  errors: DashboardError[];
  theme: DashboardTheme;
  refreshData: () => Promise<void>;
  updateSection: (sectionId: string, data: any) => void;
  clearError: (errorId?: string) => void;
}

// Export default dashboard configurations for each role
export const DEFAULT_DASHBOARD_CONFIGS: Record<DashboardRole, Partial<DashboardConfig>> = {
  user: {
    title: 'Dashboard',
    subtitle: 'Welcome to your personal dashboard',
    backgroundGradient: 'from-indigo-950 to-purple-950',
    refreshInterval: 300000, // 5 minutes
    showSystemStatus: false,
    showNotifications: true,
  },
  admin: {
    title: 'Admin Dashboard',
    subtitle: 'Platform administration and management',
    backgroundGradient: 'from-gray-900 via-purple-900 to-violet-900',
    refreshInterval: 60000, // 1 minute
    showSystemStatus: true,
    showNotifications: true,
  },
  super_admin: {
    title: 'Super Admin Dashboard',
    subtitle: 'Complete system control and monitoring',
    backgroundGradient: 'from-gray-900 via-red-900 to-purple-900',
    refreshInterval: 30000, // 30 seconds
    showSystemStatus: true,
    showNotifications: true,
  },
  mentor: {
    title: 'Mentor Dashboard',
    subtitle: 'Guide and support your mentees',
    backgroundGradient: 'from-blue-950 to-indigo-950',
    refreshInterval: 300000, // 5 minutes
    showSystemStatus: false,
    showNotifications: true,
  },
  investor: {
    title: 'Investor Dashboard',
    subtitle: 'Track investments and opportunities',
    backgroundGradient: 'from-green-950 to-emerald-950',
    refreshInterval: 300000, // 5 minutes
    showSystemStatus: false,
    showNotifications: true,
  },
  moderator: {
    title: 'Moderator Dashboard',
    subtitle: 'Monitor and manage community content',
    backgroundGradient: 'from-blue-50 via-white to-purple-50',
    refreshInterval: 120000, // 2 minutes
    showSystemStatus: false,
    showNotifications: true,
  },
};

// Dashboard themes for each role
export const DASHBOARD_THEMES: Record<DashboardRole, DashboardTheme> = {
  user: {
    role: 'user',
    primaryColor: 'text-white',
    secondaryColor: 'text-gray-300',
    backgroundGradient: 'from-indigo-950 to-purple-950',
    cardBackground: 'bg-white/10 backdrop-blur-sm border-white/20',
    textPrimary: 'text-white',
    textSecondary: 'text-gray-300',
    borderColor: 'border-indigo-500/30',
    accentColor: 'text-indigo-400',
  },
  admin: {
    role: 'admin',
    primaryColor: 'text-white',
    secondaryColor: 'text-gray-300',
    backgroundGradient: 'from-gray-900 via-purple-900 to-violet-900',
    cardBackground: 'bg-black/30 backdrop-blur-sm border-purple-500',
    textPrimary: 'text-white',
    textSecondary: 'text-gray-300',
    borderColor: 'border-purple-500/30',
    accentColor: 'text-purple-400',
  },
  super_admin: {
    role: 'super_admin',
    primaryColor: 'text-white',
    secondaryColor: 'text-gray-300',
    backgroundGradient: 'from-gray-900 via-red-900 to-purple-900',
    cardBackground: 'bg-black/30 backdrop-blur-sm border-red-500',
    textPrimary: 'text-white',
    textSecondary: 'text-gray-300',
    borderColor: 'border-red-500/30',
    accentColor: 'text-red-400',
  },
  mentor: {
    role: 'mentor',
    primaryColor: 'text-white',
    secondaryColor: 'text-blue-200',
    backgroundGradient: 'from-blue-950 to-indigo-950',
    cardBackground: 'bg-white/10 backdrop-blur-sm border-blue-500/20',
    textPrimary: 'text-white',
    textSecondary: 'text-blue-200',
    borderColor: 'border-blue-500/30',
    accentColor: 'text-blue-400',
  },
  investor: {
    role: 'investor',
    primaryColor: 'text-white',
    secondaryColor: 'text-green-200',
    backgroundGradient: 'from-green-950 to-emerald-950',
    cardBackground: 'bg-white/10 backdrop-blur-sm border-green-500/20',
    textPrimary: 'text-white',
    textSecondary: 'text-green-200',
    borderColor: 'border-green-500/30',
    accentColor: 'text-green-400',
  },
  moderator: {
    role: 'moderator',
    primaryColor: 'text-gray-900',
    secondaryColor: 'text-gray-600',
    backgroundGradient: 'from-blue-50 via-white to-purple-50',
    cardBackground: 'bg-white/70 backdrop-blur-md border-white/20',
    textPrimary: 'text-gray-900',
    textSecondary: 'text-gray-600',
    borderColor: 'border-blue-200',
    accentColor: 'text-blue-600',
  },
};
