import { apiRequest } from './api';

export interface CompetitiveAnalysis {
  business_idea_id: number;
  business_idea_title: string;
  analysis?: any;
  competitive_advantages?: any;
  competitive_disadvantages?: any;
  last_updated?: string;
}

export interface MarketTrendsAnalysis {
  business_idea_id: number;
  business_idea_title: string;
  market_trends: any;
}

export const competitiveAnalysisAPI = {
  /**
   * Get competitive analysis for a business idea
   * @param businessIdeaId - ID of the business idea
   * @returns Competitive analysis data
   */
  getCompetitiveAnalysis: async (businessIdeaId: number) => {
    try {
      return await apiRequest<CompetitiveAnalysis>(
        `/ai/competitive-analysis/${businessIdeaId}/`
      );
    } catch (error) {
      console.error(`Error getting competitive analysis for business idea ${businessIdeaId}:`, error);
      throw error;
    }
  },

  /**
   * Generate a new competitive analysis for a business idea
   * @param businessIdeaId - ID of the business idea
   * @returns Competitive analysis data
   */
  generateCompetitiveAnalysis: async (businessIdeaId: number) => {
    try {
      return await apiRequest<CompetitiveAnalysis>(
        `/ai/competitive-analysis/${businessIdeaId}/`,
        'POST'
      );
    } catch (error) {
      console.error(`Error generating competitive analysis for business idea ${businessIdeaId}:`, error);
      throw error;
    }
  },

  /**
   * Get market trends analysis for a business idea
   * @param businessIdeaId - ID of the business idea
   * @returns Market trends analysis data
   */
  getMarketTrendsAnalysis: async (businessIdeaId: number) => {
    try {
      return await apiRequest<MarketTrendsAnalysis>(
        `/ai/market-trends/${businessIdeaId}/`
      );
    } catch (error) {
      console.error(`Error getting market trends analysis for business idea ${businessIdeaId}:`, error);
      throw error;
    }
  },

  /**
   * Generate a new market trends analysis for a business idea
   * @param businessIdeaId - ID of the business idea
   * @returns Market trends analysis data
   */
  generateMarketTrendsAnalysis: async (businessIdeaId: number) => {
    try {
      return await apiRequest<MarketTrendsAnalysis>(
        `/ai/market-trends/${businessIdeaId}/`,
        'POST'
      );
    } catch (error) {
      console.error(`Error generating market trends analysis for business idea ${businessIdeaId}:`, error);
      throw error;
    }
  }
};
