/**
 * Advanced Market Intelligence Dashboard
 * Real-time market analysis with AI-powered insights and competitive intelligence
 */

import React, { useState, useEffect } from 'react';
import {
  TrendingUp,
  TrendingDown,
  BarChart3,
  PieChart,
  Globe,
  Target,
  DollarSign,
  Users,
  Zap,
  AlertTriangle,
  CheckCircle,
  Info,
  RefreshCw,
  Filter,
  Download,
  Share2,
  Eye,
  Calendar,
  MapPin,
  Briefcase,
  Award,
  Activity,
  ArrowUpRight,
  ArrowDownRight,
  Minus
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { RTLText, RTLFlex } from '../common';

interface MarketData {
  marketSize: {
    current: number;
    projected: number;
    growth: number;
    currency: string;
  };
  segments: Array<{
    name: string;
    size: number;
    growth: number;
    share: number;
  }>;
  trends: Array<{
    id: string;
    title: string;
    description: string;
    impact: 'high' | 'medium' | 'low';
    direction: 'up' | 'down' | 'stable';
    confidence: number;
  }>;
  competitors: Array<{
    id: string;
    name: string;
    marketShare: number;
    revenue: number;
    growth: number;
    strengths: string[];
    weaknesses: string[];
    threat: 'high' | 'medium' | 'low';
  }>;
  opportunities: Array<{
    id: string;
    title: string;
    description: string;
    potential: number;
    timeframe: string;
    difficulty: 'easy' | 'medium' | 'hard';
    requirements: string[];
  }>;
  risks: Array<{
    id: string;
    title: string;
    description: string;
    probability: number;
    impact: number;
    mitigation: string[];
  }>;
}

interface RegionalData {
  region: string;
  marketSize: number;
  growth: number;
  penetration: number;
  competition: 'low' | 'medium' | 'high';
  regulations: 'favorable' | 'neutral' | 'challenging';
  opportunities: number;
}

export const MarketIntelligenceDashboard: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [activeTab, setActiveTab] = useState<'overview' | 'competitors' | 'trends' | 'opportunities' | 'regional'>('overview');
  const [marketData, setMarketData] = useState<MarketData | null>(null);
  const [regionalData, setRegionalData] = useState<RegionalData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const [selectedIndustry, setSelectedIndustry] = useState('technology');
  const [selectedRegion, setSelectedRegion] = useState('middle-east');

  // Mock data generation
  const generateMarketData = (): MarketData => {
    return {
      marketSize: {
        current: 45.2,
        projected: 78.9,
        growth: 12.5,
        currency: t("common.usd.billion", "USD Billion")
      },
      segments: [
        { name: t("dashboard.enterprise.solutions", "Enterprise Solutions"), size: 18.5, growth: 15.2, share: 41 },
        { name: t("dashboard.smb.solutions", "SMB Solutions"), size: 12.3, growth: 18.7, share: 27 },
        { name: t("dashboard.consumer.apps", "Consumer Apps"), size: 8.9, growth: 22.1, share: 20 },
        { name: t("dashboard.government", "Government"), size: 5.5, growth: 8.3, share: 12 }
      ],
      trends: [
        {
          id: '1',
          title: 'AI Integration Acceleration',
          description: 'Rapid adoption of AI technologies across all business sectors',
          impact: 'high',
          direction: 'up',
          confidence: 92
        },
        {
          id: '2',
          title: 'Remote Work Normalization',
          description: 'Permanent shift to hybrid and remote work models',
          impact: 'high',
          direction: 'up',
          confidence: 88
        },
        {
          id: '3',
          title: 'Sustainability Focus',
          description: 'Increased emphasis on environmental and social responsibility',
          impact: 'medium',
          direction: 'up',
          confidence: 85
        },
        {
          id: '4',
          title: 'Traditional Retail Decline',
          description: 'Continued shift from physical to digital commerce',
          impact: 'medium',
          direction: 'down',
          confidence: 79
        }
      ],
      competitors: [
        {
          id: '1',
          name: t("dashboard.techcorp.solutions", "TechCorp Solutions"),
          marketShare: 23.5,
          revenue: 10.6,
          growth: 8.2,
          strengths: ['Strong brand', 'Large customer base', 'Global presence'],
          weaknesses: ['High prices', 'Slow innovation', 'Complex products'],
          threat: 'high'
        },
        {
          id: '2',
          name: t("dashboard.innovatetech", "InnovateTech"),
          marketShare: 18.2,
          revenue: 8.2,
          growth: 15.7,
          strengths: ['Innovative products', 'Agile development', 'Strong R&D'],
          weaknesses: ['Limited market reach', 'Higher costs', 'New brand'],
          threat: 'medium'
        },
        {
          id: '3',
          name: t("dashboard.globalsoft", "GlobalSoft"),
          marketShare: 15.8,
          revenue: 7.1,
          growth: 12.3,
          strengths: ['Cost-effective', 'Good support', 'Established partnerships'],
          weaknesses: ['Limited features', 'Outdated UI', 'Slow updates'],
          threat: 'low'
        }
      ],
      opportunities: [
        {
          id: '1',
          title: t("dashboard.emerging.markets.expansion", "Emerging Markets Expansion"),
          description: 'Untapped potential in Southeast Asia and Africa',
          potential: 85,
          timeframe: '12-18 months',
          difficulty: 'medium',
          requirements: ['Local partnerships', 'Regulatory compliance', 'Cultural adaptation']
        },
        {
          id: '2',
          title: t("dashboard.aipowered.features.description", "AI-Powered Features"),
          description: 'Integration of machine learning capabilities',
          potential: 92,
          timeframe: '6-12 months',
          difficulty: 'hard',
          requirements: ['AI expertise', 'Data infrastructure', 'Algorithm development']
        },
        {
          id: '3',
          title: t("dashboard.smb.market.penetration", "SMB Market Penetration"),
          description: 'Simplified solutions for small and medium businesses',
          potential: 78,
          timeframe: '3-6 months',
          difficulty: 'easy',
          requirements: ['Product simplification', 'Pricing strategy', 'Marketing campaign']
        }
      ],
      risks: [
        {
          id: '1',
          title: t("dashboard.economic.recession.description", "Economic Recession"),
          description: 'Potential economic downturn affecting business spending',
          probability: 35,
          impact: 85,
          mitigation: ['Diversify revenue streams', 'Build cash reserves', 'Focus on essential services']
        },
        {
          id: '2',
          title: t("dashboard.regulatory.changes.description", "Regulatory Changes"),
          description: 'New data privacy and security regulations',
          probability: 65,
          impact: 60,
          mitigation: ['Compliance team', 'Regular audits', 'Privacy-by-design']
        },
        {
          id: '3',
          title: 'Technology Disruption',
          description: 'Emergence of disruptive technologies',
          probability: 45,
          impact: 75,
          mitigation: ['Innovation investment', 'Technology partnerships', 'Agile development']
        }
      ]
    };
  };

  const generateRegionalData = (): RegionalData[] => {
    return [
      {
        region: t("dashboard.middle.east", "Middle East"),
        marketSize: 12.5,
        growth: 18.2,
        penetration: 34,
        competition: 'medium',
        regulations: 'favorable',
        opportunities: 8
      },
      {
        region: t("dashboard.north.africa", "North Africa"),
        marketSize: 8.3,
        growth: 22.1,
        penetration: 28,
        competition: 'low',
        regulations: 'neutral',
        opportunities: 12
      },
      {
        region: t("dashboard.southeast.asia", "Southeast Asia"),
        marketSize: 15.7,
        growth: 25.4,
        penetration: 42,
        competition: 'high',
        regulations: 'challenging',
        opportunities: 6
      },
      {
        region: t("dashboard.europe", "Europe"),
        marketSize: 28.9,
        growth: 8.7,
        penetration: 67,
        competition: 'high',
        regulations: 'challenging',
        opportunities: 4
      },
      {
        region: t("dashboard.north.america", "North America"),
        marketSize: 35.2,
        growth: 6.3,
        penetration: 78,
        competition: 'high',
        regulations: 'neutral',
        opportunities: 3
      }
    ];
  };

  // Load data
  useEffect(() => {
    setIsLoading(true);
    setTimeout(() => {
      setMarketData(generateMarketData());
      setRegionalData(generateRegionalData());
      setLastUpdated(new Date());
      setIsLoading(false);
    }, 1500);
  }, [selectedIndustry, selectedRegion]);

  const refreshData = () => {
    setIsLoading(true);
    setTimeout(() => {
      setMarketData(generateMarketData());
      setRegionalData(generateRegionalData());
      setLastUpdated(new Date());
      setIsLoading(false);
    }, 1000);
  };

  const getTrendIcon = (direction: string) => {
    switch (direction) {
      case 'up': return <TrendingUp className="text-green-400" size={16} />;
      case 'down': return <TrendingDown className="text-red-400" size={16} />;
      case 'stable': return <Minus className="text-gray-400" size={16} />;
      default: return <Minus className="text-gray-400" size={16} />;
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'text-red-400';
      case 'medium': return 'text-yellow-400';
      case 'low': return 'text-green-400';
      default: return 'text-gray-400';
    }
  };

  const getThreatColor = (threat: string) => {
    switch (threat) {
      case 'high': return 'bg-red-500/20 text-red-400';
      case 'medium': return 'bg-yellow-500/20 text-yellow-400';
      case 'low': return 'bg-green-500/20 text-green-400';
      default: return 'bg-gray-500/20 text-gray-400';
    }
  };

  const getCompetitionColor = (level: string) => {
    switch (level) {
      case 'high': return 'text-red-400';
      case 'medium': return 'text-yellow-400';
      case 'low': return 'text-green-400';
      default: return 'text-gray-400';
    }
  };

  const getRegulationColor = (status: string) => {
    switch (status) {
      case 'favorable': return 'text-green-400';
      case 'neutral': return 'text-yellow-400';
      case 'challenging': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  if (!marketData) {
    return (
      <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-8">
        <div className="text-center">
          <RefreshCw className="animate-spin mx-auto mb-4 text-purple-400" size={32} />
          <p className="text-gray-300">t("common.loading.market.intelligence", "Loading market intelligence...")</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6">
        <RTLFlex className="items-center justify-between mb-4">
          <RTLFlex className="items-center">
            <BarChart3 className="text-purple-400" size={28} />
            <RTLText className={`text-2xl font-bold text-white ${isRTL ? 'mr-3' : 'ml-3'}`}>
              Market Intelligence
            </RTLText>
          </RTLFlex>

          <RTLFlex className="items-center gap-4">
            <select
              value={selectedIndustry}
              onChange={(e) => setSelectedIndustry(e.target.value)}
              className="px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-purple-500"
            >
              <option value="technology">t("common.technology", "Technology")</option>
              <option value="healthcare">t("common.healthcare", "Healthcare")</option>
              <option value="finance">t("common.finance", "Finance")</option>
              <option value="education">t("common.education", "Education")</option>
              <option value="retail">t("common.retail", "Retail")</option>
            </select>

            <button
              onClick={refreshData}
              disabled={isLoading}
              className={`flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:opacity-50 text-white rounded-lg transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
            >
              <RefreshCw className={isLoading ? 'animate-spin' : ''} size={16} />
              <span className={isRTL ? 'mr-2' : 'ml-2'}>t("common.refresh", t("common.refresh", "Refresh"))</span>
            </button>
          </RTLFlex>
        </RTLFlex>

        <p className="text-gray-300 mb-4">
          Real-time market analysis and competitive intelligence for strategic decision making
        </p>

        <div className="text-sm text-gray-500">
          Last updated: {lastUpdated.toLocaleString()}
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-4">
          <RTLFlex className="items-center mb-2">
            <DollarSign className="text-green-400" size={20} />
            <RTLText className={`font-medium text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
              Market Size
            </RTLText>
          </RTLFlex>
          <div className="text-2xl font-bold text-white">
            ${marketData.marketSize.current}B
          </div>
          <div className="text-sm text-gray-400">
            Projected: ${marketData.marketSize.projected}B
          </div>
        </div>

        <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-4">
          <RTLFlex className="items-center mb-2">
            <TrendingUp className="text-blue-400" size={20} />
            <RTLText className={`font-medium text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
              Growth Rate
            </RTLText>
          </RTLFlex>
          <div className="text-2xl font-bold text-white">
            {marketData.marketSize.growth}%
          </div>
          <div className="text-sm text-green-400">
            Year over year
          </div>
        </div>

        <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-4">
          <RTLFlex className="items-center mb-2">
            <Users className="text-purple-400" size={20} />
            <RTLText className={`font-medium text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
              Competitors
            </RTLText>
          </RTLFlex>
          <div className="text-2xl font-bold text-white">
            {marketData.competitors.length}
          </div>
          <div className="text-sm text-gray-400">
            Major players
          </div>
        </div>

        <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-4">
          <RTLFlex className="items-center mb-2">
            <Target className="text-yellow-400" size={20} />
            <RTLText className={`font-medium text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
              Opportunities
            </RTLText>
          </RTLFlex>
          <div className="text-2xl font-bold text-white">
            {marketData.opportunities.length}
          </div>
          <div className="text-sm text-green-400">
            High potential
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-2">
        <div className={`flex gap-2 ${isRTL ? "flex-row-reverse" : ""}`}>
          {[
            { id: 'overview', label: t("dashboard.overview", "Overview"), icon: BarChart3 },
            { id: 'competitors', label: t("dashboard.competitors", "Competitors"), icon: Users },
            { id: 'trends', label: t("dashboard.trends", "Trends"), icon: TrendingUp },
            { id: 'opportunities', label: t("dashboard.opportunities", "Opportunities"), icon: Target },
            { id: 'regional', label: t("dashboard.regional", "Regional"), icon: Globe }
          ].map(tab => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-purple-600 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-gray-700/50'}
                }`}
              >
                <Icon size={16} />
                <span className={isRTL ? 'mr-2' : 'ml-2'}>{tab.label}</span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Market Segments */}
          <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6">
            <h3 className="text-lg font-semibold text-white mb-4">t("common.market.segments", "Market Segments")</h3>
            <div className="space-y-4">
              {marketData.segments.map((segment, index) => (
                <div key={index} className="space-y-2">
                  <RTLFlex className="items-center justify-between">
                    <span className="text-white font-medium">{segment.name}</span>
                    <span className="text-gray-400">{segment.share}%</span>
                  </RTLFlex>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-purple-500 h-2 rounded-full"
                      style={{ width: `${segment.share}%` }}
                    />
                  </div>
                  <RTLFlex className="items-center justify-between text-sm">
                    <span className="text-gray-400">${segment.size}B</span>
                    <span className="text-green-400">+{segment.growth}%</span>
                  </RTLFlex>
                </div>
              ))}
            </div>
          </div>

          {/* Key Trends */}
          <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6">
            <h3 className="text-lg font-semibold text-white mb-4">t("common.key.trends", "Key Trends")</h3>
            <div className="space-y-4">
              {marketData.trends.slice(0, 4).map(trend => (
                <div key={trend.id} className="border-l-4 border-purple-500 pl-4">
                  <RTLFlex className="items-center justify-between mb-1">
                    <RTLFlex className="items-center">
                      {getTrendIcon(trend.direction)}
                      <span className={`font-medium text-white ${isRTL ? 'mr-2' : 'ml-2'}`}>
                        {trend.title}
                      </span>
                    </RTLFlex>
                    <span className={`text-sm ${getImpactColor(trend.impact)}`}>
                      {trend.impact} impact
                    </span>
                  </RTLFlex>
                  <p className="text-sm text-gray-400 mb-2">{trend.description}</p>
                  <div className="text-xs text-gray-500">
                    Confidence: {trend.confidence}%
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'competitors' && (
        <div className="space-y-4">
          {marketData.competitors.map(competitor => (
            <div key={competitor.id} className="bg-gray-900/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6">
              <RTLFlex className="items-start justify-between mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">{competitor.name}</h3>
                  <RTLFlex className="items-center gap-4 text-sm">
                    <span className="text-gray-400">t("common.market.share", "Market Share: ")<span className="text-white">{competitor.marketShare}%</span></span>
                    <span className="text-gray-400">t("common.revenue", "Revenue: ")<span className="text-white">${competitor.revenue}B</span></span>
                    <span className="text-gray-400">t("common.growth", "Growth: ")<span className="text-green-400">+{competitor.growth}%</span></span>
                  </RTLFlex>
                </div>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getThreatColor(competitor.threat)}`}>
                  {competitor.threat} threat
                </span>
              </RTLFlex>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="text-sm font-medium text-green-400 mb-2">t("common.strengths", "Strengths")</h4>
                  <ul className="space-y-1">
                    {competitor.strengths.map((strength, idx) => (
                      <li key={idx} className={`text-sm text-gray-300 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                        <CheckCircle className={`text-green-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} size={12} />
                        {strength}
                      </li>
                    ))}
                  </ul>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-red-400 mb-2">t("common.weaknesses", "Weaknesses")</h4>
                  <ul className="space-y-1">
                    {competitor.weaknesses.map((weakness, idx) => (
                      <li key={idx} className={`text-sm text-gray-300 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                        <AlertTriangle className={`text-red-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} size={12} />
                        {weakness}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {activeTab === 'opportunities' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {marketData.opportunities.map(opportunity => (
            <div key={opportunity.id} className="bg-gray-900/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6">
              <RTLFlex className="items-start justify-between mb-4">
                <h3 className="text-lg font-semibold text-white">{opportunity.title}</h3>
                <span className="text-2xl font-bold text-green-400">{opportunity.potential}%</span>
              </RTLFlex>

              <p className="text-gray-300 mb-4">{opportunity.description}</p>

              <div className="space-y-3">
                <RTLFlex className="items-center justify-between text-sm">
                  <span className="text-gray-400">t("common.timeframe", "Timeframe:")</span>
                  <span className="text-white">{opportunity.timeframe}</span>
                </RTLFlex>
                <RTLFlex className="items-center justify-between text-sm">
                  <span className="text-gray-400">t("common.difficulty", "Difficulty:")</span>
                  <span className={`font-medium ${
                    opportunity.difficulty === 'easy' ? 'text-green-400' :
                    opportunity.difficulty === 'medium' ? 'text-yellow-400' : 'text-red-400'}
                  }`}>
                    {opportunity.difficulty}
                  </span>
                </RTLFlex>
              </div>

              <div className="mt-4">
                <h4 className="text-sm font-medium text-gray-300 mb-2">t("common.requirements", "Requirements:")</h4>
                <ul className="space-y-1">
                  {opportunity.requirements.map((req, idx) => (
                    <li key={idx} className={`text-sm text-gray-400 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                      <div className={`w-1 h-1 bg-purple-400 rounded-full mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                      {req}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>
      )}

      {activeTab === 'regional' && (
        <div className="space-y-4">
          {regionalData.map((region, index) => (
            <div key={index} className="bg-gray-900/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6">
              <RTLFlex className="items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-white">{region.region}</h3>
                <span className="text-sm text-gray-400">{region.opportunities} opportunities</span>
              </RTLFlex>

              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-white">${region.marketSize}B</div>
                  <div className="text-sm text-gray-400">t("common.market.size", "Market Size")</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">+{region.growth}%</div>
                  <div className="text-sm text-gray-400">t("common.growth", "Growth")</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-400">{region.penetration}%</div>
                  <div className="text-sm text-gray-400">t("common.penetration", "Penetration")</div>
                </div>
                <div className="text-center">
                  <div className={`text-2xl font-bold ${getCompetitionColor(region.competition)}`}>
                    {region.competition}
                  </div>
                  <div className="text-sm text-gray-400">t("common.competition", "Competition")</div>
                </div>
                <div className="text-center">
                  <div className={`text-2xl font-bold ${getRegulationColor(region.regulations)}`}>
                    {region.regulations}
                  </div>
                  <div className="text-sm text-gray-400">t("common.regulations", "Regulations")</div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default MarketIntelligenceDashboard;
