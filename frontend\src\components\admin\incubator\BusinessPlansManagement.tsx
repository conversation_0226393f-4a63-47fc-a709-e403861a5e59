import React, { useState } from 'react';
import { Search, FileText, Calendar, User, Target, DollarSign, TrendingUp, Download, Plus, Edit, Trash2, Eye, Filter, RefreshCw, <PERSON>, Copy } from 'lucide-react';

import { AdvancedFilter, BulkActions } from '../common';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';
import {
  useBusinessPlansList,
  useCreateBusinessPlan,
  useUpdateBusinessPlan,
  useDeleteBusinessPlan,
  useGenerateBusinessPlanFeedback,
  useDuplicateBusinessPlan
} from '../../../hooks/useBusinessPlans';
import { BusinessPlan } from '../../../services/businessPlanApi';
import { Alert, Button } from '../../ui';
import BusinessPlanModal from './components/BusinessPlanModal';
import BusinessPlanDeleteModal from './components/BusinessPlanDeleteModal';

interface FilterValue {
  status?: string;
  business_idea?: string;
  search?: string;
}

const BusinessPlansManagement: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  // State management
  const [selectedPlans, setSelectedPlans] = useState<number[]>([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<BusinessPlan | null>(null);
  const [filters, setFilters] = useState<FilterValue>({});
  const [searchTerm, setSearchTerm] = useState('');

  // API hooks
  const { data: plansData, isLoading, error, refetch } = useBusinessPlansList(filters);
  const createPlan = useCreateBusinessPlan();
  const updatePlan = useUpdateBusinessPlan();
  const deletePlan = useDeleteBusinessPlan();
  const generateFeedback = useGenerateBusinessPlanFeedback();
  const duplicatePlan = useDuplicateBusinessPlan();

  const plans = plansData?.results || [];

  // Filter options
  const filterOptions = [
    {
      key: 'status',
      label: t('admin.status', 'Status'),
      type: 'select' as const,
      options: [
        { value: 'draft', label: t('businessPlan.status.draft', 'Draft') },
        { value: 'in_progress', label: t('businessPlan.status.inProgress', 'In Progress') },
        { value: 'completed', label: t('businessPlan.status.completed', 'Completed') },
        { value: 'archived', label: t('businessPlan.status.archived', 'Archived') }
      ]
    },
    {
      key: 'search',
      label: t('common.search', 'Search'),
      type: 'text' as const,
      placeholder: t('businessPlan.searchPlaceholder', 'Search business plans...')
    }
  ];

  // Bulk actions
  const bulkActions = [
    {
      id: 'delete',
      label: t('common.delete', 'Delete'),
      icon: Trash2,
      variant: 'danger' as const,
      action: async (ids: number[]) => {
        for (const id of ids) {
          await deletePlan.mutateAsync(id);
        }
        setSelectedPlans([]);
      }
    },
    {
      id: 'generate-feedback',
      label: t('businessPlan.generateFeedback', 'Generate AI Feedback'),
      icon: Brain,
      variant: 'primary' as const,
      action: async (ids: number[]) => {
        for (const id of ids) {
          await generateFeedback.mutateAsync({ business_plan_id: id });
        }
        setSelectedPlans([]);
      }
    }
  ];

  // Event handlers
  const handleCreatePlan = () => {
    setSelectedPlan(null);
    setShowCreateModal(true);
  };

  const handleEditPlan = (plan: BusinessPlan) => {
    setSelectedPlan(plan);
    setShowEditModal(true);
  };

  const handleDeletePlan = (plan: BusinessPlan) => {
    setSelectedPlan(plan);
    setShowDeleteModal(true);
  };

  const handleDuplicatePlan = async (plan: BusinessPlan) => {
    try {
      await duplicatePlan.mutateAsync({ business_plan_id: plan.id });
    } catch (error) {
      console.error('Failed to duplicate plan:', error);
    }
  };

  const handleGenerateFeedback = async (plan: BusinessPlan) => {
    try {
      await generateFeedback.mutateAsync({ business_plan_id: plan.id });
    } catch (error) {
      console.error('Failed to generate feedback:', error);
    }
  };

  const handleFilterChange = (newFilters: FilterValue) => {
    setFilters(newFilters);
  };

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setFilters(prev => ({ ...prev, search: term }));
  };

  // Get plan status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft':
        return 'bg-yellow-500/20 text-yellow-300';
      case 'completed':
        return 'bg-green-500/20 text-green-300';
      case 'under_review':
        return 'bg-blue-500/20 text-blue-300';
      case 'approved':
        return 'bg-purple-500/20 text-purple-300';
      case 'needs_revision':
        return 'bg-orange-500/20 text-orange-300';
      default:
        return 'bg-gray-500/20 text-gray-300';
    }
  };

  // Get plan status label
  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'draft':
        return t('plan.status.draft', 'Draft');
      case 'completed':
        return t('plan.status.completed', 'Completed');
      case 'under_review':
        return t('plan.status.under_review', 'Under Review');
      case 'approved':
        return t('plan.status.approved', 'Approved');
      case 'needs_revision':
        return t('plan.status.needs_revision', 'Needs Revision');
      default:
        return status;
    }
  };

  // Calculate completion percentage
  const calculateCompletion = (plan: BusinessPlan) => {
    const sections = [
      plan.executive_summary,
      plan.market_analysis,
      plan.competitive_analysis,
      plan.marketing_strategy,
      plan.operations_plan,
      plan.management_team,
      plan.financial_projections,
      plan.funding_request,
      plan.risk_analysis
    ];
    
    const completedSections = sections.filter(section => section && section.trim().length > 0).length;
    return Math.round((completedSections / sections.length) * 100);
  };

  if (error) {
    return (
      <DashboardLayout currentPage="incubator">
        <Alert variant="error">
          {t('common.error.loadFailed', 'Failed to load data. Please try again.')}
        </Alert>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout currentPage="incubator">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-white">
              {t('admin.business.plans.management', 'Business Plans Management')}
            </h1>
            <p className="text-gray-400 mt-1">
              {t('admin.business.plans.description', 'View and manage business plans')}
            </p>
          </div>
          <div className="flex gap-3">
            <Button
              onClick={() => refetch()}
              variant="secondary"
              size="sm"
              disabled={isLoading}
            >
              <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              {t('common.refresh', 'Refresh')}
            </Button>
            <Button onClick={handleCreatePlan} size="sm">
              <Plus className="w-4 h-4 mr-2" />
              {t('businessPlan.create', 'Create Plan')}
            </Button>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="bg-indigo-900/50 rounded-lg p-4">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder={t('businessPlan.searchPlaceholder', 'Search business plans...')}
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                />
              </div>
            </div>
            <AdvancedFilter
              filters={filterOptions}
              values={filters}
              onChange={handleFilterChange}
            />
          </div>
        </div>

        {/* Bulk Actions */}
        {selectedPlans.length > 0 && (
          <BulkActions
            selectedCount={selectedPlans.length}
            actions={bulkActions}
            onClearSelection={() => setSelectedPlans([])}
          />
        )}

        {/* Business Plans Table */}
        <div className="bg-indigo-900/50 rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-indigo-800/50">
                <tr>
                  <th className="px-4 py-3 text-left">
                    <input
                      type="checkbox"
                      checked={selectedPlans.length === plans.length && plans.length > 0}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedPlans(plans.map(p => p.id));
                        } else {
                          setSelectedPlans([]);
                        }
                      }}
                      className="rounded border-gray-600 bg-indigo-800 text-purple-500 focus:ring-purple-500"
                    />
                  </th>
                  <th className="px-4 py-3 text-left text-gray-300 font-medium">
                    {t('common.title', 'Title')}
                  </th>
                  <th className="px-4 py-3 text-left text-gray-300 font-medium">
                    {t('businessPlan.businessIdea', 'Business Idea')}
                  </th>
                  <th className="px-4 py-3 text-left text-gray-300 font-medium">
                    {t('admin.status', 'Status')}
                  </th>
                  <th className="px-4 py-3 text-left text-gray-300 font-medium">
                    {t('businessPlan.completion', 'Completion')}
                  </th>
                  <th className="px-4 py-3 text-left text-gray-300 font-medium">
                    {t('common.lastUpdated', 'Last Updated')}
                  </th>
                  <th className="px-4 py-3 text-right text-gray-300 font-medium">
                    {t('common.actions', 'Actions')}
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-indigo-800/50">
                {isLoading ? (
                  <tr>
                    <td colSpan={7} className="px-4 py-8 text-center text-gray-400">
                      {t('common.loading', 'Loading...')}
                    </td>
                  </tr>
                ) : plans.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="px-4 py-8 text-center text-gray-400">
                      {t('businessPlan.noPlans', 'No business plans found')}
                    </td>
                  </tr>
                ) : (
                  plans.map((plan) => {
                    const completion = calculateCompletion(plan);
                    return (
                      <tr key={plan.id} className="hover:bg-indigo-800/30">
                        <td className="px-4 py-3">
                          <input
                            type="checkbox"
                            checked={selectedPlans.includes(plan.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedPlans(prev => [...prev, plan.id]);
                              } else {
                                setSelectedPlans(prev => prev.filter(id => id !== plan.id));
                              }
                            }}
                            className="rounded border-gray-600 bg-indigo-800 text-purple-500 focus:ring-purple-500"
                          />
                        </td>
                        <td className="px-4 py-3">
                          <div className="text-white font-medium">{plan.title || 'Untitled Plan'}</div>
                          {plan.executive_summary && (
                            <div className="text-gray-400 text-sm line-clamp-2 mt-1">
                              {plan.executive_summary}
                            </div>
                          )}
                        </td>
                        <td className="px-4 py-3">
                          <div className="text-gray-300">{plan.business_idea_title}</div>
                          <div className="text-gray-500 text-sm">
                            {plan.owner_details.first_name} {plan.owner_details.last_name}
                          </div>
                        </td>
                        <td className="px-4 py-3">
                          {getStatusBadge(plan.status)}
                        </td>
                        <td className="px-4 py-3">
                          <div className="flex items-center gap-2">
                            <div className="w-16 bg-gray-700 rounded-full h-2">
                              <div
                                className={`h-2 rounded-full transition-all duration-300 ${
                                  completion >= 80 ? 'bg-green-500' :
                                  completion >= 60 ? 'bg-blue-500' :
                                  completion >= 40 ? 'bg-yellow-500' :
                                  completion >= 20 ? 'bg-orange-500' : 'bg-red-500'
                                }`}
                                style={{ width: `${completion}%` }}
                              ></div>
                            </div>
                            <span className="text-gray-300 text-sm">{completion}%</span>
                          </div>
                        </td>
                        <td className="px-4 py-3">
                          <div className="text-gray-300">
                            {new Date(plan.updated_at).toLocaleDateString()}
                          </div>
                        </td>
                        <td className="px-4 py-3">
                          <div className="flex justify-end gap-2">
                            <Button
                              onClick={() => handleEditPlan(plan)}
                              variant="secondary"
                              size="sm"
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button
                              onClick={() => handleDuplicatePlan(plan)}
                              variant="secondary"
                              size="sm"
                              disabled={duplicatePlan.isPending}
                            >
                              <Copy className="w-4 h-4" />
                            </Button>
                            <Button
                              onClick={() => handleGenerateFeedback(plan)}
                              variant="secondary"
                              size="sm"
                              disabled={generateFeedback.isPending}
                            >
                              <Brain className="w-4 h-4" />
                            </Button>
                            <Button
                              onClick={() => handleDeletePlan(plan)}
                              variant="danger"
                              size="sm"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    );
                  })
                )}
              </tbody>
            </table>
          </div>
        </div>
        {/* Modals */}
        <BusinessPlanModal
          isOpen={showCreateModal || showEditModal}
          onClose={() => {
            setShowCreateModal(false);
            setShowEditModal(false);
            setSelectedPlan(null);
          }}
          plan={selectedPlan}
          isEditing={showEditModal}
        />

        <BusinessPlanDeleteModal
          isOpen={showDeleteModal}
          onClose={() => {
            setShowDeleteModal(false);
            setSelectedPlan(null);
          }}
          plan={selectedPlan}
        />
      </div>
    </DashboardLayout>
  );
};

// Helper functions
const getStatusBadge = (status: string) => {
  const statusConfig = {
    draft: { color: 'bg-gray-500', label: 'Draft' },
    in_progress: { color: 'bg-blue-500', label: 'In Progress' },
    completed: { color: 'bg-green-500', label: 'Completed' },
    archived: { color: 'bg-red-500', label: 'Archived' }
  };

  const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;

  return (
    <span className={`px-2 py-1 rounded-full text-xs font-medium text-white ${config.color}`}>
      {config.label}
    </span>
  );
};

// Calculate completion percentage
const calculateCompletion = (plan: BusinessPlan) => {
  if (!plan.content) return 0;

  const sections = [
    plan.content.executive_summary,
    plan.content.market_analysis,
    plan.content.competitive_analysis,
    plan.content.marketing_strategy,
    plan.content.operations_plan,
    plan.content.management_team,
    plan.content.financial_projections,
    plan.content.funding_request,
    plan.content.risk_analysis
  ];

  const completedSections = sections.filter(section => section && section.trim().length > 0).length;
  return Math.round((completedSections / sections.length) * 100);
};

export default BusinessPlansManagement;
