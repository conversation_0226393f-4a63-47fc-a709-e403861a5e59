import React from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../hooks/useLanguage';
import { useAppSelector } from '../../../../store/hooks';
import { RTLText, RTLFlex, RTLIcon } from '../../../common';
import { Activity, ArrowRight } from 'lucide-react';
import { BusinessIdea } from '../../../../services/incubatorApi';
interface BusinessIdeaProgressProps {
  latestIdea: BusinessIdea;
}

const BusinessIdeaProgress: React.FC<BusinessIdeaProgressProps> = ({ latestIdea  }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { language } = useAppSelector(state => state.language);

  if (!latestIdea) return null;

  return (
    <div className="mb-8">
      <RTLText as="h2" className="text-xl font-semibold mb-4">{t('dashboard.progress')}</RTLText>
      <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50">
        <RTLFlex className="justify-between mb-4" align="center">
          <RTLFlex className="items-center">
            <RTLIcon icon={Activity} size={20} className={`${language === 'ar' ? 'ml-2' : 'mr-2'} text-purple-400`} />
            <RTLText as="h3" className="font-medium">{t('dashboard.trackProgress')}</RTLText>
          </RTLFlex>
          <RTLFlex
            as={Link}
            to={`/dashboard/business-ideas/${latestIdea.id}/progress`}
            className="text-purple-400 hover:text-purple-300 text-sm"
            align="center"
          >
            {t('dashboard.viewAnalytics')} <RTLIcon icon={ArrowRight} size={14} className={language === 'ar' ? 'mr-1' : 'ml-1'} flipInRTL={true} />
          </RTLFlex>
        </RTLFlex>
        <RTLText as="p" className="text-gray-400 text-sm mb-4">
          {t('dashboard.progressDescription', { title: latestIdea.title })}
        </RTLText>
        <RTLFlex
          as={Link}
          to={`/dashboard/progress-updates/new?business_idea=${latestIdea.id}`}
          className={`bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded inline-flex ${isRTL ? "flex-row-reverse" : ""}`}
          align="center"
        >
          {t('dashboard.addUpdate')} <RTLIcon icon={ArrowRight} size={16} className={language === 'ar' ? 'mr-1' : 'ml-1'} flipInRTL={true} />
        </RTLFlex>
      </div>
    </div>
  );
};

export default BusinessIdeaProgress;
