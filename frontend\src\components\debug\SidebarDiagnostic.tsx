/**
 * Sidebar Diagnostic Component
 * Helps debug sidebar visibility issues for users
 */

import React, { useState } from 'react';
import { useAppSelector } from '../../store/hooks';
import { useTranslation } from 'react-i18next';
import { isSuperAdmin } from '../../utils/roleBasedRouting';
import { 
  Bug, 
  User, 
  Shield, 
  Eye, 
  EyeOff, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Info
} from 'lucide-react';

const SidebarDiagnostic: React.FC = () => {
  const { t } = useTranslation();
  const { user, isAuthenticated } = useAppSelector(state => state.auth);
  const [isVisible, setIsVisible] = useState(false);

  // Only show in development mode
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const getUserType = () => {
    if (!user) return 'user';
    if (isSuperAdmin(user)) return 'super_admin';
    if (user.is_admin) return 'admin';

    const roles = user.profile?.active_roles?.map(role => role.name) || [];
    const userRoles = user.roles || [];
    const allRoles = [...roles, ...userRoles];
    
    if (allRoles.includes('moderator')) return 'moderator';
    if (allRoles.includes('mentor')) return 'mentor';
    if (allRoles.includes('investor')) return 'investor';
    return 'user';
  };

  const userType = getUserType();

  const diagnosticInfo = {
    authentication: {
      isAuthenticated,
      status: isAuthenticated ? 'success' : 'error'
    },
    user: {
      exists: !!user,
      id: user?.id,
      username: user?.username,
      is_admin: user?.is_admin,
      status: user ? 'success' : 'error'
    },
    userType: {
      detected: userType,
      status: userType ? 'success' : 'warning'
    },
    roles: {
      profileRoles: user?.profile?.active_roles?.map(role => role.name) || [],
      userRoles: user?.roles || [],
      status: (user?.profile?.active_roles?.length || 0) > 0 || (user?.roles?.length || 0) > 0 ? 'success' : 'warning'
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error': return <XCircle className="w-4 h-4 text-red-500" />;
      case 'warning': return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      default: return <Info className="w-4 h-4 text-blue-500" />;
    }
  };

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 bg-purple-600 hover:bg-purple-700 text-white p-3 rounded-full shadow-lg z-50"
        title="Show Sidebar Diagnostic"
      >
        <Bug className="w-5 h-5" />
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 glass-morphism border border-glass-border rounded-lg shadow-glass p-4 max-w-md z-50">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Bug className="w-5 h-5 text-purple-600" />
          <h3 className="font-semibold text-glass-primary">Sidebar Diagnostic</h3>
        </div>
        <button
          onClick={() => setIsVisible(false)}
          className="text-glass-secondary hover:text-glass-primary"
        >
          <EyeOff className="w-4 h-4" />
        </button>
      </div>

      <div className="space-y-3 text-sm">
        {/* Authentication Status */}
        <div className="flex items-center justify-between">
          <span className="text-gray-700 dark:text-gray-300">Authentication:</span>
          <div className="flex items-center gap-1">
            {getStatusIcon(diagnosticInfo.authentication.status)}
            <span className={`font-medium ${
              diagnosticInfo.authentication.status === 'success' ? 'text-green-600' : 'text-red-600'
            }`}>
              {isAuthenticated ? 'Authenticated' : 'Not Authenticated'}
            </span>
          </div>
        </div>

        {/* User Status */}
        <div className="flex items-center justify-between">
          <span className="text-gray-700 dark:text-gray-300">User Data:</span>
          <div className="flex items-center gap-1">
            {getStatusIcon(diagnosticInfo.user.status)}
            <span className={`font-medium ${
              diagnosticInfo.user.status === 'success' ? 'text-green-600' : 'text-red-600'
            }`}>
              {user ? `ID: ${user.id}` : 'No User Data'}
            </span>
          </div>
        </div>

        {/* User Type */}
        <div className="flex items-center justify-between">
          <span className="text-gray-700 dark:text-gray-300">User Type:</span>
          <div className="flex items-center gap-1">
            {getStatusIcon(diagnosticInfo.userType.status)}
            <span className="font-medium text-blue-600 capitalize">
              {userType}
            </span>
          </div>
        </div>

        {/* Roles */}
        <div className="border-t border-gray-200 dark:border-gray-600 pt-2">
          <div className="flex items-center gap-1 mb-1">
            {getStatusIcon(diagnosticInfo.roles.status)}
            <span className="text-gray-700 dark:text-gray-300 font-medium">Roles:</span>
          </div>
          <div className="ml-5 space-y-1">
            {diagnosticInfo.roles.profileRoles.length > 0 && (
              <div>
                <span className="text-xs text-gray-500">Profile Roles:</span>
                <div className="flex flex-wrap gap-1">
                  {diagnosticInfo.roles.profileRoles.map(role => (
                    <span key={role} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                      {role}
                    </span>
                  ))}
                </div>
              </div>
            )}
            {diagnosticInfo.roles.userRoles.length > 0 && (
              <div>
                <span className="text-xs text-gray-500">User Roles:</span>
                <div className="flex flex-wrap gap-1">
                  {diagnosticInfo.roles.userRoles.map(role => (
                    <span key={role} className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded">
                      {role}
                    </span>
                  ))}
                </div>
              </div>
            )}
            {diagnosticInfo.roles.profileRoles.length === 0 && diagnosticInfo.roles.userRoles.length === 0 && (
              <span className="text-xs text-gray-500">No special roles</span>
            )}
          </div>
        </div>

        {/* Admin Status */}
        {user?.is_admin && (
          <div className="flex items-center gap-1 text-orange-600">
            <Shield className="w-4 h-4" />
            <span className="font-medium">Admin User</span>
          </div>
        )}

        {/* Super Admin Status */}
        {isSuperAdmin(user) && (
          <div className="flex items-center gap-1 text-red-600">
            <Shield className="w-4 h-4" />
            <span className="font-medium">Super Admin User</span>
          </div>
        )}
      </div>

      <div className="mt-3 pt-2 border-t border-gray-200 dark:border-gray-600">
        <p className="text-xs text-gray-500">
          Check browser console for detailed sidebar debug info
        </p>
      </div>
    </div>
  );
};

export default SidebarDiagnostic;
