import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Event, eventsAPI } from '../services/api';

// Define the state interface
interface EventsState {
  events: Event[];
  isLoading: boolean;
  error: string | null;
}

// Initial state
const initialState: EventsState = {
  events: [],
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchEvents = createAsyncThunk(
  'events/fetchEvents',
  async (_, { rejectWithValue }) => {
    try {
      const events = await eventsAPI.getEvents();

      // Ensure we always return an array
      if (!Array.isArray(events)) {
        console.error('API returned non-array events:', events);
        return [];
      }

      return events;
    } catch (err) {
      console.error('Error fetching events:', err);
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to fetch events');
    }
  }
);

export const attendEvent = createAsyncThunk(
  'events/attendEvent',
  async (eventId: number, { rejectWithValue, dispatch }) => {
    try {
      await eventsAPI.attendEvent(eventId);
      // Refresh events after attending
      dispatch(fetchEvents());
      return eventId;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to attend event');
    }
  }
);

export const unattendEvent = createAsyncThunk(
  'events/unattendEvent',
  async (eventId: number, { rejectWithValue, dispatch }) => {
    try {
      await eventsAPI.unattendEvent(eventId);
      // Refresh events after unattending
      dispatch(fetchEvents());
      return eventId;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to unattend event');
    }
  }
);

interface CreateEventData {
  title: string;
  description: string;
  date: string;
  location: string;
  is_virtual: boolean;
  virtual_link: string | null;
  organizer_id: number;
}

export const createEvent = createAsyncThunk(
  'events/createEvent',
  async (eventData: CreateEventData, { rejectWithValue, dispatch }) => {
    try {
      const newEvent = await eventsAPI.createEvent(eventData);
      // Refresh events after creating a new one
      dispatch(fetchEvents());
      return newEvent;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Failed to create event');
    }
  }
);

// Create the slice
const eventsSlice = createSlice({
  name: 'events',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch events
    builder.addCase(fetchEvents.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchEvents.fulfilled, (state, action: PayloadAction<Event[]>) => {
      state.isLoading = false;
      // Ensure events is always an array
      state.events = Array.isArray(action.payload) ? action.payload : [];
      state.error = null;

      // Log the events for debugging
      console.log('Events loaded into state:', state.events);
    });
    builder.addCase(fetchEvents.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    // Attend event
    builder.addCase(attendEvent.rejected, (state, action) => {
      state.error = action.payload as string;
    });

    // Unattend event
    builder.addCase(unattendEvent.rejected, (state, action) => {
      state.error = action.payload as string;
    });

    // Create event
    builder.addCase(createEvent.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(createEvent.fulfilled, (state) => {
      state.isLoading = false;
      state.error = null;
    });
    builder.addCase(createEvent.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });
  },
});

// Export actions and reducer
export const { clearError } = eventsSlice.actions;
export default eventsSlice.reducer;
