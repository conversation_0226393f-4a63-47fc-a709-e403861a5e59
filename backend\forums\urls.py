from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from .views import (
    ForumCategoryViewSet, ForumTopicViewSet, ForumThreadViewSet,
    ForumPostViewSet, UserReputationViewSet, ReputationActivityViewSet,
    ForumSearchView, ContentFlagViewSet, TopicSubscriptionViewSet,
    ThreadSubscriptionViewSet, ForumAttachmentViewSet, ForumAnalyticsView
)

router = DefaultRouter()
router.register(r'categories', ForumCategoryViewSet, basename='forum-category')
router.register(r'topics', ForumTopicViewSet, basename='forum-topic')
router.register(r'threads', ForumThreadViewSet, basename='forum-thread')
router.register(r'posts', ForumPostViewSet, basename='forum-post')
router.register(r'reputation', UserReputationViewSet, basename='user-reputation')
router.register(r'reputation-activities', ReputationActivityViewSet, basename='reputation-activity')
router.register(r'content-flags', ContentFlagViewSet, basename='content-flag')
router.register(r'topic-subscriptions', TopicSubscriptionViewSet, basename='topic-subscription')
router.register(r'thread-subscriptions', ThreadSubscriptionViewSet, basename='thread-subscription')
router.register(r'attachments', ForumAttachmentViewSet, basename='forum-attachment')

urlpatterns = [
    path('', include(router.urls)),
    path('search/', ForumSearchView.as_view(), name='forum-search'),
    path('analytics/', ForumAnalyticsView.as_view(), name='forum-analytics'),
]
