import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { forumTopicsAPI } from '../services/forumApi';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import { ArrowLeft, Send, AlertCircle, Upload, X } from 'lucide-react';

import { useTranslation } from 'react-i18next';
const CreateTopicPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);

  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [icon, setIcon] = useState('message-square');
  const [categoryId, setCategoryId] = useState<number | null>(null);
  const [image, setImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);

  useEffect(() => {
    // Get category ID from URL query parameter
    const params = new URLSearchParams(location.search);
    const category = params.get('category');

    if (category) {
      setCategoryId(parseInt(category));
    } else {
      // Redirect to forums if no category ID is provided
      navigate('/forum');
    }
  }, [location, navigate]);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      setFormError('Image size should be less than 2MB');
      return;
    }

    // Check file type
    if (!file.type.startsWith('image/')) {
      setFormError(t("common.please.upload.an", "Please upload an image file"));
      return;
    }

    setImage(file);

    // Create preview
    const reader = new FileReader();
    reader.onloadend = () => {
      setImagePreview(reader.result as string);
    };
    reader.readAsDataURL(file);
  };

  const removeImage = () => {
    setImage(null);
    setImagePreview(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user || !categoryId) return;

    // Validate form
    if (!title.trim()) {
      setFormError(t("common.please.enter.a", "Please enter a title for your topic"));
      return;
    }

    if (!description.trim()) {
      setFormError(t("common.please.enter.a", "Please enter a description for your topic"));
      return;
    }

    setIsSubmitting(true);
    setFormError(null);

    try {
      // Create form data for image upload
      const formData = new FormData();
      formData.append('title', title);
      formData.append('description', description);
      formData.append('category', categoryId.toString());
      formData.append('icon', icon);
      formData.append('created_by_id', user.id.toString());

      if (image) {
        formData.append('image', image);
      }

      // Call API directly since we need to handle FormData
      const response = await fetch('/api/forums/topics/', {
        method: 'POST',
        body: formData,
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error(t("common.failed.to.create", "Failed to create topic"));
      }

      const result = await response.json();

      // Navigate to the new topic
      navigate(`/forum/topic/${result.slug}`);
    } catch (err) {
      console.error("Failed to create topic:", err);
      setFormError(t("common.failed.to.create", "Failed to create topic. Please try again."));
    } finally {
      setIsSubmitting(false);
    }
  };

  const iconOptions = [
    { value: 'message-square', label: t("common.message.square", "Message Square") },
    { value: 'users', label: t("common.users", "Users") },
    { value: 'book', label: t("common.book", "Book") },
    { value: 'code', label: t("common.code", "Code") },
    { value: 'lightbulb', label: t("common.lightbulb", "Lightbulb") },
    { value: 'help-circle', label: t("common.help.circle", "Help Circle") },
    { value: 'globe', label: t("common.globe", "Globe") },
    { value: 'briefcase', label: t("common.briefcase", "Briefcase") },
    { value: 'award', label: t("common.award", "Award") },
  ];

  return (
    <div className={`min-h-screen flex flex-col ${isRTL ? "flex-row-reverse" : ""}`}>
      <Navbar />

      <main className={`flex-grow container mx-auto px-4 py-8 mt-16 ${isRTL ? "flex-row-reverse" : ""}`}>
        {/* Breadcrumb */}
        <div className="mb-6">
          <Link to="/forum" className={`text-purple-400 hover:text-purple-300 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <ArrowLeft size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
            Back to Forums
          </Link>
        </div>

        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
          <h1 className="text-2xl font-bold mb-6">t("common.create.new.topic", "Create New Topic")</h1>

          {formError && (
            <div className="bg-red-900/30 border border-red-800 rounded-lg p-4 mb-6">
              <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <AlertCircle size={20} className={`text-red-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                <div className="text-red-300">{formError}</div>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit}>
            <div className="mb-6">
              <RTLText as="label" htmlFor="title" className="block text-sm font-medium mb-2">
                {t('forum.createTopic.topicTitle')}
              </RTLText>
              <input
                type="text"
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="w-full bg-indigo-900/30 border border-indigo-800/50 rounded-lg py-2 px-4 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder={t('forum.createTopic.titlePlaceholder')}
                required
                dir={language === 'ar' ? 'rtl' : 'ltr'}
              />
            </div>

            <div className="mb-6">
              <RTLText as="label" htmlFor="description" className="block text-sm font-medium mb-2">
                {t('forum.createTopic.topicDescription')}
              </RTLText>
              <textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="w-full bg-indigo-900/30 border border-indigo-800/50 rounded-lg py-2 px-4 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 min-h-[100px]"
                placeholder={t('forum.createTopic.descriptionPlaceholder')}
                required
                dir={language === 'ar' ? 'rtl' : 'ltr'}
              />
            </div>

            <div className="mb-6">
              <RTLText as="label" htmlFor="icon" className="block text-sm font-medium mb-2">
                {t('forum.createTopic.topicIcon')}
              </RTLText>
              <select
                id="icon"
                value={icon}
                onChange={(e) => setIcon(e.target.value)}
                className="w-full bg-indigo-900/30 border border-indigo-800/50 rounded-lg py-2 px-4 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                dir={language === 'ar' ? 'rtl' : 'ltr'}
              >
                {iconOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div className="mb-6">
              <RTLText as="label" className="block text-sm font-medium mb-2">
                {t('forum.createTopic.topicImage')}
              </RTLText>

              {imagePreview ? (
                <div className="relative w-full h-40 mb-2">
                  <img
                    src={imagePreview}
                    alt={t('forum.createTopic.topicPreview')}
                    className="w-full h-full object-cover rounded-lg"
                  />
                  <button
                    type="button"
                    onClick={removeImage}
                    className={`absolute top-2 ${language === 'ar' ? 'left-2' : 'right-2'} p-1 bg-red-900/80 rounded-full hover:bg-red-800`}
                  >
                    <X size={16} />
                  </button>
                </div>
              ) : (
                <div className="border-2 border-dashed border-indigo-800/50 rounded-lg p-4 text-center">
                  <input
                    type="file"
                    id="image"
                    onChange={handleImageChange}
                    className="hidden"
                    accept="image/*"
                  />
                  <RTLFlex
                    as="label"
                    htmlFor="image"
                    className={`flex-col items-center justify-center cursor-pointer ${isRTL ? "flex-row-reverse" : ""}`}
                    direction="column"
                  >
                    <RTLIcon icon={Upload} size={24} className="mb-2 text-gray-400" />
                    <RTLText as="span" className="text-gray-400">{t('forum.createTopic.clickToUpload')}</RTLText>
                    <RTLText as="span" className="text-xs text-gray-500 mt-1">{t('forum.createTopic.imageRequirements')}</RTLText>
                  </RTLFlex>
                </div>
              )}
            </div>

            <div className={`flex ${language === 'ar' ? 'justify-start' : 'justify-end'}`}>
              <RTLFlex
                as="button"
                type="button"
                onClick={() => navigate(-1)}
                className={`px-4 py-2 bg-indigo-800/50 rounded-lg font-medium hover:bg-indigo-700/50 transition-colors ${language === 'ar' ? 'ml-4' : 'mr-4'}`}
              >
                {t('common.cancel')}
              </RTLFlex>
              <RTLFlex
                as="button"
                type="submit"
                disabled={isSubmitting || !title.trim() || !description.trim()}
                className="px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300 items-center disabled:opacity-50 disabled:cursor-not-allowed"
                align="center"
              >
                {isSubmitting ? (
                  <>
                    <div className={`animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white ${language === 'ar' ? 'ml-2' : 'mr-2'}`}></div>
                    {t('forum.createTopic.creating')}
                  </>
                ) : (
                  <>
                    <RTLIcon icon={Send} size={18} className={language === 'ar' ? 'ml-2' : 'mr-2'} />
                    {t('forum.createTopic.createTopic')}
                  </>
                )}
              </RTLFlex>
            </div>
          </form>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default CreateTopicPage;
