/**
 * Automated Business Plan Generator
 * AI-powered workflow that generates comprehensive business plans from business ideas
 */

import React, { useState, useEffect } from 'react';
import {
  FileText,
  Brain,
  Zap,
  CheckCircle,
  Clock,
  Download,
  Eye,
  RefreshCw,
  Target,
  TrendingUp,
  DollarSign,
  Users,
  BarChart3,
  Lightbulb,
  AlertTriangle,
  Play,
  Pause,
  Settings
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useConsolidatedAI } from '../../hooks/useConsolidatedAI';


interface BusinessPlanSection {
  id: string;
  title: string;
  status: 'pending' | 'generating' | 'completed' | 'error';
  content: string;
  confidence: number;
  estimatedTime: number;
  dependencies: string[];
}

interface BusinessPlanGeneratorProps {
  businessIdeaId: number;
  businessIdeaData: any;
  userId?: number;
  onPlanGenerated?: (plan: any) => void;
  className?: string;
}

export const AutomatedBusinessPlanGenerator: React.FC<BusinessPlanGeneratorProps> = ({
  businessIdeaId,
  businessIdeaData,
  userId,
  onPlanGenerated,
  className = ''
}) => {
  const { t } = useTranslation();
  const [isGenerating, setIsGenerating] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [progress, setProgress] = useState(0);
  const [estimatedTimeRemaining, setEstimatedTimeRemaining] = useState(0);
  const [generatedPlan, setGeneratedPlan] = useState<any>(null);
  
  const { sendMessage } = useConsolidatedAI();

  // Business plan sections to generate
  const [sections, setSections] = useState<BusinessPlanSection[]>([
    {
      id: 'executive_summary',
      title: 'Executive Summary',
      status: 'pending',
      content: '',
      confidence: 0,
      estimatedTime: 30,
      dependencies: []
    },
    {
      id: 'market_analysis',
      title: 'Market Analysis',
      status: 'pending',
      content: '',
      confidence: 0,
      estimatedTime: 45,
      dependencies: []
    },
    {
      id: 'competitive_analysis',
      title: 'Competitive Analysis',
      status: 'pending',
      content: '',
      confidence: 0,
      estimatedTime: 40,
      dependencies: ['market_analysis']
    },
    {
      id: 'business_model',
      title: 'Business Model & Revenue Streams',
      status: 'pending',
      content: '',
      confidence: 0,
      estimatedTime: 35,
      dependencies: []
    },
    {
      id: 'marketing_strategy',
      title: 'Marketing & Sales Strategy',
      status: 'pending',
      content: '',
      confidence: 0,
      estimatedTime: 40,
      dependencies: ['market_analysis', 'competitive_analysis']
    },
    {
      id: 'operations_plan',
      title: 'Operations Plan',
      status: 'pending',
      content: '',
      confidence: 0,
      estimatedTime: 35,
      dependencies: ['business_model']
    },
    {
      id: 'financial_projections',
      title: 'Financial Projections',
      status: 'pending',
      content: '',
      confidence: 0,
      estimatedTime: 50,
      dependencies: ['business_model', 'marketing_strategy']
    },
    {
      id: 'risk_analysis',
      title: 'Risk Analysis & Mitigation',
      status: 'pending',
      content: '',
      confidence: 0,
      estimatedTime: 30,
      dependencies: ['market_analysis', 'competitive_analysis']
    },
    {
      id: 'implementation_timeline',
      title: 'Implementation Timeline',
      status: 'pending',
      content: '',
      confidence: 0,
      estimatedTime: 25,
      dependencies: ['operations_plan', 'financial_projections']
    }
  ]);

  useEffect(() => {
    if (isGenerating && !isPaused) {
      generateNextSection();
    }
  }, [isGenerating, isPaused, currentStep]);

  const startGeneration = async () => {
    setIsGenerating(true);
    setIsPaused(false);
    setCurrentStep(0);
    setProgress(0);
    
    // Calculate total estimated time
    const totalTime = sections.reduce((sum, section) => sum + section.estimatedTime, 0);
    setEstimatedTimeRemaining(totalTime);
  };

  const pauseGeneration = () => {
    setIsPaused(true);
  };

  const resumeGeneration = () => {
    setIsPaused(false);
  };

  const generateNextSection = async () => {
    if (currentStep >= sections.length) {
      // Generation complete
      setIsGenerating(false);
      setProgress(100);
      compileFinalPlan();
      return;
    }

    const section = sections[currentStep];
    
    // Check if dependencies are met
    const dependenciesMet = section.dependencies.every(depId => 
      sections.find(s => s.id === depId)?.status === 'completed'
    );

    if (!dependenciesMet) {
      // Skip to next section or wait
      setCurrentStep(prev => prev + 1);
      return;
    }

    // Update section status to generating
    setSections(prev => prev.map(s => 
      s.id === section.id ? { ...s, status: 'generating' as const } : s
    ));

    try {
      // Generate section content
      const sectionContent = await generateSectionContent(section);
      
      // Update section with generated content
      setSections(prev => prev.map(s => 
        s.id === section.id 
          ? { 
              ...s, 
              status: 'completed' as const, 
              content: sectionContent.content,
              confidence: sectionContent.confidence 
            } 
          : s
      ));

      // Update progress
      const completedSections = currentStep + 1;
      const newProgress = (completedSections / sections.length) * 100;
      setProgress(newProgress);

      // Update estimated time remaining
      const remainingTime = sections.slice(currentStep + 1).reduce((sum, s) => sum + s.estimatedTime, 0);
      setEstimatedTimeRemaining(remainingTime);

      // Move to next section
      setCurrentStep(prev => prev + 1);

    } catch (error) {
      console.error('Error generating section:', error);
      setSections(prev => prev.map(s => 
        s.id === section.id ? { ...s, status: 'error' as const } : s
      ));
      setCurrentStep(prev => prev + 1);
    }
  };

  const generateSectionContent = async (section: BusinessPlanSection) => {
    // Get context from completed sections
    const completedSections = sections.filter(s => s.status === 'completed');
    const context = completedSections.map(s => `${s.title}: ${s.content}`).join('\n\n');

    const prompt = `Generate a comprehensive ${section.title} section for a business plan based on this business idea:

Business Idea: ${JSON.stringify(businessIdeaData)}

Context from other sections:
${context}

Please provide:
1. Detailed, professional content for the ${section.title} section
2. Specific data, metrics, and actionable insights
3. Industry-relevant information and best practices
4. Clear structure with headings and bullet points where appropriate

Make it comprehensive but concise, suitable for investors and stakeholders.`;

    const response = await sendMessage(prompt, {
      userId,
      businessIdeaId,
      language: 'en',
      context: {
        type: 'business_plan_generation',
        section: section.id,
        businessIdea: businessIdeaData
      }
    });

    // Simulate confidence calculation
    const confidence = Math.floor(Math.random() * 20) + 80; // 80-100%

    return {
      content: response?.content || `Generated ${section.title} content would appear here...`,
      confidence
    };
  };

  const compileFinalPlan = () => {
    const completedSections = sections.filter(s => s.status === 'completed');
    const plan = {
      id: `plan_${businessIdeaId}_${Date.now()}`,
      businessIdeaId,
      title: `Business Plan: ${businessIdeaData.title}`,
      sections: completedSections,
      generatedAt: new Date(),
      overallConfidence: Math.round(
        completedSections.reduce((sum, s) => sum + s.confidence, 0) / completedSections.length
      )
    };

    setGeneratedPlan(plan);
    onPlanGenerated?.(plan);
  };

  const getSectionIcon = (sectionId: string) => {
    const iconMap: Record<string, React.ReactNode> = {
      executive_summary: <FileText className="w-4 h-4" />,
      market_analysis: <BarChart3 className="w-4 h-4" />,
      competitive_analysis: <Target className="w-4 h-4" />,
      business_model: <DollarSign className="w-4 h-4" />,
      marketing_strategy: <TrendingUp className="w-4 h-4" />,
      operations_plan: <Settings className="w-4 h-4" />,
      financial_projections: <DollarSign className="w-4 h-4" />,
      risk_analysis: <AlertTriangle className="w-4 h-4" />,
      implementation_timeline: <Clock className="w-4 h-4" />
    };
    return iconMap[sectionId] || <FileText className="w-4 h-4" />;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 dark:text-green-400';
      case 'generating':
        return 'text-blue-600 dark:text-blue-400';
      case 'error':
        return 'text-red-600 dark:text-red-400';
      default:
        return 'text-gray-500 dark:text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'generating':
        return <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />;
      case 'error':
        return <AlertTriangle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
            <Brain className="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              {t('ai.businessPlan.title', 'AI Business Plan Generator')}
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {t('ai.businessPlan.subtitle', 'Automated comprehensive business plan generation')}
            </p>
          </div>
        </div>

        {/* Controls */}
        <div className="flex items-center space-x-3">
          {!isGenerating && !generatedPlan && (
            <button
              onClick={startGeneration}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Play className="w-4 h-4" />
              <span>{t('ai.businessPlan.start', 'Generate Plan')}</span>
            </button>
          )}

          {isGenerating && (
            <div className="flex items-center space-x-2">
              {!isPaused ? (
                <button
                  onClick={pauseGeneration}
                  className="flex items-center space-x-2 px-3 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors"
                >
                  <Pause className="w-4 h-4" />
                  <span>{t('ai.businessPlan.pause', 'Pause')}</span>
                </button>
              ) : (
                <button
                  onClick={resumeGeneration}
                  className="flex items-center space-x-2 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  <Play className="w-4 h-4" />
                  <span>{t('ai.businessPlan.resume', 'Resume')}</span>
                </button>
              )}
            </div>
          )}

          {generatedPlan && (
            <div className="flex items-center space-x-2">
              <button className="flex items-center space-x-2 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                <Eye className="w-4 h-4" />
                <span>{t('ai.businessPlan.view', 'View Plan')}</span>
              </button>
              <button className="flex items-center space-x-2 px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                <Download className="w-4 h-4" />
                <span>{t('ai.businessPlan.download', 'Download')}</span>
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Progress Overview */}
      {(isGenerating || generatedPlan) && (
        <div className="glass-light border rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold text-gray-900 dark:text-white">
              {t('ai.businessPlan.progress', 'Generation Progress')}
            </h3>
            <div className="flex items-center space-x-4 text-sm">
              <span className="text-gray-600 dark:text-gray-400">
                {Math.round(progress)}% {t('ai.businessPlan.complete', 'Complete')}
              </span>
              {estimatedTimeRemaining > 0 && (
                <span className="text-gray-600 dark:text-gray-400">
                  ~{estimatedTimeRemaining}s {t('ai.businessPlan.remaining', 'remaining')}
                </span>
              )}
            </div>
          </div>
          
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-4">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>

          {generatedPlan && (
            <div className="flex items-center space-x-4 text-sm">
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span className="text-green-600 dark:text-green-400">
                  {t('ai.businessPlan.completed', 'Plan Generated Successfully')}
                </span>
              </div>
              <span className="text-gray-600 dark:text-gray-400">
                {t('ai.businessPlan.confidence', 'Overall Confidence')}: {generatedPlan.overallConfidence}%
              </span>
            </div>
          )}
        </div>
      )}

      {/* Sections List */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {sections.map((section, index) => (
          <div
            key={section.id}
            className={`glass-light border rounded-lg p-4 transition-all duration-200 ${
              currentStep === index && isGenerating ? 'ring-2 ring-blue-500' : ''
            }`}
          >
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center space-x-2">
                {getSectionIcon(section.id)}
                <h4 className="font-medium text-gray-900 dark:text-white text-sm">
                  {section.title}
                </h4>
              </div>
              {getStatusIcon(section.status)}
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between text-xs">
                <span className={`font-medium ${getStatusColor(section.status)}`}>
                  {section.status.charAt(0).toUpperCase() + section.status.slice(1)}
                </span>
                {section.status === 'completed' && (
                  <span className="text-gray-600 dark:text-gray-400">
                    {section.confidence}% confidence
                  </span>
                )}
              </div>

              {section.status === 'generating' && (
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
                  <div className="bg-blue-600 h-1 rounded-full animate-pulse w-3/4" />
                </div>
              )}

              {section.dependencies.length > 0 && (
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {t('ai.businessPlan.dependsOn', 'Depends on')}: {section.dependencies.join(', ')}
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
