import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Lightbulb,
  ChevronDown,
  ChevronUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  Users,
  Target,
  Award,
  TrendingUp
} from 'lucide-react';

interface RecommendedAction {
  type: string;
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
}

interface RecommendedActionsSectionProps {
  actions: RecommendedAction[];
  expanded: boolean;
  onToggle: () => void;
}

const RecommendedActionsSection: React.FC<RecommendedActionsSectionProps> = ({
  actions,
  expanded,
  onToggle
}) => {
  const { t } = useTranslation();

  if (!actions || actions.length === 0) {
    return null;
  }

  // Group actions by priority
  const highPriorityActions = actions.filter(action => action.priority === 'high');
  const mediumPriorityActions = actions.filter(action => action.priority === 'medium');
  const lowPriorityActions = actions.filter(action => action.priority === 'low');

  // Get icon for action type
  const getActionIcon = (type: string) => {
    switch (type) {
      case 'progress':
        return <TrendingUp size={16} className="text-purple-400" />;
      case 'milestone':
        return <Target size={16} className="text-pink-400" />;
      case 'goal':
        return <Award size={16} className="text-blue-400" />;
      case 'mentorship':
        return <Users size={16} className="text-green-400" />;
      case 'risk':
        return <AlertTriangle size={16} className="text-red-400" />;
      case 'competitive':
        return <TrendingUp size={16} className="text-yellow-400" />;
      default:
        return <Lightbulb size={16} className="text-yellow-400" />;
    }
  };

  return (
    <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
      <div
        className="flex items-center justify-between cursor-pointer"
        onClick={onToggle}
      >
        <h2 className="text-lg font-bold flex items-center">
          <Lightbulb size={18} className="text-yellow-400 mr-2" />
          {t('incubator.analytics.recommendations.title')}
        </h2>

        {expanded ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
      </div>

      {expanded && (
        <div className="mt-4 space-y-6">
          {/* High Priority Actions */}
          {highPriorityActions.length > 0 && (
            <div className="bg-red-900/20 rounded-lg p-4 border border-red-800/30">
              <h3 className="text-md font-medium mb-3 flex items-center">
                <AlertTriangle size={16} className="text-red-400 mr-2" />
                {t('incubator.analytics.recommendations.priority')} {t('incubator.analytics.recommendations.high')}
              </h3>

              <div className="space-y-3">
                {highPriorityActions.map((action, index) => (
                  <div key={index} className="bg-indigo-950/50 rounded-lg p-3 border border-indigo-800/50">
                    <div className="flex items-center mb-1">
                      {getActionIcon(action.type)}
                      <h4 className="font-medium ml-2">{action.title}</h4>
                    </div>
                    <div className="text-sm text-gray-400 ml-6">{action.description}</div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Medium Priority Actions */}
          {mediumPriorityActions.length > 0 && (
            <div className="bg-yellow-900/20 rounded-lg p-4 border border-yellow-800/30">
              <h3 className="text-md font-medium mb-3 flex items-center">
                <Clock size={16} className="text-yellow-400 mr-2" />
                {t('incubator.analytics.recommendations.priority')} {t('incubator.analytics.recommendations.medium')}
              </h3>

              <div className="space-y-3">
                {mediumPriorityActions.map((action, index) => (
                  <div key={index} className="bg-indigo-950/50 rounded-lg p-3 border border-indigo-800/50">
                    <div className="flex items-center mb-1">
                      {getActionIcon(action.type)}
                      <h4 className="font-medium ml-2">{action.title}</h4>
                    </div>
                    <div className="text-sm text-gray-400 ml-6">{action.description}</div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Low Priority Actions */}
          {lowPriorityActions.length > 0 && (
            <div className="bg-blue-900/20 rounded-lg p-4 border border-blue-800/30">
              <h3 className="text-md font-medium mb-3 flex items-center">
                <CheckCircle size={16} className="text-blue-400 mr-2" />
                {t('incubator.analytics.recommendations.priority')} {t('incubator.analytics.recommendations.low')}
              </h3>

              <div className="space-y-3">
                {lowPriorityActions.map((action, index) => (
                  <div key={index} className="bg-indigo-950/50 rounded-lg p-3 border border-indigo-800/50">
                    <div className="flex items-center mb-1">
                      {getActionIcon(action.type)}
                      <h4 className="font-medium ml-2">{action.title}</h4>
                    </div>
                    <div className="text-sm text-gray-400 ml-6">{action.description}</div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* No Actions */}
          {actions.length === 0 && (
            <div className="text-center py-4 text-gray-400">
              <p>{t('incubator.analytics.recommendations.noRecommendations')}</p>
              <p className="text-sm mt-1">{t('incubator.analytics.recommendations.keepUpGoodWork')}</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default RecommendedActionsSection;
