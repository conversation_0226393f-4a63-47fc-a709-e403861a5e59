import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { fetchUserReputation, fetchReputationActivities } from '../store/forumSlice';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import {
  Award,
  ArrowLeft,
  MessageSquare,
  CheckCircle,
  ThumbsUp,
  Shield,
  Users,
  Activity,
  TrendingUp,
  Clock
} from 'lucide-react';
import { format } from 'date-fns';
import { useTranslation } from 'react-i18next';

const UserReputationPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const dispatch = useAppDispatch();
  const { userReputation, reputationActivities, loading, error } = useAppSelector((state) => state.forum);
  const { user } = useAppSelector((state) => state.auth);

  useEffect(() => {
    dispatch(fetchUserReputation());
    dispatch(fetchReputationActivities());
  }, [dispatch]);

  // Function to get level color
  const getLevelColor = (level: string) => {
    switch (level) {
      case 'Newcomer':
        return 'from-gray-600 to-gray-400';
      case 'Contributor':
        return 'from-blue-600 to-blue-400';
      case 'Active Member':
        return 'from-green-600 to-green-400';
      case 'Expert':
        return 'from-purple-600 to-purple-400';
      case 'Mentor':
        return 'from-yellow-600 to-yellow-400';
      case 'Community Leader':
        return 'from-red-600 to-red-400';
      default:
        return 'from-gray-600 to-gray-400';
    }
  };

  // Function to get activity icon
  const getActivityIcon = (activityType: string) => {
    switch (activityType) {
      case 'thread_created':
        return <MessageSquare size={16} className="text-blue-400" />;
      case 'post_created':
        return <MessageSquare size={16} className="text-purple-400" />;
      case 'solution_provided':
        return <CheckCircle size={16} className="text-green-400" />;
      case 'like_received':
        return <ThumbsUp size={16} className="text-yellow-400" />;
      case 'like_given':
        return <ThumbsUp size={16} className="text-gray-400" />;
      case 'moderation_action':
        return <Shield size={16} className="text-red-400" />;
      default:
        return <Activity size={16} className="text-gray-400" />;
    }
  };

  // Calculate next level requirements
  const getNextLevelInfo = () => {
    if (!userReputation) return null;

    const currentPoints = userReputation.points;
    let nextLevel = '';
    let pointsNeeded = 0;

    if (currentPoints < 10) {
      nextLevel = t("common.contributor", "Contributor");
      pointsNeeded = 10 - currentPoints;
    } else if (currentPoints < 50) {
      nextLevel = t("common.active.member", "Active Member");
      pointsNeeded = 50 - currentPoints;
    } else if (currentPoints < 200) {
      nextLevel = t("common.expert", "Expert");
      pointsNeeded = 200 - currentPoints;
    } else if (currentPoints < 500) {
      nextLevel = t("common.mentor", "Mentor");
      pointsNeeded = 500 - currentPoints;
    } else if (currentPoints < 1000) {
      nextLevel = t("common.community.leader", "Community Leader");
      pointsNeeded = 1000 - currentPoints;
    } else {
      return null; // Already at max level
    }

    return { nextLevel, pointsNeeded };
  };

  const nextLevelInfo = getNextLevelInfo();

  return (
    <div className={`min-h-screen flex flex-col ${isRTL ? "flex-row-reverse" : ""}`}>
      <Navbar />

      <main className={`flex-grow container mx-auto px-4 py-8 mt-16 ${isRTL ? "flex-row-reverse" : ""}`}>
        {/* Breadcrumb */}
        <div className="mb-6">
          <Link to="/forum" className={`text-purple-400 hover:text-purple-300 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <ArrowLeft size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
            Back to Forums
          </Link>
        </div>

        <h1 className={`text-3xl font-bold mb-8 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
          <Award size={28} className={`mr-3 text-yellow-400 ${isRTL ? "space-x-reverse" : ""}`} />
          Your Reputation
        </h1>

        {loading && !userReputation ? (
          <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
            <div className={`flex justify-center items-center h-40 ${isRTL ? "flex-row-reverse" : ""}`}>
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
            </div>
          </div>
        ) : error ? (
          <div className="bg-red-900/30 border border-red-800 rounded-lg p-4 mb-6">
            <div className="text-red-300">{error}</div>
          </div>
        ) : userReputation ? (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-1 space-y-6">
              {/* Reputation Card */}
              <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
                <div className={`flex flex-col items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className={`w-20 h-20 rounded-full bg-indigo-800/50 flex items-center justify-center mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <span className="text-2xl font-bold">{user?.username?.charAt(0).toUpperCase()}</span>
                  </div>
                  <h2 className="text-xl font-semibold">{user?.username}</h2>
                  <div className={`text-sm px-3 py-1 rounded-full bg-gradient-to-r ${getLevelColor(userReputation.level)} mt-2`}>
                    {userReputation.level}
                  </div>
                  <div className="text-3xl font-bold mt-4">{userReputation.points}</div>
                  <div className="text-sm text-gray-400">t("common.reputation.points", "Reputation Points")</div>
                </div>

                {nextLevelInfo && (
                  <div className="mt-6 pt-6 border-t border-indigo-800/50">
                    <h3 className="text-sm font-medium text-gray-300 mb-2">t("common.next.level", "Next Level")</h3>
                    <div className={`flex items-center justify-between mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <span>{nextLevelInfo.nextLevel}</span>
                      <span>{nextLevelInfo.pointsNeeded} points needed</span>
                    </div>
                    <div className="w-full bg-indigo-800/50 rounded-full h-2.5">
                      <div
                        className="bg-gradient-to-r from-purple-600 to-blue-600 h-2.5 rounded-full"
                        style={{
                          width: `${Math.min(100, (userReputation.points / (userReputation.points + nextLevelInfo.pointsNeeded)) * 100)}%`
                        }}
                      ></div>
                    </div>
                  </div>
                )}
              </div>

              {/* Stats Card */}
              <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
                <h3 className="text-lg font-semibold mb-4">t("common.contribution.stats", "Contribution Stats")</h3>
                <div className="space-y-4">
                  <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                      <MessageSquare size={18} className={`text-blue-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                      <span>t("common.threads.created", "Threads Created")</span>
                    </div>
                    <span className="font-medium">{userReputation.threads_created}</span>
                  </div>
                  <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                      <MessageSquare size={18} className={`text-purple-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                      <span>t("common.posts.created", "Posts Created")</span>
                    </div>
                    <span className="font-medium">{userReputation.posts_created}</span>
                  </div>
                  <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                      <CheckCircle size={18} className={`text-green-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                      <span>t("common.solutions.provided", "Solutions Provided")</span>
                    </div>
                    <span className="font-medium">{userReputation.solutions_provided}</span>
                  </div>
                  <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                      <ThumbsUp size={18} className={`text-yellow-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                      <span>t("common.likes.received", "Likes Received")</span>
                    </div>
                    <span className="font-medium">{userReputation.likes_received}</span>
                  </div>
                </div>
              </div>

              {/* Reputation Levels Info */}
              <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
                <h3 className="text-lg font-semibold mb-4">t("common.reputation.levels", "Reputation Levels")</h3>
                <div className="space-y-3 text-sm">
                  <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className={`w-3 h-3 rounded-full bg-gradient-to-r from-gray-600 to-gray-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`}></div>
                    <span>Newcomer (0-9 points)</span>
                  </div>
                  <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className={`w-3 h-3 rounded-full bg-gradient-to-r from-blue-600 to-blue-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`}></div>
                    <span>Contributor (10-49 points)</span>
                  </div>
                  <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className={`w-3 h-3 rounded-full bg-gradient-to-r from-green-600 to-green-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`}></div>
                    <span>Active Member (50-199 points)</span>
                  </div>
                  <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className={`w-3 h-3 rounded-full bg-gradient-to-r from-purple-600 to-purple-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`}></div>
                    <span>Expert (200-499 points)</span>
                  </div>
                  <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className={`w-3 h-3 rounded-full bg-gradient-to-r from-yellow-600 to-yellow-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`}></div>
                    <span>Mentor (500-999 points)</span>
                  </div>
                  <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className={`w-3 h-3 rounded-full bg-gradient-to-r from-red-600 to-red-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`}></div>
                    <span>Community Leader (1000+ points)</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="lg:col-span-2">
              {/* Recent Activity */}
              <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
                <h3 className={`text-lg font-semibold mb-4 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                  <Activity size={20} className={`mr-2 text-purple-400 ${isRTL ? "space-x-reverse" : ""}`} />
                  Recent Reputation Activity
                </h3>

                {reputationActivities.length === 0 ? (
                  <div className="text-center py-8">
                    <div className="text-gray-400">t("common.no.reputation.activity", "No reputation activity yet. Start participating in the forums to earn reputation!")</div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {reputationActivities.map((activity) => (
                      <div key={activity.id} className={`flex items-start p-3 rounded-lg bg-indigo-800/20 hover:bg-indigo-800/30 transition-colors ${isRTL ? "flex-row-reverse" : ""}`}>
                        <div className={`mr-3 mt-1 ${isRTL ? "space-x-reverse" : ""}`}>
                          {getActivityIcon(activity.activity_type)}
                        </div>
                        <div className={`flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                          <div className="text-sm">{activity.description}</div>
                          <div className={`flex items-center mt-1 text-xs text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                            <Clock size={12} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                            <span>{format(new Date(activity.created_at), 'PPp')}</span>
                          </div>
                        </div>
                        <div className={`px-2 py-1 rounded text-xs font-medium ${
                          activity.points > 0 ? 'bg-green-900/30 text-green-400' :
                          activity.points < 0 ? 'bg-red-900/30 text-red-400' :
                          'bg-gray-900/30 text-gray-400'}
                        }`}>
                          {activity.points > 0 ? `+${activity.points}` : activity.points}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50 text-center">
            <p className="text-gray-400">t("common.reputation.data.not", "Reputation data not found. Please try again later.")</p>
          </div>
        )}
      </main>

      <Footer />
    </div>
  );
};

export default UserReputationPage;
