import React, { useState } from 'react';
import { Check, X, <PERSON><PERSON><PERSON><PERSON>gle, Trash2, CheckSquare, Info } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';
import { getButtonClass } from '../../../utils/themeUtils';
interface BulkAction {
  id: string;
  label: string;
  icon?: React.ReactNode;
  action: (ids: number[]) => Promise<void>;
  variant?: 'primary' | 'danger' | 'warning' | 'success';
  disabled?: boolean;
  confirmRequired?: boolean;
  confirmMessage?: string;
}

interface BulkActionsProps {
  selectedCount: number;
  actions: {
    label: string;
    value: string;
    icon: React.ComponentType<any>;
  }[];
  onActionSelect: (action: string) => void;
  isSubmitting?: boolean;
}

const BulkActions: React.FC<BulkActionsProps> = ({ selectedCount,
  actions,
  onActionSelect,
  isSubmitting = false,
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const [currentAction, setCurrentAction] = useState<BulkAction | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingStatus, setProcessingStatus] = useState<{
    success: number;
    failed: number;
    total: number;
    inProgress: boolean;
    message: string;
  }>({
    success: 0,
    failed: 0,
    total: 0,
    inProgress: false,
    message: '',
  });

  if (selectedCount === 0) {
    return null;
  }

  // Use centralized button styling utility

  const handleActionClick = (actionValue: string) => {
    const action = actions.find(a => a.value === actionValue);
    if (!action) return;

    onActionSelect(actionValue);
  };

  return (
    <>
      <div className={`bg-indigo-900/40 backdrop-blur-sm rounded-lg p-3 mb-4 flex flex-col sm:flex-row items-center justify-between gap-3 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
          <CheckSquare size={20} className={`mr-2 text-indigo-400 ${isRTL ? "space-x-reverse" : ""}`} />
          <span className="font-medium">
            {t('admin.bulkActions.itemsSelected', { count: selectedCount })}
          </span>
        </div>

        <div className={`flex flex-wrap gap-2 justify-end ${isRTL ? "flex-row-reverse" : ""}`}>
          {actions.map((action) => (
            <button
              key={action.value}
              onClick={() => handleActionClick(action.value)}
              disabled={isSubmitting}
              className={`${getButtonClass(
                action.value === 'delete' ? 'danger' :
                action.value === 'approve' ? 'success' :
                action.value === 'reject' ? 'warning' : 'primary',
                'sm'
              )} ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              {action.icon && <span className={`mr-1.5 ${isRTL ? "space-x-reverse" : ""}`}><action.icon size={16} /></span>}
              {action.label}
            </button>
          ))}
        </div>
      </div>
    </>
  );
};

// Predefined bulk actions for different content types
export const getPostBulkActions = (
  selectedIds: number[],
  onApprove: (ids: number[]) => Promise<void>,
  onReject: (ids: number[]) => Promise<void>,
  onDelete: (ids: number[]) => Promise<void>,
  t: any
) => [
  {
    label: t('admin.bulkActions.approveSelected'),
    value: 'approve',
    icon: Check,
  },
  {
    label: t('admin.bulkActions.rejectSelected'),
    value: 'reject',
    icon: X,
  },
  {
    label: t('admin.bulkActions.deleteSelected'),
    value: 'delete',
    icon: Trash2,
  },
];

export const getEventBulkActions = (
  selectedIds: number[],
  onApprove: (ids: number[]) => Promise<void>,
  onReject: (ids: number[]) => Promise<void>,
  onDelete: (ids: number[]) => Promise<void>,
  t: any
) => [
  {
    label: t('admin.bulkActions.approveSelected'),
    value: 'approve',
    icon: Check,
  },
  {
    label: t('admin.bulkActions.rejectSelected'),
    value: 'reject',
    icon: X,
  },
  {
    label: t('admin.bulkActions.deleteSelected'),
    value: 'delete',
    icon: Trash2,
  },
];

export const getUserBulkActions = (
  selectedIds: number[],
  onMakeAdmin: (ids: number[]) => Promise<void>,
  onRemoveAdmin: (ids: number[]) => Promise<void>,
  onDelete: (ids: number[]) => Promise<void>,
  t: any
) => [
  {
    label: t('admin.bulkActions.makeAdmin'),
    value: 'make_admin',
    icon: Check,
  },
  {
    label: t('admin.bulkActions.removeAdmin'),
    value: 'remove_admin',
    icon: X,
  },
  {
    label: t('admin.bulkActions.deleteSelected'),
    value: 'delete',
    icon: Trash2,
  },
];

export default BulkActions;
