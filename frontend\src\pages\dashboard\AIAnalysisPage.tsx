/**
 * AI Analysis Page
 * Shows user's AI analysis and insights in the dashboard
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  BarChart3,
  TrendingUp,
  Target,
  AlertTriangle,
  PieChart,
  Activity,
  Calendar,
  Filter,
  Download,
  RefreshCw
} from 'lucide-react';
// AuthenticatedLayout removed - page is already wrapped by route layout
import { useLanguage } from '../../hooks/useLanguage';
import { getAuthToken } from '../../services/api';
import { useCentralizedAI } from '../../hooks/useCentralizedAI';

const AIAnalysisPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [timeRange, setTimeRange] = useState<string>('7d');

  const { status, isStatusLoading, statusError, refreshStatus } = useCentralizedAI();

  // Mock stats for compatibility
  const stats = {
    ideas_enhanced: 5,
    opportunities_found: 8,
    total_actions_today: 12
  };

  // Fetch real analysis data
  const [analysisData, setAnalysisData] = useState({
    performance: {
      ideas_analyzed: 0,
      opportunities_found: 0,
      risks_identified: 0,
      recommendations_generated: 0
    },
    trends: [],
    categories: []
  });

  useEffect(() => {
    fetchAnalysisData();
  }, []);

  const fetchAnalysisData = async () => {
    try {
      const response = await fetch('/api/ai/analysis-dashboard/', {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setAnalysisData(data.analysis || {
          performance: {
            ideas_analyzed: stats?.ideas_enhanced || 0,
            opportunities_found: stats?.opportunities_found || 0,
            risks_identified: 0,
            recommendations_generated: stats?.total_actions_today || 0
          },
          trends: [],
          categories: []
        });
      } else {
        console.error('Failed to fetch analysis data:', response.status);
      }
    } catch (error) {
      console.error('Failed to fetch analysis data:', error);
    }
  };

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-glass-primary">
              📊 {t('ai.analysis.title', 'AI Analysis & Insights')}
            </h1>
            <p className="mt-1 text-glass-secondary">
              {t('ai.analysis.subtitle', 'Deep insights and analytics from your AI assistant')}
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="glass-light rounded-lg px-3 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-glass-primary"
            >
              <option value="7d">{t('ai.timeRange.week', 'Last 7 days')}</option>
              <option value="30d">{t('ai.timeRange.month', 'Last 30 days')}</option>
              <option value="90d">{t('ai.timeRange.quarter', 'Last 3 months')}</option>
            </select>
            <button
              onClick={refreshStatus}
              className="glass-accent p-2 rounded-lg transition-all duration-300 hover:scale-105 text-white"
            >
              <RefreshCw className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Performance Overview */}
        <div className="glass-light border rounded-lg">
          <div className="p-6">
            <h2 className="text-lg font-semibold mb-4 text-glass-primary">
              {t('ai.analysis.performance', 'AI Performance Overview')}
            </h2>

            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <BarChart3 className="h-6 w-6 text-indigo-500" />
                </div>
                <div className="text-2xl font-bold text-glass-primary">
                  {analysisData.performance.ideas_analyzed}
                </div>
                <div className="text-xs text-glass-secondary">
                  {t('ai.analysis.ideasAnalyzed', 'Ideas Analyzed')}
                </div>
              </div>

              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <Target className="h-6 w-6 text-green-500" />
                </div>
                <div className="text-2xl font-bold text-green-600">
                  {analysisData.performance.opportunities_found}
                </div>
                <div className="text-xs text-glass-secondary">
                  {t('ai.analysis.opportunitiesFound', 'Opportunities Found')}
                </div>
              </div>

              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <AlertTriangle className="h-6 w-6 text-red-500" />
                </div>
                <div className="text-2xl font-bold text-red-600">
                  {analysisData.performance.risks_identified}
                </div>
                <div className="text-xs text-glass-secondary">
                  {t('ai.analysis.risksIdentified', 'Risks Identified')}
                </div>
              </div>

              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <Activity className="h-6 w-6 text-purple-500" />
                </div>
                <div className="text-2xl font-bold text-purple-600">
                  {analysisData.performance.recommendations_generated}
                </div>
                <div className="text-xs text-glass-secondary">
                  {t('ai.analysis.recommendationsGenerated', 'Recommendations Generated')}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Trends and Categories */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Trends */}
          <div className="glass-light border rounded-lg">
            <div className="p-6">
              <h3 className="text-lg font-semibold mb-4 text-glass-primary">
                {t('ai.analysis.trends', 'AI Activity Trends')}
              </h3>

              <div className="space-y-4">
                {analysisData.trends.map((trend, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 rounded-lg glass-light"
                  >
                    <div>
                      <div className="font-medium text-glass-primary">
                        {trend.period}
                      </div>
                      <div className="text-sm text-glass-secondary">
                        {trend.value} actions
                      </div>
                    </div>
                    <span className="text-green-500 font-medium">{trend.change}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Categories */}
          <div className="glass-light border rounded-lg">
            <div className="p-6">
              <h3 className="text-lg font-semibold mb-4 text-glass-primary">
                {t('ai.analysis.categories', 'Analysis Categories')}
              </h3>

              <div className="space-y-3">
                {analysisData.categories.map((category, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="text-sm font-medium text-glass-primary">
                        {category.name}
                      </div>
                      <div className="text-sm text-glass-secondary">
                        {category.count} ({category.percentage}%)
                      </div>
                    </div>
                    <div className="w-full bg-slate-700 rounded-full h-2">
                      <div
                        className="bg-indigo-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${category.percentage}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Coming Soon */}
        <div className="glass-light border rounded-lg">
          <div className="p-6 text-center">
            <PieChart className="h-12 w-12 text-indigo-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2 text-glass-primary">
              {t('ai.analysis.comingSoon', 'Advanced Analytics Coming Soon')}
            </h3>
            <p className="mb-4 text-glass-secondary">
              {t('ai.analysis.comingSoonDesc', 'We\'re working on advanced charts, predictive analytics, and detailed insights.')}
            </p>
          </div>
        </div>
      </div>
              </div>
        </div>
      </div>
    </div>
  );
};

export default AIAnalysisPage;
