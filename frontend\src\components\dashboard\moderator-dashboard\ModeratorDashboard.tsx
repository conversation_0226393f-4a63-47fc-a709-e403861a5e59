import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from '../../ui/card';
import Button from '../../ui/Button';
import { Badge } from '../../ui/badge';
import { AlertTriangle, Shield, Users, MessageSquare, Flag, CheckCircle, Clock, TrendingUp, Eye, ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';

interface ModerationStats {
  pendingReports: number;
  flaggedContent: number;
  activeUsers: number;
  totalReports: number;
  resolvedToday: number;
  averageResponseTime: number;
  communityHealth: number;
}

interface RecentActivity {
  id: string;
  type: 'report' | 'content_flagged' | 'user_action' | 'resolution';
  title: string;
  description: string;
  timestamp: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status?: string;
}

interface QuickAction {
  title: string;
  description: string;
  icon: React.ReactNode;
  link: string;
  count?: number;
  urgent?: boolean;
}

const ModeratorDashboard: React.FC = () => {
  const [stats, setStats] = useState<ModerationStats | null>(null);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);

  // Mock data - replace with actual API calls
  useEffect(() => {
    const mockStats: ModerationStats = {
      pendingReports: 12,
      flaggedContent: 8,
      activeUsers: 1247,
      totalReports: 156,
      resolvedToday: 23,
      averageResponseTime: 4.2,
      communityHealth: 94
    };

    const mockActivity: RecentActivity[] = [
      {
        id: '1',
        type: 'report',
        title: 'New spam report',
        description: 'User reported promotional content in business ideas section',
        timestamp: '2024-01-16T10:30:00Z',
        priority: 'high'
      },
      {
        id: '2',
        type: 'content_flagged',
        title: 'Content auto-flagged',
        description: 'AI system flagged potential harassment in forum thread',
        timestamp: '2024-01-16T09:45:00Z',
        priority: 'medium'
      },
      {
        id: '3',
        type: 'user_action',
        title: 'User suspended',
        description: 'Mike Wilson suspended for 7 days due to repeated violations',
        timestamp: '2024-01-16T09:15:00Z',
        priority: 'low',
        status: 'completed'
      },
      {
        id: '4',
        type: 'resolution',
        title: 'Report resolved',
        description: 'Copyright infringement claim dismissed after investigation',
        timestamp: '2024-01-16T08:30:00Z',
        priority: 'low',
        status: 'resolved'
      }
    ];

    setTimeout(() => {
      setStats(mockStats);
      setRecentActivity(mockActivity);
      setLoading(false);
    }, 1000);
  }, []);

  const quickActions: QuickAction[] = [
    {
      title: 'Review Reports',
      description: 'Handle pending user reports',
      icon: <Flag className="w-6 h-6" />,
      link: '/dashboard/moderation/reports',
      count: stats?.pendingReports,
      urgent: (stats?.pendingReports || 0) > 10
    },
    {
      title: 'Content Moderation',
      description: 'Review flagged content',
      icon: <MessageSquare className="w-6 h-6" />,
      link: '/dashboard/moderation/content',
      count: stats?.flaggedContent,
      urgent: (stats?.flaggedContent || 0) > 5
    },
    {
      title: 'User Management',
      description: 'Manage user accounts',
      icon: <Users className="w-6 h-6" />,
      link: '/dashboard/moderation/users'
    },
    {
      title: 'Forum Moderation',
      description: 'Moderate forum discussions',
      icon: <MessageSquare className="w-6 h-6" />,
      link: '/dashboard/moderation/forum'
    },
    {
      title: 'Analytics',
      description: 'View moderation metrics',
      icon: <TrendingUp className="w-6 h-6" />,
      link: '/dashboard/moderation/analytics'
    },
    {
      title: 'System Health',
      description: 'Monitor community health',
      icon: <Shield className="w-6 h-6" />,
      link: '/dashboard/moderation/analytics'
    }
  ];

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'text-red-600';
      case 'high': return 'text-orange-600';
      case 'medium': return 'text-yellow-600';
      case 'low': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'report': return <Flag className="w-4 h-4 text-red-600" />;
      case 'content_flagged': return <AlertTriangle className="w-4 h-4 text-orange-600" />;
      case 'user_action': return <Users className="w-4 h-4 text-blue-600" />;
      case 'resolution': return <CheckCircle className="w-4 h-4 text-green-600" />;
      default: return <Shield className="w-4 h-4 text-gray-600" />;
    }
  };

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Moderator Dashboard</h1>
          <p className="text-gray-600 mt-1">Monitor and manage community content and users</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Eye className="w-4 h-4 mr-2" />
            Live Feed
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700">
            <Shield className="w-4 h-4 mr-2" />
            Emergency Actions
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Pending Reports</p>
                  <p className="text-2xl font-bold text-red-600">{stats.pendingReports}</p>
                  <p className="text-sm text-gray-600">Require attention</p>
                </div>
                <Flag className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Flagged Content</p>
                  <p className="text-2xl font-bold text-orange-600">{stats.flaggedContent}</p>
                  <p className="text-sm text-gray-600">Awaiting review</p>
                </div>
                <AlertTriangle className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Resolved Today</p>
                  <p className="text-2xl font-bold text-green-600">{stats.resolvedToday}</p>
                  <p className="text-sm text-green-600">Great progress!</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Community Health</p>
                  <p className="text-2xl font-bold text-blue-600">{stats.communityHealth}%</p>
                  <p className="text-sm text-blue-600">Excellent</p>
                </div>
                <Shield className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Quick Actions */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {quickActions.map((action, index) => (
                  <Link key={index} to={action.link}>
                    <div className="p-4 border rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-3">
                          <div className="p-2 bg-blue-100 rounded-lg">
                            {action.icon}
                          </div>
                          <div>
                            <h3 className="font-semibold">{action.title}</h3>
                            <p className="text-sm text-gray-600">{action.description}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {action.count !== undefined && (
                            <Badge className={action.urgent ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'}>
                              {action.count}
                            </Badge>
                          )}
                          <ArrowRight className="w-4 h-4 text-gray-400" />
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-start gap-3 p-3 border rounded">
                    {getActivityIcon(activity.type)}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-sm">{activity.title}</h4>
                        <span className={`text-xs ${getPriorityColor(activity.priority)}`}>
                          {activity.priority}
                        </span>
                      </div>
                      <p className="text-xs text-gray-600 mb-2">{activity.description}</p>
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-500">{formatTimeAgo(activity.timestamp)}</span>
                        {activity.status && (
                          <Badge variant="secondary" className="text-xs">
                            {activity.status}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-4">
                <Link to="/dashboard/moderation/reports">
                  <Button variant="outline" className="w-full">
                    View All Activity
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Performance Metrics */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Users</p>
                  <p className="text-2xl font-bold">{stats.activeUsers.toLocaleString()}</p>
                  <p className="text-sm text-green-600">+5% from yesterday</p>
                </div>
                <Users className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Avg Response Time</p>
                  <p className="text-2xl font-bold">{stats.averageResponseTime}h</p>
                  <p className="text-sm text-green-600">-12% improvement</p>
                </div>
                <Clock className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Reports</p>
                  <p className="text-2xl font-bold">{stats.totalReports}</p>
                  <p className="text-sm text-gray-600">This month</p>
                </div>
                <TrendingUp className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default ModeratorDashboard;
