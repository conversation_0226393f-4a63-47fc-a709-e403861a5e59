import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from '../../components/ui/card';
import Button from '../../components/ui/Button';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '../../components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import { BarChart3, TrendingUp, TrendingDown, Shield, AlertTriangle, CheckCircle, Clock, Users } from 'lucide-react';

interface ModerationAnalytics {
  overview: {
    totalReports: number;
    resolvedReports: number;
    pendingReports: number;
    averageResolutionTime: number; // in hours
    moderationAccuracy: number; // percentage
    userSatisfactionRate: number; // percentage
  };
  reportTrends: {
    month: string;
    totalReports: number;
    resolved: number;
    dismissed: number;
    pending: number;
  }[];
  categoryBreakdown: {
    category: string;
    count: number;
    percentage: number;
    avgResolutionTime: number;
  }[];
  moderatorPerformance: {
    moderatorId: string;
    moderatorName: string;
    reportsHandled: number;
    averageResolutionTime: number;
    accuracyRate: number;
    userRating: number;
  }[];
  contentMetrics: {
    totalContent: number;
    flaggedContent: number;
    removedContent: number;
    falsePositives: number;
    contentTypes: {
      type: string;
      total: number;
      flagged: number;
      removed: number;
    }[];
  };
  userBehavior: {
    totalUsers: number;
    activeUsers: number;
    suspendedUsers: number;
    bannedUsers: number;
    repeatOffenders: number;
    topReporters: {
      userId: string;
      userName: string;
      reportsSubmitted: number;
      accuracyRate: number;
    }[];
  };
}

const ModerationAnalyticsPage: React.FC = () => {
  const [analytics, setAnalytics] = useState<ModerationAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeframe, setTimeframe] = useState('3m');
  const [activeTab, setActiveTab] = useState('overview');

  // Mock data - replace with actual API call
  useEffect(() => {
    const mockAnalytics: ModerationAnalytics = {
      overview: {
        totalReports: 156,
        resolvedReports: 134,
        pendingReports: 22,
        averageResolutionTime: 4.2,
        moderationAccuracy: 94.5,
        userSatisfactionRate: 87.3
      },
      reportTrends: [
        { month: 'Oct', totalReports: 45, resolved: 42, dismissed: 8, pending: 3 },
        { month: 'Nov', totalReports: 52, resolved: 48, dismissed: 12, pending: 4 },
        { month: 'Dec', totalReports: 38, resolved: 35, dismissed: 6, pending: 3 },
        { month: 'Jan', totalReports: 21, resolved: 9, dismissed: 0, pending: 12 }
      ],
      categoryBreakdown: [
        { category: 'Spam', count: 45, percentage: 28.8, avgResolutionTime: 2.1 },
        { category: 'Harassment', count: 32, percentage: 20.5, avgResolutionTime: 6.8 },
        { category: 'Inappropriate Content', count: 28, percentage: 17.9, avgResolutionTime: 3.4 },
        { category: 'Misinformation', count: 24, percentage: 15.4, avgResolutionTime: 8.2 },
        { category: 'Copyright', count: 18, percentage: 11.5, avgResolutionTime: 12.5 },
        { category: 'Other', count: 9, percentage: 5.8, avgResolutionTime: 4.7 }
      ],
      moderatorPerformance: [
        {
          moderatorId: 'mod1',
          moderatorName: 'Admin User',
          reportsHandled: 67,
          averageResolutionTime: 3.8,
          accuracyRate: 96.2,
          userRating: 4.7
        },
        {
          moderatorId: 'mod2',
          moderatorName: 'Senior Moderator',
          reportsHandled: 45,
          averageResolutionTime: 4.1,
          accuracyRate: 93.8,
          userRating: 4.5
        },
        {
          moderatorId: 'mod3',
          moderatorName: 'Junior Moderator',
          reportsHandled: 22,
          averageResolutionTime: 5.2,
          accuracyRate: 89.4,
          userRating: 4.2
        }
      ],
      contentMetrics: {
        totalContent: 2847,
        flaggedContent: 156,
        removedContent: 89,
        falsePositives: 12,
        contentTypes: [
          { type: 'Posts', total: 1245, flagged: 67, removed: 34 },
          { type: 'Comments', total: 1089, flagged: 56, removed: 28 },
          { type: 'Business Ideas', total: 345, flagged: 23, removed: 18 },
          { type: 'User Profiles', total: 168, flagged: 10, removed: 9 }
        ]
      },
      userBehavior: {
        totalUsers: 1247,
        activeUsers: 1189,
        suspendedUsers: 34,
        bannedUsers: 12,
        repeatOffenders: 8,
        topReporters: [
          { userId: 'user1', userName: 'John Smith', reportsSubmitted: 23, accuracyRate: 91.3 },
          { userId: 'user2', userName: 'Sarah Johnson', reportsSubmitted: 18, accuracyRate: 94.4 },
          { userId: 'user3', userName: 'Mike Wilson', reportsSubmitted: 15, accuracyRate: 86.7 }
        ]
      }
    };

    setTimeout(() => {
      setAnalytics(mockAnalytics);
      setLoading(false);
    }, 1000);
  }, [timeframe]);

  const formatPercentage = (value: number) => `${value.toFixed(1)}%`;
  const formatHours = (hours: number) => `${hours.toFixed(1)}h`;

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!analytics) return null;

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Moderation Analytics</h1>
          <p className="text-gray-600 mt-1">Track moderation performance and community health</p>
        </div>
        <div className="flex gap-2">
          <Select value={timeframe} onValueChange={setTimeframe}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1m">1 Month</SelectItem>
              <SelectItem value="3m">3 Months</SelectItem>
              <SelectItem value="6m">6 Months</SelectItem>
              <SelectItem value="1y">1 Year</SelectItem>
            </SelectContent>
          </Select>
          <Button className="bg-blue-600 hover:bg-blue-700">
            <BarChart3 className="w-4 h-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Reports</p>
                <p className="text-2xl font-bold">{analytics.overview.totalReports}</p>
                <p className="text-sm text-green-600">
                  {formatPercentage((analytics.overview.resolvedReports / analytics.overview.totalReports) * 100)} resolved
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Resolution Time</p>
                <p className="text-2xl font-bold">{formatHours(analytics.overview.averageResolutionTime)}</p>
                <p className="text-sm text-gray-600">Per report</p>
              </div>
              <Clock className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Moderation Accuracy</p>
                <p className="text-2xl font-bold">{formatPercentage(analytics.overview.moderationAccuracy)}</p>
                <p className="text-sm text-green-600">Excellent</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending Reports</p>
                <p className="text-2xl font-bold text-yellow-600">{analytics.overview.pendingReports}</p>
                <p className="text-sm text-gray-600">Awaiting review</p>
              </div>
              <Clock className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">User Satisfaction</p>
                <p className="text-2xl font-bold">{formatPercentage(analytics.overview.userSatisfactionRate)}</p>
                <p className="text-sm text-green-600">Good rating</p>
              </div>
              <Users className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Content Flagged</p>
                <p className="text-2xl font-bold">{analytics.contentMetrics.flaggedContent}</p>
                <p className="text-sm text-gray-600">
                  {formatPercentage((analytics.contentMetrics.flaggedContent / analytics.contentMetrics.totalContent) * 100)} of total
                </p>
              </div>
              <Shield className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Analytics Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="reports">Report Trends</TabsTrigger>
          <TabsTrigger value="moderators">Moderator Performance</TabsTrigger>
          <TabsTrigger value="content">Content Metrics</TabsTrigger>
          <TabsTrigger value="users">User Behavior</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Report Categories</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analytics.categoryBreakdown.map((category, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium">{category.category}</span>
                        <div className="text-right">
                          <span className="text-sm font-semibold">{category.count}</span>
                          <span className="text-xs text-gray-600 ml-2">({formatPercentage(category.percentage)})</span>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${category.percentage}%` }}
                        ></div>
                      </div>
                      <div className="text-xs text-gray-600">
                        Avg resolution: {formatHours(category.avgResolutionTime)}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Monthly Report Trends</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analytics.reportTrends.map((trend, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded">
                      <div>
                        <span className="font-medium">{trend.month}</span>
                      </div>
                      <div className="grid grid-cols-4 gap-4 text-sm text-center">
                        <div>
                          <p className="text-gray-600">Total</p>
                          <p className="font-semibold">{trend.totalReports}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Resolved</p>
                          <p className="font-semibold text-green-600">{trend.resolved}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Dismissed</p>
                          <p className="font-semibold text-gray-600">{trend.dismissed}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Pending</p>
                          <p className="font-semibold text-yellow-600">{trend.pending}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="reports" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Report Resolution Trends</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-center text-gray-600">Report trends chart would go here</p>
                <div className="h-64 bg-gray-100 rounded flex items-center justify-center">
                  <BarChart3 className="w-16 h-16 text-gray-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-4 text-center">
                <TrendingUp className="w-8 h-8 text-green-600 mx-auto mb-2" />
                <p className="text-2xl font-bold text-green-600">+12%</p>
                <p className="text-sm text-gray-600">Resolution Rate</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <TrendingDown className="w-8 h-8 text-red-600 mx-auto mb-2" />
                <p className="text-2xl font-bold text-red-600">-8%</p>
                <p className="text-sm text-gray-600">Response Time</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <TrendingUp className="w-8 h-8 text-green-600 mx-auto mb-2" />
                <p className="text-2xl font-bold text-green-600">+5%</p>
                <p className="text-sm text-gray-600">User Satisfaction</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="moderators" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Moderator Performance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analytics.moderatorPerformance.map((moderator, index) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="font-semibold">{moderator.moderatorName}</h3>
                      <div className="flex items-center gap-2">
                        <span className="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded">
                          {moderator.reportsHandled} reports
                        </span>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <p className="text-gray-600">Avg Resolution Time</p>
                        <p className="font-semibold">{formatHours(moderator.averageResolutionTime)}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Accuracy Rate</p>
                        <p className="font-semibold">{formatPercentage(moderator.accuracyRate)}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">User Rating</p>
                        <p className="font-semibold">{moderator.userRating}/5.0</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="content" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Content Overview</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Total Content</span>
                  <span className="font-semibold">{analytics.contentMetrics.totalContent.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Flagged Content</span>
                  <span className="font-semibold text-orange-600">{analytics.contentMetrics.flaggedContent}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Removed Content</span>
                  <span className="font-semibold text-red-600">{analytics.contentMetrics.removedContent}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">False Positives</span>
                  <span className="font-semibold text-yellow-600">{analytics.contentMetrics.falsePositives}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Content by Type</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analytics.contentMetrics.contentTypes.map((type, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">{type.type}</span>
                        <span className="text-sm text-gray-600">
                          {type.flagged}/{type.total} flagged
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-orange-600 h-2 rounded-full" 
                          style={{ width: `${(type.flagged / type.total) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="users" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>User Status Overview</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Total Users</span>
                  <span className="font-semibold">{analytics.userBehavior.totalUsers.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Active Users</span>
                  <span className="font-semibold text-green-600">{analytics.userBehavior.activeUsers.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Suspended Users</span>
                  <span className="font-semibold text-orange-600">{analytics.userBehavior.suspendedUsers}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Banned Users</span>
                  <span className="font-semibold text-red-600">{analytics.userBehavior.bannedUsers}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Repeat Offenders</span>
                  <span className="font-semibold text-red-600">{analytics.userBehavior.repeatOffenders}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Top Community Reporters</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analytics.userBehavior.topReporters.map((reporter, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded">
                      <div>
                        <p className="font-medium">{reporter.userName}</p>
                        <p className="text-sm text-gray-600">{reporter.reportsSubmitted} reports</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-semibold">{formatPercentage(reporter.accuracyRate)}</p>
                        <p className="text-xs text-gray-600">accuracy</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ModerationAnalyticsPage;
