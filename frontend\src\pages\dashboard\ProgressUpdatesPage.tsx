import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { useAppSelector } from '../../store/hooks';
import { useCRUD } from '../../hooks/useCRUD';
import { ProgressUpdate, BusinessIdea, progressUpdatesAPI, businessIdeasAPI } from '../../services/incubatorApi';
import ProgressUpdateForm from '../../components/incubator/forms/ProgressUpdateForm';
import { EnhancedCRUDTable } from '../../components/common';
import AuthenticatedLayout from '../../components/layout/AuthenticatedLayout';
import {
  Plus,
  Edit,
  Trash2,
  Eye,
  TrendingUp,
  Calendar,
  AlertCircle,
  CheckCircle,
  Clock,
  Target,
  Lightbulb
} from 'lucide-react';

const ProgressUpdatesPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { user } = useAppSelector(state => state.auth);

  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [selectedUpdate, setSelectedUpdate] = useState<ProgressUpdate | null>(null);
  const [selectedBusinessIdea, setSelectedBusinessIdea] = useState<BusinessIdea | null>(null);
  const [businessIdeas, setBusinessIdeas] = useState<BusinessIdea[]>([]);

  // CRUD operations for progress updates
  const progressUpdatesCRUD = useCRUD({
    create: async (data: Partial<ProgressUpdate>) => {
      if (!user) throw new Error('User not authenticated');
      return progressUpdatesAPI.createProgressUpdate({
        ...data,
        created_by_id: user.id
      });
    },
    read: () => progressUpdatesAPI.getProgressUpdates(),
    update: (id: number, data: Partial<ProgressUpdate>) =>
      progressUpdatesAPI.updateProgressUpdate(id, data),
    delete: (id: number) => progressUpdatesAPI.deleteProgressUpdate(id)
  }, {
    onSuccess: (operation) => {
      if (operation === 'create') {
        setShowCreateForm(false);
        setSelectedBusinessIdea(null);
      } else if (operation === 'update') {
        setShowEditForm(false);
        setSelectedUpdate(null);
      } else if (operation === 'delete') {
        setShowDeleteDialog(false);
        setSelectedUpdate(null);
      }
    }
  });

  // Load data on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        const [updates, ideas] = await Promise.all([
          progressUpdatesAPI.getProgressUpdates(),
          businessIdeasAPI.getBusinessIdeas()
        ]);

        progressUpdatesCRUD.setData(updates);
        setBusinessIdeas(ideas.filter(idea => idea.owner.id === user?.id));
      } catch (error) {
        console.error('Error loading data:', error);
      }
    };

    loadData();
  }, [user?.id]);

  // Filter to show only user's updates
  const userProgressUpdates = progressUpdatesCRUD.data.filter(update =>
    update.created_by.id === user?.id
  );

  // Handle create
  const handleCreate = (businessIdea?: BusinessIdea) => {
    setSelectedBusinessIdea(businessIdea || null);
    setShowCreateForm(true);
  };

  // Handle create submission
  const handleCreateSubmit = async (data: Partial<ProgressUpdate>) => {
    return await progressUpdatesCRUD.createItem(data);
  };

  // Handle edit
  const handleEdit = (update: ProgressUpdate) => {
    setSelectedUpdate(update);
    setShowEditForm(true);
  };

  // Handle update
  const handleUpdate = async (data: Partial<ProgressUpdate>) => {
    if (!selectedUpdate) return false;
    return await progressUpdatesCRUD.updateItem(selectedUpdate.id, data);
  };

  // Handle delete
  const handleDelete = (update: ProgressUpdate) => {
    setSelectedUpdate(update);
    setShowDeleteDialog(true);
  };

  // Confirm delete
  const confirmDelete = async () => {
    if (!selectedUpdate) return;
    await progressUpdatesCRUD.deleteItem(selectedUpdate.id);
  };

  // Table columns configuration
  const columns = [
    {
      key: 'title',
      label: t('incubator.title', 'Title'),
      render: (update: ProgressUpdate) => (
        <div>
          <div className="font-medium text-white">{update.title}</div>
          <div className="text-sm text-gray-400 truncate max-w-xs">
            {update.description}
          </div>
        </div>
      )
    },
    {
      key: 'business_idea_title',
      label: t('incubator.businessIdea', 'Business Idea'),
      render: (update: ProgressUpdate) => (
        <div className="flex items-center gap-2">
          <Lightbulb size={16} className="text-purple-400" />
          <span className="text-gray-300">{update.business_idea_title}</span>
        </div>
      )
    },
    {
      key: 'achievements',
      label: t('incubator.achievements', 'Achievements'),
      render: (update: ProgressUpdate) => (
        <div className="text-sm text-gray-300 truncate max-w-xs">
          {update.achievements}
        </div>
      )
    },
    {
      key: 'created_at',
      label: t('common.created', 'Created'),
      render: (update: ProgressUpdate) => (
        <span className="text-gray-400 text-sm">
          {new Date(update.created_at).toLocaleDateString()}
        </span>
      )
    }
  ];

  // Table actions
  const actions = [
    {
      label: t('common.view', 'View'),
      icon: Eye,
      onClick: (update: ProgressUpdate) => {
        // Navigate to update detail page or show modal
        console.log('View update:', update);
      },
      variant: 'secondary' as const
    },
    {
      label: t('common.edit', 'Edit'),
      icon: Edit,
      onClick: handleEdit,
      variant: 'primary' as const
    },
    {
      label: t('common.delete', 'Delete'),
      icon: Trash2,
      onClick: handleDelete,
      variant: 'danger' as const
    }
  ];

  // Stats cards data
  const stats = [
    {
      title: t('incubator.totalUpdates', 'Total Updates'),
      value: userProgressUpdates.length,
      icon: TrendingUp,
      color: 'blue'
    },
    {
      title: t('incubator.thisMonth', 'This Month'),
      value: userProgressUpdates.filter(update => {
        const updateDate = new Date(update.created_at);
        const now = new Date();
        return updateDate.getMonth() === now.getMonth() && updateDate.getFullYear() === now.getFullYear();
      }).length,
      icon: Calendar,
      color: 'green'
    },
    {
      title: t('incubator.activeIdeas', 'Active Ideas'),
      value: businessIdeas.length,
      icon: Lightbulb,
      color: 'purple'
    },
    {
      title: t('incubator.avgPerIdea', 'Avg per Idea'),
      value: businessIdeas.length > 0 ? Math.round(userProgressUpdates.length / businessIdeas.length * 10) / 10 : 0,
      icon: Target,
      color: 'yellow'
    }
  ];

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="space-y-6">
        {/* Header */}
        <div className={`flex flex-col sm:flex-row sm:items-center sm:justify-between ${isRTL ? 'sm:flex-row-reverse' : ''}`}>
          <div>
            <h1 className="text-2xl font-bold text-white">
              {t('incubator.progressUpdates.title', 'Progress Updates')}
            </h1>
            <p className="text-gray-400 mt-1">
              {t('incubator.trackYourProgress', 'Track your business idea progress')}
            </p>
          </div>
          <div className={`flex gap-2 mt-4 sm:mt-0 ${isRTL ? 'flex-row-reverse' : ''}`}>
            {businessIdeas.length > 0 && (
              <select
                onChange={(e) => {
                  const ideaId = parseInt(e.target.value);
                  const idea = businessIdeas.find(idea => idea.id === ideaId);
                  handleCreate(idea);
                }}
                className="px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                defaultValue=""
              >
                <option value="" disabled>
                  {t('incubator.selectIdeaToUpdate', 'Select idea to update')}
                </option>
                {businessIdeas.map(idea => (
                  <option key={idea.id} value={idea.id}>
                    {idea.title}
                  </option>
                ))}
              </select>
            )}
            <button
              onClick={() => handleCreate()}
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center gap-2"
            >
              <Plus size={20} />
              {t('incubator.createUpdate', 'Create Update')}
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <div key={index} className="bg-gray-800 rounded-lg p-6">
              <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div>
                  <p className="text-gray-400 text-sm">{stat.title}</p>
                  <p className="text-2xl font-bold text-white mt-1">{stat.value}</p>
                </div>
                <div className={`p-3 rounded-lg ${
                  stat.color === 'blue' ? 'bg-blue-900/50' :
                  stat.color === 'green' ? 'bg-green-900/50' :
                  stat.color === 'yellow' ? 'bg-yellow-900/50' :
                  'bg-purple-900/50'
                }`}>
                  <stat.icon size={24} className={
                    stat.color === 'blue' ? 'text-blue-400' :
                    stat.color === 'green' ? 'text-green-400' :
                    stat.color === 'yellow' ? 'text-yellow-400' :
                    'text-purple-400'
                  } />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Error Display */}
        {progressUpdatesCRUD.error && (
          <div className="bg-red-900/50 border border-red-500 rounded-lg p-4">
            <div className="flex items-center gap-2">
              <AlertCircle size={20} className="text-red-400" />
              <span className="text-red-200">{progressUpdatesCRUD.error}</span>
            </div>
          </div>
        )}

        {/* No Business Ideas Warning */}
        {businessIdeas.length === 0 && (
          <div className="bg-yellow-900/50 border border-yellow-500 rounded-lg p-4">
            <div className="flex items-center gap-2">
              <AlertCircle size={20} className="text-yellow-400" />
              <span className="text-yellow-200">
                {t('incubator.noBusinessIdeasWarning', 'You need to create a business idea first before adding progress updates.')}
              </span>
            </div>
          </div>
        )}

        {/* Progress Updates Table */}
        <div className="bg-gray-800 rounded-lg">
          <CRUDTable
            data={userProgressUpdates}
            columns={columns}
            actions={actions}
            isLoading={progressUpdatesCRUD.isLoading}
            emptyMessage={t('incubator.progressUpdates.noUpdates', 'No progress updates found. Create your first update to track your progress!')}
            searchPlaceholder={t('incubator.searchUpdates', 'Search your progress updates...')}
            title={t('incubator.progressUpdates.title', 'Progress Updates')}
            createButtonLabel={t('incubator.createUpdate', 'Create Update')}
            onCreate={() => handleCreate()}
          />
        </div>
      </div>

      {/* Create Form Modal */}
      {showCreateForm && (
        <ProgressUpdateForm
          mode="create"
          businessIdea={selectedBusinessIdea || undefined}
          onSubmit={handleCreateSubmit}
          onCancel={() => {
            setShowCreateForm(false);
            setSelectedBusinessIdea(null);
          }}
          isSubmitting={progressUpdatesCRUD.isLoading}
        />
      )}

      {/* Edit Form Modal */}
      {showEditForm && selectedUpdate && (
        <ProgressUpdateForm
          mode="edit"
          initialData={selectedUpdate}
          onSubmit={handleUpdate}
          onCancel={() => {
            setShowEditForm(false);
            setSelectedUpdate(null);
          }}
          isSubmitting={progressUpdatesCRUD.isLoading}
        />
      )}

      {/* Delete Confirmation Dialog */}
      {showDeleteDialog && selectedUpdate && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-gray-900 rounded-lg shadow-xl w-full max-w-md">
            <div className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <AlertCircle size={24} className="text-red-400" />
                <h3 className="text-lg font-semibold text-white">
                  {t('common.confirmDelete', 'Confirm Delete')}
                </h3>
              </div>
              <p className="text-gray-300 mb-6">
                {t('incubator.deleteUpdateConfirmation', 'Are you sure you want to delete this progress update? This action cannot be undone.')}
              </p>
              <div className={`flex gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <button
                  onClick={() => {
                    setShowDeleteDialog(false);
                    setSelectedUpdate(null);
                  }}
                  className="flex-1 px-4 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors"
                  disabled={progressUpdatesCRUD.isLoading}
                >
                  {t('common.cancel', 'Cancel')}
                </button>
                <button
                  onClick={confirmDelete}
                  className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
                  disabled={progressUpdatesCRUD.isLoading}
                >
                  {progressUpdatesCRUD.isLoading ? t('common.deleting', 'Deleting...') : t('common.delete', 'Delete')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
              </div>
        </div>
      </div>
    </AuthenticatedLayout>
  );
};

export default ProgressUpdatesPage;
