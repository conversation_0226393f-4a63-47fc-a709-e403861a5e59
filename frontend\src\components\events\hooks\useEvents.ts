import { useState, useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '../../../store/hooks';
import { fetchEvents, attendEvent, unattendEvent, createEvent } from '../../../store/eventsSlice';
import { createMockEvents } from '../utils';
import { Event } from '../../../services/api';

export const useEvents = () => {
  const dispatch = useAppDispatch();
  const { events, isLoading, error } = useAppSelector(state => state.events);
  const { isAuthenticated, user } = useAppSelector(state => state.auth);
  const [mockData, setMockData] = useState<Event[]>([]);

  // Fetch events from API
  useEffect(() => {
    dispatch(fetchEvents());
  }, [dispatch]);

  // Ensure events is always an array
  const eventsArray = Array.isArray(events) ? events : [];

  // Handle attend event
  const handleAttendEvent = async (eventId: number) => {
    if (!isAuthenticated) {
      alert('Please log in to attend events');
      return;
    }

    try {
      await dispatch(attendEvent(eventId));
    } catch (err) {
      console.error('Failed to attend event:', err);
      alert('Failed to attend event. Please try again.');
    }
  };

  // Handle unattend event
  const handleUnattendEvent = async (eventId: number) => {
    try {
      await dispatch(unattendEvent(eventId));
    } catch (err) {
      console.error('Failed to unattend event:', err);
      alert('Failed to cancel attendance. Please try again.');
    }
  };

  // Load mock data
  const loadMockData = () => {
    const mockEvents = createMockEvents();
    setMockData(mockEvents);
    return mockEvents;
  };

  return {
    events: eventsArray,
    mockData,
    isLoading,
    error,
    isAuthenticated,
    user,
    handleAttendEvent,
    handleUnattendEvent,
    loadMockData
  };
};
