import React from 'react';
import { Plus } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { RTLText, RTLIcon } from '../../common';
import EventForm, { EventFormData } from './EventForm';

import { useLanguageDirection } from "../../../hooks/useLanguageDirection";
interface CreateEventSectionProps {
  isAuthenticated: boolean;
  showEventForm: boolean;
  formData: EventFormData;
  onInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  onSubmit: (e: React.FormEvent) => void;
  onShowForm: () => void;
  onHideForm: () => void;
}

const CreateEventSection: React.FC<CreateEventSectionProps> = ({ isAuthenticated,
  showEventForm,
  formData,
  onInputChange,
  onSubmit,
  onShowForm,
  onHideForm
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="mt-16 text-center">
      {showEventForm ? (
        <EventForm 
          formData={formData}
          onInputChange={onInputChange}
          onSubmit={onSubmit}
          onCancel={onHideForm}
        />
      ) : (
        <div className="bg-gradient-to-r from-purple-900/20 to-blue-900/20 rounded-xl p-8 backdrop-blur-sm inline-block">
          <RTLText as="h3" align="center" className="text-2xl font-semibold mb-4">
            {t('home.events.hostYourOwnEvent')}
          </RTLText>
          <RTLText as="p" align="center" className="text-gray-300 mb-6 max-w-2xl mx-auto">
            {t('home.events.hostDescription')}
          </RTLText>
          <button
            onClick={onShowForm}
            className={`px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full font-medium hover:shadow-glow transition-all duration-300 flex items-center justify-center mx-auto ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <RTLIcon icon={Plus} size={18} />
            {t('home.events.createNewEvent')}
          </button>
        </div>
      )}
    </div>
  );
};

export default CreateEventSection;
