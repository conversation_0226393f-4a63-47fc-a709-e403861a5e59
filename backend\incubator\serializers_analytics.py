from rest_framework import serializers
from django.contrib.auth.models import User
from .models_analytics import (
    PredictiveAnalytics, ComparativeAnalytics, 
    AnalyticsSnapshot, MetricDefinition
)
from .models_milestone import BusinessAnalytics
from .models_base import BusinessIdea
from users.serializers import UserSerializer

class PredictiveAnalyticsSerializer(serializers.ModelSerializer):
    """Serializer for predictive analytics"""
    
    business_idea_title = serializers.SerializerMethodField()
    
    class Meta:
        model = PredictiveAnalytics
        fields = [
            'id', 'business_idea', 'business_idea_title', 'growth_predictions',
            'milestone_predictions', 'success_probability', 'risk_factors',
            'opportunity_areas', 'prediction_confidence', 'last_calculated'
        ]
        read_only_fields = ['id', 'last_calculated']
    
    def get_business_idea_title(self, obj):
        return obj.business_idea.title


class ComparativeAnalyticsSerializer(serializers.ModelSerializer):
    """Serializer for comparative analytics"""
    
    business_idea_title = serializers.SerializerMethodField()
    
    class Meta:
        model = ComparativeAnalytics
        fields = [
            'id', 'business_idea', 'business_idea_title', 'similar_ideas',
            'industry_averages', 'stage_averages', 'percentile_rankings',
            'competitive_advantages', 'competitive_disadvantages', 'last_calculated'
        ]
        read_only_fields = ['id', 'last_calculated']
    
    def get_business_idea_title(self, obj):
        return obj.business_idea.title


class AnalyticsSnapshotSerializer(serializers.ModelSerializer):
    """Serializer for analytics snapshots"""
    
    business_idea_title = serializers.SerializerMethodField()
    
    class Meta:
        model = AnalyticsSnapshot
        fields = [
            'id', 'business_idea', 'business_idea_title', 'snapshot_date',
            'progress_rate', 'milestone_completion_rate', 'goal_achievement_rate',
            'team_size', 'mentor_engagement', 'industry_percentile',
            'stage_percentile', 'success_probability'
        ]
        read_only_fields = ['id', 'snapshot_date']
    
    def get_business_idea_title(self, obj):
        return obj.business_idea.title


class MetricDefinitionSerializer(serializers.ModelSerializer):
    """Serializer for metric definitions"""
    
    class Meta:
        model = MetricDefinition
        fields = [
            'id', 'metric_key', 'display_name', 'description', 'calculation_method',
            'low_value_interpretation', 'medium_value_interpretation', 'high_value_interpretation',
            'improvement_suggestions', 'visualization_settings', 'category', 'display_order'
        ]
        read_only_fields = ['id']


class EnhancedBusinessAnalyticsSerializer(serializers.ModelSerializer):
    """Enhanced serializer for business analytics with predictive and comparative data"""
    
    business_idea_title = serializers.SerializerMethodField()
    predictive_analytics = PredictiveAnalyticsSerializer(read_only=True)
    comparative_analytics = ComparativeAnalyticsSerializer(read_only=True)
    similar_ideas = serializers.SerializerMethodField()
    historical_snapshots = serializers.SerializerMethodField()
    
    class Meta:
        model = BusinessAnalytics
        fields = [
            'id', 'business_idea', 'business_idea_title', 'progress_rate',
            'milestone_completion_rate', 'goal_achievement_rate', 'team_size',
            'mentor_engagement', 'industry_percentile', 'stage_percentile',
            'predictive_analytics', 'comparative_analytics', 'similar_ideas',
            'historical_snapshots', 'last_calculated'
        ]
        read_only_fields = ['id', 'progress_rate', 'milestone_completion_rate', 'goal_achievement_rate',
                           'team_size', 'mentor_engagement', 'industry_percentile', 'stage_percentile',
                           'last_calculated']
    
    def get_business_idea_title(self, obj):
        return obj.business_idea.title
    
    def get_similar_ideas(self, obj):
        """Get similar business ideas based on tags and stage"""
        try:
            # Get comparative analytics if available
            comparative = ComparativeAnalytics.objects.get(business_idea=obj.business_idea)
            if 'similar_ideas' in comparative.similar_ideas:
                return comparative.similar_ideas['similar_ideas']
        except ComparativeAnalytics.DoesNotExist:
            pass
        
        # Fallback to basic similar ideas
        business_idea = obj.business_idea
        similar_ideas = BusinessIdea.objects.filter(
            models.Q(current_stage=business_idea.current_stage) |
            models.Q(tags__in=business_idea.tags.all())
        ).exclude(id=business_idea.id).distinct()[:5]
        
        return [
            {
                'id': idea.id,
                'title': idea.title,
                'current_stage': idea.current_stage,
                'owner': {
                    'username': idea.owner.username
                },
                'progress_count': idea.progress_updates.count()
            }
            for idea in similar_ideas
        ]
    
    def get_historical_snapshots(self, obj):
        """Get historical analytics snapshots"""
        snapshots = AnalyticsSnapshot.objects.filter(business_idea=obj.business_idea).order_by('-snapshot_date')[:6]
        return AnalyticsSnapshotSerializer(snapshots, many=True).data


class AnalyticsDashboardSerializer(serializers.Serializer):
    """Serializer for the complete analytics dashboard"""
    
    business_analytics = EnhancedBusinessAnalyticsSerializer()
    predictive_analytics = PredictiveAnalyticsSerializer(required=False)
    comparative_analytics = ComparativeAnalyticsSerializer(required=False)
    metric_definitions = MetricDefinitionSerializer(many=True, required=False)
    
    # Additional dashboard-specific data
    dashboard_summary = serializers.JSONField(required=False)
    recommended_actions = serializers.JSONField(required=False)
    
    def to_representation(self, instance):
        """
        Custom representation that combines all analytics data
        
        Args:
            instance: BusinessAnalytics instance
            
        Returns:
            dict: Combined analytics data
        """
        business_analytics = instance
        business_idea = business_analytics.business_idea
        
        # Get predictive analytics
        try:
            predictive_analytics = PredictiveAnalytics.objects.get(business_idea=business_idea)
        except PredictiveAnalytics.DoesNotExist:
            predictive_analytics = None
        
        # Get comparative analytics
        try:
            comparative_analytics = ComparativeAnalytics.objects.get(business_idea=business_idea)
        except ComparativeAnalytics.DoesNotExist:
            comparative_analytics = None
        
        # Get metric definitions
        metric_definitions = MetricDefinition.objects.all()
        
        # Create dashboard summary
        dashboard_summary = {
            'overall_health': self._calculate_overall_health(business_analytics),
            'key_strengths': self._identify_key_strengths(business_analytics),
            'key_weaknesses': self._identify_key_weaknesses(business_analytics),
            'recent_progress': self._calculate_recent_progress(business_idea),
        }
        
        # Create recommended actions
        recommended_actions = self._generate_recommended_actions(
            business_analytics, 
            predictive_analytics, 
            comparative_analytics
        )
        
        # Combine all data
        return {
            'business_analytics': EnhancedBusinessAnalyticsSerializer(business_analytics).data,
            'predictive_analytics': PredictiveAnalyticsSerializer(predictive_analytics).data if predictive_analytics else None,
            'comparative_analytics': ComparativeAnalyticsSerializer(comparative_analytics).data if comparative_analytics else None,
            'metric_definitions': MetricDefinitionSerializer(metric_definitions, many=True).data,
            'dashboard_summary': dashboard_summary,
            'recommended_actions': recommended_actions,
        }
    
    def _calculate_overall_health(self, analytics):
        """Calculate overall health score based on analytics metrics"""
        # Simple weighted average of key metrics
        progress_weight = 0.2
        milestone_weight = 0.3
        goal_weight = 0.2
        engagement_weight = 0.3
        
        health_score = (
            (analytics.progress_rate * 20) * progress_weight +  # Normalize progress rate (0-5 scale to 0-100)
            analytics.milestone_completion_rate * milestone_weight +
            analytics.goal_achievement_rate * goal_weight +
            analytics.mentor_engagement * engagement_weight
        )
        
        # Cap at 100
        health_score = min(100, health_score)
        
        # Determine health status
        if health_score >= 75:
            status = 'excellent'
        elif health_score >= 60:
            status = 'good'
        elif health_score >= 40:
            status = 'fair'
        else:
            status = 'needs_attention'
        
        return {
            'score': health_score,
            'status': status
        }
    
    def _identify_key_strengths(self, analytics):
        """Identify key strengths based on analytics metrics"""
        strengths = []
        
        if analytics.progress_rate >= 2:  # More than 2 updates per month
            strengths.append({
                'metric': 'progress_rate',
                'value': analytics.progress_rate,
                'message': 'Strong progress update frequency'
            })
        
        if analytics.milestone_completion_rate >= 70:
            strengths.append({
                'metric': 'milestone_completion_rate',
                'value': analytics.milestone_completion_rate,
                'message': 'High milestone completion rate'
            })
        
        if analytics.goal_achievement_rate >= 60:
            strengths.append({
                'metric': 'goal_achievement_rate',
                'value': analytics.goal_achievement_rate,
                'message': 'Strong goal achievement rate'
            })
        
        if analytics.mentor_engagement >= 70:
            strengths.append({
                'metric': 'mentor_engagement',
                'value': analytics.mentor_engagement,
                'message': 'High mentor engagement'
            })
        
        return strengths[:3]  # Return top 3 strengths
    
    def _identify_key_weaknesses(self, analytics):
        """Identify key weaknesses based on analytics metrics"""
        weaknesses = []
        
        if analytics.progress_rate < 1:  # Less than 1 update per month
            weaknesses.append({
                'metric': 'progress_rate',
                'value': analytics.progress_rate,
                'message': 'Low progress update frequency'
            })
        
        if analytics.milestone_completion_rate < 40:
            weaknesses.append({
                'metric': 'milestone_completion_rate',
                'value': analytics.milestone_completion_rate,
                'message': 'Low milestone completion rate'
            })
        
        if analytics.goal_achievement_rate < 30:
            weaknesses.append({
                'metric': 'goal_achievement_rate',
                'value': analytics.goal_achievement_rate,
                'message': 'Low goal achievement rate'
            })
        
        if analytics.mentor_engagement < 30:
            weaknesses.append({
                'metric': 'mentor_engagement',
                'value': analytics.mentor_engagement,
                'message': 'Low mentor engagement'
            })
        
        return weaknesses[:3]  # Return top 3 weaknesses
    
    def _calculate_recent_progress(self, business_idea):
        """Calculate recent progress based on updates in the last 30 days"""
        from django.utils import timezone
        from datetime import timedelta
        
        thirty_days_ago = timezone.now() - timedelta(days=30)
        
        recent_updates = business_idea.progress_updates.filter(created_at__gte=thirty_days_ago).count()
        recent_milestones = business_idea.milestones.filter(
            status='completed', 
            completion_date__gte=thirty_days_ago.date()
        ).count()
        recent_goals = business_idea.goals.filter(
            status='achieved',
            achievement_date__gte=thirty_days_ago.date()
        ).count()
        
        return {
            'recent_updates': recent_updates,
            'recent_milestones': recent_milestones,
            'recent_goals': recent_goals,
            'total_recent_activities': recent_updates + recent_milestones + recent_goals
        }
    
    def _generate_recommended_actions(self, business_analytics, predictive_analytics, comparative_analytics):
        """Generate recommended actions based on analytics data"""
        actions = []
        
        # Add actions based on business analytics
        if business_analytics.progress_rate < 1:
            actions.append({
                'type': 'progress',
                'priority': 'high',
                'title': 'Increase progress updates',
                'description': 'Try to update your progress at least once a week to better track your business development.'
            })
        
        if business_analytics.milestone_completion_rate < 40:
            actions.append({
                'type': 'milestone',
                'priority': 'high',
                'title': 'Improve milestone completion',
                'description': 'Focus on completing your existing milestones before adding new ones.'
            })
        
        if business_analytics.mentor_engagement < 30:
            actions.append({
                'type': 'mentorship',
                'priority': 'medium',
                'title': 'Increase mentor engagement',
                'description': 'Schedule regular sessions with your mentors to get more guidance.'
            })
        
        # Add actions based on predictive analytics
        if predictive_analytics and 'risk_factors' in predictive_analytics.risk_factors:
            for risk in predictive_analytics.risk_factors.get('high_risks', [])[:2]:
                actions.append({
                    'type': 'risk',
                    'priority': 'high',
                    'title': f'Address risk: {risk["title"]}',
                    'description': risk.get('mitigation', 'Develop a plan to address this high-risk area.')
                })
        
        # Add actions based on comparative analytics
        if comparative_analytics and 'competitive_disadvantages' in comparative_analytics.competitive_disadvantages:
            for disadvantage in comparative_analytics.competitive_disadvantages.get('key_disadvantages', [])[:2]:
                actions.append({
                    'type': 'competitive',
                    'priority': 'medium',
                    'title': f'Improve: {disadvantage["area"]}',
                    'description': disadvantage.get('suggestion', 'Work on improving this area to be more competitive.')
                })
        
        return actions
