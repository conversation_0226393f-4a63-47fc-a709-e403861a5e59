from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from users.models import UserProfile, UserRole, UserRoleAssignment


class Command(BaseCommand):
    help = 'Ensure all users have at least the user role assigned'

    def handle(self, *args, **options):
        self.stdout.write('🔧 Fixing user roles...')
        
        # Get or create the 'user' role
        user_role, created = UserRole.objects.get_or_create(
            name='user',
            defaults={
                'display_name': 'Regular User',
                'description': 'Standard user with basic permissions',
                'permission_level': 'read',
                'is_active': True,
                'requires_approval': False
            }
        )
        
        if created:
            self.stdout.write('✅ Created user role')
        
        # Process all users
        users = User.objects.all()
        fixed_count = 0
        
        for user in users:
            # Ensure user has a profile
            profile, profile_created = UserProfile.objects.get_or_create(
                user=user,
                defaults={
                    'bio': '',
                    'location': '',
                    'company': '',
                    'is_active': True,
                    'years_of_experience': 0,
                    'language': 'en'
                }
            )
            
            if profile_created:
                self.stdout.write(f'✅ Created profile for {user.username}')
            
            # Check if user has any active roles
            active_roles = profile.get_active_roles()
            
            if not active_roles.exists():
                # Assign user role
                assignment, assignment_created = UserRoleAssignment.objects.get_or_create(
                    user_profile=profile,
                    role=user_role,
                    defaults={
                        'is_active': True,
                        'is_approved': True,
                        'assigned_by': None,
                        'approved_by': None,
                        'notes': 'Auto-assigned basic user role'
                    }
                )
                
                if assignment_created:
                    self.stdout.write(f'✅ Assigned user role to {user.username}')
                    fixed_count += 1
                else:
                    # Ensure existing assignment is active
                    if not assignment.is_active or not assignment.is_approved:
                        assignment.is_active = True
                        assignment.is_approved = True
                        assignment.save()
                        self.stdout.write(f'🔄 Activated user role for {user.username}')
                        fixed_count += 1
            
            # Set primary role if not set
            if not profile.primary_role:
                profile.primary_role = user_role
                profile.save()
                self.stdout.write(f'✅ Set primary role for {user.username}')
        
        self.stdout.write(f'🎉 Fixed roles for {fixed_count} users')
        self.stdout.write('✅ All users now have proper role assignments')
