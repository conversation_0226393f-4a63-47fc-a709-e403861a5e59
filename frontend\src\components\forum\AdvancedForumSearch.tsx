import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Search,
  Filter,
  Calendar,
  User,
  Tag,
  FileText,
  Paperclip,
  MessageSquare,
  X,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { forumApi } from '../../services/forumApi';
import { useTranslation } from 'react-i18next';

interface SearchResult {
  id: number;
  type: 'thread' | 'post';
  title?: string;
  content: string;
  author: {
    id: number;
    username: string;
  };
  created_at: string;
  thread_id?: number;
  thread_title?: string;
  topic?: {
    id: number;
    title: string;
    slug: string;
  };
  has_attachments: boolean;
  attachment_count?: number;
  attachment_types?: string[];
  like_count: number;
  post_count?: number;
  view_count?: number;
  tags?: string[];
  relevance_score: number;
}

const AdvancedForumSearch: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);

  const [query, setQuery] = useState<string>(queryParams.get('q') || '');
  const [searchType, setSearchType] = useState<'all' | 'threads' | 'posts'>(
    (queryParams.get('type') as 'all' | 'threads' | 'posts') || 'all'
  );
  const [authorFilter, setAuthorFilter] = useState<string>(queryParams.get('author') || '');
  const [tagFilter, setTagFilter] = useState<string>(queryParams.get('tag') || '');
  const [dateFilter, setDateFilter] = useState<string>(queryParams.get('date') || '');
  const [hasAttachments, setHasAttachments] = useState<boolean>(queryParams.get('attachments') === 'true');
  const [attachmentType, setAttachmentType] = useState<string>(queryParams.get('attachment_type') || '');
  const [sortBy, setSortBy] = useState<string>(queryParams.get('sort') || 'relevance');

  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [totalResults, setTotalResults] = useState<number>(0);
  const [showFilters, setShowFilters] = useState<boolean>(false);

  // Available attachment types
  const attachmentTypes = [
    { value: '', label: t("common.any.type", "Any Type") },
    { value: 'image', label: t("common.images", "Images") },
    { value: 'document', label: t("common.documents", "Documents") },
    { value: 'pdf', label: t("common.pdfs", "PDFs") },
    { value: 'spreadsheet', label: t("common.spreadsheets", "Spreadsheets") },
    { value: 'presentation', label: t("common.presentations", "Presentations") },
    { value: 'archive', label: t("common.archives", "Archives") },
    { value: 'code', label: t("common.code.files", "Code Files") },
  ];

  // Date filter options
  const dateOptions = [
    { value: '', label: t("common.any.time", "Any Time") },
    { value: 'today', label: t("common.today", "Today") },
    { value: 'week', label: t("common.this.week", "This Week") },
    { value: 'month', label: t("common.this.month", "This Month") },
    { value: 'year', label: t("common.this.year", "This Year") },
  ];

  // Sort options
  const sortOptions = [
    { value: 'relevance', label: t("common.relevance", "Relevance") },
    { value: 'date_desc', label: t("common.newest.first", "Newest First") },
    { value: 'date_asc', label: t("common.oldest.first", "Oldest First") },
    { value: 'likes', label: t("common.most.likes", "Most Likes") },
    { value: 'posts', label: t("common.most.replies", "Most Replies") },
    { value: 'views', label: t("common.most.views", "Most Views") },
  ];

  useEffect(() => {
    // If there's a query in the URL, perform search
    if (queryParams.get('q')) {
      performSearch();
    }
  }, [location.search]);

  const performSearch = async () => {
    if (!query.trim()) return;

    setLoading(true);
    setError(null);

    try {
      const searchParams = {
        q: query,
        type: searchType,
        author: authorFilter,
        tag: tagFilter,
        date: dateFilter,
        attachments: hasAttachments ? 'true' : 'false',
        attachment_type: attachmentType,
        sort: sortBy,
      };

      const response = await forumApi.searchForum(searchParams);
      setResults(response.results);
      setTotalResults(response.count);
    } catch (error) {
      console.error('Search error:', error);
      setError('An error occurred while searching. Please try again.');
      setResults([]);
      setTotalResults(0);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();

    // Update URL with search parameters
    const params = new URLSearchParams();
    if (query) params.set('q', query);
    if (searchType !== 'all') params.set('type', searchType);
    if (authorFilter) params.set('author', authorFilter);
    if (tagFilter) params.set('tag', tagFilter);
    if (dateFilter) params.set('date', dateFilter);
    if (hasAttachments) params.set('attachments', 'true');
    if (attachmentType) params.set('attachment_type', attachmentType);
    if (sortBy !== 'relevance') params.set('sort', sortBy);

    navigate(`/forum/search?${params.toString()}`);
    performSearch();
  };

  const clearFilters = () => {
    setAuthorFilter('');
    setTagFilter('');
    setDateFilter('');
    setHasAttachments(false);
    setAttachmentType('');
    setSortBy('relevance');
  };

  const highlightMatch = (text: string, term: string) => {
    if (!term.trim()) return text;

    const regex = new RegExp(`(${term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
  };

  const renderResult = (result: SearchResult) => {
    const isThread = result.type === 'thread';
    const title = isThread ? result.title : result.thread_title;
    const content = result.content;
    const highlightedTitle = title ? highlightMatch(title, query) : '';
    const highlightedContent = highlightMatch(content, query);

    return (
      <div key={`${result.type}-${result.id}`} className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50 hover:border-indigo-600/50 transition-colors">
        <div className={`flex justify-between items-start ${isRTL ? "flex-row-reverse" : ""}`}>
          <div>
            <h3 className="text-lg font-medium">
              <a
                href={isThread ? `/forum/thread/${result.id}` : `/forum/thread/${result.thread_id}#post-${result.id}`}
                className="text-purple-400 hover:text-purple-300"
                dangerouslySetInnerHTML={{ __html: highlightedTitle || t("common.untitled", "Untitled") }}
              />
            </h3>
            <div className={`flex items-center text-sm text-gray-400 mt-1 space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
              <span className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <User size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                <a
                  href={`/user/${result.author.id}`}
                  className="hover:text-purple-400"
                >
                  {result.author.username}
                </a>
              </span>
              <span className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <Calendar size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                {new Date(result.created_at).toLocaleDateString()}
              </span>
              {result.topic && (
                <span className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                  <MessageSquare size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                  <a
                    href={`/forum/topic/${result.topic.slug}`}
                    className="hover:text-purple-400"
                  >
                    {result.topic.title}
                  </a>
                </span>
              )}
            </div>
          </div>
          <div className={`flex items-center space-x-2 text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
            {result.has_attachments && (
              <span className={`flex items-center text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`} title={`${result.attachment_count} attachment(s)`}>
                <Paperclip size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                {result.attachment_count}
              </span>
            )}
          </div>
        </div>

        <div
          className="mt-2 text-gray-300 line-clamp-2"
          dangerouslySetInnerHTML={{ __html: highlightedContent }}
        />

        <div className={`mt-3 flex flex-wrap items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`flex flex-wrap gap-2 ${isRTL ? "flex-row-reverse" : ""}`}>
            {result.tags && result.tags.map(tag => (
              <span
                key={tag}
                className="px-2 py-1 bg-indigo-800/50 rounded-full text-xs"
                onClick={() => {
                  setTagFilter(tag);
                  handleSearch(new Event('submit') as any);
                }}
              >
                {tag}
              </span>
            ))}
          </div>

          <div className={`flex items-center space-x-3 text-sm text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
            {isThread && (
              <>
                <span className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                  <MessageSquare size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                  {result.post_count} {result.post_count === 1 ? 'reply' : 'replies'}
                </span>
                <span className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                  {result.view_count} {result.view_count === 1 ? 'view' : 'views'}
                </span>
              </>
            )}
            <span className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              {result.like_count} {result.like_count === 1 ? 'like' : 'likes'}
            </span>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
        <form onSubmit={handleSearch}>
          <div className={`flex flex-col md:flex-row gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className={`flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
              <div className="relative">
                <input
                  type="text"
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  placeholder={t("common.search.the.forum", "Search the forum...")}
                  className="w-full bg-indigo-900/30 border border-indigo-800/50 rounded-lg py-2 pl-10 pr-4 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
                <Search className="absolute left-3 top-2.5 text-gray-400" size={18} />
              </div>
            </div>

            <div className={`flex gap-2 ${isRTL ? "flex-row-reverse" : ""}`}>
              <select
                value={searchType}
                onChange={(e) => setSearchType(e.target.value as 'all' | 'threads' | 'posts')}
                className="bg-indigo-900/30 border border-indigo-800/50 rounded-lg py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="all">{t("common.all", "All")}</option>
                <option value="threads">{t("common.threads", "Threads")}</option>
                <option value="posts">{t("common.posts", "Posts")}</option>
              </select>

              <button
                type="button"
                onClick={() => setShowFilters(!showFilters)}
                className={`bg-indigo-900/30 border border-indigo-800/50 rounded-lg py-2 px-4 text-white hover:bg-indigo-800/30 focus:outline-none focus:ring-2 focus:ring-purple-500 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <Filter size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                Filters
                {showFilters ? <ChevronUp size={18} className={`ml-2 ${isRTL ? "space-x-reverse" : ""}`} /> : <ChevronDown size={18} className={`ml-2 ${isRTL ? "space-x-reverse" : ""}`} />}
              </button>

              <button
                type="submit"
                className="bg-purple-600 rounded-lg py-2 px-4 text-white hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                Search
              </button>
            </div>
          </div>

          {showFilters && (
            <div className="mt-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">{t("common.author", "Author")}</label>
                <input
                  type="text"
                  value={authorFilter}
                  onChange={(e) => setAuthorFilter(e.target.value)}
                  placeholder={t("common.username", "Username")}
                  className="w-full bg-indigo-900/30 border border-indigo-800/50 rounded-lg py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">{t("common.tag", "Tag")}</label>
                <input
                  type="text"
                  value={tagFilter}
                  onChange={(e) => setTagFilter(e.target.value)}
                  placeholder={t("common.tag.name", "Tag name")}
                  className="w-full bg-indigo-900/30 border border-indigo-800/50 rounded-lg py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">{t("common.date", "Date")}</label>
                <select
                  value={dateFilter}
                  onChange={(e) => setDateFilter(e.target.value)}
                  className="w-full bg-indigo-900/30 border border-indigo-800/50 rounded-lg py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  {dateOptions.map(option => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">{t("common.sort.by", "Sort By")}</label>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="w-full bg-indigo-900/30 border border-indigo-800/50 rounded-lg py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  {sortOptions.map(option => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">{t("common.attachments", "Attachments")}</label>
                <div className={`flex items-center mt-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <input
                    type="checkbox"
                    id="hasAttachments"
                    checked={hasAttachments}
                    onChange={(e) => setHasAttachments(e.target.checked)}
                    className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                  />
                  <label htmlFor="hasAttachments" className={`ml-2 block text-sm ${isRTL ? "space-x-reverse" : ""}`}>
                    Has attachments
                  </label>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">{t("common.attachment.type", "Attachment Type")}</label>
                <select
                  value={attachmentType}
                  onChange={(e) => setAttachmentType(e.target.value)}
                  disabled={!hasAttachments}
                  className="w-full bg-indigo-900/30 border border-indigo-800/50 rounded-lg py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50"
                >
                  {attachmentTypes.map(option => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
              </div>

              <div className={`flex items-end ${isRTL ? "flex-row-reverse" : ""}`}>
                <button
                  type="button"
                  onClick={clearFilters}
                  className={`bg-indigo-900/30 border border-indigo-800/50 rounded-lg py-2 px-4 text-white hover:bg-indigo-800/30 focus:outline-none focus:ring-2 focus:ring-purple-500 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                >
                  <X size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                  Clear Filters
                </button>
              </div>
            </div>
          )}
        </form>
      </div>

      {loading ? (
        <div className={`flex justify-center items-center py-12 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
        </div>
      ) : error ? (
        <div className="bg-red-900/30 backdrop-blur-sm rounded-lg p-6 border border-red-800/50">
          <div className="text-center">{error}</div>
        </div>
      ) : results.length > 0 ? (
        <div className="space-y-4">
          <div className="text-sm text-gray-400">
            Found {totalResults} {totalResults === 1 ? 'result' : 'results'} for "{query}"
          </div>

          {results.map(result => renderResult(result))}
        </div>
      ) : query ? (
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
          <p className="text-center">No results found for "{query}"</p>
          <p className="text-center text-sm text-gray-400 mt-2">
            Try different keywords or adjust your filters
          </p>
        </div>
      ) : null}
    </div>
  );
};

export default AdvancedForumSearch;
