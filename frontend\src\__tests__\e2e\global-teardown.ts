import { chromium, FullConfig } from '@playwright/test';
import fs from 'fs';
import path from 'path';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting E2E test teardown...');
  
  const { baseURL } = config.projects[0].use;
  
  try {
    // Clean up test data
    await cleanupTestData(baseURL);
    
    // Clean up authentication files
    await cleanupAuthFiles();
    
    // Generate test summary
    await generateTestSummary();
    
    console.log('✅ E2E test teardown completed successfully');
    
  } catch (error) {
    console.error('❌ E2E test teardown failed:', error);
    // Don't throw error to avoid failing the test suite
  }
}

async function cleanupTestData(baseURL?: string) {
  try {
    console.log('🗑️ Cleaning up test data...');
    
    // Launch browser for cleanup
    const browser = await chromium.launch();
    const page = await browser.newPage();
    
    // Load admin session if available
    const authFile = 'test-results/admin-auth.json';
    if (fs.existsSync(authFile)) {
      await page.context().addInitScript(() => {
        // Restore authentication state
      });
    }
    
    await page.goto(baseURL || 'http://localhost:3000');
    
    // Clean up test entities
    await cleanupTestBusinessPlans(page);
    await cleanupTestMentorProfiles(page);
    await cleanupTestFundingOpportunities(page);
    await cleanupTestMilestones(page);
    
    await browser.close();
    
    console.log('🗑️ Test data cleanup completed');
    
  } catch (error) {
    console.warn('⚠️ Could not cleanup test data:', error.message);
  }
}

async function cleanupTestBusinessPlans(page: any) {
  try {
    await page.goto('/admin/incubator/business-plans');
    
    // Find and delete test business plans
    const testPlans = page.locator('[data-testid="business-plan-row"]').filter({ hasText: 'E2E Test' });
    const count = await testPlans.count();
    
    for (let i = 0; i < count; i++) {
      const plan = testPlans.nth(i);
      const deleteButton = plan.locator('[data-testid="delete-button"]');
      
      if (await deleteButton.isVisible()) {
        await deleteButton.click();
        await page.click('[data-testid="confirm-delete-button"]');
        await page.waitForSelector('[data-testid="business-plan-delete-modal"]', { state: 'hidden' });
      }
    }
    
    console.log(`🗑️ Cleaned up ${count} test business plans`);
    
  } catch (error) {
    console.warn('Could not cleanup test business plans:', error.message);
  }
}

async function cleanupTestMentorProfiles(page: any) {
  try {
    await page.goto('/admin/incubator/mentor-profiles');
    
    // Find and delete test mentor profiles
    const testProfiles = page.locator('[data-testid="mentor-profile-row"]').filter({ hasText: 'E2E Test' });
    const count = await testProfiles.count();
    
    for (let i = 0; i < count; i++) {
      const profile = testProfiles.nth(i);
      const deleteButton = profile.locator('[data-testid="delete-button"]');
      
      if (await deleteButton.isVisible()) {
        await deleteButton.click();
        await page.click('[data-testid="confirm-delete-button"]');
        await page.waitForSelector('[data-testid="mentor-profile-delete-modal"]', { state: 'hidden' });
      }
    }
    
    console.log(`🗑️ Cleaned up ${count} test mentor profiles`);
    
  } catch (error) {
    console.warn('Could not cleanup test mentor profiles:', error.message);
  }
}

async function cleanupTestFundingOpportunities(page: any) {
  try {
    await page.goto('/admin/funding');
    
    // Find and delete test funding opportunities
    const testOpportunities = page.locator('[data-testid="funding-opportunity-row"]').filter({ hasText: 'E2E Test' });
    const count = await testOpportunities.count();
    
    for (let i = 0; i < count; i++) {
      const opportunity = testOpportunities.nth(i);
      const deleteButton = opportunity.locator('[data-testid="delete-button"]');
      
      if (await deleteButton.isVisible()) {
        await deleteButton.click();
        await page.click('[data-testid="confirm-delete-button"]');
        await page.waitForSelector('[data-testid="funding-opportunity-delete-modal"]', { state: 'hidden' });
      }
    }
    
    console.log(`🗑️ Cleaned up ${count} test funding opportunities`);
    
  } catch (error) {
    console.warn('Could not cleanup test funding opportunities:', error.message);
  }
}

async function cleanupTestMilestones(page: any) {
  try {
    await page.goto('/admin/milestones');
    
    // Find and delete test milestones
    const testMilestones = page.locator('[data-testid="milestone-row"]').filter({ hasText: 'E2E Test' });
    const count = await testMilestones.count();
    
    for (let i = 0; i < count; i++) {
      const milestone = testMilestones.nth(i);
      const deleteButton = milestone.locator('[data-testid="delete-button"]');
      
      if (await deleteButton.isVisible()) {
        await deleteButton.click();
        await page.click('[data-testid="confirm-delete-button"]');
        await page.waitForSelector('[data-testid="milestone-delete-modal"]', { state: 'hidden' });
      }
    }
    
    console.log(`🗑️ Cleaned up ${count} test milestones`);
    
  } catch (error) {
    console.warn('Could not cleanup test milestones:', error.message);
  }
}

async function cleanupAuthFiles() {
  try {
    console.log('🔐 Cleaning up authentication files...');
    
    const authFiles = [
      'test-results/admin-auth.json',
      'test-results/user-auth.json'
    ];
    
    authFiles.forEach(file => {
      if (fs.existsSync(file)) {
        fs.unlinkSync(file);
        console.log(`🗑️ Removed ${file}`);
      }
    });
    
  } catch (error) {
    console.warn('Could not cleanup auth files:', error.message);
  }
}

async function generateTestSummary() {
  try {
    console.log('📊 Generating test summary...');
    
    const testResultsDir = 'test-results';
    if (!fs.existsSync(testResultsDir)) {
      return;
    }
    
    // Read test results
    const playwrightResults = readJsonFile(path.join(testResultsDir, 'playwright-results.json'));
    const jestResults = readJsonFile(path.join(testResultsDir, 'jest-results.json'));
    
    // Generate summary
    const summary = {
      timestamp: new Date().toISOString(),
      e2e: playwrightResults ? {
        total: playwrightResults.stats?.total || 0,
        passed: playwrightResults.stats?.passed || 0,
        failed: playwrightResults.stats?.failed || 0,
        skipped: playwrightResults.stats?.skipped || 0,
        duration: playwrightResults.stats?.duration || 0
      } : null,
      unit: jestResults ? {
        total: jestResults.numTotalTests || 0,
        passed: jestResults.numPassedTests || 0,
        failed: jestResults.numFailedTests || 0,
        skipped: jestResults.numPendingTests || 0,
        duration: jestResults.testResults?.reduce((acc: number, result: any) => acc + (result.perfStats?.runtime || 0), 0) || 0
      } : null,
      coverage: jestResults?.coverageMap ? {
        statements: calculateCoveragePercentage(jestResults.coverageMap, 'statements'),
        branches: calculateCoveragePercentage(jestResults.coverageMap, 'branches'),
        functions: calculateCoveragePercentage(jestResults.coverageMap, 'functions'),
        lines: calculateCoveragePercentage(jestResults.coverageMap, 'lines')
      } : null
    };
    
    // Write summary
    fs.writeFileSync(
      path.join(testResultsDir, 'test-summary.json'),
      JSON.stringify(summary, null, 2)
    );
    
    // Log summary
    console.log('📊 Test Summary:');
    if (summary.e2e) {
      console.log(`   E2E Tests: ${summary.e2e.passed}/${summary.e2e.total} passed`);
    }
    if (summary.unit) {
      console.log(`   Unit Tests: ${summary.unit.passed}/${summary.unit.total} passed`);
    }
    if (summary.coverage) {
      console.log(`   Coverage: ${summary.coverage.statements}% statements, ${summary.coverage.lines}% lines`);
    }
    
  } catch (error) {
    console.warn('Could not generate test summary:', error.message);
  }
}

function readJsonFile(filePath: string): any {
  try {
    if (fs.existsSync(filePath)) {
      return JSON.parse(fs.readFileSync(filePath, 'utf8'));
    }
  } catch (error) {
    console.warn(`Could not read ${filePath}:`, error.message);
  }
  return null;
}

function calculateCoveragePercentage(coverageMap: any, type: string): number {
  try {
    let total = 0;
    let covered = 0;
    
    Object.values(coverageMap).forEach((file: any) => {
      if (file[type]) {
        total += Object.keys(file[type]).length;
        covered += Object.values(file[type]).filter((count: any) => count > 0).length;
      }
    });
    
    return total > 0 ? Math.round((covered / total) * 100) : 0;
  } catch {
    return 0;
  }
}

export default globalTeardown;
