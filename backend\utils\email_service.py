"""
Email service for sending notifications.
"""
import os
import logging
import uuid
from datetime import datetime, timedelta
from django.core.mail import send_mail, EmailMultiAlternatives
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.conf import settings
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from django.utils.translation import gettext as _

from .email_templates import get_email_context, render_email_template, get_user_language

logger = logging.getLogger(__name__)

class EmailService:
    """Service for sending email notifications"""

    @staticmethod
    def send_simple_email(subject, message, recipient_list, from_email=None, retry_count=0, max_retries=2):
        """
        Send a simple text email

        Args:
            subject: Email subject
            message: Email body (text)
            recipient_list: List of recipient email addresses
            from_email: Sender email address (defaults to settings.DEFAULT_FROM_EMAIL)
            retry_count: Current retry attempt (used internally for retries)
            max_retries: Maximum number of retry attempts for failed emails

        Returns:
            bool: True if email was sent successfully, False otherwise
        """
        if not from_email:
            from_email = settings.DEFAULT_FROM_EMAIL

        try:
            send_mail(
                subject=subject,
                message=message,
                from_email=from_email,
                recipient_list=recipient_list,
                fail_silently=False,
            )
            logger.info(f"Email sent to {', '.join(recipient_list)}")
            return True
        except Exception as e:
            logger.error(f"Failed to send email: {str(e)}")
            # Implement retry logic
            if retry_count < max_retries:
                logger.info(f"Retrying email send (attempt {retry_count + 1}/{max_retries})")
                return EmailService.send_simple_email(
                    subject, message, recipient_list, from_email,
                    retry_count + 1, max_retries
                )
            return False

    @staticmethod
    def send_html_email(subject, html_content, recipient_list, from_email=None, text_content=None,
                        attachments=None, retry_count=0, max_retries=2):
        """
        Send an HTML email with optional text alternative and attachments

        Args:
            subject: Email subject
            html_content: Email body (HTML)
            recipient_list: List of recipient email addresses
            from_email: Sender email address (defaults to settings.DEFAULT_FROM_EMAIL)
            text_content: Plain text alternative (generated from HTML if not provided)
            attachments: List of attachment tuples (filename, content, mimetype)
            retry_count: Current retry attempt (used internally for retries)
            max_retries: Maximum number of retry attempts for failed emails

        Returns:
            bool: True if email was sent successfully, False otherwise
        """
        if not from_email:
            from_email = settings.DEFAULT_FROM_EMAIL

        if not text_content:
            text_content = strip_tags(html_content)

        try:
            msg = EmailMultiAlternatives(
                subject=subject,
                body=text_content,
                from_email=from_email,
                to=recipient_list
            )
            msg.attach_alternative(html_content, "text/html")

            # Add attachments if provided
            if attachments:
                for attachment in attachments:
                    filename, content, mimetype = attachment
                    msg.attach(filename, content, mimetype)

            # Add tracking pixel if enabled in settings
            if getattr(settings, 'EMAIL_TRACKING_ENABLED', False):
                tracking_id = str(uuid.uuid4())
                tracking_url = f"{settings.SITE_URL}/api/email/track/{tracking_id}/"
                tracking_pixel = f'<img src="{tracking_url}" width="1" height="1" alt="" />'

                # Add tracking pixel to HTML content
                html_with_tracking = html_content.replace('</body>', f'{tracking_pixel}</body>')
                msg.alternatives = []  # Clear existing alternatives
                msg.attach_alternative(html_with_tracking, "text/html")

                # Store tracking information (would be implemented in a real tracking system)
                logger.info(f"Email tracking enabled with ID: {tracking_id}")

            msg.send()
            logger.info(f"HTML email sent to {', '.join(recipient_list)}")
            return True
        except Exception as e:
            logger.error(f"Failed to send HTML email: {str(e)}")
            # Implement retry logic
            if retry_count < max_retries:
                logger.info(f"Retrying HTML email send (attempt {retry_count + 1}/{max_retries})")
                return EmailService.send_html_email(
                    subject, html_content, recipient_list, from_email, text_content,
                    attachments, retry_count + 1, max_retries
                )
            return False

    @staticmethod
    def send_template_email(subject, template_name, context, recipient_list, from_email=None,
                           attachments=None, retry_count=0, max_retries=2, user=None, language=None):
        """
        Send an email using a template with language support

        Args:
            subject: Email subject
            template_name: Name of the template to use (without extension)
            context: Context data for the template
            recipient_list: List of recipient email addresses
            from_email: Sender email address (defaults to settings.DEFAULT_FROM_EMAIL)
            attachments: List of attachment tuples (filename, content, mimetype)
            retry_count: Current retry attempt (used internally for retries)
            max_retries: Maximum number of retry attempts for failed emails
            user: User instance for language preference (optional)
            language: Language code to use (overrides user preference if provided)

        Returns:
            bool: True if email was sent successfully, False otherwise
        """
        if not from_email:
            from_email = settings.DEFAULT_FROM_EMAIL

        try:
            # Determine language
            if not language and user:
                language = get_user_language(user)

            # Prepare context with translations
            enhanced_context = get_email_context(template_name, context, user)

            # If subject is not provided in context, use the one passed to this method
            if 'subject' not in enhanced_context:
                enhanced_context['subject'] = subject

            # Render template with language support
            email_subject, html_content, text_content = render_email_template(
                template_name, enhanced_context, language
            )

            # Use provided subject if not returned from render_email_template
            if not email_subject:
                email_subject = subject

            # Generate plain text content if not provided
            if not text_content:
                text_content = strip_tags(html_content)

            # Use the HTML email method to send the email with attachments
            return EmailService.send_html_email(
                subject=email_subject,
                html_content=html_content,
                recipient_list=recipient_list,
                from_email=from_email,
                text_content=text_content,
                attachments=attachments,
                retry_count=retry_count,
                max_retries=max_retries
            )
        except Exception as e:
            logger.error(f"Failed to send template email: {str(e)}")
            # Implement retry logic
            if retry_count < max_retries:
                logger.info(f"Retrying template email send (attempt {retry_count + 1}/{max_retries})")
                return EmailService.send_template_email(
                    subject, template_name, context, recipient_list, from_email,
                    attachments, retry_count + 1, max_retries, user, language
                )
            return False

    @staticmethod
    def send_bulk_template_email(subject, template_name, context_list, from_email=None):
        """
        Send bulk emails using a template to multiple recipients with personalized context

        Args:
            subject: Email subject or function that takes context and returns subject
            template_name: Name of the template to use (without extension)
            context_list: List of (context, recipient_email) tuples
            from_email: Sender email address (defaults to settings.DEFAULT_FROM_EMAIL)

        Returns:
            dict: Dictionary with recipient emails as keys and success status as values
        """
        if not from_email:
            from_email = settings.DEFAULT_FROM_EMAIL

        results = {}

        for context, recipient_email in context_list:
            # Handle dynamic subject if subject is a function
            email_subject = subject(context) if callable(subject) else subject

            # Send individual email with personalized context
            success = EmailService.send_template_email(
                subject=email_subject,
                template_name=template_name,
                context=context,
                recipient_list=[recipient_email],
                from_email=from_email
            )

            results[recipient_email] = success

        return results

    @staticmethod
    def schedule_email(send_time, email_type, email_data):
        """
        Schedule an email to be sent at a future time

        Args:
            send_time: Datetime when the email should be sent
            email_type: Type of email ('simple', 'html', or 'template')
            email_data: Dictionary containing all parameters for the email

        Returns:
            str: ID of the scheduled email
        """
        # In a real implementation, this would store the email in a database
        # and use a task scheduler like Celery to send it at the specified time

        email_id = str(uuid.uuid4())

        logger.info(f"Email scheduled with ID {email_id} to be sent at {send_time}")
        logger.info(f"Email type: {email_type}, data: {email_data}")

        # This is a placeholder - in a real implementation, you would:
        # 1. Store the email data in a database
        # 2. Create a scheduled task to send the email at the specified time

        return email_id

    @staticmethod
    def generate_pdf_attachment(template_name, context, filename):
        """
        Generate a PDF attachment from a template

        Args:
            template_name: Name of the template to use (without extension)
            context: Context data for the template
            filename: Name of the PDF file

        Returns:
            tuple: (filename, content, mimetype) for use with email attachments
        """
        try:
            # This is a placeholder - in a real implementation, you would:
            # 1. Render the template to HTML
            html_content = render_to_string(f"{template_name}.html", context)

            # 2. Convert HTML to PDF using a library like WeasyPrint or xhtml2pdf
            # For this example, we'll just use the HTML content as a placeholder
            pdf_content = html_content.encode('utf-8')

            return (filename, pdf_content, 'application/pdf')
        except Exception as e:
            logger.error(f"Failed to generate PDF attachment: {str(e)}")
            return None


# Specialized email functions for mentorship

def send_session_scheduled_notification(session, recipient_email):
    """
    Send notification when a mentorship session is scheduled

    Args:
        session: MentorshipSession instance
        recipient_email: Email address of the recipient
    """
    subject = f"Mentorship Session Scheduled: {session.title}"

    context = {
        'session': session,
        'session_date': session.scheduled_at.strftime('%A, %B %d, %Y'),
        'session_time': session.scheduled_at.strftime('%I:%M %p'),
        'session_duration': session.duration_minutes,
        'session_type': session.get_session_type_display(),
    }

    # Add video conferencing details if available
    if session.session_type == 'video' and session.meeting_link:
        context['meeting_link'] = session.meeting_link
        context['video_provider'] = session.get_video_provider_display()

    return EmailService.send_template_email(
        subject=subject,
        template_name='emails/session_scheduled',
        context=context,
        recipient_list=[recipient_email]
    )


def send_session_reminder(session, recipient_email):
    """
    Send reminder for an upcoming mentorship session

    Args:
        session: MentorshipSession instance
        recipient_email: Email address of the recipient
    """
    subject = f"Reminder: Upcoming Mentorship Session - {session.title}"

    context = {
        'session': session,
        'session_date': session.scheduled_at.strftime('%A, %B %d, %Y'),
        'session_time': session.scheduled_at.strftime('%I:%M %p'),
        'session_duration': session.duration_minutes,
        'session_type': session.get_session_type_display(),
    }

    # Add video conferencing details if available
    if session.session_type == 'video' and session.meeting_link:
        context['meeting_link'] = session.meeting_link
        context['video_provider'] = session.get_video_provider_display()

    return EmailService.send_template_email(
        subject=subject,
        template_name='emails/session_reminder',
        context=context,
        recipient_list=[recipient_email]
    )


def send_session_cancelled_notification(session, recipient_email, cancellation_reason=None):
    """
    Send notification when a mentorship session is cancelled

    Args:
        session: MentorshipSession instance
        recipient_email: Email address of the recipient
        cancellation_reason: Reason for cancellation (optional)
    """
    subject = f"Mentorship Session Cancelled: {session.title}"

    context = {
        'session': session,
        'session_date': session.scheduled_at.strftime('%A, %B %d, %Y'),
        'session_time': session.scheduled_at.strftime('%I:%M %p'),
        'cancellation_reason': cancellation_reason
    }

    return EmailService.send_template_email(
        subject=subject,
        template_name='emails/session_cancelled',
        context=context,
        recipient_list=[recipient_email]
    )


def send_feedback_request(session, recipient_email):
    """
    Send request for feedback after a mentorship session

    Args:
        session: MentorshipSession instance
        recipient_email: Email address of the recipient
    """
    subject = f"Please Provide Feedback for Your Mentorship Session"

    context = {
        'session': session,
        'session_date': session.scheduled_at.strftime('%A, %B %d, %Y'),
        'session_time': session.scheduled_at.strftime('%I:%M %p'),
    }

    return EmailService.send_template_email(
        subject=subject,
        template_name='emails/feedback_request',
        context=context,
        recipient_list=[recipient_email]
    )


def send_feedback_received_notification(feedback, recipient_email):
    """
    Send notification when feedback is received

    Args:
        feedback: MentorshipFeedback instance
        recipient_email: Email address of the recipient
    """
    subject = f"New Feedback Received for Mentorship Session"

    context = {
        'feedback': feedback,
        'session': feedback.session,
        'rating': feedback.rating,
        'comments': feedback.comments,
    }

    return EmailService.send_template_email(
        subject=subject,
        template_name='emails/feedback_received',
        context=context,
        recipient_list=[recipient_email]
    )


# Specialized email functions for business ideas

def send_business_idea_moderation_notification(business_idea, recipient_email):
    """
    Send notification when a business idea's moderation status changes

    Args:
        business_idea: BusinessIdea instance
        recipient_email: Email address of the recipient
    """
    status_display = {
        'approved': 'Approved',
        'pending': 'Pending Review',
        'rejected': 'Rejected'
    }.get(business_idea.moderation_status, business_idea.moderation_status)

    subject = f"Business Idea Status Update: {business_idea.title}"

    context = {
        'business_idea': business_idea,
        'status': status_display,
        'moderation_comment': business_idea.moderation_comment,
        'moderated_at': business_idea.moderated_at,
    }

    return EmailService.send_template_email(
        subject=subject,
        template_name='emails/business_idea_moderation',
        context=context,
        recipient_list=[recipient_email]
    )


def send_business_milestone_notification(milestone, recipient_email):
    """
    Send notification when a business milestone is created or updated

    Args:
        milestone: BusinessMilestone instance
        recipient_email: Email address of the recipient
    """
    action = "updated" if milestone.pk else "created"
    subject = f"Business Milestone {action.capitalize()}: {milestone.title}"

    context = {
        'milestone': milestone,
        'business_idea': milestone.business_idea,
        'action': action,
        'due_date': milestone.due_date.strftime('%A, %B %d, %Y'),
        'status': milestone.get_status_display(),
        'priority': milestone.get_priority_display(),
    }

    return EmailService.send_template_email(
        subject=subject,
        template_name='emails/business_milestone_notification',
        context=context,
        recipient_list=[recipient_email]
    )


def send_milestone_reminder(milestone, recipient_email, days_remaining):
    """
    Send reminder for an upcoming business milestone deadline

    Args:
        milestone: BusinessMilestone instance
        recipient_email: Email address of the recipient
        days_remaining: Number of days remaining until the deadline
    """
    subject = f"Reminder: Upcoming Milestone Deadline - {milestone.title}"

    context = {
        'milestone': milestone,
        'business_idea': milestone.business_idea,
        'due_date': milestone.due_date.strftime('%A, %B %d, %Y'),
        'days_remaining': days_remaining,
        'status': milestone.get_status_display(),
        'priority': milestone.get_priority_display(),
    }

    return EmailService.send_template_email(
        subject=subject,
        template_name='emails/milestone_reminder',
        context=context,
        recipient_list=[recipient_email]
    )


def send_milestone_completed_notification(milestone, recipient_email):
    """
    Send notification when a business milestone is marked as completed

    Args:
        milestone: BusinessMilestone instance
        recipient_email: Email address of the recipient
    """
    subject = f"Milestone Completed: {milestone.title}"

    context = {
        'milestone': milestone,
        'business_idea': milestone.business_idea,
        'completion_date': milestone.completion_date.strftime('%A, %B %d, %Y') if milestone.completion_date else 'N/A',
        'completion_notes': milestone.completion_notes,
    }

    return EmailService.send_template_email(
        subject=subject,
        template_name='emails/milestone_completed',
        context=context,
        recipient_list=[recipient_email]
    )


# Specialized email functions for funding opportunities

def send_funding_opportunity_notification(funding_opportunity, recipient_email):
    """
    Send notification about a new funding opportunity

    Args:
        funding_opportunity: FundingOpportunity instance
        recipient_email: Email address of the recipient
    """
    subject = f"New Funding Opportunity: {funding_opportunity.title}"

    context = {
        'funding_opportunity': funding_opportunity,
        'application_deadline': funding_opportunity.application_deadline.strftime('%A, %B %d, %Y'),
        'funding_amount': funding_opportunity.funding_amount,
    }

    return EmailService.send_template_email(
        subject=subject,
        template_name='emails/funding_opportunity',
        context=context,
        recipient_list=[recipient_email]
    )


def send_funding_application_status_notification(funding_application, recipient_email):
    """
    Send notification when a funding application status changes

    Args:
        funding_application: FundingApplication instance
        recipient_email: Email address of the recipient
    """
    status_display = {
        'pending': 'Pending Review',
        'approved': 'Approved',
        'rejected': 'Rejected',
        'funded': 'Funded',
    }.get(funding_application.status, funding_application.status)

    subject = f"Funding Application Status Update: {funding_application.business_idea.title}"

    context = {
        'funding_application': funding_application,
        'business_idea': funding_application.business_idea,
        'status': status_display,
        'reviewer_notes': funding_application.reviewer_notes,
        'requested_amount': funding_application.requested_amount,
    }

    return EmailService.send_template_email(
        subject=subject,
        template_name='emails/funding_application_status',
        context=context,
        recipient_list=[recipient_email]
    )


def send_investment_notification(investment, recipient_email):
    """
    Send notification when an investment is made

    Args:
        investment: Investment instance
        recipient_email: Email address of the recipient
    """
    subject = f"New Investment in {investment.business_idea.title}"

    context = {
        'investment': investment,
        'business_idea': investment.business_idea,
        'investor': investment.investor,
        'amount': investment.amount,
        'investment_date': investment.investment_date.strftime('%A, %B %d, %Y'),
    }

    return EmailService.send_template_email(
        subject=subject,
        template_name='emails/investment_notification',
        context=context,
        recipient_list=[recipient_email]
    )


# Specialized email functions for AI recommendations

def send_ai_recommendation_notification(recommendation, recipient_email):
    """
    Send notification about a new AI recommendation

    Args:
        recommendation: AIRecommendation instance
        recipient_email: Email address of the recipient
    """
    subject = f"New AI Recommendation for {recommendation.business_idea.title}"

    context = {
        'recommendation': recommendation,
        'business_idea': recommendation.business_idea,
        'recommendation_type': recommendation.get_recommendation_type_display(),
        'content': recommendation.content,
    }

    return EmailService.send_template_email(
        subject=subject,
        template_name='emails/ai_recommendation',
        context=context,
        recipient_list=[recipient_email]
    )


# Specialized email functions for user notifications

def send_welcome_email(user, recipient_email):
    """
    Send welcome email to new users

    Args:
        user: User instance
        recipient_email: Email address of the recipient
    """
    # Get user's preferred language
    language = get_user_language(user)

    # Default subject (will be overridden by translations if available)
    subject = f"Welcome to Yasmeen AI, {user.first_name}!"

    context = {
        'user': user,
        'site_url': settings.SITE_URL,
        'name': user.first_name or user.username,
        'email': recipient_email
    }

    return EmailService.send_template_email(
        subject=subject,
        template_name='emails/welcome',
        context=context,
        recipient_list=[recipient_email],
        user=user,
        language=language
    )


def send_password_reset_email(user, reset_token, recipient_email):
    """
    Send password reset email

    Args:
        user: User instance
        reset_token: Password reset token
        recipient_email: Email address of the recipient
    """
    subject = "Reset Your Yasmeen AI Password"

    reset_url = f"{settings.SITE_URL}/reset-password/{reset_token}/"

    context = {
        'user': user,
        'reset_url': reset_url,
        'expiry_hours': 24,  # Token expiry time in hours
    }

    return EmailService.send_template_email(
        subject=subject,
        template_name='emails/password_reset',
        context=context,
        recipient_list=[recipient_email]
    )


def send_verification_email(user, verification_token, recipient_email):
    """
    Send email verification email

    Args:
        user: User instance
        verification_token: Email verification token
        recipient_email: Email address of the recipient
    """
    subject = "Verify Your Email Address for Yasmeen AI"

    verification_url = f"{settings.SITE_URL}/verify-email/{verification_token}/"

    context = {
        'user': user,
        'verification_url': verification_url,
        'expiry_hours': 48,  # Token expiry time in hours
    }

    return EmailService.send_template_email(
        subject=subject,
        template_name='emails/email_verification',
        context=context,
        recipient_list=[recipient_email]
    )
