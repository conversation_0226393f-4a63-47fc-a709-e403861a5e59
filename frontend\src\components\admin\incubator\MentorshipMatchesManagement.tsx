import React, { useState, useEffect } from 'react';
import { Search, Users, Calendar, CheckCircle, XCircle, Clock, ArrowRight } from 'lucide-react';
import { DashboardLayout } from '../dashboard';
import { MentorshipMatch, mentorshipMatchesAPI } from '../../../services/incubatorApi';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';

const MentorshipMatchesManagement: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  // State management
  const [matches, setMatches] = useState<MentorshipMatch[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch mentorship matches
  const fetchMatches = async () => {
    try {
      setLoading(true);
      const data = await mentorshipMatchesAPI.getMentorshipMatches();
      setMatches(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error fetching mentorship matches:', error);
      setMatches([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMatches();
  }, []);

  // Filter matches based on search
  const filteredMatches = matches.filter(match => {
    return match.mentee.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      match.mentee.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      match.mentor_profile.user.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      match.mentor_profile.user.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      match.status.toLowerCase().includes(searchTerm.toLowerCase());
  });

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Get status color and icon
  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'active':
        return {
          color: 'bg-green-500/20 text-green-300',
          icon: <CheckCircle size={16} />,
          label: t('match.status.active', 'Active')
        };
      case 'completed':
        return {
          color: 'bg-blue-500/20 text-blue-300',
          icon: <CheckCircle size={16} />,
          label: t('match.status.completed', 'Completed')
        };
      case 'paused':
        return {
          color: 'bg-yellow-500/20 text-yellow-300',
          icon: <Clock size={16} />,
          label: t('match.status.paused', 'Paused')
        };
      case 'terminated':
        return {
          color: 'bg-red-500/20 text-red-300',
          icon: <XCircle size={16} />,
          label: t('match.status.terminated', 'Terminated')
        };
      default:
        return {
          color: 'bg-gray-500/20 text-gray-300',
          icon: <Clock size={16} />,
          label: status
        };
    }
  };

  // Calculate duration
  const calculateDuration = (startDate: string, endDate?: string) => {
    const start = new Date(startDate);
    const end = endDate ? new Date(endDate) : new Date();
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 30) {
      return `${diffDays} ${t('common.days', 'days')}`;
    } else if (diffDays < 365) {
      const months = Math.floor(diffDays / 30);
      return `${months} ${t('common.months', 'months')}`;
    } else {
      const years = Math.floor(diffDays / 365);
      return `${years} ${t('common.years', 'years')}`;
    }
  };

  return (
    <DashboardLayout currentPage="incubator">
      {/* Header */}
      <div className={`mb-8 ${isRTL ? "text-right" : ""}`}>
        <h1 className="text-2xl font-bold text-white">{t('admin.mentorship.matches.management', 'Mentorship Matches Management')}</h1>
        <div className="text-gray-400 mt-1">{t('admin.mentorship.matches.description', 'View and manage active mentorship relationships')}</div>
      </div>

      {/* Search */}
      <div className="mb-6">
        <div className="relative">
          <Search size={20} className={`absolute top-3 text-gray-400 ${isRTL ? "right-3" : "left-3"}`} />
          <input
            type="text"
            placeholder={t('admin.search.matches', 'Search mentorship matches...')}
            value={searchTerm}
            onChange={handleSearchChange}
            className={`w-full bg-indigo-900/50 border border-indigo-800 rounded-lg py-2.5 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 ${isRTL ? "pr-10 pl-4" : "pl-10 pr-4"}`}
          />
        </div>
      </div>

      {/* Matches List */}
      {loading ? (
        <div className="flex justify-center py-12">
          <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      ) : filteredMatches.length > 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredMatches.map((match) => {
            const statusDisplay = getStatusDisplay(match.status);
            return (
              <div key={match.id} className="bg-indigo-900/30 backdrop-blur-sm rounded-xl overflow-hidden border border-indigo-800/50 hover:border-purple-500/50 transition-all duration-300 hover:shadow-glow">
                <div className="p-6">
                  <div className={`flex justify-between items-start mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className="flex-1">
                      <div className={`flex items-center space-x-2 mb-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 ${statusDisplay.color} ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                          {statusDisplay.icon}
                          <span>{statusDisplay.label}</span>
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Mentor and Mentee */}
                  <div className="space-y-4">
                    <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
                      {/* Mentor */}
                      <div className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                        <div className="w-12 h-12 rounded-full bg-blue-700 flex items-center justify-center text-white text-sm font-bold">
                          {match.mentor_profile.user.first_name.charAt(0)}{match.mentor_profile.user.last_name.charAt(0)}
                        </div>
                        <div>
                          <p className="text-white font-medium">
                            {match.mentor_profile.user.first_name} {match.mentor_profile.user.last_name}
                          </p>
                          <p className="text-gray-400 text-sm">{t('match.mentor', 'Mentor')}</p>
                          <p className="text-gray-400 text-xs">{match.mentor_profile.position}</p>
                        </div>
                      </div>

                      {/* Arrow */}
                      <div className="flex-shrink-0 mx-4">
                        <ArrowRight size={20} className={`text-gray-400 ${isRTL ? "rotate-180" : ""}`} />
                      </div>

                      {/* Mentee */}
                      <div className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                        <div className="w-12 h-12 rounded-full bg-purple-700 flex items-center justify-center text-white text-sm font-bold">
                          {match.mentee.first_name.charAt(0)}{match.mentee.last_name.charAt(0)}
                        </div>
                        <div>
                          <p className="text-white font-medium">
                            {match.mentee.first_name} {match.mentee.last_name}
                          </p>
                          <p className="text-gray-400 text-sm">{t('match.mentee', 'Mentee')}</p>
                        </div>
                      </div>
                    </div>

                    {/* Match Details */}
                    <div className="grid grid-cols-2 gap-4 pt-4 border-t border-indigo-800/50">
                      <div>
                        <div className={`flex items-center space-x-2 text-sm ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                          <Calendar size={16} className="text-blue-400" />
                          <span className="text-gray-300">{t('match.started', 'Started')}</span>
                        </div>
                        <p className="text-white text-sm mt-1">{new Date(match.start_date).toLocaleDateString()}</p>
                      </div>

                      <div>
                        <div className={`flex items-center space-x-2 text-sm ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                          <Clock size={16} className="text-green-400" />
                          <span className="text-gray-300">{t('match.duration', 'Duration')}</span>
                        </div>
                        <p className="text-white text-sm mt-1">{calculateDuration(match.start_date, match.end_date)}</p>
                      </div>

                      {match.sessions_count !== undefined && (
                        <div>
                          <div className={`flex items-center space-x-2 text-sm ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                            <Users size={16} className="text-purple-400" />
                            <span className="text-gray-300">{t('match.sessions', 'Sessions')}</span>
                          </div>
                          <p className="text-white text-sm mt-1">{match.sessions_count}</p>
                        </div>
                      )}

                      {match.end_date && (
                        <div>
                          <div className={`flex items-center space-x-2 text-sm ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                            <Calendar size={16} className="text-red-400" />
                            <span className="text-gray-300">{t('match.ended', 'Ended')}</span>
                          </div>
                          <p className="text-white text-sm mt-1">{new Date(match.end_date).toLocaleDateString()}</p>
                        </div>
                      )}
                    </div>

                    {match.notes && (
                      <div className="pt-3 border-t border-indigo-800/50">
                        <h4 className="text-sm font-medium text-gray-300 mb-1">{t('match.notes', 'Notes')}</h4>
                        <p className="text-gray-300 text-sm line-clamp-2">{match.notes}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        <div className="text-center py-12">
          <Users size={48} className="mx-auto text-gray-600 mb-4" />
          <h3 className="text-lg font-medium text-gray-400 mb-2">
            {searchTerm 
              ? t('admin.no.matches.found', 'No mentorship matches found')
              : t('admin.no.matches', 'No mentorship matches yet')
            }
          </h3>
          <p className="text-gray-500 mb-4">
            {searchTerm
              ? t('admin.try.different.search', 'Try adjusting your search')
              : t('admin.no.matches.description', 'Mentorship matches will appear here when created')
            }
          </p>
        </div>
      )}
    </DashboardLayout>
  );
};

export default MentorshipMatchesManagement;
