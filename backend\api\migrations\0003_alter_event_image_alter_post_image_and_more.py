# Generated by Django 5.1.7 on 2025-05-14 20:21

import api.storage
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0002_comment_moderated_at_comment_moderated_by_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='event',
            name='image',
            field=models.ImageField(blank=True, null=True, storage=api.storage.OptimizedImageStorage(), upload_to='event_images/'),
        ),
        migrations.AlterField(
            model_name='post',
            name='image',
            field=models.ImageField(blank=True, null=True, storage=api.storage.OptimizedImageStorage(), upload_to='post_images/'),
        ),
        migrations.AlterField(
            model_name='resource',
            name='image',
            field=models.ImageField(blank=True, null=True, storage=api.storage.OptimizedImageStorage(), upload_to='resource_images/'),
        ),
    ]
