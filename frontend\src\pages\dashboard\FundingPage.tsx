import React, { useState, useEffect } from 'react';
import { Search, Filter, DollarSign, Calendar, MapPin, Building, ExternalLink, Clock, Users, TrendingUp, AlertCircle, RefreshCw, Plus, Eye } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import AuthenticatedLayout from '../../components/layout/AuthenticatedLayout';
import { useAppDispatch, useAppSelector } from '../../store/hooks';
import { useLanguage } from '../../hooks/useLanguage';
import { fetchFundingOpportunities, fetchFundingApplications } from '../../store/incubatorSlice';
import { FundingOpportunity } from '../../services/incubatorApi';

// Remove duplicate interface since we're importing from services

const FundingPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);
  const { fundingOpportunities, fundingApplications, isLoading, error } = useAppSelector((state) => state.incubator);

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [selectedStage, setSelectedStage] = useState<string>('all');
  const [showMyApplications, setShowMyApplications] = useState(false);
  const [selectedOpportunity, setSelectedOpportunity] = useState<FundingOpportunity | null>(null);

  // Fetch real data from API
  useEffect(() => {
    dispatch(fetchFundingOpportunities());
    dispatch(fetchFundingApplications({}));
  }, [dispatch]);

  const handleRefresh = () => {
    dispatch(fetchFundingOpportunities());
    dispatch(fetchFundingApplications({}));
  };

  // Filter opportunities based on search and filters
  const filteredOpportunities = fundingOpportunities.filter(opportunity => {
    const matchesSearch = opportunity.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         opportunity.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = selectedType === 'all' || opportunity.funding_type === selectedType;
    const matchesStage = selectedStage === 'all'; // Add stage filtering when available in API

    return matchesSearch && matchesType && matchesStage && opportunity.status === 'active';
  });

  // Get user's applications for this page
  const userApplications = fundingApplications.filter(app => app.applicant_id === user?.id);

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'grant': return <DollarSign size={16} className="text-green-400" />;
      case 'investment': return <TrendingUp size={16} className="text-blue-400" />;
      case 'loan': return <Building size={16} className="text-purple-400" />;
      case 'competition': return <Users size={16} className="text-orange-400" />;
      default: return <DollarSign size={16} className="text-gray-400" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'grant': return 'bg-green-600/20 text-green-300 border-green-600/30';
      case 'investment': return 'bg-blue-600/20 text-blue-300 border-blue-600/30';
      case 'loan': return 'bg-purple-600/20 text-purple-300 border-purple-600/30';
      case 'competition': return 'bg-orange-600/20 text-orange-300 border-orange-600/30';
      default: return 'bg-gray-600/20 text-gray-300 border-gray-600/30';
    }
  };

  const formatDeadline = (deadline: string) => {
    const date = new Date(deadline);
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) return t('funding.expired');
    if (diffDays === 0) return t('funding.today');
    if (diffDays === 1) return t('funding.tomorrow');
    if (diffDays <= 7) return t('funding.daysLeft', { days: diffDays });
    return date.toLocaleDateString();
  };

  const hasUserApplied = (opportunityId: number) => {
    return userApplications.some(app => app.funding_opportunity === opportunityId);
  };

  return (
    <AuthenticatedLayout>
      <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-white mb-2">
              {t('funding.title')}
            </h1>
            <p className="text-gray-300">
              {t('funding.description')}
            </p>
          </div>
          <div className={`flex items-center space-x-3 mt-4 sm:mt-0 ${isRTL ? 'space-x-reverse' : ''}`}>
            <button
              onClick={handleRefresh}
              className="p-2 bg-indigo-900/50 hover:bg-indigo-800/50 rounded-lg transition-colors"
              title={t('common.refresh')}
            >
              <RefreshCw size={18} className="text-gray-300" />
            </button>
            <button
              onClick={() => setShowMyApplications(!showMyApplications)}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                showMyApplications
                  ? 'bg-purple-600 text-white'
                  : 'bg-indigo-900/50 text-gray-300 hover:bg-indigo-800/50'
              }`}
            >
              <Eye size={16} className={`${isRTL ? 'ml-2' : 'mr-2'} inline`} />
              {t('funding.myApplications')}
            </button>
          </div>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
            <div className="flex items-center">
              <DollarSign size={24} className="text-green-400 mr-3" />
              <div>
                <p className="text-sm text-gray-400">{t('funding.totalOpportunities')}</p>
                <p className="text-2xl font-bold text-white">{filteredOpportunities.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
            <div className="flex items-center">
              <Users size={24} className="text-blue-400 mr-3" />
              <div>
                <p className="text-sm text-gray-400">{t('funding.myApplications')}</p>
                <p className="text-2xl font-bold text-white">{userApplications.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
            <div className="flex items-center">
              <TrendingUp size={24} className="text-purple-400 mr-3" />
              <div>
                <p className="text-sm text-gray-400">{t('funding.activeOpportunities')}</p>
                <p className="text-2xl font-bold text-white">
                  {fundingOpportunities.filter(op => op.status === 'active').length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Search */}
            <div className="relative">
              <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder={t('funding.searchPlaceholder')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-white/20 border border-white/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>

            {/* Type Filter */}
            <div className="relative">
              <Filter size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-white/20 border border-white/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="all">{t('funding.allTypes')}</option>
                <option value="grant">{t('funding.grant')}</option>
                <option value="investment">{t('funding.investment')}</option>
                <option value="loan">{t('funding.loan')}</option>
                <option value="competition">{t('funding.competition')}</option>
              </select>
            </div>

            {/* Stage Filter */}
            <div className="relative">
              <select
                value={selectedStage}
                onChange={(e) => setSelectedStage(e.target.value)}
                className="w-full px-4 py-2 bg-white/20 border border-white/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="all">{t('funding.allStages')}</option>
                <option value="seed">{t('funding.seedStage')}</option>
                <option value="early">{t('funding.earlyStage')}</option>
                <option value="growth">{t('funding.growthStage')}</option>
              </select>
            </div>
          </div>
        </div>

        {/* Error State */}
        {error && (
          <div className="bg-red-900/50 backdrop-blur-sm rounded-lg p-6 border border-red-800/50">
            <div className="flex items-center">
              <AlertCircle size={20} className="text-red-400 mr-3" />
              <div>
                <h3 className="text-lg font-semibold text-white mb-1">
                  {t('common.error')}
                </h3>
                <p className="text-gray-300">{error}</p>
              </div>
            </div>
            <button
              onClick={handleRefresh}
              className="mt-4 px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg text-white font-medium transition-colors"
            >
              {t('common.tryAgain')}
            </button>
          </div>
        )}

        {/* Funding Opportunities */}
        {isLoading ? (
          <div className="bg-indigo-900/50 backdrop-blur-sm rounded-lg p-12 border border-indigo-800/50">
            <div className="text-center">
              <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
              <h3 className="text-lg font-semibold text-white mb-2">
                {t('funding.loadingOpportunities')}
              </h3>
              <p className="text-gray-400">
                {t('funding.loadingOpportunitiesDescription')}
              </p>
            </div>
          </div>
        ) : filteredOpportunities.length > 0 ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredOpportunities.map((opportunity) => {
              const userApplied = hasUserApplied(opportunity.id);
              return (
                <div
                  key={opportunity.id}
                  className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:border-purple-500/50 transition-all duration-300"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold text-white mb-2">
                        {opportunity.title}
                      </h3>
                      <div className="flex items-center text-gray-300 mb-2">
                        <Building size={16} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                        {opportunity.provider?.first_name} {opportunity.provider?.last_name}
                      </div>
                    </div>
                    <div className="flex flex-col items-end space-y-2">
                      <div className={`px-3 py-1 rounded-full text-xs font-medium border ${getTypeColor(opportunity.funding_type)}`}>
                        <div className="flex items-center">
                          {getTypeIcon(opportunity.funding_type)}
                          <span className={`${isRTL ? 'mr-1' : 'ml-1'} capitalize`}>
                            {t(`funding.${opportunity.funding_type}`)}
                          </span>
                        </div>
                      </div>
                      {userApplied && (
                        <div className="px-2 py-1 bg-green-600/20 text-green-300 border border-green-600/30 rounded-full text-xs">
                          {t('funding.applied')}
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="space-y-3 mb-4">
                    <div className="flex items-center text-gray-300">
                      <DollarSign size={16} className={`${isRTL ? 'ml-2' : 'mr-2'} text-green-400`} />
                      <span className="font-medium">${opportunity.amount.toLocaleString()}</span>
                    </div>
                    <div className="flex items-center text-gray-300">
                      <Calendar size={16} className={`${isRTL ? 'ml-2' : 'mr-2'} text-blue-400`} />
                      <span>{t('funding.deadline')}: {formatDeadline(opportunity.application_deadline)}</span>
                    </div>
                  </div>

                  <p className="text-gray-300 text-sm mb-4 line-clamp-3">
                    {opportunity.description}
                  </p>

                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-white mb-2">{t('funding.eligibility')}:</h4>
                    <p className="text-xs text-gray-400">
                      {opportunity.eligibility_criteria}
                    </p>
                  </div>

                  <div className="flex space-x-2">
                    <button
                      onClick={() => setSelectedOpportunity(opportunity)}
                      className="flex-1 px-4 py-2 bg-indigo-600 hover:bg-indigo-700 rounded-lg font-medium transition-colors flex items-center justify-center"
                    >
                      <Eye size={16} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                      {t('funding.viewDetails')}
                    </button>
                    {!userApplied && (
                      <button
                        onClick={() => setSelectedOpportunity(opportunity)}
                        className="flex-1 px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300 flex items-center justify-center"
                      >
                        <Plus size={16} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                        {t('funding.apply')}
                      </button>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-12">
            <DollarSign size={48} className="mx-auto text-gray-500 mb-4" />
            <h3 className="text-xl font-semibold mb-2">{t('funding.noOpportunities')}</h3>
            <p className="text-gray-400">
              {searchTerm ? t('funding.tryDifferentSearch') : t('funding.checkBackLater')}
            </p>
          </div>
        )}
      </div>
              </div>
        </div>
      </div>
    </AuthenticatedLayout>
  );
};

export default FundingPage;
