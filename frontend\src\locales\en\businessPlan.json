{"businessPlan": {"businessIdeaOptionalNote": "Templates work great on their own! You can always connect a business idea later.", "selectBusinessIdea": "Select a business idea or skip", "title": "Business Plans", "description": "Create and manage your business plans", "createNew": "Create New Business Plan", "noPlans": "No business plans found", "loading": "Loading business plans...", "error": "Error loading business plans", "success": "Business plan operation successful", "createNewPlan": "Create Business Plan", "businessIdea": "Business Idea", "planTitle": "Business Plan Title", "enterPlanTitle": "Enter your business plan title", "template": "Template", "selectTemplate": "Select a template", "generateCustomTemplate": "Generate Custom Template", "create": "Create Business Plan", "edit": "Edit Business Plan", "delete": "Delete Business Plan", "view": "View Business Plan", "save": "Save Business Plan", "cancel": "Cancel", "templates": {"consulting": "Consulting Business Plan", "consultingDesc": "Professional template for consulting and service-based businesses", "ecommerce": "E-commerce Business Plan", "ecommerceDesc": "Comprehensive template for online retail and e-commerce ventures", "lean": "Lean Startup Plan", "leanDesc": "Streamlined template focusing on key business model components", "mobileApp": "Mobile App Business Plan", "mobileAppDesc": "Specialized template for mobile application development and monetization", "nonprofit": "Nonprofit Organization Plan", "nonprofitDesc": "Template designed for nonprofit organizations and social enterprises", "restaurant": "Restaurant Business Plan", "restaurantDesc": "Complete template for restaurant and food service businesses", "retail": "Retail Business Plan", "retailDesc": "Template for brick-and-mortar retail and physical store businesses", "saas": "SaaS Business Plan", "saasDesc": "Template for Software as a Service and subscription-based businesses", "manufacturing": "Manufacturing Business Plan", "manufacturingDesc": "Industrial template for manufacturing and production businesses", "healthcare": "Healthcare Business Plan", "healthcareDesc": "Specialized template for healthcare services and medical practices", "fintech": "Fintech Business Plan", "fintechDesc": "Template for financial technology and payment solution businesses", "greenBusiness": "Green Business Plan", "greenBusinessDesc": "Sustainable business template focusing on environmental impact", "socialImpact": "Social Impact Business Plan", "socialImpactDesc": "Template for businesses with social mission and community impact", "marketplace": "Marketplace Business Plan", "marketplaceDesc": "Template for platform businesses connecting buyers and sellers", "subscription": "Subscription Business Plan", "subscriptionDesc": "Template for recurring revenue and subscription-based models"}, "myPlans": "My Business Plans", "allPlans": "All Business Plans", "draftPlans": "Draft Plans", "publishedPlans": "Published Plans", "sharedPlans": "Shared Plans", "totalPlans": "Total Plans", "completedPlans": "Completed Plans", "avgCompletion": "Avg Completion", "createStandard": "Create Standard Plan", "createAIPowered": "Create AI-Powered Plan", "manageTemplates": "Manage Templates", "createDescription": "Create and manage comprehensive business plans", "loadingPlans": "Loading Plans", "loadingPlansDescription": "Please wait while we load your business plans...", "noPlansYet": "No business plans yet", "createFirstPlanButton": "Create Your First Plan", "planDetails": "Plan Details", "executiveSummary": "Executive Summary", "marketAnalysis": "Market Analysis", "competitiveAnalysis": "Competitive Analysis", "marketingStrategy": "Marketing Strategy", "operationsStrategy": "Operations Strategy", "managementTeam": "Management Team", "financialProjections": "Financial Projections", "fundingRequest": "Funding Request", "appendices": "Appendices", "planStatus": "Plan Status", "draft": "Draft", "inReview": "In Review", "approved": "Approved", "published": "Published", "rejected": "Rejected", "planType": "Plan Type", "startup": "Startup", "expansion": "Expansion", "acquisition": "Acquisition", "internal": "Internal", "investor": "Investor", "loan": "Loan Application", "planLength": "Plan Length", "short": "Short (5-10 pages)", "medium": "Medium (10-20 pages)", "long": "Long (20+ pages)", "customLength": "Custom Length", "industry": "Industry", "selectIndustry": "Select Industry", "targetMarket": "Target Market", "businessModel": "Business Model", "revenueModel": "Revenue Model", "keyMetrics": "Key Metrics", "milestones": "Milestones", "timeline": "Timeline", "budget": "Budget", "resources": "Resources", "risks": "Risks", "opportunities": "Opportunities", "assumptions": "Assumptions", "validation": "Validation", "feedback": "<PERSON><PERSON><PERSON>", "collaboration": "Collaboration", "sharing": "Sharing", "export": "Export", "import": "Import", "duplicate": "Duplicate", "archive": "Archive", "restore": "Rest<PERSON>", "version": "Version", "history": "History", "comments": "Comments", "attachments": "Attachments", "tags": "Tags", "categories": "Categories", "search": "Search Plans", "filter": "Filter <PERSON>", "sort": "Sort Plans", "noDraftPlans": "No draft plans", "noPublishedPlans": "No published plans", "createFirstPlan": "Create your first business plan", "planCreated": "Business plan created successfully", "planUpdated": "Business plan updated successfully", "planDeleted": "Business plan deleted successfully", "planPublished": "Business plan published successfully", "planArchived": "Business plan archived successfully", "planRestored": "Business plan restored successfully", "planShared": "Business plan shared successfully", "planExported": "Business plan exported successfully", "planImported": "Business plan imported successfully", "planDuplicated": "Business plan duplicated successfully", "confirmDelete": "Are you sure you want to delete this business plan?", "confirmArchive": "Are you sure you want to archive this business plan?", "confirmPublish": "Are you sure you want to publish this business plan?", "unsavedChanges": "Unsaved Changes", "validationErrors": "Please fix the validation errors before saving", "requiredFields": "Please fill in all required fields", "planTooLong": "Plan content is too long. Please reduce the length.", "planTooShort": "Plan content is too short. Please add more details.", "invalidFormat": "Invalid format. Please check your input.", "uploadError": "Error uploading file. Please try again.", "downloadError": "Error downloading file. Please try again.", "shareError": "Error sharing plan. Please try again.", "loadError": "Error loading plan. Please refresh and try again.", "saveError": "Error saving plan. Please try again.", "networkError": "Network error. Please check your internet connection and try again.", "enterIndustryRequired": "Please enter an industry to generate a template", "completion": "Completion", "lastUpdated": "Last Updated", "viewPlan": "View Plan", "searchPlaceholder": "Search business plans...", "allStatuses": "All Statuses", "inProgress": "In Progress", "completed": "Completed", "templateGenerated": "Template generated successfully for {{industry}}", "failedToGenerateTemplate": "Failed to generate template. Please try again.", "basedOn": "Based on", "hideTemplateGenerator": "Hide Template Generator", "aiTemplateGenerator": "AI Template Generator", "generateCustomTemplateDesc": "Generate a custom template for your specific industry", "enterIndustry": "Enter industry (e.g., Technology, Healthcare)", "generate": "Generate", "generating": "Generating...", "creating": "Creating...", "failedToCreate": "Failed to create business plan", "businessPlanNotFound": "Business Plan Not Found", "retryAttempt": "Retry attempt {{attempt}} of 3...", "businessPlanFor": "Business Plan for", "noContentAvailable": "No content available yet", "businessPlanStructure": "Business Plan Structure", "noSectionsCreated": "No sections have been created yet. Please add sections to your business plan.", "sectionDataKey": "Section", "contentDataKey": "Content", "statusDataKey": "Status", "businessPlanInfo": "Business Plan Information", "exportPDF": "Export PDF", "analyzePlan": "Analyze Plan", "analyzing": "Analyzing...", "completionLabel": "Completion", "sectionsProgress": "sections", "businessPlanSections": "Business Plan Sections", "guidance": "Guidance", "generateWithAI": "Generate with AI", "saving": "Saving...", "saveChanges": "Save Changes", "saveSection": "Save Section", "startWriting": "Start writing your content here...", "noSectionSelected": "No Section Selected", "selectSectionPrompt": "Select a section from the list to start editing your business plan.", "aiFeedback": "AI Feedback", "overallFeedback": "Overall Feedback", "strengths": "Strengths", "areasForImprovement": "Areas for Improvement", "suggestions": "Suggestions", "unsavedChangesMessage": "You have unsaved changes in this section. What would you like to do?", "saveAndContinue": "Save & Continue", "discardChanges": "Discard Changes", "preparingExport": "Preparing PDF export...", "exportSuccess": "PDF exported successfully!", "exportFailed": "Failed to export PDF. Please try again.", "noBusinessPlanId": "No business plan ID provided", "failedToLoad": "Failed to load business plan. Please try again.", "loadingPlan": "Loading your business plan...", "sectionSavedSuccessfully": "Section saved successfully", "failedToSave": "Failed to save section. Please try again.", "contentGeneratedSuccessfully": "Content generated successfully", "failedToGenerate": "Failed to generate content. Please try again.", "businessPlanAnalyzed": "Business plan analyzed successfully", "failedToAnalyze": "Failed to analyze business plan. Please try again.", "notFoundMessage": "The business plan you're looking for could not be found.", "goBack": "Go Back", "planCompleted": "Business plan completed!", "percentageRemaining": "{{percentage}}% remaining", "loadingTip": "This may take a few moments while we load your business plan data...", "troubleshootingTip": "If this problem persists, please check your internet connection or contact support.", "keyboardShortcuts": "Keyboard Shortcuts", "shortcutSave": "Save section", "shortcutGenerate": "Generate content", "shortcutClose": "Close dialogs", "autoSaved": "Auto-saved", "autoSaveFailed": "Auto-save failed", "selectSection": "Select section: {{section}}", "planNotFound": "Business plan not found. It may have been deleted or you don't have access to it.", "accessDenied": "Access denied. You don't have permission to view this business plan."}, "analytics": {"viewAnalytics": "View Analytics", "businessPlanAnalytics": "Business Plan Analytics", "insightsDescription": "Comprehensive insights into your business plan activities", "loading": "Loading Analytics", "loadingDescription": "Please wait while we gather your analytics data...", "error": "Analytics Error", "errorLoading": "Failed to load analytics data", "last7Days": "Last 7 Days", "last30Days": "Last 30 Days", "last90Days": "Last 90 Days", "lastYear": "Last Year", "timeSpent": "Time Spent", "collaborators": "Collaborators", "exports": "Exports", "successRate": "Success Rate", "yourProgress": "Your Progress", "totalPlans": "Total Plans", "completed": "Completed", "inProgress": "In Progress", "drafts": "Drafts", "avgCompletion": "Avg Completion", "mostUsedTemplates": "Most Used Templates", "success": "success", "noTemplateData": "No template usage data available", "collaborationActivity": "Collaboration Activity", "noCollaborationData": "No collaboration data available", "exportFormats": "Export Formats", "noExportData": "No export data available"}}