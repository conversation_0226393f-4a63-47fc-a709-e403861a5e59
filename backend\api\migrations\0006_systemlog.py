# Generated by Django 4.2.21 on 2025-05-31 08:22

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("api", "0005_membershipapplication"),
    ]

    operations = [
        migrations.CreateModel(
            name="SystemLog",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "timestamp",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                (
                    "level",
                    models.CharField(
                        choices=[
                            ("info", "Info"),
                            ("warning", "Warning"),
                            ("error", "Error"),
                            ("success", "Success"),
                        ],
                        db_index=True,
                        max_length=20,
                    ),
                ),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("authentication", "Authentication"),
                            ("database", "Database"),
                            ("api", "API"),
                            ("backup", "Backup"),
                            ("system", "System"),
                            ("user_action", "User Action"),
                            ("security", "Security"),
                            ("performance", "Performance"),
                            ("maintenance", "Maintenance"),
                        ],
                        db_index=True,
                        max_length=50,
                    ),
                ),
                ("message", models.TextField()),
                ("details", models.TextField(blank=True, null=True)),
                ("source", models.CharField(blank=True, max_length=100, null=True)),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("user_agent", models.TextField(blank=True, null=True)),
                ("request_id", models.CharField(blank=True, max_length=100, null=True)),
                ("metadata", models.JSONField(blank=True, default=dict)),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-timestamp"],
                "indexes": [
                    models.Index(
                        fields=["timestamp", "level"],
                        name="api_systeml_timesta_c32fa9_idx",
                    ),
                    models.Index(
                        fields=["category", "timestamp"],
                        name="api_systeml_categor_784cbc_idx",
                    ),
                    models.Index(
                        fields=["user", "timestamp"],
                        name="api_systeml_user_id_5e46ed_idx",
                    ),
                ],
            },
        ),
    ]
