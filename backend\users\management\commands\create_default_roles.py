from django.core.management.base import BaseCommand
from django.db import transaction
from users.models import UserRole


class Command(BaseCommand):
    help = 'Create default user roles in the system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force update existing roles',
        )

    def handle(self, *args, **options):
        force_update = options.get('force', False)
        
        self.stdout.write(
            self.style.SUCCESS('Creating default user roles...')
        )

        # Define default roles with their configurations
        default_roles = [
            {
                'name': 'admin',
                'display_name': 'Administrator',
                'description': 'Full system administrator with all permissions',
                'permission_level': 'admin',
                'requires_approval': False,
            },
            {
                'name': 'moderator',
                'display_name': 'Moderator',
                'description': 'Content moderator with moderation permissions',
                'permission_level': 'moderate',
                'requires_approval': True,
            },
            {
                'name': 'mentor',
                'display_name': '<PERSON>tor',
                'description': 'Business mentor providing guidance to entrepreneurs',
                'permission_level': 'write',
                'requires_approval': True,
            },
            {
                'name': 'investor',
                'display_name': 'Investor',
                'description': 'Investor looking for investment opportunities',
                'permission_level': 'write',
                'requires_approval': True,
            },
            {
                'name': 'user',
                'display_name': 'Regular User',
                'description': 'Standard user with basic access',
                'permission_level': 'read',
                'requires_approval': False,
            },
        ]

        with transaction.atomic():
            created_count = 0
            updated_count = 0
            
            for role_data in default_roles:
                role, created = UserRole.objects.get_or_create(
                    name=role_data['name'],
                    defaults=role_data
                )
                
                if created:
                    created_count += 1
                    self.stdout.write(f'Created role: {role.display_name}')
                elif force_update:
                    # Update existing role
                    for field, value in role_data.items():
                        if field != 'name':  # Don't update the name field
                            setattr(role, field, value)
                    role.save()
                    updated_count += 1
                    self.stdout.write(f'Updated role: {role.display_name}')
                else:
                    self.stdout.write(f'Role already exists: {role.display_name}')

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully processed roles! Created: {created_count}, Updated: {updated_count}'
            )
        )
