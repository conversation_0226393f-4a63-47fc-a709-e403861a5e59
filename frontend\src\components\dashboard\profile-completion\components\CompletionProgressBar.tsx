import React from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../hooks/useLanguage';

interface CompletionProgressBarProps {
  percentage: number;
  statusColor: string;
}

const CompletionProgressBar: React.FC<CompletionProgressBarProps> = ({ percentage,
  statusColor
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  return (
    <div className="w-full mb-4">
      <div className={`flex justify-between text-sm mb-1 ${isRTL ? "flex-row-reverse" : ""}`}>
        <span>{t('dashboard.profileCompletion.completion')}</span>
        <span>{percentage}%</span>
      </div>
      <div className="w-full bg-gray-700 rounded-full h-2">
        <div
          className={`h-2 rounded-full ${statusColor}`}
          style={{ width: `${percentage}%` }}
        ></div>
      </div>
    </div>
  );
};

export default CompletionProgressBar;
