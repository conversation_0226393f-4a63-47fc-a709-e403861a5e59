import React, { useState, useEffect, useRef } from 'react';
import { Skeleton } from './Skeleton';

import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
interface LazyImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  alt: string;
  placeholderHeight?: string | number;
  placeholderWidth?: string | number;
  placeholderClassName?: string;
  threshold?: number;
  onLoad?: () => void;
  onError?: () => void;
}

/**
 * LazyImage component that loads images only when they enter the viewport
 */
const LazyImage: React.FC<LazyImageProps> = ({ src,
  alt,
  placeholderHeight = '100%',
  placeholderWidth = '100%',
  placeholderClassName = '',
  threshold = 0.1,
  onLoad,
  onError,
  className = '',
  ...props
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const [error, setError] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Set up intersection observer to detect when image enters viewport
  useEffect(() => {
    if (!imgRef.current) return;

    observerRef.current = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        setIsInView(entry.isIntersecting);
      },
      {
        threshold,
        rootMargin: '200px', // Start loading a bit before the image enters the viewport
      }
    );

    observerRef.current.observe(imgRef.current);

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [threshold, onLoad, onError]);

  // Handle image loading
  const handleLoad = () => {
    setIsLoaded(true);
    if (onLoad) onLoad();
  };

  // Handle image error
  const handleError = () => {
    setError(true);
    setIsLoaded(true);
    if (onError) onError();
  };

  return (
    <div
      ref={imgRef}
      className="relative overflow-hidden"
      style={{
        height: props.height || placeholderHeight,
        width: props.width || placeholderWidth,
      }}
    >
      {(!isLoaded || !isInView) && !error && (
        <Skeleton
          className={`absolute inset-0 ${placeholderClassName}`}
        />
      )}

      {isInView && (
        <img
          src={src}
          alt={alt}
          className={`${className} ${isLoaded ? 'opacity-100' : 'opacity-0'} transition-opacity duration-300`}
          onLoad={handleLoad}
          onError={handleError}
          {...props}
        />
      )}

      {error && (
        <div className={`absolute inset-0 flex items-center justify-center text-sm glass-light text-glass-primary ${isRTL ? "flex-row-reverse" : ""}`}>
          {t('common.failedToLoadImage')}
        </div>
      )}
    </div>
  );
};

LazyImage.displayName = "LazyImage";

export default LazyImage;
