import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { fetchCategories, fetchReputationLeaderboard } from '../store/forumSlice';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import ForumCategories from '../components/forum/ForumCategories';
import ReputationLeaderboard from '../components/forum/ReputationLeaderboard';
import { MessageSquare, Users, Award, Info } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../hooks/useLanguage';
import { RTLIcon, RTLText, RTLFlex } from '../components/common';

const ForumPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const dispatch = useAppDispatch();
  const { categories, reputationLeaderboard, loading, error } = useAppSelector((state) => state.forum);
  const { user } = useAppSelector((state) => state.auth);
  const { language } = useAppSelector(state => state.language);

  useEffect(() => {
    const loadForumData = async () => {
      try {
        console.log("Loading forum data...");
        await dispatch(fetchCategories()).unwrap();
        console.log("Categories loaded successfully");

        await dispatch(fetchReputationLeaderboard()).unwrap();
        console.log("Reputation leaderboard loaded successfully");
      } catch (error) {
        console.error('Error loading forum data:', error);
        // The error will be handled by the Redux slice
      }
    };

    loadForumData();
  }, [dispatch]);

  return (
    <div className={`min-h-screen flex flex-col bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
      {/* Simple navigation header */}
      <header className="bg-indigo-900/50 backdrop-blur-sm border-b border-indigo-800">
        <div className="container mx-auto px-4 py-4">
          <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
            <Link to="/" className="text-xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
              {t('app.name')}
            </Link>
            <div className={`flex items-center space-x-4 ${isRTL ? "flex-row-reverse" : ""}`}>
              <Link to="/dashboard" className="text-purple-400 hover:text-purple-300 transition-colors">
                {t('nav.myDashboard')}
              </Link>
              <Link to="/logout" className="text-gray-400 hover:text-white transition-colors">
                {t('nav.logout')}
              </Link>
            </div>
          </div>
        </div>
      </header>

      <main className={`flex-grow container mx-auto px-4 py-8 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className="mb-8">
          <RTLFlex as="h1" className="text-3xl font-bold" align="center">
            <RTLIcon icon={MessageSquare} size={28} className={`${language === 'ar' ? 'ml-3' : 'mr-3'} text-purple-400`} />
            {t('forum.title')}
          </RTLFlex>
          <RTLText as="div" className="text-gray-400 mt-2">
            {t('forum.description')}
          </RTLText>
        </div>

        {error && (
          <div className="bg-red-900/30 border border-red-800 rounded-lg p-4 mb-6">
            <RTLFlex className="items-center">
              <RTLIcon icon={Info} size={20} className={`text-red-400 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
              <div>
                <RTLText as="div" className="text-red-300 font-medium">{t('forum.loadingError')}</RTLText>
                <RTLText as="div" className="text-red-400 text-sm mt-1">{error}</RTLText>
                <RTLText as="div" className="text-red-400 text-sm mt-1">
                  {t('forum.refreshPage')}
                </RTLText>
              </div>
            </RTLFlex>
          </div>
        )}

        {loading && !categories.length && (
          <div className={`flex justify-center py-12 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className="text-center">
              <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
              <RTLText as="div" className="text-gray-400">{t('forum.loadingData')}</RTLText>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            {loading && categories.length === 0 ? (
              <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
                <div className={`flex justify-center items-center h-40 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
                </div>
              </div>
            ) : (
              <ForumCategories categories={categories} />
            )}
          </div>

          <div className="space-y-6">
            {/* User Reputation Card */}
            <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
              <RTLFlex as="h2" className="text-xl font-semibold mb-4" align="center">
                <RTLIcon icon={Award} size={20} className={`${language === 'ar' ? 'ml-2' : 'mr-2'} text-yellow-400`} />
                {t('forum.yourReputation')}
              </RTLFlex>

              {user ? (
                <div className="space-y-4">
                  <RTLFlex className="justify-between">
                    <RTLText as="span" className="text-gray-400">{t('forum.level')}</RTLText>
                    <RTLText as="span" className="font-medium text-purple-300">{t('forum.activeMember')}</RTLText>
                  </RTLFlex>
                  <RTLFlex className="justify-between">
                    <RTLText as="span" className="text-gray-400">{t('forum.points')}</RTLText>
                    <RTLText as="span" className="font-medium text-purple-300">125</RTLText>
                  </RTLFlex>
                  <RTLFlex className="justify-between">
                    <RTLText as="span" className="text-gray-400">{t('forum.rank')}</RTLText>
                    <RTLText as="span" className="font-medium text-purple-300">#12</RTLText>
                  </RTLFlex>

                  <div className="pt-4">
                    <RTLFlex
                      as={Link}
                      to="/forum/reputation"
                      className="text-purple-400 hover:text-purple-300 text-sm justify-center"
                      align="center"
                    >
                      {t('forum.viewReputationDetails')}
                    </RTLFlex>
                  </div>
                </div>
              ) : (
                <RTLText as="div" className="text-gray-400">
                  {t('forum.signInForReputation')}
                </RTLText>
              )}
            </div>

            {/* Leaderboard */}
            <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
              <RTLFlex as="h2" className="text-xl font-semibold mb-4" align="center">
                <RTLIcon icon={Users} size={20} className={`${language === 'ar' ? 'ml-2' : 'mr-2'} text-blue-400`} />
                {t('forum.topContributors')}
              </RTLFlex>

              <ReputationLeaderboard leaderboard={reputationLeaderboard} loading={loading} />
            </div>

            {/* Forum Guidelines */}
            <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
              <RTLFlex as="h2" className="text-xl font-semibold mb-4" align="center">
                <RTLIcon icon={Info} size={20} className={`${language === 'ar' ? 'ml-2' : 'mr-2'} text-green-400`} />
                {t('forum.guidelines.title')}
              </RTLFlex>

              <ul className={`space-y-2 text-gray-300 text-sm ${language === 'ar' ? 'text-right' : 'text-left'}`}>
                <li><RTLText>• {t('forum.guidelines.respectful')}</RTLText></li>
                <li><RTLText>• {t('forum.guidelines.stayOnTopic')}</RTLText></li>
                <li><RTLText>• {t('forum.guidelines.shareKnowledge')}</RTLText></li>
                <li><RTLText>• {t('forum.guidelines.upvote')}</RTLText></li>
                <li><RTLText>• {t('forum.guidelines.report')}</RTLText></li>
              </ul>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default ForumPage;
