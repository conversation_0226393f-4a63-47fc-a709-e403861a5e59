import React, { useState, useEffect } from 'react';
import { Save, X, Users, Edit } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { MentorshipApplication } from '../../services/incubatorApi';

interface MentorshipApplicationEditFormProps {
  application: MentorshipApplication;
  onSave: (data: Partial<MentorshipApplication>) => Promise<boolean>;
  onCancel: () => void;
  isLoading?: boolean;
}

const MentorshipApplicationEditForm: React.FC<MentorshipApplicationEditFormProps> = ({
  application,
  onSave,
  onCancel,
  isLoading = false
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [formData, setFormData] = useState({
    goals: application.goals || '',
    specific_areas: application.specific_areas || '',
    commitment: application.commitment || '',
    preferred_communication: application.preferred_communication || 'video',
    preferred_expertise: application.preferred_expertise || '',
  });

  const [formError, setFormError] = useState<string | null>(null);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    // Check if form has changes
    const hasFormChanges = Object.keys(formData).some(key => {
      const formValue = formData[key as keyof typeof formData];
      const originalValue = application[key as keyof MentorshipApplication] || '';
      return formValue !== originalValue;
    });
    setHasChanges(hasFormChanges);
  }, [formData, application]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    setFormError(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);

    // Validate required fields
    if (!formData.goals.trim()) {
      setFormError(t('incubator.mentorshipApplication.goalsRequired'));
      return;
    }

    if (!formData.specific_areas.trim()) {
      setFormError(t('incubator.mentorshipApplication.areasRequired'));
      return;
    }

    if (!formData.commitment.trim()) {
      setFormError(t('incubator.mentorshipApplication.commitmentRequired'));
      return;
    }

    const success = await onSave(formData);
    if (!success) {
      setFormError(t('incubator.mentorshipApplication.updateError'));
    }
  };

  const canEdit = application.status === 'pending' || application.status === 'under_review';

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 rounded-xl border border-purple-500/30 max-w-3xl w-full max-h-[90vh] overflow-hidden shadow-2xl">
        {/* Header */}
        <div className={`flex items-center justify-between p-6 border-b border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <Edit size={24} className={`text-purple-400 ${isRTL ? 'ml-3' : 'mr-3'}`} />
            <h2 className="text-xl font-semibold text-white">
              {t('incubator.mentorshipApplication.editTitle')}
            </h2>
          </div>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-white transition-colors"
            disabled={isLoading}
          >
            <X size={20} />
          </button>
        </div>

        {/* Application Info */}
        <div className="px-6 py-4 bg-gray-800/50 border-b border-gray-700">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-400">{t('incubator.mentorshipApplication.businessIdea')}:</span>
              <span className="text-white font-medium ml-2">{application.business_idea.title}</span>
            </div>
            <div>
              <span className="text-gray-400">{t('incubator.mentorshipApplication.status')}:</span>
              <span className={`ml-2 px-2 py-1 rounded text-xs ${
                application.status === 'approved' ? 'bg-green-900/50 text-green-200' :
                application.status === 'rejected' ? 'bg-red-900/50 text-red-200' :
                'bg-yellow-900/50 text-yellow-200'
              }`}>
                {t(`incubator.mentorshipApplication.status.${application.status}`)}
              </span>
            </div>
            {application.preferred_mentor && (
              <div>
                <span className="text-gray-400">{t('incubator.mentorshipApplication.preferredMentor')}:</span>
                <span className="text-white font-medium ml-2">
                  {application.preferred_mentor.user.first_name} {application.preferred_mentor.user.last_name}
                </span>
              </div>
            )}
            <div>
              <span className="text-gray-400">{t('incubator.mentorshipApplication.appliedDate')}:</span>
              <span className="text-white ml-2">{new Date(application.created_at).toLocaleDateString()}</span>
            </div>
          </div>
        </div>

        {/* Form */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {!canEdit && (
            <div className="bg-yellow-900/30 border border-yellow-500/50 rounded-lg p-4 mb-6">
              <p className="text-yellow-400 text-sm">
                {t('incubator.mentorshipApplication.cannotEditStatus')}
              </p>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Error Message */}
            {formError && (
              <div className="bg-red-900/30 border border-red-500/50 rounded-lg p-4">
                <p className="text-red-400 text-sm">{formError}</p>
              </div>
            )}

            {/* Goals */}
            <div>
              <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                {t('incubator.mentorshipApplication.goals')} *
              </label>
              <textarea
                name="goals"
                value={formData.goals}
                onChange={handleInputChange}
                rows={4}
                className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-white placeholder-gray-400 resize-none ${isRTL ? 'text-right' : ''}`}
                placeholder={t('incubator.mentorshipApplication.goalsPlaceholder')}
                required
                disabled={isLoading || !canEdit}
              />
              <p className="text-gray-500 text-xs mt-1">
                {t('incubator.mentorshipApplication.goalsHelp')}
              </p>
            </div>

            {/* Specific Areas */}
            <div>
              <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                {t('incubator.mentorshipApplication.specificAreas')} *
              </label>
              <textarea
                name="specific_areas"
                value={formData.specific_areas}
                onChange={handleInputChange}
                rows={4}
                className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-white placeholder-gray-400 resize-none ${isRTL ? 'text-right' : ''}`}
                placeholder={t('incubator.mentorshipApplication.areasPlaceholder')}
                required
                disabled={isLoading || !canEdit}
              />
              <p className="text-gray-500 text-xs mt-1">
                {t('incubator.mentorshipApplication.areasHelp')}
              </p>
            </div>

            {/* Commitment */}
            <div>
              <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                {t('incubator.mentorshipApplication.commitment')} *
              </label>
              <textarea
                name="commitment"
                value={formData.commitment}
                onChange={handleInputChange}
                rows={3}
                className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-white placeholder-gray-400 resize-none ${isRTL ? 'text-right' : ''}`}
                placeholder={t('incubator.mentorshipApplication.commitmentPlaceholder')}
                required
                disabled={isLoading || !canEdit}
              />
              <p className="text-gray-500 text-xs mt-1">
                {t('incubator.mentorshipApplication.commitmentHelp')}
              </p>
            </div>

            {/* Communication Preferences */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Preferred Communication */}
              <div>
                <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                  {t('incubator.mentorshipApplication.preferredCommunication')}
                </label>
                <select
                  name="preferred_communication"
                  value={formData.preferred_communication}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-white ${isRTL ? 'text-right' : ''}`}
                  disabled={isLoading || !canEdit}
                >
                  <option value="video">{t('incubator.mentorshipApplication.communication.video')}</option>
                  <option value="phone">{t('incubator.mentorshipApplication.communication.phone')}</option>
                  <option value="email">{t('incubator.mentorshipApplication.communication.email')}</option>
                  <option value="chat">{t('incubator.mentorshipApplication.communication.chat')}</option>
                  <option value="in_person">{t('incubator.mentorshipApplication.communication.inPerson')}</option>
                </select>
              </div>

              {/* Preferred Expertise */}
              <div>
                <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                  {t('incubator.mentorshipApplication.preferredExpertise')}
                </label>
                <input
                  type="text"
                  name="preferred_expertise"
                  value={formData.preferred_expertise}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-white placeholder-gray-400 ${isRTL ? 'text-right' : ''}`}
                  placeholder={t('incubator.mentorshipApplication.expertisePlaceholder')}
                  disabled={isLoading || !canEdit}
                />
              </div>
            </div>

            {/* Admin Feedback (if any) */}
            {application.admin_feedback && (
              <div>
                <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                  {t('incubator.mentorshipApplication.adminFeedback')}
                </label>
                <div className="bg-purple-900/20 border border-purple-500/30 rounded-lg p-4">
                  <p className={`text-purple-300 text-sm ${isRTL ? 'text-right' : ''}`}>
                    {application.admin_feedback}
                  </p>
                </div>
              </div>
            )}

            {/* Matched Mentor Info (if matched) */}
            {application.status === 'approved' && application.preferred_mentor && (
              <div>
                <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                  {t('incubator.mentorshipApplication.matchedMentor')}
                </label>
                <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-4">
                  <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <Users size={20} className={`text-green-400 ${isRTL ? 'ml-3' : 'mr-3'}`} />
                    <div>
                      <p className="text-green-300 font-medium">
                        {application.preferred_mentor.user.first_name} {application.preferred_mentor.user.last_name}
                      </p>
                      <p className="text-green-400 text-sm">
                        {application.preferred_mentor.expertise_areas}
                      </p>
                      <p className="text-green-400 text-sm">
                        {application.preferred_mentor.years_of_experience} {t('incubator.mentorshipApplication.yearsExperience')}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </form>
        </div>

        {/* Footer */}
        <div className={`flex justify-end gap-4 p-6 border-t border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <button
            type="button"
            onClick={onCancel}
            className="px-6 py-2 border border-gray-600 rounded-lg hover:bg-gray-800 transition-colors text-white disabled:opacity-50"
            disabled={isLoading}
          >
            {t('common.cancel')}
          </button>
          {canEdit && (
            <button
              onClick={handleSubmit}
              disabled={isLoading || !hasChanges}
              className="px-6 py-2 bg-purple-600 rounded-lg hover:bg-purple-700 transition-colors flex items-center text-white disabled:opacity-50"
            >
              {isLoading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  {t('common.saving')}
                </>
              ) : (
                <>
                  <Save size={16} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                  {t('common.saveChanges')}
                </>
              )}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default MentorshipApplicationEditForm;
