import { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

export interface CRUDOperations<T> {
  create: (data: Partial<T>) => Promise<T>;
  read: () => Promise<T[]>;
  readOne?: (id: number) => Promise<T>;
  update: (id: number, data: Partial<T>) => Promise<T>;
  delete: (id: number) => Promise<void>;
  bulkDelete?: (ids: number[]) => Promise<void>;
  bulkUpdate?: (ids: number[], data: Partial<T>) => Promise<T[]>;
}

export interface UseEnhancedCRUDOptions {
  onSuccess?: (operation: 'create' | 'read' | 'update' | 'delete' | 'bulkDelete' | 'bulkUpdate', data?: any) => void;
  onError?: (operation: 'create' | 'read' | 'update' | 'delete' | 'bulkDelete' | 'bulkUpdate', error: any) => void;
  enableOptimisticUpdates?: boolean;
  enableCache?: boolean;
  cacheTimeout?: number; // in milliseconds
  retryAttempts?: number;
  retryDelay?: number; // in milliseconds
}

export interface UseEnhancedCRUDReturn<T> {
  // State
  data: T[];
  selectedItem: T | null;
  selectedItems: T[];
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  error: string | null;
  
  // Operations
  createItem: (data: Partial<T>) => Promise<boolean>;
  readItems: () => Promise<void>;
  readItem: (id: number) => Promise<T | null>;
  updateItem: (id: number, data: Partial<T>) => Promise<boolean>;
  deleteItem: (id: number) => Promise<boolean>;
  bulkDeleteItems: (ids: number[]) => Promise<boolean>;
  bulkUpdateItems: (ids: number[], data: Partial<T>) => Promise<boolean>;
  
  // UI State Management
  setSelectedItem: (item: T | null) => void;
  setSelectedItems: (items: T[]) => void;
  toggleItemSelection: (item: T) => void;
  selectAllItems: () => void;
  clearSelection: () => void;
  clearError: () => void;
  setData: (data: T[]) => void;
  
  // Utility functions
  refreshData: () => Promise<void>;
  getItemById: (id: number) => T | undefined;
  isItemSelected: (item: T) => boolean;
}

export function useEnhancedCRUD<T extends { id: number }>(
  operations: CRUDOperations<T>,
  options: UseEnhancedCRUDOptions = {}
): UseEnhancedCRUDReturn<T> {
  const { t } = useTranslation();
  const [data, setData] = useState<T[]>([]);
  const [selectedItem, setSelectedItem] = useState<T | null>(null);
  const [selectedItems, setSelectedItems] = useState<T[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [cache, setCache] = useState<{ data: T[]; timestamp: number } | null>(null);

  const {
    onSuccess,
    onError,
    enableOptimisticUpdates = true,
    enableCache = true,
    cacheTimeout = 5 * 60 * 1000, // 5 minutes
    retryAttempts = 3,
    retryDelay = 1000
  } = options;

  const handleError = useCallback((operation: 'create' | 'read' | 'update' | 'delete' | 'bulkDelete' | 'bulkUpdate', err: Error | unknown) => {
    const errorMessage = err instanceof Error ? err.message : t('common.error');
    setError(errorMessage);
    onError?.(operation, err);
  }, [onError, t]);

  const retryOperation = useCallback(async <R>(
    operation: () => Promise<R>,
    attempts: number = retryAttempts
  ): Promise<R> => {
    try {
      return await operation();
    } catch (error) {
      if (attempts > 1) {
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        return retryOperation(operation, attempts - 1);
      }
      throw error;
    }
  }, [retryAttempts, retryDelay]);

  const isCacheValid = useCallback(() => {
    if (!enableCache || !cache) return false;
    return Date.now() - cache.timestamp < cacheTimeout;
  }, [enableCache, cache, cacheTimeout]);

  const createItem = useCallback(async (itemData: Partial<T>): Promise<boolean> => {
    try {
      setIsCreating(true);
      setError(null);
      
      const newItem = await retryOperation(() => operations.create(itemData));
      
      if (enableOptimisticUpdates) {
        setData(prev => [newItem, ...prev]);
      } else {
        setData(prev => [newItem, ...prev]);
      }
      
      // Invalidate cache
      if (enableCache) {
        setCache(null);
      }
      
      onSuccess?.('create', newItem);
      return true;
    } catch (err) {
      handleError('create', err);
      return false;
    } finally {
      setIsCreating(false);
    }
  }, [operations, enableOptimisticUpdates, enableCache, onSuccess, handleError, retryOperation]);

  const readItems = useCallback(async (): Promise<void> => {
    try {
      // Check cache first
      if (isCacheValid() && cache) {
        setData(cache.data);
        return;
      }

      setIsLoading(true);
      setError(null);
      
      const items = await retryOperation(() => operations.read());
      setData(items);
      
      // Update cache
      if (enableCache) {
        setCache({ data: items, timestamp: Date.now() });
      }
      
      onSuccess?.('read', items);
    } catch (err) {
      handleError('read', err);
    } finally {
      setIsLoading(false);
    }
  }, [operations, enableCache, cache, isCacheValid, onSuccess, handleError, retryOperation]);

  const readItem = useCallback(async (id: number): Promise<T | null> => {
    try {
      if (!operations.readOne) {
        // Fallback to finding in current data
        const item = data.find(item => item.id === id);
        return item || null;
      }
      
      setError(null);
      const item = await retryOperation(() => operations.readOne!(id));
      return item;
    } catch (err) {
      handleError('read', err);
      return null;
    }
  }, [operations, data, handleError, retryOperation]);

  const updateItem = useCallback(async (id: number, itemData: Partial<T>): Promise<boolean> => {
    try {
      setIsUpdating(true);
      setError(null);
      
      // Optimistic update
      if (enableOptimisticUpdates) {
        setData(prev => prev.map(item => 
          item.id === id ? { ...item, ...itemData } : item
        ));
      }
      
      const updatedItem = await retryOperation(() => operations.update(id, itemData));
      
      if (!enableOptimisticUpdates) {
        setData(prev => prev.map(item => 
          item.id === id ? updatedItem : item
        ));
      } else {
        // Ensure the optimistic update matches the server response
        setData(prev => prev.map(item => 
          item.id === id ? updatedItem : item
        ));
      }
      
      // Update selected item if it's the one being updated
      if (selectedItem?.id === id) {
        setSelectedItem(updatedItem);
      }
      
      // Invalidate cache
      if (enableCache) {
        setCache(null);
      }
      
      onSuccess?.('update', updatedItem);
      return true;
    } catch (err) {
      // Revert optimistic update on error
      if (enableOptimisticUpdates) {
        await readItems();
      }
      handleError('update', err);
      return false;
    } finally {
      setIsUpdating(false);
    }
  }, [operations, selectedItem, enableOptimisticUpdates, enableCache, onSuccess, handleError, retryOperation, readItems]);

  const deleteItem = useCallback(async (id: number): Promise<boolean> => {
    try {
      setIsDeleting(true);
      setError(null);
      
      // Optimistic update
      if (enableOptimisticUpdates) {
        setData(prev => prev.filter(item => item.id !== id));
      }
      
      await retryOperation(() => operations.delete(id));
      
      if (!enableOptimisticUpdates) {
        setData(prev => prev.filter(item => item.id !== id));
      }
      
      // Clear selection if deleted item was selected
      if (selectedItem?.id === id) {
        setSelectedItem(null);
      }
      
      // Remove from selected items
      setSelectedItems(prev => prev.filter(item => item.id !== id));
      
      // Invalidate cache
      if (enableCache) {
        setCache(null);
      }
      
      onSuccess?.('delete', { id });
      return true;
    } catch (err) {
      // Revert optimistic update on error
      if (enableOptimisticUpdates) {
        await readItems();
      }
      handleError('delete', err);
      return false;
    } finally {
      setIsDeleting(false);
    }
  }, [operations, selectedItem, enableOptimisticUpdates, enableCache, onSuccess, handleError, retryOperation, readItems]);

  const bulkDeleteItems = useCallback(async (ids: number[]): Promise<boolean> => {
    try {
      if (!operations.bulkDelete) {
        // Fallback to individual deletes
        const results = await Promise.all(ids.map(id => deleteItem(id)));
        return results.every(result => result);
      }
      
      setIsDeleting(true);
      setError(null);
      
      // Optimistic update
      if (enableOptimisticUpdates) {
        setData(prev => prev.filter(item => !ids.includes(item.id)));
      }
      
      await retryOperation(() => operations.bulkDelete!(ids));
      
      if (!enableOptimisticUpdates) {
        setData(prev => prev.filter(item => !ids.includes(item.id)));
      }
      
      // Clear selections
      setSelectedItems([]);
      if (selectedItem && ids.includes(selectedItem.id)) {
        setSelectedItem(null);
      }
      
      // Invalidate cache
      if (enableCache) {
        setCache(null);
      }
      
      onSuccess?.('bulkDelete', { ids });
      return true;
    } catch (err) {
      // Revert optimistic update on error
      if (enableOptimisticUpdates) {
        await readItems();
      }
      handleError('bulkDelete', err);
      return false;
    } finally {
      setIsDeleting(false);
    }
  }, [operations, selectedItem, enableOptimisticUpdates, enableCache, onSuccess, handleError, retryOperation, readItems, deleteItem]);

  const bulkUpdateItems = useCallback(async (ids: number[], itemData: Partial<T>): Promise<boolean> => {
    try {
      if (!operations.bulkUpdate) {
        // Fallback to individual updates
        const results = await Promise.all(ids.map(id => updateItem(id, itemData)));
        return results.every(result => result);
      }
      
      setIsUpdating(true);
      setError(null);
      
      // Optimistic update
      if (enableOptimisticUpdates) {
        setData(prev => prev.map(item => 
          ids.includes(item.id) ? { ...item, ...itemData } : item
        ));
      }
      
      const updatedItems = await retryOperation(() => operations.bulkUpdate!(ids, itemData));
      
      if (!enableOptimisticUpdates) {
        setData(prev => prev.map(item => {
          const updatedItem = updatedItems.find(updated => updated.id === item.id);
          return updatedItem || item;
        }));
      }
      
      // Invalidate cache
      if (enableCache) {
        setCache(null);
      }
      
      onSuccess?.('bulkUpdate', updatedItems);
      return true;
    } catch (err) {
      // Revert optimistic update on error
      if (enableOptimisticUpdates) {
        await readItems();
      }
      handleError('bulkUpdate', err);
      return false;
    } finally {
      setIsUpdating(false);
    }
  }, [operations, enableOptimisticUpdates, enableCache, onSuccess, handleError, retryOperation, readItems, updateItem]);

  const toggleItemSelection = useCallback((item: T) => {
    setSelectedItems(prev => {
      const isSelected = prev.some(selected => selected.id === item.id);
      if (isSelected) {
        return prev.filter(selected => selected.id !== item.id);
      } else {
        return [...prev, item];
      }
    });
  }, []);

  const selectAllItems = useCallback(() => {
    setSelectedItems([...data]);
  }, [data]);

  const clearSelection = useCallback(() => {
    setSelectedItems([]);
    setSelectedItem(null);
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const refreshData = useCallback(async () => {
    // Invalidate cache and reload
    if (enableCache) {
      setCache(null);
    }
    await readItems();
  }, [enableCache, readItems]);

  const getItemById = useCallback((id: number): T | undefined => {
    return data.find(item => item.id === id);
  }, [data]);

  const isItemSelected = useCallback((item: T): boolean => {
    return selectedItems.some(selected => selected.id === item.id);
  }, [selectedItems]);

  return {
    // State
    data,
    selectedItem,
    selectedItems,
    isLoading,
    isCreating,
    isUpdating,
    isDeleting,
    error,
    
    // Operations
    createItem,
    readItems,
    readItem,
    updateItem,
    deleteItem,
    bulkDeleteItems,
    bulkUpdateItems,
    
    // UI State Management
    setSelectedItem,
    setSelectedItems,
    toggleItemSelection,
    selectAllItems,
    clearSelection,
    clearError,
    setData,
    
    // Utility functions
    refreshData,
    getItemById,
    isItemSelected
  };
}

export default useEnhancedCRUD;
