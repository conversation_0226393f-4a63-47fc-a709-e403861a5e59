from django.db.models import Q
from django.contrib.postgres.search import SearchVector, SearchQuery, SearchRank
from .models import ForumThread, ForumPost, ForumTopic, ForumCategory

def search_forums(query, user=None, search_type=None, category_id=None, topic_id=None):
    """
    Search forum content based on query and filters
    
    Args:
        query (str): The search query
        user (User, optional): The current user for filtering by moderation status
        search_type (str, optional): Type of content to search ('threads', 'posts', 'topics', 'all')
        category_id (int, optional): Filter by category ID
        topic_id (int, optional): Filter by topic ID
        
    Returns:
        dict: Dictionary containing search results
    """
    results = {
        'threads': [],
        'posts': [],
        'topics': [],
        'categories': []
    }
    
    if not query or len(query.strip()) < 3:
        return results
    
    # Normalize query
    query = query.strip()
    
    # Check if we're using PostgreSQL
    using_postgres = False
    try:
        from django.db import connection
        using_postgres = connection.vendor == 'postgresql'
    except:
        using_postgres = False
    
    # Search threads
    if search_type in ['threads', 'all', None]:
        thread_queryset = ForumThread.objects.select_related('author', 'topic', 'topic__category')
        
        # Apply filters
        if category_id:
            thread_queryset = thread_queryset.filter(topic__category_id=category_id)
        if topic_id:
            thread_queryset = thread_queryset.filter(topic_id=topic_id)
        
        # Apply moderation filter for non-admin users
        if user and not (user.is_staff or user.is_superuser):
            thread_queryset = thread_queryset.filter(
                Q(moderation_status='approved') | Q(author=user)
            )
        
        # Perform search
        if using_postgres:
            # Use PostgreSQL full-text search
            search_vector = SearchVector('title', weight='A') + SearchVector('content', weight='B')
            search_query = SearchQuery(query)
            thread_queryset = thread_queryset.annotate(
                rank=SearchRank(search_vector, search_query)
            ).filter(rank__gt=0).order_by('-rank')
        else:
            # Use basic search for SQLite
            thread_queryset = thread_queryset.filter(
                Q(title__icontains=query) | 
                Q(content__icontains=query) |
                Q(tags__name__icontains=query)
            ).distinct()
        
        results['threads'] = thread_queryset[:20]  # Limit to 20 results
    
    # Search posts
    if search_type in ['posts', 'all', None]:
        post_queryset = ForumPost.objects.select_related('author', 'thread', 'thread__topic')
        
        # Apply filters
        if category_id:
            post_queryset = post_queryset.filter(thread__topic__category_id=category_id)
        if topic_id:
            post_queryset = post_queryset.filter(thread__topic_id=topic_id)
        
        # Apply moderation filter for non-admin users
        if user and not (user.is_staff or user.is_superuser):
            post_queryset = post_queryset.filter(
                Q(moderation_status='approved') | Q(author=user)
            )
        
        # Perform search
        if using_postgres:
            # Use PostgreSQL full-text search
            search_vector = SearchVector('content', weight='A')
            search_query = SearchQuery(query)
            post_queryset = post_queryset.annotate(
                rank=SearchRank(search_vector, search_query)
            ).filter(rank__gt=0).order_by('-rank')
        else:
            # Use basic search for SQLite
            post_queryset = post_queryset.filter(content__icontains=query)
        
        results['posts'] = post_queryset[:20]  # Limit to 20 results
    
    # Search topics
    if search_type in ['topics', 'all', None]:
        topic_queryset = ForumTopic.objects.select_related('category', 'created_by')
        
        # Apply filters
        if category_id:
            topic_queryset = topic_queryset.filter(category_id=category_id)
        
        # Perform search
        if using_postgres:
            # Use PostgreSQL full-text search
            search_vector = SearchVector('title', weight='A') + SearchVector('description', weight='B')
            search_query = SearchQuery(query)
            topic_queryset = topic_queryset.annotate(
                rank=SearchRank(search_vector, search_query)
            ).filter(rank__gt=0).order_by('-rank')
        else:
            # Use basic search for SQLite
            topic_queryset = topic_queryset.filter(
                Q(title__icontains=query) | 
                Q(description__icontains=query)
            )
        
        results['topics'] = topic_queryset[:10]  # Limit to 10 results
    
    # Search categories
    if search_type in ['categories', 'all', None]:
        category_queryset = ForumCategory.objects.all()
        
        # Perform search
        if using_postgres:
            # Use PostgreSQL full-text search
            search_vector = SearchVector('name', weight='A') + SearchVector('description', weight='B')
            search_query = SearchQuery(query)
            category_queryset = category_queryset.annotate(
                rank=SearchRank(search_vector, search_query)
            ).filter(rank__gt=0).order_by('-rank')
        else:
            # Use basic search for SQLite
            category_queryset = category_queryset.filter(
                Q(name__icontains=query) | 
                Q(description__icontains=query)
            )
        
        results['categories'] = category_queryset[:5]  # Limit to 5 results
    
    return results
