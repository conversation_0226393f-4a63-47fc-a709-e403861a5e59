import React from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../hooks/useLanguage';
import { AlertCircle } from 'lucide-react';
interface MissingFieldsListProps {
  missingFields: string[];
}

const MissingFieldsList: React.FC<MissingFieldsListProps> = ({ missingFields  }) => {
  const { t } = useTranslation();
  const { language, isRTL } = useLanguage();

  if (missingFields.length === 0) return null;

  return (
    <div className="mb-4">
      <p className={`text-sm text-red-400 flex items-center mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
        <AlertCircle size={14} className={`${language === 'ar' ? 'ml-1' : 'mr-1'}`} />
        {t('dashboard.profileCompletion.missingFields')}:
      </p>
      <ul className="text-sm space-y-1 pl-5 list-disc">
        {missingFields.map((item, index) => (
          <li key={index}>{item}</li>
        ))}
      </ul>
    </div>
  );
};

export default MissingFieldsList;
