import React from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { BusinessIdea } from '../../services/incubatorApi';
import { X, Calendar, User, Target, Lightbulb, TrendingUp, CheckCircle, Clock, AlertCircle } from 'lucide-react';

interface BusinessIdeaViewModalProps {
  idea: BusinessIdea | null;
  isOpen: boolean;
  onClose: () => void;
}

const BusinessIdeaViewModal: React.FC<BusinessIdeaViewModalProps> = ({
  idea,
  isOpen,
  onClose
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  if (!isOpen || !idea) return null;

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="w-5 h-5 text-green-400" />;
      case 'rejected':
        return <AlertCircle className="w-5 h-5 text-red-400" />;
      default:
        return <Clock className="w-5 h-5 text-yellow-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'text-green-400 bg-green-500/20';
      case 'rejected':
        return 'text-red-400 bg-red-500/20';
      default:
        return 'text-yellow-400 bg-yellow-500/20';
    }
  };

  const getStageColor = (stage: string) => {
    switch (stage) {
      case 'concept':
        return 'text-blue-400 bg-blue-500/20';
      case 'validation':
        return 'text-yellow-400 bg-yellow-500/20';
      case 'development':
        return 'text-orange-400 bg-orange-500/20';
      case 'scaling':
        return 'text-purple-400 bg-purple-500/20';
      default:
        return 'text-green-400 bg-green-500/20';
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/60 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="relative w-full max-w-4xl max-h-[90vh] overflow-hidden">
        <div className="glass-morphism rounded-2xl border border-white/20 shadow-2xl">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-white/20">
            <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className="p-2 rounded-lg bg-gradient-to-br from-purple-500/20 to-blue-500/20">
                <Lightbulb className="w-6 h-6 text-purple-400" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-white">
                  {t('incubator.viewIdea', 'View Business Idea')}
                </h2>
                <p className="text-white/60 text-sm">
                  {t('incubator.ideaDetails', 'Business idea details and information')}
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white/10 rounded-lg transition-colors"
            >
              <X className="w-5 h-5 text-white/70" />
            </button>
          </div>

          {/* Content */}
          <div className="p-6 max-h-[70vh] overflow-y-auto">
            <div className="space-y-6">
              {/* Title and Description */}
              <div className="space-y-4">
                <div>
                  <h3 className="text-2xl font-bold text-white mb-2">{idea.title}</h3>
                  <p className="text-white/80 leading-relaxed">{idea.description}</p>
                </div>
              </div>

              {/* Status and Stage */}
              <div className={`grid grid-cols-1 md:grid-cols-2 gap-4 ${isRTL ? 'text-right' : 'text-left'}`}>
                <div className="glass-light rounded-lg p-4">
                  <div className={`flex items-center gap-2 mb-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    {getStatusIcon(idea.moderation_status)}
                    <span className="text-sm font-medium text-white/70">
                      {t('incubator.status', 'Status')}
                    </span>
                  </div>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(idea.moderation_status)}`}>
                    {t(`incubator.${idea.moderation_status}`, idea.moderation_status)}
                  </span>
                </div>

                <div className="glass-light rounded-lg p-4">
                  <div className={`flex items-center gap-2 mb-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <TrendingUp className="w-5 h-5 text-purple-400" />
                    <span className="text-sm font-medium text-white/70">
                      {t('incubator.stage', 'Stage')}
                    </span>
                  </div>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStageColor(idea.current_stage)}`}>
                    {t(`incubator.${idea.current_stage}`, idea.current_stage)}
                  </span>
                </div>
              </div>

              {/* Details Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Problem Statement */}
                {idea.problem_statement && (
                  <div className="glass-light rounded-lg p-4">
                    <h4 className="text-lg font-semibold text-white mb-3 flex items-center gap-2">
                      <Target className="w-5 h-5 text-red-400" />
                      {t('incubator.problemStatement', 'Problem Statement')}
                    </h4>
                    <p className="text-white/80 leading-relaxed">{idea.problem_statement}</p>
                  </div>
                )}

                {/* Solution Description */}
                {idea.solution_description && (
                  <div className="glass-light rounded-lg p-4">
                    <h4 className="text-lg font-semibold text-white mb-3 flex items-center gap-2">
                      <Lightbulb className="w-5 h-5 text-yellow-400" />
                      {t('incubator.solutionDescription', 'Solution Description')}
                    </h4>
                    <p className="text-white/80 leading-relaxed">{idea.solution_description}</p>
                  </div>
                )}

                {/* Target Audience */}
                {idea.target_audience && (
                  <div className="glass-light rounded-lg p-4">
                    <h4 className="text-lg font-semibold text-white mb-3 flex items-center gap-2">
                      <User className="w-5 h-5 text-blue-400" />
                      {t('incubator.targetAudience', 'Target Audience')}
                    </h4>
                    <p className="text-white/80 leading-relaxed">{idea.target_audience}</p>
                  </div>
                )}

                {/* Market Opportunity */}
                {idea.market_opportunity && (
                  <div className="glass-light rounded-lg p-4">
                    <h4 className="text-lg font-semibant text-white mb-3 flex items-center gap-2">
                      <TrendingUp className="w-5 h-5 text-green-400" />
                      {t('incubator.marketOpportunity', 'Market Opportunity')}
                    </h4>
                    <p className="text-white/80 leading-relaxed">{idea.market_opportunity}</p>
                  </div>
                )}
              </div>

              {/* Business Model */}
              {idea.business_model && (
                <div className="glass-light rounded-lg p-4">
                  <h4 className="text-lg font-semibold text-white mb-3 flex items-center gap-2">
                    <TrendingUp className="w-5 h-5 text-purple-400" />
                    {t('incubator.businessModel', 'Business Model')}
                  </h4>
                  <p className="text-white/80 leading-relaxed">{idea.business_model}</p>
                </div>
              )}

              {/* Metadata */}
              <div className="glass-light rounded-lg p-4">
                <h4 className="text-lg font-semibold text-white mb-3">
                  {t('common.information', 'Information')}
                </h4>
                <div className={`grid grid-cols-1 md:grid-cols-3 gap-4 ${isRTL ? 'text-right' : 'text-left'}`}>
                  <div>
                    <span className="text-white/60 text-sm block">
                      {t('common.createdBy', 'Created By')}
                    </span>
                    <span className="text-white font-medium">
                      {idea.owner.username}
                    </span>
                  </div>
                  <div>
                    <span className="text-white/60 text-sm block">
                      {t('common.created', 'Created')}
                    </span>
                    <span className="text-white font-medium">
                      {new Date(idea.created_at).toLocaleDateString()}
                    </span>
                  </div>
                  <div>
                    <span className="text-white/60 text-sm block">
                      {t('incubator.updates', 'Updates')}
                    </span>
                    <span className="text-white font-medium">
                      {idea.progress_count || 0}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="flex justify-end p-6 border-t border-white/20">
            <button
              onClick={onClose}
              className="px-6 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors border border-white/20"
            >
              {t('common.close', 'Close')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BusinessIdeaViewModal;
