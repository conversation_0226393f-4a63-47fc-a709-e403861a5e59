import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useAppSelector } from '../../store/hooks';
// Removed routeDebugger utilities - using simplified debugging
import { getAllRoutes } from '../../utils/routeValidation';
import { Bug, Eye, EyeOff, RefreshCw, Route, User, Shield } from 'lucide-react';

/**
 * Development-only route diagnostics component
 * Shows detailed information about routing and access control
 */
const RouteDiagnostics: React.FC = () => {
  const location = useLocation();
  const { user, isAuthenticated, isLoading } = useAppSelector(state => state.auth);
  const [isVisible, setIsVisible] = useState(false);
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [allRoutes, setAllRoutes] = useState<any[]>([]);

  // Only show in development mode
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  useEffect(() => {
    // Simplified debug info
    const info = {
      path: location.pathname,
      user: user?.username || 'Not authenticated',
      isAuthenticated,
      isLoading
    };
    setDebugInfo(info);
    setAllRoutes(getAllRoutes());
  }, [location.pathname, user, isAuthenticated, isLoading]);

  const handleRefresh = () => {
    // Simplified debug info refresh
    const info = {
      path: location.pathname,
      user: user?.username || 'Not authenticated',
      isAuthenticated,
      isLoading
    };
    setDebugInfo(info);
    setAllRoutes(getAllRoutes());
  };

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <button
          onClick={() => setIsVisible(true)}
          className="bg-purple-600 text-white p-3 rounded-full shadow-lg hover:bg-purple-700 transition-colors"
          title="Show Route Diagnostics"
        >
          <Bug className="w-5 h-5" />
        </button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 glass-morphism border border-glass-border rounded-lg shadow-glass max-w-md w-full max-h-96 overflow-y-auto">
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-glass-border glass-light">
        <div className="flex items-center space-x-2">
          <Bug className="w-5 h-5 text-purple-600" />
          <span className="font-semibold text-glass-primary">Route Diagnostics</span>
        </div>
        <div className="flex items-center space-x-1">
          <button
            onClick={handleRefresh}
            className="p-1 text-gray-500 hover:text-purple-600 transition-colors"
            title="Refresh"
          >
            <RefreshCw className="w-4 h-4" />
          </button>
          <button
            onClick={() => setIsVisible(false)}
            className="p-1 text-gray-500 hover:text-red-600 transition-colors"
            title="Hide"
          >
            <EyeOff className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="p-3 space-y-3 text-sm">
        {/* Current Route */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded p-2">
          <div className="flex items-center space-x-2 mb-2">
            <Route className="w-4 h-4 text-blue-600" />
            <span className="font-medium text-gray-900 dark:text-white">Current Route</span>
          </div>
          <div className="text-xs space-y-1">
            <div><strong>Path:</strong> {location.pathname}</div>
            <div><strong>Exists:</strong> 
              <span className={debugInfo?.exists ? 'text-green-600' : 'text-red-600'}>
                {debugInfo?.exists ? ' ✅ Yes' : ' ❌ No'}
              </span>
            </div>
            <div><strong>Access:</strong> 
              <span className={debugInfo?.canAccess ? 'text-green-600' : 'text-red-600'}>
                {debugInfo?.canAccess ? ' ✅ Granted' : ' ❌ Denied'}
              </span>
            </div>
            {debugInfo?.reason && (
              <div><strong>Reason:</strong> <span className="text-orange-600">{debugInfo.reason}</span></div>
            )}
          </div>
        </div>

        {/* User Info */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded p-2">
          <div className="flex items-center space-x-2 mb-2">
            <User className="w-4 h-4 text-green-600" />
            <span className="font-medium text-gray-900 dark:text-white">User Info</span>
          </div>
          <div className="text-xs space-y-1">
            <div><strong>Authenticated:</strong> 
              <span className={isAuthenticated ? 'text-green-600' : 'text-red-600'}>
                {isAuthenticated ? ' ✅ Yes' : ' ❌ No'}
              </span>
            </div>
            {isAuthenticated && (
              <>
                <div><strong>Username:</strong> {user?.username || 'N/A'}</div>
                <div><strong>Admin:</strong> 
                  <span className={user?.is_admin ? 'text-green-600' : 'text-gray-600'}>
                    {user?.is_admin ? ' ✅ Yes' : ' ❌ No'}
                  </span>
                </div>
                <div><strong>Super Admin:</strong> 
                  <span className={debugInfo?.userInfo?.isSuperAdmin ? 'text-green-600' : 'text-gray-600'}>
                    {debugInfo?.userInfo?.isSuperAdmin ? ' ✅ Yes' : ' ❌ No'}
                  </span>
                </div>
                <div><strong>Roles:</strong> {debugInfo?.userInfo?.roles?.join(', ') || 'None'}</div>
              </>
            )}
            <div><strong>Loading:</strong> 
              <span className={isLoading ? 'text-orange-600' : 'text-green-600'}>
                {isLoading ? ' ⏳ Yes' : ' ✅ No'}
              </span>
            </div>
          </div>
        </div>

        {/* Route Requirements */}
        {debugInfo?.routeInfo && (
          <div className="bg-gray-50 dark:bg-gray-700 rounded p-2">
            <div className="flex items-center space-x-2 mb-2">
              <Shield className="w-4 h-4 text-orange-600" />
              <span className="font-medium text-gray-900 dark:text-white">Route Requirements</span>
            </div>
            <div className="text-xs space-y-1">
              <div><strong>Auth Required:</strong> 
                <span className={debugInfo.routeInfo.requireAuth ? 'text-orange-600' : 'text-green-600'}>
                  {debugInfo.routeInfo.requireAuth ? ' ⚠️ Yes' : ' ✅ No'}
                </span>
              </div>
              {debugInfo.routeInfo.adminOnly && (
                <div><strong>Admin Only:</strong> <span className="text-red-600">⚠️ Yes</span></div>
              )}
              {debugInfo.routeInfo.roles && debugInfo.routeInfo.roles.length > 0 && (
                <div><strong>Required Roles:</strong> {debugInfo.routeInfo.roles.join(', ')}</div>
              )}
              {debugInfo.routeInfo.permissions && debugInfo.routeInfo.permissions.length > 0 && (
                <div><strong>Required Permissions:</strong> {debugInfo.routeInfo.permissions.join(', ')}</div>
              )}
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded p-2">
          <div className="font-medium text-gray-900 dark:text-white mb-2">Quick Actions</div>
          <div className="space-y-1">
            <button
              onClick={() => {
                console.log('🔍 Route Debug Dashboard');
                console.log('Current Path:', location.pathname);
                console.log('User:', user?.username || 'Not authenticated');
                console.log('All Routes:', getAllRoutes());
              }}
              className="w-full text-left text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 px-2 py-1 rounded hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors"
            >
              📊 Show Full Dashboard (Console)
            </button>
            <button
              onClick={() => {
                console.log('🔍 Current Route Debug Info:', debugInfo);
                console.log('🛣️ All Routes:', allRoutes);
              }}
              className="w-full text-left text-xs bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 px-2 py-1 rounded hover:bg-green-200 dark:hover:bg-green-900/50 transition-colors"
            >
              📝 Log Debug Info (Console)
            </button>
            <button
              onClick={() => {
                const testRoutes = ['/dashboard', '/admin', '/super_admin', '/dashboard/business-ideas'];
                testRoutes.forEach(route => {
                  console.log(`Testing ${route}:`, {
                    path: route,
                    user: user?.username || 'Not authenticated',
                    isAuthenticated
                  });
                });
              }}
              className="w-full text-left text-xs bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 px-2 py-1 rounded hover:bg-purple-200 dark:hover:bg-purple-900/50 transition-colors"
            >
              🧪 Test Common Routes (Console)
            </button>
          </div>
        </div>

        {/* Stats */}
        <div className="text-xs text-gray-500 dark:text-gray-400 border-t border-gray-200 dark:border-gray-600 pt-2">
          Total Routes: {allRoutes.length} | 
          Current Time: {new Date().toLocaleTimeString()}
        </div>
      </div>
    </div>
  );
};

export default RouteDiagnostics;
