"""
System Logs Serializers
Serializers for system logs API
"""

from rest_framework import serializers
from .models_logs import SystemLog
from django.contrib.auth.models import User


class UserBasicSerializer(serializers.ModelSerializer):
    """Basic user serializer for logs"""
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name']


class SystemLogSerializer(serializers.ModelSerializer):
    """Serializer for system logs"""
    user = UserBasicSerializer(read_only=True)
    user_display = serializers.SerializerMethodField()
    
    class Meta:
        model = SystemLog
        fields = [
            'id', 'timestamp', 'level', 'category', 'message', 'details',
            'source', 'user', 'user_display', 'ip_address', 'user_agent',
            'request_id', 'metadata'
        ]
        read_only_fields = ['id', 'timestamp']
    
    def get_user_display(self, obj):
        """Get user display name"""
        if obj.user:
            if obj.user.first_name and obj.user.last_name:
                return f"{obj.user.first_name} {obj.user.last_name}"
            return obj.user.username
        return None


class SystemLogCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating system logs"""
    
    class Meta:
        model = SystemLog
        fields = [
            'level', 'category', 'message', 'details', 'source',
            'ip_address', 'user_agent', 'request_id', 'metadata'
        ]
    
    def create(self, validated_data):
        # Add user from request context if available
        request = self.context.get('request')
        if request and hasattr(request, 'user') and request.user.is_authenticated:
            validated_data['user'] = request.user
        
        return SystemLog.objects.create(**validated_data)


class LogStatsSerializer(serializers.Serializer):
    """Serializer for log statistics"""
    total_logs = serializers.IntegerField()
    error_count = serializers.IntegerField()
    warning_count = serializers.IntegerField()
    info_count = serializers.IntegerField()
    success_count = serializers.IntegerField()
    last_hour_count = serializers.IntegerField()
    categories = serializers.DictField()
    levels = serializers.DictField()
    recent_errors = serializers.ListField(child=SystemLogSerializer())


class LogFilterSerializer(serializers.Serializer):
    """Serializer for log filtering parameters"""
    level = serializers.ChoiceField(
        choices=['all'] + [choice[0] for choice in SystemLog.LOG_LEVELS],
        required=False,
        default='all'
    )
    category = serializers.CharField(required=False, allow_blank=True)
    search = serializers.CharField(required=False, allow_blank=True)
    start_date = serializers.DateTimeField(required=False)
    end_date = serializers.DateTimeField(required=False)
    user_id = serializers.IntegerField(required=False)
    source = serializers.CharField(required=False, allow_blank=True)
    page = serializers.IntegerField(required=False, default=1, min_value=1)
    page_size = serializers.IntegerField(required=False, default=50, min_value=1, max_value=100)
