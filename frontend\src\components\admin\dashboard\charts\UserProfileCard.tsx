import React from 'react';
import { Award, FileText, MessageSquare } from 'lucide-react';

import { useTranslation } from 'react-i18next';
interface UserProfileCardProps {
  user: {
    username: string;
    profile_completion: number;
    activity_score: number;
    post_count: number;
    comment_count: number;
  };
  rank: number;
}

/**
 * A card component for displaying user profile information with activity metrics
 */
const UserProfileCard: React.FC<UserProfileCardProps> = ({ user, rank  }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  return (
    <div className={`bg-indigo-900/20 rounded-xl p-4 backdrop-blur-sm shadow-lg flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
      <div className={`w-10 h-10 rounded-full bg-purple-600 flex items-center justify-center text-lg font-bold mr-4 ${isRTL ? "flex-row-reverse" : ""}`}>
        {rank}
      </div>
      <div className={`flex-grow ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`flex justify-between items-center ${isRTL ? "flex-row-reverse" : ""}`}>
          <h4 className="font-medium">{user.username}</h4>
          <div className={`flex items-center text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
            <Award size={14} className={`text-yellow-400 mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
            <span>{Math.round(user.activity_score)}</span>
          </div>
        </div>
        <div className="mt-2">
          <div className={`flex justify-between text-xs text-gray-400 mb-1 ${isRTL ? "flex-row-reverse" : ""}`}>
            <span>t("admin.profile.completion", "Profile Completion")</span>
            <span>{Math.round(user.profile_completion)}%</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-1.5">
            <div
              className="bg-gradient-to-r from-purple-500 to-blue-500 h-1.5 rounded-full"
              style={{ width: `${user.profile_completion}%` }}
            ></div>
          </div>
        </div>
        <div className={`flex mt-2 text-xs text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`flex items-center mr-3 ${isRTL ? "flex-row-reverse" : ""}`}>
            <FileText size={12} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
            <span>{user.post_count} posts</span>
          </div>
          <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <MessageSquare size={12} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
            <span>{user.comment_count} comments</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserProfileCard;
