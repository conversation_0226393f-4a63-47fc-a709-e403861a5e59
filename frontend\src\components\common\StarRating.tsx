import React from 'react';
import { Star } from 'lucide-react';
import { useLanguage } from '../../hooks/useLanguage';
interface StarRatingProps {
  rating: number;
  maxRating?: number;
  size?: number;
  color?: string;
  showValue?: boolean;
  precision?: number;
  onChange?: (rating: number) => void;
  readOnly?: boolean;
  className?: string;
}

const StarRating: React.FC<StarRatingProps> = ({ rating,
  maxRating = 5,
  size = 16,
  color = 'text-yellow-400',
  showValue = false,
  precision = 1,
  onChange,
  readOnly = true,
  className = '',
 }) => {
  const { isRTL } = useLanguage();
  const handleClick = (selectedRating: number) => {
    if (!readOnly && onChange) {
      onChange(selectedRating);
    }
  };

  const handleHover = (_event: React.MouseEvent<HTMLDivElement>, _selectedRating: number) => {
    if (readOnly) return;

    // Add hover effect if needed
  };

  return (
    <div className={`flex items-center ${className}`}>
      <div className={`flex ${isRTL ? "flex-row-reverse" : ""}`}>
        {Array.from({ length: maxRating }, (_, i) => i + 1).map((star) => (
          <div
            key={star}
            onClick={() => handleClick(star)}
            onMouseEnter={(e) => handleHover(e, star)}
            className={`${readOnly ? '' : 'cursor-pointer'}`}
          >
            <Star
              size={size}
              fill={rating >= star ? 'currentColor' : 'none'}
              className={`${color} ${!readOnly && 'hover:scale-110 transition-transform'}`}
            />
          </div>
        ))}
      </div>

      {showValue && (
        <span className={`ml-2 text-sm ${isRTL ? "space-x-reverse" : ""}`}>
          {rating.toFixed(precision)}
        </span>
      )}
    </div>
  );
};

export default StarRating;
