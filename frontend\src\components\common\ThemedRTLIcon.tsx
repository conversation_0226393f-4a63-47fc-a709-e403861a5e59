import React from 'react';
import { RTLIcon } from '.';
import { useAppSelector } from '../../store/hooks';
import { LucideIcon } from 'lucide-react';
interface ThemedRTLIconProps {
  icon: LucideIcon;
  size?: number;
  className?: string;
  flipInRTL?: boolean;
  strokeWidth?: number;
  darkClassName?: string;
  lightClassName?: string;
}

/**
 * A themed RTL-aware icon component that combines ThemeWrapper and RTLIcon functionality
 * Now uses glass morphism styling instead of theme-based styling
 */
const ThemedRTLIcon: React.FC<ThemedRTLIconProps> = ({ icon,
  size = 20,
  className = '',
  flipInRTL = false,
  strokeWidth,
  darkClassName = '',
  lightClassName = '',
 }) => {
  // Use glass morphism styling instead of theme-based styling
  const finalClassName = className.includes('text-glass-') ? className : `${className} text-glass-primary`.trim();

  return (
    <RTLIcon
      icon={icon}
      size={size}
      className={finalClassName}
      flipInRTL={flipInRTL}
      strokeWidth={strokeWidth}
    />
  );
};

export default ThemedRTLIcon;
