from django.db import models
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from django.utils import timezone
import json


class AIConfiguration(models.Model):
    """
    Centralized AI Configuration Model
    Stores all AI service configurations that can be managed by Super Admin
    """
    
    # AI Service Providers (Gemini only)
    PROVIDER_CHOICES = [
        ('gemini', 'Google Gemini'),
    ]
    
    # Configuration Types
    CONFIG_TYPE_CHOICES = [
        ('api_key', 'API Key'),
        ('model_config', 'Model Configuration'),
        ('rate_limit', 'Rate Limiting'),
        ('feature_toggle', 'Feature Toggle'),
        ('prompt_template', 'Prompt Template'),
    ]
    
    # Basic Configuration
    provider = models.CharField(max_length=20, choices=PROVIDER_CHOICES)
    config_type = models.CharField(max_length=20, choices=CONFIG_TYPE_CHOICES)
    key = models.CharField(max_length=100, help_text="Configuration key (e.g., 'api_key', 'default_model')")
    value = models.TextField(help_text="Configuration value (encrypted for sensitive data)")
    
    # Metadata
    is_active = models.BooleanField(default=True)
    is_sensitive = models.BooleanField(default=False, help_text="Whether this config contains sensitive data")
    description = models.TextField(blank=True, help_text="Description of this configuration")
    
    # Audit fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True, blank=True, related_name='ai_configs_created')
    updated_by = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True, blank=True, related_name='ai_configs_updated')
    
    class Meta:
        unique_together = ['provider', 'key']
        ordering = ['provider', 'config_type', 'key']
        verbose_name = 'AI Configuration'
        verbose_name_plural = 'AI Configurations'
    
    def __str__(self):
        return f"{self.provider}.{self.key}"
    
    def get_display_value(self):
        """Get display value (masked for sensitive data)"""
        if self.is_sensitive and self.value:
            return f"{self.value[:8]}..." if len(self.value) > 8 else "***"
        return self.value
    
    @classmethod
    def get_config(cls, provider, key, default=None):
        """Get configuration value"""
        try:
            config = cls.objects.get(provider=provider, key=key, is_active=True)
            return config.value
        except cls.DoesNotExist:
            return default
    
    @classmethod
    def set_config(cls, provider, key, value, config_type='api_key', is_sensitive=False, user=None, description=''):
        """Set configuration value"""
        config, created = cls.objects.update_or_create(
            provider=provider,
            key=key,
            defaults={
                'value': value,
                'config_type': config_type,
                'is_sensitive': is_sensitive,
                'description': description,
                'updated_by': user,
                'is_active': True
            }
        )
        if created and user:
            config.created_by = user
            config.save()
        return config


class AIUsageLog(models.Model):
    """
    Track AI service usage for analytics and billing
    """
    
    # Request details
    provider = models.CharField(max_length=20, choices=AIConfiguration.PROVIDER_CHOICES)
    model = models.CharField(max_length=100)
    endpoint = models.CharField(max_length=100, help_text="API endpoint used")
    
    # User context
    user = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True, blank=True)
    session_id = models.CharField(max_length=100, blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    
    # Usage metrics
    tokens_used = models.IntegerField(default=0)
    cost = models.DecimalField(max_digits=10, decimal_places=6, default=0)
    response_time = models.FloatField(help_text="Response time in seconds")
    
    # Request/Response
    request_data = models.JSONField(default=dict, help_text="Request parameters (sanitized)")
    response_status = models.CharField(max_length=20, default='success')
    error_message = models.TextField(blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['provider', 'created_at']),
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.provider} - {self.model} - {self.created_at}"


class AIServiceStatus(models.Model):
    """
    Track AI service health and availability
    """
    
    STATUS_CHOICES = [
        ('healthy', 'Healthy'),
        ('degraded', 'Degraded'),
        ('down', 'Down'),
        ('maintenance', 'Maintenance'),
    ]
    
    provider = models.CharField(max_length=20, choices=AIConfiguration.PROVIDER_CHOICES, unique=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='healthy')
    
    # Health metrics
    response_time = models.FloatField(default=0, help_text="Average response time in seconds")
    success_rate = models.FloatField(default=100, validators=[MinValueValidator(0), MaxValueValidator(100)])
    last_check = models.DateTimeField(auto_now=True)
    
    # Error tracking
    error_count = models.IntegerField(default=0)
    last_error = models.TextField(blank=True)
    last_error_at = models.DateTimeField(null=True, blank=True)
    
    # Configuration status
    is_configured = models.BooleanField(default=False)
    configuration_issues = models.JSONField(default=list, help_text="List of configuration issues")
    
    class Meta:
        verbose_name = 'AI Service Status'
        verbose_name_plural = 'AI Service Statuses'
    
    def __str__(self):
        return f"{self.provider} - {self.status}"
    
    def update_health(self, response_time=None, success=True, error_message=None):
        """Update service health metrics"""
        if response_time is not None:
            # Calculate rolling average (simple approach)
            self.response_time = (self.response_time + response_time) / 2
        
        if success:
            # Reset error count on success
            if self.error_count > 0:
                self.error_count = max(0, self.error_count - 1)
            self.success_rate = min(100, self.success_rate + 1)
        else:
            self.error_count += 1
            self.success_rate = max(0, self.success_rate - 5)
            if error_message:
                self.last_error = error_message
                self.last_error_at = timezone.now()
        
        # Update status based on metrics
        if self.success_rate < 50 or self.error_count > 10:
            self.status = 'down'
        elif self.success_rate < 80 or self.error_count > 5:
            self.status = 'degraded'
        else:
            self.status = 'healthy'
        
        self.save()


class AIRateLimit(models.Model):
    """
    Rate limiting configuration for AI services
    """
    
    LIMIT_TYPE_CHOICES = [
        ('user', 'Per User'),
        ('role', 'Per Role'),
        ('global', 'Global'),
        ('ip', 'Per IP Address'),
    ]
    
    PERIOD_CHOICES = [
        ('minute', 'Per Minute'),
        ('hour', 'Per Hour'),
        ('day', 'Per Day'),
        ('month', 'Per Month'),
    ]
    
    provider = models.CharField(max_length=20, choices=AIConfiguration.PROVIDER_CHOICES)
    limit_type = models.CharField(max_length=20, choices=LIMIT_TYPE_CHOICES)
    identifier = models.CharField(max_length=100, help_text="User ID, role name, or 'global'")
    
    # Rate limit settings
    max_requests = models.IntegerField(validators=[MinValueValidator(1)])
    period = models.CharField(max_length=20, choices=PERIOD_CHOICES)
    max_tokens = models.IntegerField(null=True, blank=True, help_text="Optional token limit")
    
    # Current usage (reset periodically)
    current_requests = models.IntegerField(default=0)
    current_tokens = models.IntegerField(default=0)
    period_start = models.DateTimeField(default=timezone.now)
    
    # Settings
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ['provider', 'limit_type', 'identifier']
        ordering = ['provider', 'limit_type', 'identifier']
    
    def __str__(self):
        return f"{self.provider} - {self.limit_type}:{self.identifier} - {self.max_requests}/{self.period}"
    
    def is_limit_exceeded(self, tokens=0):
        """Check if rate limit is exceeded"""
        if not self.is_active:
            return False
        
        # Check if period has reset
        self.check_period_reset()
        
        # Check request limit
        if self.current_requests >= self.max_requests:
            return True
        
        # Check token limit if specified
        if self.max_tokens and (self.current_tokens + tokens) > self.max_tokens:
            return True
        
        return False
    
    def increment_usage(self, tokens=0):
        """Increment usage counters"""
        self.check_period_reset()
        self.current_requests += 1
        self.current_tokens += tokens
        self.save()
    
    def check_period_reset(self):
        """Check if the rate limit period should be reset"""
        now = timezone.now()
        
        if self.period == 'minute':
            period_duration = timezone.timedelta(minutes=1)
        elif self.period == 'hour':
            period_duration = timezone.timedelta(hours=1)
        elif self.period == 'day':
            period_duration = timezone.timedelta(days=1)
        elif self.period == 'month':
            period_duration = timezone.timedelta(days=30)
        else:
            return
        
        if now - self.period_start >= period_duration:
            self.current_requests = 0
            self.current_tokens = 0
            self.period_start = now
            self.save()
