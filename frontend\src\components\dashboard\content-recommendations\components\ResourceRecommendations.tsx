import React from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../../../../store/hooks';
import { BookOpen, User } from 'lucide-react';
import RecommendationSection from './RecommendationSection';
import { useLanguage } from '../../../../hooks/useLanguage';
interface ResourceRecommendation {
  id: number;
  title: string;
  resource_type: string;
  author: string;
}

interface ResourceRecommendationsProps {
  resources: ResourceRecommendation[];
}

const ResourceRecommendations: React.FC<ResourceRecommendationsProps> = ({ resources  }) => {
  const { t } = useTranslation();
  const { isRTL, language } = useLanguage();

  if (resources.length === 0) return null;

  return (
    <RecommendationSection
      title={t('dashboard.contentRecommendations.resourcesYouMightLike')}
      icon={<BookOpen size={20} className={`${language === 'ar' ? 'ml-2' : 'mr-2'} text-green-400`} />}
      viewAllLink="/resources"
      viewAllText={t('dashboard.contentRecommendations.viewAllResources')}
    >
      {resources.map(resource => (
        <div key={resource.id} className="bg-indigo-800/30 p-3 rounded">
          <Link to={`/resources/${resource.id}`} className="text-purple-300 hover:text-purple-200 font-medium">
            {resource.title}
          </Link>
          <div className={`flex items-center justify-between mt-2 text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
            <span className="text-gray-400 bg-indigo-700/50 px-2 py-0.5 rounded-full text-xs">
              {resource.resource_type}
            </span>
            <span className="text-gray-400">
              <User size={14} className={`inline ${language === 'ar' ? 'ml-1' : 'mr-1'}`} />
              {resource.author}
            </span>
          </div>
        </div>
      ))}
    </RecommendationSection>
  );
};

export default ResourceRecommendations;
