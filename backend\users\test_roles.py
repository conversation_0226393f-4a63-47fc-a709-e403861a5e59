"""
Simple test script to verify role-based functionality
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

from django.contrib.auth.models import User
from users.models import User<PERSON>ole, UserProfile, UserRoleAssignment

def test_role_system():
    print("🧪 Testing Role System...")
    
    # Check if roles exist
    roles = UserRole.objects.all()
    print(f"✅ Found {roles.count()} roles:")
    for role in roles:
        print(f"   - {role.name}: {role.display_name} ({role.permission_level})")
    
    # Test user role assignment
    try:
        # Get or create a test user
        user, created = User.objects.get_or_create(
            username='test_mentor',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'Mentor'
            }
        )
        
        if created:
            print(f"✅ Created test user: {user.username}")
        else:
            print(f"✅ Using existing test user: {user.username}")
        
        # Get mentor role
        mentor_role = UserRole.objects.get(name='mentor')
        
        # Check if user has mentor role
        has_mentor_role = user.profile.has_role('mentor')
        print(f"✅ User has mentor role: {has_mentor_role}")
        
        # If not, assign mentor role
        if not has_mentor_role:
            assignment, created = UserRoleAssignment.objects.get_or_create(
                user_profile=user.profile,
                role=mentor_role,
                defaults={
                    'is_active': True,
                    'is_approved': True
                }
            )
            if created:
                print(f"✅ Assigned mentor role to {user.username}")
            
            # Test again
            has_mentor_role = user.profile.has_role('mentor')
            print(f"✅ User now has mentor role: {has_mentor_role}")
        
        # Test permission level
        permission_level = user.profile.get_highest_permission_level()
        print(f"✅ User's highest permission level: {permission_level}")
        
        print("🎉 Role system test completed successfully!")
        
    except Exception as e:
        print(f"❌ Error testing role system: {e}")
        return False
    
    return True

if __name__ == "__main__":
    test_role_system()
