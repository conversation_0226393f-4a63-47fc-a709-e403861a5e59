import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  <PERSON>ting<PERSON>, Key, Brain, Zap, Shield, AlertTriangle,
  CheckCircle, RefreshCw, Save, Eye, EyeOff, Globe,
  Database, Server, Lock, Activity, BarChart3
} from 'lucide-react';
import AuthenticatedLayout from '../../layout/AuthenticatedLayout';
import { getAuthToken } from '../../../services/api';

interface AIConfigItem {
  key: string;
  value: string;
  type: 'text' | 'password' | 'number' | 'boolean' | 'select';
  category: 'api' | 'model' | 'performance' | 'security' | 'features';
  description: string;
  is_sensitive: boolean;
  options?: string[];
  min?: number;
  max?: number;
  updated_at: string;
}

interface AIServiceStatus {
  service: string;
  status: 'active' | 'inactive' | 'error';
  last_check: string;
  response_time: number;
  error_message?: string;
}

const AIConfiguration: React.FC = () => {
  const { t } = useTranslation();
  const [configItems, setConfigItems] = useState<AIConfigItem[]>([]);
  const [serviceStatus, setServiceStatus] = useState<AIServiceStatus[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);
  const [activeTab, setActiveTab] = useState<string>('api');
  const [showSensitive, setShowSensitive] = useState<Record<string, boolean>>({});
  const [testResult, setTestResult] = useState<{success: boolean; message: string} | null>(null);

  useEffect(() => {
    fetchConfiguration();
    fetchServiceStatus();
  }, []);

  const fetchConfiguration = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/users/super-admin/ai-configuration/', {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setConfigItems(data.configurations || []);
      } else {
        console.error('Failed to fetch AI configuration:', response.status);
        setConfigItems([]);
      }
    } catch (error) {
      console.error('Failed to fetch AI configuration:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchServiceStatus = async () => {
    try {
      const response = await fetch('/api/users/super-admin/ai-configuration/', {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setServiceStatus(data.service_status || []);
      } else {
        console.error('Failed to fetch service status:', response.status);
        setServiceStatus([]);
      }
    } catch (error) {
      console.error('Failed to fetch service status:', error);
    }
  };

  const updateConfiguration = async (key: string, value: string) => {
    try {
      setSaving(true);
      const response = await fetch('/api/users/super-admin/update-ai-config/', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ configurations: { [key]: value } })
      });

      if (response.ok) {
        setConfigItems(prev => prev.map(item =>
          item.key === key ? { ...item, value, updated_at: new Date().toISOString() } : item
        ));
        setTestResult({ success: true, message: 'Configuration updated successfully!' });

        // Refresh configuration to get updated status
        setTimeout(() => {
          fetchConfiguration();
          fetchServiceStatus();
        }, 1000);
      } else {
        throw new Error('Failed to update configuration');
      }
    } catch (error) {
      console.error('Failed to update configuration:', error);
      setTestResult({ success: false, message: 'Failed to update configuration' });
    } finally {
      setSaving(false);
    }
  };

  const testAIConnection = async () => {
    try {
      setTesting(true);
      setTestResult(null);

      const response = await fetch('/api/users/super-admin/test-ai-connection/', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();

      if (result.success) {
        setTestResult({ success: true, message: 'AI connection test successful!' });
        fetchServiceStatus(); // Refresh status
      } else {
        setTestResult({ success: false, message: result.error || 'Connection test failed' });
      }
    } catch (error) {
      console.error('Connection test failed:', error);
      setTestResult({ success: false, message: 'Connection test failed' });
    } finally {
      setTesting(false);
    }
  };

  const toggleSensitiveVisibility = (key: string) => {
    setShowSensitive(prev => ({ ...prev, [key]: !prev[key] }));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-400';
      case 'inactive': return 'text-gray-400';
      case 'error': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="w-4 h-4" />;
      case 'inactive': return <AlertTriangle className="w-4 h-4" />;
      case 'error': return <AlertTriangle className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'api': return <Key className="w-4 h-4" />;
      case 'model': return <Brain className="w-4 h-4" />;
      case 'performance': return <Zap className="w-4 h-4" />;
      case 'security': return <Shield className="w-4 h-4" />;
      case 'features': return <Settings className="w-4 h-4" />;
      default: return <Settings className="w-4 h-4" />;
    }
  };

  const tabs = [
    { id: 'api', name: 'API Settings', icon: <Key className="w-4 h-4" /> },
    { id: 'model', name: 'Model Config', icon: <Brain className="w-4 h-4" /> },
    { id: 'performance', name: 'Performance', icon: <Zap className="w-4 h-4" /> },
    { id: 'security', name: 'Security', icon: <Shield className="w-4 h-4" /> },
    { id: 'features', name: 'Features', icon: <Settings className="w-4 h-4" /> },
    { id: 'status', name: 'Service Status', icon: <Activity className="w-4 h-4" /> }
  ];

  const filteredItems = activeTab === 'status' ? [] : configItems.filter(item => item.category === activeTab);

  const renderConfigItem = (item: AIConfigItem) => {
    const isVisible = showSensitive[item.key] || !item.is_sensitive;

    return (
      <div key={item.key} className="bg-gray-800 rounded-lg p-4 border border-gray-700">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            {getCategoryIcon(item.category)}
            <span className="font-medium text-white">{item.key.replace(/_/g, ' ').toUpperCase()}</span>
          </div>
          {item.is_sensitive && (
            <button
              onClick={() => toggleSensitiveVisibility(item.key)}
              className="text-gray-400 hover:text-white"
            >
              {isVisible ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </button>
          )}
        </div>
        
        <p className="text-sm text-gray-400 mb-3">{item.description}</p>
        
        <div className="space-y-2">
          {item.type === 'text' || item.type === 'password' ? (
            <input
              type={item.is_sensitive && !isVisible ? 'password' : 'text'}
              value={item.value}
              onChange={(e) => updateConfiguration(item.key, e.target.value)}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:border-blue-500 focus:outline-none text-white"
            />
          ) : item.type === 'number' ? (
            <input
              type="number"
              value={item.value}
              min={item.min}
              max={item.max}
              step={item.key === 'temperature' ? '0.1' : '1'}
              onChange={(e) => updateConfiguration(item.key, e.target.value)}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:border-blue-500 focus:outline-none text-white"
            />
          ) : item.type === 'select' ? (
            <select
              value={item.value}
              onChange={(e) => updateConfiguration(item.key, e.target.value)}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:border-blue-500 focus:outline-none text-white"
            >
              {item.options?.map(option => (
                <option key={option} value={option}>{option}</option>
              ))}
            </select>
          ) : item.type === 'boolean' ? (
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={item.value === 'true'}
                onChange={(e) => updateConfiguration(item.key, e.target.checked.toString())}
                className="rounded border-gray-600 bg-gray-700 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-300">Enable</span>
            </label>
          ) : null}
          
          <div className="text-xs text-gray-500">
            Last updated: {new Date(item.updated_at).toLocaleString()}
          </div>
        </div>
      </div>
    );
  };

  return (
    <AuthenticatedLayout>
      <div className="text-white">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-2">
            <Settings className="w-8 h-8 text-purple-400" />
            <h1 className="text-3xl font-bold text-white">
              {t('superAdmin.aiConfiguration.title', 'AI Configuration')}
            </h1>
          </div>
          <p className="text-gray-400">
            {t('superAdmin.aiConfiguration.subtitle', 'Configure AI services, models, and system settings')}
          </p>
        </div>

        {/* Test Result */}
        {testResult && (
          <div className={`p-4 rounded-lg border mb-6 ${
            testResult.success
              ? 'bg-green-900/20 border-green-500/20 text-green-300'
              : 'bg-red-900/20 border-red-500/20 text-red-300'
          }`}>
            <div className="flex items-center gap-2">
              {testResult.success ? <CheckCircle className="w-5 h-5" /> : <AlertTriangle className="w-5 h-5" />}
              <span>{testResult.message}</span>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-4 mb-8">
          <button
            onClick={testAIConnection}
            disabled={testing || saving}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors disabled:opacity-50"
          >
            {testing ? <RefreshCw className="w-4 h-4 animate-spin" /> : <Activity className="w-4 h-4" />}
            {testing ? 'Testing...' : 'Test AI Connection'}
          </button>
          <button
            onClick={fetchConfiguration}
            disabled={loading}
            className="flex items-center gap-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors disabled:opacity-50"
          >
            {loading ? <RefreshCw className="w-4 h-4 animate-spin" /> : <RefreshCw className="w-4 h-4" />}
            Refresh
          </button>
        </div>

        {/* Tabs */}
        <div className="flex flex-wrap gap-2 mb-8 border-b border-gray-700">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center gap-2 px-4 py-2 rounded-t-lg transition-colors ${
                activeTab === tab.id
                  ? 'bg-purple-600 text-white border-b-2 border-purple-400'
                  : 'text-gray-400 hover:text-white hover:bg-gray-800'
              }`}
            >
              {tab.icon}
              {tab.name}
            </button>
          ))}
        </div>

        {/* Content */}
        {activeTab === 'status' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {serviceStatus.map((service) => (
              <div key={service.service} className="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold">{service.service}</h3>
                  <div className={`flex items-center gap-1 ${getStatusColor(service.status)}`}>
                    {getStatusIcon(service.status)}
                    <span className="text-sm capitalize">{service.status}</span>
                  </div>
                </div>
                
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Response Time:</span>
                    <span>{service.response_time}s</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Last Check:</span>
                    <span>{new Date(service.last_check).toLocaleTimeString()}</span>
                  </div>
                  {service.error_message && (
                    <div className="text-red-400 text-xs mt-2">
                      {service.error_message}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {filteredItems.map(renderConfigItem)}
          </div>
        )}
      </div>
    </AuthenticatedLayout>
  );
};

export default AIConfiguration;
