import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Check, X, AlertCircle, ThumbsUp, ThumbsDown, Send } from 'lucide-react';
import { useAppDispatch } from '../../store/hooks';
import { createMentorshipFeedback } from '../../store/incubatorSlice';
import { MentorshipSession } from '../../services/incubatorApi';
import StarRating from '../common/StarRating';

import { useTranslation } from 'react-i18next';
interface EnhancedFeedbackFormProps {
  session: MentorshipSession;
  onSuccess?: () => void;
  onCancel?: () => void;
}

const EnhancedFeedbackForm: React.FC<EnhancedFeedbackFormProps> = ({ session,
  onSuccess,
  onCancel
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const [formData, setFormData] = useState({
    rating: 0,
    knowledge_rating: 0,
    communication_rating: 0,
    helpfulness_rating: 0,
    preparation_rating: 0,
    responsiveness_rating: 0,
    comments: '',
    areas_of_improvement: '',
    highlights: '',
    goals_achieved: false,
    follow_up_needed: false,
    follow_up_notes: '',
    is_private: false,
    share_with_mentor: true
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({ ...prev, [name]: checked }));
  };

  const handleRatingChange = (name: string, value: number) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (formData.rating === 0) {
      setError(t("common.please.provide.an", "Please provide an overall rating"));
      return;
    }

    if (!formData.comments.trim()) {
      setError(t("common.please.provide.feedback", "Please provide feedback comments"));
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      await dispatch(createMentorshipFeedback({
        session: session.id,
        provided_by_id: 0, // Will be set by the backend
        is_from_mentee: true, // Assuming this is from the mentee
        rating: formData.rating,
        knowledge_rating: formData.knowledge_rating || null,
        communication_rating: formData.communication_rating || null,
        helpfulness_rating: formData.helpfulness_rating || null,
        preparation_rating: formData.preparation_rating || null,
        responsiveness_rating: formData.responsiveness_rating || null,
        comments: formData.comments,
        areas_of_improvement: formData.areas_of_improvement || null,
        highlights: formData.highlights || null,
        goals_achieved: formData.goals_achieved,
        follow_up_needed: formData.follow_up_needed,
        follow_up_notes: formData.follow_up_notes || null,
        is_private: formData.is_private,
        share_with_mentor: formData.share_with_mentor
      })).unwrap();

      setSuccessMessage(t("common.feedback.submitted.successfully", "Feedback submitted successfully!"));

      // Call onSuccess callback if provided
      if (onSuccess) {
        setTimeout(() => {
          onSuccess();
        }, 1500);
      } else {
        // Navigate back to session details
        setTimeout(() => {
          navigate(`/dashboard/mentorship/sessions/${session.id}`);
        }, 1500);
      }
    } catch (err) {
      setError(t("common.failed.to.submit", "Failed to submit feedback. Please try again."));
      console.error('Error submitting feedback:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStarRating = (name: string, value: number, label: string) => {
    return (
      <div className="mb-4">
        <label className="block text-sm font-medium mb-1">{label}</label>
        <div className={`flex items-center space-x-1 ${isRTL ? "flex-row-reverse" : ""}`}>
          <StarRating
            rating={value}
            size={24}
            readOnly={false}
            onChange={(newRating) => handleRatingChange(name, newRating)}
          />
          <span className={`ml-2 text-sm text-gray-400 ${isRTL ? "space-x-reverse" : ""}`}>
            {value > 0 ? [t("common.poor.below.average", "Poor', 'Below Average', 'Average"), 'Good"), 'Excellent'][value - 1] : t("common.not.rated", "Not rated")}
          </span>
        </div>
      </div>
    );
  };

  return (
    <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
      <h2 className="text-xl font-semibold mb-4">t("common.session.feedback", "Session Feedback")</h2>

      {successMessage ? (
        <div className={`bg-green-900/30 border border-green-800/50 rounded-lg p-4 mb-4 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
          <Check size={20} className={`text-green-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
          <div>{successMessage}</div>
        </div>
      ) : (
        <form onSubmit={handleSubmit}>
          {error && (
            <div className={`bg-red-900/30 border border-red-800/50 rounded-lg p-4 mb-4 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <AlertCircle size={20} className={`text-red-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
              <div>{error}</div>
            </div>
          )}

          <div className="mb-6">
            <h3 className="text-lg font-medium mb-2">t("common.overall.rating", "Overall Rating")</h3>
            {renderStarRating('rating', formData.rating, t("common.how.would.you", "How would you rate this mentorship session overall?"))}
          </div>

          <div className="mb-6">
            <h3 className="text-lg font-medium mb-2">t("common.detailed.ratings", "Detailed Ratings")</h3>
            {renderStarRating('knowledge_rating', formData.knowledge_rating, 'Knowledge & Expertise')}
            {renderStarRating('communication_rating', formData.communication_rating, t("common.communication.skills", "Communication Skills"))}
            {renderStarRating('helpfulness_rating', formData.helpfulness_rating, t("common.helpfulness", "Helpfulness"))}
            {renderStarRating('preparation_rating', formData.preparation_rating, 'Preparation & Organization')}
            {renderStarRating('responsiveness_rating', formData.responsiveness_rating, t("common.responsiveness", "Responsiveness"))}
          </div>

          <div className="mb-6">
            <h3 className="text-lg font-medium mb-2">t("common.feedback.comments", "Feedback Comments")</h3>

            <div className="mb-4">
              <label htmlFor="comments" className="block text-sm font-medium mb-1">
                General Comments <span className="text-red-400">*</span>
              </label>
              <textarea
                id="comments"
                name="comments"
                value={formData.comments}
                onChange={handleChange}
                required
                rows={4}
                className="w-full bg-indigo-950/50 border border-indigo-800/50 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder={t("common.share.your.thoughts", "Share your thoughts about this mentorship session...")}
              />
            </div>

            <div className="mb-4">
              <label htmlFor="highlights" className="block text-sm font-medium mb-1">
                What went particularly well?
              </label>
              <textarea
                id="highlights"
                name="highlights"
                value={formData.highlights}
                onChange={handleChange}
                rows={3}
                className="w-full bg-indigo-950/50 border border-indigo-800/50 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder={t("common.what.aspects.of", "What aspects of the session were most valuable to you?")}
              />
            </div>

            <div className="mb-4">
              <label htmlFor="areas_of_improvement" className="block text-sm font-medium mb-1">
                Areas for Improvement
              </label>
              <textarea
                id="areas_of_improvement"
                name="areas_of_improvement"
                value={formData.areas_of_improvement}
                onChange={handleChange}
                rows={3}
                className="w-full bg-indigo-950/50 border border-indigo-800/50 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder={t("common.what.could.have", "What could have been better about this session?")}
              />
            </div>
          </div>

          <div className="mb-6">
            <h3 className="text-lg font-medium mb-2">t("common.session.outcomes", "Session Outcomes")</h3>

            <div className={`flex items-center mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
              <input
                type="checkbox"
                id="goals_achieved"
                name="goals_achieved"
                checked={formData.goals_achieved}
                onChange={handleCheckboxChange}
                className="h-5 w-5 rounded border-indigo-800/50 text-purple-600 focus:ring-purple-500 bg-indigo-950/50"
              />
              <label htmlFor="goals_achieved" className={`ml-2 text-sm ${isRTL ? "space-x-reverse" : ""}`}>
                Were the session goals achieved?
              </label>
            </div>

            <div className={`flex items-center mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
              <input
                type="checkbox"
                id="follow_up_needed"
                name="follow_up_needed"
                checked={formData.follow_up_needed}
                onChange={handleCheckboxChange}
                className="h-5 w-5 rounded border-indigo-800/50 text-purple-600 focus:ring-purple-500 bg-indigo-950/50"
              />
              <label htmlFor="follow_up_needed" className={`ml-2 text-sm ${isRTL ? "space-x-reverse" : ""}`}>
                Is follow-up needed?
              </label>
            </div>

            {formData.follow_up_needed && (
              <div className={`mb-4 ml-7 ${isRTL ? "space-x-reverse" : ""}`}>
                <label htmlFor="follow_up_notes" className="block text-sm font-medium mb-1">
                  Follow-up Notes
                </label>
                <textarea
                  id="follow_up_notes"
                  name="follow_up_notes"
                  value={formData.follow_up_notes}
                  onChange={handleChange}
                  rows={2}
                  className="w-full bg-indigo-950/50 border border-indigo-800/50 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder={t("common.what.should.be", "What should be followed up on?")}
                />
              </div>
            )}
          </div>

          <div className="mb-6">
            <h3 className="text-lg font-medium mb-2">t("common.privacy.settings", "Privacy Settings")</h3>

            <div className={`flex items-center mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
              <input
                type="checkbox"
                id="is_private"
                name="is_private"
                checked={formData.is_private}
                onChange={handleCheckboxChange}
                className="h-5 w-5 rounded border-indigo-800/50 text-purple-600 focus:ring-purple-500 bg-indigo-950/50"
              />
              <label htmlFor="is_private" className={`ml-2 text-sm ${isRTL ? "space-x-reverse" : ""}`}>
                Make this feedback private (only visible to administrators)
              </label>
            </div>

            <div className={`flex items-center mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
              <input
                type="checkbox"
                id="share_with_mentor"
                name="share_with_mentor"
                checked={formData.share_with_mentor}
                onChange={handleCheckboxChange}
                className="h-5 w-5 rounded border-indigo-800/50 text-purple-600 focus:ring-purple-500 bg-indigo-950/50"
              />
              <label htmlFor="share_with_mentor" className={`ml-2 text-sm ${isRTL ? "space-x-reverse" : ""}`}>
                Share this feedback with the mentor
              </label>
            </div>
          </div>

          <div className={`flex justify-end space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
            {onCancel && (
              <button
                type="button"
                onClick={onCancel}
                className={`px-4 py-2 border border-indigo-800/50 rounded-lg hover:bg-indigo-800/30 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <X size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> Cancel
              </button>
            )}

            <button
              type="submit"
              disabled={isSubmitting}
              className={`px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
            >
              {isSubmitting ? (
                <>
                  <div className={`w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 ${isRTL ? "space-x-reverse" : ""}`}></div>
                  Submitting...
                </>
              ) : (
                <>
                  <Send size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> Submit Feedback
                </>
              )}
            </button>
          </div>
        </form>
      )}
    </div>
  );
};

export default EnhancedFeedbackForm;
