import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Star,
  ThumbsUp,
  MessageSquare,
  AlertCircle,
  Check,
  ChevronDown,
  ChevronUp,
  Lock,
  Unlock
} from 'lucide-react';
import { useAppDispatch, useAppSelector } from '../../store/hooks';
import {
  fetchMentorshipSession,
  createMentorshipFeedback
} from '../../store/incubatorSlice';
import { MentorshipSession } from '../../services/incubatorApi';
import { useTranslation } from 'react-i18next';

interface MentorshipFeedbackFormProps {
  sessionId?: number;
  isMentee?: boolean;
  onSuccess?: () => void;
  onCancel?: () => void;
}

const MentorshipFeedbackForm: React.FC<MentorshipFeedbackFormProps> = ({ sessionId: propSessionId,
  isMentee = true,
  onSuccess,
  onCancel
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { sessionId: paramSessionId } = useParams<{ sessionId: string }>();
  const sessionId = propSessionId || (paramSessionId ? parseInt(paramSessionId) : undefined);

  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { selectedMentorshipSession, isLoading, error } = useAppSelector(state => state.incubator);
  const { user } = useAppSelector(state => state.auth);

  const [session, setSession] = useState<MentorshipSession | null>(null);
  const [showDetailedRatings, setShowDetailedRatings] = useState(false);

  const [formData, setFormData] = useState({
    rating: 5,
    knowledge_rating: 5,
    communication_rating: 5,
    helpfulness_rating: 5,
    preparation_rating: 5,
    responsiveness_rating: 5,
    comments: '',
    areas_of_improvement: '',
    highlights: '',
    goals_achieved: false,
    follow_up_needed: false,
    follow_up_notes: '',
    is_private: false,
    share_with_mentor: true
  });

  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Fetch session data
  useEffect(() => {
    if (sessionId) {
      dispatch(fetchMentorshipSession(sessionId));
    }
  }, [dispatch, sessionId]);

  // Set session from redux state
  useEffect(() => {
    if (selectedMentorshipSession) {
      setSession(selectedMentorshipSession);
    }
  }, [selectedMentorshipSession]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error for this field
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  const handleRatingChange = (name: string, value: number) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.comments.trim()) {
      errors.comments = t("common.please.provide.some", "Please provide some feedback comments");
    }

    if (formData.follow_up_needed && !formData.follow_up_notes.trim()) {
      errors.follow_up_notes = t("common.please.provide.followup", "Please provide follow-up notes");
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm() || !sessionId) {
      return;
    }

    try {
      await dispatch(createMentorshipFeedback({
        session: sessionId,
        is_from_mentee: isMentee,
        ...formData
      })).unwrap();

      setSuccessMessage(t("common.feedback.submitted.successfully", "Feedback submitted successfully!"));

      // Call onSuccess callback if provided
      if (onSuccess) {
        setTimeout(() => {
          onSuccess();
        }, 1500);
      }
    } catch (err) {
      console.error('Error submitting feedback:', err);
      setFormErrors({
        submit: t("common.failed.to.submit", "Failed to submit feedback. Please try again.")
      });
    }
  };

  // Render star rating component
  const StarRating = ({
    name,
    value,
    onChange
  }: {
    name: string;
    value: number;
    onChange: (name: string, value: number) => void
  }) => {
    return (
      <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
        {[1, 2, 3, 4, 5].map(star => (
          <button
            key={star}
            type="button"
            onClick={() => onChange(name, star)}
            className="focus:outline-none"
          >
            <Star
              size={24}
              className={`${
                star <= value
                  ? 'text-yellow-400 fill-yellow-400'
                  : 'text-gray-400'}
              }`}
            />
          </button>
        ))}
      </div>
    );
  };

  if (isLoading && !session) {
    return (
      <div className={`flex justify-center items-center h-64 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className="w-12 h-12 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  if (error || !sessionId) {
    return (
      <div className="bg-red-900/30 text-red-200 p-4 rounded-lg">
        <h3 className="font-bold">t("common.error.loading.session", "Error Loading Session")</h3>
        <div>{error || t("common.session.id.is", "Session ID is required")}</div>
      </div>
    );
  }

  if (!session) {
    return (
      <div className="bg-indigo-900/30 text-gray-200 p-4 rounded-lg">
        <h3 className="font-bold">t("common.session.not.found", "Session Not Found")</h3>
        <div>t("common.the.requested.mentorship", "The requested mentorship session could not be found.")</div>
      </div>
    );
  }

  return (
    <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
      <h2 className="text-xl font-bold mb-1">{t("common.session.feedback", "Session Feedback")}</h2>
      <div className="text-gray-400 mb-4">
        {isMentee ? t("common.rate.your.mentor", "Rate your mentor and provide feedback") : t("common.provide.feedback.on", "Provide feedback on this mentorship session")}
      </div>

      {successMessage && (
        <div className={`mb-6 p-4 bg-green-900/50 text-green-200 rounded-md flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
          <Check size={20} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
          {successMessage}
        </div>
      )}

      {formErrors.submit && (
        <div className={`mb-6 p-4 bg-red-900/50 text-red-200 rounded-md flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
          <AlertCircle size={20} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
          {formErrors.submit}
        </div>
      )}

      <form onSubmit={handleSubmit}>
        {/* Session Info */}
        <div className="mb-6 bg-indigo-950/50 p-4 rounded-lg">
          <h3 className="font-medium mb-2">{session.title}</h3>
          <div className="text-sm text-gray-400">
            {new Date(session.scheduled_at).toLocaleString()}
            {session.duration_minutes && (
              <span className={`ml-2 ${isRTL ? "space-x-reverse" : ""}`}>({session.duration_minutes} minutes)</span>
            )}
          </div>
        </div>

        {/* Overall Rating */}
        <div className="mb-6">
          <label className="block text-lg font-medium mb-2">
            {isMentee ? t("common.rate.your.mentor", "Rate Your Mentor") : t("common.overall.session.rating", "Overall Session Rating")}
          </label>
          <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <StarRating
              name="rating"
              value={formData.rating}
              onChange={handleRatingChange}
            />
            <span className={`ml-2 text-gray-300 ${isRTL ? "space-x-reverse" : ""}`}>
              {formData.rating}/5
            </span>
          </div>
        </div>

        {/* Detailed Ratings Toggle */}
        <button
          type="button"
          className={`flex items-center text-purple-400 hover:text-purple-300 mb-4 focus:outline-none ${isRTL ? "flex-row-reverse" : ""}`}
          onClick={() => setShowDetailedRatings(!showDetailedRatings)}
        >
          {showDetailedRatings ? (
            <>
              <ChevronUp size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
              Hide Detailed Ratings
            </>
          ) : (
            <>
              <ChevronDown size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
              Show Detailed Ratings
            </>
          )}
        </button>

        {/* Detailed Ratings */}
        {showDetailedRatings && (
          <div className="mb-6 space-y-4 bg-indigo-950/50 p-4 rounded-lg">
            <div>
              <label className="block text-sm font-medium mb-1">Knowledge & Expertise</label>
              <StarRating
                name="knowledge_rating"
                value={formData.knowledge_rating}
                onChange={handleRatingChange}
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">{t("common.communication.skills", "Communication Skills")}</label>
              <StarRating
                name="communication_rating"
                value={formData.communication_rating}
                onChange={handleRatingChange}
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">{t("common.helpfulness", "Helpfulness")}</label>
              <StarRating
                name="helpfulness_rating"
                value={formData.helpfulness_rating}
                onChange={handleRatingChange}
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Preparation & Organization</label>
              <StarRating
                name="preparation_rating"
                value={formData.preparation_rating}
                onChange={handleRatingChange}
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">{t("common.responsiveness", "Responsiveness")}</label>
              <StarRating
                name="responsiveness_rating"
                value={formData.responsiveness_rating}
                onChange={handleRatingChange}
              />
            </div>
          </div>
        )}

        {/* Comments */}
        <div className="mb-4">
          <label className="block text-sm font-medium mb-1">
            Comments <span className="text-red-400">*</span>
          </label>
          <textarea
            name="comments"
            value={formData.comments}
            onChange={handleInputChange}
            placeholder={t("common.share.your.thoughts", "Share your thoughts about this session...")}
            rows={4}
            className={`w-full bg-indigo-950 border ${formErrors.comments ? 'border-red-500' : 'border-indigo-700'} rounded-md p-3 focus:outline-none focus:ring-2 focus:ring-purple-500`}
          />
          {formErrors.comments && (
            <div className="text-red-500 text-xs mt-1">{formErrors.comments}</div>
          )}
        </div>

        {/* Highlights */}
        <div className="mb-4">
          <label className="block text-sm font-medium mb-1">
            What went well? (Optional)
          </label>
          <textarea
            name="highlights"
            value={formData.highlights}
            onChange={handleInputChange}
            placeholder={t("common.what.aspects.of", "What aspects of the session were particularly helpful or effective?")}
            rows={3}
            className="w-full bg-indigo-950 border border-indigo-700 rounded-md p-3 focus:outline-none focus:ring-2 focus:ring-purple-500"
          />
        </div>

        {/* Areas for Improvement */}
        <div className="mb-4">
          <label className="block text-sm font-medium mb-1">
            Areas for improvement (Optional)
          </label>
          <textarea
            name="areas_of_improvement"
            value={formData.areas_of_improvement}
            onChange={handleInputChange}
            placeholder={t("common.what.could.have", "What could have been better or more effective?")}
            rows={3}
            className="w-full bg-indigo-950 border border-indigo-700 rounded-md p-3 focus:outline-none focus:ring-2 focus:ring-purple-500"
          />
        </div>

        {/* Session Outcomes */}
        <div className="mb-6 space-y-4 bg-indigo-950/50 p-4 rounded-lg">
          <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <input
              type="checkbox"
              id="goals_achieved"
              name="goals_achieved"
              checked={formData.goals_achieved}
              onChange={handleCheckboxChange}
              className="w-4 h-4 bg-indigo-900 border-indigo-700 rounded focus:ring-purple-500"
            />
            <label htmlFor="goals_achieved" className={`ml-2 text-sm ${isRTL ? "space-x-reverse" : ""}`}>
              Session goals were achieved
            </label>
          </div>

          <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <input
              type="checkbox"
              id="follow_up_needed"
              name="follow_up_needed"
              checked={formData.follow_up_needed}
              onChange={handleCheckboxChange}
              className="w-4 h-4 bg-indigo-900 border-indigo-700 rounded focus:ring-purple-500"
            />
            <label htmlFor="follow_up_needed" className={`ml-2 text-sm ${isRTL ? "space-x-reverse" : ""}`}>
              Follow-up is needed
            </label>
          </div>

          {formData.follow_up_needed && (
            <div>
              <label className="block text-sm font-medium mb-1">
                Follow-up notes <span className="text-red-400">*</span>
              </label>
              <textarea
                name="follow_up_notes"
                value={formData.follow_up_notes}
                onChange={handleInputChange}
                placeholder={t("common.what.followup.is", "What follow-up is needed?")}
                rows={2}
                className={`w-full bg-indigo-950 border ${formErrors.follow_up_notes ? 'border-red-500' : 'border-indigo-700'} rounded-md p-3 focus:outline-none focus:ring-2 focus:ring-purple-500`}
              />
              {formErrors.follow_up_notes && (
                <div className="text-red-500 text-xs mt-1">{formErrors.follow_up_notes}</div>
              )}
            </div>
          )}
        </div>

        {/* Privacy Settings */}
        <div className="mb-6 space-y-4 bg-indigo-950/50 p-4 rounded-lg">
          <h3 className="font-medium mb-2">{t("common.privacy.settings", "Privacy Settings")}</h3>

          <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <input
              type="checkbox"
              id="is_private"
              name="is_private"
              checked={formData.is_private}
              onChange={handleCheckboxChange}
              className="w-4 h-4 bg-indigo-900 border-indigo-700 rounded focus:ring-purple-500"
            />
            <label htmlFor="is_private" className={`ml-2 text-sm flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <Lock size={14} className={`mr-1 text-gray-400 ${isRTL ? "space-x-reverse" : ""}`} />
              Make feedback private (only visible to administrators)
            </label>
          </div>

          {isMentee && !formData.is_private && (
            <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <input
                type="checkbox"
                id="share_with_mentor"
                name="share_with_mentor"
                checked={formData.share_with_mentor}
                onChange={handleCheckboxChange}
                className="w-4 h-4 bg-indigo-900 border-indigo-700 rounded focus:ring-purple-500"
              />
              <label htmlFor="share_with_mentor" className={`ml-2 text-sm flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <Unlock size={14} className={`mr-1 text-gray-400 ${isRTL ? "space-x-reverse" : ""}`} />
                Share this feedback with the mentor
              </label>
            </div>
          )}
        </div>

        {/* Submit Buttons */}
        <div className={`flex justify-end space-x-3 mt-6 ${isRTL ? "flex-row-reverse" : ""}`}>
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-md text-white"
            >
              Cancel
            </button>
          )}

          <button
            type="submit"
            disabled={isLoading}
            className={`px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
          >
            {isLoading ? (
              <>
                <div className={`w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 ${isRTL ? "space-x-reverse" : ""}`}></div>
                Submitting...
              </>
            ) : (
              <>
                <ThumbsUp size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                Submit Feedback
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default MentorshipFeedbackForm;
