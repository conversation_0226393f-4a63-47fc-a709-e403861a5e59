import React from 'react';
import { X } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';

interface BaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '4xl';
  showCloseButton?: boolean;
  closeOnBackdropClick?: boolean;
  className?: string;
  headerClassName?: string;
  contentClassName?: string;
  footer?: React.ReactNode;
  sidebar?: React.ReactNode;
  maxHeight?: string;
}

const BaseModal: React.FC<BaseModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  size = 'lg',
  showCloseButton = true,
  closeOnBackdropClick = true,
  className = '',
  headerClassName = '',
  contentClassName = '',
  footer,
  sidebar,
  maxHeight = '90vh'
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  if (!isOpen) return null;

  const sizeClasses = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl',
    '2xl': 'max-w-6xl',
    '4xl': 'max-w-7xl'
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (closeOnBackdropClick && e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  React.useEffect(() => {
    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  return (
    <div 
      className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4"
      onClick={handleBackdropClick}
    >
      <div 
        className={`
          glass-morphism border border-glass-border rounded-xl shadow-lg w-full 
          ${sizeClasses[size]} 
          ${sidebar ? 'overflow-hidden' : 'overflow-y-auto'}
          ${className}
        `}
        style={{ maxHeight }}
      >
        {sidebar ? (
          // Layout with sidebar
          <div className="flex h-full">
            {/* Sidebar */}
            <div className="w-64 glass-light border-r border-glass-border p-4">
              {sidebar}
            </div>

            {/* Main Content */}
            <div className="flex-1 flex flex-col">
              {/* Header */}
              <div className={`
                flex justify-between items-center p-6 border-b border-glass-border
                ${headerClassName}
              `}>
                <h2 className="text-xl font-bold text-glass-primary">
                  {title}
                </h2>
                {showCloseButton && (
                  <button
                    onClick={onClose}
                    className="text-glass-muted hover:text-glass-primary transition-colors"
                    aria-label={t('common.close', 'Close')}
                  >
                    <X size={20} />
                  </button>
                )}
              </div>

              {/* Content */}
              <div className={`flex-1 p-6 overflow-y-auto ${contentClassName}`}>
                {children}
              </div>

              {/* Footer */}
              {footer && (
                <div className="p-6 border-t border-glass-border">
                  {footer}
                </div>
              )}
            </div>
          </div>
        ) : (
          // Standard layout without sidebar
          <div className="p-6">
            {/* Header */}
            <div className={`
              flex justify-between items-center mb-6
              ${headerClassName}
            `}>
              <h2 className="text-xl font-bold text-glass-primary">
                {title}
              </h2>
              {showCloseButton && (
                <button
                  onClick={onClose}
                  className="text-glass-muted hover:text-glass-primary transition-colors"
                  aria-label={t('common.close', 'Close')}
                >
                  <X size={20} />
                </button>
              )}
            </div>

            {/* Content */}
            <div className={contentClassName}>
              {children}
            </div>

            {/* Footer */}
            {footer && (
              <div className="mt-6 pt-4 border-t border-glass-border">
                {footer}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default BaseModal;
