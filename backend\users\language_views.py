from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.utils import translation
from django.conf import settings

class LanguageView(APIView):
    """
    API view for getting and setting the language
    """
    
    def get(self, request, *args, **kwargs):
        """
        Get the current language
        """
        current_language = translation.get_language()
        return Response({
            'language': current_language,
            'available_languages': dict(settings.LANGUAGES)
        })
    
    def post(self, request, *args, **kwargs):
        """
        Set the language
        """
        language = request.data.get('language')

        # Check if the language is valid
        if language not in dict(settings.LANGUAGES):
            return Response({
                'error': f'Invalid language: {language}. Available languages: {dict(settings.LANGUAGES)}'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Set the language in session
        translation.activate(language)
        request.session[translation.LANGUAGE_SESSION_KEY] = language

        # If user is authenticated, save language preference to profile
        if request.user.is_authenticated:
            try:
                profile, created = request.user.profile.get_or_create()
                profile.language = language
                profile.save()
            except Exception as e:
                # Log the error but don't fail the request
                print(f"Failed to save language preference: {e}")

        return Response({
            'message': f'Language set to {language}',
            'language': language
        })
