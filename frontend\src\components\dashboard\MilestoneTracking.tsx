import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  CheckCircle,
  Clock,
  AlertTriangle,
  Flag,
  Plus,
  Calendar,
  User,
  ArrowRight,
  Edit,
  Trash2,
  Download
} from 'lucide-react';
import { useAppSelector } from '../../store/hooks';
import { BusinessMilestone, businessMilestonesAPI } from '../../services/milestoneApi';
import { exportToCSV, exportToPDF } from '../../utils/exportUtils';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';

interface MilestoneTrackingProps {
  businessIdeaId: number;
  businessIdeaTitle: string;
}

const MilestoneTracking: React.FC<MilestoneTrackingProps> = ({ businessIdeaId, businessIdeaTitle  }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { user } = useAppSelector((state) => state.auth);
  const [milestones, setMilestones] = useState<BusinessMilestone[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [selectedMilestone, setSelectedMilestone] = useState<BusinessMilestone | null>(null);
  const [completionNotes, setCompletionNotes] = useState('');
  const [showCompletionModal, setShowCompletionModal] = useState(false);
  const [filterStatus, setFilterStatus] = useState<string>('all');

  useEffect(() => {
    fetchMilestones();
  }, [businessIdeaId]);

  const fetchMilestones = async () => {
    setLoading(true);
    try {
      const data = await businessMilestonesAPI.getMilestones(businessIdeaId);
      setMilestones(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching milestones:', err);
      setError(t("dashboard.failed.to.load", "Failed to load milestones. Please try again later."));
    } finally {
      setLoading(false);
    }
  };

  const handleCompleteMilestone = async () => {
    if (!selectedMilestone) return;

    try {
      await businessMilestonesAPI.completeMilestone(selectedMilestone.id, completionNotes);
      fetchMilestones();
      setShowCompletionModal(false);
      setCompletionNotes('');
      setSelectedMilestone(null);
    } catch (err) {
      console.error('Error completing milestone:', err);
      setError(t("dashboard.failed.to.complete", "Failed to complete milestone. Please try again later."));
    }
  };

  const handleExportCSV = () => {
    const data = milestones.map(milestone => ({
      Title: milestone.title,
      Description: milestone.description,
      Status: milestone.status_display,
      Priority: milestone.priority_display,
      'Due Date': new Date(milestone.due_date).toLocaleDateString(),
      'Days Remaining': milestone.days_remaining,
      'Is Overdue': milestone.is_overdue ? t("dashboard.yes", "Yes") : t("dashboard.no", "No"),
      'Assigned To': milestone.assigned_to ? `${milestone.assigned_to.first_name} ${milestone.assigned_to.last_name}` : t("dashboard.unassigned", "Unassigned"),
      'Completion Date': milestone.completion_date ? new Date(milestone.completion_date).toLocaleDateString() : t("dashboard.not.completed", "Not completed"),
      'Completion Notes': milestone.completion_notes || ''
    }));

    exportToCSV(data, `${businessIdeaTitle.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_milestones`);
  };

  const handleExportPDF = () => {
    const columns = [
      { header: t("dashboard.title.datakey.title", "Title"), dataKey: 'title' },
      { header: t("dashboard.status.datakey.status", "Status"), dataKey: 'status' },
      { header: t("dashboard.priority.datakey.priority", "Priority"), dataKey: 'priority' },
      { header: t("dashboard.due.date.datakey", "Due Date"), dataKey: 'dueDate' },
      { header: t("dashboard.days.remaining.datakey", "Days Remaining"), dataKey: 'daysRemaining' },
      { header: t("dashboard.assigned.to.datakey", "Assigned To"), dataKey: 'assignedTo' }
    ];

    const data = milestones.map(milestone => ({
      title: milestone.title,
      status: milestone.status_display,
      priority: milestone.priority_display,
      dueDate: new Date(milestone.due_date).toLocaleDateString(),
      daysRemaining: milestone.days_remaining.toString(),
      assignedTo: milestone.assigned_to ? `${milestone.assigned_to.first_name} ${milestone.assigned_to.last_name}` : t("dashboard.unassigned", "Unassigned")
    }));

    exportToPDF(
      columns,
      data,
      `${businessIdeaTitle} - Milestones`,
      `Milestones for ${businessIdeaTitle}`,
      `${businessIdeaTitle.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_milestones`
    );
  };

  const filteredMilestones = filterStatus === 'all'
    ? milestones
    : milestones.filter(milestone => milestone.status === filterStatus);

  // Group milestones by status
  const groupedMilestones = {
    not_started: filteredMilestones.filter(m => m.status === 'not_started'),
    in_progress: filteredMilestones.filter(m => m.status === 'in_progress'),
    completed: filteredMilestones.filter(m => m.status === 'completed'),
    delayed: filteredMilestones.filter(m => m.status === 'delayed'),
    cancelled: filteredMilestones.filter(m => m.status === 'cancelled')
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'not_started': return 'bg-gray-700 text-gray-200';
      case 'in_progress': return 'bg-blue-900/50 text-blue-200';
      case 'completed': return 'bg-green-900/50 text-green-200';
      case 'delayed': return 'bg-yellow-900/50 text-yellow-200';
      case 'cancelled': return 'bg-red-900/50 text-red-200';
      default: return 'bg-gray-700 text-gray-200';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'bg-gray-700 text-gray-200';
      case 'medium': return 'bg-blue-900/50 text-blue-200';
      case 'high': return 'bg-yellow-900/50 text-yellow-200';
      case 'critical': return 'bg-red-900/50 text-red-200';
      default: return 'bg-gray-700 text-gray-200';
    }
  };

  if (loading) {
    return (
      <div className={`flex justify-center py-12 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-900/30 backdrop-blur-sm rounded-lg p-4 border border-red-800/50">
        <div className="text-red-400">{error}</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
        <h2 className="text-xl font-bold">{t("dashboard.milestones", "Milestones")}</h2>
        <div className={`flex space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
          <button
            onClick={handleExportCSV}
            className={`px-3 py-1.5 bg-green-600 hover:bg-green-700 rounded-md text-white text-sm flex items-center transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <Download size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> CSV
          </button>
          <button
            onClick={handleExportPDF}
            className={`px-3 py-1.5 bg-red-600 hover:bg-red-700 rounded-md text-white text-sm flex items-center transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <Download size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> PDF
          </button>
          <button
            onClick={() => setShowAddForm(true)}
            className={`px-3 py-1.5 bg-purple-600 hover:bg-purple-700 rounded-md text-white text-sm flex items-center transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <Plus size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> Add Milestone
          </button>
        </div>
      </div>

      {/* Filter Controls */}
      <div className={`flex space-x-2 overflow-x-auto pb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
        <button
          onClick={() => setFilterStatus('all')}
          className={`px-3 py-1.5 rounded-md text-sm flex items-center transition-colors ${
            filterStatus === 'all' ? 'bg-purple-600 text-white' : 'bg-gray-800 text-gray-300 hover:bg-gray-700'}
          }`}
        >
          All
        </button>
        <button
          onClick={() => setFilterStatus('not_started')}
          className={`px-3 py-1.5 rounded-md text-sm flex items-center transition-colors ${
            filterStatus === 'not_started' ? 'bg-purple-600 text-white' : 'bg-gray-800 text-gray-300 hover:bg-gray-700'}
          }`}
        >
          <Clock size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> Not Started
        </button>
        <button
          onClick={() => setFilterStatus('in_progress')}
          className={`px-3 py-1.5 rounded-md text-sm flex items-center transition-colors ${
            filterStatus === 'in_progress' ? 'bg-purple-600 text-white' : 'bg-gray-800 text-gray-300 hover:bg-gray-700'}
          }`}
        >
          <Flag size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> In Progress
        </button>
        <button
          onClick={() => setFilterStatus('completed')}
          className={`px-3 py-1.5 rounded-md text-sm flex items-center transition-colors ${
            filterStatus === 'completed' ? 'bg-purple-600 text-white' : 'bg-gray-800 text-gray-300 hover:bg-gray-700'}
          }`}
        >
          <CheckCircle size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> Completed
        </button>
        <button
          onClick={() => setFilterStatus('delayed')}
          className={`px-3 py-1.5 rounded-md text-sm flex items-center transition-colors ${
            filterStatus === 'delayed' ? 'bg-purple-600 text-white' : 'bg-gray-800 text-gray-300 hover:bg-gray-700'}
          }`}
        >
          <AlertTriangle size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> Delayed
        </button>
      </div>

      {/* Milestones List */}
      {filteredMilestones.length === 0 ? (
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50 text-center">
          <div className="text-gray-400">{t("dashboard.no.milestones.found", "No milestones found. Add your first milestone to track progress.")}</div>
          <button
            onClick={() => setShowAddForm(true)}
            className={`mt-4 px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white flex items-center mx-auto ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <Plus size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} /> Add Milestone
          </button>
        </div>
      ) : (
        <div className="space-y-4">
          {Object.entries(groupedMilestones).map(([status, statusMilestones]) =>
            statusMilestones.length > 0 && (
              <div key={status} className="space-y-2">
                <h3 className={`text-lg font-medium flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                  {status === 'not_started' && <Clock size={18} className={`mr-2 text-gray-400 ${isRTL ? "space-x-reverse" : ""}`} />}
                  {status === 'in_progress' && <Flag size={18} className={`mr-2 text-blue-400 ${isRTL ? "space-x-reverse" : ""}`} />}
                  {status === 'completed' && <CheckCircle size={18} className={`mr-2 text-green-400 ${isRTL ? "space-x-reverse" : ""}`} />}
                  {status === 'delayed' && <AlertTriangle size={18} className={`mr-2 text-yellow-400 ${isRTL ? "space-x-reverse" : ""}`} />}
                  {status === 'cancelled' && <AlertTriangle size={18} className={`mr-2 text-red-400 ${isRTL ? "space-x-reverse" : ""}`} />}
                  {status === 'not_started' && t("dashboard.not.started", "Not Started")}
                  {status === 'in_progress' && t("dashboard.in.progress", "In Progress")}
                  {status === 'completed' && t("dashboard.completed", "Completed")}
                  {status === 'delayed' && t("dashboard.delayed", "Delayed")}
                  {status === 'cancelled' && t("dashboard.cancelled", "Cancelled")}
                  <span className={`ml-2 text-sm text-gray-400 ${isRTL ? "space-x-reverse" : ""}`}>({statusMilestones.length})</span>
                </h3>

                <div className="space-y-2">
                  {statusMilestones.map(milestone => (
                    <div
                      key={milestone.id}
                      className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50"
                    >
                      <div className={`flex justify-between items-start mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <h4 className="text-lg font-medium">{milestone.title}</h4>
                        <div className={`flex space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                          <span className={`px-2 py-1 rounded text-xs ${getPriorityColor(milestone.priority)}`}>
                            {milestone.priority_display}
                          </span>
                          <span className={`px-2 py-1 rounded text-xs ${getStatusColor(milestone.status)}`}>
                            {milestone.status_display}
                          </span>
                        </div>
                      </div>

                      <div className="text-gray-300 mb-3">{milestone.description}</div>

                      <div className="grid grid-cols-2 gap-4 mb-3 text-sm">
                        <div className={`flex items-center text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                          <Calendar size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                          <span>Due: {new Date(milestone.due_date).toLocaleDateString()}</span>
                        </div>

                        <div className={`flex items-center text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                          <User size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                          <span>
                            {milestone.assigned_to
                              ? `${milestone.assigned_to.first_name} ${milestone.assigned_to.last_name}`
                              : t("dashboard.unassigned", "Unassigned")}
                          </span>
                        </div>
                      </div>

                      {milestone.status !== 'completed' && milestone.is_overdue && (
                        <div className={`mb-3 text-red-400 text-sm flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                          <AlertTriangle size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                          <span>Overdue by {Math.abs(milestone.days_remaining)} days</span>
                        </div>
                      )}

                      {milestone.status !== 'completed' && !milestone.is_overdue && (
                        <div className={`mb-3 text-blue-400 text-sm flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                          <Clock size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                          <span>{milestone.days_remaining} days remaining</span>
                        </div>
                      )}

                      {milestone.status === 'completed' && milestone.completion_date && (
                        <div className={`mb-3 text-green-400 text-sm flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                          <CheckCircle size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                          <span>Completed on {new Date(milestone.completion_date).toLocaleDateString()}</span>
                        </div>
                      )}

                      <div className={`flex justify-end space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                        {milestone.status !== 'completed' && (
                          <button
                            onClick={() => {
                              setSelectedMilestone(milestone);
                              setShowCompletionModal(true);
                            }}
                            className={`px-2 py-1 bg-green-600 hover:bg-green-700 rounded text-white text-xs flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                          >
                            <CheckCircle size={12} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> Complete
                          </button>
                        )}

                        <button
                          onClick={() => {
                            // Handle edit
                          }}
                          className={`px-2 py-1 bg-blue-600 hover:bg-blue-700 rounded text-white text-xs flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                        >
                          <Edit size={12} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> Edit
                        </button>

                        <button
                          onClick={() => {
                            // Handle delete
                          }}
                          className={`px-2 py-1 bg-red-600 hover:bg-red-700 rounded text-white text-xs flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                        >
                          <Trash2 size={12} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> Delete
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )
          )}
        </div>
      )}

      {/* Completion Modal */}
      {showCompletionModal && selectedMilestone && (
        <div className={`fixed inset-0 bg-black/70 flex items-center justify-center z-50 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="bg-gray-900 rounded-lg p-6 max-w-md w-full">
            <h3 className="text-xl font-bold mb-4">{t("dashboard.complete.milestone", "Complete Milestone")}</h3>
            <div className="mb-4">Mark "{selectedMilestone.title}" as completed?</div>

            <div className="mb-4">
              <label className="block text-sm font-medium mb-1">{t("dashboard.completion.notes", "Completion Notes")}</label>
              <textarea
                className="w-full bg-gray-800 border border-gray-700 rounded-md p-2 text-white"
                rows={4}
                value={completionNotes}
                onChange={(e) => setCompletionNotes(e.target.value)}
                placeholder={t("dashboard.add.any.notes", "Add any notes about the completion of this milestone...")}
              />
            </div>

            <div className={`flex justify-end space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                onClick={() => {
                  setShowCompletionModal(false);
                  setSelectedMilestone(null);
                  setCompletionNotes('');
                }}
                className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-md text-white"
              >
                Cancel
              </button>
              <button
                onClick={handleCompleteMilestone}
                className={`px-4 py-2 bg-green-600 hover:bg-green-700 rounded-md text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <CheckCircle size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} /> Mark as Completed
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MilestoneTracking;
