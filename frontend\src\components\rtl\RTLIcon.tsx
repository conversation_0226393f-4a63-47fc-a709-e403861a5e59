import React from 'react';
import { useLanguage } from '../../hooks/useLanguage';
import { RTLIconProps } from '../../types/rtl';
import { LucideIcon } from 'lucide-react';
/**
 * RTL-aware icon component that automatically flips directional icons in RTL mode
 *
 * This component handles icon flipping based on the current language direction.
 *
 * @example
 * ```tsx
 * <RTLIcon icon={ArrowRight} flipInRTL={true} />
 * ```
 */
const RTLIcon: React.FC<RTLIconProps> = ({
  icon,
  size = 20,
  className = '',
  flipInRTL = false,
  strokeWidth,
  color,
  reverseInRTL = false,
}) => {
  const { isRTL } = useLanguage();

  // Determine if the icon should be flipped
  const shouldFlip = flipInRTL && isRTL;

  // Determine if the container should be reversed
  const shouldReverse = reverseInRTL && isRTL;

  // Check if icon is defined
  if (!icon) {
    console.error("RTLIcon: icon prop is required");
    return null;
  }

  const Icon = icon;

  return (
    <Icon
      size={size}
      className={`${className} ${shouldFlip ? 'flip-in-rtl' : ''} ${shouldReverse ? 'rtl-reverse' : ''}`}
      strokeWidth={strokeWidth}
      color={color}
    />
  );
};

export default RTLIcon;
