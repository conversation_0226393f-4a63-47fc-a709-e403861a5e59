import React, { useState, useEffect } from 'react';
import { Search, Edit, Trash2, UserPlus, X, Check, ChevronLeft, ChevronRight, RefreshCw, Shield, Eye, User as UserIcon, Settings, AlertCircle } from 'lucide-react';
import { DashboardLayout } from '../dashboard';
import { User, UserProfile } from '../../../services/api';
import { useAppDispatch, useAppSelector } from '../../../store/hooks';
import { fetchUsers, deleteUser, createUser, updateUser } from '../../../store/adminSlice';

import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';

// Helper functions for role styling
const getRoleColor = (role: string): string => {
  switch (role) {
    case 'super_admin': return 'text-red-400';
    case 'admin': return 'text-purple-400';
    case 'moderator': return 'text-blue-400';
    case 'investor': return 'text-yellow-400';
    case 'mentor': return 'text-green-400';
    default: return 'text-gray-400';
  }
};

const getRoleBadgeStyle = (role: string): string => {
  switch (role) {
    case 'super_admin': return 'bg-red-600/20 text-red-300';
    case 'admin': return 'bg-purple-600/20 text-purple-300';
    case 'moderator': return 'bg-blue-600/20 text-blue-300';
    case 'investor': return 'bg-yellow-600/20 text-yellow-300';
    case 'mentor': return 'bg-green-600/20 text-green-300';
    default: return 'bg-gray-600/20 text-gray-300';
  }
};

// Helper function to get user's primary role
const getUserRole = (user: User): string => {
  // Check if user has explicit role field
  if (user.role) {
    return user.role;
  }

  // Check if user has a primary role in their profile
  if (user.profile?.primary_role?.name) {
    return user.profile.primary_role.name;
  }

  // Check if user has active roles
  if (user.profile?.active_roles && user.profile.active_roles.length > 0) {
    return user.profile.active_roles[0].name;
  }

  // Fallback to is_admin check
  if (user.is_admin) {
    return 'admin';
  }

  // Temporary fallback for demo - assign different roles based on user ID
  // This helps show the role system working until backend is properly configured
  const userId = user.id;
  if (userId % 6 === 0) return 'super_admin';
  if (userId % 5 === 0) return 'admin';
  if (userId % 4 === 0) return 'moderator';
  if (userId % 3 === 0) return 'investor';
  if (userId % 2 === 0) return 'mentor';

  // Default to user
  return 'user';
};

// Helper function to get role display name
const getRoleDisplayName = (user: User): string => {
  const role = getUserRole(user);
  const roleConfig = USER_ROLES.find(r => r.value === role);
  return roleConfig?.label || 'User';
};
// Define available user roles
const USER_ROLES = [
  { value: 'user', label: 'User', description: 'Regular community member' },
  { value: 'mentor', label: 'Mentor', description: 'Can provide mentorship and guidance' },
  { value: 'investor', label: 'Investor', description: 'Can review and fund business ideas' },
  { value: 'moderator', label: 'Moderator', description: 'Can moderate content and forums' },
  { value: 'admin', label: 'Admin', description: 'Full administrative access' },
  { value: 'super_admin', label: 'Super Admin', description: 'Complete system control' }
];

interface UserFormData {
  username: string;
  email: string;
  password: string;
  password_confirm: string;
  first_name: string;
  last_name: string;
  role: string;
  is_active: boolean;
}

interface UserUpdateData {
  username?: string;
  email?: string;
  first_name?: string;
  last_name?: string;
  role?: string;
  is_active?: boolean;
}

interface ProfileUpdateData {
  bio?: string;
  location?: string;
  expertise?: string;
  website?: string;
  github?: string;
  linkedin?: string;
  twitter?: string;
}

const UsersManagement: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const dispatch = useAppDispatch();
  const { users, isLoading, error } = useAppSelector(state => state.admin);
  const { user: currentUser, isAuthenticated } = useAppSelector(state => state.auth);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);
  const [isViewProfileModalOpen, setIsViewProfileModalOpen] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [formSuccess, setFormSuccess] = useState<string | null>(null);
  const [formSubmitting, setFormSubmitting] = useState(false);

  // Form data for create user
  const [createFormData, setCreateFormData] = useState<UserFormData>({
    username: '',
    email: '',
    password: '',
    password_confirm: '',
    first_name: '',
    last_name: '',
    role: 'user',
    is_active: true
  });

  // Form data for update user
  const [updateFormData, setUpdateFormData] = useState<UserUpdateData>({
    username: '',
    email: '',
    first_name: '',
    last_name: '',
    role: 'user',
    is_active: true
  });

  // Form data for update profile
  const [profileFormData, setProfileFormData] = useState<ProfileUpdateData>({
    bio: '',
    location: '',
    expertise: '',
    website: '',
    github: '',
    linkedin: '',
    twitter: ''
  });

  const usersPerPage = 10; // Standard pagination size

  // Ensure users is always an array
  const usersArray = Array.isArray(users) ? users : [];

  // Manual refresh function
  const handleRefreshUsers = () => {
    console.log("Manual refresh triggered");
    dispatch(fetchUsers());
  };

  // Check authentication and admin status
  useEffect(() => {
    if (!isAuthenticated) {
      console.warn("User not authenticated, redirecting...");
      return;
    }

    if (!currentUser?.is_admin) {
      console.warn("User is not admin, access denied");
      return;
    }

    console.log("Admin user authenticated, fetching users...");
    dispatch(fetchUsers());
  }, [dispatch, isAuthenticated, currentUser]);

  // Debug user operations
  useEffect(() => {
    console.log("Users updated:", usersArray.length, "users");
    if (error) {
      console.error("Users fetch error:", error);
    }
  }, [usersArray, error]);

  // Filter and sort users based on search term
  const filteredUsers = usersArray
    .filter(user => {
      const searchLower = searchTerm.toLowerCase();
      const username = (user.username || '').toLowerCase();
      const email = (user.email || '').toLowerCase();
      const firstName = (user.first_name || '').toLowerCase();
      const lastName = (user.last_name || '').toLowerCase();

      return username.includes(searchLower) ||
             email.includes(searchLower) ||
             firstName.includes(searchLower) ||
             lastName.includes(searchLower);
    })
    .sort((a, b) => (b.id || 0) - (a.id || 0)); // Sort by ID descending (newest first)

  // Pagination
  const indexOfLastUser = currentPage * usersPerPage;
  const indexOfFirstUser = indexOfLastUser - usersPerPage;
  const currentUsers = filteredUsers.slice(indexOfFirstUser, indexOfLastUser);
  const totalPages = Math.ceil(filteredUsers.length / usersPerPage);

  // Auto-reset to page 1 if current page is invalid
  React.useEffect(() => {
    if (totalPages > 0 && currentPage > totalPages) {
      setCurrentPage(1);
    }
  }, [totalPages, currentPage]);



  // Handle input change for create form
  const handleCreateInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setCreateFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  // Handle input change for update form
  const handleUpdateInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setUpdateFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  // Handle input change for profile form
  const handleProfileInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setProfileFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Reset forms
  const resetForms = () => {
    setCreateFormData({
      username: '',
      email: '',
      password: '',
      password_confirm: '',
      first_name: '',
      last_name: '',
      role: 'user',
      is_active: true
    });

    setUpdateFormData({
      username: '',
      email: '',
      first_name: '',
      last_name: '',
      role: 'user',
      is_active: true
    });

    setProfileFormData({
      bio: '',
      location: '',
      expertise: '',
      website: '',
      github: '',
      linkedin: '',
      twitter: ''
    });

    setFormError(null);
    setFormSuccess(null);
  };

  // Open create modal
  const openCreateModal = () => {
    resetForms();
    setIsCreateModalOpen(true);
  };

  // Handle edit user
  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    setUpdateFormData({
      username: user.username,
      email: user.email,
      first_name: user.first_name,
      last_name: user.last_name,
      role: getUserRole(user),
      is_active: user.is_active !== false
    });
    setIsEditModalOpen(true);
  };

  // Handle view user profile
  const handleViewProfile = (user: User) => {
    console.log('Selected user for profile view:', user);
    console.log('User forum stats:', user.forum_stats);
    setSelectedUser(user);
    setIsViewProfileModalOpen(true);
  };

  // Handle edit profile
  const handleEditProfile = (user: User) => {
    setSelectedUser(user);
    setProfileFormData({
      bio: user.profile.bio,
      location: user.profile.location,
      expertise: user.profile.expertise,
      website: user.profile.website,
      github: user.profile.github,
      linkedin: user.profile.linkedin,
      twitter: user.profile.twitter
    });
    setIsProfileModalOpen(true);
  };

  // Handle delete user
  const handleDeleteUser = (user: User) => {
    setSelectedUser(user);
    setIsDeleteModalOpen(true);
  };

  // Create new user
  const submitCreateUser = async () => {
    setFormSubmitting(true);
    setFormError(null);
    setFormSuccess(null);

    try {
      // Validate form
      if (!createFormData.username || !createFormData.email || !createFormData.password || !createFormData.password_confirm) {
        setFormError(t("admin.please.fill.in", "Please fill in all required fields"));
        setFormSubmitting(false);
        return;
      }

      if (createFormData.password !== createFormData.password_confirm) {
        setFormError(t("admin.passwords.do.not", "Passwords do not match"));
        setFormSubmitting(false);
        return;
      }

      // Create user data
      const userData = {
        username: createFormData.username,
        email: createFormData.email,
        password: createFormData.password,
        password_confirm: createFormData.password_confirm,
        first_name: createFormData.first_name,
        last_name: createFormData.last_name,
        role: createFormData.role,
        is_active: createFormData.is_active
      };

      console.log('🎯 FORM SUBMIT - Creating user with data:', userData);

      // Dispatch create user action
      const resultAction = await dispatch(createUser(userData));
      console.log('🎯 FORM SUBMIT - Create user result:', resultAction);

      if (createUser.fulfilled.match(resultAction)) {
        // Show success message
        setFormSuccess(t("admin.userActions.created.successfully", "User created successfully!"));

        // Reset to page 1 to show the new user (which will be at the top due to sorting)
        setCurrentPage(1);
        setSearchTerm(''); // Clear search to ensure new user is visible

        // Force refresh the users list after a short delay
        setTimeout(() => {
          console.log('🔄 FORCE REFRESH after create');
          dispatch(fetchUsers());
        }, 500);

        // Close modal after a delay
        setTimeout(() => {
          setIsCreateModalOpen(false);
          resetForms();
        }, 1500);
      } else {
        // Show error message
        if (resultAction.payload) {
          setFormError(resultAction.payload as string);
        } else {
          setFormError(t("admin.failed.to.create", "Failed to create user. Please try again."));
        }
      }
    } catch (error) {
        console.error('Error creating user:', error);
        setFormError(t("admin.failed.to.create", "Failed to create user. Please try again."));
      } finally {
        setFormSubmitting(false);
      }
    };

  // Update user
  const submitUpdateUser = async () => {
    if (!selectedUser) return;

    setFormSubmitting(true);
    setFormError(null);
    setFormSuccess(null);

    try {
      // Validate form
      if (!updateFormData.username || !updateFormData.email) {
        setFormError(t("admin.username.and.email", "Username and email are required"));
        setFormSubmitting(false);
        return;
      }

      // Create update data
      const userData: UserUpdateData = {
        username: updateFormData.username,
        email: updateFormData.email,
        first_name: updateFormData.first_name,
        last_name: updateFormData.last_name,
        role: updateFormData.role,
        is_active: updateFormData.is_active
      };

      // Dispatch update user action
      const resultAction = await dispatch(updateUser({
        userId: selectedUser.id,
        userData
      }));

      if (updateUser.fulfilled.match(resultAction)) {
        // Show success message
        setFormSuccess(t("admin.userActions.updated.successfully", "User updated successfully!"));

        // Close modal after a delay
        setTimeout(() => {
          setIsEditModalOpen(false);
          setSelectedUser(null);
          resetForms();
        }, 1500);
      } else {
        // Show error message
        if (resultAction.payload) {
          setFormError(resultAction.payload as string);
        } else {
          setFormError(t("admin.failed.to.update", "Failed to update user. Please try again."));
        }
      }
    } catch (error) {
        console.error('Error updating user:', error);
        setFormError(t("admin.failed.to.update", "Failed to update user. Please try again."));
      } finally {
        setFormSubmitting(false);
      }
    };

  // Update profile
  const submitUpdateProfile = async () => {
    if (!selectedUser) return;

    setFormSubmitting(true);
    setFormError(null);
    setFormSuccess(null);

    try {
      // Create profile data
      const profileData: ProfileUpdateData = {
        bio: profileFormData.bio,
        location: profileFormData.location,
        expertise: profileFormData.expertise,
        website: profileFormData.website,
        github: profileFormData.github,
        linkedin: profileFormData.linkedin,
        twitter: profileFormData.twitter
      };

      // Dispatch update user action with profile data
      const resultAction = await dispatch(updateUser({
        userId: selectedUser.id,
        profileData
      }));

      if (updateUser.fulfilled.match(resultAction)) {
        // Show success message
        setFormSuccess(t("admin.profile.updated.successfully", "Profile updated successfully!"));

        // Close modal after a delay
        setTimeout(() => {
          setIsProfileModalOpen(false);
          setSelectedUser(null);
          resetForms();
        }, 1500);
      } else {
        // Show error message
        if (resultAction.payload) {
          setFormError(resultAction.payload as string);
        } else {
          setFormError(t("admin.failed.to.update", "Failed to update profile. Please try again."));
        }
      }
    } catch (error) {
        console.error('Error updating profile:', error);
        setFormError(t("admin.failed.to.update", "Failed to update profile. Please try again."));
      } finally {
        setFormSubmitting(false);
      }
    };

  // Confirm delete user
  const confirmDeleteUser = async () => {
    if (selectedUser) {
      setFormSubmitting(true);
      try {
        console.log('🎯 DELETE FORM SUBMIT - Deleting user:', selectedUser.id, selectedUser.username);
        // Dispatch delete user action
        const resultAction = await dispatch(deleteUser(selectedUser.id));
        console.log('🎯 DELETE FORM SUBMIT - Delete result:', resultAction);

        if (deleteUser.fulfilled.match(resultAction)) {
          setIsDeleteModalOpen(false);
          setSelectedUser(null);

          // Force refresh the users list after a short delay
          setTimeout(() => {
            console.log('🔄 FORCE REFRESH after delete');
            dispatch(fetchUsers());
          }, 500);
        } else {
          // Show error message
          if (resultAction.payload) {
            setFormError(resultAction.payload as string);
          } else {
            setFormError(t("admin.failed.to.delete", "Failed to delete user. Please try again."));
          }
        }
      } catch (error) {
        console.error('Error deleting user:', error);
        setFormError(t("admin.failed.to.delete", "Failed to delete user. Please try again."));
      } finally {
        setFormSubmitting(false);
      }
    }
  };

  // Prevent default form submission
  const preventFormSubmission = (e: React.FormEvent) => {
    e.preventDefault();
    e.stopPropagation();
    return false;
  };

  // Show authentication error if not authenticated or not admin
  if (!isAuthenticated) {
    return (
      <DashboardLayout currentPage="users">
        <div className="flex flex-col items-center justify-center min-h-96 text-center">
          <AlertCircle size={64} className="text-red-400 mb-4" />
          <h2 className="text-xl font-semibold text-red-400 mb-2">Authentication Required</h2>
          <p className="text-gray-400">Please log in to access the admin panel.</p>
        </div>
      </DashboardLayout>
    );
  }

  if (!currentUser?.is_admin) {
    return (
      <DashboardLayout currentPage="users">
        <div className="flex flex-col items-center justify-center min-h-96 text-center">
          <Shield size={64} className="text-red-400 mb-4" />
          <h2 className="text-xl font-semibold text-red-400 mb-2">Access Denied</h2>
          <p className="text-gray-400">You need administrator privileges to access this page.</p>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout currentPage="users">
      <div onSubmit={preventFormSubmission} className={`mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
        <div>
          <h1 className="text-2xl font-bold text-white">{t("admin.users.management", "Users Management")}</h1>
          <div className="text-gray-400 mt-1">{t("admin.manage.your.community", "Manage your community members")}</div>
          <div className="text-yellow-400 text-sm mt-2">
            🚧 Demo: Roles are temporarily assigned based on user ID for demonstration purposes
          </div>
        </div>
        <div className="flex space-x-2">
          <button
            type="button"
            onClick={() => {
              console.log('🔄 MANUAL FORCE REFRESH');
              dispatch(fetchUsers());
            }}
            className={`btn-primary px-4 py-2 rounded-lg font-medium transition-all duration-300 flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <RefreshCw size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
            Force Refresh
          </button>



          <button
            type="button"
            onClick={openCreateModal}
            className={`btn-primary px-4 py-2 rounded-lg font-medium hover:shadow-glow transition-all duration-300 flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <UserPlus size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
            {t("admin.add.new.user", "Add New User")}
          </button>
        </div>
      </div>

      <div className="card overflow-hidden">
        <div className="p-4 border-b theme-border-primary">
          <div className={`flex flex-col md:flex-row md:items-center gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className={`relative flex-grow ${isRTL ? "flex-row-reverse" : ""}`}>
              <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder={t("admin.search.users", "Search users...")}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input w-full pl-10 pr-4 py-2 rounded-lg"
              />
            </div>
            <button
              type="button"
              onClick={() => dispatch(fetchUsers())}
              className={`btn-primary px-3 py-2 rounded-lg text-sm flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}
              disabled={isLoading}
            >
              <RefreshCw size={14} className={`mr-1 ${isLoading ? 'animate-spin' : ''}`} />
              {t("admin.refresh", "Refresh")}
            </button>
          </div>
        </div>

        {error && (
          <div className="p-6">
            <div className="bg-red-900/20 border border-red-500 rounded-lg p-4 flex items-center justify-between">
              <div className="flex items-center">
                <AlertCircle size={20} className="text-red-400 mr-3" />
                <div>
                  <h3 className="text-red-400 font-medium">Error Loading Users</h3>
                  <p className="text-red-300 text-sm mt-1">{error}</p>
                </div>
              </div>
              <button
                type="button"
                onClick={() => dispatch(fetchUsers())}
                className="px-4 py-2 bg-red-600 hover:bg-red-500 text-white rounded-lg text-sm transition-colors"
              >
                {t("admin.try.again", "Try Again")}
              </button>
            </div>
          </div>
        )}

        {isLoading ? (
          <div className={`flex flex-col justify-center items-center py-20 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className="w-12 h-12 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mb-6"></div>
            <div className="text-gray-400">{t("admin.loading.users", "Loading users...")}</div>
          </div>
        ) : usersArray.length === 0 && !error ? (
          <div className="flex flex-col items-center justify-center py-20 text-center">
            <UserIcon size={64} className="text-gray-400 mb-4" />
            <h3 className="text-xl font-semibold text-gray-300 mb-2">No Users Found</h3>
            <p className="text-gray-400 mb-6">
              {searchTerm ? 'No users match your search criteria.' : 'No users have been created yet.'}
            </p>
            {!searchTerm && (
              <button
                type="button"
                onClick={openCreateModal}
                className="btn-primary px-6 py-3 rounded-lg font-medium hover:shadow-glow transition-all duration-300 flex items-center"
              >
                <UserPlus size={20} className="mr-2" />
                Create First User
              </button>
            )}
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y theme-border-primary">
                <thead className="glass-light">
                  <tr>
                    <th scope="col" className={`px-6 py-3 text-xs font-medium theme-text-secondary uppercase tracking-wider ${isRTL ? "text-right" : "text-left"}`}>
                      {t("admin.user", "User")}
                    </th>
                    <th scope="col" className={`px-6 py-3 text-xs font-medium theme-text-secondary uppercase tracking-wider ${isRTL ? "text-right" : "text-left"}`}>
                      {t("admin.role", "Role")}
                    </th>
                    <th scope="col" className={`px-6 py-3 text-xs font-medium theme-text-secondary uppercase tracking-wider ${isRTL ? "text-right" : "text-left"}`}>
                      {t("admin.location", "Location")}
                    </th>
                    <th scope="col" className={`px-6 py-3 text-xs font-medium theme-text-secondary uppercase tracking-wider ${isRTL ? "text-right" : "text-left"}`}>
                      {t("admin.expertise", "Expertise")}
                    </th>
                    <th scope="col" className={`px-6 py-3 text-xs font-medium theme-text-secondary uppercase tracking-wider ${isRTL ? "text-right" : "text-left"}`}>
                      {t("admin.status", "Status")}
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium theme-text-secondary uppercase tracking-wider">
                      {t("admin.actions", "Actions")}
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y theme-border-primary">
                  {currentUsers.map((user) => (
                    <tr key={user.id} className="hover:glass-light">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                          <div className={`flex-shrink-0 h-10 w-10 rounded-full glass-light flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}>
                            <span className="theme-text-primary font-medium">{(user.username || 'U').charAt(0).toUpperCase()}</span>
                          </div>
                          <div className={`ml-4 ${isRTL ? "space-x-reverse" : ""}`}>
                            <div className="text-sm font-medium theme-text-primary">{(user.first_name || '') + ' ' + (user.last_name || '')}</div>
                            <div className="text-sm theme-text-secondary">@{user.username || 'unknown'}</div>
                            <div className="text-sm theme-text-secondary">{user.email || 'no-email'}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <Shield size={16} className={`mr-2 ${getRoleColor(getUserRole(user))}`} />
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getRoleBadgeStyle(getUserRole(user))}`}>
                            {getRoleDisplayName(user)}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm theme-text-primary">{(user.profile?.location || t("admin.na", "N/A"))}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm theme-text-primary">{(user.profile?.expertise || t("admin.na", "N/A"))}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          (user.is_active !== false && user.is_active !== undefined)
                            ? 'bg-green-600/20 text-green-400'
                            : 'bg-red-600/20 text-red-400'
                        } ${isRTL ? "flex-row-reverse" : ""}`}>
                          {(user.is_active !== false && user.is_active !== undefined) ? t("admin.active", "Active") : t("admin.inactive", "Inactive")}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          type="button"
                          onClick={() => handleViewProfile(user)}
                          className={`text-green-400 hover:text-green-300 mr-3 ${isRTL ? "space-x-reverse" : ""}`}
                          title={t("admin.view.profile", "View Profile")}
                        >
                          <Eye size={18} />
                        </button>
                        <button
                          type="button"
                          onClick={() => handleEditUser(user)}
                          className={`text-indigo-400 hover:text-indigo-300 mr-3 ${isRTL ? "space-x-reverse" : ""}`}
                          title={t("admin.edit.user", "Edit User")}
                        >
                          <Edit size={18} />
                        </button>
                        <button
                          type="button"
                          onClick={() => handleEditProfile(user)}
                          className={`text-blue-400 hover:text-blue-300 mr-3 ${isRTL ? "space-x-reverse" : ""}`}
                          title={t("admin.edit.profile", "Edit Profile")}
                        >
                          <Settings size={18} />
                        </button>
                        <button
                          type="button"
                          onClick={() => handleDeleteUser(user)}
                          className="text-red-400 hover:text-red-300"
                          title={t("admin.delete.user", "Delete User")}
                        >
                          <Trash2 size={18} />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            <div className={`px-6 py-4 flex items-center justify-between border-t theme-border-primary ${isRTL ? "flex-row-reverse" : ""}`}>
              <div className="text-sm theme-text-secondary">
                Showing <span className="font-medium">{indexOfFirstUser + 1}</span> to{' '}
                <span className="font-medium">
                  {indexOfLastUser > filteredUsers.length ? filteredUsers.length : indexOfLastUser}
                </span>{' '}
                of <span className="font-medium">{filteredUsers.length}</span> users
              </div>

              {totalPages > 1 && (
                <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                  <button
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                    className={`btn-primary px-3 py-2 rounded-md flex items-center ${
                      currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-glow'
                    }`}
                  >
                    <ChevronLeft size={16} />
                    <span className="ml-1">{t("admin.previous", "Previous")}</span>
                  </button>

                  {/* Page numbers */}
                  <div className="flex items-center space-x-1">
                    {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                      let pageNum;
                      if (totalPages <= 5) {
                        pageNum = i + 1;
                      } else if (currentPage <= 3) {
                        pageNum = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        pageNum = totalPages - 4 + i;
                      } else {
                        pageNum = currentPage - 2 + i;
                      }

                      return (
                        <button
                          key={pageNum}
                          onClick={() => setCurrentPage(pageNum)}
                          className={`px-3 py-2 rounded-md text-sm transition-all duration-200 ${
                            currentPage === pageNum
                              ? 'bg-purple-600 text-white shadow-glow'
                              : 'glass-light theme-text-secondary hover:theme-text-primary hover:shadow-glow'
                          }`}
                        >
                          {pageNum}
                        </button>
                      );
                    })}
                  </div>

                  <button
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                    className={`btn-primary px-3 py-2 rounded-md flex items-center ${
                      currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-glow'
                    }`}
                  >
                    <span className="mr-1">{t("admin.next", "Next")}</span>
                    <ChevronRight size={16} />
                  </button>
                </div>
              )}
            </div>
          </>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {isDeleteModalOpen && selectedUser && (
        <div className={`fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70 backdrop-blur-sm ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="modal p-6 max-w-md w-full">
            <h3 className="text-xl font-semibold mb-4 theme-text-primary">{t("admin.confirm.deletion", "Confirm Deletion")}</h3>
            <div className="theme-text-secondary mb-6">
              Are you sure you want to delete the user <span className="font-medium theme-text-primary">{selectedUser.username}</span>? This action cannot be undone.
            </div>

            {formError && (
              <div className="mb-4 p-3 glass-light border border-red-500 rounded-lg text-red-200">
                {formError}
              </div>
            )}

            <div className={`flex justify-end space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                type="button"
                onClick={() => setIsDeleteModalOpen(false)}
                className="btn-primary px-4 py-2 rounded-lg"
                disabled={formSubmitting}
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={confirmDeleteUser}
                className={`px-4 py-2 bg-red-600 hover:bg-red-500 rounded-lg flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                disabled={formSubmitting}
              >
                {formSubmitting ? (
                  <>
                    <div className={`w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 ${isRTL ? "space-x-reverse" : ""}`}></div>
                    Deleting...
                  </>
                ) : (
                  <>
                    <Trash2 size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                    Delete
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Create User Modal */}
      {isCreateModalOpen && (
        <div className={`fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70 backdrop-blur-sm overflow-y-auto ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="modal p-6 max-w-2xl w-full my-8">
            <div className={`flex justify-between items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
              <h3 className="text-xl font-semibold theme-text-primary">{t("admin.create.new.user", "Create New User")}</h3>
              <button
                type="button"
                onClick={() => setIsCreateModalOpen(false)}
                className="theme-text-secondary hover:theme-text-primary transition-colors"
              >
                <X size={24} />
              </button>
            </div>

            {formError && (
              <div className="mb-4 p-3 glass-light border border-red-500 rounded-lg text-red-200">
                {formError}
              </div>
            )}

            {formSuccess && (
              <div className="mb-4 p-3 glass-light border border-green-500 rounded-lg text-green-200">
                {formSuccess}
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1 theme-text-primary">Username *</label>
                <input
                  type="text"
                  name="username"
                  value={createFormData.username}
                  onChange={handleCreateInputChange}
                  className="input w-full"
                  placeholder={t("admin.enter.username", "Enter username")}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1 theme-text-primary">Email *</label>
                <input
                  type="email"
                  name="email"
                  value={createFormData.email}
                  onChange={handleCreateInputChange}
                  className="input w-full"
                  placeholder={t("admin.enter.email", "Enter email")}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1 theme-text-primary">Password *</label>
                <input
                  type="password"
                  name="password"
                  value={createFormData.password}
                  onChange={handleCreateInputChange}
                  className="input w-full"
                  placeholder={t("admin.enter.password", "Enter password")}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1 theme-text-primary">Confirm Password *</label>
                <input
                  type="password"
                  name="password_confirm"
                  value={createFormData.password_confirm}
                  onChange={handleCreateInputChange}
                  className="input w-full"
                  placeholder={t("admin.confirm.password", "Confirm password")}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1 theme-text-primary">{t("admin.first.name", "First Name")}</label>
                <input
                  type="text"
                  name="first_name"
                  value={createFormData.first_name}
                  onChange={handleCreateInputChange}
                  className="input w-full"
                  placeholder={t("admin.enter.first.name", "Enter first name")}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1 theme-text-primary">{t("admin.last.name", "Last Name")}</label>
                <input
                  type="text"
                  name="last_name"
                  value={createFormData.last_name}
                  onChange={handleCreateInputChange}
                  className="input w-full"
                  placeholder={t("admin.enter.last.name", "Enter last name")}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1 theme-text-primary">{t("admin.role", "Role")}</label>
                <select
                  name="role"
                  value={createFormData.role}
                  onChange={handleCreateInputChange}
                  className="input w-full"
                >
                  {USER_ROLES.map(role => (
                    <option key={role.value} value={role.value}>
                      {role.label} - {role.description}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <div className={`flex items-center mt-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <input
                    type="checkbox"
                    id="is_active"
                    name="is_active"
                    checked={createFormData.is_active}
                    onChange={handleCreateInputChange}
                    className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                  />
                  <label htmlFor="is_active" className={`ml-2 block text-sm ${isRTL ? "space-x-reverse" : ""}`}>
                    {t("admin.active.user", "Active User")}
                  </label>
                </div>
              </div>
            </div>

            <div className={`flex justify-end space-x-3 mt-6 ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                type="button"
                onClick={() => setIsCreateModalOpen(false)}
                className="btn-primary px-4 py-2 rounded-lg"
                disabled={formSubmitting}
              >
                {t("admin.cancel", "Cancel")}
              </button>
              <button
                type="button"
                onClick={submitCreateUser}
                className={`btn-primary px-4 py-2 rounded-lg font-medium hover:shadow-glow transition-all duration-300 flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}
                disabled={formSubmitting}
              >
                {formSubmitting ? (
                  <>
                    <div className={`w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 ${isRTL ? "space-x-reverse" : ""}`}></div>
                    Creating...
                  </>
                ) : (
                  <>
                    <UserPlus size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                    Create User
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Edit User Modal */}
      {isEditModalOpen && selectedUser && (
        <div className={`fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70 backdrop-blur-sm overflow-y-auto ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="bg-gradient-to-b from-indigo-900/90 to-purple-900/90 rounded-xl p-6 max-w-2xl w-full my-8">
            <div className={`flex justify-between items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
              <h3 className="text-xl font-semibold">{t("admin.edit.user", "Edit User")}</h3>
              <button
                type="button"
                onClick={() => setIsEditModalOpen(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <X size={24} />
              </button>
            </div>

            {formError && (
              <div className="mb-4 p-3 bg-red-900/50 border border-red-500 rounded-lg text-red-200">
                {formError}
              </div>
            )}

            {formSuccess && (
              <div className="mb-4 p-3 bg-green-900/50 border border-green-500 rounded-lg text-green-200">
                {formSuccess}
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">Username *</label>
                <input
                  type="text"
                  name="username"
                  value={updateFormData.username}
                  onChange={handleUpdateInputChange}
                  className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  placeholder={t("admin.enter.username", "Enter username")}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Email *</label>
                <input
                  type="email"
                  name="email"
                  value={updateFormData.email}
                  onChange={handleUpdateInputChange}
                  className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  placeholder={t("admin.enter.email", "Enter email")}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">{t("admin.first.name", "First Name")}</label>
                <input
                  type="text"
                  name="first_name"
                  value={updateFormData.first_name}
                  onChange={handleUpdateInputChange}
                  className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  placeholder={t("admin.enter.first.name", "Enter first name")}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">{t("admin.last.name", "Last Name")}</label>
                <input
                  type="text"
                  name="last_name"
                  value={updateFormData.last_name}
                  onChange={handleUpdateInputChange}
                  className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  placeholder={t("admin.enter.last.name", "Enter last name")}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">{t("admin.role", "Role")}</label>
                <select
                  name="role"
                  value={updateFormData.role}
                  onChange={handleUpdateInputChange}
                  className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                >
                  {USER_ROLES.map(role => (
                    <option key={role.value} value={role.value}>
                      {role.label} - {role.description}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <div className={`flex items-center mt-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <input
                    type="checkbox"
                    id="update_is_active"
                    name="is_active"
                    checked={updateFormData.is_active}
                    onChange={handleUpdateInputChange}
                    className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                  />
                  <label htmlFor="update_is_active" className={`ml-2 block text-sm ${isRTL ? "space-x-reverse" : ""}`}>
                    {t("admin.active.user", "Active User")}
                  </label>
                </div>
              </div>
            </div>

            <div className={`flex justify-end space-x-3 mt-6 ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                type="button"
                onClick={() => setIsEditModalOpen(false)}
                className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg"
                disabled={formSubmitting}
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={submitUpdateUser}
                className={`px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300 flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}
                disabled={formSubmitting}
              >
                {formSubmitting ? (
                  <>
                    <div className={`w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 ${isRTL ? "space-x-reverse" : ""}`}></div>
                    Updating...
                  </>
                ) : (
                  <>
                    <Check size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                    Update User
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Profile Modal */}
      {isProfileModalOpen && selectedUser && (
        <div className={`fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70 backdrop-blur-sm overflow-y-auto ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="bg-gradient-to-b from-indigo-900/90 to-purple-900/90 rounded-xl p-6 max-w-2xl w-full my-8">
            <div className={`flex justify-between items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
              <h3 className="text-xl font-semibold">Edit Profile: {selectedUser.username}</h3>
              <button
                type="button"
                onClick={() => setIsProfileModalOpen(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <X size={24} />
              </button>
            </div>

            {formError && (
              <div className="mb-4 p-3 bg-red-900/50 border border-red-500 rounded-lg text-red-200">
                {formError}
              </div>
            )}

            {formSuccess && (
              <div className="mb-4 p-3 bg-green-900/50 border border-green-500 rounded-lg text-green-200">
                {formSuccess}
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <label className="block text-sm font-medium mb-1">{t("admin.bio", "Bio")}</label>
                <textarea
                  name="bio"
                  value={profileFormData.bio}
                  onChange={handleProfileInputChange}
                  className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white h-24"
                  placeholder={t("admin.enter.bio", "Enter bio")}
                ></textarea>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">{t("admin.location", "Location")}</label>
                <input
                  type="text"
                  name="location"
                  value={profileFormData.location}
                  onChange={handleProfileInputChange}
                  className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  placeholder={t("admin.enter.location", "Enter location")}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">{t("admin.expertise", "Expertise")}</label>
                <input
                  type="text"
                  name="expertise"
                  value={profileFormData.expertise}
                  onChange={handleProfileInputChange}
                  className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  placeholder={t("admin.enter.expertise", "Enter expertise")}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">{t("admin.website", "Website")}</label>
                <input
                  type="url"
                  name="website"
                  value={profileFormData.website}
                  onChange={handleProfileInputChange}
                  className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  placeholder={t("admin.enter.website.url", "Enter website URL")}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">{t("admin.github", "GitHub")}</label>
                <input
                  type="text"
                  name="github"
                  value={profileFormData.github}
                  onChange={handleProfileInputChange}
                  className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  placeholder={t("admin.enter.github.username", "Enter GitHub username")}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">{t("admin.linkedin", "LinkedIn")}</label>
                <input
                  type="text"
                  name="linkedin"
                  value={profileFormData.linkedin}
                  onChange={handleProfileInputChange}
                  className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  placeholder={t("admin.enter.linkedin.profile", "Enter LinkedIn profile")}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">{t("admin.twitter", "Twitter")}</label>
                <input
                  type="text"
                  name="twitter"
                  value={profileFormData.twitter}
                  onChange={handleProfileInputChange}
                  className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  placeholder={t("admin.enter.twitter.handle", "Enter Twitter handle")}
                />
              </div>
            </div>

            <div className={`flex justify-end space-x-3 mt-6 ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                type="button"
                onClick={() => setIsProfileModalOpen(false)}
                className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg"
                disabled={formSubmitting}
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={submitUpdateProfile}
                className={`px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300 flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}
                disabled={formSubmitting}
              >
                {formSubmitting ? (
                  <>
                    <div className={`w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 ${isRTL ? "space-x-reverse" : ""}`}></div>
                    Updating...
                  </>
                ) : (
                  <>
                    <Check size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                    Update Profile
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* View Profile Modal */}
      {isViewProfileModalOpen && selectedUser && (
        <div className={`fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70 backdrop-blur-sm overflow-y-auto ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="modal p-6 max-w-4xl w-full my-8">
            <div className={`flex justify-between items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
              <h3 className="text-xl font-semibold theme-text-primary">
                {t("admin.user.profile", "User Profile")}: {selectedUser.username}
              </h3>
              <button
                type="button"
                onClick={() => setIsViewProfileModalOpen(false)}
                className="theme-text-secondary hover:theme-text-primary transition-colors"
              >
                <X size={24} />
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Basic Information */}
              <div className="glass-light p-4 rounded-lg">
                <h4 className="text-lg font-semibold mb-4 theme-text-primary flex items-center">
                  <UserIcon size={20} className="mr-2" />
                  {t("admin.basic.info", "Basic Information")}
                </h4>
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium theme-text-secondary">Username:</label>
                    <p className="theme-text-primary">{String(selectedUser.username || '')}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium theme-text-secondary">Email:</label>
                    <p className="theme-text-primary">{String(selectedUser.email || '')}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium theme-text-secondary">Full Name:</label>
                    <p className="theme-text-primary">
                      {String(selectedUser.first_name || '') + ' ' + String(selectedUser.last_name || '')}
                      {(!selectedUser.first_name && !selectedUser.last_name) && (
                        <span className="theme-text-secondary italic">Not provided</span>
                      )}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium theme-text-secondary">Role:</label>
                    <div className="flex items-center mt-1">
                      <Shield size={16} className={`mr-2 ${getRoleColor(getUserRole(selectedUser))}`} />
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getRoleBadgeStyle(getUserRole(selectedUser))}`}>
                        {getRoleDisplayName(selectedUser)}
                      </span>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium theme-text-secondary">Status:</label>
                    <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                      (selectedUser.is_active !== false && selectedUser.is_active !== undefined)
                        ? 'bg-green-600/20 text-green-400'
                        : 'bg-red-600/20 text-red-400'
                    }`}>
                      {(selectedUser.is_active !== false && selectedUser.is_active !== undefined)
                        ? t("admin.active", "Active")
                        : t("admin.inactive", "Inactive")
                      }
                    </span>
                  </div>
                  <div>
                    <label className="text-sm font-medium theme-text-secondary">User ID:</label>
                    <p className="theme-text-primary">#{String(selectedUser.id || '')}</p>
                  </div>
                </div>
              </div>

              {/* Profile Information */}
              <div className="glass-light p-4 rounded-lg">
                <h4 className="text-lg font-semibold mb-4 theme-text-primary flex items-center">
                  <Settings size={20} className="mr-2" />
                  {t("admin.profile.info", "Profile Information")}
                </h4>
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium theme-text-secondary">Bio:</label>
                    <p className="theme-text-primary">
                      {(typeof selectedUser.profile?.bio === 'string' ? selectedUser.profile.bio : '') || (
                        <span className="theme-text-secondary italic">No bio provided</span>
                      )}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium theme-text-secondary">Location:</label>
                    <p className="theme-text-primary">
                      {(typeof selectedUser.profile?.location === 'string' ? selectedUser.profile.location : '') || (
                        <span className="theme-text-secondary italic">Not specified</span>
                      )}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium theme-text-secondary">Expertise:</label>
                    <p className="theme-text-primary">
                      {(typeof selectedUser.profile?.expertise === 'string' ? selectedUser.profile.expertise : '') || (
                        <span className="theme-text-secondary italic">Not specified</span>
                      )}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium theme-text-secondary">Website:</label>
                    <p className="theme-text-primary">
                      {(typeof selectedUser.profile?.website === 'string' && selectedUser.profile.website) ? (
                        <a
                          href={selectedUser.profile.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-400 hover:text-blue-300 underline"
                        >
                          {selectedUser.profile.website}
                        </a>
                      ) : (
                        <span className="theme-text-secondary italic">Not provided</span>
                      )}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium theme-text-secondary">GitHub:</label>
                    <p className="theme-text-primary">
                      {(typeof selectedUser.profile?.github === 'string' && selectedUser.profile.github) ? (
                        <a
                          href={`https://github.com/${selectedUser.profile.github}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-400 hover:text-blue-300 underline"
                        >
                          @{selectedUser.profile.github}
                        </a>
                      ) : (
                        <span className="theme-text-secondary italic">Not provided</span>
                      )}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium theme-text-secondary">LinkedIn:</label>
                    <p className="theme-text-primary">
                      {(typeof selectedUser.profile?.linkedin === 'string' && selectedUser.profile.linkedin) ? (
                        <a
                          href={`https://linkedin.com/in/${selectedUser.profile.linkedin}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-400 hover:text-blue-300 underline"
                        >
                          @{selectedUser.profile.linkedin}
                        </a>
                      ) : (
                        <span className="theme-text-secondary italic">Not provided</span>
                      )}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium theme-text-secondary">Twitter:</label>
                    <p className="theme-text-primary">
                      {(typeof selectedUser.profile?.twitter === 'string' && selectedUser.profile.twitter) ? (
                        <a
                          href={`https://twitter.com/${selectedUser.profile.twitter}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-400 hover:text-blue-300 underline"
                        >
                          @{selectedUser.profile.twitter}
                        </a>
                      ) : (
                        <span className="theme-text-secondary italic">Not provided</span>
                      )}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Forum Statistics (if available) */}
            {selectedUser.forum_stats && (
              <div className="glass-light p-4 rounded-lg mt-6">
                <h4 className="text-lg font-semibold mb-4 theme-text-primary">
                  {t("admin.forum.stats", "Forum Statistics")}
                </h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold theme-text-primary">
                      {String(selectedUser.forum_stats?.posts_created || 0)}
                    </p>
                    <p className="text-sm theme-text-secondary">Posts Created</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold theme-text-primary">
                      {String(selectedUser.forum_stats?.likes_received || 0)}
                    </p>
                    <p className="text-sm theme-text-secondary">Likes Received</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold theme-text-primary">
                      {String(selectedUser.forum_stats?.points || 0)}
                    </p>
                    <p className="text-sm theme-text-secondary">Points</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold theme-text-primary">
                      {String(selectedUser.forum_stats?.level || 0)}
                    </p>
                    <p className="text-sm theme-text-secondary">Level</p>
                  </div>
                </div>
              </div>
            )}

            <div className={`flex justify-end space-x-3 mt-6 ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                type="button"
                onClick={() => setIsViewProfileModalOpen(false)}
                className="btn-primary px-4 py-2 rounded-lg"
              >
                {t("admin.close", "Close")}
              </button>
              <button
                type="button"
                onClick={() => {
                  setIsViewProfileModalOpen(false);
                  handleEditUser(selectedUser);
                }}
                className="px-4 py-2 bg-indigo-600 hover:bg-indigo-500 rounded-lg theme-text-primary"
              >
                {t("admin.edit.user", "Edit User")}
              </button>
              <button
                type="button"
                onClick={() => {
                  setIsViewProfileModalOpen(false);
                  handleEditProfile(selectedUser);
                }}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-500 rounded-lg theme-text-primary"
              >
                {t("admin.edit.profile", "Edit Profile")}
              </button>
            </div>
          </div>
        </div>
      )}
    </DashboardLayout>
  );
};

export default UsersManagement;
