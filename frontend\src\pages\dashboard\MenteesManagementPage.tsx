import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import Button from '../../components/ui/Button';
import { Badge } from '../../components/ui/badge';
import { Input } from '../../components/ui/input';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { User, MessageSquare, Calendar, TrendingUp, Search, Filter, Plus, Mail, Phone } from 'lucide-react';

interface Mentee {
  id: string;
  name: string;
  email: string;
  phone?: string;
  avatar?: string;
  businessIdea: string;
  industry: string;
  stage: 'Idea' | 'MVP' | 'Growth' | 'Scale';
  joinDate: string;
  lastSession: string;
  nextSession?: string;
  totalSessions: number;
  progress: number; // percentage
  goals: string[];
  challenges: string[];
  status: 'Active' | 'Inactive' | 'Completed';
  rating?: number;
  notes?: string;
}

const MenteesManagementPage: React.FC = () => {
  const [mentees, setMentees] = useState<Mentee[]>([]);
  const [filteredMentees, setFilteredMentees] = useState<Mentee[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [stageFilter, setStageFilter] = useState('all');
  const [activeTab, setActiveTab] = useState('overview');

  // Mock data - replace with actual API call
  useEffect(() => {
    const mockMentees: Mentee[] = [
      {
        id: '1',
        name: 'Alex Chen',
        email: '<EMAIL>',
        phone: '+****************',
        businessIdea: 'AI-Powered Learning Platform',
        industry: 'EdTech',
        stage: 'MVP',
        joinDate: '2023-09-15',
        lastSession: '2024-01-15',
        nextSession: '2024-01-22',
        totalSessions: 12,
        progress: 75,
        goals: ['Launch MVP', 'Acquire first 100 users', 'Raise seed funding'],
        challenges: ['User acquisition', 'Product-market fit'],
        status: 'Active',
        rating: 5,
        notes: 'Very motivated and making excellent progress. Strong technical background.'
      },
      {
        id: '2',
        name: 'Sarah Johnson',
        email: '<EMAIL>',
        businessIdea: 'Sustainable Fashion Marketplace',
        industry: 'E-commerce',
        stage: 'Growth',
        joinDate: '2023-06-20',
        lastSession: '2024-01-10',
        nextSession: '2024-01-25',
        totalSessions: 18,
        progress: 85,
        goals: ['Scale to 1000 sellers', 'International expansion', 'Series A funding'],
        challenges: ['Supply chain management', 'International logistics'],
        status: 'Active',
        rating: 4,
        notes: 'Great execution skills. Needs support with scaling operations.'
      },
      {
        id: '3',
        name: 'Michael Rodriguez',
        email: '<EMAIL>',
        businessIdea: 'HealthTech Monitoring Device',
        industry: 'Healthcare',
        stage: 'Idea',
        joinDate: '2023-12-01',
        lastSession: '2024-01-08',
        totalSessions: 4,
        progress: 30,
        goals: ['Validate concept', 'Build prototype', 'Find co-founder'],
        challenges: ['Technical expertise', 'Regulatory compliance'],
        status: 'Active',
        rating: 4,
        notes: 'Early stage but shows promise. Needs technical co-founder.'
      },
      {
        id: '4',
        name: 'Emily Davis',
        email: '<EMAIL>',
        businessIdea: 'FinTech Payment Solution',
        industry: 'Financial Services',
        stage: 'Scale',
        joinDate: '2023-03-10',
        lastSession: '2023-12-20',
        totalSessions: 25,
        progress: 95,
        goals: ['IPO preparation', 'Global expansion', 'Strategic partnerships'],
        challenges: ['Regulatory compliance', 'Competition'],
        status: 'Completed',
        rating: 5,
        notes: 'Successfully completed mentorship program. Ready for next phase.'
      }
    ];

    setTimeout(() => {
      setMentees(mockMentees);
      setFilteredMentees(mockMentees);
      setLoading(false);
    }, 1000);
  }, []);

  // Filter mentees based on search and filters
  useEffect(() => {
    let filtered = mentees;

    if (searchTerm) {
      filtered = filtered.filter(mentee => 
        mentee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        mentee.businessIdea.toLowerCase().includes(searchTerm.toLowerCase()) ||
        mentee.industry.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(mentee => mentee.status === statusFilter);
    }

    if (stageFilter !== 'all') {
      filtered = filtered.filter(mentee => mentee.stage === stageFilter);
    }

    setFilteredMentees(filtered);
  }, [mentees, searchTerm, statusFilter, stageFilter]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'bg-green-100 text-green-800';
      case 'Inactive': return 'bg-yellow-100 text-yellow-800';
      case 'Completed': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStageColor = (stage: string) => {
    switch (stage) {
      case 'Idea': return 'bg-purple-100 text-purple-800';
      case 'MVP': return 'bg-blue-100 text-blue-800';
      case 'Growth': return 'bg-green-100 text-green-800';
      case 'Scale': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <span key={i} className={`text-sm ${i < rating ? 'text-yellow-400' : 'text-gray-300'}`}>
        ★
      </span>
    ));
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-48 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Mentees Management</h1>
          <p className="text-gray-600 mt-1">Manage and track your mentees' progress</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Filter className="w-4 h-4 mr-2" />
            Advanced Filters
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700">
            <Plus className="w-4 h-4 mr-2" />
            Add Mentee
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Mentees</p>
                <p className="text-2xl font-bold">{mentees.length}</p>
              </div>
              <User className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Mentees</p>
                <p className="text-2xl font-bold text-green-600">
                  {mentees.filter(m => m.status === 'Active').length}
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Progress</p>
                <p className="text-2xl font-bold text-purple-600">
                  {Math.round(mentees.reduce((sum, m) => sum + m.progress, 0) / mentees.length)}%
                </p>
              </div>
              <Calendar className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Rating</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {mentees.filter(m => m.rating).length > 0 
                    ? (mentees.filter(m => m.rating).reduce((sum, m) => sum + (m.rating || 0), 0) / 
                       mentees.filter(m => m.rating).length).toFixed(1)
                    : 'N/A'
                  }
                </p>
              </div>
              <MessageSquare className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search mentees..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select 
              value={statusFilter} 
              onChange={(e) => setStatusFilter(e.target.value)}
              className="border rounded px-3 py-2"
            >
              <option value="all">All Status</option>
              <option value="Active">Active</option>
              <option value="Inactive">Inactive</option>
              <option value="Completed">Completed</option>
            </select>
            <select 
              value={stageFilter} 
              onChange={(e) => setStageFilter(e.target.value)}
              className="border rounded px-3 py-2"
            >
              <option value="all">All Stages</option>
              <option value="Idea">Idea</option>
              <option value="MVP">MVP</option>
              <option value="Growth">Growth</option>
              <option value="Scale">Scale</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Mentees Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="active">Active</TabsTrigger>
          <TabsTrigger value="completed">Completed</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredMentees.map((mentee) => (
              <Card key={mentee.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-start">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                        <User className="w-5 h-5 text-gray-400" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{mentee.name}</CardTitle>
                        <p className="text-sm text-gray-600">{mentee.industry}</p>
                      </div>
                    </div>
                    <Badge className={getStatusColor(mentee.status)}>
                      {mentee.status}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <h4 className="font-medium text-sm mb-1">Business Idea</h4>
                    <p className="text-sm text-gray-700">{mentee.businessIdea}</p>
                  </div>

                  <div className="flex items-center justify-between">
                    <Badge className={getStageColor(mentee.stage)}>
                      {mentee.stage}
                    </Badge>
                    <span className="text-sm text-gray-600">
                      {mentee.totalSessions} sessions
                    </span>
                  </div>

                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Progress</span>
                      <span>{mentee.progress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full" 
                        style={{ width: `${mentee.progress}%` }}
                      ></div>
                    </div>
                  </div>

                  {mentee.rating && (
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-600">Rating:</span>
                      <div className="flex">{renderStars(mentee.rating)}</div>
                    </div>
                  )}

                  <div className="text-sm text-gray-600">
                    <p>Joined: {formatDate(mentee.joinDate)}</p>
                    <p>Last session: {formatDate(mentee.lastSession)}</p>
                    {mentee.nextSession && (
                      <p>Next session: {formatDate(mentee.nextSession)}</p>
                    )}
                  </div>

                  {mentee.notes && (
                    <div className="bg-gray-50 p-2 rounded text-sm">
                      <strong>Notes:</strong> {mentee.notes}
                    </div>
                  )}

                  <div className="flex gap-2 pt-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      <MessageSquare className="w-4 h-4 mr-1" />
                      Message
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1">
                      <Calendar className="w-4 h-4 mr-1" />
                      Schedule
                    </Button>
                  </div>

                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      View Profile
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1">
                      Edit Notes
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredMentees.length === 0 && (
            <Card>
              <CardContent className="p-8 text-center">
                <User className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 mb-4">No mentees found matching your criteria.</p>
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Your First Mentee
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default MenteesManagementPage;
