from rest_framework import serializers
from .models import (
    <PERSON><PERSON>ealth, SuperAdminAuditLog, SystemConfiguration,
    SystemAlert, BackupRecord, PerformanceMetric, SecurityEvent
)


class SystemHealthSerializer(serializers.ModelSerializer):
    class Meta:
        model = SystemHealth
        fields = '__all__'


class SuperAdminAuditLogSerializer(serializers.ModelSerializer):
    user_username = serializers.CharField(source='user.username', read_only=True)
    
    class Meta:
        model = SuperAdminAuditLog
        fields = '__all__'


class SystemConfigurationSerializer(serializers.ModelSerializer):
    created_by_username = serializers.Char<PERSON>ield(source='created_by.username', read_only=True)
    display_value = serializers.SerializerMethodField()
    
    class Meta:
        model = SystemConfiguration
        fields = '__all__'
    
    def get_display_value(self, obj):
        return obj.get_display_value()


class SystemAlertSerializer(serializers.ModelSerializer):
    resolved_by_username = serializers.Char<PERSON>ield(source='resolved_by.username', read_only=True)
    
    class Meta:
        model = SystemAlert
        fields = '__all__'


class BackupRecordSerializer(serializers.ModelSerializer):
    started_by_username = serializers.CharField(source='started_by.username', read_only=True)
    file_size_mb = serializers.SerializerMethodField()
    duration = serializers.SerializerMethodField()
    
    class Meta:
        model = BackupRecord
        fields = '__all__'
    
    def get_file_size_mb(self, obj):
        return obj.file_size_mb
    
    def get_duration(self, obj):
        return str(obj.duration) if obj.duration else None


class PerformanceMetricSerializer(serializers.ModelSerializer):
    class Meta:
        model = PerformanceMetric
        fields = '__all__'


class SecurityEventSerializer(serializers.ModelSerializer):
    user_username = serializers.CharField(source='user.username', read_only=True)
    resolved_by_username = serializers.CharField(source='resolved_by.username', read_only=True)
    
    class Meta:
        model = SecurityEvent
        fields = '__all__'
