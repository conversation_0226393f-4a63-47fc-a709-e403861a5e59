import React from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';
import { useContentRecommendations } from './hooks';
import {
  PostRecommendations,
  EventRecommendations,
  ResourceRecommendations
} from './components';

const ContentRecommendations: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { recommendations, loading, error, hasRecommendations } = useContentRecommendations();

  if (loading) {
    return (
      <div className="space-y-6">
        <h2 className="text-xl font-bold">{t('dashboard.contentRecommendations.title')}</h2>
        <div className={`bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50 flex justify-center ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="w-8 h-8 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <h2 className="text-xl font-bold">{t('dashboard.contentRecommendations.title')}</h2>
        <div className="bg-red-900/30 backdrop-blur-sm rounded-lg p-4 border border-red-800/50">
          <div className="text-red-400">{error}</div>
        </div>
      </div>
    );
  }

  if (!hasRecommendations) {
    return (
      <div className="space-y-6">
        <h2 className="text-xl font-bold">{t('dashboard.contentRecommendations.title')}</h2>
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50">
          <div className="text-gray-400">{t('dashboard.contentRecommendations.noRecommendations')}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-bold">{t('dashboard.contentRecommendations.title')}</h2>

      {/* Recommended Posts */}
      {recommendations && (
        <PostRecommendations posts={recommendations.recommended_posts} />
      )}

      {/* Recommended Events */}
      {recommendations && (
        <EventRecommendations events={recommendations.recommended_events} />
      )}

      {/* Recommended Resources */}
      {recommendations && (
        <ResourceRecommendations resources={recommendations.recommended_resources} />
      )}
    </div>
  );
};

export default ContentRecommendations;
