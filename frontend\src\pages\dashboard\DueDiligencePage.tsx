import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import Button from '../../components/ui/Button';
import { Badge } from '../../components/ui/badge';
import { Tabs, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '../../components/ui/tabs';
import { Input } from '../../components/ui/input';
import { Textarea } from '../../components/ui/textarea';
import { FileText, CheckCircle, AlertCircle, Clock, Download, Upload } from 'lucide-react';

interface DueDiligenceItem {
  id: string;
  companyName: string;
  category: 'Financial' | 'Legal' | 'Technical' | 'Market' | 'Team';
  documentName: string;
  status: 'Pending' | 'Under Review' | 'Approved' | 'Rejected' | 'Needs Clarification';
  priority: 'High' | 'Medium' | 'Low';
  uploadDate: string;
  reviewDate?: string;
  reviewer?: string;
  notes?: string;
  fileSize?: string;
}

interface DueDiligenceChecklist {
  category: string;
  items: {
    name: string;
    required: boolean;
    completed: boolean;
    notes?: string;
  }[];
}

const DueDiligencePage: React.FC = () => {
  const [dueDiligenceItems, setDueDiligenceItems] = useState<DueDiligenceItem[]>([]);
  const [checklist, setChecklist] = useState<DueDiligenceChecklist[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('documents');
  const [selectedCompany, setSelectedCompany] = useState('all');

  // Mock data - replace with actual API call
  useEffect(() => {
    const mockItems: DueDiligenceItem[] = [
      {
        id: '1',
        companyName: 'TechStart AI',
        category: 'Financial',
        documentName: 'Financial Statements 2023',
        status: 'Approved',
        priority: 'High',
        uploadDate: '2024-01-10',
        reviewDate: '2024-01-12',
        reviewer: 'John Smith',
        notes: 'Strong revenue growth, healthy margins',
        fileSize: '2.4 MB'
      },
      {
        id: '2',
        companyName: 'TechStart AI',
        category: 'Legal',
        documentName: 'Cap Table & Shareholder Agreements',
        status: 'Under Review',
        priority: 'High',
        uploadDate: '2024-01-11',
        fileSize: '1.8 MB'
      },
      {
        id: '3',
        companyName: 'GreenEnergy Solutions',
        category: 'Technical',
        documentName: 'Patent Portfolio Documentation',
        status: 'Needs Clarification',
        priority: 'Medium',
        uploadDate: '2024-01-08',
        reviewDate: '2024-01-10',
        reviewer: 'Sarah Johnson',
        notes: 'Need additional information on patent validity',
        fileSize: '5.2 MB'
      },
      {
        id: '4',
        companyName: 'HealthTech Pro',
        category: 'Market',
        documentName: 'Market Analysis & Competitive Landscape',
        status: 'Pending',
        priority: 'Medium',
        uploadDate: '2024-01-12',
        fileSize: '3.1 MB'
      }
    ];

    const mockChecklist: DueDiligenceChecklist[] = [
      {
        category: 'Financial Documents',
        items: [
          { name: 'Audited Financial Statements (3 years)', required: true, completed: true },
          { name: 'Management Accounts (current year)', required: true, completed: true },
          { name: 'Cash Flow Projections', required: true, completed: false },
          { name: 'Budget vs Actual Analysis', required: false, completed: false }
        ]
      },
      {
        category: 'Legal Documents',
        items: [
          { name: 'Articles of Incorporation', required: true, completed: true },
          { name: 'Shareholder Agreements', required: true, completed: false },
          { name: 'Employment Contracts (Key Personnel)', required: true, completed: false },
          { name: 'IP Assignments', required: true, completed: true }
        ]
      },
      {
        category: 'Technical Assessment',
        items: [
          { name: 'Technology Architecture Documentation', required: true, completed: true },
          { name: 'Security Audit Report', required: true, completed: false },
          { name: 'Scalability Analysis', required: false, completed: false },
          { name: 'Technical Debt Assessment', required: false, completed: true }
        ]
      }
    ];

    setTimeout(() => {
      setDueDiligenceItems(mockItems);
      setChecklist(mockChecklist);
      setLoading(false);
    }, 1000);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Approved': return 'bg-green-100 text-green-800';
      case 'Under Review': return 'bg-blue-100 text-blue-800';
      case 'Pending': return 'bg-yellow-100 text-yellow-800';
      case 'Rejected': return 'bg-red-100 text-red-800';
      case 'Needs Clarification': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Approved': return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'Under Review': return <Clock className="w-4 h-4 text-blue-600" />;
      case 'Pending': return <Clock className="w-4 h-4 text-yellow-600" />;
      case 'Rejected': return <AlertCircle className="w-4 h-4 text-red-600" />;
      case 'Needs Clarification': return <AlertCircle className="w-4 h-4 text-orange-600" />;
      default: return <FileText className="w-4 h-4 text-gray-600" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'High': return 'text-red-600';
      case 'Medium': return 'text-yellow-600';
      case 'Low': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  const getCompletionRate = (items: DueDiligenceChecklist['items']) => {
    const completed = items.filter(item => item.completed).length;
    return Math.round((completed / items.length) * 100);
  };

  const companies = ['all', ...Array.from(new Set(dueDiligenceItems.map(item => item.companyName)))];
  const filteredItems = selectedCompany === 'all' 
    ? dueDiligenceItems 
    : dueDiligenceItems.filter(item => item.companyName === selectedCompany);

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Due Diligence</h1>
          <p className="text-gray-600 mt-1">Manage and track due diligence processes</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Upload className="w-4 h-4 mr-2" />
            Upload Document
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700">
            <Download className="w-4 h-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Company Filter */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <label className="text-sm font-medium">Filter by Company:</label>
            <select 
              value={selectedCompany} 
              onChange={(e) => setSelectedCompany(e.target.value)}
              className="border rounded px-3 py-1"
            >
              {companies.map(company => (
                <option key={company} value={company}>
                  {company === 'all' ? 'All Companies' : company}
                </option>
              ))}
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Due Diligence Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="checklist">Checklist</TabsTrigger>
          <TabsTrigger value="summary">Summary</TabsTrigger>
        </TabsList>

        <TabsContent value="documents" className="space-y-4">
          <div className="grid grid-cols-1 gap-4">
            {filteredItems.map((item) => (
              <Card key={item.id}>
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <FileText className="w-5 h-5 text-gray-400" />
                        <div>
                          <h3 className="font-semibold">{item.documentName}</h3>
                          <p className="text-sm text-gray-600">{item.companyName} • {item.category}</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                        <span>Uploaded: {new Date(item.uploadDate).toLocaleDateString()}</span>
                        {item.fileSize && <span>Size: {item.fileSize}</span>}
                        <span className={getPriorityColor(item.priority)}>
                          {item.priority} Priority
                        </span>
                      </div>

                      {item.notes && (
                        <div className="bg-gray-50 p-3 rounded text-sm mb-3">
                          <strong>Notes:</strong> {item.notes}
                        </div>
                      )}

                      {item.reviewer && (
                        <p className="text-sm text-gray-600">
                          Reviewed by {item.reviewer} on {item.reviewDate && new Date(item.reviewDate).toLocaleDateString()}
                        </p>
                      )}
                    </div>

                    <div className="flex flex-col items-end gap-2">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(item.status)}
                        <Badge className={getStatusColor(item.status)}>
                          {item.status}
                        </Badge>
                      </div>
                      
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Download className="w-4 h-4 mr-1" />
                          Download
                        </Button>
                        <Button variant="outline" size="sm">
                          Review
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="checklist" className="space-y-4">
          {checklist.map((category, categoryIndex) => (
            <Card key={categoryIndex}>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle className="text-lg">{category.category}</CardTitle>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600">
                      {category.items.filter(item => item.completed).length}/{category.items.length} completed
                    </span>
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-green-600 h-2 rounded-full" 
                        style={{ width: `${getCompletionRate(category.items)}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {category.items.map((item, itemIndex) => (
                    <div key={itemIndex} className="flex items-center justify-between p-3 border rounded">
                      <div className="flex items-center gap-3">
                        <input 
                          type="checkbox" 
                          checked={item.completed}
                          onChange={() => {
                            const newChecklist = [...checklist];
                            newChecklist[categoryIndex].items[itemIndex].completed = !item.completed;
                            setChecklist(newChecklist);
                          }}
                          className="w-4 h-4"
                        />
                        <div>
                          <span className={`${item.completed ? 'line-through text-gray-500' : ''}`}>
                            {item.name}
                          </span>
                          {item.required && (
                            <Badge variant="secondary" className="ml-2 text-xs">Required</Badge>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        {item.completed ? (
                          <CheckCircle className="w-5 h-5 text-green-600" />
                        ) : (
                          <Clock className="w-5 h-5 text-gray-400" />
                        )}
                        <Button variant="outline" size="sm">
                          Add Note
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="summary" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="text-center">
                  <h3 className="text-lg font-semibold mb-2">Total Documents</h3>
                  <p className="text-3xl font-bold text-blue-600">{dueDiligenceItems.length}</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="text-center">
                  <h3 className="text-lg font-semibold mb-2">Approved</h3>
                  <p className="text-3xl font-bold text-green-600">
                    {dueDiligenceItems.filter(item => item.status === 'Approved').length}
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="text-center">
                  <h3 className="text-lg font-semibold mb-2">Pending Review</h3>
                  <p className="text-3xl font-bold text-yellow-600">
                    {dueDiligenceItems.filter(item => 
                      item.status === 'Pending' || item.status === 'Under Review'
                    ).length}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Due Diligence Progress by Company</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {companies.filter(c => c !== 'all').map(company => {
                  const companyItems = dueDiligenceItems.filter(item => item.companyName === company);
                  const approvedItems = companyItems.filter(item => item.status === 'Approved');
                  const progress = companyItems.length > 0 ? Math.round((approvedItems.length / companyItems.length) * 100) : 0;
                  
                  return (
                    <div key={company} className="flex items-center justify-between p-3 border rounded">
                      <div>
                        <h4 className="font-medium">{company}</h4>
                        <p className="text-sm text-gray-600">
                          {approvedItems.length}/{companyItems.length} documents approved
                        </p>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="w-24 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-green-600 h-2 rounded-full" 
                            style={{ width: `${progress}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium w-12">{progress}%</span>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default DueDiligencePage;
