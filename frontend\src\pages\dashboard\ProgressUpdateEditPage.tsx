import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Save, Loader2, TrendingUp, CheckCircle, Target } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import AuthenticatedLayout from '../../components/layout/AuthenticatedLayout';
import { ProgressUpdate, progressUpdatesAPI, BusinessIdea, businessIdeasAPI } from '../../services/incubatorApi';
import { useAppSelector } from '../../store/hooks';
import { RTLText, RTLFlex } from '../../components/rtl';

const ProgressUpdateEditPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { language, isRTL } = useLanguage();
  const { user } = useAppSelector(state => state.auth);

  const [progressUpdate, setProgressUpdate] = useState<ProgressUpdate | null>(null);
  const [businessIdeas, setBusinessIdeas] = useState<BusinessIdea[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const [formData, setFormData] = useState({
    business_idea: '',
    title: '',
    description: '',
    progress_percentage: 0,
    milestone_achieved: false,
    challenges_faced: '',
    next_steps: '',
  });

  useEffect(() => {
    if (id) {
      fetchData();
    }
  }, [id]);

  const fetchData = async () => {
    if (!id) return;
    
    setLoading(true);
    try {
      // Fetch user's business ideas
      const ideas = await businessIdeasAPI.getBusinessIdeas();
      const userIdeas = ideas.filter(idea => idea.owner.id === user?.id);
      setBusinessIdeas(userIdeas);

      // Fetch the progress update
      const updateData = await progressUpdatesAPI.getProgressUpdate(parseInt(id));
      
      // Check if user owns this progress update
      if (updateData.created_by.id !== user?.id && !user?.is_staff && !user?.is_superuser) {
        setError(t('progress.errors.notOwner', 'You can only edit your own progress updates'));
        return;
      }

      setProgressUpdate(updateData);
      setFormData({
        business_idea: updateData.business_idea.id.toString(),
        title: updateData.title,
        description: updateData.description,
        progress_percentage: updateData.progress_percentage,
        milestone_achieved: updateData.milestone_achieved,
        challenges_faced: updateData.challenges_faced || '',
        next_steps: updateData.next_steps || '',
      });
    } catch (error) {
      console.error('Error fetching data:', error);
      setError(t('progress.errors.failedToLoad', 'Failed to load progress update'));
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : 
              type === 'number' ? parseInt(value) || 0 : value
    }));
  };

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!id || !progressUpdate) return;

    setSaving(true);
    setError(null);

    try {
      await progressUpdatesAPI.updateProgressUpdate(parseInt(id), {
        ...formData,
        business_idea_id: parseInt(formData.business_idea),
      });
      setSuccess(t('progress.success.updated', 'Progress update updated successfully'));
      
      // Navigate back after success
      setTimeout(() => {
        navigate('/dashboard/progress-updates');
      }, 1500);
    } catch (error) {
      console.error('Error updating progress update:', error);
      setError(t('progress.errors.failedToUpdate', 'Failed to update progress update'));
    } finally {
      setSaving(false);
    }
  };

  const getProgressColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-400';
    if (percentage >= 50) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getProgressBgColor = (percentage: number) => {
    if (percentage >= 80) return 'bg-green-600';
    if (percentage >= 50) return 'bg-yellow-600';
    return 'bg-red-600';
  };

  if (loading) {
    return (
      <AuthenticatedLayout>
      <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="flex justify-center items-center py-12">
          <div className="flex items-center space-x-3">
            <Loader2 className="w-6 h-6 animate-spin" />
            <span>{t('common.loading', 'Loading...')}</span>
          </div>
        </div>
                </div>
        </div>
      </div>
    </AuthenticatedLayout>
    );
  }

  if (error && !progressUpdate) {
    return (
      <AuthenticatedLayout>
        <div className="text-center py-12">
          <div className="text-red-400 mb-4">{error}</div>
          <button
            onClick={() => navigate('/dashboard/progress-updates')}
            className="px-4 py-2 bg-purple-600 rounded-lg hover:bg-purple-700 transition-colors"
          >
            {t('common.goBack', 'Go Back')}
          </button>
        </div>
      </AuthenticatedLayout>
    );
  }

  return (
    <AuthenticatedLayout>
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className={`flex items-center justify-between mb-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`flex items-center space-x-4 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
            <button
              onClick={() => navigate('/dashboard/progress-updates')}
              className="p-2 rounded-lg hover:bg-indigo-800/50 transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <div>
              <RTLText as="h1" className="text-2xl font-bold">
                {t('progress.editUpdate', 'Edit Progress Update')}
              </RTLText>
              <RTLText as="div" className="text-gray-400 mt-1">
                {t('progress.updateYourProgress', 'Update your progress details')}
              </RTLText>
            </div>
          </div>
        </div>

        {/* Current Progress Overview */}
        {progressUpdate && (
          <div className="mb-6 p-6 bg-indigo-950/50 rounded-lg border border-indigo-800">
            <RTLFlex align="center" className="mb-4">
              <div className="p-2 bg-indigo-900/50 rounded-lg mr-3">
                <TrendingUp size={20} />
              </div>
              <div>
                <RTLText as="div" className="text-sm text-gray-400">
                  {t('progress.currentUpdate', 'Current Update')}
                </RTLText>
                <RTLText as="div" className="font-medium">
                  {progressUpdate.business_idea.title}
                </RTLText>
              </div>
              {progressUpdate.milestone_achieved && (
                <div className="ml-auto px-3 py-1 bg-green-900/30 text-green-300 rounded-full text-sm flex items-center">
                  <CheckCircle size={16} className={`${language === 'ar' ? 'ml-1' : 'mr-1'}`} />
                  {t('progress.milestoneAchieved', 'Milestone Achieved')}
                </div>
              )}
            </RTLFlex>
            
            {/* Current Progress Bar */}
            <div>
              <RTLFlex justify="between" align="center" className="mb-2">
                <span className="text-sm text-gray-400">{t('progress.currentProgress', 'Current Progress')}</span>
                <span className={`text-sm font-bold ${getProgressColor(progressUpdate.progress_percentage)}`}>
                  {progressUpdate.progress_percentage}%
                </span>
              </RTLFlex>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full ${getProgressBgColor(progressUpdate.progress_percentage)}`}
                  style={{ width: `${progressUpdate.progress_percentage}%` }}
                ></div>
              </div>
            </div>
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSave} className="space-y-6">
          <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
            {/* Business Idea */}
            <div className="mb-6">
              <label className="block text-gray-300 mb-2 font-medium">
                {t('progress.businessIdea', 'Business Idea')} *
              </label>
              <select
                name="business_idea"
                value={formData.business_idea}
                onChange={handleInputChange}
                className="w-full px-4 py-3 bg-indigo-900/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:border-purple-500 transition-colors"
                required
              >
                <option value="">{t('progress.selectBusinessIdea', 'Select a business idea')}</option>
                {businessIdeas.map(idea => (
                  <option key={idea.id} value={idea.id}>{idea.title}</option>
                ))}
              </select>
            </div>

            {/* Title */}
            <div className="mb-6">
              <label className="block text-gray-300 mb-2 font-medium">
                {t('progress.title', 'Update Title')} *
              </label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                className="w-full px-4 py-3 bg-indigo-900/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:border-purple-500 transition-colors"
                placeholder={t('progress.enterTitle', 'Enter update title')}
                required
                dir={language === 'ar' ? 'rtl' : 'ltr'}
              />
            </div>

            {/* Description */}
            <div className="mb-6">
              <label className="block text-gray-300 mb-2 font-medium">
                {t('progress.description', 'Description')} *
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={4}
                className="w-full px-4 py-3 bg-indigo-900/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:border-purple-500 transition-colors resize-none"
                placeholder={t('progress.enterDescription', 'Describe your progress')}
                required
                dir={language === 'ar' ? 'rtl' : 'ltr'}
              />
            </div>

            {/* Progress Percentage */}
            <div className="mb-6">
              <label className="block text-gray-300 mb-2 font-medium">
                {t('progress.percentage', 'Progress Percentage')} *
              </label>
              <div className="space-y-3">
                <input
                  type="number"
                  name="progress_percentage"
                  value={formData.progress_percentage}
                  onChange={handleInputChange}
                  min="0"
                  max="100"
                  className="w-full px-4 py-3 bg-indigo-900/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:border-purple-500 transition-colors"
                  required
                />
                
                {/* Progress Preview */}
                <div>
                  <RTLFlex justify="between" align="center" className="mb-2">
                    <span className="text-sm text-gray-400">{t('progress.preview', 'Preview')}</span>
                    <span className={`text-sm font-bold ${getProgressColor(formData.progress_percentage)}`}>
                      {formData.progress_percentage}%
                    </span>
                  </RTLFlex>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full transition-all duration-300 ${getProgressBgColor(formData.progress_percentage)}`}
                      style={{ width: `${formData.progress_percentage}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>

            {/* Milestone Achieved */}
            <div className="mb-6">
              <label className="flex items-center space-x-3 text-gray-300">
                <input
                  type="checkbox"
                  name="milestone_achieved"
                  checked={formData.milestone_achieved}
                  onChange={handleInputChange}
                  className="w-5 h-5 rounded border-indigo-800 bg-indigo-900/50 text-green-600 focus:ring-green-500 focus:ring-2"
                />
                <RTLFlex align="center">
                  <CheckCircle className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                  <span>{t('progress.milestoneAchieved', 'Milestone Achieved')}</span>
                </RTLFlex>
              </label>
              <div className="mt-2 text-sm text-gray-400">
                {t('progress.milestoneNote', 'Check this if you reached an important milestone')}
              </div>
            </div>

            {/* Challenges Faced */}
            <div className="mb-6">
              <label className="block text-gray-300 mb-2 font-medium">
                {t('progress.challenges', 'Challenges Faced')} (optional)
              </label>
              <textarea
                name="challenges_faced"
                value={formData.challenges_faced}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-4 py-3 bg-indigo-900/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:border-purple-500 transition-colors resize-none"
                placeholder={t('progress.enterChallenges', 'Describe any challenges you faced')}
                dir={language === 'ar' ? 'rtl' : 'ltr'}
              />
            </div>

            {/* Next Steps */}
            <div className="mb-6">
              <label className="block text-gray-300 mb-2 font-medium">
                {t('progress.nextSteps', 'Next Steps')} (optional)
              </label>
              <textarea
                name="next_steps"
                value={formData.next_steps}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-4 py-3 bg-indigo-900/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:border-purple-500 transition-colors resize-none"
                placeholder={t('progress.enterNextSteps', 'Outline your next steps')}
                dir={language === 'ar' ? 'rtl' : 'ltr'}
              />
            </div>
          </div>

          {/* Error/Success Messages */}
          {error && (
            <div className="p-4 bg-red-900/50 border border-red-800 rounded-lg text-red-200">
              {error}
            </div>
          )}

          {success && (
            <div className="p-4 bg-green-900/50 border border-green-800 rounded-lg text-green-200">
              {success}
            </div>
          )}

          {/* Action Buttons */}
          <div className={`flex justify-end space-x-4 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
            <button
              type="button"
              onClick={() => navigate('/dashboard/progress-updates')}
              className="px-6 py-3 bg-gray-700 hover:bg-gray-600 rounded-lg font-medium transition-colors"
              disabled={saving}
            >
              {t('common.cancel', 'Cancel')}
            </button>
            <RTLFlex
              as="button"
              type="submit"
              className="px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={saving}
              align="center"
            >
              {saving ? (
                <>
                  <Loader2 className={`w-5 h-5 animate-spin ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                  {t('common.saving', 'Saving...')}
                </>
              ) : (
                <>
                  <Save className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                  {t('common.save', 'Save Changes')}
                </>
              )}
            </RTLFlex>
          </div>
        </form>
      </div>
    </AuthenticatedLayout>
  );
};

export default ProgressUpdateEditPage;
