from rest_framework import serializers
from django.contrib.auth.models import User
from .models import AIRecommendation, AIRecommendationFeedback


class UserSerializer(serializers.ModelSerializer):
    """Serializer for user details in recommendations"""
    
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name']


class AIRecommendationSerializer(serializers.ModelSerializer):
    """Serializer for AI recommendations"""
    
    business_idea_title = serializers.CharField(source='business_idea.title', read_only=True)
    recommendation_type_display = serializers.CharField(source='get_recommendation_type_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    implemented_by_details = UserSerializer(source='implemented_by', read_only=True)
    feedback_count = serializers.SerializerMethodField()
    helpful_count = serializers.SerializerMethodField()
    
    class Meta:
        model = AIRecommendation
        fields = [
            'id', 'business_idea', 'business_idea_title', 'recommendation_type', 
            'recommendation_type_display', 'title', 'description', 'reasoning', 
            'priority', 'priority_display', 'action_items', 'expected_outcome', 
            'relevance_score', 'is_implemented', 'implementation_notes', 
            'implemented_by', 'implemented_by_details', 'implemented_at', 
            'feedback_count', 'helpful_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['implemented_by', 'implemented_at', 'created_at', 'updated_at']
    
    def get_feedback_count(self, obj):
        """Get total feedback count"""
        return obj.feedback.count()
    
    def get_helpful_count(self, obj):
        """Get helpful feedback count"""
        return obj.feedback.filter(feedback_type='helpful').count()


class AIRecommendationFeedbackSerializer(serializers.ModelSerializer):
    """Serializer for AI recommendation feedback"""
    
    user_details = UserSerializer(source='user', read_only=True)
    feedback_type_display = serializers.CharField(source='get_feedback_type_display', read_only=True)
    
    class Meta:
        model = AIRecommendationFeedback
        fields = [
            'id', 'recommendation', 'user', 'user_details', 'feedback_type', 
            'feedback_type_display', 'comments', 'created_at'
        ]
        read_only_fields = ['user', 'created_at']
    
    def create(self, validated_data):
        """Create feedback with current user"""
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)
