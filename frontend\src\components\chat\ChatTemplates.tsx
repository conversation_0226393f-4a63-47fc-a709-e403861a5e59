import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  MessageSquare,
  Lightbulb,
  TrendingUp,
  Users,
  DollarSign,
  Target,
  BarChart3,
  Rocket,
  Search,
  X,
  Send
} from 'lucide-react';

interface ChatTemplate {
  id: string;
  title: string;
  description: string;
  category: string;
  icon: React.ReactNode;
  message: string;
  tags: string[];
}

interface ChatTemplatesProps {
  onSelectTemplate: (message: string) => void;
  onClose: () => void;
  businessIdeaTitle?: string;
}

const ChatTemplates: React.FC<ChatTemplatesProps> = ({ onSelectTemplate,
  onClose,
  businessIdeaTitle
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const templates: ChatTemplate[] = [
    {
      id: 'business-analysis',
      title: t('chat.templates.businessAnalysis'),
      description: t('chat.templates.businessAnalysisDesc'),
      category: 'analysis',
      icon: <BarChart3 size={20} />,
      message: businessIdeaTitle
        ? `Please provide a comprehensive analysis of my business idea "${businessIdeaTitle}". Include market potential, target audience, competitive landscape, and key success factors.`
        : t("chat.please.help.me", "Please help me analyze my business idea. What are the key factors I should consider for market potential and competitive positioning?"),
      tags: ['analysis', 'market', 'competition']
    },
    {
      id: 'market-research',
      title: t('chat.templates.marketResearch'),
      description: t('chat.templates.marketResearchDesc'),
      category: 'research',
      icon: <TrendingUp size={20} />,
      message: t("chat.i.need.help", "I need help conducting market research for my business. Can you guide me through the process of identifying my target market, analyzing competitors, and understanding market trends?"),
      tags: ['research', 'market', 'trends']
    },
    {
      id: 'financial-planning',
      title: t('chat.templates.financialPlanning'),
      description: t('chat.templates.financialPlanningDesc'),
      category: 'finance',
      icon: <DollarSign size={20} />,
      message: t("chat.help.me.create", "Help me create a financial plan for my business. I need guidance on startup costs, revenue projections, funding options, and financial milestones."),
      tags: ['finance', 'planning', 'funding']
    },
    {
      id: 'marketing-strategy',
      title: t('chat.templates.marketingStrategy'),
      description: t('chat.templates.marketingStrategyDesc'),
      category: 'marketing',
      icon: <Target size={20} />,
      message: t("chat.i.need.to", "I need to develop a marketing strategy for my business. Can you help me identify the best marketing channels, create a brand message, and plan promotional activities?"),
      tags: ['marketing', 'strategy', 'branding']
    },
    {
      id: 'team-building',
      title: t('chat.templates.teamBuilding'),
      description: t('chat.templates.teamBuildingDesc'),
      category: 'team',
      icon: <Users size={20} />,
      message: 'I\'m looking to build a team for my startup. What roles should I prioritize, how do I find the right people, and what should I consider for equity and compensation?',
      tags: ['team', 'hiring', 'equity']
    },
    {
      id: 'product-development',
      title: t('chat.templates.productDevelopment'),
      description: t('chat.templates.productDevelopmentDesc'),
      category: 'product',
      icon: <Rocket size={20} />,
      message: t("chat.guide.me.through", "Guide me through the product development process. I need help with MVP planning, feature prioritization, user testing, and iteration strategies."),
      tags: ['product', 'mvp', 'development']
    },
    {
      id: 'pitch-preparation',
      title: t('chat.templates.pitchPreparation'),
      description: t('chat.templates.pitchPreparationDesc'),
      category: 'pitch',
      icon: <MessageSquare size={20} />,
      message: t("chat.i.need.to", "I need to prepare a pitch for investors. Help me structure my presentation, highlight key points, anticipate questions, and create compelling slides."),
      tags: ['pitch', 'investors', 'presentation']
    },
    {
      id: 'problem-solving',
      title: t('chat.templates.problemSolving'),
      description: t('chat.templates.problemSolvingDesc'),
      category: 'general',
      icon: <Lightbulb size={20} />,
      message: 'I\'m facing a challenge in my business and need creative solutions. Can you help me brainstorm ideas and evaluate different approaches?',
      tags: ['problem', 'solutions', 'brainstorm']
    }
  ];

  const categories = [
    { id: 'all', label: t('common.all') },
    { id: 'analysis', label: t('chat.categories.analysis') },
    { id: 'research', label: t('chat.categories.research') },
    { id: 'finance', label: t('chat.categories.finance') },
    { id: 'marketing', label: t('chat.categories.marketing') },
    { id: 'team', label: t('chat.categories.team') },
    { id: 'product', label: t('chat.categories.product') },
    { id: 'pitch', label: t('chat.categories.pitch') },
    { id: 'general', label: t('chat.categories.general') }
  ];

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = searchQuery === '' ||
      template.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase();

    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;

    return matchesSearch && matchesCategory;
  );

  const handleSelectTemplate = (template: ChatTemplate) => {
    onSelectTemplate(template.message);
    onClose();
  };

  return (
    <div className={`fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4 ${isRTL ? "flex-row-reverse" : ""}`}>
      <div className="card max-w-4xl w-full max-h-[90vh] overflow-hidden shadow-2xl backdrop-blur-md">
        {/* Header */}
        <div className={`flex items-center justify-between p-6 border-b border-indigo-800/30 bg-gradient-to-r from-indigo-900/20 to-purple-900/20 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div>
            <h2 className="text-xl font-semibold text-primary">
              {t('chat.chatTemplates')}
            </h2>
            <p className="text-sm text-secondary mt-1">
              {t('chat.templatesSubtitle')}
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
          >
            <X size={20} />
          </button>
        </div>

        {/* Search and Filters */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className={`flex flex-col sm:flex-row gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            {/* Search */}
            <div className={`flex-1 relative ${isRTL ? "flex-row-reverse" : ""}`}>
              <Search size={16} className={`absolute top-3 ${isRTL ? 'right-3' : 'left-3'} text-gray-400`} />
              <input
                type="text"
                placeholder={t('chat.searchTemplates')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className={`w-full ${isRTL ? 'pr-10 text-right' : 'pl-10'} py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
              />
            </div>

            {/* Category Filter */}
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              {categories.map(category => (
                <option key={category.id} value={category.id}>
                  {category.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Templates Grid */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {filteredTemplates.length === 0 ? (
            <div className="text-center py-12">
              <MessageSquare size={48} className="mx-auto text-gray-400 mb-4" />
              <p className="text-gray-500 dark:text-gray-400">
                {t('chat.noTemplatesFound')}
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {filteredTemplates.map((template) => (
                <div
                  key={template.id}
                  className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:border-purple-300 dark:hover:border-purple-600 transition-colors cursor-pointer group"
                  onClick={() => handleSelectTemplate(template)}
                >
                  <div className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''} mb-3`}>
                    <div className={`p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg ${isRTL ? 'ml-3' : 'mr-3'}`}>
                      {template.icon}
                    </div>
                    <div className={`flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <h3 className="font-medium text-gray-900 dark:text-white group-hover:text-purple-600 dark:group-hover:text-purple-400">
                        {template.title}
                      </h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                        {template.description}
                      </p>
                    </div>
                  </div>

                  {/* Tags */}
                  <div className={`flex flex-wrap gap-1 mb-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    {template.tags.map((tag) => (
                      <span
                        key={tag}
                        className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-xs rounded"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>

                  {/* Use Button */}
                  <button className={`w-full flex items-center justify-center px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md text-sm transition-colors ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <Send size={14} className={isRTL ? 'ml-2' : 'mr-2'} />
                    {t('chat.useTemplate')}
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChatTemplates;
