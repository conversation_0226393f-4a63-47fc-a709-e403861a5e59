import { lazy } from 'react';
import { RouteConfig, createRoleRoute } from './routeConfig';

// DEDICATED INVESTOR DASHBOARD - Investor users only
const InvestorDashboardPage = lazy(() => import('../pages/investor/InvestorDashboardPage'));
// Legacy InvestorDashboard kept for backward compatibility
const InvestorDashboard = lazy(() => import('../components/dashboard/investor-dashboard/InvestorDashboard'));
const InvestmentOpportunitiesPage = lazy(() => import('../pages/dashboard/InvestmentOpportunitiesPage'));
const PortfolioManagementPage = lazy(() => import('../pages/dashboard/PortfolioManagementPage'));
const InvestorAnalyticsPage = lazy(() => import('../pages/dashboard/InvestorAnalyticsPage'));
const DueDiligencePage = lazy(() => import('../pages/dashboard/DueDiligencePage'));
const InvestorProfilePage = lazy(() => import('../pages/dashboard/InvestorProfilePage'));

// Shared components that investors can access
const BusinessIdeasPage = lazy(() => import('../pages/dashboard/BusinessIdeasPage'));
const BusinessPlanPage = lazy(() => import('../pages/dashboard/BusinessPlanPage'));
const PostsPage = lazy(() => import('../pages/dashboard/PostsPage'));
const EventsPage = lazy(() => import('../pages/dashboard/EventsPage'));
const ConsolidatedAIPage = lazy(() => import('../pages/ConsolidatedAIPage'));
const UserSettings = lazy(() => import('../pages/UserSettings'));

/**
 * Investor-specific routes for investment management and opportunities
 */
export const investorRoutes: RouteConfig[] = [
  // Main investor dashboard - DEDICATED INVESTOR ONLY ✅ (using InvestorDashboardPage)
  createRoleRoute('/dashboard/investor', InvestorDashboardPage, ['investor'], ['read', 'write'], 'Loading investor dashboard...'),
  createRoleRoute('/dashboard/investments', InvestorDashboardPage, ['investor'], ['read', 'write'], 'Loading investment dashboard...'),

  // Investment management
  createRoleRoute('/dashboard/investments/opportunities', InvestmentOpportunitiesPage, ['investor'], ['read', 'write'], 'Loading investment opportunities...'),
  createRoleRoute('/dashboard/investments/portfolio', PortfolioManagementPage, ['investor'], ['read', 'write'], 'Loading portfolio management...'),
  createRoleRoute('/dashboard/investments/analytics', InvestorAnalyticsPage, ['investor'], ['read'], 'Loading investor analytics...'),
  createRoleRoute('/dashboard/investments/due-diligence', DueDiligencePage, ['investor'], ['read', 'write'], 'Loading due diligence...'),
  createRoleRoute('/dashboard/investments/profile', InvestorProfilePage, ['investor'], ['read', 'write'], 'Loading investor profile...'),

  // Business evaluation for investors
  createRoleRoute('/dashboard/business-ideas', BusinessIdeasPage, ['investor'], ['read', 'write'], 'Loading business ideas...'),
  createRoleRoute('/dashboard/business-plans', BusinessPlanPage, ['investor'], ['read', 'write'], 'Loading business plans...'),

  // Community features for investors
  createRoleRoute('/dashboard/posts', PostsPage, ['investor'], ['read', 'write'], 'Loading posts...'),
  createRoleRoute('/dashboard/events', EventsPage, ['investor'], ['read', 'write'], 'Loading events...'),

  // AI assistance for investors
  createRoleRoute('/chat/enhanced', ConsolidatedAIPage, ['investor'], ['read'], 'Loading AI Assistant...'),

  // Settings
  createRoleRoute('/settings', UserSettings, ['investor'], ['read', 'write'], 'Loading settings...'),
];

export default investorRoutes;
