from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from users.models import UserPro<PERSON>le, UserRole, UserRoleAssignment
from django.utils import timezone
from django.db.models import Q, Count
import json


class Command(BaseCommand):
    help = 'List all users with their roles in the application'

    def add_arguments(self, parser):
        parser.add_argument(
            '--format',
            type=str,
            choices=['table', 'json', 'csv'],
            default='table',
            help='Output format (default: table)'
        )
        parser.add_argument(
            '--include-inactive',
            action='store_true',
            help='Include inactive users'
        )
        parser.add_argument(
            '--role-filter',
            type=str,
            help='Filter by specific role name (e.g., admin, user, mentor)'
        )
        parser.add_argument(
            '--stats-only',
            action='store_true',
            help='Show only statistics summary'
        )
        parser.add_argument(
            '--no-role-only',
            action='store_true',
            help='Show only users without assigned roles'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🔍 Fetching all users from database...'))
        
        # Build base query
        query = Q()
        
        if not options['include_inactive']:
            query &= Q(is_active=True)
        
        # Get all users
        users = User.objects.filter(query).select_related().prefetch_related(
            'profile__roles',
            'profile__userroleassignment_set__role'
        )
        
        # Filter by role if specified
        if options['role_filter']:
            users = users.filter(profile__roles__name=options['role_filter']).distinct()
        
        # Filter users without roles if specified
        if options['no_role_only']:
            users = users.filter(profile__roles__isnull=True)
        
        if not users.exists():
            self.stdout.write(self.style.WARNING('❌ No users found matching the criteria'))
            return
        
        # Show statistics first
        self._show_statistics(users)
        
        if options['stats_only']:
            return
        
        # Format output based on requested format
        if options['format'] == 'json':
            self._output_json(users)
        elif options['format'] == 'csv':
            self._output_csv(users)
        else:
            self._output_table(users)
    
    def _show_statistics(self, users):
        """Show user statistics"""
        self.stdout.write(self.style.SUCCESS('\n📊 USER STATISTICS'))
        self.stdout.write(self.style.SUCCESS('=' * 50))
        
        total_users = users.count()
        active_users = users.filter(is_active=True).count()
        inactive_users = total_users - active_users
        
        # Django permissions
        staff_users = users.filter(is_staff=True).count()
        superusers = users.filter(is_superuser=True).count()
        
        # Users with profiles
        users_with_profiles = users.filter(profile__isnull=False).count()
        users_without_profiles = total_users - users_with_profiles
        
        # Role statistics
        role_stats = {}
        all_roles = UserRole.objects.all()
        
        for role in all_roles:
            count = UserRoleAssignment.objects.filter(
                role=role,
                is_active=True,
                is_approved=True,
                user_profile__user__in=users
            ).count()
            if count > 0:
                role_stats[role.display_name] = count
        
        # Users without roles
        users_without_roles = users.filter(
            Q(profile__isnull=True) | 
            Q(profile__userroleassignment__isnull=True) |
            Q(profile__userroleassignment__is_active=False)
        ).distinct().count()
        
        self.stdout.write(f"Total Users: {total_users}")
        self.stdout.write(f"Active Users: {active_users}")
        self.stdout.write(f"Inactive Users: {inactive_users}")
        self.stdout.write(f"Staff Users: {staff_users}")
        self.stdout.write(f"Superusers: {superusers}")
        self.stdout.write(f"Users with Profiles: {users_with_profiles}")
        self.stdout.write(f"Users without Profiles: {users_without_profiles}")
        self.stdout.write(f"Users without Roles: {users_without_roles}")
        
        if role_stats:
            self.stdout.write(self.style.SUCCESS('\n🎭 ROLE DISTRIBUTION:'))
            for role_name, count in sorted(role_stats.items()):
                self.stdout.write(f"  • {role_name}: {count}")
        
        self.stdout.write('')
    
    def _output_table(self, users):
        """Output in table format"""
        self.stdout.write(self.style.SUCCESS(f'\n👥 USER LIST ({users.count()} users):\n'))
        
        # Header
        header = f"{'ID':<5} {'Username':<20} {'Email':<35} {'Name':<25} {'Roles':<30} {'Status':<15} {'Last Login':<20}"
        self.stdout.write(self.style.SUCCESS(header))
        self.stdout.write(self.style.SUCCESS('-' * len(header)))
        
        for user in users:
            # Get user profile and roles
            try:
                profile = user.profile
                active_assignments = UserRoleAssignment.objects.filter(
                    user_profile=profile,
                    is_active=True,
                    is_approved=True
                ).select_related('role')
                
                roles = ', '.join([assignment.role.name for assignment in active_assignments])
                if not roles:
                    roles = 'No roles'
                
            except UserProfile.DoesNotExist:
                roles = 'No profile'
            
            # Format status
            status_parts = []
            if user.is_active:
                status_parts.append('✅ Active')
            else:
                status_parts.append('❌ Inactive')
            
            if user.is_staff:
                status_parts.append('👨‍💼 Staff')
            
            if user.is_superuser:
                status_parts.append('🔐 Super')
            
            status = ' '.join(status_parts)
            
            # Format last login
            last_login = user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login else 'Never'
            
            # Format name
            full_name = f"{user.first_name} {user.last_name}".strip() or 'N/A'
            
            row = f"{user.id:<5} {user.username:<20} {user.email:<35} {full_name:<25} {roles:<30} {status:<15} {last_login:<20}"
            self.stdout.write(row)
        
        self.stdout.write('')
    
    def _output_json(self, users):
        """Output in JSON format"""
        users_data = []
        
        for user in users:
            # Get user profile and roles
            try:
                profile = user.profile
                active_assignments = UserRoleAssignment.objects.filter(
                    user_profile=profile,
                    is_active=True,
                    is_approved=True
                ).select_related('role')
                
                roles = [
                    {
                        'name': assignment.role.name,
                        'display_name': assignment.role.display_name,
                        'permission_level': assignment.role.permission_level,
                        'assigned_at': assignment.assigned_at.isoformat() if assignment.assigned_at else None
                    }
                    for assignment in active_assignments
                ]
                
                profile_data = {
                    'bio': profile.bio,
                    'location': profile.location,
                    'company': profile.company,
                    'language': profile.language,
                    'is_active': profile.is_active,
                    'years_of_experience': profile.years_of_experience
                }
                
            except UserProfile.DoesNotExist:
                roles = []
                profile_data = None
            
            user_data = {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'is_active': user.is_active,
                'is_staff': user.is_staff,
                'is_superuser': user.is_superuser,
                'last_login': user.last_login.isoformat() if user.last_login else None,
                'date_joined': user.date_joined.isoformat(),
                'profile': profile_data,
                'roles': roles
            }
            users_data.append(user_data)
        
        self.stdout.write(json.dumps(users_data, indent=2))
    
    def _output_csv(self, users):
        """Output in CSV format"""
        import csv
        import sys
        
        writer = csv.writer(sys.stdout)
        
        # Header
        writer.writerow([
            'ID', 'Username', 'Email', 'First Name', 'Last Name',
            'Is Active', 'Is Staff', 'Is Superuser', 'Last Login',
            'Date Joined', 'Roles', 'Bio', 'Location', 'Company', 
            'Language', 'Years Experience'
        ])
        
        for user in users:
            # Get user profile and roles
            try:
                profile = user.profile
                active_assignments = UserRoleAssignment.objects.filter(
                    user_profile=profile,
                    is_active=True,
                    is_approved=True
                ).select_related('role')
                
                roles = '; '.join([assignment.role.name for assignment in active_assignments])
                bio = profile.bio
                location = profile.location
                company = profile.company
                language = profile.language
                years_exp = profile.years_of_experience
                
            except UserProfile.DoesNotExist:
                roles = ''
                bio = location = company = language = ''
                years_exp = 0
            
            writer.writerow([
                user.id,
                user.username,
                user.email,
                user.first_name,
                user.last_name,
                user.is_active,
                user.is_staff,
                user.is_superuser,
                user.last_login.isoformat() if user.last_login else '',
                user.date_joined.isoformat(),
                roles,
                bio,
                location,
                company,
                language,
                years_exp
            ])
