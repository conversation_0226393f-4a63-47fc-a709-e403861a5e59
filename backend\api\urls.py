from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import (
    EventViewSet, ResourceViewSet, PostViewSet, CommentViewSet,
    TagViewSet, MembershipApplicationViewSet, recent_activity
)
from .ai_permissions import (
    get_user_ai_access, check_ai_capability_access, get_all_ai_capabilities
)

router = DefaultRouter()
router.register(r'events', EventViewSet)
router.register(r'resources', ResourceViewSet)
router.register(r'posts', PostViewSet)
router.register(r'comments', CommentViewSet)
router.register(r'tags', TagViewSet)
router.register(r'membership-applications', MembershipApplicationViewSet)

urlpatterns = [
    path('', include(router.urls)),
    path('recent_activity/', recent_activity, name='recent-activity'),
    path('admin/logs/', include('api.urls_logs')),

    # AI Permissions and Access Control
    path('ai/access/', get_user_ai_access, name='user-ai-access'),
    path('ai/check-capability/', check_ai_capability_access, name='check-ai-capability'),
    path('ai/capabilities/all/', get_all_ai_capabilities, name='all-ai-capabilities'),
]
