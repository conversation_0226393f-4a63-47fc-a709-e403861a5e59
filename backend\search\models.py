from django.db import models
from django.db.models.signals import post_save
from django.dispatch import receiver
from api.models import Post, Resource, Event
from django.contrib.auth.models import User

class SearchDocument(models.Model):
    """
    A unified search document that contains searchable content from various models.
    This allows for search across multiple content types using SQLite.
    """
    # Content type and ID
    content_type = models.CharField(max_length=20)  # 'post', 'resource', 'event', 'user'
    object_id = models.IntegerField()

    # Common fields for all content types
    title = models.CharField(max_length=255)
    content = models.TextField()
    author = models.CharField(max_length=150, blank=True)
    created_at = models.DateTimeField()
    updated_at = models.DateTimeField()

    # Additional metadata
    tags = models.JSONField(default=list, blank=True)
    metadata = models.JSONField(default=dict, blank=True)

    # Search fields for SQLite (combined text for searching)
    search_text = models.TextField(blank=True, help_text="Combined text for searching")

    class Meta:
        indexes = [
            models.Index(fields=['content_type']),
            models.Index(fields=['object_id']),
            models.Index(fields=['created_at']),
        ]
        unique_together = ('content_type', 'object_id')

    def __str__(self):
        return f"{self.content_type}: {self.title}"


# Signal handlers to update search documents when models are saved

@receiver(post_save, sender=Post)
def update_post_search_document(sender, instance, **kwargs):
    """Update search document when a post is saved"""
    # Only index approved posts
    if instance.moderation_status != 'approved':
        # Delete any existing search document if post is not approved
        SearchDocument.objects.filter(content_type='post', object_id=instance.id).delete()
        return

    # Get tag names
    tag_names = [tag.name for tag in instance.tags.all()]
    tag_text = ' '.join(tag_names)

    # Get or create search document
    search_doc, created = SearchDocument.objects.get_or_create(
        content_type='post',
        object_id=instance.id,
        defaults={
            'title': instance.title,
            'content': instance.content,
            'author': instance.author.username,
            'created_at': instance.created_at,
            'updated_at': instance.updated_at,
            'tags': tag_names,
            'metadata': {
                'like_count': instance.likes.count(),
                'comment_count': instance.comments.count(),
            }
        }
    )

    # Update existing document if not created
    if not created:
        search_doc.title = instance.title
        search_doc.content = instance.content
        search_doc.author = instance.author.username
        search_doc.updated_at = instance.updated_at
        search_doc.tags = tag_names
        search_doc.metadata = {
            'like_count': instance.likes.count(),
            'comment_count': instance.comments.count(),
        }

    # Update search text (combined text for SQLite search)
    search_doc.search_text = f"{instance.title} {instance.content} {instance.author.username} {tag_text}"
    search_doc.save()


@receiver(post_save, sender=Resource)
def update_resource_search_document(sender, instance, **kwargs):
    """Update search document when a resource is saved"""
    # Get tag names
    tag_names = [tag.name for tag in instance.tags.all()]
    tag_text = ' '.join(tag_names)

    # Get or create search document
    search_doc, created = SearchDocument.objects.get_or_create(
        content_type='resource',
        object_id=instance.id,
        defaults={
            'title': instance.title,
            'content': instance.description,
            'author': instance.author.username,
            'created_at': instance.created_at,
            'updated_at': instance.updated_at,
            'tags': tag_names,
            'metadata': {
                'resource_type': instance.resource_type,
                'url': instance.url,
            }
        }
    )

    # Update existing document if not created
    if not created:
        search_doc.title = instance.title
        search_doc.content = instance.description
        search_doc.author = instance.author.username
        search_doc.updated_at = instance.updated_at
        search_doc.tags = tag_names
        search_doc.metadata = {
            'resource_type': instance.resource_type,
            'url': instance.url,
        }

    # Update search text (combined text for SQLite search)
    search_doc.search_text = f"{instance.title} {instance.description} {instance.author.username} {tag_text} {instance.resource_type}"
    search_doc.save()


@receiver(post_save, sender=Event)
def update_event_search_document(sender, instance, **kwargs):
    """Update search document when an event is saved"""
    # Only index approved events
    if instance.moderation_status != 'approved':
        # Delete any existing search document if event is not approved
        SearchDocument.objects.filter(content_type='event', object_id=instance.id).delete()
        return

    # Get or create search document
    search_doc, created = SearchDocument.objects.get_or_create(
        content_type='event',
        object_id=instance.id,
        defaults={
            'title': instance.title,
            'content': instance.description,
            'author': instance.organizer.username,
            'created_at': instance.created_at,
            'updated_at': instance.updated_at,
            'tags': [],
            'metadata': {
                'location': instance.location,
                'date': instance.date.isoformat(),
                'is_virtual': instance.is_virtual,
                'attendee_count': instance.attendees.count(),
            }
        }
    )

    # Update existing document if not created
    if not created:
        search_doc.title = instance.title
        search_doc.content = instance.description
        search_doc.author = instance.organizer.username
        search_doc.updated_at = instance.updated_at
        search_doc.metadata = {
            'location': instance.location,
            'date': instance.date.isoformat(),
            'is_virtual': instance.is_virtual,
            'attendee_count': instance.attendees.count(),
        }

    # Update search text (combined text for SQLite search)
    search_doc.search_text = f"{instance.title} {instance.description} {instance.organizer.username} {instance.location}"
    search_doc.save()


@receiver(post_save, sender=User)
def update_user_search_document(sender, instance, **kwargs):
    """Update search document when a user is saved"""
    # Get or create search document
    search_doc, created = SearchDocument.objects.get_or_create(
        content_type='user',
        object_id=instance.id,
        defaults={
            'title': instance.username,
            'content': f"{instance.first_name} {instance.last_name}",
            'author': '',
            'created_at': instance.date_joined,
            'updated_at': instance.date_joined,
            'tags': [],
            'metadata': {
                'email': instance.email,
                'is_staff': instance.is_staff,
                'is_superuser': instance.is_superuser,
            }
        }
    )

    # Update existing document if not created
    if not created:
        search_doc.title = instance.username
        search_doc.content = f"{instance.first_name} {instance.last_name}"
        search_doc.updated_at = instance.date_joined
        search_doc.metadata = {
            'email': instance.email,
            'is_staff': instance.is_staff,
            'is_superuser': instance.is_superuser,
        }

    # Update search text (combined text for SQLite search)
    full_name = f"{instance.first_name} {instance.last_name}".strip()
    search_doc.search_text = f"{instance.username} {full_name} {instance.email}"
    search_doc.save()
