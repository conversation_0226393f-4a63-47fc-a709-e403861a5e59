import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  Edit, 
  Trash2, 
  Plus, 
  Users, 
  Calendar, 
  Target, 
  TrendingUp,
  ArrowLeft,
  Lightbulb
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { useAppSelector } from '../../store/hooks';
// AuthenticatedLayout removed - page is already wrapped by route layout
import { BusinessIdea, businessIdeasAPI, progressUpdatesAPI } from '../../services/incubatorApi';
import BusinessIdeaForm from '../../components/incubator/BusinessIdeaForm';
import ProgressUpdateForm from '../../components/incubator/ProgressUpdateForm';
import ConfirmDialog from '../../components/common/ConfirmDialog';
import useCRUD from '../../hooks/useCRUD';

const BusinessIdeaDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { user } = useAppSelector(state => state.auth);

  const [businessIdea, setBusinessIdea] = useState<BusinessIdea | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showEditForm, setShowEditForm] = useState(false);
  const [showProgressForm, setShowProgressForm] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // CRUD operations for progress updates
  const progressCRUD = useCRUD({
    create: progressUpdatesAPI.createProgressUpdate,
    read: () => progressUpdatesAPI.getProgressUpdates(),
    update: progressUpdatesAPI.updateProgressUpdate,
    delete: progressUpdatesAPI.deleteProgressUpdate
  }, {
    onSuccess: (operation) => {
      if (operation === 'create') {
        setShowProgressForm(false);
        fetchBusinessIdea(); // Refresh to get updated progress updates
      }
    }
  });

  useEffect(() => {
    if (id) {
      fetchBusinessIdea();
    }
  }, [id]);

  const fetchBusinessIdea = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      setError(null);
      const idea = await businessIdeasAPI.getBusinessIdea(parseInt(id));
      setBusinessIdea(idea);
    } catch (err) {
      setError(err instanceof Error ? err.message : t('common.error.unknown'));
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateBusinessIdea = async (data: Partial<BusinessIdea>): Promise<boolean> => {
    if (!businessIdea) return false;

    try {
      const updatedIdea = await businessIdeasAPI.updateBusinessIdea(businessIdea.id, data);
      setBusinessIdea(updatedIdea);
      setShowEditForm(false);
      return true;
    } catch (err) {
      console.error('Error updating business idea:', err);
      return false;
    }
  };

  const handleDeleteBusinessIdea = async () => {
    if (!businessIdea) return;

    try {
      await businessIdeasAPI.deleteBusinessIdea(businessIdea.id);
      navigate('/dashboard/business-ideas');
    } catch (err) {
      console.error('Error deleting business idea:', err);
      setError(t('incubator.businessIdea.deleteError'));
    }
    setShowDeleteDialog(false);
  };

  const canEdit = businessIdea && user && (
    businessIdea.owner.id === user.id || 
    businessIdea.collaborators?.some(collab => collab.id === user.id) ||
    user.is_admin
  );

  const getStageColor = (stage: string) => {
    const colors = {
      concept: 'bg-gray-600',
      validation: 'bg-yellow-600',
      development: 'bg-blue-600',
      scaling: 'bg-purple-600',
      established: 'bg-green-600'
    };
    return colors[stage as keyof typeof colors] || colors.concept;
  };

  const getModerationStatusColor = (status: string) => {
    const colors = {
      approved: 'text-green-400',
      pending: 'text-yellow-400',
      rejected: 'text-red-400'
    };
    return colors[status as keyof typeof colors] || colors.pending;
  };

  if (loading) {
    return (
      <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="flex justify-center items-center h-64">
              <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !businessIdea) {
    return (
      <div className="text-center py-12">
        <Lightbulb size={48} className="mx-auto text-gray-600 mb-4" />
        <h3 className="text-lg font-medium text-gray-400 mb-2">
          {error || t('incubator.businessIdea.notFound')}
        </h3>
        <button
          onClick={() => navigate('/dashboard/business-ideas')}
          className="text-purple-400 hover:text-purple-300 transition-colors"
        >
          {t('common.goBack')}
        </button>
      </div>
    );
  }

  return (
    <div className="p-6">
        {/* Header */}
        <div className={`flex items-center justify-between mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <button
              onClick={() => navigate('/dashboard/business-ideas')}
              className={`text-gray-400 hover:text-white transition-colors ${isRTL ? 'ml-4' : 'mr-4'}`}
            >
              <ArrowLeft size={20} />
            </button>
            <div>
              <h1 className="text-2xl font-bold text-white">{businessIdea.title}</h1>
              <div className={`flex items-center gap-4 mt-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <span className={`px-3 py-1 rounded-full text-xs font-medium text-white ${getStageColor(businessIdea.current_stage)}`}>
                  {t(`incubator.businessIdea.stages.${businessIdea.current_stage}`)}
                </span>
                <span className={`text-sm ${getModerationStatusColor(businessIdea.moderation_status)}`}>
                  {t(`incubator.businessIdea.moderation.${businessIdea.moderation_status}`)}
                </span>
              </div>
            </div>
          </div>

          {canEdit && (
            <div className={`flex gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <button
                onClick={() => setShowEditForm(true)}
                className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors flex items-center text-white"
              >
                <Edit size={16} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                {t('common.edit')}
              </button>
              <button
                onClick={() => setShowDeleteDialog(true)}
                className="px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors flex items-center text-white"
              >
                <Trash2 size={16} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                {t('common.delete')}
              </button>
            </div>
          )}
        </div>

        {/* Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Description */}
            <div className="bg-gray-800/50 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-4">
                {t('incubator.businessIdea.description')}
              </h3>
              <p className={`text-gray-300 ${isRTL ? 'text-right' : ''}`}>
                {businessIdea.description}
              </p>
            </div>

            {/* Problem Statement */}
            <div className="bg-gray-800/50 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-4">
                {t('incubator.businessIdea.problemStatement')}
              </h3>
              <p className={`text-gray-300 ${isRTL ? 'text-right' : ''}`}>
                {businessIdea.problem_statement}
              </p>
            </div>

            {/* Solution */}
            <div className="bg-gray-800/50 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-4">
                {t('incubator.businessIdea.solutionDescription')}
              </h3>
              <p className={`text-gray-300 ${isRTL ? 'text-right' : ''}`}>
                {businessIdea.solution_description}
              </p>
            </div>

            {/* Target Audience */}
            <div className="bg-gray-800/50 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-4">
                {t('incubator.businessIdea.targetAudience')}
              </h3>
              <p className={`text-gray-300 ${isRTL ? 'text-right' : ''}`}>
                {businessIdea.target_audience}
              </p>
            </div>

            {businessIdea.market_opportunity && (
              <div className="bg-gray-800/50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-white mb-4">
                  {t('incubator.businessIdea.marketOpportunity')}
                </h3>
                <p className={`text-gray-300 ${isRTL ? 'text-right' : ''}`}>
                  {businessIdea.market_opportunity}
                </p>
              </div>
            )}

            {businessIdea.business_model && (
              <div className="bg-gray-800/50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-white mb-4">
                  {t('incubator.businessIdea.businessModel')}
                </h3>
                <p className={`text-gray-300 ${isRTL ? 'text-right' : ''}`}>
                  {businessIdea.business_model}
                </p>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Owner Info */}
            <div className="bg-gray-800/50 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-4">
                {t('incubator.businessIdea.owner')}
              </h3>
              <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className={`w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center ${isRTL ? 'ml-3' : 'mr-3'}`}>
                  <span className="text-white font-medium">
                    {businessIdea.owner.first_name?.[0] || businessIdea.owner.username[0]}
                  </span>
                </div>
                <div>
                  <p className="text-white font-medium">
                    {businessIdea.owner.first_name && businessIdea.owner.last_name
                      ? `${businessIdea.owner.first_name} ${businessIdea.owner.last_name}`
                      : businessIdea.owner.username
                    }
                  </p>
                  <p className="text-gray-400 text-sm">{businessIdea.owner.email}</p>
                </div>
              </div>
            </div>

            {/* Collaborators */}
            {businessIdea.collaborators && businessIdea.collaborators.length > 0 && (
              <div className="bg-gray-800/50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                  <Users size={20} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                  {t('incubator.businessIdea.collaborators')}
                </h3>
                <div className="space-y-3">
                  {businessIdea.collaborators.map((collaborator) => (
                    <div key={collaborator.id} className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <div className={`w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center ${isRTL ? 'ml-3' : 'mr-3'}`}>
                        <span className="text-white text-sm">
                          {collaborator.first_name?.[0] || collaborator.username[0]}
                        </span>
                      </div>
                      <div>
                        <p className="text-white text-sm">
                          {collaborator.first_name && collaborator.last_name
                            ? `${collaborator.first_name} ${collaborator.last_name}`
                            : collaborator.username
                          }
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Progress Updates */}
            <div className="bg-gray-800/50 rounded-lg p-6">
              <div className={`flex items-center justify-between mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <h3 className="text-lg font-semibold text-white flex items-center">
                  <TrendingUp size={20} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                  {t('incubator.progressUpdates.title')}
                </h3>
                {canEdit && (
                  <button
                    onClick={() => setShowProgressForm(true)}
                    className="text-blue-400 hover:text-blue-300 transition-colors"
                  >
                    <Plus size={20} />
                  </button>
                )}
              </div>
              
              {businessIdea.progress_updates && businessIdea.progress_updates.length > 0 ? (
                <div className="space-y-3">
                  {businessIdea.progress_updates.slice(0, 3).map((update) => (
                    <div key={update.id} className="border-l-2 border-blue-500 pl-4">
                      <h4 className="text-white font-medium">{update.title}</h4>
                      <p className="text-gray-400 text-sm">{update.description}</p>
                      <p className="text-gray-500 text-xs mt-1">
                        {new Date(update.created_at).toLocaleDateString()}
                      </p>
                    </div>
                  ))}
                  {businessIdea.progress_updates.length > 3 && (
                    <button
                      onClick={() => navigate(`/dashboard/business-ideas/${businessIdea.id}/progress`)}
                      className="text-blue-400 hover:text-blue-300 text-sm transition-colors"
                    >
                      {t('common.viewAll')} ({businessIdea.progress_updates.length})
                    </button>
                  )}
                </div>
              ) : (
                <p className="text-gray-400 text-sm">
                  {t('incubator.progressUpdates.noUpdates')}
                </p>
              )}
            </div>

            {/* Dates */}
            <div className="bg-gray-800/50 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                <Calendar size={20} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                {t('common.dates')}
              </h3>
              <div className="space-y-2">
                <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <span className="text-gray-400">{t('common.created')}</span>
                  <span className="text-white">
                    {new Date(businessIdea.created_at).toLocaleDateString()}
                  </span>
                </div>
                <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <span className="text-gray-400">{t('common.updated')}</span>
                  <span className="text-white">
                    {new Date(businessIdea.updated_at).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modals */}
      {showEditForm && (
        <BusinessIdeaForm
          initialData={businessIdea}
          onSubmit={handleUpdateBusinessIdea}
          onCancel={() => setShowEditForm(false)}
          mode="edit"
        />
      )}

      {showProgressForm && (
        <ProgressUpdateForm
          businessIdea={businessIdea}
          onSave={progressCRUD.createItem}
          onCancel={() => setShowProgressForm(false)}
          isLoading={progressCRUD.isLoading}
          mode="create"
        />
      )}

      <ConfirmDialog
        isOpen={showDeleteDialog}
        onClose={() => setShowDeleteDialog(false)}
        onConfirm={handleDeleteBusinessIdea}
        title={t('incubator.businessIdea.deleteConfirmTitle')}
        message={t('incubator.businessIdea.deleteConfirmMessage')}
        confirmText={t('common.delete')}
        type="danger"
      />
    </div>
  );
};

export default BusinessIdeaDetailPage;
