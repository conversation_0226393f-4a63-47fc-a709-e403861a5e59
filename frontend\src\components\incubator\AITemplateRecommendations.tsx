import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  TrendingUp,
  Target,
  Brain,
  Star,
  Clock,
  Users,
  <PERSON>Right,
  RefreshCw,
  Lightbulb,
  BarChart3,
  Zap
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { BusinessPlanTemplate } from '../../services/businessPlanApi';
import { RTLText, RTLFlex } from '../common';

interface AIRecommendation {
  template: BusinessPlanTemplate;
  score: number;
  reasons: string[];
  category: 'perfect_match' | 'good_fit' | 'alternative';
  ai_insights: {
    success_probability: number;
    market_potential: string;
    complexity_level: string;
    time_to_market: string;
  };
}

interface AITemplateRecommendationsProps {
  businessIdea?: {
    id: number;
    title: string;
    description: string;
    industry: string;
    target_market: string;
  };
  userProfile?: {
    experience_level: string;
    industry_background: string[];
    previous_plans: number;
  };
  onSelectTemplate: (template: BusinessPlanTemplate) => void;
}

export const AITemplateRecommendations: React.FC<AITemplateRecommendationsProps> = ({ businessIdea,
  userProfile,
  onSelectTemplate
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [recommendations, setRecommendations] = useState<AIRecommendation[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  useEffect(() => {
    if (businessIdea) {
      fetchAIRecommendations();
    }
  }, [businessIdea]);

  const fetchAIRecommendations = async () => {
    setLoading(true);
    setError(null);

    try {
      // Empty AI recommendations
      const mockRecommendations: AIRecommendation[] = [];

      setRecommendations(mockRecommendations);
    } catch (err) {
      console.error('Error fetching AI recommendations:', err);
      setError(t("ai.failed.to.load", "Failed to load AI recommendations"));
    } finally {
      setLoading(false);
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'perfect_match': return <Target className="text-green-400" size={20} />;
      case 'good_fit': return <TrendingUp className="text-blue-400" size={20} />;
      case 'alternative': return <Lightbulb className="text-yellow-400" size={20} />;
      default: return <Brain className="text-purple-400" size={20} />;
    }
  };

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'perfect_match': return t('ai.perfectMatch');
      case 'good_fit': return t('ai.goodFit');
      case 'alternative': return t('ai.alternative');
      default: return category;
    }
  };

  const getSuccessProbabilityColor = (probability: number) => {
    if (probability >= 80) return 'text-green-400';
    if (probability >= 60) return 'text-yellow-400';
    return 'text-red-400';
  };

  const filteredRecommendations = selectedCategory === 'all'
    ? recommendations
    : recommendations.filter(r => r.category === selectedCategory);

  if (loading) {
    return (
      <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
        <RTLFlex className="items-center justify-center py-8">
          <RefreshCw className={`animate-spin text-purple-500 mr-3 ${isRTL ? "space-x-reverse" : ""}`} size={24} />
          <RTLText>{t('ai.generatingRecommendations')}</RTLText>
        </RTLFlex>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
        <RTLText className="text-red-400 text-center">{error}</RTLText>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-900/50 to-blue-900/50 rounded-lg p-6 border border-purple-500/30">
        <RTLFlex className="items-center mb-4">
          <Sparkles className={`text-purple-400 mr-3 ${isRTL ? "space-x-reverse" : ""}`} size={24} />
          <RTLText as="h3" className="text-xl font-bold">
            {t('ai.smartTemplateRecommendations')}
          </RTLText>
        </RTLFlex>

        <RTLText className="text-gray-300 mb-4">
          {t('ai.recommendationsDescription')}
        </RTLText>

        {businessIdea && (
          <div className="bg-gray-800/50 rounded-lg p-4">
            <RTLText className="text-sm text-gray-400 mb-2">
              {t('ai.analyzingBusinessIdea')}:
            </RTLText>
            <RTLText className="font-medium">{businessIdea.title}</RTLText>
            <RTLText className="text-sm text-gray-400 mt-1">
              {businessIdea.industry} • {businessIdea.target_market}
            </RTLText>
          </div>
        )}
      </div>

      {/* Category Filters */}
      <div className={`flex flex-wrap gap-2 ${isRTL ? "flex-row-reverse" : ""}`}>
        {[
          { key: 'all', label: t('ai.allRecommendations'), icon: Brain },
          { key: 'perfect_match', label: t('ai.perfectMatch'), icon: Target },
          { key: 'good_fit', label: t('ai.goodFit'), icon: TrendingUp },
          { key: 'alternative', label: t('ai.alternatives'), icon: Lightbulb }
        ].map(({ key, label, icon: Icon }) => (
          <button
            key={key}
            onClick={() => setSelectedCategory(key)}
            className={`px-4 py-2 rounded-lg border transition-colors flex items-center ${
              selectedCategory === key
                ? 'bg-purple-600 border-purple-500 text-white'
                : 'bg-gray-800 border-gray-600 text-gray-300 hover:border-purple-500'}
            }`}
          >
            <Icon size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
            {label}
          </button>
        ))}
      </div>

      {/* Recommendations */}
      <div className="space-y-4">
        {filteredRecommendations.map((recommendation, index) => (
          <div
            key={recommendation.template.id}
            className="bg-gray-800/50 rounded-lg p-6 border border-gray-700 hover:border-purple-500/50 transition-all duration-300"
          >
            {/* Header */}
            <RTLFlex className="items-start justify-between mb-4">
              <div className={`flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                <RTLFlex className="items-center mb-2">
                  {getCategoryIcon(recommendation.category)}
                  <span className={`text-sm text-gray-400 ml-2 ${isRTL ? "space-x-reverse" : ""}`}>
                    {getCategoryLabel(recommendation.category)}
                  </span>
                  <div className={`bg-purple-600/20 text-purple-300 px-2 py-1 rounded-full text-xs ml-3 ${isRTL ? "space-x-reverse" : ""}`}>
                    {recommendation.score}% {t('ai.match')}
                  </div>
                </RTLFlex>

                <RTLText as="h4" className="text-lg font-semibold mb-2">
                  {recommendation.template.name}
                </RTLText>

                <RTLText className="text-gray-300 text-sm mb-3">
                  {recommendation.template.description}
                </RTLText>
              </div>

              <RTLFlex className="items-center gap-4 text-sm text-gray-400">
                <RTLFlex className="items-center">
                  <Star className="text-yellow-400 fill-current" size={16} />
                  <span className={`ml-1 ${isRTL ? "space-x-reverse" : ""}`}>{recommendation.template.rating}</span>
                </RTLFlex>
                <RTLFlex className="items-center">
                  <Clock size={16} className={isRTL ? 'ml-1' : 'mr-1'} />
                  <span>{recommendation.template.estimated_time}h</span>
                </RTLFlex>
                <RTLFlex className="items-center">
                  <Users size={16} className={isRTL ? 'ml-1' : 'mr-1'} />
                  <span>{recommendation.template.usage_count}</span>
                </RTLFlex>
              </RTLFlex>
            </RTLFlex>

            {/* AI Insights */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
              <div className="bg-gray-700/50 rounded-lg p-3 text-center">
                <RTLText className="text-xs text-gray-400 mb-1">
                  {t('ai.successProbability')}
                </RTLText>
                <RTLText className={`text-lg font-bold ${getSuccessProbabilityColor(recommendation.ai_insights.success_probability)}`}>
                  {recommendation.ai_insights.success_probability}%
                </RTLText>
              </div>

              <div className="bg-gray-700/50 rounded-lg p-3 text-center">
                <RTLText className="text-xs text-gray-400 mb-1">
                  {t('ai.marketPotential')}
                </RTLText>
                <RTLText className="text-sm font-medium">
                  {recommendation.ai_insights.market_potential}
                </RTLText>
              </div>

              <div className="bg-gray-700/50 rounded-lg p-3 text-center">
                <RTLText className="text-xs text-gray-400 mb-1">
                  {t('ai.complexity')}
                </RTLText>
                <RTLText className="text-sm font-medium">
                  {recommendation.ai_insights.complexity_level}
                </RTLText>
              </div>

              <div className="bg-gray-700/50 rounded-lg p-3 text-center">
                <RTLText className="text-xs text-gray-400 mb-1">
                  {t('ai.timeToMarket')}
                </RTLText>
                <RTLText className="text-sm font-medium">
                  {recommendation.ai_insights.time_to_market}
                </RTLText>
              </div>
            </div>

            {/* Reasons */}
            <div className="mb-4">
              <RTLText className="text-sm font-medium text-gray-300 mb-2">
                {t('ai.whyRecommended')}:
              </RTLText>
              <ul className="space-y-1">
                {recommendation.reasons.map((reason, reasonIndex) => (
                  <li key={reasonIndex} className={`text-sm text-gray-400 flex items-start ${isRTL ? "flex-row-reverse" : ""}`}>
                    <Zap size={12} className={`text-purple-400 mt-1 mr-2 flex-shrink-0 ${isRTL ? "flex-row-reverse" : ""}`} />
                    {reason}
                  </li>
                ))}
              </ul>
            </div>

            {/* Actions */}
            <RTLFlex className="items-center justify-between">
              <div className={`flex flex-wrap gap-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                {recommendation.template.tags.slice(0, 3).map((tag, tagIndex) => (
                  <span
                    key={tagIndex}
                    className="px-2 py-1 bg-gray-600/50 text-gray-300 rounded text-xs"
                  >
                    {tag}
                  </span>
                ))}
              </div>

              <button
                onClick={() => onSelectTemplate(recommendation.template)}
                className={`px-6 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg text-white font-medium transition-colors flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
              >
                {t('templates.useTemplate')}
                <ArrowRight size={16} className={isRTL ? 'mr-2' : 'ml-2'} />
              </button>
            </RTLFlex>
          </div>
        ))}
      </div>

      {filteredRecommendations.length === 0 && (
        <div className="text-center py-8">
          <Brain className="mx-auto mb-4 text-gray-400" size={48} />
          <RTLText className="text-gray-400">
            {t('ai.noRecommendationsFound')}
          </RTLText>
        </div>
      )}
    </div>
  );
};
