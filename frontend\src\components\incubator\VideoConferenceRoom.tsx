import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Video,
  Mic,
  MicOff,
  VideoOff,
  Phone,
  MessageSquare,
  Users,
  Settings,
  Copy,
  ExternalLink,
  Clock,
  Check
} from 'lucide-react';
import { useAppDispatch, useAppSelector } from '../../store/hooks';
import {
  fetchMentorshipSession,
  updateMentorshipSession
} from '../../store/incubatorSlice';
import { MentorshipSession } from '../../services/incubatorApi';
import { useTranslation } from 'react-i18next';

interface VideoConferenceRoomProps {
  sessionId?: number;
  onExit?: () => void;
}

const VideoConferenceRoom: React.FC<VideoConferenceRoomProps> = ({ sessionId: propSessionId,
  onExit
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { sessionId: paramSessionId } = useParams<{ sessionId: string }>();
  const sessionId = propSessionId || (paramSessionId ? parseInt(paramSessionId) : undefined);

  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { selectedMentorshipSession, isLoading, error } = useAppSelector(state => state.incubator);

  const [session, setSession] = useState<MentorshipSession | null>(null);
  const [isVideoOn, setIsVideoOn] = useState(true);
  const [isAudioOn, setIsAudioOn] = useState(true);
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);
  const [elapsedTime, setElapsedTime] = useState(0);

  const videoContainerRef = useRef<HTMLDivElement>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Fetch session data
  useEffect(() => {
    if (sessionId) {
      dispatch(fetchMentorshipSession(sessionId));
    }
  }, [dispatch, sessionId]);

  // Set session from redux state
  useEffect(() => {
    if (selectedMentorshipSession) {
      setSession(selectedMentorshipSession);

      // If session is in progress, start the timer
      if (selectedMentorshipSession.status === 'in_progress' && selectedMentorshipSession.started_at) {
        const startTime = new Date(selectedMentorshipSession.started_at).getTime();
        const now = new Date().getTime();
        const initialElapsed = Math.floor((now - startTime) / 1000);
        setElapsedTime(initialElapsed > 0 ? initialElapsed : 0);
      }
    }
  }, [selectedMentorshipSession]);

  // Start session timer
  useEffect(() => {
    if (session?.status === 'in_progress') {
      timerRef.current = setInterval(() => {
        setElapsedTime(prev => prev + 1);
      }, 1000);
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [session?.status]);

  // Format elapsed time as HH:MM:SS
  const formatElapsedTime = () => {
    const hours = Math.floor(elapsedTime / 3600);
    const minutes = Math.floor((elapsedTime % 3600) / 60);
    const seconds = elapsedTime % 60;

    return [
      hours.toString().padStart(2, '0'),
      minutes.toString().padStart(2, '0'),
      seconds.toString().padStart(2, '0')
    ].join(':');
  };

  // Handle starting the session
  const handleStartSession = async () => {
    if (!session) return;

    try {
      await dispatch(updateMentorshipSession({
        id: session.id,
        action: 'start_session'
      })).unwrap();
    } catch (err) {
      console.error('Error starting session:', err);
    }
  };

  // Handle ending the session
  const handleEndSession = async () => {
    if (!session) return;

    try {
      await dispatch(updateMentorshipSession({
        id: session.id,
        action: 'complete_session'
      })).unwrap();

      // Navigate to feedback page or call onExit
      if (onExit) {
        onExit();
      } else {
        navigate(`/dashboard/mentorship/sessions/${session.id}/feedback`);
      }
    } catch (err) {
      console.error('Error ending session:', err);
    }
  };

  // Handle copying meeting link
  const handleCopyLink = () => {
    if (!session?.meeting_link) return;

    navigator.clipboard.writeText(session.meeting_link)
      .then(() => {
        setCopySuccess(true);
        setTimeout(() => setCopySuccess(false), 2000);
      })
      .catch(err => {
        console.error("Failed to copy link:", err);
      });
  };

  // Handle opening meeting in new tab
  const handleOpenInNewTab = () => {
    if (!session?.meeting_link) return;
    window.open(session.meeting_link, '_blank');
  };

  // Toggle fullscreen
  const toggleFullscreen = () => {
    if (!videoContainerRef.current) return;

    if (!document.fullscreenElement) {
      videoContainerRef.current.requestFullscreen().catch(err => {
        console.error('Error attempting to enable fullscreen:', err);
      });
    } else {
      document.exitFullscreen();
    }
  };

  // Update fullscreen state when fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  if (isLoading && !session) {
    return (
      <div className={`flex justify-center items-center h-64 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className="w-12 h-12 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  if (error || !sessionId) {
    return (
      <div className="bg-red-900/30 text-red-200 p-4 rounded-lg">
        <h3 className="font-bold">{t("common.error.loading.session", "Error Loading Session")}</h3>
        <div>{error || t("common.session.id.is", "Session ID is required")}</div>
      </div>
    );
  }

  if (!session) {
    return (
      <div className="bg-indigo-900/30 text-gray-200 p-4 rounded-lg">
        <h3 className="font-bold">{t("common.session.not.found", "Session Not Found")}</h3>
        <div>{t("common.the.requested.mentorship", "The requested mentorship session could not be found.")}</div>
      </div>
    );
  }

  return (
    <div className="bg-indigo-950 rounded-lg overflow-hidden border border-indigo-800">
      {/* Session Header */}
      <div className={`bg-indigo-900 p-4 flex justify-between items-center ${isRTL ? "flex-row-reverse" : ""}`}>
        <div>
          <h2 className="font-bold text-lg">{session.title}</h2>
          <div className="text-sm text-gray-300">
            {new Date(session.scheduled_at).toLocaleString()}
            {session.status === 'in_progress' && (
              <span className={`ml-2 bg-green-900 text-green-200 px-2 py-0.5 rounded-full text-xs ${isRTL ? "space-x-reverse" : ""}`}>
                In Progress
              </span>
            )}
          </div>
        </div>

        <div className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
          {session.status === 'in_progress' && (
            <div className={`flex items-center bg-indigo-800 px-3 py-1 rounded-md ${isRTL ? "flex-row-reverse" : ""}`}>
              <Clock size={16} className={`mr-1 text-gray-300 ${isRTL ? "space-x-reverse" : ""}`} />
              <span className="font-mono">{formatElapsedTime()}</span>
            </div>
          )}

          <button
            onClick={handleCopyLink}
            className="bg-indigo-800 hover:bg-indigo-700 p-2 rounded-md"
            title={t("common.copy.meeting.link", "Copy meeting link")}
          >
            {copySuccess ? <Check size={18} className="text-green-400" /> : <Copy size={18} />}
          </button>

          <button
            onClick={handleOpenInNewTab}
            className="bg-indigo-800 hover:bg-indigo-700 p-2 rounded-md"
            title={t("common.open.in.new", "Open in new tab")}
          >
            <ExternalLink size={18} />
          </button>
        </div>
      </div>

      {/* Video Container */}
      <div
        ref={videoContainerRef}
        className={`relative bg-black h-[60vh] flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}
      >
        {session.meeting_link ? (
          <iframe
            src={session.meeting_link}
            allow="camera; microphone; fullscreen; display-capture; autoplay"
            className="w-full h-full border-0"
            title={t("common.video.conference", "Video Conference")}
          />
        ) : (
          <div className="text-center p-6">
            <Video size={48} className="mx-auto mb-4 text-gray-500" />
            <h3 className="text-xl font-bold mb-2">{t("common.no.meeting.link", "No Meeting Link Available")}</h3>
            <div className="text-gray-400 mb-4">
              This session doesn't have a video conference link yet.
            </div>

            {session.status === 'scheduled' && (
              <button
                onClick={() => {/* Create meeting link */}}
                className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white"
              >
                Create Meeting Link
              </button>
            )}
          </div>
        )}
      </div>

      {/* Controls */}
      <div className={`bg-indigo-900 p-4 flex justify-between items-center ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`flex space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
          <button
            onClick={() => setIsAudioOn(!isAudioOn)}
            className={`p-3 rounded-full ${isAudioOn ? 'bg-indigo-700' : 'bg-red-700'}`}
          >
            {isAudioOn ? <Mic size={20} /> : <MicOff size={20} />}
          </button>

          <button
            onClick={() => setIsVideoOn(!isVideoOn)}
            className={`p-3 rounded-full ${isVideoOn ? 'bg-indigo-700' : 'bg-red-700'}`}
          >
            {isVideoOn ? <Video size={20} /> : <VideoOff size={20} />}
          </button>

          <button
            onClick={() => setIsChatOpen(!isChatOpen)}
            className={`p-3 rounded-full ${isChatOpen ? 'bg-purple-700' : 'bg-indigo-700'}`}
          >
            <MessageSquare size={20} />
          </button>

          <button
            onClick={toggleFullscreen}
            className={`p-3 rounded-full ${isFullscreen ? 'bg-purple-700' : 'bg-indigo-700'}`}
          >
            <Settings size={20} />
          </button>
        </div>

        <div>
          {session.status === 'scheduled' || session.status === 'rescheduled' ? (
            <button
              onClick={handleStartSession}
              className={`px-4 py-2 bg-green-600 hover:bg-green-700 rounded-md text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
            >
              <Video size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
              Start Session
            </button>
          ) : session.status === 'in_progress' ? (
            <button
              onClick={handleEndSession}
              className={`px-4 py-2 bg-red-600 hover:bg-red-700 rounded-md text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
            >
              <Phone size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
              End Session
            </button>
          ) : null}
        </div>
      </div>
    </div>
  );
};

export default VideoConferenceRoom;
