from rest_framework import permissions

class IsAdminUser(permissions.BasePermission):
    """
    Custom permission to only allow admin users to access the view.
    """
    def has_permission(self, request, view):
        return bool(request.user and request.user.is_authenticated and
                   (request.user.is_staff or request.user.is_superuser))


class IsOwnerOrAdmin(permissions.BasePermission):
    """
    Custom permission to only allow owners of an object or admins to access it.
    """
    def has_object_permission(self, request, view, obj):
        # Admin permissions
        if request.user.is_staff or request.user.is_superuser:
            return True

        # Check if the object has an owner field
        if hasattr(obj, 'owner'):
            return obj.owner == request.user

        # Check if the object has a business_idea field with an owner
        if hasattr(obj, 'business_idea'):
            # Check if the user is the owner of the business idea
            if obj.business_idea.owner == request.user:
                return True

            # Check if the user is a collaborator on the business idea
            if hasattr(obj.business_idea, 'collaborators'):
                return obj.business_idea.collaborators.filter(id=request.user.id).exists()

        return False
