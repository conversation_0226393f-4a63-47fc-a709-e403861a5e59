import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { ArrowLeft, Calendar, Users, AlertCircle, Info, Clock, Video } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { useAppDispatch, useAppSelector } from '../../store/hooks';
// MainLayout removed - handled by routing system
import { MentorshipSessionScheduler } from '../../components/incubator';
import { fetchMentorshipMatches } from '../../store/incubatorSlice';

const MentorshipSessionSchedulePage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const matchIdParam = searchParams.get('match');

  const { mentorshipMatches, isLoading, error } = useAppSelector(state => state.incubator);
  const [selectedMatchId, setSelectedMatchId] = useState<number | undefined>(
    matchIdParam ? parseInt(matchIdParam) : undefined
  );
  const [showInstructions, setShowInstructions] = useState(true);

  useEffect(() => {
    dispatch(fetchMentorshipMatches());
  }, [dispatch]);

  const handleScheduleSuccess = () => {
    navigate('/dashboard/mentorship/sessions');
  };

  // Get selected match details for context
  const selectedMatch = selectedMatchId
    ? mentorshipMatches.find(match => match.id === selectedMatchId)
    : null;

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="p-6 space-y-6">
        {/* Header */}
        <div className={`flex flex-col sm:flex-row sm:items-center sm:justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
          <div>
            <div className="mb-4">
              <Link
                to="/dashboard/mentorship/sessions"
                className={`text-gray-400 hover:text-gray-300 flex items-center transition-colors ${isRTL ? 'flex-row-reverse' : ''}`}
              >
                <ArrowLeft size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
                {t('mentorship.backToSessions')}
              </Link>
            </div>
            <h1 className="text-2xl font-bold text-white mb-2">
              {t('mentorship.scheduleSession')}
            </h1>
            <p className="text-gray-300">
              {t('mentorship.scheduleSessionDescription')}
            </p>
          </div>
        </div>

        {/* Selected Match Context */}
        {selectedMatch && (
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
            <div className={`flex items-center mb-3 ${isRTL ? "flex-row-reverse" : ""}`}>
              <Users size={20} className={`text-purple-400 mr-3 ${isRTL ? "space-x-reverse" : ""}`} />
              <h3 className="text-lg font-semibold text-white">
                {t('mentorship.schedulingWith')}
              </h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-400 mb-1">{t('mentorship.mentor')}</p>
                <p className="text-white font-medium">
                  {selectedMatch.mentor.user.first_name} {selectedMatch.mentor.user.last_name}
                </p>
                <p className="text-gray-300 text-sm">
                  {selectedMatch.mentor.expertise.join(', ')}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-400 mb-1">{t('mentorship.businessIdea')}</p>
                <p className="text-white font-medium">
                  {selectedMatch.business_idea_title}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Instructions */}
        {showInstructions && (
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
            <div className={`flex items-start justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
              <div className={`flex items-start ${isRTL ? "flex-row-reverse" : ""}`}>
                <Info size={20} className={`text-blue-400 mr-3 mt-0.5 flex-shrink-0 ${isRTL ? "flex-row-reverse" : ""}`} />
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">
                    {t('mentorship.schedulingInstructions')}
                  </h3>
                  <ul className="text-gray-300 space-y-2 text-sm">
                    <li className={`flex items-start ${isRTL ? "flex-row-reverse" : ""}`}>
                      <div className={`w-1.5 h-1.5 bg-blue-400 rounded-full mr-3 mt-2 flex-shrink-0 ${isRTL ? "flex-row-reverse" : ""}`}></div>
                      {t('mentorship.instruction1')}
                    </li>
                    <li className={`flex items-start ${isRTL ? "flex-row-reverse" : ""}`}>
                      <div className={`w-1.5 h-1.5 bg-blue-400 rounded-full mr-3 mt-2 flex-shrink-0 ${isRTL ? "flex-row-reverse" : ""}`}></div>
                      {t('mentorship.instruction2')}
                    </li>
                    <li className={`flex items-start ${isRTL ? "flex-row-reverse" : ""}`}>
                      <div className={`w-1.5 h-1.5 bg-blue-400 rounded-full mr-3 mt-2 flex-shrink-0 ${isRTL ? "flex-row-reverse" : ""}`}></div>
                      {t('mentorship.instruction3')}
                    </li>
                    <li className={`flex items-start ${isRTL ? "flex-row-reverse" : ""}`}>
                      <div className={`w-1.5 h-1.5 bg-blue-400 rounded-full mr-3 mt-2 flex-shrink-0 ${isRTL ? "flex-row-reverse" : ""}`}></div>
                      {t('mentorship.instruction4')}
                    </li>
                  </ul>
                </div>
              </div>
              <button
                onClick={() => setShowInstructions(false)}
                className="text-gray-400 hover:text-gray-300 p-1"
                title={t('common.close')}
              >
                <ArrowLeft size={16} className="rotate-90" />
              </button>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-900/50 backdrop-blur-sm rounded-lg p-6 border border-red-800/50">
            <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <AlertCircle size={20} className={`text-red-400 mr-3 ${isRTL ? "space-x-reverse" : ""}`} />
              <div>
                <h3 className="text-lg font-semibold text-white mb-1">
                  {t('common.error')}
                </h3>
                <p className="text-gray-300">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Loading State */}
        {isLoading && mentorshipMatches.length === 0 ? (
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-12 border border-white/20">
            <div className="text-center">
              <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
              <h3 className="text-lg font-semibold text-white mb-2">
                {t('mentorship.loadingMatches')}
              </h3>
              <p className="text-gray-400">
                {t('mentorship.loadingMatchesDescription')}
              </p>
            </div>
          </div>
        ) : (
          /* Scheduler Component */
          <MentorshipSessionScheduler
            mentorshipMatchId={selectedMatchId}
            onSuccess={handleScheduleSuccess}
          />
        )}

        {/* Quick Actions */}
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
          <h3 className="text-lg font-semibold text-white mb-4">
            {t('mentorship.quickActions')}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link
              to="/dashboard/mentorship/sessions"
              className={`flex items-center p-4 bg-white/20 hover:bg-white/30 rounded-lg transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
            >
              <Calendar size={20} className={`text-purple-400 mr-3 ${isRTL ? "space-x-reverse" : ""}`} />
              <div>
                <p className="font-medium text-white">{t('mentorship.viewSessions')}</p>
                <p className="text-sm text-gray-400">{t('mentorship.manageSessions')}</p>
              </div>
            </Link>

            <Link
              to="/dashboard/mentorship"
              className={`flex items-center p-4 bg-white/20 hover:bg-white/30 rounded-lg transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
            >
              <Users size={20} className={`text-blue-400 mr-3 ${isRTL ? "space-x-reverse" : ""}`} />
              <div>
                <p className="font-medium text-white">{t('mentorship.dashboard')}</p>
                <p className="text-sm text-gray-400">{t('mentorship.overviewAndMatches')}</p>
              </div>
            </Link>

            <Link
              to="/dashboard/mentorship/apply"
              className={`flex items-center p-4 bg-white/20 hover:bg-white/30 rounded-lg transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
            >
              <Video size={20} className={`text-green-400 mr-3 ${isRTL ? "space-x-reverse" : ""}`} />
              <div>
                <p className="font-medium text-white">{t('mentorship.findMentor')}</p>
                <p className="text-sm text-gray-400">{t('mentorship.applyForMentorship')}</p>
              </div>
            </Link>
          </div>
        </div>
      </div>
              </div>
        </div>
      </div>
    </div>
  );
};

export default MentorshipSessionSchedulePage;
