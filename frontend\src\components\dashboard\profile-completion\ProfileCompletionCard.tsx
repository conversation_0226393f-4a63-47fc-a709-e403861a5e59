import React from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../../../store/hooks';
import { useLanguage } from '../../../hooks/useLanguage';
import { User, ArrowRight } from 'lucide-react';
import { useProfileCompletion } from './hooks';
import {
  CompletionProgressBar,
  MissingFieldsList,
  CompletionStatus
} from './components';

const ProfileCompletionCard: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL, language } = useLanguage();
  const {
    profile,
    loading,
    error,
    checklist,
    completedItems,
    completionPercentage,
    missingHighImportanceItems,
    completionStatus
  } = useProfileCompletion();

  if (loading) {
    return (
      <div className={`bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50 flex justify-center ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className="w-8 h-8 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  if (error || !profile) {
    return (
      <div className="bg-red-900/30 backdrop-blur-sm rounded-lg p-4 border border-red-800/50">
        <div className="text-red-400">{error || t("dashboard.profile.data.not", "Profile data not available")}</div>
      </div>
    );
  }

  return (
    <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50">
      <div className={`flex items-center justify-between mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
          <User size={20} className={`${language === 'ar' ? 'ml-2' : 'mr-2'} text-purple-400`} />
          <h3 className="font-semibold">{t('dashboard.profileCompletion.title')}</h3>
        </div>
        <span className={`${completionStatus.color} font-medium`}>
          {t(`dashboard.profileCompletion.${completionStatus.text}`)}
        </span>
      </div>

      {/* Progress Bar */}
      <CompletionProgressBar
        percentage={completionPercentage}
        statusColor={completionStatus.bgColor}
      />

      {/* Missing Items */}
      <MissingFieldsList missingFields={missingHighImportanceItems} />

      {/* Completed Items */}
      <CompletionStatus
        completedItems={completedItems}
        totalItems={checklist.length}
      />

      {/* Action Button */}
      <Link
        to="/dashboard/profile"
        className={`w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}
      >
        {t('dashboard.profileCompletion.completeYourProfile')} <ArrowRight size={16} className={language === 'ar' ? 'mr-1' : 'ml-1'} />
      </Link>
    </div>
  );
};

export default ProfileCompletionCard;
