import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '../../ui/card';
import Button from '../../ui/Button';
import { Badge } from '../../ui/badge';
import { DollarSign, TrendingUp, TrendingDown, PieChart, Target, Eye, Plus, ArrowRight, Briefcase, BarChart3 } from 'lucide-react';
import { Link } from 'react-router-dom';

interface InvestorStats {
  portfolioValue: number;
  totalInvested: number;
  totalReturn: number;
  returnPercentage: number;
  activeInvestments: number;
  pendingDeals: number;
  monthlyReturn: number;
  irr: number;
}

interface Investment {
  id: string;
  companyName: string;
  industry: string;
  stage: string;
  investmentAmount: number;
  currentValue: number;
  performance: number;
  lastUpdate: string;
  status: 'active' | 'exited' | 'at_risk';
}

interface Deal {
  id: string;
  companyName: string;
  industry: string;
  stage: string;
  fundingRound: string;
  askAmount: number;
  valuation: number;
  deadline: string;
  status: 'reviewing' | 'due_diligence' | 'negotiating' | 'closed';
}

interface QuickAction {
  title: string;
  description: string;
  icon: React.ReactNode;
  link: string;
  count?: number;
  highlight?: boolean;
}

const InvestorDashboard: React.FC = () => {
  const [stats, setStats] = useState<InvestorStats | null>(null);
  const [topInvestments, setTopInvestments] = useState<Investment[]>([]);
  const [pendingDeals, setPendingDeals] = useState<Deal[]>([]);
  const [loading, setLoading] = useState(true);

  // Mock data - replace with actual API calls
  useEffect(() => {
    const mockStats: InvestorStats = {
      portfolioValue: 2850000,
      totalInvested: 2100000,
      totalReturn: 750000,
      returnPercentage: 35.7,
      activeInvestments: 12,
      pendingDeals: 4,
      monthlyReturn: 2.8,
      irr: 28.5
    };

    const mockInvestments: Investment[] = [
      {
        id: '1',
        companyName: 'TechStart AI',
        industry: 'Artificial Intelligence',
        stage: 'Series A',
        investmentAmount: 250000,
        currentValue: 420000,
        performance: 68,
        lastUpdate: '2024-01-15',
        status: 'active'
      },
      {
        id: '2',
        companyName: 'HealthTech Pro',
        industry: 'Healthcare',
        stage: 'Series B',
        investmentAmount: 500000,
        currentValue: 850000,
        performance: 70,
        lastUpdate: '2024-01-12',
        status: 'active'
      },
      {
        id: '3',
        companyName: 'GreenEnergy Solutions',
        industry: 'Clean Energy',
        stage: 'Seed',
        investmentAmount: 150000,
        currentValue: 180000,
        performance: 20,
        lastUpdate: '2024-01-10',
        status: 'active'
      }
    ];

    const mockDeals: Deal[] = [
      {
        id: '1',
        companyName: 'FinTech Innovations',
        industry: 'Financial Technology',
        stage: 'Series A',
        fundingRound: 'Series A',
        askAmount: 5000000,
        valuation: 25000000,
        deadline: '2024-01-25',
        status: 'due_diligence'
      },
      {
        id: '2',
        companyName: 'EduTech Platform',
        industry: 'Education Technology',
        stage: 'Seed',
        fundingRound: 'Seed',
        askAmount: 2000000,
        valuation: 8000000,
        deadline: '2024-01-30',
        status: 'reviewing'
      }
    ];

    setTimeout(() => {
      setStats(mockStats);
      setTopInvestments(mockInvestments);
      setPendingDeals(mockDeals);
      setLoading(false);
    }, 1000);
  }, []);

  const quickActions: QuickAction[] = [
    {
      title: 'Browse Opportunities',
      description: 'Discover new investment opportunities',
      icon: <Eye className="w-6 h-6" />,
      link: '/dashboard/investments/opportunities',
      highlight: true
    },
    {
      title: 'Portfolio Management',
      description: 'Manage your investment portfolio',
      icon: <Briefcase className="w-6 h-6" />,
      link: '/dashboard/investments/portfolio',
      count: stats?.activeInvestments
    },
    {
      title: 'Due Diligence',
      description: 'Review pending investments',
      icon: <Target className="w-6 h-6" />,
      link: '/dashboard/investments/due-diligence',
      count: stats?.pendingDeals
    },
    {
      title: 'Analytics',
      description: 'View investment analytics',
      icon: <BarChart3 className="w-6 h-6" />,
      link: '/dashboard/investments/analytics'
    },
    {
      title: 'Profile Settings',
      description: 'Update investment preferences',
      icon: <DollarSign className="w-6 h-6" />,
      link: '/dashboard/investments/profile'
    },
    {
      title: 'Market Research',
      description: 'Industry trends and insights',
      icon: <TrendingUp className="w-6 h-6" />,
      link: '/dashboard/investments/analytics'
    }
  ];

  const getPerformanceColor = (performance: number) => {
    if (performance >= 50) return 'text-green-600';
    if (performance >= 20) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'exited': return 'bg-blue-100 text-blue-800';
      case 'at_risk': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getDealStatusColor = (status: string) => {
    switch (status) {
      case 'reviewing': return 'bg-yellow-100 text-yellow-800';
      case 'due_diligence': return 'bg-blue-100 text-blue-800';
      case 'negotiating': return 'bg-purple-100 text-purple-800';
      case 'closed': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Investor Dashboard</h1>
          <p className="text-gray-600 mt-1">Track your investments and discover new opportunities</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <BarChart3 className="w-4 h-4 mr-2" />
            Market Report
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700">
            <Plus className="w-4 h-4 mr-2" />
            New Investment
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Portfolio Value</p>
                  <p className="text-2xl font-bold text-blue-600">{formatCurrency(stats.portfolioValue)}</p>
                  <p className="text-sm text-green-600">+{stats.returnPercentage.toFixed(1)}% total return</p>
                </div>
                <DollarSign className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Return</p>
                  <p className="text-2xl font-bold text-green-600">{formatCurrency(stats.totalReturn)}</p>
                  <p className="text-sm text-green-600">+{stats.monthlyReturn}% this month</p>
                </div>
                <TrendingUp className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">IRR</p>
                  <p className="text-2xl font-bold text-purple-600">{stats.irr}%</p>
                  <p className="text-sm text-purple-600">Internal Rate of Return</p>
                </div>
                <Target className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Investments</p>
                  <p className="text-2xl font-bold text-orange-600">{stats.activeInvestments}</p>
                  <p className="text-sm text-gray-600">{stats.pendingDeals} pending deals</p>
                </div>
                <PieChart className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Quick Actions */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {quickActions.map((action, index) => (
                  <Link key={index} to={action.link}>
                    <div className={`p-4 border rounded-lg hover:shadow-md transition-shadow cursor-pointer ${
                      action.highlight ? 'border-blue-300 bg-blue-50' : ''
                    }`}>
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-3">
                          <div className={`p-2 rounded-lg ${
                            action.highlight ? 'bg-blue-200' : 'bg-gray-100'
                          }`}>
                            {action.icon}
                          </div>
                          <div>
                            <h3 className="font-semibold">{action.title}</h3>
                            <p className="text-sm text-gray-600">{action.description}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {action.count !== undefined && (
                            <Badge className="bg-blue-100 text-blue-800">
                              {action.count}
                            </Badge>
                          )}
                          <ArrowRight className="w-4 h-4 text-gray-400" />
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Pending Deals */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Pending Deals</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {pendingDeals.map((deal) => (
                  <div key={deal.id} className="p-3 border rounded">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-sm">{deal.companyName}</h4>
                      <Badge className={getDealStatusColor(deal.status)}>
                        {deal.status.replace('_', ' ')}
                      </Badge>
                    </div>
                    <p className="text-xs text-gray-600 mb-2">{deal.industry} • {deal.stage}</p>
                    <div className="text-xs text-gray-600">
                      <p>Ask: {formatCurrency(deal.askAmount)}</p>
                      <p>Valuation: {formatCurrency(deal.valuation)}</p>
                      <p>Deadline: {formatDate(deal.deadline)}</p>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-4">
                <Link to="/dashboard/investments/opportunities">
                  <Button variant="outline" className="w-full">
                    View All Opportunities
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Top Performing Investments */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Top Performing Investments</CardTitle>
            <Link to="/dashboard/investments/portfolio">
              <Button variant="outline" size="sm">
                View Portfolio
              </Button>
            </Link>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {topInvestments.map((investment) => (
              <div key={investment.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Briefcase className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold">{investment.companyName}</h3>
                    <p className="text-sm text-gray-600">{investment.industry} • {investment.stage}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-6">
                  <div className="text-right">
                    <p className="text-sm font-medium">Invested: {formatCurrency(investment.investmentAmount)}</p>
                    <p className="text-sm text-gray-600">Current: {formatCurrency(investment.currentValue)}</p>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center gap-1">
                      {investment.performance >= 0 ? (
                        <TrendingUp className="w-4 h-4 text-green-600" />
                      ) : (
                        <TrendingDown className="w-4 h-4 text-red-600" />
                      )}
                      <span className={`font-semibold ${getPerformanceColor(investment.performance)}`}>
                        {investment.performance >= 0 ? '+' : ''}{investment.performance}%
                      </span>
                    </div>
                    <p className="text-xs text-gray-600">Updated {formatDate(investment.lastUpdate)}</p>
                  </div>
                  <Badge className={getStatusColor(investment.status)}>
                    {investment.status}
                  </Badge>
                </div>
              </div>
            ))}
          </div>

          {topInvestments.length === 0 && (
            <div className="text-center py-8">
              <PieChart className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-4">No investments in your portfolio yet</p>
              <Link to="/dashboard/investments/opportunities">
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Explore Investment Opportunities
                </Button>
              </Link>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Performance Metrics */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Invested</p>
                  <p className="text-2xl font-bold">{formatCurrency(stats.totalInvested)}</p>
                  <p className="text-sm text-gray-600">Across {stats.activeInvestments} companies</p>
                </div>
                <DollarSign className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Portfolio Growth</p>
                  <p className="text-2xl font-bold text-green-600">+{stats.returnPercentage.toFixed(1)}%</p>
                  <p className="text-sm text-green-600">All-time return</p>
                </div>
                <TrendingUp className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Deal Flow</p>
                  <p className="text-2xl font-bold text-purple-600">{stats.pendingDeals}</p>
                  <p className="text-sm text-purple-600">Opportunities reviewing</p>
                </div>
                <Target className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default InvestorDashboard;
