/**
 * Role-Based AI API Service
 * Handles AI requests based on user roles and permissions
 */

import { apiClient } from './apiClient';
import { User } from './api';
import { getUserAIRateLimits, canAccessAICapability } from '../utils/roleBasedAI';
import { createRoleManager } from '../utils/unifiedRoleManager';

export interface AIRequest {
  message: string;
  context?: any;
  capability?: string;
  businessIdeaId?: number;
  language?: string;
}

export interface AIResponse {
  success: boolean;
  data?: any;
  error?: string;
  rateLimitInfo?: {
    remaining: number;
    resetTime: number;
    limit: number;
  };
  userRole?: string;
  capabilityUsed?: string;
}

class RoleBasedAIService {
  private baseUrl = '/api/ai';

  /**
   * Send AI chat message with role-based capabilities
   */
  async chat(user: User | null, request: AIRequest): Promise<AIResponse> {
    if (!user) {
      return {
        success: false,
        error: 'Authentication required'
      };
    }

    const userRoles = createRoleManager(user).getUserRoles();
    const rateLimits = getUserAIRateLimits(user);

    // Check if user can access the requested capability
    if (request.capability && !canAccessAICapability(user, request.capability)) {
      return {
        success: false,
        error: 'Insufficient permissions for this AI capability'
      };
    }

    try {
      const response = await apiClient.post(`${this.baseUrl}/chat`, {
        ...request,
        userRoles,
        rateLimits,
        userId: user.id
      });

      return {
        success: true,
        data: response.data,
        userRole: userRoles[0],
        capabilityUsed: request.capability
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || 'AI service error',
        rateLimitInfo: error.response?.data?.rateLimitInfo
      };
    }
  }

  /**
   * Get business analysis with role-specific depth
   */
  async analyzeBusinessIdea(user: User | null, businessIdeaId: number, analysisType?: string): Promise<AIResponse> {
    if (!user) {
      return {
        success: false,
        error: 'Authentication required'
      };
    }

    const userRoles = createRoleManager(user).getUserRoles();
    const primaryRole = userRoles[0] || 'user';

    // Define analysis depth based on role
    const analysisDepth = {
      user: 'basic',
      mentor: 'comprehensive',
      investor: 'financial_focused',
      moderator: 'content_focused',
      admin: 'full'
    };

    try {
      const response = await apiClient.post(`${this.baseUrl}/analyze`, {
        businessIdeaId,
        analysisType: analysisType || analysisDepth[primaryRole as keyof typeof analysisDepth],
        userRole: primaryRole,
        userId: user.id
      });

      return {
        success: true,
        data: response.data,
        userRole: primaryRole
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || 'Analysis service error'
      };
    }
  }

  /**
   * Get AI recommendations based on user role
   */
  async getRecommendations(user: User | null, context: any): Promise<AIResponse> {
    if (!user) {
      return {
        success: false,
        error: 'Authentication required'
      };
    }

    const userRoles = createRoleManager(user).getUserRoles();
    const primaryRole = userRoles[0] || 'user';

    // Check if user has access to recommendations
    if (!['mentor', 'investor', 'admin'].includes(primaryRole)) {
      return {
        success: false,
        error: 'Recommendations require mentor, investor, or admin role'
      };
    }

    try {
      const response = await apiClient.post(`${this.baseUrl}/recommendations`, {
        context,
        userRole: primaryRole,
        userId: user.id
      });

      return {
        success: true,
        data: response.data,
        userRole: primaryRole
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || 'Recommendations service error'
      };
    }
  }

  /**
   * Get AI status with role-specific information
   */
  async getStatus(user: User | null): Promise<AIResponse> {
    const userRoles = user ? createRoleManager(user).getUserRoles() : ['anonymous'];
    const rateLimits = user ? getUserAIRateLimits(user) : { chat: 0, analysis: 0, generation: 0 };

    try {
      const response = await apiClient.get(`${this.baseUrl}/status`, {
        params: {
          userRole: userRoles[0],
          userId: user?.id
        }
      });

      return {
        success: true,
        data: {
          ...response.data,
          userRoles,
          rateLimits,
          capabilities: user ? await this.getUserCapabilities(user) : []
        },
        userRole: userRoles[0]
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || 'Status service error'
      };
    }
  }

  /**
   * Get user's available AI capabilities
   */
  async getUserCapabilities(user: User): Promise<string[]> {
    const userRoles = createRoleManager(user).getUserRoles();
    
    // Define capabilities by role
    const roleCapabilities = {
      user: ['basic_chat', 'idea_feedback'],
      mentor: ['basic_chat', 'idea_feedback', 'mentoring_ai', 'business_analysis', 'resource_recommendations'],
      investor: ['basic_chat', 'idea_feedback', 'investment_analysis', 'market_intelligence', 'portfolio_management'],
      moderator: ['basic_chat', 'content_moderation', 'community_insights'],
      admin: ['system_ai', 'basic_chat', 'idea_feedback', 'mentoring_ai', 'business_analysis', 'investment_analysis', 'content_moderation']
    };

    const capabilities = new Set<string>();
    
    userRoles.forEach(role => {
      const roleCaps = roleCapabilities[role as keyof typeof roleCapabilities] || [];
      roleCaps.forEach(cap => capabilities.add(cap));
    });

    return Array.from(capabilities);
  }

  /**
   * Check rate limits for user
   */
  async checkRateLimit(user: User | null, operation: string): Promise<{ allowed: boolean; remaining: number; resetTime: number }> {
    if (!user) {
      return { allowed: false, remaining: 0, resetTime: 0 };
    }

    try {
      const response = await apiClient.get(`${this.baseUrl}/rate-limit`, {
        params: {
          userId: user.id,
          operation
        }
      });

      return response.data;
    } catch (error) {
      return { allowed: false, remaining: 0, resetTime: 0 };
    }
  }

  /**
   * Generate content based on user role and permissions
   */
  async generateContent(user: User | null, prompt: string, contentType: string): Promise<AIResponse> {
    if (!user) {
      return {
        success: false,
        error: 'Authentication required'
      };
    }

    const userRoles = createRoleManager(user).getUserRoles();
    const primaryRole = userRoles[0] || 'user';

    // Check generation permissions
    const generationLimits = {
      user: ['basic_text'],
      mentor: ['basic_text', 'guidance_content', 'session_plans'],
      investor: ['basic_text', 'analysis_reports', 'investment_summaries'],
      moderator: ['basic_text', 'moderation_responses'],
      admin: ['all']
    };

    const allowedTypes = generationLimits[primaryRole as keyof typeof generationLimits] || ['basic_text'];
    
    if (!allowedTypes.includes('all') && !allowedTypes.includes(contentType)) {
      return {
        success: false,
        error: `Content type '${contentType}' not allowed for role '${primaryRole}'`
      };
    }

    try {
      const response = await apiClient.post(`${this.baseUrl}/generate`, {
        prompt,
        contentType,
        userRole: primaryRole,
        userId: user.id
      });

      return {
        success: true,
        data: response.data,
        userRole: primaryRole
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || 'Content generation error'
      };
    }
  }
}

// Export singleton instance
export const roleBasedAIService = new RoleBasedAIService();
export default roleBasedAIService;
