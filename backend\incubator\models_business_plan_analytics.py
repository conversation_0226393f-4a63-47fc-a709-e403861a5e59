"""
Business Plan Analytics Models
Track detailed analytics for business plans including time spent, collaboration, exports, and success metrics
"""

from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import timedelta
from .models_business_plan import BusinessPlan, BusinessPlanTemplate


class BusinessPlanSession(models.Model):
    """Track user sessions working on business plans"""
    
    business_plan = models.ForeignKey(
        BusinessPlan, 
        on_delete=models.CASCADE,
        related_name='sessions'
    )
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    
    # Session timing
    start_time = models.DateTimeField(auto_now_add=True)
    end_time = models.DateTimeField(null=True, blank=True)
    last_activity = models.DateTimeField(auto_now=True)
    
    # Session details
    session_id = models.CharField(max_length=100, unique=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    device_type = models.Char<PERSON>ield(max_length=20, blank=True)  # desktop, mobile, tablet
    
    # Activity tracking
    sections_viewed = models.JSO<PERSON>ield(default=list, help_text="List of section IDs viewed")
    sections_edited = models.JSO<PERSON>ield(default=list, help_text="List of section IDs edited")
    ai_assistance_used = models.BooleanField(default=False)
    
    class Meta:
        db_table = 'business_plan_session'
        indexes = [
            models.Index(fields=['business_plan', 'start_time']),
            models.Index(fields=['user', 'start_time']),
            models.Index(fields=['session_id']),
        ]
    
    def __str__(self):
        return f"Session: {self.user.username} - {self.business_plan.title}"
    
    def get_duration(self):
        """Get session duration"""
        if self.end_time:
            return self.end_time - self.start_time
        return timezone.now() - self.start_time
    
    def end_session(self):
        """End the session"""
        self.end_time = timezone.now()
        self.save()


class BusinessPlanCollaboration(models.Model):
    """Track collaboration activities on business plans"""
    
    ACTION_TYPES = [
        ('view', 'View'),
        ('edit', 'Edit'),
        ('comment', 'Comment'),
        ('review', 'Review'),
        ('approve', 'Approve'),
        ('reject', 'Reject'),
        ('share', 'Share'),
        ('invite', 'Invite Collaborator'),
    ]
    
    business_plan = models.ForeignKey(
        BusinessPlan,
        on_delete=models.CASCADE,
        related_name='collaborations'
    )
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    
    # Action details
    action_type = models.CharField(max_length=20, choices=ACTION_TYPES)
    section_id = models.IntegerField(null=True, blank=True)
    section_title = models.CharField(max_length=200, blank=True)
    
    # Content
    content = models.TextField(blank=True, help_text="Comment content or edit description")
    metadata = models.JSONField(default=dict, help_text="Additional action metadata")
    
    # Timing
    created_at = models.DateTimeField(auto_now_add=True)
    
    # Context
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    
    class Meta:
        db_table = 'business_plan_collaboration'
        indexes = [
            models.Index(fields=['business_plan', 'created_at']),
            models.Index(fields=['user', 'action_type']),
            models.Index(fields=['action_type', 'created_at']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.action_type} - {self.business_plan.title}"


class BusinessPlanExport(models.Model):
    """Track business plan exports and downloads"""
    
    EXPORT_FORMATS = [
        ('pdf', 'PDF'),
        ('word', 'Microsoft Word'),
        ('excel', 'Microsoft Excel'),
        ('powerpoint', 'PowerPoint'),
        ('json', 'JSON'),
        ('html', 'HTML'),
    ]
    
    EXPORT_TYPES = [
        ('full', 'Full Business Plan'),
        ('summary', 'Executive Summary'),
        ('financial', 'Financial Projections'),
        ('sections', 'Selected Sections'),
    ]
    
    business_plan = models.ForeignKey(
        BusinessPlan,
        on_delete=models.CASCADE,
        related_name='exports'
    )
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    
    # Export details
    export_format = models.CharField(max_length=20, choices=EXPORT_FORMATS)
    export_type = models.CharField(max_length=20, choices=EXPORT_TYPES)
    sections_included = models.JSONField(default=list, help_text="List of section IDs included")
    
    # File details
    file_size = models.BigIntegerField(null=True, blank=True, help_text="File size in bytes")
    file_path = models.CharField(max_length=500, blank=True)
    
    # Timing
    created_at = models.DateTimeField(auto_now_add=True)
    downloaded_at = models.DateTimeField(null=True, blank=True)
    
    # Context
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    
    # Success tracking
    export_successful = models.BooleanField(default=True)
    error_message = models.TextField(blank=True)
    
    class Meta:
        db_table = 'business_plan_export'
        indexes = [
            models.Index(fields=['business_plan', 'created_at']),
            models.Index(fields=['user', 'export_format']),
            models.Index(fields=['export_format', 'created_at']),
        ]
    
    def __str__(self):
        return f"Export: {self.business_plan.title} - {self.export_format}"


class BusinessPlanAnalytics(models.Model):
    """Aggregated analytics for business plans"""
    
    business_plan = models.OneToOneField(
        BusinessPlan,
        on_delete=models.CASCADE,
        related_name='analytics'
    )
    
    # Time metrics
    total_time_spent = models.DurationField(default=timedelta)
    average_session_duration = models.DurationField(default=timedelta)
    total_sessions = models.IntegerField(default=0)
    
    # Collaboration metrics
    total_collaborators = models.IntegerField(default=0)
    total_comments = models.IntegerField(default=0)
    total_edits = models.IntegerField(default=0)
    total_reviews = models.IntegerField(default=0)
    
    # Export metrics
    total_exports = models.IntegerField(default=0)
    pdf_exports = models.IntegerField(default=0)
    word_exports = models.IntegerField(default=0)
    excel_exports = models.IntegerField(default=0)
    
    # Success metrics
    completion_rate = models.FloatField(default=0.0)
    sections_completed = models.IntegerField(default=0)
    total_sections = models.IntegerField(default=0)
    
    # AI usage
    ai_assistance_sessions = models.IntegerField(default=0)
    ai_content_generated = models.IntegerField(default=0)
    
    # Quality metrics
    word_count = models.IntegerField(default=0)
    character_count = models.IntegerField(default=0)
    
    # Timestamps
    last_calculated = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'business_plan_analytics'
    
    def __str__(self):
        return f"Analytics: {self.business_plan.title}"
    
    def update_analytics(self):
        """Update analytics from related data"""
        # Time metrics
        sessions = self.business_plan.sessions.all()
        self.total_sessions = sessions.count()
        
        total_duration = timedelta()
        for session in sessions.filter(end_time__isnull=False):
            total_duration += session.get_duration()
        
        self.total_time_spent = total_duration
        if self.total_sessions > 0:
            self.average_session_duration = total_duration / self.total_sessions
        
        # Collaboration metrics
        collaborations = self.business_plan.collaborations.all()
        self.total_collaborators = collaborations.values('user').distinct().count()
        self.total_comments = collaborations.filter(action_type='comment').count()
        self.total_edits = collaborations.filter(action_type='edit').count()
        self.total_reviews = collaborations.filter(action_type='review').count()
        
        # Export metrics
        exports = self.business_plan.exports.all()
        self.total_exports = exports.count()
        self.pdf_exports = exports.filter(export_format='pdf').count()
        self.word_exports = exports.filter(export_format='word').count()
        self.excel_exports = exports.filter(export_format='excel').count()
        
        # AI usage
        self.ai_assistance_sessions = sessions.filter(ai_assistance_used=True).count()
        
        # Completion metrics
        sections = self.business_plan.sections.all()
        self.total_sections = sections.count()
        self.sections_completed = sections.filter(is_completed=True).count()
        if self.total_sections > 0:
            self.completion_rate = (self.sections_completed / self.total_sections) * 100
        
        self.save()


class TemplateSuccessMetrics(models.Model):
    """Track success metrics for business plan templates"""
    
    template = models.OneToOneField(
        BusinessPlanTemplate,
        on_delete=models.CASCADE,
        related_name='success_metrics'
    )
    
    # Usage metrics
    total_uses = models.IntegerField(default=0)
    total_completions = models.IntegerField(default=0)
    completion_rate = models.FloatField(default=0.0)
    
    # Time metrics
    average_completion_time = models.DurationField(default=timedelta)
    fastest_completion = models.DurationField(null=True, blank=True)
    slowest_completion = models.DurationField(null=True, blank=True)
    
    # Success indicators
    plans_published = models.IntegerField(default=0)
    funding_received = models.IntegerField(default=0)
    business_launched = models.IntegerField(default=0)
    
    # User satisfaction
    average_rating = models.FloatField(default=0.0)
    total_ratings = models.IntegerField(default=0)
    
    # Last updated
    last_calculated = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'template_success_metrics'
    
    def __str__(self):
        return f"Success Metrics: {self.template.name}"
    
    def update_metrics(self):
        """Update metrics from business plans using this template"""
        plans = BusinessPlan.objects.filter(template=self.template)
        self.total_uses = plans.count()
        self.total_completions = plans.filter(status='completed').count()
        
        if self.total_uses > 0:
            self.completion_rate = (self.total_completions / self.total_uses) * 100
        
        # Calculate time metrics from analytics
        completed_plans = plans.filter(status='completed')
        if completed_plans.exists():
            durations = []
            for plan in completed_plans:
                if hasattr(plan, 'analytics'):
                    durations.append(plan.analytics.total_time_spent)
            
            if durations:
                self.average_completion_time = sum(durations, timedelta()) / len(durations)
                self.fastest_completion = min(durations)
                self.slowest_completion = max(durations)
        
        self.save()
