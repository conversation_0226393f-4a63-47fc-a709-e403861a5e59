import React from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../hooks/useLanguage';
import { getMarginClass } from '../utils/rtlUtils';
// MainLayout removed - handled by routing system
import { RTLFlex, RTLIcon, RTLText } from '../components/rtl';
import {
  Globe,
  Code,
  FileText,
  Layers,
  PanelLeft,
  Palette,
  TestTube,
  ArrowLeft,
  ArrowRight,
  Check,
  X,
  ChevronRight,
  ChevronLeft
} from 'lucide-react';

const RTLComponentsDocPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  // Example code snippets
  const codeSnippets = {
    rtlContainer: `
<RTLContainer
  textAlign="start"
  padding={{ start: 2, end: 1 }}
  margin={{ block: 3 }}
>
  Content that respects RTL direction
</RTLContainer>
    `,
    rtlText: `
<RTLText align="start">
  This text will be aligned to the start (right in RTL, left in LTR)
</RTLText>
    `,
    rtlIcon: `
<RTLIcon icon={ArrowRight} flipInRTL={true} size={24} />
    `,
    rtlFlex: `
<RTLFlex direction="row" align="center" justify="between">
  <div>t("common.first.item", "First item")</div>
  <div>t("common.second.item", "Second item")</div>
</RTLFlex>
    `,
    rtlFormControls: `
<RTLInput
  label="Username"
  icon={User}
  iconPosition="left"
  placeholder={t("common.enter.username", "Enter username")}
/>

<RTLTextarea
  label={t("common.description", "Description")}
  rows={4}
  placeholder={t("common.enter.description", "Enter description")}
/>

<RTLSelect
  label="Country"
  options={[}
    { value: 'us', label: t("common.united.states", "United States") },
    { value: 'sa', label: t("common.saudi.arabia", "Saudi Arabia") }
  ]}
/>
    `,
    rtlDataTable: `
<RTLDataTable
  data={users}
  columns={[}
    { key: 'name', header: t("common.name", t("common.name", "Name")) },
    { key: 'email', header: t("common.email", t("common.email", "Email")) }
  ]}
  keyExtractor={(user) => user.id}
/>
    `,
    rtlChart: `
<RTLChart
  type="bar"
  data={chartData}
  options={chartOptions}
/>
    `,
    cssClasses: `
<div className="margin-inline-start-2 padding-inline-end-3 text-start">
  This content uses logical properties
</div>
    `,
    testing: `
import { renderWithRTL, hasRTLStyles } from '../utils/rtl/testing';

test('Component respects RTL direction', () => {
  const { getByText } = renderWithRTL(<MyComponent />, { initialLanguage: 'ar' });
  const element = getByText('Some text');
  expect(hasRTLStyles(element)).toBe(true);
});
    `,
  };

  // Component for displaying code snippets
  const CodeBlock = ({ code, title }: { code: string; title: string }) => (
    <div className="mt-4 mb-6">
      <h3 className="text-lg font-semibold mb-2">{title}</h3>
      <divre className="bg-indigo-950/50 p-4 rounded-lg overflow-x-auto">
        <code className="text-sm text-indigo-100">{code}</code>
      </pre>
    </div>
  );

  return (
    <AuthenticatedLayout>
      <div className="container mx-auto px-4 py-16 mt-8">
        <div className="max-w-4xl mx-auto">
          <RTLFlex as="h1" className="text-3xl font-bold mb-8" align="center">
            <RTLIcon icon={Globe} size={28} className={getMarginClass('end', 3)} />
            <span>t("common.rtl.components.documentation", "RTL Components Documentation")</span>
          </RTLFlex>

          <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50 mb-8">
            <RTLFlex align="center" className="mb-4">
              <RTLIcon icon={FileText} size={24} className={getMarginClass('end', 3)} />
              <h2 className="text-xl font-semibold">t("common.overview", t("dashboard.overview", "Overview"))</h2>
            </RTLFlex>
            <div className="mb-4">
              The RTL components are designed to handle bidirectional text and layout automatically based on the current language direction.
              They provide a consistent way to handle RTL languages like Arabic without having to write conditional code throughout the application.
            </div>
            <p>
              Current language: <strong>{isRTL ? t("common.rtl.ltr", "RTL' : 'LTR")}</strong>
            </div>
          </div>

          <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50 mb-8">
            <RTLFlex align="center" className="mb-4">
              <RTLIcon icon={Layers} size={24} className={getMarginClass('end', 3)} />
              <h2 className="text-xl font-semibold">t("common.directory.structure", "Directory Structure")</h2>
            </RTLFlex>
            <div className="bg-indigo-950/50 p-4 rounded-lg overflow-x-auto">
              <code className="text-sm text-indigo-100">{`
frontend/src/
├── components/
│   └── rtl/                  # RTL-aware components
│       ├── index.ts          # Exports all RTL components
│       ├── RTLContainer.tsx  # Base container component
│       ├── RTLText.tsx       # Text component
│       ├── RTLIcon.tsx       # Icon component
│       ├── RTLFlex.tsx       # Flex container component
│       ├── RTLDataTable.tsx  # Data table component
│       ├── RTLChart.tsx      # Chart component
│       └── RTLFormControls.tsx # Form control components
├── utils/
│   └── rtl/                  # RTL utilities
│       ├── index.ts          # Main RTL utilities
│       ├── cssUtils.ts       # CSS transformation utilities
│       └── testing.ts        # Testing utilities
├── types/
│   └── rtl.ts                # Type definitions for RTL components
└── styles/
    └── rtl-consolidated.css  # Comprehensive RTL styles with logical properties
              `}</code>
            </div>
          </div>

          <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50 mb-8">
            <RTLFlex align="center" className="mb-4">
              <RTLIcon icon={Code} size={24} className={getMarginClass('end', 3)} />
              <h2 className="text-xl font-semibold">t("common.components", "Components")</h2>
            </RTLFlex>

            <CodeBlock title={t("common.rtlcontainer", "RTLContainer")} code={codeSnippets.rtlContainer} />
            <CodeBlock title={t("common.rtltext", "RTLText")} code={codeSnippets.rtlText} />
            <CodeBlock title={t("common.rtlicon", "RTLIcon")} code={codeSnippets.rtlIcon} />
            <CodeBlock title={t("common.rtlflex", "RTLFlex")} code={codeSnippets.rtlFlex} />
            <CodeBlock title={t("common.rtlformcontrols", "RTLFormControls")} code={codeSnippets.rtlFormControls} />
            <CodeBlock title={t("common.rtldatatable", "RTLDataTable")} code={codeSnippets.rtlDataTable} />
            <CodeBlock title={t("common.rtlchart", "RTLChart")} code={codeSnippets.rtlChart} />
          </div>

          <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50 mb-8">
            <RTLFlex align="center" className="mb-4">
              <RTLIcon icon={Palette} size={24} className={getMarginClass('end', 3)} />
              <h2 className="text-xl font-semibold">t("common.css.classes", "CSS Classes")</h2>
            </RTLFlex>
            <div className="mb-4">
              The <code>rtl-consolidated.css</code> file provides comprehensive RTL support including logical CSS classes that can be used directly in components:
            </div>
            <CodeBlock title={t("common.logical.css.classes", "Logical CSS Classes")} code={codeSnippets.cssClasses} />
          </div>

          <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50 mb-8">
            <RTLFlex align="center" className="mb-4">
              <RTLIcon icon={TestTube} size={24} className={getMarginClass('end', 3)} />
              <h2 className="text-xl font-semibold">t("common.testing", "Testing")</h2>
            </RTLFlex>
            <div className="mb-4">
              The <code>testing.ts</code> file provides utilities for testing RTL components:
            </div>
            <CodeBlock title={t("common.testing.rtl.components", "Testing RTL Components")} code={codeSnippets.testing} />
          </div>

          <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50 mb-8">
            <RTLFlex align="center" className="mb-4">
              <RTLIcon icon={Check} size={24} className={getMarginClass('end', 3)} />
              <h2 className="text-xl font-semibold">t("common.best.practices", "Best Practices")</h2>
            </RTLFlex>
            <ul className="list-disc list-inside space-y-2">
              <li>Use logical properties instead of physical ones (e.g., <code>margin-inline-start</code> instead of <code>margin-left</code>)</li>
              <li>t("common.use.the.rtl", "Use the RTL components for any content that needs to respect language direction")</li>
              <li>t("common.use.the", "Use the ")<code>useLanguage</code> hook to access RTL-specific utilities</li>
              <li>t("common.test.components.in", "Test components in both LTR and RTL modes")</li>
              <li>t("common.use.the", "Use the ")<code>rtl-consolidated.css</code> classes for consistent RTL styling</li>
            </ul>
          </div>
        </div>
      </div>
    </AuthenticatedLayout>
  );
};

export default RTLComponentsDocPage;
