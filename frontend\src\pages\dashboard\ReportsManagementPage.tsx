import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import Button from '../../components/ui/Button';
import { Badge } from '../../components/ui/badge';
import { Input } from '../../components/ui/input';
import { Textarea } from '../../components/ui/textarea';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { AlertTriangle, CheckCircle, X, Eye, Flag, Search, Filter, Clock, User, MessageSquare } from 'lucide-react';

interface Report {
  id: string;
  type: 'user' | 'post' | 'comment' | 'thread' | 'business_idea';
  reportedItemId: string;
  reportedItemTitle: string;
  reportedBy: {
    id: string;
    name: string;
    email: string;
  };
  reportedUser: {
    id: string;
    name: string;
    email: string;
  };
  reason: string;
  category: 'spam' | 'harassment' | 'inappropriate' | 'copyright' | 'misinformation' | 'other';
  description: string;
  status: 'pending' | 'investigating' | 'resolved' | 'dismissed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  createdAt: string;
  assignedTo?: string;
  assignedToName?: string;
  resolution?: string;
  resolutionDate?: string;
  moderatorNotes?: string;
}

const ReportsManagementPage: React.FC = () => {
  const [reports, setReports] = useState<Report[]>([]);
  const [filteredReports, setFilteredReports] = useState<Report[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [activeTab, setActiveTab] = useState('pending');
  const [selectedReport, setSelectedReport] = useState<Report | null>(null);
  const [resolutionNotes, setResolutionNotes] = useState('');

  // Mock data - replace with actual API call
  useEffect(() => {
    const mockReports: Report[] = [
      {
        id: '1',
        type: 'post',
        reportedItemId: 'post123',
        reportedItemTitle: 'Spam post about cryptocurrency investment',
        reportedBy: { id: 'user1', name: 'John Smith', email: '<EMAIL>' },
        reportedUser: { id: 'user3', name: 'Mike Wilson', email: '<EMAIL>' },
        reason: 'This post contains spam content promoting cryptocurrency scams',
        category: 'spam',
        description: 'User is posting promotional content about guaranteed returns on cryptocurrency investments. This appears to be a scam.',
        status: 'pending',
        priority: 'high',
        createdAt: '2024-01-15T10:30:00Z'
      },
      {
        id: '2',
        type: 'comment',
        reportedItemId: 'comment456',
        reportedItemTitle: 'Inappropriate comment on funding discussion',
        reportedBy: { id: 'user2', name: 'Sarah Johnson', email: '<EMAIL>' },
        reportedUser: { id: 'user4', name: 'Alex Brown', email: '<EMAIL>' },
        reason: 'Harassment and inappropriate language',
        category: 'harassment',
        description: 'User made personal attacks and used inappropriate language in response to funding advice.',
        status: 'investigating',
        priority: 'medium',
        createdAt: '2024-01-14T16:45:00Z',
        assignedTo: 'mod1',
        assignedToName: 'Moderator Admin'
      },
      {
        id: '3',
        type: 'user',
        reportedItemId: 'user789',
        reportedItemTitle: 'User profile: Emily Davis',
        reportedBy: { id: 'user5', name: 'David Kim', email: '<EMAIL>' },
        reportedUser: { id: 'user6', name: 'Emily Davis', email: '<EMAIL>' },
        reason: 'Fake profile with stolen photos',
        category: 'misinformation',
        description: 'This user appears to be using stolen profile photos and false credentials.',
        status: 'resolved',
        priority: 'high',
        createdAt: '2024-01-13T09:15:00Z',
        assignedTo: 'mod2',
        assignedToName: 'Senior Moderator',
        resolution: 'Profile verified as legitimate after investigation. Photos confirmed to be authentic.',
        resolutionDate: '2024-01-14T14:20:00Z',
        moderatorNotes: 'Contacted user for verification. Provided additional documentation.'
      },
      {
        id: '4',
        type: 'business_idea',
        reportedItemId: 'idea101',
        reportedItemTitle: 'Revolutionary AI startup idea',
        reportedBy: { id: 'user7', name: 'Lisa Chen', email: '<EMAIL>' },
        reportedUser: { id: 'user8', name: 'Tom Wilson', email: '<EMAIL>' },
        reason: 'Copyright infringement',
        category: 'copyright',
        description: 'This business idea is identical to our patented technology described in our public documentation.',
        status: 'dismissed',
        priority: 'low',
        createdAt: '2024-01-12T14:20:00Z',
        assignedTo: 'mod1',
        assignedToName: 'Moderator Admin',
        resolution: 'No copyright infringement found. Ideas are sufficiently different.',
        resolutionDate: '2024-01-13T11:30:00Z'
      }
    ];

    setTimeout(() => {
      setReports(mockReports);
      setFilteredReports(mockReports);
      setLoading(false);
    }, 1000);
  }, []);

  // Filter reports based on search and filters
  useEffect(() => {
    let filtered = reports;

    if (searchTerm) {
      filtered = filtered.filter(report => 
        report.reportedItemTitle.toLowerCase().includes(searchTerm.toLowerCase()) ||
        report.reportedBy.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        report.reportedUser.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        report.reason.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(report => report.status === statusFilter);
    }

    if (categoryFilter !== 'all') {
      filtered = filtered.filter(report => report.category === categoryFilter);
    }

    if (priorityFilter !== 'all') {
      filtered = filtered.filter(report => report.priority === priorityFilter);
    }

    // Tab-specific filtering
    if (activeTab === 'pending') {
      filtered = filtered.filter(report => report.status === 'pending');
    } else if (activeTab === 'investigating') {
      filtered = filtered.filter(report => report.status === 'investigating');
    } else if (activeTab === 'resolved') {
      filtered = filtered.filter(report => report.status === 'resolved');
    }

    setFilteredReports(filtered);
  }, [reports, searchTerm, statusFilter, categoryFilter, priorityFilter, activeTab]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'investigating': return 'bg-blue-100 text-blue-800';
      case 'resolved': return 'bg-green-100 text-green-800';
      case 'dismissed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'spam': return 'bg-red-100 text-red-800';
      case 'harassment': return 'bg-orange-100 text-orange-800';
      case 'inappropriate': return 'bg-purple-100 text-purple-800';
      case 'copyright': return 'bg-blue-100 text-blue-800';
      case 'misinformation': return 'bg-yellow-100 text-yellow-800';
      case 'other': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'text-red-600';
      case 'high': return 'text-orange-600';
      case 'medium': return 'text-yellow-600';
      case 'low': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'user': return <User className="w-4 h-4" />;
      case 'post': return <MessageSquare className="w-4 h-4" />;
      case 'comment': return <MessageSquare className="w-4 h-4" />;
      case 'thread': return <MessageSquare className="w-4 h-4" />;
      case 'business_idea': return <Flag className="w-4 h-4" />;
      default: return <AlertTriangle className="w-4 h-4" />;
    }
  };

  const handleAssignReport = (id: string) => {
    setReports(reports.map(report => 
      report.id === id 
        ? { ...report, status: 'investigating' as const, assignedTo: 'current_mod', assignedToName: 'Current Moderator' }
        : report
    ));
  };

  const handleResolveReport = (id: string) => {
    if (!resolutionNotes.trim()) return;
    
    setReports(reports.map(report => 
      report.id === id 
        ? { 
            ...report, 
            status: 'resolved' as const, 
            resolution: resolutionNotes,
            resolutionDate: new Date().toISOString()
          }
        : report
    ));
    setResolutionNotes('');
    setSelectedReport(null);
  };

  const handleDismissReport = (id: string) => {
    if (!resolutionNotes.trim()) return;
    
    setReports(reports.map(report => 
      report.id === id 
        ? { 
            ...report, 
            status: 'dismissed' as const, 
            resolution: resolutionNotes,
            resolutionDate: new Date().toISOString()
          }
        : report
    ));
    setResolutionNotes('');
    setSelectedReport(null);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Reports Management</h1>
          <p className="text-gray-600 mt-1">Review and manage user reports</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Filter className="w-4 h-4 mr-2" />
            Advanced Filters
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700">
            <Eye className="w-4 h-4 mr-2" />
            Bulk Actions
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Reports</p>
                <p className="text-2xl font-bold">{reports.length}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {reports.filter(r => r.status === 'pending').length}
                </p>
              </div>
              <Clock className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Investigating</p>
                <p className="text-2xl font-bold text-blue-600">
                  {reports.filter(r => r.status === 'investigating').length}
                </p>
              </div>
              <Eye className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Resolved</p>
                <p className="text-2xl font-bold text-green-600">
                  {reports.filter(r => r.status === 'resolved').length}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search reports..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select 
              value={categoryFilter} 
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="border rounded px-3 py-2"
            >
              <option value="all">All Categories</option>
              <option value="spam">Spam</option>
              <option value="harassment">Harassment</option>
              <option value="inappropriate">Inappropriate</option>
              <option value="copyright">Copyright</option>
              <option value="misinformation">Misinformation</option>
              <option value="other">Other</option>
            </select>
            <select 
              value={priorityFilter} 
              onChange={(e) => setPriorityFilter(e.target.value)}
              className="border rounded px-3 py-2"
            >
              <option value="all">All Priorities</option>
              <option value="urgent">Urgent</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
            </select>
            <select 
              value={statusFilter} 
              onChange={(e) => setStatusFilter(e.target.value)}
              className="border rounded px-3 py-2"
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="investigating">Investigating</option>
              <option value="resolved">Resolved</option>
              <option value="dismissed">Dismissed</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Reports Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="pending">Pending ({reports.filter(r => r.status === 'pending').length})</TabsTrigger>
          <TabsTrigger value="investigating">Investigating ({reports.filter(r => r.status === 'investigating').length})</TabsTrigger>
          <TabsTrigger value="resolved">Resolved ({reports.filter(r => r.status === 'resolved').length})</TabsTrigger>
          <TabsTrigger value="all">All Reports</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-4">
          <div className="space-y-4">
            {filteredReports.map((report) => (
              <Card key={report.id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        {getTypeIcon(report.type)}
                        <h3 className="font-semibold text-lg">{report.reportedItemTitle}</h3>
                        <Badge className={getStatusColor(report.status)}>
                          {report.status}
                        </Badge>
                        <Badge className={getCategoryColor(report.category)}>
                          {report.category}
                        </Badge>
                        <span className={`text-sm font-medium ${getPriorityColor(report.priority)}`}>
                          {report.priority} priority
                        </span>
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600 mb-3">
                        <div>
                          <p className="font-medium">Reported by:</p>
                          <p>{report.reportedBy.name}</p>
                        </div>
                        <div>
                          <p className="font-medium">Reported user:</p>
                          <p>{report.reportedUser.name}</p>
                        </div>
                        <div>
                          <p className="font-medium">Type:</p>
                          <p className="capitalize">{report.type}</p>
                        </div>
                        <div>
                          <p className="font-medium">Date:</p>
                          <p>{formatDate(report.createdAt)}</p>
                        </div>
                      </div>

                      <div className="mb-3">
                        <p className="text-sm font-medium text-gray-700 mb-1">Reason:</p>
                        <p className="text-sm text-gray-600">{report.reason}</p>
                      </div>

                      <div className="mb-3">
                        <p className="text-sm font-medium text-gray-700 mb-1">Description:</p>
                        <p className="text-sm text-gray-600">{report.description}</p>
                      </div>

                      {report.assignedToName && (
                        <div className="mb-3">
                          <p className="text-sm text-blue-600">Assigned to: {report.assignedToName}</p>
                        </div>
                      )}

                      {report.resolution && (
                        <div className="bg-green-50 p-3 rounded mb-3">
                          <p className="text-sm text-green-800">
                            <strong>Resolution:</strong> {report.resolution}
                          </p>
                          {report.resolutionDate && (
                            <p className="text-sm text-green-700">
                              Resolved on: {formatDate(report.resolutionDate)}
                            </p>
                          )}
                        </div>
                      )}

                      {report.moderatorNotes && (
                        <div className="bg-blue-50 p-3 rounded mb-3">
                          <p className="text-sm text-blue-800">
                            <strong>Moderator Notes:</strong> {report.moderatorNotes}
                          </p>
                        </div>
                      )}
                    </div>

                    <div className="flex flex-col gap-2 ml-4">
                      <Button variant="outline" size="sm">
                        <Eye className="w-4 h-4 mr-1" />
                        View Item
                      </Button>
                      
                      {report.status === 'pending' && (
                        <Button 
                          size="sm" 
                          className="bg-blue-600 hover:bg-blue-700"
                          onClick={() => handleAssignReport(report.id)}
                        >
                          Assign to Me
                        </Button>
                      )}

                      {(report.status === 'pending' || report.status === 'investigating') && (
                        <>
                          <Button 
                            size="sm" 
                            className="bg-green-600 hover:bg-green-700"
                            onClick={() => setSelectedReport(report)}
                          >
                            <CheckCircle className="w-4 h-4 mr-1" />
                            Resolve
                          </Button>
                          <Button 
                            size="sm" 
                            variant="outline"
                            className="border-gray-300 text-gray-600 hover:bg-gray-50"
                            onClick={() => setSelectedReport(report)}
                          >
                            <X className="w-4 h-4 mr-1" />
                            Dismiss
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredReports.length === 0 && (
            <Card>
              <CardContent className="p-8 text-center">
                <AlertTriangle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 mb-4">No reports found matching your criteria.</p>
                <Button variant="outline" onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('all');
                  setCategoryFilter('all');
                  setPriorityFilter('all');
                }}>
                  Clear Filters
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>

      {/* Resolution Modal */}
      {selectedReport && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle>Resolve Report</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm font-medium mb-2">Report: {selectedReport.reportedItemTitle}</p>
                <p className="text-sm text-gray-600">{selectedReport.reason}</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">Resolution Notes:</label>
                <Textarea
                  value={resolutionNotes}
                  onChange={(e) => setResolutionNotes(e.target.value)}
                  placeholder="Explain how this report was resolved..."
                  rows={4}
                />
              </div>

              <div className="flex gap-2">
                <Button 
                  className="flex-1 bg-green-600 hover:bg-green-700"
                  onClick={() => handleResolveReport(selectedReport.id)}
                  disabled={!resolutionNotes.trim()}
                >
                  Resolve
                </Button>
                <Button 
                  variant="outline" 
                  className="flex-1"
                  onClick={() => handleDismissReport(selectedReport.id)}
                  disabled={!resolutionNotes.trim()}
                >
                  Dismiss
                </Button>
                <Button 
                  variant="outline"
                  onClick={() => {
                    setSelectedReport(null);
                    setResolutionNotes('');
                  }}
                >
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default ReportsManagementPage;
