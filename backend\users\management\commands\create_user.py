from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from users.models import UserPro<PERSON>le, UserRole, UserRoleAssignment
from django.utils import timezone
from django.core.validators import validate_email
from django.core.exceptions import ValidationError
import getpass


class Command(BaseCommand):
    help = 'Create a new user with specified role'

    def add_arguments(self, parser):
        parser.add_argument(
            '--username',
            type=str,
            help='Username for the new user'
        )
        parser.add_argument(
            '--email',
            type=str,
            help='Email for the new user'
        )
        parser.add_argument(
            '--password',
            type=str,
            help='Password for the new user (if not provided, will prompt)'
        )
        parser.add_argument(
            '--first-name',
            type=str,
            help='First name'
        )
        parser.add_argument(
            '--last-name',
            type=str,
            help='Last name'
        )
        parser.add_argument(
            '--role',
            type=str,
            choices=['super_admin', 'admin', 'moderator', 'mentor', 'investor', 'user'],
            default='user',
            help='Role to assign to the user (default: user)'
        )
        parser.add_argument(
            '--staff',
            action='store_true',
            help='Make user a Django staff member'
        )
        parser.add_argument(
            '--superuser',
            action='store_true',
            help='Make user a Django superuser'
        )
        parser.add_argument(
            '--interactive',
            action='store_true',
            help='Interactive mode - prompt for all details'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 Creating New User'))
        self.stdout.write(self.style.SUCCESS('=' * 50))
        
        if options['interactive']:
            self._interactive_create()
        else:
            self._create_user_from_options(options)

    def _interactive_create(self):
        """Interactive user creation"""
        self.stdout.write(self.style.SUCCESS('📝 Interactive User Creation'))
        self.stdout.write('Please provide the following information:\n')
        
        # Get username
        while True:
            username = input('Username: ').strip()
            if not username:
                self.stdout.write(self.style.ERROR('Username cannot be empty'))
                continue
            if User.objects.filter(username=username).exists():
                self.stdout.write(self.style.ERROR(f'Username "{username}" already exists'))
                continue
            break
        
        # Get email
        while True:
            email = input('Email: ').strip()
            if not email:
                self.stdout.write(self.style.ERROR('Email cannot be empty'))
                continue
            try:
                validate_email(email)
            except ValidationError:
                self.stdout.write(self.style.ERROR('Invalid email format'))
                continue
            if User.objects.filter(email=email).exists():
                self.stdout.write(self.style.ERROR(f'Email "{email}" already exists'))
                continue
            break
        
        # Get password
        while True:
            password1 = getpass.getpass('Password: ')
            password2 = getpass.getpass('Confirm password: ')
            
            if password1 != password2:
                self.stdout.write(self.style.ERROR('Passwords do not match'))
                continue
            if len(password1) < 8:
                self.stdout.write(self.style.ERROR('Password must be at least 8 characters'))
                continue
            break
        
        # Get names
        first_name = input('First name (optional): ').strip()
        last_name = input('Last name (optional): ').strip()
        
        # Get role
        self.stdout.write('\nAvailable roles:')
        roles = ['user', 'admin', 'super_admin', 'moderator', 'mentor', 'investor']
        for i, role in enumerate(roles, 1):
            self.stdout.write(f'  {i}. {role}')
        
        while True:
            try:
                choice = input(f'\nSelect role (1-{len(roles)}) [default: 1]: ').strip()
                if not choice:
                    choice = '1'
                role_index = int(choice) - 1
                if 0 <= role_index < len(roles):
                    role = roles[role_index]
                    break
                else:
                    self.stdout.write(self.style.ERROR('Invalid choice'))
            except ValueError:
                self.stdout.write(self.style.ERROR('Please enter a number'))
        
        # Django permissions
        is_staff = input('Make Django staff user? (y/N): ').lower().strip() == 'y'
        is_superuser = input('Make Django superuser? (y/N): ').lower().strip() == 'y'
        
        # Create user
        self._create_user(
            username=username,
            email=email,
            password=password1,
            first_name=first_name,
            last_name=last_name,
            role=role,
            is_staff=is_staff,
            is_superuser=is_superuser
        )

    def _create_user_from_options(self, options):
        """Create user from command line options"""
        username = options.get('username')
        email = options.get('email')
        password = options.get('password')
        
        # Validate required fields
        if not username:
            username = input('Username: ').strip()
        
        if not email:
            email = input('Email: ').strip()
        
        if not password:
            password = getpass.getpass('Password: ')
        
        # Validate
        if User.objects.filter(username=username).exists():
            self.stdout.write(self.style.ERROR(f'Username "{username}" already exists'))
            return
        
        try:
            validate_email(email)
        except ValidationError:
            self.stdout.write(self.style.ERROR('Invalid email format'))
            return
        
        if User.objects.filter(email=email).exists():
            self.stdout.write(self.style.ERROR(f'Email "{email}" already exists'))
            return
        
        self._create_user(
            username=username,
            email=email,
            password=password,
            first_name=options.get('first_name', ''),
            last_name=options.get('last_name', ''),
            role=options.get('role', 'user'),
            is_staff=options.get('staff', False),
            is_superuser=options.get('superuser', False)
        )

    def _create_user(self, username, email, password, first_name, last_name, role, is_staff, is_superuser):
        """Create the user with all details"""
        try:
            # Create Django user
            user = User.objects.create_user(
                username=username,
                email=email,
                password=password,
                first_name=first_name,
                last_name=last_name,
                is_staff=is_staff,
                is_superuser=is_superuser,
                is_active=True
            )
            
            self.stdout.write(self.style.SUCCESS(f'✅ Created Django user: {username}'))
            
            # Get or create user profile
            profile, created = UserProfile.objects.get_or_create(
                user=user,
                defaults={
                    'is_active': True,
                    'language': 'en'
                }
            )
            
            if created:
                self.stdout.write(self.style.SUCCESS('✅ Created user profile'))
            
            # Assign role if specified
            if role and role != 'user':
                try:
                    user_role = UserRole.objects.get(name=role)
                    
                    assignment, assignment_created = UserRoleAssignment.objects.get_or_create(
                        user_profile=profile,
                        role=user_role,
                        defaults={
                            'is_active': True,
                            'is_approved': True,
                            'notes': f'Role assigned during user creation on {timezone.now().strftime("%Y-%m-%d %H:%M:%S")}'
                        }
                    )
                    
                    if assignment_created:
                        self.stdout.write(self.style.SUCCESS(f'✅ Assigned role: {user_role.display_name}'))
                    else:
                        self.stdout.write(self.style.WARNING(f'📊 Role already assigned: {user_role.display_name}'))
                        
                except UserRole.DoesNotExist:
                    self.stdout.write(self.style.ERROR(f'❌ Role "{role}" does not exist'))
            
            # Show summary
            self.stdout.write('\n' + '=' * 50)
            self.stdout.write(self.style.SUCCESS('🎉 USER CREATED SUCCESSFULLY!'))
            self.stdout.write('=' * 50)
            self.stdout.write(f'Username: {username}')
            self.stdout.write(f'Email: {email}')
            self.stdout.write(f'Name: {first_name} {last_name}'.strip() or 'N/A')
            self.stdout.write(f'Role: {role}')
            self.stdout.write(f'Django Staff: {"Yes" if is_staff else "No"}')
            self.stdout.write(f'Django Superuser: {"Yes" if is_superuser else "No"}')
            self.stdout.write(f'User ID: {user.id}')
            self.stdout.write('=' * 50)
            
            if role in ['admin', 'super_admin']:
                self.stdout.write(self.style.WARNING('⚠️  ADMIN USER CREATED:'))
                self.stdout.write(self.style.WARNING('• Ensure the password is secure'))
                self.stdout.write(self.style.WARNING('• User should change password on first login'))
                self.stdout.write(self.style.WARNING('• Review permissions and access levels'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Error creating user: {str(e)}'))
            import traceback
            traceback.print_exc()
