import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Download,
  Search,
  Star,
  Share2,
  Bookmark,
  History,
  MessageSquare,
  Lightbulb,
  FileText,
  Mic,
  MicOff,
  Volume2,
  VolumeX,
  Settings,
  Palette,
  Filter
} from 'lucide-react';

interface ChatFeaturesProps {
  onExportChat: () => void;
  onSearchChat: (query: string) => void;
  onToggleVoice: () => void;
  onToggleAudio: () => void;
  onShowTemplates: () => void;
  onShowHistory: () => void;
  onShowSettings: () => void;
  isVoiceEnabled: boolean;
  isAudioEnabled: boolean;
  messageCount: number;
}

const ChatFeatures: React.FC<ChatFeaturesProps> = ({ onExportChat,
  onSearchChat,
  onToggleVoice,
  onToggleAudio,
  onShowTemplates,
  onShowHistory,
  onShowSettings,
  isVoiceEnabled,
  isAudioEnabled,
  messageCount
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [searchQuery, setSearchQuery] = useState('');
  const [showFeatures, setShowFeatures] = useState(false);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      onSearchChat(searchQuery);
    }
  };

  const features = [
    {
      id: 'export',
      icon: <Download size={16} />,
      label: t('chat.exportChat'),
      description: t('chat.exportDescription'),
      onClick: onExportChat,
      disabled: messageCount === 0
    },
    {
      id: 'templates',
      icon: <MessageSquare size={16} />,
      label: t('chat.chatTemplates'),
      description: t('chat.templatesDescription'),
      onClick: onShowTemplates
    },
    {
      id: 'history',
      icon: <History size={16} />,
      label: t('chat.chatHistory'),
      description: t('chat.historyDescription'),
      onClick: onShowHistory
    },
    {
      id: 'voice',
      icon: isVoiceEnabled ? <Mic size={16} /> : <MicOff size={16} />,
      label: isVoiceEnabled ? t('chat.disableVoice') : t('chat.enableVoice'),
      description: t('chat.voiceDescription'),
      onClick: onToggleVoice,
      active: isVoiceEnabled
    },
    {
      id: 'audio',
      icon: isAudioEnabled ? <Volume2 size={16} /> : <VolumeX size={16} />,
      label: isAudioEnabled ? t('chat.disableAudio') : t('chat.enableAudio'),
      description: t('chat.audioDescription'),
      onClick: onToggleAudio,
      active: isAudioEnabled
    },
    {
      id: 'settings',
      icon: <Settings size={16} />,
      label: t('chat.chatSettings'),
      description: t('chat.settingsDescription'),
      onClick: onShowSettings
    }
  ];

  return (
    <div className="relative">
      {/* Features Toggle Button */}
      <button
        onClick={() => setShowFeatures(!showFeatures)}
        className={`p-2 rounded-md text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
          showFeatures ? 'bg-gray-100 dark:bg-gray-700' : ''}
        }`}
        title={t('chat.chatFeatures')}
      >
        <Settings size={18} />
      </button>

      {/* Features Panel */}
      {showFeatures && (
        <div className={`absolute ${isRTL ? 'left-0' : 'right-0'} top-full mt-2 w-80 card backdrop-blur-sm shadow-xl z-50`}>
          <div className="p-4">
            <h3 className="text-lg font-semibold text-primary mb-4">
              {t('chat.chatFeatures')}
            </h3>

            {/* Search */}
            <form onSubmit={handleSearch} className="mb-4">
              <div className="relative">
                <Search size={16} className={`absolute top-3 ${isRTL ? 'right-3' : 'left-3'} text-gray-400`} />
                <input
                  type="text"
                  placeholder={t('chat.searchMessages')}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className={`w-full ${isRTL ? 'pr-10 text-right' : 'pl-10'} py-2 input bg-indigo-900/20 border border-indigo-800/30 rounded-lg text-primary text-sm backdrop-blur-sm`}
                />
              </div>
            </form>

            {/* Feature Grid */}
            <div className="grid grid-cols-2 gap-2">
              {features.map((feature) => (
                <button
                  key={feature.id}
                  onClick={feature.onClick}
                  disabled={feature.disabled}
                  className={`p-3 rounded-lg border text-left transition-colors ${
                    feature.active
                      ? 'bg-purple-50 dark:bg-purple-900/20 border-purple-200 dark:border-purple-700 text-purple-700 dark:text-purple-300'
                      : feature.disabled
                      ? 'bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-gray-400 cursor-not-allowed'
                      : 'bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600'}
                  } ${isRTL ? 'text-right' : 'text-left'}`}
                >
                  <div className={`flex items-center mb-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <span className={isRTL ? 'ml-2' : 'mr-2'}>{feature.icon}</span>
                    <span className="text-sm font-medium">{feature.label}</span>
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {feature.description}
                  </p>
                </button>
              ))}
            </div>

            {/* Quick Stats */}
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <div className={`flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                <span>{t('chat.totalMessages')}</span>
                <span className="font-medium">{messageCount}</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatFeatures;
