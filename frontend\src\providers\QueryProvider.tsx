import React from 'react';
import { QueryClient, QueryClientProvider, Query<PERSON>ache, MutationCache } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { ApiError } from '../services/api';
import { toast } from '../components/ui/Toast';
import { useTranslation } from 'react-i18next';

// Cache configuration based on entity types
const entityCacheConfig = {
  // Frequently changing data (updates often)
  dynamic: {
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes
  },
  // Semi-static data (updates occasionally)
  standard: {
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  },
  // Rarely changing data (reference data)
  static: {
    staleTime: 30 * 60 * 1000, // 30 minutes
    gcTime: 60 * 60 * 1000, // 1 hour
  },
};

// Define endpoint-specific error handlers
const endpointErrorHandlers: Record<string, (error: ApiError) => string> = {
  // User-related endpoints
  '/users/': (error: ApiError) => {
    if (error.status === 401) {
      return 'You need to log in to access this resource';
    }
    if (error.status === 403) {
      return 'You do not have permission to access this resource';
    }
    return 'Error accessing user data';
  },

  // Post-related endpoints
  '/posts/': (error: ApiError) => {
    if (error.status === 404) {
      return 'The requested post could not be found';
    }
    if (error.status === 400) {
      return 'Invalid post data provided';
    }
    return 'Error accessing post data';
  },

  // Event-related endpoints
  '/events/': (error: ApiError) => {
    if (error.status === 404) {
      return 'The requested event could not be found';
    }
    if (error.status === 400) {
      return 'Invalid event data provided';
    }
    return 'Error accessing event data';
  },

  // Incubator-related endpoints
  '/incubator/': (error: ApiError) => {
    if (error.status === 404) {
      return 'The requested incubator resource could not be found';
    }
    if (error.status === 403) {
      return 'You do not have permission to access this incubator resource';
    }
    return 'Error accessing incubator data';
  },

  // Forum-related endpoints
  '/forums/': (error: ApiError) => {
    if (error.status === 404) {
      return 'The requested forum content could not be found';
    }
    if (error.status === 403) {
      return 'You do not have permission to access this forum content';
    }
    return 'Error accessing forum data';
  },

  // Default handler
  'default': (error: ApiError) => {
    if (error.status >= 500) {
      return 'Server error. Please try again later';
    }
    if (error.status === 429) {
      return 'Too many requests. Please slow down';
    }
    return error.message || 'An unknown error occurred';
  }
};

// Function to get the appropriate error handler
const getErrorHandler = (endpoint: string) => {
  for (const [key, handler] of Object.entries(endpointErrorHandlers)) {
    if (endpoint.includes(key)) {
      return handler;
    }
  }
  return endpointErrorHandlers.default;
};

// Helper to determine if we should show an error notification
const shouldShowErrorNotification = (error: Error): boolean => {
  // Don't show notifications for canceled requests or network offline errors
  if (error.name === 'CanceledError' || error.message.includes('network offline')) {
    return false;
  }

  // Don't show notifications for 401 errors (handled by auth system)
  if (error instanceof ApiError && error.status === 401) {
    return false;
  }

  return true;
};

// Create a query cache with enhanced error handling
const createQueryCache = () => {
  return new QueryCache({
    onError: (error, query) => {
      // Log errors to console in development
      if (process.env.NODE_ENV === 'development') {
        console.error(`Query error: ${error.message}`, query);
      }

      // Show error notification for user feedback (if appropriate)
      if (shouldShowErrorNotification(error)) {
        // Get the endpoint from the query key
        const queryKey = query.queryKey || [];
        const endpoint = typeof queryKey[0] === 'string' ? queryKey[0] : 'default';

        // Get the appropriate error handler
        const errorHandler = getErrorHandler(endpoint);

        // Generate the error message
        const errorMessage = error instanceof ApiError
          ? errorHandler(error)
          : 'An error occurred while fetching data';

        toast.error(errorMessage);
      }
    },
  });
};

// Create a mutation cache with enhanced error handling
const createMutationCache = () => {
  return new MutationCache({
    onError: (error, variables, context, mutation) => {
      // Log errors to console in development
      if (process.env.NODE_ENV === 'development') {
        console.error(`Mutation error: ${error.message}`, { variables, context });
      }

      // Show error notification for user feedback (if appropriate)
      if (shouldShowErrorNotification(error)) {
        // Get the endpoint from the mutation key
        const mutationKey = mutation.options.mutationKey || [];
        const endpoint = typeof mutationKey[0] === 'string' ? mutationKey[0] : 'default';

        // Get the appropriate error handler
        const errorHandler = getErrorHandler(endpoint);

        // Generate the error message
        const errorMessage = error instanceof ApiError
          ? errorHandler(error)
          : 'An error occurred while saving data';

        toast.error(errorMessage);
      }
    },
  });
};

// Create a client with optimized configuration
const createQueryClient = () => {
  return new QueryClient({
    queryCache: createQueryCache(),
    mutationCache: createMutationCache(),
    defaultOptions: {
      queries: {
        refetchOnWindowFocus: true, // Refetch on window focus for fresh data
        refetchOnReconnect: true, // Refetch when reconnecting
        refetchOnMount: true, // Refetch when component mounts
        retry: (failureCount, error) => {
          // Don't retry on 4xx client errors
          if (error instanceof ApiError && error.status >= 400 && error.status < 500) {
            return false;
          }
          // Retry server errors and network errors up to 3 times
          return failureCount < 3;
        },
        staleTime: entityCacheConfig.standard.staleTime,
        gcTime: entityCacheConfig.standard.gcTime,
        // Add suspense support (for React 18+)
        suspense: false,
      },
      mutations: {
        retry: (failureCount, error) => {
          // Only retry network errors, not API errors
          if (error instanceof ApiError) {
            return false;
          }
          // Retry network errors once
          return failureCount < 1;
        },
        // Optimistic updates are handled at the individual mutation level
      },
    },
  });
};

interface QueryProviderProps {
  children: React.ReactNode;
}

// Inner component that can use translation hooks
const QueryProviderInner: React.FC<QueryProviderProps> = ({ children }) => {
  const { t } = useTranslation();

  // Update error handlers with translations when language changes
  React.useEffect(() => {
    // Update endpoint error handlers with translations
    endpointErrorHandlers['/users/'] = (error: ApiError) => {
      if (error.status === 401) {
        return t('errors.auth.loginRequired', 'You need to log in to access this resource');
      }
      if (error.status === 403) {
        return t('errors.auth.permissionDenied', 'You do not have permission to access this resource');
      }
      return t('errors.users.general', 'Error accessing user data');
    };

    endpointErrorHandlers['/posts/'] = (error: ApiError) => {
      if (error.status === 404) {
        return t('errors.posts.notFound', 'The requested post could not be found');
      }
      if (error.status === 400) {
        return t('errors.posts.invalidData', 'Invalid post data provided');
      }
      return t('errors.posts.general', 'Error accessing post data');
    };

    endpointErrorHandlers['/events/'] = (error: ApiError) => {
      if (error.status === 404) {
        return t('errors.events.notFound', 'The requested event could not be found');
      }
      if (error.status === 400) {
        return t('errors.events.invalidData', 'Invalid event data provided');
      }
      return t('errors.events.general', 'Error accessing event data');
    };

    endpointErrorHandlers['/incubator/'] = (error: ApiError) => {
      if (error.status === 404) {
        return t('errors.incubator.notFound', 'The requested incubator resource could not be found');
      }
      if (error.status === 403) {
        return t('errors.incubator.permissionDenied', 'You do not have permission to access this incubator resource');
      }
      return t('errors.incubator.general', 'Error accessing incubator data');
    };

    endpointErrorHandlers['/forums/'] = (error: ApiError) => {
      if (error.status === 404) {
        return t('errors.forums.notFound', 'The requested forum content could not be found');
      }
      if (error.status === 403) {
        return t('errors.forums.permissionDenied', 'You do not have permission to access this forum content');
      }
      return t('errors.forums.general', 'Error accessing forum data');
    };

    endpointErrorHandlers['default'] = (error: ApiError) => {
      if (error.status >= 500) {
        return t('errors.server.general', 'Server error. Please try again later');
      }
      if (error.status === 429) {
        return t('errors.rateLimit', 'Too many requests. Please slow down');
      }
      return error.message || t('errors.unknown', 'An unknown error occurred');
    };
  }, [t]);

  return (
    <>
      {children}
      {/* Add React Query Devtools in development mode */}
      {process.env.NODE_ENV === 'development' && <ReactQueryDevtools initialIsOpen={false} />}
    </>
  );
};

export const QueryProvider: React.FC<QueryProviderProps> = ({ children }) => {
  // Create a new query client for each render to ensure translations are up-to-date
  const queryClient = React.useMemo(() => createQueryClient(), []);

  return (
    <QueryClientProvider client={queryClient}>
      <QueryProviderInner>
        {children}
      </QueryProviderInner>
    </QueryClientProvider>
  );
};

export default QueryProvider;
