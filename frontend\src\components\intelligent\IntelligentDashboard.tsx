import { useLanguage } from "../../hooks/useLanguage";
/**
 * Intelligent Dashboard
 * Main dashboard for AI-powered intelligent features
 */

import React, { useState, useEffect } from 'react';
import {
  Brain,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  Target,
  Zap,
  BarChart3,
  RefreshCw,
  Bell,
  Activity,
  Lightbulb,
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import {
  intelligentAppApi,
  IntelligentDashboard as DashboardData,
  ProactiveRecommendation,
  IntelligentAlert,
  NextAction,
  getHealthColor,
  getPriorityIcon,
  getAlertIcon,
  getEngagementColor,
  getActionTypeIcon,
  calculateHealthScoreColor,
  calculateHealthScoreBg,
  formatTimeEstimate,
} from '../../services/intelligentAppApi';

interface IntelligentDashboardProps {
  language?: string;
  className?: string;
}

export const IntelligentDashboard: React.FC<IntelligentDashboardProps> = ({ language = 'en',
  className = '',
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [dashboard, setDashboard] = useState<DashboardData | null>(null);
  const [recommendations, setRecommendations] = useState<ProactiveRecommendation[]>([]);
  const [alerts, setAlerts] = useState<IntelligentAlert[]>([]);
  const [nextActions, setNextActions] = useState<NextAction[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    loadDashboardData();

    // Set up real-time updates
    const eventSource = intelligentAppApi.createIntelligentStream(
      (data) => {
        if (data && !data.error) {
          setDashboard(data);
        }
      },
      (error) => {
        console.error('Stream error:', error);
      }
    );

    return () => {
      eventSource.close();
    };
  }, []);

  const loadDashboardData = async () => {
    setLoading(true);
    setError(null);

    try {
      const [dashboardResponse, recommendationsResponse, alertsResponse, actionsResponse] =
        await Promise.all([
          intelligentAppApi.getDashboard(),
          intelligentAppApi.getRecommendations(),
          intelligentAppApi.getAlerts(),
          intelligentAppApi.getNextActions(),
        ]);

      setDashboard(dashboardResponse.dashboard);
      setRecommendations(recommendationsResponse.recommendations);
      setAlerts(alertsResponse.alerts);
      setNextActions(actionsResponse.actions);
    } catch (err) {
      console.error('Error loading dashboard:', err);
      setError(t("dashboard.failed.to.load", "Failed to load intelligent dashboard"));
    } finally {
      setLoading(false);
    }
  };

  const refreshDashboard = async () => {
    setRefreshing(true);
    try {
      await intelligentAppApi.triggerAnalysis('full');
      await loadDashboardData();
    } catch (err) {
      console.error('Error refreshing dashboard:', err);
      setError(t("dashboard.failed.to.refresh", "Failed to refresh dashboard"));
    } finally {
      setRefreshing(false);
    }
  };

  const tabs = [
    {
      id: 'overview',
      name: language === 'ar' ? 'نظرة عامة' : {t("dashboard.overview", t("dashboard.overview", "Overview"))},
      icon: <BarChart3 className="w-4 h-4" />,
    },
    {
      id: 'recommendations',
      name: language === 'ar' ? 'التوصيات' : {t("dashboard.recommendations", t("dashboard.recommendations", "Recommendations"))},
      icon: <Lightbulb className="w-4 h-4" />,
    },
    {
      id: 'alerts',
      name: language === 'ar' ? 'التنبيهات' : t("dashboard.alerts", "Alerts"),
      icon: <Bell className="w-4 h-4" />,
    },
    {
      id: 'actions',
      name: language === 'ar' ? 'الإجراءات' : t("dashboard.actions", "Actions"),
      icon: <Target className="w-4 h-4" />,
    },
  ];

  const renderOverview = () => {
    if (!dashboard) return null;

    return (
      <div className="space-y-6">
        {/* Business Health Score */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <div className={`flex items-center justify-between mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            <h3 className={`text-lg font-semibold flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <Brain className={`w-5 h-5 mr-2 text-blue-500 ${isRTL ? "space-x-reverse" : ""}`} />
              {language === 'ar' ? 'نقاط صحة الأعمال' : t("common.business.health.score", "Business Health Score")}
            </h3>
            <button
              onClick={refreshDashboard}
              disabled={refreshing}
              className={`flex items-center px-3 py-1 text-sm text-blue-600 hover:text-blue-700 disabled:opacity-50 ${isRTL ? "flex-row-reverse" : ""}`}
            >
              <RefreshCw className={`w-4 h-4 mr-1 ${refreshing ? 'animate-spin' : ''}`} />
              {language === 'ar' ? 'تحديث' : t("common.refresh", t("common.refresh", "Refresh"))}
            </button>
          </div>

          <div className={`flex items-center justify-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className="relative">
              <div className={`text-4xl font-bold ${calculateHealthScoreColor(dashboard.business_health_score)}`}>
                {dashboard.business_health_score.toFixed(1)}
              </div>
              <div className="text-sm text-gray-600 text-center mt-1">
                {language === 'ar' ? 'من 100' : 'out of 100'}
              </div>
            </div>
          </div>

          <div className={`text-center p-3 rounded-lg ${calculateHealthScoreBg(dashboard.business_health_score)}`}>
            <p className={`font-medium ${calculateHealthScoreColor(dashboard.business_health_score)}`}>
              {dashboard.business_health_score >= 80
                ? (language === 'ar' ? 'ممتاز - عمل صحي' : t("dashboard.excellent.healthy.business", "Excellent - Healthy Business"))
                : dashboard.business_health_score >= 60
                ? (language === 'ar' ? 'جيد - تحسينات مطلوبة' : t("dashboard.good.improvements.needed", "Good - Improvements Needed"))
                : dashboard.business_health_score >= 40
                ? (language === 'ar' ? 'متوسط - تركيز مطلوب' : t("dashboard.average.focus.required", "Average - Focus Required"))
                : (language === 'ar' ? 'يحتاج عمل - اتخاذ إجراءات' : t("dashboard.needs.work.take", "Needs Work - Take Action"))
              }
            </p>
          </div>
        </div>

        {/* Progress Summary */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className={`text-lg font-semibold mb-4 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <TrendingUp className={`w-5 h-5 mr-2 text-green-500 ${isRTL ? "space-x-reverse" : ""}`} />
            {language === 'ar' ? 'ملخص التقدم' : t("common.progress.summary", "Progress Summary")}
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {dashboard.progress_summary.current_stage}
              </div>
              <div className="text-sm text-blue-700">
                {language === 'ar' ? 'المرحلة الحالية' : t("common.current.stage", "Current Stage")}
              </div>
            </div>

            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {dashboard.progress_summary.ideas_count}
              </div>
              <div className="text-sm text-green-700">
                {language === 'ar' ? 'الأفكار التجارية' : t("common.business.ideas", "Business Ideas")}
              </div>
            </div>

            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {dashboard.progress_summary.completion_percentage.toFixed(0)}%
              </div>
              <div className="text-sm text-purple-700">
                {language === 'ar' ? 'نسبة الإنجاز' : t("common.completion", "Completion")}
              </div>
            </div>
          </div>

          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-2">
              {language === 'ar' ? 'المعلم التالي' : t("common.next.milestone", "Next Milestone")}
            </h4>
            <p className="text-gray-700">{dashboard.progress_summary.next_milestone}</p>
          </div>
        </div>

        {/* Critical Alerts */}
        {dashboard.critical_alerts.length > 0 && (
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className={`text-lg font-semibold mb-4 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <AlertTriangle className={`w-5 h-5 mr-2 text-red-500 ${isRTL ? "space-x-reverse" : ""}`} />
              {language === 'ar' ? 'تنبيهات مهمة' : t("common.critical.alerts", "Critical Alerts")}
            </h3>

            <div className="space-y-3">
              {dashboard.critical_alerts.map((alert, index) => (
                <div
                  key={index}
                  className={`p-4 rounded-lg border-l-4 ${
                    alert.urgency === 'critical'
                      ? 'bg-red-50 border-red-500'
                      : 'bg-yellow-50 border-yellow-500'}
                  }`}
                >
                  <div className={`flex items-start ${isRTL ? "flex-row-reverse" : ""}`}>
                    <span className={`text-lg mr-2 ${isRTL ? "space-x-reverse" : ""}`}>{getAlertIcon(alert.type)}</span>
                    <div className={`flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <h4 className="font-medium text-gray-900">{alert.title}</h4>
                      <p className="text-sm text-gray-700 mt-1">{alert.message}</p>
                      {alert.action_required && (
                        <div className="mt-2">
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 ${isRTL ? "flex-row-reverse" : ""}`}>
                            {language === 'ar' ? 'إجراء مطلوب' : t("common.action.required", "Action Required")}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Priority Actions */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className={`text-lg font-semibold mb-4 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <Target className={`w-5 h-5 mr-2 text-blue-500 ${isRTL ? "space-x-reverse" : ""}`} />
            {language === 'ar' ? 'الإجراءات ذات الأولوية' : t("common.priority.actions", "Priority Actions")}
          </h3>

          <div className="space-y-3">
            {dashboard.priority_actions.slice(0, 5).map((action, index) => (
              <div
                key={index}
                className={`flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                  <span className={`text-lg mr-3 ${isRTL ? "space-x-reverse" : ""}`}>{getActionTypeIcon(action.type)}</span>
                  <div>
                    <h4 className="font-medium text-gray-900">{action.title}</h4>
                    <p className="text-sm text-gray-600">{action.description}</p>
                  </div>
                </div>
                <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    action.priority === 'high'
                      ? 'bg-red-100 text-red-800'
                      : action.priority === 'medium'
                      ? 'bg-yellow-100 text-yellow-800'
                      : 'bg-green-100 text-green-800'}
                  }`}>
                    {action.priority}
                  </span>
                  <Clock className="w-4 h-4 text-gray-400" />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Engagement Status */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className={`text-lg font-semibold mb-4 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <Activity className={`w-5 h-5 mr-2 text-purple-500 ${isRTL ? "space-x-reverse" : ""}`} />
            {language === 'ar' ? 'حالة المشاركة' : t("common.engagement.status", "Engagement Status")}
          </h3>

          <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
            <div>
              <div className={`text-lg font-medium ${getEngagementColor(dashboard.engagement_status)}`}>
                {dashboard.engagement_status === 'high'
                  ? (language === 'ar' ? 'مشاركة عالية' : t("dashboard.high.engagement", "High Engagement"))
                  : dashboard.engagement_status === 'medium'
                  ? (language === 'ar' ? 'مشاركة متوسطة' : t("dashboard.medium.engagement", "Medium Engagement"))
                  : (language === 'ar' ? 'مشاركة منخفضة' : t("dashboard.low.engagement", "Low Engagement"))
                }
              </div>
              <p className="text-sm text-gray-600 mt-1">
                {language === 'ar' ? 'مستوى نشاطك على المنصة' : t("common.your.activity.level", "Your activity level on the platform")}
              </p>
            </div>
            <div className={`w-16 h-16 rounded-full flex items-center justify-center ${
              dashboard.engagement_status === 'high'
                ? 'bg-green-100'
                : dashboard.engagement_status === 'medium'
                ? 'bg-yellow-100'
                : 'bg-red-100'}
            }`}>
              <Zap className={`w-8 h-8 ${getEngagementColor(dashboard.engagement_status)}`} />
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderRecommendations = () => {
    return (
      <div className="space-y-4">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className={`text-lg font-semibold mb-4 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <Lightbulb className={`w-5 h-5 mr-2 text-yellow-500 ${isRTL ? "space-x-reverse" : ""}`} />
            {language === 'ar' ? 'التوصيات الاستباقية' : t("common.proactive.recommendations", "Proactive Recommendations")}
          </h3>

          {recommendations.length === 0 ? (
            <div className="text-center py-8">
              <Lightbulb className="w-12 h-12 text-gray-400 mx-auto mb-3" />
              <p className="text-gray-600">
                {language === 'ar' ? 'لا توجد توصيات متاحة حالياً' : t("common.no.recommendations.available", "No recommendations available at the moment")}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {recommendations.map((recommendation, index) => (
                <div
                  key={index}
                  className="p-4 border rounded-lg hover:shadow-md transition-shadow"
                >
                  <div className={`flex items-start justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className={`flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <div className={`flex items-center mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <span className={`text-lg mr-2 ${isRTL ? "space-x-reverse" : ""}`}>{getActionTypeIcon(recommendation.action_type)}</span>
                        <h4 className="font-medium text-gray-900">{recommendation.title}</h4>
                        <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${
                          recommendation.priority === 'critical'
                            ? 'bg-red-100 text-red-800'
                            : recommendation.priority === 'high'
                            ? 'bg-orange-100 text-orange-800'
                            : recommendation.priority === 'medium'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-green-100 text-green-800'}
                        }`}>
                          {getPriorityIcon(recommendation.priority)} {recommendation.priority}
                        </span>
                      </div>
                      <p className="text-gray-700 mb-2">{recommendation.description}</p>
                      <div className={`flex items-center text-sm text-gray-500 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <Clock className={`w-4 h-4 mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                        <span>{formatTimeEstimate(recommendation.estimated_time)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderAlerts = () => {
    const criticalAlerts = alerts.filter(alert => alert.urgency === 'critical');
    const highAlerts = alerts.filter(alert => alert.urgency === 'high');
    const otherAlerts = alerts.filter(alert => !['critical', 'high'].includes(alert.urgency));

    return (
      <div className="space-y-6">
        {/* Critical Alerts */}
        {criticalAlerts.length > 0 && (
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className={`text-lg font-semibold mb-4 flex items-center text-red-600 ${isRTL ? "flex-row-reverse" : ""}`}>
              <AlertTriangle className={`w-5 h-5 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
              {language === 'ar' ? 'تنبيهات حرجة' : t("common.critical.alerts", "Critical Alerts")}
            </h3>
            <div className="space-y-3">
              {criticalAlerts.map((alert, index) => (
                <div key={index} className="p-4 bg-red-50 border-l-4 border-red-500 rounded-lg">
                  <div className={`flex items-start ${isRTL ? "flex-row-reverse" : ""}`}>
                    <span className={`text-lg mr-2 ${isRTL ? "space-x-reverse" : ""}`}>{getAlertIcon(alert.type)}</span>
                    <div className={`flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <h4 className="font-medium text-red-900">{alert.title}</h4>
                      <p className="text-sm text-red-700 mt-1">{alert.message}</p>
                      {alert.action_required && (
                        <div className="mt-2">
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 ${isRTL ? "flex-row-reverse" : ""}`}>
                            {language === 'ar' ? 'إجراء مطلوب' : t("common.action.required", "Action Required")}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {alerts.length === 0 && (
          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="text-center py-8">
              <CheckCircle className="w-12 h-12 text-green-400 mx-auto mb-3" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {language === 'ar' ? 'لا توجد تنبيهات' : t("common.no.alerts", "No Alerts")}
              </h3>
              <p className="text-gray-600">
                {language === 'ar' ? 'كل شيء يبدو جيداً!' : t("common.everything.looks.good", "Everything looks good!")}
              </p>
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderActions = () => {
    return (
      <div className="space-y-4">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className={`text-lg font-semibold mb-4 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <Target className={`w-5 h-5 mr-2 text-blue-500 ${isRTL ? "space-x-reverse" : ""}`} />
            {language === 'ar' ? 'الإجراءات التالية' : t("common.next.actions", "Next Actions")}
          </h3>

          {nextActions.length === 0 ? (
            <div className="text-center py-8">
              <CheckCircle className="w-12 h-12 text-green-400 mx-auto mb-3" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {language === 'ar' ? 'لا توجد إجراءات معلقة' : t("common.no.pending.actions", "No Pending Actions")}
              </h3>
              <p className="text-gray-600">
                {language === 'ar' ? 'أنت محدث مع جميع المهام!' : 'You\'re up to date with all tasks!'}
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {nextActions.map((action, index) => (
                <div key={index} className={`p-4 rounded-lg border-l-4 ${
                  action.priority === 'high'
                    ? 'bg-red-50 border-red-500'
                    : action.priority === 'medium'
                    ? 'bg-yellow-50 border-yellow-500'
                    : 'bg-green-50 border-green-500'}
                }`}>
                  <div className={`flex items-start justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className={`flex items-start flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <span className={`text-lg mr-3 ${isRTL ? "space-x-reverse" : ""}`}>{getActionTypeIcon(action.type)}</span>
                      <div className={`flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <h4 className={`font-medium ${
                          action.priority === 'high'
                            ? 'text-red-900'
                            : action.priority === 'medium'
                            ? 'text-yellow-900'
                            : 'text-green-900'}
                        }`}>
                          {action.title}
                        </h4>
                        <p className={`text-sm mt-1 ${
                          action.priority === 'high'
                            ? 'text-red-700'
                            : action.priority === 'medium'
                            ? 'text-yellow-700'
                            : 'text-green-700'}
                        }`}>
                          {action.description}
                        </p>
                        <div className={`flex items-center mt-2 text-sm ${
                          action.priority === 'high'
                            ? 'text-red-600'
                            : action.priority === 'medium'
                            ? 'text-yellow-600'
                            : 'text-green-600'}
                        }`}>
                          <Clock className={`w-4 h-4 mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                          <span>Due: {new Date(action.deadline).toLocaleDateString()}</span>
                        </div>
                      </div>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      action.priority === 'high'
                        ? 'bg-red-100 text-red-800'
                        : action.priority === 'medium'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-green-100 text-green-800'}
                    }`}>
                      {action.priority}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return renderOverview();
      case 'recommendations':
        return renderRecommendations();
      case 'alerts':
        return renderAlerts();
      case 'actions':
        return renderActions();
      default:
        return renderOverview();
    }
  };

  if (loading) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <div className="text-center">
          <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-500" />
          <p className="text-gray-600">
            {language === 'ar' ? 'جاري تحميل لوحة التحكم الذكية...' : t("common.loading.intelligent.dashboard", "Loading intelligent dashboard...")}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-gray-50 min-h-screen ${className}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className={`flex items-center justify-between h-16 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <Brain className={`w-8 h-8 text-blue-500 mr-3 ${isRTL ? "space-x-reverse" : ""}`} />
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  {language === 'ar' ? 'لوحة التحكم الذكية' : t("common.intelligent.dashboard", "Intelligent Dashboard")}
                </h1>
                <p className="text-sm text-gray-600">
                  {language === 'ar' ? 'مدعوم بالذكاء الاصطناعي' : t("common.aipowered.business.intelligence", "AI-Powered Business Intelligence")}
                </p>
              </div>
            </div>

            <div className={`flex items-center space-x-4 ${isRTL ? "flex-row-reverse" : ""}`}>
              {dashboard && (
                <div className={`flex items-center text-sm text-gray-500 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <Activity className={`w-4 h-4 mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                  <span className={getEngagementColor(dashboard.engagement_status)}>
                    {dashboard.engagement_status === 'high'
                      ? (language === 'ar' ? 'نشط' : t("dashboard.active", "Active"))
                      : dashboard.engagement_status === 'medium'
                      ? (language === 'ar' ? 'متوسط' : t("dashboard.moderate", "Moderate"))
                      : (language === 'ar' ? 'منخفض' : 'Low')
                    }
                  </span>
                </div>
              )}

              <button
                onClick={refreshDashboard}
                disabled={refreshing}
                className={`flex items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                {refreshing
                  ? (language === 'ar' ? 'جاري التحديث...' : t("dashboard.refreshing", "Refreshing..."))
                  : (language === 'ar' ? 'تحديث' : t("common.refresh", t("common.refresh", "Refresh")))
                }
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <AlertTriangle className={`w-5 h-5 text-red-500 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
              <p className="text-red-700">{error}</p>
            </div>
          </div>
        )}

        {/* Tabs */}
        <div className="border-b border-gray-200 mb-6">
          <nav className={`-mb-px flex space-x-8 ${isRTL ? "flex-row-reverse" : ""}`}>
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}
                }`}
              >
                {tab.icon}
                <span className={`ml-2 ${isRTL ? "space-x-reverse" : ""}`}>{tab.name}</span>
                {/* Add notification badges */}
                {tab.id === 'alerts' && alerts.filter(a => a.urgency === 'critical').length > 0 && (
                  <span className={`ml-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    {alerts.filter(a => a.urgency === 'critical').length}
                  </span>
                )}
                {tab.id === 'actions' && nextActions.filter(a => a.priority === 'high').length > 0 && (
                  <span className={`ml-2 bg-orange-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    {nextActions.filter(a => a.priority === 'high').length}
                  </span>
                )}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        {renderTabContent()}
      </div>
    </div>
  );
};

export default IntelligentDashboard;