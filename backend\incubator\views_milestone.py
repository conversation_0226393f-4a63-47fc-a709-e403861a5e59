from rest_framework import viewsets, permissions, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from django.db.models import Count, Avg, F, Q
from django_filters.rest_framework import DjangoFilterBackend
from django.contrib.auth.models import User

from .models import BusinessIdea, MentorProfile, MentorExpertise, MentorshipMatch
from .models_milestone import (
    BusinessMilestone, BusinessGoal, MentorRecommendation, BusinessAnalytics
)
from .serializers_milestone import (
    BusinessMilestoneSerializer, BusinessGoalSerializer,
    MentorRecommendationSerializer, BusinessAnalyticsSerializer
)
from api.permissions import IsAdminUser


class BusinessMilestoneViewSet(viewsets.ModelViewSet):
    queryset = BusinessMilestone.objects.all().order_by('due_date', '-priority')
    serializer_class = BusinessMilestoneSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['business_idea', 'status', 'priority', 'assigned_to']
    search_fields = ['title', 'description']
    ordering_fields = ['due_date', 'priority', 'created_at', 'updated_at']

    def get_queryset(self):
        # For regular users, only show milestones for their own business ideas or where they are assigned
        if self.request.user.is_authenticated and not (self.request.user.is_staff or self.request.user.is_superuser):
            return BusinessMilestone.objects.filter(
                Q(business_idea__owner=self.request.user) |
                Q(business_idea__collaborators=self.request.user) |
                Q(assigned_to=self.request.user)
            ).distinct().order_by('due_date', '-priority')
        return super().get_queryset()

    def perform_create(self, serializer):
        serializer.save(created_by_id=self.request.user.id)

    @action(detail=True, methods=['post'])
    def complete(self, request, pk=None):
        """Mark a milestone as completed"""
        milestone = self.get_object()

        # Check if user has permission to update this milestone
        if not (request.user.is_staff or request.user.is_superuser or
                request.user == milestone.business_idea.owner or
                milestone.business_idea.collaborators.filter(id=request.user.id).exists() or
                request.user == milestone.assigned_to):
            return Response(
                {"error": "You don't have permission to update this milestone"},
                status=status.HTTP_403_FORBIDDEN
            )

        completion_notes = request.data.get('completion_notes', '')

        milestone.status = 'completed'
        milestone.completion_date = timezone.now().date()
        milestone.completion_notes = completion_notes
        milestone.save()

        return Response({
            "message": "Milestone marked as completed",
            "milestone": BusinessMilestoneSerializer(milestone).data
        })


class BusinessGoalViewSet(viewsets.ModelViewSet):
    queryset = BusinessGoal.objects.all().order_by('target_date', 'timeframe')
    serializer_class = BusinessGoalSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['business_idea', 'status', 'timeframe']
    search_fields = ['title', 'description']
    ordering_fields = ['target_date', 'timeframe', 'created_at', 'updated_at']

    def get_queryset(self):
        # For regular users, only show goals for their own business ideas
        if self.request.user.is_authenticated and not (self.request.user.is_staff or self.request.user.is_superuser):
            return BusinessGoal.objects.filter(
                Q(business_idea__owner=self.request.user) |
                Q(business_idea__collaborators=self.request.user)
            ).distinct().order_by('target_date', 'timeframe')
        return super().get_queryset()

    def perform_create(self, serializer):
        serializer.save(created_by_id=self.request.user.id)

    @action(detail=True, methods=['post'])
    def achieve(self, request, pk=None):
        """Mark a goal as achieved"""
        goal = self.get_object()

        # Check if user has permission to update this goal
        if not (request.user.is_staff or request.user.is_superuser or
                request.user == goal.business_idea.owner or
                goal.business_idea.collaborators.filter(id=request.user.id).exists()):
            return Response(
                {"error": "You don't have permission to update this goal"},
                status=status.HTTP_403_FORBIDDEN
            )

        achievement_notes = request.data.get('achievement_notes', '')

        goal.status = 'achieved'
        goal.achievement_date = timezone.now().date()
        goal.achievement_notes = achievement_notes
        goal.save()

        return Response({
            "message": "Goal marked as achieved",
            "goal": BusinessGoalSerializer(goal).data
        })


class MentorRecommendationViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = MentorRecommendation.objects.all().order_by('-match_score')
    serializer_class = MentorRecommendationSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['business_idea', 'is_applied', 'is_matched']

    def get_queryset(self):
        # For regular users, only show recommendations for their own business ideas
        if self.request.user.is_authenticated and not (self.request.user.is_staff or self.request.user.is_superuser):
            return MentorRecommendation.objects.filter(
                Q(business_idea__owner=self.request.user) |
                Q(business_idea__collaborators=self.request.user)
            ).distinct().order_by('-match_score')
        return super().get_queryset()

    @action(detail=True, methods=['get'])
    def ai_explanation(self, request, pk=None):
        """Get AI-powered explanation of why this mentor is a good match"""
        recommendation = self.get_object()

        # Check if user has permission to view this recommendation
        if not (request.user.is_staff or request.user.is_superuser or
                request.user == recommendation.business_idea.owner or
                recommendation.business_idea.collaborators.filter(id=request.user.id).exists()):
            return Response(
                {"error": "You don't have permission to view this recommendation"},
                status=status.HTTP_403_FORBIDDEN
            )

        # Use enhanced matching algorithm to generate explanation
        from .enhanced_matching import EnhancedMentorMatcher
        matcher = EnhancedMentorMatcher(recommendation.business_idea.id)
        explanation = matcher.generate_ai_match_explanation(recommendation.mentor.id)

        return Response({
            "recommendation_id": recommendation.id,
            "mentor_id": recommendation.mentor.id,
            "mentor_name": recommendation.mentor.user.get_full_name() or recommendation.mentor.user.username,
            "business_idea_id": recommendation.business_idea.id,
            "business_idea_title": recommendation.business_idea.title,
            "match_score": recommendation.match_score,
            "ai_explanation": explanation
        })

    @action(detail=False, methods=['post'])
    def generate_recommendations(self, request):
        """Generate mentor recommendations for a business idea using enhanced algorithm"""
        business_idea_id = request.data.get('business_idea_id')

        if not business_idea_id:
            return Response(
                {"error": "Business idea ID is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            business_idea = BusinessIdea.objects.get(id=business_idea_id)
        except BusinessIdea.DoesNotExist:
            return Response(
                {"error": "Business idea not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Check if user has permission to generate recommendations for this idea
        if not (request.user.is_staff or request.user.is_superuser or
                request.user == business_idea.owner or
                business_idea.collaborators.filter(id=request.user.id).exists()):
            return Response(
                {"error": "You don't have permission to generate recommendations for this idea"},
                status=status.HTTP_403_FORBIDDEN
            )

        # Use enhanced matching algorithm
        from .enhanced_matching import EnhancedMentorMatcher
        matcher = EnhancedMentorMatcher(business_idea_id)
        recommendations = matcher.get_mentor_recommendations(limit=10)

        return Response({
            "message": f"Generated {len(recommendations)} mentor recommendations using enhanced algorithm",
            "recommendations": MentorRecommendationSerializer(recommendations, many=True).data
        })


class BusinessAnalyticsViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = BusinessAnalytics.objects.all()
    serializer_class = BusinessAnalyticsSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['business_idea']

    def get_queryset(self):
        # For regular users, only show analytics for their own business ideas
        if self.request.user.is_authenticated and not (self.request.user.is_staff or self.request.user.is_superuser):
            return BusinessAnalytics.objects.filter(
                Q(business_idea__owner=self.request.user) |
                Q(business_idea__collaborators=self.request.user)
            ).distinct()
        return super().get_queryset()

    @action(detail=False, methods=['post'])
    def calculate_analytics(self, request):
        """Calculate analytics for a business idea"""
        business_idea_id = request.data.get('business_idea_id')

        if not business_idea_id:
            return Response(
                {"error": "Business idea ID is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            business_idea = BusinessIdea.objects.get(id=business_idea_id)
        except BusinessIdea.DoesNotExist:
            return Response(
                {"error": "Business idea not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Check if user has permission to calculate analytics for this idea
        if not (request.user.is_staff or request.user.is_superuser or
                request.user == business_idea.owner or
                business_idea.collaborators.filter(id=request.user.id).exists()):
            return Response(
                {"error": "You don't have permission to calculate analytics for this idea"},
                status=status.HTTP_403_FORBIDDEN
            )

        # Calculate analytics
        analytics, created = BusinessAnalytics.objects.get_or_create(business_idea=business_idea)

        # Progress rate (updates per month)
        from datetime import timedelta

        progress_updates = business_idea.progress_updates.all()
        if progress_updates.exists():
            first_update = progress_updates.order_by('created_at').first()
            last_update = progress_updates.order_by('-created_at').first()

            months_diff = (last_update.created_at.year - first_update.created_at.year) * 12 + (last_update.created_at.month - first_update.created_at.month)
            if months_diff == 0:
                months_diff = 1  # Avoid division by zero

            progress_rate = progress_updates.count() / months_diff
        else:
            progress_rate = 0

        # Milestone completion rate
        milestones = BusinessMilestone.objects.filter(business_idea=business_idea)
        if milestones.exists():
            completed_on_time = milestones.filter(
                status='completed',
                completion_date__lte=F('due_date')
            ).count()

            milestone_completion_rate = (completed_on_time / milestones.count()) * 100
        else:
            milestone_completion_rate = 0

        # Goal achievement rate
        goals = BusinessGoal.objects.filter(business_idea=business_idea)
        if goals.exists():
            achieved_goals = goals.filter(status='achieved').count()
            goal_achievement_rate = (achieved_goals / goals.count()) * 100
        else:
            goal_achievement_rate = 0

        # Team size
        team_size = 1 + business_idea.collaborators.count()  # Owner + collaborators

        # Mentor engagement
        mentorship_matches = MentorshipMatch.objects.filter(business_idea=business_idea)
        if mentorship_matches.exists():
            active_matches = mentorship_matches.filter(status='active').count()
            sessions = sum(match.sessions.count() for match in mentorship_matches)

            if sessions > 0:
                mentor_engagement = min(100, (sessions / active_matches) * 20)  # 5 sessions = 100% engagement
            else:
                mentor_engagement = 0
        else:
            mentor_engagement = 0

        # Industry percentile (based on tags)
        tags = business_idea.tags.all()
        if tags.exists():
            similar_ideas = BusinessIdea.objects.filter(
                tags__in=tags,
                moderation_status='approved'
            ).distinct().exclude(id=business_idea.id)

            if similar_ideas.exists():
                # Compare progress rate
                better_than = similar_ideas.annotate(
                    update_count=Count('progress_updates')
                ).filter(update_count__lt=progress_updates.count()).count()

                industry_percentile = (better_than / similar_ideas.count()) * 100
            else:
                industry_percentile = 50  # Default if no similar ideas
        else:
            industry_percentile = 50  # Default if no tags

        # Stage percentile
        same_stage_ideas = BusinessIdea.objects.filter(
            current_stage=business_idea.current_stage,
            moderation_status='approved'
        ).exclude(id=business_idea.id)

        if same_stage_ideas.exists():
            # Compare progress rate
            better_than = same_stage_ideas.annotate(
                update_count=Count('progress_updates')
            ).filter(update_count__lt=progress_updates.count()).count()

            stage_percentile = (better_than / same_stage_ideas.count()) * 100
        else:
            stage_percentile = 50  # Default if no ideas in same stage

        # Update analytics
        analytics.progress_rate = progress_rate
        analytics.milestone_completion_rate = milestone_completion_rate
        analytics.goal_achievement_rate = goal_achievement_rate
        analytics.team_size = team_size
        analytics.mentor_engagement = mentor_engagement
        analytics.industry_percentile = industry_percentile
        analytics.stage_percentile = stage_percentile
        analytics.save()

        return Response({
            "message": "Analytics calculated successfully",
            "analytics": BusinessAnalyticsSerializer(analytics).data
        })
