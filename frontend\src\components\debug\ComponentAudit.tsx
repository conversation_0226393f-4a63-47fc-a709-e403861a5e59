import React, { useState, useEffect } from 'react';
import { Alert<PERSON>riangle, CheckCircle, X, RefreshCw } from 'lucide-react';

interface ComponentStatus {
  name: string;
  path: string;
  status: 'loading' | 'exists' | 'missing' | 'error';
  error?: string;
}

/**
 * Component Audit Tool - Development only
 * Checks for missing or broken component imports
 */
const ComponentAudit: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [components, setComponents] = useState<ComponentStatus[]>([]);
  const [isAuditing, setIsAuditing] = useState(false);

  // Critical components to check
  const criticalComponents = [
    { name: 'UserDashboard', path: '../dashboard/user-dashboard/UserDashboard' },
    { name: 'BusinessIdeasPage', path: '../../pages/dashboard/BusinessIdeasPage' },
    { name: 'BusinessPlanPage', path: '../../pages/dashboard/BusinessPlanPage' },
    { name: 'TemplatesOverviewPage', path: '../../pages/dashboard/TemplatesOverviewPage' },
    { name: 'PostsPage', path: '../../pages/dashboard/PostsPage' },
    { name: 'EventsPage', path: '../../pages/dashboard/EventsPage' },
    { name: 'ResourcesPage', path: '../../pages/dashboard/ResourcesPage' },
    { name: 'ProfilePage', path: '../../pages/ProfilePage' },
    { name: 'UserSettings', path: '../../pages/UserSettings' },
    { name: 'Dashboard', path: '../admin/dashboard/Dashboard' },
    { name: 'UsersManagement', path: '../admin/users/UsersManagement' },
  ];

  const checkComponent = async (name: string, path: string): Promise<ComponentStatus> => {
    try {
      // Try to dynamically import the component
      // @vite-ignore - Dynamic import with variable path for component auditing
      await import(/* @vite-ignore */ path);
      return { name, path, status: 'exists' };
    } catch (error) {
      return {
        name,
        path,
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  };

  const runAudit = async () => {
    setIsAuditing(true);
    setComponents(criticalComponents.map(c => ({ ...c, status: 'loading' as const })));

    const results = await Promise.all(
      criticalComponents.map(component => checkComponent(component.name, component.path))
    );

    setComponents(results);
    setIsAuditing(false);
  };

  useEffect(() => {
    if (isOpen && components.length === 0) {
      runAudit();
    }
  }, [isOpen]);

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const existingCount = components.filter(c => c.status === 'exists').length;
  const errorCount = components.filter(c => c.status === 'error').length;

  return (
    <>
      {/* Floating Button */}
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-20 right-4 z-50 bg-purple-600 hover:bg-purple-700 text-white p-3 rounded-full shadow-lg transition-colors"
        title="Component Audit"
      >
        {errorCount > 0 ? (
          <AlertTriangle className="w-5 h-5" />
        ) : (
          <CheckCircle className="w-5 h-5" />
        )}
      </button>

      {/* Audit Panel */}
      {isOpen && (
        <div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-2xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Component Audit
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Checking critical component imports
                </p>
              </div>
              <button
                onClick={() => setIsOpen(false)}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Stats */}
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{existingCount}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-300">Working</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{errorCount}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-300">Errors</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{components.length}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-300">Total</div>
                </div>
              </div>
            </div>

            {/* Component List */}
            <div className="p-6 overflow-y-auto max-h-96">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  Component Status
                </h3>
                <button
                  onClick={runAudit}
                  disabled={isAuditing}
                  className="flex items-center gap-2 px-3 py-1 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
                >
                  <RefreshCw className={`w-4 h-4 ${isAuditing ? 'animate-spin' : ''}`} />
                  Re-audit
                </button>
              </div>

              <div className="space-y-2">
                {components.map((component, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                  >
                    <div className="flex-1">
                      <div className="font-medium text-gray-900 dark:text-white">
                        {component.name}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-300">
                        {component.path}
                      </div>
                      {component.error && (
                        <div className="text-sm text-red-600 mt-1">
                          {component.error}
                        </div>
                      )}
                    </div>
                    <div className="ml-4">
                      {component.status === 'loading' && (
                        <div className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
                      )}
                      {component.status === 'exists' && (
                        <CheckCircle className="w-5 h-5 text-green-600" />
                      )}
                      {component.status === 'error' && (
                        <AlertTriangle className="w-5 h-5 text-red-600" />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Footer */}
            <div className="p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
              <p className="text-sm text-gray-600 dark:text-gray-300">
                This tool helps identify missing or broken component imports that might cause routing issues.
              </p>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ComponentAudit;
