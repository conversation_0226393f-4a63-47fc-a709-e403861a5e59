import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { useAppSelector } from '../../store/hooks';
import { useCRUD } from '../../hooks/useCRUD';
import { MentorProfile, mentorProfilesAPI } from '../../services/incubatorApi';
import MentorProfileForm from '../../components/incubator/forms/MentorProfileForm';
import { CRUDTable } from '../../components/common';
import AuthenticatedLayout from '../../components/layout/AuthenticatedLayout';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  Users, 
  Star,
  Calendar,
  AlertCircle,
  CheckCircle,
  Clock,
  Award,
  Globe
} from 'lucide-react';

const MentorProfilesPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { user } = useAppSelector(state => state.auth);
  
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [selectedProfile, setSelectedProfile] = useState<MentorProfile | null>(null);

  // CRUD operations for mentor profiles
  const mentorProfilesCRUD = useCRUD({
    create: async (data: Partial<MentorProfile>) => {
      if (!user) throw new Error('User not authenticated');
      return mentorProfilesAPI.createMentorProfile({
        ...data,
        user_id: user.id
      });
    },
    read: () => mentorProfilesAPI.getMentorProfiles(),
    update: (id: number, data: Partial<MentorProfile>) => 
      mentorProfilesAPI.updateMentorProfile(id, data),
    delete: (id: number) => mentorProfilesAPI.deleteMentorProfile(id)
  }, {
    onSuccess: (operation) => {
      if (operation === 'create') {
        setShowCreateForm(false);
      } else if (operation === 'update') {
        setShowEditForm(false);
        setSelectedProfile(null);
      } else if (operation === 'delete') {
        setShowDeleteDialog(false);
        setSelectedProfile(null);
      }
    }
  });

  // Load data on component mount
  useEffect(() => {
    mentorProfilesCRUD.readItems();
  }, []);

  // Filter to show only user's profile (users can only have one mentor profile)
  const userMentorProfile = mentorProfilesCRUD.data.find(profile => 
    profile.user.id === user?.id
  );

  // Handle create
  const handleCreate = async (data: Partial<MentorProfile>) => {
    return await mentorProfilesCRUD.createItem(data);
  };

  // Handle edit
  const handleEdit = (profile: MentorProfile) => {
    setSelectedProfile(profile);
    setShowEditForm(true);
  };

  // Handle update
  const handleUpdate = async (data: Partial<MentorProfile>) => {
    if (!selectedProfile) return false;
    return await mentorProfilesCRUD.updateItem(selectedProfile.id, data);
  };

  // Handle delete
  const handleDelete = (profile: MentorProfile) => {
    setSelectedProfile(profile);
    setShowDeleteDialog(true);
  };

  // Confirm delete
  const confirmDelete = async () => {
    if (!selectedProfile) return;
    await mentorProfilesCRUD.deleteItem(selectedProfile.id);
  };

  // Table columns configuration
  const columns = [
    {
      key: 'user',
      label: t('mentor.mentor', 'Mentor'),
      render: (profile: MentorProfile) => (
        <div className="flex items-center gap-3">
          {profile.user.profile?.profile_image ? (
            <img 
              src={profile.user.profile.profile_image} 
              alt={profile.user.username}
              className="w-10 h-10 rounded-full object-cover"
            />
          ) : (
            <div className="w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center">
              <span className="text-white font-medium">
                {profile.user.first_name?.[0] || profile.user.username[0]}
              </span>
            </div>
          )}
          <div>
            <div className="font-medium text-white">
              {profile.user.first_name} {profile.user.last_name}
            </div>
            <div className="text-sm text-gray-400">@{profile.user.username}</div>
          </div>
        </div>
      )
    },
    {
      key: 'company_position',
      label: t('mentor.companyPosition', 'Company & Position'),
      render: (profile: MentorProfile) => (
        <div>
          {profile.company && (
            <div className="font-medium text-white">{profile.company}</div>
          )}
          {profile.position && (
            <div className="text-sm text-gray-400">{profile.position}</div>
          )}
        </div>
      )
    },
    {
      key: 'experience',
      label: t('mentor.experience', 'Experience'),
      render: (profile: MentorProfile) => (
        <div className="flex items-center gap-2">
          <Award size={16} className="text-yellow-400" />
          <span className="text-gray-300">
            {profile.years_of_experience} {t('mentor.years', 'years')}
          </span>
        </div>
      )
    },
    {
      key: 'availability',
      label: t('mentor.availability', 'Availability'),
      render: (profile: MentorProfile) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          profile.availability === 'high' ? 'bg-green-900/50 text-green-200' :
          profile.availability === 'medium' ? 'bg-blue-900/50 text-blue-200' :
          profile.availability === 'low' ? 'bg-yellow-900/50 text-yellow-200' :
          'bg-red-900/50 text-red-200'
        }`}>
          {profile.availability_display || profile.availability}
        </span>
      )
    },
    {
      key: 'status',
      label: t('mentor.status', 'Status'),
      render: (profile: MentorProfile) => (
        <div className="flex items-center gap-2">
          {profile.is_verified ? (
            <CheckCircle size={16} className="text-green-400" />
          ) : (
            <Clock size={16} className="text-yellow-400" />
          )}
          <span className={`text-sm ${
            profile.is_verified ? 'text-green-400' : 'text-yellow-400'
          }`}>
            {profile.is_verified ? t('mentor.verified', 'Verified') : t('mentor.pending', 'Pending')}
          </span>
        </div>
      )
    },
    {
      key: 'mentees',
      label: t('mentor.mentees', 'Mentees'),
      render: (profile: MentorProfile) => (
        <div className="flex items-center gap-2">
          <Users size={16} className="text-purple-400" />
          <span className="text-gray-300">
            {profile.active_mentorships_count || 0}/{profile.max_mentees}
          </span>
        </div>
      )
    }
  ];

  // Table actions
  const actions = [
    {
      label: t('common.view', 'View'),
      icon: Eye,
      onClick: (profile: MentorProfile) => {
        // Navigate to profile detail page
        window.location.href = `/mentors/${profile.id}`;
      },
      variant: 'secondary' as const
    },
    {
      label: t('common.edit', 'Edit'),
      icon: Edit,
      onClick: handleEdit,
      variant: 'primary' as const,
      condition: (profile: MentorProfile) => profile.user.id === user?.id
    },
    {
      label: t('common.delete', 'Delete'),
      icon: Trash2,
      onClick: handleDelete,
      variant: 'danger' as const,
      condition: (profile: MentorProfile) => profile.user.id === user?.id
    }
  ];

  // Stats cards data
  const allProfiles = mentorProfilesCRUD.data;
  const stats = [
    {
      title: t('mentor.totalMentors', 'Total Mentors'),
      value: allProfiles.length,
      icon: Users,
      color: 'blue'
    },
    {
      title: t('mentor.verifiedMentors', 'Verified'),
      value: allProfiles.filter(profile => profile.is_verified).length,
      icon: CheckCircle,
      color: 'green'
    },
    {
      title: t('mentor.acceptingMentees', 'Accepting Mentees'),
      value: allProfiles.filter(profile => profile.is_accepting_mentees).length,
      icon: Star,
      color: 'yellow'
    },
    {
      title: t('mentor.myProfile', 'My Profile'),
      value: userMentorProfile ? 1 : 0,
      icon: Award,
      color: 'purple'
    }
  ];

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
      <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="space-y-6">
        {/* Header */}
        <div className={`flex flex-col sm:flex-row sm:items-center sm:justify-between ${isRTL ? 'sm:flex-row-reverse' : ''}`}>
          <div>
            <h1 className="text-2xl font-bold text-white">
              {t('mentor.mentorProfiles', 'Mentor Profiles')}
            </h1>
            <p className="text-gray-400 mt-1">
              {t('mentor.manageYourProfile', 'Manage your mentor profile and connect with mentees')}
            </p>
          </div>
          {!userMentorProfile && (
            <button
              onClick={() => setShowCreateForm(true)}
              className="mt-4 sm:mt-0 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center gap-2"
            >
              <Plus size={20} />
              {t('mentor.createProfile', 'Create Profile')}
            </button>
          )}
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <div key={index} className="bg-gray-800 rounded-lg p-6">
              <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div>
                  <p className="text-gray-400 text-sm">{stat.title}</p>
                  <p className="text-2xl font-bold text-white mt-1">{stat.value}</p>
                </div>
                <div className={`p-3 rounded-lg ${
                  stat.color === 'blue' ? 'bg-blue-900/50' :
                  stat.color === 'green' ? 'bg-green-900/50' :
                  stat.color === 'yellow' ? 'bg-yellow-900/50' :
                  'bg-purple-900/50'
                }`}>
                  <stat.icon size={24} className={
                    stat.color === 'blue' ? 'text-blue-400' :
                    stat.color === 'green' ? 'text-green-400' :
                    stat.color === 'yellow' ? 'text-yellow-400' :
                    'text-purple-400'
                  } />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* User's Profile Card */}
        {userMentorProfile && (
          <div className="bg-gray-800 rounded-lg p-6">
            <div className={`flex items-center justify-between mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <h2 className="text-lg font-semibold text-white">
                {t('mentor.yourProfile', 'Your Mentor Profile')}
              </h2>
              <button
                onClick={() => handleEdit(userMentorProfile)}
                className="px-3 py-1 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center gap-2"
              >
                <Edit size={16} />
                {t('common.edit', 'Edit')}
              </button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div>
                <p className="text-gray-400 text-sm">{t('mentor.status', 'Status')}</p>
                <div className="flex items-center gap-2 mt-1">
                  {userMentorProfile.is_verified ? (
                    <CheckCircle size={16} className="text-green-400" />
                  ) : (
                    <Clock size={16} className="text-yellow-400" />
                  )}
                  <span className={userMentorProfile.is_verified ? 'text-green-400' : 'text-yellow-400'}>
                    {userMentorProfile.is_verified ? t('mentor.verified', 'Verified') : t('mentor.pending', 'Pending Verification')}
                  </span>
                </div>
              </div>
              
              <div>
                <p className="text-gray-400 text-sm">{t('mentor.availability', 'Availability')}</p>
                <p className="text-white mt-1">{userMentorProfile.availability_display}</p>
              </div>
              
              <div>
                <p className="text-gray-400 text-sm">{t('mentor.activeMentorships', 'Active Mentorships')}</p>
                <p className="text-white mt-1">
                  {userMentorProfile.active_mentorships_count || 0} / {userMentorProfile.max_mentees}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Error Display */}
        {mentorProfilesCRUD.error && (
          <div className="bg-red-900/50 border border-red-500 rounded-lg p-4">
            <div className="flex items-center gap-2">
              <AlertCircle size={20} className="text-red-400" />
              <span className="text-red-200">{mentorProfilesCRUD.error}</span>
            </div>
          </div>
        )}

        {/* Mentor Profiles Table */}
        <div className="bg-gray-800 rounded-lg">
          <CRUDTable
            data={mentorProfilesCRUD.data}
            columns={columns}
            actions={actions}
            isLoading={mentorProfilesCRUD.isLoading}
            emptyMessage={t('mentor.noProfiles', 'No mentor profiles found. Create your profile to start mentoring!')}
            searchPlaceholder={t('mentor.searchProfiles', 'Search mentor profiles...')}
            title={t('mentor.allMentors', 'All Mentors')}
            createButtonLabel={t('mentor.createProfile', 'Create Profile')}
            onCreate={() => setShowCreateForm(true)}
            showCreateButton={!userMentorProfile}
          />
        </div>
      </div>

      {/* Create Form Modal */}
      {showCreateForm && (
        <MentorProfileForm
          mode="create"
          onSubmit={handleCreate}
          onCancel={() => setShowCreateForm(false)}
          isSubmitting={mentorProfilesCRUD.isLoading}
        />
      )}

      {/* Edit Form Modal */}
      {showEditForm && selectedProfile && (
        <MentorProfileForm
          mode="edit"
          initialData={selectedProfile}
          onSubmit={handleUpdate}
          onCancel={() => {
            setShowEditForm(false);
            setSelectedProfile(null);
          }}
          isSubmitting={mentorProfilesCRUD.isLoading}
        />
      )}

      {/* Delete Confirmation Dialog */}
      {showDeleteDialog && selectedProfile && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-gray-900 rounded-lg shadow-xl w-full max-w-md">
            <div className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <AlertCircle size={24} className="text-red-400" />
                <h3 className="text-lg font-semibold text-white">
                  {t('common.confirmDelete', 'Confirm Delete')}
                </h3>
              </div>
              <p className="text-gray-300 mb-6">
                {t('mentor.deleteConfirmation', 'Are you sure you want to delete your mentor profile? This will also end all active mentorships. This action cannot be undone.')}
              </p>
              <div className={`flex gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <button
                  onClick={() => {
                    setShowDeleteDialog(false);
                    setSelectedProfile(null);
                  }}
                  className="flex-1 px-4 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors"
                  disabled={mentorProfilesCRUD.isLoading}
                >
                  {t('common.cancel', 'Cancel')}
                </button>
                <button
                  onClick={confirmDelete}
                  className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
                  disabled={mentorProfilesCRUD.isLoading}
                >
                  {mentorProfilesCRUD.isLoading ? t('common.deleting', 'Deleting...') : t('common.delete', 'Delete')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
              </div>
        </div>
      </div>
    </div>
  );
};

export default MentorProfilesPage;
