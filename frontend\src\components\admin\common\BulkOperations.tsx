import React, { useState } from 'react';
import { Check, X, Trash2, Edit, Mail, Download, Upload, AlertTriangle } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';

interface BulkAction {
  id: string;
  label: string;
  icon: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'danger' | 'warning';
  requiresConfirmation?: boolean;
  action: (selectedIds: number[]) => Promise<void>;
}

interface BulkOperationsProps {
  selectedItems: number[];
  totalItems: number;
  onSelectAll: () => void;
  onClearSelection: () => void;
  actions: BulkAction[];
  entityName: string;
}

const BulkOperations: React.FC<BulkOperationsProps> = ({
  selectedItems,
  totalItems,
  onSelectAll,
  onClearSelection,
  actions,
  entityName
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const [pendingAction, setPendingAction] = useState<BulkAction | null>(null);
  const [isExecuting, setIsExecuting] = useState(false);

  // Handle action execution
  const handleAction = async (action: BulkAction) => {
    if (action.requiresConfirmation) {
      setPendingAction(action);
      setIsConfirmModalOpen(true);
    } else {
      await executeAction(action);
    }
  };

  // Execute the action
  const executeAction = async (action: BulkAction) => {
    try {
      setIsExecuting(true);
      await action.action(selectedItems);
      onClearSelection();
    } catch (error) {
      console.error('Error executing bulk action:', error);
    } finally {
      setIsExecuting(false);
      setIsConfirmModalOpen(false);
      setPendingAction(null);
    }
  };

  // Get action variant styles
  const getActionStyles = (variant?: string) => {
    switch (variant) {
      case 'danger':
        return 'bg-red-600/80 hover:bg-red-500/80 text-white';
      case 'warning':
        return 'bg-yellow-600/80 hover:bg-yellow-500/80 text-white';
      case 'secondary':
        return 'bg-gray-600/80 hover:bg-gray-500/80 text-white';
      default:
        return 'bg-blue-600/80 hover:bg-blue-500/80 text-white';
    }
  };

  if (selectedItems.length === 0) {
    return null;
  }

  return (
    <>
      {/* Bulk Operations Bar */}
      <div className="bg-indigo-900/50 backdrop-blur-sm rounded-lg border border-indigo-800/50 p-4 mb-6">
        <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`flex items-center space-x-4 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
            <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
              <Check size={20} className="text-blue-400" />
              <span className="text-white font-medium">
                {t('bulk.selected.count', '{{count}} {{entity}} selected', {
                  count: selectedItems.length,
                  entity: selectedItems.length === 1 ? entityName : `${entityName}s`
                })}
              </span>
            </div>
            
            <div className={`flex items-center space-x-2 text-sm ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
              {selectedItems.length < totalItems && (
                <button
                  onClick={onSelectAll}
                  className="text-blue-400 hover:text-blue-300 transition-colors"
                >
                  {t('bulk.select.all', 'Select all {{total}}', { total: totalItems })}
                </button>
              )}
              <button
                onClick={onClearSelection}
                className="text-gray-400 hover:text-gray-300 transition-colors"
              >
                {t('bulk.clear.selection', 'Clear selection')}
              </button>
            </div>
          </div>

          <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
            {actions.map((action) => (
              <button
                key={action.id}
                onClick={() => handleAction(action)}
                disabled={isExecuting}
                className={`px-3 py-2 rounded-lg font-medium transition-all duration-300 flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed ${getActionStyles(action.variant)} ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}
              >
                {action.icon}
                <span>{action.label}</span>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Confirmation Modal */}
      {isConfirmModalOpen && pendingAction && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-indigo-900/90 backdrop-blur-sm rounded-xl border border-indigo-800 w-full max-w-md">
            <div className="p-6">
              <div className={`flex items-center space-x-3 mb-4 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                <div className={`p-2 rounded-full ${
                  pendingAction.variant === 'danger' ? 'bg-red-500/20' : 'bg-yellow-500/20'
                }`}>
                  <AlertTriangle size={20} className={
                    pendingAction.variant === 'danger' ? 'text-red-400' : 'text-yellow-400'
                  } />
                </div>
                <h3 className="text-lg font-semibold text-white">
                  {t('bulk.confirm.action', 'Confirm Action')}
                </h3>
              </div>

              <div className="mb-6">
                <p className="text-gray-300 mb-4">
                  {t('bulk.confirm.message', 'Are you sure you want to {{action}} {{count}} {{entity}}?', {
                    action: pendingAction.label.toLowerCase(),
                    count: selectedItems.length,
                    entity: selectedItems.length === 1 ? entityName : `${entityName}s`
                  })}
                </p>
                
                {pendingAction.variant === 'danger' && (
                  <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-3">
                    <p className="text-red-300 text-sm">
                      {t('bulk.warning.irreversible', 'This action cannot be undone.')}
                    </p>
                  </div>
                )}
              </div>

              <div className={`flex justify-end space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                <button
                  onClick={() => {
                    setIsConfirmModalOpen(false);
                    setPendingAction(null);
                  }}
                  disabled={isExecuting}
                  className="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg font-medium transition-colors disabled:opacity-50"
                >
                  {t('common.cancel', 'Cancel')}
                </button>
                <button
                  onClick={() => executeAction(pendingAction)}
                  disabled={isExecuting}
                  className={`px-4 py-2 rounded-lg font-medium transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 ${getActionStyles(pendingAction.variant)} ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}
                >
                  {isExecuting ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span>{t('common.processing', 'Processing...')}</span>
                    </>
                  ) : (
                    <>
                      {pendingAction.icon}
                      <span>{pendingAction.label}</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

// Common bulk actions
export const createCommonBulkActions = (
  entityType: string,
  onApprove?: (ids: number[]) => Promise<void>,
  onReject?: (ids: number[]) => Promise<void>,
  onDelete?: (ids: number[]) => Promise<void>,
  onExport?: (ids: number[]) => Promise<void>,
  onSendEmail?: (ids: number[]) => Promise<void>
): BulkAction[] => {
  const actions: BulkAction[] = [];

  if (onApprove) {
    actions.push({
      id: 'approve',
      label: 'Approve',
      icon: <Check size={16} />,
      variant: 'primary',
      action: onApprove
    });
  }

  if (onReject) {
    actions.push({
      id: 'reject',
      label: 'Reject',
      icon: <X size={16} />,
      variant: 'warning',
      requiresConfirmation: true,
      action: onReject
    });
  }

  if (onSendEmail) {
    actions.push({
      id: 'email',
      label: 'Send Email',
      icon: <Mail size={16} />,
      variant: 'secondary',
      action: onSendEmail
    });
  }

  if (onExport) {
    actions.push({
      id: 'export',
      label: 'Export',
      icon: <Download size={16} />,
      variant: 'secondary',
      action: onExport
    });
  }

  if (onDelete) {
    actions.push({
      id: 'delete',
      label: 'Delete',
      icon: <Trash2 size={16} />,
      variant: 'danger',
      requiresConfirmation: true,
      action: onDelete
    });
  }

  return actions;
};

export default BulkOperations;
