import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Bell, 
  Check, 
  X, 
  Filter, 
  MoreVertical,
  MessageCircle,
  UserPlus,
  Heart,
  DollarSign,
  Calendar,
  AlertTriangle,
  Info,
  CheckCircle,
  Clock
} from 'lucide-react';

interface Notification {
  id: string;
  type: 'message' | 'follow' | 'like' | 'investment' | 'meeting' | 'system' | 'success' | 'warning';
  title: string;
  message: string;
  timestamp: Date;
  isRead: boolean;
  actionUrl?: string;
  avatar?: string;
  priority: 'low' | 'medium' | 'high';
}

const NotificationsPage: React.FC = () => {
  const { t } = useTranslation();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'unread' | 'read'>('all');

  useEffect(() => {
    // Simulate loading notifications
    setTimeout(() => {
      setNotifications([
        {
          id: '1',
          type: 'investment',
          title: t('New Investment Opportunity'),
          message: t('TechStart AI is seeking $2M in Series A funding. Check out their business plan.'),
          timestamp: new Date(Date.now() - 300000), // 5 minutes ago
          isRead: false,
          priority: 'high',
          actionUrl: '/dashboard/funding'
        },
        {
          id: '2',
          type: 'message',
          title: t('New Message from Mentor'),
          message: t('Ahmed Hassan sent you a message about your business plan review.'),
          timestamp: new Date(Date.now() - 1800000), // 30 minutes ago
          isRead: false,
          priority: 'medium',
          actionUrl: '/dashboard/mentorship'
        },
        {
          id: '3',
          type: 'meeting',
          title: t('Upcoming Mentorship Session'),
          message: t('Your session with Sarah Wilson is scheduled for tomorrow at 2:00 PM.'),
          timestamp: new Date(Date.now() - 3600000), // 1 hour ago
          isRead: true,
          priority: 'medium',
          actionUrl: '/dashboard/mentorship/sessions'
        },
        {
          id: '4',
          type: 'follow',
          title: t('New Follower'),
          message: t('Maria Rodriguez started following your business journey.'),
          timestamp: new Date(Date.now() - 7200000), // 2 hours ago
          isRead: true,
          priority: 'low',
          actionUrl: '/profile'
        },
        {
          id: '5',
          type: 'system',
          title: t('Platform Update'),
          message: t('New AI features are now available in your dashboard. Explore the enhanced business plan generator.'),
          timestamp: new Date(Date.now() - 86400000), // 1 day ago
          isRead: false,
          priority: 'medium',
          actionUrl: '/dashboard/ai'
        },
        {
          id: '6',
          type: 'success',
          title: t('Business Plan Approved'),
          message: t('Your business plan "AI E-commerce Platform" has been approved by the review committee.'),
          timestamp: new Date(Date.now() - 172800000), // 2 days ago
          isRead: true,
          priority: 'high',
          actionUrl: '/dashboard/business-plans'
        }
      ]);
      setLoading(false);
    }, 1000);
  }, [t]);

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'message': return <MessageCircle className="h-5 w-5 text-blue-500" />;
      case 'follow': return <UserPlus className="h-5 w-5 text-green-500" />;
      case 'like': return <Heart className="h-5 w-5 text-red-500" />;
      case 'investment': return <DollarSign className="h-5 w-5 text-yellow-500" />;
      case 'meeting': return <Calendar className="h-5 w-5 text-purple-500" />;
      case 'warning': return <AlertTriangle className="h-5 w-5 text-orange-500" />;
      case 'success': return <CheckCircle className="h-5 w-5 text-green-500" />;
      default: return <Info className="h-5 w-5 text-gray-500" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'border-l-red-500';
      case 'medium': return 'border-l-yellow-500';
      case 'low': return 'border-l-green-500';
      default: return 'border-l-gray-300';
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return t('Just now');
    if (minutes < 60) return t('{{count}} minutes ago', { count: minutes });
    if (hours < 24) return t('{{count}} hours ago', { count: hours });
    if (days < 7) return t('{{count}} days ago', { count: days });
    return timestamp.toLocaleDateString();
  };

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(notif => 
        notif.id === id ? { ...notif, isRead: true } : notif
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notif => ({ ...notif, isRead: true }))
    );
  };

  const deleteNotification = (id: string) => {
    setNotifications(prev => prev.filter(notif => notif.id !== id));
  };

  const filteredNotifications = notifications.filter(notif => {
    if (filter === 'unread') return !notif.isRead;
    if (filter === 'read') return notif.isRead;
    return true;
  });

  const unreadCount = notifications.filter(n => !n.isRead).length;

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">{t('Loading notifications...')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <div className="bg-white/70 backdrop-blur-md border-b border-white/20 sticky top-0 z-10">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 rounded-lg relative">
                <Bell className="h-6 w-6 text-blue-600" />
                {unreadCount > 0 && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    {unreadCount}
                  </span>
                )}
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{t('Notifications')}</h1>
                <p className="text-gray-600">
                  {unreadCount > 0 
                    ? t('You have {{count}} unread notifications', { count: unreadCount })
                    : t('All caught up!')
                  }
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <select
                value={filter}
                onChange={(e) => setFilter(e.target.value as any)}
                className="px-3 py-2 border border-gray-300 rounded-md bg-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">{t('All')}</option>
                <option value="unread">{t('Unread')}</option>
                <option value="read">{t('Read')}</option>
              </select>
              {unreadCount > 0 && (
                <button
                  onClick={markAllAsRead}
                  className="px-4 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors flex items-center space-x-2"
                >
                  <Check className="h-4 w-4" />
                  <span>{t('Mark all read')}</span>
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {filteredNotifications.length === 0 ? (
          <div className="text-center py-12">
            <Bell className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {filter === 'unread' ? t('No unread notifications') : t('No notifications')}
            </h3>
            <p className="text-gray-600">
              {filter === 'unread' 
                ? t('All your notifications have been read')
                : t('You\'ll see notifications here when you have them')
              }
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredNotifications.map((notification) => (
              <div
                key={notification.id}
                className={`bg-white/70 backdrop-blur-md rounded-xl border border-white/20 overflow-hidden transition-all hover:shadow-md ${
                  !notification.isRead ? 'border-l-4 ' + getPriorityColor(notification.priority) : ''
                }`}
              >
                <div className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4 flex-1">
                      <div className="flex-shrink-0 mt-1">
                        {getNotificationIcon(notification.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <h3 className={`text-sm font-medium ${!notification.isRead ? 'text-gray-900' : 'text-gray-700'}`}>
                            {notification.title}
                          </h3>
                          {!notification.isRead && (
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 mb-2">{notification.message}</p>
                        <div className="flex items-center space-x-4 text-xs text-gray-500">
                          <div className="flex items-center space-x-1">
                            <Clock className="h-3 w-3" />
                            <span>{formatTimestamp(notification.timestamp)}</span>
                          </div>
                          {notification.priority === 'high' && (
                            <span className="px-2 py-1 bg-red-100 text-red-700 rounded-full">
                              {t('High Priority')}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2 ml-4">
                      {!notification.isRead && (
                        <button
                          onClick={() => markAsRead(notification.id)}
                          className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                          title={t('Mark as read')}
                        >
                          <Check className="h-4 w-4" />
                        </button>
                      )}
                      <button
                        onClick={() => deleteNotification(notification.id)}
                        className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                        title={t('Delete notification')}
                      >
                        <X className="h-4 w-4" />
                      </button>
                      <button className="p-1 text-gray-400 hover:text-gray-600 transition-colors">
                        <MoreVertical className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                  {notification.actionUrl && (
                    <div className="mt-4">
                      <button className="px-4 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors text-sm">
                        {t('View Details')}
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default NotificationsPage;
