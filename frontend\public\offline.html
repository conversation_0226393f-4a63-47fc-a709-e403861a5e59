<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Yasmeen AI - Offline</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      background-color: #0f172a;
      color: #e2e8f0;
      margin: 0;
      padding: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      text-align: center;
    }

    .container {
      max-width: 600px;
      padding: 2rem;
      background-color: rgba(79, 70, 229, 0.1);
      border-radius: 1rem;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(79, 70, 229, 0.2);
    }

    h1 {
      font-size: 2.5rem;
      margin-bottom: 1rem;
      background: linear-gradient(to right, #a78bfa, #818cf8);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    p {
      font-size: 1.125rem;
      line-height: 1.75;
      margin-bottom: 1.5rem;
      color: #cbd5e1;
    }

    .icon {
      font-size: 4rem;
      margin-bottom: 1.5rem;
      color: #818cf8;
    }

    .button {
      display: inline-block;
      background: linear-gradient(to right, #8b5cf6, #6366f1);
      color: white;
      padding: 0.75rem 1.5rem;
      border-radius: 9999px;
      font-weight: 500;
      text-decoration: none;
      transition: all 0.3s ease;
      border: none;
      cursor: pointer;
      margin-top: 1rem;
    }

    .button:hover {
      box-shadow: 0 0 15px rgba(99, 102, 241, 0.5);
      transform: translateY(-2px);
    }

    .cached-content {
      margin-top: 2rem;
      border-top: 1px solid rgba(203, 213, 225, 0.2);
      padding-top: 1.5rem;
    }

    .cached-list {
      list-style: none;
      padding: 0;
      text-align: left;
    }

    .cached-list li {
      padding: 0.5rem 0;
      border-bottom: 1px solid rgba(203, 213, 225, 0.1);
    }

    .cached-list a {
      color: #a5b4fc;
      text-decoration: none;
    }

    .cached-list a:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="icon">📶</div>
    <h1 id="offline-title">You're Offline</h1>
    <p id="offline-description">It looks like you've lost your internet connection. Some features of Yasmeen AI may not be available while you're offline.</p>
    <p id="offline-reassurance">Don't worry though! You can still access previously visited pages and cached content.</p>

    <button class="button" onclick="window.location.reload()" id="try-again-btn">Try Again</button>

    <div class="cached-content">
      <h2 id="available-offline-title">Available Offline</h2>
      <p id="available-offline-description">You can still access these pages:</p>
      <ul class="cached-list" id="cached-pages">
        <li><a href="/" id="home-link">Home</a></li>
        <!-- Other cached pages will be added dynamically by JavaScript -->
      </ul>
    </div>
  </div>

  <script>
    // Language detection and translation
    const translations = {
      en: {
        title: "You're Offline",
        description: "It looks like you've lost your internet connection. Some features of Yasmeen AI may not be available while you're offline.",
        reassurance: "Don't worry though! You can still access previously visited pages and cached content.",
        tryAgain: "Try Again",
        availableOffline: "Available Offline",
        availableDescription: "You can still access these pages:",
        home: "Home",
        noCachedPages: "No cached pages available"
      },
      ar: {
        title: "أنت غير متصل",
        description: "يبدو أنك فقدت اتصالك بالإنترنت. قد لا تكون بعض ميزات ياسمين للذكاء الاصطناعي متاحة أثناء عدم الاتصال.",
        reassurance: "لا تقلق! لا يزال بإمكانك الوصول إلى الصفحات المزارة سابقاً والمحتوى المخزن مؤقتاً.",
        tryAgain: "حاول مرة أخرى",
        availableOffline: "متاح دون اتصال",
        availableDescription: "لا يزال بإمكانك الوصول إلى هذه الصفحات:",
        home: "الرئيسية",
        noCachedPages: "لا توجد صفحات مخزنة متاحة"
      }
    };

    // Detect language from localStorage or browser
    const getLanguage = () => {
      const savedLang = localStorage.getItem('language');
      if (savedLang && translations[savedLang]) return savedLang;

      const browserLang = navigator.language.split('-')[0];
      return translations[browserLang] ? browserLang : 'en';
    };

    // Apply translations
    const applyTranslations = () => {
      const lang = getLanguage();
      const t = translations[lang];

      document.getElementById('offline-title').textContent = t.title;
      document.getElementById('offline-description').textContent = t.description;
      document.getElementById('offline-reassurance').textContent = t.reassurance;
      document.getElementById('try-again-btn').textContent = t.tryAgain;
      document.getElementById('available-offline-title').textContent = t.availableOffline;
      document.getElementById('available-offline-description').textContent = t.availableDescription;
      document.getElementById('home-link').textContent = t.home;

      // Apply RTL for Arabic
      if (lang === 'ar') {
        document.body.dir = 'rtl';
        document.body.style.fontFamily = 'Arial, sans-serif';
      }
    };

    // Apply translations on load
    applyTranslations();

    // Check if the browser supports service workers
    if ('serviceWorker' in navigator && 'caches' in window) {
      // Get the list of cached pages
      caches.open('yasmeen-ai-cache-v1').then(cache => {
        cache.keys().then(requests => {
          const cachedPages = requests
            .filter(request => request.url.endsWith('/') || request.url.includes('.html'))
            .map(request => {
              const url = new URL(request.url);
              return {
                url: url.pathname,
                name: url.pathname === '/' ? 'Home' : url.pathname.split('/').filter(Boolean).pop()
              };
            });

          // Add cached pages to the list
          const cachedPagesList = document.getElementById('cached-pages');
          cachedPagesList.innerHTML = '';

          const lang = getLanguage();
          const t = translations[lang];

          if (cachedPages.length === 0) {
            cachedPagesList.innerHTML = `<li>${t.noCachedPages}</li>`;
          } else {
            cachedPages.forEach(page => {
              const listItem = document.createElement('li');
              const link = document.createElement('a');
              link.href = page.url;
              link.textContent = page.name || page.url;
              listItem.appendChild(link);
              cachedPagesList.appendChild(listItem);
            });
          }
        });
      });
    }
  </script>
</body>
</html>
