# Generated by Django 5.1.7 on 2025-06-11 16:06

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="AIServiceStatus",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "provider",
                    models.CharField(
                        choices=[("gemini", "Google Gemini")],
                        max_length=20,
                        unique=True,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("healthy", "Healthy"),
                            ("degraded", "Degraded"),
                            ("down", "Down"),
                            ("maintenance", "Maintenance"),
                        ],
                        default="healthy",
                        max_length=20,
                    ),
                ),
                (
                    "response_time",
                    models.FloatField(
                        default=0, help_text="Average response time in seconds"
                    ),
                ),
                (
                    "success_rate",
                    models.FloatField(
                        default=100,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                    ),
                ),
                ("last_check", models.DateTimeField(auto_now=True)),
                ("error_count", models.IntegerField(default=0)),
                ("last_error", models.TextField(blank=True)),
                ("last_error_at", models.DateTimeField(blank=True, null=True)),
                ("is_configured", models.BooleanField(default=False)),
                (
                    "configuration_issues",
                    models.JSONField(
                        default=list, help_text="List of configuration issues"
                    ),
                ),
            ],
            options={
                "verbose_name": "AI Service Status",
                "verbose_name_plural": "AI Service Statuses",
            },
        ),
        migrations.CreateModel(
            name="AIRateLimit",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "provider",
                    models.CharField(
                        choices=[("gemini", "Google Gemini")], max_length=20
                    ),
                ),
                (
                    "limit_type",
                    models.CharField(
                        choices=[
                            ("user", "Per User"),
                            ("role", "Per Role"),
                            ("global", "Global"),
                            ("ip", "Per IP Address"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "identifier",
                    models.CharField(
                        help_text="User ID, role name, or 'global'", max_length=100
                    ),
                ),
                (
                    "max_requests",
                    models.IntegerField(
                        validators=[django.core.validators.MinValueValidator(1)]
                    ),
                ),
                (
                    "period",
                    models.CharField(
                        choices=[
                            ("minute", "Per Minute"),
                            ("hour", "Per Hour"),
                            ("day", "Per Day"),
                            ("month", "Per Month"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "max_tokens",
                    models.IntegerField(
                        blank=True, help_text="Optional token limit", null=True
                    ),
                ),
                ("current_requests", models.IntegerField(default=0)),
                ("current_tokens", models.IntegerField(default=0)),
                (
                    "period_start",
                    models.DateTimeField(default=django.utils.timezone.now),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ["provider", "limit_type", "identifier"],
                "unique_together": {("provider", "limit_type", "identifier")},
            },
        ),
        migrations.CreateModel(
            name="AIConfiguration",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "provider",
                    models.CharField(
                        choices=[("gemini", "Google Gemini")], max_length=20
                    ),
                ),
                (
                    "config_type",
                    models.CharField(
                        choices=[
                            ("api_key", "API Key"),
                            ("model_config", "Model Configuration"),
                            ("rate_limit", "Rate Limiting"),
                            ("feature_toggle", "Feature Toggle"),
                            ("prompt_template", "Prompt Template"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "key",
                    models.CharField(
                        help_text="Configuration key (e.g., 'api_key', 'default_model')",
                        max_length=100,
                    ),
                ),
                (
                    "value",
                    models.TextField(
                        help_text="Configuration value (encrypted for sensitive data)"
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                (
                    "is_sensitive",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this config contains sensitive data",
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True, help_text="Description of this configuration"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="ai_configs_created",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="ai_configs_updated",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "AI Configuration",
                "verbose_name_plural": "AI Configurations",
                "ordering": ["provider", "config_type", "key"],
                "unique_together": {("provider", "key")},
            },
        ),
        migrations.CreateModel(
            name="AIUsageLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "provider",
                    models.CharField(
                        choices=[("gemini", "Google Gemini")], max_length=20
                    ),
                ),
                ("model", models.CharField(max_length=100)),
                (
                    "endpoint",
                    models.CharField(help_text="API endpoint used", max_length=100),
                ),
                ("session_id", models.CharField(blank=True, max_length=100)),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("tokens_used", models.IntegerField(default=0)),
                (
                    "cost",
                    models.DecimalField(decimal_places=6, default=0, max_digits=10),
                ),
                (
                    "response_time",
                    models.FloatField(help_text="Response time in seconds"),
                ),
                (
                    "request_data",
                    models.JSONField(
                        default=dict, help_text="Request parameters (sanitized)"
                    ),
                ),
                ("response_status", models.CharField(default="success", max_length=20)),
                ("error_message", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["provider", "created_at"],
                        name="core_aiusag_provide_2a8423_idx",
                    ),
                    models.Index(
                        fields=["user", "created_at"],
                        name="core_aiusag_user_id_45e735_idx",
                    ),
                    models.Index(
                        fields=["created_at"], name="core_aiusag_created_553760_idx"
                    ),
                ],
            },
        ),
    ]
