/**
 * Offline Mode Indicator Component
 * Shows when the app is running in offline mode (API not available)
 */

import React, { useState, useEffect } from 'react';
import { WifiOff, Wifi, AlertTriangle, Info } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { RTLText, RTLFlex } from './index';
import { useLanguage } from '../../hooks/useLanguage';

interface OfflineIndicatorProps {
  className?: string;
  showDetails?: boolean;
}

export const OfflineIndicator: React.FC<OfflineIndicatorProps> = ({ className = '',
  showDetails = false
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [apiAvailable, setApiAvailable] = useState(true);
  const [showDetailedInfo, setShowDetailedInfo] = useState(false);

  // Check network status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Check API availability
  useEffect(() => {
    const checkApiAvailability = async () => {
      try {
        const response = await fetch('/api/health/', {
          method: 'GET',
          timeout: 5000
        } as any);
        setApiAvailable(response.ok);
      } catch (error) {
        setApiAvailable(false);
      }
    };

    // Check immediately
    checkApiAvailability();

    // Check every 30 seconds
    const interval = setInterval(checkApiAvailability, 30000);

    return () => clearInterval(interval);
  }, []);

  const isOfflineMode = !isOnline || !apiAvailable;

  if (!isOfflineMode && !showDetails) {
    return null;
  }

  return (
    <div className={`${className}`}>
      {/* Main Indicator */}
      <div
        className={`flex items-center px-3 py-2 rounded-lg cursor-pointer transition-colors ${
          isOfflineMode
            ? 'bg-yellow-900/20 border border-yellow-500/50 text-yellow-300'
            : 'bg-green-900/20 border border-green-500/50 text-green-300'}
        }`}
        onClick={() => setShowDetailedInfo(!showDetailedInfo)}
      >
        <RTLFlex className="items-center">
          {isOfflineMode ? (
            <WifiOff size={16} className="text-yellow-400" />
          ) : (
            <Wifi size={16} className="text-green-400" />
          )}

          <RTLText className={`text-sm font-medium ${isRTL ? 'mr-2' : 'ml-2'}`}>
            {isOfflineMode ? t("common.offline.mode", "Offline Mode") : t("common.online", "Online")}
          </RTLText>

          {showDetails && (
            <Info size={14} className={`text-gray-400 ${isRTL ? 'mr-2' : 'ml-2'}`} />
          )}
        </RTLFlex>
      </div>

      {/* Detailed Information */}
      {showDetailedInfo && (
        <div className="mt-2 p-4 bg-gray-800/50 rounded-lg border border-gray-700/50">
          <h4 className="text-white font-medium mb-3">{t("common.connection.status", "Connection Status")}</h4>

          <div className="space-y-2">
            <RTLFlex className="items-center justify-between">
              <span className="text-gray-300">{t("common.network", "Network:")}</span>
              <RTLFlex className="items-center">
                {isOnline ? (
                  <Wifi size={14} className="text-green-400" />
                ) : (
                  <WifiOff size={14} className="text-red-400" />
                )}
                <span className={`text-sm ${isRTL ? 'mr-1' : 'ml-1'} ${
                  isOnline ? 'text-green-400' : 'text-red-400'
                }`}>
                  {isOnline ? t("common.connected", "Connected") : t("common.disconnected", "Disconnected")}
                </span>
              </RTLFlex>
            </RTLFlex>

            <RTLFlex className="items-center justify-between">
              <span className="text-gray-300">{t("common.api.server", "API Server:")}</span>
              <RTLFlex className="items-center">
                {apiAvailable ? (
                  <Wifi size={14} className="text-green-400" />
                ) : (
                  <WifiOff size={14} className="text-yellow-400" />
                )}
                <span className={`text-sm ${isRTL ? 'mr-1' : 'ml-1'} ${
                  apiAvailable ? 'text-green-400' : 'text-yellow-400'
                }`}>
                  {apiAvailable ? t("common.available", "Available") : t("common.unavailable", "Unavailable")}
                </span>
              </RTLFlex>
            </RTLFlex>
          </div>

          {isOfflineMode && (
            <div className="mt-4 p-3 bg-yellow-900/20 border border-yellow-500/50 rounded-lg">
              <RTLFlex className="items-start">
                <AlertTriangle size={16} className="text-yellow-400 mt-0.5" />
                <div className={isRTL ? 'mr-2' : 'ml-2'}>
                  <p className="text-yellow-300 text-sm font-medium mb-1">
                    Offline Mode Active
                  </p>
                  <p className="text-yellow-200 text-xs">
                    The app is using mock data and limited functionality.
                    Some features may not work as expected.
                  </p>
                </div>
              </RTLFlex>
            </div>
          )}

          {/* Features Available in Offline Mode */}
          {isOfflineMode && (
            <div className="mt-4">
              <h5 className="text-white text-sm font-medium mb-2">{t("common.available.features", "Available Features:")}</h5>
              <ul className="text-gray-300 text-xs space-y-1">
                <li>• Template browsing (mock data)</li>
                <li>• Template preview</li>
                <li>• Performance monitoring</li>
                <li>• Analytics dashboard (simulated data)</li>
                <li>• Voice input testing</li>
                <li>• UI component testing</li>
              </ul>
            </div>
          )}

          {/* Retry Button */}
          {isOfflineMode && (
            <button
              onClick={() => window.location.reload()}
              className="mt-4 w-full px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg text-sm transition-colors"
            >
              Retry Connection
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default OfflineIndicator;
