import React from 'react';
import { Bar, Line, Radar } from 'react-chartjs-2';
import { DashboardStats } from '../../../services/api';
import { Users, Activity, FileText } from 'lucide-react';
import {
  ChartContainer,
  StatCard,
  UserProfileCard,
  chartOptions,
  chartColors
} from './charts';
import { useTranslation } from 'react-i18next';

interface EnhancedAnalyticsProps {
  stats: DashboardStats;
}

const EnhancedAnalytics: React.FC<EnhancedAnalyticsProps> = ({ stats  }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  // Format data for user engagement chart
  const userEngagementData = {
    labels: [t('admin.analytics.dailyActive'), t('admin.analytics.weeklyActive'), t('admin.analytics.monthlyActive'), t('admin.analytics.totalUsers')],
    datasets: [
      {
        label: t('admin.analytics.userEngagement'),
        data: [
          stats.users?.daily_active_users || 0,
          stats.users?.weekly_active_users || 0,
          stats.users?.monthly_active_users || 0,
          stats.users?.total_users || 0
        ],
        backgroundColor: [
          chartColors.indigo.light,
          chartColors.purple.light,
          chartColors.blue.light,
          chartColors.sky.light,
        ],
        borderColor: [
          chartColors.indigo.primary,
          chartColors.purple.primary,
          chartColors.blue.primary,
          chartColors.sky.primary,
        ],
        borderWidth: 1,
      },
    ],
  };

  // Format data for content engagement over time
  const contentEngagementData = {
    labels: Object.keys(stats.users?.content_engagement_by_month || {}),
    datasets: [
      {
        label: t('admin.analytics.posts'),
        data: Object.values(stats.users?.content_engagement_by_month || {}).map(item => (item as any).posts),
        borderColor: chartColors.indigo.primary,
        backgroundColor: chartColors.indigo.light,
      },
      {
        label: t('admin.analytics.comments'),
        data: Object.values(stats.users?.content_engagement_by_month || {}).map(item => (item as any).comments),
        borderColor: chartColors.purple.primary,
        backgroundColor: chartColors.purple.light,
      },
    ],
  };

  // Format data for user activity radar chart
  const topUser = stats.users?.most_active_users?.[0] || {
    post_count: 0,
    comment_count: 0,
    event_count: 0,
    resource_count: 0,
    likes_received: 0,
  };

  const userActivityRadarData = {
    labels: [t('admin.analytics.posts'), t('admin.analytics.comments'), t('admin.analytics.events'), t('admin.analytics.resources'), t('admin.analytics.likesReceived')],
    datasets: [
      {
        label: t('admin.analytics.topUserActivity'),
        data: [
          topUser.post_count,
          topUser.comment_count,
          topUser.event_count,
          topUser.resource_count,
          topUser.likes_received,
        ],
        backgroundColor: `${chartColors.purple.light}33`, // Adding transparency
        borderColor: chartColors.purple.primary,
        pointBackgroundColor: chartColors.purple.primary,
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: chartColors.purple.primary,
      },
    ],
  };

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-bold mb-4">{t('admin.analytics.enhancedDashboard')}</h2>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatCard
          title={t('admin.analytics.totalUsers')}
          value={stats.users?.total_users || 0}
          icon={<Users size={24} className="text-blue-400" />}
          change={stats.users?.user_growth_rate}
        />
        <StatCard
          title={t('admin.analytics.engagementRate')}
          value={(stats.users?.engagement_rate || 0).toFixed(2)}
          icon={<Activity size={24} className="text-purple-400" />}
          suffix={t('admin.analytics.perUser')}
        />
        <StatCard
          title={t('admin.analytics.retentionRate')}
          value={(stats.users?.retention_rate || 0).toFixed(1)}
          icon={<Users size={24} className="text-green-400" />}
          suffix="%"
        />
        <StatCard
          title={t('admin.analytics.postsPerUser')}
          value={(stats.users?.posts_per_user || 0).toFixed(2)}
          icon={<FileText size={24} className="text-yellow-400" />}
        />
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <ChartContainer title={t('admin.analytics.userEngagementLevels')}>
          <Bar
            options={{
              ...chartOptions,
              plugins: {
                ...chartOptions.plugins,
                title: {
                  ...chartOptions.plugins.title,
                  text: t('admin.analytics.activeUsersByTimePeriod'),
                },
              },
            }}
            data={userEngagementData}
          />
        </ChartContainer>

        <ChartContainer title={t('admin.analytics.contentEngagementOverTime')}>
          <Line
            options={{
              ...chartOptions,
              plugins: {
                ...chartOptions.plugins,
                title: {
                  ...chartOptions.plugins.title,
                  text: t('admin.analytics.postsCommentsByMonth'),
                },
              },
            }}
            data={contentEngagementData}
          />
        </ChartContainer>
      </div>

      {/* Top Users Section */}
      <div className="bg-indigo-900/20 rounded-xl p-4 backdrop-blur-sm shadow-lg">
        <h3 className="text-lg font-semibold mb-4">{t('admin.analytics.topActiveUsers')}</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {stats.users?.most_active_users?.slice(0, 6).map((user, index) => (
            <UserProfileCard key={user.id} user={user} rank={index + 1} />
          ))}
        </div>
      </div>
    </div>
  );
};

export default EnhancedAnalytics;
