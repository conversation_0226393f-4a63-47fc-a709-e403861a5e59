"""
Views for managing mentorship feedback and ratings.
"""
from rest_framework import viewsets, permissions, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from django.db.models import Q, Avg
from django_filters.rest_framework import DjangoFilterBackend
from django.contrib.auth.models import User

from .models import (
    MentorProfile, MentorshipMatch, MentorshipSession, MentorshipFeedback
)
from .serializers import (
    MentorProfileSerializer, MentorshipFeedbackSerializer
)
from .tasks import notify_feedback_received
from api.permissions import IsAdminUser


class MentorshipFeedbackViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing mentorship feedback and ratings
    """
    queryset = MentorshipFeedback.objects.all().order_by('-created_at')
    serializer_class = MentorshipFeedbackSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['session', 'is_from_mentee', 'rating', 'goals_achieved', 'follow_up_needed']
    search_fields = ['comments', 'areas_of_improvement', 'highlights', 'follow_up_notes']
    ordering_fields = ['created_at', 'rating']

    def get_queryset(self):
        """
        Filter feedback based on user role:
        - Admins see all feedback
        - Mentors see feedback for their sessions (respecting privacy settings)
        - Mentees see their own feedback and feedback for their sessions (respecting privacy settings)
        """
        user = self.request.user
        if not user.is_authenticated:
            return MentorshipFeedback.objects.none()

        if user.is_staff or user.is_superuser:
            return super().get_queryset()

        # Check if user is a mentor
        try:
            mentor_profile = user.mentor_profile
            mentor_matches = mentor_profile.mentorship_matches.all()
            mentor_sessions = MentorshipSession.objects.filter(mentorship_match__in=mentor_matches)

            # Mentors can see all feedback they provided and feedback for their sessions that's shared with them
            return MentorshipFeedback.objects.filter(
                Q(provided_by=user) |
                (Q(session__in=mentor_sessions) & (Q(share_with_mentor=True) | Q(is_from_mentee=False)))
            )
        except:
            # User is not a mentor, check if they're a mentee
            mentee_matches = user.mentorship_matches.all()
            mentee_sessions = MentorshipSession.objects.filter(mentorship_match__in=mentee_matches)

            # Mentees can see all feedback they provided and non-private feedback for their sessions
            return MentorshipFeedback.objects.filter(
                Q(provided_by=user) |
                (Q(session__in=mentee_sessions) & Q(is_private=False))
            )

    def perform_create(self, serializer):
        """Set the provided_by field to the current user and send notification"""
        # Save the feedback
        feedback = serializer.save(provided_by=self.request.user)

        # Send notification
        notify_feedback_received(feedback)

    @action(detail=False, methods=['get'])
    def mentor_ratings(self, request):
        """
        Get aggregated ratings for a mentor

        Query parameters:
        - mentor_id: ID of the mentor to get ratings for
        """
        mentor_id = request.query_params.get('mentor_id')
        if not mentor_id:
            return Response(
                {"error": "mentor_id is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            mentor = MentorProfile.objects.get(id=mentor_id)
        except MentorProfile.DoesNotExist:
            return Response(
                {"error": "Mentor not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Get all matches for this mentor
        matches = mentor.mentorship_matches.all()

        # Get all sessions for these matches
        sessions = MentorshipSession.objects.filter(mentorship_match__in=matches)

        # Get all feedback for these sessions from mentees
        feedback = MentorshipFeedback.objects.filter(
            session__in=sessions,
            is_from_mentee=True
        )

        # Calculate average ratings
        overall_rating = feedback.aggregate(avg=Avg('rating'))['avg'] or 0
        knowledge_rating = feedback.aggregate(avg=Avg('knowledge_rating'))['avg'] or 0
        communication_rating = feedback.aggregate(avg=Avg('communication_rating'))['avg'] or 0
        helpfulness_rating = feedback.aggregate(avg=Avg('helpfulness_rating'))['avg'] or 0
        preparation_rating = feedback.aggregate(avg=Avg('preparation_rating'))['avg'] or 0
        responsiveness_rating = feedback.aggregate(avg=Avg('responsiveness_rating'))['avg'] or 0

        # Count ratings by score (1-5)
        rating_counts = {
            '1': feedback.filter(rating=1).count(),
            '2': feedback.filter(rating=2).count(),
            '3': feedback.filter(rating=3).count(),
            '4': feedback.filter(rating=4).count(),
            '5': feedback.filter(rating=5).count(),
        }

        # Get recent feedback (limited to 5)
        recent_feedback = feedback.order_by('-created_at')[:5]
        recent_feedback_serialized = self.get_serializer(recent_feedback, many=True).data

        return Response({
            'mentor': MentorProfileSerializer(mentor).data,
            'feedback_count': feedback.count(),
            'overall_rating': overall_rating,
            'detailed_ratings': {
                'knowledge': knowledge_rating,
                'communication': communication_rating,
                'helpfulness': helpfulness_rating,
                'preparation': preparation_rating,
                'responsiveness': responsiveness_rating,
            },
            'rating_counts': rating_counts,
            'recent_feedback': recent_feedback_serialized
        })

    @action(detail=False, methods=['get'])
    def session_feedback(self, request):
        """
        Get all feedback for a specific session

        Query parameters:
        - session_id: ID of the session to get feedback for
        """
        session_id = request.query_params.get('session_id')
        if not session_id:
            return Response(
                {"error": "session_id is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Filter feedback for this session
        queryset = self.get_queryset().filter(session_id=session_id)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def toggle_privacy(self, request, pk=None):
        """Toggle the privacy setting of feedback"""
        feedback = self.get_object()

        # Only the feedback provider or an admin can change privacy settings
        if feedback.provided_by != request.user and not (request.user.is_staff or request.user.is_superuser):
            return Response(
                {"error": "You don't have permission to change this feedback's privacy settings"},
                status=status.HTTP_403_FORBIDDEN
            )

        feedback.is_private = not feedback.is_private
        feedback.save()

        serializer = self.get_serializer(feedback)
        return Response(serializer.data)
