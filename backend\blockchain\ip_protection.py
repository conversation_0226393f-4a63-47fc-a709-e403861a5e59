"""
Blockchain-based Intellectual Property Protection for Yasmeen AI Platform
Provides timestamped, immutable records of business ideas and innovations
"""

import hashlib
import json
import time
from typing import Dict, List, Any, Optional
from datetime import datetime
from django.conf import settings
from django.utils import timezone
import logging

logger = logging.getLogger(__name__)

class IPProtectionService:
    """Blockchain-inspired IP protection service"""
    
    def __init__(self):
        self.chain = []
        self.pending_records = []
        self.difficulty = 4  # Mining difficulty
        
    def create_genesis_block(self):
        """Create the first block in the chain"""
        genesis_block = {
            'index': 0,
            'timestamp': time.time(),
            'data': 'Genesis Block - Yasmeen AI IP Protection',
            'previous_hash': '0',
            'nonce': 0
        }
        genesis_block['hash'] = self.calculate_hash(genesis_block)
        self.chain.append(genesis_block)
        return genesis_block
    
    def calculate_hash(self, block: Dict) -> str:
        """Calculate SHA-256 hash of a block"""
        block_string = json.dumps({
            'index': block['index'],
            'timestamp': block['timestamp'],
            'data': block['data'],
            'previous_hash': block['previous_hash'],
            'nonce': block['nonce']
        }, sort_keys=True)
        return hashlib.sha256(block_string.encode()).hexdigest()
    
    def get_latest_block(self) -> Dict:
        """Get the latest block in the chain"""
        if not self.chain:
            return self.create_genesis_block()
        return self.chain[-1]
    
    def mine_block(self, data: Dict) -> Dict:
        """Mine a new block with proof of work"""
        latest_block = self.get_latest_block()
        
        new_block = {
            'index': latest_block['index'] + 1,
            'timestamp': time.time(),
            'data': data,
            'previous_hash': latest_block['hash'],
            'nonce': 0
        }
        
        # Proof of work
        target = '0' * self.difficulty
        while not self.calculate_hash(new_block).startswith(target):
            new_block['nonce'] += 1
        
        new_block['hash'] = self.calculate_hash(new_block)
        return new_block
    
    def add_block(self, data: Dict) -> Dict:
        """Add a new block to the chain"""
        new_block = self.mine_block(data)
        self.chain.append(new_block)
        return new_block
    
    def register_business_idea(self, business_idea: Dict, user_id: int) -> Dict:
        """Register a business idea on the blockchain"""
        try:
            ip_record = {
                'type': 'business_idea_registration',
                'business_idea_id': business_idea.get('id'),
                'title': business_idea.get('title'),
                'description_hash': hashlib.sha256(
                    business_idea.get('description', '').encode()
                ).hexdigest(),
                'owner_id': user_id,
                'registration_timestamp': timezone.now().isoformat(),
                'metadata': {
                    'category': business_idea.get('category'),
                    'stage': business_idea.get('current_stage'),
                    'platform': 'yasmeen_ai'
                }
            }
            
            block = self.add_block(ip_record)
            
            # Store in database for quick access
            self._store_ip_record(ip_record, block['hash'])
            
            return {
                'success': True,
                'block_hash': block['hash'],
                'block_index': block['index'],
                'registration_id': f"YAI-{block['index']}-{block['hash'][:8]}",
                'timestamp': block['timestamp']
            }
            
        except Exception as e:
            logger.error(f"Business idea registration failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def register_innovation(self, innovation_data: Dict, user_id: int) -> Dict:
        """Register an innovation or improvement"""
        try:
            ip_record = {
                'type': 'innovation_registration',
                'innovation_id': innovation_data.get('id'),
                'title': innovation_data.get('title'),
                'description_hash': hashlib.sha256(
                    innovation_data.get('description', '').encode()
                ).hexdigest(),
                'owner_id': user_id,
                'parent_idea_id': innovation_data.get('business_idea_id'),
                'registration_timestamp': timezone.now().isoformat(),
                'innovation_type': innovation_data.get('type', 'improvement'),
                'metadata': {
                    'technical_details_hash': hashlib.sha256(
                        innovation_data.get('technical_details', '').encode()
                    ).hexdigest() if innovation_data.get('technical_details') else None,
                    'platform': 'yasmeen_ai'
                }
            }
            
            block = self.add_block(ip_record)
            self._store_ip_record(ip_record, block['hash'])
            
            return {
                'success': True,
                'block_hash': block['hash'],
                'block_index': block['index'],
                'registration_id': f"YAI-INN-{block['index']}-{block['hash'][:8]}",
                'timestamp': block['timestamp']
            }
            
        except Exception as e:
            logger.error(f"Innovation registration failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def verify_ownership(self, registration_id: str, user_id: int) -> Dict:
        """Verify ownership of a registered IP"""
        try:
            # Extract block index from registration ID
            parts = registration_id.split('-')
            if len(parts) < 3:
                return {'success': False, 'error': 'Invalid registration ID'}
            
            block_index = int(parts[1]) if parts[0] == 'YAI' else int(parts[2])
            
            if block_index >= len(self.chain):
                return {'success': False, 'error': 'Block not found'}
            
            block = self.chain[block_index]
            data = block['data']
            
            if data.get('owner_id') == user_id:
                return {
                    'success': True,
                    'verified': True,
                    'owner_id': data['owner_id'],
                    'registration_timestamp': data['registration_timestamp'],
                    'block_hash': block['hash']
                }
            else:
                return {
                    'success': True,
                    'verified': False,
                    'message': 'User is not the registered owner'
                }
                
        except Exception as e:
            logger.error(f"Ownership verification failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_user_registrations(self, user_id: int) -> List[Dict]:
        """Get all IP registrations for a user"""
        try:
            registrations = []
            
            for block in self.chain:
                if block['index'] == 0:  # Skip genesis block
                    continue
                    
                data = block['data']
                if data.get('owner_id') == user_id:
                    registrations.append({
                        'registration_id': f"YAI-{block['index']}-{block['hash'][:8]}",
                        'type': data.get('type'),
                        'title': data.get('title'),
                        'registration_timestamp': data.get('registration_timestamp'),
                        'block_hash': block['hash'],
                        'block_index': block['index']
                    })
            
            return registrations
            
        except Exception as e:
            logger.error(f"Get user registrations failed: {e}")
            return []
    
    def validate_chain(self) -> bool:
        """Validate the integrity of the blockchain"""
        try:
            for i in range(1, len(self.chain)):
                current_block = self.chain[i]
                previous_block = self.chain[i - 1]
                
                # Check if current block's hash is valid
                if current_block['hash'] != self.calculate_hash(current_block):
                    return False
                
                # Check if current block points to previous block
                if current_block['previous_hash'] != previous_block['hash']:
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Chain validation failed: {e}")
            return False
    
    def _store_ip_record(self, record: Dict, block_hash: str):
        """Store IP record in database for quick access"""
        try:
            from .models import IPRecord
            
            IPRecord.objects.create(
                record_type=record['type'],
                owner_id=record['owner_id'],
                title=record.get('title', ''),
                block_hash=block_hash,
                registration_timestamp=record['registration_timestamp'],
                metadata=record.get('metadata', {})
            )
            
        except Exception as e:
            logger.error(f"Failed to store IP record: {e}")
    
    def generate_certificate(self, registration_id: str) -> Dict:
        """Generate a digital certificate for IP registration"""
        try:
            parts = registration_id.split('-')
            block_index = int(parts[1]) if parts[0] == 'YAI' else int(parts[2])
            
            if block_index >= len(self.chain):
                return {'success': False, 'error': 'Registration not found'}
            
            block = self.chain[block_index]
            data = block['data']
            
            certificate = {
                'certificate_id': f"CERT-{registration_id}",
                'registration_id': registration_id,
                'title': data.get('title'),
                'owner_id': data.get('owner_id'),
                'registration_date': data.get('registration_timestamp'),
                'block_hash': block['hash'],
                'verification_url': f"https://yasmeen-ai.com/verify/{registration_id}",
                'certificate_generated': timezone.now().isoformat(),
                'platform': 'Yasmeen AI - Business Incubator',
                'certificate_hash': hashlib.sha256(
                    f"{registration_id}{block['hash']}{data.get('title', '')}".encode()
                ).hexdigest()
            }
            
            return {
                'success': True,
                'certificate': certificate
            }
            
        except Exception as e:
            logger.error(f"Certificate generation failed: {e}")
            return {'success': False, 'error': str(e)}

class SmartContractSimulator:
    """Simulate smart contract functionality for IP licensing"""
    
    def __init__(self):
        self.contracts = {}
    
    def create_licensing_contract(self, ip_registration_id: str, terms: Dict) -> Dict:
        """Create a licensing contract for IP"""
        try:
            contract_id = f"CONTRACT-{int(time.time())}-{ip_registration_id}"
            
            contract = {
                'contract_id': contract_id,
                'ip_registration_id': ip_registration_id,
                'licensor_id': terms.get('licensor_id'),
                'licensee_id': terms.get('licensee_id'),
                'license_type': terms.get('license_type', 'non_exclusive'),
                'royalty_percentage': terms.get('royalty_percentage', 0),
                'duration_months': terms.get('duration_months', 12),
                'territory': terms.get('territory', 'worldwide'),
                'created_at': timezone.now().isoformat(),
                'status': 'pending',
                'terms': terms
            }
            
            self.contracts[contract_id] = contract
            
            return {
                'success': True,
                'contract_id': contract_id,
                'contract': contract
            }
            
        except Exception as e:
            logger.error(f"Contract creation failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def execute_contract(self, contract_id: str) -> Dict:
        """Execute a licensing contract"""
        try:
            if contract_id not in self.contracts:
                return {'success': False, 'error': 'Contract not found'}
            
            contract = self.contracts[contract_id]
            contract['status'] = 'active'
            contract['executed_at'] = timezone.now().isoformat()
            
            return {
                'success': True,
                'message': 'Contract executed successfully',
                'contract': contract
            }
            
        except Exception as e:
            logger.error(f"Contract execution failed: {e}")
            return {'success': False, 'error': str(e)}

# Global instances
ip_service = IPProtectionService()
smart_contracts = SmartContractSimulator()

# Initialize genesis block
if not ip_service.chain:
    ip_service.create_genesis_block()

# Export services
__all__ = ['IPProtectionService', 'SmartContractSimulator', 'ip_service', 'smart_contracts']
