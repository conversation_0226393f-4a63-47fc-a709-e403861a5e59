import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';
import { MentorProfile } from '../../../services/incubatorApi';
import { Loader, Save, X, AlertCircle, Plus, Trash2 } from 'lucide-react';

interface MentorProfileFormProps {
  initialData?: Partial<MentorProfile>;
  onSubmit: (data: Partial<MentorProfile>) => Promise<boolean>;
  onCancel: () => void;
  isSubmitting?: boolean;
  mode: 'create' | 'edit';
}

interface FormData {
  bio: string;
  company: string;
  position: string;
  years_of_experience: number;
  linkedin_profile: string;
  website: string;
  availability: 'high' | 'medium' | 'low' | 'limited';
  max_mentees: number;
  is_accepting_mentees: boolean;
}

interface FormErrors {
  [key: string]: string;
}

const MentorProfileForm: React.FC<MentorProfileFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isSubmitting = false,
  mode
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [formData, setFormData] = useState<FormData>({
    bio: initialData?.bio || '',
    company: initialData?.company || '',
    position: initialData?.position || '',
    years_of_experience: initialData?.years_of_experience || 0,
    linkedin_profile: initialData?.linkedin_profile || '',
    website: initialData?.website || '',
    availability: initialData?.availability || 'medium',
    max_mentees: initialData?.max_mentees || 5,
    is_accepting_mentees: initialData?.is_accepting_mentees ?? true
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  // Validation rules
  const validateField = (name: string, value: string | number | boolean): string => {
    switch (name) {
      case 'bio':
        if (!value || (typeof value === 'string' && !value.trim())) {
          return t('validation.required', 'This field is required');
        }
        if (typeof value === 'string' && value.length < 50) {
          return t('validation.minLength', 'Must be at least 50 characters');
        }
        break;
      case 'years_of_experience':
        if (typeof value === 'number' && value < 0) {
          return t('validation.positiveNumber', 'Must be a positive number');
        }
        if (typeof value === 'number' && value > 50) {
          return t('validation.maxValue', 'Must be less than 50 years');
        }
        break;
      case 'max_mentees':
        if (typeof value === 'number' && value < 1) {
          return t('validation.minValue', 'Must be at least 1');
        }
        if (typeof value === 'number' && value > 20) {
          return t('validation.maxValue', 'Must be less than 20');
        }
        break;
      case 'linkedin_profile':
        if (value && typeof value === 'string' && value.trim() && !value.includes('linkedin.com')) {
          return t('validation.invalidLinkedIn', 'Must be a valid LinkedIn URL');
        }
        break;
      case 'website':
        if (value && typeof value === 'string' && value.trim()) {
          const urlPattern = /^https?:\/\/.+/;
          if (!urlPattern.test(value)) {
            return t('validation.invalidUrl', 'Must be a valid URL starting with http:// or https://');
          }
        }
        break;
    }
    return '';
  };

  // Validate all fields
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};
    
    Object.keys(formData).forEach(key => {
      const error = validateField(key, formData[key as keyof FormData]);
      if (error) {
        newErrors[key] = error;
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    let processedValue: string | number | boolean = value;
    
    if (type === 'number') {
      processedValue = parseInt(value) || 0;
    } else if (type === 'checkbox') {
      processedValue = (e.target as HTMLInputElement).checked;
    }
    
    setFormData(prev => ({
      ...prev,
      [name]: processedValue
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // Handle field blur
  const handleBlur = (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    setTouched(prev => ({
      ...prev,
      [name]: true
    }));

    // Validate field on blur
    let processedValue: string | number | boolean = value;
    if (type === 'number') {
      processedValue = parseInt(value) || 0;
    } else if (type === 'checkbox') {
      processedValue = (e.target as HTMLInputElement).checked;
    }
    
    const error = validateField(name, processedValue);
    setErrors(prev => ({
      ...prev,
      [name]: error
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Mark all fields as touched
    const allTouched = Object.keys(formData).reduce((acc, key) => {
      acc[key] = true;
      return acc;
    }, {} as Record<string, boolean>);
    setTouched(allTouched);

    // Validate form
    if (!validateForm()) {
      return;
    }

    // Submit form
    const success = await onSubmit(formData);
    if (success) {
      // Form will be closed by parent component
    }
  };

  const availabilityOptions = [
    { value: 'high', label: t('mentor.availability.high', 'High (10+ hours/week)') },
    { value: 'medium', label: t('mentor.availability.medium', 'Medium (5-10 hours/week)') },
    { value: 'low', label: t('mentor.availability.low', 'Low (2-5 hours/week)') },
    { value: 'limited', label: t('mentor.availability.limited', 'Limited (1-2 hours/week)') }
  ];

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <div className="bg-gray-900 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className={`flex items-center justify-between p-6 border-b border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <h2 className="text-xl font-semibold text-white">
            {mode === 'create' 
              ? t('mentor.createProfile', 'Create Mentor Profile')
              : t('mentor.editProfile', 'Edit Mentor Profile')
            }
          </h2>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-white transition-colors"
            disabled={isSubmitting}
          >
            <X size={24} />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Bio */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('mentor.bio', 'Bio')} *
            </label>
            <textarea
              name="bio"
              value={formData.bio}
              onChange={handleInputChange}
              onBlur={handleBlur}
              rows={4}
              className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white resize-vertical ${
                errors.bio && touched.bio ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder={t('mentor.bioPlaceholder', 'Tell us about your background, expertise, and what you can offer as a mentor...')}
              disabled={isSubmitting}
            />
            {errors.bio && touched.bio && (
              <p className="mt-1 text-sm text-red-400 flex items-center">
                <AlertCircle size={16} className="mr-1" />
                {errors.bio}
              </p>
            )}
          </div>

          {/* Company and Position */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('mentor.company', 'Company')}
              </label>
              <input
                type="text"
                name="company"
                value={formData.company}
                onChange={handleInputChange}
                onBlur={handleBlur}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                placeholder={t('mentor.companyPlaceholder', 'Your current company')}
                disabled={isSubmitting}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('mentor.position', 'Position')}
              </label>
              <input
                type="text"
                name="position"
                value={formData.position}
                onChange={handleInputChange}
                onBlur={handleBlur}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                placeholder={t('mentor.positionPlaceholder', 'Your current position')}
                disabled={isSubmitting}
              />
            </div>
          </div>

          {/* Years of Experience */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('mentor.yearsOfExperience', 'Years of Experience')} *
            </label>
            <input
              type="number"
              name="years_of_experience"
              value={formData.years_of_experience}
              onChange={handleInputChange}
              onBlur={handleBlur}
              min="0"
              max="50"
              className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white ${
                errors.years_of_experience && touched.years_of_experience ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder={t('mentor.yearsPlaceholder', 'Enter your years of experience')}
              disabled={isSubmitting}
            />
            {errors.years_of_experience && touched.years_of_experience && (
              <p className="mt-1 text-sm text-red-400 flex items-center">
                <AlertCircle size={16} className="mr-1" />
                {errors.years_of_experience}
              </p>
            )}
          </div>

          {/* LinkedIn and Website */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('mentor.linkedinProfile', 'LinkedIn Profile')}
              </label>
              <input
                type="url"
                name="linkedin_profile"
                value={formData.linkedin_profile}
                onChange={handleInputChange}
                onBlur={handleBlur}
                className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white ${
                  errors.linkedin_profile && touched.linkedin_profile ? 'border-red-500' : 'border-gray-600'
                }`}
                placeholder="https://linkedin.com/in/yourprofile"
                disabled={isSubmitting}
              />
              {errors.linkedin_profile && touched.linkedin_profile && (
                <p className="mt-1 text-sm text-red-400 flex items-center">
                  <AlertCircle size={16} className="mr-1" />
                  {errors.linkedin_profile}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('mentor.website', 'Website')}
              </label>
              <input
                type="url"
                name="website"
                value={formData.website}
                onChange={handleInputChange}
                onBlur={handleBlur}
                className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white ${
                  errors.website && touched.website ? 'border-red-500' : 'border-gray-600'
                }`}
                placeholder="https://yourwebsite.com"
                disabled={isSubmitting}
              />
              {errors.website && touched.website && (
                <p className="mt-1 text-sm text-red-400 flex items-center">
                  <AlertCircle size={16} className="mr-1" />
                  {errors.website}
                </p>
              )}
            </div>
          </div>

          {/* Availability and Max Mentees */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('mentor.availability', 'Availability')} *
              </label>
              <select
                name="availability"
                value={formData.availability}
                onChange={handleInputChange}
                onBlur={handleBlur}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                disabled={isSubmitting}
              >
                {availabilityOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('mentor.maxMentees', 'Maximum Mentees')} *
              </label>
              <input
                type="number"
                name="max_mentees"
                value={formData.max_mentees}
                onChange={handleInputChange}
                onBlur={handleBlur}
                min="1"
                max="20"
                className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white ${
                  errors.max_mentees && touched.max_mentees ? 'border-red-500' : 'border-gray-600'
                }`}
                placeholder={t('mentor.maxMenteesPlaceholder', 'How many mentees can you handle?')}
                disabled={isSubmitting}
              />
              {errors.max_mentees && touched.max_mentees && (
                <p className="mt-1 text-sm text-red-400 flex items-center">
                  <AlertCircle size={16} className="mr-1" />
                  {errors.max_mentees}
                </p>
              )}
            </div>
          </div>

          {/* Accepting Mentees */}
          <div>
            <label className="flex items-center gap-3">
              <input
                type="checkbox"
                name="is_accepting_mentees"
                checked={formData.is_accepting_mentees}
                onChange={handleInputChange}
                className="w-4 h-4 text-purple-600 bg-gray-800 border-gray-600 rounded focus:ring-purple-500 focus:ring-2"
                disabled={isSubmitting}
              />
              <span className="text-sm font-medium text-gray-300">
                {t('mentor.acceptingMentees', 'Currently accepting new mentees')}
              </span>
            </label>
            <p className="mt-1 text-xs text-gray-400">
              {t('mentor.acceptingMenteesHelp', 'You can change this setting later')}
            </p>
          </div>

          {/* Form Actions */}
          <div className={`flex gap-4 pt-4 border-t border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <button
              type="button"
              onClick={onCancel}
              className="px-6 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors"
              disabled={isSubmitting}
            >
              {t('common.cancel', 'Cancel')}
            </button>
            <button
              type="submit"
              className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader size={16} className="animate-spin" />
                  {t('common.saving', 'Saving...')}
                </>
              ) : (
                <>
                  <Save size={16} />
                  {mode === 'create'
                    ? t('common.create', 'Create')
                    : t('common.update', 'Update')
                  }
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default MentorProfileForm;
