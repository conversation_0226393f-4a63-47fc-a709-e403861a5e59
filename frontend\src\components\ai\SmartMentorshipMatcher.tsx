/**
 * Smart Mentorship Matching System
 * AI-powered mentor matching based on business needs, experience, and compatibility
 */

import React, { useState, useEffect } from 'react';
import {
  Users,
  Brain,
  Star,
  MapPin,
  Briefcase,
  Clock,
  MessageSquare,
  CheckCircle,
  Target,
  TrendingUp,
  Award,
  Calendar,
  Filter,
  Search,
  Heart,
  Zap,
  Eye,
  Send
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useConsolidatedAI } from '../../hooks/useCentralizedAI';


interface Mentor {
  id: string;
  name: string;
  title: string;
  company: string;
  avatar: string;
  location: string;
  experience_years: number;
  industries: string[];
  expertise: string[];
  mentoring_style: string;
  availability: 'high' | 'medium' | 'low';
  rating: number;
  total_mentees: number;
  success_stories: number;
  languages: string[];
  timezone: string;
  bio: string;
  achievements: string[];
  matching_score: number;
  compatibility_reasons: string[];
  estimated_response_time: string;
}

interface MatchingCriteria {
  industry: string;
  business_stage: string;
  expertise_needed: string[];
  mentoring_preference: string;
  time_commitment: string;
  location_preference: string;
  language: string;
}

interface SmartMentorshipMatcherProps {
  businessIdeaId: number;
  businessIdeaData: any;
  userId?: number;
  onMentorSelected?: (mentor: Mentor) => void;
  className?: string;
}

export const SmartMentorshipMatcher: React.FC<SmartMentorshipMatcherProps> = ({
  businessIdeaId,
  businessIdeaData,
  userId,
  onMentorSelected,
  className = ''
}) => {
  const { t } = useTranslation();
  const [isMatching, setIsMatching] = useState(false);
  const [matchedMentors, setMatchedMentors] = useState<Mentor[]>([]);
  const [selectedMentor, setSelectedMentor] = useState<Mentor | null>(null);
  const [matchingCriteria, setMatchingCriteria] = useState<MatchingCriteria>({
    industry: businessIdeaData?.industry || '',
    business_stage: businessIdeaData?.stage || 'idea',
    expertise_needed: [],
    mentoring_preference: 'structured',
    time_commitment: 'medium',
    location_preference: 'any',
    language: 'en'
  });

  const { sendMessage } = useCentralizedAI();

  const expertiseOptions = [
    'Business Strategy', 'Marketing & Sales', 'Product Development', 'Fundraising',
    'Operations', 'Technology', 'Legal & Compliance', 'Financial Planning',
    'Team Building', 'International Expansion', 'Digital Transformation', 'Sustainability'
  ];

  const startMatching = async () => {
    setIsMatching(true);
    try {
      const matches = await findMentorMatches();
      setMatchedMentors(matches);
    } catch (error) {
      console.error('Mentor matching failed:', error);
    } finally {
      setIsMatching(false);
    }
  };

  const findMentorMatches = async (): Promise<Mentor[]> => {
    const prompt = `Find the best mentor matches for this business idea and criteria:

Business Idea: ${JSON.stringify(businessIdeaData)}
Matching Criteria: ${JSON.stringify(matchingCriteria)}

Please analyze and provide mentor recommendations based on:
1. Industry expertise alignment
2. Business stage experience
3. Specific skills needed
4. Mentoring style compatibility
5. Availability and commitment level

Return detailed mentor profiles with matching scores and compatibility reasons.`;

    const response = await sendMessage(prompt, {
      userId,
      businessIdeaId,
      language: 'en',
      context: {
        type: 'mentor_matching',
        criteria: matchingCriteria,
        businessIdea: businessIdeaData
      }
    });

    // Fetch real mentor data from API with AI-generated matching scores
    try {
      const response = await mentorshipApi.getMatchingMentors({
        business_idea: businessIdea,
        user_preferences: preferences,
        matching_criteria: {
          industry_match: true,
          expertise_match: true,
          availability_match: true,
          location_preference: preferences.location_preference,
          experience_level: preferences.experience_level
        }
      });

      return response.data.map(mentor => ({
        ...mentor,
        matching_score: mentor.ai_matching_score || 0,
        compatibility_reasons: mentor.ai_compatibility_analysis || []
      }));
    } catch (error) {
      console.error('Failed to fetch matching mentors:', error);
      return [];
    }
  };

  const getAvailabilityColor = (availability: string) => {
    switch (availability) {
      case 'high':
        return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30';
      case 'medium':
        return 'text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/30';
      case 'low':
        return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30';
      default:
        return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900/30';
    }
  };

  const getMatchingScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600 dark:text-green-400';
    if (score >= 80) return 'text-blue-600 dark:text-blue-400';
    if (score >= 70) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-gray-600 dark:text-gray-400';
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
            <Users className="w-6 h-6 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              {t('ai.mentorship.title', 'Smart Mentorship Matcher')}
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {t('ai.mentorship.subtitle', 'AI-powered mentor matching based on your needs')}
            </p>
          </div>
        </div>

        {!isMatching && matchedMentors.length === 0 && (
          <button
            onClick={startMatching}
            className="flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            <Brain className="w-4 h-4" />
            <span>{t('ai.mentorship.findMentors', 'Find Mentors')}</span>
          </button>
        )}
      </div>

      {/* Matching Criteria */}
      {!isMatching && matchedMentors.length === 0 && (
        <div className="glass-light border rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-4">
            <Filter className="w-4 h-4 text-gray-600 dark:text-gray-400" />
            <h3 className="font-semibold text-gray-900 dark:text-white">
              {t('ai.mentorship.criteria', 'Matching Criteria')}
            </h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {t('ai.mentorship.businessStage', 'Business Stage')}
              </label>
              <select
                value={matchingCriteria.business_stage}
                onChange={(e) => setMatchingCriteria(prev => ({ ...prev, business_stage: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              >
                <option value="idea">Idea Stage</option>
                <option value="validation">Validation</option>
                <option value="mvp">MVP Development</option>
                <option value="growth">Growth Stage</option>
                <option value="scaling">Scaling</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {t('ai.mentorship.timeCommitment', 'Time Commitment')}
              </label>
              <select
                value={matchingCriteria.time_commitment}
                onChange={(e) => setMatchingCriteria(prev => ({ ...prev, time_commitment: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              >
                <option value="low">1-2 hours/month</option>
                <option value="medium">3-5 hours/month</option>
                <option value="high">6+ hours/month</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {t('ai.mentorship.mentoringStyle', 'Mentoring Style')}
              </label>
              <select
                value={matchingCriteria.mentoring_preference}
                onChange={(e) => setMatchingCriteria(prev => ({ ...prev, mentoring_preference: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              >
                <option value="structured">Structured Program</option>
                <option value="flexible">Flexible Schedule</option>
                <option value="project_based">Project-Based</option>
                <option value="advisory">Strategic Advisory</option>
              </select>
            </div>
          </div>

          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('ai.mentorship.expertiseNeeded', 'Expertise Needed')}
            </label>
            <div className="flex flex-wrap gap-2">
              {expertiseOptions.map((expertise) => (
                <button
                  key={expertise}
                  onClick={() => {
                    setMatchingCriteria(prev => ({
                      ...prev,
                      expertise_needed: prev.expertise_needed.includes(expertise)
                        ? prev.expertise_needed.filter(e => e !== expertise)
                        : [...prev.expertise_needed, expertise]
                    }));
                  }}
                  className={`px-3 py-1 text-xs rounded-full border transition-colors ${
                    matchingCriteria.expertise_needed.includes(expertise)
                      ? 'glass-morphism border-purple-500/30 text-purple-300 bg-purple-600/20'
                      : 'glass-light border-glass-border text-glass-secondary hover:bg-glass-hover'
                  }`}
                >
                  {expertise}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Matching Progress */}
      {isMatching && (
        <div className="glass-light border rounded-lg p-6 text-center">
          <Brain className="w-12 h-12 text-purple-600 mx-auto mb-4 animate-pulse" />
          <h3 className="text-lg font-semibold text-glass-primary mb-2">
            {t('ai.mentorship.matching', 'Finding Perfect Mentors')}
          </h3>
          <p className="text-glass-secondary">
            {t('ai.mentorship.matchingDescription', 'AI is analyzing mentor profiles and compatibility...')}
          </p>
        </div>
      )}

      {/* Matched Mentors */}
      {matchedMentors.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {t('ai.mentorship.matches', 'Recommended Mentors')} ({matchedMentors.length})
            </h3>
            <button
              onClick={startMatching}
              className="flex items-center space-x-2 px-3 py-2 text-sm bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 rounded-lg hover:bg-purple-200 dark:hover:bg-purple-900/50 transition-colors"
            >
              <Search className="w-4 h-4" />
              <span>{t('ai.mentorship.refineSearch', 'Refine Search')}</span>
            </button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {matchedMentors.map((mentor) => (
              <div
                key={mentor.id}
                className="glass-light border rounded-lg p-6 hover:shadow-lg transition-all duration-200"
              >
                {/* Mentor Header */}
                <div className="flex items-start space-x-4 mb-4">
                  <div className="relative">
                    <div className="w-16 h-16 bg-gradient-to-br from-purple-400 to-blue-500 rounded-full flex items-center justify-center text-white font-bold text-xl">
                      {mentor.name.split(' ').map(n => n[0]).join('')}
                    </div>
                    <div className={`absolute -bottom-1 -right-1 w-5 h-5 rounded-full border-2 border-white dark:border-gray-800 ${getAvailabilityColor(mentor.availability)}`} />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="font-semibold text-gray-900 dark:text-white truncate">{mentor.name}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400 truncate">{mentor.title}</p>
                    <p className="text-sm text-gray-500 dark:text-gray-500 truncate">{mentor.company}</p>
                  </div>
                  <div className="text-right">
                    <div className={`text-lg font-bold ${getMatchingScoreColor(mentor.matching_score)}`}>
                      {mentor.matching_score}%
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">match</div>
                  </div>
                </div>

                {/* Mentor Stats */}
                <div className="grid grid-cols-3 gap-2 mb-4 text-center">
                  <div>
                    <div className="flex items-center justify-center space-x-1">
                      <Star className="w-3 h-3 text-yellow-500" />
                      <span className="text-sm font-medium">{mentor.rating}</span>
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Rating</div>
                  </div>
                  <div>
                    <div className="text-sm font-medium">{mentor.total_mentees}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Mentees</div>
                  </div>
                  <div>
                    <div className="text-sm font-medium">{mentor.experience_years}y</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Experience</div>
                  </div>
                </div>

                {/* Expertise Tags */}
                <div className="mb-4">
                  <div className="flex flex-wrap gap-1">
                    {mentor.expertise.slice(0, 3).map((skill) => (
                      <span
                        key={skill}
                        className="px-2 py-1 text-xs bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 rounded-full"
                      >
                        {skill}
                      </span>
                    ))}
                    {mentor.expertise.length > 3 && (
                      <span className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 rounded-full">
                        +{mentor.expertise.length - 3}
                      </span>
                    )}
                  </div>
                </div>

                {/* Compatibility Reasons */}
                <div className="mb-4">
                  <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                    {t('ai.mentorship.whyMatch', 'Why this match?')}
                  </h5>
                  <ul className="space-y-1">
                    {mentor.compatibility_reasons.slice(0, 2).map((reason, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <CheckCircle className="w-3 h-3 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-xs text-gray-600 dark:text-gray-400">{reason}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-2">
                  <button
                    onClick={() => setSelectedMentor(mentor)}
                    className="flex-1 flex items-center justify-center space-x-2 px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm"
                  >
                    <Eye className="w-4 h-4" />
                    <span>{t('ai.mentorship.viewProfile', 'View Profile')}</span>
                  </button>
                  <button
                    onClick={() => onMentorSelected?.(mentor)}
                    className="flex items-center justify-center px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  >
                    <Send className="w-4 h-4" />
                  </button>
                </div>

                {/* Response Time */}
                <div className="mt-3 flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                  <Clock className="w-3 h-3" />
                  <span>{t('ai.mentorship.responseTime', 'Responds')} {mentor.estimated_response_time}</span>
                </div>
              </ThemeWrapper>
            ))}
          </div>
        </div>
      )}

      {/* Mentor Detail Modal */}
      {selectedMentor && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="glass-light max-w-2xl w-full max-h-[90vh] overflow-y-auto rounded-lg border">
            <div className="p-6">
              <div className="flex items-start justify-between mb-6">
                <div className="flex items-center space-x-4">
                  <div className="w-20 h-20 bg-gradient-to-br from-purple-400 to-blue-500 rounded-full flex items-center justify-center text-white font-bold text-2xl">
                    {selectedMentor.name.split(' ').map(n => n[0]).join('')}
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white">{selectedMentor.name}</h3>
                    <p className="text-gray-600 dark:text-gray-400">{selectedMentor.title}</p>
                    <p className="text-gray-500 dark:text-gray-500">{selectedMentor.company}</p>
                    <div className="flex items-center space-x-2 mt-2">
                      <MapPin className="w-4 h-4 text-gray-400" />
                      <span className="text-sm text-gray-600 dark:text-gray-400">{selectedMentor.location}</span>
                    </div>
                  </div>
                </div>
                <button
                  onClick={() => setSelectedMentor(null)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
                >
                  ×
                </button>
              </div>

              <div className="space-y-6">
                {/* Bio */}
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-2">About</h4>
                  <p className="text-gray-600 dark:text-gray-400">{selectedMentor.bio}</p>
                </div>

                {/* Achievements */}
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Key Achievements</h4>
                  <ul className="space-y-1">
                    {selectedMentor.achievements.map((achievement, index) => (
                      <li key={index} className="flex items-center space-x-2">
                        <Award className="w-4 h-4 text-yellow-500" />
                        <span className="text-gray-600 dark:text-gray-400">{achievement}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* All Compatibility Reasons */}
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Why This Match?</h4>
                  <ul className="space-y-2">
                    {selectedMentor.compatibility_reasons.map((reason, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                        <span className="text-gray-600 dark:text-gray-400">{reason}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-3 pt-4">
                  <button
                    onClick={() => {
                      onMentorSelected?.(selectedMentor);
                      setSelectedMentor(null);
                    }}
                    className="flex-1 flex items-center justify-center space-x-2 px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                  >
                    <Send className="w-4 h-4" />
                    <span>{t('ai.mentorship.connectNow', 'Connect Now')}</span>
                  </button>
                  <button
                    onClick={() => setSelectedMentor(null)}
                    className="px-4 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                  >
                    {t('ai.mentorship.close', 'Close')}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
