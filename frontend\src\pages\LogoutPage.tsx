import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch } from '../store/hooks';
import { logout } from '../store/authSlice';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../hooks/useLanguage';
const LogoutPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  useEffect(() => {
    const performLogout = async () => {
      try {
        console.log('Performing logout...');
        await dispatch(logout()).unwrap();
        console.log('Logout successful, redirecting to home...');
        
        // Clear any remaining local storage
        localStorage.clear();
        sessionStorage.clear();
        
        // Redirect to home page
        navigate('/', { replace: true });
      } catch (error) {
        console.error('Logout failed:', error);
        // Even if logout fails, clear local state and redirect
        localStorage.clear();
        sessionStorage.clear();
        navigate('/', { replace: true });
      }
    };

    performLogout();
  }, [dispatch, navigate]);

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}>
      <div className="text-center">
        <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <h2 className="text-xl font-semibold text-white mb-2">{t('auth.loggingOut')}</h2>
        <p className="text-gray-300">{t('auth.pleaseWait')}</p>
      </div>
    </div>
  );
};

export default LogoutPage;
