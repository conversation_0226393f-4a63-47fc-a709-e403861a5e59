import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Briefcase, Clock, Award, Users, ArrowRight, Star, Calendar } from 'lucide-react';
import { MentorProfile } from '../../services/incubatorApi';

import { useTranslation } from 'react-i18next';
interface MentorProfileCardProps {
  mentor: MentorProfile;
  compact?: boolean;
}

const MentorProfileCard: React.FC<MentorProfileCardProps> = ({ mentor, compact = false  }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  // Get expertise categories
  const expertiseCategories = mentor.expertise_areas.reduce<Record<string, string[]>>((acc, expertise) => {
    if (!acc[expertise.category_display]) {
      acc[expertise.category_display] = [];
    }
    acc[expertise.category_display].push(expertise.specific_expertise);
    return acc;
  }, {});

  // Format availability
  const getAvailabilityColor = (availability: string) => {
    switch (availability) {
      case 'high':
        return 'bg-green-600/50 text-green-200';
      case 'medium':
        return 'bg-blue-600/50 text-blue-200';
      case 'low':
        return 'bg-yellow-600/50 text-yellow-200';
      case 'limited':
        return 'bg-red-600/50 text-red-200';
      default:
        return 'bg-gray-600/50 text-gray-200';
    }
  };

  // Get top 3 expertise areas
  const topExpertise = mentor.expertise_areas
    .sort((a, b) => {
      // Sort by level first (expert > advanced > intermediate > beginner)
      const levelOrder = { expert: 4, advanced: 3, intermediate: 2, beginner: 1 };
      const levelDiff = (levelOrder[b.level as keyof typeof levelOrder] || 0) - (levelOrder[a.level as keyof typeof levelOrder] || 0);
      
      if (levelDiff !== 0) return levelDiff;
      
      // Then by years of experience
      return b.years_experience - a.years_experience;
    })
    .slice(0, 3);

  if (compact) {
    return (
      <div className="bg-indigo-900/50 rounded-lg overflow-hidden border border-indigo-800 hover:border-purple-500 transition-all duration-300 p-4">
        <div className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`w-12 h-12 rounded-full bg-purple-700 flex items-center justify-center text-white text-lg font-bold ${isRTL ? "flex-row-reverse" : ""}`}>
            {mentor.user.first_name.charAt(0)}{mentor.user.last_name.charAt(0)}
          </div>
          <div>
            <h3 className="text-lg font-semibold text-white">
              {mentor.user.first_name} {mentor.user.last_name}
            </h3>
            <div className="text-gray-300 text-sm">{mentor.position} at {mentor.company}</div>
          </div>
        </div>
        
        <div className={`mt-3 flex flex-wrap gap-1 ${isRTL ? "flex-row-reverse" : ""}`}>
          {topExpertise.map(expertise => (
            <span key={expertise.id} className="px-2 py-1 bg-purple-900/50 text-purple-200 rounded-md text-xs">
              {expertise.specific_expertise}
            </span>
          ))}
        </div>
        
        <div className={`mt-3 flex justify-between items-center ${isRTL ? "flex-row-reverse" : ""}`}>
          <span className={`px-2 py-1 rounded-md text-xs ${getAvailabilityColor(mentor.availability)}`}>
            {mentor.availability_display}
          </span>
          
          <Link
            to={`/incubator/mentors/${mentor.id}`}
            className={`text-purple-400 hover:text-purple-300 text-sm flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
          >
            View Profile <ArrowRight size={14} className={`ml-1 ${isRTL ? "space-x-reverse" : ""}`} />
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-indigo-900/50 rounded-lg overflow-hidden border border-indigo-800 hover:border-purple-500 transition-all duration-300 hover:shadow-glow">
      <div className="p-6">
        <div className={`flex justify-between items-start mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`flex items-center space-x-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className={`w-16 h-16 rounded-full bg-purple-700 flex items-center justify-center text-white text-xl font-bold ${isRTL ? "flex-row-reverse" : ""}`}>
              {mentor.user.first_name.charAt(0)}{mentor.user.last_name.charAt(0)}
            </div>
            <div>
              <h3 className="text-xl font-bold text-white">
                {mentor.user.first_name} {mentor.user.last_name}
              </h3>
              <div className="text-gray-300">
                {mentor.position} at {mentor.company}
              </div>
            </div>
          </div>
          
          <span className={`px-2 py-1 rounded-md text-xs font-medium ${getAvailabilityColor(mentor.availability)}`}>
            {mentor.availability_display}
          </span>
        </div>
        
        <div className="mb-4">
          <div className="text-gray-300 line-clamp-3">
            {mentor.bio}
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className={`flex items-center text-gray-300 ${isRTL ? "flex-row-reverse" : ""}`}>
            <Briefcase size={16} className={`mr-2 text-purple-400 ${isRTL ? "space-x-reverse" : ""}`} />
            <span>{mentor.years_of_experience} years experience</span>
          </div>
          
          <div className={`flex items-center text-gray-300 ${isRTL ? "flex-row-reverse" : ""}`}>
            <Users size={16} className={`mr-2 text-blue-400 ${isRTL ? "space-x-reverse" : ""}`} />
            <span>Mentoring {mentor.active_mentorships_count} mentees</span>
          </div>
          
          <div className={`flex items-center text-gray-300 ${isRTL ? "flex-row-reverse" : ""}`}>
            <Award size={16} className={`mr-2 text-yellow-400 ${isRTL ? "space-x-reverse" : ""}`} />
            <span>{mentor.expertise_areas.length} expertise areas</span>
          </div>
          
          <div className={`flex items-center text-gray-300 ${isRTL ? "flex-row-reverse" : ""}`}>
            <Clock size={16} className={`mr-2 text-green-400 ${isRTL ? "space-x-reverse" : ""}`} />
            <span>Max {mentor.max_mentees} mentees</span>
          </div>
        </div>
        
        <div className="mb-4">
          <h4 className="text-sm uppercase text-gray-400 mb-2">t("common.top.expertise", "Top Expertise")</h4>
          <div className={`flex flex-wrap gap-2 ${isRTL ? "flex-row-reverse" : ""}`}>
            {topExpertise.map(expertise => (
              <div key={expertise.id} className={`px-3 py-1.5 bg-purple-900/50 text-purple-200 rounded-md text-sm flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <Star size={14} className={`mr-1.5 text-yellow-400 ${isRTL ? "space-x-reverse" : ""}`} />
                {expertise.specific_expertise}
                <span className={`ml-1.5 text-xs text-gray-400 ${isRTL ? "space-x-reverse" : ""}`}>({expertise.level_display})</span>
              </div>
            ))}
          </div>
        </div>
        
        <div className={`flex justify-between items-center mt-4 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`text-sm text-gray-400 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <Calendar size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
            Joined {new Date(mentor.created_at).toLocaleDateString()}
          </div>
          
          <Link
            to={`/incubator/mentors/${mentor.id}`}
            className={`px-3 py-1.5 bg-purple-600 hover:bg-purple-700 rounded-md text-white text-sm flex items-center transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
          >
            View Profile <ArrowRight size={14} className={`ml-1 ${isRTL ? "space-x-reverse" : ""}`} />
          </Link>
        </div>
      </div>
    </div>
  );
};

export default MentorProfileCard;
