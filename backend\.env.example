# Django settings
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database settings
USE_POSTGRES=False
DB_NAME=yasmeen_ai
DB_USER=postgres
DB_PASSWORD=postgres
DB_HOST=localhost
DB_PORT=5432

# Gemini AI API key
GEMINI_API_KEY=your-gemini-api-key-here

# Email settings
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password
EMAIL_USE_TLS=True

# CORS settings
# Add your production domains here for CORS_ALLOWED_ORIGINS
PRODUCTION_DOMAIN=https://yasmeenai.com

# Security settings
# Set these to True in production
CSRF_COOKIE_SECURE=False
CSRF_COOKIE_HTTPONLY=False
