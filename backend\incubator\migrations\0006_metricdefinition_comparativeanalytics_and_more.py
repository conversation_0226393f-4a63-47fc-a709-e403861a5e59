# Generated by Django 5.1.7 on 2025-05-15 18:54

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('incubator', '0005_businessplantemplate_businessplan_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='MetricDefinition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('metric_key', models.CharField(help_text='Unique identifier for the metric', max_length=100, unique=True)),
                ('display_name', models.CharField(help_text='User-friendly name for the metric', max_length=100)),
                ('description', models.TextField(help_text='Detailed explanation of what the metric measures')),
                ('calculation_method', models.TextField(help_text='How the metric is calculated')),
                ('low_value_interpretation', models.TextField(help_text='What a low value means')),
                ('medium_value_interpretation', models.TextField(help_text='What a medium value means')),
                ('high_value_interpretation', models.TextField(help_text='What a high value means')),
                ('improvement_suggestions', models.TextField(help_text='Suggestions for improving this metric')),
                ('visualization_settings', models.JSONField(default=dict, help_text='Settings for visualizing this metric')),
                ('category', models.CharField(choices=[('progress', 'Progress Metrics'), ('engagement', 'Engagement Metrics'), ('comparative', 'Comparative Metrics'), ('predictive', 'Predictive Metrics')], default='progress', max_length=50)),
                ('display_order', models.IntegerField(default=0, help_text='Order in which to display this metric')),
            ],
            options={
                'ordering': ['category', 'display_order'],
            },
        ),
        migrations.CreateModel(
            name='ComparativeAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('similar_ideas', models.JSONField(default=dict, help_text='List of similar business ideas and their metrics')),
                ('industry_averages', models.JSONField(default=dict, help_text='Average metrics for the industry')),
                ('stage_averages', models.JSONField(default=dict, help_text='Average metrics for the current stage')),
                ('percentile_rankings', models.JSONField(default=dict, help_text='Percentile rankings across different metrics')),
                ('competitive_advantages', models.JSONField(default=dict, help_text='Identified competitive advantages')),
                ('competitive_disadvantages', models.JSONField(default=dict, help_text='Identified competitive disadvantages')),
                ('last_calculated', models.DateTimeField(auto_now=True)),
                ('business_idea', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='comparative_analytics', to='incubator.businessidea')),
            ],
        ),
        migrations.CreateModel(
            name='PredictiveAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('growth_predictions', models.JSONField(default=dict, help_text='Predicted growth metrics for the next 6 months')),
                ('milestone_predictions', models.JSONField(default=dict, help_text='Predicted completion dates for milestones')),
                ('success_probability', models.FloatField(default=0.0, help_text='Probability of business success (0-100)')),
                ('risk_factors', models.JSONField(default=dict, help_text='Identified risk factors and their severity')),
                ('opportunity_areas', models.JSONField(default=dict, help_text='Identified opportunity areas for growth')),
                ('prediction_confidence', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High')], default='low', help_text='Confidence level in the predictions', max_length=20)),
                ('last_calculated', models.DateTimeField(auto_now=True)),
                ('business_idea', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='predictive_analytics', to='incubator.businessidea')),
            ],
        ),
        migrations.CreateModel(
            name='AnalyticsSnapshot',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('snapshot_date', models.DateField(auto_now_add=True)),
                ('progress_rate', models.FloatField(default=0.0)),
                ('milestone_completion_rate', models.FloatField(default=0.0)),
                ('goal_achievement_rate', models.FloatField(default=0.0)),
                ('team_size', models.IntegerField(default=1)),
                ('mentor_engagement', models.FloatField(default=0.0)),
                ('industry_percentile', models.FloatField(default=0.0)),
                ('stage_percentile', models.FloatField(default=0.0)),
                ('success_probability', models.FloatField(default=0.0)),
                ('business_idea', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='analytics_snapshots', to='incubator.businessidea')),
            ],
            options={
                'ordering': ['-snapshot_date'],
                'unique_together': {('business_idea', 'snapshot_date')},
            },
        ),
    ]
