import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Bar<PERSON>hart2 } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../../store/hooks';
import { RTLIcon, RTLText, RTLFlex } from '../common';
interface ChatHeaderProps {
  onClearChat: () => void;
}

const ChatHeader: React.FC<ChatHeaderProps> = ({ onClearChat  }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { language } = useAppSelector(state => state.language);

  return (
    <div className={`flex justify-between items-center p-4 border-b border-gray-800 bg-gray-800 ${isRTL ? "flex-row-reverse" : ""}`}>
      <RTLFlex className="items-center">
        <RTLIcon icon={Bot} size={20} className={`text-purple-400 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
        <RTLText as="h2" className="font-medium">{t('chat.chatWith')}</RTLText>
      </RTLFlex>
      <div className={`flex ${language === 'ar' ? 'space-x-reverse' : 'space-x-2'} space-x-2`}>
        <RTLFlex
          as={Link}
          to="/chat/enhanced"
          className="px-3 py-1 bg-indigo-600 hover:bg-indigo-700 rounded-md text-sm text-white"
          align="center"
        >
          <RTLIcon icon={Sparkles} size={14} className={language === 'ar' ? 'ml-1' : 'mr-1'} />
          {t('chat.enhanced')}
        </RTLFlex>
        <RTLFlex
          as={Link}
          to="/chat/analytics"
          className="px-3 py-1 bg-purple-600 hover:bg-purple-700 rounded-md text-sm text-white"
          align="center"
        >
          <RTLIcon icon={BarChart2} size={14} className={language === 'ar' ? 'ml-1' : 'mr-1'} />
          {t('chat.analytics.title')}
        </RTLFlex>
        <button
          onClick={onClearChat}
          className="px-3 py-1 bg-gray-700 hover:bg-gray-600 rounded-md text-sm text-white"
        >
          {t('chat.clear')}
        </button>
      </div>
    </div>
  );
};

export default ChatHeader;
