# Generated by Django 5.1.7 on 2025-06-11 12:11

import datetime
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("incubator", "0013_populate_template_real_data"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="BusinessPlanAnalytics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("total_time_spent", models.DurationField(default=datetime.timedelta)),
                (
                    "average_session_duration",
                    models.DurationField(default=datetime.timedelta),
                ),
                ("total_sessions", models.IntegerField(default=0)),
                ("total_collaborators", models.IntegerField(default=0)),
                ("total_comments", models.IntegerField(default=0)),
                ("total_edits", models.IntegerField(default=0)),
                ("total_reviews", models.IntegerField(default=0)),
                ("total_exports", models.IntegerField(default=0)),
                ("pdf_exports", models.IntegerField(default=0)),
                ("word_exports", models.IntegerField(default=0)),
                ("excel_exports", models.IntegerField(default=0)),
                ("completion_rate", models.FloatField(default=0.0)),
                ("sections_completed", models.IntegerField(default=0)),
                ("total_sections", models.IntegerField(default=0)),
                ("ai_assistance_sessions", models.IntegerField(default=0)),
                ("ai_content_generated", models.IntegerField(default=0)),
                ("word_count", models.IntegerField(default=0)),
                ("character_count", models.IntegerField(default=0)),
                ("last_calculated", models.DateTimeField(auto_now=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "business_plan",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="analytics",
                        to="incubator.businessplan",
                    ),
                ),
            ],
            options={
                "db_table": "business_plan_analytics",
            },
        ),
        migrations.CreateModel(
            name="TemplateSuccessMetrics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("total_uses", models.IntegerField(default=0)),
                ("total_completions", models.IntegerField(default=0)),
                ("completion_rate", models.FloatField(default=0.0)),
                (
                    "average_completion_time",
                    models.DurationField(default=datetime.timedelta),
                ),
                ("fastest_completion", models.DurationField(blank=True, null=True)),
                ("slowest_completion", models.DurationField(blank=True, null=True)),
                ("plans_published", models.IntegerField(default=0)),
                ("funding_received", models.IntegerField(default=0)),
                ("business_launched", models.IntegerField(default=0)),
                ("average_rating", models.FloatField(default=0.0)),
                ("total_ratings", models.IntegerField(default=0)),
                ("last_calculated", models.DateTimeField(auto_now=True)),
                (
                    "template",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="success_metrics",
                        to="incubator.businessplantemplate",
                    ),
                ),
            ],
            options={
                "db_table": "template_success_metrics",
            },
        ),
        migrations.CreateModel(
            name="BusinessPlanCollaboration",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "action_type",
                    models.CharField(
                        choices=[
                            ("view", "View"),
                            ("edit", "Edit"),
                            ("comment", "Comment"),
                            ("review", "Review"),
                            ("approve", "Approve"),
                            ("reject", "Reject"),
                            ("share", "Share"),
                            ("invite", "Invite Collaborator"),
                        ],
                        max_length=20,
                    ),
                ),
                ("section_id", models.IntegerField(blank=True, null=True)),
                ("section_title", models.CharField(blank=True, max_length=200)),
                (
                    "content",
                    models.TextField(
                        blank=True, help_text="Comment content or edit description"
                    ),
                ),
                (
                    "metadata",
                    models.JSONField(
                        default=dict, help_text="Additional action metadata"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                (
                    "business_plan",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="collaborations",
                        to="incubator.businessplan",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "business_plan_collaboration",
                "indexes": [
                    models.Index(
                        fields=["business_plan", "created_at"],
                        name="business_pl_busines_31df19_idx",
                    ),
                    models.Index(
                        fields=["user", "action_type"],
                        name="business_pl_user_id_0a4925_idx",
                    ),
                    models.Index(
                        fields=["action_type", "created_at"],
                        name="business_pl_action__f48885_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="BusinessPlanExport",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "export_format",
                    models.CharField(
                        choices=[
                            ("pdf", "PDF"),
                            ("word", "Microsoft Word"),
                            ("excel", "Microsoft Excel"),
                            ("powerpoint", "PowerPoint"),
                            ("json", "JSON"),
                            ("html", "HTML"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "export_type",
                    models.CharField(
                        choices=[
                            ("full", "Full Business Plan"),
                            ("summary", "Executive Summary"),
                            ("financial", "Financial Projections"),
                            ("sections", "Selected Sections"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "sections_included",
                    models.JSONField(
                        default=list, help_text="List of section IDs included"
                    ),
                ),
                (
                    "file_size",
                    models.BigIntegerField(
                        blank=True, help_text="File size in bytes", null=True
                    ),
                ),
                ("file_path", models.CharField(blank=True, max_length=500)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("downloaded_at", models.DateTimeField(blank=True, null=True)),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("user_agent", models.TextField(blank=True)),
                ("export_successful", models.BooleanField(default=True)),
                ("error_message", models.TextField(blank=True)),
                (
                    "business_plan",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="exports",
                        to="incubator.businessplan",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "business_plan_export",
                "indexes": [
                    models.Index(
                        fields=["business_plan", "created_at"],
                        name="business_pl_busines_3b05bf_idx",
                    ),
                    models.Index(
                        fields=["user", "export_format"],
                        name="business_pl_user_id_92adfd_idx",
                    ),
                    models.Index(
                        fields=["export_format", "created_at"],
                        name="business_pl_export__4f17e1_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="BusinessPlanSession",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("start_time", models.DateTimeField(auto_now_add=True)),
                ("end_time", models.DateTimeField(blank=True, null=True)),
                ("last_activity", models.DateTimeField(auto_now=True)),
                ("session_id", models.CharField(max_length=100, unique=True)),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("user_agent", models.TextField(blank=True)),
                ("device_type", models.CharField(blank=True, max_length=20)),
                (
                    "sections_viewed",
                    models.JSONField(
                        default=list, help_text="List of section IDs viewed"
                    ),
                ),
                (
                    "sections_edited",
                    models.JSONField(
                        default=list, help_text="List of section IDs edited"
                    ),
                ),
                ("ai_assistance_used", models.BooleanField(default=False)),
                (
                    "business_plan",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sessions",
                        to="incubator.businessplan",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "business_plan_session",
                "indexes": [
                    models.Index(
                        fields=["business_plan", "start_time"],
                        name="business_pl_busines_7c1631_idx",
                    ),
                    models.Index(
                        fields=["user", "start_time"],
                        name="business_pl_user_id_787086_idx",
                    ),
                    models.Index(
                        fields=["session_id"], name="business_pl_session_11af1f_idx"
                    ),
                ],
            },
        ),
    ]
