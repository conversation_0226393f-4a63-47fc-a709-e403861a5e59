import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { useAppSelector } from '../../store/hooks';
import { useCRUD } from '../../hooks/useCRUD';
import { Event, eventsAPI } from '../../services/api';
import EventForm from '../../components/events/forms/EventForm';
import { CRUDTable } from '../../components/common';
import AuthenticatedLayout from '../../components/layout/AuthenticatedLayout';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  Calendar, 
  MapPin,
  Users,
  DollarSign,
  Clock,
  AlertCircle,
  CheckCircle,
  Globe,
  Star
} from 'lucide-react';

const EventsPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { user } = useAppSelector(state => state.auth);
  
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);

  // CRUD operations for events
  const eventsCRUD = useCRUD({
    create: async (data: Partial<Event>) => {
      if (!user) throw new Error('User not authenticated');
      return eventsAPI.createEvent({
        ...data,
        organizer_id: user.id
      });
    },
    read: () => eventsAPI.getEvents(),
    update: (id: number, data: Partial<Event>) => 
      eventsAPI.updateEvent(id, data),
    delete: (id: number) => eventsAPI.deleteEvent(id)
  }, {
    onSuccess: (operation) => {
      if (operation === 'create') {
        setShowCreateForm(false);
      } else if (operation === 'update') {
        setShowEditForm(false);
        setSelectedEvent(null);
      } else if (operation === 'delete') {
        setShowDeleteDialog(false);
        setSelectedEvent(null);
      }
    }
  });

  // Load data on component mount
  useEffect(() => {
    eventsCRUD.readItems();
  }, []);

  // Handle create
  const handleCreate = async (data: Partial<Event>) => {
    return await eventsCRUD.createItem(data);
  };

  // Handle edit
  const handleEdit = (event: Event) => {
    setSelectedEvent(event);
    setShowEditForm(true);
  };

  // Handle update
  const handleUpdate = async (data: Partial<Event>) => {
    if (!selectedEvent) return false;
    return await eventsCRUD.updateItem(selectedEvent.id, data);
  };

  // Handle delete
  const handleDelete = (event: Event) => {
    setSelectedEvent(event);
    setShowDeleteDialog(true);
  };

  // Confirm delete
  const confirmDelete = async () => {
    if (!selectedEvent) return;
    await eventsCRUD.deleteItem(selectedEvent.id);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get event type icon
  const getEventTypeIcon = (type: string) => {
    switch (type) {
      case 'workshop':
        return <Users size={16} className="text-blue-400" />;
      case 'webinar':
        return <Globe size={16} className="text-green-400" />;
      case 'networking':
        return <Users size={16} className="text-purple-400" />;
      case 'conference':
        return <Calendar size={16} className="text-orange-400" />;
      case 'meetup':
        return <MapPin size={16} className="text-pink-400" />;
      case 'competition':
        return <Star size={16} className="text-yellow-400" />;
      default:
        return <Calendar size={16} className="text-gray-400" />;
    }
  };

  // Table columns configuration
  const columns = [
    {
      key: 'title',
      label: t('events.title', 'Title'),
      render: (value: any, event: Event) => (
        <div>
          <div className="flex items-center gap-2">
            {getEventTypeIcon(event.event_type)}
            <span className="font-medium text-white">{event.title}</span>
            {event.is_virtual && (
              <Globe size={14} className="text-blue-400" />
            )}
          </div>
          <div className="text-sm text-gray-400 truncate max-w-xs mt-1">
            {event.description}
          </div>
        </div>
      )
    },
    {
      key: 'event_type',
      label: t('events.type', 'Type'),
      render: (value: any, event: Event) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          event.event_type === 'workshop' ? 'bg-blue-900/50 text-blue-200' :
          event.event_type === 'webinar' ? 'bg-green-900/50 text-green-200' :
          event.event_type === 'networking' ? 'bg-purple-900/50 text-purple-200' :
          event.event_type === 'conference' ? 'bg-orange-900/50 text-orange-200' :
          event.event_type === 'meetup' ? 'bg-pink-900/50 text-pink-200' :
          'bg-yellow-900/50 text-yellow-200'
        }`}>
          {t(`events.types.${event.event_type}`, event.event_type)}
        </span>
      )
    },
    {
      key: 'start_date',
      label: t('events.startDate', 'Start Date'),
      render: (value: any, event: Event) => (
        <div className="flex items-center gap-2">
          <Calendar size={16} className="text-blue-400" />
          <span className={`text-sm ${
            new Date(event.start_date) < new Date() ? 'text-gray-400' : 'text-gray-300'
          }`}>
            {formatDate(event.start_date)}
          </span>
        </div>
      )
    },
    {
      key: 'location',
      label: t('events.location', 'Location'),
      render: (value: any, event: Event) => (
        <div className="flex items-center gap-2">
          {event.is_virtual ? (
            <Globe size={16} className="text-green-400" />
          ) : (
            <MapPin size={16} className="text-red-400" />
          )}
          <span className="text-gray-300 text-sm truncate max-w-xs">
            {event.is_virtual ? t('events.virtual', 'Virtual') : event.location}
          </span>
        </div>
      )
    },
    {
      key: 'attendees',
      label: t('events.attendees', 'Attendees'),
      render: (value: any, event: Event) => (
        <div className="flex items-center gap-2">
          <Users size={16} className="text-purple-400" />
          <span className="text-gray-300 text-sm">
            {event.attendees_count || 0}
            {event.max_attendees > 0 && `/${event.max_attendees}`}
          </span>
        </div>
      )
    },
    {
      key: 'price',
      label: t('events.price', 'Price'),
      render: (value: any, event: Event) => (
        <div className="flex items-center gap-2">
          <DollarSign size={16} className="text-green-400" />
          <span className="text-gray-300 text-sm">
            {event.is_free ? t('events.free', 'Free') : `$${event.price}`}
          </span>
        </div>
      )
    },
    {
      key: 'status',
      label: t('events.status', 'Status'),
      render: (value: any, event: Event) => (
        <div className="flex items-center gap-2">
          {event.is_published ? (
            <CheckCircle size={16} className="text-green-400" />
          ) : (
            <Clock size={16} className="text-yellow-400" />
          )}
          <span className={`text-sm ${
            event.is_published ? 'text-green-400' : 'text-yellow-400'
          }`}>
            {event.is_published ? t('events.published', 'Published') : t('events.draft', 'Draft')}
          </span>
        </div>
      )
    }
  ];

  // Table actions
  const actions = [
    {
      label: t('common.view', 'View'),
      icon: <Eye className="w-4 h-4" />,
      onClick: (event: Event) => {
        // Navigate to event detail page
        window.location.href = `/events/${event.id}`;
      },
      variant: 'secondary' as const
    },
    {
      label: t('common.edit', 'Edit'),
      icon: <Edit className="w-4 h-4" />,
      onClick: handleEdit,
      variant: 'primary' as const,
      disabled: (event: Event) => event.organizer?.id !== user?.id
    },
    {
      label: t('common.delete', 'Delete'),
      icon: <Trash2 className="w-4 h-4" />,
      onClick: handleDelete,
      variant: 'danger' as const,
      disabled: (event: Event) => event.organizer?.id !== user?.id
    }
  ];

  // Stats cards data
  const allEvents = eventsCRUD.data;
  const userEvents = allEvents.filter(event => event.organizer?.id === user?.id);
  const publishedEvents = allEvents.filter(event => event.is_published);
  const upcomingEvents = allEvents.filter(event => new Date(event.start_date) > new Date());

  const stats = [
    {
      title: t('events.totalEvents', 'Total Events'),
      value: allEvents.length,
      icon: Calendar,
      color: 'blue'
    },
    {
      title: t('events.published', 'Published'),
      value: publishedEvents.length,
      icon: CheckCircle,
      color: 'green'
    },
    {
      title: t('events.upcoming', 'Upcoming'),
      value: upcomingEvents.length,
      icon: Clock,
      color: 'yellow'
    },
    {
      title: t('events.myEvents', 'My Events'),
      value: userEvents.length,
      icon: Star,
      color: 'purple'
    }
  ];

  return (
    <>
      <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="space-y-6">
        {/* Header */}
        <div className={`flex flex-col sm:flex-row sm:items-center sm:justify-between ${isRTL ? 'sm:flex-row-reverse' : ''}`}>
          <div>
            <h1 className="text-2xl font-bold text-white">
              {t('events.events', 'Events')}
            </h1>
            <p className="text-gray-400 mt-1">
              {t('events.manageEvents', 'Create and manage community events')}
            </p>
          </div>
          <button
            onClick={() => setShowCreateForm(true)}
            className="mt-4 sm:mt-0 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center gap-2"
          >
            <Plus size={20} />
            {t('events.createEvent', 'Create Event')}
          </button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <div key={index} className="bg-gray-800 rounded-lg p-6">
              <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div>
                  <p className="text-gray-400 text-sm">{stat.title}</p>
                  <p className="text-2xl font-bold text-white mt-1">{stat.value}</p>
                </div>
                <div className={`p-3 rounded-lg ${
                  stat.color === 'blue' ? 'bg-blue-900/50' :
                  stat.color === 'green' ? 'bg-green-900/50' :
                  stat.color === 'yellow' ? 'bg-yellow-900/50' :
                  'bg-purple-900/50'
                }`}>
                  <stat.icon size={24} className={
                    stat.color === 'blue' ? 'text-blue-400' :
                    stat.color === 'green' ? 'text-green-400' :
                    stat.color === 'yellow' ? 'text-yellow-400' :
                    'text-purple-400'
                  } />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Error Display */}
        {eventsCRUD.error && (
          <div className="bg-red-900/50 border border-red-500 rounded-lg p-4">
            <div className="flex items-center gap-2">
              <AlertCircle size={20} className="text-red-400" />
              <span className="text-red-200">{eventsCRUD.error}</span>
            </div>
          </div>
        )}

        {/* Events Table */}
        <div className="bg-gray-800 rounded-lg">
          <CRUDTable
            data={eventsCRUD.data}
            columns={columns}
            actions={actions}
            isLoading={eventsCRUD.isLoading}
            emptyMessage={t('events.noEvents', 'No events found. Create your first event to get started!')}
            searchPlaceholder={t('events.searchEvents', 'Search events...')}
            title={t('events.allEvents', 'All Events')}
            createButtonLabel={t('events.createEvent', 'Create Event')}
            onCreate={() => setShowCreateForm(true)}
          />
        </div>
      </div>

      {/* Create Form Modal */}
      {showCreateForm && (
        <EventForm
          mode="create"
          onSubmit={handleCreate}
          onCancel={() => setShowCreateForm(false)}
          isSubmitting={eventsCRUD.isLoading}
        />
      )}

      {/* Edit Form Modal */}
      {showEditForm && selectedEvent && (
        <EventForm
          mode="edit"
          initialData={selectedEvent}
          onSubmit={handleUpdate}
          onCancel={() => {
            setShowEditForm(false);
            setSelectedEvent(null);
          }}
          isSubmitting={eventsCRUD.isLoading}
        />
      )}

      {/* Delete Confirmation Dialog */}
      {showDeleteDialog && selectedEvent && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-gray-900 rounded-lg shadow-xl w-full max-w-md">
            <div className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <AlertCircle size={24} className="text-red-400" />
                <h3 className="text-lg font-semibold text-white">
                  {t('common.confirmDelete', 'Confirm Delete')}
                </h3>
              </div>
              <p className="text-gray-300 mb-6">
                {t('events.deleteConfirmation', 'Are you sure you want to delete this event? This will also cancel all registrations. This action cannot be undone.')}
              </p>
              <div className={`flex gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <button
                  onClick={() => {
                    setShowDeleteDialog(false);
                    setSelectedEvent(null);
                  }}
                  className="flex-1 px-4 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors"
                  disabled={eventsCRUD.isLoading}
                >
                  {t('common.cancel', 'Cancel')}
                </button>
                <button
                  onClick={confirmDelete}
                  className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
                  disabled={eventsCRUD.isLoading}
                >
                  {eventsCRUD.isLoading ? t('common.deleting', 'Deleting...') : t('common.delete', 'Delete')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
              </div>
        </div>
      </div>
    </>
  );
};

export default EventsPage;
