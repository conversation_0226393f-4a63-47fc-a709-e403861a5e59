import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { fetchReputationLeaderboard } from '../store/forumSlice';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';

import {
  Award,
  ArrowLeft,
  Users,
  Trophy,
  Medal,
  MessageSquare,
  CheckCircle,
  ThumbsUp
} from 'lucide-react';
import { useTranslation } from 'react-i18next';

const ForumLeaderboardPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const dispatch = useAppDispatch();
  const { reputationLeaderboard, loading, error } = useAppSelector((state) => state.forum);

  useEffect(() => {
    dispatch(fetchReputationLeaderboard());
  }, [dispatch]);

  // Function to get position badge
  const getPositionBadge = (position: number) => {

    switch (position) {
      case 0:
        return (
          <div className={`flex items-center justify-center w-8 h-8 rounded-full glass-light text-yellow-400 ${isRTL ? "flex-row-reverse" : ""}`}>
            <Trophy size={16} />
          </div>
        );
      case 1:
        return (
          <div className={`flex items-center justify-center w-8 h-8 rounded-full glass-light text-gray-300 ${isRTL ? "flex-row-reverse" : ""}`}>
            <Medal size={16} />
          </div>
        );
      case 2:
        return (
          <div className={`flex items-center justify-center w-8 h-8 rounded-full glass-light text-amber-500 ${isRTL ? "flex-row-reverse" : ""}`}>
            <Medal size={16} />
          </div>
        );
      default:
        return (
          <div className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium glass-light text-glass-primary ${isRTL ? "flex-row-reverse" : ""}`}>
            #{position + 1}
          </div>
        );
    }
  };

  // Function to get level color
  const getLevelColor = (level: string) => {
    const levelColors = {
      'Newcomer': 'text-gray-400',
      'Contributor': 'text-blue-400',
      'Active Member': 'text-green-400',
      'Expert': 'text-purple-400',
      'Mentor': 'text-yellow-400',
      'Community Leader': 'text-red-400'
    };

    return levelColors[level as keyof typeof levelColors] || 'text-gray-400';
  };

  return (
    <div className={`min-h-screen flex flex-col ${isRTL ? "flex-row-reverse" : ""}`}>
      <Navbar />

      <main className={`flex-grow container mx-auto px-4 py-8 mt-16 ${isRTL ? "flex-row-reverse" : ""}`}>
        {/* Breadcrumb */}
        <div className="mb-6">
          <Link
            to="/forum"
            className={`flex items-center hover:opacity-80 transition-opacity text-purple-400 ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <ArrowLeft size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
            Back to Forums
          </Link>
        </div>

        <h1 className={`text-3xl font-bold mb-8 flex items-center text-glass-primary ${isRTL ? "flex-row-reverse" : ""}`}>
          <span className={`mr-3 text-yellow-400 ${isRTL ? "space-x-reverse" : ""}`}>
            <Trophy size={28} />
          </span>
          Community Leaderboard
        </h1>

        {loading && reputationLeaderboard.length === 0 ? (
          <div className="glass-light rounded-lg p-6 border">
            <div className={`flex justify-center items-center h-40 ${isRTL ? "flex-row-reverse" : ""}`}>
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
            </div>
          </div>
        ) : error ? (
          <div className="glass-light rounded-lg p-4 mb-6 border bg-red-900/30 border-red-800 text-red-300">
            <div>{error}</div>
          </div>
        ) : reputationLeaderboard.length > 0 ? (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              {/* Leaderboard Table */}
              <ThemeWrapper
                className="rounded-lg p-6 backdrop-blur-sm border"
                darkClassName="bg-indigo-900/30 border-indigo-800/50"
                lightClassName="bg-white border-slate-200"
              >
                <h2 className={`text-xl font-semibold mb-6 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                  <ThemeWrapper
                    as="span"
                    className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`}
                    darkClassName="text-blue-400"
                    lightClassName="text-blue-600"
                  >
                    <Users size={20} />
                  </ThemeWrapper>
                  Top Contributors
                </h2>

                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <ThemeWrapper
                        as="tr"
                        className="border-b"
                        darkClassName="border-indigo-800/50"
                        lightClassName="border-slate-300"
                      >
                        <th className={`px-4 py-3  ${isRTL ? "text-right" : "text-left"}`}>t("common.rank", "Rank")</th>
                        <th className={`px-4 py-3  ${isRTL ? "text-right" : "text-left"}`}>t("common.user", "User")</th>
                        <th className={`px-4 py-3  ${isRTL ? "text-right" : "text-left"}`}>t("common.level", "Level")</th>
                        <th className="px-4 py-3 text-right">t("common.points", "Points")</th>
                        <th className="px-4 py-3 text-right">t("common.threads", "Threads")</th>
                        <th className="px-4 py-3 text-right">t("common.posts", "Posts")</th>
                        <th className="px-4 py-3 text-right">t("common.solutions", "Solutions")</th>
                      </ThemeWrapper>
                    </thead>
                    <tbody>
                      {reputationLeaderboard.map((user, index) => (
                        <ThemeWrapper
                          as="tr"
                          key={user.id}
                          className="border-b"
                          darkClassName={`border-indigo-800/30 ${index < 3 ? 'bg-indigo-800/20' : ''}`}
                          lightClassName={`border-slate-200 ${index < 3 ? 'bg-indigo-100/50' : ''}`}
                        >
                          <td className="px-4 py-3">
                            {getPositionBadge(index)}
                          </td>
                          <td className="px-4 py-3 font-medium">{user.username}</td>
                          <ThemeWrapper
                            as="td"
                            className="px-4 py-3"
                            darkClassName={getLevelColor(user.level) === 'text-gray-400' ? 'text-gray-400' : ''}
                            lightClassName={getLevelColor(user.level) === 'text-gray-700' ? 'text-gray-700' : ''}
                          >
                            <span className={getLevelColor(user.level)}>{user.level}</span>
                          </ThemeWrapper>
                          <td className="px-4 py-3 text-right font-bold">{user.points}</td>
                          <td className="px-4 py-3 text-right">{user.threads_created}</td>
                          <td className="px-4 py-3 text-right">{user.posts_created}</td>
                          <td className="px-4 py-3 text-right">{user.solutions_provided}</td>
                        </ThemeWrapper>
                      ))}
                    </tbody>
                  </table>
                </div>
              </ThemeWrapper>
            </div>

            <div className="lg:col-span-1 space-y-6">
              {/* How to Earn Points */}
              <ThemeWrapper
                className="rounded-lg p-6 backdrop-blur-sm border"
                darkClassName="bg-indigo-900/30 border-indigo-800/50"
                lightClassName="bg-white border-slate-200"
              >
                <h3 className="text-lg font-semibold mb-4">t("common.how.to.earn", "How to Earn Points")</h3>
                <div className="space-y-4">
                  <div className={`flex items-start ${isRTL ? "flex-row-reverse" : ""}`}>
                    <ThemeWrapper
                      className={`flex items-center justify-center w-8 h-8 rounded-full mr-3 flex-shrink-0 ${isRTL ? "flex-row-reverse" : ""}`}
                      darkClassName="bg-blue-900/30"
                      lightClassName="bg-blue-100"
                    >
                      <ThemeWrapper
                        as="span"
                        darkClassName="text-blue-400"
                        lightClassName="text-blue-600"
                      >
                        <MessageSquare size={16} />
                      </ThemeWrapper>
                    </ThemeWrapper>
                    <div>
                      <div className="font-medium">t("common.create.a.thread", "Create a Thread")</div>
                      <ThemeWrapper
                        as="p"
                        className="text-sm"
                        darkClassName="text-gray-400"
                        lightClassName="text-gray-700"
                      >
                        +5 points
                      </ThemeWrapper>
                    </div>
                  </div>

                  <div className={`flex items-start ${isRTL ? "flex-row-reverse" : ""}`}>
                    <ThemeWrapper
                      className={`flex items-center justify-center w-8 h-8 rounded-full mr-3 flex-shrink-0 ${isRTL ? "flex-row-reverse" : ""}`}
                      darkClassName="bg-purple-900/30"
                      lightClassName="bg-purple-100"
                    >
                      <ThemeWrapper
                        as="span"
                        darkClassName="text-purple-400"
                        lightClassName="text-purple-600"
                      >
                        <MessageSquare size={16} />
                      </ThemeWrapper>
                    </ThemeWrapper>
                    <div>
                      <div className="font-medium">t("common.create.a.post", "Create a Post")</div>
                      <ThemeWrapper
                        as="p"
                        className="text-sm"
                        darkClassName="text-gray-400"
                        lightClassName="text-gray-700"
                      >
                        +2 points
                      </ThemeWrapper>
                    </div>
                  </div>

                  <div className={`flex items-start ${isRTL ? "flex-row-reverse" : ""}`}>
                    <ThemeWrapper
                      className={`flex items-center justify-center w-8 h-8 rounded-full mr-3 flex-shrink-0 ${isRTL ? "flex-row-reverse" : ""}`}
                      darkClassName="bg-green-900/30"
                      lightClassName="bg-green-100"
                    >
                      <ThemeWrapper
                        as="span"
                        darkClassName="text-green-400"
                        lightClassName="text-green-600"
                      >
                        <CheckCircle size={16} />
                      </ThemeWrapper>
                    </ThemeWrapper>
                    <div>
                      <div className="font-medium">t("common.provide.a.solution", "Provide a Solution")</div>
                      <ThemeWrapper
                        as="p"
                        className="text-sm"
                        darkClassName="text-gray-400"
                        lightClassName="text-gray-700"
                      >
                        +15 points
                      </ThemeWrapper>
                    </div>
                  </div>

                  <div className={`flex items-start ${isRTL ? "flex-row-reverse" : ""}`}>
                    <ThemeWrapper
                      className={`flex items-center justify-center w-8 h-8 rounded-full mr-3 flex-shrink-0 ${isRTL ? "flex-row-reverse" : ""}`}
                      darkClassName="bg-yellow-900/30"
                      lightClassName="bg-yellow-100"
                    >
                      <ThemeWrapper
                        as="span"
                        darkClassName="text-yellow-400"
                        lightClassName="text-yellow-600"
                      >
                        <ThumbsUp size={16} />
                      </ThemeWrapper>
                    </ThemeWrapper>
                    <div>
                      <div className="font-medium">t("common.receive.a.like", "Receive a Like")</div>
                      <ThemeWrapper
                        as="p"
                        className="text-sm"
                        darkClassName="text-gray-400"
                        lightClassName="text-gray-700"
                      >
                        +1 point per like
                      </ThemeWrapper>
                    </div>
                  </div>
                </div>
              </ThemeWrapper>

              {/* Reputation Levels */}
              <ThemeWrapper
                className="rounded-lg p-6 backdrop-blur-sm border"
                darkClassName="bg-indigo-900/30 border-indigo-800/50"
                lightClassName="bg-white border-slate-200"
              >
                <ThemeWrapper
                  as="h3"
                  className="text-lg font-semibold mb-4"
                  darkClassName="text-white"
                  lightClassName="text-gray-800"
                >
                  Reputation Levels
                </ThemeWrapper>
                <div className="space-y-3 text-sm">
                  <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    <ThemeWrapper
                      className={`w-3 h-3 rounded-full mr-2 ${isRTL ? "space-x-reverse" : ""}`}
                      darkClassName="bg-gradient-to-r from-gray-700 to-gray-500"
                      lightClassName="bg-gradient-to-r from-gray-500 to-gray-300"
                    ></ThemeWrapper>
                    <ThemeWrapper
                      as="span"
                      darkClassName="text-gray-300"
                      lightClassName="text-gray-700"
                    >
                      Newcomer (0-9 points)
                    </ThemeWrapper>
                  </div>
                  <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    <ThemeWrapper
                      className={`w-3 h-3 rounded-full mr-2 ${isRTL ? "space-x-reverse" : ""}`}
                      darkClassName="bg-gradient-to-r from-blue-700 to-blue-500"
                      lightClassName="bg-gradient-to-r from-blue-500 to-blue-300"
                    ></ThemeWrapper>
                    <ThemeWrapper
                      as="span"
                      darkClassName="text-gray-300"
                      lightClassName="text-gray-700"
                    >
                      Contributor (10-49 points)
                    </ThemeWrapper>
                  </div>
                  <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    <ThemeWrapper
                      className={`w-3 h-3 rounded-full mr-2 ${isRTL ? "space-x-reverse" : ""}`}
                      darkClassName="bg-gradient-to-r from-green-700 to-green-500"
                      lightClassName="bg-gradient-to-r from-green-500 to-green-300"
                    ></ThemeWrapper>
                    <ThemeWrapper
                      as="span"
                      darkClassName="text-gray-300"
                      lightClassName="text-gray-700"
                    >
                      Active Member (50-199 points)
                    </ThemeWrapper>
                  </div>
                  <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    <ThemeWrapper
                      className={`w-3 h-3 rounded-full mr-2 ${isRTL ? "space-x-reverse" : ""}`}
                      darkClassName="bg-gradient-to-r from-purple-700 to-purple-500"
                      lightClassName="bg-gradient-to-r from-purple-500 to-purple-300"
                    ></ThemeWrapper>
                    <ThemeWrapper
                      as="span"
                      darkClassName="text-gray-300"
                      lightClassName="text-gray-700"
                    >
                      Expert (200-499 points)
                    </ThemeWrapper>
                  </div>
                  <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    <ThemeWrapper
                      className={`w-3 h-3 rounded-full mr-2 ${isRTL ? "space-x-reverse" : ""}`}
                      darkClassName="bg-gradient-to-r from-yellow-700 to-yellow-500"
                      lightClassName="bg-gradient-to-r from-yellow-500 to-yellow-300"
                    ></ThemeWrapper>
                    <ThemeWrapper
                      as="span"
                      darkClassName="text-gray-300"
                      lightClassName="text-gray-700"
                    >
                      Mentor (500-999 points)
                    </ThemeWrapper>
                  </div>
                  <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    <ThemeWrapper
                      className={`w-3 h-3 rounded-full mr-2 ${isRTL ? "space-x-reverse" : ""}`}
                      darkClassName="bg-gradient-to-r from-red-700 to-red-500"
                      lightClassName="bg-gradient-to-r from-red-500 to-red-300"
                    ></ThemeWrapper>
                    <ThemeWrapper
                      as="span"
                      darkClassName="text-gray-300"
                      lightClassName="text-gray-700"
                    >
                      Community Leader (1000+ points)
                    </ThemeWrapper>
                  </div>
                </div>
              </ThemeWrapper>

              {/* View Your Reputation */}
              <ThemeWrapper
                className="rounded-lg p-6 backdrop-blur-sm border text-center"
                darkClassName="bg-indigo-900/30 border-indigo-800/50"
                lightClassName="bg-white border-slate-200"
              >
                <ThemeWrapper
                  as="span"
                  className="mx-auto mb-3 block"
                  darkClassName="text-purple-400"
                  lightClassName="text-purple-600"
                >
                  <Award size={24} />
                </ThemeWrapper>
                <h3 className="text-lg font-semibold mb-2">t("common.track.your.progress", "Track Your Progress")</h3>
                <ThemeWrapper
                  as="p"
                  className="mb-4"
                  darkClassName="text-gray-400"
                  lightClassName="text-gray-500"
                >
                  View your reputation details and activity history
                </ThemeWrapper>
                <ThemeWrapper
                  as={Link}
                  to="/forum/reputation"
                  className="px-4 py-2 bg-gradient-to-r rounded-lg font-medium transition-all duration-300 inline-block"
                  darkClassName="from-purple-600 to-blue-600 hover:shadow-glow"
                  lightClassName="from-purple-500 to-blue-500 hover:shadow-md"
                >
                  View Your Reputation
                </ThemeWrapper>
              </ThemeWrapper>
            </div>
          </div>
        ) : (
          <ThemeWrapper
            className="rounded-lg p-6 backdrop-blur-sm border text-center"
            darkClassName="bg-indigo-900/30 border-indigo-800/50"
            lightClassName="bg-white border-slate-200"
          >
            <ThemeWrapper
              as="p"
              darkClassName="text-gray-400"
              lightClassName="text-gray-500"
            >
              No leaderboard data available yet.
            </ThemeWrapper>
          </ThemeWrapper>
        )}
      </main>

      <Footer />
    </div>
  );
};

export default ForumLeaderboardPage;
