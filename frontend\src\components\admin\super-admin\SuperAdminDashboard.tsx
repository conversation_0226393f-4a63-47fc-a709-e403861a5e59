import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Shield, Server, Users, Database, Activity, Settings,
  AlertTriangle, CheckCircle, XCircle, TrendingUp,
  Cpu, HardDrive, Monitor, Network, Lock, Eye,
  BarChart3, Zap, Globe, Cog, RefreshCw
} from 'lucide-react';
// DashboardLayout removed - handled by routing system with AuthenticatedLayout
import SuperAdminDebug from '../../debug/SuperAdminDebug';
import AuthDebug from '../../debug/AuthDebug';
import SuperAdminApiTest from '../../debug/SuperAdminApiTest';
import { getAuthToken } from '../../../services/api';
import { superAdminApi, DashboardData, SystemHealth, Capability } from '../../../services/superAdminApi';

interface SystemHealth {
  cpu: {
    usage_percent: number;
    count: number;
    status: 'healthy' | 'warning' | 'critical';
  };
  memory: {
    total: number;
    available: number;
    percent: number;
    used: number;
    status: 'healthy' | 'warning' | 'critical';
  };
  disk: {
    total: number;
    used: number;
    free: number;
    percent: number;
    status: 'healthy' | 'warning' | 'critical';
  };
  overall_status: 'healthy' | 'warning' | 'critical';
  timestamp: string;
}

interface SuperAdminStats {
  system_health: {
    cpu_usage: number;
    memory_usage: number;
    disk_usage: number;
    status: 'healthy' | 'warning' | 'critical';
  };
  user_stats: {
    total_users: number;
    active_users: number;
    new_users_today: number;
    role_distribution: Array<{ role__name: string; count: number }>;
  };
  timestamp: string;
}

interface SuperAdminCapability {
  id: string;
  name: string;
  description: string;
  features: string[];
}

const SuperAdminDashboard: React.FC = () => {
  const { t } = useTranslation();
  const [stats, setStats] = useState<SuperAdminStats | null>(null);
  const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null);
  const [capabilities, setCapabilities] = useState<SuperAdminCapability[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [redirectMessage, setRedirectMessage] = useState<string | null>(null);

  useEffect(() => {
    fetchSuperAdminData();

    // Reduce refresh frequency to 5 minutes to avoid spam
    const interval = setInterval(fetchSuperAdminData, 5 * 60 * 1000); // Refresh every 5 minutes

    // Check for redirect messages
    const aiBlockMessage = sessionStorage.getItem('superAdminAIBlockMessage');
    if (aiBlockMessage) {
      setRedirectMessage(aiBlockMessage);
      sessionStorage.removeItem('superAdminAIBlockMessage');

      // Clear message after 10 seconds
      setTimeout(() => {
        setRedirectMessage(null);
      }, 10000);
    }

    return () => clearInterval(interval);
  }, []);

  const fetchSuperAdminData = async () => {
    try {
      setLoading(true);

      const token = getAuthToken();
      if (!token) {
        setError('No authentication token found. Please login again.');
        setLoading(false);
        return;
      }

      console.log('🔄 Fetching Super Admin data using new API service...');

      // Test API connectivity
      const connectivityTest = await superAdminApi.testConnection();
      const failedTests = connectivityTest.filter(test => !test.success);

      if (failedTests.length > 0) {
        console.warn('⚠️ Some API tests failed:', failedTests);
      }

      // Fetch dashboard data
      const dashboardResult = await superAdminApi.getDashboardData();
      if (dashboardResult.success && dashboardResult.data) {
        const data = dashboardResult.data as DashboardData;
        setStats(data);
        console.log('✅ Dashboard data loaded:', data);
      } else {
        console.error('❌ Failed to load dashboard data:', dashboardResult.error);
      }

      // Fetch system health
      const healthResult = await superAdminApi.getSystemHealth();
      if (healthResult.success && healthResult.data) {
        const health = healthResult.data as SystemHealth;
        setSystemHealth(health);
        console.log('✅ System health loaded:', health);
      } else {
        console.error('❌ Failed to load system health:', healthResult.error);
      }

      // Fetch capabilities
      const capabilitiesResult = await superAdminApi.getCapabilities();
      if (capabilitiesResult.success && capabilitiesResult.data) {
        const caps = capabilitiesResult.data as { capabilities: Capability[] };
        setCapabilities(caps.capabilities || []);
        console.log('✅ Capabilities loaded:', caps.capabilities?.length || 0, 'items');
      } else {
        console.error('❌ Failed to load capabilities:', capabilitiesResult.error);
      }

      // Set error if critical APIs failed
      const criticalFailures = failedTests.filter(test =>
        ['Dashboard', 'System Health'].includes(test.name)
      );

      if (criticalFailures.length > 0) {
        const errorMsg = `Critical API failures: ${criticalFailures.map(t => t.name).join(', ')}`;
        setError(errorMsg);
        console.error('💥 Critical API failures detected:', criticalFailures);
      }

      console.log('🎉 Super Admin data fetch completed!');
    } catch (err) {
      setError('Failed to fetch Super Admin data');
      console.error('Super Admin data fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-400';
      case 'warning': return 'text-yellow-400';
      case 'critical': return 'text-red-400';
      case 'unknown': return 'text-gray-400';
      default: return 'text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="w-5 h-5" />;
      case 'warning': return <AlertTriangle className="w-5 h-5" />;
      case 'critical': return <XCircle className="w-5 h-5" />;
      case 'unknown': return <Activity className="w-5 h-5" />;
      default: return <Activity className="w-5 h-5" />;
    }
  };

  const formatBytes = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  if (loading && !stats) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-900">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-red-900 to-purple-900">
      <div className="px-4 sm:px-6 lg:px-8 py-6">
        <div className="max-w-7xl mx-auto text-white">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-2">
            <Shield className="w-8 h-8 text-red-400" />
            <h1 className="text-3xl font-bold text-white">
              {t('superAdmin.dashboard.title', 'Super Admin Dashboard')}
            </h1>
          </div>
          <p className="text-gray-400">
            {t('superAdmin.dashboard.subtitle', 'Complete system control and monitoring')}
          </p>
        </div>

        {/* AI Block Redirect Message */}
        {redirectMessage && (
          <div className="mb-6 bg-yellow-900/20 border border-yellow-500 rounded-lg p-4">
            <div className="flex items-center gap-3">
              <AlertTriangle className="w-5 h-5 text-yellow-400" />
              <div>
                <h3 className="text-yellow-300 font-semibold">
                  {t('superAdmin.aiBlock.title', 'AI Access Restricted')}
                </h3>
                <p className="text-yellow-200 text-sm mt-1">
                  {redirectMessage}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Debug Components - Remove in production */}
        <AuthDebug />
        <SuperAdminDebug />
        <SuperAdminApiTest />

        {/* API Test Button */}
        <div className="mb-6 flex gap-4">
          <button
            onClick={() => {
              fetchSuperAdminData();
            }}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
          >
            <RefreshCw className="w-4 h-4" />
            Refresh Data
          </button>
          <button
            onClick={async () => {
              const results = await superAdminApi.testConnection();
              alert(`API Test Results:\n${results.map(r => `${r.name}: ${r.success ? '✅' : '❌'}`).join('\n')}`);
            }}
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
          >
            <CheckCircle className="w-4 h-4" />
            Test APIs
          </button>
        </div>

      {error && (
        <div className="bg-red-900/20 border border-red-500 rounded-lg p-4 mb-6">
          <div className="flex items-center gap-2">
            <XCircle className="w-5 h-5 text-red-400" />
            <span className="text-red-300">{error}</span>
          </div>
        </div>
      )}

      {/* System Health Overview */}
      {systemHealth && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <Cpu className="w-5 h-5 text-blue-400" />
                <span className="font-medium">CPU Usage</span>
              </div>
              <div className={`flex items-center gap-1 ${getStatusColor(systemHealth.cpu?.status || 'unknown')}`}>
                {getStatusIcon(systemHealth.cpu?.status || 'unknown')}
              </div>
            </div>
            <div className="text-2xl font-bold mb-1">
              {systemHealth.cpu?.usage_percent?.toFixed(1) || '0.0'}%
            </div>
            <div className="text-sm text-gray-400">
              {systemHealth.cpu?.count || 'N/A'} cores
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <Monitor className="w-5 h-5 text-green-400" />
                <span className="font-medium">Memory</span>
              </div>
              <div className={`flex items-center gap-1 ${getStatusColor(systemHealth.memory?.status || 'unknown')}`}>
                {getStatusIcon(systemHealth.memory?.status || 'unknown')}
              </div>
            </div>
            <div className="text-2xl font-bold mb-1">
              {systemHealth.memory?.percent?.toFixed(1) || '0.0'}%
            </div>
            <div className="text-sm text-gray-400">
              {formatBytes(systemHealth.memory?.used || 0)} / {formatBytes(systemHealth.memory?.total || 0)}
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <HardDrive className="w-5 h-5 text-purple-400" />
                <span className="font-medium">Disk Usage</span>
              </div>
              <div className={`flex items-center gap-1 ${getStatusColor(systemHealth.disk?.status || 'unknown')}`}>
                {getStatusIcon(systemHealth.disk?.status || 'unknown')}
              </div>
            </div>
            <div className="text-2xl font-bold mb-1">
              {systemHealth.disk?.percent?.toFixed(1) || '0.0'}%
            </div>
            <div className="text-sm text-gray-400">
              {formatBytes(systemHealth.disk?.used || 0)} / {formatBytes(systemHealth.disk?.total || 0)}
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <Activity className="w-5 h-5 text-yellow-400" />
                <span className="font-medium">System Status</span>
              </div>
              <div className={`flex items-center gap-1 ${getStatusColor(systemHealth.overall_status || 'unknown')}`}>
                {getStatusIcon(systemHealth.overall_status || 'unknown')}
              </div>
            </div>
            <div className="text-2xl font-bold mb-1 capitalize">
              {systemHealth.overall_status || 'Unknown'}
            </div>
            <div className="text-sm text-gray-400">
              Last updated: {systemHealth.timestamp ? new Date(systemHealth.timestamp).toLocaleTimeString() : 'Never'}
            </div>
          </div>
        </div>
      )}

      {/* User Statistics */}
      {stats && stats.user_stats && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <div className="flex items-center gap-2 mb-4">
              <Users className="w-5 h-5 text-blue-400" />
              <span className="font-medium">Total Users</span>
            </div>
            <div className="text-3xl font-bold text-blue-400">
              {stats.user_stats.total_users?.toLocaleString() || '0'}
            </div>
            <div className="text-sm text-gray-400 mt-1">
              {stats.user_stats.active_users || 0} active
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <div className="flex items-center gap-2 mb-4">
              <TrendingUp className="w-5 h-5 text-green-400" />
              <span className="font-medium">New Users Today</span>
            </div>
            <div className="text-3xl font-bold text-green-400">
              {stats.user_stats.new_users_today || 0}
            </div>
            <div className="text-sm text-gray-400 mt-1">
              Registered today
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <div className="flex items-center gap-2 mb-4">
              <BarChart3 className="w-5 h-5 text-purple-400" />
              <span className="font-medium">Role Distribution</span>
            </div>
            <div className="space-y-2">
              {(stats.user_stats.role_distribution || []).map((role, index) => (
                <div key={index} className="flex justify-between text-sm">
                  <span className="capitalize">{role.role__name || 'Unknown'}</span>
                  <span className="text-purple-400">{role.count || 0}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Super Admin Capabilities */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold mb-6 flex items-center gap-2">
          <Zap className="w-6 h-6 text-yellow-400" />
          Super Admin Capabilities
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {(capabilities || []).map((capability) => (
            <div key={capability.id} className="bg-gray-800 rounded-lg p-6 border border-gray-700 hover:border-gray-600 transition-colors">
              <h3 className="text-lg font-semibold mb-2 text-white">
                {capability.name || 'Unknown Capability'}
              </h3>
              <p className="text-gray-400 text-sm mb-4">
                {capability.description || 'No description available'}
              </p>
              <div className="space-y-1">
                {(capability.features || []).slice(0, 3).map((feature, index) => (
                  <div key={index} className="flex items-center gap-2 text-sm">
                    <CheckCircle className="w-3 h-3 text-green-400" />
                    <span className="text-gray-300">{feature}</span>
                  </div>
                ))}
                {(capability.features || []).length > 3 && (
                  <div className="text-xs text-gray-500 mt-2">
                    +{(capability.features || []).length - 3} more features
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <button className="bg-blue-600 hover:bg-blue-700 rounded-lg p-4 text-center transition-colors">
          <Server className="w-6 h-6 mx-auto mb-2" />
          <span className="text-sm font-medium">System Config</span>
        </button>
        <button className="bg-green-600 hover:bg-green-700 rounded-lg p-4 text-center transition-colors">
          <Database className="w-6 h-6 mx-auto mb-2" />
          <span className="text-sm font-medium">Database</span>
        </button>
        <button className="bg-purple-600 hover:bg-purple-700 rounded-lg p-4 text-center transition-colors">
          <Lock className="w-6 h-6 mx-auto mb-2" />
          <span className="text-sm font-medium">Security</span>
        </button>
        <button className="bg-yellow-600 hover:bg-yellow-700 rounded-lg p-4 text-center transition-colors">
          <Eye className="w-6 h-6 mx-auto mb-2" />
          <span className="text-sm font-medium">Audit Logs</span>
        </button>
        </div>
        </div>
      </div>
    </div>
  );
};

export default SuperAdminDashboard;
