/**
 * Predictive Analytics Dashboard
 * Advanced AI-powered business predictions and forecasting
 */

import React, { useState, useEffect } from 'react';
import {
  TrendingUp,
  Target,
  AlertTriangle,
  DollarSign,
  BarChart3,
  Brain,
  Zap,
  Star,
  ArrowUp,
  ArrowDown,
  Activity,
  Shield,
  Lightbulb,
  RefreshCw
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { apiRequest } from '../../services/api';


interface PredictiveAnalyticsProps {
  businessData?: any;
  onAnalysisComplete?: (results: any) => void;
  className?: string;
}

interface AnalysisResult {
  success_probability: number;
  confidence_score: number;
  risk_level: string;
  key_factors: string[];
  recommendations: string[];
  market_fit_score: number;
  financial_viability: number;
  competitive_advantage: number;
}

interface MarketForecast {
  industry: string;
  overall_trend: string;
  average_growth_rate: number;
  volatility_score: number;
  forecast_data: Array<{
    date: string;
    growth_rate: number;
    confidence: number;
    market_size_change: number;
  }>;
  key_insights: string[];
  opportunities: string[];
  risks: string[];
}

interface RiskAssessment {
  overall_risk_score: number;
  risk_level: string;
  risk_categories: Record<string, {
    score: number;
    level: string;
    factors: string[];
  }>;
  top_risks: Array<[string, any]>;
  mitigation_strategies: string[];
}

interface InvestmentScore {
  investment_readiness_score: number;
  readiness_level: string;
  criteria_scores: Record<string, number>;
  strengths: string[];
  improvement_areas: string[];
  suggested_investor_types: string[];
  funding_recommendations: string[];
}

export const PredictiveAnalyticsDashboard: React.FC<PredictiveAnalyticsProps> = ({
  businessData,
  onAnalysisComplete,
  className = ''
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<'success' | 'market' | 'risk' | 'investment'>('success');
  const [isLoading, setIsLoading] = useState(false);
  const [results, setResults] = useState<{
    success?: AnalysisResult;
    market?: MarketForecast;
    risk?: RiskAssessment;
    investment?: InvestmentScore;
  }>({});

  const tabs = [
    { id: 'success', label: 'Success Prediction', icon: Target, color: 'text-green-600' },
    { id: 'market', label: 'Market Forecast', icon: TrendingUp, color: 'text-blue-600' },
    { id: 'risk', label: 'Risk Assessment', icon: AlertTriangle, color: 'text-orange-600' },
    { id: 'investment', label: 'Investment Score', icon: DollarSign, color: 'text-purple-600' }
  ];

  const runPredictiveAnalysis = async (analysisType: string) => {
    setIsLoading(true);
    try {
      const data = await apiRequest('/ai/predictive-analytics/', 'POST', {
        analysis_type: analysisType,
        business_data: businessData || {},
        industry: 'technology',
        timeframe_days: 90
      });
      
      setResults(prev => ({
        ...prev,
        [analysisType === 'success_prediction' ? 'success' : 
         analysisType === 'market_forecast' ? 'market' :
         analysisType === 'risk_assessment' ? 'risk' : 'investment']: data.result
      }));

      onAnalysisComplete?.(data.result);
    } catch (error) {
      console.error('Predictive analysis error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 0.8) return 'text-green-600';
    if (score >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreIcon = (score: number) => {
    if (score >= 0.8) return ArrowUp;
    if (score >= 0.6) return Activity;
    return ArrowDown;
  };

  const renderSuccessAnalysis = () => {
    const data = results.success;
    if (!data) return null;

    const ScoreIcon = getScoreIcon(data.success_probability);

    return (
      <div className="space-y-6">
        {/* Success Score Card */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Business Success Prediction
            </h3>
            <ScoreIcon className={`w-6 h-6 ${getScoreColor(data.success_probability)}`} />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className={`text-3xl font-bold ${getScoreColor(data.success_probability)}`}>
                {(data.success_probability * 100).toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Success Probability</div>
            </div>
            
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600">
                {(data.confidence_score * 100).toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Confidence</div>
            </div>
            
            <div className="text-center">
              <div className={`text-xl font-semibold ${
                data.risk_level === 'Low' ? 'text-green-600' :
                data.risk_level === 'Medium' ? 'text-yellow-600' : 'text-red-600'
              }`}>
                {data.risk_level}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Risk Level</div>
            </div>
          </div>
        </div>

        {/* Detailed Scores */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Market Fit</span>
              <span className={`font-semibold ${getScoreColor(data.market_fit_score)}`}>
                {(data.market_fit_score * 100).toFixed(0)}%
              </span>
            </div>
          </div>
          
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Financial Viability</span>
              <span className={`font-semibold ${getScoreColor(data.financial_viability)}`}>
                {(data.financial_viability * 100).toFixed(0)}%
              </span>
            </div>
          </div>
          
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Competitive Edge</span>
              <span className={`font-semibold ${getScoreColor(data.competitive_advantage)}`}>
                {(data.competitive_advantage * 100).toFixed(0)}%
              </span>
            </div>
          </div>
        </div>

        {/* Key Factors and Recommendations */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
            <h4 className="font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
              <Star className="w-5 h-5 mr-2 text-yellow-500" />
              Key Success Factors
            </h4>
            <ul className="space-y-2">
              {data.key_factors.map((factor, index) => (
                <li key={index} className="text-sm text-gray-600 dark:text-gray-400 flex items-start">
                  <span className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  {factor}
                </li>
              ))}
            </ul>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
            <h4 className="font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
              <Lightbulb className="w-5 h-5 mr-2 text-blue-500" />
              Recommendations
            </h4>
            <ul className="space-y-2">
              {data.recommendations.map((rec, index) => (
                <li key={index} className="text-sm text-gray-600 dark:text-gray-400 flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  {rec}
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    );
  };

  return (
    <ThemeWrapper>
      <div className={`predictive-analytics-dashboard ${className}`}>
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Brain className="w-8 h-8 text-purple-600" />
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                  Predictive Analytics
                </h2>
                <p className="text-gray-600 dark:text-gray-400">
                  AI-powered business intelligence and forecasting
                </p>
              </div>
            </div>
            
            <button
              onClick={() => runPredictiveAnalysis(
                activeTab === 'success' ? 'success_prediction' :
                activeTab === 'market' ? 'market_forecast' :
                activeTab === 'risk' ? 'risk_assessment' : 'investment_readiness'
              )}
              disabled={isLoading}
              className="flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50"
            >
              <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              <span>{isLoading ? 'Analyzing...' : 'Run Analysis'}</span>
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 mb-6 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex-1 flex items-center justify-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                  activeTab === tab.id
                    ? 'bg-white dark:bg-gray-700 shadow-sm'
                    : 'hover:bg-gray-200 dark:hover:bg-gray-700'
                }`}
              >
                <Icon className={`w-4 h-4 ${tab.color}`} />
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {tab.label}
                </span>
              </button>
            );
          })}
        </div>

        {/* Content */}
        <div className="min-h-96">
          {activeTab === 'success' && renderSuccessAnalysis()}
          {/* Add other tab content here */}
        </div>
      </div>
    </ThemeWrapper>
  );
};
