/**
 * Template Navigation Page
 * Central navigation hub for all template-related pages
 */

import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  FileText,
  Plus,
  BarChart3,
  Users,
  Brain,
  BookOpen,
  GitCompare,
  Settings,
  Sparkles,
  Library,
  ArrowRight
} from 'lucide-react';
// MainLayout removed - handled by routing system
import { RTLText, RTLFlex } from '../../components/common';
import { useLanguage } from '../../hooks/useLanguage';

interface TemplateRoute {
  path: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  color: string;
  roles: string[];
}

const TemplateNavigationPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();

  const templateRoutes: TemplateRoute[] = [
    {
      path: '/dashboard/templates',
      title: 'Templates Overview',
      description: 'Browse and explore all available business plan templates',
      icon: FileText,
      color: 'blue',
      roles: ['user', 'admin']
    },
    {
      path: '/dashboard/templates/create',
      title: 'Create Template',
      description: 'Create custom business plan templates',
      icon: Plus,
      color: 'green',
      roles: ['admin']
    },
    {
      path: '/dashboard/templates/library',
      title: 'Template Library',
      description: 'Access the complete template library',
      icon: Library,
      color: 'purple',
      roles: ['user', 'admin']
    },
    {
      path: '/dashboard/templates/analytics',
      title: 'Template Analytics',
      description: 'View detailed analytics and performance metrics',
      icon: BarChart3,
      color: 'orange',
      roles: ['admin']
    },
    {
      path: '/dashboard/templates/collaborate',
      title: 'Collaborative Templates',
      description: 'Work together on template creation and editing',
      icon: Users,
      color: 'cyan',
      roles: ['user', 'admin']
    },
    {
      path: '/dashboard/templates/ai-generator',
      title: 'AI Template Generator',
      description: 'Generate templates using artificial intelligence',
      icon: Brain,
      color: 'pink',
      roles: ['admin']
    },
    {
      path: '/dashboard/templates/compare',
      title: 'Template Comparison',
      description: 'Compare different templates side by side',
      icon: GitCompare,
      color: 'indigo',
      roles: ['user', 'admin']
    },
    {
      path: '/dashboard/templates/management',
      title: 'Template Management',
      description: 'Advanced template management dashboard',
      icon: Settings,
      color: 'gray',
      roles: ['admin']
    },
    {
      path: '/dashboard/templates/manage',
      title: 'Template Manager',
      description: 'Manage your custom templates',
      icon: Sparkles,
      color: 'yellow',
      roles: ['user', 'admin']
    }
  ];

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: 'bg-blue-600/20 text-blue-400 border-blue-500/30 hover:border-blue-400/50',
      green: 'bg-green-600/20 text-green-400 border-green-500/30 hover:border-green-400/50',
      purple: 'bg-purple-600/20 text-purple-400 border-purple-500/30 hover:border-purple-400/50',
      orange: 'bg-orange-600/20 text-orange-400 border-orange-500/30 hover:border-orange-400/50',
      cyan: 'bg-cyan-600/20 text-cyan-400 border-cyan-500/30 hover:border-cyan-400/50',
      pink: 'bg-pink-600/20 text-pink-400 border-pink-500/30 hover:border-pink-400/50',
      indigo: 'bg-indigo-600/20 text-indigo-400 border-indigo-500/30 hover:border-indigo-400/50',
      gray: 'bg-gray-600/20 text-gray-400 border-gray-500/30 hover:border-gray-400/50',
      yellow: 'bg-yellow-600/20 text-yellow-400 border-yellow-500/30 hover:border-yellow-400/50'
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  const handleNavigate = (path: string) => {
    navigate(path);
  };

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">
            {t('templates.navigation.title', 'Template Center')}
          </h1>
          <p className="text-gray-400">
            {t('templates.navigation.subtitle', 'Access all template-related features and tools')}
          </p>
        </div>

        {/* Template Routes Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {templateRoutes.map((route) => {
            const Icon = route.icon;
            const colorClasses = getColorClasses(route.color);

            return (
              <div
                key={route.path}
                onClick={() => handleNavigate(route.path)}
                className={`group p-6 bg-gray-800/50 rounded-lg border-2 border-transparent cursor-pointer transition-all duration-300 hover:bg-gray-700/50 ${colorClasses}`}
              >
                <div className={`flex items-start justify-between mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className={`p-3 rounded-lg ${colorClasses.split(' ')[0]} ${colorClasses.split(' ')[1]}`}>
                    <Icon size={24} />
                  </div>
                  <ArrowRight 
                    size={20} 
                    className={`text-gray-400 group-hover:text-white transition-colors ${isRTL ? 'rotate-180' : ''}`} 
                  />
                </div>

                <div>
                  <RTLText as="h3" className="text-lg font-semibold text-white mb-2">
                    {route.title}
                  </RTLText>
                  <RTLText className="text-sm text-gray-300 mb-4">
                    {route.description}
                  </RTLText>

                  {/* Role badges */}
                  <div className={`flex gap-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                    {route.roles.map((role) => (
                      <span
                        key={role}
                        className={`px-2 py-1 text-xs rounded-full ${
                          role === 'admin' 
                            ? 'bg-red-600/20 text-red-400' 
                            : 'bg-blue-600/20 text-blue-400'
                        }`}
                      >
                        {role}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Quick Actions */}
        <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
          <h2 className="text-xl font-semibold text-white mb-4">
            {t('templates.quickActions', 'Quick Actions')}
          </h2>
          
          <div className={`grid grid-cols-1 md:grid-cols-3 gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            <button
              onClick={() => navigate('/dashboard/templates')}
              className={`p-4 bg-blue-600 hover:bg-blue-700 rounded-lg text-white transition-colors flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
            >
              <BookOpen size={20} className={isRTL ? 'ml-3' : 'mr-3'} />
              {t('templates.browseAll', 'Browse All Templates')}
            </button>

            <button
              onClick={() => navigate('/dashboard/templates/create')}
              className={`p-4 bg-green-600 hover:bg-green-700 rounded-lg text-white transition-colors flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
            >
              <Plus size={20} className={isRTL ? 'ml-3' : 'mr-3'} />
              {t('templates.createNew', 'Create New Template')}
            </button>

            <button
              onClick={() => navigate('/dashboard/templates/analytics')}
              className={`p-4 bg-purple-600 hover:bg-purple-700 rounded-lg text-white transition-colors flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
            >
              <BarChart3 size={20} className={isRTL ? 'ml-3' : 'mr-3'} />
              {t('templates.viewAnalytics', 'View Analytics')}
            </button>
          </div>
        </div>

        {/* Status Information */}
        <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
          <h2 className="text-xl font-semibold text-white mb-4">
            {t('templates.systemStatus', 'System Status')}
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-400">50+</div>
              <div className="text-sm text-gray-400">{t('templates.totalTemplates', 'Total Templates')}</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">12</div>
              <div className="text-sm text-gray-400">{t('templates.categories', 'Categories')}</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-400">8</div>
              <div className="text-sm text-gray-400">{t('templates.customTemplates', 'Custom Templates')}</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-400">95%</div>
              <div className="text-sm text-gray-400">{t('templates.systemHealth', 'System Health')}</div>
            </div>
          </div>
        </div>
      </div>
              </div>
        </div>
      </div>
    </div>
  );
};

export default TemplateNavigationPage;
