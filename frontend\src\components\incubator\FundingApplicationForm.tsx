import React, { useState, useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '../../store/hooks';
import { createFundingApplication } from '../../store/incubatorSlice';
import { BusinessIdea, FundingOpportunity } from '../../services/incubatorApi';
import { businessIdeasAPI } from '../../services/incubatorApi';

import { useTranslation } from 'react-i18next';
interface FundingApplicationFormProps {
  fundingOpportunity: FundingOpportunity;
  onSubmitSuccess: () => void;
  onCancel: () => void;
}

const FundingApplicationForm: React.FC<FundingApplicationFormProps> = ({ fundingOpportunity,
  onSubmitSuccess,
  onCancel
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector(state => state.auth);
  const { isLoading, error } = useAppSelector(state => state.incubator);

  const [userBusinessIdeas, setUserBusinessIdeas] = useState<BusinessIdea[]>([]);
  const [formData, setFormData] = useState({
    business_idea: '',
    pitch: '',
    requested_amount: fundingOpportunity.amount.toString(),
    use_of_funds: '',
  });

  const [formError, setFormError] = useState<string | null>(null);
  const [formSuccess, setFormSuccess] = useState<string | null>(null);

  // Fetch user's business ideas
  useEffect(() => {
    const fetchUserBusinessIdeas = async () => {
      try {
        const ideas = await businessIdeasAPI.getBusinessIdeas();
        // Filter for approved ideas owned by the user
        const userIdeas = ideas.filter(idea =>
          idea.owner.id === user?.id &&
          idea.moderation_status === 'approved'
        );
        setUserBusinessIdeas(userIdeas);

        // Set default business idea if user has any
        if (userIdeas.length > 0) {
          setFormData(prev => ({
            ...prev,
            business_idea: userIdeas[0].id.toString()
          }));
        }
      } catch (error) {
        console.error('Error fetching user business ideas:', error);
        setFormError(t("common.failed.to.load", "Failed to load your business ideas"));
      }
    };

    if (user) {
      fetchUserBusinessIdeas();
    }
  }, [user]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);

    // Validate form
    if (!formData.business_idea) {
      setFormError(t("common.please.select.a", "Please select a business idea"));
      return;
    }

    if (!formData.pitch.trim()) {
      setFormError(t("common.pitch.is.required", "Pitch is required"));
      return;
    }

    if (!formData.requested_amount.trim() || isNaN(Number(formData.requested_amount)) || Number(formData.requested_amount) <= 0) {
      setFormError(t("common.please.enter.a", "Please enter a valid amount"));
      return;
    }

    if (!formData.use_of_funds.trim()) {
      setFormError(t("common.please.explain.how", "Please explain how you will use the funds"));
      return;
    }

    if (!user) {
      setFormError('You must be logged in to apply for funding');
      return;
    }

    try {
      // Dispatch action to create funding application
      await dispatch(createFundingApplication({
        business_idea: parseInt(formData.business_idea),
        funding_opportunity: fundingOpportunity.id,
        applicant_id: user.id,
        pitch: formData.pitch,
        requested_amount: parseFloat(formData.requested_amount),
        use_of_funds: formData.use_of_funds
      })).unwrap();

      setFormSuccess(t("common.funding.application.submitted", "Funding application submitted successfully!"));

      // Reset form
      setFormData({
        business_idea: userBusinessIdeas.length > 0 ? userBusinessIdeas[0].id.toString() : '',
        pitch: '',
        requested_amount: fundingOpportunity.amount.toString(),
        use_of_funds: '',
      });

      // Close modal after success
      setTimeout(() => {
        onSubmitSuccess();
      }, 1500);
    } catch (err) {
      setFormError(err instanceof Error ? err.message : t("common.failed.to.submit", "Failed to submit funding application"));
    }
  };

  // Format currency
  const formatCurrency = (amount: string) => {
    if (!amount || isNaN(Number(amount))) return '';

    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(Number(amount));
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {formError && (
        <div className="bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded-md">
          {formError}
        </div>
      )}

      {formSuccess && (
        <div className="bg-green-900/50 border border-green-500 text-green-200 px-4 py-3 rounded-md">
          {formSuccess}
        </div>
      )}

      <div>
        <label htmlFor="business_idea" className="block text-sm font-medium mb-1">
          Select Business Idea <span className="text-red-400">*</span>
        </label>
        {userBusinessIdeas.length > 0 ? (
          <select
            id="business_idea"
            name="business_idea"
            value={formData.business_idea}
            onChange={handleInputChange}
            className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
            required
          >
            {userBusinessIdeas.map(idea => (
              <option key={idea.id} value={idea.id}>
                {idea.title}
              </option>
            ))}
          </select>
        ) : (
          <div className="text-yellow-300 text-sm mb-2">
            You don't have any approved business ideas. Please submit a business idea first and wait for approval.
          </div>
        )}
      </div>

      <div>
        <label htmlFor="pitch" className="block text-sm font-medium mb-1">
          Your Pitch <span className="text-red-400">*</span>
        </label>
        <textarea
          id="pitch"
          name="pitch"
          value={formData.pitch}
          onChange={handleInputChange}
          rows={4}
          className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
          placeholder={t("common.explain.why.your", "Explain why your business idea deserves this funding")}
          required
        />
      </div>

      <div>
        <label htmlFor="requested_amount" className="block text-sm font-medium mb-1">
          Requested Amount <span className="text-red-400">*</span>
        </label>
        <div className="relative">
          <span className="absolute left-3 top-2.5 text-gray-400">$</span>
          <input
            type="number"
            id="requested_amount"
            name="requested_amount"
            value={formData.requested_amount}
            onChange={handleInputChange}
            className="w-full px-3 py-2 pl-8 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
            placeholder={t("common.enter.amount", "Enter amount")}
            min="1"
            max={fundingOpportunity.amount}
            required
          />
        </div>
        <div className="text-sm text-gray-400 mt-1">
          Maximum available: {formatCurrency(fundingOpportunity.amount.toString())}
        </div>
      </div>

      <div>
        <label htmlFor="use_of_funds" className="block text-sm font-medium mb-1">
          Use of Funds <span className="text-red-400">*</span>
        </label>
        <textarea
          id="use_of_funds"
          name="use_of_funds"
          value={formData.use_of_funds}
          onChange={handleInputChange}
          rows={3}
          className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
          placeholder={t("common.explain.how.you", "Explain how you will use the funds")}
          required
        />
      </div>

      <div className={`flex justify-end space-x-4 pt-4 ${isRTL ? "flex-row-reverse" : ""}`}>
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-600 rounded-lg hover:bg-gray-800 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isLoading || userBusinessIdeas.length === 0}
          className={`px-4 py-2 bg-purple-600 rounded-lg hover:bg-purple-700 transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed ${isRTL ? "flex-row-reverse" : ""}`}
        >
          {isLoading ? (
            <>
              <span className={`w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 ${isRTL ? "space-x-reverse" : ""}`}></span>
              Submitting...
            </>
          ) : (
            t("common.submit.application", "Submit Application")
          )}
        </button>
      </div>
    </form>
  );
};

export default FundingApplicationForm;
