import React, { ComponentType, useEffect } from 'react';
import { trackComponentLoad } from './analytics';

import { useTranslation } from 'react-i18next';
/**
 * Higher-order component that adds performance tracking to a component
 * 
 * @param Component The component to wrap with performance tracking
 * @param componentName Optional name for the component (defaults to Component.displayName or Component.name)
 * @returns The wrapped component with performance tracking
 */
export function withPerformanceTracking<P extends object>(
  Component: ComponentType<P>,
  componentName?: string
): React.FC<P> {
  const displayName = componentName || Component.displayName || Component.name || t("common.unknowncomponent", "UnknownComponent");
  
  const WrappedComponent: React.FC<P> = (props) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

    const startTime = performance.now();
    
    useEffect(() => {
      // Track component mount time
      const mountTime = performance.now() - startTime;
      trackComponentLoad(displayName, mountTime, { event: 'mount' });
      
      return () => {
        // Track component lifetime on unmount
        const lifetime = performance.now() - startTime;
        trackComponentLoad(displayName, lifetime, { event: 'unmount' });
      };
    }, []);
    
    return <Component {...props} />;
  };
  
  WrappedComponent.displayName = `WithPerformanceTracking(${displayName})`;
  
  return WrappedComponent;
}

export default withPerformanceTracking;
