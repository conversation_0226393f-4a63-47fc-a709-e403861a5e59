import React from 'react';
import { LucideIcon } from 'lucide-react';

import { useTranslation } from 'react-i18next';
interface StatsCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  subtitle?: string;
  change?: number;
  changeLabel?: string;
}

const StatsCard: React.FC<StatsCardProps> = ({ title,
  value,
  icon,
  subtitle,
  change,
  changeLabel
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  return (
    <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
      <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
        <h3 className="text-lg font-semibold">{title}</h3>
        {icon}
      </div>
      <p className="text-3xl font-bold mt-2">{value}</p>
      {(subtitle || change !== undefined) && (
        <p className="text-sm text-gray-400 mt-1">
          {change !== undefined && (
            <span className={change > 0 ? "text-green-400" : change < 0 ? "text-red-400" : "text-gray-400"}>
              {change > 0 ? "+" : ""}
              {typeof change === 'number' ? change.toFixed(1) : change}%
            </span>
          )}
          {change !== undefined && changeLabel && ` ${changeLabel}`}
          {!change && subtitle}
        </p>
      )}
    </div>
  );
};

export default StatsCard;
