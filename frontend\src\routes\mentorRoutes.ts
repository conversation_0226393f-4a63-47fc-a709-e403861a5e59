import { lazy } from 'react';
import { RouteConfig, createRoleRoute } from './routeConfig';

// DEDICATED MENTOR DASHBOARD - Mentor users only
const MentorDashboardPage = lazy(() => import('../pages/mentor/MentorDashboardPage'));
// Legacy MentorDashboard kept for backward compatibility
const MentorDashboard = lazy(() => import('../components/dashboard/mentor-dashboard/MentorDashboard'));
const MentorshipSessionsPage = lazy(() => import('../pages/dashboard/MentorshipSessionsPage'));
const MenteesManagementPage = lazy(() => import('../pages/dashboard/MenteesManagementPage'));
const MentorProfilePage = lazy(() => import('../pages/dashboard/MentorProfilePage'));
const MentorAnalyticsPage = lazy(() => import('../pages/dashboard/MentorAnalyticsPage'));
const MentorAvailabilityPage = lazy(() => import('../pages/dashboard/MentorAvailabilityPage'));

// Shared components that mentors can access
const BusinessIdeasPage = lazy(() => import('../pages/dashboard/BusinessIdeasPage'));
const BusinessPlanPage = lazy(() => import('../pages/dashboard/BusinessPlanPage'));
const PostsPage = lazy(() => import('../pages/dashboard/PostsPage'));
const EventsPage = lazy(() => import('../pages/dashboard/EventsPage'));
const ConsolidatedAIPage = lazy(() => import('../pages/ConsolidatedAIPage'));
const UserSettings = lazy(() => import('../pages/UserSettings'));

/**
 * Mentor-specific routes for mentorship and guidance
 */
export const mentorRoutes: RouteConfig[] = [
  // Main mentor dashboard - DEDICATED MENTOR ONLY ✅ (using MentorDashboardPage)
  createRoleRoute('/dashboard/mentor', MentorDashboardPage, ['mentor'], ['read', 'write'], 'Loading mentor dashboard...'),

  // Mentorship management
  createRoleRoute('/dashboard/mentorship/sessions', MentorshipSessionsPage, ['mentor'], ['read', 'write'], 'Loading mentorship sessions...'),
  createRoleRoute('/dashboard/mentorship/mentees', MenteesManagementPage, ['mentor'], ['read', 'write'], 'Loading mentees management...'),
  createRoleRoute('/dashboard/mentorship/profile', MentorProfilePage, ['mentor'], ['read', 'write'], 'Loading mentor profile...'),
  createRoleRoute('/dashboard/mentorship/analytics', MentorAnalyticsPage, ['mentor'], ['read'], 'Loading mentor analytics...'),
  createRoleRoute('/dashboard/mentorship/availability', MentorAvailabilityPage, ['mentor'], ['read', 'write'], 'Loading availability management...'),

  // Business-related functionality for mentors
  createRoleRoute('/dashboard/business-ideas', BusinessIdeasPage, ['mentor'], ['read', 'write'], 'Loading business ideas...'),
  createRoleRoute('/dashboard/business-plans', BusinessPlanPage, ['mentor'], ['read', 'write'], 'Loading business plans...'),

  // Community features for mentors
  createRoleRoute('/dashboard/posts', PostsPage, ['mentor'], ['read', 'write'], 'Loading posts...'),
  createRoleRoute('/dashboard/events', EventsPage, ['mentor'], ['read', 'write'], 'Loading events...'),

  // AI assistance for mentors
  createRoleRoute('/chat/enhanced', ConsolidatedAIPage, ['mentor'], ['read'], 'Loading AI Assistant...'),

  // Settings
  createRoleRoute('/settings', UserSettings, ['mentor'], ['read', 'write'], 'Loading settings...'),
];

export default mentorRoutes;
