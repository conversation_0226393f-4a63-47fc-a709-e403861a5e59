#!/usr/bin/env python3

"""
Simple API test to verify the predictive analytics 500 error fix
"""

import os
import sys
import django
import requests
import json
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

def test_predictive_analytics_api():
    """Test the predictive analytics API endpoint directly"""
    print("🔍 Testing Predictive Analytics API Fix...")
    
    # Test data
    test_payload = {
        'analysis_type': 'success_prediction',
        'business_data': {
            'title': 'AI-Powered E-commerce Platform',
            'description': 'An innovative e-commerce platform using AI for personalized shopping experiences',
            'industry': 'technology',
            'stage': 'idea',
            'target_market': 'online shoppers',
            'revenue_model': 'subscription'
        },
        'industry': 'technology',
        'timeframe_days': 90
    }
    
    try:
        # Test the view directly (simulating the API call)
        from core.ai_views import PredictiveAnalyticsView
        from unittest.mock import Mock
        from django.contrib.auth.models import User
        
        # Create or get test user
        user, created = User.objects.get_or_create(
            username='api_test_user',
            defaults={'email': '<EMAIL>'}
        )
        
        # Create mock request
        mock_request = Mock()
        mock_request.user = user
        mock_request.data = test_payload
        
        # Test the view
        view = PredictiveAnalyticsView()
        response = view.post(mock_request)
        
        print(f"✅ API Response Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ SUCCESS: Predictive Analytics API is working!")
            
            if hasattr(response, 'data') and response.data:
                result = response.data.get('result', {})
                print(f"   📊 Success Probability: {result.get('success_probability', 'N/A')}")
                print(f"   📊 Confidence Score: {result.get('confidence_score', 'N/A')}")
                print(f"   📊 Risk Level: {result.get('risk_level', 'N/A')}")
                print(f"   📊 Market Fit Score: {result.get('market_fit_score', 'N/A')}")
                
                # Test different analysis types
                analysis_types = [
                    'market_forecast',
                    'risk_assessment', 
                    'investment_readiness'
                ]
                
                print("\n🔍 Testing other analysis types...")
                for analysis_type in analysis_types:
                    mock_request.data = {**test_payload, 'analysis_type': analysis_type}
                    response = view.post(mock_request)
                    status = "✅ PASS" if response.status_code == 200 else f"❌ FAIL ({response.status_code})"
                    print(f"   {analysis_type}: {status}")
                
                return True
            else:
                print("⚠️ Response has no data")
                return False
                
        elif response.status_code == 503:
            print("⚠️ Advanced AI features not available (expected if ML libraries missing)")
            return False
        else:
            print(f"❌ FAILED: API returned status {response.status_code}")
            if hasattr(response, 'data'):
                print(f"   Error: {response.data}")
            return False
            
    except Exception as e:
        print(f"❌ API Test Failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_analytics():
    """Test enhanced analytics features"""
    print("\n🔍 Testing Enhanced Analytics Features...")
    
    enhanced_tests = [
        ('failure_prediction', 'Startup Failure Prediction'),
        ('timing_optimization', 'Market Timing Optimization'),
        ('competitor_analysis', 'Competitor Analysis'),
        ('cac_prediction', 'Customer Acquisition Cost Prediction')
    ]
    
    try:
        from core.ai_views import PredictiveAnalyticsView
        from unittest.mock import Mock
        from django.contrib.auth.models import User
        
        user, _ = User.objects.get_or_create(
            username='enhanced_test_user',
            defaults={'email': '<EMAIL>'}
        )
        
        results = {}
        
        for analysis_type, description in enhanced_tests:
            mock_request = Mock()
            mock_request.user = user
            mock_request.data = {
                'analysis_type': analysis_type,
                'business_data': {
                    'title': 'Test Business',
                    'industry': 'technology',
                    'stage': 'growth'
                },
                'historical_data': [],
                'market_context': {},
                'competitor_data': [],
                'marketing_data': {}
            }
            
            view = PredictiveAnalyticsView()
            response = view.post(mock_request)
            
            status = "✅ PASS" if response.status_code == 200 else f"❌ FAIL ({response.status_code})"
            results[analysis_type] = response.status_code == 200
            print(f"   {description}: {status}")
        
        success_count = sum(1 for success in results.values() if success)
        total_count = len(results)
        
        print(f"\n📊 Enhanced Analytics Summary: {success_count}/{total_count} features working")
        return success_count == total_count
        
    except Exception as e:
        print(f"❌ Enhanced Analytics Test Failed: {e}")
        return False

def main():
    """Run API fix verification"""
    print("🚀 Predictive Analytics API Fix Verification")
    print("=" * 50)
    
    # Test basic API functionality
    basic_success = test_predictive_analytics_api()
    
    # Test enhanced features
    enhanced_success = test_enhanced_analytics()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 FIX VERIFICATION SUMMARY")
    print("=" * 50)
    
    if basic_success:
        print("✅ BASIC API: Fixed - No more 500 errors!")
    else:
        print("❌ BASIC API: Still has issues")
    
    if enhanced_success:
        print("✅ ENHANCED FEATURES: All working correctly")
    else:
        print("⚠️ ENHANCED FEATURES: Some issues remain")
    
    overall_success = basic_success and enhanced_success
    
    if overall_success:
        print("\n🎉 SUCCESS: Predictive Analytics API is fully functional!")
        print("   The 500 Internal Server Error has been resolved.")
    else:
        print("\n⚠️ PARTIAL SUCCESS: Main issue fixed, but some features need attention.")
    
    print("\n💡 Next Steps:")
    if basic_success:
        print("   1. ✅ Test the API in your browser/frontend")
        print("   2. ✅ Verify all analysis types work as expected")
        print("   3. ✅ Monitor for any remaining issues")
    else:
        print("   1. 🔧 Check Django server logs for additional errors")
        print("   2. 🔧 Verify all required dependencies are installed")
        print("   3. 🔧 Test API endpoints manually")

if __name__ == "__main__":
    main()
