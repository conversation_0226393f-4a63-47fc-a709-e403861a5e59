import React, { useState, useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '../../store/hooks';
import { createMentorshipApplication, fetchMentorProfiles } from '../../store/incubatorSlice';
import { BusinessIdea, MentorProfile } from '../../services/incubatorApi';
import { businessIdeasAPI } from '../../services/incubatorApi';

import { useTranslation } from 'react-i18next';
interface MentorshipApplicationFormProps {
  preferredMentor?: MentorProfile;
  onSubmitSuccess: () => void;
  onCancel: () => void;
}

const MentorshipApplicationForm: React.FC<MentorshipApplicationFormProps> = ({ preferredMentor, 
  onSubmitSuccess, 
  onCancel 
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector(state => state.auth);
  const { mentorProfiles, isLoading, error } = useAppSelector(state => state.incubator);
  
  const [userBusinessIdeas, setUserBusinessIdeas] = useState<BusinessIdea[]>([]);
  const [formData, setFormData] = useState({
    business_idea: '',
    goals: '',
    specific_areas: '',
    commitment: '',
    preferred_mentor: preferredMentor ? preferredMentor.id.toString() : '',
    preferred_communication: 'video',
    preferred_expertise: '',
  });
  
  const [formError, setFormError] = useState<string | null>(null);
  const [formSuccess, setFormSuccess] = useState<string | null>(null);
  
  // Fetch user's business ideas
  useEffect(() => {
    const fetchUserBusinessIdeas = async () => {
      try {
        const ideas = await businessIdeasAPI.getBusinessIdeas();
        // Filter for approved ideas owned by the user
        const userIdeas = ideas.filter(idea => 
          idea.owner.id === user?.id && 
          idea.moderation_status === 'approved'
        );
        setUserBusinessIdeas(userIdeas);
        
        // Set default business idea if user has any
        if (userIdeas.length > 0) {
          setFormData(prev => ({
            ...prev,
            business_idea: userIdeas[0].id.toString()
          }));
        }
      } catch (error) {
        console.error('Error fetching user business ideas:', error);
        setFormError(t("common.failed.to.load", "Failed to load your business ideas"));
      }
    };
    
    if (user) {
      fetchUserBusinessIdeas();
    }
  }, [user]);
  
  // Fetch mentor profiles if not already loaded
  useEffect(() => {
    if (mentorProfiles.length === 0) {
      dispatch(fetchMentorProfiles());
    }
  }, [dispatch, mentorProfiles.length]);
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);
    
    // Validate form
    if (!formData.business_idea) {
      setFormError(t("common.please.select.a", "Please select a business idea"));
      return;
    }
    
    if (!formData.goals.trim()) {
      setFormError(t("common.goals.are.required", "Goals are required"));
      return;
    }
    
    if (!formData.specific_areas.trim()) {
      setFormError(t("common.specific.areas.are", "Specific areas are required"));
      return;
    }
    
    if (!formData.commitment.trim()) {
      setFormError(t("common.commitment.information.is", "Commitment information is required"));
      return;
    }
    
    if (!user) {
      setFormError('You must be logged in to apply for mentorship');
      return;
    }
    
    try {
      // Dispatch action to create mentorship application
      await dispatch(createMentorshipApplication({
        business_idea: parseInt(formData.business_idea),
        applicant_id: user.id,
        goals: formData.goals,
        specific_areas: formData.specific_areas,
        commitment: formData.commitment,
        preferred_mentor_id: formData.preferred_mentor ? parseInt(formData.preferred_mentor) : undefined,
        preferred_communication: formData.preferred_communication as 'video' | 'phone' | 'email' | 'chat' | 'in_person',
        preferred_expertise: formData.preferred_expertise || undefined
      })).unwrap();
      
      setFormSuccess(t("common.mentorship.application.submitted", "Mentorship application submitted successfully!"));
      
      // Reset form
      setFormData({
        business_idea: userBusinessIdeas.length > 0 ? userBusinessIdeas[0].id.toString() : '',
        goals: '',
        specific_areas: '',
        commitment: '',
        preferred_mentor: preferredMentor ? preferredMentor.id.toString() : '',
        preferred_communication: 'video',
        preferred_expertise: '',
      });
      
      // Close modal after success
      setTimeout(() => {
        onSubmitSuccess();
      }, 1500);
    } catch (err) {
      setFormError(err instanceof Error ? err.message : t("common.failed.to.submit", "Failed to submit mentorship application"));
    }
  };
  
  // Get expertise categories from all mentors
  const expertiseCategories = React.useMemo(() => {
    const categories = new Set<string>();
    mentorProfiles.forEach(mentor => {
      mentor.expertise_areas.forEach(expertise => {
        categories.add(expertise.category);
      });
    });
    return Array.from(categories).sort();
  }, [mentorProfiles]);
  
  // Get category display names
  const getCategoryDisplay = (category: string) => {
    const expertise = mentorProfiles.flatMap(m => m.expertise_areas).find(e => e.category === category);
    return expertise?.category_display || category;
  };
  
  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {formError && (
        <div className="bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded-md">
          {formError}
        </div>
      )}
      
      {formSuccess && (
        <div className="bg-green-900/50 border border-green-500 text-green-200 px-4 py-3 rounded-md">
          {formSuccess}
        </div>
      )}
      
      <div>
        <label htmlFor="business_idea" className="block text-sm font-medium mb-1">
          Select Business Idea <span className="text-red-400">*</span>
        </label>
        {userBusinessIdeas.length > 0 ? (
          <select
            id="business_idea"
            name="business_idea"
            value={formData.business_idea}
            onChange={handleInputChange}
            className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
            required
          >
            {userBusinessIdeas.map(idea => (
              <option key={idea.id} value={idea.id}>
                {idea.title}
              </option>
            ))}
          </select>
        ) : (
          <div className="text-yellow-300 text-sm mb-2">
            You don't have any approved business ideas. Please submit a business idea first and wait for approval.
          </div>
        )}
      </div>
      
      <div>
        <label htmlFor="goals" className="block text-sm font-medium mb-1">
          Mentorship Goals <span className="text-red-400">*</span>
        </label>
        <textarea
          id="goals"
          name="goals"
          value={formData.goals}
          onChange={handleInputChange}
          rows={3}
          className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
          placeholder={t("common.what.do.you", "What do you hope to achieve with mentorship?")}
          required
        />
      </div>
      
      <div>
        <label htmlFor="specific_areas" className="block text-sm font-medium mb-1">
          Specific Areas <span className="text-red-400">*</span>
        </label>
        <textarea
          id="specific_areas"
          name="specific_areas"
          value={formData.specific_areas}
          onChange={handleInputChange}
          rows={3}
          className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
          placeholder={t("common.what.specific.areas", "What specific areas do you need help with?")}
          required
        />
      </div>
      
      <div>
        <label htmlFor="commitment" className="block text-sm font-medium mb-1">
          Time Commitment <span className="text-red-400">*</span>
        </label>
        <textarea
          id="commitment"
          name="commitment"
          value={formData.commitment}
          onChange={handleInputChange}
          rows={2}
          className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
          placeholder={t("common.how.much.time", "How much time can you commit to working with a mentor?")}
          required
        />
      </div>
      
      <div>
        <label htmlFor="preferred_mentor" className="block text-sm font-medium mb-1">
          Preferred Mentor (Optional)
        </label>
        <select
          id="preferred_mentor"
          name="preferred_mentor"
          value={formData.preferred_mentor}
          onChange={handleInputChange}
          className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
        >
          <option value="">t("common.no.preference", "No preference")</option>
          {mentorProfiles
            .filter(mentor => mentor.is_verified && mentor.is_accepting_mentees)
            .map(mentor => (
              <option key={mentor.id} value={mentor.id}>
                {mentor.user.first_name} {mentor.user.last_name} - {mentor.position} at {mentor.company}
              </option>
            ))}
        </select>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label htmlFor="preferred_communication" className="block text-sm font-medium mb-1">
            Preferred Communication Method
          </label>
          <select
            id="preferred_communication"
            name="preferred_communication"
            value={formData.preferred_communication}
            onChange={handleInputChange}
            className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
          >
            <option value="video">t("common.video.call", "Video Call")</option>
            <option value="phone">t("common.phone.call", "Phone Call")</option>
            <option value="email">t("common.email", t("common.email", "Email"))</option>
            <option value="chat">Chat/Messaging</option>
            <option value="in_person">t("common.in.person", "In Person")</option>
          </select>
        </div>
        
        <div>
          <label htmlFor="preferred_expertise" className="block text-sm font-medium mb-1">
            Preferred Expertise Area (Optional)
          </label>
          <select
            id="preferred_expertise"
            name="preferred_expertise"
            value={formData.preferred_expertise}
            onChange={handleInputChange}
            className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
          >
            <option value="">t("common.no.preference", "No preference")</option>
            {expertiseCategories.map(category => (
              <option key={category} value={category}>
                {getCategoryDisplay(category)}
              </option>
            ))}
          </select>
        </div>
      </div>
      
      <div className={`flex justify-end space-x-4 pt-4 ${isRTL ? "flex-row-reverse" : ""}`}>
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-600 rounded-lg hover:bg-gray-800 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isLoading || userBusinessIdeas.length === 0}
          className={`px-4 py-2 bg-purple-600 rounded-lg hover:bg-purple-700 transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed ${isRTL ? "flex-row-reverse" : ""}`}
        >
          {isLoading ? (
            <>
              <span className={`w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 ${isRTL ? "space-x-reverse" : ""}`}></span>
              Submitting...
            </>
          ) : (
            t("common.submit.application", "Submit Application")
          )}
        </button>
      </div>
    </form>
  );
};

export default MentorshipApplicationForm;
