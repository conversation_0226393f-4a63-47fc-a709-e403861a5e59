import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '../../components/ui/card';
import Button from '../../components/ui/Button';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '../../components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import { TrendingUp, Users, Calendar, Star, Clock, Target, Award, BarChart3 } from 'lucide-react';

interface MentorAnalytics {
  overview: {
    totalMentees: number;
    activeMentees: number;
    completedMentorships: number;
    totalSessions: number;
    averageRating: number;
    responseRate: number;
  };
  performance: {
    monthlyStats: {
      month: string;
      sessions: number;
      newMentees: number;
      completions: number;
      rating: number;
    }[];
    topAchievements: string[];
    improvementAreas: string[];
  };
  menteeProgress: {
    menteeId: string;
    menteeName: string;
    businessIdea: string;
    stage: string;
    progress: number;
    sessionsCompleted: number;
    lastSession: string;
    nextMilestone: string;
  }[];
  sessionAnalytics: {
    averageSessionDuration: number;
    sessionTypes: { type: string; count: number; percentage: number }[];
    popularTopics: { topic: string; frequency: number }[];
    timeSlots: { slot: string; bookings: number }[];
  };
}

const MentorAnalyticsPage: React.FC = () => {
  const [analytics, setAnalytics] = useState<MentorAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeframe, setTimeframe] = useState('3m');
  const [activeTab, setActiveTab] = useState('overview');

  // Mock data - replace with actual API call
  useEffect(() => {
    const mockAnalytics: MentorAnalytics = {
      overview: {
        totalMentees: 23,
        activeMentees: 8,
        completedMentorships: 15,
        totalSessions: 156,
        averageRating: 4.8,
        responseRate: 95
      },
      performance: {
        monthlyStats: [
          { month: 'Oct', sessions: 12, newMentees: 2, completions: 1, rating: 4.7 },
          { month: 'Nov', sessions: 15, newMentees: 3, completions: 2, rating: 4.8 },
          { month: 'Dec', sessions: 18, newMentees: 1, completions: 3, rating: 4.9 },
          { month: 'Jan', sessions: 14, newMentees: 2, completions: 1, rating: 4.8 }
        ],
        topAchievements: [
          'Helped 3 mentees secure funding',
          'Maintained 4.8+ rating for 6 months',
          'Completed 15 successful mentorships',
          'Top mentor in Product Strategy category'
        ],
        improvementAreas: [
          'Increase session frequency',
          'Expand expertise in AI/ML',
          'Improve follow-up consistency'
        ]
      },
      menteeProgress: [
        {
          menteeId: '1',
          menteeName: 'Alex Chen',
          businessIdea: 'AI Learning Platform',
          stage: 'MVP',
          progress: 75,
          sessionsCompleted: 8,
          lastSession: '2024-01-15',
          nextMilestone: 'User Testing'
        },
        {
          menteeId: '2',
          menteeName: 'Sarah Johnson',
          businessIdea: 'Sustainable Fashion',
          stage: 'Growth',
          progress: 85,
          sessionsCompleted: 12,
          lastSession: '2024-01-12',
          nextMilestone: 'Series A Prep'
        },
        {
          menteeId: '3',
          menteeName: 'Michael Rodriguez',
          businessIdea: 'HealthTech Device',
          stage: 'Idea',
          progress: 30,
          sessionsCompleted: 3,
          lastSession: '2024-01-10',
          nextMilestone: 'Prototype Development'
        }
      ],
      sessionAnalytics: {
        averageSessionDuration: 52,
        sessionTypes: [
          { type: 'Video Call', count: 89, percentage: 65 },
          { type: 'In-Person', count: 34, percentage: 25 },
          { type: 'Phone Call', count: 14, percentage: 10 }
        ],
        popularTopics: [
          { topic: 'Product Strategy', frequency: 45 },
          { topic: 'Fundraising', frequency: 32 },
          { topic: 'Team Building', frequency: 28 },
          { topic: 'Go-to-Market', frequency: 25 },
          { topic: 'Technical Architecture', frequency: 18 }
        ],
        timeSlots: [
          { slot: '9:00-11:00 AM', bookings: 35 },
          { slot: '2:00-4:00 PM', bookings: 42 },
          { slot: '4:00-6:00 PM', bookings: 38 },
          { slot: '6:00-8:00 PM', bookings: 25 }
        ]
      }
    };

    setTimeout(() => {
      setAnalytics(mockAnalytics);
      setLoading(false);
    }, 1000);
  }, [timeframe]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  const getStageColor = (stage: string) => {
    switch (stage) {
      case 'Idea': return 'bg-purple-100 text-purple-800';
      case 'MVP': return 'bg-blue-100 text-blue-800';
      case 'Growth': return 'bg-green-100 text-green-800';
      case 'Scale': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!analytics) return null;

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Mentor Analytics</h1>
          <p className="text-gray-600 mt-1">Track your mentoring performance and impact</p>
        </div>
        <div className="flex gap-2">
          <Select value={timeframe} onValueChange={setTimeframe}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1m">1 Month</SelectItem>
              <SelectItem value="3m">3 Months</SelectItem>
              <SelectItem value="6m">6 Months</SelectItem>
              <SelectItem value="1y">1 Year</SelectItem>
            </SelectContent>
          </Select>
          <Button className="bg-blue-600 hover:bg-blue-700">
            <BarChart3 className="w-4 h-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Mentees</p>
                <p className="text-2xl font-bold">{analytics.overview.totalMentees}</p>
                <p className="text-sm text-green-600">{analytics.overview.activeMentees} active</p>
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Sessions</p>
                <p className="text-2xl font-bold">{analytics.overview.totalSessions}</p>
                <p className="text-sm text-gray-600">Avg {analytics.sessionAnalytics.averageSessionDuration} min</p>
              </div>
              <Calendar className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Average Rating</p>
                <p className="text-2xl font-bold">{analytics.overview.averageRating}</p>
                <p className="text-sm text-yellow-600">★★★★★</p>
              </div>
              <Star className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-2xl font-bold">{analytics.overview.completedMentorships}</p>
                <p className="text-sm text-gray-600">Mentorships</p>
              </div>
              <Award className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Response Rate</p>
                <p className="text-2xl font-bold">{analytics.overview.responseRate}%</p>
                <p className="text-sm text-green-600">Excellent</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Success Rate</p>
                <p className="text-2xl font-bold">
                  {Math.round((analytics.overview.completedMentorships / analytics.overview.totalMentees) * 100)}%
                </p>
                <p className="text-sm text-gray-600">Completion</p>
              </div>
              <Target className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Analytics Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="mentees">Mentee Progress</TabsTrigger>
          <TabsTrigger value="sessions">Session Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Monthly Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analytics.performance.monthlyStats.map((stat, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded">
                      <div>
                        <span className="font-medium">{stat.month}</span>
                      </div>
                      <div className="grid grid-cols-4 gap-4 text-sm text-center">
                        <div>
                          <p className="text-gray-600">Sessions</p>
                          <p className="font-semibold">{stat.sessions}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">New</p>
                          <p className="font-semibold">{stat.newMentees}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Completed</p>
                          <p className="font-semibold">{stat.completions}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Rating</p>
                          <p className="font-semibold">{stat.rating}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Top Achievements</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analytics.performance.topAchievements.map((achievement, index) => (
                    <div key={index} className="flex items-center gap-3 p-2 bg-green-50 rounded">
                      <Award className="w-5 h-5 text-green-600" />
                      <span className="text-sm">{achievement}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Session Types Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analytics.sessionAnalytics.sessionTypes.map((type, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">{type.type}</span>
                        <span className="text-sm text-gray-600">{type.count} ({type.percentage}%)</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${type.percentage}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Popular Topics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analytics.sessionAnalytics.popularTopics.map((topic, index) => (
                    <div key={index} className="flex items-center justify-between p-2 border rounded">
                      <span className="font-medium">{topic.topic}</span>
                      <span className="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded">
                        {topic.frequency} sessions
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Areas for Improvement</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {analytics.performance.improvementAreas.map((area, index) => (
                  <div key={index} className="p-3 bg-yellow-50 border border-yellow-200 rounded">
                    <p className="text-sm text-yellow-800">{area}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="mentees" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Mentee Progress Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analytics.menteeProgress.map((mentee) => (
                  <div key={mentee.menteeId} className="p-4 border rounded-lg">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h3 className="font-semibold">{mentee.menteeName}</h3>
                        <p className="text-sm text-gray-600">{mentee.businessIdea}</p>
                      </div>
                      <div className="text-right">
                        <span className={`px-2 py-1 rounded text-xs font-medium ${getStageColor(mentee.stage)}`}>
                          {mentee.stage}
                        </span>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-3">
                      <div>
                        <p className="text-gray-600">Progress</p>
                        <p className="font-semibold">{mentee.progress}%</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Sessions</p>
                        <p className="font-semibold">{mentee.sessionsCompleted}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Last Session</p>
                        <p className="font-semibold">{formatDate(mentee.lastSession)}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Next Milestone</p>
                        <p className="font-semibold">{mentee.nextMilestone}</p>
                      </div>
                    </div>

                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-green-600 h-2 rounded-full" 
                        style={{ width: `${mentee.progress}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sessions" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Popular Time Slots</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analytics.sessionAnalytics.timeSlots.map((slot, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">{slot.slot}</span>
                        <span className="text-sm text-gray-600">{slot.bookings} bookings</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-purple-600 h-2 rounded-full" 
                          style={{ width: `${(slot.bookings / 50) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Session Statistics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                    <span className="text-gray-600">Average Duration</span>
                    <span className="font-semibold">{analytics.sessionAnalytics.averageSessionDuration} minutes</span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                    <span className="text-gray-600">Total Sessions</span>
                    <span className="font-semibold">{analytics.overview.totalSessions}</span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                    <span className="text-gray-600">Response Rate</span>
                    <span className="font-semibold">{analytics.overview.responseRate}%</span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                    <span className="text-gray-600">Average Rating</span>
                    <span className="font-semibold">{analytics.overview.averageRating}/5.0</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default MentorAnalyticsPage;
