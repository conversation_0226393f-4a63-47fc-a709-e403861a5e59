import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../store/hooks';
import {
  fetchMentorshipSession,
  startMentorshipSession,
  completeMentorshipSession,
  cancelMentorshipSession,
  createMeeting
} from '../../store/incubatorSlice';
import { MentorshipSession } from '../../services/incubatorApi';
import {
  Calendar,
  Clock,
  Video,
  User,
  MessageSquare,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Play,
  Square,
  Edit,
  Trash,
  ExternalLink
} from 'lucide-react';
import LoadingSpinner from '../common/LoadingSpinner';
import EnhancedFeedbackForm from './EnhancedFeedbackForm';
import CalendarIntegration from './CalendarIntegration';
import StarRating from '../common/StarRating';
import { useTranslation } from 'react-i18next';

const EnhancedSessionDetails: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const { id } = useParams<{ id: string }>();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const { currentSession, loading, error } = useAppSelector(state => state.incubator);
  const { user } = useAppSelector(state => state.auth);

  const [showFeedbackForm, setShowFeedbackForm] = useState(false);
  const [showCalendarIntegration, setShowCalendarIntegration] = useState(false);

  useEffect(() => {
    if (id) {
      dispatch(fetchMentorshipSession(parseInt(id);
    }
  }, [dispatch, id]);

  const handleStartSession = () => {
    if (id) {
      dispatch(startMentorshipSession(parseInt(id);
    }
  };

  const handleCompleteSession = () => {
    if (id) {
      dispatch(completeMentorshipSession(parseInt(id)
        .then(() => {
          setShowFeedbackForm(true);
        });
    }
  };

  const handleCancelSession = () => {
    if (window.confirm(t("ai.are.you.sure", "Are you sure you want to cancel this session? This action cannot be undone."))) {
      if (id) {
        dispatch(cancelMentorshipSession(parseInt(id);
      };

  const handleCreateMeeting = (provider: string) => {
    if (id) {
      dispatch(createMeeting({
        id: parseInt(id),
        videoProvider: provider
      }));
    }
  };

  const handleEditSession = () => {
    navigate(`/dashboard/mentorship/sessions/${id}/edit`);
  };

  const handleDeleteSession = () => {
    if (window.confirm(t("ai.are.you.sure", "Are you sure you want to delete this session? This action cannot be undone."))) {
      // Implement delete functionality
      navigate('/dashboard/mentorship/sessions');
    }
  };

  const getStatusBadge = (session: MentorshipSession) => {
    const statusColors = {
      scheduled: 'bg-blue-900/30 text-blue-400 border-blue-800/50',
      completed: 'bg-green-900/30 text-green-400 border-green-800/50',
      cancelled: 'bg-red-900/30 text-red-400 border-red-800/50',
      rescheduled: 'bg-yellow-900/30 text-yellow-400 border-yellow-800/50',
      in_progress: 'bg-purple-900/30 text-purple-400 border-purple-800/50'
    };

    const statusIcons = {
      scheduled: <Calendar size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />,
      completed: <CheckCircle size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />,
      cancelled: <XCircle size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />,
      rescheduled: <AlertTriangle size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />,
      in_progress: <Play size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
    };

    const color = statusColors[session.status] || 'bg-gray-900/30 text-gray-400 border-gray-800/50';
    const icon = statusIcons[session.status] || null;

    return (
      <span className={`px-2 py-1 rounded-full text-xs flex items-center border ${color}`}>
        {icon}
        {session.status_display}
      </span>
    );
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return (
      <div className="bg-red-900/30 border border-red-800/50 rounded-lg p-4 mb-4">
        <h3 className="text-lg font-medium mb-2 text-red-400">t("common.error", t("common.error", "Error"))</h3>
        <div>{error}</div>
      </div>
    );
  }

  if (!currentSession) {
    return (
      <div className="bg-indigo-900/30 border border-indigo-800/50 rounded-lg p-4 mb-4">
        <h3 className="text-lg font-medium mb-2">t("common.session.not.found", "Session Not Found")</h3>
        <div>t("common.the.requested.mentorship", "The requested mentorship session could not be found.")</div>
      </div>
    );
  }

  const session = currentSession;
  const sessionDate = new Date(session.scheduled_at);
  const isPastSession = sessionDate < new Date();
  const canStart = session.status === 'scheduled' || session.status === 'rescheduled';
  const canComplete = session.status === 'in_progress';
  const canCancel = session.status === 'scheduled' || session.status === 'rescheduled' || session.status === 'in_progress';
  const canEdit = session.status !== 'completed' && session.status !== 'cancelled';

  return (
    <div className="space-y-6">
      <div className={`flex flex-col md:flex-row justify-between items-start md:items-center gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div>
          <h1 className="text-2xl font-bold">{session.title}</h1>
          <div className={`flex items-center mt-2 space-x-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            {getStatusBadge(session)}
            <span className={`text-sm text-gray-400 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <Clock size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
              {sessionDate.toLocaleString()} ({session.duration_minutes} min)
            </span>
          </div>
        </div>

        <div className={`flex space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
          {canStart && (
            <button
              onClick={handleStartSession}
              className={`px-3 py-1.5 bg-green-600 hover:bg-green-700 rounded text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
            >
              <Play size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> Start Session
            </button>
          )}

          {canComplete && (
            <button
              onClick={handleCompleteSession}
              className={`px-3 py-1.5 bg-blue-600 hover:bg-blue-700 rounded text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
            >
              <CheckCircle size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> Complete
            </button>
          )}

          {canCancel && (
            <button
              onClick={handleCancelSession}
              className={`px-3 py-1.5 bg-red-900/50 hover:bg-red-900/70 rounded text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
            >
              <XCircle size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> Cancel
            </button>
          )}

          {canEdit && (
            <button
              onClick={handleEditSession}
              className={`px-3 py-1.5 bg-indigo-900/50 hover:bg-indigo-900/70 rounded text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
            >
              <Edit size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> Edit
            </button>
          )}

          <button
            onClick={handleDeleteSession}
            className={`px-3 py-1.5 bg-red-900/30 hover:bg-red-900/50 rounded text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <Trash size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> Delete
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2 space-y-6">
          {/* Session Details */}
          <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
            <h2 className="text-xl font-semibold mb-4">t("common.session.details", "Session Details")</h2>

            {session.description && (
              <div className="mb-4">
                <h3 className="text-sm font-medium text-gray-400 mb-1">t("common.description", t("common.description", "Description"))</h3>
                <div className="text-gray-300">{session.description}</div>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <h3 className="text-sm font-medium text-gray-400 mb-1">t("common.session.type", "Session Type")</h3>
                <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                  {session.session_type === 'video' ? (
                    <Video size={16} className={`mr-1 text-purple-400 ${isRTL ? "space-x-reverse" : ""}`} />
                  ) : (
                    <MessageSquare size={16} className={`mr-1 text-purple-400 ${isRTL ? "space-x-reverse" : ""}`} />
                  )}
                  {session.session_type_display}
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-400 mb-1">t("common.duration", "Duration")</h3>
                <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                  <Clock size={16} className={`mr-1 text-purple-400 ${isRTL ? "space-x-reverse" : ""}`} />
                  {session.duration_minutes} minutes
                </div>
              </div>
            </div>

            {/* Video Meeting Details */}
            {session.video_provider && (
              <div className="bg-indigo-800/30 rounded-lg p-4 mb-4">
                <div className={`flex items-center justify-between mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <h3 className="text-sm font-medium">t("common.video.meeting", "Video Meeting")</h3>
                  <span className="text-xs bg-purple-900/50 px-2 py-0.5 rounded">
                    {session.video_provider_display}
                  </span>
                </div>

                {session.meeting_link && (
                  <a
                    href={session.meeting_link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={`flex items-center text-purple-400 hover:text-purple-300 mb-2 ${isRTL ? "flex-row-reverse" : ""}`}
                  >
                    <ExternalLink size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                    Join Meeting
                  </a>
                )}

                {session.meeting_password && (
                  <div className="text-xs text-gray-400">
                    Password: {session.meeting_password}
                  </div>
                )}

                {!session.meeting_link && canEdit && (
                  <div className="mt-2">
                    <div className="text-sm text-gray-400 mb-2">t("common.no.meeting.link", "No meeting link yet. Create one:")</div>
                    <div className={`flex space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <button
                        onClick={() => handleCreateMeeting('zoom')}
                        className="px-2 py-1 bg-blue-900/30 hover:bg-blue-900/50 rounded text-xs"
                      >
                        Zoom
                      </button>
                      <button
                        onClick={() => handleCreateMeeting('google_meet')}
                        className="px-2 py-1 bg-red-900/30 hover:bg-red-900/50 rounded text-xs"
                      >
                        Google Meet
                      </button>
                      <button
                        onClick={() => handleCreateMeeting('jitsi')}
                        className="px-2 py-1 bg-green-900/30 hover:bg-green-900/50 rounded text-xs"
                      >
                        Jitsi Meet
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Notes */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {session.mentor_notes && (
                <div>
                  <h3 className="text-sm font-medium text-gray-400 mb-1">t("common.mentor.notes", "Mentor Notes")</h3>
                  <div className="text-sm text-gray-300">{session.mentor_notes}</div>
                </div>
              )}

              {session.mentee_notes && (
                <div>
                  <h3 className="text-sm font-medium text-gray-400 mb-1">t("common.mentee.notes", "Mentee Notes")</h3>
                  <div className="text-sm text-gray-300">{session.mentee_notes}</div>
                </div>
              )}
            </div>
          </div>

          {/* Feedback Form */}
          {showFeedbackForm && (
            <EnhancedFeedbackForm
              session={session}
              onSuccess={() => setShowFeedbackForm(false)}
              onCancel={() => setShowFeedbackForm(false)}
            />
          )}

          {/* Feedback Display */}
          {session.feedback && session.feedback.length > 0 && (
            <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
              <h2 className="text-xl font-semibold mb-4">t("common.session.feedback", "Session Feedback")</h2>

              {session.feedback.map(feedback => (
                <div key={feedback.id} className="mb-4 last:mb-0">
                  <div className={`flex items-center justify-between mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                      <User size={16} className={`mr-1 text-purple-400 ${isRTL ? "space-x-reverse" : ""}`} />
                      <span className="font-medium">
                        {feedback.provided_by.first_name} {feedback.provided_by.last_name}
                      </span>
                      <span className={`text-xs ml-2 text-gray-400 ${isRTL ? "space-x-reverse" : ""}`}>
                        ({feedback.is_from_mentee ? t("common.mentee.mentor", "Mentee' : 'Mentor")})
                      </span>
                    </div>
                    <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                      <StarRating rating={feedback.rating} size={14} />
                      <span className={`ml-1 text-xs text-gray-400 ${isRTL ? "space-x-reverse" : ""}`}>
                        ({feedback.rating}/5)
                      </span>
                    </div>
                  </div>

                  <div className="text-sm text-gray-300 mb-2">{feedback.comments}</div>

                  {feedback.highlights && (
                    <div className="mb-2">
                      <h4 className="text-xs font-medium text-green-400">t("common.highlights", "Highlights")</h4>
                      <div className="text-xs text-gray-400">{feedback.highlights}</div>
                    </div>
                  )}

                  {feedback.areas_of_improvement && (
                    <div>
                      <h4 className="text-xs font-medium text-yellow-400">t("common.areas.for.improvement", "Areas for Improvement")</h4>
                      <div className="text-xs text-gray-400">{feedback.areas_of_improvement}</div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="space-y-6">
          {/* Calendar Integration */}
          <div>
            {showCalendarIntegration ? (
              <CalendarIntegration
                session={session}
                onSuccess={() => setShowCalendarIntegration(false)}
              />
            ) : (
              <button
                onClick={() => setShowCalendarIntegration(true)}
                className={`w-full py-2 bg-indigo-900/50 hover:bg-indigo-900/70 rounded-lg text-white flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <Calendar size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> Add to Calendar
              </button>
            )}
          </div>

          {/* Provide Feedback Button */}
          {!showFeedbackForm && session.status === 'completed' && (
            <button
              onClick={() => setShowFeedbackForm(true)}
              className={`w-full py-2 bg-purple-600 hover:bg-purple-700 rounded-lg text-white flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}
            >
              <MessageSquare size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> Provide Feedback
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default EnhancedSessionDetails;
