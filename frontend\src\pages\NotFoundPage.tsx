import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Home, ArrowLeft, LogIn, Search, HelpCircle } from 'lucide-react';
import { useAppSelector } from '../store/hooks';
import { isSuperAdmin } from '../utils/roleBasedRouting';
import { useLanguage } from '../hooks/useLanguage';
import { RTLText, RTLFlex } from '../components/common';

/**
 * Dedicated 404 Not Found page component
 * Provides helpful navigation options and clear messaging
 */
const NotFoundPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();
  const { isAuthenticated, user } = useAppSelector(state => state.auth);

  const getDashboardPath = () => {
    if (!user) return '/';
    
    if (isSuperAdmin(user)) {
      return '/super_admin';
    } else if (user.is_admin) {
      return '/admin';
    } else {
      return '/dashboard';
    }
  };

  const handleGoBack = () => {
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      navigate(getDashboardPath());
    }
  };

  const handleGoHome = () => {
    navigate(getDashboardPath());
  };

  const handleSearch = () => {
    if (isAuthenticated) {
      navigate('/dashboard/search');
    } else {
      navigate('/search');
    }
  };

  const quickLinks = isAuthenticated ? [
    { 
      label: t('navigation.dashboard', 'Dashboard'), 
      path: getDashboardPath(), 
      icon: Home,
      description: t('notFound.dashboardDesc', 'Go to your main dashboard')
    },
    { 
      label: t('navigation.search', 'Search'), 
      path: '/dashboard/search', 
      icon: Search,
      description: t('notFound.searchDesc', 'Search for content')
    },
    { 
      label: t('navigation.help', 'Help'), 
      path: '/help', 
      icon: HelpCircle,
      description: t('notFound.helpDesc', 'Get help and support')
    }
  ] : [
    { 
      label: t('navigation.home', 'Home'), 
      path: '/', 
      icon: Home,
      description: t('notFound.homeDesc', 'Return to homepage')
    },
    { 
      label: t('auth.login', 'Login'), 
      path: '/login', 
      icon: LogIn,
      description: t('notFound.loginDesc', 'Sign in to your account')
    },
    { 
      label: t('navigation.features', 'Features'), 
      path: '/features', 
      icon: Search,
      description: t('notFound.featuresDesc', 'Explore our features')
    }
  ];

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center px-4 ${isRTL ? 'font-arabic' : ''}`}>
      <div className="max-w-2xl w-full text-center">
        {/* 404 Header */}
        <div className="mb-12">
          <div className="relative">
            <h1 className="text-9xl font-bold text-gray-200 dark:text-gray-700 select-none">
              404
            </h1>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-6xl">🔍</div>
            </div>
          </div>
          
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4 mt-8">
            <RTLText text={t('notFound.title', 'Page Not Found')} />
          </h2>
          
          <p className="text-lg text-gray-600 dark:text-gray-400 mb-8 max-w-md mx-auto">
            <RTLText text={t('notFound.description', 'The page you\'re looking for doesn\'t exist or you don\'t have permission to access it.')} />
          </p>
        </div>

        {/* Action Buttons */}
        <RTLFlex className="flex-wrap gap-4 justify-center mb-12">
          <button
            onClick={handleGoBack}
            className={`inline-flex items-center px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 ${isRTL ? 'flex-row-reverse' : ''}`}
          >
            <ArrowLeft className={`w-5 h-5 ${isRTL ? 'ml-2 rotate-180' : 'mr-2'}`} />
            <RTLText text={t('common.goBack', 'Go Back')} />
          </button>
          
          <button
            onClick={handleGoHome}
            className={`inline-flex items-center px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 ${isRTL ? 'flex-row-reverse' : ''}`}
          >
            <Home className={`w-5 h-5 ${isRTL ? 'ml-2' : 'mr-2'}`} />
            <RTLText text={isAuthenticated ? t('navigation.dashboard', 'Dashboard') : t('navigation.home', 'Home')} />
          </button>
        </RTLFlex>

        {/* Quick Links */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
            <RTLText text={t('notFound.quickLinks', 'Quick Links')} />
          </h3>
          
          <div className="grid gap-4 md:grid-cols-3">
            {quickLinks.map((link, index) => {
              const IconComponent = link.icon;
              return (
                <button
                  key={index}
                  onClick={() => navigate(link.path)}
                  className={`p-4 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-purple-300 dark:hover:border-purple-600 hover:bg-purple-50 dark:hover:bg-purple-900/20 transition-all group ${isRTL ? 'text-right' : 'text-left'}`}
                >
                  <RTLFlex className="items-center mb-2">
                    <IconComponent className={`w-5 h-5 text-purple-600 dark:text-purple-400 group-hover:text-purple-700 dark:group-hover:text-purple-300 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                    <span className="font-medium text-gray-900 dark:text-white">
                      <RTLText text={link.label} />
                    </span>
                  </RTLFlex>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    <RTLText text={link.description} />
                  </p>
                </button>
              );
            })}
          </div>
        </div>

        {/* Additional Help */}
        <div className="mt-8 text-sm text-gray-500 dark:text-gray-400">
          <RTLText text={t('notFound.needHelp', 'Need help? Contact our support team or check our documentation.')} />
        </div>
      </div>
    </div>
  );
};

export default NotFoundPage;
