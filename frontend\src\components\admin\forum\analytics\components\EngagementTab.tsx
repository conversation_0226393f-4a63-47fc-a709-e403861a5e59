import React from 'react';
import { MessageSquare, Users, Calendar } from 'lucide-react';
import {
  <PERSON><PERSON><PERSON> as Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  Tooltip,
  ResponsiveContainer
} from 'recharts';
import StatsCard from './StatsCard';
import { useTranslation } from 'react-i18next';

interface EngagementTabProps {
  data: any;
}

const EngagementTab: React.FC<EngagementTabProps> = ({ data  }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  if (!data) return <div className="text-center py-8">t("admin.no.data.available", t("common.noDataAvailable", "No data available"))</div>;

  // Prepare data for charts
  const userEngagementData = [
    { name: t("admin.active.users", "Active Users"), value: data.participation_rate },
    { name: t("admin.inactive.users", "Inactive Users"), value: 100 - data.participation_rate }
  ];

  const subscriptionData = [
    { name: t("admin.subscribed", "Subscribed"), value: data.subscription_rate },
    { name: t("admin.not.subscribed", "Not Subscribed"), value: 100 - data.subscription_rate }
  ];

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <StatsCard
          title={t("admin.forum.users", "Forum Users")}
          value={data.total_forum_users}
          icon={<Users size={20} className="text-purple-400" />}
          subtitle={`Out of ${data.total_users} total users`}
        />

        <StatsCard
          title={t("admin.avg.posts.per", "Avg. Posts Per User")}
          value={data.avg_posts_per_user.toFixed(1)}
          icon={<MessageSquare size={20} className="text-purple-400" />}
          subtitle={`${data.avg_threads_per_user.toFixed(1)} threads per user`}
        />

        <StatsCard
          title={t("admin.subscribed.users", "Subscribed Users")}
          value={data.users_with_subscriptions}
          icon={<Calendar size={20} className="text-purple-400" />}
          subtitle={`${data.subscription_rate.toFixed(1)}% subscription rate`}
        />
      </div>

      {/* Participation Rate Chart */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
          <h3 className="text-xl font-semibold mb-4">t("admin.user.participation.rate", "User Participation Rate")</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <RechartsPieChart>
                <Pie
                  data={userEngagementData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, value }) => `${name}: ${value.toFixed(1)}%`}
                >
                  <Cell fill="#8884d8" />
                  <Cell fill="#444" />
                </Pie>
                <Tooltip
                  contentStyle={{ backgroundColor: '#1e1e3f', borderColor: '#444', color: '#fff' }}
                />
              </RechartsPieChart>
            </ResponsiveContainer>
          </div>
        </div>

        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
          <h3 className="text-xl font-semibold mb-4">t("admin.subscription.rate", "Subscription Rate")</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <RechartsPieChart>
                <Pie
                  data={subscriptionData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#82ca9d"
                  dataKey="value"
                  label={({ name, value }) => `${name}: ${value.toFixed(1)}%`}
                >
                  <Cell fill="#82ca9d" />
                  <Cell fill="#444" />
                </Pie>
                <Tooltip
                  contentStyle={{ backgroundColor: '#1e1e3f', borderColor: '#444', color: '#fff' }}
                />
              </RechartsPieChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EngagementTab;
