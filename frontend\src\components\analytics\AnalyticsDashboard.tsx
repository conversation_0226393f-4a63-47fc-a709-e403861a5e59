import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  BarChart2,
  TrendingUp,
  AlertCircle,
  RefreshCw,
  Download,
  Target,
  Award,
  Users,
  Calendar,
  CheckCircle,
  XCircle
} from 'lucide-react';
import {
  AnalyticsDashboard as AnalyticsDashboardType,
  enhancedAnalyticsAPI
} from '../../services/enhancedAnalyticsApi';
import { BusinessIdea, businessIdeasAPI } from '../../services/incubatorApi';
import { exportAnalyticsToPDF, formatPercentage } from '../../utils/exportUtils';
import PredictiveAnalyticsSection from './PredictiveAnalyticsSection';
import ComparativeAnalyticsSection from './ComparativeAnalyticsSection';
import HistoricalAnalyticsSection from './HistoricalAnalyticsSection';
import RecommendedActionsSection from './RecommendedActionsSection';

interface AnalyticsDashboardProps {
  businessIdeaId: number;
}

const AnalyticsDashboard: React.FC<AnalyticsDashboardProps> = ({ businessIdeaId }) => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const [businessIdea, setBusinessIdea] = useState<BusinessIdea | null>(null);
  const [analytics, setAnalytics] = useState<AnalyticsDashboardType | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    predictive: true,
    comparative: true,
    historical: true,
    recommendations: true
  });

  // Fetch business idea and analytics data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch business idea
        const idea = await businessIdeasAPI.getBusinessIdea(businessIdeaId);
        setBusinessIdea(idea);

        // Fetch analytics dashboard
        const dashboardData = await enhancedAnalyticsAPI.getDashboard(businessIdeaId);
        setAnalytics(dashboardData);

        setError(null);
      } catch (err) {
        console.error('Error fetching analytics data:', err);
        setError(t('incubator.analytics.failedToLoadAnalytics'));
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [businessIdeaId]);

  const handleRefreshAnalytics = async () => {
    if (!analytics?.business_analytics?.id) return;

    try {
      setRefreshing(true);

      // Recalculate analytics
      await enhancedAnalyticsAPI.recalculateAnalytics(analytics.business_analytics.id);

      // Fetch updated dashboard
      const dashboardData = await enhancedAnalyticsAPI.getDashboard(businessIdeaId);
      setAnalytics(dashboardData);

      setError(null);
    } catch (err) {
      console.error('Error refreshing analytics:', err);
      setError(t('incubator.analytics.failedToRefreshAnalytics'));
    } finally {
      setRefreshing(false);
    }
  };

  const handleExportPDF = () => {
    if (!analytics || !businessIdea) return;

    exportAnalyticsToPDF(
      analytics,
      businessIdea.title,
      `${businessIdea.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_analytics_report`
    );
  };

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  if (loading) {
    return (
      <div className="flex justify-center py-12">
        <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-900/30 backdrop-blur-sm rounded-lg p-6 border border-red-800/50 text-center">
        <AlertCircle size={40} className="mx-auto mb-3 text-red-500" />
        <h3 className="text-xl font-bold mb-2">{t('incubator.analytics.errorLoadingAnalytics')}</h3>
        <div className="text-gray-400 mb-4">{error}</div>
        <button
          onClick={() => window.location.reload()}
          className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white inline-flex items-center"
        >
          <RefreshCw size={16} className="mr-1" />
          {t('incubator.analytics.retry')}
        </button>
      </div>
    );
  }

  if (!analytics || !businessIdea) {
    return (
      <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50 text-center">
        <BarChart2 size={40} className="mx-auto mb-3 text-gray-500" />
        <h3 className="text-xl font-bold mb-2">{t('incubator.analytics.noAnalyticsAvailable')}</h3>
        <div className="text-gray-400 mb-4">
          {t('incubator.analytics.analyticsNotAvailable')}
        </div>
      </div>
    );
  }

  // Extract data for dashboard
  const businessAnalytics = analytics.business_analytics;
  const predictiveAnalytics = analytics.predictive_analytics;
  const comparativeAnalytics = analytics.comparative_analytics;
  const dashboardSummary = analytics.dashboard_summary;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">{t('incubator.analytics.dashboard')}</h1>
          <div className="text-gray-400 mt-1">
            {t('incubator.analytics.advancedAnalytics')} {businessIdea.title}
          </div>
        </div>

        <div className="flex space-x-2">
          <button
            onClick={handleExportPDF}
            className="px-3 py-1 bg-indigo-800 hover:bg-indigo-700 rounded-md text-white text-sm flex items-center"
          >
            <Download size={14} className="mr-1" />
            {t('incubator.analytics.exportPDF')}
          </button>

          <button
            onClick={handleRefreshAnalytics}
            disabled={refreshing}
            className="px-3 py-1 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-800/50 disabled:cursor-not-allowed rounded-md text-white text-sm flex items-center"
          >
            <RefreshCw size={14} className={`mr-1 ${refreshing ? 'animate-spin' : ''}`} />
            {refreshing ? t('incubator.analytics.refreshing') : t('incubator.analytics.refreshData')}
          </button>
        </div>
      </div>

      {/* Overall Health Score */}
      <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold">{t('incubator.analytics.overallBusinessHealth')}</h2>

          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-400">{t('incubator.analytics.lastUpdated')} {new Date(businessAnalytics.last_calculated).toLocaleString()}</span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Health Score */}
          <div className="bg-indigo-950/50 rounded-lg p-4 border border-indigo-800/50 flex flex-col items-center justify-center">
            <div className="relative w-32 h-32 mb-3">
              <svg className="w-full h-full" viewBox="0 0 100 100">
                <circle
                  cx="50"
                  cy="50"
                  r="45"
                  fill="none"
                  stroke="#1e293b"
                  strokeWidth="10"
                />
                <circle
                  cx="50"
                  cy="50"
                  r="45"
                  fill="none"
                  stroke={
                    dashboardSummary.overall_health.status === 'excellent' ? '#10b981' :
                    dashboardSummary.overall_health.status === 'good' ? '#3b82f6' :
                    dashboardSummary.overall_health.status === 'fair' ? '#f59e0b' :
                    '#ef4444'
                  }
                  strokeWidth="10"
                  strokeDasharray={`${dashboardSummary.overall_health.score * 2.83} 283`}
                  strokeDashoffset="0"
                  strokeLinecap="round"
                  transform="rotate(-90 50 50)"
                />
                <text
                  x="50"
                  y="50"
                  dominantBaseline="middle"
                  textAnchor="middle"
                  fontSize="24"
                  fontWeight="bold"
                  fill="white"
                >
                  {Math.round(dashboardSummary.overall_health.score)}
                </text>
                <text
                  x="50"
                  y="65"
                  dominantBaseline="middle"
                  textAnchor="middle"
                  fontSize="12"
                  fill="#94a3b8"
                >
                  {t('incubator.analytics.outOf100')}
                </text>
              </svg>
            </div>
            <h3 className="text-lg font-medium">{t('incubator.analytics.healthScore')}</h3>
            <div className={`text-sm ${
              dashboardSummary.overall_health.status === 'excellent' ? 'text-green-400' :
              dashboardSummary.overall_health.status === 'good' ? 'text-blue-400' :
              dashboardSummary.overall_health.status === 'fair' ? 'text-yellow-400' :
              'text-red-400'
            }`}>
              {t(`incubator.analytics.${dashboardSummary.overall_health.status}`)}
            </div>
          </div>

          {/* Key Metrics */}
          <div className="bg-indigo-950/50 rounded-lg p-4 border border-indigo-800/50">
            <h3 className="text-md font-medium mb-3">{t('incubator.analytics.keyMetrics')}</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <TrendingUp size={16} className="text-purple-400 mr-2" />
                  <span className="text-sm">{t('incubator.analytics.progressRate')}</span>
                </div>
                <span className="font-medium">{businessAnalytics.progress_rate} {t('incubator.analytics.updatesPerMonth')}</span>
              </div>

              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <Target size={16} className="text-pink-400 mr-2" />
                  <span className="text-sm">{t('incubator.analytics.milestoneCompletion')}</span>
                </div>
                <span className="font-medium">{formatPercentage(businessAnalytics.milestone_completion_rate)}</span>
              </div>

              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <Award size={16} className="text-blue-400 mr-2" />
                  <span className="text-sm">{t('incubator.analytics.goalAchievement')}</span>
                </div>
                <span className="font-medium">{formatPercentage(businessAnalytics.goal_achievement_rate)}</span>
              </div>

              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <Users size={16} className="text-green-400 mr-2" />
                  <span className="text-sm">{t('incubator.analytics.teamSize')}</span>
                </div>
                <span className="font-medium">{businessAnalytics.team_size}</span>
              </div>

              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <Calendar size={16} className="text-yellow-400 mr-2" />
                  <span className="text-sm">{t('incubator.analytics.mentorEngagement')}</span>
                </div>
                <span className="font-medium">{formatPercentage(businessAnalytics.mentor_engagement)}</span>
              </div>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="bg-indigo-950/50 rounded-lg p-4 border border-indigo-800/50">
            <h3 className="text-md font-medium mb-3">{t('incubator.analytics.recentActivity30Days')}</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm">{t('incubator.analytics.progressUpdates')}</span>
                <span className="font-medium">{dashboardSummary.recent_progress.recent_updates}</span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sm">{t('incubator.analytics.milestonesCompleted')}</span>
                <span className="font-medium">{dashboardSummary.recent_progress.recent_milestones}</span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sm">{t('incubator.analytics.goalsAchieved')}</span>
                <span className="font-medium">{dashboardSummary.recent_progress.recent_goals}</span>
              </div>

              <div className="mt-4 pt-3 border-t border-indigo-800/50">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">{t('incubator.analytics.totalActivities')}</span>
                  <span className="font-bold text-purple-400">{dashboardSummary.recent_progress.total_recent_activities}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Strengths and Weaknesses */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Strengths */}
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
          <h2 className="text-lg font-bold mb-4 flex items-center">
            <CheckCircle size={18} className="text-green-500 mr-2" />
            {t('incubator.analytics.keyStrengths')}
          </h2>

          <div className="space-y-4">
            {dashboardSummary.key_strengths.length > 0 ? (
              dashboardSummary.key_strengths.map((strength, index) => (
                <div key={index} className="bg-green-900/20 rounded-lg p-3 border border-green-800/30">
                  <div className="flex justify-between items-center mb-1">
                    <h3 className="font-medium">{strength.metric}</h3>
                    <span className="text-green-400 font-medium">{
                      strength.metric === 'progress_rate' ? `${strength.value} ${t('incubator.analytics.updatesPerMonth')}` :
                      `${formatPercentage(strength.value)}`
                    }</span>
                  </div>
                  <div className="text-sm text-gray-400">{strength.message}</div>
                </div>
              ))
            ) : (
              <div className="text-center py-4 text-gray-400">
                <div>{t('incubator.analytics.noKeyStrengthsYet')}</div>
                <p className="text-sm mt-1">{t('incubator.analytics.continueWorkingToStrengths')}</p>
              </div>
            )}
          </div>
        </div>

        {/* Weaknesses */}
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
          <h2 className="text-lg font-bold mb-4 flex items-center">
            <XCircle size={18} className="text-red-500 mr-2" />
            {t('incubator.analytics.areasForImprovement')}
          </h2>

          <div className="space-y-4">
            {dashboardSummary.key_weaknesses.length > 0 ? (
              dashboardSummary.key_weaknesses.map((weakness, index) => (
                <div key={index} className="bg-red-900/20 rounded-lg p-3 border border-red-800/30">
                  <div className="flex justify-between items-center mb-1">
                    <h3 className="font-medium">{weakness.metric}</h3>
                    <span className="text-red-400 font-medium">{
                      weakness.metric === 'progress_rate' ? `${weakness.value} ${t('incubator.analytics.updatesPerMonth')}` :
                      `${formatPercentage(weakness.value)}`
                    }</span>
                  </div>
                  <div className="text-sm text-gray-400">{weakness.message}</div>
                </div>
              ))
            ) : (
              <div className="text-center py-4 text-gray-400">
                <p>{t('incubator.analytics.noSignificantWeaknesses')}</p>
                <p className="text-sm mt-1">{t('incubator.analytics.keepUpGoodWork')}</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Predictive Analytics Section */}
      <PredictiveAnalyticsSection
        predictiveAnalytics={predictiveAnalytics}
        expanded={expandedSections.predictive}
        onToggle={() => toggleSection('predictive')}
      />

      {/* Comparative Analytics Section */}
      <ComparativeAnalyticsSection
        comparativeAnalytics={comparativeAnalytics}
        expanded={expandedSections.comparative}
        onToggle={() => toggleSection('comparative')}
      />

      {/* Historical Analytics Section */}
      <HistoricalAnalyticsSection
        historicalSnapshots={businessAnalytics.historical_snapshots || []}
        expanded={expandedSections.historical}
        onToggle={() => toggleSection('historical')}
      />

      {/* Recommended Actions Section */}
      <RecommendedActionsSection
        actions={analytics.recommended_actions || []}
        expanded={expandedSections.recommendations}
        onToggle={() => toggleSection('recommendations')}
      />
    </div>
  );
};

export default AnalyticsDashboard;
