import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import AuthenticatedLayout from '../../components/layout/AuthenticatedLayout';
import { SVGSearchBar, SVGSearchResults } from '../../components/search/SVGSearchComponents';
import { RTLText, RTLFlex } from '../../components/common';
import {
  Search,
  Filter,
  SlidersHorizontal,
  Calendar,
  User,
  Tag,
  TrendingUp,
  Clock,
  BarChart3,
  Bookmark,
  History,
  Zap,
  Target,
  Globe,
  Users,
  FileText,
  MessageSquare,
  Lightbulb,
  X,
  ChevronDown,
  ChevronUp
} from 'lucide-react';

interface SearchFilters {
  types: string[];
  dateRange: string;
  author: string;
  tags: string[];
  difficulty: string;
  category: string;
  rating: number;
  language: string;
}

interface SearchResult {
  id: string;
  type: 'business' | 'forum' | 'resource' | 'user' | 'template';
  title: string;
  description: string;
  score: number;
  metadata?: Record<string, any>;
  author?: string;
  createdAt?: string;
  tags?: string[];
  rating?: number;
  category?: string;
}

const AdvancedSearchPage: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();

  const [query, setQuery] = useState(searchParams.get('q') || '');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [totalResults, setTotalResults] = useState(0);
  const [searchTime, setSearchTime] = useState(0);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [savedSearches, setSavedSearches] = useState<Array<{id: string, name: string, query: string, filters: SearchFilters}>>([]);
  const [searchSuggestions, setSearchSuggestions] = useState<string[]>([]);

  const [filters, setFilters] = useState<SearchFilters>({
    types: [],
    dateRange: '',
    author: '',
    tags: [],
    difficulty: '',
    category: '',
    rating: 0,
    language: ''
  });

  const contentTypes = [
    { id: 'business', name: t('search.types.business'), icon: Lightbulb, color: 'text-blue-400' },
    { id: 'template', name: t('search.types.template'), icon: FileText, color: 'text-green-400' },
    { id: 'forum', name: t('search.types.forum'), icon: MessageSquare, color: 'text-purple-400' },
    { id: 'resource', name: t('search.types.resource'), icon: Target, color: 'text-orange-400' },
    { id: 'user', name: t('search.types.user'), icon: User, color: 'text-pink-400' }
  ];

  const dateRanges = [
    { id: '', name: t('search.dateRange.all') },
    { id: 'today', name: t('search.dateRange.today') },
    { id: 'week', name: t('search.dateRange.week') },
    { id: 'month', name: t('search.dateRange.month') },
    { id: 'year', name: t('search.dateRange.year') }
  ];

  const categories = [
    { id: '', name: t('search.categories.all') },
    { id: 'technology', name: t('search.categories.technology') },
    { id: 'retail', name: t('search.categories.retail') },
    { id: 'services', name: t('search.categories.services') },
    { id: 'hospitality', name: t('search.categories.hospitality') },
    { id: 'healthcare', name: t('search.categories.healthcare') },
    { id: 'education', name: t('search.categories.education') }
  ];

  const popularTags = [
    'AI', 'Machine Learning', 'Startup', 'Business Plan', 'Marketing',
    'Finance', 'Technology', 'Innovation', 'Strategy', 'Growth'
  ];

  // Calculate search relevance score
  const calculateSearchScore = (template: any, query: string): number => {
    let score = 0;
    const queryLower = query.toLowerCase();

    // Title match (highest weight)
    if (template.name?.toLowerCase().includes(queryLower)) {
      score += 50;
    }

    // Description match
    if (template.description?.toLowerCase().includes(queryLower)) {
      score += 30;
    }

    // Tags/industry match
    if (template.industry?.toLowerCase().includes(queryLower)) {
      score += 20;
    }

    // Usage count bonus
    if (template.usage_count > 100) {
      score += 10;
    }

    // Rating bonus
    if (template.rating > 4) {
      score += 5;
    }

    return Math.min(score / 100, 1); // Normalize to 0-1
  };

  useEffect(() => {
    loadRecentSearches();
    loadSavedSearches();

    const initialQuery = searchParams.get('q');
    if (initialQuery) {
      performSearch(initialQuery, filters);
    }
  }, []);

  useEffect(() => {
    if (query.length >= 2) {
      loadSearchSuggestions(query);
    } else {
      setSearchSuggestions([]);
    }
  }, [query]);

  const loadRecentSearches = () => {
    const recent = JSON.parse(localStorage.getItem('recentSearches') || '[]');
    setRecentSearches(recent.slice(0, 5));
  };

  const loadSavedSearches = () => {
    const saved = JSON.parse(localStorage.getItem('savedSearches') || '[]');
    setSavedSearches(saved);
  };

  const loadSearchSuggestions = async (searchQuery: string) => {
    try {
      // Get real search suggestions from templates
      const { businessPlanTemplatesAPI } = await import('../../services/templateCustomizationApi');
      const templates = await businessPlanTemplatesAPI.getTemplates({ search: searchQuery });

      // Generate suggestions from real template data
      const suggestions = templates
        .slice(0, 5) // Limit to 5 suggestions
        .map(template => template.name)
        .filter(name => name && name.toLowerCase().includes(searchQuery.toLowerCase()));

      // Add some generic suggestions if we don't have enough
      const genericSuggestions = [
        `${searchQuery} business plan`,
        `${searchQuery} template`,
        `${searchQuery} strategy`
      ];

      const allSuggestions = [...suggestions, ...genericSuggestions]
        .slice(0, 5) // Limit to 5 total suggestions
        .filter((suggestion, index, arr) => arr.indexOf(suggestion) === index); // Remove duplicates

      setSearchSuggestions(allSuggestions);
    } catch (error) {
      console.error('Error loading suggestions:', error);
      // Fallback to empty suggestions on error
      setSearchSuggestions([]);
    }
  };

  const performSearch = async (searchQuery: string, searchFilters: SearchFilters) => {
    if (!searchQuery.trim()) return;

    setIsLoading(true);
    const startTime = Date.now();

    try {
      // Update URL
      const params = new URLSearchParams();
      params.set('q', searchQuery);
      if (searchFilters.types.length) params.set('types', searchFilters.types.join(','));
      if (searchFilters.dateRange) params.set('dateRange', searchFilters.dateRange);
      if (searchFilters.author) params.set('author', searchFilters.author);
      if (searchFilters.category) params.set('category', searchFilters.category);
      setSearchParams(params);

      // Perform real search using API
      const { businessPlanTemplatesAPI } = await import('../../services/templateCustomizationApi');
      const templates = await businessPlanTemplatesAPI.getTemplates({
        search: searchQuery,
        industry: filters.category !== 'all' ? filters.category : undefined,
        template_type: filters.type !== 'all' ? filters.type : undefined
      });

      // Transform templates to search results
      const searchResults: SearchResult[] = templates
        .filter(template => template.name && template.description)
        .map(template => ({
          id: template.id?.toString() || `template_${Date.now()}`,
          type: 'template' as const,
          title: template.name,
          description: template.description,
          score: calculateSearchScore(template, searchQuery),
          author: template.author || t("dashboard.business.expert", "Business Expert"),
          createdAt: template.created_at || new Date().toISOString(),
          tags: template.tags || [template.industry, template.template_type].filter(Boolean),
          rating: template.rating || 0,
          category: template.industry?.toLowerCase() || 'general'
        }))
        .sort((a, b) => b.score - a.score);

      // Filter results based on filters
      let filteredResults = searchResults;

      if (searchFilters.types.length > 0) {
        filteredResults = filteredResults.filter(result => searchFilters.types.includes(result.type));
      }

      if (searchFilters.category) {
        filteredResults = filteredResults.filter(result => result.category === searchFilters.category);
      }

      setResults(filteredResults);
      setTotalResults(filteredResults.length);
      setSearchTime(Date.now() - startTime);

      // Save to recent searches
      saveRecentSearch(searchQuery);

    } catch (error) {
      console.error('Search error:', error);
      setResults([]);
      setTotalResults(0);
    } finally {
      setIsLoading(false);
    }
  };

  const saveRecentSearch = (searchQuery: string) => {
    const recent = JSON.parse(localStorage.getItem('recentSearches') || '[]');
    const updated = [searchQuery, ...recent.filter(q => q !== searchQuery)].slice(0, 10);
    localStorage.setItem('recentSearches', JSON.stringify(updated));
    setRecentSearches(updated.slice(0, 5));
  };

  const saveCurrentSearch = () => {
    const searchName = prompt(t('search.saveSearchPrompt'));
    if (searchName) {
      const newSavedSearch = {
        id: Date.now().toString(),
        name: searchName,
        query,
        filters
      };
      const updated = [...savedSearches, newSavedSearch];
      localStorage.setItem('savedSearches', JSON.stringify(updated));
      setSavedSearches(updated);
    }
  };

  const loadSavedSearch = (savedSearch: any) => {
    setQuery(savedSearch.query);
    setFilters(savedSearch.filters);
    performSearch(savedSearch.query, savedSearch.filters);
  };

  const deleteSavedSearch = (searchId: string) => {
    const updated = savedSearches.filter(s => s.id !== searchId);
    localStorage.setItem('savedSearches', JSON.stringify(updated));
    setSavedSearches(updated);
  };

  const handleFilterChange = (key: keyof SearchFilters, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);

    if (query) {
      performSearch(query, newFilters);
    }
  };

  const toggleContentType = (type: string) => {
    const newTypes = filters.types.includes(type)
      ? filters.types.filter(t => t !== type)
      : [...filters.types, type];
    handleFilterChange('types', newTypes);
  };

  const addTag = (tag: string) => {
    if (!filters.tags.includes(tag)) {
      handleFilterChange('tags', [...filters.tags, tag]);
    }
  };

  const removeTag = (tag: string) => {
    handleFilterChange('tags', filters.tags.filter(t => t !== tag));
  };

  const clearAllFilters = () => {
    const clearedFilters: SearchFilters = {
      types: [],
      dateRange: '',
      author: '',
      tags: [],
      difficulty: '',
      category: '',
      rating: 0,
      language: ''
    };
    setFilters(clearedFilters);
    if (query) {
      performSearch(query, clearedFilters);
    }
  };

  const handleResultClick = (result: SearchResult) => {
    switch (result.type) {
      case 'business':
        navigate(`/dashboard/business-ideas`);
        break;
      case 'template':
        navigate(`/dashboard/templates`);
        break;
      case 'forum':
        navigate(`/forum`);
        break;
      case 'resource':
        navigate(`/dashboard/resources`);
        break;
      case 'user':
        navigate(`/dashboard`);
        break;
    }
  };

  return (
    <AuthenticatedLayout>
      <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="p-6 space-y-6">
        {/* Header */}
        <div className="text-center">
          <RTLText as="h1" className="text-3xl font-bold text-white mb-2">
            {t('search.advanced.title')}
          </RTLText>
          <RTLText className="text-gray-300">
            {t('search.advanced.description')}
          </RTLText>
        </div>

        {/* Search Bar */}
        <div className="max-w-4xl mx-auto">
          <SVGSearchBar
            onSearch={(q, f) => {
              setQuery(q);
              performSearch(q, { ...filters, ...f });
            }}
            placeholder={t('search.advanced.placeholder')}
            animated={true}
          />
        </div>

        {/* Quick Actions */}
        <div className={`flex flex-wrap gap-2 justify-center ${isRTL ? "flex-row-reverse" : ""}`}>
          <button
            onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
            className={`flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <SlidersHorizontal size={16} />
            {t('search.advanced.filters')}
            {showAdvancedFilters ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
          </button>

          {query && (
            <button
              onClick={saveCurrentSearch}
              className={`flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
            >
              <Bookmark size={16} />
              {t('search.saveSearch')}
            </button>
          )}

          <button
            onClick={clearAllFilters}
            className={`flex items-center gap-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <X size={16} />
            {t('search.clearFilters')}
          </button>
        </div>

        {/* Advanced Filters */}
        {showAdvancedFilters && (
          <div className="max-w-6xl mx-auto bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 p-6 space-y-6">
            {/* Content Types */}
            <div>
              <RTLText as="h3" className="text-lg font-semibold text-white mb-3">
                {t('search.contentTypes')}
              </RTLText>
              <div className={`flex flex-wrap gap-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                {contentTypes.map(type => (
                  <button
                    key={type.id}
                    onClick={() => toggleContentType(type.id)}
                    className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-colors ${
                      filters.types.includes(type.id)
                        ? 'bg-purple-600 text-white'
                        : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}
                    }`}
                  >
                    <type.icon size={16} className={type.color} />
                    {type.name}
                  </button>
                ))}
              </div>
            </div>

            {/* Date Range and Category */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {t('search.dateRange.label')}
                </label>
                <select
                  value={filters.dateRange}
                  onChange={(e) => handleFilterChange('dateRange', e.target.value)}
                  className="w-full p-3 bg-white/20 border border-white/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  {dateRanges.map(range => (
                    <option key={range.id} value={range.id}>
                      {range.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {t('search.category')}
                </label>
                <select
                  value={filters.category}
                  onChange={(e) => handleFilterChange('category', e.target.value)}
                  className="w-full p-3 bg-white/20 border border-white/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Tags */}
            <div>
              <RTLText as="h3" className="text-lg font-semibold text-white mb-3">
                {t('search.tags')}
              </RTLText>

              {/* Selected Tags */}
              {filters.tags.length > 0 && (
                <div className={`flex flex-wrap gap-2 mb-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                  {filters.tags.map(tag => (
                    <span
                      key={tag}
                      className={`flex items-center gap-1 px-3 py-1 bg-purple-600 text-white rounded-full text-sm ${isRTL ? "flex-row-reverse" : ""}`}
                    >
                      {tag}
                      <button
                        onClick={() => removeTag(tag)}
                        className="hover:text-red-300 transition-colors"
                      >
                        <X size={14} />
                      </button>
                    </span>
                  ))}
                </div>
              )}

              {/* Popular Tags */}
              <div className={`flex flex-wrap gap-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                {popularTags.map(tag => (
                  <button
                    key={tag}
                    onClick={() => addTag(tag)}
                    disabled={filters.tags.includes(tag)}
                    className={`px-3 py-1 rounded-full text-sm transition-colors ${
                      filters.tags.includes(tag)
                        ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                        : 'bg-white/20 text-gray-300 hover:bg-white/30'}
                    }`}
                  >
                    {tag}
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Sidebar with Recent and Saved Searches */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar */}
          <div className="space-y-6">
            {/* Recent Searches */}
            {recentSearches.length > 0 && (
              <div className="bg-gray-800/50 rounded-lg border border-gray-700 p-4">
                <div className={`flex items-center gap-2 mb-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <History size={16} className="text-gray-400" />
                  <RTLText as="h3" className="font-semibold text-white">
                    {t('search.recentSearches')}
                  </RTLText>
                </div>
                <div className="space-y-2">
                  {recentSearches.map((search, index) => (
                    <button
                      key={index}
                      onClick={() => {
                        setQuery(search);
                        performSearch(search, filters);
                      }}
                      className={`block w-full  px-3 py-2 text-gray-300 hover:bg-gray-700 rounded-md transition-colors ${isRTL ? "text-right" : "text-left"}`}
                    >
                      {search}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Saved Searches */}
            {savedSearches.length > 0 && (
              <div className="bg-gray-800/50 rounded-lg border border-gray-700 p-4">
                <div className={`flex items-center gap-2 mb-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <Bookmark size={16} className="text-blue-400" />
                  <RTLText as="h3" className="font-semibold text-white">
                    {t('search.savedSearches')}
                  </RTLText>
                </div>
                <div className="space-y-2">
                  {savedSearches.map((search) => (
                    <div key={search.id} className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
                      <button
                        onClick={() => loadSavedSearch(search)}
                        className={`flex-1 text-left px-3 py-2 text-gray-300 hover:bg-gray-700 rounded-md transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
                      >
                        {search.name}
                      </button>
                      <button
                        onClick={() => deleteSavedSearch(search.id)}
                        className="p-1 text-gray-400 hover:text-red-400 transition-colors"
                      >
                        <X size={14} />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Main Results */}
          <div className="lg:col-span-3">
            {/* Search Stats */}
            {(results.length > 0 || isLoading) && (
              <div className="mb-6">
                <div className={`flex items-center justify-between text-sm text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <span>
                    {isLoading
                      ? t('search.searching')
                      : t('search.resultsFound', { count: totalResults, time: searchTime })
                    }
                  </span>
                  {!isLoading && (
                    <div className={`flex items-center space-x-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <span className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                        <TrendingUp size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                        {t('search.relevanceSort')}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Results */}
            <SVGSearchResults
              results={results}
              isLoading={isLoading}
              onResultClick={handleResultClick}
            />

            {/* Empty State */}
            {!isLoading && results.length === 0 && query && (
              <div className="text-center py-12">
                <Search size={48} className="mx-auto text-gray-500 mb-4" />
                <RTLText as="h3" className="text-xl font-semibold text-gray-300 mb-2">
                  {t('search.noResults')}
                </RTLText>
                <RTLText className="text-gray-400 mb-6">
                  {t('search.noResultsDescription')}
                </RTLText>
                <button
                  onClick={clearAllFilters}
                  className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg text-white transition-colors"
                >
                  {t('search.clearFilters')}
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
              </div>
        </div>
      </div>
    </AuthenticatedLayout>
  );
};

export default AdvancedSearchPage;
