import React, { useState, useEffect } from 'react';
import { Target, Calendar, AlertCircle, FileText } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../hooks/useLanguage';
import { useCreateBusinessMilestone, useUpdateBusinessMilestone } from '../../../../hooks/useMilestones';
import { BusinessMilestone } from '../../../../services/milestoneApi';
import { Button } from '../../../ui';
import { BaseModal } from '../../../common';

interface MilestoneModalProps {
  isOpen: boolean;
  onClose: () => void;
  milestone: BusinessMilestone | null;
  isEditing: boolean;
}

interface FormData {
  title: string;
  description: string;
  business_idea: number;
  due_date: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'not_started' | 'in_progress' | 'completed' | 'delayed' | 'cancelled';
  completion_criteria: string;
  estimated_hours: number;
  dependencies: string;
}

const MilestoneModal: React.FC<MilestoneModalProps> = ({
  isOpen,
  onClose,
  milestone,
  isEditing
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  const createMilestone = useCreateBusinessMilestone();
  const updateMilestone = useUpdateBusinessMilestone();

  const [formData, setFormData] = useState<FormData>({
    title: '',
    description: '',
    business_idea: 0,
    due_date: '',
    priority: 'medium',
    status: 'not_started',
    completion_criteria: '',
    estimated_hours: 0,
    dependencies: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form data when milestone changes
  useEffect(() => {
    if (isEditing && milestone) {
      setFormData({
        title: milestone.title || '',
        description: milestone.description || '',
        business_idea: milestone.business_idea || 0,
        due_date: milestone.due_date ? milestone.due_date.split('T')[0] : '',
        priority: milestone.priority || 'medium',
        status: milestone.status || 'not_started',
        completion_criteria: milestone.completion_criteria || '',
        estimated_hours: milestone.estimated_hours || 0,
        dependencies: milestone.dependencies || ''
      });
    } else {
      // Reset form for create mode
      setFormData({
        title: '',
        description: '',
        business_idea: 0,
        due_date: '',
        priority: 'medium',
        status: 'not_started',
        completion_criteria: '',
        estimated_hours: 0,
        dependencies: ''
      });
    }
    setErrors({});
  }, [isEditing, milestone]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? Number(value) : value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = t('validation.required', 'This field is required');
    }
    if (!formData.description.trim()) {
      newErrors.description = t('validation.required', 'This field is required');
    }
    if (!formData.business_idea) {
      newErrors.business_idea = t('validation.required', 'This field is required');
    }
    if (!formData.due_date) {
      newErrors.due_date = t('validation.required', 'This field is required');
    }
    if (formData.estimated_hours < 0) {
      newErrors.estimated_hours = t('validation.positiveNumber', 'Must be a positive number');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      if (isEditing && milestone) {
        await updateMilestone.mutateAsync({
          id: milestone.id,
          data: formData
        });
      } else {
        await createMilestone.mutateAsync(formData);
      }
      onClose();
    } catch (error) {
      console.error('Failed to save milestone:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const modalTitle = isEditing ? t('milestone.editMilestone', 'Edit Milestone') : t('milestone.createMilestone', 'Create Milestone');

  const modalFooter = (
    <div className="flex justify-end gap-3">
      <Button
        type="button"
        onClick={onClose}
        variant="secondary"
        disabled={isSubmitting}
      >
        {t('common.cancel', 'Cancel')}
      </Button>
      <Button
        type="submit"
        form="milestone-form"
        disabled={isSubmitting}
      >
        {isSubmitting ? t('common.saving', 'Saving...') :
         isEditing ? t('common.update', 'Update') : t('common.create', 'Create')}
      </Button>
    </div>
  );

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title={modalTitle}
      size="lg"
      footer={modalFooter}
    >
      <form id="milestone-form" onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <div>
              <label className="block text-gray-300 mb-2">
                {t('milestone.title', 'Milestone Title')} *
              </label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                placeholder={t('milestone.titlePlaceholder', 'Enter milestone title')}
              />
              {errors.title && <p className="text-red-400 text-sm mt-1">{errors.title}</p>}
            </div>

            <div>
              <label className="block text-gray-300 mb-2">
                {t('milestone.description', 'Description')} *
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                placeholder={t('milestone.descriptionPlaceholder', 'Describe what needs to be accomplished...')}
              />
              {errors.description && <p className="text-red-400 text-sm mt-1">{errors.description}</p>}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-gray-300 mb-2">
                  {t('milestone.businessIdea', 'Business Idea ID')} *
                </label>
                <input
                  type="number"
                  name="business_idea"
                  value={formData.business_idea}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                  placeholder="Business Idea ID"
                />
                {errors.business_idea && <p className="text-red-400 text-sm mt-1">{errors.business_idea}</p>}
              </div>

              <div>
                <label className="block text-gray-300 mb-2">
                  {t('milestone.dueDate', 'Due Date')} *
                </label>
                <input
                  type="date"
                  name="due_date"
                  value={formData.due_date}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                />
                {errors.due_date && <p className="text-red-400 text-sm mt-1">{errors.due_date}</p>}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-gray-300 mb-2">
                  {t('milestone.priority', 'Priority')}
                </label>
                <select
                  name="priority"
                  value={formData.priority}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                >
                  <option value="low">{t('milestone.priority.low', 'Low')}</option>
                  <option value="medium">{t('milestone.priority.medium', 'Medium')}</option>
                  <option value="high">{t('milestone.priority.high', 'High')}</option>
                  <option value="critical">{t('milestone.priority.critical', 'Critical')}</option>
                </select>
              </div>

              <div>
                <label className="block text-gray-300 mb-2">
                  {t('admin.status', 'Status')}
                </label>
                <select
                  name="status"
                  value={formData.status}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                >
                  <option value="not_started">{t('milestone.status.notStarted', 'Not Started')}</option>
                  <option value="in_progress">{t('milestone.status.inProgress', 'In Progress')}</option>
                  <option value="completed">{t('milestone.status.completed', 'Completed')}</option>
                  <option value="delayed">{t('milestone.status.delayed', 'Delayed')}</option>
                  <option value="cancelled">{t('milestone.status.cancelled', 'Cancelled')}</option>
                </select>
              </div>

              <div>
                <label className="block text-gray-300 mb-2">
                  {t('milestone.estimatedHours', 'Estimated Hours')}
                </label>
                <input
                  type="number"
                  name="estimated_hours"
                  value={formData.estimated_hours}
                  onChange={handleInputChange}
                  min="0"
                  step="0.5"
                  className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                />
                {errors.estimated_hours && <p className="text-red-400 text-sm mt-1">{errors.estimated_hours}</p>}
              </div>
            </div>

            <div>
              <label className="block text-gray-300 mb-2">
                {t('milestone.completionCriteria', 'Completion Criteria')}
              </label>
              <textarea
                name="completion_criteria"
                value={formData.completion_criteria}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                placeholder={t('milestone.completionCriteriaPlaceholder', 'Define what constitutes completion of this milestone...')}
              />
            </div>

            <div>
              <label className="block text-gray-300 mb-2">
                {t('milestone.dependencies', 'Dependencies')}
              </label>
              <textarea
                name="dependencies"
                value={formData.dependencies}
                onChange={handleInputChange}
                rows={2}
                className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                placeholder={t('milestone.dependenciesPlaceholder', 'List any dependencies or prerequisites...')}
              />
            </div>

          </form>
    </BaseModal>
  );
};

export default MilestoneModal;
