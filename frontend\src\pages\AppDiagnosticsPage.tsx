import React, { useState, useEffect } from 'react';
import { useAppSelector, useAppDispatch } from '../store/hooks';
import { getCurrentUser } from '../store/authSlice';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../hooks/useLanguage';

// Import test components
import { LoadingFallback } from '../components/ui';
import { RTLContainer, RTLFlex, RTLIcon } from '../components/rtl';
import { LanguageSwitcher } from '../components/common';

// Import icons
import { 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  Loader, 
  User, 
  Settings,
  Database,
  Wifi,
  Shield,
  Zap,
  Globe,
  Smartphone,
  Home,
  LogIn,
  RefreshCw
} from 'lucide-react';

// Import API functions for testing
import { authAPI, api } from '../services/api';
import { roleBasedApi } from '../services/roleBasedApi';

interface TestResult {
  name: string;
  status: 'success' | 'error' | 'warning' | 'loading';
  message: string;
  details?: any;
}

const AppDiagnosticsPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const dispatch = useAppDispatch();
  const { user, isAuthenticated, isLoading, error } = useAppSelector(state => state.auth);

  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunningTests, setIsRunningTests] = useState(false);

  const updateTestResult = (name: string, status: TestResult['status'], message: string, details?: any) => {
    setTestResults(prev => {
      const existing = prev.find(r => r.name === name);
      const newResult = { name, status, message, details };
      
      if (existing) {
        return prev.map(r => r.name === name ? newResult : r);
      } else {
        return [...prev, newResult];
      }
    });
  };

  const runDiagnostics = async () => {
    setIsRunningTests(true);
    setTestResults([]);

    // Test 1: Frontend Basic Functionality
    updateTestResult('Frontend', 'loading', 'Testing frontend components...');
    try {
      // Test if React is working
      const reactVersion = React.version;
      updateTestResult('Frontend', 'success', `React ${reactVersion} is working correctly`);
    } catch (error) {
      updateTestResult('Frontend', 'error', 'Frontend components failed to load', error);
    }

    // Test 2: Backend Connectivity
    updateTestResult('Backend', 'loading', 'Testing backend connectivity...');
    try {
      const response = await api.get('/api/ai/ping/');
      updateTestResult('Backend', 'success', 'Backend is responding correctly', response);
    } catch (error) {
      updateTestResult('Backend', 'error', 'Backend connectivity failed', error);
    }

    // Test 3: Authentication System
    updateTestResult('Authentication', 'loading', 'Testing authentication system...');
    try {
      if (isAuthenticated && user) {
        updateTestResult('Authentication', 'success', `Authenticated as ${user.username}`, user);
      } else {
        updateTestResult('Authentication', 'warning', 'Not authenticated - this is normal for public access');
      }
    } catch (error) {
      updateTestResult('Authentication', 'error', 'Authentication system error', error);
    }

    // Test 4: Internationalization
    updateTestResult('i18n', 'loading', 'Testing internationalization...');
    try {
      const testTranslation = t('nav.home', 'Home');
      updateTestResult('i18n', 'success', `i18n working - RTL: ${isRTL}, Translation: "${testTranslation}"`);
    } catch (error) {
      updateTestResult('i18n', 'error', 'Internationalization failed', error);
    }

    // Test 5: Role-based API
    updateTestResult('Roles', 'loading', 'Testing role-based functionality...');
    try {
      const userRoles = await roleBasedApi.getUserRoles();
      updateTestResult('Roles', 'success', `Role system working - Roles: ${userRoles.join(', ')}`, userRoles);
    } catch (error) {
      updateTestResult('Roles', 'warning', 'Role system test failed (may require authentication)', error);
    }

    // Test 6: Local Storage
    updateTestResult('Storage', 'loading', 'Testing local storage...');
    try {
      localStorage.setItem('test_key', 'test_value');
      const testValue = localStorage.getItem('test_key');
      localStorage.removeItem('test_key');
      
      if (testValue === 'test_value') {
        updateTestResult('Storage', 'success', 'Local storage is working correctly');
      } else {
        updateTestResult('Storage', 'error', 'Local storage test failed');
      }
    } catch (error) {
      updateTestResult('Storage', 'error', 'Local storage is not available', error);
    }

    setIsRunningTests(false);
  };

  useEffect(() => {
    runDiagnostics();
  }, []);

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="text-green-500" size={20} />;
      case 'error':
        return <XCircle className="text-red-500" size={20} />;
      case 'warning':
        return <AlertCircle className="text-yellow-500" size={20} />;
      case 'loading':
        return <Loader className="text-blue-500 animate-spin" size={20} />;
      default:
        return <AlertCircle className="text-gray-500" size={20} />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return 'border-green-200 bg-green-50';
      case 'error':
        return 'border-red-200 bg-red-50';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50';
      case 'loading':
        return 'border-blue-200 bg-blue-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 p-6">
      <RTLContainer className="max-w-4xl mx-auto">
        <div className="glass-light rounded-xl p-8">
          <RTLFlex className="items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-glass-primary mb-2">
                App Diagnostics
              </h1>
              <p className="text-glass-secondary">
                Testing all application components and functionality
              </p>
            </div>
            <button
              onClick={runDiagnostics}
              disabled={isRunningTests}
              className="flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 transition-colors"
            >
              <RefreshCw className={isRunningTests ? 'animate-spin' : ''} size={16} />
              {isRunningTests ? 'Running Tests...' : 'Run Tests'}
            </button>
          </RTLFlex>

          <div className="space-y-4">
            {testResults.map((result, index) => (
              <div
                key={result.name}
                className={`p-4 rounded-lg border-2 transition-all duration-300 ${getStatusColor(result.status)}`}
              >
                <RTLFlex className="items-center justify-between">
                  <RTLFlex className="items-center gap-3">
                    {getStatusIcon(result.status)}
                    <div>
                      <h3 className="font-semibold text-gray-800">{result.name}</h3>
                      <p className="text-sm text-gray-600">{result.message}</p>
                    </div>
                  </RTLFlex>
                  
                  {result.details && (
                    <details className="text-xs">
                      <summary className="cursor-pointer text-gray-500">Details</summary>
                      <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto max-w-md">
                        {JSON.stringify(result.details, null, 2)}
                      </pre>
                    </details>
                  )}
                </RTLFlex>
              </div>
            ))}
          </div>

          {testResults.length === 0 && !isRunningTests && (
            <div className="text-center py-8">
              <p className="text-gray-500">Click "Run Tests" to start diagnostics</p>
            </div>
          )}

          <div className="mt-8 p-4 bg-blue-50 rounded-lg">
            <h3 className="font-semibold text-blue-800 mb-2">System Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <strong>User Agent:</strong> {navigator.userAgent.substring(0, 50)}...
              </div>
              <div>
                <strong>Language:</strong> {navigator.language}
              </div>
              <div>
                <strong>Online:</strong> {navigator.onLine ? 'Yes' : 'No'}
              </div>
              <div>
                <strong>Current URL:</strong> {window.location.href}
              </div>
            </div>
          </div>

          <div className="mt-6">
            <LanguageSwitcher variant="dropdown" />
          </div>
        </div>
      </RTLContainer>
    </div>
  );
};

export default AppDiagnosticsPage;
