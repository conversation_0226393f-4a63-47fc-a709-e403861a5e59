from django.db.models.signals import post_save, m2m_changed
from django.dispatch import receiver
from django.db import transaction
from django.contrib.auth.models import User
from .models import ForumThread, ForumPost, UserReputation, ReputationActivity

@receiver(post_save, sender=User)
def create_user_reputation(sender, instance, created, **kwargs):
    """Create a UserReputation instance when a new user is created"""
    if created:
        UserReputation.objects.create(user=instance)

@receiver(post_save, sender=ForumThread)
def update_topic_last_activity(sender, instance, **kwargs):
    """Update the topic's last_activity timestamp when a thread is updated"""
    instance.topic.save()  # This will update the updated_at field

@receiver(post_save, sender=ForumPost)
def update_thread_last_activity(sender, instance, **kwargs):
    """Update the thread's last_activity timestamp when a post is created/updated"""
    instance.thread.last_activity = instance.created_at
    instance.thread.save(update_fields=['last_activity'])

    # Also update the topic
    instance.thread.topic.save()

@receiver(m2m_changed, sender=ForumPost.likes.through)
def handle_post_likes_changed(sender, instance, action, pk_set, **kwargs):
    """Handle reputation updates when a post's likes change"""
    if action == 'post_add':
        # A user liked the post
        for user_id in pk_set:
            user = User.objects.get(id=user_id)

            # Update reputation for the post author (receiving a like)
            try:
                with transaction.atomic():
                    # Get or create user reputation
                    reputation, created = UserReputation.objects.get_or_create(user=instance.author)

                    # Update reputation stats
                    reputation.likes_received += 1
                    reputation.points += 1  # Award 1 point for receiving a like
                    reputation.save()

                    # Update level if needed
                    reputation.update_level()

                    # Record the activity
                    ReputationActivity.objects.create(
                        user=instance.author,
                        activity_type='like_received',
                        points=1,
                        post=instance,
                        thread=instance.thread,
                        description=f"Received like from {user.username} on post in thread: {instance.thread.title}"
                    )
            except Exception as e:
                print(f"Error updating reputation for like received: {e}")

            # Update reputation for the user giving the like
            try:
                with transaction.atomic():
                    # Get or create user reputation for the user giving the like
                    giver_reputation, created = UserReputation.objects.get_or_create(user=user)

                    # Update reputation stats
                    giver_reputation.likes_given += 1
                    giver_reputation.save()

                    # Record the activity
                    ReputationActivity.objects.create(
                        user=user,
                        activity_type='like_given',
                        points=0,  # No points for giving likes
                        post=instance,
                        thread=instance.thread,
                        description=f"Gave like to {instance.author.username} on post in thread: {instance.thread.title}"
                    )
            except Exception as e:
                print(f"Error updating reputation for like given: {e}")

    elif action == 'post_remove':
        # A user unliked the post
        for user_id in pk_set:
            user = User.objects.get(id=user_id)

            # Update reputation for the post author (losing a like)
            try:
                with transaction.atomic():
                    # Get user reputation
                    reputation = UserReputation.objects.get(user=instance.author)

                    # Update reputation stats
                    reputation.likes_received -= 1
                    reputation.points -= 1  # Remove 1 point for losing a like
                    reputation.save()

                    # Update level if needed
                    reputation.update_level()
            except Exception as e:
                print(f"Error updating reputation for unlike: {e}")

            # Update reputation for the user removing the like
            try:
                with transaction.atomic():
                    # Get user reputation for the user removing the like
                    giver_reputation = UserReputation.objects.get(user=user)

                    # Update reputation stats
                    giver_reputation.likes_given -= 1
                    giver_reputation.save()
            except Exception as e:
                print(f"Error updating reputation for unlike given: {e}")
