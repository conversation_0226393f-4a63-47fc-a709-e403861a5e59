from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.db import transaction
from django.db.models import Q


class Command(BaseCommand):
    help = 'Delete all users from the database (except superusers for safety)'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force deletion of all users including superusers',
        )
        parser.add_argument(
            '--keep-superusers',
            action='store_true',
            help='Keep superuser accounts (default behavior)',
        )

    def handle(self, *args, **options):
        force = options['force']
        keep_superusers = options.get('keep_superusers', True)

        self.stdout.write(self.style.WARNING('🚨 WARNING: This will delete users from the database!'))
        
        if force:
            self.stdout.write(self.style.ERROR('⚠️  FORCE MODE: Will delete ALL users including superusers!'))
        elif keep_superusers:
            self.stdout.write(self.style.SUCCESS('🛡️  SAFE MODE: Will keep superuser accounts'))

        # Count users before deletion
        total_users = User.objects.count()
        if keep_superusers and not force:
            users_to_delete = User.objects.filter(is_superuser=False)
        else:
            users_to_delete = User.objects.all()

        users_count = users_to_delete.count()
        superusers_count = User.objects.filter(is_superuser=True).count()

        self.stdout.write(f'📊 Current database state:')
        self.stdout.write(f'   Total users: {total_users}')
        self.stdout.write(f'   Superusers: {superusers_count}')
        self.stdout.write(f'   Users to delete: {users_count}')

        if users_count == 0:
            self.stdout.write(self.style.SUCCESS('✅ No users to delete!'))
            return

        # Confirm deletion
        confirm = input(f'\n❓ Are you sure you want to delete {users_count} users? (yes/no): ')
        if confirm.lower() != 'yes':
            self.stdout.write(self.style.WARNING('❌ Operation cancelled.'))
            return

        self.stdout.write(self.style.WARNING('🔄 Starting deletion process...'))

        try:
            # Disable foreign key constraints for SQLite
            from django.db import connection
            with connection.cursor() as cursor:
                cursor.execute("PRAGMA foreign_keys = OFF;")

            with transaction.atomic():
                # Clean up related data first to avoid foreign key constraints
                self.stdout.write('🧹 Cleaning up related data...')
                
                # Clean up forum-related data
                try:
                    from forums.models import ForumThread, ForumPost, UserReputation, ReputationActivity
                    
                    # Delete forum posts and threads
                    ForumPost.objects.filter(author__in=users_to_delete).delete()
                    ForumThread.objects.filter(author__in=users_to_delete).delete()
                    
                    # Delete reputation data
                    UserReputation.objects.filter(user__in=users_to_delete).delete()
                    ReputationActivity.objects.filter(user__in=users_to_delete).delete()
                    
                    self.stdout.write('   ✅ Forum data cleaned')
                except ImportError:
                    self.stdout.write('   ⚠️  Forum models not found, skipping...')

                # Clean up API-related data
                try:
                    from api.models import Post, Comment, Event, Resource

                    # Delete posts and comments instead of updating (due to NOT NULL constraints)
                    posts_count = Post.objects.filter(author__in=users_to_delete).count()
                    comments_count = Comment.objects.filter(author__in=users_to_delete).count()

                    Post.objects.filter(author__in=users_to_delete).delete()
                    Comment.objects.filter(author__in=users_to_delete).delete()

                    self.stdout.write(f'   🗑️  Deleted {posts_count} posts and {comments_count} comments')

                    # Remove users from event attendees
                    events_updated = 0
                    for event in Event.objects.all():
                        attendees_to_remove = [user for user in users_to_delete if user in event.attendees.all()]
                        if attendees_to_remove:
                            event.attendees.remove(*attendees_to_remove)
                            events_updated += 1

                    self.stdout.write(f'   👥 Updated {events_updated} events (removed attendees)')

                    # Delete events organized by users to be deleted
                    events_count = Event.objects.filter(organizer__in=users_to_delete).count()
                    Event.objects.filter(organizer__in=users_to_delete).delete()
                    self.stdout.write(f'   🗑️  Deleted {events_count} events')

                    # Delete resources authored by users to be deleted
                    resources_count = Resource.objects.filter(author__in=users_to_delete).count()
                    Resource.objects.filter(author__in=users_to_delete).delete()
                    self.stdout.write(f'   🗑️  Deleted {resources_count} resources')

                    self.stdout.write('   ✅ API data cleaned')
                except ImportError:
                    self.stdout.write('   ⚠️  API models not found, skipping...')
                except Exception as e:
                    self.stdout.write(f'   ⚠️  Error cleaning API data: {e}')

                # Clean up incubator-related data
                try:
                    from incubator.models import (
                        BusinessIdea, BusinessPlan, FundingOpportunity, 
                        MentorProfile, InvestorProfile, MentorshipApplication,
                        MentorshipMatch, MentorshipSession, ProgressUpdate,
                        FundingApplication, BusinessMilestone
                    )
                    
                    # Delete incubator data
                    BusinessIdea.objects.filter(creator__in=users_to_delete).delete()
                    BusinessPlan.objects.filter(creator__in=users_to_delete).delete()
                    MentorProfile.objects.filter(user__in=users_to_delete).delete()
                    InvestorProfile.objects.filter(user__in=users_to_delete).delete()
                    MentorshipApplication.objects.filter(applicant__in=users_to_delete).delete()
                    MentorshipMatch.objects.filter(
                        Q(mentee__in=users_to_delete) | Q(mentor__user__in=users_to_delete)
                    ).delete()
                    MentorshipSession.objects.filter(
                        Q(mentee__in=users_to_delete) | Q(mentor__user__in=users_to_delete)
                    ).delete()
                    ProgressUpdate.objects.filter(creator__in=users_to_delete).delete()
                    FundingApplication.objects.filter(applicant__in=users_to_delete).delete()
                    BusinessMilestone.objects.filter(creator__in=users_to_delete).delete()
                    
                    self.stdout.write('   ✅ Incubator data cleaned')
                except ImportError:
                    self.stdout.write('   ⚠️  Incubator models not found, skipping...')

                # Delete user profiles first
                from users.models import UserProfile
                UserProfile.objects.filter(user__in=users_to_delete).delete()
                self.stdout.write('   ✅ User profiles deleted')

                # Finally delete the users
                deleted_count, deleted_details = users_to_delete.delete()
                
                self.stdout.write(self.style.SUCCESS(f'✅ Successfully deleted {deleted_count} objects!'))
                self.stdout.write(f'📋 Deletion details: {deleted_details}')

                # Show final count
                remaining_users = User.objects.count()
                self.stdout.write(f'📊 Remaining users: {remaining_users}')
                
                if remaining_users > 0:
                    self.stdout.write('🔍 Remaining users:')
                    for user in User.objects.all():
                        status = "Superuser" if user.is_superuser else "Staff" if user.is_staff else "User"
                        self.stdout.write(f'   - {user.username} ({user.email}) [{status}]')

            # Re-enable foreign key constraints
            with connection.cursor() as cursor:
                cursor.execute("PRAGMA foreign_keys = ON;")

        except Exception as e:
            # Re-enable foreign key constraints even on error
            try:
                with connection.cursor() as cursor:
                    cursor.execute("PRAGMA foreign_keys = ON;")
            except:
                pass
            self.stdout.write(self.style.ERROR(f'❌ Error during deletion: {str(e)}'))
            raise

        self.stdout.write(self.style.SUCCESS('🎉 User deletion completed successfully!'))
