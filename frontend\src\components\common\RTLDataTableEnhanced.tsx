import React from 'react';
import { useLanguage } from '../../hooks/useLanguage';
import { useTranslation } from 'react-i18next';
interface Column {
  key: string;
  title: string;
  render?: (value: any, record: any) => React.ReactNode;
  align?: 'left' | 'center' | 'right';
  width?: string | number;
}

interface RTLDataTableEnhancedProps {
  columns: Column[];
  data: any[];
  loading?: boolean;
  className?: string;
  rowKey?: string | ((record: any) => string);
  onRowClick?: (record: any) => void;
  emptyText?: string;
  pagination?: {
    current: number;
    total: number;
    pageSize: number;
    onChange: (page: number) => void;
  };
}

const RTLDataTableEnhanced: React.FC<RTLDataTableEnhancedProps> = ({
  columns,
  data,
  loading = false,
  className = '',
  rowKey = 'id',
  onRowClick,
  emptyText,
  pagination
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const defaultEmptyText = emptyText || t("common.noDataAvailable", "No data available");

  const getRowKey = (record: any, index: number) => {
    if (typeof rowKey === 'function') {
      return rowKey(record);
    }
    return record[rowKey] || index;
  };

  const getColumnAlign = (column: Column) => {
    if (column.align) {
      return column.align;
    }
    return isRTL ? 'right' : 'left';
  };

  if (loading) {
    return (
      <div className="animate-pulse glass-light">
        <div className="h-64 rounded-lg"></div>
      </div>
    );
  }

  return (
    <div className={`rtl-data-table-enhanced ${className}`} dir={isRTL ? 'rtl' : 'ltr'}>
      <div className="overflow-x-auto rounded-lg border border-glass-border glass-light">
        <table className="min-w-full divide-y divide-glass-border">
          <thead>
            <tr>
              {columns.map((column, index) => (
                <th
                  key={column.key}
                  className={`px-6 py-3 text-xs font-medium uppercase tracking-wider glass-light text-glass-text border-glass-border ${
                    getColumnAlign(column) === 'center' ? 'text-center' :
                    getColumnAlign(column) === 'right' ? 'text-right' : 'text-left'}
                  }`}
                  style={{ width: column.width }}
                >
                  {column.title}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="divide-y divide-glass-border">
            {data.length === 0 ? (
              <tr>
                <td colSpan={columns.length} className="px-6 py-12 text-center">
                  <div className="text-sm text-glass-text-secondary">
                    {defaultEmptyText}
                  </div>
                </td>
              </tr>
            ) : (
              data.map((record, index) => (
                <tr
                  key={getRowKey(record, index)}
                  className={`transition-colors glass-light hover:glass-hover border-glass-border ${onRowClick ? 'cursor-pointer' : ''}`}
                  onClick={() => onRowClick?.(record)}
                >
                  {columns.map((column) => (
                    <td
                      key={column.key}
                      className={`px-6 py-4 whitespace-nowrap text-sm text-glass-text ${
                        getColumnAlign(column) === 'center' ? 'text-center' :
                        getColumnAlign(column) === 'right' ? 'text-right' : 'text-left'}
                      }`}
                    >
                      {column.render ?
                        column.render(record[column.key], record) :
                        record[column.key]
                      }
                    </td>
                  ))}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {pagination && (
        <div className={`mt-4 flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className="text-sm text-glass-text-secondary">
            Showing {((pagination.current - 1) * pagination.pageSize) + 1} to{' '}
            {Math.min(pagination.current * pagination.pageSize, pagination.total)} of{' '}
            {pagination.total} results
          </div>

          <div className={`flex items-center space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>
            <button
              onClick={() => pagination.onChange(pagination.current - 1)}
              disabled={pagination.current <= 1}
              className="px-3 py-1 rounded border border-glass-border text-sm disabled:opacity-50 disabled:cursor-not-allowed glass-light text-glass-text hover:glass-hover"
            >
              {isRTL ? 'التالي' : t("common.previous", t("common.previous", "Previous"))}
            </button>

            <span className="text-sm">
              {pagination.current} / {Math.ceil(pagination.total / pagination.pageSize)}
            </span>

            <button
              onClick={() => pagination.onChange(pagination.current + 1)}
              disabled={pagination.current >= Math.ceil(pagination.total / pagination.pageSize)}
              className="px-3 py-1 rounded border border-glass-border text-sm disabled:opacity-50 disabled:cursor-not-allowed glass-light text-glass-text hover:glass-hover"
            >
              {isRTL ? 'السابق' : t("common.next", t("common.next", "Next"))}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default RTLDataTableEnhanced;
