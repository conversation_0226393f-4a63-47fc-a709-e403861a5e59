"""
Business Plan Analytics Service
Provides comprehensive analytics for business plans including time tracking, collaboration stats, and success metrics
"""

from datetime import datetime, timedelta
from django.utils import timezone
from django.db.models import Count, Avg, Sum, F, Q, Max, Min
from django.db.models.functions import <PERSON>runcMonth, TruncWeek, TruncDay
from django.contrib.auth.models import User
from .models_business_plan import BusinessPlan, BusinessPlanTemplate
from .models_business_plan_analytics import (
    BusinessPlanSession, BusinessPlanCollaboration, BusinessPlanExport,
    BusinessPlanAnalytics, TemplateSuccessMetrics
)
import uuid


class BusinessPlanAnalyticsService:
    """Service for business plan analytics and tracking"""
    
    @staticmethod
    def start_session(business_plan_id, user, request=None):
        """Start a new session for working on a business plan"""
        session_id = str(uuid.uuid4())
        
        session_data = {
            'business_plan_id': business_plan_id,
            'user': user,
            'session_id': session_id,
        }
        
        if request:
            session_data.update({
                'ip_address': BusinessPlanAnalyticsService._get_client_ip(request),
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                'device_type': BusinessPlanAnalyticsService._get_device_type(request),
            })
        
        session = BusinessPlanSession.objects.create(**session_data)
        return session
    
    @staticmethod
    def end_session(session_id):
        """End a session"""
        try:
            session = BusinessPlanSession.objects.get(session_id=session_id)
            session.end_session()
            return session
        except BusinessPlanSession.DoesNotExist:
            return None
    
    @staticmethod
    def track_collaboration(business_plan_id, user, action_type, **kwargs):
        """Track collaboration activity"""
        collaboration_data = {
            'business_plan_id': business_plan_id,
            'user': user,
            'action_type': action_type,
            'section_id': kwargs.get('section_id'),
            'section_title': kwargs.get('section_title', ''),
            'content': kwargs.get('content', ''),
            'metadata': kwargs.get('metadata', {}),
            'ip_address': kwargs.get('ip_address'),
        }
        
        return BusinessPlanCollaboration.objects.create(**collaboration_data)
    
    @staticmethod
    def track_export(business_plan_id, user, export_format, export_type, **kwargs):
        """Track business plan export"""
        export_data = {
            'business_plan_id': business_plan_id,
            'user': user,
            'export_format': export_format,
            'export_type': export_type,
            'sections_included': kwargs.get('sections_included', []),
            'file_size': kwargs.get('file_size'),
            'file_path': kwargs.get('file_path', ''),
            'ip_address': kwargs.get('ip_address'),
            'user_agent': kwargs.get('user_agent', ''),
            'export_successful': kwargs.get('export_successful', True),
            'error_message': kwargs.get('error_message', ''),
        }
        
        return BusinessPlanExport.objects.create(**export_data)
    
    @staticmethod
    def get_time_analytics(business_plan_id=None, user_id=None, date_range=30):
        """Get time spent analytics"""
        end_date = timezone.now()
        start_date = end_date - timedelta(days=date_range)
        
        sessions = BusinessPlanSession.objects.filter(
            start_time__gte=start_date,
            end_time__isnull=False
        )
        
        if business_plan_id:
            sessions = sessions.filter(business_plan_id=business_plan_id)
        if user_id:
            sessions = sessions.filter(user_id=user_id)
        
        # Calculate total time spent
        total_time = timedelta()
        session_durations = []
        
        for session in sessions:
            duration = session.get_duration()
            total_time += duration
            session_durations.append(duration.total_seconds())
        
        # Daily breakdown
        daily_time = sessions.extra(
            select={'day': "date(start_time)"}
        ).values('day').annotate(
            total_sessions=Count('id'),
            avg_duration=Avg(
                F('end_time') - F('start_time')
            )
        ).order_by('day')
        
        return {
            'total_time_seconds': total_time.total_seconds(),
            'total_sessions': sessions.count(),
            'average_session_duration': sum(session_durations) / len(session_durations) if session_durations else 0,
            'daily_breakdown': list(daily_time),
            'most_active_day': max(daily_time, key=lambda x: x['total_sessions']) if daily_time else None,
        }
    
    @staticmethod
    def get_template_usage_analytics(date_range=30):
        """Get most used templates analytics"""
        end_date = timezone.now()
        start_date = end_date - timedelta(days=date_range)
        
        # Get template usage from business plans
        template_usage = BusinessPlan.objects.filter(
            created_at__gte=start_date
        ).values(
            'template__id',
            'template__name',
            'template__industry'
        ).annotate(
            usage_count=Count('id'),
            completion_rate=Avg('completion_percentage'),
            avg_time_to_complete=Avg(
                F('updated_at') - F('created_at')
            )
        ).order_by('-usage_count')
        
        # Get success metrics
        success_metrics = []
        for template_data in template_usage:
            if template_data['template__id']:
                template_plans = BusinessPlan.objects.filter(
                    template_id=template_data['template__id'],
                    created_at__gte=start_date
                )
                
                completed_plans = template_plans.filter(status='completed').count()
                total_plans = template_plans.count()
                
                success_metrics.append({
                    **template_data,
                    'completion_success_rate': (completed_plans / total_plans * 100) if total_plans > 0 else 0,
                    'total_plans': total_plans,
                    'completed_plans': completed_plans,
                })
        
        return success_metrics
    
    @staticmethod
    def get_collaboration_analytics(business_plan_id=None, date_range=30):
        """Get collaboration statistics"""
        end_date = timezone.now()
        start_date = end_date - timedelta(days=date_range)
        
        collaborations = BusinessPlanCollaboration.objects.filter(
            created_at__gte=start_date
        )
        
        if business_plan_id:
            collaborations = collaborations.filter(business_plan_id=business_plan_id)
        
        # Action type breakdown
        action_breakdown = collaborations.values('action_type').annotate(
            count=Count('id')
        ).order_by('-count')
        
        # User activity
        user_activity = collaborations.values(
            'user__username',
            'user__first_name',
            'user__last_name'
        ).annotate(
            total_actions=Count('id'),
            comments=Count('id', filter=Q(action_type='comment')),
            edits=Count('id', filter=Q(action_type='edit')),
            reviews=Count('id', filter=Q(action_type='review'))
        ).order_by('-total_actions')
        
        # Daily activity
        daily_activity = collaborations.extra(
            select={'day': "date(created_at)"}
        ).values('day').annotate(
            total_actions=Count('id'),
            unique_users=Count('user', distinct=True)
        ).order_by('day')
        
        return {
            'total_collaborations': collaborations.count(),
            'unique_collaborators': collaborations.values('user').distinct().count(),
            'action_breakdown': list(action_breakdown),
            'user_activity': list(user_activity),
            'daily_activity': list(daily_activity),
        }
    
    @staticmethod
    def get_export_analytics(business_plan_id=None, date_range=30):
        """Get export/download statistics"""
        end_date = timezone.now()
        start_date = end_date - timedelta(days=date_range)
        
        exports = BusinessPlanExport.objects.filter(
            created_at__gte=start_date
        )
        
        if business_plan_id:
            exports = exports.filter(business_plan_id=business_plan_id)
        
        # Format breakdown
        format_breakdown = exports.values('export_format').annotate(
            count=Count('id')
        ).order_by('-count')
        
        # Type breakdown
        type_breakdown = exports.values('export_type').annotate(
            count=Count('id')
        ).order_by('-count')
        
        # Success rate
        total_exports = exports.count()
        successful_exports = exports.filter(export_successful=True).count()
        success_rate = (successful_exports / total_exports * 100) if total_exports > 0 else 0
        
        # Daily exports
        daily_exports = exports.extra(
            select={'day': "date(created_at)"}
        ).values('day').annotate(
            total_exports=Count('id'),
            unique_users=Count('user', distinct=True)
        ).order_by('day')
        
        return {
            'total_exports': total_exports,
            'successful_exports': successful_exports,
            'success_rate': success_rate,
            'format_breakdown': list(format_breakdown),
            'type_breakdown': list(type_breakdown),
            'daily_exports': list(daily_exports),
        }
    
    @staticmethod
    def get_success_rate_metrics(date_range=30):
        """Get overall success rate metrics"""
        end_date = timezone.now()
        start_date = end_date - timedelta(days=date_range)
        
        plans = BusinessPlan.objects.filter(created_at__gte=start_date)
        
        total_plans = plans.count()
        completed_plans = plans.filter(status='completed').count()
        published_plans = plans.filter(status='published').count()
        
        # Average completion percentage
        avg_completion = plans.aggregate(
            avg_completion=Avg('completion_percentage')
        )['avg_completion'] or 0
        
        # Time to completion
        completed_plan_times = []
        for plan in plans.filter(status='completed'):
            time_diff = plan.updated_at - plan.created_at
            completed_plan_times.append(time_diff.total_seconds())
        
        avg_time_to_complete = sum(completed_plan_times) / len(completed_plan_times) if completed_plan_times else 0
        
        return {
            'total_plans': total_plans,
            'completed_plans': completed_plans,
            'published_plans': published_plans,
            'completion_rate': (completed_plans / total_plans * 100) if total_plans > 0 else 0,
            'publish_rate': (published_plans / total_plans * 100) if total_plans > 0 else 0,
            'average_completion_percentage': round(avg_completion, 2),
            'average_time_to_complete_hours': avg_time_to_complete / 3600 if avg_time_to_complete else 0,
        }
    
    @staticmethod
    def update_business_plan_analytics(business_plan_id):
        """Update analytics for a specific business plan"""
        try:
            business_plan = BusinessPlan.objects.get(id=business_plan_id)
            analytics, created = BusinessPlanAnalytics.objects.get_or_create(
                business_plan=business_plan
            )
            analytics.update_analytics()
            return analytics
        except BusinessPlan.DoesNotExist:
            return None
    
    @staticmethod
    def _get_client_ip(request):
        """Get client IP address from request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    @staticmethod
    def _get_device_type(request):
        """Determine device type from user agent"""
        user_agent = request.META.get('HTTP_USER_AGENT', '').lower()
        if 'mobile' in user_agent or 'android' in user_agent or 'iphone' in user_agent:
            return 'mobile'
        elif 'tablet' in user_agent or 'ipad' in user_agent:
            return 'tablet'
        else:
            return 'desktop'
