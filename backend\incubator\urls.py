from django.urls import path, include
from rest_framework.routers import DefaultRout<PERSON>
from .views import (
    BusinessIdeaViewSet, ProgressUpdateViewSet,
    IncubatorResourceViewSet, MentorshipApplicationViewSet,
    InvestorProfileViewSet, FundingOpportunityViewSet,
    FundingApplicationViewSet, InvestmentViewSet,
    MentorProfileViewSet, MentorExpertiseViewSet, MentorshipMatchViewSet
)
from .views_milestone import (
    BusinessMilestoneViewSet, BusinessGoalViewSet,
    MentorRecommendationViewSet, BusinessAnalyticsViewSet
)
from .views_session import MentorshipSessionViewSet
from .views_feedback import MentorshipFeedbackViewSet
from .views_business_plan import (
    BusinessPlanViewSet, BusinessPlanSectionViewSet
)
from .views_analytics import (
    EnhancedBusinessAnalyticsViewSet, MetricDefinitionViewSet
)
from .views_template_customization import (
    BusinessPlanTemplateViewSet, CustomBusinessPlanTemplateViewSet, TemplateSectionDefinitionViewSet
)
from .views_template_analytics import TemplateAnalyticsViewSet
from .views_business_plan_analytics import BusinessPlanAnalyticsViewSet

router = DefaultRouter()
router.register(r'business-ideas', BusinessIdeaViewSet)
router.register(r'progress-updates', ProgressUpdateViewSet)
router.register(r'resources', IncubatorResourceViewSet)
router.register(r'mentorship-applications', MentorshipApplicationViewSet)
router.register(r'investor-profiles', InvestorProfileViewSet)
router.register(r'funding-opportunities', FundingOpportunityViewSet)
router.register(r'funding-applications', FundingApplicationViewSet)
router.register(r'investments', InvestmentViewSet)
router.register(r'mentor-profiles', MentorProfileViewSet)
router.register(r'mentor-expertise', MentorExpertiseViewSet)
router.register(r'mentorship-matches', MentorshipMatchViewSet)
router.register(r'mentorship-sessions', MentorshipSessionViewSet)
router.register(r'mentorship-feedback', MentorshipFeedbackViewSet)

# Register milestone viewsets
router.register(r'business-milestones', BusinessMilestoneViewSet)
router.register(r'business-goals', BusinessGoalViewSet)
router.register(r'mentor-recommendations', MentorRecommendationViewSet)
router.register(r'business-analytics', BusinessAnalyticsViewSet)

# Register business plan viewsets
router.register(r'business-plan-templates', BusinessPlanTemplateViewSet)
router.register(r'business-plans', BusinessPlanViewSet)
router.register(r'business-plan-sections', BusinessPlanSectionViewSet)
router.register(r'custom-templates', CustomBusinessPlanTemplateViewSet, basename='custom-templates')
router.register(r'template-sections', TemplateSectionDefinitionViewSet)

# Register enhanced analytics viewsets
router.register(r'enhanced-analytics', EnhancedBusinessAnalyticsViewSet, basename='enhanced-analytics')
router.register(r'metric-definitions', MetricDefinitionViewSet)

# Register template analytics viewsets
router.register(r'template-analytics', TemplateAnalyticsViewSet, basename='template-analytics')

# Register business plan analytics viewsets
router.register(r'business-plan-analytics', BusinessPlanAnalyticsViewSet, basename='business-plan-analytics')

urlpatterns = [
    path('', include(router.urls)),
]
