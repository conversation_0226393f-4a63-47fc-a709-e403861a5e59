import React from 'react';
import { LogOut } from 'lucide-react';
import { User } from '../../../../types/auth';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../hooks/useLanguage';
import { LanguageSwitcher } from '../../../common';

interface UserProfileSectionProps {
  user: User | null;
  onLogout: (e: React.MouseEvent) => void;
}

const UserProfileSection: React.FC<UserProfileSectionProps> = ({ user, onLogout  }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  return (
    <div className={`flex-shrink-0 p-4 border-t border-white/20 ${isRTL ? "flex-row-reverse" : ""}`}>
      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
        <div className={`flex-shrink-0 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`w-10 h-10 rounded-full bg-purple-600/30 flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}>
            {user?.username?.charAt(0).toUpperCase() || 'A'}
          </div>
        </div>
        <div className={isRTL ? 'mr-3' : 'ml-3'}>
          <p className={`text-sm font-medium text-white ${isRTL ? 'text-right' : 'text-left'}`}>
            {user?.username || t("admin.admin", "Admin")}
          </p>
          <p className={`text-xs text-gray-400 ${isRTL ? 'text-right' : 'text-left'}`}>
            {user?.email || '<EMAIL>'}
          </p>
        </div>
      </div>

      {/* Language Switcher */}
      <div className="mt-3 mb-3">
        <LanguageSwitcher variant="compact" />
      </div>

      <button
        onClick={onLogout}
        className={`flex w-full items-center px-4 py-3 text-gray-300 hover:bg-red-800/30 hover:text-white rounded-lg transition-colors ${isRTL ? 'flex-row-reverse text-right' : 'text-left'}`}
      >
        <LogOut size={20} className={isRTL ? 'ml-3' : 'mr-3'} />
        {t('nav.logout')}
      </button>
    </div>
  );
};

export default UserProfileSection;
