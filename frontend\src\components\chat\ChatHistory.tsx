import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  History,
  Search,
  Calendar,
  MessageSquare,
  Trash2,
  Download,
  Star,
  StarOff,
  X,
  Clock,
  User,
  Bot
} from 'lucide-react';
import { ChatMessage } from '../../services/chatApi';

interface ChatSession {
  id: string;
  title: string;
  date: string;
  messageCount: number;
  lastMessage: string;
  businessIdeaTitle?: string;
  isFavorite: boolean;
  messages: ChatMessage[];
}

interface ChatHistoryProps {
  onClose: () => void;
  onLoadSession: (messages: ChatMessage[]) => void;
  onExportSession: (session: ChatSession) => void;
  currentMessages: ChatMessage[];
}

const ChatHistory: React.FC<ChatHistoryProps> = ({ onClose,
  onLoadSession,
  onExportSession,
  currentMessages
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [sessions, setSessions] = useState<ChatSession[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedDate, setSelectedDate] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  // Mock data for demonstration
  useEffect(() => {
    const mockSessions: ChatSession[] = [
      {
        id: '1',
        title: t("chat.business.plan.discussion", "Business Plan Discussion"),
        date: '2024-01-15',
        messageCount: 12,
        lastMessage: t("chat.thank.you.for", "Thank you for the detailed market analysis. This helps me understand..."),
        businessIdeaTitle: 'E-commerce Platform"),
        isFavorite: true,
        messages: [
          {
            role: 'user',
            content: t("chat.help.me.create", "Help me create a business plan for my e-commerce platform"),
            timestamp: '2024-01-15T10:00:00Z'
          },
          {
            role: 'assistant',
            content: 'I\'d be happy to help you create a comprehensive business plan...',
            timestamp: '2024-01-15T10:01:00Z'
          }
        ]
      },
      {
        id: '2',
        title: t("chat.marketing.strategy", "Marketing Strategy"),
        date: '2024-01-14',
        messageCount: 8,
        lastMessage: t("chat.the.social.media", "The social media strategy you outlined looks promising..."),
        businessIdeaTitle: 'Food Delivery App"),
        isFavorite: false,
        messages: [
          {
            role: 'user',
            content: t("chat.what.marketing.strategies", "What marketing strategies work best for food delivery apps?"),
            timestamp: '2024-01-14T15:30:00Z'
          }
        ]
      },
      {
        id: '3',
        title: t("chat.financial.planning", "Financial Planning"),
        date: '2024-01-13',
        messageCount: 15,
        lastMessage: t("chat.your.revenue.projections", "Your revenue projections seem realistic based on the market data..."),
        isFavorite: false,
        messages: []
      }
    ];

    // Add current session if it has messages
    if (currentMessages.length > 0) {
      const currentSession: ChatSession = {
        id: 'current',
        title: t("chat.current.session", "Current Session"),
        date: new Date().toISOString().split('T')[0],
        messageCount: currentMessages.length,
        lastMessage: currentMessages[currentMessages.length - 1]?.content.substring(0, 100) + '...',
        isFavorite: false,
        messages: currentMessages
      };
      mockSessions.unshift(currentSession);
    }

    setSessions(mockSessions);
    setIsLoading(false);
  }, [currentMessages]);

  const filteredSessions = sessions.filter(session => {
    const matchesSearch = searchQuery === '' ||
      session.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      session.lastMessage.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (session.businessIdeaTitle && session.businessIdeaTitle.toLowerCase().includes(searchQuery.toLowerCase();

    const matchesDate = selectedDate === '' || session.date === selectedDate;

    return matchesSearch && matchesDate;
  );

  const handleToggleFavorite = (sessionId: string) => {
    setSessions(prev => prev.map(session =>
      session.id === sessionId
        ? { ...session, isFavorite: !session.isFavorite }
        : session
    ));
  };

  const handleDeleteSession = (sessionId: string) => {
    if (window.confirm(t('chat.confirmDeleteSession') {
      setSessions(prev => prev.filter(session => session.id !== sessionId));
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return t('chat.justNow');
    if (diffInHours < 24) return t('chat.hoursAgo', { count: diffInHours });

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return t('chat.daysAgo', { count: diffInDays });

    return formatDate(dateString);
  };

  return (
    <div className={`fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4 ${isRTL ? "flex-row-reverse" : ""}`}>
      <div className="card max-w-4xl w-full max-h-[90vh] overflow-hidden shadow-2xl backdrop-blur-md">
        {/* Header */}
        <div className={`flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <History size={24} className={`text-purple-600 ${isRTL ? 'ml-3' : 'mr-3'}`} />
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                {t('chat.chatHistory')}
              </h2>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                {t('chat.historySubtitle')}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
          >
            <X size={20} />
          </button>
        </div>

        {/* Search and Filters */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className={`flex flex-col sm:flex-row gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            {/* Search */}
            <div className={`flex-1 relative ${isRTL ? "flex-row-reverse" : ""}`}>
              <Search size={16} className={`absolute top-3 ${isRTL ? 'right-3' : 'left-3'} text-gray-400`} />
              <input
                type="text"
                placeholder={t('chat.searchHistory')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className={`w-full ${isRTL ? 'pr-10 text-right' : 'pl-10'} py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
              />
            </div>

            {/* Date Filter */}
            <div className="relative">
              <Calendar size={16} className={`absolute top-3 ${isRTL ? 'right-3' : 'left-3'} text-gray-400`} />
              <input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className={`${isRTL ? 'pr-10 text-right' : 'pl-10'} py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
              />
            </div>
          </div>
        </div>

        {/* Sessions List */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {isLoading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"></div>
              <p className="text-gray-500 dark:text-gray-400 mt-4">{t('common.loading')}</p>
            </div>
          ) : filteredSessions.length === 0 ? (
            <div className="text-center py-12">
              <History size={48} className="mx-auto text-gray-400 mb-4" />
              <p className="text-gray-500 dark:text-gray-400">
                {t('chat.noHistoryFound')}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredSessions.map((session) => (
                <div
                  key={session.id}
                  className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:border-purple-300 dark:hover:border-purple-600 transition-colors"
                >
                  <div className={`flex items-start justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className={`flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''} mb-2`}>
                        <h3 className="font-medium text-gray-900 dark:text-white">
                          {session.title}
                        </h3>
                        {session.isFavorite && (
                          <Star size={16} className={`text-yellow-500 ${isRTL ? 'mr-2' : 'ml-2'}`} />
                        )}
                        {session.id === 'current' && (
                          <span className={`px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 text-xs rounded ${isRTL ? 'mr-2' : 'ml-2'}`}>
                            {t('chat.current')}
                          </span>
                        )}
                      </div>

                      {session.businessIdeaTitle && (
                        <p className="text-sm text-purple-600 dark:text-purple-400 mb-1">
                          {session.businessIdeaTitle}
                        </p>
                      )}

                      <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                        {session.lastMessage}
                      </p>

                      <div className={`flex items-center text-xs text-gray-500 dark:text-gray-400 ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <Clock size={12} className={isRTL ? 'ml-1' : 'mr-1'} />
                        <span>{getTimeAgo(session.date)}</span>
                        <MessageSquare size={12} className={`${isRTL ? 'ml-3 mr-1' : 'mr-1 ml-3'}`} />
                        <span>{session.messageCount} {t('chat.messages')}</span>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className={`flex items-center space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>
                      <button
                        onClick={() => handleToggleFavorite(session.id)}
                        className="p-1 text-gray-400 hover:text-yellow-500"
                        title={session.isFavorite ? t('chat.removeFavorite') : t('chat.addFavorite')}
                      >
                        {session.isFavorite ? <Star size={16} /> : <StarOff size={16} />}
                      </button>

                      <button
                        onClick={() => onLoadSession(session.messages)}
                        className="p-1 text-gray-400 hover:text-purple-600"
                        title={t('chat.loadSession')}
                      >
                        <MessageSquare size={16} />
                      </button>

                      <button
                        onClick={() => onExportSession(session)}
                        className="p-1 text-gray-400 hover:text-blue-600"
                        title={t('chat.exportSession')}
                      >
                        <Download size={16} />
                      </button>

                      {session.id !== 'current' && (
                        <button
                          onClick={() => handleDeleteSession(session.id)}
                          className="p-1 text-gray-400 hover:text-red-600"
                          title={t('chat.deleteSession')}
                        >
                          <Trash2 size={16} />
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChatHistory;
