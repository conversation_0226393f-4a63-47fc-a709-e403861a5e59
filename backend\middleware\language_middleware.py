"""
Middleware for handling language preferences.
"""

from django.utils.translation import activate
from django.utils.deprecation import MiddlewareMixin
from django.conf import settings

from ..utils.language_utils import get_language_from_request, get_language_from_user


class UserLanguageMiddleware(MiddlewareMixin):
    """
    Middleware that sets the language based on user preferences.
    """
    
    def process_request(self, request):
        """
        Process the request and set the language.
        
        Args:
            request: HTTP request object
        """
        # If user is authenticated, use their language preference
        if request.user.is_authenticated:
            language = get_language_from_user(request.user)
            activate(language)
            request.LANGUAGE_CODE = language
        else:
            # Otherwise, use the language from the request
            language = get_language_from_request(request)
            activate(language)
            request.LANGUAGE_CODE = language
        
        return None
