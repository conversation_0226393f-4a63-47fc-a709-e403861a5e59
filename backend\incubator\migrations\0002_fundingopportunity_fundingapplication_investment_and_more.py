# Generated by Django 5.1.7 on 2025-05-15 13:22

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('incubator', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='FundingOpportunity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('funding_type', models.CharField(choices=[('grant', 'Grant'), ('equity', 'Equity Investment'), ('loan', 'Loan'), ('convertible', 'Convertible Note'), ('prize', 'Competition Prize'), ('crowdfunding', 'Crowdfunding'), ('other', 'Other')], max_length=20)),
                ('amount', models.DecimalField(decimal_places=2, help_text='Amount in USD', max_digits=12, validators=[django.core.validators.MinValueValidator(0)])),
                ('eligibility_criteria', models.TextField(help_text='Who can apply for this funding')),
                ('application_process', models.TextField(help_text='How to apply for this funding')),
                ('application_deadline', models.DateField(help_text='Deadline for applications')),
                ('status', models.CharField(choices=[('active', 'Active'), ('closed', 'Closed'), ('draft', 'Draft')], default='draft', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('provider', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='provided_funding_opportunities', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name_plural': 'Funding Opportunities',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='FundingApplication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('pitch', models.TextField(help_text='Pitch for why this business idea deserves funding')),
                ('requested_amount', models.DecimalField(decimal_places=2, help_text='Amount requested in USD', max_digits=12, validators=[django.core.validators.MinValueValidator(0)])),
                ('use_of_funds', models.TextField(help_text='How the funds will be used')),
                ('status', models.CharField(choices=[('pending', 'Pending Review'), ('shortlisted', 'Shortlisted'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('funded', 'Funded')], default='pending', max_length=20)),
                ('reviewer_notes', models.TextField(blank=True, help_text='Private notes for reviewers', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('applicant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='funding_applications', to=settings.AUTH_USER_MODEL)),
                ('business_idea', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='funding_applications', to='incubator.businessidea')),
                ('funding_opportunity', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='applications', to='incubator.fundingopportunity')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Investment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('investment_type', models.CharField(choices=[('equity', 'Equity Investment'), ('loan', 'Loan'), ('convertible', 'Convertible Note'), ('grant', 'Grant'), ('other', 'Other')], max_length=20)),
                ('amount', models.DecimalField(decimal_places=2, help_text='Amount in USD', max_digits=12, validators=[django.core.validators.MinValueValidator(0)])),
                ('equity_percentage', models.DecimalField(blank=True, decimal_places=2, help_text='Percentage of equity', max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('terms', models.TextField(help_text='Terms of the investment')),
                ('status', models.CharField(choices=[('proposed', 'Proposed'), ('negotiating', 'Negotiating'), ('accepted', 'Accepted'), ('completed', 'Completed'), ('declined', 'Declined')], default='proposed', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('business_idea', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='investments', to='incubator.businessidea')),
                ('investor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='investments_made', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='InvestorProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('investor_type', models.CharField(choices=[('angel', 'Angel Investor'), ('vc', 'Venture Capital'), ('corporate', 'Corporate Investor'), ('government', 'Government Fund'), ('crowdfunding', 'Crowdfunding Platform'), ('other', 'Other')], max_length=20)),
                ('company_name', models.CharField(blank=True, max_length=200, null=True)),
                ('bio', models.TextField(help_text='Brief description of the investor')),
                ('investment_focus', models.TextField(help_text='Areas of interest for investment')),
                ('investment_range_min', models.DecimalField(decimal_places=2, help_text='Minimum investment amount in USD', max_digits=12, validators=[django.core.validators.MinValueValidator(0)])),
                ('investment_range_max', models.DecimalField(decimal_places=2, help_text='Maximum investment amount in USD', max_digits=12, validators=[django.core.validators.MinValueValidator(0)])),
                ('linkedin_profile', models.URLField(blank=True, null=True)),
                ('website', models.URLField(blank=True, null=True)),
                ('is_verified', models.BooleanField(default=False, help_text='Whether the investor has been verified by administrators')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='investor_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
