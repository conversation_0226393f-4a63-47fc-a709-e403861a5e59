/**
 * Unified Stats Section
 * Consolidated stats section that adapts to different user roles
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../hooks/useLanguage';
import { RTLText, RTLIcon } from '../../../common';
import { DashboardRole, DashboardStat } from '../../../../types/dashboard';
import { useDashboardTheme } from '../../../../contexts/DashboardContext';
import { formatStatValue, getChangeIndicator } from '../../../../utils/dashboardUtils';
import {
  Users,
  Calendar,
  BookOpen,
  MessageSquare,
  Lightbulb,
  CheckCircle,
  Clock,
  AlertCircle,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Target,
  Shield,
  Activity,
  Eye,
  UserCheck,
  Briefcase,
  BarChart3
} from 'lucide-react';

interface UnifiedStatsSectionProps {
  role: DashboardRole;
  onUpdate?: (sectionId: string, data: any) => void;
  className?: string;
}

const UnifiedStatsSection: React.FC<UnifiedStatsSectionProps> = ({
  role,
  onUpdate,
  className = '',
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { theme, getCardClasses, getTextClasses } = useDashboardTheme();

  const [stats, setStats] = useState<DashboardStat[]>([]);
  const [loading, setLoading] = useState(true);

  // Fetch role-specific stats
  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 800));
        
        const roleStats = getRoleSpecificStats(role);
        setStats(roleStats);
        
        // Notify parent of data update
        onUpdate?.('stats', roleStats);
      } catch (error) {
        console.error('Error fetching stats:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, [role, onUpdate]);

  // Get role-specific stats configuration
  const getRoleSpecificStats = (userRole: DashboardRole): DashboardStat[] => {
    switch (userRole) {
      case 'super_admin':
        return [
          {
            id: 'total_users',
            title: t('stats.totalUsers', 'Total Users'),
            value: 12547,
            icon: Users,
            color: 'bg-blue-600/30',
            change: 8.5,
            changeType: 'increase',
            description: 'All registered users'
          },
          {
            id: 'system_health',
            title: t('stats.systemHealth', 'System Health'),
            value: '99.8%',
            icon: Shield,
            color: 'bg-green-600/30',
            change: 0.2,
            changeType: 'increase',
            description: 'Overall system uptime'
          },
          {
            id: 'active_sessions',
            title: t('stats.activeSessions', 'Active Sessions'),
            value: 1247,
            icon: Activity,
            color: 'bg-purple-600/30',
            change: -2.1,
            changeType: 'decrease',
            description: 'Current active user sessions'
          },
          {
            id: 'revenue',
            title: t('stats.revenue', 'Revenue'),
            value: 125000,
            icon: DollarSign,
            color: 'bg-emerald-600/30',
            change: 15.3,
            changeType: 'increase',
            suffix: 'USD',
            description: 'Monthly revenue'
          }
        ];

      case 'admin':
        return [
          {
            id: 'total_users',
            title: t('stats.totalUsers', 'Total Users'),
            value: 8567,
            icon: Users,
            color: 'bg-blue-600/30',
            change: 5.2,
            changeType: 'increase'
          },
          {
            id: 'active_events',
            title: t('stats.activeEvents', 'Active Events'),
            value: 45,
            icon: Calendar,
            color: 'bg-purple-600/30',
            change: 12.0,
            changeType: 'increase'
          },
          {
            id: 'resources',
            title: t('stats.resources', 'Resources'),
            value: 234,
            icon: BookOpen,
            color: 'bg-green-600/30',
            change: 3.1,
            changeType: 'increase'
          },
          {
            id: 'community_posts',
            title: t('stats.communityPosts', 'Community Posts'),
            value: 1567,
            icon: MessageSquare,
            color: 'bg-orange-600/30',
            change: 8.7,
            changeType: 'increase'
          }
        ];

      case 'moderator':
        return [
          {
            id: 'pending_reports',
            title: t('stats.pendingReports', 'Pending Reports'),
            value: 12,
            icon: AlertCircle,
            color: 'bg-red-600/30',
            change: -15.2,
            changeType: 'decrease'
          },
          {
            id: 'resolved_today',
            title: t('stats.resolvedToday', 'Resolved Today'),
            value: 8,
            icon: CheckCircle,
            color: 'bg-green-600/30',
            change: 25.0,
            changeType: 'increase'
          },
          {
            id: 'flagged_content',
            title: t('stats.flaggedContent', 'Flagged Content'),
            value: 5,
            icon: Eye,
            color: 'bg-yellow-600/30',
            change: -10.0,
            changeType: 'decrease'
          },
          {
            id: 'community_health',
            title: t('stats.communityHealth', 'Community Health'),
            value: '85%',
            icon: Shield,
            color: 'bg-blue-600/30',
            change: 2.1,
            changeType: 'increase'
          }
        ];

      case 'mentor':
        return [
          {
            id: 'total_mentees',
            title: t('stats.totalMentees', 'Total Mentees'),
            value: 12,
            icon: UserCheck,
            color: 'bg-blue-600/30',
            change: 20.0,
            changeType: 'increase'
          },
          {
            id: 'active_sessions',
            title: t('stats.activeSessions', 'Active Sessions'),
            value: 8,
            icon: Activity,
            color: 'bg-purple-600/30',
            change: 14.3,
            changeType: 'increase'
          },
          {
            id: 'completed_sessions',
            title: t('stats.completedSessions', 'Completed Sessions'),
            value: 45,
            icon: CheckCircle,
            color: 'bg-green-600/30',
            change: 8.9,
            changeType: 'increase'
          },
          {
            id: 'average_rating',
            title: t('stats.averageRating', 'Average Rating'),
            value: '4.8',
            icon: TrendingUp,
            color: 'bg-yellow-600/30',
            change: 0.2,
            changeType: 'increase'
          }
        ];

      case 'investor':
        return [
          {
            id: 'portfolio_value',
            title: t('stats.portfolioValue', 'Portfolio Value'),
            value: 250000,
            icon: DollarSign,
            color: 'bg-green-600/30',
            change: 12.5,
            changeType: 'increase',
            suffix: 'USD'
          },
          {
            id: 'active_investments',
            title: t('stats.activeInvestments', 'Active Investments'),
            value: 8,
            icon: Briefcase,
            color: 'bg-blue-600/30',
            change: 33.3,
            changeType: 'increase'
          },
          {
            id: 'opportunities',
            title: t('stats.opportunities', 'New Opportunities'),
            value: 15,
            icon: Target,
            color: 'bg-purple-600/30',
            change: 25.0,
            changeType: 'increase'
          },
          {
            id: 'roi',
            title: t('stats.roi', 'Average ROI'),
            value: '18.5%',
            icon: TrendingUp,
            color: 'bg-emerald-600/30',
            change: 2.3,
            changeType: 'increase'
          }
        ];

      default: // user
        return [
          {
            id: 'total_ideas',
            title: t('stats.totalIdeas', 'Total Ideas'),
            value: 7,
            icon: Lightbulb,
            color: 'bg-indigo-600/30',
            change: 16.7,
            changeType: 'increase'
          },
          {
            id: 'approved_ideas',
            title: t('stats.approved', 'Approved'),
            value: 3,
            icon: CheckCircle,
            color: 'bg-green-600/30',
            change: 50.0,
            changeType: 'increase'
          },
          {
            id: 'pending_ideas',
            title: t('stats.pending', 'Pending'),
            value: 2,
            icon: Clock,
            color: 'bg-yellow-600/30',
            change: 0,
            changeType: 'neutral'
          },
          {
            id: 'rejected_ideas',
            title: t('stats.rejected', 'Rejected'),
            value: 2,
            icon: AlertCircle,
            color: 'bg-red-600/30',
            change: -33.3,
            changeType: 'decrease'
          }
        ];
    }
  };

  // Get grid columns based on role
  const getGridCols = () => {
    switch (role) {
      case 'super_admin':
      case 'admin':
      case 'investor':
        return 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4';
      case 'user':
        return 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4';
      default:
        return 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3';
    }
  };

  // Render individual stat card
  const renderStatCard = (stat: DashboardStat) => {
    const IconComponent = stat.icon;
    const changeIndicator = getChangeIndicator(stat.change || 0);
    
    return (
      <div key={stat.id} className={`${getCardClasses()} ${stat.color}`}>
        <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
          <div>
            <RTLText as="p" className={`text-sm ${getTextClasses('secondary')}`}>
              {stat.title}
            </RTLText>
            <RTLText as="h3" className={`text-2xl font-bold ${getTextClasses('primary')} mt-1`}>
              {loading ? (
                <span className="animate-pulse">---</span>
              ) : (
                formatStatValue(stat.value, typeof stat.value === 'number' && stat.suffix === 'USD' ? 'currency' : 'number')
              )}
              {stat.suffix && stat.suffix !== 'USD' && ` ${stat.suffix}`}
            </RTLText>
            {stat.change !== undefined && !loading && (
              <div className={`flex items-center mt-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                {stat.change > 0 ? (
                  <TrendingUp className={`w-4 h-4 ${changeIndicator.color} ${isRTL ? 'ml-1' : 'mr-1'}`} />
                ) : stat.change < 0 ? (
                  <TrendingDown className={`w-4 h-4 ${changeIndicator.color} ${isRTL ? 'ml-1' : 'mr-1'}`} />
                ) : (
                  <BarChart3 className={`w-4 h-4 ${changeIndicator.color} ${isRTL ? 'ml-1' : 'mr-1'}`} />
                )}
                <span className={`text-sm ${changeIndicator.color}`}>
                  {changeIndicator.symbol}{Math.abs(stat.change).toFixed(1)}%
                </span>
              </div>
            )}
            {stat.description && (
              <RTLText as="p" className={`text-xs ${getTextClasses('secondary')} mt-1 opacity-70`}>
                {stat.description}
              </RTLText>
            )}
          </div>
          <div className={`p-3 rounded-full bg-black/20 ${isRTL ? 'ml-4' : 'mr-0'}`}>
            <RTLIcon icon={IconComponent} size={24} className="text-white" />
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={className}>
      <div className="mb-6">
        <RTLText as="h2" className={`text-xl font-semibold ${getTextClasses('primary')}`}>
          {t('dashboard.keyMetrics', 'Key Metrics')}
        </RTLText>
      </div>
      
      <div className={`grid ${getGridCols()} gap-6`}>
        {stats.map(renderStatCard)}
      </div>
    </div>
  );
};

export default UnifiedStatsSection;
