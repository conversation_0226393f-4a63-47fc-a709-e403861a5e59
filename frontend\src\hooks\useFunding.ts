import { useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import {
  useApiList,
  useApiQuery,
  useApiCreate,
  useApiUpdate,
  useApiDelete,
  useApiInfinite,
  usePrefetchApiQuery,
  usePrefetchApiList
} from './useApi';
import { 
  InvestorProfile, 
  FundingOpportunity, 
  FundingApplication, 
  Investment 
} from '../services/incubatorApi';
import { QueryParams } from '../types/api';

// Query keys for funding entities
export const FundingKeys = {
  // Investor Profiles
  investorProfiles: ['investor-profiles'] as const,
  investorProfileLists: () => [...FundingKeys.investorProfiles, 'list'] as const,
  investorProfileList: (params?: QueryParams) => [...FundingKeys.investorProfileLists(), params] as const,
  investorProfileDetails: () => [...FundingKeys.investorProfiles, 'detail'] as const,
  investorProfileDetail: (id: number) => [...FundingKeys.investorProfileDetails(), id] as const,

  // Funding Opportunities
  opportunities: ['funding-opportunities'] as const,
  opportunityLists: () => [...FundingKeys.opportunities, 'list'] as const,
  opportunityList: (params?: QueryParams) => [...FundingKeys.opportunityLists(), params] as const,
  opportunityDetails: () => [...FundingKeys.opportunities, 'detail'] as const,
  opportunityDetail: (id: number) => [...FundingKeys.opportunityDetails(), id] as const,

  // Funding Applications
  applications: ['funding-applications'] as const,
  applicationLists: () => [...FundingKeys.applications, 'list'] as const,
  applicationList: (params?: QueryParams) => [...FundingKeys.applicationLists(), params] as const,
  applicationDetails: () => [...FundingKeys.applications, 'detail'] as const,
  applicationDetail: (id: number) => [...FundingKeys.applicationDetails(), id] as const,

  // Investments
  investments: ['investments'] as const,
  investmentLists: () => [...FundingKeys.investments, 'list'] as const,
  investmentList: (params?: QueryParams) => [...FundingKeys.investmentLists(), params] as const,
  investmentDetails: () => [...FundingKeys.investments, 'detail'] as const,
  investmentDetail: (id: number) => [...FundingKeys.investmentDetails(), id] as const,
};

// ============ INVESTOR PROFILES ============

export function useInvestorProfilesList(params?: QueryParams) {
  return useApiList<InvestorProfile>(
    FundingKeys.investorProfileList(params),
    '/incubator/investor-profiles/',
    params
  );
}

export function useInvestorProfilesInfinite(params?: QueryParams) {
  return useApiInfinite<InvestorProfile>(
    FundingKeys.investorProfileList(params),
    '/incubator/investor-profiles/',
    params
  );
}

export function useInvestorProfile(id: number) {
  return useApiQuery<InvestorProfile>(
    FundingKeys.investorProfileDetail(id),
    `/incubator/investor-profiles/${id}/`
  );
}

export function useCreateInvestorProfile() {
  const queryClient = useQueryClient();

  return useApiCreate<InvestorProfile, Partial<InvestorProfile>>(
    '/incubator/investor-profiles/',
    {
      onSuccess: (newProfile) => {
        queryClient.invalidateQueries({ queryKey: FundingKeys.investorProfileLists() });
        queryClient.setQueryData(FundingKeys.investorProfileDetail(newProfile.id), newProfile);
      }
    }
  );
}

export function useUpdateInvestorProfile() {
  const queryClient = useQueryClient();

  return useApiUpdate<InvestorProfile, Partial<InvestorProfile>>(
    (id: number) => `/incubator/investor-profiles/${id}/`,
    {
      onSuccess: (updatedProfile) => {
        queryClient.setQueryData(FundingKeys.investorProfileDetail(updatedProfile.id), updatedProfile);
        queryClient.invalidateQueries({ queryKey: FundingKeys.investorProfileLists() });
      }
    }
  );
}

export function useDeleteInvestorProfile() {
  const queryClient = useQueryClient();

  return useApiDelete(
    (id: number) => `/incubator/investor-profiles/${id}/`,
    {
      onSuccess: (_, id) => {
        queryClient.removeQueries({ queryKey: FundingKeys.investorProfileDetail(id) });
        queryClient.invalidateQueries({ queryKey: FundingKeys.investorProfileLists() });
      }
    }
  );
}

// ============ FUNDING OPPORTUNITIES ============

export function useFundingOpportunitiesList(params?: QueryParams) {
  return useApiList<FundingOpportunity>(
    FundingKeys.opportunityList(params),
    '/incubator/funding-opportunities/',
    params
  );
}

export function useFundingOpportunitiesInfinite(params?: QueryParams) {
  return useApiInfinite<FundingOpportunity>(
    FundingKeys.opportunityList(params),
    '/incubator/funding-opportunities/',
    params
  );
}

export function useFundingOpportunity(id: number) {
  return useApiQuery<FundingOpportunity>(
    FundingKeys.opportunityDetail(id),
    `/incubator/funding-opportunities/${id}/`
  );
}

export function useCreateFundingOpportunity() {
  const queryClient = useQueryClient();

  return useApiCreate<FundingOpportunity, Partial<FundingOpportunity>>(
    '/incubator/funding-opportunities/',
    {
      onSuccess: (newOpportunity) => {
        queryClient.invalidateQueries({ queryKey: FundingKeys.opportunityLists() });
        queryClient.setQueryData(FundingKeys.opportunityDetail(newOpportunity.id), newOpportunity);
      }
    }
  );
}

export function useUpdateFundingOpportunity() {
  const queryClient = useQueryClient();

  return useApiUpdate<FundingOpportunity, Partial<FundingOpportunity>>(
    (id: number) => `/incubator/funding-opportunities/${id}/`,
    {
      onSuccess: (updatedOpportunity) => {
        queryClient.setQueryData(FundingKeys.opportunityDetail(updatedOpportunity.id), updatedOpportunity);
        queryClient.invalidateQueries({ queryKey: FundingKeys.opportunityLists() });
      }
    }
  );
}

export function useDeleteFundingOpportunity() {
  const queryClient = useQueryClient();

  return useApiDelete(
    (id: number) => `/incubator/funding-opportunities/${id}/`,
    {
      onSuccess: (_, id) => {
        queryClient.removeQueries({ queryKey: FundingKeys.opportunityDetail(id) });
        queryClient.invalidateQueries({ queryKey: FundingKeys.opportunityLists() });
      }
    }
  );
}

// ============ FUNDING APPLICATIONS ============

export function useFundingApplicationsList(params?: QueryParams) {
  return useApiList<FundingApplication>(
    FundingKeys.applicationList(params),
    '/incubator/funding-applications/',
    params
  );
}

export function useFundingApplication(id: number) {
  return useApiQuery<FundingApplication>(
    FundingKeys.applicationDetail(id),
    `/incubator/funding-applications/${id}/`
  );
}

export function useCreateFundingApplication() {
  const queryClient = useQueryClient();

  return useApiCreate<FundingApplication, Partial<FundingApplication>>(
    '/incubator/funding-applications/',
    {
      onSuccess: (newApplication) => {
        queryClient.invalidateQueries({ queryKey: FundingKeys.applicationLists() });
        queryClient.setQueryData(FundingKeys.applicationDetail(newApplication.id), newApplication);
      }
    }
  );
}

export function useUpdateFundingApplication() {
  const queryClient = useQueryClient();

  return useApiUpdate<FundingApplication, Partial<FundingApplication>>(
    (id: number) => `/incubator/funding-applications/${id}/`,
    {
      onSuccess: (updatedApplication) => {
        queryClient.setQueryData(FundingKeys.applicationDetail(updatedApplication.id), updatedApplication);
        queryClient.invalidateQueries({ queryKey: FundingKeys.applicationLists() });
      }
    }
  );
}

export function useDeleteFundingApplication() {
  const queryClient = useQueryClient();

  return useApiDelete(
    (id: number) => `/incubator/funding-applications/${id}/`,
    {
      onSuccess: (_, id) => {
        queryClient.removeQueries({ queryKey: FundingKeys.applicationDetail(id) });
        queryClient.invalidateQueries({ queryKey: FundingKeys.applicationLists() });
      }
    }
  );
}

// ============ INVESTMENTS ============

export function useInvestmentsList(params?: QueryParams) {
  return useApiList<Investment>(
    FundingKeys.investmentList(params),
    '/incubator/investments/',
    params
  );
}

export function useInvestment(id: number) {
  return useApiQuery<Investment>(
    FundingKeys.investmentDetail(id),
    `/incubator/investments/${id}/`
  );
}

export function useCreateInvestment() {
  const queryClient = useQueryClient();

  return useApiCreate<Investment, Partial<Investment>>(
    '/incubator/investments/',
    {
      onSuccess: (newInvestment) => {
        queryClient.invalidateQueries({ queryKey: FundingKeys.investmentLists() });
        queryClient.setQueryData(FundingKeys.investmentDetail(newInvestment.id), newInvestment);
      }
    }
  );
}

export function useUpdateInvestment() {
  const queryClient = useQueryClient();

  return useApiUpdate<Investment, Partial<Investment>>(
    (id: number) => `/incubator/investments/${id}/`,
    {
      onSuccess: (updatedInvestment) => {
        queryClient.setQueryData(FundingKeys.investmentDetail(updatedInvestment.id), updatedInvestment);
        queryClient.invalidateQueries({ queryKey: FundingKeys.investmentLists() });
      }
    }
  );
}

export function useDeleteInvestment() {
  const queryClient = useQueryClient();

  return useApiDelete(
    (id: number) => `/incubator/investments/${id}/`,
    {
      onSuccess: (_, id) => {
        queryClient.removeQueries({ queryKey: FundingKeys.investmentDetail(id) });
        queryClient.invalidateQueries({ queryKey: FundingKeys.investmentLists() });
      }
    }
  );
}

// ============ SPECIALIZED ACTIONS ============

/**
 * Hook for approving/rejecting funding applications
 */
export function useApproveFundingApplication() {
  const queryClient = useQueryClient();

  return useApiUpdate<FundingApplication, { status: string; notes?: string }>(
    (id: number) => `/incubator/funding-applications/${id}/approve/`,
    {
      onSuccess: (updatedApplication) => {
        queryClient.setQueryData(FundingKeys.applicationDetail(updatedApplication.id), updatedApplication);
        queryClient.invalidateQueries({ queryKey: FundingKeys.applicationLists() });
      }
    }
  );
}

/**
 * Hook for creating investment from approved application
 */
export function useCreateInvestmentFromApplication() {
  const queryClient = useQueryClient();

  return useApiCreate<Investment, { application_id: number; amount: number; terms?: string }>(
    '/incubator/investments/from-application/',
    {
      onSuccess: (newInvestment) => {
        queryClient.invalidateQueries({ queryKey: FundingKeys.investmentLists() });
        queryClient.invalidateQueries({ queryKey: FundingKeys.applicationLists() });
        queryClient.setQueryData(FundingKeys.investmentDetail(newInvestment.id), newInvestment);
      }
    }
  );
}
