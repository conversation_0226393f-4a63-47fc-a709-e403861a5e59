/**
 * Automatic AI API Service
 * Provides utilities and API calls for automatic AI system management
 */

import { apiRequest } from './api';

export interface AutomaticAIResponse {
  success: boolean;
  message: string;
  data?: any;
}

/**
 * Automatic AI API endpoints
 */
export const automaticAiApi = {
  /**
   * Start automatic AI services
   */
  startAutomaticAI: (): Promise<AutomaticAIResponse> =>
    apiRequest<AutomaticAIResponse>('/ai/automatic/start/', 'POST'),

  /**
   * Stop automatic AI services
   */
  stopAutomaticAI: (): Promise<AutomaticAIResponse> =>
    apiRequest<AutomaticAIResponse>('/ai/automatic/stop/', 'POST'),

  /**
   * Get automatic AI status
   */
  getAutomaticAIStatus: (): Promise<any> =>
    apiRequest<any>('/ai/automatic/status/'),

  /**
   * Trigger manual AI analysis
   */
  triggerManualAnalysis: (): Promise<AutomaticAIResponse> =>
    apiRequest<AutomaticAIResponse>('/ai/automatic/trigger/', 'POST'),

  /**
   * Get AI workers status
   */
  getWorkersStatus: (): Promise<any> =>
    apiRequest<any>('/ai/automatic/workers/'),

  /**
   * Get recent AI actions
   */
  getRecentActions: (limit?: number): Promise<any> =>
    apiRequest<any>(`/ai/automatic/actions/${limit ? `?limit=${limit}` : ''}`),

  /**
   * Get AI performance metrics
   */
  getPerformanceMetrics: (): Promise<any> =>
    apiRequest<any>('/ai/automatic/performance/'),
};

/**
 * Utility functions for automatic AI
 */
export const automaticAiUtils = {
  /**
   * Format timestamp to relative time (e.g., "2 minutes ago")
   */
  timeAgo: (timestamp: string): string => {
    try {
      const now = new Date();
      const time = new Date(timestamp);
      const diffInSeconds = Math.floor((now.getTime() - time.getTime()) / 1000);

      if (diffInSeconds < 60) {
        return `${diffInSeconds} seconds ago`;
      }

      const diffInMinutes = Math.floor(diffInSeconds / 60);
      if (diffInMinutes < 60) {
        return `${diffInMinutes} minute${diffInMinutes !== 1 ? 's' : ''} ago`;
      }

      const diffInHours = Math.floor(diffInMinutes / 60);
      if (diffInHours < 24) {
        return `${diffInHours} hour${diffInHours !== 1 ? 's' : ''} ago`;
      }

      const diffInDays = Math.floor(diffInHours / 24);
      if (diffInDays < 30) {
        return `${diffInDays} day${diffInDays !== 1 ? 's' : ''} ago`;
      }

      const diffInMonths = Math.floor(diffInDays / 30);
      if (diffInMonths < 12) {
        return `${diffInMonths} month${diffInMonths !== 1 ? 's' : ''} ago`;
      }

      const diffInYears = Math.floor(diffInMonths / 12);
      return `${diffInYears} year${diffInYears !== 1 ? 's' : ''} ago`;
    } catch (error) {
      return 'Unknown time';
    }
  },

  /**
   * Format duration in milliseconds to human readable format
   */
  formatDuration: (milliseconds: number): string => {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    }
    if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    }
    return `${seconds}s`;
  },

  /**
   * Get status color based on health status
   */
  getStatusColor: (status: string): string => {
    switch (status?.toLowerCase()) {
      case 'excellent':
      case 'active':
      case 'online':
        return 'text-green-600 bg-green-100';
      case 'good':
      case 'working':
        return 'text-blue-600 bg-blue-100';
      case 'warning':
      case 'fair':
      case 'idle':
        return 'text-orange-600 bg-orange-100';
      case 'critical':
      case 'poor':
      case 'error':
      case 'offline':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  },

  /**
   * Format percentage value
   */
  formatPercentage: (value: number | string): string => {
    if (typeof value === 'string') {
      return value.includes('%') ? value : `${value}%`;
    }
    return `${Math.round(value)}%`;
  },

  /**
   * Format large numbers (e.g., 1000 -> 1K)
   */
  formatNumber: (num: number): string => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  },

  /**
   * Get action type icon
   */
  getActionTypeIcon: (type: string): string => {
    switch (type?.toLowerCase()) {
      case 'enhancement':
      case 'improve':
        return '✨';
      case 'risk':
      case 'warning':
        return '⚠️';
      case 'opportunity':
      case 'success':
        return '🎯';
      case 'analysis':
        return '📊';
      case 'recommendation':
        return '💡';
      default:
        return '🤖';
    }
  },

  /**
   * Validate AI response
   */
  isValidResponse: (response: any): boolean => {
    return response && typeof response === 'object' && response.success !== undefined;
  },

  /**
   * Extract error message from API response
   */
  getErrorMessage: (error: any): string => {
    if (typeof error === 'string') {
      return error;
    }
    if (error?.message) {
      return error.message;
    }
    if (error?.detail) {
      return error.detail;
    }
    if (error?.error) {
      return error.error;
    }
    return 'An unknown error occurred';
  }
};

export default automaticAiApi;
