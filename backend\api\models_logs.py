"""
System Logs Models
Models for storing and managing system logs
"""

from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
import uuid


class SystemLog(models.Model):
    """
    Model for storing system logs
    """
    LOG_LEVELS = [
        ('info', 'Info'),
        ('warning', 'Warning'),
        ('error', 'Error'),
        ('success', 'Success'),
    ]
    
    CATEGORIES = [
        ('authentication', 'Authentication'),
        ('database', 'Database'),
        ('api', 'API'),
        ('backup', 'Backup'),
        ('system', 'System'),
        ('user_action', 'User Action'),
        ('security', 'Security'),
        ('performance', 'Performance'),
        ('maintenance', 'Maintenance'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    timestamp = models.DateTimeField(default=timezone.now, db_index=True)
    level = models.CharField(max_length=20, choices=LOG_LEVELS, db_index=True)
    category = models.CharField(max_length=50, choices=CATEGORIES, db_index=True)
    message = models.TextField()
    details = models.TextField(blank=True, null=True)
    source = models.CharField(max_length=100, blank=True, null=True)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True)
    ip_address = models.GenericIPAddressField(blank=True, null=True)
    user_agent = models.TextField(blank=True, null=True)
    request_id = models.CharField(max_length=100, blank=True, null=True)
    
    # Additional metadata
    metadata = models.JSONField(default=dict, blank=True)
    
    class Meta:
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['timestamp', 'level']),
            models.Index(fields=['category', 'timestamp']),
            models.Index(fields=['user', 'timestamp']),
        ]
    
    def __str__(self):
        return f"[{self.level.upper()}] {self.category}: {self.message[:50]}"
    
    @classmethod
    def log_info(cls, message, category='system', user=None, source=None, **kwargs):
        """Log an info message"""
        return cls.objects.create(
            level='info',
            category=category,
            message=message,
            user=user,
            source=source,
            **kwargs
        )
    
    @classmethod
    def log_warning(cls, message, category='system', user=None, source=None, **kwargs):
        """Log a warning message"""
        return cls.objects.create(
            level='warning',
            category=category,
            message=message,
            user=user,
            source=source,
            **kwargs
        )
    
    @classmethod
    def log_error(cls, message, category='system', user=None, source=None, **kwargs):
        """Log an error message"""
        return cls.objects.create(
            level='error',
            category=category,
            message=message,
            user=user,
            source=source,
            **kwargs
        )
    
    @classmethod
    def log_success(cls, message, category='system', user=None, source=None, **kwargs):
        """Log a success message"""
        return cls.objects.create(
            level='success',
            category=category,
            message=message,
            user=user,
            source=source,
            **kwargs
        )


class LoggingMiddleware:
    """
    Middleware to automatically log certain events
    """
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # Log authentication events
        if request.path.startswith('/api/auth/'):
            if request.method == 'POST':
                if 'login' in request.path:
                    # Will be logged after response
                    request._log_login = True
        
        response = self.get_response(request)
        
        # Log after response
        if hasattr(request, '_log_login'):
            if response.status_code == 200:
                SystemLog.log_info(
                    message="User login successful",
                    category='authentication',
                    user=getattr(request, 'user', None) if hasattr(request, 'user') and request.user.is_authenticated else None,
                    ip_address=self.get_client_ip(request),
                    user_agent=request.META.get('HTTP_USER_AGENT', ''),
                    source='auth_middleware'
                )
            else:
                SystemLog.log_warning(
                    message="User login failed",
                    category='authentication',
                    ip_address=self.get_client_ip(request),
                    user_agent=request.META.get('HTTP_USER_AGENT', ''),
                    source='auth_middleware',
                    details=f"Status code: {response.status_code}"
                )
        
        return response
    
    def get_client_ip(self, request):
        """Get client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


# Utility functions for logging common events
def log_user_action(user, action, details=None, ip_address=None):
    """Log a user action"""
    SystemLog.log_info(
        message=f"User performed action: {action}",
        category='user_action',
        user=user,
        details=details,
        ip_address=ip_address,
        source='user_action_logger'
    )

def log_api_error(message, details=None, user=None, source='api'):
    """Log an API error"""
    SystemLog.log_error(
        message=message,
        category='api',
        user=user,
        details=details,
        source=source
    )

def log_security_event(message, details=None, user=None, ip_address=None):
    """Log a security event"""
    SystemLog.log_warning(
        message=message,
        category='security',
        user=user,
        details=details,
        ip_address=ip_address,
        source='security_monitor'
    )

def log_performance_issue(message, details=None, source='performance_monitor'):
    """Log a performance issue"""
    SystemLog.log_warning(
        message=message,
        category='performance',
        details=details,
        source=source
    )

def log_maintenance_event(message, details=None, user=None):
    """Log a maintenance event"""
    SystemLog.log_info(
        message=message,
        category='maintenance',
        user=user,
        details=details,
        source='maintenance_service'
    )
