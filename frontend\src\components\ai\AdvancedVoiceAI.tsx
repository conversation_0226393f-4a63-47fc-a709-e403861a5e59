/**
 * Advanced Voice AI Interface
 * Enhanced voice recognition, synthesis, and command processing with backend integration
 */

import React, { useState, useRef, useEffect } from 'react';
import {
  Mi<PERSON>,
  MicOff,
  Volume2,
  VolumeX,
  Play,
  Pause,
  Square,
  MessageSquare,
  Command,
  Users,
  Download,
  Upload,
  Zap,
  Brain,
  Activity,
  RefreshCw
} from 'lucide-react';
import { useTranslation } from 'react-i18next';


interface AdvancedVoiceAIProps {
  onTranscriptionComplete?: (result: any) => void;
  onCommandExecuted?: (command: any) => void;
  className?: string;
}

interface TranscriptionResult {
  success: boolean;
  transcribed_text: string;
  language: string;
  confidence: number;
  method: string;
}

interface SynthesisResult {
  success: boolean;
  audio_data: string;
  format: string;
  language: string;
  estimated_duration: number;
}

interface SentimentResult {
  sentiment: {
    score: number;
    label: string;
    confidence: number;
  };
  emotion: {
    dominant: string;
    scores: Record<string, number>;
    confidence: number;
  };
  key_phrases: string[];
  speaking_patterns: {
    word_count: number;
    sentence_count: number;
    avg_words_per_sentence: number;
    complexity: string;
  };
  recommendations: string[];
}

export const AdvancedVoiceAI: React.FC<AdvancedVoiceAIProps> = ({
  onTranscriptionComplete,
  onCommandExecuted,
  className = ''
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<'transcribe' | 'synthesize' | 'commands' | 'meeting'>('transcribe');
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [transcriptionResult, setTranscriptionResult] = useState<TranscriptionResult | null>(null);
  const [synthesisResult, setSynthesisResult] = useState<SynthesisResult | null>(null);
  const [sentimentResult, setSentimentResult] = useState<SentimentResult | null>(null);
  const [textToSynthesize, setTextToSynthesize] = useState('');
  const [language, setLanguage] = useState('auto');
  const [isPlaying, setIsPlaying] = useState(false);

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  const tabs = [
    { id: 'transcribe', label: 'Speech to Text', icon: Mic, color: 'text-blue-600' },
    { id: 'synthesize', label: 'Text to Speech', icon: Volume2, color: 'text-green-600' },
    { id: 'commands', label: 'Voice Commands', icon: Command, color: 'text-purple-600' },
    { id: 'meeting', label: 'Meeting Transcription', icon: Users, color: 'text-orange-600' }
  ];

  useEffect(() => {
    return () => {
      if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
        mediaRecorderRef.current.stop();
      }
    };
  }, []);

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' });
        setAudioBlob(audioBlob);
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorder.start();
      setIsRecording(true);
    } catch (error) {
      console.error('Error starting recording:', error);
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  const transcribeAudio = async (audioBlob: Blob) => {
    setIsProcessing(true);
    try {
      const formData = new FormData();
      formData.append('action', 'transcribe');
      formData.append('audio', audioBlob, 'recording.wav');
      formData.append('language', language);

      const response = await fetch('/api/ai/voice-ai/', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error('Transcription failed');
      }

      const data = await response.json();
      setTranscriptionResult(data.result);
      onTranscriptionComplete?.(data.result);

      // Auto-analyze sentiment if transcription successful
      if (data.result.success && data.result.transcribed_text) {
        await analyzeSentiment(data.result.transcribed_text);
      }
    } catch (error) {
      console.error('Transcription error:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const synthesizeSpeech = async () => {
    if (!textToSynthesize.trim()) return;

    setIsProcessing(true);
    try {
      const response = await fetch('/api/ai/voice-ai/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          action: 'synthesize',
          text: textToSynthesize,
          language: language === 'auto' ? 'en' : language,
          voice_style: 'neutral'
        })
      });

      if (!response.ok) {
        throw new Error('Speech synthesis failed');
      }

      const data = await response.json();
      setSynthesisResult(data.result);
    } catch (error) {
      console.error('Speech synthesis error:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const analyzeSentiment = async (text: string) => {
    try {
      const response = await fetch('/api/ai/voice-ai/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          action: 'analyze_sentiment',
          transcribed_text: text
        })
      });

      if (!response.ok) {
        throw new Error('Sentiment analysis failed');
      }

      const data = await response.json();
      setSentimentResult(data.result);
    } catch (error) {
      console.error('Sentiment analysis error:', error);
    }
  };

  const processVoiceCommand = async (text: string) => {
    try {
      const response = await fetch('/api/ai/voice-ai/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          action: 'process_command',
          transcribed_text: text
        })
      });

      if (!response.ok) {
        throw new Error('Command processing failed');
      }

      const data = await response.json();
      onCommandExecuted?.(data.result);
      return data.result;
    } catch (error) {
      console.error('Command processing error:', error);
    }
  };

  const playAudio = () => {
    if (synthesisResult?.audio_data) {
      const audioData = `data:audio/mp3;base64,${synthesisResult.audio_data}`;
      const audio = new Audio(audioData);
      audioRef.current = audio;
      
      audio.onplay = () => setIsPlaying(true);
      audio.onended = () => setIsPlaying(false);
      audio.onerror = () => setIsPlaying(false);
      
      audio.play();
    }
  };

  const stopAudio = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      setIsPlaying(false);
    }
  };

  const getSentimentColor = (label: string) => {
    switch (label) {
      case 'positive': return 'text-green-600';
      case 'negative': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getEmotionColor = (emotion: string) => {
    switch (emotion) {
      case 'joy': return 'text-yellow-600';
      case 'confidence': return 'text-blue-600';
      case 'anger': return 'text-red-600';
      case 'sadness': return 'text-gray-600';
      case 'fear': return 'text-purple-600';
      default: return 'text-gray-500';
    }
  };

  const renderTranscribeTab = () => (
    <div className="space-y-6">
      {/* Recording Controls */}
      <div className="text-center">
        <div className="mb-4">
          <button
            onClick={isRecording ? stopRecording : startRecording}
            disabled={isProcessing}
            className={`w-20 h-20 rounded-full flex items-center justify-center transition-colors ${
              isRecording
                ? 'bg-red-500 hover:bg-red-600 text-white animate-pulse'
                : 'bg-blue-500 hover:bg-blue-600 text-white'
            } disabled:opacity-50`}
          >
            {isRecording ? <Square className="w-8 h-8" /> : <Mic className="w-8 h-8" />}
          </button>
        </div>
        
        <p className="text-sm text-gray-600 dark:text-gray-400">
          {isRecording ? 'Recording... Click to stop' : 'Click to start recording'}
        </p>
        
        {audioBlob && !isRecording && (
          <button
            onClick={() => transcribeAudio(audioBlob)}
            disabled={isProcessing}
            className="mt-4 px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 flex items-center space-x-2 mx-auto"
          >
            <RefreshCw className={`w-4 h-4 ${isProcessing ? 'animate-spin' : ''}`} />
            <span>{isProcessing ? 'Transcribing...' : 'Transcribe Audio'}</span>
          </button>
        )}
      </div>

      {/* Language Selection */}
      <div className="flex justify-center">
        <select
          value={language}
          onChange={(e) => setLanguage(e.target.value)}
          className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
        >
          <option value="auto">Auto Detect</option>
          <option value="en">English</option>
          <option value="ar">Arabic</option>
        </select>
      </div>

      {/* Transcription Results */}
      {transcriptionResult && (
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Transcription Result
          </h3>
          
          {transcriptionResult.success ? (
            <div className="space-y-4">
              <div className="bg-gray-50 dark:bg-gray-700 rounded p-4">
                <p className="text-gray-900 dark:text-white">
                  {transcriptionResult.transcribed_text}
                </p>
              </div>
              
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Language:</span>
                  <span className="ml-2 font-medium">{transcriptionResult.language}</span>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Confidence:</span>
                  <span className="ml-2 font-medium">{(transcriptionResult.confidence * 100).toFixed(1)}%</span>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Method:</span>
                  <span className="ml-2 font-medium">{transcriptionResult.method}</span>
                </div>
              </div>

              {/* Voice Command Processing */}
              <div className="pt-4 border-t border-gray-200 dark:border-gray-600">
                <button
                  onClick={() => processVoiceCommand(transcriptionResult.transcribed_text)}
                  className="flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
                >
                  <Command className="w-4 h-4" />
                  <span>Process as Voice Command</span>
                </button>
              </div>
            </div>
          ) : (
            <div className="text-red-600 dark:text-red-400">
              Transcription failed: {transcriptionResult.error || 'Unknown error'}
            </div>
          )}
        </div>
      )}

      {/* Sentiment Analysis Results */}
      {sentimentResult && (
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Voice Sentiment Analysis
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-3">Sentiment & Emotion</h4>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Sentiment:</span>
                  <span className={`font-medium ${getSentimentColor(sentimentResult.sentiment.label)}`}>
                    {sentimentResult.sentiment.label} ({(sentimentResult.sentiment.score * 100).toFixed(0)}%)
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Emotion:</span>
                  <span className={`font-medium ${getEmotionColor(sentimentResult.emotion.dominant)}`}>
                    {sentimentResult.emotion.dominant}
                  </span>
                </div>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-3">Speaking Patterns</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Words:</span>
                  <span>{sentimentResult.speaking_patterns.word_count}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Sentences:</span>
                  <span>{sentimentResult.speaking_patterns.sentence_count}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Complexity:</span>
                  <span className="capitalize">{sentimentResult.speaking_patterns.complexity}</span>
                </div>
              </div>
            </div>
          </div>

          {sentimentResult.key_phrases.length > 0 && (
            <div className="mt-4">
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">Key Phrases</h4>
              <div className="flex flex-wrap gap-2">
                {sentimentResult.key_phrases.map((phrase, index) => (
                  <span
                    key={index}
                    className="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-sm"
                  >
                    {phrase}
                  </span>
                ))}
              </div>
            </div>
          )}

          {sentimentResult.recommendations.length > 0 && (
            <div className="mt-4">
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">Recommendations</h4>
              <ul className="space-y-1">
                {sentimentResult.recommendations.map((rec, index) => (
                  <li key={index} className="text-sm text-gray-600 dark:text-gray-400 flex items-start">
                    <Zap className="w-4 h-4 text-yellow-500 mt-0.5 mr-2 flex-shrink-0" />
                    {rec}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}
    </div>
  );

  return (
    <div className={`advanced-voice-ai glass-light ${className}`}>
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center space-x-3">
            <Brain className="w-8 h-8 text-purple-600" />
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                Advanced Voice AI
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Enhanced speech recognition, synthesis, and voice commands
              </p>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 mb-6 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex-1 flex items-center justify-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                  activeTab === tab.id
                    ? 'bg-white dark:bg-gray-700 shadow-sm'
                    : 'hover:bg-gray-200 dark:hover:bg-gray-700'
                }`}
              >
                <Icon className={`w-4 h-4 ${tab.color}`} />
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {tab.label}
                </span>
              </button>
            );
          })}
        </div>

        {/* Content */}
        <div className="min-h-96">
          {activeTab === 'transcribe' && renderTranscribeTab()}
          {/* Other tabs can be implemented similarly */}
        </div>
      </div>
  );
};
