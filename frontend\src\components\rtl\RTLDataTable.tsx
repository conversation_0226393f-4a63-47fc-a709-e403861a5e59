import React from 'react';
import { useLanguage } from '../../hooks/useLanguage';
interface RTLDataTableProps {
  children: React.ReactNode;
  className?: string;
  dir?: 'ltr' | 'rtl';
  style?: React.CSSProperties;
}

/**
 * RTL-aware data table component
 *
 * This component automatically handles RTL text direction based on the current language
 * and applies appropriate styling for RTL tables (right-aligned text, flipped column order, etc.)
 */
const RTLDataTable: React.FC<RTLDataTableProps> = ({
  children,
  className = '',
  dir,
  style = {},
}) => {
  const { language, isRTL: contextIsRTL } = useLanguage();
  const isRTL = dir ? dir === 'rtl' : contextIsRTL;

  // Apply RTL-specific styles
  const rtlStyles: React.CSSProperties = isRTL
    ? {
        direction: 'rtl',
        textAlign: 'right',
        ...style,
      }
    : {
        direction: 'ltr',
        textAlign: 'left',
        ...style,
      };

  return (
    <div
      className={`rtl-data-table ${className}`}
      dir={isRTL ? 'rtl' : 'ltr'}
      style={rtlStyles}
    >
      {children}
    </div>
  );
};

export default RTLDataTable;
