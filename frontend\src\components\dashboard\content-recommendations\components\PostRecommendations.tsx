import React from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../hooks/useLanguage';
import { FileText, User } from 'lucide-react';
import RecommendationSection from './RecommendationSection';
interface PostRecommendation {
  id: number;
  title: string;
  author: string;
}

interface PostRecommendationsProps {
  posts: PostRecommendation[];
}

const PostRecommendations: React.FC<PostRecommendationsProps> = ({ posts  }) => {
  const { t } = useTranslation();
  const { language, isRTL } = useLanguage();

  if (posts.length === 0) return null;

  return (
    <RecommendationSection
      title={t('dashboard.contentRecommendations.postsYouMightLike')}
      icon={<FileText size={20} className={`${language === 'ar' ? 'ml-2' : 'mr-2'} text-indigo-400`} />}
      viewAllLink="/posts"
      viewAllText={t('dashboard.contentRecommendations.viewAllPosts')}
    >
      {posts.map(post => (
        <div key={post.id} className="bg-indigo-800/30 p-3 rounded">
          <Link to={`/posts/${post.id}`} className="text-purple-300 hover:text-purple-200 font-medium">
            {post.title}
          </Link>
          <div className={`flex items-center mt-2 text-sm text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
            <User size={14} className={language === 'ar' ? 'ml-1' : 'mr-1'} />
            <span>{t('dashboard.contentRecommendations.by')} {post.author}</span>
          </div>
        </div>
      ))}
    </RecommendationSection>
  );
};

export default PostRecommendations;
