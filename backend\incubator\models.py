from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from api.storage import OptimizedImageStorage
from api.models import Tag

# Import base models
from .models_base import BusinessIdea, ProgressUpdate

# Import milestone models to make them available when models.py is imported
from .models_milestone import (
    BusinessMilestone, BusinessGoal, MentorRecommendation, BusinessAnalytics
)

# Import business plan analytics models
from .models_business_plan_analytics import (
    BusinessPlanSession, BusinessPlanCollaboration, BusinessPlanExport,
    BusinessPlanAnalytics, TemplateSuccessMetrics
)


class IncubatorResource(models.Model):
    """Learning resources specifically for the business incubator"""

    RESOURCE_TYPES = (
        ('article', 'Article'),
        ('video', 'Video'),
        ('template', 'Template'),
        ('tool', 'Tool'),
        ('course', 'Course'),
        ('ebook', 'E-Book'),
        ('other', 'Other'),
    )

    CATEGORY_CHOICES = (
        ('ideation', 'Business Ideation'),
        ('validation', 'Market Validation'),
        ('planning', 'Business Planning'),
        ('finance', 'Financial Planning'),
        ('marketing', 'Marketing'),
        ('legal', 'Legal & Compliance'),
        ('operations', 'Operations'),
        ('technology', 'Technology'),
        ('growth', 'Growth & Scaling'),
        ('other', 'Other'),
    )

    title = models.CharField(max_length=200)
    description = models.TextField()
    resource_type = models.CharField(max_length=20, choices=RESOURCE_TYPES)
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES)
    url = models.URLField()
    image = models.ImageField(upload_to='incubator_resource_images/', storage=OptimizedImageStorage(), blank=True, null=True)
    file = models.FileField(upload_to='incubator_resource_files/', blank=True, null=True)

    author = models.ForeignKey(User, on_delete=models.CASCADE, related_name='incubator_resources')
    tags = models.ManyToManyField(Tag, related_name='incubator_resources', blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title

    class Meta:
        ordering = ['-created_at']


class MentorProfile(models.Model):
    """Profile for mentors in the business incubator"""

    AVAILABILITY_CHOICES = (
        ('high', 'High - 5+ hours per week'),
        ('medium', 'Medium - 2-5 hours per week'),
        ('low', 'Low - 1-2 hours per week'),
        ('limited', 'Limited - Less than 1 hour per week'),
    )

    EXPERTISE_LEVEL_CHOICES = (
        ('beginner', 'Beginner'),
        ('intermediate', 'Intermediate'),
        ('advanced', 'Advanced'),
        ('expert', 'Expert'),
    )

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='mentor_profile')
    bio = models.TextField(help_text="Professional background and experience")
    company = models.CharField(max_length=200, blank=True, null=True)
    position = models.CharField(max_length=200, blank=True, null=True)
    years_of_experience = models.PositiveIntegerField(default=0)
    linkedin_profile = models.URLField(blank=True, null=True)
    website = models.URLField(blank=True, null=True)

    availability = models.CharField(max_length=20, choices=AVAILABILITY_CHOICES, default='medium')
    max_mentees = models.PositiveIntegerField(default=3, help_text="Maximum number of mentees willing to take on")
    is_accepting_mentees = models.BooleanField(default=True)

    is_verified = models.BooleanField(default=False, help_text="Whether the mentor has been verified by administrators")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Mentor: {self.user.username}"

    class Meta:
        ordering = ['-created_at']


class MentorExpertise(models.Model):
    """Areas of expertise for mentors"""

    EXPERTISE_CATEGORIES = (
        ('business_strategy', 'Business Strategy'),
        ('marketing', 'Marketing & Sales'),
        ('finance', 'Finance & Accounting'),
        ('operations', 'Operations & Logistics'),
        ('technology', 'Technology & Development'),
        ('product', 'Product Management'),
        ('legal', 'Legal & Compliance'),
        ('hr', 'Human Resources'),
        ('fundraising', 'Fundraising & Investment'),
        ('international', 'International Business'),
        ('ecommerce', 'E-Commerce'),
        ('social_impact', 'Social Impact'),
        ('other', 'Other'),
    )

    mentor = models.ForeignKey(MentorProfile, on_delete=models.CASCADE, related_name='expertise_areas')
    category = models.CharField(max_length=50, choices=EXPERTISE_CATEGORIES)
    specific_expertise = models.CharField(max_length=200, help_text="Specific expertise within this category")
    level = models.CharField(max_length=20, choices=MentorProfile.EXPERTISE_LEVEL_CHOICES, default='intermediate')
    years_experience = models.PositiveIntegerField(default=0)

    def __str__(self):
        return f"{self.get_category_display()}: {self.specific_expertise}"

    class Meta:
        unique_together = ('mentor', 'category', 'specific_expertise')
        ordering = ['category', '-level']


class MentorshipApplication(models.Model):
    """Applications for mentorship in the business incubator"""

    STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
    )

    PREFERRED_COMMUNICATION_CHOICES = (
        ('video', 'Video Call'),
        ('phone', 'Phone Call'),
        ('email', 'Email'),
        ('chat', 'Chat/Messaging'),
        ('in_person', 'In Person'),
    )

    business_idea = models.ForeignKey(BusinessIdea, on_delete=models.CASCADE, related_name='mentorship_applications')
    applicant = models.ForeignKey(User, on_delete=models.CASCADE, related_name='mentorship_applications')
    preferred_mentor = models.ForeignKey(MentorProfile, on_delete=models.SET_NULL, null=True, blank=True, related_name='preferred_applications')

    goals = models.TextField(help_text="What do you hope to achieve with mentorship?")
    specific_areas = models.TextField(help_text="What specific areas do you need help with?")
    commitment = models.TextField(help_text="How much time can you commit to working with a mentor?")
    preferred_communication = models.CharField(max_length=20, choices=PREFERRED_COMMUNICATION_CHOICES, default='video')
    preferred_expertise = models.CharField(max_length=50, choices=MentorExpertise.EXPERTISE_CATEGORIES, blank=True, null=True)

    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    admin_notes = models.TextField(blank=True, null=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Mentorship for {self.business_idea.title}"

    class Meta:
        ordering = ['-created_at']


class MentorshipMatch(models.Model):
    """Matches between mentors and mentees"""

    STATUS_CHOICES = (
        ('active', 'Active'),
        ('completed', 'Completed'),
        ('paused', 'Paused'),
        ('terminated', 'Terminated'),
    )

    mentor = models.ForeignKey(MentorProfile, on_delete=models.CASCADE, related_name='mentorship_matches')
    mentee = models.ForeignKey(User, on_delete=models.CASCADE, related_name='mentorship_matches')
    business_idea = models.ForeignKey(BusinessIdea, on_delete=models.CASCADE, related_name='mentorship_matches')
    application = models.OneToOneField(MentorshipApplication, on_delete=models.SET_NULL, null=True, blank=True, related_name='resulting_match')

    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    start_date = models.DateField(auto_now_add=True)
    end_date = models.DateField(null=True, blank=True)

    goals = models.TextField()
    focus_areas = models.TextField()

    mentee_notes = models.TextField(blank=True, null=True, help_text="Private notes for the mentee")
    mentor_notes = models.TextField(blank=True, null=True, help_text="Private notes for the mentor")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Match: {self.mentor.user.username} mentoring {self.mentee.username}"

    class Meta:
        verbose_name_plural = "Mentorship Matches"
        ordering = ['-created_at']


class MentorshipSession(models.Model):
    """Individual mentorship sessions"""

    STATUS_CHOICES = (
        ('scheduled', 'Scheduled'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
        ('rescheduled', 'Rescheduled'),
        ('in_progress', 'In Progress'),
    )

    SESSION_TYPE_CHOICES = (
        ('video', 'Video Call'),
        ('phone', 'Phone Call'),
        ('in_person', 'In Person'),
        ('chat', 'Chat/Messaging'),
    )

    VIDEO_PROVIDER_CHOICES = (
        ('zoom', 'Zoom'),
        ('google_meet', 'Google Meet'),
        ('microsoft_teams', 'Microsoft Teams'),
        ('jitsi', 'Jitsi Meet'),
        ('custom', 'Custom Link'),
    )

    mentorship_match = models.ForeignKey(MentorshipMatch, on_delete=models.CASCADE, related_name='sessions')
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True, null=True)

    # Scheduling fields
    scheduled_at = models.DateTimeField()
    duration_minutes = models.PositiveIntegerField(default=60)
    session_type = models.CharField(max_length=20, choices=SESSION_TYPE_CHOICES, default='video')
    location = models.CharField(max_length=200, blank=True, null=True, help_text="Physical location or virtual meeting link")

    # Video conferencing fields
    video_provider = models.CharField(max_length=20, choices=VIDEO_PROVIDER_CHOICES, blank=True, null=True)
    meeting_id = models.CharField(max_length=200, blank=True, null=True, help_text="Meeting ID for video conferencing")
    meeting_password = models.CharField(max_length=50, blank=True, null=True, help_text="Password for video conferencing")
    meeting_link = models.URLField(blank=True, null=True, help_text="Direct link to join the meeting")

    # Reminders
    reminder_sent = models.BooleanField(default=False)
    reminder_sent_at = models.DateTimeField(blank=True, null=True)

    # Calendar integration
    mentor_calendar_event_id = models.CharField(max_length=200, blank=True, null=True, help_text="Calendar event ID for mentor")
    mentee_calendar_event_id = models.CharField(max_length=200, blank=True, null=True, help_text="Calendar event ID for mentee")
    calendar_provider = models.CharField(max_length=50, blank=True, null=True, help_text="Calendar provider (google_calendar, outlook, etc.)")

    # Status
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='scheduled')

    # Notes
    mentor_notes = models.TextField(blank=True, null=True)
    mentee_notes = models.TextField(blank=True, null=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    started_at = models.DateTimeField(blank=True, null=True)
    ended_at = models.DateTimeField(blank=True, null=True)

    def __str__(self):
        return f"Session: {self.title} ({self.mentorship_match})"

    class Meta:
        ordering = ['-scheduled_at']


class MentorshipFeedback(models.Model):
    """Feedback for mentorship sessions"""

    RATING_CHOICES = (
        (1, '1 - Poor'),
        (2, '2 - Below Average'),
        (3, '3 - Average'),
        (4, '4 - Good'),
        (5, '5 - Excellent'),
    )

    CATEGORY_CHOICES = (
        ('knowledge', 'Knowledge & Expertise'),
        ('communication', 'Communication Skills'),
        ('helpfulness', 'Helpfulness'),
        ('preparation', 'Preparation & Organization'),
        ('responsiveness', 'Responsiveness'),
        ('overall', 'Overall Experience'),
    )

    session = models.ForeignKey(MentorshipSession, on_delete=models.CASCADE, related_name='feedback')
    provided_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='provided_feedback')
    is_from_mentee = models.BooleanField(default=True)

    # Overall rating
    rating = models.PositiveSmallIntegerField(choices=RATING_CHOICES)

    # Detailed ratings
    knowledge_rating = models.PositiveSmallIntegerField(choices=RATING_CHOICES, blank=True, null=True, help_text="Rating for knowledge and expertise")
    communication_rating = models.PositiveSmallIntegerField(choices=RATING_CHOICES, blank=True, null=True, help_text="Rating for communication skills")
    helpfulness_rating = models.PositiveSmallIntegerField(choices=RATING_CHOICES, blank=True, null=True, help_text="Rating for helpfulness")
    preparation_rating = models.PositiveSmallIntegerField(choices=RATING_CHOICES, blank=True, null=True, help_text="Rating for preparation and organization")
    responsiveness_rating = models.PositiveSmallIntegerField(choices=RATING_CHOICES, blank=True, null=True, help_text="Rating for responsiveness")

    # Feedback text
    comments = models.TextField()
    areas_of_improvement = models.TextField(blank=True, null=True)
    highlights = models.TextField(blank=True, null=True, help_text="What went particularly well in this session")

    # Session outcomes
    goals_achieved = models.BooleanField(default=False, help_text="Were the session goals achieved?")
    follow_up_needed = models.BooleanField(default=False, help_text="Is follow-up needed?")
    follow_up_notes = models.TextField(blank=True, null=True, help_text="Notes for follow-up")

    # Privacy settings
    is_private = models.BooleanField(default=False, help_text="If true, feedback is only visible to administrators")
    share_with_mentor = models.BooleanField(default=True, help_text="Share this feedback with the mentor")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Feedback for {self.session} by {self.provided_by.username}"

    class Meta:
        ordering = ['-created_at']


class InvestorProfile(models.Model):
    """Profile for investors interested in funding business ideas"""

    INVESTOR_TYPES = (
        ('angel', 'Angel Investor'),
        ('vc', 'Venture Capital'),
        ('corporate', 'Corporate Investor'),
        ('government', 'Government Fund'),
        ('crowdfunding', 'Crowdfunding Platform'),
        ('other', 'Other'),
    )

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='investor_profile')
    investor_type = models.CharField(max_length=20, choices=INVESTOR_TYPES)
    company_name = models.CharField(max_length=200, blank=True, null=True)
    bio = models.TextField(help_text="Brief description of the investor")
    investment_focus = models.TextField(help_text="Areas of interest for investment")
    investment_range_min = models.DecimalField(max_digits=12, decimal_places=2, help_text="Minimum investment amount in USD", validators=[MinValueValidator(0)])
    investment_range_max = models.DecimalField(max_digits=12, decimal_places=2, help_text="Maximum investment amount in USD", validators=[MinValueValidator(0)])
    linkedin_profile = models.URLField(blank=True, null=True)
    website = models.URLField(blank=True, null=True)
    is_verified = models.BooleanField(default=False, help_text="Whether the investor has been verified by administrators")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username} - {self.get_investor_type_display()}"

    class Meta:
        ordering = ['-created_at']


class FundingOpportunity(models.Model):
    """Funding opportunities posted by investors or administrators"""

    FUNDING_TYPES = (
        ('grant', 'Grant'),
        ('equity', 'Equity Investment'),
        ('loan', 'Loan'),
        ('convertible', 'Convertible Note'),
        ('prize', 'Competition Prize'),
        ('crowdfunding', 'Crowdfunding'),
        ('other', 'Other'),
    )

    STATUS_CHOICES = (
        ('active', 'Active'),
        ('closed', 'Closed'),
        ('draft', 'Draft'),
    )

    title = models.CharField(max_length=200)
    description = models.TextField()
    funding_type = models.CharField(max_length=20, choices=FUNDING_TYPES)
    amount = models.DecimalField(max_digits=12, decimal_places=2, help_text="Amount in USD", validators=[MinValueValidator(0)])
    provider = models.ForeignKey(User, on_delete=models.CASCADE, related_name='provided_funding_opportunities')
    eligibility_criteria = models.TextField(help_text="Who can apply for this funding")
    application_process = models.TextField(help_text="How to apply for this funding")
    application_deadline = models.DateField(help_text="Deadline for applications")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.title} - {self.get_funding_type_display()}"

    class Meta:
        ordering = ['-created_at']
        verbose_name_plural = "Funding Opportunities"


class FundingApplication(models.Model):
    """Applications submitted by entrepreneurs for funding opportunities"""

    STATUS_CHOICES = (
        ('pending', 'Pending Review'),
        ('shortlisted', 'Shortlisted'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('funded', 'Funded'),
    )

    business_idea = models.ForeignKey(BusinessIdea, on_delete=models.CASCADE, related_name='funding_applications')
    funding_opportunity = models.ForeignKey(FundingOpportunity, on_delete=models.CASCADE, related_name='applications')
    applicant = models.ForeignKey(User, on_delete=models.CASCADE, related_name='funding_applications')

    pitch = models.TextField(help_text="Pitch for why this business idea deserves funding")
    requested_amount = models.DecimalField(max_digits=12, decimal_places=2, help_text="Amount requested in USD", validators=[MinValueValidator(0)])
    use_of_funds = models.TextField(help_text="How the funds will be used")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    reviewer_notes = models.TextField(blank=True, null=True, help_text="Private notes for reviewers")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Funding application for {self.business_idea.title}"

    class Meta:
        ordering = ['-created_at']


class Investment(models.Model):
    """Investments made in business ideas"""

    INVESTMENT_TYPES = (
        ('equity', 'Equity Investment'),
        ('loan', 'Loan'),
        ('convertible', 'Convertible Note'),
        ('grant', 'Grant'),
        ('other', 'Other'),
    )

    STATUS_CHOICES = (
        ('proposed', 'Proposed'),
        ('negotiating', 'Negotiating'),
        ('accepted', 'Accepted'),
        ('completed', 'Completed'),
        ('declined', 'Declined'),
    )

    business_idea = models.ForeignKey(BusinessIdea, on_delete=models.CASCADE, related_name='investments')
    investor = models.ForeignKey(User, on_delete=models.CASCADE, related_name='investments_made')
    investment_type = models.CharField(max_length=20, choices=INVESTMENT_TYPES)
    amount = models.DecimalField(max_digits=12, decimal_places=2, help_text="Amount in USD", validators=[MinValueValidator(0)])
    equity_percentage = models.DecimalField(max_digits=5, decimal_places=2, blank=True, null=True, help_text="Percentage of equity", validators=[MinValueValidator(0), MaxValueValidator(100)])
    terms = models.TextField(help_text="Terms of the investment")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='proposed')

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    completed_at = models.DateTimeField(blank=True, null=True)

    def __str__(self):
        return f"Investment in {self.business_idea.title} by {self.investor.username}"

    class Meta:
        ordering = ['-created_at']
