import React from 'react';
import { Bar } from 'react-chartjs-2';

import { useTranslation } from 'react-i18next';
interface ActivityChartProps {
  chartData: {
    labels: string[];
    datasets: {
      label: string;
      data: number[];
      backgroundColor: string;
      borderColor: string;
      borderWidth: number;
    }[];
  } | null;
}

const ActivityChart: React.FC<ActivityChartProps> = ({ chartData  }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  if (!chartData) return null;

  return (
    <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50">
      <h3 className="text-lg font-semibold mb-4">Activity Over Time (Last 7 Days)</h3>
      <div className="h-64">
        <Bar
          data={chartData}
          options={{
            responsive: true,
            maintainAspectRatio: false,
            scales: {
              y: {
                beginAtZero: true,
                ticks: {
                  color: '#e2e8f0',
                },
                grid: {
                  color: 'rgba(226, 232, 240, 0.1)',
                },
              },
              x: {
                ticks: {
                  color: '#e2e8f0',
                },
                grid: {
                  color: 'rgba(226, 232, 240, 0.1)',
                },
              },
            },
            plugins: {
              legend: {
                labels: {
                  color: '#e2e8f0',
                },
              },
              title: {
                display: true,
                text: t("dashboard.posts.and.comments", "Posts and Comments"),
                color: '#e2e8f0',
              },
            },
          }}
        />
      </div>
    </div>
  );
};

export default ActivityChart;
