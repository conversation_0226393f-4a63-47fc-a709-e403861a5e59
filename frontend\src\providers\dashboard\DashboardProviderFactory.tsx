/**
 * Dashboard Provider Factory
 * Provides the appropriate dashboard provider based on user role
 * Consolidates all role-specific dashboard providers
 */

import React from 'react';
import { useAppSelector } from '../../store/hooks';
import { getDashboardRole } from '../../utils/dashboardUtils';
import { DashboardRole } from '../../types/dashboard';

// Import all role-specific providers
import UserDashboardProvider from './UserDashboardProvider';
import AdminDashboardProvider from './AdminDashboardProvider';
import SuperAdminDashboardProvider from './SuperAdminDashboardProvider';
import ModeratorDashboardProvider from './ModeratorDashboardProvider';

interface DashboardProviderFactoryProps {
  children: React.ReactNode;
  forceRole?: DashboardRole; // For testing or specific role override
}

/**
 * Factory component that provides the appropriate dashboard provider
 * based on the current user's role
 */
const DashboardProviderFactory: React.FC<DashboardProviderFactoryProps> = ({
  children,
  forceRole,
}) => {
  const { user } = useAppSelector(state => state.auth);
  const role = forceRole || getDashboardRole(user);

  // Return the appropriate provider based on role
  switch (role) {
    case 'super_admin':
      return (
        <SuperAdminDashboardProvider>
          {children}
        </SuperAdminDashboardProvider>
      );

    case 'admin':
      return (
        <AdminDashboardProvider>
          {children}
        </AdminDashboardProvider>
      );

    case 'moderator':
      return (
        <ModeratorDashboardProvider>
          {children}
        </ModeratorDashboardProvider>
      );

    case 'mentor':
      // For now, mentors use the user provider with mentor-specific data
      // Can be extended with a dedicated MentorDashboardProvider later
      return (
        <UserDashboardProvider>
          {children}
        </UserDashboardProvider>
      );

    case 'investor':
      // For now, investors use the user provider with investor-specific data
      // Can be extended with a dedicated InvestorDashboardProvider later
      return (
        <UserDashboardProvider>
          {children}
        </UserDashboardProvider>
      );

    case 'user':
    default:
      return (
        <UserDashboardProvider>
          {children}
        </UserDashboardProvider>
      );
  }
};

/**
 * Hook to get dashboard data based on current user role
 * This provides a unified interface regardless of the underlying provider
 */
export const useDashboardData = () => {
  const { user } = useAppSelector(state => state.auth);
  const role = getDashboardRole(user);

  // Import hooks dynamically based on role
  // Note: This is a simplified approach. In a real implementation,
  // you might want to create a more sophisticated hook system
  
  try {
    switch (role) {
      case 'super_admin':
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const { useSuperAdminDashboard } = require('./SuperAdminDashboardProvider');
        return useSuperAdminDashboard();

      case 'admin':
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const { useAdminDashboard } = require('./AdminDashboardProvider');
        return useAdminDashboard();

      case 'moderator':
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const { useModeratorDashboard } = require('./ModeratorDashboardProvider');
        return useModeratorDashboard();

      case 'mentor':
      case 'investor':
      case 'user':
      default:
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const { useUserDashboard } = require('./UserDashboardProvider');
        return useUserDashboard();
    }
  } catch (error) {
    console.error('Error loading dashboard provider for role:', role, error);
    // Fallback to user dashboard
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const { useUserDashboard } = require('./UserDashboardProvider');
    return useUserDashboard();
  }
};

/**
 * Simplified hook that provides common dashboard data structure
 * regardless of the specific role provider
 */
export const useUnifiedDashboardData = () => {
  const { user } = useAppSelector(state => state.auth);
  const role = getDashboardRole(user);
  const dashboardData = useDashboardData();

  return {
    role,
    user,
    stats: dashboardData.stats || [],
    quickActions: dashboardData.quickActions || [],
    loading: dashboardData.loading || false,
    error: dashboardData.error || null,
    refreshData: dashboardData.refreshData || (() => Promise.resolve()),
    // Additional data that might be role-specific
    additionalData: {
      systemHealth: (dashboardData as any).systemHealth,
      systemMetrics: (dashboardData as any).systemMetrics,
      securityAlerts: (dashboardData as any).securityAlerts,
      pendingReports: (dashboardData as any).pendingReports,
      recentActivity: (dashboardData as any).recentActivity,
      recentIdeas: (dashboardData as any).recentIdeas,
    }
  };
};

/**
 * Provider component that can be used at the app level
 * to provide dashboard context for any role
 */
export const GlobalDashboardProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <DashboardProviderFactory>
      {children}
    </DashboardProviderFactory>
  );
};

export default DashboardProviderFactory;
