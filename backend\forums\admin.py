from django.contrib import admin
from .models import (
    ForumCategory, ForumTopic, ForumThread, ForumPost,
    UserReputation, ReputationActivity
)

class ForumTopicInline(admin.TabularInline):
    model = ForumTopic
    extra = 0
    fields = ['title', 'is_pinned', 'is_locked', 'is_active', 'created_at']
    readonly_fields = ['created_at']

@admin.register(ForumCategory)
class ForumCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'slug', 'order', 'is_active', 'topic_count', 'created_at']
    list_filter = ['is_active']
    search_fields = ['name', 'description']
    prepopulated_fields = {'slug': ('name',)}
    inlines = [ForumTopicInline]

    def topic_count(self, obj):
        return obj.topics.count()
    topic_count.short_description = 'Topics'

class ForumThreadInline(admin.TabularInline):
    model = ForumThread
    extra = 0
    fields = ['title', 'author', 'is_pinned', 'is_locked', 'moderation_status', 'created_at']
    readonly_fields = ['author', 'created_at']

@admin.register(ForumTopic)
class ForumTopicAdmin(admin.ModelAdmin):
    list_display = ['title', 'category', 'is_pinned', 'is_locked', 'is_active', 'thread_count', 'created_at']
    list_filter = ['category', 'is_pinned', 'is_locked', 'is_active']
    search_fields = ['title', 'description']
    prepopulated_fields = {'slug': ('title',)}
    inlines = [ForumThreadInline]

    def thread_count(self, obj):
        return obj.threads.count()
    thread_count.short_description = 'Threads'

class ForumPostInline(admin.TabularInline):
    model = ForumPost
    extra = 0
    fields = ['author', 'content', 'is_solution', 'moderation_status', 'created_at']
    readonly_fields = ['author', 'created_at']

@admin.register(ForumThread)
class ForumThreadAdmin(admin.ModelAdmin):
    list_display = ['title', 'topic', 'author', 'is_pinned', 'is_locked', 'views', 'post_count', 'moderation_status', 'created_at']
    list_filter = ['topic', 'is_pinned', 'is_locked', 'moderation_status']
    search_fields = ['title', 'content', 'author__username']
    readonly_fields = ['views', 'last_activity', 'created_at', 'updated_at']
    inlines = [ForumPostInline]

    def post_count(self, obj):
        return obj.posts.count()
    post_count.short_description = 'Posts'

@admin.register(ForumPost)
class ForumPostAdmin(admin.ModelAdmin):
    list_display = ['id', 'thread_title', 'author', 'is_solution', 'like_count', 'moderation_status', 'created_at']
    list_filter = ['is_solution', 'moderation_status']
    search_fields = ['content', 'author__username', 'thread__title']
    readonly_fields = ['created_at', 'updated_at']

    def thread_title(self, obj):
        return obj.thread.title
    thread_title.short_description = 'Thread'

    def like_count(self, obj):
        return obj.likes.count()
    like_count.short_description = 'Likes'

@admin.register(UserReputation)
class UserReputationAdmin(admin.ModelAdmin):
    list_display = ['user', 'points', 'level', 'threads_created', 'posts_created', 'solutions_provided', 'likes_received', 'likes_given']
    list_filter = ['level']
    search_fields = ['user__username', 'user__email']
    readonly_fields = ['created_at', 'updated_at']

@admin.register(ReputationActivity)
class ReputationActivityAdmin(admin.ModelAdmin):
    list_display = ['user', 'activity_type', 'points', 'description', 'created_at']
    list_filter = ['activity_type']
    search_fields = ['user__username', 'description']
    readonly_fields = ['created_at']
