import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  Copy, 
  Search, 
  Filter,
  MoreVertical,
  AlertCircle,
  CheckCircle,
  Loader2,
  Download,
  Upload
} from 'lucide-react';
// MainLayout removed - handled by routing system
import { RTLText, RTLFlex } from '../../components/common';
import { useLanguage } from '../../hooks/useLanguage';
import { useTemplates } from '../../hooks/useTemplates';
import { BusinessPlanTemplate, CustomBusinessPlanTemplate } from '../../services/templateCustomizationApi';

const TemplateManagementPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();

  // Use templates hook for data management
  const {
    templates,
    customTemplates,
    loading,
    error,
    createCustomTemplate,
    updateCustomTemplate,
    deleteCustomTemplate,
    refreshTemplates,
    clearError
  } = useTemplates();

  // Local state
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<CustomBusinessPlanTemplate | null>(null);
  const [selectedTemplates, setSelectedTemplates] = useState<number[]>([]);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<number | null>(null);

  // Filter templates based on search and category
  const filteredTemplates = React.useMemo(() => {
    let allTemplates = [...templates, ...customTemplates];

    if (searchQuery) {
      allTemplates = allTemplates.filter(template =>
        template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (selectedCategory !== 'all') {
      allTemplates = allTemplates.filter(template => {
        if (selectedCategory === 'system') return 'is_system' in template && template.is_system;
        if (selectedCategory === 'custom') return !('is_system' in template) || !template.is_system;
        return template.industry === selectedCategory;
      });
    }

    return allTemplates;
  }, [templates, customTemplates, searchQuery, selectedCategory]);

  const handleCreateTemplate = async (templateData: Partial<CustomBusinessPlanTemplate>) => {
    try {
      await createCustomTemplate(templateData);
      setShowCreateModal(false);
    } catch (error) {
      console.error('Error creating template:', error);
    }
  };

  const handleEditTemplate = async (id: number, templateData: Partial<CustomBusinessPlanTemplate>) => {
    try {
      await updateCustomTemplate(id, templateData);
      setEditingTemplate(null);
    } catch (error) {
      console.error('Error updating template:', error);
    }
  };

  const handleDeleteTemplate = async (id: number) => {
    try {
      await deleteCustomTemplate(id);
      setShowDeleteConfirm(null);
      setSelectedTemplates(prev => prev.filter(templateId => templateId !== id));
    } catch (error) {
      console.error('Error deleting template:', error);
    }
  };

  const handleBulkDelete = async () => {
    try {
      await Promise.all(selectedTemplates.map(id => deleteCustomTemplate(id)));
      setSelectedTemplates([]);
    } catch (error) {
      console.error('Error deleting templates:', error);
    }
  };

  const handleDuplicateTemplate = async (template: BusinessPlanTemplate | CustomBusinessPlanTemplate) => {
    const duplicateData = {
      name: `${template.name} (Copy)`,
      description: template.description,
      base_template: 'id' in template ? template.id : 1,
      sections: template.sections || {},
      is_public: false
    };

    await handleCreateTemplate(duplicateData);
  };

  if (loading) {
    return (
      <AuthenticatedLayout>
      <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="flex items-center justify-center py-12">
          <Loader2 className="animate-spin text-purple-500" size={32} />
          <span className="ml-3 text-gray-400">{t('common.loading', 'Loading templates...')}</span>
        </div>
                </div>
        </div>
      </div>
    </AuthenticatedLayout>
    );
  }

  return (
    <AuthenticatedLayout>
      <div className="space-y-6">
        {/* Header */}
        <RTLFlex className="items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white">
              {t('templates.management.title', 'Template Management')}
            </h1>
            <p className="text-gray-400 mt-1">
              {t('templates.management.subtitle', 'Manage your business plan templates')}
            </p>
          </div>

          <div className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
            <button
              onClick={() => setShowCreateModal(true)}
              className={`flex items-center px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 hover:shadow-glow text-white rounded-lg transition-all duration-300 ${isRTL ? "flex-row-reverse" : ""}`}
            >
              <Plus size={20} className={isRTL ? 'ml-2' : 'mr-2'} />
              {t('templates.create', 'Create Template')}
            </button>

            <button
              onClick={refreshTemplates}
              className="px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-all duration-300 border border-white/30"
            >
              {t('common.refresh', 'Refresh')}
            </button>
          </div>
        </RTLFlex>

        {/* Error Display */}
        {error && (
          <div className="bg-black/30 backdrop-blur-sm border border-red-500/30 rounded-lg p-4">
            <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
              <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <AlertCircle className="text-red-400" size={20} />
                <span className={`text-red-400 ${isRTL ? 'mr-2' : 'ml-2'}`}>{error}</span>
              </div>
              <button
                onClick={clearError}
                className="text-red-400 hover:text-red-300 text-sm"
              >
                {t('common.dismiss', 'Dismiss')}
              </button>
            </div>
          </div>
        )}

        {/* Search and Filters */}
        <div className="bg-gray-800/50 rounded-lg p-4 space-y-4 border border-gray-700">
          <div className="relative">
            <Search className={`absolute top-3 ${isRTL ? 'right-3' : 'left-3'} text-gray-400`} size={20} />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder={t('templates.searchPlaceholder', 'Search templates...')}
              className={`w-full ${isRTL ? 'pr-10 pl-4' : 'pl-10 pr-4'} py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:ring-2 focus:ring-purple-500 focus:border-transparent`}
            />
          </div>

          <RTLFlex className={`flex-wrap gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className="flex-1 min-w-[200px]">
              <label className="block text-sm font-medium mb-1">
                {t('templates.category', 'Category')}
              </label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-purple-500"
              >
                <option value="all">{t('templates.allCategories', 'All Categories')}</option>
                <option value="system">{t('templates.systemTemplates', 'System Templates')}</option>
                <option value="custom">{t('templates.customTemplates', 'Custom Templates')}</option>
                <option value="technology">{t('templates.categories.technology', 'Technology')}</option>
                <option value="retail">{t('templates.categories.retail', 'Retail')}</option>
                <option value="services">{t('templates.categories.services', 'Services')}</option>
                <option value="hospitality">{t('templates.categories.hospitality', 'Hospitality')}</option>
              </select>
            </div>

            {selectedTemplates.length > 0 && (
              <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                <span className="text-sm text-gray-400">
                  {selectedTemplates.length} {t('common.selected', 'selected')}
                </span>
                <button
                  onClick={handleBulkDelete}
                  className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded text-sm transition-colors"
                >
                  {t('common.delete', 'Delete')}
                </button>
              </div>
            )}
          </RTLFlex>
        </div>

        {/* Templates Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTemplates.map(template => {
            const isSystemTemplate = 'is_system' in template && template.is_system;
            const isSelected = selectedTemplates.includes(template.id);

            return (
              <div
                key={template.id}
                className={`relative bg-gray-800/50 rounded-lg p-6 border-2 transition-all ${
                  isSelected ? 'border-purple-500 bg-purple-900/20' : 'border-transparent hover:border-gray-600'
                }`}
              >
                {/* Selection Checkbox */}
                {!isSystemTemplate && (
                  <div className="absolute top-3 left-3">
                    <input
                      type="checkbox"
                      checked={isSelected}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedTemplates(prev => [...prev, template.id]);
                        } else {
                          setSelectedTemplates(prev => prev.filter(id => id !== template.id));
                        }
                      }}
                      className="w-4 h-4 text-purple-600 bg-gray-700 border-gray-600 rounded focus:ring-purple-500"
                    />
                  </div>
                )}

                {/* Template Type Badge */}
                <div className="absolute top-3 right-3">
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    isSystemTemplate 
                      ? 'bg-blue-600 text-white' 
                      : 'bg-green-600 text-white'
                  }`}>
                    {isSystemTemplate ? t('templates.system', 'System') : t('templates.custom', 'Custom')}
                  </span>
                </div>

                {/* Template Content */}
                <div className="mt-8">
                  <h3 className="font-semibold text-lg text-white mb-2">
                    {template.name}
                  </h3>
                  <p className="text-gray-300 text-sm mb-4 line-clamp-2">
                    {template.description}
                  </p>

                  {/* Template Metadata */}
                  <div className="space-y-2 text-xs text-gray-400 mb-4">
                    <div className={`flex justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
                      <span>{t('templates.industry', 'Industry')}</span>
                      <span className="capitalize">{template.industry || 'General'}</span>
                    </div>
                    <div className={`flex justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
                      <span>{t('templates.sections', 'Sections')}</span>
                      <span>{Object.keys(template.sections || {}).length}</span>
                    </div>
                    <div className={`flex justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
                      <span>{t('templates.lastUpdated', 'Updated')}</span>
                      <span>{new Date(template.updated_at).toLocaleDateString()}</span>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className={`flex gap-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <button
                      onClick={() => navigate(`/dashboard/templates/${template.id}/preview`)}
                      className={`flex-1 px-3 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg text-sm transition-all duration-300 flex items-center justify-center gap-1 border border-white/30 ${isRTL ? "flex-row-reverse" : ""}`}
                    >
                      <Eye size={14} />
                      {t('common.preview', 'Preview')}
                    </button>

                    <button
                      onClick={() => handleDuplicateTemplate(template)}
                      className={`px-3 py-2 bg-gradient-to-r from-blue-600 to-cyan-600 hover:shadow-glow text-white rounded-lg text-sm transition-all duration-300 flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}
                    >
                      <Copy size={14} />
                    </button>

                    {!isSystemTemplate && (
                      <>
                        <button
                          onClick={() => setEditingTemplate(template as CustomBusinessPlanTemplate)}
                          className={`px-3 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded text-sm transition-colors flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}
                        >
                          <Edit size={14} />
                        </button>

                        <button
                          onClick={() => setShowDeleteConfirm(template.id)}
                          className={`px-3 py-2 bg-red-600 hover:bg-red-700 text-white rounded text-sm transition-colors flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}
                        >
                          <Trash2 size={14} />
                        </button>
                      </>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* No Templates Message */}
        {filteredTemplates.length === 0 && (
          <div className="text-center py-12">
            <AlertCircle size={48} className="text-gray-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-400 mb-2">
              {t('templates.noTemplatesFound', 'No Templates Found')}
            </h3>
            <p className="text-gray-500">
              {t('templates.tryDifferentFilters', 'Try adjusting your search or filters')}
            </p>
          </div>
        )}

        {/* Delete Confirmation Modal */}
        {showDeleteConfirm && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold text-white mb-4">
                {t('templates.confirmDelete', 'Confirm Delete')}
              </h3>
              <p className="text-gray-300 mb-6">
                {t('templates.deleteWarning', 'Are you sure you want to delete this template? This action cannot be undone.')}
              </p>
              <div className={`flex gap-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                <button
                  onClick={() => setShowDeleteConfirm(null)}
                  className="flex-1 px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded transition-colors"
                >
                  {t('common.cancel', 'Cancel')}
                </button>
                <button
                  onClick={() => handleDeleteTemplate(showDeleteConfirm)}
                  className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded transition-colors"
                >
                  {t('common.delete', 'Delete')}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </AuthenticatedLayout>
  );
};

export default TemplateManagementPage;
