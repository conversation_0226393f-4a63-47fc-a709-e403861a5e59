/**
 * AI-Enhanced Business Idea Creation Page
 * Demonstrates the new intelligent contextual assistance features
 */

import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Brain,
  Sparkles,
  Target,
  TrendingUp,
  Save,
  ArrowLeft,
  Lightbulb,
  Zap,
  CheckCircle,
  AlertTriangle
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
// MainLayout removed - handled by routing system
// Placeholder components - these would be implemented separately
const SmartFloatingAssistant = ({ currentPage, currentContext, businessIdeaId, userId }: any) => null;

const RealTimeIdeaEnhancer = ({ ideaText, onIdeaChange, businessIdeaId, userId, placeholder }: any) => (
  <textarea
    value={ideaText}
    onChange={(e) => onIdeaChange(e.target.value)}
    placeholder={placeholder}
    className="w-full h-32 p-3 border border-gray-300 dark:border-gray-600 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
  />
);

const IntelligentFormAssistant = ({ fields, onFieldChange, formType, userId, autoSuggest }: any) => (
  <div className="space-y-4">
    {fields.map((field: any) => (
      <div key={field.name}>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          {field.label} {field.required && <span className="text-red-500">*</span>}
        </label>
        {field.type === 'select' ? (
          <select
            value={field.value}
            onChange={(e) => onFieldChange(field.name, e.target.value)}
            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Select {field.label}</option>
            {field.options?.map((option: any) => (
              <option key={option.value} value={option.value}>{option.label}</option>
            ))}
          </select>
        ) : field.type === 'textarea' ? (
          <textarea
            value={field.value}
            onChange={(e) => onFieldChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            className="w-full h-24 p-2 border border-gray-300 dark:border-gray-600 rounded-lg resize-none focus:ring-2 focus:ring-blue-500"
          />
        ) : (
          <input
            type="text"
            value={field.value}
            onChange={(e) => onFieldChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500"
          />
        )}
      </div>
    ))}
  </div>
);

const ProactiveAINotifications = ({ userId }: any) => (
  <div className="flex items-center space-x-2 px-3 py-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
    <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
    <span className="text-sm text-blue-700 dark:text-blue-300">AI Active</span>
  </div>
);

import { useAppSelector } from '../../store/hooks';
import { useAIContext } from '../../contexts/AIContextProvider';

interface BusinessIdeaForm {
  title: string;
  description: string;
  target_market: string;
  revenue_model: string;
  competitive_advantage: string;
  funding_needed: string;
  timeline: string;
  industry: string;
  stage: string;
}

const AIEnhancedBusinessIdeaPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();
  const { id } = useParams();
  const { user } = useAppSelector((state) => state.auth);
  const { aiData, updateContext } = useAIContext();

  const [formData, setFormData] = useState<BusinessIdeaForm>({
    title: '',
    description: '',
    target_market: '',
    revenue_model: '',
    competitive_advantage: '',
    funding_needed: '',
    timeline: '',
    industry: '',
    stage: 'idea'
  });

  const [isSaving, setIsSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');
  const [aiInsights, setAIInsights] = useState<any>(null);

  const isEditing = Boolean(id);

  // Update AI context when component mounts
  useEffect(() => {
    updateContext({
      currentPage: isEditing ? 'edit-business-idea' : 'create-business-idea',
      currentContext: {
        pageType: 'business-idea-form',
        formData,
        isEditing,
        needsHelp: !formData.title && !formData.description
      },
      businessIdeaId: id ? parseInt(id, 10) : undefined
    });
  }, [id, formData, isEditing, updateContext]);

  // Form fields configuration for the Intelligent Form Assistant
  const formFields = [
    {
      name: 'title',
      label: t('businessIdea.title', 'Business Idea Title'),
      type: 'text' as const,
      value: formData.title,
      placeholder: t('businessIdea.titlePlaceholder', 'Enter a compelling title for your business idea'),
      required: true
    },
    {
      name: 'industry',
      label: t('businessIdea.industry', 'Industry'),
      type: 'select' as const,
      value: formData.industry,
      options: [
        { value: 'technology', label: t("common.technology", "Technology") },
        { value: 'healthcare', label: t("common.healthcare", "Healthcare") },
        { value: 'finance', label: t("common.finance", "Finance") },
        { value: 'education', label: t("common.education", "Education") },
        { value: 'retail', label: t("common.retail", "Retail") },
        { value: 'manufacturing', label: t("common.manufacturing", "Manufacturing") },
        { value: 'services', label: t("common.services", "Services") },
        { value: 'other', label: t("common.other", "Other") }
      ],
      required: true
    },
    {
      name: 'target_market',
      label: t('businessIdea.targetMarket', 'Target Market'),
      type: 'text' as const,
      value: formData.target_market,
      placeholder: t('businessIdea.targetMarketPlaceholder', 'Who are your ideal customers?')
    },
    {
      name: 'revenue_model',
      label: t('businessIdea.revenueModel', 'Revenue Model'),
      type: 'text' as const,
      value: formData.revenue_model,
      placeholder: t('businessIdea.revenueModelPlaceholder', 'How will you make money?')
    },
    {
      name: 'competitive_advantage',
      label: t('businessIdea.competitiveAdvantage', 'Competitive Advantage'),
      type: 'textarea' as const,
      value: formData.competitive_advantage,
      placeholder: t('businessIdea.competitiveAdvantagePlaceholder', 'What makes you different from competitors?')
    },
    {
      name: 'funding_needed',
      label: t('businessIdea.fundingNeeded', 'Funding Needed'),
      type: 'text' as const,
      value: formData.funding_needed,
      placeholder: t('businessIdea.fundingNeededPlaceholder', 'How much funding do you need?')
    },
    {
      name: 'timeline',
      label: t('businessIdea.timeline', 'Timeline'),
      type: 'text' as const,
      value: formData.timeline,
      placeholder: t('businessIdea.timelinePlaceholder', 'When do you plan to launch?')
    },
    {
      name: 'stage',
      label: t('businessIdea.stage', 'Current Stage'),
      type: 'select' as const,
      value: formData.stage,
      options: [
        { value: 'idea', label: t("common.idea.stage", "Idea Stage") },
        { value: 'research', label: 'Research & Validation' },
        { value: 'planning', label: t("common.business.planning", "Business Planning") },
        { value: 'development', label: t("common.product.development", "Product Development") },
        { value: 'testing', label: 'Testing & Iteration' },
        { value: 'launch', label: t("common.ready.to.launch", "Ready to Launch") },
        { value: 'scaling', label: t("common.scaling", "Scaling") }
      ],
      required: true
    }
  ];

  const handleFieldChange = (fieldName: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: value
    }));
  };

  const handleDescriptionChange = (description: string) => {
    setFormData(prev => ({
      ...prev,
      description
    }));
  };

  const handleSave = async () => {
    setIsSaving(true);
    setSaveStatus('saving');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      // In real implementation, save to backend
      console.log('Saving business idea:', formData);

      setSaveStatus('saved');

      // Navigate back to business ideas list after successful save
      setTimeout(() => {
        navigate('/dashboard/business-ideas');
      }, 1500);

    } catch (error) {
      console.error('Error saving business idea:', error);
      setSaveStatus('error');
    } finally {
      setIsSaving(false);
    }
  };

  const getSaveButtonContent = () => {
    switch (saveStatus) {
      case 'saving':
        return (
          <>
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
            {t('businessIdea.saving', 'Saving...')}
          </>
        );
      case 'saved':
        return (
          <>
            <CheckCircle className="w-4 h-4" />
            {t('businessIdea.saved', 'Saved!')}
          </>
        );
      case 'error':
        return (
          <>
            <AlertTriangle className="w-4 h-4" />
            {t('businessIdea.saveError', 'Error')}
          </>
        );
      default:
        return (
          <>
            <Save className="w-4 h-4" />
            {t('businessIdea.save', 'Save Business Idea')}
          </>
        );
    }
  };

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`flex items-center space-x-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            <button
              onClick={() => navigate('/dashboard/business-ideas')}
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            >
              <ArrowLeft className="w-5 h-5 text-gray-600 dark:text-gray-400" />
            </button>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                {isEditing
                  ? t('businessIdea.editTitle', 'Edit Business Idea')
                  : t('businessIdea.createTitle', 'Create New Business Idea')
                }
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                {t('businessIdea.subtitle', 'AI will help you create a comprehensive business idea')}
              </p>
            </div>
          </div>

          {/* AI Status Indicator */}
          <div className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className={`flex items-center space-x-2 px-3 py-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700 ${isRTL ? "flex-row-reverse" : ""}`}>
              <Brain className="w-4 h-4 text-blue-500" />
              <span className="text-sm text-blue-700 dark:text-blue-300">
                {t('ai.active', 'AI Active')}
              </span>
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
            </div>
            <ProactiveAINotifications userId={user?.id} />
          </div>
        </div>

        {/* AI Enhancement Notice */}
        <div className="bg-gradient-to-r from-purple-900/20 to-blue-900/20 border border-purple-700/30 backdrop-blur-sm rounded-lg p-4">
          <div className={`flex items-start space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
            <Sparkles className="w-5 h-5 text-purple-400 mt-0.5" />
            <div>
              <h3 className="font-semibold text-purple-100 mb-1">
                {t('ai.enhancement.title', 'AI-Enhanced Creation Experience')}
              </h3>
              <p className="text-sm text-purple-200">
                {t('ai.enhancement.description', 'Our AI will provide real-time suggestions, auto-complete fields, and help optimize your business idea as you work.')}
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Business Idea Description with Real-time Enhancement */}
            <div className="bg-indigo-900/30 backdrop-blur-sm border border-indigo-800/50 rounded-lg p-6">
              <div className={`flex items-center space-x-2 mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                <Lightbulb className="w-5 h-5 text-yellow-500" />
                <h2 className="text-lg font-semibold text-white">
                  {t('businessIdea.description', 'Business Idea Description')}
                </h2>
                <span className="text-xs px-2 py-1 bg-blue-900/30 text-blue-300 rounded-full">
                  {t('ai.realTimeEnhancement', 'Real-time AI Enhancement')}
                </span>
              </div>

              <RealTimeIdeaEnhancer
                ideaText={formData.description}
                onIdeaChange={handleDescriptionChange}
                businessIdeaId={id ? parseInt(id, 10) : undefined}
                userId={user?.id}
                placeholder={t('businessIdea.descriptionPlaceholder', 'Describe your business idea in detail. AI will provide suggestions as you type...')}
              />
            </div>

            {/* Intelligent Form Fields */}
            <div className="bg-indigo-900/30 backdrop-blur-sm border border-indigo-800/50 rounded-lg p-6">
              <div className={`flex items-center space-x-2 mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
                <Target className="w-5 h-5 text-green-500" />
                <h2 className="text-lg font-semibold text-white">
                  {t('businessIdea.details', 'Business Details')}
                </h2>
                <span className="text-xs px-2 py-1 bg-green-900/30 text-green-300 rounded-full">
                  {t('ai.smartCompletion', 'Smart Auto-completion')}
                </span>
              </div>

              <IntelligentFormAssistant
                fields={formFields}
                onFieldChange={handleFieldChange}
                formType="business_idea"
                userId={user?.id}
                autoSuggest={true}
              />
            </div>
          </div>

          {/* AI Insights Sidebar */}
          <div className="space-y-6">
            {/* Save Button */}
            <button
              onClick={handleSave}
              disabled={isSaving || !formData.title || !formData.description}
              className={`w-full flex items-center justify-center space-x-2 py-3 px-4 rounded-lg font-medium transition-all duration-200 ${
                saveStatus === 'saved'
                  ? 'bg-green-600 text-white'
                  : saveStatus === 'error'
                  ? 'bg-red-600 text-white'
                  : 'bg-blue-600 hover:bg-blue-700 text-white disabled:opacity-50 disabled:cursor-not-allowed'
              }`}
            >
              {getSaveButtonContent()}
            </button>

            {/* AI Progress Indicator */}
            <div className="bg-indigo-900/30 backdrop-blur-sm border border-indigo-800/50 rounded-lg p-4">
              <div className={`flex items-center space-x-2 mb-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                <Zap className="w-4 h-4 text-blue-500" />
                <h3 className="font-semibold text-white">
                  {t('ai.progress.title', 'AI Analysis Progress')}
                </h3>
              </div>

              <div className="space-y-3">
                <div className={`flex items-center justify-between text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
                  <span className="text-gray-600 dark:text-gray-400">
                    {t('ai.progress.completeness', 'Completeness')}
                  </span>
                  <span className="font-medium">
                    {Math.round((Object.values(formData).filter(v => v).length / Object.keys(formData).length) * 100)}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${(Object.values(formData).filter(v => v).length / Object.keys(formData).length) * 100}%`
                    }}
                  />
                </div>
              </div>
            </div>

            {/* Quick AI Actions */}
            <div className="bg-indigo-900/30 backdrop-blur-sm border border-indigo-800/50 rounded-lg p-4">
              <h3 className="font-semibold text-gray-900 dark:text-white mb-3">
                {t('ai.quickActions', 'Quick AI Actions')}
              </h3>

              <div className="space-y-2">
                <button className={`w-full  p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-sm ${isRTL ? "text-right" : "text-left"}`}>
                  <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <TrendingUp className="w-4 h-4 text-green-500" />
                    <span>{t('ai.actions.marketAnalysis', 'Run Market Analysis')}</span>
                  </div>
                </button>
                <button className={`w-full  p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-sm ${isRTL ? "text-right" : "text-left"}`}>
                  <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <Target className="w-4 h-4 text-blue-500" />
                    <span>{t('ai.actions.competitorAnalysis', 'Analyze Competitors')}</span>
                  </div>
                </button>
                <button className={`w-full  p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-sm ${isRTL ? "text-right" : "text-left"}`}>
                  <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <Lightbulb className="w-4 h-4 text-yellow-500" />
                    <span>{t('ai.actions.generateIdeas', 'Generate Related Ideas')}</span>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Smart Floating Assistant */}
      <SmartFloatingAssistant
        currentPage={aiData.currentPage}
        currentContext={aiData.currentContext}
        businessIdeaId={aiData.businessIdeaId}
        userId={user?.id}
      />
              </div>
        </div>
      </div>
    </div>
  );
};

export default AIEnhancedBusinessIdeaPage;
