import React, { useEffect, useState } from 'react';
import { use<PERSON>ara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { fetchTopic, fetchThreads, clearCurrentTopic } from '../store/forumSlice';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../hooks/useLanguage';
import { RTLIcon, RTLText, RTLFlex } from '../components/common';
import {
  MessageSquare,
  Users,
  Clock,
  ArrowLeft,
  Plus,
  Search,
  Pin,
  Lock,
  Eye,
  Tag as TagIcon
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

const ForumTopicPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { slug } = useParams<{ slug: string }>();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { currentTopic, threads, loading, error } = useAppSelector((state) => state.forum);
  const { user } = useAppSelector((state) => state.auth);
  const { language } = useAppSelector(state => state.language);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (slug) {
      dispatch(fetchTopic(slug));
    }

    return () => {
      dispatch(clearCurrentTopic());
    };
  }, [dispatch, slug]);

  useEffect(() => {
    if (currentTopic) {
      dispatch(fetchThreads(currentTopic.id));
    }
  }, [dispatch, currentTopic]);

  // Filter threads based on search term
  const filteredThreads = threads.filter(thread =>
    thread.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    thread.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
    thread.tags.some(tag => tag.name.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const handleCreateThread = () => {
    if (!currentTopic) return;
    navigate(`/forum/thread/create?topic=${currentTopic.id}`);
  };

  return (
    <div className={`min-h-screen flex flex-col ${isRTL ? "flex-row-reverse" : ""}`}>
      <Navbar />

      <main className={`flex-grow container mx-auto px-4 py-8 ${isRTL ? "flex-row-reverse" : ""}`}>
        {/* Breadcrumb */}
        <div className="mb-6">
          {currentTopic && currentTopic.category && (
            <RTLFlex className="items-center">
              <Link to="/forum" className="text-purple-400 hover:text-purple-300">
                {t('forum.title')}
              </Link>
              <span className="mx-2 text-gray-500">/</span>
              <Link
                to={`/forum/category/${currentTopic.category}`}
                className="text-purple-400 hover:text-purple-300"
              >
                {/* We would ideally have the category name here */}
                {t('forum.categories')}
              </Link>
              <span className="mx-2 text-gray-500">/</span>
              <span className="text-gray-400">{currentTopic.title}</span>
            </RTLFlex>
          )}
        </div>

        {loading && !currentTopic ? (
          <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
            <div className={`flex justify-center items-center h-40 ${isRTL ? "flex-row-reverse" : ""}`}>
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
            </div>
          </div>
        ) : error ? (
          <div className="bg-red-900/30 border border-red-800 rounded-lg p-4 mb-6">
            <RTLText as="div" className="text-red-300">{error}</RTLText>
          </div>
        ) : currentTopic ? (
          <>
            <div className="mb-8">
              <RTLFlex className="items-center">
                <RTLText as="h1" className="text-3xl font-bold">{currentTopic.title}</RTLText>
                {currentTopic.is_pinned && (
                  <Pin size={20} className={`${language === 'ar' ? 'mr-3' : 'ml-3'} text-yellow-400`} />
                )}
                {currentTopic.is_locked && (
                  <Lock size={20} className={`${language === 'ar' ? 'mr-3' : 'ml-3'} text-red-400`} />
                )}
              </RTLFlex>
              <RTLText as="div" className="text-gray-400 mt-2">
                {currentTopic.description}
              </RTLText>

              <div className={`flex flex-wrap gap-4 mt-4 text-sm ${language === 'ar' ? 'justify-end' : ''}`}>
                <RTLFlex className="items-center text-gray-400">
                  <RTLIcon icon={MessageSquare} size={16} className={language === 'ar' ? 'ml-1' : 'mr-1'} />
                  <span>{currentTopic.thread_count} {t('forum.threads')}</span>
                </RTLFlex>
                <RTLFlex className="items-center text-gray-400">
                  <RTLIcon icon={Users} size={16} className={language === 'ar' ? 'ml-1' : 'mr-1'} />
                  <span>{currentTopic.post_count} {t('forum.posts')}</span>
                </RTLFlex>
                <RTLFlex className="items-center text-gray-400">
                  <RTLIcon icon={Clock} size={16} className={language === 'ar' ? 'ml-1' : 'mr-1'} />
                  <span>{t('forum.lastActivity')}: {formatDistanceToNow(new Date(currentTopic.last_activity), { addSuffix: true })}</span>
                </RTLFlex>
              </div>
            </div>

            <div className={`flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
              <div className={`relative flex-1 max-w-md ${isRTL ? "flex-row-reverse" : ""}`}>
                <input
                  type="text"
                  placeholder={t('forum.searchThreads')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className={`w-full bg-indigo-900/30 border border-indigo-800/50 rounded-lg py-2 ${language === 'ar' ? 'pr-10 pl-4' : 'pl-10 pr-4'} text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500`}
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                />
                <Search size={18} className={`absolute ${language === 'ar' ? 'right-3' : 'left-3'} top-2.5 text-gray-400`} />
              </div>

              {!currentTopic.is_locked && (
                <RTLFlex
                  as="button"
                  onClick={handleCreateThread}
                  className="px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300"
                  align="center"
                >
                  <RTLIcon icon={Plus} size={18} className={language === 'ar' ? 'ml-2' : 'mr-2'} />
                  {t('forum.createNewThread')}
                </RTLFlex>
              )}
            </div>

            {/* Threads List */}
            {filteredThreads.length === 0 ? (
              <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50 text-center">
                <RTLText as="div" className="text-gray-400">
                  {searchTerm
                    ? t('forum.noThreadsFound')
                    : t('forum.noThreadsInTopic')}
                </RTLText>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredThreads.map((thread) => (
                  <Link
                    key={thread.id}
                    to={`/forum/thread/${thread.id}`}
                    className="block bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50 hover:bg-indigo-800/30 transition-colors"
                  >
                    <div className={`flex items-start ${isRTL ? "flex-row-reverse" : ""}`}>
                      <div className={`flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <RTLFlex className="items-center">
                          <RTLText as="h2" className="text-xl font-semibold">{thread.title}</RTLText>
                          {thread.is_pinned && (
                            <RTLIcon icon={Pin} size={16} className={`${language === 'ar' ? 'mr-2' : 'ml-2'} text-yellow-400`} />
                          )}
                          {thread.is_locked && (
                            <RTLIcon icon={Lock} size={16} className={`${language === 'ar' ? 'mr-2' : 'ml-2'} text-red-400`} />
                          )}
                        </RTLFlex>

                        <RTLFlex className="items-center mt-2">
                          <div className={`w-8 h-8 rounded-full bg-indigo-800/50 flex items-center justify-center ${language === 'ar' ? 'ml-2' : 'mr-2'}`}>
                            <span className="text-xs font-medium">{thread.author.username.charAt(0).toUpperCase()}</span>
                          </div>
                          <div>
                            <RTLText as="div" className="text-sm">
                              <span className="text-purple-400">{thread.author.username}</span>
                              <span className="text-gray-500 mx-1">•</span>
                              <span className="text-gray-400">{formatDistanceToNow(new Date(thread.created_at), { addSuffix: true })}</span>
                            </RTLText>
                            <RTLText as="p" className="text-xs text-gray-500">
                              {thread.author_reputation.level} • {thread.author_reputation.points} {t('forum.points')}
                            </RTLText>
                          </div>
                        </RTLFlex>

                        <div className="mt-3">
                          <RTLText as="div" className="text-gray-300 line-clamp-2">
                            {thread.content.replace(/<[^>]*>?/gm, '')}
                          </RTLText>
                        </div>

                        <div className={`flex flex-wrap gap-2 mt-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                          {thread.tags.map(tag => (
                            <span
                              key={tag.id}
                              className={`inline-flex items-center px-2 py-1 rounded-full text-xs bg-indigo-800/50 text-indigo-300 ${isRTL ? "flex-row-reverse" : ""}`}
                            >
                              <RTLIcon icon={TagIcon} size={12} className={language === 'ar' ? 'ml-1' : 'mr-1'} />
                              {tag.name}
                            </span>
                          ))}
                        </div>

                        <div className={`flex flex-wrap gap-4 mt-4 text-sm ${language === 'ar' ? 'justify-end' : ''}`}>
                          <RTLFlex className="items-center text-gray-400">
                            <RTLIcon icon={MessageSquare} size={16} className={language === 'ar' ? 'ml-1' : 'mr-1'} />
                            <span>{thread.post_count} {t('forum.replies')}</span>
                          </RTLFlex>
                          <RTLFlex className="items-center text-gray-400">
                            <RTLIcon icon={Eye} size={16} className={language === 'ar' ? 'ml-1' : 'mr-1'} />
                            <span>{thread.views} {t('forum.views')}</span>
                          </RTLFlex>
                          <RTLFlex className="items-center text-gray-400">
                            <RTLIcon icon={Clock} size={16} className={language === 'ar' ? 'ml-1' : 'mr-1'} />
                            <span>{t('forum.lastReply')}: {formatDistanceToNow(new Date(thread.last_activity), { addSuffix: true })}</span>
                          </RTLFlex>
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            )}
          </>
        ) : (
          <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50 text-center">
            <RTLText as="p" className="text-gray-400">{t('forum.topicNotFound')}</RTLText>
          </div>
        )}
      </main>

      <Footer />
    </div>
  );
};

export default ForumTopicPage;
