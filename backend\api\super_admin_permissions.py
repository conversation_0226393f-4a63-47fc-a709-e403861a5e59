from rest_framework import permissions
from django.contrib.auth.models import User
from users.models import UserProfile, UserRole
import logging

logger = logging.getLogger(__name__)


class IsSuperAdminUser(permissions.BasePermission):
    """
    Custom permission to only allow Super Admin users to access the view.
    Super Admin has the highest level of system access.
    """
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        # Check if user is Django superuser
        if request.user.is_superuser:
            return True
        
        # Check if user has super_admin role
        return self.user_has_super_admin_role(request.user)
    
    def user_has_super_admin_role(self, user):
        """Check if user has super_admin role"""
        try:
            profile = user.userprofile
            active_roles = profile.get_active_roles()
            return any(role.name == 'super_admin' for role in active_roles)
        except UserProfile.DoesNotExist:
            return False


class SuperAdminOrOwner(permissions.BasePermission):
    """
    Custom permission that allows Super Admin or object owner access.
    """
    
    def has_object_permission(self, request, view, obj):
        # Super Admin permissions
        if request.user.is_superuser:
            return True
        
        # Check super_admin role
        if IsSuperAdminUser().user_has_super_admin_role(request.user):
            return True
        
        # Check if the object has an owner field
        if hasattr(obj, 'owner'):
            return obj.owner == request.user
        
        return False


class SuperAdminSystemAccess(permissions.BasePermission):
    """
    Permission for system-level operations that only Super Admin can perform.
    """
    
    SYSTEM_OPERATIONS = [
        'database_management',
        'server_configuration',
        'security_settings',
        'backup_restore',
        'user_impersonation',
        'system_logs',
        'ai_system_config',
        'deployment_management',
        'database_schema_access',
        'migration_control',
        'environment_management',
        'advanced_analytics',
        'bulk_user_operations',
        'security_audit',
        'compliance_reporting',
        'system_monitoring',
        'performance_optimization'
    ]
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        # Only Super Admin can perform system operations
        if not (request.user.is_superuser or 
                IsSuperAdminUser().user_has_super_admin_role(request.user)):
            return False
        
        # Check specific operation permission
        required_operation = getattr(view, 'required_system_operation', None)
        if required_operation and required_operation not in self.SYSTEM_OPERATIONS:
            logger.warning(f"Unknown system operation requested: {required_operation}")
            return False
        
        return True


def get_user_super_admin_capabilities(user):
    """Get Super Admin capabilities for a user"""
    if not user.is_authenticated:
        return []
    
    # Check if user is Super Admin
    is_super_admin = (user.is_superuser or 
                     IsSuperAdminUser().user_has_super_admin_role(user))
    
    if not is_super_admin:
        return []
    
    return [
        {
            'id': 'system_management',
            'name': 'System Management',
            'description': 'Full system control and configuration',
            'features': [
                'Database schema management',
                'Server configuration',
                'Environment variables',
                'Application deployment',
                'System health monitoring'
            ]
        },
        {
            'id': 'security_management',
            'name': 'Security Management',
            'description': 'Advanced security and compliance control',
            'features': [
                'Security audit logs',
                'Compliance reporting',
                'Data privacy management',
                'Security policy enforcement',
                'User impersonation'
            ]
        },
        {
            'id': 'ai_system_control',
            'name': 'AI System Control',
            'description': 'Complete AI system management',
            'features': [
                'AI model configuration',
                'Token usage analytics',
                'Performance optimization',
                'Custom prompt management',
                'AI feature toggles'
            ]
        },
        {
            'id': 'advanced_analytics',
            'name': 'Advanced Analytics',
            'description': 'Comprehensive business intelligence',
            'features': [
                'Platform-wide analytics',
                'Revenue optimization',
                'User behavior analysis',
                'Performance metrics',
                'Predictive analytics'
            ]
        },
        {
            'id': 'database_mastery',
            'name': 'Database Mastery',
            'description': 'Complete database control and optimization',
            'features': [
                'Schema management and migrations',
                'Query optimization and analysis',
                'Database backup and restore',
                'Performance monitoring',
                'Data integrity checks'
            ]
        },
        {
            'id': 'bulk_operations',
            'name': 'Bulk Operations',
            'description': 'Mass user and content management',
            'features': [
                'Bulk user activation/deactivation',
                'Mass role assignments',
                'Batch content operations',
                'Data import/export tools',
                'Automated workflows'
            ]
        },
        {
            'id': 'security_compliance',
            'name': 'Security & Compliance',
            'description': 'Advanced security and regulatory compliance',
            'features': [
                'Security audit reports',
                'Compliance monitoring',
                'Data privacy management',
                'Threat detection',
                'Access control enforcement'
            ]
        },
        {
            'id': 'system_optimization',
            'name': 'System Optimization',
            'description': 'Performance tuning and resource management',
            'features': [
                'Resource usage optimization',
                'Cache management',
                'Performance profiling',
                'Scalability planning',
                'Load balancing control'
            ]
        },
        {
            'id': 'user_management',
            'name': 'Advanced User Management',
            'description': 'Complete user and role administration',
            'features': [
                'Role creation and management',
                'Permission assignment',
                'User impersonation',
                'Bulk user operations',
                'Account recovery'
            ]
        },
        {
            'id': 'integration_management',
            'name': 'Integration Management',
            'description': 'Third-party service and API management',
            'features': [
                'API key management',
                'Webhook configuration',
                'External service integration',
                'Rate limit management',
                'Service monitoring'
            ]
        }
    ]


def log_super_admin_action(user, action, details=None):
    """Log Super Admin actions for audit trail"""
    logger.info(f"Super Admin Action - User: {user.username}, Action: {action}, Details: {details}")


class SuperAdminAuditMixin:
    """Mixin to add audit logging to Super Admin views"""
    
    def dispatch(self, request, *args, **kwargs):
        response = super().dispatch(request, *args, **kwargs)
        
        # Log Super Admin actions
        if (hasattr(request.user, 'is_authenticated') and 
            request.user.is_authenticated and
            (request.user.is_superuser or 
             IsSuperAdminUser().user_has_super_admin_role(request.user))):
            
            action = f"{request.method} {request.path}"
            log_super_admin_action(request.user, action, 
                                 {'status_code': response.status_code})
        
        return response
