/**
 * Service Worker for Yasmeen AI
 *
 * This service worker handles caching and offline support for the application.
 * It has been updated to handle fetch errors gracefully.
 */

// Cache name and version
const CACHE_NAME = 'yasmeen-ai-cache-v1';

// Assets to cache on install
const PRECACHE_ASSETS = [
  '/',
  '/index.html',
  '/logo.png',
  '/offline.html',
  '/offline.ar.html',
];

// API endpoints to exclude from caching
const API_ENDPOINTS = [
  '/api/',
  '/auth/',
  '/admin/',
];

// Install event - cache precache assets
self.addEventListener('install', (event) => {
  console.log('[Service Worker] Installing...');

  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(async (cache) => {
        console.log('[Service Worker] Caching precache assets');

        // Cache assets individually to avoid failing on missing files
        const cachePromises = PRECACHE_ASSETS.map(async (asset) => {
          try {
            const response = await fetch(asset);
            if (response.ok) {
              await cache.put(asset, response);
              console.log(`[Service Worker] Cached: ${asset}`);
            } else {
              console.warn(`[Service Worker] Failed to cache ${asset}: ${response.status}`);
            }
          } catch (error) {
            console.warn(`[Service Worker] Error caching ${asset}:`, error);
          }
        });

        await Promise.allSettled(cachePromises);
        console.log('[Service Worker] Precache completed');
      })
      .catch((error) => {
        console.error('[Service Worker] Precache error:', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('[Service Worker] Activating...');

  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME) {
              console.log('[Service Worker] Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('[Service Worker] Claiming clients');
        return self.clients.claim();
      })
      .catch((error) => {
        console.error('[Service Worker] Activation error:', error);
      })
  );
});

// Helper function to determine if a request should be cached
const shouldCache = (request) => {
  const url = new URL(request.url);

  // Don't cache API requests
  if (API_ENDPOINTS.some(endpoint => url.pathname.startsWith(endpoint))) {
    return false;
  }

  // Don't cache browser extensions
  if (url.protocol === 'chrome-extension:') {
    return false;
  }

  // Only cache GET requests
  return request.method === 'GET';
};

// Helper function to create a fallback response
const createFallbackResponse = (message = 'Network request failed', status = 408) => {
  return new Response(
    JSON.stringify({
      error: true,
      message: message,
      status: status,
    }),
    {
      status: status,
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );
};

// Fetch event - serve from cache or network
self.addEventListener('fetch', (event) => {
  // Skip cross-origin requests
  if (!event.request.url.startsWith(self.location.origin)) {
    return;
  }

  event.respondWith(
    caches.match(event.request)
      .then((cachedResponse) => {
        // Return cached response if found
        if (cachedResponse) {
          return cachedResponse;
        }

        // Otherwise fetch from network
        return fetch(event.request)
          .then((response) => {
            // Don't cache bad responses
            if (!response || response.status !== 200 || response.type !== 'basic') {
              return response;
            }

            // Cache the response if it should be cached
            if (shouldCache(event.request)) {
              const clonedResponse = response.clone();
              caches.open(CACHE_NAME)
                .then((cache) => {
                  cache.put(event.request, clonedResponse);
                })
                .catch((error) => {
                  console.error('[Service Worker] Cache put error:', error);
                });
            }

            return response;
          })
          .catch((error) => {
            console.error('[Service Worker] Fetch error:', error);

            // Check if the request is for an HTML page
            if (event.request.headers.get('accept').includes('text/html')) {
              // Try to return the cached index page as a fallback
              return caches.match('/index.html');
            }

            // Return a fallback JSON response for API requests
            return createFallbackResponse('Network request failed. Please check your connection.');
          });
      })
  );
});
