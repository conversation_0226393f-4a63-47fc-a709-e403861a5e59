import React from 'react';
import { <PERSON><PERSON><PERSON>, ChevronRight, ChevronDown } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../hooks/useLanguage';
import { RTLText } from '../../../common';
import SimpleCharts from '../SimpleCharts';
import AdvancedCharts from '../AdvancedCharts';
import { DashboardStats } from '../../../../types/admin';

interface AnalyticsSectionProps {
  stats: DashboardStats;
  isLoading: boolean;
  showEnhanced: boolean;
  onToggleView: () => void;
}

/**
 * AnalyticsSection component displays analytics charts with toggle between basic and enhanced views
 */
const AnalyticsSection: React.FC<AnalyticsSectionProps> = ({ stats,
  isLoading,
  showEnhanced,
  onToggleView
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  return (
    <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
      <div className={`flex items-center justify-between mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
        <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className="p-2 bg-purple-600/20 rounded-lg">
            <BarChart size={20} className="text-purple-400" />
          </div>
          <div className={isRTL ? 'mr-3' : 'ml-3'}>
            <RTLText as="h3" className="text-xl font-semibold text-white">
              {t('admin.analyticsOverview', 'Analytics Overview')}
            </RTLText>
            <RTLText as="p" className="text-sm text-gray-400">
              {t('admin.platformInsights', 'Platform insights and trends')}
            </RTLText>
          </div>
        </div>
        <button
          onClick={onToggleView}
          className={`flex items-center px-4 py-2 bg-purple-600/20 hover:bg-purple-600/30 border border-purple-500/30 rounded-xl text-sm transition-all duration-300 hover:scale-105 ${isRTL ? 'flex-row-reverse' : ''}`}
        >
          {showEnhanced ? (
            <>
              <span className="text-white">{t('admin.basicCharts', 'Basic Charts')}</span>
              <ChevronDown size={16} className={`text-purple-400 ${isRTL ? 'mr-2' : 'ml-2'}`} />
            </>
          ) : (
            <>
              <span className="text-white">{t('admin.advancedCharts', 'Advanced Charts')}</span>
              <ChevronRight size={16} className={`text-purple-400 ${isRTL ? 'mr-2' : 'ml-2'}`} />
            </>
          )}
        </button>
      </div>

      {isLoading ? (
        <div className="flex justify-center py-12">
          <div className="w-10 h-10 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      ) : showEnhanced ? (
        <AdvancedCharts stats={stats} />
      ) : (
        <SimpleCharts stats={stats} />
      )}
    </div>
  );
};

export default AnalyticsSection;
