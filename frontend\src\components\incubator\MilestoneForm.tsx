import React, { useState, useEffect } from 'react';
import { Save, X, Target, Plus } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { BusinessIdea } from '../../services/incubatorApi';

interface Milestone {
  id?: number;
  title: string;
  description: string;
  due_date: string;
  status: 'not_started' | 'in_progress' | 'completed' | 'delayed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'critical';
  business_idea: number;
  assigned_to?: number;
  completion_date?: string;
  completion_notes?: string;
}

interface MilestoneFormProps {
  milestone?: Milestone;
  businessIdea: BusinessIdea;
  onSave: (data: Partial<Milestone>) => Promise<boolean>;
  onCancel: () => void;
  isLoading?: boolean;
  mode: 'create' | 'edit';
}

const MilestoneForm: React.FC<MilestoneFormProps> = ({
  milestone,
  businessIdea,
  onSave,
  onCancel,
  isLoading = false,
  mode
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [formData, setFormData] = useState({
    title: milestone?.title || '',
    description: milestone?.description || '',
    due_date: milestone?.due_date || '',
    status: milestone?.status || 'not_started',
    priority: milestone?.priority || 'medium',
    business_idea: businessIdea.id,
    completion_notes: milestone?.completion_notes || ''
  });

  const [formError, setFormError] = useState<string | null>(null);
  const [hasChanges, setHasChanges] = useState(mode === 'create');

  useEffect(() => {
    if (mode === 'edit' && milestone) {
      // Check if form has changes
      const hasFormChanges = Object.keys(formData).some(key => {
        if (key === 'business_idea') return false;
        const formValue = formData[key as keyof typeof formData];
        const originalValue = milestone[key as keyof Milestone] || '';
        return formValue !== originalValue;
      });
      setHasChanges(hasFormChanges);
    }
  }, [formData, milestone, mode]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    setFormError(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);

    // Validate required fields
    if (!formData.title.trim()) {
      setFormError(t('incubator.milestone.titleRequired'));
      return;
    }

    if (!formData.description.trim()) {
      setFormError(t('incubator.milestone.descriptionRequired'));
      return;
    }

    if (!formData.due_date) {
      setFormError(t('incubator.milestone.dueDateRequired'));
      return;
    }

    // Validate due date is not in the past (unless milestone is completed)
    const dueDate = new Date(formData.due_date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (dueDate < today && formData.status !== 'completed') {
      setFormError(t('incubator.milestone.dueDatePastError'));
      return;
    }

    const success = await onSave(formData);
    if (!success) {
      setFormError(mode === 'create' 
        ? t('incubator.milestone.createError')
        : t('incubator.milestone.updateError')
      );
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 rounded-xl border border-green-500/30 max-w-2xl w-full max-h-[90vh] overflow-hidden shadow-2xl">
        {/* Header */}
        <div className={`flex items-center justify-between p-6 border-b border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            {mode === 'create' ? (
              <Plus size={24} className={`text-green-400 ${isRTL ? 'ml-3' : 'mr-3'}`} />
            ) : (
              <Target size={24} className={`text-green-400 ${isRTL ? 'ml-3' : 'mr-3'}`} />
            )}
            <h2 className="text-xl font-semibold text-white">
              {mode === 'create' 
                ? t('incubator.milestone.createTitle')
                : t('incubator.milestone.editTitle')
              }
            </h2>
          </div>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-white transition-colors"
            disabled={isLoading}
          >
            <X size={20} />
          </button>
        </div>

        {/* Business Idea Info */}
        <div className="px-6 py-4 bg-gray-800/50 border-b border-gray-700">
          <p className={`text-sm text-gray-400 ${isRTL ? 'text-right' : ''}`}>
            {t('incubator.milestone.forBusinessIdea')}: 
            <span className="text-white font-medium ml-1">{businessIdea.title}</span>
          </p>
        </div>

        {/* Form */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Error Message */}
            {formError && (
              <div className="bg-red-900/30 border border-red-500/50 rounded-lg p-4">
                <p className="text-red-400 text-sm">{formError}</p>
              </div>
            )}

            {/* Title */}
            <div>
              <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                {t('incubator.milestone.title')} *
              </label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-white placeholder-gray-400 ${isRTL ? 'text-right' : ''}`}
                placeholder={t('incubator.milestone.titlePlaceholder')}
                required
                disabled={isLoading}
              />
            </div>

            {/* Description */}
            <div>
              <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                {t('incubator.milestone.description')} *
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={4}
                className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-white placeholder-gray-400 resize-none ${isRTL ? 'text-right' : ''}`}
                placeholder={t('incubator.milestone.descriptionPlaceholder')}
                required
                disabled={isLoading}
              />
            </div>

            {/* Due Date and Priority Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Due Date */}
              <div>
                <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                  {t('incubator.milestone.dueDate')} *
                </label>
                <input
                  type="date"
                  name="due_date"
                  value={formData.due_date}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-white ${isRTL ? 'text-right' : ''}`}
                  required
                  disabled={isLoading}
                />
              </div>

              {/* Priority */}
              <div>
                <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                  {t('incubator.milestone.priority')}
                </label>
                <select
                  name="priority"
                  value={formData.priority}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-white ${isRTL ? 'text-right' : ''}`}
                  disabled={isLoading}
                >
                  <option value="low">{t('incubator.milestone.priority.low')}</option>
                  <option value="medium">{t('incubator.milestone.priority.medium')}</option>
                  <option value="high">{t('incubator.milestone.priority.high')}</option>
                  <option value="critical">{t('incubator.milestone.priority.critical')}</option>
                </select>
              </div>
            </div>

            {/* Status */}
            <div>
              <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                {t('incubator.milestone.status')}
              </label>
              <select
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-white ${isRTL ? 'text-right' : ''}`}
                disabled={isLoading}
              >
                <option value="not_started">{t('incubator.milestone.status.notStarted')}</option>
                <option value="in_progress">{t('incubator.milestone.status.inProgress')}</option>
                <option value="completed">{t('incubator.milestone.status.completed')}</option>
                <option value="delayed">{t('incubator.milestone.status.delayed')}</option>
                <option value="cancelled">{t('incubator.milestone.status.cancelled')}</option>
              </select>
            </div>

            {/* Completion Notes (only show if status is completed) */}
            {formData.status === 'completed' && (
              <div>
                <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                  {t('incubator.milestone.completionNotes')}
                </label>
                <textarea
                  name="completion_notes"
                  value={formData.completion_notes}
                  onChange={handleInputChange}
                  rows={3}
                  className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-white placeholder-gray-400 resize-none ${isRTL ? 'text-right' : ''}`}
                  placeholder={t('incubator.milestone.completionNotesPlaceholder')}
                  disabled={isLoading}
                />
              </div>
            )}
          </form>
        </div>

        {/* Footer */}
        <div className={`flex justify-end gap-4 p-6 border-t border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <button
            type="button"
            onClick={onCancel}
            className="px-6 py-2 border border-gray-600 rounded-lg hover:bg-gray-800 transition-colors text-white disabled:opacity-50"
            disabled={isLoading}
          >
            {t('common.cancel')}
          </button>
          <button
            onClick={handleSubmit}
            disabled={isLoading || (mode === 'edit' && !hasChanges)}
            className="px-6 py-2 bg-green-600 rounded-lg hover:bg-green-700 transition-colors flex items-center text-white disabled:opacity-50"
          >
            {isLoading ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                {mode === 'create' ? t('common.creating') : t('common.saving')}
              </>
            ) : (
              <>
                <Save size={16} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                {mode === 'create' ? t('common.create') : t('common.saveChanges')}
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default MilestoneForm;
