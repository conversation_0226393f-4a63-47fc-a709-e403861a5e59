"""
Mobile API Views for Yasmeen AI Platform
Optimized endpoints for mobile app integration
"""

from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.db.models import Q, Count, Avg
from django.utils import timezone
from datetime import timedelta
import json
import logging

logger = logging.getLogger(__name__)

class MobileBusinessIdeaViewSet(viewsets.ViewSet):
    """Mobile-optimized business idea endpoints"""
    permission_classes = [IsAuthenticated]
    
    def list(self, request):
        """Get user's business ideas with mobile-optimized data"""
        try:
            from incubator.models import BusinessIdea
            from incubator.serializers import BusinessIdeaSerializer
            
            user_ideas = BusinessIdea.objects.filter(owner=request.user).select_related(
                'owner'
            ).prefetch_related(
                'progress_updates',
                'ai_recommendations',
                'mentorship_matches'
            )
            
            # Mobile-optimized serialization
            mobile_data = []
            for idea in user_ideas:
                mobile_data.append({
                    'id': idea.id,
                    'title': idea.title,
                    'description': idea.description[:200] + '...' if len(idea.description) > 200 else idea.description,
                    'current_stage': idea.current_stage,
                    'stage_display': idea.get_current_stage_display(),
                    'progress_percentage': getattr(idea, 'progress_percentage', 0),
                    'last_update': idea.updated_at.isoformat(),
                    'has_mentor': idea.mentorship_matches.filter(status='active').exists(),
                    'recommendation_count': idea.ai_recommendations.count(),
                    'image_url': idea.image.url if idea.image else None,
                    'quick_stats': {
                        'updates_count': idea.progress_updates.count(),
                        'days_since_creation': (timezone.now() - idea.created_at).days
                    }
                })
            
            return Response({
                'success': True,
                'data': mobile_data,
                'count': len(mobile_data)
            })
            
        except Exception as e:
            logger.error(f"Mobile business ideas list failed: {e}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=True, methods=['get'])
    def quick_overview(self, request, pk=None):
        """Get quick overview of a business idea for mobile"""
        try:
            from incubator.models import BusinessIdea
            
            idea = BusinessIdea.objects.select_related('owner').prefetch_related(
                'progress_updates',
                'ai_recommendations',
                'mentorship_matches__mentor__user'
            ).get(id=pk, owner=request.user)
            
            # Recent activity
            recent_updates = idea.progress_updates.order_by('-created_at')[:3]
            recent_recommendations = idea.ai_recommendations.order_by('-created_at')[:3]
            
            # Active mentorship
            active_mentorship = idea.mentorship_matches.filter(status='active').first()
            
            overview_data = {
                'basic_info': {
                    'id': idea.id,
                    'title': idea.title,
                    'current_stage': idea.current_stage,
                    'stage_display': idea.get_current_stage_display(),
                    'created_at': idea.created_at.isoformat(),
                    'last_updated': idea.updated_at.isoformat()
                },
                'progress': {
                    'total_updates': idea.progress_updates.count(),
                    'recent_updates': [
                        {
                            'id': update.id,
                            'title': update.title,
                            'created_at': update.created_at.isoformat()
                        } for update in recent_updates
                    ]
                },
                'ai_support': {
                    'total_recommendations': idea.ai_recommendations.count(),
                    'recent_recommendations': [
                        {
                            'id': rec.id,
                            'title': rec.title,
                            'priority': rec.priority,
                            'is_implemented': rec.is_implemented
                        } for rec in recent_recommendations
                    ]
                },
                'mentorship': {
                    'has_active_mentor': bool(active_mentorship),
                    'mentor_info': {
                        'name': f"{active_mentorship.mentor.user.first_name} {active_mentorship.mentor.user.last_name}",
                        'company': active_mentorship.mentor.company
                    } if active_mentorship else None
                }
            }
            
            return Response({
                'success': True,
                'data': overview_data
            })
            
        except Exception as e:
            logger.error(f"Mobile business idea overview failed: {e}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class MobileAIAssistantViewSet(viewsets.ViewSet):
    """Mobile-optimized AI assistant endpoints"""
    permission_classes = [IsAuthenticated]
    
    @action(detail=False, methods=['post'])
    def quick_chat(self, request):
        """Quick chat interface for mobile"""
        try:
            from ai_recommendations.consolidated_ai_service import process_message
            
            message = request.data.get('message', '')
            business_idea_id = request.data.get('business_idea_id')
            language = request.data.get('language', 'en')
            
            if not message:
                return Response({
                    'success': False,
                    'error': 'Message is required'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Prepare context for mobile
            context = {
                'user_id': request.user.id,
                'language': language,
                'platform': 'mobile',
                'business_idea_id': business_idea_id
            }
            
            # Process message
            response = process_message(message, context)
            
            # Mobile-optimized response
            mobile_response = {
                'message': response.get('response', ''),
                'language': response.get('language', language),
                'workflow_used': response.get('workflow_type', 'chat'),
                'confidence': response.get('confidence_score', 0),
                'suggestions': response.get('suggestions', []),
                'timestamp': timezone.now().isoformat()
            }
            
            return Response({
                'success': True,
                'data': mobile_response
            })
            
        except Exception as e:
            logger.error(f"Mobile AI chat failed: {e}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['get'])
    def quick_recommendations(self, request):
        """Get quick AI recommendations for mobile using new centralized AI service"""
        try:
            from core.ai_service import ai_generate_intelligent_content

            business_idea_id = request.query_params.get('business_idea_id')
            limit = int(request.query_params.get('limit', 5))

            # Generate AI recommendations using new service
            if business_idea_id:
                from incubator.models import BusinessIdea
                try:
                    business_idea = BusinessIdea.objects.get(id=business_idea_id, owner=request.user)
                    ai_response = ai_generate_intelligent_content(
                        'quick_recommendations',
                        {
                            'business_idea': business_idea.title,
                            'description': business_idea.description,
                            'limit': limit
                        },
                        'en',
                        request.user.id
                    )

                    if ai_response.get('success'):
                        # Return mock recommendations structure for mobile compatibility
                        return Response({
                            'recommendations': [{
                                'id': i,
                                'title': f'AI Recommendation {i+1}',
                                'description': ai_response.get('content', 'AI recommendation content'),
                                'priority': 'medium',
                                'recommendation_type': 'general'
                            } for i in range(min(limit, 3))]
                        })
                except BusinessIdea.DoesNotExist:
                    pass
            
            if business_idea_id:
                recommendations_query = recommendations_query.filter(
                    business_idea_id=business_idea_id
                )
            
            recommendations = recommendations_query[:limit]
            
            mobile_recommendations = []
            for rec in recommendations:
                mobile_recommendations.append({
                    'id': rec.id,
                    'title': rec.title,
                    'description': rec.description[:150] + '...' if len(rec.description) > 150 else rec.description,
                    'priority': rec.priority,
                    'priority_display': rec.get_priority_display(),
                    'type': rec.recommendation_type,
                    'type_display': rec.get_recommendation_type_display(),
                    'is_implemented': rec.is_implemented,
                    'relevance_score': rec.relevance_score,
                    'created_at': rec.created_at.isoformat(),
                    'business_idea': {
                        'id': rec.business_idea.id,
                        'title': rec.business_idea.title
                    }
                })
            
            return Response({
                'success': True,
                'data': mobile_recommendations,
                'count': len(mobile_recommendations)
            })
            
        except Exception as e:
            logger.error(f"Mobile AI recommendations failed: {e}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class MobileDashboardViewSet(viewsets.ViewSet):
    """Mobile dashboard with key metrics"""
    permission_classes = [IsAuthenticated]
    
    @action(detail=False, methods=['get'])
    def overview(self, request):
        """Get mobile dashboard overview"""
        try:
            from incubator.models import BusinessIdea, ProgressUpdate
            from forums.models import ForumPost, ForumThread

            user = request.user

            # Business ideas summary
            business_ideas = BusinessIdea.objects.filter(owner=user)
            ideas_count = business_ideas.count()

            # Recent activity
            recent_updates = ProgressUpdate.objects.filter(
                business_idea__owner=user
            ).order_by('-created_at')[:5]

            # AI interaction summary (using new centralized service)
            ai_messages_count = 0  # Will be tracked differently with new service
            pending_recommendations = 0  # Will be generated on-demand with new service
            
            # Forum activity
            forum_posts_count = ForumPost.objects.filter(author=user).count()
            forum_threads_count = ForumThread.objects.filter(author=user).count()
            
            # Quick stats
            dashboard_data = {
                'user_info': {
                    'name': f"{user.first_name} {user.last_name}".strip() or user.username,
                    'email': user.email,
                    'member_since': user.date_joined.isoformat()
                },
                'business_summary': {
                    'total_ideas': ideas_count,
                    'active_ideas': business_ideas.exclude(current_stage='established').count(),
                    'recent_updates_count': recent_updates.count()
                },
                'ai_summary': {
                    'total_messages': ai_messages_count,
                    'pending_recommendations': pending_recommendations,
                    'last_interaction': ChatMessage.objects.filter(
                        user=user
                    ).order_by('-created_at').first().created_at.isoformat() if ai_messages_count > 0 else None
                },
                'community_summary': {
                    'forum_posts': forum_posts_count,
                    'forum_threads': forum_threads_count,
                    'total_contributions': forum_posts_count + forum_threads_count
                },
                'recent_activity': [
                    {
                        'id': update.id,
                        'type': 'progress_update',
                        'title': update.title,
                        'business_idea': update.business_idea.title,
                        'created_at': update.created_at.isoformat()
                    } for update in recent_updates
                ]
            }
            
            return Response({
                'success': True,
                'data': dashboard_data
            })
            
        except Exception as e:
            logger.error(f"Mobile dashboard overview failed: {e}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['get'])
    def notifications(self, request):
        """Get mobile notifications"""
        try:
            # This would integrate with a notification system
            # For now, return sample structure
            notifications = [
                {
                    'id': 1,
                    'type': 'ai_recommendation',
                    'title': 'New AI Recommendation',
                    'message': 'You have a new high-priority recommendation for your business idea.',
                    'is_read': False,
                    'created_at': timezone.now().isoformat(),
                    'action_url': '/recommendations/123'
                },
                {
                    'id': 2,
                    'type': 'mentorship',
                    'title': 'Mentorship Session Reminder',
                    'message': 'Your mentorship session is scheduled for tomorrow at 2 PM.',
                    'is_read': False,
                    'created_at': (timezone.now() - timedelta(hours=2)).isoformat(),
                    'action_url': '/mentorship/sessions/456'
                }
            ]
            
            return Response({
                'success': True,
                'data': notifications,
                'unread_count': sum(1 for n in notifications if not n['is_read'])
            })
            
        except Exception as e:
            logger.error(f"Mobile notifications failed: {e}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# Export viewsets
__all__ = [
    'MobileBusinessIdeaViewSet',
    'MobileAIAssistantViewSet', 
    'MobileDashboardViewSet'
]
