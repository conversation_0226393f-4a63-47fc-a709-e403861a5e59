/**
 * Hooks Index
 *
 * This file exports all hooks from the hooks directory.
 * Import hooks from here instead of directly from their files.
 */

// Authentication hooks
export { default as useAuth } from './useAuth';

// Language and RTL hooks
export { default as useLanguage } from './useLanguage';
export { useLanguageDirection } from '../utils/rtl';
export { default as useRTLCSSProperties } from '../utils/rtl/cssUtils';

// Theme hooks
export { useThemeSync, useTheme } from './useThemeSync';

// API hooks
export {
  useApiQuery,
  useApiList,
  useApiInfinite,
  usePrefetchApiQuery,
  usePrefetchApiList,
  useApiCreate,
  useApiUpdate,
  useApiUpdateById,
  useApiPatch,
  useApiPatchById,
  useApiDelete,
  useApiDeleteById,
  useApiUpload
} from './useApi';
export {
  UserKeys,
  useUsersList,
  useUsersInfinite,
  useUser,
  useCurrentUser,
  usePrefetchUser,
  usePrefetchUsersList,
  useCreateUser,
  useUpdateUser,
  useUpdateCurrentUser,
  useUpdateUserProfile
} from './useUsers';
// Posts hooks
export {
  PostKeys,
  usePostsList,
  usePostsInfinite,
  usePost,
  usePrefetchPost,
  usePrefetchPostsList,
  useCreatePost,
  useUpdatePost,
  usePatchPost,
  useDeletePost,
  useLikePost,
  useUnlikePost,
  useCommentPost
} from './usePosts';

// Optimistic posts hooks (these override some of the above)
export {
  useCreatePost as useOptimisticCreatePost,
  useUpdatePost as useOptimisticUpdatePost,
  useDeletePost as useOptimisticDeletePost
} from './useOptimisticPosts';

// AI Hooks - Centralized AI Service (Recommended)
export {
  useCentralizedAI,
  useCentralizedChat,
  useCentralizedBusinessAnalysis,
  useCentralizedAIStatus
} from './useCentralizedAI';

// Automatic AI Hook
export { useAutomaticAI } from './useAutomaticAI';

// Business Plans hooks
export {
  BusinessPlanKeys,
  useBusinessPlansList,
  useBusinessPlansInfinite,
  useBusinessPlan,
  usePrefetchBusinessPlan,
  usePrefetchBusinessPlansList,
  useCreateBusinessPlan,
  useUpdateBusinessPlan,
  useDeleteBusinessPlan,
  useGenerateBusinessPlanFeedback,
  useDuplicateBusinessPlan
} from './useBusinessPlans';

// Mentorship hooks
export {
  MentorshipKeys,
  useMentorProfilesList,
  useMentorProfilesInfinite,
  useMentorProfile,
  useCreateMentorProfile,
  useUpdateMentorProfile,
  useDeleteMentorProfile,
  useMentorshipApplicationsList,
  useMentorshipApplication,
  useCreateMentorshipApplication,
  useUpdateMentorshipApplication,
  useDeleteMentorshipApplication,
  useMentorshipMatchesList,
  useMentorshipMatch,
  useCreateMentorshipMatch,
  useUpdateMentorshipMatch,
  useDeleteMentorshipMatch,
  useMentorshipSessionsList,
  useMentorshipSession,
  useCreateMentorshipSession,
  useUpdateMentorshipSession,
  useDeleteMentorshipSession
} from './useMentorship';

// Funding hooks
export {
  FundingKeys,
  useInvestorProfilesList,
  useInvestorProfilesInfinite,
  useInvestorProfile,
  useCreateInvestorProfile,
  useUpdateInvestorProfile,
  useDeleteInvestorProfile,
  useFundingOpportunitiesList,
  useFundingOpportunitiesInfinite,
  useFundingOpportunity,
  useCreateFundingOpportunity,
  useUpdateFundingOpportunity,
  useDeleteFundingOpportunity,
  useFundingApplicationsList,
  useFundingApplication,
  useCreateFundingApplication,
  useUpdateFundingApplication,
  useDeleteFundingApplication,
  useInvestmentsList,
  useInvestment,
  useCreateInvestment,
  useUpdateInvestment,
  useDeleteInvestment,
  useApproveFundingApplication,
  useCreateInvestmentFromApplication
} from './useFunding';

// Milestone hooks
export {
  MilestoneKeys,
  useBusinessMilestonesList,
  useBusinessMilestonesInfinite,
  useBusinessMilestone,
  useCreateBusinessMilestone,
  useUpdateBusinessMilestone,
  useDeleteBusinessMilestone,
  useBusinessGoalsList,
  useBusinessGoalsInfinite,
  useBusinessGoal,
  useCreateBusinessGoal,
  useUpdateBusinessGoal,
  useDeleteBusinessGoal,
  useCompleteMilestone,
  useUpdateMilestoneStatus,
  useUpdateGoalProgress,
  useBulkMilestoneUpdate,
  useGenerateMilestoneRecommendations,
  useMilestoneAnalytics
} from './useMilestones';

// Utility hooks
export { useInfiniteScroll } from './useInfiniteScroll';
export { usePerformanceMonitor } from './usePerformanceMonitor';
export { default as useCRUD } from './useCRUD';
