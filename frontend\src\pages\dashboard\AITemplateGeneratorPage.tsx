import { useLanguage } from "../../hooks/useLanguage";
/**
 * AI Template Generator Page
 * Create custom business plan templates using AI
 */

import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
  Sparkles,
  Brain,
  Zap,
  Target,
  TrendingUp,
  ArrowRight,
  CheckCircle,
  Star,
  Clock
} from 'lucide-react';
// MainLayout removed - handled by routing system
import AITemplateGenerator from '../../components/templates/AITemplateGenerator';
import { RTLText, RTLFlex } from '../../components/common';

const AITemplateGeneratorPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();
  const [showGenerator, setShowGenerator] = useState(false);

  const features = [
    {
      icon: Brain,
      title: t('aiGeneration.features.intelligentAnalysis'),
      description: t('aiGeneration.features.intelligentAnalysisDesc'),
      color: 'text-purple-400'
    },
    {
      icon: Target,
      title: t('aiGeneration.features.industrySpecific'),
      description: t('aiGeneration.features.industrySpecificDesc'),
      color: 'text-blue-400'
    },
    {
      icon: Zap,
      title: t('aiGeneration.features.fastGeneration'),
      description: t('aiGeneration.features.fastGenerationDesc'),
      color: 'text-green-400'
    },
    {
      icon: TrendingUp,
      title: t('aiGeneration.features.optimizedSuccess'),
      description: t('aiGeneration.features.optimizedSuccessDesc'),
      color: 'text-yellow-400'
    }
  ];

  const benefits = [
    t('aiGeneration.benefits.saveTime'),
    t('aiGeneration.benefits.expertGuidance'),
    t('aiGeneration.benefits.customized'),
    t('aiGeneration.benefits.comprehensive'),
    t('aiGeneration.benefits.aiPowered')
  ];

  const handleTemplateGenerated = (template: any) => {
    console.log("Generated template:", template);
    setShowGenerator(false);
    // Navigate to the new template or show success message
    navigate('/dashboard/templates');
  };

  if (showGenerator) {
    return (
      <AITemplateGenerator
        onTemplateGenerated={handleTemplateGenerated}
        onClose={() => setShowGenerator(false)}
      />
    );
  }

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="space-y-8">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-purple-600/20 to-blue-600/20 rounded-lg p-8">
          <RTLFlex className="items-center justify-between">
            <div className={`flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
              <RTLFlex className="items-center mb-4">
                <Sparkles className={`text-purple-400 mr-3 ${isRTL ? "space-x-reverse" : ""}`} size={32} />
                <RTLText as="h1" className="text-3xl font-bold">
                  {t('aiGeneration.title')}
                </RTLText>
              </RTLFlex>
              <p className="text-xl text-gray-300 mb-6">
                {t('aiGeneration.subtitle')}
              </p>
              <p className="text-gray-400 mb-8">
                {t('aiGeneration.description')}
              </p>

              <button
                onClick={() => setShowGenerator(true)}
                className={`px-8 py-4 bg-purple-600 hover:bg-purple-700 rounded-lg text-white text-lg font-semibold transition-colors flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <Sparkles size={24} className={`mr-3 ${isRTL ? "space-x-reverse" : ""}`} />
                {t('aiGeneration.startGenerating')}
                <ArrowRight size={20} className={`ml-3 ${isRTL ? "space-x-reverse" : ""}`} />
              </button>
            </div>

            {/* Stats */}
            <div className={`grid grid-cols-2 gap-6 ml-8 ${isRTL ? "space-x-reverse" : ""}`}>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-400">15+</div>
                <div className="text-sm text-gray-400">{t('aiGeneration.stats.industries')}</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-400">30+</div>
                <div className="text-sm text-gray-400">{t('aiGeneration.stats.sectionTypes')}</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-400">5min</div>
                <div className="text-sm text-gray-400">{t('aiGeneration.stats.avgTime')}</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-yellow-400">95%</div>
                <div className="text-sm text-gray-400">{t('aiGeneration.stats.satisfaction')}</div>
              </div>
            </div>
          </RTLFlex>
        </div>

        {/* Features Grid */}
        <div>
          <RTLText as="h2" className="text-2xl font-bold mb-6">
            {t('aiGeneration.howItWorks')}
          </RTLText>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <div key={index} className="bg-gray-800/50 rounded-lg p-6">
                  <div className={`p-3 rounded-lg ${feature.color.replace('text-', 'bg-').replace('-400', '-600/20')} mb-4 w-fit`}>
                    <Icon size={24} className={feature.color} />
                  </div>
                  <RTLText as="h3" className="text-lg font-semibold mb-2">
                    {feature.title}
                  </RTLText>
                  <p className="text-gray-400 text-sm">
                    {feature.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>

        {/* Benefits Section */}
        <div className="bg-gray-800/50 rounded-lg p-8">
          <RTLText as="h2" className="text-2xl font-bold mb-6">
            {t('aiGeneration.whyChooseAI')}
          </RTLText>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              {benefits.map((benefit, index) => (
                <RTLFlex key={index} className="items-center">
                  <CheckCircle size={20} className={`text-green-400 mr-3 flex-shrink-0 ${isRTL ? "flex-row-reverse" : ""}`} />
                  <span className="text-gray-300">{benefit}</span>
                </RTLFlex>
              ))}
            </div>

            {/* Process Steps */}
            <div className="space-y-4">
              <RTLText as="h3" className="text-lg font-semibold mb-4">
                {t('aiGeneration.simpleProcess')}
              </RTLText>
              <div className="space-y-3">
                <div className={`flex items-center p-3 bg-gray-700/50 rounded-lg ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className={`w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-sm font-bold mr-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                    1
                  </div>
                  <span className="text-sm">{t('aiGeneration.process.step1')}</span>
                </div>
                <div className={`flex items-center p-3 bg-gray-700/50 rounded-lg ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className={`w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-sm font-bold mr-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                    2
                  </div>
                  <span className="text-sm">{t('aiGeneration.process.step2')}</span>
                </div>
                <div className={`flex items-center p-3 bg-gray-700/50 rounded-lg ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className={`w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-sm font-bold mr-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                    3
                  </div>
                  <span className="text-sm">{t('aiGeneration.process.step3')}</span>
                </div>
                <div className={`flex items-center p-3 bg-gray-700/50 rounded-lg ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className={`w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-sm font-bold mr-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                    4
                  </div>
                  <span className="text-sm">{t('aiGeneration.process.step4')}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Testimonials */}
        <div>
          <RTLText as="h2" className="text-2xl font-bold mb-6">
            {t('aiGeneration.whatUsersSay')}
          </RTLText>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[
              {
                name: 'أحمد محمد',
                role: 'مؤسس شركة تقنية',
                comment: 'وفر علي الذكاء الاصطناعي ساعات من العمل في إنشاء خطة عمل شاملة ومخصصة لشركتي.',
                rating: 5
              },
              {
                name: 'سارة أحمد',
                role: 'رائدة أعمال',
                comment: 'القوالب المولدة بالذكاء الاصطناعي دقيقة جداً وتحتوي على كل ما أحتاجه لبدء مشروعي.',
                rating: 5
              },
              {
                name: 'محمد علي',
                role: 'مستشار أعمال',
                comment: 'أستخدم هذه الأداة مع عملائي وهي تساعدهم في إنشاء خطط عمل احترافية بسرعة.',
                rating: 5
              }
            ].map((testimonial, index) => (
              <div key={index} className="bg-gray-800/50 rounded-lg p-6">
                <RTLFlex className="items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} size={16} className="text-yellow-400 fill-current" />
                  ))}
                </RTLFlex>
                <p className="text-gray-300 mb-4 italic">"{testimonial.comment}"</p>
                <div>
                  <RTLText className="font-semibold">{testimonial.name}</RTLText>
                  <div className="text-sm text-gray-400">{testimonial.role}</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-purple-600/20 to-blue-600/20 rounded-lg p-8 text-center">
          <RTLText as="h2" className="text-2xl font-bold mb-4">
            {t('aiGeneration.readyToStart')}
          </RTLText>
          <p className="text-gray-300 mb-6">
            {t('aiGeneration.readyToStartDesc')}
          </p>
          <button
            onClick={() => setShowGenerator(true)}
            className={`px-8 py-4 bg-purple-600 hover:bg-purple-700 rounded-lg text-white text-lg font-semibold transition-colors flex items-center mx-auto ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <Sparkles size={24} className={`mr-3 ${isRTL ? "space-x-reverse" : ""}`} />
            {t('aiGeneration.generateNow')}
            <ArrowRight size={20} className={`ml-3 ${isRTL ? "space-x-reverse" : ""}`} />
          </button>
        </div>
      </div>
              </div>
        </div>
      </div>
    </div>
  );
};

export default AITemplateGeneratorPage;
