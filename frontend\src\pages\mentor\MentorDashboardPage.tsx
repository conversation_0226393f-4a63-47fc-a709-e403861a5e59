import React from 'react';
import MentorDashboard from '../../components/dashboard/mentor-dashboard/MentorDashboard';

/**
 * DEDICATED MENTOR DASHBOARD PAGE
 * 
 * This page is specifically for mentor users only.
 * It provides mentorship management and guidance tools.
 * 
 * Key Features:
 * - Mentee management and tracking
 * - Session scheduling and management
 * - Progress monitoring for mentees
 * - Resource sharing and recommendations
 * - Mentorship analytics and insights
 * - Availability management
 * - Communication tools
 * - Performance tracking
 */
const MentorDashboardPage: React.FC = () => {
  return <MentorDashboard />;
};

export default MentorDashboardPage;
