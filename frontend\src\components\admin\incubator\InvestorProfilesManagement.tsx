import React, { useState, useEffect } from 'react';
import { Search, User, DollarSign, Building, TrendingUp, MapPin, Globe } from 'lucide-react';
import { DashboardLayout } from '../dashboard';
import { InvestorProfile, investorProfilesAPI } from '../../../services/incubatorApi';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';

const InvestorProfilesManagement: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  // State management
  const [investors, setInvestors] = useState<InvestorProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch investor profiles
  const fetchInvestors = async () => {
    try {
      setLoading(true);
      const data = await investorProfilesAPI.getInvestorProfiles();
      setInvestors(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error fetching investor profiles:', error);
      setInvestors([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchInvestors();
  }, []);

  // Filter investors based on search
  const filteredInvestors = investors.filter(investor => {
    return investor.user.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      investor.user.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (investor.company_name && investor.company_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (investor.investment_focus && investor.investment_focus.toLowerCase().includes(searchTerm.toLowerCase())) ||
      investor.investor_type.toLowerCase().includes(searchTerm.toLowerCase());
  });

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Get investor type color and label
  const getInvestorTypeDisplay = (type: string) => {
    switch (type) {
      case 'angel':
        return {
          color: 'bg-blue-500/20 text-blue-300',
          label: t('investor.type.angel', 'Angel Investor')
        };
      case 'vc':
        return {
          color: 'bg-purple-500/20 text-purple-300',
          label: t('investor.type.vc', 'Venture Capital')
        };
      case 'corporate':
        return {
          color: 'bg-green-500/20 text-green-300',
          label: t('investor.type.corporate', 'Corporate Investor')
        };
      case 'fund':
        return {
          color: 'bg-orange-500/20 text-orange-300',
          label: t('investor.type.fund', 'Investment Fund')
        };
      case 'individual':
        return {
          color: 'bg-cyan-500/20 text-cyan-300',
          label: t('investor.type.individual', 'Individual Investor')
        };
      default:
        return {
          color: 'bg-gray-500/20 text-gray-300',
          label: type
        };
    }
  };

  // Format investment range
  const formatInvestmentRange = (min?: number, max?: number) => {
    if (min && max) {
      return `$${min.toLocaleString()} - $${max.toLocaleString()}`;
    } else if (min) {
      return `$${min.toLocaleString()}+`;
    } else if (max) {
      return `Up to $${max.toLocaleString()}`;
    }
    return t('investor.range.not.specified', 'Range not specified');
  };

  return (
    <DashboardLayout currentPage="incubator">
      {/* Header */}
      <div className={`mb-8 ${isRTL ? "text-right" : ""}`}>
        <h1 className="text-2xl font-bold text-white">{t('admin.investor.profiles.management', 'Investor Profiles Management')}</h1>
        <div className="text-gray-400 mt-1">{t('admin.investor.profiles.description', 'View and manage investor profiles')}</div>
      </div>

      {/* Search */}
      <div className="mb-6">
        <div className="relative">
          <Search size={20} className={`absolute top-3 text-gray-400 ${isRTL ? "right-3" : "left-3"}`} />
          <input
            type="text"
            placeholder={t('admin.search.investors', 'Search investor profiles...')}
            value={searchTerm}
            onChange={handleSearchChange}
            className={`w-full bg-indigo-900/50 border border-indigo-800 rounded-lg py-2.5 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 ${isRTL ? "pr-10 pl-4" : "pl-10 pr-4"}`}
          />
        </div>
      </div>

      {/* Investors List */}
      {loading ? (
        <div className="flex justify-center py-12">
          <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      ) : filteredInvestors.length > 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredInvestors.map((investor) => {
            const typeDisplay = getInvestorTypeDisplay(investor.investor_type);
            
            return (
              <div key={investor.id} className="bg-indigo-900/30 backdrop-blur-sm rounded-xl overflow-hidden border border-indigo-800/50 hover:border-purple-500/50 transition-all duration-300 hover:shadow-glow">
                <div className="p-6">
                  <div className={`flex justify-between items-start mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className="flex-1">
                      <div className={`flex items-center space-x-3 mb-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                        <div className="w-12 h-12 rounded-full bg-green-700 flex items-center justify-center text-white text-lg font-bold">
                          {investor.user.first_name.charAt(0)}{investor.user.last_name.charAt(0)}
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-white">
                            {investor.user.first_name} {investor.user.last_name}
                          </h3>
                          {investor.company_name && (
                            <p className="text-gray-300 text-sm">{investor.company_name}</p>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex flex-wrap gap-2 mb-3">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${typeDisplay.color}`}>
                          {typeDisplay.label}
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${investor.is_accredited ? 'bg-green-500/20 text-green-300' : 'bg-gray-500/20 text-gray-300'}`}>
                          {investor.is_accredited ? t('investor.accredited', 'Accredited') : t('investor.non.accredited', 'Non-Accredited')}
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${investor.is_active ? 'bg-blue-500/20 text-blue-300' : 'bg-gray-500/20 text-gray-300'}`}>
                          {investor.is_active ? t('common.active', 'Active') : t('common.inactive', 'Inactive')}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    {/* Investment Range */}
                    <div className={`flex items-center text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
                      <DollarSign size={16} className={`text-green-400 ${isRTL ? "ml-2" : "mr-2"}`} />
                      <span className="text-gray-300">
                        {formatInvestmentRange(investor.min_investment, investor.max_investment)}
                      </span>
                    </div>

                    {/* Investment Focus */}
                    {investor.investment_focus && (
                      <div className={`flex items-center text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
                        <TrendingUp size={16} className={`text-purple-400 ${isRTL ? "ml-2" : "mr-2"}`} />
                        <span className="text-gray-300 line-clamp-1">{investor.investment_focus}</span>
                      </div>
                    )}

                    {/* Location */}
                    {investor.location && (
                      <div className={`flex items-center text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
                        <MapPin size={16} className={`text-blue-400 ${isRTL ? "ml-2" : "mr-2"}`} />
                        <span className="text-gray-300">{investor.location}</span>
                      </div>
                    )}

                    {/* Website */}
                    {investor.website && (
                      <div className={`flex items-center text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
                        <Globe size={16} className={`text-cyan-400 ${isRTL ? "ml-2" : "mr-2"}`} />
                        <a 
                          href={investor.website} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-cyan-300 hover:text-cyan-200 transition-colors line-clamp-1"
                        >
                          {investor.website}
                        </a>
                      </div>
                    )}

                    {/* Portfolio Size */}
                    {investor.portfolio_size && (
                      <div className={`flex items-center text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
                        <Building size={16} className={`text-orange-400 ${isRTL ? "ml-2" : "mr-2"}`} />
                        <span className="text-gray-300">
                          {investor.portfolio_size} {t('investor.portfolio.companies', 'portfolio companies')}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Bio */}
                  {investor.bio && (
                    <div className="mt-4 pt-4 border-t border-indigo-800/50">
                      <p className="text-gray-300 text-sm line-clamp-3">{investor.bio}</p>
                    </div>
                  )}

                  {/* Investment Criteria */}
                  {investor.investment_criteria && (
                    <div className="mt-4 pt-4 border-t border-indigo-800/50">
                      <h4 className="text-sm font-medium text-gray-300 mb-2">{t('investor.criteria', 'Investment Criteria')}</h4>
                      <p className="text-gray-300 text-sm line-clamp-2">{investor.investment_criteria}</p>
                    </div>
                  )}

                  {/* Preferred Industries */}
                  {investor.preferred_industries && investor.preferred_industries.length > 0 && (
                    <div className="mt-4 pt-4 border-t border-indigo-800/50">
                      <h4 className="text-sm font-medium text-gray-300 mb-2">{t('investor.industries', 'Preferred Industries')}</h4>
                      <div className="flex flex-wrap gap-1">
                        {investor.preferred_industries.slice(0, 3).map((industry, index) => (
                          <span key={index} className="px-2 py-1 bg-indigo-800/50 rounded text-xs text-gray-300">
                            {industry}
                          </span>
                        ))}
                        {investor.preferred_industries.length > 3 && (
                          <span className="px-2 py-1 bg-indigo-800/50 rounded text-xs text-gray-400">
                            +{investor.preferred_industries.length - 3} {t('common.more', 'more')}
                          </span>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Footer */}
                  <div className={`flex justify-between items-center mt-4 pt-4 border-t border-indigo-800/50 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className="text-xs text-gray-400">
                      {t('investor.joined', 'Joined')} {new Date(investor.created_at).toLocaleDateString()}
                    </div>
                    <div className="text-xs text-gray-400">
                      {investor.investments_count || 0} {t('investor.investments', 'investments')}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        <div className="text-center py-12">
          <User size={48} className="mx-auto text-gray-600 mb-4" />
          <h3 className="text-lg font-medium text-gray-400 mb-2">
            {searchTerm 
              ? t('admin.no.investors.found', 'No investor profiles found')
              : t('admin.no.investors', 'No investor profiles yet')
            }
          </h3>
          <p className="text-gray-500 mb-4">
            {searchTerm
              ? t('admin.try.different.search', 'Try adjusting your search')
              : t('admin.no.investors.description', 'Investor profiles will appear here when they register')
            }
          </p>
        </div>
      )}
    </DashboardLayout>
  );
};

export default InvestorProfilesManagement;
