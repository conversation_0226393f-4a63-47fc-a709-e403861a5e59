import React from 'react';
import {
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  Tooltip,
  ResponsiveContainer
} from 'recharts';
import { useTranslation } from 'react-i18next';
import { useLanguageDirection } from '../../../../../utils/rtl';

const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff8042', '#0088fe'];

interface ContributorsTabProps {
  data: any[];
}

const ContributorsTab: React.FC<ContributorsTabProps> = ({ data  }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  if (!data || data.length === 0) return <div className="text-center py-8">t("admin.no.data.available", t("common.noDataAvailable", "No data available"))</div>;

  return (
    <div className="space-y-6">
      <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
        <h3 className="text-xl font-semibold mb-4">t("admin.top.contributors", "Top Contributors")</h3>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-indigo-800/50">
                <th className={`px-4 py-2  ${isRTL ? "text-right" : "text-left"}`}>t("admin.rank", "Rank")</th>
                <th className={`px-4 py-2  ${isRTL ? "text-right" : "text-left"}`}>t("admin.user", "User")</th>
                <th className={`px-4 py-2  ${isRTL ? "text-right" : "text-left"}`}>t("admin.level", "Level")</th>
                <th className="px-4 py-2 text-right">t("admin.points", "Points")</th>
                <th className="px-4 py-2 text-right">t("admin.threads", "Threads")</th>
                <th className="px-4 py-2 text-right">t("admin.posts", "Posts")</th>
                <th className="px-4 py-2 text-right">t("admin.solutions", "Solutions")</th>
                <th className="px-4 py-2 text-right">t("admin.likes", "Likes")</th>
              </tr>
            </thead>
            <tbody>
              {data.map((user, index) => (
                <tr key={user.id} className="border-b border-indigo-800/30">
                  <td className={`px-4 py-3  ${isRTL ? "text-right" : "text-left"}`}>{index + 1}</td>
                  <td className={`px-4 py-3  font-medium ${isRTL ? "text-right" : "text-left"}`}>{user.username}</td>
                  <td className={`px-4 py-3  ${isRTL ? "text-right" : "text-left"}`}>
                    <span className="px-2 py-1 bg-indigo-800/50 rounded text-xs">
                      {user.level}
                    </span>
                  </td>
                  <td className="px-4 py-3 text-right">{user.points}</td>
                  <td className="px-4 py-3 text-right">{user.thread_count}</td>
                  <td className="px-4 py-3 text-right">{user.post_count}</td>
                  <td className="px-4 py-3 text-right">{user.solution_count}</td>
                  <td className="px-4 py-3 text-right">{user.like_count}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
        <h3 className="text-xl font-semibold mb-4">t("admin.contribution.distribution", "Contribution Distribution")</h3>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <RechartsPieChart>
              <Pie
                data={data.map((user) => ({
                  name: user.username,
                  value: user.points
                }))}
                cx="50%"
                cy="50%"
                labelLine={false}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
                label={({ name, percent }: any) => `${name} ${(percent * 100).toFixed(0)}%`}
              >
                {data.map((_, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip
                contentStyle={{ backgroundColor: '#1e1e3f', borderColor: '#444', color: '#fff' }}
              />
            </RechartsPieChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
};

export default ContributorsTab;
