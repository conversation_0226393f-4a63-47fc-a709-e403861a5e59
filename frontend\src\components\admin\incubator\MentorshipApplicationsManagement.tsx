import React, { useState, useEffect } from 'react';
import { Search, User, Clock, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { DashboardLayout } from '../dashboard';
import { MentorshipApplication, mentorshipApplicationsAPI } from '../../../services/incubatorApi';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';

const MentorshipApplicationsManagement: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  // State management
  const [applications, setApplications] = useState<MentorshipApplication[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch mentorship applications
  const fetchApplications = async () => {
    try {
      setLoading(true);
      const data = await mentorshipApplicationsAPI.getMentorshipApplications();
      setApplications(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error fetching mentorship applications:', error);
      setApplications([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchApplications();
  }, []);

  // Filter applications based on search
  const filteredApplications = applications.filter(application => {
    return application.mentee.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      application.mentee.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      application.mentor_profile.user.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      application.mentor_profile.user.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      application.status.toLowerCase().includes(searchTerm.toLowerCase());
  });

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Get status color and icon
  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'pending':
        return {
          color: 'bg-yellow-500/20 text-yellow-300',
          icon: <Clock size={16} />,
          label: t('application.status.pending', 'Pending')
        };
      case 'approved':
        return {
          color: 'bg-green-500/20 text-green-300',
          icon: <CheckCircle size={16} />,
          label: t('application.status.approved', 'Approved')
        };
      case 'rejected':
        return {
          color: 'bg-red-500/20 text-red-300',
          icon: <XCircle size={16} />,
          label: t('application.status.rejected', 'Rejected')
        };
      case 'under_review':
        return {
          color: 'bg-blue-500/20 text-blue-300',
          icon: <AlertCircle size={16} />,
          label: t('application.status.under_review', 'Under Review')
        };
      default:
        return {
          color: 'bg-gray-500/20 text-gray-300',
          icon: <Clock size={16} />,
          label: status
        };
    }
  };

  return (
    <DashboardLayout currentPage="incubator">
      {/* Header */}
      <div className={`mb-8 ${isRTL ? "text-right" : ""}`}>
        <h1 className="text-2xl font-bold text-white">{t('admin.mentorship.applications.management', 'Mentorship Applications Management')}</h1>
        <div className="text-gray-400 mt-1">{t('admin.mentorship.applications.description', 'Review and manage mentorship applications')}</div>
      </div>

      {/* Search */}
      <div className="mb-6">
        <div className="relative">
          <Search size={20} className={`absolute top-3 text-gray-400 ${isRTL ? "right-3" : "left-3"}`} />
          <input
            type="text"
            placeholder={t('admin.search.applications', 'Search mentorship applications...')}
            value={searchTerm}
            onChange={handleSearchChange}
            className={`w-full bg-indigo-900/50 border border-indigo-800 rounded-lg py-2.5 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 ${isRTL ? "pr-10 pl-4" : "pl-10 pr-4"}`}
          />
        </div>
      </div>

      {/* Applications List */}
      {loading ? (
        <div className="flex justify-center py-12">
          <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      ) : filteredApplications.length > 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredApplications.map((application) => {
            const statusDisplay = getStatusDisplay(application.status);
            return (
              <div key={application.id} className="bg-indigo-900/30 backdrop-blur-sm rounded-xl overflow-hidden border border-indigo-800/50 hover:border-purple-500/50 transition-all duration-300 hover:shadow-glow">
                <div className="p-6">
                  <div className={`flex justify-between items-start mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className="flex-1">
                      <div className={`flex items-center space-x-3 mb-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                        <div className="w-10 h-10 rounded-full bg-purple-700 flex items-center justify-center text-white text-sm font-bold">
                          {application.mentee.first_name.charAt(0)}{application.mentee.last_name.charAt(0)}
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-white">
                            {application.mentee.first_name} {application.mentee.last_name}
                          </h3>
                          <p className="text-gray-300 text-sm">{t('application.mentee', 'Mentee')}</p>
                        </div>
                      </div>
                      
                      <div className={`flex items-center space-x-2 mb-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 ${statusDisplay.color} ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                          {statusDisplay.icon}
                          <span>{statusDisplay.label}</span>
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div>
                      <h4 className="text-sm font-medium text-gray-300 mb-1">{t('application.mentor', 'Mentor')}</h4>
                      <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                        <div className="w-8 h-8 rounded-full bg-blue-700 flex items-center justify-center text-white text-xs font-bold">
                          {application.mentor_profile.user.first_name.charAt(0)}{application.mentor_profile.user.last_name.charAt(0)}
                        </div>
                        <div>
                          <p className="text-white text-sm font-medium">
                            {application.mentor_profile.user.first_name} {application.mentor_profile.user.last_name}
                          </p>
                          <p className="text-gray-400 text-xs">{application.mentor_profile.position}</p>
                        </div>
                      </div>
                    </div>

                    {application.message && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-300 mb-1">{t('application.message', 'Message')}</h4>
                        <p className="text-gray-300 text-sm line-clamp-3">{application.message}</p>
                      </div>
                    )}

                    <div className="grid grid-cols-2 gap-4 text-xs text-gray-400">
                      <div>
                        <span className="block">{t('application.applied', 'Applied')}</span>
                        <span className="text-white">{new Date(application.created_at).toLocaleDateString()}</span>
                      </div>
                      {application.reviewed_at && (
                        <div>
                          <span className="block">{t('application.reviewed', 'Reviewed')}</span>
                          <span className="text-white">{new Date(application.reviewed_at).toLocaleDateString()}</span>
                        </div>
                      )}
                    </div>

                    {application.goals && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-300 mb-1">{t('application.goals', 'Goals')}</h4>
                        <p className="text-gray-300 text-sm line-clamp-2">{application.goals}</p>
                      </div>
                    )}

                    {application.expectations && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-300 mb-1">{t('application.expectations', 'Expectations')}</h4>
                        <p className="text-gray-300 text-sm line-clamp-2">{application.expectations}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        <div className="text-center py-12">
          <User size={48} className="mx-auto text-gray-600 mb-4" />
          <h3 className="text-lg font-medium text-gray-400 mb-2">
            {searchTerm 
              ? t('admin.no.applications.found', 'No mentorship applications found')
              : t('admin.no.applications', 'No mentorship applications yet')
            }
          </h3>
          <p className="text-gray-500 mb-4">
            {searchTerm
              ? t('admin.try.different.search', 'Try adjusting your search')
              : t('admin.no.applications.description', 'Mentorship applications will appear here when submitted')
            }
          </p>
        </div>
      )}
    </DashboardLayout>
  );
};

export default MentorshipApplicationsManagement;
