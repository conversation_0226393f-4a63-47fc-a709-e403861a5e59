/**
 * Real-Time Analytics Dashboard
 * Advanced analytics with live data updates and AI insights
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  BarChart3,
  TrendingUp,
  Users,
  Eye,
  Clock,
  Target,
  Brain,
  Zap,
  Activity,
  Globe,
  Smartphone,
  Monitor
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { RTLText, RTLFlex } from '../common';

interface AnalyticsData {
  activeUsers: number;
  pageViews: number;
  sessionDuration: number;
  bounceRate: number;
  conversionRate: number;
  topPages: Array<{ page: string; views: number; change: number }>;
  userFlow: Array<{ step: string; users: number; dropoff: number }>;
  deviceBreakdown: { desktop: number; mobile: number; tablet: number };
  geographicData: Array<{ country: string; users: number; flag: string }>;
  realTimeEvents: Array<{ timestamp: Date; event: string; user: string; page: string }>;
}

interface AIInsight {
  id: string;
  type: 'opportunity' | 'warning' | 'trend' | 'recommendation';
  title: string;
  description: string;
  confidence: number;
  impact: 'high' | 'medium' | 'low';
  actionable: boolean;
}

export const RealTimeAnalytics: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [analyticsData, setAnalyticsData] = useState<AnalyticsData>({
    activeUsers: 0,
    pageViews: 0,
    sessionDuration: 0,
    bounceRate: 0,
    conversionRate: 0,
    topPages: [],
    userFlow: [],
    deviceBreakdown: { desktop: 0, mobile: 0, tablet: 0 },
    geographicData: [],
    realTimeEvents: []
  });

  const [aiInsights, setAiInsights] = useState<AIInsight[]>([]);
  const [isLive, setIsLive] = useState(false);
  const [timeRange, setTimeRange] = useState<'1h' | '24h' | '7d' | '30d'>('24h');

  // Fetch real-time analytics data from API
  const fetchRealTimeData = useCallback(async (): Promise<AnalyticsData> => {
    try {
      // Fetch real analytics data from API
      const { analyticsAPI } = await import('../../services/analyticsApi');
      const realTimeData = await analyticsAPI.getRealTimeAnalytics();

      return realTimeData;
    } catch (error) {
      console.error('Error fetching real-time analytics:', error);
      // Return empty data structure on error
      return {
        activeUsers: 0,
        pageViews: 0,
        sessionDuration: 0,
        bounceRate: 0,
        conversionRate: 0,
        topPages: [],
        userFlow: [],
        deviceBreakdown: { desktop: 0, mobile: 0, tablet: 0 },
        geographicData: [],
        realTimeEvents: []
      };
    }
  }, []);



  // Generate AI insights based on data
  const generateAIInsights = useCallback((data: AnalyticsData): AIInsight[] => {
    const insights: AIInsight[] = [];

    // High bounce rate insight
    if (data.bounceRate > 50) {
      insights.push({
        id: 'high-bounce',
        type: 'warning',
        title: t("common.high.bounce.rate", "High Bounce Rate Detected"),
        description: `Bounce rate is ${data.bounceRate}%. Consider improving landing page content and loading speed.`,
        confidence: 85,
        impact: 'high',
        actionable: true
      });
    }

    // Mobile usage trend
    if (data.deviceBreakdown.mobile > 50) {
      insights.push({
        id: 'mobile-trend',
        type: 'trend',
        title: t("common.mobilefirst.user.behavior", "Mobile-First User Behavior"),
        description: `${data.deviceBreakdown.mobile}% of users are on mobile. Prioritize mobile optimization.`,
        confidence: 92,
        impact: 'high',
        actionable: true
      });
    }

    // Conversion opportunity
    if (data.conversionRate < 5) {
      insights.push({
        id: 'conversion-opportunity',
        type: 'opportunity',
        title: t("common.conversion.rate.optimization", "Conversion Rate Optimization"),
        description: `Current conversion rate is ${data.conversionRate}%. A/B test call-to-action buttons and forms.`,
        confidence: 78,
        impact: 'medium',
        actionable: true
      });
    }

    // Geographic expansion
    const topCountry = data.geographicData[0];
    if (topCountry && topCountry.users > 200) {
      insights.push({
        id: 'geo-expansion',
        type: 'recommendation',
        title: t("common.geographic.expansion.opportunity", "Geographic Expansion Opportunity"),
        description: `Strong user base in ${topCountry.country} (${topCountry.users} users). Consider localized marketing.`,
        confidence: 88,
        impact: 'medium',
        actionable: true
      });
    }

    return insights;
  }, []);

  // Update data periodically when live
  useEffect(() => {
    const updateData = async () => {
      const newData = await fetchRealTimeData();
      setAnalyticsData(newData);
      setAiInsights(generateAIInsights(newData));
    };

    updateData(); // Initial load

    if (isLive) {
      const interval = setInterval(updateData, 5000); // Update every 5 seconds
      return () => clearInterval(interval);
    }
  }, [isLive, fetchRealTimeData, generateAIInsights]);

  const getInsightIcon = (type: AIInsight['type']) => {
    switch (type) {
      case 'opportunity': return <Target className="text-green-400" size={20} />;
      case 'warning': return <Zap className="text-yellow-400" size={20} />;
      case 'trend': return <TrendingUp className="text-blue-400" size={20} />;
      case 'recommendation': return <Brain className="text-purple-400" size={20} />;
    }
  };

  const formatTime = (timestamp: Date) => {
    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <RTLFlex className="items-center justify-between">
        <RTLFlex className="items-center">
          <BarChart3 className="text-purple-400" size={28} />
          <RTLText className={`text-2xl font-bold text-white ${isRTL ? 'mr-3' : 'ml-3'}`}>
            Real-Time Analytics
          </RTLText>
        </RTLFlex>

        <RTLFlex className="items-center gap-4">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as any)}
            className="px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-purple-500"
          >
            <option value="1h">t("common.last.hour", "Last Hour")</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
          </select>

          <button
            onClick={() => setIsLive(!isLive)}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              isLive
                ? 'bg-red-600 hover:bg-red-700 text-white'
                : 'bg-green-600 hover:bg-green-700 text-white'}
            }`}
          >
            {isLive ? '🔴 Live' : '▶️ Start Live'}
          </button>
        </RTLFlex>
      </RTLFlex>

      {/* Key Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-4">
          <RTLFlex className="items-center mb-2">
            <Users className="text-green-400" size={20} />
            <RTLText className={`font-medium text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
              Active Users
            </RTLText>
          </RTLFlex>
          <div className="text-2xl font-bold text-white">
            {analyticsData.activeUsers.toLocaleString()}
          </div>
          {isLive && <div className="text-xs text-green-400 animate-pulse">● Live</div>}
        </div>

        <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-4">
          <RTLFlex className="items-center mb-2">
            <Eye className="text-blue-400" size={20} />
            <RTLText className={`font-medium text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
              Page Views
            </RTLText>
          </RTLFlex>
          <div className="text-2xl font-bold text-white">
            {analyticsData.pageViews.toLocaleString()}
          </div>
        </div>

        <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-4">
          <RTLFlex className="items-center mb-2">
            <Clock className="text-yellow-400" size={20} />
            <RTLText className={`font-medium text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
              Avg. Session
            </RTLText>
          </RTLFlex>
          <div className="text-2xl font-bold text-white">
            {Math.floor(analyticsData.sessionDuration / 60)}m {analyticsData.sessionDuration % 60}s
          </div>
        </div>

        <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-4">
          <RTLFlex className="items-center mb-2">
            <Activity className="text-red-400" size={20} />
            <RTLText className={`font-medium text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
              Bounce Rate
            </RTLText>
          </RTLFlex>
          <div className="text-2xl font-bold text-white">
            {analyticsData.bounceRate}%
          </div>
        </div>

        <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-4">
          <RTLFlex className="items-center mb-2">
            <Target className="text-purple-400" size={20} />
            <RTLText className={`font-medium text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
              Conversion
            </RTLText>
          </RTLFlex>
          <div className="text-2xl font-bold text-white">
            {analyticsData.conversionRate}%
          </div>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Top Pages */}
        <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6">
          <h3 className="text-lg font-semibold text-white mb-4">t("common.top.pages", "Top Pages")</h3>
          <div className="space-y-3">
            {analyticsData.topPages.map((page, index) => (
              <div key={index} className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
                <div>
                  <div className="text-white font-medium">{page.page}</div>
                  <div className="text-sm text-gray-400">{page.views.toLocaleString()} views</div>
                </div>
                <div className={`text-sm font-medium ${
                  page.change > 0 ? 'text-green-400' : page.change < 0 ? 'text-red-400' : 'text-gray-400'}
                }`}>
                  {page.change > 0 ? '+' : ''}{page.change}%
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Device Breakdown */}
        <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6">
          <h3 className="text-lg font-semibold text-white mb-4">t("common.device.breakdown", "Device Breakdown")</h3>
          <div className="space-y-4">
            <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
              <RTLFlex className="items-center">
                <Monitor className="text-blue-400" size={20} />
                <span className={`text-white ${isRTL ? 'mr-2' : 'ml-2'}`}>t("common.desktop", "Desktop")</span>
              </RTLFlex>
              <span className="text-white font-medium">{analyticsData.deviceBreakdown.desktop}%</span>
            </div>
            <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
              <RTLFlex className="items-center">
                <Smartphone className="text-green-400" size={20} />
                <span className={`text-white ${isRTL ? 'mr-2' : 'ml-2'}`}>t("common.mobile", "Mobile")</span>
              </RTLFlex>
              <span className="text-white font-medium">{analyticsData.deviceBreakdown.mobile}%</span>
            </div>
            <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
              <RTLFlex className="items-center">
                <Monitor className="text-purple-400" size={20} />
                <span className={`text-white ${isRTL ? 'mr-2' : 'ml-2'}`}>t("common.tablet", "Tablet")</span>
              </RTLFlex>
              <span className="text-white font-medium">{analyticsData.deviceBreakdown.tablet}%</span>
            </div>
          </div>
        </div>

        {/* Geographic Data */}
        <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6">
          <h3 className="text-lg font-semibold text-white mb-4">t("common.top.countries", "Top Countries")</h3>
          <div className="space-y-3">
            {analyticsData.geographicData.map((country, index) => (
              <div key={index} className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
                <RTLFlex className="items-center">
                  <span className="text-xl">{country.flag}</span>
                  <span className={`text-white ${isRTL ? 'mr-2' : 'ml-2'}`}>{country.country}</span>
                </RTLFlex>
                <span className="text-white font-medium">{country.users}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* AI Insights */}
      {aiInsights.length > 0 && (
        <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6">
          <RTLFlex className="items-center mb-4">
            <Brain className="text-purple-400" size={24} />
            <RTLText className={`text-lg font-semibold text-white ${isRTL ? 'mr-3' : 'ml-3'}`}>
              AI Insights
            </RTLText>
          </RTLFlex>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {aiInsights.map((insight) => (
              <div key={insight.id} className="bg-gray-800/50 rounded-lg p-4">
                <RTLFlex className="items-start mb-2">
                  {getInsightIcon(insight.type)}
                  <div className={isRTL ? 'mr-3' : 'ml-3'}>
                    <h4 className="font-medium text-white">{insight.title}</h4>
                    <p className="text-sm text-gray-400 mt-1">{insight.description}</p>
                  </div>
                </RTLFlex>

                <RTLFlex className="items-center justify-between mt-3">
                  <span className="text-xs text-gray-500">
                    {insight.confidence}% confidence
                  </span>
                  <span className={`text-xs px-2 py-1 rounded ${
                    insight.impact === 'high' ? 'bg-red-900/50 text-red-300' :
                    insight.impact === 'medium' ? 'bg-yellow-900/50 text-yellow-300' :
                    'bg-blue-900/50 text-blue-300'}
                  }`}>
                    {insight.impact} impact
                  </span>
                </RTLFlex>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Real-Time Events */}
      <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6">
        <RTLFlex className="items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white">t("common.realtime.activity", "Real-Time Activity")</h3>
          {isLive && <div className="text-green-400 text-sm animate-pulse">● Live Updates</div>}
        </RTLFlex>

        <div className="space-y-2 max-h-64 overflow-y-auto">
          {analyticsData.realTimeEvents.map((event, index) => (
            <div key={index} className={`flex items-center justify-between py-2 border-b border-gray-700/30 ${isRTL ? "flex-row-reverse" : ""}`}>
              <div>
                <span className="text-white">{event.user}</span>
                <span className="text-gray-400 mx-2">•</span>
                <span className="text-gray-300">{event.event}</span>
                <span className="text-gray-500 text-sm block">{event.page}</span>
              </div>
              <span className="text-gray-500 text-sm">
                {formatTime(event.timestamp)}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default RealTimeAnalytics;
