import React, { useState, useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import {
  ArrowLeft,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { useAppDispatch, useAppSelector } from '../../store/hooks';
import AuthenticatedLayout from '../../components/layout/AuthenticatedLayout';
import {
  fetchMentorshipSession,
  updateMentorshipSession
} from '../../store/incubatorSlice';
import { VideoConferenceRoom } from '../../components/incubator';
import { useTranslation } from 'react-i18next';

const MentorshipSessionJoinPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const { sessionId } = useParams<{ sessionId: string }>();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { selectedMentorshipSession, isLoading, error } = useAppSelector(state => state.incubator);

  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  useEffect(() => {
    if (sessionId) {
      dispatch(fetchMentorshipSession(parseInt(sessionId)));
    }
  }, [dispatch, sessionId]);

  const handleSessionExit = () => {
    navigate(`/dashboard/mentorship/sessions/${sessionId}`);
  };

  const handleSessionEnd = async () => {
    if (!sessionId) return;

    try {
      await dispatch(updateMentorshipSession({
        id: parseInt(sessionId),
        action: 'complete_session'
      })).unwrap();

      setSuccessMessage(t("dashboard.session.completed.successfully", "Session completed successfully!"));
      setTimeout(() => {
        setSuccessMessage(null);
        navigate(`/dashboard/mentorship/sessions/${sessionId}/feedback`);
      }, 1500);
    } catch (err) {
      setErrorMessage(t("dashboard.failed.to.complete", "Failed to complete session. Please try again."));
      setTimeout(() => setErrorMessage(null), 3000);
    }
  };

  return (
    <AuthenticatedLayout>
      <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="p-6">
        {/* Back button */}
        <div className="mb-6">
          <Link
            to={`/dashboard/mentorship/sessions/${sessionId}`}
            className={`text-gray-400 hover:text-gray-300 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <ArrowLeft size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
            Back to Session Details
          </Link>
        </div>

        {isLoading && !selectedMentorshipSession && (
          <div className={`flex justify-center py-12 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
          </div>
        )}

        {error && (
          <div className={`bg-red-900/30 text-red-200 p-4 rounded-lg mb-6 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <AlertCircle size={20} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
            <span>Error loading session: {error}</span>
          </div>
        )}

        {successMessage && (
          <div className={`bg-green-900/30 text-green-200 p-4 rounded-lg mb-6 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <CheckCircle size={20} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
            <span>{successMessage}</span>
          </div>
        )}

        {errorMessage && (
          <div className={`bg-red-900/30 text-red-200 p-4 rounded-lg mb-6 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <AlertCircle size={20} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
            <span>{errorMessage}</span>
          </div>
        )}

        {selectedMentorshipSession && (
          <>
            <h1 className="text-2xl font-bold mb-4">{selectedMentorshipSession.title}</h1>

            {selectedMentorshipSession.session_type === 'video' ? (
              <VideoConferenceRoom
                sessionId={parseInt(sessionId!)}
                onExit={handleSessionExit}
              />
            ) : (
              <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50 text-center">
                <AlertCircle size={40} className="mx-auto mb-3 text-yellow-500" />
                <h3 className="text-lg font-medium mb-2">t("dashboard.not.a.video", "Not a Video Session")</h3>
                <p className="text-gray-400 mb-4">
                  This session is not configured as a video conference. Please check the session details.
                </p>
                <Link
                  to={`/dashboard/mentorship/sessions/${sessionId}`}
                  className={`px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white inline-flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                >
                  View Session Details
                </Link>
              </div>
            )}
          </>
        )}
      </div>
              </div>
        </div>
      </div>
    </AuthenticatedLayout>
  );
};

export default MentorshipSessionJoinPage;
