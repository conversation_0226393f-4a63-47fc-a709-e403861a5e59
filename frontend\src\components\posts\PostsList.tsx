import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { usePostsList, usePostsInfinite } from '../../hooks/usePosts';
import { Post } from '../../services/api';
import { QueryParams } from '../../types/api';
import { Loader, Alert, Button } from '../ui';
interface PostsListProps {
  usePagination?: boolean;
  initialParams?: QueryParams;
}

const PostsList: React.FC<PostsListProps> = ({ 
  usePagination = false,
  initialParams = {}
}) => {
  const [params, setParams] = useState<QueryParams>(initialParams);
  
  // Use either standard pagination or infinite scrolling based on prop
  const {
    data: paginatedData,
    isLoading: isPaginatedLoading,
    error: paginatedError,
    refetch: refetchPaginated
  } = usePostsList(usePagination ? params : undefined);
  
  const {
    data: infiniteData,
    isLoading: isInfiniteLoading,
    error: infiniteError,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch: refetchInfinite
  } = usePostsInfinite(!usePagination ? params : undefined);
  
  // Determine which data to use based on pagination mode
  const isLoading = usePagination ? isPaginatedLoading : isInfiniteLoading;
  const error = usePagination ? paginatedError : infiniteError;
  const refetch = usePagination ? refetchPaginated : refetchInfinite;
  
  // Extract posts from either paginated or infinite data
  const posts = usePagination 
    ? (paginatedData?.results || [])
    : (infiniteData?.pages.flatMap(page => page.results) || []);
  
  // Handle search
  const [searchTerm, setSearchTerm] = useState('');
  
  const handleSearch = () => {
    setParams({
      ...params,
      search: searchTerm
    });
  };
  
  // Handle load more for infinite scrolling
  const handleLoadMore = () => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  };
  
  if (isLoading && posts.length === 0) {
    return <Loader />;
  }
  
  if (error) {
    return (
      <Alert variant="error">
        {t('common.error')}: {error.message}
        <Button onClick={() => refetch()} className="mt-2">
          {t('common.retry')}
        </Button>
      </Alert>
    );
  }
  
  return (
    <div className="space-y-4">
      {/* Search bar */}
      <div className={`flex gap-2 ${isRTL ? "flex-row-reverse" : ""}`}>
        <input
          type="text"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          placeholder={t('posts.searchPlaceholder')}
          className={`flex-1 px-4 py-2 rounded-lg bg-indigo-900/30 border border-indigo-800/50 ${isRTL ? "flex-row-reverse" : ""}`}
        />
        <Button onClick={handleSearch}>
          {t('common.search')}
        </Button>
      </div>
      
      {/* Posts list */}
      <div className="space-y-4">
        {posts.length === 0 ? (
          <div className="text-center py-8">{t('posts.noPosts')}</div>
        ) : (
          posts.map((post: Post) => (
            <PostItem key={post.id} post={post} />
          )}
      </div>
      
      {/* Load more button for infinite scrolling */}
      {!usePagination && hasNextPage && (
        <div className={`flex justify-center mt-4 ${isRTL ? "flex-row-reverse" : ""}`}>
          <Button 
            onClick={handleLoadMore} 
            disabled={isFetchingNextPage}
            variant="secondary"
          >
            {isFetchingNextPage ? t('common.loading') : t('common.loadMore')}
          </Button>
        </div>
      )}
      
      {/* Pagination controls */}
      {usePagination && paginatedData && (
        <Pagination
          currentPage={params.page || 1}
          totalPages={Math.ceil(paginatedData.count / (params.page_size || 10))}
          onPageChange={(page) => setParams({ ...params, page })}
        />
      )}
    </div>
  );
};

// Simple Post Item component
const PostItem: React.FC<{ post: Post }> = ({ post }) => {
  return (
    <div className="p-4 rounded-lg bg-indigo-900/30 border border-indigo-800/50">
      <h3 className="text-xl font-semibold">{post.title}</h3>
      <div className="text-sm text-indigo-300 mt-1">
        {post.author.username} • {new Date(post.created_at).toLocaleDateString()}
      </div>
      <p className="mt-2">{post.content}</div>
      <div className={`flex items-center gap-2 mt-3 text-sm text-indigo-300 ${isRTL ? "flex-row-reverse" : ""}`}>
        <span>{post.like_count} likes</span>
        <span>•</span>
        <span>{post.comments.length} comments</span>
      </div>
    </div>
  );
};

// Simple Pagination component
const Pagination: React.FC<{
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}> = ({ currentPage, totalPages, onPageChange }) => {
  const { t } = useTranslation();
  
  return (
    <div className={`flex justify-center gap-2 mt-4 ${isRTL ? "flex-row-reverse" : ""}`}>
      <Button
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage <= 1}
        variant="secondary"
        size="sm"
      >
        {t('common.previous')}
      </Button>
      
      <span className="px-4 py-2">
        {t('common.pageOf', { current: currentPage, total: totalPages })}
      </span>
      
      <Button
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage >= totalPages}
        variant="secondary"
        size="sm"
      >
        {t('common.next')}
      </Button>
    </div>
  );
};

export default PostsList;
