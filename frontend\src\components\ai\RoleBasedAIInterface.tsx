/**
 * Role-Based AI Interface Component
 * Displays AI features and capabilities based on user's role
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../../store/hooks';
import { useLanguage } from '../../hooks/useLanguage';
import { 
  MessageSquare, 
  Users, 
  TrendingUp, 
  BarChart3, 
  Shield, 
  Settings,
  Lightbulb,
  BookOpen,
  Globe,
  PieChart,
  Crown,
  Lock,
  Zap
} from 'lucide-react';
import { RTLText } from '../rtl';
import { getUserAICapabilities, getRoleBasedAIConfig, AICapability } from '../../utils/roleBasedAI';

const iconMap = {
  MessageSquare,
  Users,
  TrendingUp,
  BarChart3,
  Shield,
  Settings,
  Lightbulb,
  BookOpen,
  Globe,
  PieChart,
  Crown,
  Lock,
  Zap
};

interface RoleBasedAIInterfaceProps {
  onCapabilitySelect?: (capability: AICapability) => void;
  showHeader?: boolean;
  compact?: boolean;
}

const RoleBasedAIInterface: React.FC<RoleBasedAIInterfaceProps> = ({
  onCapabilitySelect,
  showHeader = true,
  compact = false
}) => {
  const { t } = useTranslation();
  const { user } = useAppSelector(state => state.auth);
  const { isRTL } = useLanguage();
  const [selectedCapability, setSelectedCapability] = useState<string | null>(null);

  const aiConfig = getRoleBasedAIConfig(user);
  const capabilities = getUserAICapabilities(user);

  const handleCapabilityClick = (capability: AICapability) => {
    setSelectedCapability(capability.id);
    onCapabilitySelect?.(capability);
  };

  const getRoleDisplayName = (role: string) => {
    const roleNames = {
      admin: t('roles.admin', 'Administrator'),
      moderator: t('roles.moderator', 'Moderator'),
      mentor: t('roles.mentor', 'Mentor'),
      investor: t('roles.investor', 'Investor'),
      user: t('roles.user', 'User')
    };
    return roleNames[role as keyof typeof roleNames] || role;
  };

  const getRoleColor = (role: string) => {
    const colors = {
      admin: 'from-red-500 to-pink-500',
      moderator: 'from-purple-500 to-indigo-500',
      mentor: 'from-blue-500 to-cyan-500',
      investor: 'from-green-500 to-emerald-500',
      user: 'from-gray-500 to-slate-500'
    };
    return colors[role as keyof typeof colors] || colors.user;
  };

  if (!user) {
    return (
      <div className="text-center py-8">
        <Lock className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <RTLText as="p" className="text-gray-400">
          {t('ai.loginRequired', 'Please log in to access AI features')}
        </RTLText>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {showHeader && (
        <div className="text-center">
          {/* Role Badge */}
          <div className="flex items-center justify-center mb-4">
            <div className={`px-6 py-3 rounded-full bg-gradient-to-r ${getRoleColor(aiConfig.primaryRole)} text-white shadow-lg`}>
              <div className="flex items-center space-x-2">
                <Crown className="w-5 h-5" />
                <RTLText as="span" className="font-semibold">
                  {getRoleDisplayName(aiConfig.primaryRole)}
                </RTLText>
              </div>
            </div>
          </div>

          {/* AI Access Summary */}
          <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-6 mb-6">
            <RTLText as="h3" className="text-xl font-bold text-white mb-4">
              {t('ai.yourAIAccess', 'Your AI Access Level')}
            </RTLText>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-blue-500/20 rounded-lg p-4">
                <MessageSquare className="w-8 h-8 text-blue-400 mx-auto mb-2" />
                <RTLText as="div" className="text-sm text-blue-300">
                  {t('ai.chatLimit', 'Chat Messages')}
                </RTLText>
                <RTLText as="div" className="text-xl font-bold text-white">
                  {aiConfig.rateLimits.chat}/hr
                </RTLText>
              </div>
              
              <div className="bg-green-500/20 rounded-lg p-4">
                <BarChart3 className="w-8 h-8 text-green-400 mx-auto mb-2" />
                <RTLText as="div" className="text-sm text-green-300">
                  {t('ai.analysisLimit', 'Analysis')}
                </RTLText>
                <RTLText as="div" className="text-xl font-bold text-white">
                  {aiConfig.rateLimits.analysis}/day
                </RTLText>
              </div>
              
              <div className="bg-purple-500/20 rounded-lg p-4">
                <Zap className="w-8 h-8 text-purple-400 mx-auto mb-2" />
                <RTLText as="div" className="text-sm text-purple-300">
                  {t('ai.generationLimit', 'Generation')}
                </RTLText>
                <RTLText as="div" className="text-xl font-bold text-white">
                  {aiConfig.rateLimits.generation}/day
                </RTLText>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* AI Capabilities Grid */}
      <div className="space-y-4">
        <RTLText as="h4" className="text-lg font-semibold text-white mb-4">
          {t('ai.availableCapabilities', 'Available AI Capabilities')}
        </RTLText>
        
        <div className={`grid gap-4 ${compact ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'}`}>
          {capabilities.map((capability) => {
            const IconComponent = iconMap[capability.icon as keyof typeof iconMap] || MessageSquare;
            const isSelected = selectedCapability === capability.id;
            
            return (
              <button
                key={capability.id}
                onClick={() => handleCapabilityClick(capability)}
                className={`p-6 rounded-xl border transition-all duration-300 text-left group hover:scale-105 ${
                  isSelected
                    ? 'bg-gradient-to-br from-blue-500/30 to-purple-500/30 border-blue-500/50 shadow-lg shadow-blue-500/25'
                    : 'bg-white/10 backdrop-blur-sm border-white/20 hover:bg-white/20 hover:border-white/30'
                }`}
              >
                <div className="flex items-start space-x-4">
                  <div className={`p-3 rounded-lg ${
                    isSelected ? 'bg-blue-500/30' : 'bg-white/20'
                  } group-hover:bg-blue-500/30 transition-colors`}>
                    <IconComponent className={`w-6 h-6 ${
                      isSelected ? 'text-blue-300' : 'text-white'
                    } group-hover:text-blue-300`} />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <RTLText as="h5" className="font-semibold text-white mb-2 truncate">
                      {capability.name}
                    </RTLText>
                    <RTLText as="p" className="text-sm text-gray-300 mb-3 line-clamp-2">
                      {capability.description}
                    </RTLText>
                    
                    {/* Features List */}
                    <div className="space-y-1">
                      {capability.features.slice(0, compact ? 2 : 3).map((feature, index) => (
                        <div key={index} className="flex items-center text-xs text-gray-400">
                          <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mr-2 flex-shrink-0"></div>
                          <span className="truncate">{feature}</span>
                        </div>
                      ))}
                      {capability.features.length > (compact ? 2 : 3) && (
                        <div className="text-xs text-blue-300">
                          +{capability.features.length - (compact ? 2 : 3)} more features
                        </div>
                      )}
                    </div>
                    
                    {/* Rate Limit Info */}
                    {capability.rateLimit && (
                      <div className="mt-3 text-xs text-gray-400">
                        <span className="bg-gray-700/50 px-2 py-1 rounded">
                          {capability.rateLimit.requests}/{capability.rateLimit.period}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </button>
            );
          })}
        </div>
      </div>

      {/* Role Upgrade Suggestion */}
      {aiConfig.primaryRole === 'user' && (
        <div className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30 rounded-xl p-6">
          <div className="flex items-center mb-4">
            <Crown className="w-6 h-6 text-purple-400 mr-3" />
            <RTLText as="h5" className="font-semibold text-white">
              {t('ai.upgradeAccess', 'Upgrade Your AI Access')}
            </RTLText>
          </div>
          <RTLText as="p" className="text-gray-300 mb-4">
            {t('ai.upgradeDescription', 'Apply for mentor or investor roles to unlock advanced AI capabilities and higher usage limits.')}
          </RTLText>
          <button className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors">
            {t('ai.applyForRole', 'Apply for Advanced Role')}
          </button>
        </div>
      )}
    </div>
  );
};

export default RoleBasedAIInterface;
