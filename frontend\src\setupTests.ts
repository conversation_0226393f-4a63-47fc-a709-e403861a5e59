// jest-dom adds custom jest matchers for asserting on DOM nodes.
// allows you to do things like:
// expect(element).toHaveTextContent(/react/i)
// learn more: https://github.com/testing-library/jest-dom
import '@testing-library/jest-dom';
import { configure } from '@testing-library/react';
import React from 'react';
// import { server } from './mocks/server'; // Commented out for now

// Configure testing library
configure({
  testIdAttribute: 'data-testid',
});

// Mock IntersectionObserver
(global as any).IntersectionObserver = class IntersectionObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
};

// Mock ResizeObserver
(global as any).ResizeObserver = class ResizeObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
};

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock scrollTo
Object.defineProperty(window, 'scrollTo', {
  writable: true,
  value: jest.fn(),
});

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
  length: 0,
  key: jest.fn(),
};
(global as any).localStorage = localStorageMock;

// Mock sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
  length: 0,
  key: jest.fn(),
};
(global as any).sessionStorage = sessionStorageMock;

// Mock fetch
(global as any).fetch = jest.fn();

// Mock console methods to reduce noise in tests
const originalError = console.error;
const originalWarn = console.warn;

beforeAll(() => {
  // Start the MSW server
  // server.listen(); // Commented out for now
  
  // Mock console.error to ignore React warnings in tests
  console.error = (...args) => {
    if (
      typeof args[0] === 'string' &&
      (args[0].includes('Warning:') ||
       args[0].includes('ReactDOM.render is no longer supported'))
    ) {
      return;
    }
    originalError.call(console, ...args);
  };

  // Mock console.warn to ignore React warnings in tests
  console.warn = (...args) => {
    if (
      typeof args[0] === 'string' &&
      args[0].includes('Warning:')
    ) {
      return;
    }
    originalWarn.call(console, ...args);
  };
});

afterEach(() => {
  // Reset any request handlers that we may add during the tests,
  // so they don't affect other tests.
  // server.resetHandlers(); // Commented out for now
  
  // Clear all mocks
  jest.clearAllMocks();
  
  // Clear localStorage and sessionStorage
  localStorageMock.clear();
  sessionStorageMock.clear();
});

afterAll(() => {
  // Clean up after the tests are finished.
  // server.close(); // Commented out for now
  
  // Restore console methods
  console.error = originalError;
  console.warn = originalWarn;
});

// All mock data functions removed - application now uses real API data only
// Test utilities focus on real API integration and data validation

// Test wrapper utilities
export const createQueryClientWrapper = () => {
  // Dynamic imports for test environment
  const QueryClient = (globalThis as any).QueryClient;
  const QueryClientProvider = (globalThis as any).QueryClientProvider;
  const React = (globalThis as any).React;

  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        cacheTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  });

  return ({ children }: { children: React.ReactNode }) =>
    React.createElement(QueryClientProvider, { client: queryClient }, children);
};

export const createRouterWrapper = () => {
  const BrowserRouter = (globalThis as any).BrowserRouter;
  const React = (globalThis as any).React;

  return ({ children }: { children: React.ReactNode }) =>
    React.createElement(BrowserRouter, {}, children);
};

export const createI18nWrapper = () => {
  const I18nextProvider = (globalThis as any).I18nextProvider;
  const React = (globalThis as any).React;
  const i18n = (globalThis as any).i18n;

  return ({ children }: { children: React.ReactNode }) =>
    React.createElement(I18nextProvider, { i18n }, children);
};

export const createFullWrapper = () => {
  const QueryWrapper = createQueryClientWrapper();
  const RouterWrapper = createRouterWrapper();
  const I18nWrapper = createI18nWrapper();

  return ({ children }: { children: React.ReactNode }) =>
    React.createElement(
      QueryWrapper,
      {},
      React.createElement(
        RouterWrapper,
        {},
        React.createElement(I18nWrapper, {}, children)
      )
    );
};
