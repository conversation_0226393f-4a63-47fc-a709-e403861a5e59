import { useState, useEffect } from 'react';
import { userAPI, ContentRecommendations } from '../../../../services/api';

interface UseContentRecommendationsResult {
  recommendations: ContentRecommendations | null;
  loading: boolean;
  error: string | null;
  hasRecommendations: boolean;
}

export const useContentRecommendations = (): UseContentRecommendationsResult => {
  const [recommendations, setRecommendations] = useState<ContentRecommendations | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchRecommendations = async () => {
      setLoading(true);
      try {
        const data = await userAPI.getContentRecommendations();
        setRecommendations(data);
        setError(null);
      } catch (err: any) {
        // Only log errors in development
        if (import.meta.env.DEV) {
          if (import.meta.env.DEV) console.error('Error fetching content recommendations:', err);
        }

        // Don't show error for 500 errors - just fail silently for now
        // This is likely a backend endpoint that's not implemented yet
        if (err?.status === 500) {
          if (import.meta.env.DEV) {
            if (import.meta.env.DEV) console.warn('Content recommendations endpoint not available (500 error)');
          }
          setRecommendations({
            recommended_posts: [],
            recommended_events: [],
            recommended_resources: []
          });
          setError(null);
        } else {
          setError('Failed to load recommendations. Please try again later.');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchRecommendations();
  }, []);

  // Check if there are any recommendations
  const hasRecommendations = !!(
    recommendations &&
    (recommendations.recommended_posts.length > 0 ||
     recommendations.recommended_events.length > 0 ||
     recommendations.recommended_resources.length > 0)
  );

  return {
    recommendations,
    loading,
    error,
    hasRecommendations
  };
};

export default useContentRecommendations;
