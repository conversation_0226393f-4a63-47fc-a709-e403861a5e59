/* Business Ideas Page - Glass Morphism Dark Theme */

.business-ideas-page {
  background: linear-gradient(135deg, #1e1b4b 0%, #581c87 50%, #7c3aed 100%) !important;
  min-height: 100vh;
}

.business-ideas-header {
  background: rgba(255, 255, 255, 0.05) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37) !important;
}

.business-ideas-stats-card {
  background: rgba(255, 255, 255, 0.05) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37) !important;
  transition: all 0.3s ease !important;
}

.business-ideas-stats-card:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 12px 40px rgba(31, 38, 135, 0.5) !important;
}

.business-ideas-table-container {
  background: rgba(255, 255, 255, 0.05) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37) !important;
}

/* Force dark theme on table */
.business-ideas-table-container .bg-white {
  background: transparent !important;
}

.business-ideas-table-container .bg-gray-50 {
  background: rgba(255, 255, 255, 0.05) !important;
}

.business-ideas-table-container .text-gray-900 {
  color: white !important;
}

.business-ideas-table-container .text-gray-500 {
  color: rgba(255, 255, 255, 0.6) !important;
}

.business-ideas-table-container .text-gray-600 {
  color: rgba(255, 255, 255, 0.7) !important;
}

.business-ideas-table-container .text-gray-700 {
  color: rgba(255, 255, 255, 0.8) !important;
}

.business-ideas-table-container .border-gray-200 {
  border-color: rgba(255, 255, 255, 0.2) !important;
}

.business-ideas-table-container .border-gray-300 {
  border-color: rgba(255, 255, 255, 0.2) !important;
}

.business-ideas-table-container .divide-gray-200 > * {
  border-color: rgba(255, 255, 255, 0.2) !important;
}

.business-ideas-table-container .hover\\:bg-gray-50:hover {
  background: rgba(255, 255, 255, 0.05) !important;
}

.business-ideas-table-container .hover\\:bg-gray-100:hover {
  background: rgba(255, 255, 255, 0.1) !important;
}

/* Search input styling */
.business-ideas-table-container input[type="text"] {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: white !important;
}

.business-ideas-table-container input[type="text"]::placeholder {
  color: rgba(255, 255, 255, 0.4) !important;
}

.business-ideas-table-container input[type="text"]:focus {
  background: rgba(255, 255, 255, 0.2) !important;
  border-color: rgba(147, 51, 234, 0.5) !important;
  box-shadow: 0 0 0 3px rgba(147, 51, 234, 0.1) !important;
}

/* Button styling */
.business-ideas-table-container button {
  transition: all 0.3s ease !important;
}

.business-ideas-table-container .bg-blue-600 {
  background: linear-gradient(135deg, #7c3aed, #3b82f6) !important;
}

.business-ideas-table-container .bg-blue-600:hover {
  background: linear-gradient(135deg, #8b5cf6, #60a5fa) !important;
  transform: scale(1.05) !important;
  box-shadow: 0 0 20px rgba(147, 51, 234, 0.5) !important;
}

/* Action buttons */
.business-ideas-table-container .text-blue-600 {
  color: #60a5fa !important;
  border: 1px solid rgba(96, 165, 250, 0.3) !important;
  background: rgba(96, 165, 250, 0.1) !important;
}

.business-ideas-table-container .text-red-600 {
  color: #f87171 !important;
  border: 1px solid rgba(248, 113, 113, 0.3) !important;
  background: rgba(248, 113, 113, 0.1) !important;
}

.business-ideas-table-container .text-gray-600 {
  color: rgba(255, 255, 255, 0.7) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  background: rgba(255, 255, 255, 0.1) !important;
}

/* Hover effects for action buttons */
.business-ideas-table-container .text-blue-600:hover {
  background: rgba(96, 165, 250, 0.2) !important;
  transform: scale(1.05) !important;
}

.business-ideas-table-container .text-red-600:hover {
  background: rgba(248, 113, 113, 0.2) !important;
  transform: scale(1.05) !important;
}

.business-ideas-table-container .text-gray-600:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  transform: scale(1.05) !important;
}

/* Table rows */
.business-ideas-table-container tbody tr {
  transition: all 0.3s ease !important;
}

.business-ideas-table-container tbody tr:hover {
  background: rgba(255, 255, 255, 0.05) !important;
  transform: translateX(2px) !important;
}

/* Gradient text for titles */
.gradient-title {
  background: linear-gradient(135deg, #a855f7, #3b82f6) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
}

/* Icon backgrounds */
.icon-bg-blue {
  background: rgba(59, 130, 246, 0.2) !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
}

.icon-bg-green {
  background: rgba(34, 197, 94, 0.2) !important;
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3) !important;
}

.icon-bg-yellow {
  background: rgba(234, 179, 8, 0.2) !important;
  box-shadow: 0 4px 12px rgba(234, 179, 8, 0.3) !important;
}

.icon-bg-purple {
  background: rgba(168, 85, 247, 0.2) !important;
  box-shadow: 0 4px 12px rgba(168, 85, 247, 0.3) !important;
}
