from django.apps import AppConfig


class AiModelsConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'ai_models'
    verbose_name = 'AI Models - Predictive Analytics & Machine Learning'
    
    def ready(self):
        """Initialize AI models when Django starts"""
        try:
            from .predictive_engine import PredictiveAnalyticsEngine
            # Initialize the predictive engine
            PredictiveAnalyticsEngine.get_instance()
        except Exception as e:
            print(f"Warning: Could not initialize AI models: {e}")
