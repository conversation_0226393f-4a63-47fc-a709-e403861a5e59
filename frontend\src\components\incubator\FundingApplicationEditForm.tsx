import React, { useState, useEffect } from 'react';
import { Save, X, DollarSign, Edit } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { FundingApplication, FundingOpportunity, BusinessIdea } from '../../services/incubatorApi';

interface FundingApplicationEditFormProps {
  application: FundingApplication;
  onSave: (data: Partial<FundingApplication>) => Promise<boolean>;
  onCancel: () => void;
  isLoading?: boolean;
}

const FundingApplicationEditForm: React.FC<FundingApplicationEditFormProps> = ({
  application,
  onSave,
  onCancel,
  isLoading = false
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [formData, setFormData] = useState({
    pitch: application.pitch || '',
    requested_amount: application.requested_amount?.toString() || '',
    use_of_funds: application.use_of_funds || '',
  });

  const [formError, setFormError] = useState<string | null>(null);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    // Check if form has changes
    const hasFormChanges = Object.keys(formData).some(key => {
      const formValue = formData[key as keyof typeof formData];
      const originalValue = key === 'requested_amount' 
        ? application.requested_amount?.toString() || ''
        : application[key as keyof FundingApplication] || '';
      return formValue !== originalValue;
    });
    setHasChanges(hasFormChanges);
  }, [formData, application]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    setFormError(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);

    // Validate required fields
    if (!formData.pitch.trim()) {
      setFormError(t('incubator.fundingApplication.pitchRequired'));
      return;
    }

    if (!formData.requested_amount.trim() || isNaN(Number(formData.requested_amount)) || Number(formData.requested_amount) <= 0) {
      setFormError(t('incubator.fundingApplication.amountRequired'));
      return;
    }

    if (!formData.use_of_funds.trim()) {
      setFormError(t('incubator.fundingApplication.useOfFundsRequired'));
      return;
    }

    const updateData = {
      ...formData,
      requested_amount: parseFloat(formData.requested_amount)
    };

    const success = await onSave(updateData);
    if (!success) {
      setFormError(t('incubator.fundingApplication.updateError'));
    }
  };

  const canEdit = application.status === 'pending' || application.status === 'under_review';

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 rounded-xl border border-green-500/30 max-w-3xl w-full max-h-[90vh] overflow-hidden shadow-2xl">
        {/* Header */}
        <div className={`flex items-center justify-between p-6 border-b border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <Edit size={24} className={`text-green-400 ${isRTL ? 'ml-3' : 'mr-3'}`} />
            <h2 className="text-xl font-semibold text-white">
              {t('incubator.fundingApplication.editTitle')}
            </h2>
          </div>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-white transition-colors"
            disabled={isLoading}
          >
            <X size={20} />
          </button>
        </div>

        {/* Application Info */}
        <div className="px-6 py-4 bg-gray-800/50 border-b border-gray-700">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-400">{t('incubator.fundingApplication.opportunity')}:</span>
              <span className="text-white font-medium ml-2">{application.funding_opportunity.title}</span>
            </div>
            <div>
              <span className="text-gray-400">{t('incubator.fundingApplication.businessIdea')}:</span>
              <span className="text-white font-medium ml-2">{application.business_idea.title}</span>
            </div>
            <div>
              <span className="text-gray-400">{t('incubator.fundingApplication.status')}:</span>
              <span className={`ml-2 px-2 py-1 rounded text-xs ${
                application.status === 'approved' ? 'bg-green-900/50 text-green-200' :
                application.status === 'rejected' ? 'bg-red-900/50 text-red-200' :
                'bg-yellow-900/50 text-yellow-200'
              }`}>
                {t(`incubator.fundingApplication.status.${application.status}`)}
              </span>
            </div>
            <div>
              <span className="text-gray-400">{t('incubator.fundingApplication.appliedDate')}:</span>
              <span className="text-white ml-2">{new Date(application.created_at).toLocaleDateString()}</span>
            </div>
          </div>
        </div>

        {/* Form */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {!canEdit && (
            <div className="bg-yellow-900/30 border border-yellow-500/50 rounded-lg p-4 mb-6">
              <p className="text-yellow-400 text-sm">
                {t('incubator.fundingApplication.cannotEditStatus')}
              </p>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Error Message */}
            {formError && (
              <div className="bg-red-900/30 border border-red-500/50 rounded-lg p-4">
                <p className="text-red-400 text-sm">{formError}</p>
              </div>
            )}

            {/* Pitch */}
            <div>
              <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                {t('incubator.fundingApplication.pitch')} *
              </label>
              <textarea
                name="pitch"
                value={formData.pitch}
                onChange={handleInputChange}
                rows={6}
                className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-white placeholder-gray-400 resize-none ${isRTL ? 'text-right' : ''}`}
                placeholder={t('incubator.fundingApplication.pitchPlaceholder')}
                required
                disabled={isLoading || !canEdit}
              />
              <p className="text-gray-500 text-xs mt-1">
                {t('incubator.fundingApplication.pitchHelp')}
              </p>
            </div>

            {/* Requested Amount */}
            <div>
              <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                {t('incubator.fundingApplication.requestedAmount')} *
              </label>
              <div className="relative">
                <DollarSign size={20} className={`absolute ${isRTL ? 'right-3' : 'left-3'} top-3 text-gray-400`} />
                <input
                  type="number"
                  name="requested_amount"
                  value={formData.requested_amount}
                  onChange={handleInputChange}
                  min="0"
                  step="0.01"
                  className={`w-full ${isRTL ? 'pr-10 pl-4' : 'pl-10 pr-4'} py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-white placeholder-gray-400 ${isRTL ? 'text-right' : ''}`}
                  placeholder={t('incubator.fundingApplication.amountPlaceholder')}
                  required
                  disabled={isLoading || !canEdit}
                />
              </div>
              {application.funding_opportunity.amount_max && (
                <p className="text-gray-500 text-xs mt-1">
                  {t('incubator.fundingApplication.maxAmount')}: ${application.funding_opportunity.amount_max.toLocaleString()}
                </p>
              )}
            </div>

            {/* Use of Funds */}
            <div>
              <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                {t('incubator.fundingApplication.useOfFunds')} *
              </label>
              <textarea
                name="use_of_funds"
                value={formData.use_of_funds}
                onChange={handleInputChange}
                rows={5}
                className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-white placeholder-gray-400 resize-none ${isRTL ? 'text-right' : ''}`}
                placeholder={t('incubator.fundingApplication.useOfFundsPlaceholder')}
                required
                disabled={isLoading || !canEdit}
              />
              <p className="text-gray-500 text-xs mt-1">
                {t('incubator.fundingApplication.useOfFundsHelp')}
              </p>
            </div>

            {/* Application Requirements (Read-only) */}
            {application.funding_opportunity.requirements && (
              <div>
                <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                  {t('incubator.fundingApplication.requirements')}
                </label>
                <div className="bg-gray-800/50 border border-gray-600 rounded-lg p-4">
                  <p className={`text-gray-300 text-sm ${isRTL ? 'text-right' : ''}`}>
                    {application.funding_opportunity.requirements}
                  </p>
                </div>
              </div>
            )}

            {/* Application Process (Read-only) */}
            {application.funding_opportunity.application_process && (
              <div>
                <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                  {t('incubator.fundingApplication.process')}
                </label>
                <div className="bg-gray-800/50 border border-gray-600 rounded-lg p-4">
                  <p className={`text-gray-300 text-sm ${isRTL ? 'text-right' : ''}`}>
                    {application.funding_opportunity.application_process}
                  </p>
                </div>
              </div>
            )}

            {/* Admin Feedback (if any) */}
            {application.admin_feedback && (
              <div>
                <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                  {t('incubator.fundingApplication.adminFeedback')}
                </label>
                <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4">
                  <p className={`text-blue-300 text-sm ${isRTL ? 'text-right' : ''}`}>
                    {application.admin_feedback}
                  </p>
                </div>
              </div>
            )}
          </form>
        </div>

        {/* Footer */}
        <div className={`flex justify-end gap-4 p-6 border-t border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <button
            type="button"
            onClick={onCancel}
            className="px-6 py-2 border border-gray-600 rounded-lg hover:bg-gray-800 transition-colors text-white disabled:opacity-50"
            disabled={isLoading}
          >
            {t('common.cancel')}
          </button>
          {canEdit && (
            <button
              onClick={handleSubmit}
              disabled={isLoading || !hasChanges}
              className="px-6 py-2 bg-green-600 rounded-lg hover:bg-green-700 transition-colors flex items-center text-white disabled:opacity-50"
            >
              {isLoading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  {t('common.saving')}
                </>
              ) : (
                <>
                  <Save size={16} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                  {t('common.saveChanges')}
                </>
              )}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default FundingApplicationEditForm;
