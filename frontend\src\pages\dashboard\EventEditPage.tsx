import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Save, Loader2, Calendar, MapPin, Globe } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import AuthenticatedLayout from '../../components/layout/AuthenticatedLayout';
import { Event, eventsAPI } from '../../services/api';
import { useAppSelector } from '../../store/hooks';
import { RTLText, RTLFlex } from '../../components/rtl';

const EventEditPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { language, isRTL } = useLanguage();
  const { user } = useAppSelector(state => state.auth);

  const [event, setEvent] = useState<Event | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    date: '',
    location: '',
    is_virtual: false,
    virtual_link: '',
  });

  useEffect(() => {
    if (id) {
      fetchEvent();
    }
  }, [id]);

  const fetchEvent = async () => {
    if (!id) return;
    
    setLoading(true);
    try {
      const eventData = await eventsAPI.getEvent(parseInt(id));
      
      // Check if user owns this event
      if (eventData.organizer.id !== user?.id && !user?.is_staff && !user?.is_superuser) {
        setError(t('events.errors.notOwner', 'You can only edit your own events'));
        return;
      }

      setEvent(eventData);
      
      // Format date for datetime-local input
      const eventDate = new Date(eventData.date);
      const formattedDate = eventDate.toISOString().slice(0, 16);
      
      setFormData({
        title: eventData.title,
        description: eventData.description,
        date: formattedDate,
        location: eventData.location,
        is_virtual: eventData.is_virtual,
        virtual_link: eventData.virtual_link || '',
      });
    } catch (error) {
      console.error('Error fetching event:', error);
      setError(t('events.errors.failedToLoad', 'Failed to load event'));
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!id || !event) return;

    setSaving(true);
    setError(null);

    try {
      await eventsAPI.updateEvent(parseInt(id), {
        ...formData,
        organizer_id: user?.id,
      });
      setSuccess(t('events.success.updated', 'Event updated successfully'));
      
      // Navigate back after success
      setTimeout(() => {
        navigate('/dashboard/events');
      }, 1500);
    } catch (error) {
      console.error('Error updating event:', error);
      setError(t('events.errors.failedToUpdate', 'Failed to update event'));
    } finally {
      setSaving(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(language === 'ar' ? 'ar-SA' : undefined, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const isUpcoming = (dateString: string) => {
    return new Date(dateString) > new Date();
  };

  if (loading) {
    return (
      <AuthenticatedLayout>
      <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="flex justify-center items-center py-12">
          <div className="flex items-center space-x-3">
            <Loader2 className="w-6 h-6 animate-spin" />
            <span>{t('common.loading', 'Loading...')}</span>
          </div>
        </div>
                </div>
        </div>
      </div>
    </AuthenticatedLayout>
    );
  }

  if (error && !event) {
    return (
      <AuthenticatedLayout>
        <div className="text-center py-12">
          <div className="text-red-400 mb-4">{error}</div>
          <button
            onClick={() => navigate('/dashboard/events')}
            className="px-4 py-2 bg-purple-600 rounded-lg hover:bg-purple-700 transition-colors"
          >
            {t('common.goBack', 'Go Back')}
          </button>
        </div>
      </AuthenticatedLayout>
    );
  }

  return (
    <AuthenticatedLayout>
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className={`flex items-center justify-between mb-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`flex items-center space-x-4 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
            <button
              onClick={() => navigate('/dashboard/events')}
              className="p-2 rounded-lg hover:bg-indigo-800/50 transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <div>
              <RTLText as="h1" className="text-2xl font-bold">
                {t('events.editEvent', 'Edit Event')}
              </RTLText>
              <RTLText as="div" className="text-gray-400 mt-1">
                {t('events.updateYourEvent', 'Update your event details')}
              </RTLText>
            </div>
          </div>
        </div>

        {/* Event Status */}
        {event && (
          <div className="mb-6 p-4 bg-indigo-950/50 rounded-lg border border-indigo-800">
            <RTLFlex align="center">
              <Calendar className={`w-5 h-5 ${language === 'ar' ? 'ml-3' : 'mr-3'}`} />
              <div>
                <RTLText as="div" className="text-sm text-gray-400">
                  {t('events.currentStatus', 'Current Status')}
                </RTLText>
                <RTLText as="div" className={`font-medium ${isUpcoming(event.date) ? 'text-green-400' : 'text-gray-400'}`}>
                  {isUpcoming(event.date) ? t('events.upcoming', 'Upcoming') : t('events.past', 'Past Event')}
                </RTLText>
              </div>
              <div className="ml-auto">
                <RTLText as="div" className="text-sm text-gray-400">
                  {formatDate(event.date)}
                </RTLText>
              </div>
            </RTLFlex>
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSave} className="space-y-6">
          <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
            {/* Title */}
            <div className="mb-6">
              <label className="block text-gray-300 mb-2 font-medium">
                {t('events.title', 'Title')} *
              </label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                className="w-full px-4 py-3 bg-indigo-900/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:border-purple-500 transition-colors"
                placeholder={t('events.enterTitle', 'Enter event title')}
                required
                dir={language === 'ar' ? 'rtl' : 'ltr'}
              />
            </div>

            {/* Description */}
            <div className="mb-6">
              <label className="block text-gray-300 mb-2 font-medium">
                {t('events.description', 'Description')} *
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={4}
                className="w-full px-4 py-3 bg-indigo-900/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:border-purple-500 transition-colors resize-none"
                placeholder={t('events.enterDescription', 'Describe your event')}
                required
                dir={language === 'ar' ? 'rtl' : 'ltr'}
              />
            </div>

            {/* Date & Time */}
            <div className="mb-6">
              <label className="block text-gray-300 mb-2 font-medium">
                {t('events.dateTime', 'Date & Time')} *
              </label>
              <input
                type="datetime-local"
                name="date"
                value={formData.date}
                onChange={handleInputChange}
                className="w-full px-4 py-3 bg-indigo-900/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:border-purple-500 transition-colors"
                required
              />
            </div>

            {/* Virtual Event Toggle */}
            <div className="mb-6">
              <label className="flex items-center space-x-3 text-gray-300">
                <input
                  type="checkbox"
                  name="is_virtual"
                  checked={formData.is_virtual}
                  onChange={handleInputChange}
                  className="w-5 h-5 rounded border-indigo-800 bg-indigo-900/50 text-purple-600 focus:ring-purple-500 focus:ring-2"
                />
                <RTLFlex align="center">
                  <Globe className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                  <span>{t('events.virtualEvent', 'Virtual Event')}</span>
                </RTLFlex>
              </label>
            </div>

            {/* Location or Virtual Link */}
            {formData.is_virtual ? (
              <div className="mb-6">
                <label className="block text-gray-300 mb-2 font-medium">
                  {t('events.virtualLink', 'Virtual Link')}
                </label>
                <input
                  type="url"
                  name="virtual_link"
                  value={formData.virtual_link}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-indigo-900/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:border-purple-500 transition-colors"
                  placeholder="https://zoom.us/j/..."
                />
                <div className="mt-2 text-sm text-gray-400">
                  {t('events.virtualLinkNote', 'Provide a link for attendees to join the virtual event')}
                </div>
              </div>
            ) : (
              <div className="mb-6">
                <label className="block text-gray-300 mb-2 font-medium">
                  {t('events.location', 'Location')} *
                </label>
                <RTLFlex align="center" className="relative">
                  <MapPin className={`absolute ${language === 'ar' ? 'right-3' : 'left-3'} top-3 w-5 h-5 text-gray-400`} />
                  <input
                    type="text"
                    name="location"
                    value={formData.location}
                    onChange={handleInputChange}
                    className={`w-full px-4 py-3 ${language === 'ar' ? 'pr-12' : 'pl-12'} bg-indigo-900/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:border-purple-500 transition-colors`}
                    placeholder={t('events.enterLocation', 'Enter event location')}
                    required={!formData.is_virtual}
                    dir={language === 'ar' ? 'rtl' : 'ltr'}
                  />
                </RTLFlex>
              </div>
            )}

            {/* Event Type Indicator */}
            <div className="mb-6 p-4 bg-indigo-950/30 rounded-lg border border-indigo-800/50">
              <RTLFlex align="center">
                {formData.is_virtual ? (
                  <>
                    <Globe className={`w-5 h-5 text-blue-400 ${language === 'ar' ? 'ml-3' : 'mr-3'}`} />
                    <div>
                      <RTLText as="div" className="font-medium text-blue-400">
                        {t('events.virtualEvent', 'Virtual Event')}
                      </RTLText>
                      <RTLText as="div" className="text-sm text-gray-400">
                        {t('events.virtualEventDesc', 'Attendees will join online')}
                      </RTLText>
                    </div>
                  </>
                ) : (
                  <>
                    <MapPin className={`w-5 h-5 text-green-400 ${language === 'ar' ? 'ml-3' : 'mr-3'}`} />
                    <div>
                      <RTLText as="div" className="font-medium text-green-400">
                        {t('events.physicalEvent', 'Physical Event')}
                      </RTLText>
                      <RTLText as="div" className="text-sm text-gray-400">
                        {t('events.physicalEventDesc', 'Attendees will meet in person')}
                      </RTLText>
                    </div>
                  </>
                )}
              </RTLFlex>
            </div>
          </div>

          {/* Error/Success Messages */}
          {error && (
            <div className="p-4 bg-red-900/50 border border-red-800 rounded-lg text-red-200">
              {error}
            </div>
          )}

          {success && (
            <div className="p-4 bg-green-900/50 border border-green-800 rounded-lg text-green-200">
              {success}
            </div>
          )}

          {/* Action Buttons */}
          <div className={`flex justify-end space-x-4 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
            <button
              type="button"
              onClick={() => navigate('/dashboard/events')}
              className="px-6 py-3 bg-gray-700 hover:bg-gray-600 rounded-lg font-medium transition-colors"
              disabled={saving}
            >
              {t('common.cancel', 'Cancel')}
            </button>
            <RTLFlex
              as="button"
              type="submit"
              className="px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={saving}
              align="center"
            >
              {saving ? (
                <>
                  <Loader2 className={`w-5 h-5 animate-spin ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                  {t('common.saving', 'Saving...')}
                </>
              ) : (
                <>
                  <Save className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                  {t('common.save', 'Save Changes')}
                </>
              )}
            </RTLFlex>
          </div>
        </form>
      </div>
    </AuthenticatedLayout>
  );
};

export default EventEditPage;
