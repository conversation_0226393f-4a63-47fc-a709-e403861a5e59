/**
 * Layout Components
 * Universal layout system that eliminates sidebar duplications
 */

// Main layout components
export { default as PublicLayout } from './PublicLayout';
export { default as AuthenticatedLayout } from './AuthenticatedLayout';
export { default as UniversalSidebar } from './UniversalSidebar';

// Layout types and interfaces
export interface LayoutProps {
  children: React.ReactNode;
  showSidebar?: boolean;
  sidebarVariant?: 'auto' | 'always' | 'never';
}

export interface SidebarProps {
  isOpen?: boolean;
  onClose?: () => void;
  variant?: 'desktop' | 'mobile';
}

// Layout utilities
export const getLayoutForRoute = (pathname: string) => {
  if (pathname.startsWith('/admin')) return 'admin';
  if (pathname.startsWith('/dashboard')) return 'user';
  return 'public';
};

export const shouldShowSidebar = (pathname: string, isAuthenticated: boolean) => {
  if (!isAuthenticated) return false;
  return pathname.startsWith('/dashboard') || pathname.startsWith('/admin') || pathname.startsWith('/templates');
};
