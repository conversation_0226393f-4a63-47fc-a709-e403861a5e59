import React, { useEffect, useState } from 'react';
import { usePara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { fetchCategory, fetchTopics, clearCurrentCategory } from '../store/forumSlice';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import {
  MessageSquare,
  Users,
  Clock,
  ArrowLeft,
  Plus,
  Search,
  Pin,
  Lock,
  Eye
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { useTranslation } from 'react-i18next';

const ForumCategoryPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const { slug } = useParams<{ slug: string }>();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { currentCategory, topics, loading, error } = useAppSelector((state) => state.forum);
  const { user } = useAppSelector((state) => state.auth);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (slug) {
      dispatch(fetchCategory(slug));
      dispatch(fetchTopics(slug));
    }

    return () => {
      dispatch(clearCurrentCategory());
    };
  }, [dispatch, slug]);

  // Filter topics based on search term
  const filteredTopics = topics.filter(topic =>
    topic.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    topic.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleCreateTopic = () => {
    if (!currentCategory) return;
    navigate(`/forum/topic/create?category=${currentCategory.id}`);
  };

  return (
    <div className={`min-h-screen flex flex-col ${isRTL ? "flex-row-reverse" : ""}`}>
      <Navbar />

      <main className={`flex-grow container mx-auto px-4 py-8 ${isRTL ? "flex-row-reverse" : ""}`}>
        {/* Breadcrumb */}
        <div className="mb-6">
          <Link to="/forum" className={`text-purple-400 hover:text-purple-300 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <ArrowLeft size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
            Back to Forums
          </Link>
        </div>

        {loading && !currentCategory ? (
          <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
            <div className={`flex justify-center items-center h-40 ${isRTL ? "flex-row-reverse" : ""}`}>
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
            </div>
          </div>
        ) : error ? (
          <div className="bg-red-900/30 border border-red-800 rounded-lg p-4 mb-6">
            <div className="text-red-300">{error}</div>
          </div>
        ) : currentCategory ? (
          <>
            <div className="mb-8">
              <h1 className={`text-3xl font-bold flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <MessageSquare size={28} className={`mr-3 text-purple-400 ${isRTL ? "space-x-reverse" : ""}`} />
                {currentCategory.name}
              </h1>
              <div className="text-gray-400 mt-2">
                {currentCategory.description}
              </div>

              <div className={`flex flex-wrap gap-4 mt-4 text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
                <div className={`flex items-center text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <MessageSquare size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                  <span>{currentCategory.topic_count} Topics</span>
                </div>
                <div className={`flex items-center text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <Users size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                  <span>{currentCategory.thread_count} Threads</span>
                </div>
              </div>
            </div>

            <div className={`flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
              <div className={`relative flex-1 max-w-md ${isRTL ? "flex-row-reverse" : ""}`}>
                <input
                  type="text"
                  placeholder={t("common.search.topics", "Search topics...")}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full bg-indigo-900/30 border border-indigo-800/50 rounded-lg py-2 pl-10 pr-4 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
                <Search size={18} className="absolute left-3 top-2.5 text-gray-400" />
              </div>

              <button
                onClick={handleCreateTopic}
                className={`px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <Plus size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                Create New Topic
              </button>
            </div>

            {/* Topics List */}
            {filteredTopics.length === 0 ? (
              <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50 text-center">
                <div className="text-gray-400">
                  {searchTerm
                    ? t("common.no.topics.found", "No topics found matching your search.")
                    : "No topics available in this category yet. Be the first to create one!"}
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredTopics.map((topic) => (
                  <Link
                    key={topic.id}
                    to={`/forum/topic/${topic.slug}`}
                    className="block bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50 hover:bg-indigo-800/30 transition-colors"
                  >
                    <div className={`flex items-start ${isRTL ? "flex-row-reverse" : ""}`}>
                      <div className={`mr-4 ${isRTL ? "space-x-reverse" : ""}`}>
                        {topic.icon ? (
                          <MessageSquare size={24} className="text-purple-400" />
                        ) : (
                          <MessageSquare size={24} className="text-purple-400" />
                        )}
                      </div>
                      <div className={`flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                          <h2 className="text-xl font-semibold">{topic.title}</h2>
                          {topic.is_pinned && (
                            <Pin size={16} className={`ml-2 text-yellow-400 ${isRTL ? "space-x-reverse" : ""}`} />
                          )}
                          {topic.is_locked && (
                            <Lock size={16} className={`ml-2 text-red-400 ${isRTL ? "space-x-reverse" : ""}`} />
                          )}
                        </div>
                        <div className="text-gray-400 mt-1">{topic.description}</div>

                        <div className={`flex flex-wrap gap-4 mt-4 text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
                          <div className={`flex items-center text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                            <MessageSquare size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                            <span>{topic.thread_count} Threads</span>
                          </div>
                          <div className={`flex items-center text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                            <Users size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                            <span>{topic.post_count} Posts</span>
                          </div>
                          <div className={`flex items-center text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                            <Clock size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                            <span>Last activity: {formatDistanceToNow(new Date(topic.last_activity), { addSuffix: true })}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            )}
          </>
        ) : (
          <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50 text-center">
            <p className="text-gray-400">{t("common.category.not.found", "Category not found.")}</p>
          </div>
        )}
      </main>

      <Footer />
    </div>
  );
};

export default ForumCategoryPage;
