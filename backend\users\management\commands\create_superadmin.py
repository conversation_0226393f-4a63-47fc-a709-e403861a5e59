from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.db import transaction
from django.utils import timezone
from users.models import User<PERSON>ole, UserProfile, UserRoleAssignment
import getpass

class Command(BaseCommand):
    help = 'Create a Super Admin user with full system access'

    def add_arguments(self, parser):
        parser.add_argument('--username', type=str, help='Username for the Super Admin')
        parser.add_argument('--email', type=str, help='Email for the Super Admin')
        parser.add_argument('--password', type=str, help='Password for the Super Admin')
        parser.add_argument('--first-name', type=str, help='First name')
        parser.add_argument('--last-name', type=str, help='Last name')
        parser.add_argument('--interactive', action='store_true', help='Interactive mode')

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 Creating Super Admin User'))
        self.stdout.write('=' * 50)

        if options['interactive']:
            # Interactive mode
            username = input('Username: ')
            email = input('Email: ')
            first_name = input('First Name (optional): ')
            last_name = input('Last Name (optional): ')
            
            while True:
                password = getpass.getpass('Password: ')
                password_confirm = getpass.getpass('Confirm Password: ')
                
                if password != password_confirm:
                    self.stdout.write(self.style.ERROR('❌ Passwords don\'t match!'))
                    continue
                
                if len(password) < 8:
                    self.stdout.write(self.style.ERROR('❌ Password must be at least 8 characters!'))
                    continue
                
                break
        else:
            # Command line arguments
            username = options['username']
            email = options['email']
            password = options['password']
            first_name = options.get('first_name', '')
            last_name = options.get('last_name', '')

            if not username or not email or not password:
                self.stdout.write(self.style.ERROR('❌ Username, email, and password are required!'))
                self.stdout.write('Use --interactive for interactive mode or provide all arguments.')
                return

        try:
            with transaction.atomic():
                # Check if user exists
                if User.objects.filter(username=username).exists():
                    self.stdout.write(self.style.ERROR(f'❌ User "{username}" already exists!'))
                    return

                if User.objects.filter(email=email).exists():
                    self.stdout.write(self.style.ERROR(f'❌ Email "{email}" is already in use!'))
                    return

                # Create user
                user = User.objects.create_user(
                    username=username,
                    email=email,
                    password=password,
                    first_name=first_name,
                    last_name=last_name,
                    is_staff=True,
                    is_superuser=True,
                    is_active=True
                )

                self.stdout.write(self.style.SUCCESS(f'✅ Created Django superuser: {username}'))

                # Create or get Super Admin role
                super_admin_role, role_created = UserRole.objects.get_or_create(
                    name='super_admin',
                    defaults={
                        'display_name': 'Super Administrator',
                        'description': 'Complete system control with highest level access to all platform features, system management, user impersonation, AI system control, and advanced analytics.',
                        'permission_level': 'super_admin',
                        'is_active': True,
                        'requires_approval': False
                    }
                )

                if role_created:
                    self.stdout.write(self.style.SUCCESS('✅ Created Super Admin role'))
                else:
                    self.stdout.write(self.style.WARNING('📊 Super Admin role already exists'))

                # Create user profile
                profile, profile_created = UserProfile.objects.get_or_create(
                    user=user,
                    defaults={
                        'bio': 'Super Administrator with full system access',
                        'location': '',
                        'website': '',
                        'company': '',
                        'is_active': True,
                        'language': 'en'
                    }
                )

                if profile_created:
                    self.stdout.write(self.style.SUCCESS('✅ Created user profile'))

                # Assign Super Admin role
                assignment, assignment_created = UserRoleAssignment.objects.get_or_create(
                    user_profile=profile,
                    role=super_admin_role,
                    defaults={
                        'is_active': True,
                        'is_approved': True,
                        'assigned_by': None,
                        'approved_by': None,
                        'notes': f'Super Admin role assigned during user creation on {timezone.now().strftime("%Y-%m-%d %H:%M:%S")}'
                    }
                )

                if assignment_created:
                    self.stdout.write(self.style.SUCCESS('✅ Assigned Super Admin role'))

                # Success message
                self.stdout.write('')
                self.stdout.write('=' * 50)
                self.stdout.write(self.style.SUCCESS('🎉 SUCCESS! Super Admin user created!'))
                self.stdout.write('=' * 50)
                self.stdout.write(f'Username: {username}')
                self.stdout.write(f'Email: {email}')
                self.stdout.write(f'Role: Super Administrator')
                self.stdout.write(f'Django Superuser: Yes')
                self.stdout.write(f'Staff Status: Yes')
                self.stdout.write(f'Active: Yes')
                self.stdout.write('')
                self.stdout.write(self.style.SUCCESS('🔐 You can now login with these credentials!'))
                self.stdout.write('')
                self.stdout.write('📍 Login URLs:')
                self.stdout.write('- Admin Panel: http://localhost:8000/admin/')
                self.stdout.write('- Super Admin Dashboard: http://localhost:3000/admin/super-admin/')
                self.stdout.write('- Main Application: http://localhost:3000/')

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Error creating Super Admin user: {str(e)}'))
