/**
 * User Dashboard Content Provider
 * Provides user-specific dashboard data, stats, and actions
 */

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useAppSelector } from '../../store/hooks';
import { DashboardStat, DashboardQuickAction } from '../../types/dashboard';
import { businessIdeasAPI } from '../../services/api';
import {
  Lightbulb,
  CheckCircle,
  Clock,
  AlertCircle,
  FileText,
  BookOpen,
  Users,
  TrendingUp
} from 'lucide-react';

interface UserDashboardData {
  stats: DashboardStat[];
  quickActions: DashboardQuickAction[];
  recentIdeas: any[];
  loading: boolean;
  error: string | null;
  refreshData: () => Promise<void>;
}

const UserDashboardContext = createContext<UserDashboardData | null>(null);

interface UserDashboardProviderProps {
  children: React.ReactNode;
}

export const UserDashboardProvider: React.FC<UserDashboardProviderProps> = ({ children }) => {
  const { user } = useAppSelector(state => state.auth);
  const [stats, setStats] = useState<DashboardStat[]>([]);
  const [quickActions, setQuickActions] = useState<DashboardQuickAction[]>([]);
  const [recentIdeas, setRecentIdeas] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch user dashboard data
  const fetchDashboardData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch user's business ideas
      const businessIdeas = await businessIdeasAPI.getUserBusinessIdeas();
      
      // Calculate stats from business ideas
      const totalIdeas = businessIdeas.length;
      const approvedIdeas = businessIdeas.filter(idea => idea.status === 'approved').length;
      const pendingIdeas = businessIdeas.filter(idea => idea.status === 'pending').length;
      const rejectedIdeas = businessIdeas.filter(idea => idea.status === 'rejected').length;

      // Create stats
      const userStats: DashboardStat[] = [
        {
          id: 'total_ideas',
          title: 'Total Ideas',
          value: totalIdeas,
          icon: Lightbulb,
          color: 'bg-indigo-600/30',
          change: totalIdeas > 0 ? 16.7 : 0,
          changeType: 'increase',
          description: 'Your submitted business ideas'
        },
        {
          id: 'approved_ideas',
          title: 'Approved',
          value: approvedIdeas,
          icon: CheckCircle,
          color: 'bg-green-600/30',
          change: approvedIdeas > 0 ? 50.0 : 0,
          changeType: 'increase',
          description: 'Ideas approved for development'
        },
        {
          id: 'pending_ideas',
          title: 'Pending',
          value: pendingIdeas,
          icon: Clock,
          color: 'bg-yellow-600/30',
          change: 0,
          changeType: 'neutral',
          description: 'Ideas under review'
        },
        {
          id: 'rejected_ideas',
          title: 'Rejected',
          value: rejectedIdeas,
          icon: AlertCircle,
          color: 'bg-red-600/30',
          change: rejectedIdeas > 0 ? -33.3 : 0,
          changeType: 'decrease',
          description: 'Ideas that need improvement'
        }
      ];

      // Create quick actions
      const userQuickActions: DashboardQuickAction[] = [
        {
          id: 'create_idea',
          title: 'Create Business Idea',
          description: 'Submit a new business idea for review',
          icon: Lightbulb,
          href: '/dashboard/business-ideas/create',
          color: 'bg-indigo-600/20 hover:bg-indigo-600/30 border-indigo-500/30',
        },
        {
          id: 'view_ideas',
          title: 'My Ideas',
          description: 'View and manage your submitted ideas',
          icon: FileText,
          href: '/dashboard/business-ideas',
          color: 'bg-blue-600/20 hover:bg-blue-600/30 border-blue-500/30',
          badge: totalIdeas > 0 ? totalIdeas : undefined,
        },
        {
          id: 'resources',
          title: 'Learning Resources',
          description: 'Access entrepreneurship resources and guides',
          icon: BookOpen,
          href: '/dashboard/resources',
          color: 'bg-green-600/20 hover:bg-green-600/30 border-green-500/30',
        },
        {
          id: 'mentorship',
          title: 'Find Mentor',
          description: 'Connect with experienced mentors',
          icon: Users,
          href: '/dashboard/mentorship',
          color: 'bg-purple-600/20 hover:bg-purple-600/30 border-purple-500/30',
        },
        {
          id: 'business_plans',
          title: 'Business Plans',
          description: 'Create and manage business plans',
          icon: FileText,
          href: '/dashboard/business-plans',
          color: 'bg-orange-600/20 hover:bg-orange-600/30 border-orange-500/30',
        },
        {
          id: 'analytics',
          title: 'My Analytics',
          description: 'View your progress and insights',
          icon: TrendingUp,
          href: '/dashboard/analytics',
          color: 'bg-pink-600/20 hover:bg-pink-600/30 border-pink-500/30',
        }
      ];

      setStats(userStats);
      setQuickActions(userQuickActions);
      setRecentIdeas(businessIdeas.slice(0, 5)); // Get 5 most recent ideas

    } catch (err) {
      console.error('Error fetching user dashboard data:', err);
      setError('Failed to load dashboard data');
      
      // Fallback to mock data
      const mockStats: DashboardStat[] = [
        {
          id: 'total_ideas',
          title: 'Total Ideas',
          value: 0,
          icon: Lightbulb,
          color: 'bg-indigo-600/30',
          change: 0,
          changeType: 'neutral',
          description: 'Your submitted business ideas'
        },
        {
          id: 'approved_ideas',
          title: 'Approved',
          value: 0,
          icon: CheckCircle,
          color: 'bg-green-600/30',
          change: 0,
          changeType: 'neutral',
          description: 'Ideas approved for development'
        },
        {
          id: 'pending_ideas',
          title: 'Pending',
          value: 0,
          icon: Clock,
          color: 'bg-yellow-600/30',
          change: 0,
          changeType: 'neutral',
          description: 'Ideas under review'
        },
        {
          id: 'rejected_ideas',
          title: 'Rejected',
          value: 0,
          icon: AlertCircle,
          color: 'bg-red-600/30',
          change: 0,
          changeType: 'neutral',
          description: 'Ideas that need improvement'
        }
      ];

      const mockQuickActions: DashboardQuickAction[] = [
        {
          id: 'create_idea',
          title: 'Create Business Idea',
          description: 'Submit a new business idea for review',
          icon: Lightbulb,
          href: '/dashboard/business-ideas/create',
          color: 'bg-indigo-600/20 hover:bg-indigo-600/30 border-indigo-500/30',
        },
        {
          id: 'view_ideas',
          title: 'My Ideas',
          description: 'View and manage your submitted ideas',
          icon: FileText,
          href: '/dashboard/business-ideas',
          color: 'bg-blue-600/20 hover:bg-blue-600/30 border-blue-500/30',
        },
        {
          id: 'resources',
          title: 'Learning Resources',
          description: 'Access entrepreneurship resources and guides',
          icon: BookOpen,
          href: '/dashboard/resources',
          color: 'bg-green-600/20 hover:bg-green-600/30 border-green-500/30',
        },
        {
          id: 'mentorship',
          title: 'Find Mentor',
          description: 'Connect with experienced mentors',
          icon: Users,
          href: '/dashboard/mentorship',
          color: 'bg-purple-600/20 hover:bg-purple-600/30 border-purple-500/30',
        }
      ];

      setStats(mockStats);
      setQuickActions(mockQuickActions);
      setRecentIdeas([]);
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Initial data fetch
  useEffect(() => {
    if (user) {
      fetchDashboardData();
    }
  }, [user, fetchDashboardData]);

  const contextValue: UserDashboardData = {
    stats,
    quickActions,
    recentIdeas,
    loading,
    error,
    refreshData: fetchDashboardData,
  };

  return (
    <UserDashboardContext.Provider value={contextValue}>
      {children}
    </UserDashboardContext.Provider>
  );
};

// Hook to use user dashboard data
export const useUserDashboard = (): UserDashboardData => {
  const context = useContext(UserDashboardContext);
  if (!context) {
    throw new Error('useUserDashboard must be used within a UserDashboardProvider');
  }
  return context;
};

export default UserDashboardProvider;
