"""
Enhanced Predictive Analytics Helper Methods
Supporting functions for advanced predictive analytics features
"""

import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging

logger = logging.getLogger(__name__)


class PredictiveAnalyticsHelpers:
    """Helper methods for enhanced predictive analytics"""
    
    @staticmethod
    def extract_failure_features(business_data: Dict[str, Any], historical_data: List[Dict] = None) -> np.ndarray:
        """Extract comprehensive features for failure prediction"""
        features = []
        
        # Basic business features
        features.extend([
            len(business_data.get('title', '')),
            len(business_data.get('description', '')),
            business_data.get('stage', 1),
            1 if business_data.get('has_team', False) else 0,
            1 if business_data.get('has_funding', False) else 0,
        ])
        
        # Financial health indicators
        features.extend([
            business_data.get('monthly_revenue', 0),
            business_data.get('burn_rate', 0),
            business_data.get('runway_months', 0),
            business_data.get('customer_count', 0),
            business_data.get('growth_rate', 0),
        ])
        
        # Team and operational features
        features.extend([
            business_data.get('team_size', 1),
            business_data.get('founder_experience', 0),
            business_data.get('market_size', 0),
            business_data.get('competition_level', 5),  # 1-10 scale
        ])
        
        # Historical trend features
        if historical_data:
            recent_data = historical_data[-3:] if len(historical_data) >= 3 else historical_data
            features.extend([
                len(recent_data),
                np.mean([d.get('progress_rate', 0) for d in recent_data]),
                np.std([d.get('progress_rate', 0) for d in recent_data]) if len(recent_data) > 1 else 0,
            ])
        else:
            features.extend([0, 0, 0])
        
        return np.array(features).reshape(1, -1)
    
    @staticmethod
    def extract_timing_features(business_data: Dict[str, Any], market_context: Dict[str, Any] = None) -> np.ndarray:
        """Extract features for market timing optimization"""
        features = []
        
        # Business readiness features
        features.extend([
            business_data.get('product_readiness', 0.5),  # 0-1 scale
            business_data.get('team_readiness', 0.5),
            business_data.get('funding_readiness', 0.5),
            business_data.get('market_validation', 0.5),
        ])
        
        # Market condition features
        if market_context:
            features.extend([
                market_context.get('market_growth_rate', 0.03),
                market_context.get('competition_intensity', 0.5),
                market_context.get('economic_conditions', 0.5),
                market_context.get('seasonal_factor', 0.5),
            ])
        else:
            features.extend([0.03, 0.5, 0.5, 0.5])
        
        # Competitive landscape features
        features.extend([
            business_data.get('competitive_advantage_score', 0.5),
            business_data.get('differentiation_level', 0.5),
            business_data.get('barrier_to_entry', 0.5),
        ])
        
        return np.array(features).reshape(1, -1)
    
    @staticmethod
    def extract_competitor_features(business_data: Dict[str, Any], competitor_data: List[Dict] = None) -> np.ndarray:
        """Extract features for competitor analysis"""
        features = []
        
        # Business positioning features
        features.extend([
            business_data.get('market_share', 0.01),
            business_data.get('brand_strength', 0.5),
            business_data.get('product_quality', 0.5),
            business_data.get('pricing_position', 0.5),  # relative to market
        ])
        
        # Competitive landscape features
        if competitor_data:
            competitor_count = len(competitor_data)
            avg_competitor_strength = np.mean([c.get('strength_score', 0.5) for c in competitor_data])
            max_competitor_threat = max([c.get('threat_level', 0.5) for c in competitor_data])
        else:
            competitor_count = 5  # default assumption
            avg_competitor_strength = 0.5
            max_competitor_threat = 0.5
        
        features.extend([
            competitor_count,
            avg_competitor_strength,
            max_competitor_threat,
        ])
        
        # Market dynamics
        features.extend([
            business_data.get('market_growth_rate', 0.03),
            business_data.get('customer_switching_cost', 0.5),
            business_data.get('network_effects', 0.3),
        ])
        
        return np.array(features).reshape(1, -1)
    
    @staticmethod
    def extract_cac_features(business_data: Dict[str, Any], marketing_data: Dict[str, Any] = None) -> np.ndarray:
        """Extract features for CAC prediction"""
        features = []
        
        # Business characteristics
        features.extend([
            business_data.get('industry_category', 1),  # encoded category
            business_data.get('business_model', 1),  # encoded model type
            business_data.get('target_market_size', 1000000),
            business_data.get('product_complexity', 0.5),
        ])
        
        # Marketing context
        if marketing_data:
            features.extend([
                marketing_data.get('total_marketing_budget', 10000),
                marketing_data.get('channel_count', 3),
                marketing_data.get('conversion_rate', 0.02),
                marketing_data.get('brand_awareness', 0.1),
            ])
        else:
            features.extend([10000, 3, 0.02, 0.1])
        
        # Market conditions
        features.extend([
            business_data.get('competition_level', 5),
            business_data.get('market_maturity', 0.5),
            business_data.get('customer_education_needed', 0.5),
        ])
        
        return np.array(features).reshape(1, -1)
    
    @staticmethod
    def generate_early_warning_indicators(business_data: Dict[str, Any], failure_probability: float) -> List[Dict[str, Any]]:
        """Generate early warning indicators for startup failure"""
        indicators = []
        
        # Financial warning indicators
        if business_data.get('burn_rate', 0) > business_data.get('monthly_revenue', 0) * 2:
            indicators.append({
                'type': 'financial',
                'severity': 'high',
                'indicator': 'High burn rate relative to revenue',
                'recommendation': 'Reduce expenses or increase revenue urgently'
            })
        
        # Growth warning indicators
        if business_data.get('growth_rate', 0) < 0.05:  # Less than 5% monthly growth
            indicators.append({
                'type': 'growth',
                'severity': 'medium',
                'indicator': 'Low growth rate',
                'recommendation': 'Focus on customer acquisition and retention'
            })
        
        # Market warning indicators
        if business_data.get('customer_count', 0) < 100:
            indicators.append({
                'type': 'market',
                'severity': 'medium',
                'indicator': 'Limited customer base',
                'recommendation': 'Accelerate customer acquisition efforts'
            })
        
        # Team warning indicators
        if business_data.get('team_size', 1) < 2:
            indicators.append({
                'type': 'team',
                'severity': 'low',
                'indicator': 'Single founder risk',
                'recommendation': 'Consider bringing on co-founders or key hires'
            })
        
        return indicators
    
    @staticmethod
    def calculate_detailed_risk_factors(business_data: Dict[str, Any], historical_data: List[Dict] = None) -> Dict[str, Any]:
        """Calculate detailed risk factors for failure prediction"""
        risk_factors = {}
        
        # Financial risk factors
        runway_months = business_data.get('runway_months', 12)
        if runway_months < 6:
            risk_factors['financial_runway'] = {
                'score': 0.9,
                'description': 'Critical: Less than 6 months runway',
                'impact': 'high'
            }
        elif runway_months < 12:
            risk_factors['financial_runway'] = {
                'score': 0.6,
                'description': 'Warning: Less than 12 months runway',
                'impact': 'medium'
            }
        
        # Market risk factors
        market_size = business_data.get('market_size', 1000000)
        if market_size < 100000:
            risk_factors['market_size'] = {
                'score': 0.8,
                'description': 'Small addressable market',
                'impact': 'high'
            }
        
        # Competition risk factors
        competition_level = business_data.get('competition_level', 5)
        if competition_level > 8:
            risk_factors['high_competition'] = {
                'score': 0.7,
                'description': 'Highly competitive market',
                'impact': 'medium'
            }
        
        return risk_factors
    
    @staticmethod
    def determine_alert_level(failure_probability: float) -> str:
        """Determine alert level based on failure probability"""
        if failure_probability > 0.7:
            return 'critical'
        elif failure_probability > 0.5:
            return 'high'
        elif failure_probability > 0.3:
            return 'medium'
        else:
            return 'low'
    
    @staticmethod
    def generate_monitoring_schedule(alert_level: str) -> Dict[str, Any]:
        """Generate monitoring schedule based on alert level"""
        schedules = {
            'critical': {
                'frequency': 'weekly',
                'metrics': ['cash_flow', 'customer_churn', 'team_morale'],
                'review_interval_days': 7
            },
            'high': {
                'frequency': 'bi-weekly',
                'metrics': ['revenue_growth', 'customer_acquisition', 'burn_rate'],
                'review_interval_days': 14
            },
            'medium': {
                'frequency': 'monthly',
                'metrics': ['growth_metrics', 'market_position', 'team_performance'],
                'review_interval_days': 30
            },
            'low': {
                'frequency': 'quarterly',
                'metrics': ['strategic_goals', 'market_trends', 'competitive_position'],
                'review_interval_days': 90
            }
        }
        
        return schedules.get(alert_level, schedules['medium'])
    
    @staticmethod
    def calculate_next_review_date(alert_level: str) -> str:
        """Calculate next review date based on alert level"""
        schedule = PredictiveAnalyticsHelpers.generate_monitoring_schedule(alert_level)
        next_date = datetime.now() + timedelta(days=schedule['review_interval_days'])
        return next_date.isoformat()

    @staticmethod
    def analyze_current_market_conditions(business_data: Dict[str, Any], market_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Analyze current market conditions for timing optimization"""
        if not market_context:
            market_context = {}

        return {
            'market_growth_rate': market_context.get('market_growth_rate', 0.03),
            'economic_conditions': market_context.get('economic_conditions', 0.7),
            'industry_trends': market_context.get('industry_trends', ['digital_transformation', 'sustainability']),
            'regulatory_environment': market_context.get('regulatory_environment', 'stable'),
            'funding_availability': market_context.get('funding_availability', 0.6),
            'talent_availability': market_context.get('talent_availability', 0.5),
            'market_sentiment': market_context.get('market_sentiment', 'optimistic')
        }

    @staticmethod
    def determine_recommended_action(timing_scores: Dict[str, float]) -> str:
        """Determine recommended action based on timing scores"""
        max_score = max(timing_scores.values())
        recommended_action = max(timing_scores, key=timing_scores.get)

        if max_score > 0.8:
            return f"Strongly recommend: {recommended_action.replace('_', ' ').title()}"
        elif max_score > 0.6:
            return f"Consider: {recommended_action.replace('_', ' ').title()}"
        else:
            return "Wait and reassess market conditions"

    @staticmethod
    def analyze_individual_competitors(competitor_data: List[Dict]) -> List[Dict[str, Any]]:
        """Analyze individual competitors"""
        if not competitor_data:
            return [{
                'name': 'Generic Competitor',
                'threat_level': 0.5,
                'strengths': ['Market presence'],
                'weaknesses': ['Limited innovation'],
                'market_share': 0.1,
                'recent_activities': ['Product updates']
            }]

        analyzed_competitors = []
        for competitor in competitor_data:
            analysis = {
                'name': competitor.get('name', 'Unknown'),
                'threat_level': competitor.get('threat_level', 0.5),
                'strengths': competitor.get('strengths', ['Market presence']),
                'weaknesses': competitor.get('weaknesses', ['Unknown']),
                'market_share': competitor.get('market_share', 0.05),
                'recent_activities': competitor.get('recent_activities', []),
                'funding_status': competitor.get('funding_status', 'Unknown'),
                'growth_rate': competitor.get('growth_rate', 0.0)
            }
            analyzed_competitors.append(analysis)

        return analyzed_competitors

    @staticmethod
    def analyze_cac_by_channel(marketing_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze CAC by marketing channel"""
        channels = marketing_data.get('channels', {})

        if not channels:
            # Default channel analysis
            channels = {
                'organic_search': {'spend': 0, 'acquisitions': 10, 'cac': 0},
                'paid_search': {'spend': 5000, 'acquisitions': 50, 'cac': 100},
                'social_media': {'spend': 3000, 'acquisitions': 30, 'cac': 100},
                'content_marketing': {'spend': 2000, 'acquisitions': 25, 'cac': 80},
                'referrals': {'spend': 500, 'acquisitions': 15, 'cac': 33}
            }

        channel_analysis = {}
        for channel, data in channels.items():
            spend = data.get('spend', 0)
            acquisitions = data.get('acquisitions', 1)
            cac = spend / acquisitions if acquisitions > 0 else spend

            channel_analysis[channel] = {
                'cac': round(cac, 2),
                'efficiency_score': PredictiveAnalyticsHelpers._calculate_channel_efficiency(cac),
                'volume': acquisitions,
                'spend': spend,
                'recommendation': PredictiveAnalyticsHelpers._get_channel_recommendation(cac, acquisitions)
            }

        return channel_analysis

    @staticmethod
    def _calculate_channel_efficiency(cac: float) -> float:
        """Calculate channel efficiency score"""
        if cac < 50:
            return 0.9
        elif cac < 100:
            return 0.7
        elif cac < 200:
            return 0.5
        else:
            return 0.3

    @staticmethod
    def _get_channel_recommendation(cac: float, volume: int) -> str:
        """Get recommendation for marketing channel"""
        if cac < 50 and volume > 10:
            return "Scale up - high efficiency"
        elif cac < 100:
            return "Optimize and maintain"
        elif cac < 200:
            return "Optimize or reduce spend"
        else:
            return "Consider pausing or major optimization"

    @staticmethod
    def calculate_ltv_cac_ratio(predicted_cac: float, business_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate LTV:CAC ratio analysis"""
        # Estimate LTV based on business data
        monthly_revenue_per_customer = business_data.get('monthly_revenue_per_customer', 50)
        churn_rate = business_data.get('monthly_churn_rate', 0.05)
        gross_margin = business_data.get('gross_margin', 0.7)

        # Simple LTV calculation: (Monthly Revenue * Gross Margin) / Churn Rate
        ltv = (monthly_revenue_per_customer * gross_margin) / churn_rate if churn_rate > 0 else monthly_revenue_per_customer * 20

        ltv_cac_ratio = ltv / predicted_cac if predicted_cac > 0 else 0

        return {
            'ltv': round(ltv, 2),
            'cac': round(predicted_cac, 2),
            'ltv_cac_ratio': round(ltv_cac_ratio, 2),
            'ratio_category': PredictiveAnalyticsHelpers._categorize_ltv_cac_ratio(ltv_cac_ratio),
            'payback_period_months': round(predicted_cac / (monthly_revenue_per_customer * gross_margin), 1) if monthly_revenue_per_customer > 0 else 0,
            'recommendation': PredictiveAnalyticsHelpers._get_ltv_cac_recommendation(ltv_cac_ratio)
        }

    @staticmethod
    def _categorize_ltv_cac_ratio(ratio: float) -> str:
        """Categorize LTV:CAC ratio"""
        if ratio >= 3:
            return "Excellent"
        elif ratio >= 2:
            return "Good"
        elif ratio >= 1:
            return "Acceptable"
        else:
            return "Poor"

    @staticmethod
    def _get_ltv_cac_recommendation(ratio: float) -> str:
        """Get recommendation based on LTV:CAC ratio"""
        if ratio >= 3:
            return "Strong unit economics - consider scaling marketing"
        elif ratio >= 2:
            return "Healthy ratio - optimize for growth"
        elif ratio >= 1:
            return "Break-even - focus on improving LTV or reducing CAC"
        else:
            return "Unsustainable - urgent optimization needed"

    # Fallback methods for when ML models are not available
    @staticmethod
    def fallback_failure_prediction(business_data: Dict[str, Any]) -> Dict[str, Any]:
        """Fallback failure prediction when ML is not available"""
        return {
            'failure_probability': 0.3,
            'success_probability': 0.7,
            'confidence_score': 0.6,
            'alert_level': 'medium',
            'warning_indicators': [
                {
                    'type': 'general',
                    'severity': 'medium',
                    'indicator': 'Basic risk assessment needed',
                    'recommendation': 'Conduct comprehensive business analysis'
                }
            ],
            'risk_factors': {
                'general_risk': {
                    'score': 0.5,
                    'description': 'Standard startup risks apply',
                    'impact': 'medium'
                }
            },
            'early_warning_signals': ['Monitor key metrics regularly'],
            'prevention_recommendations': [
                'Focus on customer validation',
                'Maintain healthy cash flow',
                'Build strong team'
            ],
            'monitoring_schedule': PredictiveAnalyticsHelpers.generate_monitoring_schedule('medium'),
            'next_review_date': PredictiveAnalyticsHelpers.calculate_next_review_date('medium'),
            'benchmark_comparison': {'industry_average': 0.4, 'position': 'above_average'},
            'timestamp': datetime.now().isoformat(),
            'note': 'Basic analysis - ML models not available'
        }

    @staticmethod
    def fallback_timing_optimization(business_data: Dict[str, Any]) -> Dict[str, Any]:
        """Fallback timing optimization when ML is not available"""
        return {
            'timing_scores': {
                'launch_score': 0.6,
                'pivot_score': 0.4,
                'scale_score': 0.5
            },
            'recommended_action': 'Consider: Launch',
            'optimal_timing_windows': {
                'launch': 'Q2-Q3 typically optimal for most businesses',
                'pivot': 'Assess quarterly',
                'scale': 'After achieving product-market fit'
            },
            'market_conditions': PredictiveAnalyticsHelpers.analyze_current_market_conditions(business_data),
            'timing_recommendations': [
                'Validate market readiness',
                'Ensure product-market fit',
                'Secure adequate funding'
            ],
            'risk_factors': ['Market uncertainty', 'Competitive pressure'],
            'competitive_timing': 'Monitor competitor activities',
            'seasonal_factors': 'Consider industry seasonality',
            'readiness_assessment': 'Moderate readiness level',
            'confidence_level': 0.6,
            'timestamp': datetime.now().isoformat(),
            'note': 'Basic analysis - ML models not available'
        }

    @staticmethod
    def fallback_competitor_analysis(business_data: Dict[str, Any]) -> Dict[str, Any]:
        """Fallback competitor analysis when ML is not available"""
        return {
            'competitive_threat_score': 0.5,
            'threat_level': 'medium',
            'competitor_analysis': PredictiveAnalyticsHelpers.analyze_individual_competitors([]),
            'market_positioning': {
                'position': 'emerging',
                'differentiation': 'moderate',
                'market_share': 0.01
            },
            'competitive_gaps': ['Market research needed', 'Competitive intelligence required'],
            'competitive_advantages': ['Innovation potential', 'Agility'],
            'monitoring_alerts': ['Set up competitor tracking'],
            'strategic_recommendations': [
                'Conduct competitive analysis',
                'Identify unique value proposition',
                'Monitor market changes'
            ],
            'market_share_analysis': 'Detailed analysis requires competitor data',
            'differentiation_opportunities': ['Product innovation', 'Customer experience'],
            'competitive_timeline': 'Regular monitoring recommended',
            'next_analysis_date': (datetime.now() + timedelta(days=30)).isoformat(),
            'timestamp': datetime.now().isoformat(),
            'note': 'Basic analysis - ML models not available'
        }

    @staticmethod
    def fallback_cac_prediction(business_data: Dict[str, Any]) -> Dict[str, Any]:
        """Fallback CAC prediction when ML is not available"""
        industry_avg_cac = 150  # Default industry average

        return {
            'predicted_cac': industry_avg_cac,
            'confidence_score': 0.6,
            'cac_category': 'average',
            'channel_analysis': PredictiveAnalyticsHelpers.analyze_cac_by_channel({}),
            'cac_trends': {
                'trend': 'stable',
                'monthly_change': 0.0,
                'projection_3_months': industry_avg_cac
            },
            'optimization_recommendations': [
                'Track CAC by channel',
                'Optimize conversion rates',
                'Test new acquisition channels'
            ],
            'industry_benchmarks': {
                'industry_average': industry_avg_cac,
                'top_quartile': industry_avg_cac * 0.7,
                'bottom_quartile': industry_avg_cac * 1.5
            },
            'ltv_cac_analysis': PredictiveAnalyticsHelpers.calculate_ltv_cac_ratio(industry_avg_cac, business_data),
            'warning_indicators': ['Monitor CAC trends closely'],
            'cost_efficiency_score': 0.6,
            'recommended_budget_allocation': {
                'organic_search': 0.3,
                'paid_search': 0.25,
                'social_media': 0.2,
                'content_marketing': 0.15,
                'referrals': 0.1
            },
            'monitoring_metrics': ['CAC by channel', 'Conversion rates', 'LTV:CAC ratio'],
            'next_review_date': (datetime.now() + timedelta(days=30)).isoformat(),
            'timestamp': datetime.now().isoformat(),
            'note': 'Basic analysis - ML models not available'
        }
