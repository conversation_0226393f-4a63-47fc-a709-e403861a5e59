import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { Globe, Check, Loader2 } from 'lucide-react';

interface LanguageOption {
  code: string;
  name: string;
  nativeName: string;
  flag: string;
}

const AVAILABLE_LANGUAGES: LanguageOption[] = [
  {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸'
  },
  {
    code: 'ar',
    name: 'Arabic',
    nativeName: 'العربية',
    flag: '🇸🇦'
  }
];

interface LanguageSettingsProps {
  className?: string;
}

const LanguageSettings: React.FC<LanguageSettingsProps> = ({ className = '' }) => {
  const { t } = useTranslation();
  const { language, isRTL, changeLanguage } = useLanguage();
  const [isChanging, setIsChanging] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState(language);

  useEffect(() => {
    setSelectedLanguage(language);
  }, [language]);

  const handleLanguageChange = (langCode: string) => {
    if (langCode === selectedLanguage || isChanging) return;

    setIsChanging(true);
    try {
      changeLanguage(langCode);
      setSelectedLanguage(langCode);
    } catch (error) {
      console.error('Failed to change language:', error);
    } finally {
      setIsChanging(false);
    }
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
      <div className="flex items-center gap-3 mb-6">
        <Globe className="w-5 h-5 text-blue-600 dark:text-blue-400" />
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          {t('settings.language.title', 'Language Settings')}
        </h3>
      </div>

      <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
        {t('settings.language.description', 'Choose your preferred language for the interface. This setting will be saved to your profile.')}
      </p>

      <div className="space-y-3">
        {AVAILABLE_LANGUAGES.map((lang) => (
          <div
            key={lang.code}
            className={`
              relative flex items-center p-4 rounded-lg border cursor-pointer transition-all duration-200
              ${selectedLanguage === lang.code
                ? 'border-blue-500/50 glass-morphism'
                : 'border-glass-border glass-light hover:bg-glass-hover'
              }
              ${isChanging ? 'opacity-50 cursor-not-allowed' : ''}
            `}
            onClick={() => handleLanguageChange(lang.code)}
          >
            <div className={`flex items-center gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <span className="text-2xl">{lang.flag}</span>
              <div className={`flex-1 ${isRTL ? 'text-right' : 'text-left'}`}>
                <div className="font-medium text-gray-900 dark:text-white">
                  {lang.nativeName}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {lang.name}
                </div>
              </div>
            </div>

            {selectedLanguage === lang.code && (
              <div className={`absolute ${isRTL ? 'left-4' : 'right-4'} top-1/2 transform -translate-y-1/2`}>
                {isChanging ? (
                  <Loader2 className="w-5 h-5 text-blue-600 animate-spin" />
                ) : (
                  <Check className="w-5 h-5 text-blue-600" />
                )}
              </div>
            )}
          </div>
        ))}
      </div>

      <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
        <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
          {t('settings.language.currentSettings', 'Current Settings')}
        </h4>
        <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
          <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <span>{t('settings.language.currentLanguage', 'Language')}:</span>
            <span className="font-medium">
              {AVAILABLE_LANGUAGES.find(l => l.code === selectedLanguage)?.nativeName}
            </span>
          </div>
          <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <span>{t('settings.language.textDirection', 'Text Direction')}:</span>
            <span className="font-medium">
              {isRTL ? t('settings.language.rtl', 'Right-to-Left') : t('settings.language.ltr', 'Left-to-Right')}
            </span>
          </div>
        </div>
      </div>

      <div className="mt-4 text-xs text-gray-500 dark:text-gray-400">
        {t('settings.language.note', 'Note: Language changes are saved automatically and will persist across sessions.')}
      </div>
    </div>
  );
};

export default LanguageSettings;
