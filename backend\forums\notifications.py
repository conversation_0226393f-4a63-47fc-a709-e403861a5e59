from django.contrib.auth.models import User
from django.db.models.signals import post_save, m2m_changed
from django.dispatch import receiver
from django.urls import reverse
from .models import ForumThread, ForumPost

def create_notification(user, title, message, link, notification_type, related_object=None):
    """
    Create a notification for a user
    
    Args:
        user (User): The user to notify
        title (str): Notification title
        message (str): Notification message
        link (str): URL to link to
        notification_type (str): Type of notification
        related_object (obj, optional): Related object (thread, post, etc.)
    """
    # Check if notifications app is installed
    try:
        from notifications.models import Notification
        
        # Create notification
        Notification.objects.create(
            recipient=user,
            actor=related_object.author if hasattr(related_object, 'author') else None,
            verb=title,
            description=message,
            target=related_object,
            action_object=related_object,
            public=False,
            notification_type=notification_type,
            link=link
        )
    except ImportError:
        # Notifications app not installed, log the notification
        print(f"Notification for {user.username}: {title} - {message}")


@receiver(post_save, sender=ForumPost)
def notify_on_new_post(sender, instance, created, **kwargs):
    """Notify thread author and participants when a new post is created"""
    if not created:
        return
    
    post = instance
    thread = post.thread
    author = post.author
    
    # Don't notify the author of their own post
    if thread.author != author:
        # Notify thread author
        title = "New reply to your thread"
        message = f"{author.username} replied to your thread: {thread.title}"
        link = f"/forum/thread/{thread.id}"
        create_notification(
            user=thread.author,
            title=title,
            message=message,
            link=link,
            notification_type="forum_reply",
            related_object=post
        )
    
    # Notify other participants (excluding the thread author and post author)
    participants = User.objects.filter(
        forum_posts__thread=thread
    ).distinct().exclude(id__in=[thread.author.id, author.id])
    
    for participant in participants:
        title = "New activity in a thread you're participating in"
        message = f"{author.username} replied to: {thread.title}"
        link = f"/forum/thread/{thread.id}"
        create_notification(
            user=participant,
            title=title,
            message=message,
            link=link,
            notification_type="forum_activity",
            related_object=post
        )


@receiver(post_save, sender=ForumThread)
def notify_on_new_thread(sender, instance, created, **kwargs):
    """Notify topic followers when a new thread is created"""
    if not created:
        return
    
    thread = instance
    topic = thread.topic
    
    # In a real implementation, you would have a TopicFollower model
    # For now, we'll just notify the topic creator if it's not the same as the thread author
    if topic.created_by and topic.created_by != thread.author:
        title = "New thread in a topic you're following"
        message = f"{thread.author.username} created a new thread: {thread.title}"
        link = f"/forum/thread/{thread.id}"
        create_notification(
            user=topic.created_by,
            title=title,
            message=message,
            link=link,
            notification_type="forum_new_thread",
            related_object=thread
        )


@receiver(m2m_changed, sender=ForumPost.likes.through)
def notify_on_post_like(sender, instance, action, pk_set, **kwargs):
    """Notify post author when their post is liked"""
    if action != 'post_add':
        return
    
    post = instance
    author = post.author
    thread = post.thread
    
    # Get the users who liked the post
    for user_id in pk_set:
        try:
            liker = User.objects.get(id=user_id)
            
            # Don't notify if the author liked their own post
            if liker != author:
                title = "Someone liked your post"
                message = f"{liker.username} liked your post in: {thread.title}"
                link = f"/forum/thread/{thread.id}"
                create_notification(
                    user=author,
                    title=title,
                    message=message,
                    link=link,
                    notification_type="forum_like",
                    related_object=post
                )
        except User.DoesNotExist:
            pass


def notify_on_solution_marked(post):
    """Notify post author when their post is marked as a solution"""
    author = post.author
    thread = post.thread
    
    title = "Your post was marked as a solution"
    message = f"Your post in '{thread.title}' was marked as the solution"
    link = f"/forum/thread/{thread.id}"
    create_notification(
        user=author,
        title=title,
        message=message,
        link=link,
        notification_type="forum_solution",
        related_object=post
    )


def notify_on_thread_moderation(thread, status, moderator):
    """Notify thread author when their thread is moderated"""
    author = thread.author
    
    if status == 'approved':
        title = "Your thread was approved"
        message = f"Your thread '{thread.title}' has been approved by a moderator"
    else:
        title = "Your thread was rejected"
        message = f"Your thread '{thread.title}' has been rejected by a moderator"
    
    link = f"/forum/thread/{thread.id}"
    create_notification(
        user=author,
        title=title,
        message=message,
        link=link,
        notification_type="forum_moderation",
        related_object=thread
    )


def notify_on_post_moderation(post, status, moderator):
    """Notify post author when their post is moderated"""
    author = post.author
    thread = post.thread
    
    if status == 'approved':
        title = "Your post was approved"
        message = f"Your post in '{thread.title}' has been approved by a moderator"
    else:
        title = "Your post was rejected"
        message = f"Your post in '{thread.title}' has been rejected by a moderator"
    
    link = f"/forum/thread/{thread.id}"
    create_notification(
        user=author,
        title=title,
        message=message,
        link=link,
        notification_type="forum_moderation",
        related_object=post
    )
