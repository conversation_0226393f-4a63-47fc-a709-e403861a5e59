import React, { useEffect, useState } from 'react';
import { AlertTriangle, X, Info, CheckCircle, XCircle } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';

interface ErrorDisplayProps {
  error?: string | null;
  type?: 'error' | 'warning' | 'info' | 'success';
  dismissible?: boolean;
  onDismiss?: () => void;
  className?: string;
}

/**
 * Centralized error display component with support for different message types
 */
const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  type = 'error',
  dismissible = true,
  onDismiss,
  className = ''
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [isVisible, setIsVisible] = useState(!!error);

  useEffect(() => {
    setIsVisible(!!error);
  }, [error]);

  const handleDismiss = () => {
    setIsVisible(false);
    if (onDismiss) {
      onDismiss();
    }
  };

  if (!error || !isVisible) {
    return null;
  }

  const getIcon = () => {
    switch (type) {
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      case 'info':
        return <Info className="w-5 h-5 text-blue-500" />;
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      default:
        return <AlertTriangle className="w-5 h-5 text-red-500" />;
    }
  };

  const getStyles = () => {
    const baseStyles = "p-4 rounded-lg border flex items-start gap-3 shadow-sm";
    
    switch (type) {
      case 'error':
        return `${baseStyles} bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-200`;
      case 'warning':
        return `${baseStyles} bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-900/20 dark:border-yellow-800 dark:text-yellow-200`;
      case 'info':
        return `${baseStyles} bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-200`;
      case 'success':
        return `${baseStyles} bg-green-50 border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-800 dark:text-green-200`;
      default:
        return `${baseStyles} bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-200`;
    }
  };

  return (
    <div className={`${getStyles()} ${className} ${isRTL ? 'flex-row-reverse' : ''}`}>
      <div className="flex-shrink-0">
        {getIcon()}
      </div>
      
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium">
          {error}
        </p>
      </div>
      
      {dismissible && (
        <button
          onClick={handleDismiss}
          className={`flex-shrink-0 p-1 rounded-md hover:bg-black/5 dark:hover:bg-white/5 transition-colors ${
            isRTL ? 'ml-2' : 'mr-2'
          }`}
          aria-label={t('common.dismiss', 'Dismiss')}
        >
          <X className="w-4 h-4" />
        </button>
      )}
    </div>
  );
};

/**
 * Hook to manage session storage error messages
 */
export const useSessionError = (key: string = 'accessError') => {
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const storedError = sessionStorage.getItem(key);
    if (storedError) {
      setError(storedError);
      sessionStorage.removeItem(key); // Clear after reading
    }
  }, [key]);

  const clearError = () => {
    setError(null);
    sessionStorage.removeItem(key);
  };

  return { error, clearError };
};

export default ErrorDisplay;
