import React, { useState, useEffect } from 'react';
import { X, FileText, DollarSign, Target } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../hooks/useLanguage';
import { useCreateBusinessPlan, useUpdateBusinessPlan } from '../../../../hooks/useBusinessPlans';
import { BusinessPlan } from '../../../../services/businessPlanApi';
import { Button } from '../../../ui';

interface BusinessPlanModalProps {
  isOpen: boolean;
  onClose: () => void;
  plan: BusinessPlan | null;
  isEditing: boolean;
}

interface FormData {
  business_idea: number;
  template: number;
  title: string;
  status: 'draft' | 'in_progress' | 'completed' | 'archived';
  content: {
    executive_summary?: string;
    market_analysis?: string;
    competitive_analysis?: string;
    marketing_strategy?: string;
    operations_plan?: string;
    management_team?: string;
    financial_projections?: string;
    funding_request?: string;
    risk_analysis?: string;
  };
}

const BusinessPlanModal: React.FC<BusinessPlanModalProps> = ({
  isOpen,
  onClose,
  plan,
  isEditing
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  const createPlan = useCreateBusinessPlan();
  const updatePlan = useUpdateBusinessPlan();

  const [formData, setFormData] = useState<FormData>({
    business_idea: 0,
    template: 1,
    title: '',
    status: 'draft',
    content: {}
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeSection, setActiveSection] = useState('basic');

  // Initialize form data when plan changes
  useEffect(() => {
    if (isEditing && plan) {
      setFormData({
        business_idea: plan.business_idea,
        template: plan.template,
        title: plan.title || '',
        status: plan.status,
        content: plan.content || {}
      });
    } else {
      // Reset form for create mode
      setFormData({
        business_idea: 0,
        template: 1,
        title: '',
        status: 'draft',
        content: {}
      });
    }
    setErrors({});
  }, [isEditing, plan]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    if (name.startsWith('content.')) {
      const contentField = name.replace('content.', '');
      setFormData(prev => ({
        ...prev,
        content: {
          ...prev.content,
          [contentField]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = t('validation.required', 'This field is required');
    }
    // Business idea is optional - users can create business plans from templates without business ideas

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      if (isEditing && plan) {
        await updatePlan.mutateAsync({
          id: plan.id,
          data: formData
        });
      } else {
        await createPlan.mutateAsync(formData);
      }
      onClose();
    } catch (error) {
      console.error('Failed to save business plan:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const sections = [
    { id: 'basic', label: t('businessPlan.basicInfo', 'Basic Information'), icon: FileText },
    { id: 'content', label: t('businessPlan.content', 'Plan Content'), icon: Target }
  ];

  const contentSections = [
    { key: 'executive_summary', label: t('businessPlan.executiveSummary', 'Executive Summary') },
    { key: 'market_analysis', label: t('businessPlan.marketAnalysis', 'Market Analysis') },
    { key: 'competitive_analysis', label: t('businessPlan.competitiveAnalysis', 'Competitive Analysis') },
    { key: 'marketing_strategy', label: t('businessPlan.marketingStrategy', 'Marketing Strategy') },
    { key: 'operations_plan', label: t('businessPlan.operationsPlan', 'Operations Plan') },
    { key: 'management_team', label: t('businessPlan.managementTeam', 'Management Team') },
    { key: 'financial_projections', label: t('businessPlan.financialProjections', 'Financial Projections') },
    { key: 'funding_request', label: t('businessPlan.fundingRequest', 'Funding Request') },
    { key: 'risk_analysis', label: t('businessPlan.riskAnalysis', 'Risk Analysis') }
  ];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-indigo-900/90 rounded-xl shadow-lg w-full max-w-4xl max-h-[90vh] overflow-hidden">
        <div className="flex h-full">
          {/* Sidebar */}
          <div className="w-64 bg-indigo-800/50 border-r border-indigo-700/50 p-4">
            <h3 className="text-lg font-semibold text-white mb-4">
              {isEditing ? t('businessPlan.editPlan', 'Edit Plan') : t('businessPlan.createPlan', 'Create Plan')}
            </h3>
            <nav className="space-y-2">
              {sections.map((section) => {
                const Icon = section.icon;
                return (
                  <button
                    key={section.id}
                    onClick={() => setActiveSection(section.id)}
                    className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-colors ${
                      activeSection === section.id
                        ? 'bg-purple-600 text-white'
                        : 'text-gray-300 hover:bg-indigo-700/50'
                    }`}
                  >
                    <Icon size={16} />
                    {section.label}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Main Content */}
          <div className="flex-1 flex flex-col">
            {/* Header */}
            <div className="flex justify-between items-center p-6 border-b border-indigo-700/50">
              <h2 className="text-xl font-bold text-white">
                {activeSection === 'basic' ? t('businessPlan.basicInfo', 'Basic Information') : 
                 t('businessPlan.content', 'Plan Content')}
              </h2>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-white"
              >
                <X size={20} />
              </button>
            </div>

            {/* Form Content */}
            <div className="flex-1 overflow-y-auto p-6">
              <form onSubmit={handleSubmit} className="space-y-6">
                {activeSection === 'basic' && (
                  <>
                    {/* Basic Information */}
                    <div>
                      <label className="block text-gray-300 mb-2">
                        {t('businessPlan.title', 'Plan Title')} *
                      </label>
                      <input
                        type="text"
                        name="title"
                        value={formData.title}
                        onChange={handleInputChange}
                        className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                        placeholder={t('businessPlan.titlePlaceholder', 'Enter business plan title')}
                      />
                      {errors.title && <p className="text-red-400 text-sm mt-1">{errors.title}</p>}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-gray-300 mb-2">
                          {t('businessPlan.businessIdea', 'Business Idea')} *
                        </label>
                        <input
                          type="number"
                          name="business_idea"
                          value={formData.business_idea}
                          onChange={handleInputChange}
                          className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                          placeholder="Business Idea ID"
                        />
                        {errors.business_idea && <p className="text-red-400 text-sm mt-1">{errors.business_idea}</p>}
                      </div>

                      <div>
                        <label className="block text-gray-300 mb-2">
                          {t('businessPlan.status', 'Status')}
                        </label>
                        <select
                          name="status"
                          value={formData.status}
                          onChange={handleInputChange}
                          className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                        >
                          <option value="draft">{t('businessPlan.status.draft', 'Draft')}</option>
                          <option value="in_progress">{t('businessPlan.status.inProgress', 'In Progress')}</option>
                          <option value="completed">{t('businessPlan.status.completed', 'Completed')}</option>
                          <option value="archived">{t('businessPlan.status.archived', 'Archived')}</option>
                        </select>
                      </div>
                    </div>
                  </>
                )}

                {activeSection === 'content' && (
                  <div className="space-y-6">
                    {contentSections.map((section) => (
                      <div key={section.key}>
                        <label className="block text-gray-300 mb-2">
                          {section.label}
                        </label>
                        <textarea
                          name={`content.${section.key}`}
                          value={formData.content[section.key as keyof typeof formData.content] || ''}
                          onChange={handleInputChange}
                          rows={4}
                          className="w-full px-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                          placeholder={t('businessPlan.sectionPlaceholder', `Enter ${section.label.toLowerCase()}...`)}
                        />
                      </div>
                    ))}
                  </div>
                )}
              </form>
            </div>

            {/* Footer */}
            <div className="flex justify-end gap-3 p-6 border-t border-indigo-700/50">
              <Button
                type="button"
                onClick={onClose}
                variant="secondary"
                disabled={isSubmitting}
              >
                {t('common.cancel', 'Cancel')}
              </Button>
              <Button
                type="submit"
                onClick={handleSubmit}
                disabled={isSubmitting}
              >
                {isSubmitting ? t('common.saving', 'Saving...') : 
                 isEditing ? t('common.update', 'Update') : t('common.create', 'Create')}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BusinessPlanModal;
