import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { LogIn } from 'lucide-react';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { useTranslation } from 'react-i18next';
import { RTLIcon, RTLText } from '../components/common';
import { login, clearError } from '../store/authSlice';
import { useLanguage } from '../hooks/useLanguage';
import { getPrimaryDashboardRoute } from '../utils/roleBasedRouting';
import { safeRedirect } from '../utils/authRedirectManager';

const LoginPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { isAuthenticated, isLoading, error, user } = useAppSelector(state => state.auth);
  const { language, isRTL } = useLanguage();

  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [formError, setFormError] = useState<string | null>(null);

  // Redirect if already authenticated (prevent redirect loops)
  React.useEffect(() => {
    if (isAuthenticated && user) {
      // Use role-based routing to determine the appropriate dashboard
      const dashboardRoute = getPrimaryDashboardRoute(user);

      // Only redirect if not already on the target route and safe to redirect
      if (window.location.pathname !== dashboardRoute) {
        const redirectSuccess = safeRedirect(dashboardRoute, navigate);
        if (redirectSuccess && process.env.NODE_ENV === 'development') {
          console.log('LoginPage: User already authenticated, redirecting to:', dashboardRoute);
        }
      }
    }
  }, [isAuthenticated, navigate, user]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    dispatch(clearError());
    setFormError(null);

    // Validate form
    if (!username.trim()) {
      setFormError(t('auth.usernameRequired'));
      return;
    }
    if (!password) {
      setFormError(t('auth.passwordRequired'));
      return;
    }

    try {
      if (process.env.NODE_ENV === 'development') {
        console.log('Attempting login with username:', username);
      }

      const resultAction = await dispatch(login({ username: username.trim(), password }));
      if (login.fulfilled.match(resultAction)) {
        // Use role-based routing to determine the appropriate dashboard
        const dashboardRoute = getPrimaryDashboardRoute(resultAction.payload);

        if (process.env.NODE_ENV === 'development') {
          console.log('Login successful, redirecting to:', dashboardRoute);
        }

        // Clear any previous error messages
        setFormError(null);

        // Navigate with replace to prevent back button issues
        const redirectSuccess = safeRedirect(dashboardRoute, navigate);
        if (!redirectSuccess && process.env.NODE_ENV === 'development') {
          console.warn('Login redirect was prevented due to redirect loop protection');
        }
      } else if (login.rejected.match(resultAction)) {
        // If we get here, there was an error but it was handled by the thunk
        const errorMessage = resultAction.payload as string || 'Login failed';
        setFormError(errorMessage);

        if (process.env.NODE_ENV === 'development') {
          console.error('Login rejected:', errorMessage);
        }
      }
    } catch (err) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Login failed:', err);
      }
      setFormError(err instanceof Error ? err.message : 'Login failed');
    }
  };

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center p-4 ${isRTL ? "flex-row-reverse" : ""}`}>
      <div className="max-w-md w-full">
        <div className="text-center mb-8">
          <Link to="/" className="inline-block">
            <h1 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-blue-400">
              {t('app.name')}
            </h1>
          </Link>
          <RTLText as="div" align="center" className="text-gray-300 mt-2">
            {t('auth.signInToAccount')}
          </RTLText>
        </div>

        <div className="bg-black/30 backdrop-blur-sm rounded-lg p-8 shadow-lg border border-white/20">
          <div className="text-center mb-6">
            <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full bg-purple-600/20 mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
              <RTLIcon icon={LogIn} size={28} className="text-purple-400" flipInRTL={true} />
            </div>
            <h2 className="text-2xl font-bold text-white">{t('auth.welcomeBack')}</h2>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            {(error || formError) && (
              <div className="bg-red-500/20 text-red-300 p-3 rounded-lg text-sm">
                {formError || error}
              </div>
            )}

            <div>
              <label htmlFor="username" className="block text-sm font-medium text-gray-300 mb-1">
                {t('auth.username')}
              </label>
              <input
                id="username"
                type="text"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                className="w-full px-4 py-2 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                placeholder={t('auth.enterUsername')}
                dir={language === 'ar' ? 'rtl' : 'ltr'}
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-300 mb-1">
                {t('auth.password')}
              </label>
              <input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-4 py-2 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                placeholder={t('auth.enterPassword')}
                dir={language === 'ar' ? 'rtl' : 'ltr'}
              />
            </div>

            <div className="pt-2">
              <button
                type="submit"
                disabled={isLoading}
                className="w-full px-4 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300 flex justify-center items-center"
              >
                {isLoading ? (
                  <span className={`inline-block w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin ${language === 'ar' ? 'ml-2' : 'mr-2'}`}></span>
                ) : (
                  <RTLIcon icon={LogIn} size={18} className={language === 'ar' ? 'ml-2' : 'mr-2'} flipInRTL={true} />
                )}
                {isLoading ? t('auth.signingIn') : t('auth.signIn')}
              </button>
            </div>
          </form>

          <div className="mt-6 text-center">
            <RTLText as="p" align="center" className="text-gray-300">
              {t('auth.noAccount')}{' '}
              <Link to="/register" className="text-purple-400 hover:text-purple-300 transition-colors">
                {t('auth.register')}
              </Link>
            </RTLText>
            <Link to="/" className="text-sm text-purple-400 hover:text-purple-300 block mt-4">
              {t('common.returnToHomepage')}
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
