import React from 'react';
import { Lightbulb } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';

interface AIRecommendationsProps {
  businessIdeaId: number;
  businessIdeaTitle: string;
}

const AIRecommendations: React.FC<AIRecommendationsProps> = ({ businessIdeaId, businessIdeaTitle }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  return (
    <div className="glass-light rounded-xl p-6 border shadow-lg">
      <div className={`flex items-center justify-between mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
        <h2 className="text-xl font-bold text-glass-primary flex items-center">
          <Lightbulb size={20} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
          {t('dashboard.aiRecommendations', 'AI-Powered Recommendations')}
        </h2>
      </div>
      
      <div className="text-center py-12">
        <div className="glass-light rounded-lg p-8 border">
          <Lightbulb size={48} className="mx-auto mb-4 text-purple-500" />
          <h3 className="text-lg font-medium mb-2 text-glass-primary">
            {t('dashboard.aiRecommendationsComingSoon', 'AI Recommendations Coming Soon')}
          </h3>
          <p className="text-glass-secondary">
            {t('dashboard.aiRecommendationsDescription', 'Get personalized AI-powered recommendations to help grow your business idea.')}
          </p>
        </div>
      </div>
    </div>
  );
};

export default AIRecommendations;
