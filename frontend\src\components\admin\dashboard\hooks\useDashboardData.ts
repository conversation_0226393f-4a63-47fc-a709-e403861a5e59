import { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '../../../../store/hooks';
import { fetchDashboardStats, fetchRecentActivity } from '../../../../store/adminSlice';

/**
 * Custom hook to fetch dashboard data (stats and recent activity)
 * @returns Object containing dashboard stats, recent activity, and loading state
 */
const useDashboardData = () => {
  const dispatch = useAppDispatch();
  const { dashboardStats, recentActivity, isLoading, error } = useAppSelector(state => state.admin);

  useEffect(() => {
    // Fetch dashboard stats and recent activity
    dispatch(fetchDashboardStats());
    dispatch(fetchRecentActivity());
  }, [dispatch]);

  return {
    stats: dashboardStats,
    recentActivity,
    isLoading,
    error
  };
};

export default useDashboardData;
