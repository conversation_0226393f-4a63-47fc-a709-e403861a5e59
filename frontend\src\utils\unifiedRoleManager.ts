/**
 * Unified Role Management System
 * 
 * This module consolidates all role-related functionality into a single, consistent system.
 * It replaces the scattered role checking functions across the codebase.
 */

import { User } from '../store/authSlice';

// Define all possible user roles in the system
export type UserRole = 'user' | 'admin' | 'super_admin' | 'mentor' | 'investor' | 'moderator';

// Define permission levels
export type PermissionLevel = 'read' | 'write' | 'moderate' | 'admin' | 'super_admin';

// Define dashboard types
export type DashboardType = 'user' | 'admin' | 'super_admin' | 'mentor' | 'investor' | 'moderator';

// Role hierarchy for priority determination (higher number = higher priority)
const ROLE_HIERARCHY: Record<UserRole, number> = {
  'super_admin': 6,
  'admin': 5,
  'moderator': 4,
  'mentor': 3,
  'investor': 2,
  'user': 1
};

// Permission hierarchy
const PERMISSION_HIERARCHY: Record<PermissionLevel, number> = {
  'super_admin': 5,
  'admin': 4,
  'moderate': 3,
  'write': 2,
  'read': 1
};

// Role to permission mapping
const ROLE_PERMISSIONS: Record<UserRole, PermissionLevel[]> = {
  'super_admin': ['super_admin', 'admin', 'moderate', 'write', 'read'],
  'admin': ['admin', 'moderate', 'write', 'read'],
  'moderator': ['moderate', 'write', 'read'],
  'mentor': ['write', 'read'],
  'investor': ['write', 'read'],
  'user': ['read']
};

// Development mode detection
const isDevelopment = () => import.meta.env?.DEV || window.location.hostname === 'localhost';

/**
 * Core class for unified role management
 */
export class UnifiedRoleManager {
  private user: User | null;

  constructor(user: User | null) {
    this.user = user;
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return !!this.user;
  }

  /**
   * Check if user is a Super Admin
   */
  isSuperAdmin(): boolean {
    if (!this.user) return false;

    // Check Django superuser flag
    if (this.user.is_superuser) {
      return true;
    }

    // Check if user has super_admin role
    const activeRoles = this.user.profile?.active_roles || [];
    return activeRoles.some(role => role.name === 'super_admin');
  }

  /**
   * Check if user is an Admin (includes super admin)
   */
  isAdmin(): boolean {
    if (!this.user) return false;

    // Super admin is also admin
    if (this.isSuperAdmin()) return true;

    // Check Django admin flag
    if (this.user.is_admin) return true;

    // Check if user has admin role
    const activeRoles = this.user.profile?.active_roles || [];
    return activeRoles.some(role => role.name === 'admin');
  }

  /**
   * Get all user roles
   */
  getUserRoles(): UserRole[] {
    if (!this.user) return ['user'];

    const roles: UserRole[] = [];

    // Add base user role for all authenticated users
    roles.push('user');

    // Check for super admin first (highest priority)
    if (this.isSuperAdmin()) {
      roles.push('super_admin');
      roles.push('admin'); // Super admin also has admin privileges
    } else if (this.isAdmin()) {
      roles.push('admin');
    }

    // Add roles from profile
    const activeRoles = this.user.profile?.active_roles || [];
    activeRoles.forEach(role => {
      const roleName = role.name as UserRole;
      if (roleName && !roles.includes(roleName)) {
        roles.push(roleName);
      }
    });

    return roles;
  }

  /**
   * Get the primary (highest priority) role
   */
  getPrimaryRole(): UserRole {
    const roles = this.getUserRoles();
    
    // Find the role with the highest hierarchy value
    let primaryRole: UserRole = 'user';
    let highestPriority = 0;

    roles.forEach(role => {
      const priority = ROLE_HIERARCHY[role] || 0;
      if (priority > highestPriority) {
        highestPriority = priority;
        primaryRole = role;
      }
    });

    return primaryRole;
  }

  /**
   * Check if user has a specific role
   */
  hasRole(roleName: UserRole): boolean {
    const roles = this.getUserRoles();
    return roles.includes(roleName);
  }

  /**
   * Check if user has any of the specified roles
   */
  hasAnyRole(roleNames: UserRole[]): boolean {
    // Super admin has all roles
    if (this.isSuperAdmin()) return true;

    const userRoles = this.getUserRoles();
    return roleNames.some(role => userRoles.includes(role));
  }

  /**
   * Check if user has all of the specified roles
   */
  hasAllRoles(roleNames: UserRole[]): boolean {
    const userRoles = this.getUserRoles();
    return roleNames.every(role => userRoles.includes(role));
  }

  /**
   * Get user permissions
   */
  getUserPermissions(): PermissionLevel[] {
    const roles = this.getUserRoles();
    const permissions = new Set<PermissionLevel>();

    roles.forEach(role => {
      const rolePermissions = ROLE_PERMISSIONS[role] || [];
      rolePermissions.forEach(permission => permissions.add(permission));
    });

    return Array.from(permissions);
  }

  /**
   * Check if user has a specific permission
   */
  hasPermission(permission: PermissionLevel): boolean {
    const permissions = this.getUserPermissions();
    return permissions.includes(permission);
  }

  /**
   * Get the highest permission level
   */
  getHighestPermissionLevel(): PermissionLevel {
    const permissions = this.getUserPermissions();
    
    let highestPermission: PermissionLevel = 'read';
    let highestValue = 0;

    permissions.forEach(permission => {
      const value = PERMISSION_HIERARCHY[permission] || 0;
      if (value > highestValue) {
        highestValue = value;
        highestPermission = permission;
      }
    });

    return highestPermission;
  }

  /**
   * Get dashboard type based on user role
   */
  getDashboardType(): DashboardType {
    const primaryRole = this.getPrimaryRole();
    return primaryRole as DashboardType;
  }

  /**
   * Get role-specific dashboard route
   */
  getDashboardRoute(): string {
    const dashboardType = this.getDashboardType();

    const routes: Record<DashboardType, string> = {
      'super_admin': '/super_admin',
      'admin': '/admin',
      'moderator': '/dashboard/moderator',
      'mentor': '/dashboard/mentor',
      'investor': '/dashboard/investor',
      'user': '/dashboard'
    };

    return routes[dashboardType];
  }

  /**
   * Check if user can access a specific route
   */
  canAccessRoute(requiredRoles: UserRole[], requiredPermissions?: PermissionLevel[]): boolean {
    if (!this.isAuthenticated()) return false;

    // Super admin can access everything
    if (this.isSuperAdmin()) return true;

    // Check role requirements
    if (requiredRoles.length > 0 && !this.hasAnyRole(requiredRoles)) {
      return false;
    }

    // Check permission requirements
    if (requiredPermissions && requiredPermissions.length > 0) {
      const hasRequiredPermission = requiredPermissions.some(permission => 
        this.hasPermission(permission)
      );
      if (!hasRequiredPermission) {
        return false;
      }
    }

    return true;
  }

  /**
   * Debug information (development only)
   */
  getDebugInfo(): any {
    if (!isDevelopment()) return null;

    return {
      user: this.user?.username || 'anonymous',
      isAuthenticated: this.isAuthenticated(),
      isSuperAdmin: this.isSuperAdmin(),
      isAdmin: this.isAdmin(),
      roles: this.getUserRoles(),
      primaryRole: this.getPrimaryRole(),
      permissions: this.getUserPermissions(),
      highestPermission: this.getHighestPermissionLevel(),
      dashboardType: this.getDashboardType(),
      dashboardRoute: this.getDashboardRoute(),
      rawUserData: {
        is_admin: this.user?.is_admin,
        is_superuser: this.user?.is_superuser,
        active_roles: this.user?.profile?.active_roles?.map(r => r.name) || [],
        primary_role: this.user?.profile?.primary_role?.name || null
      }
    };
  }
}

/**
 * Factory function to create a role manager instance
 */
export function createRoleManager(user: User | null): UnifiedRoleManager {
  return new UnifiedRoleManager(user);
}

/**
 * Convenience functions for common role checks
 */
export function isSuperAdmin(user: User | null): boolean {
  return createRoleManager(user).isSuperAdmin();
}

export function isAdmin(user: User | null): boolean {
  return createRoleManager(user).isAdmin();
}

export function hasRole(user: User | null, roleName: UserRole): boolean {
  return createRoleManager(user).hasRole(roleName);
}

export function hasAnyRole(user: User | null, roleNames: UserRole[]): boolean {
  return createRoleManager(user).hasAnyRole(roleNames);
}

export function getUserRoles(user: User | null): UserRole[] {
  return createRoleManager(user).getUserRoles();
}

export function getPrimaryRole(user: User | null): UserRole {
  return createRoleManager(user).getPrimaryRole();
}

export function getDashboardRoute(user: User | null): string {
  return createRoleManager(user).getDashboardRoute();
}

export function canAccessRoute(user: User | null, requiredRoles: UserRole[], requiredPermissions?: PermissionLevel[]): boolean {
  return createRoleManager(user).canAccessRoute(requiredRoles, requiredPermissions);
}
