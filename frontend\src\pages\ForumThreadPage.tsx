import React, { useEffect, useState, useRef } from 'react';
import { useParams, Link, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { fetchThread, createPost, likePost, unlikePost, markAsSolution, clearCurrentThread } from '../store/forumSlice';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import {
  MessageSquare,
  ArrowLeft,
  ThumbsUp,
  ThumbsDown,
  CheckCircle,
  Clock,
  Eye,
  User,
  Tag as TagIcon,
  Pin,
  Lock,
  Flag,
  Share,
  Award,
  Send
} from 'lucide-react';
import { formatDistanceToNow, format } from 'date-fns';
import { RichTextEditor } from '../components/common';
import { useTranslation } from 'react-i18next';

const ForumThreadPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const { id } = useParams<{ id: string }>();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { currentThread, loading, error } = useAppSelector((state) => state.forum);
  const { user } = useAppSelector((state) => state.auth);
  const [replyContent, setReplyContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const replyEditorRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (id) {
      dispatch(fetchThread(parseInt(id)));
    }

    return () => {
      dispatch(clearCurrentThread());
    };
  }, [dispatch, id]);

  const handleReplySubmit = async () => {
    if (!replyContent.trim() || !user || !currentThread || isSubmitting) return;

    setIsSubmitting(true);
    try {
      await dispatch(createPost({
        thread: currentThread.id,
        author_id: user.id,
        content: replyContent
      })).unwrap();

      setReplyContent('');
      // Scroll to the bottom to see the new post
      window.scrollTo(0, document.body.scrollHeight);
    } catch (error) {
      console.error("Failed to submit reply:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleLikePost = async (postId: number, isLiked: boolean) => {
    if (!user) {
      navigate('/login');
      return;
    }

    try {
      if (isLiked) {
        await dispatch(unlikePost(postId)).unwrap();
      } else {
        await dispatch(likePost(postId)).unwrap();
      }
    } catch (error) {
      console.error('Failed to like/unlike post:', error);
    }
  };

  const handleMarkAsSolution = async (postId: number) => {
    if (!user || !currentThread) return;

    // Only thread author or admin can mark a solution
    if (user.id !== currentThread.author.id && !user.is_admin) {
      alert('Only the thread author or an admin can mark a solution');
      return;
    }

    try {
      await dispatch(markAsSolution(postId)).unwrap();
    } catch (error) {
      console.error("Failed to mark post as solution:", error);
    }
  };

  const handleReportPost = (postId: number) => {
    // Implement report functionality
    alert(`Reporting post ${postId} - This feature is coming soon`);
  };

  const handleShareThread = () => {
    // Implement share functionality
    navigator.clipboard.writeText(window.location.href);
    alert('Thread URL copied to clipboard');
  };

  // Function to get level color
  const getLevelColor = (level: string) => {
    switch (level) {
      case 'Newcomer':
        return 'text-gray-400';
      case 'Contributor':
        return 'text-blue-400';
      case 'Active Member':
        return 'text-green-400';
      case 'Expert':
        return 'text-purple-400';
      case 'Mentor':
        return 'text-yellow-400';
      case 'Community Leader':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };

  return (
    <div className={`min-h-screen flex flex-col bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
      {/* Forum thread page uses its own navigation - no main navbar needed */}

      <main className={`flex-grow container mx-auto px-4 py-8 ${isRTL ? "flex-row-reverse" : ""}`}>
        {/* Breadcrumb */}
        <div className="mb-6">
          <Link to="/forum" className={`text-purple-400 hover:text-purple-300 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <ArrowLeft size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
            Back to Forums
          </Link>
        </div>

        {loading && !currentThread ? (
          <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
            <div className={`flex justify-center items-center h-40 ${isRTL ? "flex-row-reverse" : ""}`}>
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
            </div>
          </div>
        ) : error ? (
          <div className="bg-red-900/30 border border-red-800 rounded-lg p-4 mb-6">
            <div className="text-red-300">{error}</div>
          </div>
        ) : currentThread ? (
          <>
            {/* Thread Header */}
            <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50 mb-6">
              <div className={`flex items-center justify-between mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                  <h1 className="text-2xl font-bold">{currentThread.title}</h1>
                  {currentThread.is_pinned && (
                    <Pin size={18} className={`ml-2 text-yellow-400 ${isRTL ? "space-x-reverse" : ""}`} />
                  )}
                  {currentThread.is_locked && (
                    <Lock size={18} className={`ml-2 text-red-400 ${isRTL ? "space-x-reverse" : ""}`} />
                  )}
                </div>
                <div className={`flex space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <button
                    onClick={handleShareThread}
                    className="p-2 rounded-full hover:bg-indigo-800/50 transition-colors"
                    title={t("common.share.thread", t("common.share.thread", "Share Thread"))}
                  >
                    <Share size={18} />
                  </button>
                </div>
              </div>

              <div className={`flex items-center mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                <div className={`w-10 h-10 rounded-full bg-indigo-800/50 flex items-center justify-center mr-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <span className="text-sm font-medium">{currentThread.author.username.charAt(0).toUpperCase()}</span>
                </div>
                <div>
                  <div className="text-sm">
                    <span className="text-purple-400 font-medium">{currentThread.author.username}</span>
                    <span className="text-gray-500 mx-1">•</span>
                    <span className={`text-xs ${getLevelColor(currentThread.author_reputation.level)}`}>
                      {currentThread.author_reputation.level}
                    </span>
                    <span className="text-gray-500 mx-1">•</span>
                    <span className="text-gray-400">{format(new Date(currentThread.created_at), 'PPP')}</span>
                  </div>
                </div>
              </div>

              <div
                className="prose prose-invert max-w-none mb-4"
                dangerouslySetInnerHTML={{ __html: currentThread.content }}
              />

              <div className={`flex flex-wrap gap-2 mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                {currentThread.tags.map(tag => (
                  <span
                    key={tag.id}
                    className={`inline-flex items-center px-2 py-1 rounded-full text-xs bg-indigo-800/50 text-indigo-300 ${isRTL ? "flex-row-reverse" : ""}`}
                  >
                    <TagIcon size={12} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                    {tag.name}
                  </span>
                ))}
              </div>

              <div className={`flex flex-wrap gap-4 text-sm text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                  <MessageSquare size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                  <span>{currentThread.post_count} Replies</span>
                </div>
                <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                  <Eye size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                  <span>{currentThread.views} Views</span>
                </div>
                <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                  <Clock size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                  <span>Last reply: {formatDistanceToNow(new Date(currentThread.last_activity), { addSuffix: true })}</span>
                </div>
              </div>
            </div>

            {/* Posts/Replies */}
            {currentThread.posts && currentThread.posts.length > 0 ? (
              <div className="space-y-6 mb-8">
                <h2 className="text-xl font-semibold">t("common.replies", "Replies")</h2>

                {currentThread.posts.map((post) => (
                  <div
                    key={post.id}
                    className={`bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border ${
                      post.is_solution ? 'border-green-600/50' : 'border-indigo-800/50'}
                    }`}
                  >
                    {post.is_solution && (
                      <div className={`flex items-center mb-4 bg-green-900/30 text-green-300 p-2 rounded-lg ${isRTL ? "flex-row-reverse" : ""}`}>
                        <CheckCircle size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                        <span>t("common.this.reply.has", "This reply has been marked as the solution")</span>
                      </div>
                    )}

                    <div className={`flex items-center mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <div className={`w-10 h-10 rounded-full bg-indigo-800/50 flex items-center justify-center mr-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <span className="text-sm font-medium">{post.author.username.charAt(0).toUpperCase()}</span>
                      </div>
                      <div>
                        <div className="text-sm">
                          <span className="text-purple-400 font-medium">{post.author.username}</span>
                          <span className="text-gray-500 mx-1">•</span>
                          <span className={`text-xs ${getLevelColor(post.author_reputation.level)}`}>
                            {post.author_reputation.level}
                          </span>
                          <span className="text-gray-500 mx-1">•</span>
                          <span className="text-gray-400">{format(new Date(post.created_at), 'PPP')}</span>
                        </div>
                      </div>
                    </div>

                    <div
                      className="prose prose-invert max-w-none mb-6"
                      dangerouslySetInnerHTML={{ __html: post.content }}
                    />

                    <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
                      <div className={`flex items-center space-x-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <button
                          onClick={() => handleLikePost(post.id, post.is_liked)}
                          className={`flex items-center space-x-1 ${
                            post.is_liked ? 'text-purple-400' : 'text-gray-400 hover:text-purple-300'}
                          }`}
                          disabled={!user}
                        >
                          {post.is_liked ? (
                            <ThumbsUp size={18} className="fill-current" />
                          ) : (
                            <ThumbsUp size={18} />
                          )}
                          <span>{post.like_count}</span>
                        </button>

                        {!post.is_solution && user && (
                          currentThread.author.id === user.id || user.is_admin ? (
                            <button
                              onClick={() => handleMarkAsSolution(post.id)}
                              className={`flex items-center space-x-1 text-gray-400 hover:text-green-400 ${isRTL ? "flex-row-reverse" : ""}`}
                              title={t("common.mark.as.solution", t("common.mark.as.solution", "Mark as solution"))}
                            >
                              <Award size={18} />
                              <span>t("common.mark.as.solution", "Mark as Solution")</span>
                            </button>
                          ) : null
                        )}

                        <button
                          onClick={() => handleReportPost(post.id)}
                          className={`flex items-center space-x-1 text-gray-400 hover:text-red-400 ${isRTL ? "flex-row-reverse" : ""}`}
                          title={t("common.report.post", t("common.report.post", "Report post"))}
                        >
                          <Flag size={18} />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50 text-center mb-8">
                <div className="text-gray-400">t("common.no.replies.yet", "No replies yet. Be the first to reply!")</div>
              </div>
            )}

            {/* Reply Form */}
            {!currentThread.is_locked ? (
              <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
                <h2 className="text-xl font-semibold mb-4">t("common.post.a.reply", "Post a Reply")</h2>

                {user ? (
                  <>
                    <div ref={replyEditorRef} className="mb-4">
                      <RichTextEditor
                        value={replyContent}
                        onChange={setReplyContent}
                        placeholder={t("common.write.your.reply", "Write your reply here...")}
                      />
                    </div>

                    <div className={`flex justify-end ${isRTL ? "flex-row-reverse" : ""}`}>
                      <button
                        onClick={handleReplySubmit}
                        disabled={!replyContent.trim() || isSubmitting}
                        className={`px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300 flex items-center disabled:opacity-50 disabled:cursor-not-allowed ${isRTL ? "flex-row-reverse" : ""}`}
                      >
                        {isSubmitting ? (
                          <>
                            <div className={`animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2 ${isRTL ? "space-x-reverse" : ""}`}></div>
                            Posting...
                          </>
                        ) : (
                          <>
                            <Send size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                            Post Reply
                          </>
                        )}
                      </button>
                    </div>
                  </>
                ) : (
                  <div className="text-center py-4">
                    <div className="text-gray-400 mb-4">t("common.you.need.to", "You need to be logged in to reply.")</div>
                    <Link
                      to="/login"
                      className={`px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300 inline-flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                    >
                      <User size={18} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                      Log In to Reply
                    </Link>
                  </div>
                )}
              </div>
            ) : (
              <div className="bg-red-900/30 backdrop-blur-sm rounded-lg p-6 border border-red-800/50 text-center">
                <div className={`flex items-center justify-center mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <Lock size={24} className={`text-red-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                  <h2 className="text-xl font-semibold">t("common.thread.locked", "Thread Locked")</h2>
                </div>
                <div className="text-gray-400">t("common.this.thread.has", "This thread has been locked and is no longer accepting replies.")</div>
              </div>
            )}
          </>
        ) : (
          <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50 text-center">
            <p className="text-gray-400">t("common.thread.not.found", "Thread not found.")</p>
          </div>
        )}
      </main>

      <Footer />
    </div>
  );
};

export default ForumThreadPage;
