import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Key, Globe, Activity, Clock, TrendingUp, Alert<PERSON>riangle,
  CheckCircle, RefreshCw, Eye, EyeOff, Copy, Plus,
  Trash2, Edit, Shield, BarChart3
} from 'lucide-react';
import AuthenticatedLayout from '../../layout/AuthenticatedLayout';
import { getAuthToken } from '../../../services/api';

interface APIKey {
  id: string;
  name: string;
  key: string;
  permissions: string[];
  is_active: boolean;
  created_at: string;
  last_used: string | null;
  usage_count: number;
  rate_limit: number;
}

interface APIStats {
  total_requests_24h: number;
  successful_requests: number;
  failed_requests: number;
  avg_response_time: number;
  top_endpoints: Array<{
    endpoint: string;
    requests: number;
    avg_response_time: number;
  }>;
}

const APIManagement: React.FC = () => {
  const { t } = useTranslation();
  const [apiKeys, setApiKeys] = useState<APIKey[]>([]);
  const [apiStats, setApiStats] = useState<APIStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'keys' | 'stats' | 'endpoints'>('keys');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [visibleKeys, setVisibleKeys] = useState<Record<string, boolean>>({});
  const [newKeyData, setNewKeyData] = useState({
    name: '',
    permissions: [] as string[],
    rate_limit: 1000
  });

  useEffect(() => {
    fetchAPIData();
  }, []);

  const fetchAPIData = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/superadmin/system/api_management/', {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setApiKeys(data.api_keys || []);
        setApiStats(data.statistics || null);
      } else {
        console.error('Failed to fetch API data:', response.status);
        setApiKeys([]);
        setApiStats(null);
      }
    } catch (error) {
      console.error('Failed to fetch API data:', error);
      setApiKeys([]);
      setApiStats(null);
    } finally {
      setLoading(false);
    }
  };

  const toggleKeyVisibility = (keyId: string) => {
    setVisibleKeys(prev => ({ ...prev, [keyId]: !prev[keyId] }));
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  const createAPIKey = async () => {
    try {
      const response = await fetch('/api/superadmin/system/create_api_key/', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newKeyData.name,
          permissions: newKeyData.permissions,
          rate_limit: newKeyData.rate_limit
        })
      });

      if (response.ok) {
        const data = await response.json();
        setApiKeys(prev => [...prev, data.api_key]);
        setShowCreateModal(false);
        setNewKeyData({ name: '', permissions: [], rate_limit: 1000 });
      } else {
        console.error('Failed to create API key:', response.status);
      }
    } catch (error) {
      console.error('Failed to create API key:', error);
    }
  };

  const toggleKeyStatus = async (keyId: string) => {
    try {
      setApiKeys(prev => prev.map(key => 
        key.id === keyId ? { ...key, is_active: !key.is_active } : key
      ));
    } catch (error) {
      console.error('Failed to toggle key status:', error);
    }
  };

  const deleteAPIKey = async (keyId: string) => {
    if (window.confirm('Are you sure you want to delete this API key?')) {
      try {
        setApiKeys(prev => prev.filter(key => key.id !== keyId));
      } catch (error) {
        console.error('Failed to delete API key:', error);
      }
    }
  };

  const maskKey = (key: string) => {
    if (key.length <= 8) return key;
    return key.substring(0, 8) + '...' + key.substring(key.length - 4);
  };

  const getStatusColor = (isActive: boolean) => {
    return isActive ? 'text-green-400' : 'text-red-400';
  };

  const getStatusIcon = (isActive: boolean) => {
    return isActive ? <CheckCircle className="w-4 h-4" /> : <AlertTriangle className="w-4 h-4" />;
  };

  const tabs = [
    { id: 'keys', name: 'API Keys', icon: <Key className="w-4 h-4" /> },
    { id: 'stats', name: 'Statistics', icon: <BarChart3 className="w-4 h-4" /> },
    { id: 'endpoints', name: 'Endpoints', icon: <Globe className="w-4 h-4" /> }
  ];

  const renderAPIKeys = () => (
    <div className="space-y-6">
      {/* Create Key Button */}
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-white">API Keys</h3>
        <button
          onClick={() => setShowCreateModal(true)}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
        >
          <Plus className="w-4 h-4" />
          Create API Key
        </button>
      </div>

      {/* API Keys List */}
      <div className="space-y-4">
        {apiKeys.map((apiKey) => (
          <div key={apiKey.id} className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <h4 className="font-medium text-white">{apiKey.name}</h4>
                  <div className={`flex items-center gap-1 ${getStatusColor(apiKey.is_active)}`}>
                    {getStatusIcon(apiKey.is_active)}
                    <span className="text-sm">{apiKey.is_active ? 'Active' : 'Inactive'}</span>
                  </div>
                </div>
                
                <div className="flex items-center gap-2 mb-3">
                  <code className="bg-gray-700 px-3 py-1 rounded text-sm text-gray-300">
                    {visibleKeys[apiKey.id] ? apiKey.key : maskKey(apiKey.key)}
                  </code>
                  <button
                    onClick={() => toggleKeyVisibility(apiKey.id)}
                    className="text-gray-400 hover:text-white"
                  >
                    {visibleKeys[apiKey.id] ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </button>
                  <button
                    onClick={() => copyToClipboard(apiKey.key)}
                    className="text-gray-400 hover:text-white"
                  >
                    <Copy className="w-4 h-4" />
                  </button>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-gray-400">Permissions:</span>
                    <div className="text-white">{apiKey.permissions.join(', ')}</div>
                  </div>
                  <div>
                    <span className="text-gray-400">Usage Count:</span>
                    <div className="text-white">{apiKey.usage_count.toLocaleString()}</div>
                  </div>
                  <div>
                    <span className="text-gray-400">Rate Limit:</span>
                    <div className="text-white">{apiKey.rate_limit}/hour</div>
                  </div>
                  <div>
                    <span className="text-gray-400">Last Used:</span>
                    <div className="text-white">
                      {apiKey.last_used ? new Date(apiKey.last_used).toLocaleDateString() : 'Never'}
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-2 ml-4">
                <button
                  onClick={() => toggleKeyStatus(apiKey.id)}
                  className={`px-3 py-1 rounded text-sm transition-colors ${
                    apiKey.is_active 
                      ? 'bg-red-600 hover:bg-red-700 text-white' 
                      : 'bg-green-600 hover:bg-green-700 text-white'
                  }`}
                >
                  {apiKey.is_active ? 'Disable' : 'Enable'}
                </button>
                <button
                  onClick={() => deleteAPIKey(apiKey.id)}
                  className="p-2 text-red-400 hover:text-red-300 hover:bg-red-900/20 rounded"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderStats = () => (
    <div className="space-y-6">
      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <div className="flex items-center gap-2 mb-2">
            <Activity className="w-5 h-5 text-blue-400" />
            <span className="text-sm text-gray-400">Total Requests (24h)</span>
          </div>
          <div className="text-2xl font-bold text-white">{apiStats?.total_requests_24h?.toLocaleString() || 0}</div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <div className="flex items-center gap-2 mb-2">
            <CheckCircle className="w-5 h-5 text-green-400" />
            <span className="text-sm text-gray-400">Successful Requests</span>
          </div>
          <div className="text-2xl font-bold text-white">{apiStats?.successful_requests?.toLocaleString() || 0}</div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <div className="flex items-center gap-2 mb-2">
            <AlertTriangle className="w-5 h-5 text-red-400" />
            <span className="text-sm text-gray-400">Failed Requests</span>
          </div>
          <div className="text-2xl font-bold text-white">{apiStats?.failed_requests?.toLocaleString() || 0}</div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <div className="flex items-center gap-2 mb-2">
            <Clock className="w-5 h-5 text-yellow-400" />
            <span className="text-sm text-gray-400">Avg Response Time</span>
          </div>
          <div className="text-2xl font-bold text-white">{apiStats?.avg_response_time?.toFixed(1) || 0}s</div>
        </div>
      </div>

      {/* Top Endpoints */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">Top API Endpoints</h3>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b border-gray-700">
                <th className="text-left py-2 text-gray-400">Endpoint</th>
                <th className="text-right py-2 text-gray-400">Requests</th>
                <th className="text-right py-2 text-gray-400">Avg Response Time</th>
              </tr>
            </thead>
            <tbody>
              {apiStats?.top_endpoints?.map((endpoint, index) => (
                <tr key={index} className="border-b border-gray-800">
                  <td className="py-2 text-white font-mono">{endpoint.endpoint}</td>
                  <td className="py-2 text-right text-white">{endpoint.requests.toLocaleString()}</td>
                  <td className="py-2 text-right text-white">{endpoint.avg_response_time.toFixed(1)}s</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  return (
    <AuthenticatedLayout>
      <div className="text-white">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-2">
            <Key className="w-8 h-8 text-blue-400" />
            <h1 className="text-3xl font-bold text-white">
              {t('superAdmin.api.title', 'API Management')}
            </h1>
          </div>
          <p className="text-gray-400">
            {t('superAdmin.api.subtitle', 'Manage API keys, monitor usage, and track performance')}
          </p>
        </div>

        {/* Controls */}
        <div className="flex justify-between items-center mb-8">
          <div className="text-sm text-gray-400">
            {apiKeys.length} API keys • {apiKeys.filter(k => k.is_active).length} active
          </div>
          <button
            onClick={fetchAPIData}
            disabled={loading}
            className="flex items-center gap-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors disabled:opacity-50"
          >
            {loading ? <RefreshCw className="w-4 h-4 animate-spin" /> : <RefreshCw className="w-4 h-4" />}
            Refresh
          </button>
        </div>

        {/* Tabs */}
        <div className="flex flex-wrap gap-2 mb-8 border-b border-gray-700">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center gap-2 px-4 py-2 rounded-t-lg transition-colors ${
                activeTab === tab.id
                  ? 'bg-blue-600 text-white border-b-2 border-blue-400'
                  : 'text-gray-400 hover:text-white hover:bg-gray-800'
              }`}
            >
              {tab.icon}
              {tab.name}
            </button>
          ))}
        </div>

        {/* Content */}
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <RefreshCw className="w-8 h-8 animate-spin text-blue-400" />
            <span className="ml-2 text-gray-400">Loading API data...</span>
          </div>
        ) : (
          <>
            {activeTab === 'keys' && renderAPIKeys()}
            {activeTab === 'stats' && renderStats()}
            {activeTab === 'endpoints' && (
              <div className="text-center py-12">
                <Globe className="w-16 h-16 text-gray-500 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-white mb-2">API Endpoints Documentation</h3>
                <p className="text-gray-400">Detailed API endpoints documentation would go here</p>
              </div>
            )}
          </>
        )}

        {/* Create API Key Modal */}
        {showCreateModal && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md">
              <h3 className="text-lg font-semibold text-white mb-4">Create New API Key</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Name</label>
                  <input
                    type="text"
                    value={newKeyData.name}
                    onChange={(e) => setNewKeyData(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                    placeholder="e.g., Mobile App"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Rate Limit (per hour)</label>
                  <input
                    type="number"
                    value={newKeyData.rate_limit}
                    onChange={(e) => setNewKeyData(prev => ({ ...prev, rate_limit: parseInt(e.target.value) }))}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Permissions</label>
                  <div className="space-y-2">
                    {['read', 'write', 'admin'].map(permission => (
                      <label key={permission} className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          checked={newKeyData.permissions.includes(permission)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setNewKeyData(prev => ({ ...prev, permissions: [...prev.permissions, permission] }));
                            } else {
                              setNewKeyData(prev => ({ ...prev, permissions: prev.permissions.filter(p => p !== permission) }));
                            }
                          }}
                          className="rounded border-gray-600 bg-gray-700 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="text-gray-300 capitalize">{permission}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>

              <div className="flex gap-3 mt-6">
                <button
                  onClick={createAPIKey}
                  disabled={!newKeyData.name || newKeyData.permissions.length === 0}
                  className="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 rounded-lg transition-colors"
                >
                  Create Key
                </button>
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </AuthenticatedLayout>
  );
};

export default APIManagement;
