import React from 'react';
import { Calendar, MapPin, Clock, Users, ExternalLink } from 'lucide-react';
import { LazyImage } from '../../ui';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../../../store/hooks';
import { RTLText, RTLFlex, RTLIcon } from '../../common';
export interface EventCardProps {
  id: number;
  title: string;
  date: string;
  time: string;
  location: string;
  type: string;
  image: string;
  attendees: number;
  isAttending: boolean;
  isVirtual: boolean;
  virtualLink?: string;
  onAttend: (id: number) => void;
  onUnattend: (id: number) => void;
}

const EventCard: React.FC<EventCardProps> = ({ id,
  title,
  date,
  time,
  location,
  type,
  image,
  attendees,
  isAttending,
  isVirtual,
  virtualLink,
  onAttend,
  onUnattend
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { language } = useAppSelector(state => state.language);
  const { isAuthenticated } = useAppSelector(state => state.auth);

  return (
    <div className="rounded-xl overflow-hidden shadow-lg hover:shadow-purple-500/10 transition-all duration-300 group glass-light border">
      <div className="h-48 overflow-hidden relative">
        <LazyImage
          src={image || 'https://images.pexels.com/photos/2599244/pexels-photo-2599244.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'}
          alt={title}
          className="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-500"
          placeholderHeight="100%"
          placeholderWidth="100%"
          placeholderClassName="bg-indigo-900/50"
          threshold={0.1}
        />
        <div className="absolute top-0 right-0 m-3">
          <span className="px-3 py-1 text-sm font-medium bg-purple-500/80 backdrop-blur-sm text-white rounded-full">
            {type}
          </span>
        </div>
        {isAttending && (
          <div className="absolute bottom-0 left-0 right-0 bg-green-500/80 text-white text-center py-1 text-sm font-medium">
            {t('home.events.youreAttending')}
          </div>
        )}
      </div>
      <div className="p-6">
        <RTLText as="h3" className="text-xl font-semibold mb-3 text-glass-primary">{title}</RTLText>
        <div className="space-y-2 mb-4">
          <RTLFlex className="items-center text-glass-secondary">
            <RTLIcon icon={Calendar} size={16} className="text-purple-400" />
            <RTLText as="span">{date}</RTLText>
          </RTLFlex>
          <RTLFlex className="items-center text-glass-secondary">
            <RTLIcon icon={Clock} size={16} className="text-purple-400" />
            <RTLText as="span">{time}</RTLText>
          </RTLFlex>
          <RTLFlex className="items-center text-glass-secondary">
            <RTLIcon icon={MapPin} size={16} className="text-purple-400" />
            <RTLText as="span">{location} {isVirtual && '(Virtual)'}</RTLText>
          </RTLFlex>
          <RTLFlex className="items-center text-glass-secondary">
            <RTLIcon icon={Users} size={16} className="text-purple-400" />
            <RTLText as="span">{attendees} {t('home.events.attendees')}</RTLText>
          </RTLFlex>
        </div>
        <div className={`flex space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
          {isVirtual && virtualLink && (
            <a
              href={virtualLink}
              target="_blank"
              rel="noopener noreferrer"
              className={`inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full text-sm font-medium hover:shadow-glow transition-all duration-300 ${isRTL ? "flex-row-reverse" : ""}`}
            >
              {t('home.events.join')} <RTLIcon icon={ExternalLink} size={14} />
            </a>
          )}

          {isAuthenticated && (
            isAttending ? (
              <button
                onClick={() => onUnattend(id)}
                className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 glass-light text-glass-primary hover:glass-accent ${isRTL ? "flex-row-reverse" : ""}`}
              >
                {t('home.events.cancelAttendance')}
              </button>
            ) : (
              <button
                onClick={() => onAttend(id)}
                className={`inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full text-sm font-medium hover:shadow-glow transition-all duration-300 ${isRTL ? "flex-row-reverse" : ""}`}
              >
                {t('home.events.attend')}
              </button>
            )
          )}
        </div>
      </div>
    </div>
  );
};

export default EventCard;
