import React from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';

/**
 * Debug component to test sidebar translations and RTL layout
 */
const SidebarDebug: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { isRTL, toggleLanguage } = useLanguage();

  const testKeys = [
    'dashboard.title',
    'businessPlans.title', 
    'incubator.title',
    'events.title',
    'resources.title',
    'templates.title',
    'analytics.title',
    'navigation.ai',
    'navigation.content',
    'auth.logout',
    'common.collapse',
    'common.expand',
    'app.name',
    'app.tagline'
  ];

  return (
    <div className={`p-6 bg-gray-100 min-h-screen ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">Sidebar Translation Debug</h1>
        
        <div className="mb-6">
          <p><strong>Current Language:</strong> {i18n.language}</p>
          <p><strong>RTL Mode:</strong> {isRTL ? 'Yes' : 'No'}</p>
          <button 
            onClick={toggleLanguage}
            className="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Switch to {isRTL ? 'English' : 'Arabic'}
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-white p-4 rounded shadow">
            <h2 className="text-lg font-semibold mb-4">Translation Test</h2>
            <div className="space-y-2">
              {testKeys.map(key => (
                <div key={key} className="flex justify-between items-center p-2 border-b">
                  <span className="text-sm text-gray-600">{key}:</span>
                  <span className="font-medium">{t(key)}</span>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-white p-4 rounded shadow">
            <h2 className="text-lg font-semibold mb-4">RTL Layout Test</h2>
            <div className={`space-y-2 ${isRTL ? 'text-right' : 'text-left'}`}>
              <div className="p-3 bg-blue-100 rounded">
                <p>App Name: {t('app.name', 'Yasmeen AI')}</p>
              </div>
              <div className="p-3 bg-green-100 rounded">
                <p>Dashboard: {t('dashboard.title', 'Dashboard')}</p>
              </div>
              <div className="p-3 bg-purple-100 rounded">
                <p>Business Plans: {t('businessPlans.title', 'Business Plans')}</p>
              </div>
              <div className="p-3 bg-yellow-100 rounded">
                <p>AI Features: {t('navigation.ai', 'AI Features')}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-6 bg-white p-4 rounded shadow">
          <h2 className="text-lg font-semibold mb-4">Font Test</h2>
          <div className={`space-y-2 ${isRTL ? 'text-right' : 'text-left'}`}>
            <p style={{ fontFamily: 'Cairo, sans-serif' }}>
              Cairo Font: {t('app.name', 'ياسمين AI')} - {t('dashboard.title', 'لوحة التحكم')}
            </p>
            <p style={{ fontFamily: 'Amiri, serif' }}>
              Amiri Font: {t('app.name', 'ياسمين AI')} - {t('dashboard.title', 'لوحة التحكم')}
            </p>
            <p style={{ fontFamily: 'Noto Sans Arabic, sans-serif' }}>
              Noto Sans Arabic: {t('app.name', 'ياسمين AI')} - {t('dashboard.title', 'لوحة التحكم')}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SidebarDebug;
