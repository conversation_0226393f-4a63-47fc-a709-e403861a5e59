# Admin Dashboard Components

This directory contains components for the admin dashboard in the application.

## Component Structure

### Main Components

- **Dashboard**: The main dashboard component that composes all the sections
- **DashboardLayout**: The layout component for all admin pages
- **DashboardCharts**: Charts for displaying key metrics
- **EnhancedAnalytics**: Advanced analytics with more detailed charts and metrics

### Section Components

Located in the `components` directory:

- **WelcomeSection**: Displays a welcome message to the admin
- **StatsSection**: Shows key statistics
- **AnalyticsSection**: Displays analytics charts
- **ActivitySection**: Shows recent activity
- **UpcomingEventsSection**: Displays upcoming events
- **UserProfileSection**: User profile information

**Note**: Navigation is now handled by the UniversalSidebar component from `../../layout/UniversalSidebar.tsx` which provides unified navigation for all user types.

### Chart Components

Located in the `charts` directory:

- **ChartContainer**: Container for charts with consistent styling
- **StatCard**: Card for displaying statistics
- **UserProfileCard**: Card for displaying user profile information
- **chartConfig**: Configuration for charts including options and colors

### Custom Hooks

Located in the `hooks` directory:

- **useDashboardData**: Fetches dashboard data (stats and recent activity)
- **useUpcomingEvents**: Fetches upcoming events

## Usage

The Dashboard component is designed to be used as the main page for admin users after login. It provides an overview of platform activity and quick access to key features.

```tsx
import { Dashboard } from '../components/admin';

const AdminDashboardPage = () => {
  return <Dashboard />;
};
```

## Component Props

### DashboardLayout
- `children`: React nodes to render inside the layout
- `currentPage`: Current page identifier for highlighting in navigation

### DashboardCharts
- `stats`: Dashboard statistics object

### EnhancedAnalytics
- `stats`: Dashboard statistics object

### ChartContainer
- `title`: Chart title
- `children`: Chart component to render

### StatCard
- `title`: Card title
- `value`: Value to display
- `icon`: Icon to display
- `change`: Optional percentage change
- `suffix`: Optional suffix for the value

## Data Flow

1. The `useDashboardData` hook fetches dashboard data
2. The main `Dashboard` component receives this data
3. The data is passed to the appropriate section components
4. Each section component renders its part of the dashboard

## Chart Configuration

The `chartConfig.ts` file provides shared configuration for charts:

- `chartOptions`: Default options for line and bar charts
- `doughnutOptions`: Options for doughnut charts
- `chartColors`: Color palette for charts
