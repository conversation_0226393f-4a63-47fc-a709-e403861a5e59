from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.db import transaction
from django.db.models import Q


class Command(BaseCommand):
    help = 'Delete all test users while keeping essential admin accounts'

    def add_arguments(self, parser):
        parser.add_argument(
            '--keep-admins',
            nargs='+',
            type=int,
            default=[1, 3, 4, 29, 37],
            help='User IDs to keep (admin accounts)'
        )
        parser.add_argument(
            '--delete-all',
            action='store_true',
            help='Delete ALL users except superusers'
        )
        parser.add_argument(
            '--confirm',
            action='store_true',
            help='Confirm the deletion without prompting'
        )

    def handle(self, *args, **options):
        keep_admin_ids = options['keep_admins']
        delete_all = options['delete_all']
        confirm = options['confirm']

        # Show current users
        self.stdout.write(self.style.WARNING('Current users in database:'))
        for user in User.objects.all():
            admin_status = "Admin" if user.is_staff else "User"
            super_status = "Super" if user.is_superuser else ""
            self.stdout.write(f"ID: {user.id}, Username: {user.username}, Email: {user.email}, {admin_status} {super_status}")

        total_users = User.objects.count()
        self.stdout.write(f"\nTotal users: {total_users}")

        if delete_all:
            # Keep only superusers
            users_to_delete = User.objects.filter(is_superuser=False)
            self.stdout.write(self.style.WARNING(f"\nWill delete ALL non-superuser accounts ({users_to_delete.count()} users)"))
        else:
            # Keep specified admin accounts
            users_to_delete = User.objects.exclude(id__in=keep_admin_ids)
            self.stdout.write(self.style.WARNING(f"\nWill keep admin accounts with IDs: {keep_admin_ids}"))
            self.stdout.write(self.style.WARNING(f"Will delete {users_to_delete.count()} users"))

        # Show users that will be deleted
        self.stdout.write(self.style.ERROR('\nUsers to be deleted:'))
        for user in users_to_delete:
            self.stdout.write(f"- ID: {user.id}, Username: {user.username}, Email: {user.email}")

        # Confirm deletion
        if not confirm:
            response = input('\nAre you sure you want to delete these users? Type "yes" to confirm: ')
            if response.lower() != 'yes':
                self.stdout.write(self.style.SUCCESS('Operation cancelled.'))
                return

        # Perform deletion with proper foreign key handling
        self.stdout.write(self.style.WARNING('\nStarting deletion process...'))
        
        deleted_count = 0
        errors = []

        with transaction.atomic():
            for user in users_to_delete:
                try:
                    # Clean up related data before deletion
                    self._cleanup_user_data(user)
                    
                    username = user.username
                    user_id = user.id
                    user.delete()
                    
                    deleted_count += 1
                    self.stdout.write(f"✓ Deleted user: {username} (ID: {user_id})")
                    
                except Exception as e:
                    error_msg = f"✗ Failed to delete user {user.username} (ID: {user.id}): {str(e)}"
                    errors.append(error_msg)
                    self.stdout.write(self.style.ERROR(error_msg))

        # Show results
        remaining_users = User.objects.count()
        self.stdout.write(self.style.SUCCESS(f'\n✓ Deletion complete!'))
        self.stdout.write(f'Users deleted: {deleted_count}')
        self.stdout.write(f'Errors: {len(errors)}')
        self.stdout.write(f'Remaining users: {remaining_users}')

        if errors:
            self.stdout.write(self.style.ERROR('\nErrors encountered:'))
            for error in errors:
                self.stdout.write(error)

        # Show remaining users
        self.stdout.write(self.style.SUCCESS('\nRemaining users:'))
        for user in User.objects.all():
            admin_status = "Admin" if user.is_staff else "User"
            super_status = "Super" if user.is_superuser else ""
            self.stdout.write(f"ID: {user.id}, Username: {user.username}, Email: {user.email}, {admin_status} {super_status}")

    def _cleanup_user_data(self, user):
        """Clean up user-related data to avoid foreign key constraints"""
        try:
            # Clean up posts and comments
            from api.models import Post, Comment, Event, Resource
            
            # Update posts to remove author reference or delete them
            Post.objects.filter(author=user).delete()
            
            # Update comments to remove author reference or delete them
            Comment.objects.filter(author=user).delete()
            
            # Remove user from event attendees
            for event in Event.objects.filter(attendees=user):
                event.attendees.remove(user)
            
            # Transfer or delete events organized by user
            Event.objects.filter(organizer=user).delete()
            
            # Delete resources created by user
            Resource.objects.filter(author=user).delete()
            
        except Exception as e:
            # If cleanup fails, we'll try to delete the user anyway
            self.stdout.write(self.style.WARNING(f"Warning: Could not fully cleanup data for {user.username}: {str(e)}"))

        try:
            # Clean up forum data
            from forums.models import ForumThread, ForumPost, UserReputation, ReputationActivity
            
            # Delete forum threads and posts
            ForumThread.objects.filter(author=user).delete()
            ForumPost.objects.filter(author=user).delete()
            
            # Delete reputation data
            UserReputation.objects.filter(user=user).delete()
            ReputationActivity.objects.filter(user=user).delete()
            
        except Exception as e:
            self.stdout.write(self.style.WARNING(f"Warning: Could not cleanup forum data for {user.username}: {str(e)}"))

        try:
            # Clean up user profile
            if hasattr(user, 'profile'):
                user.profile.delete()
        except Exception as e:
            self.stdout.write(self.style.WARNING(f"Warning: Could not delete profile for {user.username}: {str(e)}"))
