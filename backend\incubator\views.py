from rest_framework import viewsets, permissions, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from django.db import models
from django_filters.rest_framework import DjangoFilterBackend
from django.contrib.auth.models import User

from .models import (
    BusinessIdea, ProgressUpdate, IncubatorResource, MentorshipApplication,
    InvestorProfile, FundingOpportunity, FundingApplication, Investment,
    MentorProfile, MentorExpertise, MentorshipMatch, MentorshipSession, MentorshipFeedback
)
from .serializers import (
    BusinessIdeaSerializer, ProgressUpdateSerializer,
    IncubatorResourceSerializer, MentorshipApplicationSerializer,
    InvestorProfileSerializer, FundingOpportunitySerializer,
    FundingApplicationSerializer, InvestmentSerializer,
    MentorProfileSerializer, MentorExpertiseSerializer, MentorshipMatchSerializer,
    MentorshipSessionSerializer, MentorshipFeedbackSerializer
)
from api.permissions import IsAdminUser

class BusinessIdeaViewSet(viewsets.ModelViewSet):
    queryset = BusinessIdea.objects.all().order_by('-created_at')
    serializer_class = BusinessIdeaSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['current_stage', 'moderation_status']
    search_fields = ['title', 'description', 'problem_statement', 'solution_description']
    ordering_fields = ['created_at', 'updated_at', 'title']

    def get_queryset(self):
        # For regular users, only show approved ideas or their own ideas
        if self.request.user.is_authenticated and not (self.request.user.is_staff or self.request.user.is_superuser):
            return BusinessIdea.objects.filter(
                models.Q(moderation_status='approved') |
                models.Q(owner=self.request.user)
            ).order_by('-created_at')
        return super().get_queryset()

    def perform_create(self, serializer):
        # If user is admin, auto-approve the idea
        if self.request.user.is_staff or self.request.user.is_superuser:
            serializer.save(
                owner_id=self.request.user.id,
                moderation_status='approved',
                moderated_by=self.request.user,
                moderated_at=timezone.now()
            )
        else:
            serializer.save(owner_id=self.request.user.id)

    def perform_update(self, serializer):
        # Check if user is the owner or admin
        idea = self.get_object()
        if not (self.request.user.is_staff or self.request.user.is_superuser or
                idea.owner == self.request.user):
            raise permissions.PermissionDenied("You can only edit your own business ideas")
        serializer.save()

    def perform_destroy(self, instance):
        # Check if user is the owner or admin
        if not (self.request.user.is_staff or self.request.user.is_superuser or
                instance.owner == self.request.user):
            raise permissions.PermissionDenied("You can only delete your own business ideas")
        instance.delete()

    @action(detail=True, methods=['post'])
    def add_collaborator(self, request, pk=None):
        idea = self.get_object()

        # Only the owner or admin can add collaborators
        if request.user != idea.owner and not (request.user.is_staff or request.user.is_superuser):
            return Response(
                {"error": "Only the owner or admin can add collaborators"},
                status=status.HTTP_403_FORBIDDEN
            )

        user_id = request.data.get('user_id')
        try:
            user = User.objects.get(id=user_id)
            idea.collaborators.add(user)
            return Response({"message": f"Added {user.username} as collaborator"})
        except User.DoesNotExist:
            return Response(
                {"error": "User not found"},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=True, methods=['post'])
    def remove_collaborator(self, request, pk=None):
        idea = self.get_object()

        # Only the owner or admin can remove collaborators
        if request.user != idea.owner and not (request.user.is_staff or request.user.is_superuser):
            return Response(
                {"error": "Only the owner or admin can remove collaborators"},
                status=status.HTTP_403_FORBIDDEN
            )

        user_id = request.data.get('user_id')
        try:
            user = User.objects.get(id=user_id)
            idea.collaborators.remove(user)
            return Response({"message": f"Removed {user.username} as collaborator"})
        except User.DoesNotExist:
            return Response(
                {"error": "User not found"},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=True, methods=['post'])
    def moderate(self, request, pk=None):
        # Only admin can moderate
        if not (request.user.is_staff or request.user.is_superuser):
            return Response(
                {"error": "Only administrators can moderate ideas"},
                status=status.HTTP_403_FORBIDDEN
            )

        idea = self.get_object()
        moderation_status = request.data.get('moderation_status')
        moderation_comment = request.data.get('moderation_comment', '')

        if moderation_status not in [status for status, _ in BusinessIdea.MODERATION_STATUS]:
            return Response(
                {"error": "Invalid moderation status"},
                status=status.HTTP_400_BAD_REQUEST
            )

        idea.moderation_status = moderation_status
        idea.moderation_comment = moderation_comment
        idea.moderated_by = request.user
        idea.moderated_at = timezone.now()
        idea.save()

        return Response({
            "message": f"Idea moderation status updated to {moderation_status}",
            "idea": BusinessIdeaSerializer(idea).data
        })

    @action(detail=True, methods=['get'])
    def progress_tracking(self, request, pk=None):
        """Get progress tracking data for a business idea"""
        idea = self.get_object()

        # Check if user has permission to view this idea's progress
        if not (request.user.is_staff or request.user.is_superuser or
                request.user == idea.owner or
                idea.collaborators.filter(id=request.user.id).exists()):
            return Response(
                {"error": "You don't have permission to view this idea's progress"},
                status=status.HTTP_403_FORBIDDEN
            )

        # Get all progress updates for this idea
        from django.db.models import Count
        from datetime import timedelta
        from django.utils import timezone

        progress_updates = ProgressUpdate.objects.filter(business_idea=idea).order_by('-created_at')

        # Calculate progress metrics
        total_updates = progress_updates.count()

        # Get updates by month (last 6 months)
        now = timezone.now()
        updates_by_month = {}

        for i in range(6):
            month_start = (now - timedelta(days=30 * i)).replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            month_end = (month_start.replace(month=month_start.month + 1) if month_start.month < 12
                        else month_start.replace(year=month_start.year + 1, month=1)) - timedelta(seconds=1)
            month_name = month_start.strftime('%B %Y')

            updates_by_month[month_name] = progress_updates.filter(
                created_at__gte=month_start,
                created_at__lte=month_end
            ).count()

        # Calculate update frequency (updates per month)
        update_frequency = 0
        if total_updates > 0:
            oldest_update = progress_updates.order_by('created_at').first()
            if oldest_update:
                months_diff = (now.year - oldest_update.created_at.year) * 12 + (now.month - oldest_update.created_at.month)
                if months_diff > 0:
                    update_frequency = total_updates / months_diff
                else:
                    update_frequency = total_updates  # All updates in the same month

        # Get recent updates
        recent_updates = progress_updates[:5]

        # Extract common challenges and achievements
        from collections import Counter
        import re

        # Simple word extraction for common themes
        all_achievements = " ".join([update.achievements for update in progress_updates])
        all_challenges = " ".join([update.challenges for update in progress_updates if update.challenges])

        # Simple word tokenization and counting
        achievement_words = re.findall(r'\b\w+\b', all_achievements.lower())
        challenge_words = re.findall(r'\b\w+\b', all_challenges.lower())

        # Filter out common stop words
        stop_words = {'the', 'and', 'a', 'to', 'of', 'in', 'that', 'is', 'was', 'for', 'on', 'with', 'as', 'by', 'at', 'from'}
        achievement_words = [word for word in achievement_words if word not in stop_words and len(word) > 3]
        challenge_words = [word for word in challenge_words if word not in stop_words and len(word) > 3]

        # Get most common words
        common_achievements = Counter(achievement_words).most_common(5)
        common_challenges = Counter(challenge_words).most_common(5)

        return Response({
            'total_updates': total_updates,
            'updates_by_month': updates_by_month,
            'update_frequency': round(update_frequency, 2),
            'recent_updates': [
                {
                    'id': update.id,
                    'title': update.title,
                    'created_at': update.created_at,
                    'created_by': update.created_by.username
                } for update in recent_updates
            ],
            'common_achievement_themes': [{'word': word, 'count': count} for word, count in common_achievements],
            'common_challenge_themes': [{'word': word, 'count': count} for word, count in common_challenges],
            'current_stage': idea.current_stage,
            'idea_age_days': (timezone.now() - idea.created_at).days
        })


class ProgressUpdateViewSet(viewsets.ModelViewSet):
    queryset = ProgressUpdate.objects.all().order_by('-created_at')
    serializer_class = ProgressUpdateSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['business_idea']
    search_fields = ['title', 'description', 'achievements', 'next_steps']
    ordering_fields = ['created_at']

    def get_queryset(self):
        # Filter by business idea if provided in query params
        business_idea_id = self.request.query_params.get('business_idea', None)
        if business_idea_id:
            return ProgressUpdate.objects.filter(business_idea_id=business_idea_id).order_by('-created_at')

        # For regular users, only show updates for approved ideas or their own ideas
        if self.request.user.is_authenticated and not (self.request.user.is_staff or self.request.user.is_superuser):
            return ProgressUpdate.objects.filter(
                models.Q(business_idea__moderation_status='approved') |
                models.Q(business_idea__owner=self.request.user) |
                models.Q(business_idea__collaborators=self.request.user)
            ).distinct().order_by('-created_at')

        return super().get_queryset()

    def perform_create(self, serializer):
        serializer.save(created_by_id=self.request.user.id)

    def perform_update(self, serializer):
        # Check if user is the creator or admin
        update = self.get_object()
        if not (self.request.user.is_staff or self.request.user.is_superuser or
                update.created_by == self.request.user):
            raise permissions.PermissionDenied("You can only edit your own progress updates")
        serializer.save()

    def perform_destroy(self, instance):
        # Check if user is the creator or admin
        if not (self.request.user.is_staff or self.request.user.is_superuser or
                instance.created_by == self.request.user):
            raise permissions.PermissionDenied("You can only delete your own progress updates")
        instance.delete()


class IncubatorResourceViewSet(viewsets.ModelViewSet):
    queryset = IncubatorResource.objects.all().order_by('-created_at')
    serializer_class = IncubatorResourceSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['resource_type', 'category']
    search_fields = ['title', 'description']
    ordering_fields = ['created_at', 'title']

    def perform_create(self, serializer):
        serializer.save(author_id=self.request.user.id)


class MentorshipApplicationViewSet(viewsets.ModelViewSet):
    queryset = MentorshipApplication.objects.all().order_by('-created_at')
    serializer_class = MentorshipApplicationSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['business_idea', 'status']
    search_fields = ['goals', 'specific_areas']

    def get_queryset(self):
        # For regular users, only show their own applications
        if self.request.user.is_authenticated and not (self.request.user.is_staff or self.request.user.is_superuser):
            return MentorshipApplication.objects.filter(applicant=self.request.user).order_by('-created_at')
        return super().get_queryset()

    def perform_create(self, serializer):
        serializer.save(applicant_id=self.request.user.id)

    @action(detail=True, methods=['post'])
    def update_status(self, request, pk=None):
        # Only admin can update status
        if not (request.user.is_staff or request.user.is_superuser):
            return Response(
                {"error": "Only administrators can update application status"},
                status=status.HTTP_403_FORBIDDEN
            )

        application = self.get_object()
        new_status = request.data.get('status')
        admin_notes = request.data.get('admin_notes', '')

        if new_status not in [status for status, _ in MentorshipApplication.STATUS_CHOICES]:
            return Response(
                {"error": "Invalid status"},
                status=status.HTTP_400_BAD_REQUEST
            )

        application.status = new_status
        application.admin_notes = admin_notes
        application.save()

        return Response({
            "message": f"Application status updated to {new_status}",
            "application": MentorshipApplicationSerializer(application).data
        })


class InvestorProfileViewSet(viewsets.ModelViewSet):
    queryset = InvestorProfile.objects.all().order_by('-created_at')
    serializer_class = InvestorProfileSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['investor_type', 'is_verified']
    search_fields = ['user__username', 'company_name', 'bio', 'investment_focus']

    def get_queryset(self):
        # For regular users, only show verified investors
        if self.request.user.is_authenticated and not (self.request.user.is_staff or self.request.user.is_superuser):
            return InvestorProfile.objects.filter(is_verified=True).order_by('-created_at')
        return super().get_queryset()

    def perform_create(self, serializer):
        serializer.save(user_id=self.request.user.id)

    @action(detail=True, methods=['post'])
    def verify(self, request, pk=None):
        # Only admin can verify investors
        if not (request.user.is_staff or request.user.is_superuser):
            return Response(
                {"error": "Only administrators can verify investors"},
                status=status.HTTP_403_FORBIDDEN
            )

        investor_profile = self.get_object()
        investor_profile.is_verified = True
        investor_profile.save()

        return Response({
            "message": "Investor profile verified successfully",
            "investor_profile": InvestorProfileSerializer(investor_profile).data
        })

    @action(detail=True, methods=['post'])
    def unverify(self, request, pk=None):
        # Only admin can unverify investors
        if not (request.user.is_staff or request.user.is_superuser):
            return Response(
                {"error": "Only administrators can unverify investors"},
                status=status.HTTP_403_FORBIDDEN
            )

        investor_profile = self.get_object()
        investor_profile.is_verified = False
        investor_profile.save()

        return Response({
            "message": "Investor profile unverified",
            "investor_profile": InvestorProfileSerializer(investor_profile).data
        })


class FundingOpportunityViewSet(viewsets.ModelViewSet):
    queryset = FundingOpportunity.objects.all().order_by('-created_at')
    serializer_class = FundingOpportunitySerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['funding_type', 'status']
    search_fields = ['title', 'description', 'eligibility_criteria']
    ordering_fields = ['created_at', 'application_deadline', 'amount']

    def get_queryset(self):
        # For regular users, only show active opportunities
        if self.request.user.is_authenticated and not (self.request.user.is_staff or self.request.user.is_superuser):
            return FundingOpportunity.objects.filter(status='active').order_by('-created_at')
        return super().get_queryset()

    def perform_create(self, serializer):
        serializer.save(provider_id=self.request.user.id)

    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        # Only admin or the provider can activate
        opportunity = self.get_object()
        if not (request.user.is_staff or request.user.is_superuser or request.user == opportunity.provider):
            return Response(
                {"error": "Only administrators or the provider can activate this opportunity"},
                status=status.HTTP_403_FORBIDDEN
            )

        opportunity.status = 'active'
        opportunity.save()

        return Response({
            "message": "Funding opportunity activated",
            "opportunity": FundingOpportunitySerializer(opportunity).data
        })

    @action(detail=True, methods=['post'])
    def close(self, request, pk=None):
        # Only admin or the provider can close
        opportunity = self.get_object()
        if not (request.user.is_staff or request.user.is_superuser or request.user == opportunity.provider):
            return Response(
                {"error": "Only administrators or the provider can close this opportunity"},
                status=status.HTTP_403_FORBIDDEN
            )

        opportunity.status = 'closed'
        opportunity.save()

        return Response({
            "message": "Funding opportunity closed",
            "opportunity": FundingOpportunitySerializer(opportunity).data
        })


class FundingApplicationViewSet(viewsets.ModelViewSet):
    queryset = FundingApplication.objects.all().order_by('-created_at')
    serializer_class = FundingApplicationSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['business_idea', 'funding_opportunity', 'status']
    search_fields = ['pitch', 'use_of_funds']

    def get_queryset(self):
        # For regular users, only show their own applications
        if self.request.user.is_authenticated and not (self.request.user.is_staff or self.request.user.is_superuser):
            return FundingApplication.objects.filter(applicant=self.request.user).order_by('-created_at')

        # For funding providers, show applications for their opportunities
        funding_opportunities = FundingOpportunity.objects.filter(provider=self.request.user)
        if funding_opportunities.exists():
            return FundingApplication.objects.filter(funding_opportunity__in=funding_opportunities).order_by('-created_at')

        return super().get_queryset()

    def perform_create(self, serializer):
        serializer.save(applicant_id=self.request.user.id)

    @action(detail=True, methods=['post'])
    def update_status(self, request, pk=None):
        # Only admin or the funding provider can update status
        application = self.get_object()
        if not (request.user.is_staff or request.user.is_superuser or request.user == application.funding_opportunity.provider):
            return Response(
                {"error": "Only administrators or the funding provider can update application status"},
                status=status.HTTP_403_FORBIDDEN
            )

        new_status = request.data.get('status')
        reviewer_notes = request.data.get('reviewer_notes', '')

        if new_status not in [status for status, _ in FundingApplication.STATUS_CHOICES]:
            return Response(
                {"error": "Invalid status"},
                status=status.HTTP_400_BAD_REQUEST
            )

        application.status = new_status
        application.reviewer_notes = reviewer_notes
        application.save()

        return Response({
            "message": f"Application status updated to {new_status}",
            "application": FundingApplicationSerializer(application).data
        })


class InvestmentViewSet(viewsets.ModelViewSet):
    queryset = Investment.objects.all().order_by('-created_at')
    serializer_class = InvestmentSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['business_idea', 'investment_type', 'status']
    search_fields = ['terms']

    def get_queryset(self):
        # For regular users, only show investments related to their business ideas or made by them
        if self.request.user.is_authenticated and not (self.request.user.is_staff or self.request.user.is_superuser):
            return Investment.objects.filter(
                models.Q(business_idea__owner=self.request.user) |
                models.Q(investor=self.request.user)
            ).order_by('-created_at')
        return super().get_queryset()

    def perform_create(self, serializer):
        serializer.save(investor_id=self.request.user.id)

    @action(detail=True, methods=['post'])
    def update_status(self, request, pk=None):
        # Only the business idea owner or the investor can update status
        investment = self.get_object()
        if not (request.user.is_staff or request.user.is_superuser or
                request.user == investment.business_idea.owner or
                request.user == investment.investor):
            return Response(
                {"error": "Only administrators, the business owner, or the investor can update investment status"},
                status=status.HTTP_403_FORBIDDEN
            )

        new_status = request.data.get('status')

        if new_status not in [status for status, _ in Investment.STATUS_CHOICES]:
            return Response(
                {"error": "Invalid status"},
                status=status.HTTP_400_BAD_REQUEST
            )

        investment.status = new_status

        # If status is completed, set completed_at
        if new_status == 'completed':
            investment.completed_at = timezone.now()

        investment.save()

        return Response({
            "message": f"Investment status updated to {new_status}",
            "investment": InvestmentSerializer(investment).data
        })


class MentorProfileViewSet(viewsets.ModelViewSet):
    queryset = MentorProfile.objects.all().order_by('-created_at')
    serializer_class = MentorProfileSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['availability', 'is_accepting_mentees', 'is_verified']
    search_fields = ['user__username', 'bio', 'company', 'position']

    def get_queryset(self):
        # For regular users, only show verified mentors who are accepting mentees
        if self.request.user.is_authenticated and not (self.request.user.is_staff or self.request.user.is_superuser):
            return MentorProfile.objects.filter(
                is_verified=True,
                is_accepting_mentees=True
            ).order_by('-created_at')
        return super().get_queryset()

    def perform_create(self, serializer):
        serializer.save(user_id=self.request.user.id)

    @action(detail=True, methods=['post'])
    def verify(self, request, pk=None):
        # Only admin can verify mentors
        if not (request.user.is_staff or request.user.is_superuser):
            return Response(
                {"error": "Only administrators can verify mentors"},
                status=status.HTTP_403_FORBIDDEN
            )

        mentor_profile = self.get_object()
        mentor_profile.is_verified = True
        mentor_profile.save()

        return Response({
            "message": "Mentor profile verified successfully",
            "mentor_profile": MentorProfileSerializer(mentor_profile).data
        })

    @action(detail=True, methods=['post'])
    def unverify(self, request, pk=None):
        # Only admin can unverify mentors
        if not (request.user.is_staff or request.user.is_superuser):
            return Response(
                {"error": "Only administrators can unverify mentors"},
                status=status.HTTP_403_FORBIDDEN
            )

        mentor_profile = self.get_object()
        mentor_profile.is_verified = False
        mentor_profile.save()

        return Response({
            "message": "Mentor profile unverified",
            "mentor_profile": MentorProfileSerializer(mentor_profile).data
        })

    @action(detail=True, methods=['post'])
    def toggle_accepting_mentees(self, request, pk=None):
        # Only the mentor or admin can toggle accepting mentees
        mentor_profile = self.get_object()
        if not (request.user.is_staff or request.user.is_superuser or request.user == mentor_profile.user):
            return Response(
                {"error": "Only administrators or the mentor can update this setting"},
                status=status.HTTP_403_FORBIDDEN
            )

        mentor_profile.is_accepting_mentees = not mentor_profile.is_accepting_mentees
        mentor_profile.save()

        return Response({
            "message": f"Now {'accepting' if mentor_profile.is_accepting_mentees else 'not accepting'} new mentees",
            "mentor_profile": MentorProfileSerializer(mentor_profile).data
        })


class MentorExpertiseViewSet(viewsets.ModelViewSet):
    queryset = MentorExpertise.objects.all().order_by('category', '-level')
    serializer_class = MentorExpertiseSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['mentor', 'category', 'level']
    search_fields = ['specific_expertise']

    def get_queryset(self):
        # Filter by mentor if provided in query params
        mentor_id = self.request.query_params.get('mentor_id', None)
        if mentor_id:
            return MentorExpertise.objects.filter(mentor_id=mentor_id).order_by('category', '-level')

        # For regular users, only show expertise of verified mentors
        if self.request.user.is_authenticated and not (self.request.user.is_staff or self.request.user.is_superuser):
            return MentorExpertise.objects.filter(mentor__is_verified=True).order_by('category', '-level')

        return super().get_queryset()

    def perform_create(self, serializer):
        # Check if the user is creating expertise for their own profile
        mentor_id = serializer.validated_data.get('mentor').id
        user_mentor_profile = getattr(self.request.user, 'mentor_profile', None)

        if not (self.request.user.is_staff or self.request.user.is_superuser) and (not user_mentor_profile or user_mentor_profile.id != mentor_id):
            raise permissions.PermissionDenied("You can only add expertise to your own mentor profile")

        serializer.save()


class MentorshipMatchViewSet(viewsets.ModelViewSet):
    queryset = MentorshipMatch.objects.all().order_by('-created_at')
    serializer_class = MentorshipMatchSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['mentor', 'mentee', 'business_idea', 'status']
    search_fields = ['goals', 'focus_areas']

    def get_queryset(self):
        # For regular users, only show matches where they are the mentor or mentee
        if self.request.user.is_authenticated and not (self.request.user.is_staff or self.request.user.is_superuser):
            user_mentor_profile = getattr(self.request.user, 'mentor_profile', None)

            if user_mentor_profile:
                return MentorshipMatch.objects.filter(
                    models.Q(mentor=user_mentor_profile) |
                    models.Q(mentee=self.request.user)
                ).order_by('-created_at')
            else:
                return MentorshipMatch.objects.filter(mentee=self.request.user).order_by('-created_at')

        return super().get_queryset()

    def perform_create(self, serializer):
        # Check if the user is an admin, the mentor, or the mentee
        mentor = serializer.validated_data.get('mentor')
        mentee = serializer.validated_data.get('mentee', self.request.user)

        if not (self.request.user.is_staff or self.request.user.is_superuser):
            user_mentor_profile = getattr(self.request.user, 'mentor_profile', None)

            if user_mentor_profile != mentor and self.request.user != mentee:
                raise permissions.PermissionDenied("You can only create matches where you are the mentor or mentee")

        serializer.save(mentee=mentee)

    @action(detail=True, methods=['post'])
    def update_status(self, request, pk=None):
        # Only the mentor, mentee, or admin can update status
        match = self.get_object()
        user_mentor_profile = getattr(request.user, 'mentor_profile', None)

        if not (request.user.is_staff or request.user.is_superuser or
                user_mentor_profile == match.mentor or
                request.user == match.mentee):
            return Response(
                {"error": "Only administrators, the mentor, or the mentee can update match status"},
                status=status.HTTP_403_FORBIDDEN
            )

        new_status = request.data.get('status')

        if new_status not in [status for status, _ in MentorshipMatch.STATUS_CHOICES]:
            return Response(
                {"error": "Invalid status"},
                status=status.HTTP_400_BAD_REQUEST
            )

        match.status = new_status

        # If status is completed or terminated, set end_date
        if new_status in ['completed', 'terminated']:
            match.end_date = timezone.now().date()

        match.save()

        return Response({
            "message": f"Match status updated to {new_status}",
            "match": MentorshipMatchSerializer(match).data
        })


class MentorshipSessionViewSet(viewsets.ModelViewSet):
    queryset = MentorshipSession.objects.all().order_by('-scheduled_at')
    serializer_class = MentorshipSessionSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['mentorship_match', 'status']
    search_fields = ['title', 'description']

    def get_queryset(self):
        # Filter by mentorship match if provided in query params
        match_id = self.request.query_params.get('match_id', None)
        if match_id:
            return MentorshipSession.objects.filter(mentorship_match_id=match_id).order_by('-scheduled_at')

        # For regular users, only show sessions where they are the mentor or mentee
        if self.request.user.is_authenticated and not (self.request.user.is_staff or self.request.user.is_superuser):
            user_mentor_profile = getattr(self.request.user, 'mentor_profile', None)

            if user_mentor_profile:
                return MentorshipSession.objects.filter(
                    models.Q(mentorship_match__mentor=user_mentor_profile) |
                    models.Q(mentorship_match__mentee=self.request.user)
                ).order_by('-scheduled_at')
            else:
                return MentorshipSession.objects.filter(mentorship_match__mentee=self.request.user).order_by('-scheduled_at')

        return super().get_queryset()

    def perform_create(self, serializer):
        # Check if the user is an admin, the mentor, or the mentee of the match
        match = serializer.validated_data.get('mentorship_match')

        if not (self.request.user.is_staff or self.request.user.is_superuser):
            user_mentor_profile = getattr(self.request.user, 'mentor_profile', None)

            if user_mentor_profile != match.mentor and self.request.user != match.mentee:
                raise permissions.PermissionDenied("You can only create sessions for matches where you are the mentor or mentee")

        serializer.save()

    @action(detail=True, methods=['post'])
    def update_status(self, request, pk=None):
        # Only the mentor, mentee, or admin can update status
        session = self.get_object()
        user_mentor_profile = getattr(request.user, 'mentor_profile', None)

        if not (request.user.is_staff or request.user.is_superuser or
                user_mentor_profile == session.mentorship_match.mentor or
                request.user == session.mentorship_match.mentee):
            return Response(
                {"error": "Only administrators, the mentor, or the mentee can update session status"},
                status=status.HTTP_403_FORBIDDEN
            )

        new_status = request.data.get('status')

        if new_status not in [status for status, _ in MentorshipSession.STATUS_CHOICES]:
            return Response(
                {"error": "Invalid status"},
                status=status.HTTP_400_BAD_REQUEST
            )

        session.status = new_status
        session.save()

        return Response({
            "message": f"Session status updated to {new_status}",
            "session": MentorshipSessionSerializer(session).data
        })


class MentorshipFeedbackViewSet(viewsets.ModelViewSet):
    queryset = MentorshipFeedback.objects.all().order_by('-created_at')
    serializer_class = MentorshipFeedbackSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['session', 'is_from_mentee', 'rating']
    search_fields = ['comments', 'areas_of_improvement']

    def get_queryset(self):
        # Filter by session if provided in query params
        session_id = self.request.query_params.get('session_id', None)
        if session_id:
            return MentorshipFeedback.objects.filter(session_id=session_id).order_by('-created_at')

        # For regular users, only show feedback they provided or feedback for their sessions that isn't private
        if self.request.user.is_authenticated and not (self.request.user.is_staff or self.request.user.is_superuser):
            user_mentor_profile = getattr(self.request.user, 'mentor_profile', None)

            if user_mentor_profile:
                return MentorshipFeedback.objects.filter(
                    models.Q(provided_by=self.request.user) |
                    (models.Q(session__mentorship_match__mentor=user_mentor_profile) & models.Q(is_private=False))
                ).order_by('-created_at')
            else:
                return MentorshipFeedback.objects.filter(
                    models.Q(provided_by=self.request.user) |
                    (models.Q(session__mentorship_match__mentee=self.request.user) & models.Q(is_private=False))
                ).order_by('-created_at')

        return super().get_queryset()

    def perform_create(self, serializer):
        # Check if the user is providing feedback for a session they participated in
        session = serializer.validated_data.get('session')
        is_from_mentee = serializer.validated_data.get('is_from_mentee')

        if not (self.request.user.is_staff or self.request.user.is_superuser):
            user_mentor_profile = getattr(self.request.user, 'mentor_profile', None)

            if is_from_mentee and self.request.user != session.mentorship_match.mentee:
                raise permissions.PermissionDenied("You can only provide mentee feedback for your own sessions")

            if not is_from_mentee and (not user_mentor_profile or user_mentor_profile != session.mentorship_match.mentor):
                raise permissions.PermissionDenied("You can only provide mentor feedback for your own sessions")

        serializer.save(provided_by_id=self.request.user.id)
