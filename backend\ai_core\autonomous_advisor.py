"""
Autonomous Business Advisor
AI that proactively guides users through their entrepreneurial journey
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from django.utils import timezone
from django.contrib.auth.models import User

# Use the new centralized AI service instead of deprecated ai_recommendations
from core.ai_service import ai_generate_intelligent_content, ai_is_available, get_ai_service
from incubator.models import BusinessIdea

logger = logging.getLogger(__name__)


class AutonomousBusinessAdvisor:
    """
    AI that proactively monitors user progress and provides intelligent guidance
    """
    
    def __init__(self):
        self.ai_service = get_ai_service()
    
    async def analyze_user_journey(self, user_context: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze user's entrepreneurial journey and provide insights"""
        try:
            user_id = user_context['user_id']
            business_stage = user_context.get('business_stage', 'ideation')
            pain_points = user_context.get('pain_points', [])
            engagement_level = user_context.get('engagement_level', 'low')
            
            # Analyze current situation
            situation_analysis = self._analyze_current_situation(user_context)
            
            # Generate proactive recommendations
            recommendations = await self._generate_proactive_recommendations(user_context)
            
            # Identify bottlenecks
            bottlenecks = self._identify_bottlenecks(user_context)
            
            # Calculate progress score
            progress_score = self._calculate_progress_score(user_context)
            
            # Generate next actions
            next_actions = await self._suggest_next_actions(user_context)
            
            # Create alerts and notifications
            alerts = self._generate_alerts(user_context)
            
            return {
                'user_id': user_id,
                'analysis_timestamp': datetime.now().isoformat(),
                'situation_analysis': situation_analysis,
                'progress_score': progress_score,
                'recommendations': recommendations,
                'bottlenecks': bottlenecks,
                'next_actions': next_actions,
                'alerts': alerts,
                'engagement_insights': self._analyze_engagement(user_context)
            }
            
        except Exception as e:
            logger.error(f"Error analyzing user journey: {e}")
            return {'error': str(e)}
    
    def _analyze_current_situation(self, user_context: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze user's current business situation"""
        business_ideas = user_context.get('business_ideas', [])
        business_stage = user_context.get('business_stage', 'ideation')
        latest_analysis = user_context.get('latest_analysis')
        
        situation = {
            'stage': business_stage,
            'ideas_count': len(business_ideas),
            'has_recent_analysis': latest_analysis is not None,
            'strengths': [],
            'weaknesses': [],
            'opportunities': []
        }
        
        # Identify strengths
        if len(business_ideas) > 1:
            situation['strengths'].append('Multiple business ideas showing entrepreneurial mindset')
        
        if latest_analysis and latest_analysis.get('viability_score', 0) > 7:
            situation['strengths'].append('High viability business concept')
        
        if user_context.get('engagement_level') == 'high':
            situation['strengths'].append('High platform engagement and commitment')
        
        # Identify weaknesses
        if not business_ideas:
            situation['weaknesses'].append('No business ideas developed yet')
        
        if not latest_analysis:
            situation['weaknesses'].append('No AI analysis performed')
        
        if user_context.get('profile_completion', 0) < 70:
            situation['weaknesses'].append('Incomplete profile limiting opportunities')
        
        # Identify opportunities
        if business_stage == 'concept':
            situation['opportunities'].append('Ready for market validation phase')
        
        if latest_analysis and latest_analysis.get('funding_readiness', 0) > 6:
            situation['opportunities'].append('Approaching funding readiness')
        
        return situation
    
    async def _generate_proactive_recommendations(self, user_context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate proactive recommendations based on user context"""
        recommendations = []
        
        business_stage = user_context.get('business_stage', 'ideation')
        pain_points = user_context.get('pain_points', [])
        latest_analysis = user_context.get('latest_analysis')
        
        # Stage-specific recommendations
        stage_recommendations = {
            'ideation': [
                {
                    'title': 'Develop Your First Business Idea',
                    'description': 'Start by creating a detailed business idea with problem statement and solution',
                    'priority': 'high',
                    'action_type': 'create_business_idea',
                    'estimated_time': '2-3 hours'
                }
            ],
            'concept': [
                {
                    'title': 'Run AI Business Analysis',
                    'description': 'Get comprehensive insights about your business viability and market opportunity',
                    'priority': 'high',
                    'action_type': 'run_analysis',
                    'estimated_time': '30 minutes'
                },
                {
                    'title': 'Complete Market Research',
                    'description': 'Research your target market, competitors, and customer needs',
                    'priority': 'medium',
                    'action_type': 'market_research',
                    'estimated_time': '4-6 hours'
                }
            ],
            'validation': [
                {
                    'title': 'Build Minimum Viable Product',
                    'description': 'Create a simple version of your product to test with customers',
                    'priority': 'high',
                    'action_type': 'build_mvp',
                    'estimated_time': '2-4 weeks'
                },
                {
                    'title': 'Conduct Customer Interviews',
                    'description': 'Interview potential customers to validate your assumptions',
                    'priority': 'high',
                    'action_type': 'customer_interviews',
                    'estimated_time': '1-2 weeks'
                }
            ]
        }
        
        recommendations.extend(stage_recommendations.get(business_stage, []))
        
        # Pain point specific recommendations
        if 'incomplete_profile' in pain_points:
            recommendations.append({
                'title': 'Complete Your Profile',
                'description': 'A complete profile helps with mentor matching and networking',
                'priority': 'medium',
                'action_type': 'complete_profile',
                'estimated_time': '15 minutes'
            })
        
        if 'outdated_analysis' in pain_points:
            recommendations.append({
                'title': 'Update Business Analysis',
                'description': 'Your last analysis is outdated. Run a fresh analysis for current insights',
                'priority': 'medium',
                'action_type': 'update_analysis',
                'estimated_time': '30 minutes'
            })
        
        if 'low_viability' in pain_points:
            recommendations.append({
                'title': 'Improve Business Viability',
                'description': 'Focus on addressing the weak points identified in your analysis',
                'priority': 'high',
                'action_type': 'improve_viability',
                'estimated_time': '1-2 weeks'
            })
        
        # AI-generated contextual recommendations
        if latest_analysis:
            ai_recommendations = await self._generate_ai_recommendations(user_context)
            recommendations.extend(ai_recommendations)
        
        # Sort by priority and return top recommendations
        priority_order = {'high': 3, 'medium': 2, 'low': 1}
        recommendations.sort(key=lambda x: priority_order.get(x['priority'], 0), reverse=True)
        
        return recommendations[:8]  # Return top 8 recommendations
    
    async def _generate_ai_recommendations(self, user_context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate AI-powered contextual recommendations"""
        try:
            # Build prompt for AI recommendation generation
            prompt = self._build_recommendation_prompt(user_context)

            # Use centralized AI service
            response = ai_generate_intelligent_content(
                'proactive_recommendations',
                {
                    'user_context': user_context,
                    'prompt': prompt
                },
                'en'
            )

            if response.get('success'):
                recommendations_text = response.get('data', {}).get('content', '')
                # Parse AI recommendations
                return self._parse_ai_recommendations(recommendations_text)
            return []

        except Exception as e:
            logger.error(f"Error generating AI recommendations: {e}")
            return []
    
    def _build_recommendation_prompt(self, user_context: Dict[str, Any]) -> str:
        """Build prompt for AI recommendation generation"""
        business_stage = user_context.get('business_stage', 'ideation')
        pain_points = user_context.get('pain_points', [])
        latest_analysis = user_context.get('latest_analysis')
        engagement_level = user_context.get('engagement_level', 'low')
        
        prompt = f"""
        As an expert business advisor, analyze this entrepreneur's situation and provide 3-5 specific, actionable recommendations.
        
        Current Situation:
        - Business Stage: {business_stage}
        - Engagement Level: {engagement_level}
        - Pain Points: {', '.join(pain_points) if pain_points else 'None identified'}
        - Has Recent Analysis: {latest_analysis is not None}
        
        """
        
        if latest_analysis:
            prompt += f"""
        Latest Analysis Results:
        - Viability Score: {latest_analysis.get('viability_score', 'N/A')}/10
        - Funding Readiness: {latest_analysis.get('funding_readiness', 'N/A')}/10
        """
        
        prompt += """
        
        Provide recommendations in this JSON format:
        [
            {
                "title": "Specific Action Title",
                "description": "Detailed description of what to do and why",
                "priority": "high|medium|low",
                "action_type": "descriptive_action_type",
                "estimated_time": "time estimate"
            }
        ]
        
        Focus on actionable, specific recommendations that will move the business forward.
        """
        
        return prompt
    
    def _parse_ai_recommendations(self, recommendations_text: str) -> List[Dict[str, Any]]:
        """Parse AI-generated recommendations"""
        try:
            import json
            import re
            
            # Try to extract JSON from the response
            json_match = re.search(r'\[.*\]', recommendations_text, re.DOTALL)
            if json_match:
                recommendations = json.loads(json_match.group())
                return recommendations
            else:
                # Fallback: create structured recommendations from text
                return [{
                    'title': 'AI-Generated Recommendation',
                    'description': recommendations_text[:200] + '...' if len(recommendations_text) > 200 else recommendations_text,
                    'priority': 'medium',
                    'action_type': 'ai_suggestion',
                    'estimated_time': 'Variable'
                }]
                
        except Exception as e:
            logger.error(f"Error parsing AI recommendations: {e}")
            return []
    
    def _identify_bottlenecks(self, user_context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identify bottlenecks in user's progress"""
        bottlenecks = []
        
        pain_points = user_context.get('pain_points', [])
        business_ideas = user_context.get('business_ideas', [])
        recent_activity = user_context.get('recent_activity', {})
        
        # Stagnant progress
        if 'stagnant_progress' in pain_points:
            bottlenecks.append({
                'type': 'stagnant_progress',
                'title': 'Business Ideas Not Progressing',
                'description': 'Your business ideas haven\'t been updated recently',
                'severity': 'medium',
                'suggested_action': 'Review and update your business ideas with new insights'
            })
        
        # Low engagement
        if user_context.get('engagement_level') == 'low':
            bottlenecks.append({
                'type': 'low_engagement',
                'title': 'Low Platform Engagement',
                'description': 'Limited activity on the platform may slow your progress',
                'severity': 'medium',
                'suggested_action': 'Set aside regular time for business development activities'
            })
        
        # No analysis
        if not user_context.get('latest_analysis'):
            bottlenecks.append({
                'type': 'no_analysis',
                'title': 'Missing Business Analysis',
                'description': 'Without analysis, you\'re missing key insights about your business',
                'severity': 'high',
                'suggested_action': 'Run a comprehensive business analysis'
            })
        
        return bottlenecks
    
    def _calculate_progress_score(self, user_context: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate overall progress score"""
        score = 0
        max_score = 100
        
        # Business ideas (20 points)
        business_ideas = user_context.get('business_ideas', [])
        if business_ideas:
            score += min(len(business_ideas) * 10, 20)
        
        # Profile completion (15 points)
        profile_completion = user_context.get('profile_completion', 0)
        score += (profile_completion / 100) * 15
        
        # Analysis completion (25 points)
        latest_analysis = user_context.get('latest_analysis')
        if latest_analysis:
            score += 25
        
        # Engagement level (20 points)
        engagement_scores = {'high': 20, 'medium': 12, 'low': 5}
        engagement_level = user_context.get('engagement_level', 'low')
        score += engagement_scores.get(engagement_level, 0)
        
        # Business stage progress (20 points)
        stage_scores = {
            'ideation': 5,
            'concept': 10,
            'validation': 15,
            'development': 18,
            'scaling': 20
        }
        business_stage = user_context.get('business_stage', 'ideation')
        score += stage_scores.get(business_stage, 0)
        
        progress_percentage = min((score / max_score) * 100, 100)
        
        return {
            'overall_score': round(progress_percentage, 1),
            'breakdown': {
                'business_ideas': min(len(business_ideas) * 10, 20),
                'profile_completion': round((profile_completion / 100) * 15, 1),
                'analysis_completion': 25 if latest_analysis else 0,
                'engagement': engagement_scores.get(engagement_level, 0),
                'stage_progress': stage_scores.get(business_stage, 0)
            },
            'level': self._get_progress_level(progress_percentage)
        }
    
    def _get_progress_level(self, percentage: float) -> str:
        """Get progress level based on percentage"""
        if percentage >= 80:
            return 'Advanced'
        elif percentage >= 60:
            return 'Intermediate'
        elif percentage >= 40:
            return 'Developing'
        elif percentage >= 20:
            return 'Beginner'
        else:
            return 'Getting Started'
    
    async def _suggest_next_actions(self, user_context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Suggest specific next actions"""
        next_milestones = user_context.get('next_milestones', [])
        pain_points = user_context.get('pain_points', [])
        
        actions = []
        
        # Convert milestones to actions
        for milestone in next_milestones[:3]:
            actions.append({
                'title': milestone['title'],
                'description': f"Complete this milestone for {milestone.get('business_idea_title', 'your business')}",
                'priority': milestone['priority'],
                'type': 'milestone',
                'deadline': self._calculate_suggested_deadline(milestone['priority'])
            })
        
        # Add pain point resolution actions
        if 'incomplete_profile' in pain_points:
            actions.append({
                'title': 'Complete Your Profile',
                'description': 'Add missing information to improve mentor matching',
                'priority': 'medium',
                'type': 'profile_completion',
                'deadline': (datetime.now() + timedelta(days=1)).isoformat()
            })
        
        return actions[:5]  # Return top 5 actions
    
    def _calculate_suggested_deadline(self, priority: str) -> str:
        """Calculate suggested deadline based on priority"""
        days_map = {'high': 3, 'medium': 7, 'low': 14}
        days = days_map.get(priority, 7)
        deadline = datetime.now() + timedelta(days=days)
        return deadline.isoformat()
    
    def _generate_alerts(self, user_context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate alerts and notifications"""
        alerts = []
        
        pain_points = user_context.get('pain_points', [])
        latest_analysis = user_context.get('latest_analysis')
        
        # Urgent alerts
        if 'stagnant_progress' in pain_points:
            alerts.append({
                'type': 'warning',
                'title': 'Progress Stalled',
                'message': 'Your business ideas haven\'t been updated in 2+ weeks',
                'urgency': 'medium',
                'action_required': True
            })
        
        if latest_analysis and latest_analysis.get('viability_score', 0) < 5:
            alerts.append({
                'type': 'critical',
                'title': 'Low Viability Score',
                'message': 'Your business idea needs significant improvements',
                'urgency': 'high',
                'action_required': True
            })
        
        # Opportunity alerts
        if latest_analysis and latest_analysis.get('funding_readiness', 0) > 7:
            alerts.append({
                'type': 'success',
                'title': 'Funding Ready',
                'message': 'Your business is approaching funding readiness!',
                'urgency': 'low',
                'action_required': False
            })
        
        return alerts
    
    def _analyze_engagement(self, user_context: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze user engagement patterns"""
        engagement_level = user_context.get('engagement_level', 'low')
        recent_activity = user_context.get('recent_activity', {})
        
        return {
            'current_level': engagement_level,
            'activity_trend': self._determine_activity_trend(recent_activity),
            'suggestions': self._get_engagement_suggestions(engagement_level),
            'optimal_schedule': self._suggest_optimal_schedule(engagement_level)
        }
    
    def _determine_activity_trend(self, recent_activity: Dict[str, Any]) -> str:
        """Determine if activity is increasing, decreasing, or stable"""
        # This is simplified - in a real implementation, you'd compare with historical data
        total_activity = (
            recent_activity.get('business_ideas_updated', 0) +
            recent_activity.get('forum_posts', 0) +
            recent_activity.get('analyses_run', 0)
        )
        
        if total_activity >= 5:
            return 'increasing'
        elif total_activity >= 2:
            return 'stable'
        else:
            return 'decreasing'
    
    def _get_engagement_suggestions(self, engagement_level: str) -> List[str]:
        """Get suggestions to improve engagement"""
        suggestions = {
            'low': [
                'Set aside 30 minutes daily for business development',
                'Join forum discussions in your industry',
                'Update your business idea weekly'
            ],
            'medium': [
                'Increase forum participation',
                'Run monthly business analysis updates',
                'Connect with mentors in your field'
            ],
            'high': [
                'Share your expertise in forums',
                'Mentor other entrepreneurs',
                'Lead community discussions'
            ]
        }
        
        return suggestions.get(engagement_level, [])
    
    def _suggest_optimal_schedule(self, engagement_level: str) -> Dict[str, str]:
        """Suggest optimal activity schedule"""
        schedules = {
            'low': {
                'daily': '15-30 minutes',
                'weekly': '2-3 hours',
                'focus': 'Consistent small steps'
            },
            'medium': {
                'daily': '30-60 minutes',
                'weekly': '4-6 hours',
                'focus': 'Regular progress and networking'
            },
            'high': {
                'daily': '1-2 hours',
                'weekly': '8-10 hours',
                'focus': 'Leadership and advanced development'
            }
        }
        
        return schedules.get(engagement_level, schedules['low'])


# Global advisor instance
autonomous_advisor = AutonomousBusinessAdvisor()
