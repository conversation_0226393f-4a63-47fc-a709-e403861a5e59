<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mentorship Session Scheduled</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #4f46e5;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 5px 5px 0 0;
        }
        .content {
            background-color: #f9fafb;
            padding: 20px;
            border-radius: 0 0 5px 5px;
            border: 1px solid #e5e7eb;
            border-top: none;
        }
        .session-details {
            background-color: white;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #e5e7eb;
        }
        .session-details h3 {
            margin-top: 0;
            color: #4f46e5;
        }
        .detail-row {
            margin-bottom: 10px;
        }
        .detail-label {
            font-weight: bold;
            color: #6b7280;
        }
        .button {
            display: inline-block;
            background-color: #4f46e5;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .footer {
            margin-top: 20px;
            text-align: center;
            color: #6b7280;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Mentorship Session Scheduled</h1>
        </div>
        <div class="content">
            <p>Hello,</p>
            <p>A new mentorship session has been scheduled. Here are the details:</p>
            
            <div class="session-details">
                <h3>{{ session.title }}</h3>
                
                <div class="detail-row">
                    <span class="detail-label">Date:</span> {{ session_date }}
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">Time:</span> {{ session_time }}
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">Duration:</span> {{ session_duration }} minutes
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">Session Type:</span> {{ session_type }}
                </div>
                
                {% if session.description %}
                <div class="detail-row">
                    <span class="detail-label">Description:</span> {{ session.description }}
                </div>
                {% endif %}
                
                {% if meeting_link %}
                <div class="detail-row">
                    <span class="detail-label">Video Provider:</span> {{ video_provider }}
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">Meeting Link:</span> <a href="{{ meeting_link }}">{{ meeting_link }}</a>
                </div>
                {% endif %}
            </div>
            
            <p>Please make sure to add this session to your calendar. You will receive a reminder before the session starts.</p>
            
            <a href="http://localhost:5173/dashboard/mentorship/sessions/{{ session.id }}" class="button">View Session Details</a>
            
            <p>If you need to reschedule or cancel this session, please do so at least 24 hours in advance.</p>
            
            <p>Best regards,<br>Yasmeen AI Team</p>
        </div>
        <div class="footer">
            <p>This is an automated message from Yasmeen AI. Please do not reply to this email.</p>
        </div>
    </div>
</body>
</html>
