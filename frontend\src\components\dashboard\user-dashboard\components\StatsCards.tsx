import React from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../hooks/useLanguage';
import { RTLText, RTLIcon } from '../../../common';
import { Lightbulb, CheckCircle, Clock, AlertCircle } from 'lucide-react';


interface StatsCardsProps {
  stats: {
    totalIdeas: number;
    approvedIdeas: number;
    pendingIdeas: number;
    rejectedIdeas: number;
  };
}

const StatsCards: React.FC<StatsCardsProps> = ({ stats  }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
      <div className="glass-light border-indigo-500/30 rounded-lg p-4">
        <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
          <div>
            <RTLText as="p" className="text-sm text-glass-secondary">
              {String(t('dashboard.stats.totalIdeas', 'Total Ideas'))}
            </RTLText>
            <RTLText as="h3" className="text-2xl font-bold text-glass-primary">{stats.totalIdeas || 0}</RTLText>
            {stats.totalIdeas === 0 && (
              <RTLText as="p" className="text-xs text-glass-secondary/70 mt-1">
                {String(t('dashboard.stats.getStarted', 'Get started!'))}
              </RTLText>
            )}
          </div>
          <div className="p-3 rounded-full glass-light border-indigo-500/20">
            <RTLIcon icon={Lightbulb} size={24} className="text-indigo-400" />
          </div>
        </div>
      </div>

      <div className="glass-light border-green-500/30 rounded-lg p-4">
        <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
          <div>
            <RTLText as="p" className="text-sm text-glass-secondary">
              {String(t('dashboard.stats.approved', 'Approved'))}
            </RTLText>
            <RTLText as="h3" className="text-2xl font-bold text-glass-primary">{stats.approvedIdeas}</RTLText>
          </div>
          <div className="p-3 rounded-full glass-light border-green-500/20">
            <RTLIcon icon={CheckCircle} size={24} className="text-green-400" />
          </div>
        </div>
      </div>

      <div className="glass-light border-yellow-500/30 rounded-lg p-4">
        <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
          <div>
            <RTLText as="p" className="text-sm text-glass-secondary">
              {String(t('dashboard.stats.pending', 'Pending'))}
            </RTLText>
            <RTLText as="h3" className="text-2xl font-bold text-glass-primary">{stats.pendingIdeas}</RTLText>
          </div>
          <div className="p-3 rounded-full glass-light border-yellow-500/20">
            <RTLIcon icon={Clock} size={24} className="text-yellow-400" />
          </div>
        </div>
      </div>

      <div className="glass-light border-red-500/30 rounded-lg p-4">
        <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
          <div>
            <RTLText as="p" className="text-sm text-glass-secondary">
              {String(t('dashboard.stats.rejected', 'Rejected'))}
            </RTLText>
            <RTLText as="h3" className="text-2xl font-bold text-glass-primary">{stats.rejectedIdeas}</RTLText>
          </div>
          <div className="p-3 rounded-full glass-light border-red-500/20">
            <RTLIcon icon={AlertCircle} size={24} className="text-red-400" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default StatsCards;
