import React, { useState, useEffect } from 'react';
import { Save, X, FileText, Plus } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { BusinessIdea } from '../../services/incubatorApi';

interface BusinessPlan {
  id?: number;
  title: string;
  executive_summary: string;
  market_analysis: string;
  product_service_description: string;
  marketing_strategy: string;
  operational_plan: string;
  financial_projections: string;
  risk_analysis: string;
  funding_requirements: string;
  business_idea: number;
  version: string;
  status: 'draft' | 'review' | 'approved' | 'needs_revision';
}

interface BusinessPlanFormProps {
  businessPlan?: BusinessPlan;
  businessIdea: BusinessIdea;
  onSave: (data: Partial<BusinessPlan>) => Promise<boolean>;
  onCancel: () => void;
  isLoading?: boolean;
  mode: 'create' | 'edit';
}

const BusinessPlanForm: React.FC<BusinessPlanFormProps> = ({
  businessPlan,
  businessIdea,
  onSave,
  onCancel,
  isLoading = false,
  mode
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [formData, setFormData] = useState({
    title: businessPlan?.title || `${businessIdea.title} - Business Plan`,
    executive_summary: businessPlan?.executive_summary || '',
    market_analysis: businessPlan?.market_analysis || '',
    product_service_description: businessPlan?.product_service_description || '',
    marketing_strategy: businessPlan?.marketing_strategy || '',
    operational_plan: businessPlan?.operational_plan || '',
    financial_projections: businessPlan?.financial_projections || '',
    risk_analysis: businessPlan?.risk_analysis || '',
    funding_requirements: businessPlan?.funding_requirements || '',
    business_idea: businessIdea.id,
    version: businessPlan?.version || '1.0',
    status: businessPlan?.status || 'draft'
  });

  const [formError, setFormError] = useState<string | null>(null);
  const [hasChanges, setHasChanges] = useState(mode === 'create');
  const [activeSection, setActiveSection] = useState('executive_summary');

  useEffect(() => {
    if (mode === 'edit' && businessPlan) {
      // Check if form has changes
      const hasFormChanges = Object.keys(formData).some(key => {
        if (key === 'business_idea') return false;
        const formValue = formData[key as keyof typeof formData];
        const originalValue = businessPlan[key as keyof BusinessPlan] || '';
        return formValue !== originalValue;
      });
      setHasChanges(hasFormChanges);
    }
  }, [formData, businessPlan, mode]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    setFormError(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);

    // Validate required fields
    if (!formData.title.trim()) {
      setFormError(t('business.businessPlan.titleRequired'));
      return;
    }

    if (!formData.executive_summary.trim()) {
      setFormError(t('business.businessPlan.executiveSummaryRequired'));
      return;
    }

    const success = await onSave(formData);
    if (!success) {
      setFormError(mode === 'create' 
        ? t('business.businessPlan.createError')
        : t('business.businessPlan.updateError')
      );
    }
  };

  const sections = [
    { key: 'executive_summary', label: t('business.businessPlan.executiveSummary'), required: true },
    { key: 'market_analysis', label: t('business.businessPlan.marketAnalysis'), required: false },
    { key: 'product_service_description', label: t('business.businessPlan.productService'), required: false },
    { key: 'marketing_strategy', label: t('business.businessPlan.marketingStrategy'), required: false },
    { key: 'operational_plan', label: t('business.businessPlan.operationalPlan'), required: false },
    { key: 'financial_projections', label: t('business.businessPlan.financialProjections'), required: false },
    { key: 'risk_analysis', label: t('business.businessPlan.riskAnalysis'), required: false },
    { key: 'funding_requirements', label: t('business.businessPlan.fundingRequirements'), required: false }
  ];

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 rounded-xl border border-indigo-500/30 max-w-6xl w-full max-h-[90vh] overflow-hidden shadow-2xl">
        {/* Header */}
        <div className={`flex items-center justify-between p-6 border-b border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            {mode === 'create' ? (
              <Plus size={24} className={`text-indigo-400 ${isRTL ? 'ml-3' : 'mr-3'}`} />
            ) : (
              <FileText size={24} className={`text-indigo-400 ${isRTL ? 'ml-3' : 'mr-3'}`} />
            )}
            <h2 className="text-xl font-semibold text-white">
              {mode === 'create' 
                ? t('business.businessPlan.createTitle')
                : t('business.businessPlan.editTitle')
              }
            </h2>
          </div>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-white transition-colors"
            disabled={isLoading}
          >
            <X size={20} />
          </button>
        </div>

        {/* Business Idea Info */}
        <div className="px-6 py-4 bg-gray-800/50 border-b border-gray-700">
          <p className={`text-sm text-gray-400 ${isRTL ? 'text-right' : ''}`}>
            {t('business.businessPlan.forBusinessIdea')}: 
            <span className="text-white font-medium ml-1">{businessIdea.title}</span>
          </p>
        </div>

        <div className="flex h-[calc(90vh-200px)]">
          {/* Section Navigation */}
          <div className="w-64 bg-gray-800/30 border-r border-gray-700 overflow-y-auto">
            <div className="p-4">
              <h3 className="text-sm font-medium text-gray-300 mb-3">
                {t('business.businessPlan.sections')}
              </h3>
              <nav className="space-y-1">
                {sections.map((section) => (
                  <button
                    key={section.key}
                    onClick={() => setActiveSection(section.key)}
                    className={`w-full text-left px-3 py-2 rounded-lg text-sm transition-colors ${
                      activeSection === section.key
                        ? 'bg-indigo-600 text-white'
                        : 'text-gray-400 hover:text-white hover:bg-gray-700'
                    }`}
                  >
                    {section.label}
                    {section.required && <span className="text-red-400 ml-1">*</span>}
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* Form Content */}
          <div className="flex-1 overflow-y-auto">
            <div className="p-6">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Error Message */}
                {formError && (
                  <div className="bg-red-900/30 border border-red-500/50 rounded-lg p-4">
                    <p className="text-red-400 text-sm">{formError}</p>
                  </div>
                )}

                {/* Basic Info Section */}
                {activeSection === 'executive_summary' && (
                  <div className="space-y-6">
                    {/* Title */}
                    <div>
                      <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                        {t('business.businessPlan.title')} *
                      </label>
                      <input
                        type="text"
                        name="title"
                        value={formData.title}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-white placeholder-gray-400 ${isRTL ? 'text-right' : ''}`}
                        placeholder={t('business.businessPlan.titlePlaceholder')}
                        required
                        disabled={isLoading}
                      />
                    </div>

                    {/* Version and Status */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                          {t('business.businessPlan.version')}
                        </label>
                        <input
                          type="text"
                          name="version"
                          value={formData.version}
                          onChange={handleInputChange}
                          className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-white placeholder-gray-400 ${isRTL ? 'text-right' : ''}`}
                          placeholder="1.0"
                          disabled={isLoading}
                        />
                      </div>

                      <div>
                        <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                          {t('business.businessPlan.status')}
                        </label>
                        <select
                          name="status"
                          value={formData.status}
                          onChange={handleInputChange}
                          className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-white ${isRTL ? 'text-right' : ''}`}
                          disabled={isLoading}
                        >
                          <option value="draft">{t('business.businessPlan.status.draft')}</option>
                          <option value="review">{t('business.businessPlan.status.review')}</option>
                          <option value="approved">{t('business.businessPlan.status.approved')}</option>
                          <option value="needs_revision">{t('business.businessPlan.status.needsRevision')}</option>
                        </select>
                      </div>
                    </div>

                    {/* Executive Summary */}
                    <div>
                      <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                        {t('business.businessPlan.executiveSummary')} *
                      </label>
                      <textarea
                        name="executive_summary"
                        value={formData.executive_summary}
                        onChange={handleInputChange}
                        rows={8}
                        className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-white placeholder-gray-400 resize-none ${isRTL ? 'text-right' : ''}`}
                        placeholder={t('business.businessPlan.executiveSummaryPlaceholder')}
                        required
                        disabled={isLoading}
                      />
                      <p className="text-gray-500 text-xs mt-1">
                        {t('business.businessPlan.executiveSummaryHelp')}
                      </p>
                    </div>
                  </div>
                )}

                {/* Dynamic Sections */}
                {sections.slice(1).map((section) => (
                  activeSection === section.key && (
                    <div key={section.key}>
                      <label className={`block text-sm font-medium text-gray-300 mb-2 ${isRTL ? 'text-right' : ''}`}>
                        {section.label} {section.required && <span className="text-red-400">*</span>}
                      </label>
                      <textarea
                        name={section.key}
                        value={formData[section.key as keyof typeof formData] as string}
                        onChange={handleInputChange}
                        rows={12}
                        className={`w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-white placeholder-gray-400 resize-none ${isRTL ? 'text-right' : ''}`}
                        placeholder={t(`business.businessPlan.${section.key}Placeholder`)}
                        required={section.required}
                        disabled={isLoading}
                      />
                      <p className="text-gray-500 text-xs mt-1">
                        {t(`business.businessPlan.${section.key}Help`)}
                      </p>
                    </div>
                  )
                ))}
              </form>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className={`flex justify-between items-center p-6 border-t border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={`flex gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <button
              type="button"
              onClick={() => {
                const currentIndex = sections.findIndex(s => s.key === activeSection);
                if (currentIndex > 0) {
                  setActiveSection(sections[currentIndex - 1].key);
                }
              }}
              disabled={sections.findIndex(s => s.key === activeSection) === 0}
              className="px-4 py-2 border border-gray-600 rounded-lg hover:bg-gray-800 transition-colors text-white disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {t('common.previous')}
            </button>
            <button
              type="button"
              onClick={() => {
                const currentIndex = sections.findIndex(s => s.key === activeSection);
                if (currentIndex < sections.length - 1) {
                  setActiveSection(sections[currentIndex + 1].key);
                }
              }}
              disabled={sections.findIndex(s => s.key === activeSection) === sections.length - 1}
              className="px-4 py-2 border border-gray-600 rounded-lg hover:bg-gray-800 transition-colors text-white disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {t('common.next')}
            </button>
          </div>

          <div className={`flex gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <button
              type="button"
              onClick={onCancel}
              className="px-6 py-2 border border-gray-600 rounded-lg hover:bg-gray-800 transition-colors text-white disabled:opacity-50"
              disabled={isLoading}
            >
              {t('common.cancel')}
            </button>
            <button
              onClick={handleSubmit}
              disabled={isLoading || (mode === 'edit' && !hasChanges)}
              className="px-6 py-2 bg-indigo-600 rounded-lg hover:bg-indigo-700 transition-colors flex items-center text-white disabled:opacity-50"
            >
              {isLoading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  {mode === 'create' ? t('common.creating') : t('common.saving')}
                </>
              ) : (
                <>
                  <Save size={16} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                  {mode === 'create' ? t('common.create') : t('common.saveChanges')}
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BusinessPlanForm;
