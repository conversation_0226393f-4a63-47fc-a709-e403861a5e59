import React, { useState, useEffect } from 'react';
import { Bell, X, Check, AlertTriangle, Info, DollarSign, Users, Calendar, MessageSquare } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';

interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error' | 'funding' | 'mentorship' | 'milestone' | 'message';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  actionUrl?: string;
  actionLabel?: string;
  priority: 'low' | 'medium' | 'high';
  category: string;
}

interface NotificationCenterProps {
  isOpen: boolean;
  onClose: () => void;
  notifications: Notification[];
  onMarkAsRead: (id: string) => void;
  onMarkAllAsRead: () => void;
  onDeleteNotification: (id: string) => void;
}

const NotificationCenter: React.FC<NotificationCenterProps> = ({
  isOpen,
  onClose,
  notifications,
  onMarkAsRead,
  onMarkAllAsRead,
  onDeleteNotification
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [filter, setFilter] = useState<string>('all');

  // Get notification icon
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <Check size={16} className="text-green-400" />;
      case 'warning':
        return <AlertTriangle size={16} className="text-yellow-400" />;
      case 'error':
        return <AlertTriangle size={16} className="text-red-400" />;
      case 'funding':
        return <DollarSign size={16} className="text-green-400" />;
      case 'mentorship':
        return <Users size={16} className="text-purple-400" />;
      case 'milestone':
        return <Calendar size={16} className="text-blue-400" />;
      case 'message':
        return <MessageSquare size={16} className="text-cyan-400" />;
      default:
        return <Info size={16} className="text-blue-400" />;
    }
  };

  // Get notification color
  const getNotificationColor = (type: string, priority: string) => {
    if (priority === 'high') {
      return 'border-red-500/50 bg-red-500/10';
    }
    switch (type) {
      case 'success':
        return 'border-green-500/50 bg-green-500/10';
      case 'warning':
        return 'border-yellow-500/50 bg-yellow-500/10';
      case 'error':
        return 'border-red-500/50 bg-red-500/10';
      case 'funding':
        return 'border-green-500/50 bg-green-500/10';
      case 'mentorship':
        return 'border-purple-500/50 bg-purple-500/10';
      case 'milestone':
        return 'border-blue-500/50 bg-blue-500/10';
      case 'message':
        return 'border-cyan-500/50 bg-cyan-500/10';
      default:
        return 'border-indigo-500/50 bg-indigo-500/10';
    }
  };

  // Filter notifications
  const filteredNotifications = notifications.filter(notification => {
    if (filter === 'all') return true;
    if (filter === 'unread') return !notification.read;
    return notification.type === filter;
  });

  // Get relative time
  const getRelativeTime = (timestamp: string) => {
    const now = new Date();
    const notificationTime = new Date(timestamp);
    const diffMs = now.getTime() - notificationTime.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return t('notification.just.now', 'Just now');
    if (diffMins < 60) return t('notification.minutes.ago', '{{count}}m ago', { count: diffMins });
    if (diffHours < 24) return t('notification.hours.ago', '{{count}}h ago', { count: diffHours });
    if (diffDays < 7) return t('notification.days.ago', '{{count}}d ago', { count: diffDays });
    return notificationTime.toLocaleDateString();
  };

  // Get unread count
  const unreadCount = notifications.filter(n => !n.read).length;

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-start justify-end z-50 p-4">
      <div className="bg-indigo-900/90 backdrop-blur-sm rounded-xl border border-indigo-800 w-full max-w-md h-[80vh] flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-indigo-800/50">
          <div className={`flex justify-between items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
              <Bell size={20} className="text-purple-400" />
              <h2 className="text-lg font-semibold text-white">
                {t('notifications.title', 'Notifications')}
              </h2>
              {unreadCount > 0 && (
                <span className="bg-red-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                  {unreadCount}
                </span>
              )}
            </div>
            <button
              onClick={onClose}
              className="p-1 hover:bg-indigo-800/50 rounded transition-colors"
            >
              <X size={18} className="text-gray-400" />
            </button>
          </div>

          {/* Filters */}
          <div className="mt-3">
            <div className="flex flex-wrap gap-2">
              {[
                { value: 'all', label: t('notifications.all', 'All') },
                { value: 'unread', label: t('notifications.unread', 'Unread') },
                { value: 'funding', label: t('notifications.funding', 'Funding') },
                { value: 'mentorship', label: t('notifications.mentorship', 'Mentorship') },
                { value: 'milestone', label: t('notifications.milestone', 'Milestones') }
              ].map((filterOption) => (
                <button
                  key={filterOption.value}
                  onClick={() => setFilter(filterOption.value)}
                  className={`px-3 py-1 rounded-full text-xs font-medium transition-colors ${
                    filter === filterOption.value
                      ? 'bg-purple-600 text-white'
                      : 'bg-indigo-800/50 text-gray-300 hover:bg-indigo-700/50'
                  }`}
                >
                  {filterOption.label}
                </button>
              ))}
            </div>
          </div>

          {/* Actions */}
          {unreadCount > 0 && (
            <div className="mt-3">
              <button
                onClick={onMarkAllAsRead}
                className="text-sm text-blue-400 hover:text-blue-300 transition-colors"
              >
                {t('notifications.mark.all.read', 'Mark all as read')}
              </button>
            </div>
          )}
        </div>

        {/* Notifications List */}
        <div className="flex-1 overflow-y-auto">
          {filteredNotifications.length > 0 ? (
            <div className="p-2">
              {filteredNotifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-3 mb-2 rounded-lg border transition-all duration-300 hover:shadow-lg ${
                    notification.read 
                      ? 'border-indigo-700/50 bg-indigo-800/20' 
                      : getNotificationColor(notification.type, notification.priority)
                  }`}
                >
                  <div className={`flex items-start space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                    <div className="flex-shrink-0 mt-1">
                      {getNotificationIcon(notification.type)}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className={`flex justify-between items-start mb-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <h4 className={`text-sm font-medium text-white ${notification.read ? 'opacity-70' : ''}`}>
                          {notification.title}
                        </h4>
                        <div className={`flex items-center space-x-1 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                          {notification.priority === 'high' && (
                            <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                          )}
                          <button
                            onClick={() => onDeleteNotification(notification.id)}
                            className="p-1 hover:bg-indigo-700/50 rounded transition-colors opacity-0 group-hover:opacity-100"
                          >
                            <X size={12} className="text-gray-400" />
                          </button>
                        </div>
                      </div>
                      
                      <p className={`text-sm text-gray-300 mb-2 ${notification.read ? 'opacity-70' : ''}`}>
                        {notification.message}
                      </p>
                      
                      <div className={`flex justify-between items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                        <span className="text-xs text-gray-400">
                          {getRelativeTime(notification.timestamp)}
                        </span>
                        
                        <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                          {notification.actionUrl && (
                            <button className="text-xs text-blue-400 hover:text-blue-300 transition-colors">
                              {notification.actionLabel || t('notifications.view', 'View')}
                            </button>
                          )}
                          
                          {!notification.read && (
                            <button
                              onClick={() => onMarkAsRead(notification.id)}
                              className="text-xs text-purple-400 hover:text-purple-300 transition-colors"
                            >
                              {t('notifications.mark.read', 'Mark as read')}
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-full p-8 text-center">
              <Bell size={48} className="text-gray-600 mb-4" />
              <h3 className="text-lg font-medium text-gray-400 mb-2">
                {filter === 'unread' 
                  ? t('notifications.no.unread', 'No unread notifications')
                  : t('notifications.no.notifications', 'No notifications')
                }
              </h3>
              <p className="text-gray-500 text-sm">
                {filter === 'unread'
                  ? t('notifications.all.caught.up', "You're all caught up!")
                  : t('notifications.will.appear.here', 'Notifications will appear here when you have updates')
                }
              </p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-3 border-t border-indigo-800/50">
          <div className="text-center">
            <button className="text-sm text-gray-400 hover:text-gray-300 transition-colors">
              {t('notifications.view.all', 'View all notifications')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Notification Hook
export const useNotifications = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isOpen, setIsOpen] = useState(false);

  // Mock notifications for demonstration
  useEffect(() => {
    const mockNotifications: Notification[] = [
      {
        id: '1',
        type: 'funding',
        title: 'New Funding Application',
        message: 'TechStart has submitted a funding application for $50,000',
        timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
        read: false,
        priority: 'high',
        category: 'funding',
        actionUrl: '/admin/funding-applications',
        actionLabel: 'Review Application'
      },
      {
        id: '2',
        type: 'mentorship',
        title: 'Mentorship Match Created',
        message: 'Sarah Wilson has been matched with mentor John Doe',
        timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        read: false,
        priority: 'medium',
        category: 'mentorship'
      },
      {
        id: '3',
        type: 'milestone',
        title: 'Milestone Completed',
        message: 'HealthApp has completed their MVP milestone',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        read: true,
        priority: 'medium',
        category: 'progress'
      }
    ];
    setNotifications(mockNotifications);
  }, []);

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === id 
          ? { ...notification, read: true }
          : notification
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notification => ({ ...notification, read: true }))
    );
  };

  const deleteNotification = (id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  return {
    notifications,
    isOpen,
    setIsOpen,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    unreadCount
  };
};

export default NotificationCenter;
