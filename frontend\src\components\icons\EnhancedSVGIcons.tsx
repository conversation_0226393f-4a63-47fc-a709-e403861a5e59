import { useTranslation } from 'react-i18next';
/**
 * Enhanced SVG Icon System
 * Custom SVG icons with animations and theming
 */

import React from 'react';
import { useLanguage } from '../../hooks/useLanguage';

interface IconProps {
  size?: number;
  className?: string;
  color?: string;
  animated?: boolean;
  onClick?: () => void;
}

// AI Brain Icon with animation
export const AIBrainIcon: React.FC<IconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
  animated = false
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  return (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    className={`${className} ${animated ? 'animate-pulse' : ''}`}
    fill="none"
  >
    <path
      d="M12 2C8.5 2 6 4.5 6 8c0 1.5.5 3 1.5 4L12 22l4.5-10c1-1 1.5-2.5 1.5-4 0-3.5-2.5-6-6-6z"
      stroke={color}
      strokeWidth="2"
      fill={animated ? color : 'none'}
      className={animated ? 'animate-pulse' : ''}
    />
    <circle cx="12" cy="8" r="3" stroke={color} strokeWidth="2" />
    {animated && (
      <>
        <circle cx="9" cy="6" r="1" fill={color} className="animate-ping" />
        <circle cx="15" cy="6" r="1" fill={color} className="animate-ping" style={{ animationDelay: '0.5s' }} />
        <circle cx="12" cy="10" r="1" fill={color} className="animate-ping" style={{ animationDelay: '1s' }} />
      </>
    )}
  </svg>
  );
};

// Arabic Text Icon
export const ArabicTextIcon: React.FC<IconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor'
}) => {
  const { isRTL } = useLanguage();

  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      className={className}
      fill="none"
      style={{ transform: isRTL ? 'scaleX(-1)' : 'none' }}
    >
      <path
        d="M3 7h18M3 12h12M3 17h8"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
      />
      <path
        d="M16 17c2 0 3-1 3-3s-1-3-3-3-3 1-3 3 1 3 3 3z"
        stroke={color}
        strokeWidth="2"
        fill="none"
      />
    </svg>
  );
};

// Business Growth Icon with animation
export const BusinessGrowthIcon: React.FC<IconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
  animated = false
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    className={className}
    fill="none"
  >
    <path
      d="M3 17l6-6 4 4 8-8"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={animated ? 'animate-pulse' : ''}
    />
    <path
      d="M17 7h4v4"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    {animated && (
      <>
        <circle cx="3" cy="17" r="2" fill={color} className="animate-bounce" />
        <circle cx="9" cy="11" r="2" fill={color} className="animate-bounce" style={{ animationDelay: '0.2s' }} />
        <circle cx="13" cy="15" r="2" fill={color} className="animate-bounce" style={{ animationDelay: '0.4s' }} />
        <circle cx="21" cy="7" r="2" fill={color} className="animate-bounce" style={{ animationDelay: '0.6s' }} />
      </>
    )}
  </svg>
);

// Search Lens Icon with animation
export const SearchLensIcon: React.FC<IconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
  animated = false
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    className={className}
    fill="none"
  >
    <circle
      cx="11"
      cy="11"
      r="8"
      stroke={color}
      strokeWidth="2"
      className={animated ? 'animate-pulse' : ''}
    />
    <path
      d="M21 21l-4.35-4.35"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      className={animated ? 'animate-bounce' : ''}
    />
    {animated && (
      <circle cx="11" cy="11" r="3" fill={color} className="animate-ping" opacity="0.5" />
    )}
  </svg>
);

// Innovation Icon
export const InnovationIcon: React.FC<IconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
  animated = false
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    className={className}
    fill="none"
  >
    <path
      d="M9 12l2 2 4-4"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9c1.66 0 3.22.45 4.56 1.23"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={animated ? 'animate-spin' : ''}
      style={{ transformOrigin: '12px 12px', animationDuration: '3s' }}
    />
    {animated && (
      <>
        <circle cx="12" cy="6" r="1" fill={color} className="animate-ping" />
        <circle cx="18" cy="12" r="1" fill={color} className="animate-ping" style={{ animationDelay: '0.5s' }} />
        <circle cx="12" cy="18" r="1" fill={color} className="animate-ping" style={{ animationDelay: '1s' }} />
        <circle cx="6" cy="12" r="1" fill={color} className="animate-ping" style={{ animationDelay: '1.5s' }} />
      </>
    )}
  </svg>
);

// Community Icon
export const CommunityIcon: React.FC<IconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
  animated = false
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    className={className}
    fill="none"
  >
    <path
      d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"
      stroke={color}
      strokeWidth="2"
    />
    <circle cx="9" cy="7" r="4" stroke={color} strokeWidth="2" />
    <path
      d="M23 21v-2a4 4 0 0 0-3-3.87"
      stroke={color}
      strokeWidth="2"
    />
    <path
      d="M16 3.13a4 4 0 0 1 0 7.75"
      stroke={color}
      strokeWidth="2"
      className={animated ? 'animate-pulse' : ''}
    />
    {animated && (
      <>
        <circle cx="9" cy="7" r="2" fill={color} className="animate-pulse" opacity="0.5" />
        <circle cx="20" cy="7" r="1" fill={color} className="animate-ping" />
      </>
    )}
  </svg>
);

// Data Analytics Icon
export const DataAnalyticsIcon: React.FC<IconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
  animated = false
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    className={className}
    fill="none"
  >
    <path
      d="M3 3v18h18"
      stroke={color}
      strokeWidth="2"
    />
    <path
      d="M7 16l4-4 2 2 6-6"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={animated ? 'animate-pulse' : ''}
    />
    <rect
      x="7"
      y="12"
      width="2"
      height="8"
      fill={color}
      className={animated ? 'animate-bounce' : ''}
    />
    <rect
      x="11"
      y="8"
      width="2"
      height="12"
      fill={color}
      className={animated ? 'animate-bounce' : ''}
      style={{ animationDelay: '0.2s' }}
    />
    <rect
      x="15"
      y="10"
      width="2"
      height="10"
      fill={color}
      className={animated ? 'animate-bounce' : ''}
      style={{ animationDelay: '0.4s' }}
    />
    <rect
      x="19"
      y="6"
      width="2"
      height="14"
      fill={color}
      className={animated ? 'animate-bounce' : ''}
      style={{ animationDelay: '0.6s' }}
    />
  </svg>
);

// Rocket Launch Icon
export const RocketLaunchIcon: React.FC<IconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
  animated = false
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    className={`${className} ${animated ? 'animate-bounce' : ''}`}
    fill="none"
  >
    <path
      d="M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z"
      stroke={color}
      strokeWidth="2"
      fill={animated ? color : 'none'}
    />
    <path
      d="M12 15l-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z"
      stroke={color}
      strokeWidth="2"
      fill={animated ? color : 'none'}
    />
    <path
      d="M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0"
      stroke={color}
      strokeWidth="2"
    />
    <path
      d="M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5"
      stroke={color}
      strokeWidth="2"
    />
    {animated && (
      <>
        <circle cx="18" cy="6" r="1" fill={color} className="animate-ping" />
        <path
          d="M8 20l4-4 4 4-4 4-4-4z"
          fill={color}
          className="animate-pulse"
          opacity="0.3"
        />
      </>
    )}
  </svg>
);

// Export all icons
export const EnhancedIcons = {
  AIBrainIcon,
  ArabicTextIcon,
  BusinessGrowthIcon,
  SearchLensIcon,
  InnovationIcon,
  CommunityIcon,
  DataAnalyticsIcon,
  RocketLaunchIcon
};

export default EnhancedIcons;
