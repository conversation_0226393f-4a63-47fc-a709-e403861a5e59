import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { authAPI, clearAuthTokens, getAuthToken, getRefreshToken } from '../../services/api';
import { debugTokens, clearAllTokens, testTokenValidity } from '../../utils/tokenDebug';

interface TokenErrorHandlerProps {
  error?: string;
  onRetry?: () => void;
}

const TokenErrorHandler: React.FC<TokenErrorHandlerProps> = ({ error, onRetry }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [debugInfo, setDebugInfo] = useState<string>('');

  useEffect(() => {
    // Debug tokens on mount
    debugTokens();
  }, []);

  const handleRefreshToken = async () => {
    setIsRefreshing(true);
    try {
      const success = await authAPI.refreshToken();
      if (success) {
        console.log('✅ Token refreshed successfully');
        if (onRetry) {
          onRetry();
        } else {
          window.location.reload();
        }
      } else {
        console.log('❌ Token refresh failed');
        handleLogout();
      }
    } catch (error) {
      console.error('Token refresh error:', error);
      handleLogout();
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleLogout = () => {
    clearAllTokens();
    navigate('/login');
  };

  const handleDebugTokens = () => {
    debugTokens();
    
    const accessToken = getAuthToken();
    const refreshToken = getRefreshToken();
    
    let info = `Access Token: ${accessToken ? 'Present' : 'Missing'}\n`;
    info += `Refresh Token: ${refreshToken ? 'Present' : 'Missing'}\n`;
    
    if (accessToken) {
      try {
        const payload = JSON.parse(atob(accessToken.split('.')[1]));
        const now = Math.floor(Date.now() / 1000);
        const isExpired = payload.exp < now;
        
        info += `Token Expiry: ${new Date(payload.exp * 1000).toLocaleString()}\n`;
        info += `Is Expired: ${isExpired ? 'Yes' : 'No'}\n`;
        info += `User ID: ${payload.user_id}\n`;
      } catch (e) {
        info += 'Failed to decode token\n';
      }
    }
    
    setDebugInfo(info);
  };

  const handleTestToken = async () => {
    const isValid = await testTokenValidity();
    setDebugInfo(prev => prev + `\nToken Test Result: ${isValid ? 'Valid' : 'Invalid'}`);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 flex items-center justify-center p-4">
      <div className="bg-white/10 backdrop-blur-md rounded-xl p-8 max-w-md w-full border border-white/20">
        <div className="text-center mb-6">
          <div className="text-6xl mb-4">🔐</div>
          <h1 className="text-2xl font-bold text-white mb-2">
            {t('auth.tokenError', 'Authentication Error')}
          </h1>
          <p className="text-gray-300">
            {error || t('auth.tokenInvalid', 'Your session has expired or is invalid')}
          </p>
        </div>

        <div className="space-y-4">
          <button
            onClick={handleRefreshToken}
            disabled={isRefreshing}
            className="w-full bg-purple-600 hover:bg-purple-700 disabled:bg-purple-800/50 text-white py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center"
          >
            {isRefreshing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                {t('auth.refreshing', 'Refreshing...')}
              </>
            ) : (
              t('auth.refreshToken', 'Refresh Session')
            )}
          </button>

          <button
            onClick={handleLogout}
            className="w-full bg-gray-600 hover:bg-gray-700 text-white py-3 px-4 rounded-lg font-medium transition-colors"
          >
            {t('auth.loginAgain', 'Login Again')}
          </button>

          <div className="border-t border-white/20 pt-4">
            <p className="text-sm text-gray-400 mb-2">Debug Tools:</p>
            <div className="flex gap-2">
              <button
                onClick={handleDebugTokens}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-3 rounded text-sm"
              >
                Debug
              </button>
              <button
                onClick={handleTestToken}
                className="flex-1 bg-green-600 hover:bg-green-700 text-white py-2 px-3 rounded text-sm"
              >
                Test
              </button>
              <button
                onClick={clearAllTokens}
                className="flex-1 bg-red-600 hover:bg-red-700 text-white py-2 px-3 rounded text-sm"
              >
                Clear
              </button>
            </div>
          </div>

          {debugInfo && (
            <div className="bg-black/30 rounded-lg p-3 mt-4">
              <pre className="text-xs text-gray-300 whitespace-pre-wrap">{debugInfo}</pre>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TokenErrorHandler;
