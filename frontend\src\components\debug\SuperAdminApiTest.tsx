import React, { useState } from 'react';
import { superAdminApi } from '../../services/superAdminApi';
import { CheckCircle, XCircle, RefreshCw, AlertTriangle } from 'lucide-react';

interface TestResult {
  name: string;
  success: boolean;
  error?: string;
  status?: number;
  data?: any;
}

const SuperAdminApiTest: React.FC = () => {
  const [testing, setTesting] = useState(false);
  const [results, setResults] = useState<TestResult[]>([]);

  const runAllTests = async () => {
    setTesting(true);
    setResults([]);

    const tests = [
      {
        name: 'Dashboard Data',
        test: () => superAdminApi.getDashboardData()
      },
      {
        name: 'System Health',
        test: () => superAdminApi.getSystemHealth()
      },
      {
        name: 'Capabilities',
        test: () => superAdminApi.getCapabilities()
      },
      {
        name: 'AI Configuration',
        test: () => superAdminApi.getAIConfiguration()
      },
      {
        name: 'Communication Data',
        test: () => superAdminApi.getCommunicationData()
      },
      {
        name: 'Security Events',
        test: () => superAdminApi.getSecurityEvents()
      }
    ];

    const testResults: TestResult[] = [];

    for (const test of tests) {
      console.log(`🧪 Testing ${test.name}...`);
      
      try {
        const result = await test.test();
        testResults.push({
          name: test.name,
          success: result.success,
          error: result.error,
          status: result.status,
          data: result.success ? Object.keys(result.data || {}) : undefined
        });
      } catch (error) {
        testResults.push({
          name: test.name,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }

      setResults([...testResults]);
      await new Promise(resolve => setTimeout(resolve, 500)); // Small delay between tests
    }

    setTesting(false);
    console.log('🎉 All API tests completed:', testResults);
  };

  const getStatusIcon = (success: boolean) => {
    return success ? 
      <CheckCircle className="w-5 h-5 text-green-400" /> : 
      <XCircle className="w-5 h-5 text-red-400" />;
  };

  const getStatusColor = (success: boolean) => {
    return success ? 'border-green-500 bg-green-900/20' : 'border-red-500 bg-red-900/20';
  };

  return (
    <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <AlertTriangle className="w-5 h-5 text-yellow-400" />
          <h3 className="text-lg font-semibold text-white">Super Admin API Test</h3>
        </div>
        <button
          onClick={runAllTests}
          disabled={testing}
          className="bg-blue-600 hover:bg-blue-700 disabled:opacity-50 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
        >
          {testing ? <RefreshCw className="w-4 h-4 animate-spin" /> : <CheckCircle className="w-4 h-4" />}
          {testing ? 'Testing...' : 'Run API Tests'}
        </button>
      </div>

      <p className="text-gray-300 text-sm mb-4">
        Test all Super Admin API endpoints to diagnose connection issues.
      </p>

      {results.length > 0 && (
        <div className="space-y-3">
          <h4 className="text-white font-medium">Test Results:</h4>
          {results.map((result, index) => (
            <div
              key={index}
              className={`border rounded-lg p-3 ${getStatusColor(result.success)}`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {getStatusIcon(result.success)}
                  <span className="text-white font-medium">{result.name}</span>
                </div>
                <div className="text-sm text-gray-300">
                  {result.status && `Status: ${result.status}`}
                </div>
              </div>
              
              {result.error && (
                <div className="mt-2 text-sm text-red-300">
                  Error: {result.error}
                </div>
              )}
              
              {result.success && result.data && (
                <div className="mt-2 text-sm text-green-300">
                  Data keys: {Array.isArray(result.data) ? result.data.join(', ') : 'Object received'}
                </div>
              )}
            </div>
          ))}
          
          <div className="mt-4 p-3 bg-gray-700/50 rounded-lg">
            <div className="text-sm text-gray-300">
              <strong>Summary:</strong> {results.filter(r => r.success).length} / {results.length} tests passed
            </div>
          </div>
        </div>
      )}

      {testing && results.length === 0 && (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span className="ml-3 text-gray-300">Starting API tests...</span>
        </div>
      )}
    </div>
  );
};

export default SuperAdminApiTest;
