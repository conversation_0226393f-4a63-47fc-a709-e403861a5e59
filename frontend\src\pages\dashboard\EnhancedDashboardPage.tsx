import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  Bo<PERSON>,
  BarChart3,
  MessageSquare,
  Sparkles,
  TrendingUp,
  Clock,
  Globe,
  Users,
  Activity,
  ArrowRight,
  Zap,
  Brain,
  Target,
  Lightbulb
} from 'lucide-react';
// MainLayout removed - handled by routing system
import { enhancedChatAPI } from '../../services/chatApi';
import { useAppSelector } from '../../store/hooks';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { RTLText, RTLFlex, RTLIcon } from '../../components/common';

interface QuickStats {
  total_messages: number;
  total_sessions: number;
  average_response_time: number;
  recent_activity: {
    messages_last_30_days: number;
    sessions_last_30_days: number;
  };
  language_distribution: { arabic: number; english: number };
}

const EnhancedDashboardPage: React.FC = () => {
  const { user } = useAppSelector((state) => state.auth);
  const { language } = useAppSelector(state => state.language);
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [stats, setStats] = useState<QuickStats | null>(null);
  const [loading, setLoading] = useState(true);
  const isRTL = language === 'ar';

  useEffect(() => {
    fetchQuickStats();
  }, []);

  const fetchQuickStats = async () => {
    try {
      const data = await enhancedChatAPI.getChatAnalytics();
      setStats(data);
    } catch (error) {
      console.error('Error fetching stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const features = [
    {
      title: t("dashboard.enhanced.ai.chat", "Enhanced AI Chat"),
      description: 'Advanced AI assistant with Arabic cultural context and multi-modal capabilities',
      icon: Bot,
      color: 'purple',
      link: '/chat/enhanced',
      stats: stats ? `${stats.total_messages} messages` : t("dashboard.loading", t("common.loading", "Loading..."))
    },
    {
      title: t("dashboard.chat.analytics.description", "Chat Analytics"),
      description: 'Comprehensive insights into your AI interactions and performance metrics',
      icon: BarChart3,
      color: 'blue',
      link: '/chat/analytics',
      stats: stats ? `${stats.total_sessions} sessions` : t("dashboard.loading", t("common.loading", "Loading..."))
    },
    {
      title: t("dashboard.business.plan.generator", "Business Plan Generator"),
      description: 'AI-powered business plan creation with cultural and market insights',
      icon: Target,
      color: 'green',
      link: '/dashboard/business-plan',
      stats: t("dashboard.smart.templates", "Smart Templates")
    },
    {
      title: t("dashboard.template.library.description", "Template Library"),
      description: 'Extensive collection of business templates optimized for the Arab market',
      icon: Lightbulb,
      color: 'yellow',
      link: '/dashboard/templates',
      stats: '50+ Templates'
    },
    {
      title: t("dashboard.mentorship.hub.description", "Mentorship Hub"),
      description: 'Connect with experienced mentors and schedule video sessions',
      icon: Users,
      color: 'indigo',
      link: '/dashboard/mentorship',
      stats: t("dashboard.expert.mentors", "Expert Mentors")
    },
    {
      title: t("dashboard.advanced.analytics.description", "Advanced Analytics"),
      description: 'Deep insights into your business progress and market opportunities',
      icon: TrendingUp,
      color: 'pink',
      link: '/dashboard/analytics',
      stats: t("dashboard.realtime.data", "Real-time Data")
    }
  ];

  const getColorClasses = (color: string) => {
    const colors = {
      purple: 'from-purple-600 to-purple-800 border-purple-500/30',
      blue: 'from-blue-600 to-blue-800 border-blue-500/30',
      green: 'from-green-600 to-green-800 border-green-500/30',
      yellow: 'from-yellow-600 to-yellow-800 border-yellow-500/30',
      indigo: 'from-indigo-600 to-indigo-800 border-indigo-500/30',
      pink: 'from-pink-600 to-pink-800 border-pink-500/30'
    };
    return colors[color as keyof typeof colors] || colors.purple;
  };

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
        {/* Header */}
        <div className="mb-8">
          <RTLFlex className="items-center mb-4">
            <div className={`w-16 h-16 rounded-full bg-gradient-to-r from-purple-600 to-blue-600 flex items-center justify-center ${language === 'ar' ? 'ml-4' : 'mr-4'} shadow-glow`}>
              <Sparkles size={32} className="text-white" />
            </div>
            <div>
              <RTLText as="h1" className="text-3xl font-bold text-white">
                {t('dashboard.enhancedTitle', 'Enhanced Yasmeen AI Dashboard')}
              </RTLText>
              <RTLText className="text-gray-300 text-lg">
                {t('dashboard.welcomeBack', `Welcome back, ${user?.first_name || user?.username}!`)}
              </RTLText>
            </div>
          </RTLFlex>

          <RTLText className="text-gray-300 max-w-2xl">
            {t('dashboard.enhancedDescription', 'Access all enhanced AI features, analytics, and tools designed specifically for Arab entrepreneurs and businesses.')}
          </RTLText>
        </div>

        {/* Quick Stats */}
        {!loading && stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <RTLFlex className="items-center justify-between">
                <div>
                  <RTLText as="h3" className="text-2xl font-bold text-purple-400">
                    {stats.total_messages}
                  </RTLText>
                  <RTLText className="text-gray-400">{t('analytics.totalMessages', 'Total Messages')}</RTLText>
                </div>
                <MessageSquare size={24} className="text-purple-400" />
              </RTLFlex>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <RTLFlex className="items-center justify-between">
                <div>
                  <RTLText as="h3" className="text-2xl font-bold text-green-400">
                    {stats.total_sessions}
                  </RTLText>
                  <RTLText className="text-gray-400">{t('analytics.chatSessions', 'Chat Sessions')}</RTLText>
                </div>
                <Users size={24} className="text-green-400" />
              </RTLFlex>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <RTLFlex className="items-center justify-between">
                <div>
                  <RTLText as="h3" className="text-2xl font-bold text-blue-400">
                    {stats.average_response_time.toFixed(1)}s
                  </RTLText>
                  <RTLText className="text-gray-400">{t('analytics.avgResponseTime', 'Avg Response Time')}</RTLText>
                </div>
                <Clock size={24} className="text-blue-400" />
              </RTLFlex>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <RTLFlex className="items-center justify-between">
                <div>
                  <RTLText as="h3" className="text-2xl font-bold text-yellow-400">
                    {stats.language_distribution.arabic}%
                  </RTLText>
                  <RTLText className="text-gray-400">{t('analytics.arabicUsage', 'Arabic Usage')}</RTLText>
                </div>
                <Globe size={24} className="text-yellow-400" />
              </RTLFlex>
            </div>
          </div>
        )}

        {/* Enhanced Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {features.map((feature, index) => (
            <Link
              key={index}
              to={feature.link}
              className="group block"
            >
              <div className={`bg-gradient-to-br ${getColorClasses(feature.color)} rounded-xl p-6 border backdrop-blur-sm transition-all duration-300 hover:scale-105 hover:shadow-2xl`}>
                <RTLFlex className="items-center justify-between mb-4">
                  <feature.icon size={32} className="text-white" />
                  <ArrowRight size={20} className="text-white/70 group-hover:text-white group-hover:translate-x-1 transition-all duration-300" />
                </RTLFlex>

                <RTLText as="h3" className="text-xl font-bold text-white mb-2">
                  {feature.title}
                </RTLText>

                <RTLText className="text-white/80 mb-4 text-sm">
                  {feature.description}
                </RTLText>

                <RTLText className="text-white/60 text-xs font-medium">
                  {feature.stats}
                </RTLText>
              </div>
            </Link>
          ))}
        </div>

        {/* Recent Activity & Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Activity */}
          <div className="bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 p-6">
            <RTLText as="h3" className={`text-lg font-semibold mb-4 flex items-center text-white ${isRTL ? "flex-row-reverse" : ""}`}>
              <Activity size={20} className={`text-green-400 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
              {t('dashboard.recentActivity', 'Recent Activity')}
            </RTLText>

            {stats && (
              <div className="space-y-3">
                <RTLFlex className="justify-between items-center p-3 bg-white/5 rounded-lg">
                  <RTLText className="text-gray-300">{t('dashboard.messagesLast30Days', 'Messages (Last 30 Days)')}</RTLText>
                  <RTLText className="text-purple-400 font-semibold">{stats.recent_activity.messages_last_30_days}</RTLText>
                </RTLFlex>

                <RTLFlex className="justify-between items-center p-3 bg-white/5 rounded-lg">
                  <RTLText className="text-gray-300">{t('dashboard.sessionsLast30Days', 'Sessions (Last 30 Days)')}</RTLText>
                  <RTLText className="text-green-400 font-semibold">{stats.recent_activity.sessions_last_30_days}</RTLText>
                </RTLFlex>

                <RTLFlex className="justify-between items-center p-3 bg-white/5 rounded-lg">
                  <RTLText className="text-gray-300">{t('dashboard.arabicPreference', 'Arabic Preference')}</RTLText>
                  <RTLText className="text-yellow-400 font-semibold">{stats.language_distribution.arabic}%</RTLText>
                </RTLFlex>
              </div>
            )}
          </div>

          {/* Quick Actions */}
          <div className="bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 p-6">
            <RTLText as="h3" className={`text-lg font-semibold mb-4 flex items-center text-white ${isRTL ? "flex-row-reverse" : ""}`}>
              <Zap size={20} className={`text-yellow-400 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
              {t('dashboard.quickActions', 'Quick Actions')}
            </RTLText>

            <div className="space-y-3">
              <Link
                to="/chat/enhanced"
                className={`flex items-center p-3 bg-white/5 hover:bg-white/10 rounded-lg transition-colors group ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <Bot size={20} className={`text-purple-400 ${language === 'ar' ? 'ml-3' : 'mr-3'}`} />
                <RTLText className="text-gray-300 group-hover:text-white">
                  {t('dashboard.startEnhancedChat', 'Start Enhanced Chat')}
                </RTLText>
                <ArrowRight size={16} className={`text-gray-400 group-hover:text-white ${language === 'ar' ? 'mr-auto' : 'ml-auto'}`} />
              </Link>

              <Link
                to="/dashboard/business-plan"
                className={`flex items-center p-3 bg-white/5 hover:bg-white/10 rounded-lg transition-colors group ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <Target size={20} className={`text-green-400 ${language === 'ar' ? 'ml-3' : 'mr-3'}`} />
                <RTLText className="text-gray-300 group-hover:text-white">
                  {t('dashboard.generateBusinessPlan', 'Generate Business Plan')}
                </RTLText>
                <ArrowRight size={16} className={`text-gray-400 group-hover:text-white ${language === 'ar' ? 'mr-auto' : 'ml-auto'}`} />
              </Link>

              <Link
                to="/chat/analytics"
                className={`flex items-center p-3 bg-white/5 hover:bg-white/10 rounded-lg transition-colors group ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <BarChart3 size={20} className={`text-blue-400 ${language === 'ar' ? 'ml-3' : 'mr-3'}`} />
                <RTLText className="text-gray-300 group-hover:text-white">
                  {t('dashboard.viewAnalytics', 'View Analytics')}
                </RTLText>
                <ArrowRight size={16} className={`text-gray-400 group-hover:text-white ${language === 'ar' ? 'mr-auto' : 'ml-auto'}`} />
              </Link>
            </div>
          </div>
        </div>
      </div>
        </div>
      </div>
  );
};

export default EnhancedDashboardPage;
