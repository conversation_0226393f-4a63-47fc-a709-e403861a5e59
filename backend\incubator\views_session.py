"""
Views for managing mentorship sessions, including scheduling and video conferencing.
"""
from rest_framework import viewsets, permissions, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from django.db.models import Q, Avg
from django_filters.rest_framework import DjangoFilterBackend
from django.contrib.auth.models import User

from .models import (
    MentorshipMatch, MentorshipSession, MentorshipFeedback
)
from .serializers import (
    MentorshipSessionSerializer, MentorshipFeedbackSerializer
)
from .video_conferencing import create_video_meeting, VideoConferencingError
from .calendar_integration import get_calendar_provider, CalendarIntegrationError
from .tasks import notify_session_scheduled, notify_session_cancelled
from api.permissions import IsAdminUser


class MentorshipSessionViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing mentorship sessions with scheduling and video conferencing
    """
    queryset = MentorshipSession.objects.all().order_by('-scheduled_at')
    serializer_class = MentorshipSessionSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['mentorship_match', 'status', 'session_type']
    search_fields = ['title', 'description']
    ordering_fields = ['scheduled_at', 'created_at', 'updated_at']

    def perform_create(self, serializer):
        """Create a new session and send notifications"""
        session = serializer.save()

        # Send notifications
        notify_session_scheduled(session)

    def get_queryset(self):
        """
        Filter sessions based on user role:
        - Admins see all sessions
        - Mentors see their sessions
        - Mentees see their sessions
        """
        user = self.request.user
        if not user.is_authenticated:
            return MentorshipSession.objects.none()

        if user.is_staff or user.is_superuser:
            return super().get_queryset()

        # Check if user is a mentor
        try:
            mentor_profile = user.mentor_profile
            mentor_matches = mentor_profile.mentorship_matches.all()
            return MentorshipSession.objects.filter(mentorship_match__in=mentor_matches)
        except:
            # User is not a mentor, check if they're a mentee
            mentee_matches = user.mentorship_matches.all()
            return MentorshipSession.objects.filter(mentorship_match__in=mentee_matches)

    @action(detail=False, methods=['get'])
    def upcoming(self, request):
        """Get upcoming sessions for the current user"""
        now = timezone.now()
        queryset = self.get_queryset().filter(
            scheduled_at__gte=now,
            status__in=['scheduled', 'rescheduled']
        ).order_by('scheduled_at')

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def past(self, request):
        """Get past sessions for the current user"""
        now = timezone.now()
        queryset = self.get_queryset().filter(
            Q(scheduled_at__lt=now) | Q(status='completed')
        ).order_by('-scheduled_at')

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def create_meeting(self, request, pk=None):
        """
        Create a video conference meeting for this session

        Required parameters:
        - video_provider: The video conferencing provider to use (jitsi, zoom, etc.)

        Optional parameters:
        - meeting_link: Custom meeting link (required if provider is 'custom')
        """
        session = self.get_object()

        # Check if session already has a meeting
        if session.meeting_link:
            return Response(
                {"error": "This session already has a meeting link"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get provider from request
        video_provider = request.data.get('video_provider')
        if not video_provider:
            return Response(
                {"error": "Video provider is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Create meeting
            meeting_details = create_video_meeting(
                provider_name=video_provider,
                title=session.title,
                start_time=session.scheduled_at,
                duration_minutes=session.duration_minutes,
                meeting_link=request.data.get('meeting_link')  # For custom provider
            )

            # Update session with meeting details
            session.video_provider = meeting_details['provider']
            session.meeting_id = meeting_details.get('meeting_id')
            session.meeting_password = meeting_details.get('meeting_password')
            session.meeting_link = meeting_details['meeting_link']
            session.save()

            return Response({
                "message": "Meeting created successfully",
                "meeting_details": meeting_details
            })

        except VideoConferencingError as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['post'])
    def start_session(self, request, pk=None):
        """Mark a session as in progress and record start time"""
        session = self.get_object()

        if session.status not in ['scheduled', 'rescheduled']:
            return Response(
                {"error": f"Cannot start a session with status '{session.status}'"},
                status=status.HTTP_400_BAD_REQUEST
            )

        session.status = 'in_progress'
        session.started_at = timezone.now()
        session.save()

        serializer = self.get_serializer(session)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def complete_session(self, request, pk=None):
        """Mark a session as completed and record end time"""
        session = self.get_object()

        if session.status != 'in_progress':
            return Response(
                {"error": "Only in-progress sessions can be completed"},
                status=status.HTTP_400_BAD_REQUEST
            )

        session.status = 'completed'
        session.ended_at = timezone.now()
        session.save()

        serializer = self.get_serializer(session)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def cancel_session(self, request, pk=None):
        """Cancel a scheduled session"""
        session = self.get_object()

        if session.status not in ['scheduled', 'rescheduled']:
            return Response(
                {"error": f"Cannot cancel a session with status '{session.status}'"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get cancellation reason if provided
        cancellation_reason = request.data.get('cancellation_reason')

        # Update session status
        session.status = 'cancelled'
        session.save()

        # Send cancellation notifications
        notify_session_cancelled(session, cancellation_reason)

        serializer = self.get_serializer(session)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def reschedule_session(self, request, pk=None):
        """Reschedule a session to a new time"""
        session = self.get_object()

        if session.status not in ['scheduled', 'rescheduled']:
            return Response(
                {"error": f"Cannot reschedule a session with status '{session.status}'"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get new scheduled time
        scheduled_at = request.data.get('scheduled_at')
        if not scheduled_at:
            return Response(
                {"error": "New scheduled time is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update session
        session.scheduled_at = scheduled_at
        session.status = 'rescheduled'
        session.save()

        serializer = self.get_serializer(session)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def add_to_calendar(self, request, pk=None):
        """
        Add session to user's calendar

        Required parameters:
        - calendar_provider: The calendar provider to use (google_calendar, outlook, etc.)
        """
        session = self.get_object()
        user = request.user

        # Get provider from request
        calendar_provider = request.data.get('calendar_provider')
        if not calendar_provider:
            return Response(
                {"error": "Calendar provider is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Get calendar provider
            provider = get_calendar_provider(calendar_provider)

            # Add event to calendar
            event_details = provider.add_event(session.id, user.id)

            # Update session with calendar provider
            session.calendar_provider = calendar_provider
            session.save(update_fields=['calendar_provider'])

            return Response({
                "message": "Session added to calendar successfully",
                "event_details": event_details
            })

        except CalendarIntegrationError as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['post'])
    def update_calendar(self, request, pk=None):
        """Update session in user's calendar"""
        session = self.get_object()
        user = request.user

        if not session.calendar_provider:
            return Response(
                {"error": "This session is not linked to a calendar"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Get calendar provider
            provider = get_calendar_provider(session.calendar_provider)

            # Update event in calendar
            event_details = provider.update_event(session.id, user.id)

            return Response({
                "message": "Calendar event updated successfully",
                "event_details": event_details
            })

        except CalendarIntegrationError as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['post'])
    def remove_from_calendar(self, request, pk=None):
        """Remove session from user's calendar"""
        session = self.get_object()
        user = request.user

        if not session.calendar_provider:
            return Response(
                {"error": "This session is not linked to a calendar"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Get calendar provider
            provider = get_calendar_provider(session.calendar_provider)

            # Delete event from calendar
            provider.delete_event(session.id, user.id)

            # Clear calendar fields for this user
            is_mentor = hasattr(user, 'mentor_profile') and user.mentor_profile == session.mentorship_match.mentor

            if is_mentor:
                session.mentor_calendar_event_id = None
            else:
                session.mentee_calendar_event_id = None

            # If both mentor and mentee have removed the event, clear the provider
            if not session.mentor_calendar_event_id and not session.mentee_calendar_event_id:
                session.calendar_provider = None

            session.save()

            return Response({
                "message": "Session removed from calendar successfully"
            })

        except CalendarIntegrationError as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['get'])
    def get_availability(self, request):
        """
        Get user's availability from their calendar

        Required parameters:
        - start_date: Start date for availability check (YYYY-MM-DD)
        - end_date: End date for availability check (YYYY-MM-DD)
        - calendar_provider: The calendar provider to use (google_calendar, outlook, etc.)
        """
        user = request.user

        # Get parameters from request
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        calendar_provider = request.query_params.get('calendar_provider')

        if not all([start_date, end_date, calendar_provider]):
            return Response(
                {"error": "start_date, end_date, and calendar_provider are required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Parse dates
            from datetime import datetime
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

            # Get calendar provider
            provider = get_calendar_provider(calendar_provider)

            # Get availability
            available_slots = provider.get_user_availability(user.id, start_date, end_date)

            return Response({
                "available_slots": available_slots
            })

        except ValueError:
            return Response(
                {"error": "Invalid date format. Use YYYY-MM-DD"},
                status=status.HTTP_400_BAD_REQUEST
            )
        except CalendarIntegrationError as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
