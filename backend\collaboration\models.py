"""
Real-time Collaboration Models for Yasmeen AI Platform
Enables collaborative editing and real-time features
"""

from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
import json
import uuid

class CollaborationSession(models.Model):
    """Real-time collaboration session for business plans and documents"""
    
    SESSION_TYPES = (
        ('business_plan', 'Business Plan'),
        ('document', 'Document'),
        ('brainstorm', 'Brainstorming'),
        ('meeting', 'Meeting'),
    )
    
    STATUS_CHOICES = (
        ('active', 'Active'),
        ('paused', 'Paused'),
        ('ended', 'Ended'),
    )
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=200)
    session_type = models.CharField(max_length=20, choices=SESSION_TYPES)
    business_idea = models.ForeignKey(
        'incubator.BusinessIdea', 
        on_delete=models.CASCADE, 
        related_name='collaboration_sessions',
        null=True, blank=True
    )
    
    # Session management
    host = models.ForeignKey(User, on_delete=models.CASCADE, related_name='hosted_sessions')
    participants = models.ManyToManyField(User, through='SessionParticipant', related_name='collaboration_sessions')
    max_participants = models.PositiveIntegerField(default=10)
    
    # Session state
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    document_content = models.JSONField(default=dict, help_text="Current document state")
    version = models.PositiveIntegerField(default=1)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    started_at = models.DateTimeField(default=timezone.now)
    ended_at = models.DateTimeField(null=True, blank=True)
    
    # Settings
    is_public = models.BooleanField(default=False)
    allow_anonymous = models.BooleanField(default=False)
    recording_enabled = models.BooleanField(default=False)
    
    def __str__(self):
        return f"Collaboration: {self.title}"
    
    @property
    def duration(self):
        """Calculate session duration"""
        end_time = self.ended_at or timezone.now()
        return end_time - self.started_at
    
    @property
    def active_participants_count(self):
        """Count of currently active participants"""
        return self.session_participants.filter(is_active=True).count()
    
    class Meta:
        ordering = ['-created_at']

class SessionParticipant(models.Model):
    """Participants in collaboration sessions"""
    
    ROLE_CHOICES = (
        ('host', 'Host'),
        ('editor', 'Editor'),
        ('viewer', 'Viewer'),
        ('commenter', 'Commenter'),
    )
    
    session = models.ForeignKey(CollaborationSession, on_delete=models.CASCADE, related_name='session_participants')
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    role = models.CharField(max_length=20, choices=ROLE_CHOICES, default='viewer')
    
    # Participation tracking
    joined_at = models.DateTimeField(auto_now_add=True)
    last_seen = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)
    
    # Permissions
    can_edit = models.BooleanField(default=False)
    can_comment = models.BooleanField(default=True)
    can_invite = models.BooleanField(default=False)
    
    def __str__(self):
        return f"{self.user.username} in {self.session.title}"
    
    class Meta:
        unique_together = ['session', 'user']
        ordering = ['joined_at']

class DocumentEdit(models.Model):
    """Track document edits for version control and real-time sync"""
    
    EDIT_TYPES = (
        ('insert', 'Insert'),
        ('delete', 'Delete'),
        ('format', 'Format'),
        ('replace', 'Replace'),
    )
    
    session = models.ForeignKey(CollaborationSession, on_delete=models.CASCADE, related_name='edits')
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    
    # Edit details
    edit_type = models.CharField(max_length=20, choices=EDIT_TYPES)
    position = models.PositiveIntegerField(help_text="Character position in document")
    content = models.TextField(help_text="Content that was inserted/deleted")
    length = models.PositiveIntegerField(default=0, help_text="Length of edit")
    
    # Metadata
    timestamp = models.DateTimeField(auto_now_add=True)
    version = models.PositiveIntegerField()
    operation_id = models.UUIDField(default=uuid.uuid4, help_text="Unique operation identifier")
    
    # Conflict resolution
    is_applied = models.BooleanField(default=True)
    conflicts_with = models.ManyToManyField('self', blank=True, symmetrical=False)
    
    def __str__(self):
        return f"{self.edit_type} by {self.user.username} at {self.position}"
    
    class Meta:
        ordering = ['timestamp']

class Comment(models.Model):
    """Comments on specific parts of documents"""
    
    STATUS_CHOICES = (
        ('open', 'Open'),
        ('resolved', 'Resolved'),
        ('archived', 'Archived'),
    )
    
    session = models.ForeignKey(CollaborationSession, on_delete=models.CASCADE, related_name='comments')
    author = models.ForeignKey(User, on_delete=models.CASCADE)
    
    # Comment content
    content = models.TextField()
    position = models.PositiveIntegerField(help_text="Character position in document")
    selection_start = models.PositiveIntegerField(default=0)
    selection_end = models.PositiveIntegerField(default=0)
    
    # Comment thread
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='replies')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='open')
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    resolved_at = models.DateTimeField(null=True, blank=True)
    resolved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='resolved_comments')
    
    def __str__(self):
        return f"Comment by {self.author.username}: {self.content[:50]}..."
    
    @property
    def is_thread_starter(self):
        return self.parent is None
    
    @property
    def reply_count(self):
        return self.replies.count()
    
    class Meta:
        ordering = ['created_at']

class SessionInvitation(models.Model):
    """Invitations to collaboration sessions"""
    
    STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('accepted', 'Accepted'),
        ('declined', 'Declined'),
        ('expired', 'Expired'),
    )
    
    session = models.ForeignKey(CollaborationSession, on_delete=models.CASCADE, related_name='invitations')
    invited_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sent_invitations')
    invited_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='received_invitations', null=True, blank=True)
    email = models.EmailField(null=True, blank=True, help_text="For inviting non-users")
    
    # Invitation details
    role = models.CharField(max_length=20, choices=SessionParticipant.ROLE_CHOICES, default='viewer')
    message = models.TextField(blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    responded_at = models.DateTimeField(null=True, blank=True)
    
    # Security
    invitation_token = models.UUIDField(default=uuid.uuid4, unique=True)
    
    def __str__(self):
        recipient = self.invited_user.username if self.invited_user else self.email
        return f"Invitation to {recipient} for {self.session.title}"
    
    @property
    def is_expired(self):
        return timezone.now() > self.expires_at
    
    class Meta:
        ordering = ['-created_at']
        unique_together = ['session', 'invited_user']

class SessionRecording(models.Model):
    """Recordings of collaboration sessions"""
    
    session = models.OneToOneField(CollaborationSession, on_delete=models.CASCADE, related_name='recording')
    
    # Recording metadata
    file_path = models.CharField(max_length=500)
    file_size = models.PositiveBigIntegerField(default=0)
    duration = models.DurationField()
    format = models.CharField(max_length=20, default='mp4')
    
    # Processing status
    is_processed = models.BooleanField(default=False)
    processing_started_at = models.DateTimeField(null=True, blank=True)
    processing_completed_at = models.DateTimeField(null=True, blank=True)
    
    # Access control
    is_public = models.BooleanField(default=False)
    download_count = models.PositiveIntegerField(default=0)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"Recording of {self.session.title}"
    
    class Meta:
        ordering = ['-created_at']
