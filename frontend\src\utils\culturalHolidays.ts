import i18n from 'i18next';

/**
 * Cultural holidays and special dates utility
 * Provides functions for working with holidays and special dates based on cultural preferences
 */

// Define holidays by locale and calendar type
export const HOLIDAYS = {
  // English (US) holidays - Gregorian calendar
  en: {
    // Fixed date holidays (month, day)
    fixed: [
      { month: 1, day: 1, name: 'New Year\'s Day', type: 'public' },
      { month: 2, day: 14, name: '<PERSON>\'s Day', type: 'cultural' },
      { month: 3, day: 17, name: 'St. Patrick\'s Day', type: 'cultural' },
      { month: 7, day: 4, name: 'Independence Day', type: 'public' },
      { month: 10, day: 31, name: 'Halloween', type: 'cultural' },
      { month: 11, day: 11, name: 'Veterans Day', type: 'public' },
      { month: 12, day: 25, name: 'Christmas Day', type: 'public' },
      { month: 12, day: 31, name: 'New Year\'s Eve', type: 'cultural' },
    ],
    // Variable date holidays (calculated each year)
    variable: [
      { 
        name: '<PERSON> Jr. <PERSON>', 
        type: 'public',
        calculate: (year: number) => getNthDayOfMonth(year, 1, 1, 3) // 3rd Monday in January
      },
      { 
        name: 'Presidents\' Day', 
        type: 'public',
        calculate: (year: number) => getNthDayOfMonth(year, 2, 1, 3) // 3rd Monday in February
      },
      { 
        name: 'Memorial Day', 
        type: 'public',
        calculate: (year: number) => getLastDayOfMonth(year, 5, 1) // Last Monday in May
      },
      { 
        name: 'Labor Day', 
        type: 'public',
        calculate: (year: number) => getNthDayOfMonth(year, 9, 1, 1) // 1st Monday in September
      },
      { 
        name: 'Columbus Day', 
        type: 'public',
        calculate: (year: number) => getNthDayOfMonth(year, 10, 1, 2) // 2nd Monday in October
      },
      { 
        name: 'Thanksgiving Day', 
        type: 'public',
        calculate: (year: number) => getNthDayOfMonth(year, 11, 4, 4) // 4th Thursday in November
      },
      {
        name: 'Easter',
        type: 'religious',
        calculate: (year: number) => calculateEaster(year)
      }
    ]
  },
  
  // Arabic holidays - includes both Gregorian and Hijri calendar holidays
  ar: {
    // Fixed date holidays (month, day) - Gregorian calendar
    fixed: [
      { month: 1, day: 1, name: 'رأس السنة الميلادية', type: 'public' }, // New Year's Day
      { month: 5, day: 1, name: 'عيد العمال', type: 'public' }, // Labor Day
    ],
    // Variable date holidays (calculated each year) - includes Hijri calendar holidays
    variable: [
      {
        name: 'رمضان', // Ramadan
        type: 'religious',
        calculate: (year: number) => calculateHijriDate(year, 9, 1) // 1st of Ramadan
      },
      {
        name: 'عيد الفطر', // Eid al-Fitr
        type: 'religious',
        calculate: (year: number) => calculateHijriDate(year, 10, 1) // 1st of Shawwal
      },
      {
        name: 'عيد الأضحى', // Eid al-Adha
        type: 'religious',
        calculate: (year: number) => calculateHijriDate(year, 12, 10) // 10th of Dhu al-Hijjah
      },
      {
        name: 'رأس السنة الهجرية', // Islamic New Year
        type: 'religious',
        calculate: (year: number) => calculateHijriDate(year, 1, 1) // 1st of Muharram
      },
      {
        name: 'المولد النبوي', // Mawlid (Prophet's Birthday)
        type: 'religious',
        calculate: (year: number) => calculateHijriDate(year, 3, 12) // 12th of Rabi' al-Awwal
      }
    ]
  }
};

// Helper function to get the nth occurrence of a specific day in a month
function getNthDayOfMonth(year: number, month: number, dayOfWeek: number, n: number): Date {
  const firstDay = new Date(year, month - 1, 1);
  const firstDayOfWeek = firstDay.getDay();
  
  // Calculate the date of the first occurrence of the specified day of the week
  let dayOfMonth = 1 + (dayOfWeek - firstDayOfWeek + 7) % 7;
  
  // Add (n-1) weeks to get the nth occurrence
  dayOfMonth += (n - 1) * 7;
  
  return new Date(year, month - 1, dayOfMonth);
}

// Helper function to get the last occurrence of a specific day in a month
function getLastDayOfMonth(year: number, month: number, dayOfWeek: number): Date {
  // Get the last day of the month
  const lastDay = new Date(year, month, 0);
  const lastDate = lastDay.getDate();
  
  // Find the last occurrence of the specified day of the week
  const dayOfMonth = lastDate - ((lastDay.getDay() - dayOfWeek + 7) % 7);
  
  return new Date(year, month - 1, dayOfMonth);
}

// Helper function to calculate Easter date (Gregorian calendar)
function calculateEaster(year: number): Date {
  // This is a simplified version of the Meeus/Jones/Butcher algorithm
  const a = year % 19;
  const b = Math.floor(year / 100);
  const c = year % 100;
  const d = Math.floor(b / 4);
  const e = b % 4;
  const f = Math.floor((b + 8) / 25);
  const g = Math.floor((b - f + 1) / 3);
  const h = (19 * a + b - d - g + 15) % 30;
  const i = Math.floor(c / 4);
  const k = c % 4;
  const l = (32 + 2 * e + 2 * i - h - k) % 7;
  const m = Math.floor((a + 11 * h + 22 * l) / 451);
  const month = Math.floor((h + l - 7 * m + 114) / 31);
  const day = ((h + l - 7 * m + 114) % 31) + 1;
  
  return new Date(year, month - 1, day);
}

// Helper function to approximate Hijri calendar dates in Gregorian calendar
// Note: This is a simplified approximation and may be off by 1-2 days
function calculateHijriDate(gregorianYear: number, hijriMonth: number, hijriDay: number): Date {
  // This is a very simplified approximation
  // For a real application, you would use a proper Hijri calendar library
  
  // Approximate the Hijri year based on the Gregorian year
  const hijriYear = Math.floor(gregorianYear - 622 + (gregorianYear - 622) / 32);
  
  // Approximate days since the start of the Hijri calendar
  const daysInHijriYear = 354.367; // Average
  const daysSinceHijriStart = (hijriYear - 1) * daysInHijriYear + (hijriMonth - 1) * 29.5 + hijriDay;
  
  // Convert to Gregorian date (very approximate)
  const gregorianDate = new Date(622, 6, 16); // Approximate start of Hijri calendar in Gregorian
  gregorianDate.setDate(gregorianDate.getDate() + Math.floor(daysSinceHijriStart));
  
  return gregorianDate;
}

// Type for holiday
export interface Holiday {
  name: string;
  date: Date;
  type: 'public' | 'religious' | 'cultural';
}

/**
 * Get holidays for a specific year based on the current locale
 * @param year Year to get holidays for
 * @param lang Optional language code
 * @returns Array of holidays
 */
export const getHolidays = (year: number, lang?: string): Holiday[] => {
  const currentLang = lang || i18n.language || 'en';
  const localeHolidays = HOLIDAYS[currentLang as keyof typeof HOLIDAYS] || HOLIDAYS.en;
  
  const holidays: Holiday[] = [];
  
  // Add fixed date holidays
  localeHolidays.fixed.forEach(holiday => {
    holidays.push({
      name: holiday.name,
      date: new Date(year, holiday.month - 1, holiday.day),
      type: holiday.type as 'public' | 'religious' | 'cultural',
    });
  });
  
  // Add variable date holidays
  localeHolidays.variable.forEach(holiday => {
    holidays.push({
      name: holiday.name,
      date: holiday.calculate(year),
      type: holiday.type as 'public' | 'religious' | 'cultural',
    });
  });
  
  // Sort holidays by date
  return holidays.sort((a, b) => a.date.getTime() - b.date.getTime());
};

/**
 * Check if a date is a holiday
 * @param date Date to check
 * @param lang Optional language code
 * @returns Holiday object if the date is a holiday, null otherwise
 */
export const isHoliday = (date: Date, lang?: string): Holiday | null => {
  const year = date.getFullYear();
  const holidays = getHolidays(year, lang);
  
  // Check if the date matches any holiday
  const holiday = holidays.find(h => 
    h.date.getDate() === date.getDate() && 
    h.date.getMonth() === date.getMonth() && 
    h.date.getFullYear() === date.getFullYear()
  );
  
  return holiday || null;
};

/**
 * Get upcoming holidays
 * @param count Number of upcoming holidays to get
 * @param lang Optional language code
 * @returns Array of upcoming holidays
 */
export const getUpcomingHolidays = (count: number = 5, lang?: string): Holiday[] => {
  const today = new Date();
  const currentYear = today.getFullYear();
  
  // Get holidays for current and next year
  const holidays = [
    ...getHolidays(currentYear, lang),
    ...getHolidays(currentYear + 1, lang),
  ];
  
  // Filter to only include upcoming holidays
  const upcomingHolidays = holidays.filter(holiday => 
    holiday.date.getTime() >= today.getTime()
  );
  
  // Return the specified number of upcoming holidays
  return upcomingHolidays.slice(0, count);
};

export default {
  getHolidays,
  isHoliday,
  getUpcomingHolidays,
};
