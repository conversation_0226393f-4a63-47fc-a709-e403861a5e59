import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';
import i18n from 'i18next';
import { languageAPI } from '../services/languageApi';
import { isRTLLanguage, getDirectionFromLanguage } from '../utils/rtl';

// Define the state interface
interface LanguageState {
  language: string;
  direction: 'ltr' | 'rtl';
  availableLanguages: Record<string, string>;
  isLoading: boolean;
  error: string | null;
}

// Get initial language from localStorage or default to 'en'
const getInitialLanguage = (): string => {
  const savedLanguage = localStorage.getItem('language');
  return savedLanguage || 'en';
};

// Get direction based on language
const getDirection = (language: string): 'ltr' | 'rtl' => {
  return getDirectionFromLanguage(language);
};

// Initialize language
export const initializeLanguage = () => {
  const language = getInitialLanguage();

  // Check if i18n is initialized before calling changeLanguage
  if (i18n.isInitialized) {
    i18n.changeLanguage(language);
  } else {
    // If i18n is not initialized, we'll set the language in the i18n.init options
    console.log('i18n not initialized yet, language will be set after initialization');
  }

  // Set document direction and language
  document.documentElement.dir = getDirection(language);
  document.documentElement.lang = language;

  return language;
};

// Async thunks
export const fetchLanguage = createAsyncThunk(
  'language/fetchLanguage',
  async (_, { rejectWithValue }) => {
    try {
      const response = await languageAPI.getLanguage();
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch language');
    }
  }
);

export const setLanguageAPI = createAsyncThunk(
  'language/setLanguageAPI',
  async (language: string, { rejectWithValue }) => {
    try {
      const response = await languageAPI.setLanguage(language);
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to set language');
    }
  }
);

// Initial state
const initialState: LanguageState = {
  language: getInitialLanguage(),
  direction: getDirection(getInitialLanguage()),
  availableLanguages: {
    en: 'English',
    ar: 'Arabic'
  },
  isLoading: false,
  error: null
};

// Create the slice
const languageSlice = createSlice({
  name: 'language',
  initialState,
  reducers: {
    setLanguage: (state, action: PayloadAction<string>) => {
      const language = action.payload;
      state.language = language;
      state.direction = getDirection(language);

      // Update i18n language
      i18n.changeLanguage(language);

      // Update HTML direction and language
      document.documentElement.dir = state.direction;
      document.documentElement.lang = language;

      // Save to localStorage
      localStorage.setItem('language', language);
    },
    toggleLanguage: (state) => {
      const newLanguage = state.language === 'en' ? 'ar' : 'en';
      state.language = newLanguage;
      state.direction = getDirection(newLanguage);

      // Update i18n language
      i18n.changeLanguage(newLanguage);

      // Update HTML direction and language
      document.documentElement.dir = state.direction;
      document.documentElement.lang = newLanguage;

      // Save to localStorage
      localStorage.setItem('language', newLanguage);
    },
  },
  extraReducers: (builder) => {
    builder
      // fetchLanguage
      .addCase(fetchLanguage.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchLanguage.fulfilled, (state, action) => {
        state.isLoading = false;
        state.language = action.payload.language;
        state.direction = getDirection(action.payload.language);
        state.availableLanguages = action.payload.available_languages;

        // Update i18n language
        i18n.changeLanguage(action.payload.language);

        // Update HTML direction and language
        document.documentElement.dir = state.direction;
        document.documentElement.lang = action.payload.language;

        // Save to localStorage
        localStorage.setItem('language', action.payload.language);
      })
      .addCase(fetchLanguage.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // setLanguageAPI
      .addCase(setLanguageAPI.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(setLanguageAPI.fulfilled, (state, action) => {
        state.isLoading = false;
        state.language = action.payload.language;
        state.direction = getDirection(action.payload.language);

        // Update i18n language
        i18n.changeLanguage(action.payload.language);

        // Update HTML direction and language
        document.documentElement.dir = state.direction;
        document.documentElement.lang = action.payload.language;

        // Save to localStorage
        localStorage.setItem('language', action.payload.language);
      })
      .addCase(setLanguageAPI.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

// Export actions and reducer
export const { setLanguage, toggleLanguage } = languageSlice.actions;
export default languageSlice.reducer;
