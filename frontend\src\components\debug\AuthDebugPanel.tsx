import React, { useState } from 'react';
import { useAppSelector } from '../../store/hooks';
import { useAuth } from '../../hooks/useAuth';
import { isSuperAdmin } from '../../utils/roleBasedRouting';
import { ChevronDown, ChevronUp, User, Shield, Key, Info } from 'lucide-react';

/**
 * Debug panel that shows authentication and role information
 * Only visible in development mode
 */
const AuthDebugPanel: React.FC = () => {
  const [isExpanded, setIsExpanded] = useState(false);
  const { user, isAuthenticated, isLoading, error } = useAppSelector(state => state.auth);
  const { getUserRoles, hasRole, isAdmin } = useAuth();

  // Only show in development and to super admins
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  // Only show to super admins
  if (!isAuthenticated || !user || !isSuperAdmin(user)) {
    return null;
  }

  const userRoles = getUserRoles();
  const isSuperAdminUser = isSuperAdmin(user);

  return (
    <div className="fixed bottom-4 right-4 z-50 bg-gray-900 border border-gray-700 rounded-lg shadow-lg max-w-sm">
      {/* Header */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full flex items-center justify-between p-3 text-white hover:bg-gray-800 rounded-t-lg transition-colors"
      >
        <div className="flex items-center gap-2">
          <Shield className="w-4 h-4" />
          <span className="text-sm font-medium">Auth Debug</span>
        </div>
        {isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
      </button>

      {/* Content */}
      {isExpanded && (
        <div className="p-3 border-t border-gray-700 text-xs text-gray-300 space-y-3">
          {/* Authentication Status */}
          <div>
            <div className="flex items-center gap-2 mb-1">
              <Key className="w-3 h-3" />
              <span className="font-medium text-white">Authentication</span>
            </div>
            <div className="pl-5 space-y-1">
              <div className="flex justify-between">
                <span>Authenticated:</span>
                <span className={isAuthenticated ? 'text-green-400' : 'text-red-400'}>
                  {isAuthenticated ? 'Yes' : 'No'}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Loading:</span>
                <span className={isLoading ? 'text-yellow-400' : 'text-gray-400'}>
                  {isLoading ? 'Yes' : 'No'}
                </span>
              </div>
              {error && (
                <div className="text-red-400 text-xs break-words">
                  Error: {error}
                </div>
              )}
            </div>
          </div>

          {/* User Information */}
          {user && (
            <div>
              <div className="flex items-center gap-2 mb-1">
                <User className="w-3 h-3" />
                <span className="font-medium text-white">User Info</span>
              </div>
              <div className="pl-5 space-y-1">
                <div className="flex justify-between">
                  <span>Username:</span>
                  <span className="text-blue-400">{user.username}</span>
                </div>
                <div className="flex justify-between">
                  <span>Email:</span>
                  <span className="text-blue-400 truncate max-w-32">{user.email}</span>
                </div>
                <div className="flex justify-between">
                  <span>Is Admin:</span>
                  <span className={user.is_admin ? 'text-green-400' : 'text-gray-400'}>
                    {user.is_admin ? 'Yes' : 'No'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Is Super Admin:</span>
                  <span className={isSuperAdminUser ? 'text-red-400' : 'text-gray-400'}>
                    {isSuperAdminUser ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Roles */}
          <div>
            <div className="flex items-center gap-2 mb-1">
              <Shield className="w-3 h-3" />
              <span className="font-medium text-white">Roles</span>
            </div>
            <div className="pl-5">
              {userRoles.length > 0 ? (
                <div className="flex flex-wrap gap-1">
                  {userRoles.map(role => (
                    <span
                      key={role}
                      className="px-2 py-1 bg-purple-600 text-white rounded text-xs"
                    >
                      {role}
                    </span>
                  ))}
                </div>
              ) : (
                <span className="text-gray-500">No roles</span>
              )}
            </div>
          </div>

          {/* Profile Information */}
          {user?.profile && (
            <div>
              <div className="flex items-center gap-2 mb-1">
                <Info className="w-3 h-3" />
                <span className="font-medium text-white">Profile</span>
              </div>
              <div className="pl-5 space-y-1">
                {user.profile.primary_role && (
                  <div className="flex justify-between">
                    <span>Primary Role:</span>
                    <span className="text-green-400">{user.profile.primary_role.name}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span>Active Roles:</span>
                  <span className="text-blue-400">
                    {user.profile.active_roles?.length || 0}
                  </span>
                </div>
                {user.profile.highest_permission_level && (
                  <div className="flex justify-between">
                    <span>Permission Level:</span>
                    <span className="text-yellow-400">{user.profile.highest_permission_level}</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Quick Role Tests */}
          <div>
            <div className="flex items-center gap-2 mb-1">
              <Shield className="w-3 h-3" />
              <span className="font-medium text-white">Role Tests</span>
            </div>
            <div className="pl-5 space-y-1 text-xs">
              <div className="flex justify-between">
                <span>hasRole('admin'):</span>
                <span className={hasRole('admin') ? 'text-green-400' : 'text-gray-400'}>
                  {hasRole('admin') ? 'Yes' : 'No'}
                </span>
              </div>
              <div className="flex justify-between">
                <span>hasRole('user'):</span>
                <span className={hasRole('user') ? 'text-green-400' : 'text-gray-400'}>
                  {hasRole('user') ? 'Yes' : 'No'}
                </span>
              </div>
              <div className="flex justify-between">
                <span>isAdmin():</span>
                <span className={isAdmin() ? 'text-green-400' : 'text-gray-400'}>
                  {isAdmin() ? 'Yes' : 'No'}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AuthDebugPanel;
