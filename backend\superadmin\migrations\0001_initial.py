# Generated by Django 5.1.7 on 2025-06-12 06:32

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="SystemHealth",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                ("cpu_usage", models.FloatField()),
                ("memory_usage", models.FloatField()),
                ("disk_usage", models.FloatField()),
                ("network_io", models.JSONField(blank=True, default=dict)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("healthy", "Healthy"),
                            ("warning", "Warning"),
                            ("critical", "Critical"),
                            ("offline", "Offline"),
                        ],
                        default="healthy",
                        max_length=20,
                    ),
                ),
                ("details", models.JSONField(blank=True, default=dict)),
            ],
            options={
                "verbose_name": "System Health Record",
                "verbose_name_plural": "System Health Records",
                "ordering": ["-timestamp"],
            },
        ),
        migrations.CreateModel(
            name="BackupRecord",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "backup_type",
                    models.CharField(
                        choices=[
                            ("full", "Full Backup"),
                            ("incremental", "Incremental"),
                            ("database", "Database Only"),
                            ("files", "Files Only"),
                        ],
                        default="full",
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("running", "Running"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("file_path", models.CharField(blank=True, max_length=500)),
                ("file_size", models.BigIntegerField(blank=True, null=True)),
                ("started_at", models.DateTimeField(auto_now_add=True)),
                ("completed_at", models.DateTimeField(blank=True, null=True)),
                ("error_message", models.TextField(blank=True)),
                ("metadata", models.JSONField(blank=True, default=dict)),
                (
                    "started_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Backup Record",
                "verbose_name_plural": "Backup Records",
                "ordering": ["-started_at"],
            },
        ),
        migrations.CreateModel(
            name="PerformanceMetric",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("metric_name", models.CharField(max_length=100)),
                ("metric_value", models.FloatField()),
                ("metric_unit", models.CharField(default="", max_length=20)),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("cpu", "CPU"),
                            ("memory", "Memory"),
                            ("disk", "Disk"),
                            ("network", "Network"),
                            ("database", "Database"),
                            ("api", "API Response"),
                            ("ai", "AI Performance"),
                        ],
                        default="system",
                        max_length=50,
                    ),
                ),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                ("metadata", models.JSONField(blank=True, default=dict)),
            ],
            options={
                "verbose_name": "Performance Metric",
                "verbose_name_plural": "Performance Metrics",
                "ordering": ["-timestamp"],
                "indexes": [
                    models.Index(
                        fields=["metric_name", "timestamp"],
                        name="superadmin__metric__bf4a30_idx",
                    ),
                    models.Index(
                        fields=["category", "timestamp"],
                        name="superadmin__categor_36fb2d_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="SecurityEvent",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "event_type",
                    models.CharField(
                        choices=[
                            ("login_attempt", "Login Attempt"),
                            ("failed_login", "Failed Login"),
                            ("permission_denied", "Permission Denied"),
                            ("suspicious_activity", "Suspicious Activity"),
                            ("data_breach", "Data Breach"),
                            ("unauthorized_access", "Unauthorized Access"),
                            ("malware_detected", "Malware Detected"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "severity",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                            ("critical", "Critical"),
                        ],
                        default="medium",
                        max_length=20,
                    ),
                ),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("user_agent", models.TextField(blank=True)),
                ("description", models.TextField()),
                ("is_resolved", models.BooleanField(default=False)),
                ("resolved_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("metadata", models.JSONField(blank=True, default=dict)),
                (
                    "resolved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="resolved_security_events",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Security Event",
                "verbose_name_plural": "Security Events",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="SuperAdminAuditLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("action", models.CharField(max_length=100)),
                ("resource", models.CharField(blank=True, max_length=100)),
                ("resource_id", models.CharField(blank=True, max_length=50)),
                ("details", models.JSONField(blank=True, default=dict)),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("user_agent", models.TextField(blank=True)),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                ("success", models.BooleanField(default=True)),
                ("error_message", models.TextField(blank=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Super Admin Audit Log",
                "verbose_name_plural": "Super Admin Audit Logs",
                "ordering": ["-timestamp"],
            },
        ),
        migrations.CreateModel(
            name="SystemAlert",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("message", models.TextField()),
                (
                    "alert_type",
                    models.CharField(
                        choices=[
                            ("info", "Information"),
                            ("warning", "Warning"),
                            ("error", "Error"),
                            ("critical", "Critical"),
                        ],
                        default="info",
                        max_length=20,
                    ),
                ),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("system", "System"),
                            ("security", "Security"),
                            ("performance", "Performance"),
                            ("ai", "AI System"),
                            ("backup", "Backup"),
                            ("user", "User Management"),
                        ],
                        default="system",
                        max_length=50,
                    ),
                ),
                ("is_resolved", models.BooleanField(default=False)),
                ("resolved_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("metadata", models.JSONField(blank=True, default=dict)),
                (
                    "resolved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="resolved_alerts",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "System Alert",
                "verbose_name_plural": "System Alerts",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="SystemConfiguration",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("key", models.CharField(max_length=100, unique=True)),
                ("value", models.TextField()),
                ("description", models.TextField(blank=True)),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("system", "System"),
                            ("security", "Security"),
                            ("performance", "Performance"),
                            ("ai", "AI Configuration"),
                            ("backup", "Backup"),
                            ("monitoring", "Monitoring"),
                        ],
                        default="system",
                        max_length=50,
                    ),
                ),
                ("is_sensitive", models.BooleanField(default=False)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "System Configuration",
                "verbose_name_plural": "System Configurations",
                "ordering": ["category", "key"],
            },
        ),
    ]
