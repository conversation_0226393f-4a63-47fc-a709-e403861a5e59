// Countries list with Syria as the first option
export const COUNTRIES = [
  { code: 'SY', name: 'Syria', nameAr: 'سوريا' },
  { code: 'AE', name: 'United Arab Emirates', nameAr: 'الإمارات العربية المتحدة' },
  { code: 'SA', name: 'Saudi Arabia', nameAr: 'المملكة العربية السعودية' },
  { code: 'EG', name: 'Egypt', nameAr: 'مصر' },
  { code: 'JO', name: 'Jordan', nameAr: 'الأردن' },
  { code: 'LB', name: 'Lebanon', nameAr: 'لبنان' },
  { code: 'IQ', name: 'Iraq', nameAr: 'العراق' },
  { code: 'KW', name: 'Kuwait', nameAr: 'الكويت' },
  { code: 'QA', name: 'Qatar', nameAr: 'قطر' },
  { code: 'BH', name: 'Bahrain', nameAr: 'البحرين' },
  { code: 'OM', name: 'Oman', nameAr: 'عُمان' },
  { code: 'YE', name: 'Yemen', nameAr: 'اليمن' },
  { code: 'PS', name: 'Palestine', nameAr: 'فلسطين' },
  { code: 'MA', name: 'Morocco', nameAr: 'المغرب' },
  { code: 'DZ', name: 'Algeria', nameAr: 'الجزائر' },
  { code: 'TN', name: 'Tunisia', nameAr: 'تونس' },
  { code: 'LY', name: 'Libya', nameAr: 'ليبيا' },
  { code: 'SD', name: 'Sudan', nameAr: 'السودان' },
  { code: 'TR', name: 'Turkey', nameAr: 'تركيا' },
  { code: 'US', name: 'United States', nameAr: 'الولايات المتحدة' },
  { code: 'CA', name: 'Canada', nameAr: 'كندا' },
  { code: 'GB', name: 'United Kingdom', nameAr: 'المملكة المتحدة' },
  { code: 'DE', name: 'Germany', nameAr: 'ألمانيا' },
  { code: 'FR', name: 'France', nameAr: 'فرنسا' },
  { code: 'IT', name: 'Italy', nameAr: 'إيطاليا' },
  { code: 'ES', name: 'Spain', nameAr: 'إسبانيا' },
  { code: 'NL', name: 'Netherlands', nameAr: 'هولندا' },
  { code: 'SE', name: 'Sweden', nameAr: 'السويد' },
  { code: 'NO', name: 'Norway', nameAr: 'النرويج' },
  { code: 'DK', name: 'Denmark', nameAr: 'الدنمارك' },
  { code: 'AU', name: 'Australia', nameAr: 'أستراليا' },
  { code: 'NZ', name: 'New Zealand', nameAr: 'نيوزيلندا' },
  { code: 'JP', name: 'Japan', nameAr: 'اليابان' },
  { code: 'KR', name: 'South Korea', nameAr: 'كوريا الجنوبية' },
  { code: 'CN', name: 'China', nameAr: 'الصين' },
  { code: 'IN', name: 'India', nameAr: 'الهند' },
  { code: 'SG', name: 'Singapore', nameAr: 'سنغافورة' },
  { code: 'MY', name: 'Malaysia', nameAr: 'ماليزيا' },
  { code: 'ID', name: 'Indonesia', nameAr: 'إندونيسيا' },
  { code: 'TH', name: 'Thailand', nameAr: 'تايلاند' },
  { code: 'PH', name: 'Philippines', nameAr: 'الفلبين' },
  { code: 'VN', name: 'Vietnam', nameAr: 'فيتنام' },
  { code: 'BD', name: 'Bangladesh', nameAr: 'بنغلاديش' },
  { code: 'PK', name: 'Pakistan', nameAr: 'باكستان' },
  { code: 'AF', name: 'Afghanistan', nameAr: 'أفغانستان' },
  { code: 'IR', name: 'Iran', nameAr: 'إيران' },
  { code: 'BR', name: 'Brazil', nameAr: 'البرازيل' },
  { code: 'AR', name: 'Argentina', nameAr: 'الأرجنتين' },
  { code: 'CL', name: 'Chile', nameAr: 'تشيلي' },
  { code: 'CO', name: 'Colombia', nameAr: 'كولومبيا' },
  { code: 'MX', name: 'Mexico', nameAr: 'المكسيك' },
  { code: 'ZA', name: 'South Africa', nameAr: 'جنوب أفريقيا' },
  { code: 'NG', name: 'Nigeria', nameAr: 'نيجيريا' },
  { code: 'KE', name: 'Kenya', nameAr: 'كينيا' },
  { code: 'ET', name: 'Ethiopia', nameAr: 'إثيوبيا' },
  { code: 'GH', name: 'Ghana', nameAr: 'غانا' },
  { code: 'RU', name: 'Russia', nameAr: 'روسيا' },
  { code: 'UA', name: 'Ukraine', nameAr: 'أوكرانيا' },
  { code: 'PL', name: 'Poland', nameAr: 'بولندا' },
  { code: 'CZ', name: 'Czech Republic', nameAr: 'جمهورية التشيك' },
  { code: 'HU', name: 'Hungary', nameAr: 'المجر' },
  { code: 'RO', name: 'Romania', nameAr: 'رومانيا' },
  { code: 'BG', name: 'Bulgaria', nameAr: 'بلغاريا' },
  { code: 'HR', name: 'Croatia', nameAr: 'كرواتيا' },
  { code: 'RS', name: 'Serbia', nameAr: 'صربيا' },
  { code: 'BA', name: 'Bosnia and Herzegovina', nameAr: 'البوسنة والهرسك' },
  { code: 'AL', name: 'Albania', nameAr: 'ألبانيا' },
  { code: 'MK', name: 'North Macedonia', nameAr: 'مقدونيا الشمالية' },
  { code: 'ME', name: 'Montenegro', nameAr: 'الجبل الأسود' },
  { code: 'SI', name: 'Slovenia', nameAr: 'سلوفينيا' },
  { code: 'SK', name: 'Slovakia', nameAr: 'سلوفاكيا' },
  { code: 'LT', name: 'Lithuania', nameAr: 'ليتوانيا' },
  { code: 'LV', name: 'Latvia', nameAr: 'لاتفيا' },
  { code: 'EE', name: 'Estonia', nameAr: 'إستونيا' },
  { code: 'FI', name: 'Finland', nameAr: 'فنلندا' },
  { code: 'IS', name: 'Iceland', nameAr: 'آيسلندا' },
  { code: 'IE', name: 'Ireland', nameAr: 'أيرلندا' },
  { code: 'PT', name: 'Portugal', nameAr: 'البرتغال' },
  { code: 'GR', name: 'Greece', nameAr: 'اليونان' },
  { code: 'CY', name: 'Cyprus', nameAr: 'قبرص' },
  { code: 'MT', name: 'Malta', nameAr: 'مالطا' },
  { code: 'LU', name: 'Luxembourg', nameAr: 'لوكسمبورغ' },
  { code: 'BE', name: 'Belgium', nameAr: 'بلجيكا' },
  { code: 'AT', name: 'Austria', nameAr: 'النمسا' },
  { code: 'CH', name: 'Switzerland', nameAr: 'سويسرا' },
  { code: 'LI', name: 'Liechtenstein', nameAr: 'ليختنشتاين' },
  { code: 'MC', name: 'Monaco', nameAr: 'موناكو' },
  { code: 'AD', name: 'Andorra', nameAr: 'أندورا' },
  { code: 'SM', name: 'San Marino', nameAr: 'سان مارينو' },
  { code: 'VA', name: 'Vatican City', nameAr: 'مدينة الفاتيكان' },
];

// Syrian states/provinces (governorates)
export const SYRIAN_STATES = [
  { code: 'DM', name: 'Damascus', nameAr: 'دمشق' },
  { code: 'DR', name: 'Daraa', nameAr: 'درعا' },
  { code: 'AL', name: 'Aleppo', nameAr: 'حلب' },
  { code: 'HM', name: 'Homs', nameAr: 'حمص' },
  { code: 'HA', name: 'Hama', nameAr: 'حماة' },
  { code: 'LA', name: 'Latakia', nameAr: 'اللاذقية' },
  { code: 'TA', name: 'Tartus', nameAr: 'طرطوس' },
  { code: 'ID', name: 'Idlib', nameAr: 'إدلب' },
  { code: 'RQ', name: 'Raqqa', nameAr: 'الرقة' },
  { code: 'DZ', name: 'Deir ez-Zor', nameAr: 'دير الزور' },
  { code: 'HS', name: 'Al-Hasakah', nameAr: 'الحسكة' },
  { code: 'QU', name: 'Quneitra', nameAr: 'القنيطرة' },
  { code: 'SW', name: 'As-Suwayda', nameAr: 'السويداء' },
  { code: 'RD', name: 'Rif Dimashq', nameAr: 'ريف دمشق' },
];

// Helper function to get country name based on language
export const getCountryName = (country: typeof COUNTRIES[0], language: string): string => {
  return language === 'ar' ? country.nameAr : country.name;
};

// Helper function to get state name based on language
export const getStateName = (state: typeof SYRIAN_STATES[0], language: string): string => {
  return language === 'ar' ? state.nameAr : state.name;
};
