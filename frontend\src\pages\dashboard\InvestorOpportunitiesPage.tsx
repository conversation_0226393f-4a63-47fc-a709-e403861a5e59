import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { useParams } from 'react-router-dom';
import AuthenticatedLayout from '../../components/layout/AuthenticatedLayout';
import { 
  Target, 
  TrendingUp, 
  DollarSign, 
  Calendar,
  Building,
  Star,
  Filter,
  Search
} from 'lucide-react';

interface InvestmentOpportunity {
  id: string;
  companyName: string;
  industry: string;
  stage: string;
  fundingGoal: number;
  currentFunding: number;
  valuation: number;
  roi_projection: number;
  risk_level: 'low' | 'medium' | 'high';
  description: string;
  deadline: string;
  rating: number;
}

const InvestorOpportunitiesPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { id } = useParams();
  const [opportunities, setOpportunities] = useState<InvestmentOpportunity[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedIndustry, setSelectedIndustry] = useState('all');
  const [selectedStage, setSelectedStage] = useState('all');

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setOpportunities([
        {
          id: '1',
          companyName: 'TechStart AI',
          industry: 'Artificial Intelligence',
          stage: 'Series A',
          fundingGoal: 5000000,
          currentFunding: 3200000,
          valuation: 25000000,
          roi_projection: 25,
          risk_level: 'medium',
          description: 'AI-powered business automation platform revolutionizing workflow management',
          deadline: '2024-02-15',
          rating: 4.5
        },
        {
          id: '2',
          companyName: 'GreenTech Solutions',
          industry: 'Clean Energy',
          stage: 'Seed',
          fundingGoal: 1500000,
          currentFunding: 800000,
          valuation: 8000000,
          roi_projection: 35,
          risk_level: 'high',
          description: 'Renewable energy storage solutions for residential and commercial use',
          deadline: '2024-01-30',
          rating: 4.2
        },
        {
          id: '3',
          companyName: 'HealthTech Pro',
          industry: 'Healthcare',
          stage: 'Series B',
          fundingGoal: 10000000,
          currentFunding: 7500000,
          valuation: 50000000,
          roi_projection: 20,
          risk_level: 'low',
          description: 'Digital health platform connecting patients with healthcare providers',
          deadline: '2024-03-01',
          rating: 4.8
        }
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'high': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const filteredOpportunities = opportunities.filter(opp => {
    const matchesSearch = opp.companyName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         opp.industry.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesIndustry = selectedIndustry === 'all' || opp.industry === selectedIndustry;
    const matchesStage = selectedStage === 'all' || opp.stage === selectedStage;
    
    return matchesSearch && matchesIndustry && matchesStage;
  });

  if (loading) {
    return (
      <AuthenticatedLayout>
      <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">{t('Loading opportunities...')}</p>
          </div>
        </div>
                </div>
        </div>
      </div>
    </AuthenticatedLayout>
    );
  }

  return (
    <AuthenticatedLayout>
      <div className="p-6">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center space-x-3 mb-2">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Target className="h-6 w-6 text-purple-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-white">{t('Investment Opportunities')}</h1>
              <p className="text-gray-300">{t('Discover and evaluate investment opportunities')}</p>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder={t('Search opportunities...')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-white/20 border border-white/30 rounded-md text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
            
            <select
              value={selectedIndustry}
              onChange={(e) => setSelectedIndustry(e.target.value)}
              className="px-4 py-2 bg-white/20 border border-white/30 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              <option value="all">{t('All Industries')}</option>
              <option value="Artificial Intelligence">{t('AI/ML')}</option>
              <option value="Clean Energy">{t('Clean Energy')}</option>
              <option value="Healthcare">{t('Healthcare')}</option>
            </select>

            <select
              value={selectedStage}
              onChange={(e) => setSelectedStage(e.target.value)}
              className="px-4 py-2 bg-white/20 border border-white/30 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              <option value="all">{t('All Stages')}</option>
              <option value="Seed">{t('Seed')}</option>
              <option value="Series A">{t('Series A')}</option>
              <option value="Series B">{t('Series B')}</option>
            </select>
          </div>
        </div>

        {/* Opportunities Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredOpportunities.map((opportunity) => (
            <div key={opportunity.id} className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-white">{opportunity.companyName}</h3>
                  <p className="text-gray-300">{opportunity.industry} • {opportunity.stage}</p>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRiskColor(opportunity.risk_level)}`}>
                    {opportunity.risk_level.toUpperCase()}
                  </span>
                  <div className="flex items-center">
                    <Star className="h-4 w-4 text-yellow-400 fill-current" />
                    <span className="text-sm text-gray-300 ml-1">{opportunity.rating}</span>
                  </div>
                </div>
              </div>
              
              <p className="text-gray-300 mb-4">{opportunity.description}</p>
              
              <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                <div>
                  <span className="text-gray-400">{t('Funding Goal')}: </span>
                  <span className="text-white font-medium">{formatCurrency(opportunity.fundingGoal)}</span>
                </div>
                <div>
                  <span className="text-gray-400">{t('Valuation')}: </span>
                  <span className="text-white font-medium">{formatCurrency(opportunity.valuation)}</span>
                </div>
                <div>
                  <span className="text-gray-400">{t('ROI Projection')}: </span>
                  <span className="text-green-400 font-medium">{opportunity.roi_projection}%</span>
                </div>
                <div>
                  <span className="text-gray-400">{t('Deadline')}: </span>
                  <span className="text-white font-medium">{new Date(opportunity.deadline).toLocaleDateString()}</span>
                </div>
              </div>
              
              <div className="mb-4">
                <div className="flex justify-between text-sm text-gray-400 mb-1">
                  <span>{t('Funding Progress')}</span>
                  <span>{Math.round((opportunity.currentFunding / opportunity.fundingGoal) * 100)}%</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div 
                    className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${(opportunity.currentFunding / opportunity.fundingGoal) * 100}%` }}
                  ></div>
                </div>
              </div>
              
              <div className="flex space-x-3">
                <button className="flex-1 px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors text-sm">
                  {t('View Details')}
                </button>
                <button className="flex-1 px-4 py-2 bg-white/20 text-white rounded-md hover:bg-white/30 transition-colors text-sm">
                  {t('Add to Watchlist')}
                </button>
              </div>
            </div>
          ))}
        </div>

        {filteredOpportunities.length === 0 && (
          <div className="text-center py-12">
            <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-300 mb-2">{t('No opportunities found')}</h3>
            <p className="text-gray-400">{t('Try adjusting your search criteria')}</p>
          </div>
        )}
      </div>
    </AuthenticatedLayout>
  );
};

export default InvestorOpportunitiesPage;
