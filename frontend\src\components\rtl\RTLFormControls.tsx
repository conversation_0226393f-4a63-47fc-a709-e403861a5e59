import React from 'react';
import { useLanguage } from '../../hooks/useLanguage';
interface RTLFormControlBaseProps {
  className?: string;
  style?: React.CSSProperties;
}

// Input props
interface RTLInputProps extends RTLFormControlBaseProps, React.InputHTMLAttributes<HTMLInputElement> {}

// Textarea props
interface RTLTextareaProps extends RTLFormControlBaseProps, React.TextareaHTMLAttributes<HTMLTextAreaElement> {}

// Select props
interface RTLSelectProps extends RTLFormControlBaseProps, React.SelectHTMLAttributes<HTMLSelectElement> {
  children: React.ReactNode;
}

// Checkbox props
interface RTLCheckboxProps extends RTLFormControlBaseProps, Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type'> {
  label?: string;
}

// Radio props
interface RTLRadioProps extends RTLFormControlBaseProps, Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type'> {
  label?: string;
}

/**
 * RTL-aware input component
 */
export const RTLInput: React.FC<RTLInputProps> = ({
  className = '',
  dir,
  style = {},
  ...props
}) => {
  const { isRTL: contextIsRTL } = useLanguage();
  const isRTL = dir ? dir === 'rtl' : contextIsRTL;

  return (
    <input
      className={`rtl-input ${className}`}
      dir={isRTL ? 'rtl' : 'ltr'}
      style={{
        textAlign: isRTL ? 'right' : 'left',
        ...style,
      }}
      {...props}
    />
  );
};

/**
 * RTL-aware textarea component
 */
export const RTLTextarea: React.FC<RTLTextareaProps> = ({
  className = '',
  dir,
  style = {},
  ...props
}) => {
  const { isRTL: contextIsRTL } = useLanguage();
  const isRTL = dir ? dir === 'rtl' : contextIsRTL;

  return (
    <textarea
      className={`rtl-textarea ${className}`}
      dir={isRTL ? 'rtl' : 'ltr'}
      style={{
        textAlign: isRTL ? 'right' : 'left',
        ...style,
      }}
      {...props}
    />
  );
};

/**
 * RTL-aware select component
 */
export const RTLSelect: React.FC<RTLSelectProps> = ({
  className = '',
  dir,
  style = {},
  children,
  ...props
}) => {
  const { isRTL: contextIsRTL } = useLanguage();
  const isRTL = dir ? dir === 'rtl' : contextIsRTL;

  return (
    <select
      className={`rtl-select ${className}`}
      dir={isRTL ? 'rtl' : 'ltr'}
      style={{
        textAlign: isRTL ? 'right' : 'left',
        ...style,
      }}
      {...props}
    >
      {children}
    </select>
  );
};

/**
 * RTL-aware checkbox component
 */
export const RTLCheckbox: React.FC<RTLCheckboxProps> = ({
  className = '',
  dir,
  style = {},
  label,
  id,
  ...props
}) => {
  const { isRTL: contextIsRTL } = useLanguage();
  const isRTL = dir ? dir === 'rtl' : contextIsRTL;
  const checkboxId = id || `checkbox-${Math.random().toString(36).substring(2, 9)}`;

  return (
    <div
      className={`rtl-checkbox-container flex items-center ${className}`}
      dir={isRTL ? 'rtl' : 'ltr'}
      style={{
        flexDirection: isRTL ? 'row-reverse' : 'row',
        ...style,
      }}
    >
      <input
        type="checkbox"
        id={checkboxId}
        className="flex-shrink-0"
        {...props}
      />
      {label && (
        <label
          htmlFor={checkboxId}
          className={isRTL ? 'mr-2' : 'ml-2'}
        >
          {label}
        </label>
      )}
    </div>
  );
};

/**
 * RTL-aware radio component
 */
export const RTLRadio: React.FC<RTLRadioProps> = ({
  className = '',
  dir,
  style = {},
  label,
  id,
  ...props
}) => {
  const { isRTL: contextIsRTL } = useLanguage();
  const isRTL = dir ? dir === 'rtl' : contextIsRTL;
  const radioId = id || `radio-${Math.random().toString(36).substring(2, 9)}`;

  return (
    <div
      className={`rtl-radio-container flex items-center ${className}`}
      dir={isRTL ? 'rtl' : 'ltr'}
      style={{
        flexDirection: isRTL ? 'row-reverse' : 'row',
        ...style,
      }}
    >
      <input
        type="radio"
        id={radioId}
        className="flex-shrink-0"
        {...props}
      />
      {label && (
        <label
          htmlFor={radioId}
          className={isRTL ? 'mr-2' : 'ml-2'}
        >
          {label}
        </label>
      )}
    </div>
  );
};
