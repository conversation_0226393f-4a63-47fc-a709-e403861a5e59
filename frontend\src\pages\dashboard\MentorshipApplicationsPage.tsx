import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { useAppSelector } from '../../store/hooks';
import { useCRUD } from '../../hooks/useCRUD';
import { MentorshipApplication, MentorProfile, mentorshipApplicationsAPI, mentorProfilesAPI } from '../../services/incubatorApi';
import MentorshipApplicationForm from '../../components/incubator/forms/MentorshipApplicationForm';
import { EnhancedCRUDTable } from '../../components/common';
import AuthenticatedLayout from '../../components/layout/AuthenticatedLayout';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  Users, 
  Clock,
  Calendar,
  AlertCircle,
  CheckCircle,
  XCircle,
  MessageSquare,
  User
} from 'lucide-react';

const MentorshipApplicationsPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { user } = useAppSelector(state => state.auth);
  
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [selectedApplication, setSelectedApplication] = useState<MentorshipApplication | null>(null);
  const [selectedMentor, setSelectedMentor] = useState<MentorProfile | null>(null);
  const [mentorProfiles, setMentorProfiles] = useState<MentorProfile[]>([]);

  // CRUD operations for mentorship applications
  const applicationsCRUD = useCRUD({
    create: async (data: Partial<MentorshipApplication>) => {
      if (!user) throw new Error('User not authenticated');
      return mentorshipApplicationsAPI.createMentorshipApplication({
        ...data,
        mentee_id: user.id
      });
    },
    read: () => mentorshipApplicationsAPI.getMentorshipApplications(),
    update: (id: number, data: Partial<MentorshipApplication>) => 
      mentorshipApplicationsAPI.updateMentorshipApplication(id, data),
    delete: (id: number) => mentorshipApplicationsAPI.deleteMentorshipApplication(id)
  }, {
    onSuccess: (operation) => {
      if (operation === 'create') {
        setShowCreateForm(false);
        setSelectedMentor(null);
      } else if (operation === 'update') {
        setShowEditForm(false);
        setSelectedApplication(null);
      } else if (operation === 'delete') {
        setShowDeleteDialog(false);
        setSelectedApplication(null);
      }
    }
  });

  // Load data on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        const [applications, mentors] = await Promise.all([
          mentorshipApplicationsAPI.getMentorshipApplications(),
          mentorProfilesAPI.getMentorProfiles()
        ]);
        
        applicationsCRUD.setData(applications);
        setMentorProfiles(mentors.filter(mentor => 
          mentor.is_accepting_mentees && mentor.is_verified
        ));
      } catch (error) {
        console.error('Error loading data:', error);
      }
    };

    loadData();
  }, [user?.id]);

  // Filter to show only user's applications
  const userApplications = applicationsCRUD.data.filter(app => 
    app.mentee.id === user?.id
  );

  // Handle create
  const handleCreate = (mentor?: MentorProfile) => {
    setSelectedMentor(mentor || null);
    setShowCreateForm(true);
  };

  // Handle create submission
  const handleCreateSubmit = async (data: Partial<MentorshipApplication>) => {
    return await applicationsCRUD.createItem(data);
  };

  // Handle edit
  const handleEdit = (application: MentorshipApplication) => {
    setSelectedApplication(application);
    setShowEditForm(true);
  };

  // Handle update
  const handleUpdate = async (data: Partial<MentorshipApplication>) => {
    if (!selectedApplication) return false;
    return await applicationsCRUD.updateItem(selectedApplication.id, data);
  };

  // Handle delete
  const handleDelete = (application: MentorshipApplication) => {
    setSelectedApplication(application);
    setShowDeleteDialog(true);
  };

  // Confirm delete
  const confirmDelete = async () => {
    if (!selectedApplication) return;
    await applicationsCRUD.deleteItem(selectedApplication.id);
  };

  // Table columns configuration
  const columns = [
    {
      key: 'mentor',
      label: t('mentorship.mentor', 'Mentor'),
      render: (application: MentorshipApplication) => (
        <div className="flex items-center gap-3">
          {application.mentor_profile?.user.profile?.profile_image ? (
            <img 
              src={application.mentor_profile.user.profile.profile_image} 
              alt={application.mentor_profile.user.username}
              className="w-10 h-10 rounded-full object-cover"
            />
          ) : (
            <div className="w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center">
              <span className="text-white font-medium">
                {application.mentor_profile?.user.first_name?.[0] || application.mentor_profile?.user.username[0]}
              </span>
            </div>
          )}
          <div>
            <div className="font-medium text-white">
              {application.mentor_profile?.user.first_name} {application.mentor_profile?.user.last_name}
            </div>
            <div className="text-sm text-gray-400">
              {application.mentor_profile?.company} - {application.mentor_profile?.position}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'status',
      label: t('mentorship.status', 'Status'),
      render: (application: MentorshipApplication) => (
        <div className="flex items-center gap-2">
          {application.status === 'approved' ? (
            <CheckCircle size={16} className="text-green-400" />
          ) : application.status === 'rejected' ? (
            <XCircle size={16} className="text-red-400" />
          ) : (
            <Clock size={16} className="text-yellow-400" />
          )}
          <span className={`text-sm ${
            application.status === 'approved' ? 'text-green-400' :
            application.status === 'rejected' ? 'text-red-400' :
            'text-yellow-400'
          }`}>
            {t(`mentorship.status.${application.status}`, application.status)}
          </span>
        </div>
      )
    },
    {
      key: 'experience_level',
      label: t('mentorship.experienceLevel', 'Experience Level'),
      render: (application: MentorshipApplication) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          application.experience_level === 'beginner' ? 'bg-blue-900/50 text-blue-200' :
          application.experience_level === 'intermediate' ? 'bg-yellow-900/50 text-yellow-200' :
          'bg-green-900/50 text-green-200'
        }`}>
          {t(`mentorship.experience.${application.experience_level}`, application.experience_level)}
        </span>
      )
    },
    {
      key: 'preferred_communication',
      label: t('mentorship.communication', 'Communication'),
      render: (application: MentorshipApplication) => (
        <span className="text-gray-300">
          {t(`mentorship.communication.${application.preferred_communication}`, application.preferred_communication)}
        </span>
      )
    },
    {
      key: 'created_at',
      label: t('common.applied', 'Applied'),
      render: (application: MentorshipApplication) => (
        <span className="text-gray-400 text-sm">
          {new Date(application.created_at).toLocaleDateString()}
        </span>
      )
    }
  ];

  // Table actions
  const actions = [
    {
      label: t('common.view', 'View'),
      icon: Eye,
      onClick: (application: MentorshipApplication) => {
        // Navigate to application detail page or show modal
        console.log('View application:', application);
      },
      variant: 'secondary' as const
    },
    {
      label: t('common.edit', 'Edit'),
      icon: Edit,
      onClick: handleEdit,
      variant: 'primary' as const,
      condition: (application: MentorshipApplication) => application.status === 'pending'
    },
    {
      label: t('common.delete', 'Delete'),
      icon: Trash2,
      onClick: handleDelete,
      variant: 'danger' as const,
      condition: (application: MentorshipApplication) => application.status === 'pending'
    }
  ];

  // Stats cards data
  const stats = [
    {
      title: t('mentorship.totalApplications', 'Total Applications'),
      value: userApplications.length,
      icon: MessageSquare,
      color: 'blue'
    },
    {
      title: t('mentorship.approved', 'Approved'),
      value: userApplications.filter(app => app.status === 'approved').length,
      icon: CheckCircle,
      color: 'green'
    },
    {
      title: t('mentorship.pending', 'Pending'),
      value: userApplications.filter(app => app.status === 'pending').length,
      icon: Clock,
      color: 'yellow'
    },
    {
      title: t('mentorship.availableMentors', 'Available Mentors'),
      value: mentorProfiles.length,
      icon: Users,
      color: 'purple'
    }
  ];

  return (
    <AuthenticatedLayout>
      <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="space-y-6">
        {/* Header */}
        <div className={`flex flex-col sm:flex-row sm:items-center sm:justify-between ${isRTL ? 'sm:flex-row-reverse' : ''}`}>
          <div>
            <h1 className="text-2xl font-bold text-white">
              {t('mentorship.myApplications', 'My Mentorship Applications')}
            </h1>
            <p className="text-gray-400 mt-1">
              {t('mentorship.manageApplications', 'Apply for mentorship and track your applications')}
            </p>
          </div>
          <div className={`flex gap-2 mt-4 sm:mt-0 ${isRTL ? 'flex-row-reverse' : ''}`}>
            {mentorProfiles.length > 0 && (
              <select
                onChange={(e) => {
                  const mentorId = parseInt(e.target.value);
                  const mentor = mentorProfiles.find(mentor => mentor.id === mentorId);
                  handleCreate(mentor);
                }}
                className="px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                defaultValue=""
              >
                <option value="" disabled>
                  {t('mentorship.selectMentor', 'Select mentor to apply')}
                </option>
                {mentorProfiles.map(mentor => (
                  <option key={mentor.id} value={mentor.id}>
                    {mentor.user.first_name} {mentor.user.last_name} - {mentor.company}
                  </option>
                ))}
              </select>
            )}
            <button
              onClick={() => handleCreate()}
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center gap-2"
            >
              <Plus size={20} />
              {t('mentorship.applyForMentorship', 'Apply for Mentorship')}
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <div key={index} className="bg-gray-800 rounded-lg p-6">
              <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div>
                  <p className="text-gray-400 text-sm">{stat.title}</p>
                  <p className="text-2xl font-bold text-white mt-1">{stat.value}</p>
                </div>
                <div className={`p-3 rounded-lg ${
                  stat.color === 'blue' ? 'bg-blue-900/50' :
                  stat.color === 'green' ? 'bg-green-900/50' :
                  stat.color === 'yellow' ? 'bg-yellow-900/50' :
                  'bg-purple-900/50'
                }`}>
                  <stat.icon size={24} className={
                    stat.color === 'blue' ? 'text-blue-400' :
                    stat.color === 'green' ? 'text-green-400' :
                    stat.color === 'yellow' ? 'text-yellow-400' :
                    'text-purple-400'
                  } />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Error Display */}
        {applicationsCRUD.error && (
          <div className="bg-red-900/50 border border-red-500 rounded-lg p-4">
            <div className="flex items-center gap-2">
              <AlertCircle size={20} className="text-red-400" />
              <span className="text-red-200">{applicationsCRUD.error}</span>
            </div>
          </div>
        )}

        {/* No Mentors Warning */}
        {mentorProfiles.length === 0 && (
          <div className="bg-yellow-900/50 border border-yellow-500 rounded-lg p-4">
            <div className="flex items-center gap-2">
              <AlertCircle size={20} className="text-yellow-400" />
              <span className="text-yellow-200">
                {t('mentorship.noMentorsAvailable', 'No mentors are currently accepting applications.')}
              </span>
            </div>
          </div>
        )}

        {/* Mentorship Applications Table */}
        <div className="bg-gray-800 rounded-lg">
          <CRUDTable
            data={userApplications}
            columns={columns}
            actions={actions}
            isLoading={applicationsCRUD.isLoading}
            emptyMessage={t('mentorship.noApplications', 'No mentorship applications found. Apply to a mentor to get started!')}
            searchPlaceholder={t('mentorship.searchApplications', 'Search your applications...')}
            title={t('mentorship.applications', 'Applications')}
            createButtonLabel={t('mentorship.applyForMentorship', 'Apply for Mentorship')}
            onCreate={() => handleCreate()}
          />
        </div>
      </div>

      {/* Create Form Modal */}
      {showCreateForm && (
        <MentorshipApplicationForm
          mode="create"
          mentor={selectedMentor || undefined}
          onSubmit={handleCreateSubmit}
          onCancel={() => {
            setShowCreateForm(false);
            setSelectedMentor(null);
          }}
          isSubmitting={applicationsCRUD.isLoading}
        />
      )}

      {/* Edit Form Modal */}
      {showEditForm && selectedApplication && (
        <MentorshipApplicationForm
          mode="edit"
          initialData={selectedApplication}
          onSubmit={handleUpdate}
          onCancel={() => {
            setShowEditForm(false);
            setSelectedApplication(null);
          }}
          isSubmitting={applicationsCRUD.isLoading}
        />
      )}

      {/* Delete Confirmation Dialog */}
      {showDeleteDialog && selectedApplication && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-gray-900 rounded-lg shadow-xl w-full max-w-md">
            <div className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <AlertCircle size={24} className="text-red-400" />
                <h3 className="text-lg font-semibold text-white">
                  {t('common.confirmDelete', 'Confirm Delete')}
                </h3>
              </div>
              <p className="text-gray-300 mb-6">
                {t('mentorship.deleteConfirmation', 'Are you sure you want to delete this mentorship application? This action cannot be undone.')}
              </p>
              <div className={`flex gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <button
                  onClick={() => {
                    setShowDeleteDialog(false);
                    setSelectedApplication(null);
                  }}
                  className="flex-1 px-4 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors"
                  disabled={applicationsCRUD.isLoading}
                >
                  {t('common.cancel', 'Cancel')}
                </button>
                <button
                  onClick={confirmDelete}
                  className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
                  disabled={applicationsCRUD.isLoading}
                >
                  {applicationsCRUD.isLoading ? t('common.deleting', 'Deleting...') : t('common.delete', 'Delete')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
              </div>
        </div>
      </div>
    </AuthenticatedLayout>
  );
};

export default MentorshipApplicationsPage;
