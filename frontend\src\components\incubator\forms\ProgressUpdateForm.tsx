import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';
import { ProgressUpdate, BusinessIdea } from '../../../services/incubatorApi';
import { Loader, Save, X, AlertCircle } from 'lucide-react';

interface ProgressUpdateFormProps {
  initialData?: Partial<ProgressUpdate>;
  businessIdea?: BusinessIdea;
  onSubmit: (data: Partial<ProgressUpdate>) => Promise<boolean>;
  onCancel: () => void;
  isSubmitting?: boolean;
  mode: 'create' | 'edit';
}

interface FormData {
  title: string;
  description: string;
  achievements: string;
  challenges: string;
  next_steps: string;
  business_idea: number;
}

interface FormErrors {
  [key: string]: string;
}

const ProgressUpdateForm: React.FC<ProgressUpdateFormProps> = ({
  initialData,
  businessIdea,
  onSubmit,
  onCancel,
  isSubmitting = false,
  mode
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [formData, setFormData] = useState<FormData>({
    title: initialData?.title || '',
    description: initialData?.description || '',
    achievements: initialData?.achievements || '',
    challenges: initialData?.challenges || '',
    next_steps: initialData?.next_steps || '',
    business_idea: initialData?.business_idea || businessIdea?.id || 0
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  // Validation rules
  const validateField = (name: string, value: string): string => {
    switch (name) {
      case 'title':
        if (!value.trim()) return t('validation.required', 'This field is required');
        if (value.length < 3) return t('validation.minLength', 'Must be at least 3 characters');
        if (value.length > 200) return t('validation.maxLength', 'Must be less than 200 characters');
        break;
      case 'description':
        if (!value.trim()) return t('validation.required', 'This field is required');
        if (value.length < 10) return t('validation.minLength', 'Must be at least 10 characters');
        break;
      case 'achievements':
        if (!value.trim()) return t('validation.required', 'This field is required');
        if (value.length < 10) return t('validation.minLength', 'Must be at least 10 characters');
        break;
      case 'next_steps':
        if (!value.trim()) return t('validation.required', 'This field is required');
        if (value.length < 10) return t('validation.minLength', 'Must be at least 10 characters');
        break;
    }
    return '';
  };

  // Validate all fields
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};
    
    Object.keys(formData).forEach(key => {
      if (key !== 'business_idea' && key !== 'challenges') { // challenges is optional
        const error = validateField(key, formData[key as keyof FormData] as string);
        if (error) {
          newErrors[key] = error;
        }
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // Handle field blur
  const handleBlur = (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    setTouched(prev => ({
      ...prev,
      [name]: true
    }));

    // Validate field on blur
    const error = validateField(name, value);
    setErrors(prev => ({
      ...prev,
      [name]: error
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Mark all fields as touched
    const allTouched = Object.keys(formData).reduce((acc, key) => {
      acc[key] = true;
      return acc;
    }, {} as Record<string, boolean>);
    setTouched(allTouched);

    // Validate form
    if (!validateForm()) {
      return;
    }

    // Submit form
    const success = await onSubmit(formData);
    if (success) {
      // Form will be closed by parent component
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <div className="bg-gray-900 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className={`flex items-center justify-between p-6 border-b border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div>
            <h2 className="text-xl font-semibold text-white">
              {mode === 'create' 
                ? t('incubator.createProgressUpdate', 'Create Progress Update')
                : t('incubator.editProgressUpdate', 'Edit Progress Update')
              }
            </h2>
            {businessIdea && (
              <p className="text-gray-400 text-sm mt-1">
                {t('incubator.forBusinessIdea', 'For')}: {businessIdea.title}
              </p>
            )}
          </div>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-white transition-colors"
            disabled={isSubmitting}
          >
            <X size={24} />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('incubator.updateTitle', 'Update Title')} *
            </label>
            <input
              type="text"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              onBlur={handleBlur}
              className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white ${
                errors.title && touched.title ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder={t('incubator.updateTitlePlaceholder', 'Enter a title for this update')}
              disabled={isSubmitting}
            />
            {errors.title && touched.title && (
              <p className="mt-1 text-sm text-red-400 flex items-center">
                <AlertCircle size={16} className="mr-1" />
                {errors.title}
              </p>
            )}
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('incubator.description', 'Description')} *
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              onBlur={handleBlur}
              rows={3}
              className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white resize-vertical ${
                errors.description && touched.description ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder={t('incubator.descriptionPlaceholder', 'Provide a brief description of this update')}
              disabled={isSubmitting}
            />
            {errors.description && touched.description && (
              <p className="mt-1 text-sm text-red-400 flex items-center">
                <AlertCircle size={16} className="mr-1" />
                {errors.description}
              </p>
            )}
          </div>

          {/* Achievements */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('incubator.achievements', 'Achievements')} *
            </label>
            <textarea
              name="achievements"
              value={formData.achievements}
              onChange={handleInputChange}
              onBlur={handleBlur}
              rows={4}
              className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white resize-vertical ${
                errors.achievements && touched.achievements ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder={t('incubator.achievementsPlaceholder', 'What milestones have you achieved?')}
              disabled={isSubmitting}
            />
            {errors.achievements && touched.achievements && (
              <p className="mt-1 text-sm text-red-400 flex items-center">
                <AlertCircle size={16} className="mr-1" />
                {errors.achievements}
              </p>
            )}
          </div>

          {/* Challenges */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('incubator.challenges', 'Challenges')}
            </label>
            <textarea
              name="challenges"
              value={formData.challenges}
              onChange={handleInputChange}
              onBlur={handleBlur}
              rows={3}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white resize-vertical"
              placeholder={t('incubator.challengesPlaceholder', 'What challenges are you facing? (Optional)')}
              disabled={isSubmitting}
            />
          </div>

          {/* Next Steps */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('incubator.nextSteps', 'Next Steps')} *
            </label>
            <textarea
              name="next_steps"
              value={formData.next_steps}
              onChange={handleInputChange}
              onBlur={handleBlur}
              rows={3}
              className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white resize-vertical ${
                errors.next_steps && touched.next_steps ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder={t('incubator.nextStepsPlaceholder', 'What are your next steps?')}
              disabled={isSubmitting}
            />
            {errors.next_steps && touched.next_steps && (
              <p className="mt-1 text-sm text-red-400 flex items-center">
                <AlertCircle size={16} className="mr-1" />
                {errors.next_steps}
              </p>
            )}
          </div>

          {/* Form Actions */}
          <div className={`flex gap-4 pt-4 border-t border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <button
              type="button"
              onClick={onCancel}
              className="px-6 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors"
              disabled={isSubmitting}
            >
              {t('common.cancel', 'Cancel')}
            </button>
            <button
              type="submit"
              className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader size={16} className="animate-spin" />
                  {t('common.saving', 'Saving...')}
                </>
              ) : (
                <>
                  <Save size={16} />
                  {mode === 'create' 
                    ? t('common.create', 'Create')
                    : t('common.update', 'Update')
                  }
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ProgressUpdateForm;
