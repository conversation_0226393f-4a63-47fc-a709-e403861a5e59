import React, { useState, useEffect } from 'react';
import { X, Plus, Tag as TagIcon } from 'lucide-react';
import { api } from '../../services/api';
import { useLanguage } from '../../hooks/useLanguage';
import { useTranslation } from 'react-i18next';
export interface Tag {
  id: number;
  name: string;
  slug: string;
  description?: string;
}

interface TagSelectorProps {
  selectedTags: Tag[];
  onChange: (tags: Tag[]) => void;
  className?: string;
}

const TagSelector: React.FC<TagSelectorProps> = ({ selectedTags,
  onChange,
  className = ''
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [availableTags, setAvailableTags] = useState<Tag[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // Fetch available tags
  useEffect(() => {
    const fetchTags = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await fetch(`${api.API_URL}/tags/`);
        if (!response.ok) {
          throw new Error(t("common.failed.to.fetch", "Failed to fetch tags"));
        }
        const data = await response.json();

        // Handle paginated response
        if (data && typeof data === 'object' && 'results' in data && Array.isArray(data.results)) {
          setAvailableTags(data.results);
        } else if (Array.isArray(data)) {
          setAvailableTags(data);
        } else {
          console.error("Unexpected tags data format:", data);
          setAvailableTags([]);
        }
      } catch (err) {
        setError('Error loading tags');
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTags();
  }, []);

  // Filter tags based on search term
  const filteredTags = availableTags.filter(tag =>
    tag.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
    !selectedTags.some(selectedTag => selectedTag.id === tag.id)
  );

  // Add a tag to selected tags
  const addTag = (tag: Tag) => {
    onChange([...selectedTags, tag]);
    setSearchTerm('');
    setIsDropdownOpen(false);
  };

  // Remove a tag from selected tags
  const removeTag = (tagId: number) => {
    onChange(selectedTags.filter(tag => tag.id !== tagId));
  };

  return (
    <div className={`tag-selector ${className}`}>
      <div className={`flex flex-wrap gap-2 mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
        {selectedTags.map(tag => (
          <div
            key={tag.id}
            className={`flex items-center gap-1 bg-indigo-900/50 text-white px-2 py-1 rounded-full text-sm ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <TagIcon size={14} />
            <span>{tag.name}</span>
            <button
              type="button"
              onClick={() => removeTag(tag.id)}
              className="text-gray-300 hover:text-white"
            >
              <X size={14} />
            </button>
          </div>
        ))}
      </div>

      <div className="relative">
        <div className={`flex items-center border border-indigo-800 rounded-lg bg-indigo-950/50 overflow-hidden ${isRTL ? "flex-row-reverse" : ""}`}>
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onFocus={() => setIsDropdownOpen(true)}
            placeholder={t("common.search.tags", "Search tags...")}
            className={`flex-grow px-3 py-2 bg-transparent text-white focus:outline-none ${isRTL ? "flex-row-reverse" : ""}`}
          />
          <button
            type="button"
            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
            className="px-3 py-2 text-gray-300 hover:text-white"
          >
            <Plus size={18} />
          </button>
        </div>

        {isDropdownOpen && (
          <div className="absolute z-10 mt-1 w-full bg-indigo-900 border border-indigo-700 rounded-lg shadow-lg max-h-60 overflow-y-auto">
            {isLoading ? (
              <div className="p-3 text-center text-gray-300">{t("common.loading.tags", "Loading tags...")}</div>
            ) : error ? (
              <div className="p-3 text-center text-red-400">{error}</div>
            ) : filteredTags.length === 0 ? (
              <div className="p-3 text-center text-gray-300">{t("common.no.matching.tags", "No matching tags found")}</div>
            ) : (
              filteredTags.map(tag => (
                <div
                  key={tag.id}
                  onClick={() => addTag(tag)}
                  className={`p-2 hover:bg-indigo-800 cursor-pointer flex items-center gap-2 ${isRTL ? "flex-row-reverse" : ""}`}
                >
                  <TagIcon size={14} className="text-purple-400" />
                  <span className="text-white">{tag.name}</span>
                  {tag.description && (
                    <span className="text-gray-300 text-xs truncate">{tag.description}</span>
                  )}
                </div>
              ))
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default TagSelector;
