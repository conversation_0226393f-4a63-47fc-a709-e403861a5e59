"""
Collaborative Template Models
Enable multi-user collaboration on business plan templates
"""

from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from .models_business_plan import BusinessPlanTemplate, BusinessPlan
import json


class CollaborativeTemplate(models.Model):
    """Extended template model for collaboration features"""

    base_template = models.OneToOneField(
        BusinessPlanTemplate,
        on_delete=models.CASCADE,
        related_name='collaborative_features'
    )

    # Collaboration settings
    is_collaborative = models.BooleanField(default=False)
    max_collaborators = models.IntegerField(default=5)
    collaboration_mode = models.CharField(max_length=20, choices=[
        ('open', 'Open Collaboration'),
        ('invite_only', 'Invite Only'),
        ('review_only', 'Review Only'),
        ('sequential', 'Sequential Editing'),
    ], default='invite_only')

    # Permissions
    allow_section_assignment = models.BooleanField(default=True)
    allow_real_time_editing = models.BooleanField(default=True)
    allow_comments = models.BooleanField(default=True)
    allow_version_control = models.<PERSON>oleanField(default=True)
    require_approval = models.BooleanField(default=False)

    # Workflow settings
    workflow_enabled = models.BooleanField(default=False)
    workflow_config = models.JSONField(default=list, blank=True)
    current_stage = models.CharField(max_length=50, blank=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'collaborative_template'

    def __str__(self):
        return f"Collaborative: {self.base_template.name}"


class TemplateCollaborator(models.Model):
    """Users collaborating on a template"""

    template = models.ForeignKey(
        CollaborativeTemplate,
        on_delete=models.CASCADE,
        related_name='collaborators'
    )
    user = models.ForeignKey(User, on_delete=models.CASCADE)

    # Role and permissions
    role = models.CharField(max_length=20, choices=[
        ('owner', 'Owner'),
        ('editor', 'Editor'),
        ('reviewer', 'Reviewer'),
        ('viewer', 'Viewer'),
        ('contributor', 'Contributor'),
    ])

    # Specific permissions
    can_edit_sections = models.BooleanField(default=True)
    can_add_sections = models.BooleanField(default=False)
    can_delete_sections = models.BooleanField(default=False)
    can_invite_others = models.BooleanField(default=False)
    can_approve_changes = models.BooleanField(default=False)
    can_export = models.BooleanField(default=True)

    # Assignment
    assigned_sections = models.JSONField(default=list, blank=True)
    deadline = models.DateTimeField(null=True, blank=True)

    # Status
    invitation_sent_at = models.DateTimeField(null=True, blank=True)
    invitation_accepted_at = models.DateTimeField(null=True, blank=True)
    last_active_at = models.DateTimeField(null=True, blank=True)
    is_active = models.BooleanField(default=True)

    # Metadata
    invited_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='template_invitations_sent'
    )
    joined_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'template_collaborator'
        unique_together = ['template', 'user']
        indexes = [
            models.Index(fields=['template', 'role']),
            models.Index(fields=['user', 'is_active']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.role} on {self.template.base_template.name}"

    @property
    def has_accepted_invitation(self):
        return self.invitation_accepted_at is not None


class TemplateVersion(models.Model):
    """Version control for collaborative templates"""

    template = models.ForeignKey(
        CollaborativeTemplate,
        on_delete=models.CASCADE,
        related_name='versions'
    )

    # Version info
    version_number = models.CharField(max_length=20)
    version_name = models.CharField(max_length=100, blank=True)
    description = models.TextField(blank=True)

    # Content
    template_data = models.JSONField()
    sections_data = models.JSONField()

    # Change tracking
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    changes_summary = models.JSONField(default=dict, blank=True)

    # Status
    is_published = models.BooleanField(default=False)
    is_current = models.BooleanField(default=False)

    # Approval workflow
    requires_approval = models.BooleanField(default=False)
    approved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_template_versions'
    )
    approved_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'template_version'
        unique_together = ['template', 'version_number']
        indexes = [
            models.Index(fields=['template', 'created_at']),
            models.Index(fields=['is_current']),
        ]

    def __str__(self):
        return f"v{self.version_number} - {self.template.base_template.name}"


class TemplateComment(models.Model):
    """Comments and discussions on template sections"""

    template = models.ForeignKey(
        CollaborativeTemplate,
        on_delete=models.CASCADE,
        related_name='comments'
    )
    section_key = models.CharField(max_length=100, blank=True)

    # Comment details
    author = models.ForeignKey(User, on_delete=models.CASCADE)
    content = models.TextField()
    comment_type = models.CharField(max_length=20, choices=[
        ('general', 'General Comment'),
        ('suggestion', 'Suggestion'),
        ('question', 'Question'),
        ('issue', 'Issue'),
        ('approval', 'Approval Request'),
    ], default='general')

    # Threading
    parent_comment = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='replies'
    )

    # Status
    is_resolved = models.BooleanField(default=False)
    resolved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='resolved_comments'
    )
    resolved_at = models.DateTimeField(null=True, blank=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Attachments
    attachments = models.JSONField(default=list, blank=True)

    class Meta:
        db_table = 'template_comment'
        indexes = [
            models.Index(fields=['template', 'section_key']),
            models.Index(fields=['author', 'created_at']),
            models.Index(fields=['is_resolved']),
        ]

    def __str__(self):
        return f"Comment by {self.author.username} on {self.template.base_template.name}"


class TemplateChangeLog(models.Model):
    """Track all changes made to collaborative templates"""

    template = models.ForeignKey(
        CollaborativeTemplate,
        on_delete=models.CASCADE,
        related_name='change_logs'
    )

    # Change details
    changed_by = models.ForeignKey(User, on_delete=models.CASCADE)
    change_type = models.CharField(max_length=30, choices=[
        ('section_added', 'Section Added'),
        ('section_edited', 'Section Edited'),
        ('section_deleted', 'Section Deleted'),
        ('section_moved', 'Section Moved'),
        ('collaborator_added', 'Collaborator Added'),
        ('collaborator_removed', 'Collaborator Removed'),
        ('permissions_changed', 'Permissions Changed'),
        ('template_published', 'Template Published'),
        ('comment_added', 'Comment Added'),
        ('version_created', 'Version Created'),
    ])

    section_key = models.CharField(max_length=100, blank=True)
    old_value = models.JSONField(null=True, blank=True)
    new_value = models.JSONField(null=True, blank=True)
    change_description = models.TextField(blank=True)

    # Metadata
    timestamp = models.DateTimeField(auto_now_add=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)

    class Meta:
        db_table = 'template_change_log'
        indexes = [
            models.Index(fields=['template', 'timestamp']),
            models.Index(fields=['changed_by', 'timestamp']),
            models.Index(fields=['change_type']),
        ]

    def __str__(self):
        return f"{self.change_type} by {self.changed_by.username} at {self.timestamp}"


class TemplateWorkflowStage(models.Model):
    """Workflow stages for collaborative template development"""

    template = models.ForeignKey(
        CollaborativeTemplate,
        on_delete=models.CASCADE,
        related_name='workflow_stages'
    )

    # Stage details
    stage_name = models.CharField(max_length=100)
    stage_order = models.IntegerField()
    description = models.TextField(blank=True)

    # Requirements
    required_sections = models.JSONField(default=list, blank=True)
    required_approvals = models.IntegerField(default=0)
    assigned_users = models.ManyToManyField(User, blank=True)

    # Status
    is_current = models.BooleanField(default=False)
    is_completed = models.BooleanField(default=False)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    # Deadlines
    deadline = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'template_workflow_stage'
        unique_together = ['template', 'stage_order']
        indexes = [
            models.Index(fields=['template', 'stage_order']),
            models.Index(fields=['is_current']),
        ]

    def __str__(self):
        return f"Stage {self.stage_order}: {self.stage_name}"


class TemplateNotification(models.Model):
    """Notifications for collaborative template activities"""

    recipient = models.ForeignKey(User, on_delete=models.CASCADE)
    template = models.ForeignKey(
        CollaborativeTemplate,
        on_delete=models.CASCADE,
        related_name='notifications'
    )

    # Notification details
    notification_type = models.CharField(max_length=30, choices=[
        ('invitation', 'Collaboration Invitation'),
        ('assignment', 'Section Assignment'),
        ('comment', 'New Comment'),
        ('mention', 'Mentioned in Comment'),
        ('approval_request', 'Approval Request'),
        ('deadline_reminder', 'Deadline Reminder'),
        ('stage_completed', 'Stage Completed'),
        ('template_published', 'Template Published'),
        ('change_made', 'Change Made'),
    ])

    title = models.CharField(max_length=200)
    message = models.TextField()
    action_url = models.URLField(blank=True)

    # Related objects
    triggered_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='triggered_notifications'
    )
    related_comment = models.ForeignKey(
        TemplateComment,
        on_delete=models.CASCADE,
        null=True,
        blank=True
    )

    # Status
    is_read = models.BooleanField(default=False)
    read_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    # Delivery
    email_sent = models.BooleanField(default=False)
    push_sent = models.BooleanField(default=False)

    class Meta:
        db_table = 'template_notification'
        indexes = [
            models.Index(fields=['recipient', 'is_read']),
            models.Index(fields=['template', 'created_at']),
            models.Index(fields=['notification_type']),
        ]

    def __str__(self):
        return f"Notification: {self.title} for {self.recipient.username}"

    def mark_as_read(self):
        """Mark notification as read"""
        if not self.is_read:
            self.is_read = True
            self.read_at = timezone.now()
            self.save()


class TemplateCollaborationSession(models.Model):
    """Track real-time collaboration sessions"""

    template = models.ForeignKey(
        CollaborativeTemplate,
        on_delete=models.CASCADE,
        related_name='collaboration_sessions'
    )
    user = models.ForeignKey(User, on_delete=models.CASCADE)

    # Session details
    session_id = models.CharField(max_length=100, unique=True)
    section_key = models.CharField(max_length=100, blank=True)

    # Status
    is_active = models.BooleanField(default=True)
    started_at = models.DateTimeField(auto_now_add=True)
    last_activity_at = models.DateTimeField(auto_now=True)
    ended_at = models.DateTimeField(null=True, blank=True)

    # Real-time data
    cursor_position = models.JSONField(default=dict, blank=True)
    current_selection = models.JSONField(default=dict, blank=True)

    class Meta:
        db_table = 'template_collaboration_session'
        indexes = [
            models.Index(fields=['template', 'is_active']),
            models.Index(fields=['session_id']),
        ]

    def __str__(self):
        return f"Session: {self.user.username} on {self.template.base_template.name}"
