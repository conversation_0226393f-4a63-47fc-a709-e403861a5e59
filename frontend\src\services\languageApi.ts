import { apiRequest } from './api';

// Types
export interface LanguageResponse {
  language: string;
  available_languages: Record<string, string>;
}

export interface SetLanguageResponse {
  message: string;
  language: string;
}

// Language API
export const languageAPI = {
  /**
   * Get the current language and available languages
   * @returns The current language and available languages
   */
  getLanguage: async (): Promise<LanguageResponse> => {
    try {
      return await apiRequest<LanguageResponse>('/users/language/');
    } catch (error) {
      console.error('Error fetching language:', error);
      // Return default values on error
      return {
        language: 'en',
        available_languages: {
          en: 'English',
          ar: 'Arabic'
        }
      };
    }
  },

  /**
   * Set the language
   * @param language The language code to set
   * @returns The response from the server
   */
  setLanguage: async (language: string): Promise<SetLanguageResponse> => {
    try {
      return await apiRequest<SetLanguageResponse>('/users/language/', 'POST', { language });
    } catch (error) {
      console.error('Error setting language:', error);
      throw error;
    }
  }
};
