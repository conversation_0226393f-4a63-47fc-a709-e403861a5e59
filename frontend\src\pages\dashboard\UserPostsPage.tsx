import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Plus, Search, Edit, Trash2, MessageSquare, Calendar, Loader2 } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import AuthenticatedLayout from '../../components/layout/AuthenticatedLayout';
import { Post, postsAPI } from '../../services/api';
import { useAppSelector } from '../../store/hooks';
import { RTLText, RTLFlex, RTLIcon } from '../../components/rtl';

const UserPostsPage: React.FC = () => {
  const { t } = useTranslation();
  const { language, isRTL } = useLanguage();
  const { user } = useAppSelector(state => state.auth);

  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPost, setSelectedPost] = useState<Post | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    fetchUserPosts();
  }, [user]);

  const fetchUserPosts = async () => {
    setLoading(true);
    try {
      const allPosts = await postsAPI.getPosts();
      // Filter posts to only show the current user's posts
      const userPosts = allPosts.filter(post => post.author.id === user?.id);
      setPosts(userPosts);
    } catch (error) {
      console.error('Error fetching user posts:', error);
      setError(t('posts.errors.failedToLoad', 'Failed to load posts'));
    } finally {
      setLoading(false);
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const openDeleteModal = (post: Post) => {
    setSelectedPost(post);
    setIsDeleteModalOpen(true);
  };

  const handleDeletePost = async () => {
    if (!selectedPost) return;

    setDeleting(true);
    try {
      await postsAPI.deletePost(selectedPost.id);
      setSuccess(t('posts.success.deleted', 'Post deleted successfully'));
      fetchUserPosts();
      setIsDeleteModalOpen(false);
      setSelectedPost(null);
      
      // Clear success message after delay
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error('Error deleting post:', error);
      setError(t('posts.errors.failedToDelete', 'Failed to delete post'));
    } finally {
      setDeleting(false);
    }
  };

  // Filter posts based on search term
  const filteredPosts = posts.filter(post =>
    post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    post.content.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const stripHtml = (html: string) => {
    const tmp = document.createElement('div');
    tmp.innerHTML = html;
    return tmp.textContent || tmp.innerText || '';
  };

  return (
    <div className="p-6">
        {/* Header */}
        <div className={`mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
          <div>
            <RTLText as="h1" className="text-2xl font-bold text-white">{t('posts.myPosts', 'My Posts')}</RTLText>
            <RTLText as="div" className="text-gray-300 mt-1">{t('posts.manageYourPosts', 'Manage your forum posts')}</RTLText>
          </div>
          <Link
            to="/forum"
            className="mt-4 sm:mt-0 px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300 flex items-center justify-center"
          >
            <Plus className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
            {t('posts.createNewPost', 'Create New Post')}
          </Link>
        </div>

        {/* Error/Success Messages */}
        {error && (
          <div className="mb-4 p-3 bg-red-900/50 border border-red-500 rounded-md text-red-200">
            {error}
          </div>
        )}

        {success && (
          <div className="mb-4 p-3 bg-green-900/50 border border-green-500 rounded-md text-green-200">
            {success}
          </div>
        )}

        {/* Search */}
        <div className={`mb-6 relative ${isRTL ? "flex-row-reverse" : ""}`}>
          <input
            type="text"
            placeholder={t('posts.searchPosts', 'Search your posts...')}
            value={searchTerm}
            onChange={handleSearchChange}
            className={`w-full px-4 py-2 ${language === 'ar' ? 'pr-10' : 'pl-10'} bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400`}
            dir={language === 'ar' ? 'rtl' : 'ltr'}
          />
          <Search size={18} className={`absolute ${language === 'ar' ? 'right-3' : 'left-3'} top-2.5 text-gray-400`} />
        </div>

        {/* Posts List */}
        {loading ? (
          <div className="flex justify-center py-12">
            <div className="flex items-center space-x-3">
              <Loader2 className="w-6 h-6 animate-spin" />
              <span>{t('common.loading', 'Loading...')}</span>
            </div>
          </div>
        ) : filteredPosts.length > 0 ? (
          <div className="space-y-4">
            {filteredPosts.map(post => (
              <div
                key={post.id}
                className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:border-purple-500/50 transition-colors"
              >
                <div className={`flex justify-between items-start mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className="flex-1">
                    <RTLText as="h3" className="text-xl font-bold mb-2">{post.title}</RTLText>
                    <RTLText as="div" className="text-gray-300 mb-3 line-clamp-3">
                      {stripHtml(post.content)}
                    </RTLText>
                    <div className={`flex items-center text-sm text-gray-400 space-x-4 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                      <RTLFlex align="center">
                        <Calendar className={`w-4 h-4 ${language === 'ar' ? 'ml-1' : 'mr-1'}`} />
                        {new Date(post.created_at).toLocaleDateString(language === 'ar' ? 'ar-SA' : undefined)}
                      </RTLFlex>
                      <RTLFlex align="center">
                        <MessageSquare className={`w-4 h-4 ${language === 'ar' ? 'ml-1' : 'mr-1'}`} />
                        {post.comments?.length || 0} {t('posts.comments', 'comments')}
                      </RTLFlex>
                    </div>
                  </div>
                  <div className={`flex space-x-2 ml-4 ${isRTL ? "flex-row-reverse space-x-reverse mr-4 ml-0" : ""}`}>
                    <Link
                      to={`/posts/edit/${post.id}`}
                      className="p-2 bg-blue-600 hover:bg-blue-700 rounded-md transition-colors"
                      title={t('common.edit', 'Edit')}
                    >
                      <Edit size={16} />
                    </Link>
                    <button
                      onClick={() => openDeleteModal(post)}
                      className="p-2 bg-red-600 hover:bg-red-700 rounded-md transition-colors"
                      title={t('common.delete', 'Delete')}
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20">
            <MessageSquare size={48} className="mx-auto text-gray-500 mb-4" />
            <RTLText as="h3" className="text-xl font-semibold mb-2">
              {searchTerm ? t('posts.noPostsFound', 'No posts found') : t('posts.noPostsYet', 'No posts yet')}
            </RTLText>
            <RTLText as="div" className="text-gray-400 mb-4">
              {searchTerm 
                ? t('posts.tryDifferentSearch', 'Try a different search term')
                : t('posts.createFirstPost', 'Create your first post to get started')
              }
            </RTLText>
            <Link
              to="/forum"
              className="px-4 py-2 bg-purple-600 rounded-lg hover:bg-purple-700 transition-colors inline-flex items-center"
            >
              <Plus className={`w-4 h-4 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
              {t('posts.createPost', 'Create Post')}
            </Link>
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {isDeleteModalOpen && selectedPost && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
          <div className="bg-white/10 backdrop-blur-sm rounded-lg w-full max-w-md p-6 border border-white/20">
            <RTLText as="h3" className="text-xl font-bold mb-4">{t('posts.confirmDelete', 'Confirm Delete')}</RTLText>
            <RTLText as="div" className="text-gray-300 mb-6">
              {t('posts.deleteWarning', 'Are you sure you want to delete this post? This action cannot be undone.')}
            </RTLText>
            <RTLText as="div" className="text-sm text-gray-400 mb-6 p-3 bg-white/10 rounded border-l-4 border-purple-500">
              <strong>{selectedPost.title}</strong>
            </RTLText>
            <div className={`flex justify-end space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
              <button
                onClick={() => setIsDeleteModalOpen(false)}
                className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
                disabled={deleting}
              >
                {t('common.cancel', 'Cancel')}
              </button>
              <button
                onClick={handleDeletePost}
                className="px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors flex items-center disabled:opacity-50"
                disabled={deleting}
              >
                {deleting ? (
                  <>
                    <Loader2 className={`w-4 h-4 animate-spin ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                    {t('common.deleting', 'Deleting...')}
                  </>
                ) : (
                  <>
                    <Trash2 className={`w-4 h-4 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                    {t('common.delete', 'Delete')}
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserPostsPage;
