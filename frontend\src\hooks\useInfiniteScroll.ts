import { useState, useEffect, useCallback, useRef } from 'react';

interface UseInfiniteScrollOptions<T> {
  /**
   * Initial data array
   */
  initialData?: T[];
  
  /**
   * Function to fetch more data
   * @param page Page number to fetch
   * @param limit Number of items per page
   * @returns Promise with array of new items
   */
  fetchData: (page: number, limit: number) => Promise<T[]>;
  
  /**
   * Number of items to fetch per page
   */
  limit?: number;
  
  /**
   * Distance from bottom of page to trigger next fetch (in pixels)
   */
  threshold?: number;
  
  /**
   * Whether to enable the infinite scroll
   */
  enabled?: boolean;
}

/**
 * Custom hook for implementing infinite scrolling
 */
export function useInfiniteScroll<T>({
  initialData = [],
  fetchData,
  limit = 10,
  threshold = 200,
  enabled = true,
}: UseInfiniteScrollOptions<T>) {
  const [data, setData] = useState<T[]>(initialData);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const observer = useRef<IntersectionObserver | null>(null);
  
  // Reference to the last element in the list
  const lastElementRef = useCallback((node: HTMLElement | null) => {
    if (loading || !enabled) return;
    
    // Disconnect previous observer
    if (observer.current) observer.current.disconnect();
    
    // Create new observer
    observer.current = new IntersectionObserver(entries => {
      if (entries[0].isIntersecting && hasMore) {
        setPage(prevPage => prevPage + 1);
      }
    }, {
      rootMargin: `0px 0px ${threshold}px 0px`,
    });
    
    // Observe new node
    if (node) observer.current.observe(node);
  }, [loading, hasMore, threshold, enabled]);
  
  // Load more data when page changes
  useEffect(() => {
    if (!enabled) return;
    
    const loadMoreData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const newData = await fetchData(page, limit);
        
        if (newData.length === 0 || newData.length < limit) {
          setHasMore(false);
        }
        
        setData(prevData => (page === 1 ? newData : [...prevData, ...newData]));
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Error loading data');
        console.error('Error in infinite scroll:', err);
      } finally {
        setLoading(false);
      }
    };
    
    loadMoreData();
  }, [page, limit, fetchData, enabled]);
  
  // Function to manually refresh data
  const refresh = useCallback(() => {
    setPage(1);
    setHasMore(true);
  }, []);
  
  // Function to manually load more data
  const loadMore = useCallback(() => {
    if (!loading && hasMore) {
      setPage(prevPage => prevPage + 1);
    }
  }, [loading, hasMore]);
  
  return {
    data,
    loading,
    error,
    hasMore,
    lastElementRef,
    refresh,
    loadMore,
  };
}

export default useInfiniteScroll;
