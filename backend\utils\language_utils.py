"""
Language utilities for handling multilingual content.
"""

from django.utils.translation import get_language
from django.conf import settings


def get_language_from_request(request):
    """
    Get the language from the request.
    
    Args:
        request: HTTP request object
        
    Returns:
        str: Language code (e.g., 'en', 'ar')
    """
    # Check if language is set in the request
    if hasattr(request, 'LANGUAGE_CODE'):
        return request.LANGUAGE_CODE
    
    # Check if language is set in the session
    if hasattr(request, 'session') and 'django_language' in request.session:
        return request.session['django_language']
    
    # Check if language is set in the cookies
    if hasattr(request, 'COOKIES') and 'django_language' in request.COOKIES:
        return request.COOKIES['django_language']
    
    # Check if language is set in the Accept-Language header
    if hasattr(request, 'META') and 'HTTP_ACCEPT_LANGUAGE' in request.META:
        accept_language = request.META['HTTP_ACCEPT_LANGUAGE']
        for language in accept_language.split(','):
            lang_code = language.split(';')[0].strip()
            if lang_code in settings.LANGUAGES:
                return lang_code
    
    # Default to the current language
    return get_language() or 'en'


def get_language_from_user(user):
    """
    Get the language from the user.

    Args:
        user: User instance

    Returns:
        str: Language code (e.g., 'en', 'ar')
    """
    # Check if user has a language preference in their profile
    try:
        if hasattr(user, 'profile') and user.profile and hasattr(user.profile, 'language'):
            return user.profile.language
    except Exception:
        # Profile doesn't exist or other error, continue to fallback
        pass

    # Default to the current language
    return get_language() or 'en'


def get_language_direction(language):
    """
    Get the text direction for a language.
    
    Args:
        language: Language code (e.g., 'en', 'ar')
        
    Returns:
        str: 'rtl' for right-to-left languages, 'ltr' otherwise
    """
    rtl_languages = getattr(settings, 'RTL_LANGUAGES', ['ar', 'he', 'fa', 'ur'])
    return 'rtl' if language in rtl_languages else 'ltr'


def get_language_name(language):
    """
    Get the name of a language.
    
    Args:
        language: Language code (e.g., 'en', 'ar')
        
    Returns:
        tuple: (English name, Native name)
    """
    language_names = {
        'en': ('English', 'English'),
        'ar': ('Arabic', 'العربية'),
        'fr': ('French', 'Français'),
        'es': ('Spanish', 'Español'),
        'de': ('German', 'Deutsch'),
        'zh': ('Chinese', '中文'),
        'ja': ('Japanese', '日本語'),
        'ko': ('Korean', '한국어'),
        'ru': ('Russian', 'Русский'),
        'pt': ('Portuguese', 'Português'),
        'it': ('Italian', 'Italiano'),
        'nl': ('Dutch', 'Nederlands'),
        'tr': ('Turkish', 'Türkçe'),
        'pl': ('Polish', 'Polski'),
        'uk': ('Ukrainian', 'Українська'),
        'vi': ('Vietnamese', 'Tiếng Việt'),
        'th': ('Thai', 'ไทย'),
        'id': ('Indonesian', 'Bahasa Indonesia'),
        'hi': ('Hindi', 'हिन्दी'),
        'bn': ('Bengali', 'বাংলা'),
        'ur': ('Urdu', 'اردو'),
        'fa': ('Persian', 'فارسی'),
        'he': ('Hebrew', 'עברית'),
    }
    
    return language_names.get(language, ('Unknown', 'Unknown'))
