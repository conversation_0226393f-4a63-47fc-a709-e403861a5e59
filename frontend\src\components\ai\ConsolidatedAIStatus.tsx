/**
 * Consolidated AI Status Dashboard
 * Monitor the health and capabilities of the consolidated AI service
 */

import React, { useState, useEffect } from 'react';
import {
  Activity,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Zap,
  Brain,
  Globe,
  Bot,
  RefreshCw,
  Clock,
  BarChart3,
  <PERSON>rk<PERSON>,
} from 'lucide-react';
import { useCentralizedAI } from '../../hooks/useCentralizedAI';

interface ConsolidatedAIStatusProps {
  className?: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

interface ServiceComponent {
  name: string;
  key: string;
  icon: React.ReactNode;
  description: string;
  status: boolean;
  critical: boolean;
}

export const ConsolidatedAIStatus: React.FC<ConsolidatedAIStatusProps> = ({
  className = '',
  autoRefresh = true,
  refreshInterval = 30000, // 30 seconds
}) => {
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const [refreshing, setRefreshing] = useState(false);

  const {
    isAvailable,
    status,
    chatError,
  } = useCentralizedAI();

  // Mock functions for compatibility
  const availability = {
    consolidatedAI: isAvailable,
    gemini: isAvailable,
    langchain: true,
    workflows: true,
    mlService: true,
    arabicProcessing: true,
  };

  const error = chatError;

  const refreshStatus = async () => {
    // Mock refresh - in real implementation would refresh the status
    setLastRefresh(new Date());
  };

  const checkHealth = async () => {
    // Mock health check
    return isAvailable;
  };

  // Auto-refresh
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(async () => {
      await refreshStatus();
      setLastRefresh(new Date());
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, refreshStatus]);

  const handleManualRefresh = async () => {
    setRefreshing(true);
    try {
      await refreshStatus();
      setLastRefresh(new Date());
    } finally {
      setRefreshing(false);
    }
  };

  const serviceComponents: ServiceComponent[] = [
    {
      name: 'Consolidated AI Service',
      key: 'consolidatedAI',
      icon: <Sparkles className="w-5 h-5" />,
      description: 'Main unified AI service',
      status: availability.consolidatedAI,
      critical: true,
    },
    {
      name: 'Gemini AI',
      key: 'gemini',
      icon: <Bot className="w-5 h-5" />,
      description: 'Google Gemini AI integration',
      status: availability.gemini,
      critical: true,
    },
    {
      name: 'LangChain/LangGraph',
      key: 'langchain',
      icon: <Zap className="w-5 h-5" />,
      description: 'Advanced workflow orchestration',
      status: availability.langchain,
      critical: false,
    },
    {
      name: 'Workflows',
      key: 'workflows',
      icon: <Activity className="w-5 h-5" />,
      description: 'Business analysis workflows',
      status: availability.workflows,
      critical: false,
    },
    {
      name: 'ML Service',
      key: 'mlService',
      icon: <Brain className="w-5 h-5" />,
      description: 'Machine learning insights',
      status: availability.mlService,
      critical: false,
    },
    {
      name: 'Arabic Processing',
      key: 'arabicProcessing',
      icon: <Globe className="w-5 h-5" />,
      description: 'Enhanced Arabic language support',
      status: availability.arabicProcessing,
      critical: false,
    },
  ];

  const getOverallStatus = () => {
    const criticalServices = serviceComponents.filter(s => s.critical);
    const criticalOnline = criticalServices.filter(s => s.status);

    if (criticalOnline.length === criticalServices.length) {
      return 'healthy';
    } else if (criticalOnline.length > 0) {
      return 'degraded';
    } else {
      return 'down';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'text-green-600 bg-green-100';
      case 'degraded':
        return 'text-yellow-600 bg-yellow-100';
      case 'down':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'degraded':
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      case 'down':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <AlertTriangle className="w-5 h-5 text-gray-500" />;
    }
  };

  const overallStatus = getOverallStatus();
  const onlineServices = serviceComponents.filter(s => s.status).length;
  const totalServices = serviceComponents.length;

  return (
    <div className={`bg-white rounded-lg shadow-lg ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={`flex items-center justify-center w-12 h-12 rounded-lg ${getStatusColor(overallStatus)}`}>
              {getStatusIcon(overallStatus)}
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Consolidated AI Service Status
              </h2>
              <p className="text-sm text-gray-600">
                {onlineServices}/{totalServices} services online
              </p>
            </div>
          </div>

          <button
            onClick={handleManualRefresh}
            disabled={refreshing}
            className="flex items-center px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>

        {/* Overall status */}
        <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Overall Status</p>
                <p className={`text-lg font-semibold capitalize ${
                  overallStatus === 'healthy' ? 'text-green-600' :
                  overallStatus === 'degraded' ? 'text-yellow-600' :
                  'text-red-600'
                }`}>
                  {overallStatus}
                </p>
              </div>
              {getStatusIcon(overallStatus)}
            </div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Request Count</p>
                <p className="text-lg font-semibold text-gray-900">
                  {status?.request_count || 0}
                </p>
              </div>
              <BarChart3 className="w-5 h-5 text-blue-500" />
            </div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Uptime</p>
                <p className="text-lg font-semibold text-gray-900">
                  {status?.uptime ? `${Math.floor(status.uptime / 3600)}h` : 'N/A'}
                </p>
              </div>
              <Clock className="w-5 h-5 text-purple-500" />
            </div>
          </div>
        </div>
      </div>

      {/* Service Components */}
      <div className="p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Service Components</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {serviceComponents.map((service) => (
            <div
              key={service.key}
              className={`border rounded-lg p-4 ${
                service.status ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                    service.status ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'
                  }`}>
                    {service.icon}
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 flex items-center">
                      {service.name}
                      {service.critical && (
                        <span className="ml-2 px-2 py-1 text-xs bg-orange-100 text-orange-800 rounded-full">
                          Critical
                        </span>
                      )}
                    </h4>
                    <p className="text-sm text-gray-600">{service.description}</p>
                  </div>
                </div>

                <div className={`w-3 h-3 rounded-full ${
                  service.status ? 'bg-green-500' : 'bg-red-500'
                }`} />
              </div>
            </div>
          ))}
        </div>

        {/* Workflows */}
        {status?.workflows && status.workflows.length > 0 && (
          <div className="mt-6">
            <h4 className="font-medium text-gray-900 mb-3">Available Workflows</h4>
            <div className="flex flex-wrap gap-2">
              {status.workflows.map((workflow: string) => (
                <span
                  key={workflow}
                  className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                >
                  {workflow.replace('WorkflowType.', '').replace('_', ' ')}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-start">
              <XCircle className="w-5 h-5 text-red-400 mt-0.5" />
              <div className="ml-3">
                <h4 className="text-sm font-medium text-red-800">Service Error</h4>
                <p className="text-sm text-red-700 mt-1">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Last Refresh */}
        <div className="mt-6 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-between text-sm text-gray-500">
            <span>Last updated: {lastRefresh.toLocaleTimeString()}</span>
            {autoRefresh && (
              <span>Auto-refresh: {refreshInterval / 1000}s</span>
            )}
          </div>
        </div>

        {/* Health Check Button */}
        <div className="mt-4">
          <button
            onClick={async () => {
              const isHealthy = await checkHealth();
              alert(`Health check: ${isHealthy ? 'Healthy' : 'Unhealthy'}`);
            }}
            className="w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
          >
            Run Health Check
          </button>
        </div>

        {/* Service Recommendations */}
        {overallStatus !== 'healthy' && (
          <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h4 className="text-sm font-medium text-yellow-800 mb-2">Recommendations</h4>
            <ul className="text-sm text-yellow-700 space-y-1">
              {!availability.consolidatedAI && (
                <li>• Check if the consolidated AI service is properly configured</li>
              )}
              {!availability.gemini && (
                <li>• Verify GEMINI_API_KEY environment variable is set</li>
              )}
              {!availability.langchain && (
                <li>• Install LangChain dependencies for advanced workflows</li>
              )}
              {!availability.mlService && (
                <li>• Configure ML service for enhanced insights</li>
              )}
              {!availability.arabicProcessing && (
                <li>• Set up Arabic processing utilities for cultural context</li>
              )}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

export default ConsolidatedAIStatus;
