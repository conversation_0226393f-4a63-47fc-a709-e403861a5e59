"""
Centralized AI URLs
Universal AI endpoints that replace all duplicate AI URLs across the application
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import AIConfigurationViewSet, AIStatusView
from .ai_views import (
    UniversalChatView,
    UniversalBusinessAnalysisView,
    UniversalTextAnalysisView,
    UniversalAIStatusView,
    IntelligentContentView,
    LanguageDetectionView,
    PredictiveAnalyticsView,
    ComputerVisionView,
    VoiceAIView,
    universal_chat,
    universal_business_analysis,
    universal_ai_status,
    universal_ai_test,
    detect_text_language
)

# Create router for AI configuration management
router = DefaultRouter()
router.register(r'config', AIConfigurationViewSet, basename='ai-config')

# Enhanced Universal AI URL patterns - used across the entire application
urlpatterns = [
    # ========================================
    # AI CONFIGURATION MANAGEMENT (SUPER ADMIN)
    # ========================================
    # Configuration endpoints for Super Admin
    path('', include(router.urls)),
    path('status/', AIStatusView.as_view(), name='ai-status'),

    # ========================================
    # ENHANCED UNIVERSAL AI ENDPOINTS (RECOMMENDED)
    # ========================================
    # These endpoints replace ALL other AI endpoints in the application
    # Now with caching, rate limiting, and intelligent features

    # Core AI functionality
    path('chat/', UniversalChatView.as_view(), name='universal-chat'),
    path('business-analysis/', UniversalBusinessAnalysisView.as_view(), name='universal-business-analysis'),
    path('text-analysis/', UniversalTextAnalysisView.as_view(), name='universal-text-analysis'),
    path('service-status/', UniversalAIStatusView.as_view(), name='universal-ai-status'),

    # New intelligent features
    path('intelligent-content/', IntelligentContentView.as_view(), name='intelligent-content'),
    path('detect-language/', LanguageDetectionView.as_view(), name='language-detection'),

    # ========================================
    # ADVANCED AI FEATURES (NEW)
    # ========================================
    # Predictive Analytics
    path('predictive-analytics/', PredictiveAnalyticsView.as_view(), name='predictive-analytics'),
    path('predict/', PredictiveAnalyticsView.as_view(), name='ai-predict'),
    path('forecast/', PredictiveAnalyticsView.as_view(), name='ai-forecast'),
    path('risk-assessment/', PredictiveAnalyticsView.as_view(), name='risk-assessment'),
    path('investment-score/', PredictiveAnalyticsView.as_view(), name='investment-score'),

    # Computer Vision
    path('computer-vision/', ComputerVisionView.as_view(), name='computer-vision'),
    path('analyze-document/', ComputerVisionView.as_view(), name='analyze-document'),
    path('analyze-pitch-deck/', ComputerVisionView.as_view(), name='analyze-pitch-deck'),
    path('analyze-logo/', ComputerVisionView.as_view(), name='analyze-logo'),
    path('extract-chart-data/', ComputerVisionView.as_view(), name='extract-chart-data'),

    # Voice AI
    path('voice-ai/', VoiceAIView.as_view(), name='voice-ai'),
    path('transcribe/', VoiceAIView.as_view(), name='voice-transcribe'),
    path('synthesize/', VoiceAIView.as_view(), name='voice-synthesize'),
    path('voice-sentiment/', VoiceAIView.as_view(), name='voice-sentiment'),
    path('voice-command/', VoiceAIView.as_view(), name='voice-command'),
    path('transcribe-meeting/', VoiceAIView.as_view(), name='transcribe-meeting'),

    # Function-based views (simple usage)
    path('chat/', universal_chat, name='ai-chat'),
    path('business-analysis/', universal_business_analysis, name='ai-business-analysis'),
    path('status/', universal_ai_status, name='ai-status'),
    path('test/', universal_ai_test, name='ai-test'),
    
    # Utility endpoints
    path('detect-language/', detect_text_language, name='detect-language'),
    
    # ========================================
    # EASY ACCESS ENDPOINTS
    # ========================================
    # Simple endpoints for quick access
    
    # Chat endpoints
    path('ask/', universal_chat, name='ai-ask'),
    path('chat-auto/', universal_chat, name='ai-chat-auto'),  # Auto language detection
    
    # Analysis endpoints
    path('analyze/', universal_business_analysis, name='ai-analyze'),
    path('analyze-business/', universal_business_analysis, name='ai-analyze-business'),
    path('analyze-text/', UniversalTextAnalysisView.as_view(), name='ai-analyze-text'),
    
    # Status and health
    path('health/', universal_ai_status, name='ai-health'),
    path('ping/', universal_ai_test, name='ai-ping'),
    
    # ========================================
    # LANGUAGE-SPECIFIC ENDPOINTS
    # ========================================
    # Convenience endpoints for specific languages
    
    # Arabic endpoints
    path('arabic/chat/', universal_chat, name='ai-arabic-chat'),
    path('arabic/analyze/', universal_business_analysis, name='ai-arabic-analyze'),
    
    # English endpoints
    path('english/chat/', universal_chat, name='ai-english-chat'),
    path('english/analyze/', universal_business_analysis, name='ai-english-analyze'),
]


