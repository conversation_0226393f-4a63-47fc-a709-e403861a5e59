import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import {
  FileText,
  Plus,
  Trash2,
  Edit,
  ArrowLeft,
  Share2,
  Eye,
  EyeOff,
  RefreshCw,
  AlertCircle,
  Sparkles,
  Users
} from 'lucide-react';
import { useAppSelector } from '../../store/hooks';
import { TemplateCustomizer } from '../../components/incubator';
import {
  CustomBusinessPlanTemplate,
  customTemplatesAPI
} from '../../services/templateCustomizationApi';

const TemplateCustomizationPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();
  const { user } = useAppSelector(state => state.auth);

  const [customTemplates, setCustomTemplates] = useState<CustomBusinessPlanTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCustomizer, setShowCustomizer] = useState(false);
  const [deleteConfirmation, setDeleteConfirmation] = useState<number | null>(null);

  useEffect(() => {
    fetchCustomTemplates();
  }, []);

  const fetchCustomTemplates = async () => {
    setLoading(true);
    try {
      const templates = await customTemplatesAPI.getCustomTemplates();
      setCustomTemplates(templates);
      setError(null);
    } catch (err) {
      console.error('Error fetching custom templates:', err);
      setError('Failed to load custom templates. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteTemplate = async (id: number) => {
    try {
      await customTemplatesAPI.deleteCustomTemplate(id);
      setCustomTemplates(prev => prev.filter(template => template.id !== id));
      setDeleteConfirmation(null);
    } catch (err) {
      console.error(`Error deleting template ${id}:`, err);
      setError('Failed to delete template. Please try again.');
    }
  };

  const handleMakePublic = async (id: number) => {
    try {
      await customTemplatesAPI.makePublic(id);
      // Update the template in the list
      setCustomTemplates(prev => prev.map(template =>
        template.id === id ? { ...template, is_public: true } : template
      ));
    } catch (err) {
      console.error(`Error making template ${id} public:`, err);
      setError('Failed to update template visibility. Please try again.');
    }
  };

  const handleMakePrivate = async (id: number) => {
    try {
      await customTemplatesAPI.makePrivate(id);
      // Update the template in the list
      setCustomTemplates(prev => prev.map(template =>
        template.id === id ? { ...template, is_public: false } : template
      ));
    } catch (err) {
      console.error(`Error making template ${id} private:`, err);
      setError('Failed to update template visibility. Please try again.');
    }
  };

  const handleTemplateCreated = (templateId: number) => {
    // Refresh the list of templates
    fetchCustomTemplates();
    // Hide the customizer
    setShowCustomizer(false);
  };

  return (
      <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="mb-6">
        <Link
          to="/dashboard/business-plans"
          className="text-purple-400 hover:text-purple-300 flex items-center mb-4"
        >
          <ArrowLeft size={16} className="mr-1" /> {t('common.back')}
        </Link>

        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">{t('templates.businessPlanTemplates')}</h1>
            <div className="text-gray-400 mt-1">
              {t('templates.customTemplatesDescription')}
            </div>
          </div>

          {!showCustomizer && (
            <button
              onClick={() => setShowCustomizer(true)}
              className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white flex items-center"
            >
              <Plus size={16} className="mr-1" />
              {t('templates.createNew')}
            </button>
          )}
        </div>
      </div>

      {error && (
        <div className="mb-6 p-3 bg-red-900/50 border border-red-800 rounded-md text-red-200 flex items-start">
          <AlertCircle size={18} className="mr-2 mt-0.5 flex-shrink-0" />
          <span>{error}</span>
        </div>
      )}

      {showCustomizer ? (
        <TemplateCustomizer
          onTemplateCreated={handleTemplateCreated}
          onCancel={() => setShowCustomizer(false)}
        />
      ) : (
        <>
          {loading ? (
            <div className="flex justify-center py-12">
              <RefreshCw size={32} className="animate-spin text-purple-500" />
            </div>
          ) : customTemplates.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {customTemplates.map(template => (
                <div
                  key={template.id}
                  className="bg-gray-900 rounded-lg border border-gray-800 p-4"
                >
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h3 className="font-medium text-lg">{template.name}</h3>
                      <div className="flex items-center text-sm text-gray-400">
                        <span className="mr-3">
                          {t('common.basedOn')}: {template.base_template_details?.name || t('templates.customTemplate')}
                        </span>
                        {template.is_public ? (
                          <span className="flex items-center text-green-400">
                            <Eye size={14} className="mr-1" /> {t('common.public')}
                          </span>
                        ) : (
                          <span className="flex items-center text-gray-500">
                            <EyeOff size={14} className="mr-1" /> {t('common.private')}
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="flex">
                      {template.owner === user.id && (
                        <>
                          {template.is_public ? (
                            <button
                              onClick={() => handleMakePrivate(template.id)}
                              className="p-1.5 text-gray-400 hover:text-white"
                              title={t('common.makePrivate')}
                            >
                              <EyeOff size={16} />
                            </button>
                          ) : (
                            <button
                              onClick={() => handleMakePublic(template.id)}
                              className="p-1.5 text-gray-400 hover:text-white"
                              title={t('common.makePublic')}
                            >
                              <Eye size={16} />
                            </button>
                          )}

                          <button
                            onClick={() => navigate(`/dashboard/templates/${template.id}/edit`)}
                            className="p-1.5 text-gray-400 hover:text-white"
                            title={t('common.edit')}
                          >
                            <Edit size={16} />
                          </button>

                          <button
                            onClick={() => setDeleteConfirmation(template.id)}
                            className="p-1.5 text-gray-400 hover:text-white"
                            title={t('common.delete')}
                          >
                            <Trash2 size={16} />
                          </button>
                        </>
                      )}
                    </div>
                  </div>

                  <div className="text-gray-300 text-sm mb-4">{template.description}</div>

                  <div className="flex justify-between items-center">
                    <div className="flex items-center text-sm text-gray-400">
                      <FileText size={14} className="mr-1" />
                      <span>{template.sections?.sections?.length || 0} {t('templates.sections')}</span>

                      {template.shared_with?.length > 0 && (
                        <span className="ml-3 flex items-center">
                          <Users size={14} className="mr-1" />
                          {t('common.sharedWith')} {template.shared_with.length}
                        </span>
                      )}
                    </div>

                    <button
                      onClick={() => navigate(`/dashboard/business-plans/new?template=${template.id}`)}
                      className="px-3 py-1 bg-purple-600 hover:bg-purple-700 rounded-md text-sm text-white flex items-center"
                    >
                      <Sparkles size={14} className="mr-1" />
                      {t('templates.useTemplate')}
                    </button>
                  </div>

                  {/* Delete confirmation */}
                  {deleteConfirmation === template.id && (
                    <div className="mt-4 p-3 bg-red-900/30 border border-red-800 rounded-md">
                      <div className="text-sm text-red-200 mb-2">
                        {t('common.deleteConfirmation')}
                      </div>
                      <div className="flex justify-end space-x-2">
                        <button
                          onClick={() => setDeleteConfirmation(null)}
                          className="px-3 py-1 bg-gray-700 hover:bg-gray-600 rounded-md text-sm text-white"
                        >
                          {t('common.cancel')}
                        </button>
                        <button
                          onClick={() => handleDeleteTemplate(template.id)}
                          className="px-3 py-1 bg-red-600 hover:bg-red-700 rounded-md text-sm text-white"
                        >
                          {t('common.delete')}
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="bg-gray-900 rounded-lg border border-gray-800 p-8 text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-purple-900/30 mb-4">
                <FileText size={24} className="text-purple-400" />
              </div>
              <h3 className="text-xl font-medium mb-2">{t('templates.noCustomTemplates')}</h3>
              <p className="text-gray-400 mb-6 max-w-md mx-auto">
                {t('templates.createFirstTemplate')}
              </p>
              <button
                onClick={() => setShowCustomizer(true)}
                className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white inline-flex items-center"
              >
                <Plus size={16} className="mr-1" />
                {t('templates.createNew')}
              </button>
            </div>
          )}
        </>
      )}
              </div>
        </div>
      </div>
  );
};

export default TemplateCustomizationPage;
