/**
 * Enhanced Predictive Analytics Dashboard
 * Displays advanced analytics including failure prediction, timing optimization,
 * competitor analysis, and CAC prediction
 */

import React, { useState, useEffect } from 'react';
import { 
  AlertTriangle, 
  TrendingUp, 
  Users, 
  DollarSign, 
  Calendar,
  Target,
  Shield,
  Activity,
  BarChart3,
  AlertCircle,
  CheckCircle,
  Clock,
  Zap
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { 
  enhancedPredictiveAnalytics,
  BusinessData,
  FailurePredictionResult,
  TimingOptimizationResult,
  CompetitorAnalysisResult,
  CACPredictionResult
} from '../../services/enhancedPredictiveAnalytics';

interface EnhancedPredictiveAnalyticsDashboardProps {
  businessData: BusinessData;
  className?: string;
}

const EnhancedPredictiveAnalyticsDashboard: React.FC<EnhancedPredictiveAnalyticsDashboardProps> = ({
  businessData,
  className = ''
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [analytics, setAnalytics] = useState<{
    failurePrediction?: FailurePredictionResult;
    timingOptimization?: TimingOptimizationResult;
    competitorAnalysis?: CompetitorAnalysisResult;
    cacPrediction?: CACPredictionResult;
  }>({});
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadAnalytics();
  }, [businessData]);

  const loadAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const results = await enhancedPredictiveAnalytics.getComprehensiveAnalytics(businessData);
      setAnalytics(results);
    } catch (err) {
      console.error('Error loading enhanced analytics:', err);
      setError('Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  const getAlertLevelColor = (level: string) => {
    switch (level) {
      case 'critical': return 'text-red-600 bg-red-50 border-red-200';
      case 'high': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low': return 'text-green-600 bg-green-50 border-green-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getAlertIcon = (level: string) => {
    switch (level) {
      case 'critical': return <AlertTriangle className="w-5 h-5" />;
      case 'high': return <AlertCircle className="w-5 h-5" />;
      case 'medium': return <Clock className="w-5 h-5" />;
      case 'low': return <CheckCircle className="w-5 h-5" />;
      default: return <Activity className="w-5 h-5" />;
    }
  };

  if (loading) {
    return (
      <div className={`p-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`p-6 ${className}`}>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertTriangle className="w-5 h-5 text-red-600 mr-2" />
            <span className="text-red-800">{error}</span>
          </div>
          <button
            onClick={loadAnalytics}
            className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`p-6 space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900 flex items-center">
          <BarChart3 className="w-6 h-6 mr-2 text-blue-600" />
          Enhanced Predictive Analytics
        </h2>
        <button
          onClick={loadAnalytics}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center"
        >
          <Zap className="w-4 h-4 mr-2" />
          Refresh
        </button>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Failure Risk Card */}
        {analytics.failurePrediction && (
          <div className={`p-4 rounded-lg border ${getAlertLevelColor(analytics.failurePrediction.alert_level)}`}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium">Failure Risk</p>
                <p className="text-2xl font-bold">
                  {(analytics.failurePrediction.failure_probability * 100).toFixed(1)}%
                </p>
              </div>
              {getAlertIcon(analytics.failurePrediction.alert_level)}
            </div>
            <p className="text-xs mt-2 capitalize">
              {analytics.failurePrediction.alert_level} Alert Level
            </p>
          </div>
        )}

        {/* Market Timing Card */}
        {analytics.timingOptimization && (
          <div className="p-4 rounded-lg border bg-blue-50 border-blue-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-800">Market Timing</p>
                <p className="text-lg font-bold text-blue-900">
                  {analytics.timingOptimization.recommended_action.split(':')[1]?.trim() || 'Analyze'}
                </p>
              </div>
              <Calendar className="w-5 h-5 text-blue-600" />
            </div>
            <p className="text-xs mt-2 text-blue-700">
              Confidence: {(analytics.timingOptimization.confidence_level * 100).toFixed(0)}%
            </p>
          </div>
        )}

        {/* Competitive Threat Card */}
        {analytics.competitorAnalysis && (
          <div className="p-4 rounded-lg border bg-purple-50 border-purple-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-800">Competitive Threat</p>
                <p className="text-2xl font-bold text-purple-900">
                  {(analytics.competitorAnalysis.competitive_threat_score * 100).toFixed(0)}%
                </p>
              </div>
              <Users className="w-5 h-5 text-purple-600" />
            </div>
            <p className="text-xs mt-2 text-purple-700 capitalize">
              {analytics.competitorAnalysis.threat_level} Threat
            </p>
          </div>
        )}

        {/* CAC Prediction Card */}
        {analytics.cacPrediction && (
          <div className="p-4 rounded-lg border bg-green-50 border-green-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-800">Predicted CAC</p>
                <p className="text-2xl font-bold text-green-900">
                  ${analytics.cacPrediction.predicted_cac.toFixed(0)}
                </p>
              </div>
              <DollarSign className="w-5 h-5 text-green-600" />
            </div>
            <p className="text-xs mt-2 text-green-700 capitalize">
              {analytics.cacPrediction.cac_category} Level
            </p>
          </div>
        )}
      </div>

      {/* Detailed Analysis Sections */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Failure Prediction Details */}
        {analytics.failurePrediction && (
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center">
              <Shield className="w-5 h-5 mr-2 text-red-600" />
              Early Warning System
            </h3>
            
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Warning Indicators</h4>
                <div className="space-y-2">
                  {analytics.failurePrediction.warning_indicators.slice(0, 3).map((indicator, index) => (
                    <div key={index} className="flex items-start space-x-2">
                      <div className={`w-2 h-2 rounded-full mt-2 ${
                        indicator.severity === 'high' ? 'bg-red-500' :
                        indicator.severity === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                      }`}></div>
                      <div>
                        <p className="text-sm font-medium">{indicator.indicator}</p>
                        <p className="text-xs text-gray-600">{indicator.recommendation}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="font-medium text-gray-900 mb-2">Next Review</h4>
                <p className="text-sm text-gray-600">
                  {new Date(analytics.failurePrediction.next_review_date).toLocaleDateString()}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Market Timing Details */}
        {analytics.timingOptimization && (
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center">
              <Target className="w-5 h-5 mr-2 text-blue-600" />
              Timing Optimization
            </h3>
            
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Action Scores</h4>
                <div className="space-y-2">
                  {Object.entries(analytics.timingOptimization.timing_scores).map(([action, score]) => (
                    <div key={action} className="flex items-center justify-between">
                      <span className="text-sm capitalize">{action.replace('_', ' ')}</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-20 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full" 
                            style={{ width: `${score * 100}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium">{(score * 100).toFixed(0)}%</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="font-medium text-gray-900 mb-2">Recommendations</h4>
                <div className="space-y-1">
                  {analytics.timingOptimization.timing_recommendations.slice(0, 3).map((rec, index) => (
                    <p key={index} className="text-sm text-gray-600">• {rec}</p>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Competitor Analysis Details */}
        {analytics.competitorAnalysis && (
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center">
              <Users className="w-5 h-5 mr-2 text-purple-600" />
              Competitive Intelligence
            </h3>
            
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Top Competitors</h4>
                <div className="space-y-2">
                  {analytics.competitorAnalysis.competitor_analysis.slice(0, 3).map((competitor, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm">{competitor.name}</span>
                      <span className={`text-xs px-2 py-1 rounded ${
                        competitor.threat_level > 0.7 ? 'bg-red-100 text-red-800' :
                        competitor.threat_level > 0.4 ? 'bg-yellow-100 text-yellow-800' :
                        'bg-green-100 text-green-800'
                      }`}>
                        {(competitor.threat_level * 100).toFixed(0)}% threat
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="font-medium text-gray-900 mb-2">Strategic Recommendations</h4>
                <div className="space-y-1">
                  {analytics.competitorAnalysis.strategic_recommendations.slice(0, 3).map((rec, index) => (
                    <p key={index} className="text-sm text-gray-600">• {rec}</p>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* CAC Analysis Details */}
        {analytics.cacPrediction && (
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center">
              <TrendingUp className="w-5 h-5 mr-2 text-green-600" />
              CAC Optimization
            </h3>
            
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">LTV:CAC Ratio</h4>
                <div className="flex items-center space-x-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-600">
                      {analytics.cacPrediction.ltv_cac_analysis.ltv_cac_ratio.toFixed(1)}:1
                    </p>
                    <p className="text-xs text-gray-600">
                      {analytics.cacPrediction.ltv_cac_analysis.ratio_category}
                    </p>
                  </div>
                  <div className="text-sm text-gray-600">
                    <p>LTV: ${analytics.cacPrediction.ltv_cac_analysis.ltv}</p>
                    <p>Payback: {analytics.cacPrediction.ltv_cac_analysis.payback_period_months} months</p>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-medium text-gray-900 mb-2">Top Channels</h4>
                <div className="space-y-2">
                  {Object.entries(analytics.cacPrediction.channel_analysis)
                    .sort(([,a], [,b]) => a.efficiency_score - b.efficiency_score)
                    .slice(0, 3)
                    .map(([channel, data]) => (
                    <div key={channel} className="flex items-center justify-between">
                      <span className="text-sm capitalize">{channel.replace('_', ' ')}</span>
                      <div className="text-right">
                        <p className="text-sm font-medium">${data.cac}</p>
                        <p className="text-xs text-gray-600">{data.recommendation.split(' ')[0]}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EnhancedPredictiveAnalyticsDashboard;
