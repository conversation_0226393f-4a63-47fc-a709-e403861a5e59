from django.db.models import Q
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from .models import SearchDocument
from .serializers import SearchDocumentSerializer, SearchResultSerializer
import re

class SearchViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint for searching across all content types.
    """
    queryset = SearchDocument.objects.all()
    serializer_class = SearchDocumentSerializer
    permission_classes = [permissions.IsAuthenticated]

    @action(detail=False, methods=['get'], url_path='query')
    def search_query(self, request):
        """
        Search across all content types with a query string.

        Query parameters:
        - q: The search query string
        - types: Comma-separated list of content types to search (default: all)
        - limit: Maximum number of results to return (default: 20)
        - offset: Number of results to skip (default: 0)
        """
        # Get query parameters
        query_string = request.query_params.get('q', '')
        content_types = request.query_params.get('types', '')
        limit = int(request.query_params.get('limit', 20))
        offset = int(request.query_params.get('offset', 0))

        # Validate query string
        if not query_string:
            return Response(
                {"error": "Search query is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Parse content types
        if content_types:
            content_types = content_types.split(',')
            queryset = self.queryset.filter(content_type__in=content_types)
        else:
            queryset = self.queryset

        # Split query into words for better matching
        query_words = query_string.lower().split()

        # Create a Q object for searching in the search_text field
        query_filter = Q()
        for word in query_words:
            query_filter &= Q(search_text__icontains=word)

        # Filter by search query
        queryset = queryset.filter(query_filter)

        # Order by most recent first (since we can't do proper ranking with SQLite)
        queryset = queryset.order_by('-created_at')

        # Apply pagination
        queryset = queryset[offset:offset+limit]

        # Generate highlighted results
        results = []
        for doc in queryset:
            # Simple highlighting function
            def highlight_text(text, query_words):
                highlighted = text

                # Sort query words by length (longest first) to avoid nested highlights
                for word in sorted(query_words, key=len, reverse=True):
                    if len(word) < 3:  # Skip very short words
                        continue

                    # Find all occurrences of the word
                    pattern = re.compile(re.escape(word), re.IGNORECASE)
                    highlighted = pattern.sub(r'<mark>\g<0></mark>', highlighted)

                return highlighted

            # For content, we want to show a snippet around the match
            content = doc.content

            # Find the first match position
            first_match_pos = -1
            for word in query_words:
                if len(word) < 3:  # Skip very short words
                    continue
                pos = content.lower().find(word.lower())
                if pos != -1 and (first_match_pos == -1 or pos < first_match_pos):
                    first_match_pos = pos

            # Determine the content snippet to use
            if first_match_pos != -1:
                start = max(0, first_match_pos - 100)
                end = min(len(content), first_match_pos + 200)

                # Adjust to word boundaries
                if start > 0:
                    while start > 0 and content[start] != ' ':
                        start -= 1
                    content_snippet = '...' + content[start:end]
                else:
                    content_snippet = content[start:end]

                if end < len(content):
                    while end < len(content) and content[end] != ' ':
                        end += 1
                    content_snippet += '...'
            else:
                # If no match found, just take the first part of the content
                content_snippet = content[:250] + '...' if len(content) > 250 else content

            # Add to results
            result = {
                'id': doc.id,
                'content_type': doc.content_type,
                'object_id': doc.object_id,
                'title': doc.title,
                'content': doc.content,
                'author': doc.author,
                'created_at': doc.created_at,
                'updated_at': doc.updated_at,
                'tags': doc.tags,
                'metadata': doc.metadata,
                'title_highlighted': highlight_text(doc.title, query_words),
                'content_highlighted': highlight_text(content_snippet, query_words),
                'score': 1.0  # Default score since we can't do proper ranking with SQLite
            }
            results.append(result)

        # Serialize results
        serializer = SearchResultSerializer(results, many=True)

        # Return response with pagination info
        return Response({
            'count': queryset.count(),
            'next': offset + limit if offset + limit < queryset.count() else None,
            'previous': offset - limit if offset > 0 else None,
            'results': serializer.data
        })

    @action(detail=False, methods=['get'], url_path='autocomplete')
    def autocomplete(self, request):
        """
        Autocomplete search for titles only.

        Query parameters:
        - q: The search query string
        - types: Comma-separated list of content types to search (default: all)
        - limit: Maximum number of results to return (default: 10)
        """
        # Get query parameters
        query_string = request.query_params.get('q', '')
        content_types = request.query_params.get('types', '')
        limit = int(request.query_params.get('limit', 10))

        # Validate query string
        if not query_string:
            return Response(
                {"error": "Search query is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Parse content types
        if content_types:
            content_types = content_types.split(',')
            queryset = self.queryset.filter(content_type__in=content_types)
        else:
            queryset = self.queryset

        # Filter by title containing query string
        queryset = queryset.filter(title__icontains=query_string)
        queryset = queryset.order_by('title')[:limit]

        # Add a simple relevance score based on exact match
        for doc in queryset:
            if query_string.lower() == doc.title.lower():
                doc.score = 1.0  # Exact match
            elif doc.title.lower().startswith(query_string.lower()):
                doc.score = 0.8  # Starts with query
            else:
                doc.score = 0.5  # Contains query

        # Serialize results
        serializer = self.get_serializer(queryset, many=True)

        return Response(serializer.data)
