/**
 * AI System Admin Page
 * Dedicated admin page for AI system management with proper styling and translation
 */

import React, { useState, useEffect } from 'react';
import {
  Activity,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Zap,
  Brain,
  Globe,
  Bot,
  RefreshCw,
  Clock,
  BarChart3,
  Sparkles,
  Settings,
  Users,
  TrendingUp,
  DollarSign
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { useCentralizedAI } from '../../hooks/useCentralizedAI';


// RTL Text component fallback
const RTLText: React.FC<{
  children: React.ReactNode;
  as?: keyof JSX.IntrinsicElements;
  className?: string;
}> = ({ children, as: Component = 'span', className = '' }) => {
  return React.createElement(Component, { className }, children);
};

interface ServiceComponent {
  name: string;
  key: string;
  icon: React.ReactNode;
  description: string;
  status: boolean;
  critical: boolean;
}

const AISystemPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const [refreshing, setRefreshing] = useState(false);

  const {
    isAvailable,
    status,
    chatError,
  } = useCentralizedAI();

  // Service availability
  const availability = {
    consolidatedAI: isAvailable,
    gemini: isAvailable,
    langchain: true,
    workflows: true,
    mlService: true,
    arabicProcessing: true,
  };

  const error = chatError;

  const refreshStatus = async () => {
    setRefreshing(true);
    try {
      // Mock refresh - in real implementation would refresh the status
      setLastRefresh(new Date());
      await new Promise(resolve => setTimeout(resolve, 1000));
    } finally {
      setRefreshing(false);
    }
  };

  const serviceComponents: ServiceComponent[] = [
    {
      name: t('admin.ai.consolidatedService', 'Consolidated AI Service'),
      key: 'consolidatedAI',
      icon: <Sparkles className="w-5 h-5" />,
      description: t('admin.ai.mainUnifiedService', 'Main unified AI service'),
      status: availability.consolidatedAI,
      critical: true,
    },
    {
      name: t('admin.ai.geminiAI', 'Gemini AI'),
      key: 'gemini',
      icon: <Bot className="w-5 h-5" />,
      description: t('admin.ai.geminiIntegration', 'Google Gemini AI integration'),
      status: availability.gemini,
      critical: true,
    },
    {
      name: t('admin.ai.langchain', 'LangChain/LangGraph'),
      key: 'langchain',
      icon: <Zap className="w-5 h-5" />,
      description: t('admin.ai.workflowOrchestration', 'Advanced workflow orchestration'),
      status: availability.langchain,
      critical: false,
    },
    {
      name: t('admin.ai.workflows', 'Workflows'),
      key: 'workflows',
      icon: <Activity className="w-5 h-5" />,
      description: t('admin.ai.businessAnalysisWorkflows', 'Business analysis workflows'),
      status: availability.workflows,
      critical: false,
    },
    {
      name: t('admin.ai.mlService', 'ML Service'),
      key: 'mlService',
      icon: <Brain className="w-5 h-5" />,
      description: t('admin.ai.machineLearningInsights', 'Machine learning insights'),
      status: availability.mlService,
      critical: false,
    },
    {
      name: t('admin.ai.arabicProcessing', 'Arabic Processing'),
      key: 'arabicProcessing',
      icon: <Globe className="w-5 h-5" />,
      description: t('admin.ai.arabicLanguageSupport', 'Enhanced Arabic language support'),
      status: availability.arabicProcessing,
      critical: false,
    },
  ];

  const getOverallStatus = () => {
    const criticalServices = serviceComponents.filter(s => s.critical);
    const criticalOnline = criticalServices.filter(s => s.status);

    if (criticalOnline.length === criticalServices.length) {
      return 'healthy';
    } else if (criticalOnline.length > 0) {
      return 'degraded';
    } else {
      return 'down';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'text-green-400 bg-green-600/20 border-green-500/30';
      case 'degraded':
        return 'text-yellow-400 bg-yellow-600/20 border-yellow-500/30';
      case 'down':
        return 'text-red-400 bg-red-600/20 border-red-500/30';
      default:
        return 'text-gray-400 bg-gray-600/20 border-gray-500/30';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="w-5 h-5 text-green-400" />;
      case 'degraded':
        return <AlertTriangle className="w-5 h-5 text-yellow-400" />;
      case 'down':
        return <XCircle className="w-5 h-5 text-red-400" />;
      default:
        return <AlertTriangle className="w-5 h-5 text-gray-400" />;
    }
  };

  const overallStatus = getOverallStatus();
  const onlineServices = serviceComponents.filter(s => s.status).length;
  const totalServices = serviceComponents.length;

  return (
    <div>
      {/* Header */}
      <div className="mb-8">
        <RTLText as="h1" className="text-2xl font-bold mb-2 text-white">
          {t('admin.ai.systemManagement', 'AI System Management')}
        </RTLText>
        <RTLText className="text-gray-300">
          {t('admin.ai.monitorAndManage', 'Monitor and manage the AI service infrastructure')}
        </RTLText>
      </div>

      {/* Overall Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className={`glass-morphism rounded-lg p-6 border ${getStatusColor(overallStatus)}`}>
          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className="flex-shrink-0">
              {getStatusIcon(overallStatus)}
            </div>
            <div className={isRTL ? 'mr-3' : 'ml-3'}>
              <RTLText className="text-sm font-medium text-glass-secondary">
                {t('admin.ai.overallStatus', 'Overall Status')}
              </RTLText>
              <RTLText as="h3" className="text-lg font-bold capitalize text-glass-primary">
                {t(`admin.ai.status.${overallStatus}`, overallStatus)}
              </RTLText>
            </div>
          </div>
        </div>

        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className="flex-shrink-0">
              <Activity className="w-5 h-5 text-blue-400" />
            </div>
            <div className={isRTL ? 'mr-3' : 'ml-3'}>
              <RTLText className="text-sm font-medium text-gray-400">
                {t('admin.ai.servicesOnline', 'Services Online')}
              </RTLText>
              <RTLText as="h3" className="text-lg font-bold text-white">
                {onlineServices}/{totalServices}
              </RTLText>
            </div>
          </div>
        </div>

        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className="flex-shrink-0">
              <BarChart3 className="w-5 h-5 text-purple-400" />
            </div>
            <div className={isRTL ? 'mr-3' : 'ml-3'}>
              <RTLText className="text-sm font-medium text-gray-400">
                {t('admin.ai.requestCount', 'Request Count')}
              </RTLText>
              <RTLText as="h3" className="text-lg font-bold text-white">
                {status?.request_count || 0}
              </RTLText>
            </div>
          </div>
        </div>

        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className="flex-shrink-0">
              <Clock className="w-5 h-5 text-green-400" />
            </div>
            <div className={isRTL ? 'mr-3' : 'ml-3'}>
              <RTLText className="text-sm font-medium text-gray-400">
                {t('admin.ai.uptime', 'Uptime')}
              </RTLText>
              <RTLText as="h3" className="text-lg font-bold text-white">
                {status?.uptime ? `${Math.floor(status.uptime / 3600)}h` : 'N/A'}
              </RTLText>
            </div>
          </div>
        </div>
      </div>

      {/* Service Components */}
      <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 mb-8 border border-white/20">
        <div className={`flex items-center justify-between mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <RTLText as="h2" className="text-xl font-semibold text-white">
            {t('admin.ai.serviceComponents', 'Service Components')}
          </RTLText>
          <button
            onClick={refreshStatus}
            disabled={refreshing}
            className={`flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors disabled:opacity-50 text-white ${isRTL ? 'flex-row-reverse' : ''}`}
          >
            <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''} ${isRTL ? 'ml-2' : 'mr-2'}`} />
            <RTLText>{t('common.refresh', 'Refresh')}</RTLText>
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {serviceComponents.map((service) => (
            <div
              key={service.key}
              className={`p-4 rounded-lg border backdrop-blur-sm ${
                service.status 
                  ? 'border-green-500/30 bg-green-600/10' 
                  : 'border-red-500/30 bg-red-600/10'
              }`}
            >
              <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                    service.status ? 'bg-green-600/20 text-green-400' : 'bg-red-600/20 text-red-400'
                  }`}>
                    {service.icon}
                  </div>
                  <div className={isRTL ? 'mr-3' : 'ml-3'}>
                    <RTLText as="h4" className="font-medium flex items-center">
                      {service.name}
                      {service.critical && (
                        <span className={`px-2 py-1 text-xs bg-orange-600/20 text-orange-400 rounded-full ${isRTL ? 'mr-2' : 'ml-2'}`}>
                          {t('admin.ai.critical', 'Critical')}
                        </span>
                      )}
                    </RTLText>
                    <RTLText className="text-sm text-gray-400">{service.description}</RTLText>
                  </div>
                </div>

                <div className={`w-3 h-3 rounded-full ${
                  service.status ? 'bg-green-400' : 'bg-red-400'
                }`} />
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 mb-8 border border-red-500/30">
          <div className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
            <XCircle className="w-5 h-5 text-red-400 mt-0.5" />
            <div className={isRTL ? 'mr-3' : 'ml-3'}>
              <RTLText as="h4" className="text-sm font-medium text-red-400">
                {t('admin.ai.serviceError', 'Service Error')}
              </RTLText>
              <RTLText className="text-sm text-red-300 mt-1">{error}</RTLText>
            </div>
          </div>
        </div>
      )}

      {/* Last Refresh */}
      <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
        <div className={`flex items-center justify-between text-sm text-gray-400 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <RTLText>
            {t('admin.ai.lastUpdated', 'Last updated')}: {lastRefresh.toLocaleTimeString()}
          </RTLText>
          <RTLText>
            {t('admin.ai.autoRefresh', 'Auto-refresh')}: 30s
          </RTLText>
        </div>
    </div>
  );
};

export default AISystemPage;
