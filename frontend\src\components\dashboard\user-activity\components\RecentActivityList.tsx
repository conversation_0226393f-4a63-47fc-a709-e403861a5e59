import React from 'react';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../../../../store/hooks';
import { useLanguage } from '../../../../hooks/useLanguage';
import { FileText, MessageSquare, Calendar } from 'lucide-react';
import { UserActivity } from '../../../../services/api';

interface RecentActivityListProps {
  activity: UserActivity;
}

const RecentActivityList: React.FC<RecentActivityListProps> = ({ activity  }) => {
  const { t } = useTranslation();
  const { isRTL, language } = useLanguage();

  const hasActivity =
    activity.recent_posts.length > 0 ||
    activity.recent_comments.length > 0 ||
    activity.recent_events.length > 0;

  if (!hasActivity) {
    return (
      <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50">
        <h3 className="text-lg font-semibold mb-4">{t('dashboard.userActivity.recentActivity')}</h3>
        <div className="text-gray-400">{t('dashboard.userActivity.noRecentActivity')}</div>
      </div>
    );
  }

  return (
    <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/50">
      <h3 className="text-lg font-semibold mb-4">{t('dashboard.userActivity.recentActivity')}</h3>
      <div className="space-y-4">
        {activity.recent_posts.length > 0 && (
          <div>
            <h4 className="text-md font-medium text-purple-300 mb-2">{t('dashboard.userActivity.recentPosts')}</h4>
            <ul className="space-y-2">
              {activity.recent_posts.map(post => (
                <li key={post.id} className="bg-indigo-800/30 p-3 rounded">
                  <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    <FileText size={16} className={`text-indigo-400 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                    <span>{post.title}</span>
                  </div>
                  <div className="text-xs text-gray-400 mt-1">
                    {new Date(post.created_at).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                  </div>
                </li>
              ))}
            </ul>
          </div>
        )}

        {activity.recent_comments.length > 0 && (
          <div>
            <h4 className="text-md font-medium text-purple-300 mb-2">{t('dashboard.userActivity.recentComments')}</h4>
            <ul className="space-y-2">
              {activity.recent_comments.map(comment => (
                <li key={comment.id} className="bg-indigo-800/30 p-3 rounded">
                  <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    <MessageSquare size={16} className={`text-purple-400 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                    <span>{comment.content}</span>
                  </div>
                  <div className="text-xs text-gray-400 mt-1">
                    {new Date(comment.created_at).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                  </div>
                </li>
              ))}
            </ul>
          </div>
        )}

        {activity.recent_events.length > 0 && (
          <div>
            <h4 className="text-md font-medium text-purple-300 mb-2">{t('dashboard.userActivity.recentEvents')}</h4>
            <ul className="space-y-2">
              {activity.recent_events.map(event => (
                <li key={event.id} className="bg-indigo-800/30 p-3 rounded">
                  <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    <Calendar size={16} className={`text-blue-400 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                    <span>{event.title}</span>
                  </div>
                  <p className="text-xs text-gray-400 mt-1">
                    {new Date(event.date).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                  </p>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

export default RecentActivityList;
