import React from 'react';
import { formatNumber, formatCurrency, formatPercentage, formatFileSize } from '../../utils/numberFormatter';

interface LocalizedNumberProps {
  value: number;
  format?: 'number' | 'currency' | 'percentage' | 'fileSize';
  className?: string;
  options?: {
    minimumFractionDigits?: number;
    maximumFractionDigits?: number;
    useGrouping?: boolean;
    currency?: string;
    displaySymbol?: boolean;
    convertFromDecimal?: boolean;
  };
}

/**
 * Component for displaying localized numbers
 */
const LocalizedNumber: React.FC<LocalizedNumberProps> = ({
  value,
  format = 'number',
  className = '',
  options,
}) => {
  if (value === null || value === undefined || isNaN(value)) {
    return null;
  }

  let formattedValue = '';

  // Format based on the specified format type
  switch (format) {
    case 'currency':
      formattedValue = formatCurrency(value, {
        currency: options?.currency,
        minimumFractionDigits: options?.minimumFractionDigits,
        maximumFractionDigits: options?.maximumFractionDigits,
        displaySymbol: options?.displaySymbol,
      });
      break;
    case 'percentage':
      formattedValue = formatPercentage(value, {
        minimumFractionDigits: options?.minimumFractionDigits,
        maximumFractionDigits: options?.maximumFractionDigits,
        convertFromDecimal: options?.convertFromDecimal,
      });
      break;
    case 'fileSize':
      formattedValue = formatFileSize(value, options?.maximumFractionDigits || 2);
      break;
    case 'number':
    default:
      formattedValue = formatNumber(value, {
        minimumFractionDigits: options?.minimumFractionDigits,
        maximumFractionDigits: options?.maximumFractionDigits,
        useGrouping: options?.useGrouping,
      });
      break;
  }

  return <span className={className}>{formattedValue}</span>;
};

export default LocalizedNumber;
