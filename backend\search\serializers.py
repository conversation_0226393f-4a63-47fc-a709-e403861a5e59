from rest_framework import serializers
from .models import SearchDocument

class SearchDocumentSerializer(serializers.ModelSerializer):
    """Serializer for search documents"""
    
    class Meta:
        model = SearchDocument
        fields = [
            'id', 'content_type', 'object_id', 'title', 'content', 
            'author', 'created_at', 'updated_at', 'tags', 'metadata'
        ]
        read_only_fields = fields  # All fields are read-only

class SearchResultSerializer(serializers.Serializer):
    """Serializer for search results with highlighting"""
    id = serializers.IntegerField()
    content_type = serializers.CharField()
    object_id = serializers.IntegerField()
    title = serializers.CharField()
    content = serializers.CharField()
    author = serializers.CharField()
    created_at = serializers.DateTimeField()
    updated_at = serializers.DateTimeField()
    tags = serializers.ListField(child=serializers.CharField(), required=False)
    metadata = serializers.DictField(required=False)
    
    # Highlighted fields
    title_highlighted = serializers.Char<PERSON>ield(required=False)
    content_highlighted = serializers.CharField(required=False)
    
    # Relevance score
    score = serializers.FloatField(required=False)
