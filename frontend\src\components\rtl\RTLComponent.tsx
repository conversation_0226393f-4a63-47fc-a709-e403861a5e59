import React from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';


/**
 * RTL Component
 *
 * A unified RTL-aware component that combines functionality from multiple RTL components.
 * This component simplifies the component hierarchy by providing a single component
 * that handles RTL-specific layout, text alignment, and theming.
 */

interface SpacingProps {
  top?: number;
  bottom?: number;
  left?: number;
  right?: number;
  start?: number;
  end?: number;
  x?: number;
  y?: number;
}

/**
 * Props for the RTLComponent
 */
export interface RTLComponentProps {
  /**
   * The content to display
   */
  children: React.ReactNode;

  /**
   * Additional class names
   */
  className?: string;

  /**
   * The HTML element to render
   */
  as?: keyof JSX.IntrinsicElements;

  /**
   * Whether to apply theme styles
   */
  theme?: boolean;

  /**
   * Dark mode class names
   */
  darkClassName?: string;

  /**
   * Light mode class names
   */
  lightClassName?: string;

  /**
   * Text alignment
   */
  align?: 'start' | 'end' | 'center';

  /**
   * Whether to reverse the component in RTL mode
   */
  reverseInRTL?: boolean;

  /**
   * Flex direction
   */
  direction?: 'row' | 'column';

  /**
   * Flex alignment
   */
  flexAlign?: 'start' | 'center' | 'end' | 'stretch' | 'baseline';

  /**
   * Flex justification
   */
  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';

  /**
   * Whether to use flex layout
   */
  flex?: boolean;

  /**
   * Whether to wrap flex items
   */
  wrap?: boolean;

  /**
   * Gap between flex items
   */
  gap?: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 8 | 10 | 12;

  /**
   * Margin properties
   */
  margin?: SpacingProps;

  /**
   * Padding properties
   */
  padding?: SpacingProps;

  /**
   * Additional props
   */
  [key: string]: any;
}

/**
 * A unified RTL-aware component
 */
export const RTLComponent: React.FC<RTLComponentProps> = ({ children,
  className = '',
  as: Component = 'div',
  theme = false,
  darkClassName = '',
  lightClassName = '',
  align,
  reverseInRTL = false,
  direction = 'row',
  flexAlign,
  justify,
  flex = false,
  wrap = false,
  gap = 0,
  margin,
  padding,
  ...props
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  // Helper functions for RTL classes
  const getTextAlignClass = (alignment: 'start' | 'end' | 'center') => {
    switch (alignment) {
      case 'start':
        return isRTL ? 'text-right' : 'text-left';
      case 'end':
        return isRTL ? 'text-left' : 'text-right';
      case 'center':
        return 'text-center';
      default:
        return 'text-left';
    }
  };

  const getFlexDirectionClass = (direction: 'row' | 'column', reverse: boolean) => {
    if (direction === 'column') {
      return reverse ? 'flex-col-reverse' : 'flex-col';
    }
    if (reverse && isRTL) {
      return 'flex-row-reverse';
    }
    return 'flex-row';
  };

  // Build class names
  const classes: string[] = [];

  // Add base class
  classes.push(className);

  // Add text alignment class
  if (align) {
    classes.push(getTextAlignClass(align));
  }

  // Add flex classes
  if (flex) {
    classes.push('flex');

    // Add flex direction class
    if (direction === 'row') {
      classes.push(getFlexDirectionClass(reverseInRTL));
    } else {
      classes.push('flex-col');
    }

    // Add flex alignment class
    if (flexAlign) {
      switch (flexAlign) {
        case 'start':
          classes.push('items-start');
          break;
        case 'center':
          classes.push('items-center');
          break;
        case 'end':
          classes.push('items-end');
          break;
        case 'stretch':
          classes.push('items-stretch');
          break;
        case 'baseline':
          classes.push('items-baseline');
          break;
      }
    }

    // Add flex justification class
    if (justify) {
      switch (justify) {
        case 'start':
          classes.push('justify-start');
          break;
        case 'center':
          classes.push('justify-center');
          break;
        case 'end':
          classes.push('justify-end');
          break;
        case 'between':
          classes.push('justify-between');
          break;
        case 'around':
          classes.push('justify-around');
          break;
        case 'evenly':
          classes.push('justify-evenly');
          break;
      }
    }

    // Add flex wrap class
    if (wrap) {
      classes.push('flex-wrap');
    }

    // Add gap class
    if (gap > 0) {
      classes.push(`gap-${gap}`);
    }
  }

  // Add margin classes
  if (margin) {
    classes.push(getMarginClasses(margin));
  }

  // Add padding classes
  if (padding) {
    classes.push(getPaddingClasses(padding));
  }

  // Apply RTL-specific styles
  const rtlClass = reverseInRTL && isRTL && !flex ? 'rtl-reverse' : '';
  if (rtlClass) {
    classes.push(rtlClass);
  }

  // Combine all classes
  const combinedClassName = classes.join(' ');

  // Apply glass morphism styling if theme is enabled
  const finalClassName = theme
    ? `${combinedClassName} glass-light text-glass-primary`
    : combinedClassName;

  return (
    <Component className={finalClassName} {...props}>
      {children}
    </Component>
  );
};

export default RTLComponent;
